[{"primary_key": "3865382", "vector": [], "sparse_vector": [], "title": "Minimum Fill-In: Inapproximability and Almost Tight Lower Bounds.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "Performing Gaussian elimination to a sparse matrix may turn some zeroes into nonzero values, so called fill-ins, which we want to minimize to keep the matrix sparse. Let n denote the rows of the matrix and k the number of fill-ins. For the minimum fill-in problem, we exclude the existence of polynomial time approximation schemes, assuming P≠NP, and the existence of 2O(n1+ δ) -time approximation schemes for any positive δ, assuming the Exponential Time Hypothesis. Also implied is a 2O(K1\\2-δ). nO(1) parameterized lower bound. Behind these results is a new reduction from vertex cover, which might be of its own interest: All previous reductions for similar problems are from some kind of graph layout problems.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.55"}, {"primary_key": "3865383", "vector": [], "sparse_vector": [], "title": "An Improved Upper Bound for the Universal TSP on the Grid.", "authors": ["<PERSON>", "Alk<PERSON>"], "summary": "We study the universal Traveling Salesman Problem in an n × n grid with the shortest path metric. The goal is to define a (universal) total ordering over the set of grid's vertices, in a way that for any input (subset of vertices), the tour, which visits the points in this ordering, is a good approximation of the optimal tour, i.e. has low competitive ratio.This problem was first studied by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [26]. They proposed a heuristic, which was based on the <PERSON><PERSON><PERSON><PERSON> space-filling curve, in order to define a universal ordering of the unit square [0,1]2 under the Euclidean metric. Their heuristic visits the points of the unit square in the order of their appearance along the space-filling curve. They provided a logarithmic upper bound which was shown to be tight up to a constant by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [3]. <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> further showed logarithmic lower bounds for other space-filling curves and they conjectured that any universal ordering has a logarithmic lower bound for the n × n grid.In this work, we disprove this conjecture by showing that there exists a universal ordering of the n × n grid with competitive ratio of The heuristic we propose defines a universal ordering of the grid's vertices based on a generalization of the Lebesgue space filling curve. In order to analyze the competitive ratio of our heuristic, we employ techniques from the theory of geometric spanners in Euclidean spaces. We finally show that our analysis is tight up to a constant.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.64"}, {"primary_key": "3865384", "vector": [], "sparse_vector": [], "title": "Maximum Scatter TSP in Doubling Metrics.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the problem of finding a tour of n points in which every edge is long. More precisely, we wish to find a tour that visits every point exactly once, maximizing the length of the shortest edge in the tour. The problem is known as Maximum Scatter TSP, and was introduced by <PERSON><PERSON> et al. (SODA 1997), motivated by applications in manufacturing and medical imaging. <PERSON><PERSON> et al. gave a 0.5- approximation for the metric version of the problem and showed that this is the best possible ratio achievable in polynomial time (assuming P ≠ NP). <PERSON><PERSON> et al. raised the question of whether a better approximation ratio can be obtained in the Euclidean plane.We answer this question in the affirmative in a more general setting, by giving a (1-∊)-approximation algorithm for d-dimensional doubling metrics, with running time Õ(n3 + 2O(K log K)), where As a corollary we obtain (i) an efficient polynomial- time approximation scheme (EPTAS) for all constant dimensions d, (ii) a polynomial-time approximation scheme (PTAS) for dimension d = log log n/c, for a sufficiently large constant c, and (iii) a PTAS for constant d and ∊ = Ω(1/log log n). Furthermore, we show the dependence on d in our approximation scheme to be essentially optimal, unless Satisfiability can be solved in subexponential time.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.10"}, {"primary_key": "3865385", "vector": [], "sparse_vector": [], "title": "pBWT: Achieving Succinct Data Structures for Parameterized Pattern Matching and Related Problems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The fields of succinct data structures and compressed text indexing have seen quite a bit of progress over the last two decades. An important achievement, primarily using techniques based on the Burrows-Wheeler Transform (BWT), was obtaining the full functionality of the suffix tree in the optimal number of bits. A crucial property that allows the use of BWT for designing compressed indexes is order-preserving suffix links. Specifically, the relative order between two suffixes in the subtree of an internal node is same as that of the suffixes obtained by truncating the first character of the two suffixes. Unfortunately, in many variants of the text-indexing problem, for e.g., parameterized pattern matching, 2D pattern matching, and order-isomorphic pattern matching, this property does not hold. Consequently, the compressed indexes based on BWT do not directly apply. Furthermore, a compressed index for any of these variants has been elusive throughout the advancement of the field of succinct data structures. We achieve a positive breakthrough on one such problem, namely the Parameterized Pattern Matching problem.Let T be a text that contains η characters from an alphabet Σ, which is the union of two disjoint sets: Ss containing static characters (s-characters) and Σρ containing parameterized characters (p-characters). A pattern P (also over Σ) matches an equal-length substring S of T iff the s-characters match exactly, and there exists a one-to-one function that renames the p-characters in S to that in P. The task is to find the starting positions (occurrences) of all such substrings S. Previous index [<PERSON>, STOC 1993], known as Parameterized Suffix Tree, requires Θ(n log n) bits of space, and can find all occ occurrences in time O(|P| log σ + occ), where σ = |Σ|. We introduce an η log σ + O(n)-bit index with O(|P| log σ+ occ Log η log σ) query time. At the core, lies a new BWT-like transform, which we call the Parameterized Burrows-Wheeler Transform (pBWT). The techniques are extended to obtain a succinct index for the Parameterized Dictionary Matching problem of Idury and Schaffer [CPM, 1994].", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.25"}, {"primary_key": "3865386", "vector": [], "sparse_vector": [], "title": "Geodesic Spanners for Points on a Polyhedral Terrain.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Let S be a set S of n points on a polyhedral terrain T in ℝ3, and let ∊ > 0 be a fixed constant. We prove that S admits a (2 + ∊)-spanner with O(n log n) edges with respect to the geodesic distance. This is the first spanner with constant spanning ratio and a near-linear number of edges for points on a terrain. On our way to this result, we prove that any set of n weighted points in ℝd admits an additively weighted (2 + ∊)-spanner with O(n) edges; this improves the previously best known bound on the spanning ratio (which was 5 + ∊), and almost matches the lower bound.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.161"}, {"primary_key": "3865387", "vector": [], "sparse_vector": [], "title": "A Hierarchy of Lower Bounds for Sublinear Additive Spanners.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Spanners, emulators, and approximate distance oracles can be viewed as lossy compression schemes that represent an unweighted graph metric in small space, say Õ(n1+δ) bits. There is an inherent tradeoff between the sparsity parameter δ and the stretch function f of the compression scheme, but the qualitative nature of this tradeoff has remained a persistent open problem.It has been known for some time that when δ > 1/3 there are schemes with constant additive stretch (distance d is stretched to at most f (d) = d + O(1)), and recent results of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> show that when δ < 1/3 there are no such schemes. Thus, to get practically efficient graph compression with δ → 0 we must pay super-constant additive stretch, but exactly how much do we have to pay?In this paper we show that the lower bound of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> is just the first step in a hierarchy of lower bounds that characterize the asymptotic behavior of the optimal stretch function f for sparsity parameter δ ∊ (0,1/3). Specifically, for any integer k ≥ 2, any compression scheme with size has a sublinear additive stretch function f:This lower bound matches <PERSON><PERSON> and <PERSON><PERSON>'s (2006) construction of sublinear additive emulators. It also shows that <PERSON><PERSON> and <PERSON><PERSON><PERSON>'s (1 + ∊, ß)-spanners have an essentially optimal tradeoff between δ, ∊, and β, and that the sublinear additive spanners of <PERSON><PERSON> (2009) and <PERSON><PERSON><PERSON> (2013) are not too far from optimal. To complement these lower bounds we present a new construction of (1 + ∊, O(k/ ∊)k—1)-spanners with size where hk < 3/4. This size bound improves on the spanners of <PERSON>kin and <PERSON>eleg (2004), <PERSON>up and <PERSON>wick (2006), and Pet- tie (2009). According to our lower bounds neither the size nor stretch function can be substantially improved.Our lower bound technique exhibits several interesting degrees of freedom in the framework of Abboud and Bodwin. By carefully exploiting these freedoms, we are able to obtain lower bounds for several related combinatorial objects. We get lower bounds on the size of (β, ∊)-hopsets, matching Elkin and Neiman's construction (2016), and lower bounds on shortcut- ting sets for digraphs that preserve the transitive closure. Our lower bound simplifies Hesse's (2003) refutation of Thorup's conjecture (1992), which stated that adding a linear number of shortcuts suffices to reduce the diameter to polylogarithmic. Finally, we show matching upper and lower bounds for graph compression schemes that work for graph metrics with girth at least 2γ + 1. One consequence is that Baswana et al.'s (2010) additive O(7)-spanners with size cannot be improved in the exponent.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.36"}, {"primary_key": "3865388", "vector": [], "sparse_vector": [], "title": "Three Colors Suffice: Conflict-Free Coloring of Planar Graphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A conflict-free k-coloring of a graph assigns one of k different colors to some of the vertices such that, for every vertex v, there is a color that is assigned to exactly one vertex among v and v's neighbors. Such colorings have applications in wireless networking, robotics, and geometry, and are well-studied in graph theory. Here we study the natural problem of the conflict-free chromatic number xCF(G) (the smallest k for which conflict-free k-colorings exist), with a focus on planar graphs.For general graphs, we prove the conflict-free variant of the famous Hadwiger Conjecture: If G does not contain Kk+1 as a minor, then xCF(G) < k. For planar graphs, we obtain a tight worst-case bound: three colors are sometimes necessary and always sufficient. In addition, we give a complete characterization of the algorithmic/computational complexity of conflict-free coloring. It is NP-complete to decide whether a planar graph has a conflict-free coloring with one color, while for outer- planar graphs, this can be decided in polynomial time. Furthermore, it is NP-complete to decide whether a planar graph has a conflict-free coloring with two colors, while for outerplanar graphs, two colors always suffice. For the bicriteria problem of minimizing the number of colored vertices subject to a given bound k on the number of colors, we give a full algorithmic characterization in terms of complexity and approximation for outerplanar and planar graphs.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.127"}, {"primary_key": "3865389", "vector": [], "sparse_vector": [], "title": "Fully dynamic all-pairs shortest paths with worst-case update-time revisited.", "authors": ["<PERSON><PERSON> Abraham", "<PERSON><PERSON>", "<PERSON>"], "summary": "We revisit the classic problem of dynamically maintaining shortest paths between all pairs of nodes of a directed weighted graph. The allowed updates are insertions and deletions of nodes and their incident edges. We give worst-case guarantees on the time needed to process a single update (in contrast to related results, the update time is not amortized over a sequence of updates). Our main result is a simple randomized algorithm that for any parameter $c>1$ has a worst-case update time of $O(cn^{2+2/3} \\log^{4/3}{n})$ and answers distance queries correctly with probability $1-1/n^c$, against an adaptive online adversary if the graph contains no negative cycle. The best deterministic algorithm is by <PERSON><PERSON> [STOC 2005] with a worst-case update time of $\\tilde O(n^{2+3/4})$ and assumes non-negative weights. This is the first improvement for this problem for more than a decade. Conceptually, our algorithm shows that randomization along with a more direct approach can provide better bounds.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.28"}, {"primary_key": "3865390", "vector": [], "sparse_vector": [], "title": "Sample-Optimal Density Estimation in Nearly-Linear Time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We design a new, fast algorithm for agnostically learning univariate probability distributions whose densities are well-approximated by piecewise polynomial functions. Let ƒ be the density function of an arbitrary univariate distribution, and suppose that ƒ is OPT-close in Li- distance to an unknown piecewise polynomial function with t interval pieces and degree d. For any γ > 0, our algorithm draws n = Õγ(t(d + 1)/ ∊2) samples from ƒ, runs in time Õ(n), and with probability at least 9/10 outputs an Ογ (t)-piecewise degree-d hypothesis H that is (3 + γ) · OPT + ∊ close to f. Our approximation factor almost matches the best known information-theoretic (but computationally inefficient) upper bound of 3.Our general algorithm yields (nearly) sample- optimal and nearly-linear time estimators for a wide range of structured distribution families over both continuous and discrete domains in a unified way. For most of our applications, these are the first sample-optimal and nearly-linear time estimators in the literature. As a consequence, our work resolves the sample and computational complexities of a broad class of inference tasks via a single “meta-algorithm”. Moreover, we demonstrate that our algorithm performs very well in experiments.Our algorithm consists of three levels: (i) At the top level, we employ an iterative greedy algorithm for finding a good partition of the real line into the pieces of a piecewise polynomial. (ii) For each piece, we show that the sub-problem of finding a good polynomial fit on the current interval can be solved efficiently with a separation oracle method. (iii) We reduce the task of finding a separating hyperplane to a combinatorial problem and design a nearly-linear algorithm for this problem. Combining these three procedures gives a density estimation algorithm with the claimed guarantees.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.83"}, {"primary_key": "3865391", "vector": [], "sparse_vector": [], "title": "Beating Approximation Factor Two for Weighted Tree Augmentation with Bounded Costs.", "authors": ["<PERSON>"], "summary": "The Weighted Tree Augmentation Problem (WTAP) is a fundamental well-studied problem in the field of network design. Given an undirected tree G = (V,E), an additional set of edges L ⊆ V × V disjoint from E called links and a cost vector WTAP asks to find a minimum-cost set F c L with the property that (V, E ∪ F) is 2-edge connected. The special case where cℓ = 1 for all ℓ ℓ L is called the Tree Augmentation Problem (TAP).For the class of bounded cost vectors, we present a first improved approximation algorithm for WTAP since more than three decades. Concretely, for any M G r≥1 and ∊ > 0, we present an LP based (δ + ∊)-approximation for WTAP restricted to cost vectors c in [1,M]L for δ ≈ 1.96417. More generally, our result is a (δ + ∊)-approximation algorithm with running time where r = cmax/cmin is the ratio between the largest and the smallest cost of any link. For the special case of TAP we improve this factor to | + e.Our results rely on several new ideas, including a new LP relaxation of WTAP and a two-phase rounding algorithm. In the first phase, the algorithm uses the fractional LP solution to guide a simple decomposition method that breaks the tree into well-structured trees and equips each tree with a part of the fraction LP solution. In the second phase, the fractional solution in each part of the decomposition is rounded to an integral solution with two rounding procedures, and the best outcome is included in the solution. One rounding procedure exploits the constraints in the new LP, while the other one exploits a connection to the Edge Cover Problem. We show that both procedures can not have a bad approximation guarantee simultaneously to obtain the claimed approximation factor.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.157"}, {"primary_key": "3865392", "vector": [], "sparse_vector": [], "title": "Firefighting on Trees Beyond Integrality Gaps.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The Firefighter problem and a variant of it, known as Resource Minimization for Fire Containment (RMFC), are natural models for optimal inhibition of harmful spreading processes. Despite considerable progress on several fronts, the approximability of these problems is still badly understood. This is the case even when the underlying graph is a tree, which is one of the most- studied graph structures in this context and the focus of this paper. In their simplest version, a fire spreads from one fixed vertex step by step from burning to adjacent non-burning vertices, and at each time step B many non-burning vertices can be protected from catching fire. The Firefighter problem asks, for a given B, to maximize the number of vertices that will not catch fire, whereas RMFC (on a tree) asks to find the smallest B that allows for saving all leaves of the tree. Prior to this work, the best known approximation ratios were an O(1)-approximation for the Firefighter problem and an O(log* n)-approximation for RMFC, both being LP-based and essentially matching the integrality gaps of two natural LP relaxations.We improve on both approximations by presenting a PTAS for the Firefighter problem and an O(1)-approximation for RMFC, both qualitatively matching the known hardness results. Our results are obtained through a combination of the known LPs with several new techniques, which allow for efficiently enumerating over super-constant size sets of constraints to strengthen the natural LPs.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.156"}, {"primary_key": "3865393", "vector": [], "sparse_vector": [], "title": "Cross-Referenced Dictionaries and the Limits of Write Optimization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Tsai"], "summary": "Dictionaries remain the most well studied class of data structures. A dictionary supports insertions, deletions, membership queries, and usually successor, predecessor, and extract-min. In a RAM, all such operations take O(logN) time on N elements. Dictionaries are often cross-referenced as follows. Con- sider a set of tuples fhai; bi; ci : : :ig. A database might in- clude more than one dictionary on such a set, for example, one indexed on the a's, another on the b's, and so on. Once again, in a RAM, inserting into a set of L cross-referenced dictionaries takes O(LlogN) time, as does deleting. The situation is more interesting in external memory. On a Disk Access Machine (DAM), B-trees achieve O(logB N) I/Os for insertions and deletions on a single dictionary and K-element range queries take optimal O(logB N + K=B) I/Os. These bounds are also achievable by a B-tree on cross-referenced dictionaries, with a slowdown of an L factor on insertion and deletions. In recent years, both the theory and practice of external- memory dictionaries has been revolutionized by write- optimization techniques. A dictionary is write optimized if it is close to a B-tree for query time while beating B- trees on insertions. The best (and optimal) dictionaries achieve a substantially improved insertion and deletion cost of, amortized I/Os on a single dictionary while maintaining optimal O(log1+B\" N +K=B)- I/O range queries. Although write optimization still helps for insertions into cross-referenced dictionaries, its value for deletions would seem to be greatly reduced. A deletion into a cross- referenced dictionary only specifies a key a. It seems to be necessary to look up the associated values b; c : : : in order to delete them from the other dictionaries. This takes (logB N) I/Os, well above the per-dictionary write- optimization budget of O( So the total deletion cost is O(logB N + L In short, for deletions, write optimization offers an ad- vantage over B-trees in that L multiplies a lower order term, but when L = 2, write optimization seems to offer no asymp- totic advantage over B-trees. That is, no known query- optimal solution for pairs of cross-referenced dictionaries seem to beat B-trees for deletions. In this paper, we show a lower bound establishing that a pair of cross-referenced dictionaries that are optimal for range queries and that supports deletions cannot match the write optimization bound available to insert-only dictionar- ies. This result thus establishes a limit to the applicability of write-optimization techniques on which many new databases and file systems are based.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.99"}, {"primary_key": "3865394", "vector": [], "sparse_vector": [], "title": "Feedback Vertex Set Inspired Kernel for Chordal Vertex Deletion.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Pranaben<PERSON> Mi<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Given a graph G and a parameter k, the Chordal Vertex Deletion (CVD) problem asks whether there exists a subset U ⊆ V (G) of size at most k that hits all induced cycles of size at least 4. The existence of a polynomial kernel for CVD was a well-known open problem in the field of Parameterized Complexity. Recently, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> resolved this question affirmatively by designing a polynomial kernel for CVD of size O(k161 log58 k), and asked whether one can design a kernel of size O(k10). While we do not completely resolve this question, we design a significantly smaller kernel of size O(k25 log14 k), inspired by the O(k2)-size kernel for Feedback Vertex Set. To obtain this result, we first design an O(opt-log2 n)-factor approximation algorithm for CVD, which is central to our kernelization procedure. Thus, we improve upon both the kernelization algorithm and the approximation algorithm of <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>. Next, we introduce the notion of the independence degree of a vertex, which is our main conceptual contribution. We believe that this notion could be useful in designing kernels for other problems.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.90"}, {"primary_key": "3865395", "vector": [], "sparse_vector": [], "title": "Parameter-free Locality Sensitive Hashing for Spherical Range Reporting.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present a data structure for *spherical range reporting* on a point set S, i.e., reporting all points in S that lie within radius r of a given query point q. Our solution builds upon the Locality-Sensitive Hashing (LSH) framework of Indyk and Motwani, which represents the asymptotically best solutions to near neighbor problems in high dimensions. While traditional LSH data structures have several parameters whose optimal values depend on the distance distribution from q to the points of S, our data structure is parameter-free, except for the space usage, which is configurable by the user. Nevertheless, its expected query time basically matches that of an LSH data structure whose parameters have been *optimally chosen for the data and query* in question under the given space constraints. In particular, our data structure provides a smooth trade-off between hard queries (typically addressed by standard LSH) and easy queries such as those where the number of points to report is a constant fraction of S, or where almost all points in S are far away from the query point. In contrast, known data structures fix LSH parameters based on certain parameters of the input alone. The algorithm has expected query time bounded by O(t(n/t)ρ), where t is the number of points to report and ρ∈(0,1) depends on the data distribution and the strength of the LSH family used. We further present a parameter-free way of using multi-probing, for LSH families that support it, and show that for many such families this approach allows us to get expected query time close to O(nρ+t), which is the best we can hope to achieve using LSH. The previously best running time in high dimensions was Ω(tnρ). For many data distributions where the intrinsic dimensionality of the point set close to q is low, we can give improved upper bounds on the expected query time.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.16"}, {"primary_key": "3865396", "vector": [], "sparse_vector": [], "title": "Time-Space Trade-offs in Population Protocols.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Population protocols are a popular model of distributed computing, in which randomly-interacting agents with little computational power cooperate to jointly perform computational tasks. Inspired by developments in molecular computation, and in particular DNA computing, recent algorithmic work has focused on the complexity of solving simple yet fundamental tasks in the population model, such as leader election (which requires convergence to a single agent in a special “leader” state), and majority (in which agents must converge to a decision as to which of two possible initial states had higher initial count). Known results point towards an inherent trade-off between the time complexity of such algorithms, and the space complexity, i.e. size of the memory available to each agent.In this paper, we explore this trade-off and provide new upper and lower bounds for majority and leader election. First, we prove a unified lower bound, which relates the space available per node with the time complexity achievable by a protocol: for instance, our result implies that any protocol solving either of these tasks for n agents using O(log log n) states must take Ω(n/polylogn) expected time. This is the first result to characterize time complexity for protocols which employ super-constant number of states per node, and proves that fast, poly-logarithmic running times require protocols to have relatively large space costs.On the positive side, we give algorithms showing that fast, poly-logarithmic convergence time can be achieved using O (log2 n) space per node, in the case of both tasks. Overall, our results highlight a time complexity separation between O (log log n) and Θ(log2 n) state space size for both majority and leader election in population protocols, and introduce new techniques, which should be applicable more broadly.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.169"}, {"primary_key": "3865397", "vector": [], "sparse_vector": [], "title": "Optimal induced universal graphs for bounded-degree graphs.", "authors": ["Noga Alon", "<PERSON><PERSON>"], "summary": "We show that for any constant Δ ≥ 2, there exists a graph Γ with O(nΔ/2) vertices which contains every n-vertex graph with maximum degree Δ as an induced subgraph. For odd Δ this significantly improves the best-known earlier bound of <PERSON><PERSON><PERSON> et al. and is optimal up to a constant factor, as it is known that any such graph must have at least Ω(nΔ/2) vertices.Our proof builds on the approach of <PERSON><PERSON> and <PERSON><PERSON><PERSON> (SODA 2008) together with several additional ingredients. The construction of Γ is explicit and is based on an appropriately defined composition of high-girth expander graphs. The proof also provides an efficient deterministic procedure for finding, for any given input graph H on n vertices with maximum degree at most Δ, an induced subgraph of Γ isomorphic to H.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.74"}, {"primary_key": "3865398", "vector": [], "sparse_vector": [], "title": "Optimal Hashing-based Time-Space Trade-offs for Approximate Near Neighbors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Ilya <PERSON>", "<PERSON>"], "summary": "[See the paper for the full abstract.] We show tight upper and lower bounds for time-space trade-offs for the $c$-Approximate Near Neighbor Search problem. For the $d$-dimensional Euclidean space and $n$-point datasets, we develop a data structure with space $n^{1 + \\rho_u + o(1)} + O(dn)$ and query time $n^{\\rho_q + o(1)} + d n^{o(1)}$ for every $\\rho_u, \\rho_q \\geq 0$ such that: \\begin{equation} c^2 \\sqrt{\\rho_q} + (c^2 - 1) \\sqrt{\\rho_u} = \\sqrt{2c^2 - 1}. \\end{equation} This is the first data structure that achieves sublinear query time and near-linear space for every approximation factor $c > 1$, improving upon [<PERSON><PERSON><PERSON>, PODS 2015]. The data structure is a culmination of a long line of work on the problem for all space regimes; it builds on Spherical Locality-Sensitive Filtering [Becker, Du<PERSON>, Gama, Laarhoven, SODA 2016] and data-dependent hashing [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SOD<PERSON> 2014] [<PERSON><PERSON>, <PERSON>, STOC 2015]. Our matching lower bounds are of two types: conditional and unconditional. First, we prove tightness of the whole above trade-off in a restricted model of computation, which captures all known hashing-based approaches. We then show unconditional cell-probe lower bounds for one and two probes that match the above trade-off for $\\rho_q = 0$, improving upon the best known lower bounds from [Panigrahy, Talwar, Wieder, FOCS 2010]. In particular, this is the first space lower bound (for any static data structure) for two probes which is not polynomially smaller than the one-probe bound. To show the result for two probes, we establish and exploit a connection to locally-decodable codes.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.4"}, {"primary_key": "3865399", "vector": [], "sparse_vector": [], "title": "LSH Forest: Practical Algorithms Made Theoretical.", "authors": ["<PERSON><PERSON><PERSON>", "Ilya <PERSON>", "Negev <PERSON>"], "summary": "We analyze LSH Forest [BCG05]—a popular heuristic for the nearest neighbor search—and show that a careful yet simple modification of it outperforms “vanilla” LSH algorithms. The end result is the first instance of a simple, practical algorithm that provably leverages data-dependent hashing to improve upon data-oblivious LSH.Here is the entire algorithm for the d-dimensional Hamming space. The LSH Forest, for a given dataset, applies a random permutation to all the d coordinates, and builds a trie on the resulting strings. In our modification, we further augment this trie: for each node, we store a constant number of points close to the mean of the corresponding subset of the dataset, which are compared to any query point reaching that node. The overall data structure is simply several such tries sampled independently.While the new algorithm does not quantitatively improve upon the best data-dependent hashing algorithms from [AR15] (which are known to be optimal), it is significantly simpler, being based on a practical heuristic, and is provably better than the best LSH algorithm for the Hamming space [IM98, HIM12].", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.5"}, {"primary_key": "3865400", "vector": [], "sparse_vector": [], "title": "Algorithmic and Hardness Results for the Hub Labeling Problem.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Vsevolod Oparin"], "summary": "There has been significant success in designing highly efficient algorithms for distance and shortest-path queries in recent years; many of the state-of-the-art algorithms use the hub labeling framework. In this paper, we study the approximability of the Hub Labeling problem. We prove a hardness of Ω(logn) for Hub Labeling, matching known approximation guarantees. The hardness result applies to graphs that have multiple shortest paths between some pairs of vertices. No hardness of approximation results were known previously. Then, we focus on graphs that have a unique shortest path between each pair of vertices. This is a very natural family of graphs, and much research on the Hub Labeling problem has studied such graphs. We give an O (log D) approximation algorithm for graphs of shortest-path diameter D with unique shortest paths. In particular, we get an O(loglog n) approximation for graphs of polylogarithmic diameter, while previously known algorithms gave an O(log n) approximation. Finally, we present a polynomial-time approximation scheme (PTAS) and quasi-polynomial-time algorithms for Hub Labeling on trees; additionally, we analyze a simple combinatorial heuristic for Hub Labeling on trees, proposed by <PERSON><PERSON>g in 2000. We show that this heuristic gives an approximation factor of 2.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.94"}, {"primary_key": "3865401", "vector": [], "sparse_vector": [], "title": "Eliminating Depth Cycles among Triangles in Three Dimensions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Given n non-vertical pairwise disjoint triangles in 3-space, their vertical depth (above/below) relation may contain cycles. We show that, for any ∊ > 0, the triangles can be cut into O(n3/2+∊) pieces, where each piece is a connected semi-algebraic set whose description complexity depends only on the choice of ∊, such that the depth relation among these pieces is now a proper partial order. This bound is nearly tight in the worst case. We are not aware of any previous study of this problem with a subquadratic bound on the number of pieces.This work extends the recent study by two of the authors on eliminating depth cycles among lines in 3-space. Our approach is again algebraic, and makes use of a recent variant of the polynomial partitioning technique, due to <PERSON><PERSON>, which leads to a recursive procedure for cutting the triangles. In contrast to the case of lines, our analysis here is considerably more involved, due to the two-dimensional nature of the objects being cut, so additional tools, from topology and algebra, need to be brought to bear.Our result essentially settles a 35-year-old open problem in computational geometry, motivated by hidden-surface removal in computer graphics.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.164"}, {"primary_key": "3865402", "vector": [], "sparse_vector": [], "title": "Optimal Approximate Polytope Membership.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Fonseca", "<PERSON>"], "summary": "In the polytope membership problem, a convex polytope $K$ in $R^d$ is given, and the objective is to preprocess $K$ into a data structure so that, given a query point $q \\in R^d$, it is possible to determine efficiently whether $q \\in K$. We consider this problem in an approximate setting and assume that $d$ is a constant. Given an approximation parameter $\\varepsilon &gt; 0$, the query can be answered either way if the distance from $q$ to $K$'s boundary is at most $\\varepsilon$ times $K$'s diameter. Previous solutions to the problem were on the form of a space-time trade-off, where logarithmic query time demands $O(1/\\varepsilon^{d-1})$ storage, whereas storage $O(1/\\varepsilon^{(d-1)/2})$ admits roughly $O(1/\\varepsilon^{(d-1)/8})$ query time. In this paper, we present a data structure that achieves logarithmic query time with storage of only $O(1/\\varepsilon^{(d-1)/2})$, which matches the worst-case lower bound on the complexity of any $\\varepsilon$-approximating polytope. Our data structure is based on a new technique, a hierarchy of ellipsoids defined as approximations to Macbeath regions. As an application, we obtain major improvements to approximate Euclidean nearest neighbor searching. Notably, the storage needed to answer $\\varepsilon$-approximate nearest neighbor queries for a set of $n$ points in $O(\\log \\frac{n}{\\varepsilon})$ time is reduced to $O(n/\\varepsilon^{d/2})$. This halves the exponent in the $\\varepsilon$-dependency of the existing space bound of roughly $O(n/\\varepsilon^d)$, which has stood for 15 years (Har-Peled, 2001).", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.18"}, {"primary_key": "3865403", "vector": [], "sparse_vector": [], "title": "On Estimating Maximum Matching Size in Graph Streams.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the problem of estimating the maximum matching size in graphs whose edges are revealed in a streaming manner. We consider both insertion-only streams, which only contain edge insertions, and dynamic streams that allow both insertions and deletions of the edges, and present new upper and lower bound results for both cases.On the upper bound front, we show that an α- approximate estimate of the matching size can be computed in dynamic streams using Õ(n2/a4) space, and in insertion-only streams using Õ(n/a2)-space. These bounds respectively shave off a factor of α from the space necessary to compute an α-approximate matching (as opposed to only size), thus proving a non-trivial separation between approximate estimation and approximate computation of matchings in data streams.On the lower bound front, we prove that any α- approximation algorithm for estimating matching size in dynamic graph streams requires bits of space, even if the underlying graph is both sparse and has arboricity bounded by O (α). We further improve our lower bound to Ω(n/α2) in the case of dense graphs. These results establish the first non-trivial streaming lower bounds for super- constant approximation of matching size.Furthermore, we present the first super-linear space lower bound for computing a (1 + ∊)-approximation of matching size even in insertion-only streams. In particular, we prove that a (1 + ∊)-approximation to matching size requires RS(n) · η1–0(∊) space; here, RS(n) denotes the maximum number of edge-disjoint induced matchings of size Θ(n) in an n-vertex graph. It is a major open problem with far-reaching implications to determine the value of RS(n), and current results leave open the possibility that RS(n) may be as large as n/logn. Moreover, using the best known lower bounds for RS(n), our result already rules out any O(n · poly(log n/e))-space algorithm for (1 + ∊)- approximation of matchings. We also show how to avoid the dependency on the parameter RS(n) in proving lower bound for dynamic streams and present a near-optimal lower bound of n2–0(£) for (1 + ∊)-approximation in this model.Using a well-known connection between matching size and matrix rank, all our lower bounds also hold for the problem of estimating matrix rank. In particular our results imply a near-optimal n2–0(£) bit lower bound for (1 + ∊)- approximation of matrix ranks for dense matrices in dynamic streams, answering an open question of Li and Woodruff (STOC 2016).", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.113"}, {"primary_key": "3865404", "vector": [], "sparse_vector": [], "title": "High-dimensional approximate r-nets.", "authors": ["Georgia Avarikioti", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The construction of r-nets offers a powerful tool in computational and metric geometry. We focus on high- dimensional spaces and present a new randomized algorithm which efficiently computes approximate r-nets with respect to Euclidean distance. For any fixed ∊ > 0, the approximation factor is 1 + ∊ and the complexity is polynomial in the dimension and subquadratic in the number of points. The algorithm succeeds with high probability. Specifically, we improve upon the best previously known (LSH- based) construction of <PERSON><PERSON><PERSON> et al. [EHS15] in terms of complexity, by reducing the dependence on ∊, provided that ∊ is sufficiently small. Our method does not require LSH but, instead, follows <PERSON><PERSON>'s [Val15] approach in designing a sequence of reductions of our problem to other problems in different spaces, under Euclidean distance or inner product, for which r-nets are computed efficiently and the error can be controlled. Our result immediately implies efficient solutions to a number of geometric problems in high dimension, such as finding the (1 + ∊)-approximate kth nearest neighbor distance in time subquadratic in the size of the input.MSC codesMetric geometryHigh dimensionApproximation algorithmsr-netsLocality-sensitive hashing", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.2"}, {"primary_key": "3865405", "vector": [], "sparse_vector": [], "title": "Polylogarithmic Bounds on the Competitiveness of Min-cost Perfect Matching with Delays.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the problem of online Min-cost Perfect Matching with Delays (MPMD) recently introduced by <PERSON><PERSON> et al, (STOC 2016). This problem is defined on an underlying n-point metric space. An adversary presents real-time requests online at points of the metric space, and the algorithm is required to match them, possibly after keeping them waiting for some time. The cost incurred is the sum of the distances between matched pairs of requests (the connection cost), and the sum of the waiting times of the requests (the delay cost). We prove the first logarithmic upper bound and the first polylogarithmic lower bound on the randomized competitive ratio of this problem. We present an algorithm with a competitive ratio of O(log n), which improves the upper bound of O log2 n + logΔ) of <PERSON><PERSON> et al, by removing the dependence on Δ, the aspect ratio of the metric space (which can be unbounded as a function of n). The core of our algorithm is a deterministic algorithm for MPMD on metrics induced by edge-weighted trees of height h, whose cost is guaranteed to be at most O(1) times the connection cost plus O(h) times the delay cost of every feasible solution. The reduction from MPMD on arbitrary metrics to MPMD on trees is achieved using the result on embedding n-point metric spaces into distributions over weighted hierarchically separated trees of height O(log n), with distortion O(log n). We also prove a lower bound of on the competitive ratio of any randomized algorithm. This is the first lower bound which increases with n, and is attained on the metric of n equally spaced points on a line.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.67"}, {"primary_key": "3865406", "vector": [], "sparse_vector": [], "title": "Online Lower Bounds via Duality.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we exploit linear programming duality in the online setting, where input arrives on the fly, from the unique perspective of designing lower bounds (i.e., hardness results) on the competitive ratio. In particular, we provide a systematic method (as opposed to ad hoc case analysis that is typically done) for obtaining online deterministic and randomized lower bounds on the competitive ratio for a wide variety of problems. We show the usefulness of our approach by providing new, tight hardness results for three diverse online problems: the Vector Bin Packing problem, Ad-auctions (and various online matching problems), and the Capital Investment problem. Our methods are sufficiently general that they can also be used to reconstruct existing lower bounds.Our approach is in stark contrast to previous works, which exploit linear programming duality to obtain positive results, often via the useful primal- dual scheme. We design a general recipe with the opposite aim of obtaining negative results via duality. The general idea behind our approach is to construct a parameterized family of primal linear programs based on a candidate collection of input sequences for proving the lower bound, where the objective function corresponds to optimizing the competitive ratio. Solving the parameterized family of primal linear programs optimally would yield a valid lower bound, but is a challenging task and limits the tools that can be applied, since analysis must be done precisely and exact optimality needs to be proved. To this end, we consider the corresponding parameterized family of dual linear programs and provide feasible solutions, where the objective function yields a lower bound on the competitive ratio. This opens up additional doors for analysis, including some of the techniques we employ (e.g., continuous analysis, differential equations, etc.), as we need not be so careful about exact optimality. We are confident that our methods can be successfully applied to produce many more lower bounds for a wide array of online problems.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.66"}, {"primary_key": "3865407", "vector": [], "sparse_vector": [], "title": "Better Approximations for Tree Sparsity in Nearly-Linear Time.", "authors": ["Arturs Backurs", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The Tree Sparsity problem is defined as follows: given a node-weighted tree of size η and an integer k, output a rooted subtree of size k with maximum weight. The best known algorithm solves this problem in time O(kn), i.e., quadratic in the size of the input tree for k = Θ(n).In this work, we design (1+∊)-approximation algorithms for the Tree Sparsity problem that run in nearly-linear time. Unlike prior algorithms for this problem, our results offer single criterion approximations, i.e., they do not increase the sparsity of the output solution, and work for arbitrary trees (not only balanced trees). We also provide further algorithms for this problem with different runtime vs approximation trade-offs.Finally, we show that if the exact version of the Tree Sparsity problem can be solved in strongly subquadratic time, then the (min, +) convolution problem can be solved in strongly subquadratic time as well. The latter is a well- studied problem for which no strongly subquadratic time algorithm is known.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.145"}, {"primary_key": "3865408", "vector": [], "sparse_vector": [], "title": "LP-Based Robust Algorithms for Noisy Minor-Free and Bounded Treewidth Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We give a general approach for solving optimization problems on noisy minor free and bounded treewidth graphs, where a fraction of edges are adversarially corrupted. The noisy setting was first considered by <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> and they gave a (1 + ∊)-estimation algorithm for the independent set problem. Later, <PERSON> and <PERSON><PERSON> designed a local search algorithm that finds a (1 + ∊)-approximate independent set. However, nothing was known regarding other problems in the noisy setting. Our main contribution is a general LP-based framework that yields (1 + ∊)-approximation algorithms for noisy MAX-k-CSPs.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.128"}, {"primary_key": "3865409", "vector": [], "sparse_vector": [], "title": "The (h, k)-Server Problem on Bounded Depth Trees.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Lukasz Jez", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We study the k-server problem in the resource augmentation setting i.e., when the performance of the online algorithm with k servers is compared to the offline optimal solution with H ≤ k servers. The problem is very poorly understood beyond uniform metrics. For this special case, the classic k-server algorithms are roughly (1 + 1/∊)-competitive when k = (1 + ∊)h, for any ∊ > 0. Surprisingly however, no o(h)- competitive algorithm is known even for HSTs of depth 2 and even when k/h is arbitrarily large.We obtain several new results for the problem. First we show that the known k-server algorithms do not work even on very simple metrics. In particular, the Double Coverage algorithm has competitive ratio O(h) irrespective of the value of k, even for depth-2 HSTs. Similarly the Work Function Algorithm, that is believed to be optimal for all metric spaces when k = h, has competitive ratio O(h) on depth-3 HSTs even if k = 2h. Our main result is a new algorithm that is O(1)-competitive for constant depth trees, whenever k = (1 + ∊)h for any ∊ > 0. Finally, we give a general lower bound that any deterministic online algorithm has competitive ratio at least 2.4 even for depth-2 HSTs and when k/h is arbitrarily large. This gives a surprising qualitative separation between uniform metrics and depth-2 HSTs for the (h, k)-server problem, and gives the strongest known lower bound for the problem on general metrics.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.65"}, {"primary_key": "3865410", "vector": [], "sparse_vector": [], "title": "Small Extended Formulation for Knapsack Cover Inequalities from Monotone Circuits.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Initially developed for the min-knapsack problem, the knapsack cover inequalities are used in the current best relaxations for numerous combinatorial optimization problems of covering type. In spite of their widespread use, these inequalities yield linear programming (LP) relaxations of exponential size, over which it is not known how to optimize exactly in polynomial time. In this paper we address this issue and obtain LP relaxations of quasi-polynomial size that are at least as strong as that given by the knapsack cover inequalities.For the min-knapsack cover problem, our main result can be stated formally as follows: for any ∊ > 0, there is a (1/∊)O(1)nO(log n)-size LP relaxation with an integrality gap of at most 2 + ∊, where n is the number of items. Prior to this work, there was no known relaxation of subexponential size with a constant upper bound on the integrality gap.Our construction is inspired by a connection between extended formulations and monotone circuit complexity via Ka<PERSON>mer-<PERSON><PERSON>on games. In particular, our LP is based on O (log2 n)-depth monotone circuits with fan-in 2 for evaluating weighted threshold functions with n inputs, as constructed by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>. We believe that a further understanding of this connection may lead to more positive results complementing the numerous lower bounds recently proved for extended formulations.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.153"}, {"primary_key": "3865411", "vector": [], "sparse_vector": [], "title": "Massively-Parallel Similarity Join, Edge-Isoperimetry, and Distance Correlations on the Hypercube.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We study distributed protocols for finding all pairs of similar vectors in a large dataset. Our results pertain to a variety of discrete metrics, and we give concrete instantiations for Hamming distance. In particular, we give improved upper bounds on the overhead required for similarity defined by Hamming distance r > 1 and prove a lower bound showing qualitative optimality of the overhead required for similarity over any Hamming distance r. Our main conceptual contribution is a connection between similarity search algorithms and certain graph-theoretic quantities. For our upper bounds, we exhibit a general method for designing one-round protocols using edge-isoperimetric shapes in similarity graphs. For our lower bounds, we define a new combinatorial optimization problem, which can be stated in purely graph-theoretic terms yet also captures the core of the analysis in previous theoretical work on distributed similarity joins. As one of our main technical results, we prove new bounds on distance correlations in subsets of the Hamming cube.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.19"}, {"primary_key": "3865412", "vector": [], "sparse_vector": [], "title": "Find Your Place: Simple Distributed Algorithms for Community Detection.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Given an underlying graph, we consider the following dynamics: Initially, each node locally chooses a value in {-1,1}, uniformly at random and independently of other nodes. Then, in each consecutive round, every node updates its local value to the average of the values held by its neighbors, at the same time applying an elementary, local clustering rule that only depends on the current and the previous values held by the node.We prove that the process resulting from this dynamics produces a clustering that exactly or approximately (depending on the graph) reflects the underlying cut in logarithmic time, under various graph models that exhibit a sparse balanced cut, including the stochastic block model. We also prove that a natural extension of this dynamics performs community detection on a regularized version of the stochastic block model with multiple communities.Rather surprisingly, our results provide rigorous evidence for the ability of an extremely simple and natural dynamics to address a computational problem that is non-trivial even in a centralized setting.Distributed Algorithms, Averaging Dynamics, Community Detection, Spectral Analysis, Stochastic Block Models.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.59"}, {"primary_key": "3865413", "vector": [], "sparse_vector": [], "title": "The Identity Problem for Matrix Semigroups in SL2(ℤ) is NP-complete.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we show that the problem of determining if the identity matrix belongs to a finitely generated semigroup of 2 × 2 matrices from the modular group PSL2(ℤ) and thus the Special Linear group SL2(ℤ) is solvable in NP. From this fact, we can immediately derive that the fundamental problem of whether a given finite set of matrices from SL2(ℤ) or PSL2(ℤ) generates a group or free semigroup is also decidable in NP. The previous algorithm for these problems, shown in 2005 by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, was in EXPSPACE mainly due to the translation of matrices into exponentially long words over a binary alphabet {s, r} and further constructions with a large nondeterministic finite state automaton that is built on these words. Our algorithm is based on various new techniques that allow us to operate with compressed word representations of matrices without explicit expansions. When combined with the known NP-hard lower bound, this proves that the membership problem for the identity problem, the group problem and the freeness problem in SL2 (ℤ) are NP-complete.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.13"}, {"primary_key": "3865414", "vector": [], "sparse_vector": [], "title": "File Maintenance: When in Doubt, Change the Layout!", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper gives a new deamortized solution to the sequential-file-maintenance problem. The data structure uses several new tools for solving this historically complicated problem. These tools include an unbalanced ternary-tree layout embedded in the sparse table, one-way rebalancing, and extra structural properties to keep interaction among rebalances to a minimum.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.98"}, {"primary_key": "3865415", "vector": [], "sparse_vector": [], "title": "Optimization of Bootstrapping in Circuits.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In 2009, Gentry proposed the first Fully Homomorphic Encryption (FHE) scheme, an extremely powerful cryptographic primitive that enables to perform computations, i.e., to evaluate circuits, on encrypted data without decrypting them first. This has many applications, particularly in cloud computing.In all currently known FHE schemes, encryptions are associated with some (non-negative integer) noise level. At each evaluation of an AND gate, this noise level increases. This increase is problematic because decryption succeeds only if the noise level stays below some maximum level L at every gate of the circuit. To ensure that property, it is possible to perform an operation called bootstrapping to reduce the noise level. Though critical, boostrapping is a time-consuming operation. This expense motivates a new problem in discrete optimization: minimizing the number of bootstrappings in a circuit while still controlling the noise level.In this paper, we (1) formally define the bootstrap problem, (2) design a polynomial-time L-approximation algorithm using a novel method of rounding of a linear program, and (3) show a matching hardness result: (L — ∊)- inapproximability for any ∊ > 0.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.160"}, {"primary_key": "3865416", "vector": [], "sparse_vector": [], "title": "Linear Diophantine Equations, Group CSPs, and Graph Isomorphism.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In recent years, we have seen several approaches to the graph isomorphism problem based on \"generic\" mathematical programming or algebraic (<PERSON><PERSON><PERSON> basis) techniques. For most of these, lower bounds have been established. In fact, it has been shown that the pairs of non-isomorphic CFI-graphs (introduced by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> in 1992 as hard examples for the combinatorial <PERSON><PERSON><PERSON><PERSON><PERSON> algorithm) cannot be distinguished by these mathematical algorithms. A notable exception were the algebraic algorithms over the field 2, for which no lower bound was known. Another, in some way even stronger, approach to graph isomorphism testing is based on solving systems of linear Diophantine equations (that is, linear equations over the integers), which is known to be possible in polynomial time. So far, no lower bounds for this approach were known.Lower bounds for the algebraic algorithms can best be proved in the framework of proof complexity, where they can be phrased as lower bounds for algebraic proof systems such as Nullstellensatz or the (more powerful) polynomial calculus. We give new hard examples for these systems: families of pairs of non-isomorphic graphs that are hard to distinguish by polynomial calculus proofs simultaneously over all prime fields, including 2, as well as examples that are hard to distinguish by the systems-of-linear-Diophantine- equations approach.In a previous paper, we observed that the CFI-graphs are closely related to what we call \"group CSPs\": constraint satisfaction problems where the constraints are membership tests in some coset of a subgroup of a cartesian power of a base group (ℤ2 in the case of the classical CFI-graphs). Our new examples are also based on group CSPs (for Abelian groups), but here we extend the CSPs by a few non-group constraints to obtain even harder instances for graph isomorphism.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.21"}, {"primary_key": "3865417", "vector": [], "sparse_vector": [], "title": "Deterministic Partially Dynamic Single Source Shortest Paths for Sparse Graphs.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we consider the decremental single-source shortest paths (SSSP) problem, where given a graph G and a source node s the goal is to maintain shortest paths between s and all other nodes in G under a sequence of online adversarial edge deletions. (Our algorithm can also be modified to work in the incremental setting, where the graph is initially empty and subject to a sequence of online adversarial edge insertions.)In their seminal work, <PERSON> <PERSON> <PERSON><PERSON><PERSON> [JACM 1981] presented an exact solution to the problem with only O (mn) total update time over all edge deletions. Later papers presented conditional lower bounds showing that O(mn) is optimal up to log factors.In SODA 2011, <PERSON> and <PERSON><PERSON> showed how to bypass these lower bounds and improve upon the Even and Shiloach O(mn) total update time bound by allowing a (1 + ∊) approximation. This triggered a series of new results, culminating in a recent breakthrough of <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [FOCS 14], who presented a (1 + ∊)-approximate algorithm whose total update time is near linear: However, every single one of these improvements over the Even-Shiloach algorithm was randomized and assumed a non-adaptive adversary. This additional assumption meant that the algorithms were not suitable for certain settings and could not be used as a black box data structure. Very recently <PERSON> and <PERSON><PERSON><PERSON> presented in STOC 2016 the first deterministic improvement over <PERSON> and <PERSON><PERSON><PERSON>, that did not rely on randomization or assumptions about the adversary: in an undirected unweighted graph the algorithm maintains (1+ ∊)-approximate distances and has total update time O(n2).In this paper, we present a new deterministic algorithm for the problem with total update time Õ(n1.25√m) = Õ (mn3/4): it returns a (1 + ∊) approximation, and is limited to undirected unweighted graphs. Although this result is still far from matching the randomized near-linear total update time, it presents important progress towards that direction, because unlike the STOC 2016 Õ (n2) algorithm it beats the Even and Shiloach Õ (mn) bound for all graphs, not just sufficientl6y dense ones. In particular, the Õ (n2) algorithm relied entirely on a new sparsification technique, and so could not hope to yield an improvement for sparse graphs. We present the first deterministic improvement for sparse graphs by significantly extending some of the ideas from the Õ (n2) algorithm and combining them with the hop-set technique used in several earlier dynamic shortest path papers.Also, because decremental single source shortest paths is often used as a building block for fully dynamic all pairs shortest paths, using our new algorithm as a black box yields new deterministic algorithms for fully dynamic approximate all pairs shortest paths.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.29"}, {"primary_key": "3865418", "vector": [], "sparse_vector": [], "title": "Approximately Sampling Elements with Fixed Rank in Graded Posets.", "authors": ["Prateek Bhakta", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Graded posets frequently arise throughout combinatorics, where it is natural to try to count the number of elements of a fixed rank. These counting problems are often $\\#\\textbf{P}$-complete, so we consider approximation algorithms for counting and uniform sampling. We show that for certain classes of posets, biased Markov chains that walk along edges of their Hasse diagrams allow us to approximately generate samples with any fixed rank in expected polynomial time. Our arguments do not rely on the typical proofs of log-concavity, which are used to construct a stationary distribution with a specific mode in order to give a lower bound on the probability of outputting an element of the desired rank. Instead, we infer this directly from bounds on the mixing time of the chains through a method we call $\\textit{balanced bias}$. A noteworthy application of our method is sampling restricted classes of integer partitions of $n$. We give the first provably efficient Markov chain algorithm to uniformly sample integer partitions of $n$ from general restricted classes. Several observations allow us to improve the efficiency of this chain to require $O(n^{1/2}\\log(n))$ space, and for unrestricted integer partitions, expected $O(n^{9/4})$ time. Related applications include sampling permutations with a fixed number of inversions and lozenge tilings on the triangular lattice with a fixed average height.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.119"}, {"primary_key": "3865419", "vector": [], "sparse_vector": [], "title": "Fully Dynamic Approximate Maximum Matching and Minimum Vertex Cover in O(log3 n) Worst Case Update Time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Danupon <PERSON>"], "summary": "We consider the problem of maintaining an approximately maximum (fractional) matching and an approximately minimum vertex cover in a dynamic graph. Starting with the seminal paper by <PERSON><PERSON> and <PERSON> [STOC 2010], this problem has received significant attention in recent years. There remains, however, a polynomial gap between the best known worst case update time and the best known amortised update time for this problem, even after allowing for randomisation. Specifically, <PERSON> and <PERSON> [ICALP 2015, SODA 2016] have the best known worst case update time. They present a deterministic data structure with approximation ratio (3/2 + ∊) and worst case update time O(m1/4/ ∊2), where m is the number of edges in the graph. In recent past, <PERSON> and <PERSON> [FOCS 2013] gave a deterministic data structure with approximation ratio (1+ ∊) and worst case update time No known randomised data structure beats the worst case update times of these two results. In contrast, the paper by <PERSON><PERSON> and <PERSON> [STOC 2010] gave a randomised data structure with approximation ratio O(1) and amortised update time O(log2 n), where n is the number of nodes in the graph. This was later improved by <PERSON><PERSON><PERSON>, <PERSON> and <PERSON> [FOCS 2011] and <PERSON> [FOCS 2016], leading to a randomised date structure with approximation ratio 2 and amortised update time O(1).We bridge the polynomial gap between the worst case and amortised update times for this problem, without using any randomisation. We present a deterministic data structure with approximation ratio (2 + ∊) and worst case update time O(log3 n), for all sufficiently small constants ∊.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.30"}, {"primary_key": "3865420", "vector": [], "sparse_vector": [], "title": "Tight Bounds for Online TSP on the Line.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We consider the online traveling salesperson problem (TSP), where requests appear online over time on the real line and need to be visited by a server initially located at the origin. We distinguish between closed and open online TSP, depending on whether the server eventually needs to return to the origin or not. While online TSP on the line is a very natural online problem that was introduced more than two decades ago, no tight competitive analysis was known to date. We settle this problem by providing tight bounds on the competitive ratios for both the closed and the open variant of the problem. In particular, for closed online TSP, we provide a 1.64-competitive algorithm, thus matching a known lower bound. For open online TSP, we give a new upper bound as well as a matching lower bound that establish the remarkable competitive ratio of 2.04.Additionally, we consider the online Dial-A-Ride problem on the line, where each request needs to be transported to a specified destination. We provide an improved non-preemptive lower bound of 1.75 for this setting, as well as an improved preemptive algorithm with competitive ratio 2.41.Finally, we generalize known and give new complexity results for the underlying offline problems. In particular, we give an algorithm with running time O(n2) for closed offline TSP on the line with release dates and show that both variants of offline Dial-A-Ride on the line are NP-hard for any capacity c ≥ 2 of the server.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.63"}, {"primary_key": "3865421", "vector": [], "sparse_vector": [], "title": "Opting Into Optimal Matchings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Eviatar B. Procaccia", "<PERSON><PERSON><PERSON>"], "summary": "We revisit the problem of designing optimal, individually rational matching mechanisms (in a general sense, allowing for cycles in directed graphs), where each player—who is associated with a subset of vertices—matches as many of his own vertices when he opts into the matching mechanism as when he opts out. We offer a new perspective on this problem by considering an arbitrary graph, but assuming that vertices are associated with players at random. Our main result asserts that, under certain conditions, any fixed optimal matching is likely to be individually rational up to lower-order terms. We also show that a simple and practical mechanism is (fully) individually rational, and likely to be optimal up to lower-order terms. We discuss the implications of our results for market design in general, and kidney exchange in particular.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.155"}, {"primary_key": "3865423", "vector": [], "sparse_vector": [], "title": "Linear Size Distance Preservers.", "authors": ["<PERSON>"], "summary": "The famous shortest path tree lemma states that, for any node s in a graph G = (V, E), there is a subgraph on O(n) edges that preserves all distances between node pairs in the set {s} × V. A very basic question in distance sketching research, with applications to other problems in the field, is to categorize when else graphs admit sparse subgraphs that preserve distances between a set P of p node pairs, where P has some different structure than {s} × V or possibly no guaranteed structure at all. Trivial lower bounds of a path or a clique show that such a subgraph will need Ω(n + p) edges in the worst case. The question is then to determine when these trivial lower bounds are sharp; that is, when do graphs have linear size distance preservers on O(n + p) edges?In this paper, we make the first new progress on this fundamental question in over ten years. We show:1.All G, P has a distance preserver on O(n) edges whenever p = O(n1/3), even if G is directed and/or weighted. These are the first nontrivial preservers of size O(n) known for directed graphs.2.All G, P has a distance preserver on O(p) edges whenever and G is undirected and unweighted. Here, RS(n) is the Ruzsa-Sze<PERSON>i function from combinatoric graph theory. These are the first nontrivial preservers of size O(p) known in any setting.3.To preserve distances within a subset of s nodes in a graph, ω(s2) edges are sometimes needed when even if G is undirected and unweighted. For weighted graphs, the range of this lower bound improves to s = o(n2/3). This result reflects a polynomial improvement over lower bounds given by Coppersmith and Elkin (SODA '05).", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.39"}, {"primary_key": "3865424", "vector": [], "sparse_vector": [], "title": "An Efficient Representation for Filtrations of Simplicial Complexes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> C. S."], "summary": "A filtration over a simplicial complex K is an ordering of the simplices of K such that all prefixes in the ordering are subcomplexes of K. Filtrations are at the core of Persistent Homology, a major tool in Topological Data Analysis. In order to represent the filtration of a simplicial complex, the entire filtration can be appended to any data structure that explicitly stores all the simplices of the complex such as the <PERSON><PERSON> diagram or the recently introduced Simplex Tree [Algorithmica ‘14]. However, with the popularity of various computational methods that need to handle simplicial complexes, and with the rapidly increasing size of the complexes, the task of finding a compact data structure that can still support efficient queries is of great interest.This direction has been recently pursued for the case of maintaining simplicial complexes. For instance, <PERSON><PERSON><PERSON> et al. [SoCG ‘15] considered storing the simplices that are maximal for the inclusion and <PERSON><PERSON><PERSON> et al. [IJCGA ‘12] considered storing the simplices that block the expansion of the complex. Nevertheless, so far there has been no data structure that compactly stores the filtration of a simplicial complex, while also allowing the efficient implementation of basic operations on the complex.In this paper, we propose a new data structure called the Critical Simplex Diagram (CSD) which is a variant of the Simplex Array List (SAL) [SoCG ‘15]. Our data structure allows to store in a compact way the filtration of a simplicial complex, and allows for the efficient implementation of a large range of basic operations. Moreover, we prove that our data structure is essentially optimal with respect to the requisite storage space. Next, we show that the CSD representation admits the following construction algorithms.•A new edge-deletion algorithm for the fast construction of Flag complexes, which only depends on the number of critical simplices and the number of vertices.•A new matrix-parsing algorithm to quickly construct relaxed Delaunay complexes, depending only on the number of witnesses and the dimension of the complex.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.179"}, {"primary_key": "3865425", "vector": [], "sparse_vector": [], "title": "An Axiomatic and an Average-Case Analysis of Algorithms and Heuristics for Metric Properties of Graphs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In recent years, researchers proposed several algorithms that compute metric quantities of real-world complex networks, and that are very efficient in practice, although there is no worst-case guarantee.In this work, we propose an axiomatic framework to analyze the performances of these algorithms, by proving that they are efficient on the class of graphs satisfying certain properties. Furthermore, we prove that these properties are verified asymptotically almost surely by several probabilistic models that generate power law random graphs, such as the Configuration Model, the Chung-Lu model, and the Norros-Reitt<PERSON> model. Thus, our results imply average-case analyses in these models.For example, in our framework, existing algorithms can compute the diameter and the radius of a graph in subquadratic time, and sometimes even in time n1+o(1). Moreover, in some regimes, it is possible to compute the k most central vertices according to closeness centrality in subquadratic time, and to design a distance oracle with sublinear query time and subquadratic space occupancy.In the worst case, it is impossible to obtain comparable results for any of these problems, unless widely- believed conjectures are false.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.58"}, {"primary_key": "3865426", "vector": [], "sparse_vector": [], "title": "ETH Hardness for Densest-k-Subgraph with Perfect Completeness.", "authors": ["<PERSON>", "Young Kun-Ko", "<PERSON>via<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show that, assuming the (deterministic) Exponential Time Hypothesis, distinguishing between a graph with an induced k-clique and a graph in which all k-subgraphs have density at most 1 - ∊, requires time. Our result essentially matches the quasi-polynomial algorithms of <PERSON><PERSON> and <PERSON><PERSON><PERSON> [FS97] and <PERSON><PERSON> [Bar15] for this problem, and is the first one to rule out an additive PTAS for Densest k-Subgraph. We further strengthen this result by showing that our lower bound continues to hold when, in the soundness case, even subgraphs smaller by a near-polynomial factor are assumed to be at most (1 - ∊)-dense.Our reduction is inspired by recent applications of the \"birthday repetition\" technique [AIM14, BKW15]. Our analysis relies on information theoretical machinery and is similar in spirit to analyzing a parallel repetition of two- prover games in which the provers may choose to answer some challenges multiple times, while completely ignoring other challenges.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.86"}, {"primary_key": "3865427", "vector": [], "sparse_vector": [], "title": "A Near-Linear Pseudopolynomial Time Algorithm for Subset Sum.", "authors": ["<PERSON>"], "summary": "Given a set Z of n positive integers and a target value t, the SuBSETSuM problem asks whether any subset of Z sums to t. A textbook pseudopolynomial time algorithm by <PERSON><PERSON> from 1957 solves SuBSETSuM in time O(nt). This has been improved to O(nmaxZ) by <PERSON><PERSON><PERSON> [<PERSON><PERSON>'99] and recently to by <PERSON><PERSON><PERSON> and <PERSON> [SODA'17].Here we present a simple and elegant randomized algorithm running in time Õ (n+1). This improves upon a classic algorithm and is likely to be near-optimal, since it matches conditional lower bounds from SetC<PERSON>er and k-Clique.We then use our new algorithm and additional tricks to improve the best known polynomial space solution from time Õ(n3t) and space Õ(n2) to time Õ(nt) and space Õ(nlogt), assuming the Extended Riemann Hypothesis. Unconditionally, we obtain time Õ(nt1+e) and space Õ(nte) for any constant ∊ > 0.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.69"}, {"primary_key": "3865428", "vector": [], "sparse_vector": [], "title": "Partial and Constrained Level Planarity.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Let G = (V, E) be a directed graph and ℓ: V → [k] := {1,…, k} a level assignment such that ℓ(u) < ℓ(v) for all directed edges (u, v) ∊ E. A level planar drawing of G is a drawing of G where each vertex v is mapped to a unique point on the horizontal line íj with y-coordinate í(v), and each edge is drawn as a y-monotone curve between its endpoints such that no two curves cross in their interior.In the problem Constrained Level Planarity (CLP for short), we are further given a partial ordering of Vi := ℓ−1(i) for i ∊ [k], and we seek a level planar drawing where the order of the vertices on ℓi is a linear extension of A special case of this is the problem Partial Level Planarity (PLP for short), where we are asked to extend a given level-planar drawing H of a subgraph H ⊆ G to a complete drawing G of G without modifying the given drawing, i.e., the restriction of G to H must coincide with H.We give a simple polynomial-time algorithm with running time O(n5) for CLP of single-source graphs that is based on a simplified version of an existing level- planarity testing algorithm for single-source graphs. We introduce a modified type of PQ-tree data structure that is capable of efficiently handling the arising constraints to improve the running time to O(n + kℓ), where ℓ is the size of the constraints. We complement this result by showing that PLP is NP-complete even in very restricted cases. In particular, PLP remains NP- complete even when G is a subdivision of a triconnected planar graph with bounded degree.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.130"}, {"primary_key": "3865429", "vector": [], "sparse_vector": [], "title": "O(depth)-Competitive Algorithm for Online Multi-level Aggregation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON> (<PERSON><PERSON><PERSON>) <PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider a multi-level aggregation problem in a weighted rooted tree, studied recently by <PERSON><PERSON><PERSON> et al. [7]. In this problem requests arrive over time at the nodes of the tree, and each request specifies a deadline. A request is served by sending it to the root before its deadline at a cost equal to the weight of the path from the node in which it resides to the root. However, requests from different nodes can be aggregated, and served together, so as to save on cost. The cost of serving an aggregated set of requests is equal to the weight of the subtree spanning the nodes in which the requests reside. Thus, the problem is to find a competitive online aggregation algorithm that minimizes the total cost of the aggregated requests. This problem arises naturally in many scenarios, including multicasting, supply- chain management and sensor networks. It is also related to the well studied TCP-acknowledgement problem and the online joint replenishment problem.We present an online O(D)-competitive algorithm for the problem, where D is the depth, or number of levels, of the aggregation tree. This result improves upon the D22d- competitive algorithm obtained recently by <PERSON><PERSON><PERSON> et al. [7].MSC codesonline algorithmscompetitive analysisaggregation of requests", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.80"}, {"primary_key": "3865430", "vector": [], "sparse_vector": [], "title": "Fair Coin Flipping: Tighter Analysis and the Many-Party Case.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Nissan Levi", "<PERSON>ad <PERSON>"], "summary": "In a multi-party fair coin-flipping protocol, the parties output a common (close to) unbiased bit, even when some corrupted parties try to bias the output. In this work we focus on the case of dishonest majority, ie at least half of the parties can be corrupted. [19] [STOC 1986] has shown that in any m-round coin-flipping protocol the corrupted parties can bias the honest parties' common output bit by Θ(1/m). For more than two decades the best known coin-flipping protocols against majority was the protocol of [9] [Manuscript 1985], who presented a t-party, m-round protocol with bias This was changed by the breakthrough result of [42] [TCC 2009], who constructed an m-round, two-party coin-flipping protocol with optimal bias Θ(1/m). Recently, [32] [STOC 14] constructed an m-round, three-party coin-flipping protocol with bias O(log3 m/m). Still for the case of more than three parties, against arbitrary number of corruptions, the best known protocol remained the protocol of [9].We make a step towards eliminating the above gap, presenting a t-party, m-round coin-flipping protocol, with bias This improves upon the protocol of [9] for any t ≤ 1/2 · log log m, and in particular for t ∊ O(1), this yields an protocol. For the three-party case, this yields an protocol, improving over the the O(log3 m/m)-bias protocol of [32]. Our protocol generalizes that of [32], by presenting an appropriate \"defense protocols\" for the remaining parties to interact in, in the case that some parties abort or caught cheating ([32] only presented a two-party defense protocol, which limits their final protocol to handle three parties).We analyze our new protocols by presenting a new paradigm for analyzing fairness of coin-flipping protocols. We map the set of adversarial strategies that try to bias the honest parties outcome in the protocol to the set of the feasible solutions of a linear program. The gain each strategy achieves is the value of the corresponding solution. We then bound the the optimal value of the linear program by constructing a feasible solution to its dual.MSC codescoin-flippingfair computationstopping time problems", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.170"}, {"primary_key": "3865431", "vector": [], "sparse_vector": [], "title": "Simplex Transformations and the Multiway Cut Problem.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We consider Multiway Cut, a basic graph partitioning problem in which the goal is to find the minimum weight collection of edges disconnecting a given set of special vertices called terminals. Multiway Cut admits a well known simplex embedding relaxation, where rounding this embedding is equivalent to partitioning the simplex. Current best known solutions to the problem are comprised of a mix of several different ingredients, resulting in intricate algorithms. Moreover, the best of these algorithms is too complex to fully analyze analytically and its approximation factor was verified using a computer. We propose a new approach to simplex partitioning and the Multiway Cut problem based on general transformations of the simplex that allow dependencies between the different variables. Our approach admits much simpler algorithms, and in addition yields an approximation guarantee for the Multiway Cut problem that (roughly) matches the current best computer verified approximation factor.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.158"}, {"primary_key": "3865432", "vector": [], "sparse_vector": [], "title": "Computing the Fréchet Distance between Real-Valued Surfaces.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The Fréchet distance is a well-studied measure for the similarity of shapes. While efficient algorithms for computing the Fréchet distance between curves exist, there are only few results on the Fréchet distance between surfaces. Recent work has shown that the Fréchet distance is computable between piecewise linear functions f and g: M → ℝk with M a triangulated surface of genus zero. We focus on the case k =1 and M being a topological sphere or disk with constant boundary. Intuitively, we measure the distance between terrains based solely on the height function. Our main result is that in this case computing the Frechet distance between f and g is in NP.We additionally show that already for k = 1, computing a factor 2 – ∊ approximation of the Fréchet distance is NP-hard, showing that this problem is in fact NP-complete. We also define an intermediate distance, between contour trees, which we also show to be NP- complete to compute. Finally, we discuss how our and other distance measures between contour trees relate to each other.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.162"}, {"primary_key": "3865433", "vector": [], "sparse_vector": [], "title": "Make Up Your Mind: The Price of Online Queries in Differential Privacy.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider the problem of answering queries about a sensitive dataset subject to differential privacy. The queries may be chosen adversarially from a larger set Q of allowable queries in one of three ways, which we list in order from easiest to hardest to answer:•Offline: The queries are chosen all at once and the differentially private mechanism answers the queries in a single batch.•Online: The queries are chosen all at once, but the mechanism only receives the queries in a streaming fashion and must answer each query before seeing the next query.•Adaptive: The queries are chosen one at a time and the mechanism must answer each query before the next query is chosen. In particular, each query may depend on the answers given to previous queries.Many differentially private mechanisms are just as efficient in the adaptive model as they are in the offline model. Meanwhile, most lower bounds for differential privacy hold in the offline setting. This suggests that the three models may be equivalent.We prove that these models are all, in fact, distinct. Specifically, we show that there is a family of statistical queries such that exponentially more queries from this family can be answered in the offline model than in the online model. We also exhibit a family of search queries such that exponentially more queries from this family can be answered in the online model than in the adaptive model. We also investigate whether such separations might hold for simple queries like threshold queries over the real line.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.85"}, {"primary_key": "3865434", "vector": [], "sparse_vector": [], "title": "Subquadratic Algorithms for the Diameter and the Sum of Pairwise Distances in Planar Graphs.", "authors": ["<PERSON>"], "summary": "We show how to compute in O(n11/6 polylog(n)) expected time the diameter and the sum of the pairwise distances in an undirected planar graph with n vertices and positive edge weights. These are the first algorithms for these problems using time O(nc) for some constant c < 2.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.139"}, {"primary_key": "3865435", "vector": [], "sparse_vector": [], "title": "The Complexity of Simulation and Matrix Multiplication.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Computing the simulation preorder of a given <PERSON>ripke structure (i.e., a directed graph with n labeled vertices) has crucial applications in model checking of temporal logic. It amounts to solving a specific two-players reachability game, called simulation game. We offer the first conditional lower bounds for this problem, and we relate its complexity (for computation, verification, and certification) to some variants of n × n matrix multiplication. We show that any O(na)-time algorithm for simulation games, even restricting to acyclic games/structures, can be used to compute n χ n boolean matrix multiplication (BMM) in O(nα) time. In the acyclic case, we match this bound by presenting the first subcubic algorithm, based on fast BMM, and running in nω +°(1) time (where ω < 2.376 is the exponent of matrix multiplication). For both acyclic and cyclic structures, we point out the existence of natural and canonical O(n2)-size certificates, that can be verified in truly subcubic time by means of matrix multiplication. In the acyclic case, O(n2) time is sufficient, employing standard (+, ×)-matrix product verification. In the cyclic case, a min-edge witness matrix multiplication (EWMM) is used, i.e., a matrix multiplication on the semi-ring (max, ×) where one matrix contains only 0's and 1's, which is computable in truly subcubic n(3+ω)/2+o(1) time. Finally, we show a reduction from EWMM to cyclic simulation games which implies a separation between the cyclic and the acyclic cases, unless EWMM can be verified in nω+°(1) time.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.144"}, {"primary_key": "3865436", "vector": [], "sparse_vector": [], "title": "When and Why the Topological Coverage Criterion Works.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In their seminal work on homological sensor networks, <PERSON> and <PERSON><PERSON> showed the surprising fact that it's possible to certify the coverage of a coordinate-free sensor network even with very minimal knowledge of the space to be covered. Here, coverage means that every point in the domain (except possibly those very near the boundary) has a nearby sensor. More generally, their algorithm takes a pair of nested neighborhood graphs along with a labeling of vertices as either boundary or interior and computes the relative homology of a simplicial complex induced by the graphs. This approach, called the Topological Coverage Criterion (TCC), requires some assumptions about the underlying geometric domain as well as some assumptions about the relationship of the input graphs to the domain. The goal of this paper is to generalize these assumptions and show how the TCC can be applied to both much more general domains as well as very weak assumptions on the input. We give a new, simpler proof of the de Silva-Ghrist Topological Coverage Criterion that eliminates any assumptions about the smoothness of the boundary of the underlying space, allowing the results to be applied to much more general problems. The new proof factors the geometric, topological, and combinatorial aspects, allowing us to provide a coverage condition that supports thick boundaries, k-coverage, and weighted coverage, in which sensors have varying radii.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.177"}, {"primary_key": "3865437", "vector": [], "sparse_vector": [], "title": "Local Search for Max-Sum Diversification.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We provide simple and fast polynomial-time approximation schemes (PTASs) for several variants of the max-sum diversification problem which, in its most basic form, is as follows: given n points p1,…,pn ∊ ℝq and an integer k, select k points such that the average Euclidean distance between these points is maximized. This problem is commonly applied in web search and information retrieval in order to select a diverse set of representative points from the input. In this context, it has recently received a lot of attention.We present new techniques to analyze natural local- search algorithms. This leads to a for distances of negative type, even subject to a general matroid constraint of rank k, in time O(nk2 log k), when assuming that distance evaluations and calls to the independence oracle are constant time. Negative-type distances include as special cases Euclidean and Manhattan distances, among other natural distances. Our result easily transforms into a PTAS. It improves on the only previously known PTAS for this setting, which relies on convex optimization techniques in an n-dimensional space and is impractical for large data sets. In contrast, our procedure has an (optimal) linear dependence on n.Using generalized exchange properties of matroid intersection, we show that a PTAS can be obtained for matroid- intersection constraints as well. Moreover, our techniques, being based on local search, are conceptually simple and allow for various extensions. In particular, we get asymptotically optimal O(1)-approximations when combining the classic dispersion function with a monotone submodular objective, which is a very common class of functions to measure diversity and relevance. This result leverages recent advances on local-search techniques based on proxy functions to obtain optimal approximations for monotone submodular function maximization subject to a matroid constraint.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.9"}, {"primary_key": "3865438", "vector": [], "sparse_vector": [], "title": "Beyond Metric Embedding: Approximating Group Steiner Trees on Bounded Treewidth Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Bundit Laekhanukit", "<PERSON>"], "summary": "NEXT ARTICLENegative-Weight Shortest Paths and Unit Capacity Minimum Cost Flow in Õ (m10/7 log W) Time (Extended Abstract)", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.47"}, {"primary_key": "3865439", "vector": [], "sparse_vector": [], "title": "Universal Shape Replicators via Self-Assembly with Attractive and Repulsive Forces.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We show how to design a universal shape replicator in a self- assembly system with both attractive and repulsive forces. More precisely, we show that there is a universal set of constant-size objects that, when added to any unknown holefree polyomino shape, produces an unbounded number of copies of that shape (plus constant-size garbage objects). The constant-size objects can be easily constructed from a constant number of individual tile types using a constant number of preprocessing self-assembly steps. Our construction uses the well-studied 2-Handed Assembly Model (2HAM) of tile self-assembly, in the simple model where glues interact only with identical glues, allowing glue strengths that are either positive (attractive) or negative (repulsive), and constant temperature (required glue strength for parts to hold together). We also require that the given shape has specified glue types on its surface, and that the feature size (smallest distance between nonincident edges) is bounded below by a constant. Shape replication necessarily requires a self-assembly model where parts can both attach and detach, and this construction is the first to do so using the natural model of negative/repulsive glues (also studied before for other problems such as fuel-efficient computation); previous replication constructions require more powerful global operations such as an \"enzyme\" that destroys a subset of the tile types.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.15"}, {"primary_key": "3865440", "vector": [], "sparse_vector": [], "title": "Online Submodular Maximization with Free Disposal: Randomization Beats ¼ for Partition Matroids.", "authors": ["T.<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shaofeng H.-<PERSON><PERSON> Jiang", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the online submodular maximization problem with free disposal under a matroid constraint. Elements from some ground set arrive one by one in rounds, and the algorithm maintains a feasible set that is independent in the underlying matroid. In each round when a new element arrives, the algorithm may accept the new element into its feasible set and possibly remove elements from it, provided that the resulting set is still independent. The goal is to maximize the value of the final feasible set under some monotone submodular function, to which the algorithm has oracle access.For k-uniform matroids, we give a deterministic algorithm with competitive ratio at least 0.2959, and the ratio approaches as k approaches infinity, improving the previous best ratio of 0.25 by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (IPCO 2014), <PERSON><PERSON><PERSON><PERSON> et al. (SODA 2015) and <PERSON><PERSON><PERSON> et al. (ICALP 2015). We also show that our algorithm is optimal among a class of deterministic monotone algorithms that accept a new arriving element only if the objective is strictly increased.Further, we prove that no deterministic monotone algorithm can be strictly better than 0.25-competitive even for partition matroids, the most modest generalization of k-uniform matroids, matching the competitive ratio by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (IPCO 2014) and <PERSON><PERSON><PERSON> et al. (ICALP 2015). Interestingly, we show that randomized algorithms are strictly more powerful by giving a (non-monotone) randomized algorithm for partition matroids with ratio Finally, our techniques can be extended to a more general problem that generalizes both the online sub- modular maximization problem and the online bipartite matching problem with free disposal. Using the techniques developed in this paper, we give constant- competitive algorithms for the submodular online bipartite matching problem.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.78"}, {"primary_key": "3865441", "vector": [], "sparse_vector": [], "title": "Random Walks and Evolving Sets: Faster Convergences and Limitations.", "authors": ["<PERSON><PERSON>", "Tsz Chiu Kwok", "<PERSON><PERSON>"], "summary": "Analyzing the mixing time of random walks is a well- studied problem with applications in random sampling and more recently in graph partitioning. In this work, we present new analysis of random walks and evolving sets using more combinatorial graph structures, and show some implications in approximating small-set expansion. On the other hand, we provide examples showing the limitations of using random walks and evolving sets in disproving the small-set expansion hypothesis.1.We define a combinatorial analog of the spectral gap, and use it to prove the convergence of non- lazy random walks. A corollary is a tight lower bound on the small-set expansion of graph powers for any graph.2.We prove that random walks converge faster when the robust vertex expansion of the graph is larger. This provides an improved analysis of the local graph partitioning algorithm using the evolving set process, and also derives an alternative proof of an improved <PERSON><PERSON><PERSON>'s inequality.3.We give an example showing that the evolving set process fails to disprove the small-set expansion hypothesis. This refutes a conjecture of <PERSON><PERSON><PERSON> and shows the limitations of all existing local graph partitioning algorithms in approximating small-set expansion.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.121"}, {"primary_key": "3865442", "vector": [], "sparse_vector": [], "title": "Approximate Hierarchical Clustering via Sparsest Cut and Spreading Metrics.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON> recently introduced a cost function for the hierarchical clustering of a set of points given pairwise similarities between them. He showed that this function is NP-hard to optimize, but a top-down recursive partitioning heuristic based on an an-approximation algorithm for uniform sparsest cut gives an approximation of O(an log n) (the current best algorithm has We show that the aforementioned sparsest cut heuristic in fact obtains an O(αn)-approximation. The algorithm also applies to a generalized cost function studied by <PERSON><PERSON><PERSON>. Moreover, we obtain a strong inapproximability result, showing that the Hierarchical Clustering objective is hard to approximate to within any constant factor assuming the Small-Set Expansion (SSE) Hypothesis. Finally, we discuss approximation algorithms based on convex relaxations. We present a spreading metric SDP relaxation for the problem and show that it has integrality gap at most The advantage of the SDP relative to the sparsest cut heuristic is that it provides an explicit lower bound on the optimal solution and could potentially yield an even better approximation for hierarchical clustering. In fact our analysis of this SDP served as the inspiration for our improved analysis of the sparsest cut heuristic. We also show that a spreading metric LP relaxation gives an O(log n)-approximation.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.53"}, {"primary_key": "3865443", "vector": [], "sparse_vector": [], "title": "Tight Network Topology Dependent Bounds on Rounds of Communication.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We prove tight network topology dependent bounds on the round complexity of computing well studied k-party functions such as set disjointness and element distinctness. Unlike the usual case in the CONGEST model in distributed computing, we fix the function and then vary the underlying network topology. This complements the recent such results on total communication that have received some attention. We also present some applications to distributed graph computation problems.Our main contribution is a proof technique that allows us to reduce the problem on a general graph topology to a relevant two-party communication complexity problem. However, unlike many previous works that also used the same high level strategy, we do not reason about a two-party communication problem that is induced by a cut in the graph. To ‘stitch’ back the various lower bounds from the two party communication problems, we use the notion of timed graph that has seen prior use in network coding. Our reductions use some tools from Steiner tree packing and multi-commodity flow problems that have a delay constraint.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.167"}, {"primary_key": "3865444", "vector": [], "sparse_vector": [], "title": "(1 + ∊)-Approximate f-Sensitive Distance Oracles.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "An f-Sensitive Distance Oracle with stretch a preprocesses a graph G(V, E) and produces a small data structure that is used to answer subsequent queries. A query is a triple consisting of a set F ⊂ E of at most f edges, and vertices s and t. The oracle answers a query (F,s.,t) by returning a value d which is equal to the length of some path between s and t in the graph G\\F (the graph obtained from G by discarding all edges in F). Moreover, d is at most a times the length of the shortest path between s and t in G \\ F. The oracle can also construct a path between s and t in G\\F of length d. To the best of our knowledge we give the first nontrivial f-sensitive distance oracle with fast query time and small stretch capable of handling multiple edge failures. Specifically, for any and a fixed ∊ > 0 our oracle answers queries (F,s,t) in time O(l) with (1 + ∊) stretch using a data structure of size n2+0(1) For comparison, the naive alternative requires mfn2 space for sublinear query time.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.96"}, {"primary_key": "3865445", "vector": [], "sparse_vector": [], "title": "Faster Algorithms for Computing Maximal 2-Connected Subgraphs in Sparse Directed Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Giuseppe F<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Connectivity related concepts are of fundamental interest in graph theory. The area has received extensive attention over four decades, but many problems remain unsolved, especially for directed graphs. A directed graph is 2-edge-connected (resp., 2-vertex-connected) if the removal of any edge (resp., vertex) leaves the graph strongly connected. In this paper we present improved algorithms for computing the maximal 2-edge- and 2- vertex-connected subgraphs of a given directed graph. These problems were first studied more than 35 years ago, with Õ(mn) time algorithms for graphs with m edges and n vertices being known since the late 1980s. In contrast, the same problems for undirected graphs are known to be solvable in linear time. <PERSON><PERSON><PERSON> et al. [ICALP 2015] recently introduced O(n2) time algorithms for the directed case, thus improving the running times for dense graphs. Our new algorithms run in time O(m3/2), which further improves the running times for sparse graphs.The notion of 2-connectivity naturally generalizes to k-connectivity for k > 2. For constant values of k, we extend one of our algorithms to compute the maximal k-edge-connected in time O(m3/2 logn), improving again for sparse graphs the best known algorithm by <PERSON><PERSON><PERSON> et al. [ICALP 2015] that runs in O(n2 log n) time.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.124"}, {"primary_key": "3865446", "vector": [], "sparse_vector": [], "title": "Approximating Multicut and the Demand Graph.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the minimum Multicut problem, the input is an edge- weighted supply graph G = (V, E) and a demand graph H = (V, F). Either G and H are directed (Dir-MülC) or both are undirected (Undir-MülC). The goal is to remove a minimum weight set of supply edges E' ⊆ E such that in G — E' there is no path from s to t for any demand edge (s,t) ∊ F. Undir-MülC admits O(log k)-approximation where k is the number of edges in H while the best known approximation for Dir-MülC is min{k, Õ(|V|11/23)}. These approximations are obtained by proving corresponding results on the multicommodity flow-cut gap. In this paper we consider the role that the structure of the demand graph plays in determining the approximability of Multicut. We obtain several new positive and negative results.In undirected graphs our main result is a 2- approximation in nO(t) time when the demand graph excludes an induced matching of size t. This gives a constant factor approximation for a specific demand graph that motivated this work, and is based on a reduction to uniform metric labeling and not via the flow-cut gap.In contrast to the positive result for undirected graphs, we prove that in directed graphs such approximation algorithms can not exist. We prove that, assuming the Unique Games Conjecture (UGC), that for a large class of fixed demand graphs Dir-MülC cannot be approximated to a factor better than the worst- case flow-cut gap. As a consequence we prove that for any fixed k, assuming UGC, Dir-MülC with k demand pairs is hard to approximate to within a factor better than k. On the positive side, we obtain a k approximation when the demand graph excludes certain graphs as an induced subgraph. This generalizes the known 2 approximation for directed Multiway Cut to a larger class of demand graphs.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.54"}, {"primary_key": "3865447", "vector": [], "sparse_vector": [], "title": "Near-Linear Time Approximation Schemes for some Implicit Fractional Packing Problems.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We consider several implicit fractional packing problems and obtain faster implementations of approximation schemes based on multiplicative-weight updates. This leads to new algorithms with near-linear running times for some fundamental problems in combinatorial optimization. We highlight two concrete applications. The first is to find the maximum fractional packing of spanning trees in a capacitated graph; we obtain a (1 - ∊)-approximation in Õ(m/∊2) time, where m is the number of edges in the graph. Second, we consider the LP relaxation of the weighted unsplittable flow problem on a path and obtain a (1 - ∊)-approximation in O(n/∊2) time, where n is the number of demands.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.51"}, {"primary_key": "3865448", "vector": [], "sparse_vector": [], "title": "Computing minimum cuts in hypergraphs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We study algorithmic and structural aspects of connectivity in hypergraphs. Given a hypergraph H = (V, E) with n = │V│, m = |E| and p = Σe∊E lel the fastest known algorithm to compute a global minimum cut in H runs in O(np) time for the uncapacitated case, and in O(np + n2 logn) time for the capacitated case. We show the following new results.•Given an uncapacitated hypergraph H and an integer k we describe an algorithm that runs in O(p) time to find a subhypergraph H' with sum of degrees O(kn) that preserves all edge-connectivities up to k (a k-sparsifier). This generalizes the corresponding result of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> from graphs to hypergraphs. Using this sparsification we obtain an O(p + Λn2) time algorithm for computing a global minimum cut of H where λ is the minimum cut value.•We generalize <PERSON><PERSON>'s argument for graphs to hy- pergraphs and obtain a (2 + ∊)-approximation to the global minimum cut in a capacitated hypergraph in time, and in in O(p/ ∊) time for uncapacitated hypergraphs.•We show that a hypercactus representation of all the global minimum cuts of a capacitated hypergraph can be computed in O(np + n2 logn) time and O(p) space.Our results build upon properties of vertex orderings that were inspired by the maximum adjacency ordering for graphs due to <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. Unlike graphs we observe that there are several different orderings for hypergraphs which yield different insights.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.70"}, {"primary_key": "3865449", "vector": [], "sparse_vector": [], "title": "Competitive analysis of the top-K ranking problem.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Motivated by applications in recommender systems, web search, social choice and crowdsourcing, we consider the problem of identifying the set of top K items from noisy pairwise comparisons. In our setting, we are non-actively given r pairwise comparisons between each pair of n items, where each comparison has noise constrained by a very general noise model called the strong stochastic transitivity (SST) model. We analyze the competitive ratio of algorithms for the top-K problem. In particular, we present a linear time algorithm for the top-K problem which has a competitive ratio of i.e. to solve any instance of top-K, our algorithm needs at most times as many samples needed as the best possible algorithm for that instance (in contrast, all previous known algorithms for the top-K problem have competitive ratios of Ω(n) or worse). We further show that this is tight: any algorithm for the top-K problem has competitive ratio at least", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.81"}, {"primary_key": "3865450", "vector": [], "sparse_vector": [], "title": "Parameterized Algorithms for Constraint Satisfaction Problems Above Average with Global Cardinality Constraints.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Given a constraint satisfaction problem (CSP) on n variables, x1,x2,…, xn ∊ {±1}, and m constraints, aglobal cardinality constraint has the form of where p ∊ (Ω(1), 1 - Ω(1)) and pn is an integer. Let AVG be the expected number of constraints satisfied by randomly choosing an assignment to x1, x2,…, xn, complying with the global cardinality constraint. The CSP above average with the global cardinality constraint problem asks whether there is an assignment (complying with the cardinality constraint) that satisfies more than (AVG + t) constraints, where t is an input parameter.In this paper, we present an algorithm that finds a valid assignment satisfying more than (AVG + t) constraints (if there exists one) in time (2O(t2 + nO(d)). Therefore, the CSP above average with the global cardinality constraint problem is fixed-parameter tractable.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.23"}, {"primary_key": "3865451", "vector": [], "sparse_vector": [], "title": "Playing Anonymous Games using Simple Strategies.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We investigate the complexity of computing approximate Nash equilibria in anonymous games. Our main algorithmic result is the following: For any n-player anonymous game with a bounded number of strategies and any constant δ > 0, an Ο(1/n1-δ)-approximate Nash equilibrium can be computed in polynomial time. Complementing this positive result, we show that if there exists any constant δ > 0 such that an Ο(1/n1+δ)- approximate equilibrium can be computed in polynomial time, then there is a fully polynomial-time approximation scheme (FPTAS) for this problem.We also present a faster algorithm that, for any n-player k-strategy anonymous game, runs in time Õ ((n + k)knk} and computes an Õ(n−1/3k11/3)- approximate equilibrium. This algorithm follows from the existence of simple approximate equilibria of anonymous games, where each player plays one strategy with probability 1 — δ, for some small δ, and plays uniformly at random with probability δ.Our approach exploits the connection between Nash equilibria in anonymous games and Poisson multinomial distributions (PMDs). Specifically, we prove a new probabilistic lemma establishing the following: Two PMDs, with large variance in each direction, whose first few moments are approximately matching are close in total variation distance. Our structural result strengthens previous work by providing a smooth tradeoff between the variance bound and the number of matching moments.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.40"}, {"primary_key": "3865452", "vector": [], "sparse_vector": [], "title": "Core congestion is inherent in hyperbolic networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We investigate the impact the negative curvature has on the traffic congestion in large-scale networks. We prove that every Gromov hyperbolic network $G$ admits a core, thus answering in the positive a conjecture by <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>, Internet Mathematics, 7 (2011) which is based on the experimental observation by <PERSON> and <PERSON>, Physical Review E, 84 (2011) that real-world networks with small hyperbolicity have a core congestion. Namely, we prove that for every subset $X$ of vertices of a $\\delta$-hyperbolic graph $G$ there exists a vertex $m$ of $G$ such that the disk $D(m,4 \\delta)$ of radius $4 \\delta$ centered at $m$ intercepts at least one half of the total flow between all pairs of vertices of $X$, where the flow between two vertices $x,y\\in X$ is carried by geodesic (or quasi-geodesic) $(x,y)$-paths. A set $S$ intercepts the flow between two nodes $x$ and $y$ if $S$ intersect every shortest path between $x$ and $y$. Differently from what was conjectured by <PERSON><PERSON><PERSON><PERSON> et al., we show that $m$ is not (and cannot be) the center of mass of $X$ but is a node close to the median of $X$ in the so-called injective hull of $X$. In case of non-uniform traffic between nodes of $X$ (in this case, the unit flow exists only between certain pairs of nodes of $X$ defined by a commodity graph $R$), we prove a primal-dual result showing that for any $\\rho>5\\delta$ the size of a $\\rho$-multi-core (i.e., the number of disks of radius $\\rho$) intercepting all pairs of $R$ is upper bounded by the maximum number of pairwise $(\\rho-3\\delta)$-apart pairs of $R$.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.149"}, {"primary_key": "3865453", "vector": [], "sparse_vector": [], "title": "On Rationality of Nonnegative Matrix Factorization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Nonnegative matrix factorization (NMF) is the problem of decomposing a given nonnegative n × m matrix M into a product of a nonnegative n × d matrix W and a nonnegative d × m matrix H. NMF has a wide variety of applications, including bioinformatics, chemometrics, communication complexity, machine learning, polyhedral combinatorics, among many others. A longstanding open question, posed by <PERSON> and <PERSON> in 1993, is whether every rational matrix M has an NMF with minimal d whose factors W and H are also rational. We answer this question negatively, by exhibiting a matrix M for which W and H require irrational entries.As an application of this result, we show that state minimization of labeled Markov chains can require the introduction of irrational transition probabilities.We complement these irrationality results with an NP- complete version of NMF for which rational numbers suffice.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.84"}, {"primary_key": "3865454", "vector": [], "sparse_vector": [], "title": "Approximating Spanners and Directed Steiner Forest: Upper and Lower Bounds.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Bundit Laekhanukit"], "summary": "It was recently found that there are very close connectionsbetween the existence of additive spanners (subgraphs where all distances are preserved up to an additive stretch), distance preservers (subgraphs in which demand pairs have their distance preserved exactly), and pairwise spanners (subgraphs in which demand pairs have their distance preserved up to a multiplicative or additive stretch) [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SODA ‘16, <PERSON><PERSON><PERSON><PERSON> ‘16]. We study these problemsfrom an optimization point of view, where ratherthan studying the existence of extremal instances we are given an instance and are asked to find the sparsest possible spanner/preserver. We give an O(n3/5+∊)-approximation for distance preservers and pairwisespanners (for arbitrary constant ∊ > 0). This is the first nontrivial upper bound for either problem, both of which are known to be as hard to approximate as Label Cover. We also prove Label Cover hardness for approximating additive spanners, even for the cases of additive 1 stretch (where one might expect a polylogarithmic approximation, since the related multiplicative 2-spanner problem admits an O(logn)-approximation) and additive polylogarithmic stretch (where the related multiplicative spanner problem has an O(1)-approximation).Interestingly, the techniques we use in our approximation algorithm extend beyond distance-based problem to pure connectivity network design problems. In particular, our techniques allow us to give an O(n3/5+∊)- approximation for the Directed Steiner Forest problem (for arbitrary constant ∊ > 0) when all edges have uniform costs, improving the previous best O(n2/3+∊)- approximation due to <PERSON><PERSON> et al. [ICALP ‘11] (whichholds for general edge costs).", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.34"}, {"primary_key": "3865455", "vector": [], "sparse_vector": [], "title": "Minimizing the Union: Tight Approximations for Small Set Bipartite Vertex Expansion.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In the Minimum k-Union problem (MkU) we are given a set system with n sets and are asked to select k sets in order to minimize the size of their union. Despite being a very natural problem, it has received surprisingly little attention: the only known approximation algorithm is an due to [<PERSON><PERSON><PERSON><PERSON> et al APPROX'16]. This problem can also be viewed as the bipartite version of the Small Set Vertex Expansion problem (SSVE), which we call the Small Set Bipartite Vertex Expansion problem (SSBVE). SSVE, in which we are asked to find a set of k nodes to minimize their vertex expansion, has not been as well studied as its edge-based counterpart Small Set Expansion (SSE), but has recently received significant attention, e.g. [Louis-Makarychev APPROX '15]. However, due to the connection to Unique Games and hardness of approximation the focus has mostly been on sets of size k = Ω(n), while we focus on the case of general k, for which no polylogarithmic approximation is known.We improve the upper bound for this problem by giving an η1/4+∊ approximation for SSBVE for any constant ∊ > 0. Our algorithm follows in the footsteps of Densest k-Subgraph (DkS) and related problems, by designing a tight algorithm for random models, and then extending it to give the same guarantee for arbitrary instances. Moreover, we show that this is tight under plausible complexity conjectures: it cannot be approximated better than O(n1/4) assuming an extension of the so-called \"Dense versus Random\" conjecture for DkS to hypergraphs.In addition to conjectured hardness via our reduction, we show that the same lower bound is also matched by an integrality gap for a super-constant number of rounds of the Sherali-Adams LP hierarchy, and an even worse integrality gap for the natural SDP relaxation. Finally, we note that there exists a simple bicriteria approximation for the more general SSVE problem (where no non-trivial approximations were known for general k).", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.56"}, {"primary_key": "3865456", "vector": [], "sparse_vector": [], "title": "Approximation Algorithms for Label Cover and The Log-Density Threshold.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many known optimal NP-hardness of approximation results are reductions from a problem called Label Cover. The input is a bipartite graph G = (L, R, E) and each edge e = (x,y) ∊ E carries a projection π∊ that maps labels to x to labels to y. The objective is to find a labeling of the vertices that satisfies as many of the projections as possible. It is believed that the best approximation ratio efficiently achievable for Label-Cover is of the form N−c where N = nk, n is the number of vertices, k is the number of labels, and 0 ≤ c 0, a polynomial-time approximation algorithm for semirandom Label-Cover whose approximation ratio is In our semi-random model, the input graph is random (or even just expanding), and the projections on the edges are arbitrary.For worst-case Label-Cover we show a polynomial- time algorithm whose approximation ratio is roughly Ν-°·233. The previous best efficient approximation ratio was Ν−0·25. We present some evidence towards an Ν−c threshold by constructing integrality gaps for Νω(1) rounds of the Sum-of-squares/Lasserre hierarchy of the natural relaxation of Label Cover. For general 2CSP the \"log density threshold\" is Ν−0·25, and we give a polynomial-time algorithm in the semi-random model whose approximation ratio is Ν−0·25+∊ for any ∊ > 0.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.57"}, {"primary_key": "3865457", "vector": [], "sparse_vector": [], "title": "A Framework for Similarity Search with Space-Time Tradeoffs using Locality-Sensitive Filtering.", "authors": ["<PERSON>"], "summary": "We present a framework for similarity search based on Locality-Sensitive Filtering~(LSF),generalizing the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (STOC 1998) Locality-Sensitive Hashing~(LSH) framework to support space-time tradeoffs. Given a family of filters, defined as a distribution over pairs of subsets of space that satisfies certain locality-sensitivity properties, we can construct a dynamic data structure that solves the approximate near neighbor problem in $d$-dimensional space with query time $dn^{\\rho_q + o(1)}$, update time $dn^{\\rho_u + o(1)}$, and space usage $dn + n^{1 + \\rho_u + o(1)}$ where $n$ denotes the number of points in the data structure.The space-time tradeoff is tied to the tradeoff between query time and update time (insertions/deletions), controlled by the exponents $\\rho_q, \\rho_u$ that are determined by the filter family. \\\\ Locality-sensitive filtering was introduced by <PERSON> et al. (SODA 2016) together with a framework yielding a single, balanced, tradeoff between query time and space, further relying on the assumption of an efficient oracle for the filter evaluation algorithm.We extend the LSF framework to support space-time tradeoffs and through a combination of existing techniques we remove the oracle assumption. \\<PERSON> (arXiv 2015), building on <PERSON> et al., introduced a family of filters with space-time tradeoffs for the high-dimensional unit sphere under inner product similarity and analyzed it for the important special case of random data.We show that a small modification to the family of filters gives a simpler analysis that we use, together with our framework, to provide guarantees for worst-case data. Through an application of Bochner's~Theorem from harmonic analysis by Rahimi \\& Recht (NIPS 2007), we are able to extend our solution on the unit sphere to $\\real^d$ under the class of similarity measures corresponding to real-valued characteristic functions.For the characteristic functions of $s$-stable distributions we obtain a solution to the $(r, cr)$-near neighbor problem in $\\ell_s^d$-spaces with query and update exponents $\\rho_q = \\frac{c^s (1+\\lambda)^2}{(c^s + \\lambda)^2}$ and $\\rho_u = \\frac{c^s (1-\\lambda)^2}{(c^s + \\lambda)^2}$where $\\lambda \\in [-1,1]$ is a tradeoff parameter. This result improves upon the space-time tradeoff of Kapralov (PODS 2015) and is shown to be optimal in the case of a balanced tradeoff, matching the LSH lower bound by O'Donnell et al.~\\mbox{(ITCS 2011)} and a similar LSF lower bound proposed in this paper.Finally, we show a lower bound for the space-time tradeoff on the unit sphere that matches Laarhoven's and our own upper bound in the case of random data.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.3"}, {"primary_key": "3865458", "vector": [], "sparse_vector": [], "title": "Better upper bounds on the Füredi-<PERSON>jnal limits of permutations.", "authors": ["<PERSON>", "<PERSON>"], "summary": "A binary matrix is a matrix with entries from the set {0,1}. We say that a binary matrix A contains a binary matrix S if S can be obtained from A by removal of some rows, some columns, and changing some 1-entries to 0-entries. If A does not contain S, we say that A avoids S. A k-permutation matrix P is a binary k χ k matrix with exactly one 1-entry in every row and one 1-entry in every column.The Füredi-Hajnal conjecture, proved by <PERSON> and <PERSON>, states that for every permutation matrix P, there is a constant cp such that for every n ∊ ℕ, every n × n binary matrix A with at least cpn 1-entries contains P.We show that cp ≤ 2O(κ2/3 log7/3 k/(log log κ)1/3) asymptotically almost surely for a random k-permutation matrix P. We also show that cp < 2(4+o(1))k for every k- permutation matrix P, improving the constant in the exponent of a recent upper bound on cp by <PERSON>.We also consider a higher-dimensional generalization of the <PERSON><PERSON><PERSON><PERSON><PERSON> conjecture about the number of d-dimensional n-permutation matrices avoiding a fixed d-dimensional k-permutation matrix, and prove almost matching upper and lower bounds of the form (2k)O(n) · (n!)d-1-1/(d-1) and n−O(k)kΩ(n) · (n!)d-1-1/(d-1), respectively.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.150"}, {"primary_key": "3865459", "vector": [], "sparse_vector": [], "title": "Low-Rank PSD Approximation in Input-Sparsity Time.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We give algorithms for approximation by low-rank positive semidefinite (PSD) matrices. For symmetric input matrix A ∊ ℝn×n, target rank k, and error parameter ∊ > 0, one algorithm finds with constant probability a PSD matrix Ỹ of rank k suchthat where Ak,+ denotes the best rank-k PSD approximation to A, and the norm is Frobenius. The algorithm takes time O(nnz(A) log n) + n poly((log n)k/∊) + poly(k/∊), where nnz(A) denotes the number of nonzero entries of A, and poly(k/∊) denotes a polynomial in k/∊. (There are two different polynomials in the time bound.) Here the output matrix Y has the form CUCT, where the O(k/∊) columns of c are columns of A. In contrast to prior work, we do not require the input matrix A to be PSD, our output is rank k (not larger), and our running time is O(nnz (A) log n) provided this is larger than npoly((log n)k/e). We give a similar algorithm that is faster and simpler, but whose rank- k PSD output does not involve columns of A, and does not require A to be symmetric. We give similar algorithms for best rank-k approximation subject to the constraint of symmetry. We also show that there are asymmetric input matrices that cannot have good symmetric column-selected approximations.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.134"}, {"primary_key": "3865460", "vector": [], "sparse_vector": [], "title": "Exploring an Infinite Space with Finite Memory Scouts.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Consider a small number of scouts exploring the infinite d-dimensional grid with the aim of hitting a hidden target point. Each scout is controlled by a probabilistic finite automaton that determines its movement (to a neighboring grid point) based on its current state. The scouts, that operate under a fully synchronous schedule, communicate with each other (in a way that affects their respective states) when they share the same grid point and operate independently otherwise. Our main research question is: How many scouts are required to guarantee that the target admits a finite mean hitting time? Recently, it was shown that d + 1 is an upper bound on the answer to this question for any dimension d ≥ 1 and the main contribution of this paper comes in the form of proving that this bound is tight for d ∊ {1, 2}.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.14"}, {"primary_key": "3865461", "vector": [], "sparse_vector": [], "title": "Input Sparsity Time Low-rank Approximation via Ridge Leverage Score Sampling.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a new algorithm for finding a near optimal low-rank approximation of a matrix A in O(nnz(A)) time. Our method is based on a recursive sampling scheme for computing a representative subset of <PERSON>'s columns, which is then used to find a low-rank approximation.This approach differs substantially from prior O(nnz(A)) time algorithms, which are all based on fast <PERSON><PERSON><PERSON> random projections. Our algorithm matches the guarantees of the random projection methods while offering a number of advantages.In addition to better performance on sparse and structured data, sampling algorithms can be applied in settings where random projections cannot. For example, we give new streaming algorithms for the column subset selection and projection-cost preserving sample problems. Our method has also been used in the fastest algorithms for provably accurate <PERSON>yström approximation of kernel matrices [56].", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.115"}, {"primary_key": "3865462", "vector": [], "sparse_vector": [], "title": "Negative-Weight Shortest Paths and Unit Capacity Minimum Cost Flow in Õ (m10/7 log W) Time (Extended Abstract).", "authors": ["<PERSON>", "Aleksander Madry", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we study a set of combinatorial optimization problems on weighted graphs: the shortest path problem with negative weights, the weighted perfect bipartite matching problem, the unit-capacity minimum-cost maximum flow problem, and the weighted perfect bipartite b-matching problem under the assumption that‖b‖1= O(m). We show that each of these four problems can be solved in Õ(m10/7 log W) time, where W is the absolute maximum weight of an edge in the graph, providing the first polynomial improvement in their sparse-graph time complexity in over 25 years.At a high level, our algorithms build on the interior- point method-based framework developed by <PERSON><PERSON><PERSON><PERSON> (FOCS 2013) for solving unit-capacity maximum flow problem. We develop a refined way to analyze this framework, as well as provide new variants of the underlying preconditioning and perturbation techniques. Consequently, we are able to extend the whole interior-point method-based approach to make it applicable in the weighted graph regime.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.48"}, {"primary_key": "3865463", "vector": [], "sparse_vector": [], "title": "Robust algorithms with polynomial loss for near-unanimity CSPs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "An instance of the Constraint Satisfaction Problem (CSP) is given by a family of constraints on overlapping sets of variables, and the goal is to assign values from a fixed domain to the variables so that all constraints are satisfied. In the optimization version, the goal is to maximize the number of satisfied constraints. An approximation algorithm for CSP is called robust if it outputs an assignment satisfying a (1 — g(∊))-fraction of constraints on any (1 — ∊)-satisfiable instance, where the loss function g is such that g(∊) → 0 as ∊ → 0.We study how the robust approximability of CSPs depends on the set of constraint relations allowed in instances, the so-called constraint language. All constraint languages admitting a robust polynomial-time algorithm (with some g) have been characterised by <PERSON><PERSON> and <PERSON><PERSON><PERSON>, with the general bound on the loss g being doubly exponential, specifically g(∊) = O((loglog(1/ ∊))/log(1/ ∊)). It is natural to ask when a better loss can be achieved: in particular, polynomial loss g(∊) = O(∊1/k) for some constant k. In this paper, we consider CSPs with a constraint language having a near- unanimity polymorphism. We give two randomized robust algorithms with polynomial loss for such CSPs: one works for any near-unanimity polymorphism and the parameter k in the loss depends on the size of the domain and the arity of the relations in Γ, while the other works for a special ternary near-unanimity operation called dual discriminator with k = 2 for any domain size. In the latter case, the CSP is a common generalisation of Unique Games with a fixed domain and 2-Sat. In the former case, we use the algebraic approach to the CSP. Both cases use the standard semidefinite programming relaxation for CSP.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.22"}, {"primary_key": "3865464", "vector": [], "sparse_vector": [], "title": "Random Walks with the Minimum Degree Local Rule Have O(N2) Cover Time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "For a simple (unbiased) random walk on a connected graph with n vertices, the cover time (the expected number of steps it takes to visit all vertices) is at most O(n3). We consider locally biased random walks, in which the probability of traversing an edge depends on the degrees of its endpoints. We confirm a conjecture of <PERSON>, <PERSON> and <PERSON> [2015] that the min-degree local bias rule ensures a cover time of O(n2). For this we formulate and prove the following lemma about spanning trees.Let R(e) denote for edge e the minimum degree among its two endpoints. We say that a weight function W for the edges is feasible if it is nonnegative, dominated by R (for every edge W (e) < R(e)) and the sum over all edges of the ratios W(e)/R(e) equals n - 1. For example, in trees W (e) = R(e), and in regular graphs the sum of edge weights is d(n - 1).Lemma: for every feasible W, the minimum weight spanning tree has total weight O(n).For regular graphs, a similar lemma was proved by <PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON> [1989].", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.120"}, {"primary_key": "3865465", "vector": [], "sparse_vector": [], "title": "Parameter-free Topology Inference and Sparsification for Data on Manifolds.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In topology inference from data, current approaches face two major problems. One concerns the selection of a correct parameter to build an appropriate complex on top of the data points; the other involves with the typical ‘large’ size of this complex. We address these two issues in the context of inferring homology from sample points of a smooth manifold of known dimension sitting in an Euclidean space ℝk. We show that, for a sample size of n points, we can identify a set of O(n2) points (as opposed to Voronoi vertices) approximating a subset of the medial axis that suffices to compute a distance sandwiched between the well known local feature size and the local weak feature size (in fact, the approximating set can be further reduced in size to O(n)). This distance, called the lean feature size, helps pruning the input set at least to the level of local feature size while making the data locally uniform. The local uniformity in turn helps in building a complex for homology inference on top of the sparsified data without requiring any user-supplied distance threshold. Unlike most topology inference results, ours does not require that the input is dense relative to a global feature such as reach or weak feature size; instead it can be adaptive with respect to the local feature size. We present some empirical evidence in support of our theoretical claims.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.181"}, {"primary_key": "3865466", "vector": [], "sparse_vector": [], "title": "Connectivity Oracles for Graphs Subject to Vertex Failures.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce new data structures for answering connectivity queries in graphs subject to batched vertex failures. Our deterministic structure processes a batch of d ≤ d* failed vertices in Õ(d3) time and thereafter answers connectivity queries in Õ(d) time. It occupies space Õ(d*m log n). We develop a randomized Monte Carlo version of our data structure with update time Õ(d2), query time Õ(d), and space Õ(m) for any d*. This is the first connectivity oracle for general graphs that can efficiently deal with an unbounded number of vertex failures.Our data structures are based on a new decomposition theorem for an undirected graph G = (V, E), which is of independent interest. It states that for any terminal set U ⊆ V we can remove a set B of |U|/(s — 2) vertices such that the remaining graph contains a Steiner forest for U - B with maximum degree s.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.31"}, {"primary_key": "3865467", "vector": [], "sparse_vector": [], "title": "Scaling Algorithms for Weighted Matching in General Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We present a new scaling algorithm for maximum (or minimum) weight perfect matching on general, edge weighted graphs. Our algorithm runs in time, per scale, which matches the running time of the best cardinality matching algorithms on sparse graphs [29, 18]. Here m,n, and n bound the number of edges, vertices, and magnitude of any integer edge weight. Our result improves on a 25-year old algorithm of Gabow and Tarjan, which runs in time.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.50"}, {"primary_key": "3865468", "vector": [], "sparse_vector": [], "title": "Best-Response Dynamics in Combinatorial Auctions with Item Bidding.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In a combinatorial auction with item bidding, agents participate in multiple single-item second-price auctions at once. As some items might be substitutes, agents need to strate- gize in order to maximize their utilities. A number of results indicate that high welfare can be achieved this way, giving bounds on the welfare at equilibrium. Recently, however, criticism has been raised that equilibria are hard to compute and therefore unlikely to be attained.In this paper, we take a different perspective. We study simple best-response dynamics. That is, agents are activated one after the other and each activated agent updates his strategy myopically to a best response against the other agents’ current strategies. Often these dynamics may take exponentially long before they converge or they may not converge at all. However, as we show, convergence is not even necessary for good welfare guarantees. Given that agents’ bid updates are aggressive enough but not too aggressive, the game will remain in states of good welfare after each agent has updated his bid at least once.In more detail, we show that if agents have fractionally subadditive valuations, natural dynamics reach and remain in a state that provides a 1/3 approximation to the optimal welfare after each agent has updated his bid at least once. For subadditive valuations, we can guarantee an Ω(1/log m) approximation in case of m items that applies after each agent has updated his bid at least once and at any point after that. The latter bound is complemented by a negative result, showing that no kind of best-response dynamics can guarantee more than a an o(log log m/ log m) fraction of the optimal social welfare.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.33"}, {"primary_key": "3865469", "vector": [], "sparse_vector": [], "title": "Convergence of Incentive-Driven Dynamics in Fisher Markets.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In both general equilibrium theory and game theory, the dominant mathematical models rest on a fully rational solution concept in which every player's action is a best-response to the actions of the other players. In both theories there is less agreement on suitable out- of-equilibrium modeling, but one attractive approach is the level k model in which a level 0 player adopts a very simple response to current conditions, a level 1 player best-responds to a model in which others take level 0 actions, and so forth. (This is analogous to k-ply exploration of game trees in AI, and to receding-horizon control in control theory.) If players have deterministic mental models with this kind of finite-level response, there is obviously no way their mental models can all be consistent. Nevertheless, there is experimental evidence that people act this way in many situations, motivating the question of what the dynamics of such interactions lead to.We address the problem of out-of-equilibrium price dynamics in the setting of Fisher markets. We develop a general framework in which sellers have (a) a set of atomic price update rules which are simple responses to a price vector; (b) a belief-formation procedure that simulates actions of other sellers (themselves using the atomic price updates) to some finite horizon in the future. In this framework, sellers use an atomic price update rule to respond to a price vector they generate with the belief formation procedure. The framework is general and allows sellers to have inconsistent and time- varying beliefs about each other. Under certain assumptions on the atomic update rules, we show that despite the inconsistent and time-varying nature of beliefs, the market converges to a unique equilibrium. (If the price updates are driven by weak-gross substitutes demands, this is the same equilibrium point predicted by those demands.) This result holds for both synchronous and asynchronous discrete-time updates. Moreover, the result is computationally feasible in the sense that the convergence rate is linear, i.e., the distance to equilibrium decays exponentially fast. To the best of our knowledge, this is the first result that demonstrates, in Fisher markets, convergence at any rate for dynamics driven by a plausible model of seller incentives.We then specialize our results to Fisher markets with elastic demands (a further special case corresponds to demand generated by buyers with constant elasticity of substitution (CES) utilities, in the weak gross substitutes (WGS) regime) and show that the atomic update rule in which a seller uses the best-response (=profit- maximizing) update given the prices of all other sellers, satisfies the assumptions required on atomic price update rules in our framework. We can even characterize the convergence rate (as a function of elasticity parameters of the demand function).Our results apply also to settings where, to the best of our knowledge, there exists no previous demonstration of efficient convergence of any discrete dynamic of price updates. Even for the simple case of (level 0) best- response dynamics, our result is the first to demonstrate a linear rate of convergence.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.35"}, {"primary_key": "3865470", "vector": [], "sparse_vector": [], "title": "Efficient Algorithms for Constructing Very Sparse Spanners and Emulators.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON> et al. [43] devised a distributed1 algorithm in the CONGEST model, that given a parameter k = 1, 2,…, constructs an O(k)-spanner of an input unweighted n-vertex graph with O(n1+1/k) expected edges in O(k) rounds of communication. In this paper we improve the result of [43], by showing a k-round distributed algorithm in the same model, that constructs a (2k — 1)- spanner with O(n1+1/k/∊) edges, with probability 1 — ∊, for any ∊ > 0. Moreover, when k = ω(log n), our algorithm produces (still in k rounds) ultra-sparse spanners, i.e., spanners of size n(1 + o(1)), with probability 1 — o(1). To our knowledge, this is the first distributed algorithm in the CONGEST or in the PRAM models that constructs spanners or skeletons (i.e., connected spanning subgraphs) that sparse. Our algorithm can also be implemented in linear time in the standard centralized model, and for large k, it provides spanners that are sparser than any other spanner given by a known (near-)linear time algorithm.We also devise improved bounds (and algorithms realizing these bounds) for (1 + ∊, ß)-spanners and emulators. In particular, we show that for any unweighted n-vertex graph and any ∊ > 0, there exists a with O(n) edges. All previous constructions of (1 + ∊, β)-spanners and emulators employ a superlinear number of edges, for all choices of parameters.Finally, we provide some applications of our results to approximate shortest paths' computation in unweighted graphs.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.41"}, {"primary_key": "3865471", "vector": [], "sparse_vector": [], "title": "Reordering Buffers with Logarithmic Diameter Dependency for Trees.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In the reordering buffer problem a sequence of items located in a metric space arrive online, and have to be processed by a single server moving within the metric space. At any point in time, the first k still unprocessed items from the sequence are available for processing and the server has to select one of these items and process it by visiting its location. The goal is to process all items while minimizing the total distance the server moves.<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> (STOC’07) gave a deterministic O(D. log k)-competitive online algorithm for weighted tree metrics with hop-diameter D. We improve the analysis of this algorithm and significantly improve the dependency on D. Specifically, we show that the algorithm is in fact O(log D+log k)-competitive. Our analysis is quite robust. Even when an optimal algorithm, to which we compare the online algorithm, is allowed to choose between the first h > k unprocessed items, the online algorithm is still O(h· (log D+log h)/k)- competitive. For H = (1 + ∊) · k, with constant ∊ > 0, this is optimal.Our results also imply better competitive ratio for general metric spaces, improving the randomized O(log n · log2 k) result for n-point metric spaces from STOC’07 to O (log n · log k).", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.79"}, {"primary_key": "3865472", "vector": [], "sparse_vector": [], "title": "Statistical Query Algorithms for Mean Vector Estimation and Stochastic Convex Optimization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Santosh S<PERSON>"], "summary": "Stochastic convex optimization, where the objective is the expectation of a random convex function, is an important and widely used method with numerous applications in machine learning, statistics, operations research and other areas. We study the complexity of stochastic convex optimization given only statistical query (SQ) access to the objective function. We show that well-known and popular first-order iterative methods can be implemented using only statistical queries. For many cases of interest we derive nearly matching upper and lower bounds on the estimation (sample) complexity including linear optimization in the most general setting. We then present several consequences for machine learning, differential privacy and proving concrete lower bounds on the power of convex optimization based methods.The key ingredient of our work is SQ algorithms and lower bounds for estimating the mean vector of a distribution over vectors supported on a convex body in ℝd. This natural problem has not been previously studied and we show that our solutions can be used to get substantially improved SQ versions of Perceptron and other online algorithms for learning halfspaces.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.82"}, {"primary_key": "3865473", "vector": [], "sparse_vector": [], "title": "Building a Good Team: Secretary Problems and the Supermodular Degree.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In the (classical) Secretary Problem, one has to hire the best among n candidates. The candidates are interviewed, one at a time, at a uniformly random order, and one has to decide on the spot, whether to hire a candidate or continue interviewing. It is well known that the best candidate can be hired with a probability of 1/e (<PERSON><PERSON>, 1963). Recent works extend this problem to settings in which multiple candidates can be hired, subject to some constraint. Here, one wishes to hire a set of candidates maximizing a given objective set function.Almost all extensions considered in the literature assume the objective set function is either linear or submod- ular. Unfortunately, real world functions might not have either of these properties. Consider, for example, a scenario where one hires researchers for a project. Indeed, it can be that some researchers can substitute others for that matter. However, it can also be that some combinations of researchers result in synergy (see, e.g., <PERSON> et al., Science 2010, for a study on collective intelligence). The first phenomenon can be modeled by a submoudlar set function, while the latter cannot.In this work, we study the secretary problem with an arbitrary non-negative monotone valuation function, subject to a general matroid constraint. One can prove that, generally, only very poor results can be obtained for this class of objective functions. We tackle this hardness by combining the following: (1) Parametrizing our algorithms by the supermodular degree of the objective function (defined by <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, ITCS 2013), which, roughly speaking, measures the distance of a function from being submodular. (2) Suggesting an (arguably) natural model that permits approximation guarantees that are polynomial in the supermodular degree (as opposed to the standard model which allows only exponential guarantees). Our algorithms learn the input by running a non-trivial estimation algorithm on a portion of it whose size depends on the supermodular degree.We also provide better approximation guarantees for the special case of a uniform matroid constraint. To the best of our knowledge, our results represent the first algorithms for a secretary problem handling arbitrary non-negative monotone valuation functions.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.109"}, {"primary_key": "3865474", "vector": [], "sparse_vector": [], "title": "Spanning Circuits in Regular Matroids.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We consider the fundamental Matroid Theory problem of finding a circuit in a matroid spanning a set t of given terminal elements. For graphic matroids this corresponds to the problem of finding a simple cycle passing through a set of given terminal edges in a graph. The algorithmic study of the problem on regular matroids, a superclass of graphic matroids, was initiated by <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON> [ICALP'12], who proved that the case of the problem with |T| = 2 is fixed-parameter tractable (FPT) when parameterized by the length of the circuit. We extend the result of <PERSON><PERSON><PERSON><PERSON>, Kr<PERSON><PERSON>', and <PERSON><PERSON> by showing that for regular matroids•the Minimum Spanning Circuit problem, deciding whether there is a circuit with at most ℓ elements containing T, is FPT parameterized by k = ℓ — |T|;•the Spanning Circuit problem, deciding whether there is a circuit containing T, is FPT parameterized by |T|.We note that extending our algorithmic findings to binary matroids, a superclass of regular matroids, is highly unlikely: Minimum Spanning Circuit parameterized by ℓ is W[1]- hard on binary matroids even when |T| = 1. We also show a limit to how far our results can be strengthened by considering a smaller parameter. More precisely, we prove that Minimum Spanning Circuit parameterized by |T| is W[1]-hard even on cographic matroids, a proper subclass of regular matroids.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.93"}, {"primary_key": "3865475", "vector": [], "sparse_vector": [], "title": "Fully polynomial-time parameterized computations for graphs and matrices of low treewidth.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We investigate the complexity of several fundamental polynomial-time solvable problems on graphs and on matrices, when the given instance has low treewidth; in the case of matrices, we consider the treewidth of the graph formed by non-zero entries. In each of the considered cases, the best known algorithms working on general graphs run in polynomial, but far from linear, time. Thus, our goal is to construct algorithms with running time of the form poly(k) · n or poly(k) · n log n, where k is the width of the tree decomposition given on the input. Such procedures would outperform the best known algorithms for the considered problems already for moderate values of the treewidth, like O(n1/c) for some small constant c. Our results include:an algorithm for computing the determinant and the rank of an n × n matrix using O(k3 · n) time and arithmetic operations;an algorithm for solving a system of linear equations using O(k3 · n) time and arithmetic operations;an O(k3 · n log n)-time randomized algorithm for finding the cardinality of a maximum matching in a graph;an O(k4 · nlog2 n)-time randomized algorithm for constructing a maximum matching in a graph;an O(k2 · n log n)-time algorithm for finding a maximum vertex flow in a directed graph.Moreover, we provide an approximation algorithm for treewidth with time complexity suited to the running times as above. Namely, the algorithm, when given a graph G and integer k, runs in time O(k2 · n log n) and either correctly reports that the treewidth of G is larger than k, or constructs a tree decomposition of G of width O(k2).The above results stand in contrast with the recent work of Abboud et al. [SODA 2016], which shows that the existence of algorithms with similar running times is unlikely for the problems of finding the diameter and the radius of a graph of low treewidth.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.92"}, {"primary_key": "3865476", "vector": [], "sparse_vector": [], "title": "A tight bound for Green&apos;s arithmetic triangle removal lemma in vector spaces.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Let p be a fixed prime. A triangle in is an ordered triple (x, y, z) of points satisfying x+y+ z = 0. Let <PERSON> proved an arithmetic triangle removal lemma which says that for every ∊ > 0 and prime p, there is a δ > 0 such that if and the number of triangles in X × Y × Z is at most δN2, then we can delete ∊N elements from X, Y, and Z and remove all triangles. <PERSON> posed the problem of improving the quantitative bounds on the arithmetic triangle removal lemma, and, in particular, asked whether a polynomial bound holds. Despite considerable attention, prior to this paper, the best known bound, due to the first author, showed that 1/δ can be taken to be an exponential tower of twos of height logarithmic in 1/∊.We solve <PERSON>'s problem, proving an essentially tight bound for <PERSON>'s arithmetic triangle removal lemma in We show that a polynomial bound holds, and further determine the best possible exponent. Namely, there is a computable number Cp such that we may take and we must have In particular, C2 = 1 + 1/(5/3 - log2 3) ≈ 13.239, and C3 = 1 + 1/c3 with and which gives C3 ≈ 13.901. The proof uses <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>'s essentially sharp bound on multicolored sum- free sets, which builds on the recent breakthrough on the cap set problem by <PERSON><PERSON><PERSON><PERSON>, and the subsequent work by <PERSON>, <PERSON><PERSON>k<PERSON>-<PERSON>-<PERSON>, and <PERSON><PERSON>.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.106"}, {"primary_key": "3865477", "vector": [], "sparse_vector": [], "title": "Permutation Property Testing under Different Metrics with Low Query Complexity.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The goal of property testing is to quickly distinguish between objects which satisfy a property and objects that are ε-far from satisfying the property. There are now several general results in this area which show that natural properties of combinatorial objects can be tested with \"constant\" query complexity, depending only on ε and the property, and not on the size of the object being tested. The upper bound on the query complexity coming from the proof techniques are often enormous and impractical. It remains a major open problem if better bounds hold.<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> conjectured and <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> proved that hereditary permutation properties are strongly testable, i.e., can be tested with respect to <PERSON>'s tau distance. The query complexity bound coming from this proof is huge. Even for testing a single forbidden subpermutation it is of Ackermann-type in 1 /ε. We give a new proof which gives a polynomial bound in 1/ε for testing a single forbidden subpermutation.Maybe surprisingly, for testing with respect to the rectangular distance, we prove there is a universal (not depending on the property), polynomial in 1/ε query complexity bound for two-sided testing hereditary properties of sufficiently large permutations. We further give a nearly linear bound with respect to a closely related metric which also depends on the smallest forbidden sub- permutation for the property. Finally, we show that several different permutation metrics of interest are related to the rectangular distance, yielding similar results for testing with respect to these metrics.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.107"}, {"primary_key": "3865479", "vector": [], "sparse_vector": [], "title": "On the insertion time of random walk cuckoo hashing.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Cuckoo Hashing is a hashing scheme invented by <PERSON><PERSON> and <PERSON> [12]. It uses d ≥ 2 distinct hash functions to insert items into the hash table. It has been an open question for some time as to the expected time for Random Walk Insertion to add items. We show that if the number of hash functions d = O(1) is sufficiently large, then the expected insertion time is O(1) per item.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.97"}, {"primary_key": "3865480", "vector": [], "sparse_vector": [], "title": "Completeness for First-Order Properties on Sparse Structures with Algorithmic Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Properties definable in first-order logic are algorithmically interesting for both theoretical and pragmatic reasons. Many of the most studied algorithmic problems, such as Hitting Set and Orthogonal Vectors, are first-order, and the first-order properties naturally arise as relational database queries. A relatively straightforward algorithm for evaluating a property with k + 1 quantifiers takes time O(mk) and, assuming the Strong Exponential Time Hypothesis (SETH), some such properties require O(mk-∊) time for any ∊ > 0. (Here, m represents the size of the input structure, i.e. the number of tuples in all relations.)We give algorithms for every first-order property that improves this upper bound to i.e., an improvement by a factor more than any poly-log, but less than the polynomial required to refute SETH. Moreover, we show that further improvement is equivalent to improving algorithms for sparse instances of the well-studied Orthogonal Vectors problem. Surprisingly, both results are obtained by showing completeness of the Sparse Orthogonal Vectors problem for the class of first-order properties under fine-grained reductions. To obtain improved algorithms, we apply the fast Orthogonal Vectors algorithm of [3, 16].While fine-grained reductions (reductions that closely preserve the conjectured complexities of problems) have been used to relate the hardness of disparate specific problems both within P and beyond, this is the first such completeness result for a standard complexity class.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.141"}, {"primary_key": "3865481", "vector": [], "sparse_vector": [], "title": "Sparse Suffix Tree Construction in Optimal Time and Space.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Suffix tree (and the closely related suffix array) are fundamental structures capturing all substrings of a given text essentially by storing all its suffixes in the lexicographical order. In some applications, such as sparse text indexing, we work with a subset of b interesting suffixes, which are stored in the so-called sparse suffix tree. Because the size of this structure is Θ(b), it is natural to seek a construction algorithm using only O(b) words of space assuming read-only random access to the text. We design a linear-time Monte Carlo algorithm for this problem, hence resolving an open question explicitly stated by <PERSON><PERSON> et al. [TALG 2016]. The best previously known algorithm by <PERSON> et al. [STACS 2014] works in O(n log b) time. As opposed to previous solutions, which were based on the divide-and-conquer paradigm, our solution proceeds in n/b rounds. In the r-th round, we consider all suffixes starting at positions congruent to r modulo n/b. By maintaining rolling hashes, we can lexicographically sort all interesting suffixes starting at such positions, and then we can merge them with the already considered suffixes. For efficient merging, we also need to answer LCE queries efficiently (and in small space). By plugging in the structure of <PERSON><PERSON> et al. [CPM 2015] we obtain O(n + b log b) time complexity. We improve this structure by a recursive application of the so-called difference covers, which then implies a linear-time sparse suffix tree construction algorithm.We complement our <PERSON> algorithm with a deterministic verification procedure. The verification takes time, which improves upon the bound of O(n log b) obtained by I et al. [STACS 2014]. This is obtained by first observing that the pruning done inside the previous solution has a rather clean description using the notion of graph spanners with small multiplicative stretch. Then, we are able to decrease the verification time by applying difference covers twice. Combined with the Monte Carlo algorithm, this gives us an and O(b)-space Las Vegas algorithm.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.27"}, {"primary_key": "3865482", "vector": [], "sparse_vector": [], "title": "Strong Connectivity in Directed Graphs under Failures, with Applications.", "authors": ["<PERSON><PERSON>", "Giuseppe F<PERSON>", "<PERSON><PERSON>"], "summary": "Let G be a directed graph (digraph) with m edges and n vertices, and let G \\ e (resp., G \\ v) be the digraph obtained after deleting edge e (resp., vertex v) from G. We show how to compute in O(m + n) worst-case time:•The total number of strongly connected components in G \\ e (resp., G \\ v), for all edges e (resp., for all vertices v) in G.•The size of the largest and of the smallest strongly connected components in G \\ e (resp., G \\ v), for all edges e (resp., for all vertices v) in G.Let G be strongly connected. We say that edge e (resp., vertex v) separates two vertices x and y, if x and y are no longer strongly connected in G \\ e (resp., G \\ v). We also show how to build in O(m+n) time O(n)-space data structures that can answer in optimal time the following basic connectivity queries on digraphs:•Report in O(n) worst-case time all the strongly connected components of G \\ e (resp., G \\ v), for a query edge e (resp., vertex v).•Test whether an edge or a vertex separates two query vertices in O(1) worst-case time.•Report all edges (resp., vertices) that separate two query vertices in optimal worst-case time, i.e., in time O(k), where k is the number of separating edges (resp., separating vertices). (For k = 0, the time is O(1)).All our bounds are tight and are obtained with a common algorithmic framework, based on a novel compact representation of the decompositions induced by 1-edge and 1-vertex cuts in digraphs, which might be of independent interest. With the help of our data structures we can design efficient algorithms for several other connectivity problems on digraphs and we can also obtain in linear time a strongly connected spanning subgraph of G with O(n) edges that maintains the 1-connectivity cuts of G and the decompositions induced by those cuts.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.123"}, {"primary_key": "3865483", "vector": [], "sparse_vector": [], "title": "Random Contractions and Sampling for Hypergraph and Hedge Connectivity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We initiate the study of hedge connectivity of undirected graphs, motivated by dependent edge failures in real-world networks. In this model, edges are partitioned into groups called hedges that fail together. The hedge connectivity of a graph is the minimum number of hedges whose removal disconnects the graph. We give a polynomial-time approximation scheme and a quasi-polynomial exact algorithm for hedge connectivity. This provides strong evidence that the hedge connectivity problem is tractable, which contrasts with prior work that established the intractability of the corresponding s-t min-cut problem. Our techniques also yield new combinatorial and algorithmic results in hypergraph connectivity. Next, we study the behavior of hedge graphs under uniform random sampling of hedges. We show that unlike graphs, all cuts in the sample do not converge to their expected value in hedge graphs. Nevertheless, the min-cut of the sample does indeed concentrate around the expected value of the original min-cut. This leads to a sharp threshold on hedge survival probabilities for graph disconnection. To the best of our knowledge, this is the first network reliability analysis under dependent edge failures.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.71"}, {"primary_key": "3865484", "vector": [], "sparse_vector": [], "title": "Distributed Degree Splitting, Edge Coloring, and Orientations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We study a family of closely-related distributed graph problems, which we call degree splitting, where roughly speaking the objective is to partition (or orient) the edges such that each node's degree is split almost uniformly. Our findings lead to answers for a number of problems, a sampling of which includes:•We present a poly log n round deterministic algorithm for (2Δ – 1)•(1+o(1))-edge-coloring, where Δ denotes the maximum degree. <PERSON><PERSON><PERSON> the 1 + o(1) factor, this settles one of the long-standing open problems of the area from the 1990's (see e.g. <PERSON> and <PERSON><PERSON><PERSON><PERSON> [PODC'92]). Indeed, a weaker requirement of (2Δ – 1) · poly log Δ-edge- coloring in poly log n rounds was asked for in the 4th open question in the Distributed Graph Coloring book by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>.•We show that sinkless orientation—i.e., orienting edges such that each node has at least one outgoing edge—on Δ-regular graphs can be solved in O(logA log n) rounds randomized and in O(logA n) rounds deterministically. These prove the corresponding lower bounds by <PERSON> et al. [STOC'16] and <PERSON>, <PERSON>, and <PERSON><PERSON> [FOCS'16] to be tight. Moreover, these show that sinkless orientation exhibits an exponential separation between its randomized and deterministic complexities, akin to the results of <PERSON> et al. for Δ-coloring Δ- regular trees.•We present a randomized O (log4 n) round algorithm for orienting a-arboricity graphs with maximum out-degree a(1 + ∊). This can be also turned into a decomposition into a(1 + ∊) forests when a = 0(logn) and into a(1 + ∊) pseduo-forests when a = o(log n). Obtaining an efficient distributed decomposition into less than 2a forests was stated as the 10th open problem in the book by Barenboim and Elkin.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.166"}, {"primary_key": "3865485", "vector": [], "sparse_vector": [], "title": "Approximation Algorithms for Finding Maximum Induced Expanders.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We initiate the study of approximating the largest induced expander in a given graph G. Given a Δ-regular graph G with n vertices, the goal is to find the set with the largest induced expansion of size at least δ · n. We design a bi-criteria approximation algorithm for this problem; if the optimum has induced spectral expansion λ our algorithm returns a expander of size at least δn (up to constants).Our proof introduces and employs a novel semidefi- nite programming relaxation for the largest induced expander problem. We expect to see further applications of our SDP relaxation in graph partitioning problems. In particular, because of the close connection to the small set expansion problem, one may be able to obtain new insights into the unique games problem.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.75"}, {"primary_key": "3865486", "vector": [], "sparse_vector": [], "title": "Maximally Recoverable Codes for Grid-like Topologies.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Guangda Hu", "Swas<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The explosion in the volumes of data being stored online has resulted in distributed storage systems transitioning to erasure coding based schemes. Yet, the codes being deployed in practice are fairly short. In this work, we address what we view as the main coding theoretic barrier to deploying longer codes in storage: at large lengths, failures are not independent and correlated failures are inevitable. This motivates designing codes that allow quick data recovery even after large correlated failures, and which have efficient encoding and decoding.We propose that code design for distributed storage be viewed as a two step process. The first step is choose a topology of the code, which incorporates knowledge about the correlate d failures that need to be handled, and ensures local recovery from such failures. In the second step one specifies a code with the chosen topology by choosing coefficients from a finite field Fq. In this step, one tries to balance reliability (which is better over larger fields) with encoding and decoding efficiency (which is better over smaller fields).This work initiates an in-depth study of this reliability/efficiency tradeoff. We consider the field-size needed for achieving maximal recover ability: the strongest reliability possible with a given topology. We propose a family of topologies called grid-like topologies which unify a number of topologies considered both in theory and practice, and prove the following results about codes for such topologies:•The first super-polynomial lower bound on the field size needed for achieving maximal recoverability in a simple grid-like topology. To our knowledge, there was no super-linear lower bound known before, for any topology.•A combinatorial characterization of erasure patterns correctable by Maximally Recoverable codes for a topology which corresponds to tensoring MDS codes with a parity check code. This topology is used in practice (for instance see [MLR+14]). We conjecture a similar characterization for Maximally Recoverable codes instantiating arbitrary tensor product topologies.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.136"}, {"primary_key": "3865487", "vector": [], "sparse_vector": [], "title": "Locally Testable and Locally Correctable Codes Approaching the Gilbert-Varshamov Bound.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Swas<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "One of the most important open problems in the theory of error-correcting codes is to determine the tradeoff between the rate R and minimum distance δ of a binary code. The best known tradeoff is the Gilbert<PERSON><PERSON><PERSON><PERSON><PERSON> bound, and says that for every δ ∊ (0,1/2), there are codes with minimum distance δ and rate R = rGV (δ) > 0 (for a certain simple function rGV(·)). In this paper we show that the Gilbert-<PERSON><PERSON><PERSON><PERSON> bound can be achieved by codes which support local error-detection and error- correction algorithms. Specifically, we show the following results.1.Local Testing: For all δ ∊ (0,1/2) and all R 0, for all δ < 1/2 sufficiently large, and all R < (1 — ∊)RGV(δ), there exist codes with length n, rate R and minimum distance δ that are locally correctable from fraction errors with O(ne) query complexity.Furthermore, these codes have an efficient randomized construction, and the local testing and local correction algorithms can be made to run in time polynomial in the query complexity. Our results on locally correctable codes also immediately give locally decodable codes with the same parameters.Our local testing result is obtained by combining <PERSON><PERSON><PERSON>'s random concatenation technique and the best known locally testable codes from [KMRS16]. Our local correction result, which is significantly more involved, also uses random concatenation, along with a number of further ideas: the Guruswami-Sudan-Indyk list decoding strategy for concatenated codes, Alon- Edmonds-Luby distance amplification, and the local list-decodability, local list-recoverability and local testability of Reed-Muller codes. Curiously, our final local correction algorithms go via local list-decoding and local testing algorithms; this seems to be the first time local testability is used in the construction of a locally correctable code.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.135"}, {"primary_key": "3865488", "vector": [], "sparse_vector": [], "title": "Distance Sensitive Bloom Filters Without False Negatives.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A Bloom filter is a widely used data-structure for representing a set S and answering queries of the form “Is x in S?”. By allowing some false positive answers (saying ‘yes’ when the answer is in fact ‘no’) Bloom filters use space significantly below what is required for storing S. In the distance sensitive setting we work with a set S of (Hamming) vectors and seek a data structure that offers a similar trade-off, but answers queries of the form “Is x close to an element of S?” (in Hamming distance). Previous work on distance sensitive Bloom filters have accepted false positive and false negative answers. Absence of false negatives is of critical importance in many applications of Bloom filters, so it is natural to ask if this can be also achieved in the distance sensitive setting. Our main contributions are upper and lower bounds (that are tight in several cases) for space usage in the distance sensitive setting where false negatives are not allowed.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.17"}, {"primary_key": "3865489", "vector": [], "sparse_vector": [], "title": "Faster Sublinear Algorithms using Conditional Sampling.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Manolis Zampetakis"], "summary": "A conditional sampling oracle for a probability distribution D returns samples from the conditional distribution of D restricted to a specified subset of the domain. A recent line of work [7, 6] has shown that having access to such a conditional sampling oracle requires only polylogarithmic or even constant number of samples to solve distribution testing problems like identity and uniformity. This significantly improves over the standard sampling model where polynomially many samples are necessary.Inspired by these results, we introduce a computational model based on conditional sampling to develop sublinear algorithms with exponentially faster runtimes compared to standard sublinear algorithms. We focus on geometric optimization problems over points in high dimensional Euclidean space. Access to these points is provided via a conditional sampling oracle that takes as input a succinct representation of a subset of the domain and outputs a uniformly random point in that subset. We study two well studied problems: k-means clustering and estimating the weight of the minimum spanning tree. In contrast to prior algorithms for the classic model, our algorithms have time, space and sample complexity that is polynomial in the dimension and polylogarithmic in the number of points.Finally, we comment on the applicability of the model and compare with existing ones like streaming, parallel and distributed computational models.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.114"}, {"primary_key": "3865490", "vector": [], "sparse_vector": [], "title": "To Augment or Not to Augment: Solving Unsplittable Flow on a Path by Creating Slack.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In the Unsplittable Flow on a Path problem (UFP) we are given a path with non-negative edge capacities and a set of tasks, each one characterized by a subpath, a demand, and a profit. Our goal is to select a subset of tasks of maximum total profit so that the total demand of the selected tasks on each edge does not exceed the respective edge capacity. UFP naturally captures several applications in bandwidth allocation, job scheduling, and caching.Following a sequence of improvements, the current best (polynomial time) approximation factor for UFP is 2 + ∊ [<PERSON><PERSON><PERSON> et al. SODA'14]. UFP also admits a QPTAS [<PERSON><PERSON> et al. STOC'06, <PERSON><PERSON> et al. SODA'15], and finding a PTAS is considered a challenging open problem.In this paper we make progress in the direction of the mentioned open problem. Informally, we introduce a technique to obtain real PTASs from PTASs with resource augmentation where edge capacities can be violated by a 1 + ∊ factor. While unfortunately we do not have a resource-augmentation PTAS for the general case of UFP, for many relevant special cases we have such an algorithm or we provide one in this paper. For example, our approach leads to a PTAS for the rooted case of UFP, where all tasks share a common edge. This is one of the simplest natural restrictions of UFP where the best-known approximation was 2 + ∊ (like for the general case).At a high level, our technique is to sacrifice a few tasks in the optimal solution (with a small loss of profit) in order to create a sufficient amount of slack capacity on each edge. This slack turns out to be large enough to substitute the additional capacity we would gain from resource augmentation. Crucial for our approach is that we obtain slack from tasks with relatively small and relatively large demand simultaneously. In all prior polynomial time approximation algorithms the sacrificed tasks came from only one of these two groups.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.159"}, {"primary_key": "3865491", "vector": [], "sparse_vector": [], "title": "Sampling on the Sphere by Mutually Orthogonal Subspaces.", "authors": ["<PERSON><PERSON>"], "summary": "The purpose of this paper is twofold. First, we provide an optimal bits lower bound for any two-way protocol for the Vector in Subspace Communication Problem which is of bounded total rank. This result complements <PERSON><PERSON>'s protocol, which has a simple variant of bounded total rank. Second, we present a plausible mathematical conjecture on a measure concentration phenomenon that implies an lower bound for a general protocol. We prove the conjecture for the subclass of sets that depend only on directions.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.61"}, {"primary_key": "3865492", "vector": [], "sparse_vector": [], "title": "Random cluster dynamics for the Ising model is rapidly mixing.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We show for the first time that the mixing time of Glauber (single edge update) dynamics for the random cluster model at q = 2 is bounded by a polynomial in the size of the underlying graph. As a consequence, the Swendsen- <PERSON> algorithm for the ferromagnetic Ising model at any temperature has the same polynomial mixing time bound.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.118"}, {"primary_key": "3865493", "vector": [], "sparse_vector": [], "title": "LAST but not Least: Online Spanners for Buy-at-Bulk.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The online (uniform) buy-at-bulk network design problem asks us to design a network, where the edge-costs exhibit economy-of-scale. Previous approaches to this problem used tree-embeddings, giving us randomized algorithms. Moreover, the optimal results with a logarithmic competitive ratio requires the metric on which the network is being built to be known up-front; the competitive ratios then depend on the size of this metric (which could be much larger than the number of terminals that arrive).We consider the buy-at-bulk problem in the least restrictive model where the metric is not known in advance, but revealed in parts along with the demand points seeking connectivity arriving online. For the single sink buy-at-bulk problem, we give a deterministic online algorithm with competitive ratio that is logarithmic in k, the number of terminals that have arrived, matching the lower bound known even for the online Steiner tree problem. In the oblivious case when the buy-at-bulk function used to compute the edge-costs of the network is not known in advance (but is the same across all edges), we give a deterministic algorithm with competitive ratio polylogarithmic in k, the number of terminals.At the heart of our algorithms are optimal constructions for online Light Approximate Shortest-path Trees (LASTs) and spanners, and their variants. We give constructions that have optimal trade-offs in terms of cost and stretch. We also define and give constructions for a new notion of LASTs where the set of roots (in addition to the points) expands over time. We expect these techniques will find applications in other online network-design problems.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.38"}, {"primary_key": "3865494", "vector": [], "sparse_vector": [], "title": "Adaptivity Gaps for Stochastic Probing: Submodular and XOS Functions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Suppose we are given a submodular function f over a set of elements, and we want to maximize its value subject to certain constraints. Good approximation algorithms are known for such problems under both monotone and non-monotone submodular functions. We consider these problems in a stochastic setting, where elements are not all active and we only get value from active elements. Each element e is active independently with some known probability pe, but we don't know the element's status a priori: we find it out only when we probe the element e. Moreover, the sequence of elements we probe must satisfy a given prefix-closed constraint, e.g., matroid, orienteering, deadline, precedence, or any downward-closed constraint.In this paper we study the gap between adaptive and non-adaptive strategies for f being a submodular or a fractionally subadditive (XOS) function. If this gap is small, we can focus on finding good non-adaptive strategies instead, which are easier to find as well as to represent. We show that the adaptivity gap is a constant for monotone and non-monotone submodular functions, and logarithmic for XOS functions of small width. These bounds are nearly tight. Our techniques show new ways of arguing about the optimal adaptive decision tree for stochastic optimization problems.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.111"}, {"primary_key": "3865495", "vector": [], "sparse_vector": [], "title": "MDS Code Constructions with Small Sub-packetization and Near-optimal Repair Bandwidth.", "authors": ["<PERSON>en<PERSON><PERSON>wami", "<PERSON><PERSON><PERSON>"], "summary": "An (n, M) vector code is a collection of M codewords where n elements (from the field ) in each of the codewords are referred to as code blocks. Assuming that , the code blocks are treated as ℓ-length vectors over the base field . Equivalently, the code is said to have the sub-packetization level ℓ. This paper addresses the problem of constructing MDS vector codes which enable exact reconstruction of each code block by downloading small amount of information from the remaining code blocks. The repair bandwidth of a code measures the information flow from the remaining code blocks during the reconstruction of a single code block. This problem naturally arises in the context of distributed storage systems as the node repair problem [4]. Assuming that , the repair bandwidth of an MDS vector code is lower bounded by ((n — 1)/(n — k)) ·ℓ symbols (over the base field ) which is also referred to as the cut-set bound [4]. For all values of n and k, the MDS vector codes that attain the cut-set bound with the sub-packetization level ℓ = (n − k)⌈n/(n−k)⌉ are known in the literature [23,36].This paper presents a construction for MDS vector codes which simultaneously ensures both small repair bandwidth and small sub-packetization level. The obtained codes have the smallest possible sub-packetization level ℓ = O(n — k) for an MDS vector code and the repair bandwidth which is at most twice the cut-set bound. The paper then generalizes this code construction so that the repair bandwidth of the obtained codes approach the cut-set bound at the cost of increased sub-packetization level. The constructions presented in this paper give MDS vector codes which are linear over the base field .", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.137"}, {"primary_key": "3865496", "vector": [], "sparse_vector": [], "title": "Parallel algorithms and concentration bounds for the Lovász Local Lemma via witness-DAGs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The Lovász Local Lemma (LLL) is a cornerstone principle in the probabilistic method of combinatorics, and a seminal algorithm of <PERSON>ser & Tardos (2010) provides an efficient randomized algorithm to implement it. This algorithm can be parallelized to give an algorithm that uses polynomially many processors and runs in O(log3 n) time, stemming from O(log n) adaptive computations of a maximal independent set (MIS). <PERSON> et al. (2014) developed faster local and parallel algorithms, potentially running in time O (log2 n), but these algorithms work under significantly more stringent conditions than the LLL.We give a new parallel algorithm that works under essentially the same conditions as the original algorithm of Moser & Tardos but uses only a single MIS computation, thus running in O(log2 n) time. This conceptually new algorithm also gives a clean combinatorial description of a satisfying assignment which might be of independent interest. Our techniques extend to the deterministic LLL algorithm given by <PERSON><PERSON><PERSON><PERSON> et al. (2013) leading to an NC-algorithm running in time O(log2 n) as well.We also provide improved bounds on the runtimes of the sequential and parallel resampling-based algorithms originally developed by <PERSON>ser & Tardos. Our bounds extend to any problem instance in which the tighter Shearer LLL criterion is satisfied. We also improve on the analysis of <PERSON><PERSON><PERSON> & <PERSON>zeged<PERSON> (2011) to give tighter concentration results.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.76"}, {"primary_key": "3865497", "vector": [], "sparse_vector": [], "title": "Bridging the Capacity Gap Between Interactive and One-Way Communication.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the communication rate of coding schemes for interactive communication that transform any two-party interactive protocol into a protocol that is robust to noise.Recently, <PERSON><PERSON><PERSON> [11] showed that if an ∊ > 0 fraction of transmissions are corrupted, adversarially or randomly, then it is possible to achieve a communication rate of Furthermore, <PERSON><PERSON><PERSON> conjectured that this rate is optimal for general input protocols. This stands in contrast to the classical setting of one-way communication in which error-correcting codes are known to achieve an optimal communication rate of 1 In this work, we show that the quadratically smaller rate loss of the one-way setting can also be achieved in interactive coding schemes for a very natural class of input protocols. We introduce the notion of average message length, or the average number of bits a party sends before receiving a reply, as a natural parameter for measuring the level of interactivity in a protocol. Moreover, we show that any protocol with average message length ℓ = Ω(poly(1/∊)) can be simulated by a protocol with optimal communication rate 1 — Θ(Η(∊)) over an oblivious adversarial channel with error fraction e. Furthermore, under the additional assumption of access to public shared randomness, the optimal communication rate is achieved ratelessly, i.e., the communication rate adapts automatically to the actual error rate e without having to specify it in advance.This shows that the capacity gap between one-way and interactive communication can be bridged even for very small (constant in e) average message lengths, which are likely to be found in many applications.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.138"}, {"primary_key": "3865498", "vector": [], "sparse_vector": [], "title": "Proximity in the Age of Distraction: Robust Approximate Nearest Neighbor Search.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce a new variant of the nearest neighbor search problem, which allows for some coordinates of the dataset to be arbitrarily corrupted or unknown. Formally, given a dataset of n points P = {x1,…, xn} in high-dimensions, and a parameter k, the goal is to preprocess the dataset, such that given a query point q, one can compute quickly a point x ∊ P, such that the distance of the query to the point x is minimized, when ignoring the “optimal” k coordinates. Note, that the coordinates being ignored are a function of both the query point and the point returned.We present a general reduction from this problem to answering ANN queries, which is similar in spirit to LSH (locality sensitive hashing) [19]. Specifically, we give a sampling technique which achieves a bi-criterion approximation for this problem. If the distance to the nearest neighbor after ignoring k coordinates is r, the data-structure returns a point that is within a distance of O(r) after ignoring O(k) coordinates. We also present other applications and further extensions and refinements of the above result.The new data-structures are simple and (arguably) elegant, and should be practical - specifically, all bounds are polynomial in all relevant parameters (including the dimension of the space, and the robustness parameter k).", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.1"}, {"primary_key": "3865499", "vector": [], "sparse_vector": [], "title": "Deterministic parallel algorithms for fooling polylogarithmic juntas and the Lovász Local Lemma.", "authors": ["<PERSON>"], "summary": "Many randomized algorithms can be derandomized efficiently using either the method of conditional expectations or probability spaces with low (almost-) independence. A series of papers, beginning with work by <PERSON><PERSON> (1988) and continuing with <PERSON> (1991) and <PERSON><PERSON> et al. (1994), showed that these techniques can be combined to give deterministic parallel algorithms for combinatorial optimization problems involving sums of w-juntas. We improve these algorithms through derandomized variable partitioning. This reduces the processor complexity to essentially independent of w while the running time is reduced from exponential in w to linear in w. For example, we improve the time complexity of an algorithm of <PERSON> & R<PERSON> (1991) for rainbow hypergraph coloring by a factor of approximately log2 n and the processor complexity by a factor of approximately mln2.As a major application of this, we give an NC algorithm for the Lovász Local Lemma. Previous NC algorithms, including the seminal algorithm of <PERSON><PERSON> & Tardos (2010) and the work of <PERSON><PERSON><PERSON><PERSON> et. al (2013), required that (essentially) the bad-events could span only O(log n) variables; we relax this to allowing polylog(n) variables. As two applications of our new algorithm, we give algorithms for defective vertex coloring and domatic graph partition.One main sub-problem encountered in these algorithms is to generate a probability space which can “fool” a given list of GF(2) Fourier characters. <PERSON><PERSON><PERSON> <PERSON> (1992) gave an NC algorithm for this; we dramatically improve its efficiency to near-optimal time and processor complexity and code dimension. This leads to a new algorithm to solve the heavy-codeword problem, introduced by <PERSON><PERSON> (1993), with a near-linear processor complexity (mn)1+o(1).", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.77"}, {"primary_key": "3865500", "vector": [], "sparse_vector": [], "title": "Sequential measurements, disturbance and property testing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>-<PERSON>", "<PERSON>"], "summary": "We describe two procedures which, given access to one copy of a quantum state and a sequence of two-outcome measurements, can distinguish between the case that at least one of the measurements accepts the state with high probability, and the case that all of the measurements have low probability of acceptance. The measurements cannot simply be tried in sequence, because early measurements may disturb the state being tested. One procedure is based on a variant of Marriott-Watrous amplification. The other procedure is based on the use of a test for this disturbance, which is applied with low probability. We find a number of applications:•Quantum query complexity separations in the property testing model for testing isomorphism of functions under group actions. We give quantum algorithms for testing isomorphism, linear isomorphism and affine isomorphism of boolean functions which use exponentially fewer queries than is possible classically, and a quantum algorithm for testing graph isomorphism which uses polynomially fewer queries than the best algorithm known.•Testing properties of quantum states and operations. We show that any finite property of quantum states can be tested using a number of copies of the state which is logarithmic in the size of the property, and give a test for genuine multipartite entanglement of states of n qubits that uses O(n) copies of the state. We also show that equivalence of two unitary operations under conjugation by a unitary picked from a fixed set can be tested efficiently. This is a natural quantum generalisation of testing isomorphism of boolean functions.•Correcting an error in a result of <PERSON><PERSON> on de- Merlinizing quantum protocols. This result claimed that, in any one-way quantum communication protocol where two parties are assisted by an all-powerful but untrusted third party, the third party can be removed with only a modest increase in the communication cost. We give a corrected proof of a key technical lemma required for <PERSON><PERSON>'s result.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.105"}, {"primary_key": "3865501", "vector": [], "sparse_vector": [], "title": "Local Flow Partitioning for Faster Edge Connectivity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the problem of computing a minimum cut in a simple, undirected graph and give a deterministic O(m log2 n log log2 n) time algorithm. This improves both on the best previously known deterministic running time of O(m log12 n) (<PERSON><PERSON> and <PERSON><PERSON> [12]) and the best previously known randomized running time of O(mlog3n) (<PERSON><PERSON> [11]) for this problem, though <PERSON><PERSON>'s algorithm can be further applied to weighted graphs.Our approach is using the Kawarabayashi and Tho- rup graph compression technique, which repeatedly finds low-conductance cuts. To find these cuts they use a diffusion-based local algorithm. We use instead a flow- based local algorithm and suitably adjust their framework to work with our flow-based subroutine. Both flow and diffusion based methods have a long history of being applied to finding low conductance cuts. Diffusion algorithms have several variants that are naturally local while it is more complicated to make flow methods local. Some prior work has proven nice properties for local flow based algorithms with respect to improving or cleaning up low conductance cuts. Our flow subroutine, however, is the first that is both local and produces low conductance cuts. Thus, it may be of independent interest.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.125"}, {"primary_key": "3865502", "vector": [], "sparse_vector": [], "title": "Faster approximation schemes for the two-dimensional knapsack problem.", "authors": ["<PERSON>", "<PERSON>"], "summary": "An important question in theoretical computer science is to determine the best possible running time for solving a problem at hand. For geometric optimization problems, we often understand their complexity on a rough scale, but not very well on a finer scale. One such example is the two-dimensional knapsack problem for squares. There is a polynomial time (1 + ∊)-approximation algorithm for it (i.e., a PTAS) but the running time of this algorithm is triple exponential in 1/e, i.e., A double or triple exponential dependence on 1/e is inherent in how this and several other algorithms for other geometric problems work. In this paper, we present an EPTAS for knapsack for squares, i.e., a (1+∊)-approximation algorithm with a running time of O∊(1) · nO(1). In particular, the exponent of n in the running time does not depend on e at all! Since there can be no FPTAS for the problem (unless P = NP) this is the best kind of approximation scheme we can hope for. To achieve this improvement, we introduce two new key ideas: We present a fast method to guess the Ω(221/∊) relatively large squares of a suitable near-optimal packing instead of using brute-force enumeration. Secondly, we introduce an indirect guessing framework to define sizes of cells for the remaining squares.In the previous PTAS each of these steps needs a running time of and we improve both to Oe(1) · nO(1). We complete our result by giving an algorithm for two-dimensional knapsack for rectangles under (1 + ∊)-resource augmentation. In this setting, we also improve the best known running time of Ω(n1/∊) to O∊(1) nO(1), and compute even a solution with optimal profit, in contrast to the best previously known polynomial time algorithm for this setting that computes only an approximation. We believe that our new techniques have the potential to be useful for other settings as well.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.6"}, {"primary_key": "3865503", "vector": [], "sparse_vector": [], "title": "Extension Complexity Lower Bounds for Mixed-Integer Extended Formulations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We prove that any mixed-integer linear extended formulation for the matching polytope of the complete graph on n vertices, with a polynomial number of constraints, requires many integer variables. By known reductions, this result extends to the traveling salesman polytope. This lower bound has various implications regarding the existence of small mixed-integer mathematical formulations of common problems in operations research. In particular, it shows that for many classic vehicle routing problems and problems involving matchings, any compact mixed-integer linear description of such a problem requires a large number of integer variables. This provides a first nontrivial lower bound on the number of integer variables needed in such settings.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.154"}, {"primary_key": "3865504", "vector": [], "sparse_vector": [], "title": "A Logarithmic Additive Integrality Gap for Bin Packing.", "authors": ["<PERSON>", "<PERSON>"], "summary": "For bin packing, the input consists of n items with sizes s1,…, sn ∊ [0,1] which have to be assigned to a minimum number of bins of size 1. Recently, the second author gave an LP-based polynomial time algorithm that employed techniques from discrepancy theory to find a solution using at most OPT + O(logOPT • loglog OPT) bins. In this paper, we build on the techniques of Rothvos<PERSON> to present an approximation algorithm that has an additive gap of only O(log OPT) bins. This gap matches certain combinatorial lower bounds, and any further improvement would have to use more algebraic structure.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.172"}, {"primary_key": "3865505", "vector": [], "sparse_vector": [], "title": "Fully Dynamic Connectivity in O(log n(log log n)2) Amortized Expected Time.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Dynamic connectivity is one of the most fundamental problems in dynamic graph algorithms. We present a new randomized dynamic connectivity structure with O(log n (log log n)2) amortized expected update time and O(log n/ log log log n) query time, which comes within an O(log log n)2) factor of a lower bound due to <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. The new structure is based on a dynamic connectivity algorithm proposed by <PERSON><PERSON> in an extended abstract at STOC 2000, which left out some important details.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.32"}, {"primary_key": "3865506", "vector": [], "sparse_vector": [], "title": "Popularity, Mixed Matchings, and Self-duality.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Telike<PERSON><PERSON>"], "summary": "Our input instance is a bipartite graph G = (A ∪ B, E) where A is a set of applicants, B is a set of jobs, and each vertex u ∊ A ∪ B has a preference list ranking its neighbors in a strict order of preference. For any two matchings M and T in G, let φ (M, T) be the number of vertices that prefer M to T. A matching M is popular if φ(Μ, T) ≥ φ(Τ,M) for all matchings T in G. There is a utility function w : E → ℚ and we consider the problem of matching applicants to jobs in a popular and utility-optimal manner. A popular mixed matching could have a much higher utility than all popular matchings, where a mixed matching is a probability distribution over matchings, i.e., a mixed matching Π = {(M0, p0),…, (Mk, pk)} for some matchings M0,…,Mk and for all i. The function φ(·,) easily extends to mixed matchings; a mixed matching Π is popular if φ(Π,Λ) > φ(Λ, Π) for all mixed matchings Λ in G.Motivated by the fact that a popular mixed matching could have a much higher utility than all popular matchings, we study the popular fractional matching polytope Pg. Our main result is that this polytope is half-integral and in the special case where a stable matching in G is a perfect matching, this polytope is integral. This implies that there is always a max-utility popular mixed matching Π such that where M0 and M1 are matchings in G. As Π can be computed in polynomial time, an immediate consequence of our result is that in order to implement a max-utility popular mixed matching in G, we need just a single random bit.We analyze PG whose description may have exponentially many constraints via an extended formulation with a linear number of constraints. The linear program that gives rise to this formulation has an unusual property: self-duality. In other words, this linear program is identical to its dual program. This is a rare case where an LP of a natural problem has such a property. The self-duality of this LP plays a crucial role in our proof of half-integrality of PG.We also show that our result carries over to the roommates problem, where the graph G need not be bipartite. The polytope of popular fractional matchings is still half-integral here and so we can compute a max-utility popular half-integral matching in G in polynomial time. To complement this result, we also show that the problem of computing a max-utility popular (integral) matching in a roommates instance is NP-hard.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.151"}, {"primary_key": "3865507", "vector": [], "sparse_vector": [], "title": "Stochastic k-Center and j-Flat-Center Problems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Solving geometric optimization problems over uncertain data has become increasingly important in many applications and has attracted a lot of attentions in recent years. In this paper, we study two important geometric optimization problems, the k-center problem and the j-flat-center problem, over stochastic/uncertain data points in Euclidean spaces. For the stochastic k-center problem, we would like to find k points in a fixed dimensional Euclidean space, such that the expected value of the k-center objective is minimized. For the stochastic j-flat-center problem, we seek a j-flat (i.e., a j-dimensional affine subspace) such that the expected value of the maximum distance from any point to the j-flat is minimized. We consider both problems under two popular stochastic geometric models, the existential uncertainty model, where the existence of each point may be uncertain, and the locational uncertainty model, where the location of each point may be uncertain. We provide the first PTAS (Polynomial Time Approximation Scheme) for both problems under the two models. Our results generalize the previous results for stochastic minimum enclosing ball and stochastic enclosing cylinder.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.8"}, {"primary_key": "3865508", "vector": [], "sparse_vector": [], "title": "Hardness of Continuous Local Search: Query Complexity and Cryptographic Lower Bounds.", "authors": ["<PERSON>", "E<PERSON>"], "summary": "Local search proved to be an extremely useful tool when facing hard optimization problems (e.g., via the simplex algorithm, simulated annealing, or genetic algorithms). Although powerful, it has its limitations: there are functions for which exponentially many queries are needed to find a local optimum. In many contexts the optimization problem is defined by a continuous function, which might offer an advantage when performing the local search. This leads us to study the following natural question: How hard is continuous local search? The computational complexity of such search problems is captured by the complexity class CLS (Daskalakis and Papadimitriou SODA’11) which is contained in the intersection of PLS and PPAD, two important subclasses of TFNP (the class of NP search problems with a guaranteed solution).In this work, we show the first hardness results for CLS (the smallest non-trivial class among the currently defined subclasses of TFNP). Our hardness results are in terms of black-box (where only oracle access to the function is given) and white-box (where the function is represented succinctly by a circuit). In the black-box case, we show instances for which any (computationally unbounded) randomized algorithm must perform exponentially many queries in order to find a local optimum. In the white-box case, we show hardness for computationally bounded algorithms under cryptographic assumptions. Our results demonstrate a strong conceptual barrier precluding design of efficient algorithms for solving local search problems even over continuous domains.As our main technical contribution we introduce a new total search problem which we call End-OF-Metered-Line. The special structure of End-OF-Metered-Line enables us to: (1) show that it is contained in CLS, and (2) prove hardness for it both in the black-box and the white-box setting.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.88"}, {"primary_key": "3865509", "vector": [], "sparse_vector": [], "title": "Fair Scheduling via Iterative Quasi-Uniform Sampling.", "authors": ["Sungjin Im", "<PERSON>"], "summary": "In the paper we consider minimizing the ℓk-norms of flow time on a single machine offline using a preemptive scheduler for k ≥ 1. We show the first O( 1)- approximation for the problem, improving upon the previous best O(log log P)-approximation by Bansal and Pruhs (FOCS 09 and SICOMP 14) where P is the ratio of the maximum job size to the minimum. Our main technical ingredient is a novel combination of quasi-uniform sampling and iterative rounding, which is of interest in its own right.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.171"}, {"primary_key": "3865510", "vector": [], "sparse_vector": [], "title": "Exponential Segregation in a Two-Dimensional Schelling Model with Tolerant Individuals.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We prove that the two-dimensional Schelling segregation model yields monochromatic regions of size exponential in the area of individuals’ neighborhoods, provided that the tolerance parameter is a constant strictly less than 1/2 but sufficiently close to it. Our analysis makes use of a connection with the first-passage percolation model from the theory of stochastic processes.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.62"}, {"primary_key": "3865511", "vector": [], "sparse_vector": [], "title": "Near-Optimal (Euclidean) Metric Compression.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The metric sketching problem is defined as follows. Given a metric on n points, and ∊ > 0, we wish to produce a small size data structure (sketch) that, given any pair of point indices, recovers the distance between the points up to a 1 + ∊ distortion. In this paper we consider metrics induced by l2 and l1 norms whose spread (the ratio of the diameter to the closest pair distance) is bounded by Φ > 0. A well-known dimensionality reduction theorem due to <PERSON> and <PERSON> yields a sketch of size O(∊−2 log Φn)n log n), i.e., O(∊−2 logΦn) logn) bits per point. We show that this bound is not optimal, and can be substantially improved to O(∊−2 log(1/∊) · log n + log log Φ) bits per point. Furthermore, we show that our bound is tight up to a factor of log(1/∊).We also consider sketching of general metrics and provide a sketch of size O(n log(1/∊) + log log Φ) bits per point, which we show is optimal.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.45"}, {"primary_key": "3865512", "vector": [], "sparse_vector": [], "title": "About the Structure of the Integer Cone and its Application to Bin Packing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the bin packing problem with d different item sizes and revisit the structure theorem given by <PERSON><PERSON><PERSON> and <PERSON><PERSON> [5] about solutions of the integer cone. We present new techniques on how solutions can be modified and give a new structure theorem that relies on the set of vertices of the underlying integer polytope. As a result of our new structure theorem, we obtain an algorithm for the bin packing problem with running time where V is the set of vertices of the integer knapsack poly- tope and enc(I) is the encoding length of the bin packing instance. The algorithm is fixed parameter tractable, parameterized by the number of vertices of the integer knapsack polytope |V|. This shows that the bin packing problem can be solved efficiently when the underlying integer knapsack polytope has an easy structure, i.e. has a small number of vertices. Furthermore, we show that the presented bounds of the structure theorem are asymptotically tight. We give a construction of bin packing instances using new structural insights and classical number theoretical theorems which yield the desired lower bound.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.103"}, {"primary_key": "3865513", "vector": [], "sparse_vector": [], "title": "Approximation and Kernelization for Chordal Vertex Deletion.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The Chordal Vertex Deletion (ChVD) problem asks to delete a minimum number of vertices from an input graph to obtain a chordal graph. In this paper we develop a polynomial kernel for ChVD under the parameterization by the solution size. Using a new Erdos-Posa type packing/covering duality for holes in nearly-chordal graphs, we present a polynomial-time algorithm that reduces any instance (G, k) of ChVD to an equivalent instance with poly(k) vertices. The existence of a polynomial kernel answers an open problem of <PERSON> from 2006 [WG 2006, LNCS 4271, 37–48]. To obtain the kernelization, we develop the first poly(oPT)- approximation algorithm for ChVD, which is of independent interest. In polynomial time, it either decides that <PERSON> has no chordal deletion set of size k, or outputs a solution of size O(k4 log2 k).", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.91"}, {"primary_key": "3865514", "vector": [], "sparse_vector": [], "title": "On the Configuration-LP of the Restricted Assignment Problem.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We consider the classical problem of scheduling on unrelated machines. In this problem a set of jobs is to be distributed among a set of machines and the maximum load (makespan) is to be minimized. The processing time pij of a job j depends on the machine i it is assigned to. <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> gave a polynomial time 2-approximation for this problem [8]. In this paper we focus on a prominent special case, the restricted assignment problem, in which pij ∊ {pj, ∞}. The configuration-LP is a linear programming relaxation for the restricted assignment problem. It was shown by <PERSON><PERSON> that the multiplicative gap between integral and fractional solution, the integrality gap, is at most 2 – 1/17 ≈ 1.9412 [11]. In this paper we significantly simplify his proof and achieve a bound of 2 – 1/6 ≈ 1.8333. As a direct consequence this provides a polynomial (2 – 1/6 + ∊)-estimation algorithm for the restricted assignment problem by approximating the configuration-LP. The best lower bound known for the integrality gap is 1.5 and no estimation algorithm with a guarantee better than 1.5 exists unless P = NP.MSC codesestimation algorithmsschedulinglocal searchlinear programmingintegrality gap", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.176"}, {"primary_key": "3865515", "vector": [], "sparse_vector": [], "title": "Hardness of Permutation Pattern Matching.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Permutation Pattern Matching (or PPM) is a decision problem whose input is a pair of permutations π and τ, represented as sequences of integers, and the task is to determine whether τ contains a subsequence order-isomorphic to π. <PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON> proved that PPM is NP-complete on general inputs.We show that PPM is NP-complete even when π has no decreasing subsequence of length 3 and τ has no decreasing subsequence of length 4. This provides the first known example of PPM being hard when one or both of π and σ are restricted to a proper hereditary class of permutations.This hardness result is tight in the sense that PPM is known to be polynomial when both π and τ avoid a decreasing subsequence of length 3, as well as when π avoids a decreasing subsequence of length 2. The result is also tight in another sense: we will show that for any hereditary proper subclass c of the class of permutations avoiding a decreasing sequence of length 3, there is a polynomial algorithm solving PPM instances where π is from c and τ is arbitrary.We also obtain analogous hardness and tractability results for the class of so-called skew-merged patterns.From these results, we deduce a complexity dichotomy for the PPM problem restricted to π belonging to Av(α), where Av(α) denotes the class of permutations avoiding a permutation α. Specifically, we show that the problem is polynomial when α is in the set {1,12, 21,132, 213, 231, 312}, and it is NP-complete for any other α.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.24"}, {"primary_key": "3865516", "vector": [], "sparse_vector": [], "title": "Unrelated Machine Scheduling of Jobs with Uniform Smith Ratios.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the classic problem of scheduling jobs on unrelated machines so as to minimize the weighted sum of completion times. Recently, for a small constant ∊ > 0, <PERSON><PERSON> et al. gave a (3/2 – ∊)-approximation algorithm improving upon the “natural” barrier of 3/2 which follows from independent randomized rounding. In simplified terms, their result is obtained by an enhancement of independent randomized rounding via strong negative correlation properties.In this work, we take a different approach and propose to use the same elegant rounding scheme for the weighted completion time objective as devised by <PERSON><PERSON><PERSON><PERSON> and <PERSON> for optimizing a linear function subject to makespan constraints. Our main result is a 1.21-approximation algorithm for the natural special case where the weight of a job is proportional to its processing time (specifically, all jobs have the same Smith ratio), which expresses the notion that each unit of work has the same weight. In addition, as a direct consequence of the rounding, our algorithm also achieves a bi-criteria 2-approximation for the makespan objective. Our technical contribution is a tight analysis of the expected cost of the solution compared to the one given by the Configuration-LP relaxation - we reduce this task to that of understanding certain worst-case instances which are simple to analyze.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.175"}, {"primary_key": "3865517", "vector": [], "sparse_vector": [], "title": "A Hybrid Sampling Scheme for Triangle Counting.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We study the problem of estimating the number of triangles in a graph stream. No streaming algorithm can get sublinear space on all graphs, so methods in this area bound the space in terms of parameters of the input graph such as the maximum number of triangles sharing a single edge. We give a sampling algorithm that is additionally parameterized by the maximum number of triangles sharing a single vertex. Our bound matches the best known turnstile results in all graphs, and gets better performance on simple graphs like G(n,p) or a set of independent triangles.We complement the upper bound with a lower bound showing that no sampling algorithm can do better on those graphs by more than a log factor. In particular, any insertion stream algorithm must use space when all the triangles share a common vertex, and any sampling algorithm must take T1/3 samples when all the triangles are independent. We add another lower bound, also matching our algorithm's performance, which applies to all graph classes. This lower bound covers \"triangle- dependent\" sampling algorithms, a subclass that includes our algorithm and all previous sampling algorithms for the problem.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.116"}, {"primary_key": "3865518", "vector": [], "sparse_vector": [], "title": "Iterative Partial Rounding for Vertex Cover with Hard Capacities.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "We provide a simple and novel algorithmic design technique, for which we call iterative partial rounding, that gives a tight rounding-based approximation for vertex cover with hard capacities (VC-HC). In particular, we obtain an f-approximation for VC-HC on hypergraphs, improving over a previous results of <PERSON> et al. (SODA 2014) to the tight extent. This also closes the gap of approximation since it was posted by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> in (FOCS 2002).Our main technical tool for establishing the approximation guarantee is a separation lemma that certifies the existence of a strong partition for solutions that are basic feasible in an extended version of the natural LP. We believe that our rounding technique is of independent interest when hard constraints are considered.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.174"}, {"primary_key": "3865519", "vector": [], "sparse_vector": [], "title": "Dynamic Planar Voronoi Diagrams for General Distance Functions and their Algorithmic Applications.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We describe a new data structure for dynamic nearest neighbor queries in the plane with respect to a general family of distance functions that includes Lp-norms and additively weighted Euclidean distances, and for general (convex, pair- wise disjoint) sites that have constant description complexity (line segments, disks, etc.). Our data structure has a polylogarithmic update and query time, improving an earlier data structure of <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON> that required O(n∊) time for an update and O(log n) time for a query [1]. Our data structure has numerous applications, and in all of them it gives faster algorithms, typically reducing an O(n∊) factor in the bounds to polylogarithmic. To further demonstrate its effectiveness, we give here two new applications: an efficient construction of a spanner in a disk intersection graph, and a data structure for efficient connectivity queries in a dynamic disk graph.To obtain this data structure, we combine and extend various techniques and obtain several side results that are of independent interest. Our data structure depends on the existence and an efficient construction of “vertical” shallow cuttings in arrangements of bivariate algebraic functions. We prove that an appropriate level in an arrangement of a random sample of a suitable size provides such a cutting. To compute it efficiently, we develop a randomized incremental construction algorithm for finding the lowest k levels in an arrangement of bivariate algebraic functions (we mostly consider here collections of functions whose lower envelope has linear complexity, as is the case in the dynamic nearest- neighbor context). To analyze this algorithm, we improve a longstanding bound on the combinatorial complexity of the vertical decomposition of these levels. Finally, to obtain our structure, we plug our vertical shallow cutting construction into <PERSON>'s algorithm for efficiently maintaining the lower envelope of a dynamic set of planes in ℝ3. While doing this, we also revisit <PERSON>'s technique and present a variant that uses a single binary counter, with a simpler analysis and an improved amortized deletion time.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.165"}, {"primary_key": "3865520", "vector": [], "sparse_vector": [], "title": "(1 + Ω(1))-Αpproximation to MAX-CUT Requires Linear Space.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Madhu <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the problem of estimating the value of MAXCUT in a graph in the streaming model of computation. We show that there exists a constant ∊* > 0 such that any randomized streaming algorithm that computes a (1 + ∊*)- approximation to MAX-CUT requires Ω(n) space on an n vertex graph. By contrast, there are algorithms that produce a (1 + ∊)-approximation in space O(n/∊2) for every ∊ > 0. Our result is the first linear space lower bound for the task of approximating the max cut value and partially answers an open question from the literature [2]. The prior state of the art ruled out (2 - ∊)-approximation in space or (1 + ∊)-approximation in space, for any ∊ > 0.Previous lower bounds for the MAX-CUT problem relied, in essence, on a lower bound on the communication complexity of the following task: Several players are each given some edges of a graph and they wish to determine if the union of these edges is ε-close to forming a bipartite graph, using one-way communication. The previous works proved a lower bound of for this task when ∊ = 1/2, and n1_O(∊) for every ∊ > 0, even when one of the players is given a candidate bipartition of the graph and the graph is promised to be bipartite with respect to this partition or ε-far from bipartite. This added information was essential in enabling the previous analyses but also yields a weak bound since, with this extra information, there is an n1_O(∊) communication protocol for this problem. In this work, we give an O(n) lower bound on the communication complexity of the original problem (without the extra information) for ∊ = Ω(1) in the three-player setting. Obtaining this O(n) lower bound on the communication complexity is the main technical result in this paper. We achieve it by a delicate choice of distributions on instances as well as a novel use of the convolution theorem from Fourier analysis combined with graph-theoretic considerations to analyze the communication complexity.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.112"}, {"primary_key": "3865521", "vector": [], "sparse_vector": [], "title": "Even Delta-Matroids and the Complexity of Planar Boolean CSPs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The main result of this paper is a generalization of the classical blossom algorithm for finding perfect matchings. Our algorithm can efficiently solve Boolean CSPs where each variable appears in exactly two constraints (we call it edge CSP) and all constraints are even Δ-matroid relations (represented by lists of tuples). As a consequence of this, we settle the complexity classification of planar Boolean CSPs started by <PERSON><PERSON> and <PERSON><PERSON>.Knowing that edge CSP is tractable for even Δ-matroid constraints allows us to extend the tractability result to a larger class of Δ-matroids that includes many classes that were known to be tractable before, namely co-independent, compact, local and binary.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.20"}, {"primary_key": "3865522", "vector": [], "sparse_vector": [], "title": "On Max-Clique for intersection graphs of sets and the <PERSON><PERSON><PERSON><PERSON> numbers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Let HDd(p, q) denote the minimal size of a transversal that can always be guaranteed for a family of compact convex sets in ℝd which satisfy the (p, q)-property (p ≥ q ≥ d + 1). In a celebrated proof of the <PERSON><PERSON><PERSON><PERSON><PERSON> conjecture, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> proved that HDd(p, q) exists for all P ≥ q ≥ d +1. Specifically, they prove that HDd(p,d + 1) is This paper has two parts. In the first part we present several improved bounds on HDd(p, q). In particular, we obtain the first near tight estimate of HDd(p, q) for an extended range of values of (p, q) since the 1957 <PERSON><PERSON><PERSON>-<PERSON>runner theorem.In the second part we prove a (p, 2)-theorem for families in ℝ2 with union complexity below a specific quadratic bound. Based on this, we introduce a polynomial time constant factor approximation algorithm for MAX-CLIQUE of intersection graphs of convex sets satisfying this property. It is not likely that our constant factor approximation can be improved to a PTAS as MAX-CLIQUE for intersection graphs of fat ellipses is known to be APX-HARD and fat ellipses have sub-quadratic union complexity.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.148"}, {"primary_key": "3865523", "vector": [], "sparse_vector": [], "title": "A Faster Pseudopolynomial Time Algorithm for Subset Sum.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Given a multiset S of n positive integers and a target integer t, the subset sum problem is to decide if there is a subset of S that sums up to t. We present a new divide-and-conquer algorithm that computes all the realizable subset sums up to an integer u in where σ is the sum of all elements in S and Õ hides polylogarithmic factors. This result improves upon the standard dynamic programming algorithm that runs in O(nu) time. To the best of our knowledge, the new algorithm is the fastest general deterministic algorithm for this problem. We also present a modified algorithm for finite cyclic groups, which computes all the realizable subset sums within the group in time, where m is the order of the group.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.68"}, {"primary_key": "3865524", "vector": [], "sparse_vector": [], "title": "Beyond Highway Dimension: Small Distance Labels Using Tree Skeletons.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The goal of a hub-based distance labeling scheme for a network G = (V, E) is to assign a small subset S(u) $\\subseteq$ V to each node u $\\in$ V, in such a way that for any pair of nodes u, v, the intersection of hub sets S(u) $\\cap$ S(v) contains a node on the shortest uv-path. The existence of small hub sets, and consequently efficient shortest path processing algorithms, for road networks is an empirical observation. A theoretical explanation for this phenomenon was proposed by <PERSON> et al. (SODA 2010) through a network parameter they called highway dimension, which captures the size of a hitting set for a collection of shortest paths of length at least r intersecting a given ball of radius 2r. In this work, we revisit this explanation, introducing a more tractable (and directly comparable) parameter based solely on the structure of shortest-path spanning trees, which we call skeleton dimension. We show that skeleton dimension admits an intuitive definition for both directed and undirected graphs, provides a way of computing labels more efficiently than by using highway dimension, and leads to comparable or stronger theoretical bounds on hub set size.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.95"}, {"primary_key": "3865525", "vector": [], "sparse_vector": [], "title": "Polynomial Kernels and Wideness Properties of Nowhere Dense Graph Classes.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Nowhere dense classes of graphs [21, 22] are very general classes of uniformly sparse graphs with several seemingly unrelated characterisations. From an algorithmic perspective, a characterisation of these classes in terms of uniform quasi-wideness, a concept originating in finite model theory, has proved to be particularly useful. Uniform quasi-wideness is used in many fpt-algorithms on nowhere dense classes. However, the existing constructions showing the equivalence of nowhere denseness and uniform quasi-wideness imply a non-elementary blow up in the parameter dependence of the fpt-algorithms, making them infeasible in practice.As a first main result of this paper, we use tools from logic, in particular from a sub-field of model theory known as stability theory, to establish polynomial bounds for the equivalence of nowhere denseness and uniform quasi-wideness.As an algorithmic application of our new methods, we obtain for every fixed value of r ∊ ℕ a polynomial kernel for the distance-r dominating set problem on nowhere dense classes of graphs. This is particularly interesting, as it implies that for every subgraph-closed class C, the distance-r dominating set problem admits a kernel on C for every value of r if, and only if, it admits a polynomial kernel for every value of r (under the standard assumption of parameterized complexity theory that FPT ≠ W[2]).Finally, we demonstrate how to use the new methods to improve the parameter dependence of many fixed- parameter algorithms. As an example we provide a single exponential parameterized algorithm for the Connected Dominating Set problem on nowhere dense graph classes.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.100"}, {"primary_key": "3865526", "vector": [], "sparse_vector": [], "title": "A Framework for Analyzing Resparsification Algorithms.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A spectral sparsifier of a graph G is a sparser graph H that approximately preserves the quadratic form of G, i.e., for all vectors x, xTLGx ≈ xTLHx, where LG and LH denote the respective graph Laplacians. Spectral sparsifiers generalize cut sparsifiers, and have found many applications in designing graph algorithms. In recent years, there has been interest in computing spectral sparsifiers in semi-streaming and dynamic settings. Natural algorithms in these settings often involve repeated sparsification of a graph, and in turn accumulation of errors across these steps. We present a framework for analyzing algorithms that perform repeated sparsifications that only incur error corresponding to a single sparsification step, leading to better results for many of these reseparsification based algorithms.As an application, we show how to maintain a spectral sparsifier in the semi-streaming setting: We present a simple algorithm that, for a graph G on n vertices and m edges, computes a spectral sparsifier of G with O(n log n) edges in a single pass over G, using only O(n log n) space, and O(m log2 n) total time. This improves on previous best semi-streaming algorithms for both spectral and cut sparsifiers by a factor of log n in both space and runtime. The algorithm also extends to semi-streaming row sampling for general PSD matrices. As another application, we use this framework to combine a spectral sparsification algorithm by <PERSON><PERSON><PERSON> with improved spanner constructions to give a parallel algorithm for constructing O(n log2 n log log n) sized spectral sparsifiers in O(m log2 n log log n) time. This is the best combinatorial graph sparsification algorithm to date, and the size of the sparsifiers produced is only a factor log n log log n more than ones produced by numerical routines.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.132"}, {"primary_key": "3865527", "vector": [], "sparse_vector": [], "title": "Faster Online Matrix-Vector Multiplication.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the Online Boolean Matrix-Vector Multiplication (OMV) problem studied by <PERSON><PERSON><PERSON> et al. [STOC'15]: given an n × n Boolean matrix M, we receive n Boolean vectors v1,…, vn one at a time, and are required to output Mvi (over the Boolean semiring) before seeing the vector vi+1, for all i. Previous known algorithms for this problem are combinatorial, running in O(n3/log2 n) time. <PERSON><PERSON><PERSON> et al. conjecture there is no O(n3-∊) time algorithm for OMV, for all ∊ > 0; their OMV conjecture is shown to imply strong hardness results for many basic dynamic problems.We give a substantially faster method for computing OMV, running in randomized time. In fact, after seeing vectors, we already achieve amortized time for matrix-vector multiplication. Our approach gives a way to reduce matrix-vector multiplication to solving a version of the Orthogonal Vectors problem, which in turn reduces to \"small\" algebraic matrix-matrix multiplication. Applications include faster independent set detection, partial match retrieval, and 2-CNF evaluation.We also show how a modification of our method gives a cell probe data structure for OMV with worst case time per query vector, where w is the word size. This result rules out an unconditional proof of the OMV conjecture using purely information-theoretic arguments.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.142"}, {"primary_key": "3865528", "vector": [], "sparse_vector": [], "title": "Partitioning a Graph into Small Pieces with Applications to Path Transversal.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Lee"], "summary": "Given a graph G = (V, E) and an integer k ∊ ℕ, we study k-Vertex Separator (resp. k-Edge Separator), where the goal is to remove the minimum number of vertices (resp. edges) such that each connected component in the resulting graph has at most k vertices. Our primary focus is on the case where k is either a constant or a slowly growing function of n (e.g. O(logn) or no(1)). Our problems can be interpreted as a special case of three general classes of problems that have been studied separately (balanced graph partitioning, Hypergraph Vertex Cover (HVC), and fixed parameter tractability (FPT)).Our main result is an O(log k)-approximation algorithm for k-Vertex Separator that runs in time 2O(k)nO(1), and an O(log k)-approximation algorithm for k-Edge Separator that runs in time nO(1). Our result on k-Edge Separator improves the best previous graph partitioning algorithm [24] for small k. Our result on k-Vertex Separator improves the simple (k + 1)- approximation from HVC [3]. When OPT > k, the running time 2O(k)nO(1) is faster than the lower bound kΩ(OPT)n°(1) for exact algorithms assuming the Exponential Time Hypothesis [12]. While the running time of 2O(k)nO(1) for k-Vertex Separator seems unsatisfactory, we show that the superpolynomial dependence on k may be needed to achieve a polylogarithmic approximation ratio, based on hardness of Densest k-Subgraph.We also study k-Path Transversal, where the goal is to remove the minimum number of vertices such that there is no simple path of length k. With additional ideas from FPT algorithms and graph theory, we present an O(log k)-approximation algorithm for k-Path Transversal that runs in time 2O(k log k)nO(1). Previously, the existence of even (1 - δ)k-approximation algorithm for fixed δ > 0 was open [9].", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.101"}, {"primary_key": "3865529", "vector": [], "sparse_vector": [], "title": "Counting matchings in irregular bipartite graphs and random lifts.", "authors": ["<PERSON>"], "summary": "We give a sharp lower bound on the number of matchings of a given size in a bipartite graph. When specialized to regular bipartite graphs, our results imply <PERSON><PERSON><PERSON><PERSON><PERSON>'s theorem and <PERSON><PERSON><PERSON>'s Lower Matching Conjecture proven by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>. Indeed, our work extends the recent work of <PERSON><PERSON><PERSON><PERSON><PERSON> done for regular and bi-regular bipartite graphs. Moreover, our lower bounds are order optimal as they are attained for a sequence of 2-lifts of the original graph as well as for random n-lifts of the original graph when n tends to infinity.We then extend our results to permanents and sub- permanents sums. For permanents, we are able to recover the lower bound of <PERSON><PERSON><PERSON><PERSON><PERSON> recently proved by <PERSON><PERSON><PERSON><PERSON> using stable polynomials. Our proof is algorithmic and borrows ideas from the theory of local weak convergence of graphs, statistical physics and covers of graphs. We provide new lower bounds for subpermanents sums and obtain new results on the number of matchings in random n-lifts with some implications for the matching measure and the spectral measure of random n-lifts as well as for the spectral measure of infinite trees.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.146"}, {"primary_key": "3865530", "vector": [], "sparse_vector": [], "title": "Computing Walrasian Equilibria: Fast Algorithms and Structural Properties.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present the first polynomial time algorithm for computing Walrasian equilibrium in an economy with indivisible goods and general buyer valuations having only access to an aggregate demand oracle, i.e., an oracle that given prices on all goods, returns the aggregated demand over the entire population of buyers. For the important special case of gross substitute valuations, our algorithm queries the aggregate demand oracle Õ(n) times and takes Õ(n3) time, where n is the number of goods. At the heart of our solution is a method for exactly minimizing certain convex functions which cannot be evaluated but for which the subgradients can be computed.We also give the fastest known algorithm for computing Walrasian equilibrium for gross substitute valuations in the value oracle model. Our algorithm has running time Õ((mn + n3)TV) where TV is the cost of querying the value oracle. A key technical ingredient is to regularize a convex programming formulation of the problem in a way that subgradients are cheap to compute. En route, we give necessary and sufficient conditions for the existence of robust Walrasian prices, i.e., prices for which each agent has a unique demanded bundle and the demanded bundles clear the market. When such prices exist, the market can be perfectly coordinated by solely using prices.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.42"}, {"primary_key": "3865531", "vector": [], "sparse_vector": [], "title": "Constant Approximation Algorithm for Non-Uniform Capacitated Multi-Item Lot-Sizing via Strong Covering Inequalities.", "authors": ["<PERSON>"], "summary": "We study the non-uniform capacitated multi-item lot-sizing (CMILS) problem. In this problem, there is a set of demands over a planning horizon of T time periods and all demands must be satisfied on time. We can place an order at the beginning of each period s, incurring an ordering cost Ks. The total quantity of all products ordered at time s can not exceed a given capacity Cs. On the other hand, carrying inventory from time to time incurs inventory holding cost. The goal of the problem is to find a feasible solution that minimizes the sum of ordering and holding costs.<PERSON> et al. (<PERSON>, <PERSON> and <PERSON><PERSON>, Mathmatics of Operations Research 33(2), 2008) gave a 2-approximation for the problem when the capacities Cs are the same. In this paper, we extend their result to the case of non-uniform capacities. That is, we give a constant approximation algorithm for the capacitated multi-item lot-sizing problem with general capacities.The constant approximation is achieved by adding an exponentially large set of new covering inequalities to the natural facility-location type linear programming relaxation for the problem. Along the way of our algorithm, we reduce the CMILS problem to two generalizations of the classic knapsack covering problem. We give LP-based constant approximation algorithms for both generalizations, via the iterative rounding technique.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.152"}, {"primary_key": "3865532", "vector": [], "sparse_vector": [], "title": "Beating Brute Force for Systems of Polynomial Equations over Finite Fields.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Huacheng Yu"], "summary": "We consider the problem of solving systems of multivariate polynomial equations of degree k over a finite field. For every integer k ≤ 2 and finite field q where q = pd for a prime p, we give, to the best of our knowledge, the first algorithms that achieve an exponential speedup over the brute force O(qn) time algorithm in the worst case. We present two algorithms, a randomized algorithm with running time qn+o(n) · q−n/O(k) time if q < 24ekd, and otherwise, where e = 2.718… is <PERSON>'s constant, and a deterministic algorithm for counting solutions with running time qn+o(n) · q−n/O(kq6/7d). For the important special case of quadratic equations in F2, our randomized algorithm has running time O(20.8765n).For systems over 2 we also consider the case where the input polynomials do not have bounded degree, but instead can be efficiently represented as a ΣΠΣ circuit, i.e., a sum of products of sums of variables. For this case we present a deterministic algorithm running in time 2n-dn for δ = 1/O(log(s/n)) for instances with s product gates in total and n variables.Our algorithms adapt several techniques recently developed via the polynomial method from circuit complexity. The algorithm for systems of ΣΠΣ polynomials also introduces a new degree reduction method that takes an instance of the problem and outputs a subexponential-sized set of instances, in such a way that feasibility is preserved and every polynomial among the output instances has degree O(log(s/n)).", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.143"}, {"primary_key": "3865533", "vector": [], "sparse_vector": [], "title": "An FPTAS for Counting Proper Four-Colorings on Cubic Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Graph coloring is arguably the most exhaustively studied problem in the area of approximate counting. It is conjectured that there is a fully polynomial-time (randomized) approximation scheme (FPTAS/FPRAS) for counting the number of proper colorings as long as q ≥ Δ + 1, where q is the number of colors and Δ is the maximum degree of the graph. The bound of q = Δ + 1 is the uniqueness threshold for Gibbs measure on Δ-regular infinite trees. However, the conjecture remained open even for any fixed Δ > 3 (The cases of Δ = 1, 2 are trivial). In this paper, we design an FP- TAS for counting the number of proper four-colorings on graphs with maximum degree three and thus confirm the conjecture in the case of Δ = 3. This is the first time to achieve this optimal bound of q = Δ + 1. Previously, the best FPRAS requires and the best deterministic FPTAS requires q > 2.581Δ + 1 for general graphs. In the case of Δ = 3, the best previous result is an FPRAS for counting proper 5-colorings. We note that there is a barrier to go beyond q = Δ + 2 for single-site Glauber dynamics based FPRAS and we overcome this by correlation decay approach. Moreover, we develop a number of new techniques for the correlation decay approach which can find applications in other approximate counting problems.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.117"}, {"primary_key": "3865534", "vector": [], "sparse_vector": [], "title": "A polynomial time algorithm to compute quantum invariants of 3-manifolds with bounded first Betti number.", "authors": ["<PERSON><PERSON><PERSON> Maria", "<PERSON>"], "summary": "In this article, we introduce a fixed parameter tractable algorithm for computing the Turaev-Viro invariants TV4,q, using the dimension of the first homology group of the manifold as parameter.This is, to our knowledge, the first parameterised algorithm in computational 3-manifold topology using a topological parameter. The computation of TV4,q is known to be #P-hard in general; using a topological parameter provides an algorithm polynomial in the size of the input triangulation for the extremely large family of 3-manifolds with first homology group of bounded rank.Our algorithm is easy to implement and running times are comparable with running times to compute integral homology groups for standard libraries of triangulated 3- manifolds. The invariants we can compute this way are powerful: in combination with integral homology and using standard data sets we are able to roughly double the pairs of 3-manifolds we can distinguish.We hope this qualifies TV4,q to be added to the short list of standard properties (such as orientability, connectedness, Betti numbers, etc.) that can be computed ad-hoc when first investigating an unknown triangulation.MSC codesfixed parameter tractable algorithmsTuraev-Viro invariantstriangulations of 3-manifolds(integral) homologyalmost normal surfacescombinatorial algorithms", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.180"}, {"primary_key": "3865535", "vector": [], "sparse_vector": [], "title": "Explicit Resilient Functions Matching Ajtai-Linial.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "A Boolean function on n variables is q-resilient if for any subset of at most q variables, the function is very likely to be determined by a uniformly random assignment to the remaining n - q variables; in other words, no coalition of at most q variables has significant influence on the function. Resilient functions have been extensively studied with a variety of applications in cryptography, distributed computing, and pseudorandomness. The best known resilient function on n variables due to <PERSON><PERSON><PERSON> and <PERSON><PERSON> [AL93] has the property that only sets of size Ω (n/(log2 n)) can have influence bounded away from zero. However, the construction of <PERSON><PERSON><PERSON> and <PERSON><PERSON> is by the probabilistic method and does not give an efficiently computable function.We construct an explicit monotone depth three almost- balanced Boolean function on n bits that is Ω (n/(log2 n))- resilient matching the bounds of <PERSON><PERSON><PERSON> and <PERSON><PERSON>. The best previous explicit constructions of Meka [Mek09] (which only gives a logarithmic depth function), and <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [CZ15] were only (n1-β)-resilient for any constant 0 < β < 1. Our construction and analysis are motivated by (and simplifies parts of) the recent breakthrough of [CZ15] giving explicit two-sources extractors for polylogarithmic min-entropy; a key ingredient in their result was the construction of explicit constant-depth resilient functions.An important ingredient in our construction is a new randomness-optimal oblivious sampler that preserves moment generating functions of sums of variables and could be useful elsewhere.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.73"}, {"primary_key": "3865536", "vector": [], "sparse_vector": [], "title": "The Rainbow at the End of the Line - A PPAD Formulation of the Colorful Carathéodory Theorem with Applications.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Let C1,…, Cd+i be d + 1 point sets in ℝd, each containing the origin in its convex hull. A subset C of is called a colorful choice (or rainbow) for C1,…, Cd+1, if it contains exactly one point from each set Ci. The colorful Carathéodory theorem states that there always exists a colorful choice for C1,…, Cd+1 that has the origin in its convex hull. This theorem is very general and can be used to prove several other existence theorems in high-dimensional discrete geometry, such as the centerpoint theorem or <PERSON><PERSON><PERSON>'s theorem. The colorful Carathéodory problem (ColorfulCarathéodory) is the computational problem of finding such a colorful choice. Despite several efforts in the past, the computational complexity of ColorfulCarathéodory in arbitrary dimension is still open.We show that ColorfulCarathéodory lies in the intersection of the complexity classes PPAD and PLS. This makes it one of the few geometric problems in PPAD and PLS that are not known to be solvable in polynomial time. Moreover, it implies that the problem of computing centerpoints, computing Tverberg partitions, and computing points with large simplicial depth is contained in PPAD Π PLS. This is the first nontrivial upper bound on the complexity of these problems.Finally, we show that our PPAD formulation leads to a polynomial-time algorithm for a special case of ColorfulCarathéodory in which we have only two color classes C1 and C2 in d dimensions, each with the origin in its convex hull, and we would like to find a set with half the points from each color class that contains the origin in its convex hull.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.87"}, {"primary_key": "3865537", "vector": [], "sparse_vector": [], "title": "Online and Random-order Load Balancing Simultaneously.", "authors": ["<PERSON>"], "summary": "We consider the problem of online load balancing under ℓp-norms: sequential jobs need to be assigned to one of the machines and the goal is to minimize the ℓp-norm of the machine loads. This generalizes the classical problem of scheduling for makespan minimization (case and has been thoroughly studied. However, despite the recent push for beyond worst-case analyses, no such results are known for this problem.In this paper we provide algorithms with simultaneous guarantees for the worst-case model as well as for the random-order (i.e. secretary) model, where an arbitrary set of jobs comes in random order. First, we show that the greedy algorithm (with restart), known to have optimal O(p) worst-case guarantee, also has a (typically) improved random-order guarantee. However, the behavior of this algorithm in the random-order model degrades with p. We then propose algorithm SimultaneousLB that has simultaneously optimal guarantees (within constants) in both worst-case and random- order models. In particular, the random-order guarantee of SimultaneousLB improves as p increases.One of the main components is a new algorithm with improved regret for Online Linear Optimization (OLO) over the non-negative vectors in the ℓq ball. Interestingly, this OLO algorithm is also used to prove a purely probabilistic inequality that controls the correlations arising in the random-order model, a common source of difficulty for the analysis. Another important component used in both SimultaneousLB and our OLO algorithm is a smoothing of the ℓp-norm that may be of independent interest. This smoothness property allows us to see algorithm SimultaneousL<PERSON> as essentially a greedy one in the worst-case model and as a primal-dual one in the random-order model, which is instrumental for its simultaneous guarantees.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.108"}, {"primary_key": "3865538", "vector": [], "sparse_vector": [], "title": "Split Packing: An Algorithm for Packing Circles with Optimal Worst-Case Density.", "authors": ["<PERSON>"], "summary": "In the classic circle packing problem, one asks whether a given set of circles can be packed into the unit square. This problem is known to be NP-hard. In this paper, we present a new sufficient condition using only the circles’ combined area: It is possible to pack any circle instance with a combined area of up to ≈ 0.5390. This bound is tight, in the sense that for any larger combined area, there are instances which cannot be packed, which is why we call this number the problem's critical density. Similar results have long been known for squares, but to the best of our knowledge, this paper gives the first results of this type for circular objects.Our proof is constructive: We describe a subdivision scheme which recursively splits the circles into groups and then packs these into subcontainers. We call this algorithm Split Packing. Beside realizing all packings up to the critical density bound, Split Packing also serves as a constant-factor approximation algorithm when looking for the smallest square in which a given set of circles can be packed.We believe that the ideas behind Split Packing are interesting and elegant on their own, and we see many opportunities to apply this technique in the context of other packing and covering problems.A browser-based, interactive visualization of the Split Packing approach and other related material can be found at https://morr.cc/split-packing/.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.7"}, {"primary_key": "3865539", "vector": [], "sparse_vector": [], "title": "Space-Efficient Construction of Compressed Indexes in Deterministic Linear Time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We show that the compressed suffix array and the compressed suffix tree of a string T can be built in O(n) deterministic time using O(n log σ) bits of space, where n is the string length and σ is the alphabet size. Previously described deterministic algorithms either run in time that depends on the alphabet size or need ω(n log σ) bits of working space. Our result has immediate applications to other problems, such as yielding the first deterministic linear-time LZ77 and LZ78 parsing algorithms that use O(n log σ) bits.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.26"}, {"primary_key": "3865540", "vector": [], "sparse_vector": [], "title": "A constant-time algorithm for middle levels Gray codes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "For any integer n ≥ 1 a middle levels Gray code is a cyclic listing of all n-element and (n + 1)- element subsets of {1,2,…, 2n +1} such that any two consecutive subsets differ in adding or removing a single element. The question whether such a Gray code exists for any n ≥ 1 has been the subject of intensive research during the last 30 years, and has been answered affirmatively only recently [<PERSON><PERSON> Proof of the middle levels conjecture. To appear in Proc. London Math. Soc., 2014]. In a follow-up paper [<PERSON><PERSON> and <PERSON><PERSON>. An efficient algorithm for computing a middle levels Gray code. Proc. ESA, 2015] this existence proof was turned into an algorithm that computes each new set in the Gray code in time O(n) on average. In this work we complete this line of research by presenting an algorithm for computing a middle levels Gray code in optimal time and space: Each new set is generated in time O(1), and the required space is O(n).", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.147"}, {"primary_key": "3865541", "vector": [], "sparse_vector": [], "title": "Probabilistic clustering of high dimensional norms.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Separating decompositions of metric spaces are an important randomized clustering paradigm that was formulated by <PERSON><PERSON> in [Bar96] and is defined as follows. Given a metric space (X, dx), its modulus of separated decomposability, denoted SEP(X, dx), is the infimum over those σ ∊ (0,∞] such that for every finite subset S ⊆ X and every Δ > 0 there exists a distribution over random partitions P of S into sets of diameter at most Δ such that for every x,y ∊ S the probability that both x and y do not fall into the same cluster of the random partition P is at most σdx (x, y)/Δ. Here we obtain new bounds on SEP(X, ‖·‖x) when (X, ‖· ‖x) is a finite dimensional normed space, yielding, as a special case, that for every n ∊ N. More generally, for every p ∊ [2, ∞]. This improves over the work [CCG+ 98] of <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, who obtained this bound when p = 2, yet for p ∊ (2, ∞] they obtained the asymptotically weaker estimate One should note that it was claimed in [CCG+98] that the bound is sharp for every p ∊ [2, ∞], and in particular it was claimed in [CCG+98] that However, the above results show that this claim of [CCG+98] is incorrect for every p ∊ (2, ∞]. Our new bounds on the modulus of separated decomposability rely on extremal results for orthogonal hyperplane projections of convex bodies, specifically using the work [BN02] of Barthe and the author. This yields additional refined estimates, an example of which is that for every n ∊ ℕ and k ∊ {1,…,n} we have where denotesthe subset of ℝn consisting of all those vectors that have at most k nonzero entries, equipped with the Euclidean metric.The above statements have implications to the Lip- schitz extension problem through its connection to random partitions that was developed by Lee and the author in [LN04, LN05]. Given a metric space (X,dx), let e(X) denote the infimum over those K ∊ (0, ∞] such that for every Banach space Y and every subset S ⊆ X, every 1-Lipschitz function f : S → Y has a K-Lipschitz extension to all of X. Johnson, Lindenstrauss and Schechtman proved in [JLS86] that e(X) < dim(X) for every finite dimensional normed space (X, ‖ · ‖x). It is a longstanding open problem to determine the correct asymptotic dependence on dim(X) in this context, with the best known lower bound, due to Johnson and Lindenstrauss [JL84], being that the quantity e(X) must sometimes be at least a constant multiple of In particular, the previously best known upper bound on e(ℓn∞) was the O(n) estimate of [JLS86]. It is shown here that for every n ∊ ℕ we have thus answering (up to logarithmic factors) a question that was posed by Brudnyi and Brudnyi in [BB05, Problem 2]. More generally, for every p ∊ [2, ∞], thus resolving (negatively) a conjecture of Brudnyi and Brudnyi in [BB05, Conjecture 5].", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.44"}, {"primary_key": "3865542", "vector": [], "sparse_vector": [], "title": "Accurate and Nearly Optimal Sublinear Approximations to Ulam Distance.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The Ulam distance between two permutations of length η is the minimum number of insertions and deletions needed to transform one sequence into the other. Equivalently, the Ulam distance d is n minus the length of the longest common subsequence (LCS) between the permutations. Our main result is an algorithm, that for any fixed ∊ > 0, provides a (1 + ∊)-multiplicative approximation for d in time, which has been shown to be optimal up to polylogarithmic factors. This is the first sublinear time algorithm (provided that d = (log n)ω(1)) that obtains arbitrarily good multiplicative approximations to the Ulam distance. The previous best bound is an O(1)-approximation (with a large constant) by <PERSON><PERSON> and <PERSON><PERSON><PERSON> (2010) with the same running time bound (ignoring polylogarithmic factors).The improvement in the approximation factor from O(1) to (1 + ∊) allows for significantly more powerful sublinear algorithms. For example, for any fixed δ > 0, we can get additive δη approximations for the LCS between permutations in time. Previous sublinear algorithms require δ to be at least 1–1/C, where c is the approximation factor, which is close to 1 when c is large.Our algorithm is obtained by abstracting the basic algorithmic framework of <PERSON><PERSON> and <PERSON><PERSON><PERSON>, and combining it with the sublinear approximations for the longest increasing subsequence by <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (2010).", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.131"}, {"primary_key": "3865543", "vector": [], "sparse_vector": [], "title": "A Treehouse with Custom Windows: Minimum Distortion Embeddings into Bounded Treewidth Graphs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We describe a (1 + ∊)-approximation algorithm for finding the minimum distortion embedding of an n-point metric space X into the shortest path metric space of a weighted graph G with m vertices. The running time of our algorithm is parametrized by the values of the minimum distortion, δopt, the spread, Δ, of the points of X, the treewidth, ω, of G, and the doubling dimension, λ, of G.In particular, our result implies a PTAS provided an X with polynomial spread, and the doubling dimension of G, the treewidth of G, and δopt, are all constant. For example, if X has a polynomial spread and δopt is a constant, we obtain PTAS's for embedding X into the following spaces: the line, a cycle, a tree of bounded doubling dimension, and a k-outer planar graph of bounded doubling dimension (for a constant k).", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.46"}, {"primary_key": "3865544", "vector": [], "sparse_vector": [], "title": "Testing for Forbidden Order Patterns in an Array.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we study testing of sequence properties that are defined by forbidden order patterns. A sequence f : {1,…, n} → ℝ of length n contains a pattern is the group of permutations of k elements), iff there are indices i1 f (iy) whenever π(χ) > π(y). If f does not contain π, we say f is π-free. For example, for π = (2,1), the property of being π-free is equivalent to being non-decreasing, i.e. monotone. The property of being (k,k — 1,…, 1)-free is equivalent to the property of having a partition into at most k - 1 non-decreasing subsequences.Let k constant, be a (forbidden) pattern. Assuming f is stored in an array, we consider the property testing problem of distinguishing the case that f is π-free from the case that f differs in more than en places from any π-free sequence. We show the following results: There is a clear dichotomy between the monotone patterns and the non-monotone ones:•For monotone patterns of length k, i.e., (k,k - 1,…, 1) and (1, 2,…, k), we design non-adaptive one-sided error ε-tests of (∊−1 log n)O(k2) query complexity.•For non-monotone patterns, we show that for any size-k non-monotone π, any non-adaptive one-sided error ε-test requires at least Ω(γ/η) queries. This general lower bound can be further strengthened for specific non-monotone k-length patterns to Ω(n1–2/(k+1)).On the other hand, there always exists a non- adaptive one-sided error ε-test for with O(e−1/kn1–1/k) query complexity Again, this general upper bound can be further strengthened for specific non-monotone patterns. E.g., for π = (1, 3, 2), we describe an ε-test with (almost tight) query complexity of Finally, we show that adaptivity can make a big difference in testing non-monotone patterns, and develop an adaptive algorithm that for any tests π-freeness by making (∊−1 logn)O(1) queries.For all algorithms presented here, the running times are linear in their query complexity.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.104"}, {"primary_key": "3865545", "vector": [], "sparse_vector": [], "title": "An O(nm) time algorithm for finding the min length directed cycle in a graph.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this paper, we introduce an O(nm) time algorithm to determine the minimum length directed cycle (also called the \"minimum weight directed cycle\") in a directed network with n nodes and m arcs and with no negative length directed cycles.This result improves upon the previous best time bound of O(nm + n 2 log log n).Our algorithm first determines the cycle with minimum mean length λ * in O(nm) time.Subsequently, it chooses node potentials so that all reduced costs are λ * or greater.It then solves the all pairs shortest path problem, but restricts attention to paths of length at most nλ * .We speed up the shortest path calculations to O(m) per source node, leading to an O(nm) running time in total.We also carry out computational experiments comparing the performance of the proposed methods and other state-of-the-art methods.Experiments confirmed that it is advantageous to solve the minimum mean cycle problem prior to solving shortest path problems.Analysis of our experiments suggest that the running time to solve the minimum length directed cycle problem was much faster than O(n 2 ) on average. Introduction.We address the determination of the Minimum Length Directed Cycle (MLDC) in a graph G = (V, A) with n nodes and m arcs and with no negative length directed cycles.(Elsewhere, researchers have referred to the MLDC as the minimum weight directed cycle or the minimum cost directed cycle.)<PERSON> [12] and <PERSON><PERSON> [32] showed how to solve this problem in O(n 3 ) time.An alternative approach is to find shortest paths between all pairs of nodes.In case there are negative length arcs, the first shortest path problem is solved using the label correcting algorithm.Subsequently, one can use reduced costs to transform the problem into an equivalent problem with nonnegative arc lengths.The subsequent n -1 shortest path problems are solved using Dijkstra's Algorithm.Using the shortest path algorithm of Fredman and Tarjan [14], the running time", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.122"}, {"primary_key": "3865546", "vector": [], "sparse_vector": [], "title": "Matrix Balancing in Lp Norms: Bounding the Convergence Rate of Osborne&apos;s Iteration.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study an iterative matrix conditioning algorithm due to <PERSON> (1960). The goal of the algorithm is to convert a square matrix into a balanced matrix where every row and corresponding column have the same norm. The original algorithm was proposed for balancing rows and columns in the L2 norm, and it works by iterating over balancing a row-column pair in fixed round-robin order. Variants of the algorithm for other norms have been heavily studied and are implemented as standard preconditioners in many numerical linear algebra packages. Recently, <PERSON><PERSON><PERSON> and <PERSON> (2015), in a first result of its kind for any norm, analyzed the rate of convergence of a variant of <PERSON>'s algorithm that uses the L∞ norm and a different order of choosing row-column pairs. In this paper we study matrix balancing in the L1 norm and other Lp norms. We show the following results for any matrix , resolving in particular a main open problem mentioned by <PERSON><PERSON><PERSON> and <PERSON>.1.We analyze the iteration for the L1 norm under a greedy order of balancing. We show that it converges to an ∊-balanced matrix in K = O(min{ ∊−2 log w, ∊−1n3/2 log(w / ∊)}) iterations that cost a total of O(m + Kn log n) arithmetic operations over O(n log(w/∊))-bit numbers. Here m is the number of non-zero entries of A, and w =∑i,j |aij|/amin with amin = min{|aij| : aj ≠ 0}.2.We show that the original round-robin implementation converges to an ∊ -balanced matrix in O(∊−2n2 log w) iterations totaling O(∊−2mn log w) arithmetic operations over O(nlog(w/∊))-bit numbers.3.We show that a random implementation of the iteration converges to an ∊ -balanced matrix in O(∊−2 log w) iterations using O(m + ∊−2n log w) arithmetic operations over O(log(wn/∊))-bit numbers.4.We demonstrate a lower bound of on the convergence rate of any implementation of the iteration.5.We observe, through a known trivial reduction, that our results for L1 balancing apply to any Lp norm for all finite p, at the cost of increasing the number of iterations by only a factor of p.We note that our techniques are very different from those used by Schulman and Sinclair.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.11"}, {"primary_key": "3865547", "vector": [], "sparse_vector": [], "title": "A (2 + ∊)-Approximation for Maximum Weight Matching in the Semi-Streaming Model.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We present a simple deterministic single-pass (2 + ∊)-approximation algorithm for the maximum weight matching problem in the semi-streaming model. This improves upon the currently best known approximation ratio of (3.5 + ∊).Our algorithm uses O(n log2 n) space for constant values of e. It relies on a variation of the local-ratio theorem, which may be of independent interest in the semi-streaming model.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.140"}, {"primary_key": "3865548", "vector": [], "sparse_vector": [], "title": "Totally Unimodular Congestion Games.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We investigate new class of congestion games, called Totally Unimodular (TU) Congestion Games, where the players' strategies are binary vectors inside polyhedra defined by totally unimodular constraint matrices. Network congestion games belong to this class.In the symmetric case, when all players have the same strategy set, we design an algorithm that finds an optimal aggregated strategy and then decomposes it into the single players' strategies. This approach yields strongly polynomial-time algorithms to (i) find a pure Nash equilibrium, and (ii) compute a socially optimal state, if the delay functions are weakly convex. We also show how this technique can be extended to matroid congestion games.We then introduce some combinatorial TU congestion games, where the players'strategies are matchings, vertex covers, edge covers, and stable sets of a given bipartite graph. In the asymmetric case, we show that for these games (i) it is PLS-complete to find a pure Nash equilibrium even in case of linear delay functions, and (ii) it is NP-hard to compute a socially optimal state, even in case of weakly convex delay functions.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.37"}, {"primary_key": "3865549", "vector": [], "sparse_vector": [], "title": "Decidability of the Membership Problem for 2 × 2 integer matrices.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The main result of this paper is the decidability of the membership problem for 2 × 2 nonsingular integer matrices. Namely, we will construct the first algorithm that for any nonsingular 2 × 2 integer matrices M1,…, <PERSON>n and <PERSON> decides whether <PERSON> belongs to the semigroup generated by {M1,…, Mn}. Our algorithm relies on a translation of numerical problems on matrices into combinatorial problems on words. It also makes use of some algebraic properties of well-known subgroups of GL(2, Z) and various new techniques and constructions that help to convert matrix equations into the emptiness problem for intersection of regular languages.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.12"}, {"primary_key": "3865550", "vector": [], "sparse_vector": [], "title": "LP Relaxations of Some NP-Hard Problems Are as Hard as Any LP.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We show that solving linear programming (LP) relaxations of many classical NP-hard combinatorial optimization problems is as hard as solving the general LP problem. Precisely, the general LP can be reduced in linear time to the LP relaxation of each of these problems. This result poses a fundamental limitation for designing efficient algorithms to solve the LP relaxations, because finding such an algorithm might improve the complexity of best known algorithms for the general LP. Besides linear-time reductions, we show that the LP relaxations of the considered problems are P-complete under log-space reduction, therefore also hard to parallelize.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.89"}, {"primary_key": "3865551", "vector": [], "sparse_vector": [], "title": "Sandpile prediction on a tree in near linear time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In the sandpile model, we are given an undirected graph G and an initial list of chip counts on each vertex of G and we may fire degree(v) chips from any vertex v to its neighbors. Doing chip moves either results in a unique terminal configuration or recurs forever. On many families of graphs - including trees - the problem of computing the final configuration is P-complete [13] and simulation can take as long as Θ(n3) time. We give a O(n log5 n) time algorithm for trees that computes the terminal configuration or shows that chip firing will not terminate.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.72"}, {"primary_key": "3865552", "vector": [], "sparse_vector": [], "title": "Combinatorial Prophet Inequalities.", "authors": ["<PERSON>via<PERSON>", "<PERSON><PERSON>"], "summary": "We introduce a novel framework of Prophet Inequalities for combinatorial valuation functions. For a (non-monotone) submodular objective function over an arbitrary matroid feasibility constraint, we give an O(1)-competitive algorithm. For a monotone subadditive objective function over an arbitrary downward- closed feasibility constraint, we give an O(log n log2 r)- competitive algorithm (where r is the cardinality of the largest feasible subset).Inspired by the proof of our subadditive prophet inequality, we also obtain an O(log n · log2 r)-competitive algorithm for the Secretary Problem with a monotone subadditive objective function subject to an arbitrary downward-closed feasibility constraint. Even for the special case of a cardinality feasibility constraint, our algorithm circumvents an lower bound by <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [10] in a restricted query model.En route to our submodular prophet inequality, we prove a technical result of independent interest: we show a variant of the Correlation Gap Lemma [14, 1] for nonmonotone submodular functions.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.110"}, {"primary_key": "3865553", "vector": [], "sparse_vector": [], "title": "Sorting from Noisier Samples.", "authors": ["<PERSON>via<PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of constructing an order over a set of elements given noisy samples. We consider two models for generating the noisy samples; in both, the distribution of samples is induced by an unknown state of nature: a permutation ρ. In <PERSON><PERSON>'s model, r permutations ni are generated independently from p, each with probability proportional to e−ßdK(ρ,πί), where dK (p, πi) is the Kemeny distance between ρ and ni - the number of pairs they order differently. In the noisy comparisons model, we are given a tournament, generated from ρ as follows: if i is before j in p, then with probability 1/2 + γ, the edge between them is oriented from i to j. Both of these problems were studied by <PERSON><PERSON> and <PERSON><PERSON> [7]; they showed how to construct a maximum-likelihood permutation when the noise parameter (ß or γ, respectively) is constant.In this work, we obtain algorithms that work in the presence of stronger noise or respectively). In <PERSON><PERSON>'s model, our algorithm works for a relaxed solution concept: likelier than nature. That is, rather than requiring that our output maximizes the likelihood over the entire domain, we guarantee that the likelihood of our output is, w.h.p., greater than or equal to that of the true state of nature (p). An interesting feature of our algorithm is that it handles noise by adding more noise.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.60"}, {"primary_key": "3865554", "vector": [], "sparse_vector": [], "title": "Fast and Memory-Efficient Algorithms for Evacuation Problems.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We study two classical flow over time problems that capture the essence of evacuation planning. Given a network with capacities and transit times on the arcs and sources/sinks with supplies/demands, a quickest transshipment sends the supplies from the sources to meet the demands at the sinks as quickly as possible. In a 1995 landmark paper, <PERSON><PERSON> and <PERSON><PERSON><PERSON> describe the first strongly polynomial time algorithm solving the quickest transshipment problem. Their algorithm relies on repeatedly calling an oracle for parametric submodular function minimization.We present a somewhat simpler and more efficient algorithm for the quickest transshipment problem. Our algorithm (i) relies on only one parametric submodular function minimization and, as a consequence, has considerably improved running time, (ii) uses not only the solution of a submodular function minimization but actually exploits the underlying algorithmic approach to determine a quickest transshipment as a convex combination of simple lex-max flows over time, and (iii) in this way determines a structurally easier solution in the form of a generalized temporally repeated flow.Our second main result is an entirely novel algorithm for computing earliest arrival transshipments, which feature a particularly desirable property in the context of evacuation planning. An earliest arrival transshipment - which in general only exists in networks with a single sink - is a quickest transshipment maximizing the amount of flow which has reached the sink for every point in time simultaneously. In contrast to previous approaches, our algorithm solely works on the given network and, as a consequence, requires only polynomial space.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.52"}, {"primary_key": "3865555", "vector": [], "sparse_vector": [], "title": "Incidences with curves and surfaces in three dimensions, with applications to distinct and repeated distances.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study a wide spectrum of incidence problems involving points and curves or points and surfaces in ℝ3. The current (and in fact the only viable) approach to such problems, pioneered by <PERSON><PERSON> and <PERSON> [35, 36], requires a variety of tools from algebraic geometry, most notably (i) the polynomial partitioning technique, and (ii) the study of algebraic surfaces that are ruled by lines or, in more recent studies [37], by algebraic curves of some constant degree. By exploiting and refining these tools, we obtain new and improved bounds for numerous incidence problems in ℝ3.In broad terms, we consider two kinds of problems, those involving points and constant-degree algebraic curves, and those involving points and constant-degree algebraic surfaces. In some variants we assume that the points lie on some fixed constant-degree algebraic variety, and in others we consider arbitrary sets of points in 3-space.The case of points and curves has been considered in several previous studies, starting with <PERSON><PERSON> and <PERSON>'s work on points and lines [36]. Our results, which are based on a recent work of <PERSON><PERSON> and <PERSON> [37] concerning surfaces that are doubly ruled by curves, provide a grand generalization of all previous results. We reconstruct the bound for points and lines, and improve, in certain significant ways, recent bounds involving points and circles (in [50]) and points and arbitrary constant-degree algebraic curves (in [49]). While in these latter instances the bounds are not known (and are strongly suspected not) to be tight, our bounds are, in a certain sense, the best that can be obtained with this approach, given the current state of knowledge.In the case of points and surfaces, the incidence graph between them can contain large complete bipartite graphs, each involving points on some curve and surfaces containing this curve (unlike earlier studies, we do not rule out this possibility, which makes our approach more general). Our bounds estimate the total size of the vertex sets in such a complete bipartite graph decomposition of the incidence graph. In favorable cases, our bounds translate into actual incidence bounds. Overall, here too our results can be regarded as providing a “grand generalization” of most of the previous studies of (special instances of) this problem.As applications of our point-surface incidence bounds, we consider the problems of distinct and repeated distances determined by a set of n points in ℝ3, two of the most celebrated open problems in combinatorial geometry. We obtain new and improved bounds for two special cases, one in which the points lie on some algebraic variety of constant degree, and one involving distances between pairs in P1 × P2, where Pi is contained in a variety and P2 is arbitrary.MSC codesCombinatorial geometryincidencesthe polynomial methodalgebraic geometrydistinct distancesrepeated distances", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.163"}, {"primary_key": "3865556", "vector": [], "sparse_vector": [], "title": "Generalized Preconditioning and Undirected Minimum-Cost Flow.", "authors": ["<PERSON>"], "summary": "PREVIOUS ARTICLENegative-Weight Shortest Paths and Unit Capacity Minimum Cost Flow in Õ (m10/7 log W) Time (Extended Abstract)", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.49"}, {"primary_key": "3865557", "vector": [], "sparse_vector": [], "title": "Metric embeddings with outliers.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We initiate the study of metric embeddings with outliers. Given some finite metric space we wish to remove a small set of points and to find either an isometric or a low-distortion embedding of the remaining points into some host metric space. This is a natural problem that captures scenarios where a small fraction of points in the input corresponds to noise.We present polynomial-time approximation algorithms for computing outlier embeddings into Euclidean space, trees, and ultrametrics. In the case of isometric embeddings the objective is to minimize the number of outliers, while in the case of non-isometries we have a bi-criteria optimization problem where the goal is to minimize both the number of outliers and the distortion. We complement our approximation algorithms with NP-hardness results for these problems.We conclude with a brief experimental evaluation of our non-isometric outlier embedding on synthetic and real-world data sets.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.43"}, {"primary_key": "3865558", "vector": [], "sparse_vector": [], "title": "Doubly Balanced Connected Graph Partitioning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We introduce and study the Doubly Balanced Connected graph Partitioning (DBCP) problem: Let G=(V, E) be a connected graph with a weight (supply/demand) function p:V→{-1, +1} satisfying P(V)=∑j∊v p(j)=0· The objective is to partition G into (V1, V2) such that G[V1] and G[V2] are connected, |p(V1)|, |p(V2)|≤cp, and for some constants cp and cs · When G is 2-connected, we show that a solution with cp=1 and cs=3 always exists and can be found in polynomial time. Moreover, when G is 3-connected, we show that there is always a ‘perfect’ solution (a partition with p(V1)=p(V2)=0 and |V1| = |V2|, if |V|≡0(mod 4)), and it can be found in polynomial time. Our techniques can be extended, with similar results, to the case in which the weights are arbitrary (not necessarily ±1), and to the case that p(V)=0 and the excess supply/demand should be split evenly. They also apply to the problem of partitioning a graph with two types of nodes into two large connected subgraphs that preserve approximately the proportion of the two types.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.126"}, {"primary_key": "3865559", "vector": [], "sparse_vector": [], "title": "Adaptive Matrix Vector Product.", "authors": ["Santosh S<PERSON>", "<PERSON>"], "summary": "We consider the following streaming problem: given a hardwired m × n matrix A together with a poly(mn)-bit hardwired string of advice that may depend on A, for any x with coordinates x1,…xn presented in order, output the coordinates of A · x in order. Our focus is on using as little memory as possible while computing A · x; we do not count the size of the output tape on which the coordinates of A · x are written; for some matrices A such as the identity matrix, a constant number of words of space is achievable. Such an algorithm has to adapt its memory contents to the changing structure of A and exploit it on the fly. We give a nearly tight characterization, for any number of passes over the coordinates of x, of the space complexity of such a streaming algorithm. Our characterization is constructive, in that we provide an efficient algorithm matching our lower bound on the space complexity. The essential parameters, streaming rank and multi-pass streaming rank of A, might be of independent interest, and we show they can be computed efficiently. We give several applications of our results to computing Johnson-Lindenstrauss transforms. Finally, we note that we can characterize the optimal space complexity when the coordinates of A · x can be written in any order.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.133"}, {"primary_key": "3865560", "vector": [], "sparse_vector": [], "title": "Deciding Contractibility of a Non-Simple Curve on the Boundary of a 3-Manifold.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present an algorithm for the following problem. Given a triangulated 3-manifold M and a (possibly non-simple) closed curve on the boundary of M, decide whether this curve is contractible in M. Our algorithm is combinatorial and runs in exponential time. This is the first algorithm that is specifically designed for this problem; its running time considerably improves upon the existing bounds implicit in the literature for the more general problem of contractibility of closed curves in a 3-manifold. The proof of the correctness of the algorithm relies on methods of 3-manifold topology and in particular on those used in the proof of the Loop Theorem.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.178"}, {"primary_key": "3865561", "vector": [], "sparse_vector": [], "title": "LP-branching algorithms based on biased graphs.", "authors": ["<PERSON>"], "summary": "We give a combinatorial condition for the existence of efficient, LP-based FPT algorithms for a broad class of graph-theoretical optimisation problems. Our condition is based on the notion of biased graphs known from ma- troid theory. Specifically, we show that given a biased graph Ψ = (G, B), where B is a class of balanced cycles in G, the problem of finding a set X of at most k vertices in G which intersects every unbalanced cycle in G admits an FPT algorithm using an LP-branching approach, similar to those previously seen for VCSP problems (<PERSON><PERSON>, SODA 2014). Our algorithm has two parts. First we define a local problem, where we are additionally given a root vertex v0 G V and asked only to delete vertices X (excluding v0) so that the connected component of v0 in G - X contains no unbalanced cycle. We show that this local problem admits a persistent, half-integral LP-relaxation with a polynomial-time solvable separation oracle, and can therefore be solved in FPT time via LP-branching, assuming only oracle membership queries for the class of balanced cycles in G. We then show that solutions to this local problem can be used to tile the graph, producing an optimal solution to the original, global problem as well. This framework captures many of the problems previously solved via the VCSP approach to LP-branching, as well as new generalisations, such as Group Feedback Vertex Set for infinite groups (e.g., for graphs whose edges are labelled by matrices). A major advantage compared to previous work is that it is immediate to check the applicability of the result for a given problem, whereas testing applicability of the VCSP approach for a specific VCSP, requires determining the existence of an embedding language with certain algebraically defined properties, which is not known to be decidable in general.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.102"}, {"primary_key": "3865562", "vector": [], "sparse_vector": [], "title": "Tight Algorithms for Vertex Cover with Hard Capacities on Multigraphs and Hypergraphs.", "authors": ["<PERSON><PERSON>"], "summary": "In this paper we give a f-approximation algorithm for the minimum unweighted Vertex Cover problem with Hard Capacity constraints (VCHC) on f-hypergraphs. This problem generalizes standard vertex cover for which the best known approximation ratio is also f and cannot be improved assuming the unique game conjecture. Our result is therefore essentially the best possible. This improves over the previous 2.155 (for f = 2) and 2f approximation algorithms by <PERSON>, <PERSON> and <PERSON> (CGW).At the heart of our approach is to apply iterative rounding to a natural LP relaxation that is slightly different from prior works which used (non-iterative) rounding. Our algorithm is significantly simpler and offers an intuitive explanation why f-approximation can be achieved for VCHC. We also present faster implementations of our method based on iteratively rounding the solution to certain CGW-style covering LPs.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.173"}, {"primary_key": "3865563", "vector": [], "sparse_vector": [], "title": "Front Matter.", "authors": [], "summary": "\"Universal design provides a blueprint for maximum inclusion of all people\" [7]. This statement is now nearly 20 years old. The present collection of papers from the 3rd International Conference on Universal Design (UD 2016) is testament to the fact that Universal Design is now benefitting from a climate that is more knowledgeable about, and possibly more accommodating of, individual differences between people. However, there are still many open issues, and much to be learnt from exchanging experiences between all stakeholders interested in Universal Design, be they policy makers, practitioners or researchers. This is due to the many changes in society, the environment and technology that have occurred in the last two decades. In this collection of papers from the conference, we cover many areas of theory and practice of Universal Design, with applications from the built environment and tangible products, to communication, services, and system design issues. There are also papers about advocating and teaching Universal Design, debates about policy, and about codes, regulations and standards. We hope the collection is a useful way for policy makers, practitioners and researchers interested in these different strands of work to learn and exchange ideas and best practices, and to break down the \"silos\" that inevitably emerge in any group attempting to address a topic of mutual interest from many perspectives. In gathering together these different strands, we also need to reflect on the current manifestations of Universal Design, and what implications there are for the years to come. We need to look back to where we came from, and to look forward to the future shaping of Universal Design. With the benefit of nearly 20 years since the publication of the Principles of Universal Design Principles of Universal Design Version 2.0 4/1/97. © Copyright 1997 NC State University, The Center for Universal Design, an initiative of the College of Design. Compiled by advocates of universal design, listed in alphabetical order: <PERSON>e <PERSON>, <PERSON>, <PERSON>, <PERSON>eller, <PERSON>bir <PERSON>lli<PERSON>, <PERSON> <PERSON><PERSON>ff, <PERSON> Sanford, <PERSON>, Molly Story, & Gregg <PERSON>derheiden https://www.ncsu.edu/project/design-projects/sites/cud/content/principles/principles.html. , we can review some of the terminology and its development around the concept of Universal Design. From the outset, the term referred to a broad spectrum of applications. It covered the design of buildings, products and environments, and the need for these to be inherently accessible to older people, people without disabilities, and people with disabilities. More specifically, the term Universal Design was defined in 1997 as \"the design of products and environments to be usable by all people, to the greatest extent possible, without the need for adaptation or specialized design\" https://www.ncsu.edu/ncsu/design/cud/about_ud/about_ud.htm. . As with all concise definitions, it has been necessary to emphasise and add to this statement, for instance: • Universal Design is really about including all people and not a euphemism for \"design for those with a disability\". It is about products, services, and environments being usable, to the greatest extent possible, by everyone, regardless of their age, ability, cultural background or status in life [1]. • The phrase \"to the greatest extent possible\" was added to counter criticism that Universal Design was a utopian ideal, and to underscore that Universal Design is a practical as well as conceptual approach. • The focus of Universal Design is on mainstream products, services, and environments and not on adaptations or specialist products, services, and environments. • Universal Design emphasises the need to design from the outset for the widest possible range of users, rather than try to make modifications later on, whether during the design process or after release. • Products, services, and environments should also be aesthetically pleasing as well as non-discriminatory and non-stigmatising. Most of these elaborations are enshrined in the Principles of Universal Design. This is a set of seven principles that were developed to lay out guidance for the design of environments, products and communications, to evaluate existing designs, and to educate both designers and consumers about the characteristics of more usable products and environments. In addition, other terms have come into being, responding to the need to explain different aspects of the Universal Design spectrum. In 1998, at the \"Designing for the 21st Century Conference\" Ron Mace's presentation differentiated between the meanings and practices associated with the terms \"Barrier-Free Design\", \"Assistive Technology\" and \"Universal Design\" [2]. At the turn of the millennium, \"Design for All\" was the term adopted by the European Commission which focused on ensuring that environments, products, services and interfaces of the Information Society Technologies (ISTs) work for people of all ages and abilities in different situations and under various circumstances [6, 8]. It spelt out the \"adaptation or specialized design\" with a three-part strategy: • Design of IST products, services and applications which are demonstrably suitable for most of the potential users without any modifications. • Design of products which are easily adaptable to different users (e.g. by incorporating adaptable or customisable user interfaces). • Design of products which have standardised interfaces, capable of being accessed by specialised user interaction devices [6]. Alongside setting out this strategy, the definition of Design for All made a conscious effort to make the concept more widely acceptable by explaining how Design for All could benefit not just consumers of ISTs, but also producers, and give wider social and economic benefit. Amongst other things, adopting a Design for All approach would help deal proactively with the demographic trend of the aging population, and benefit businesses with increased sales of innovatively designed products that everyone could use. In short, Design for All advocated a policy of mutual benefit, where the \"for all\" descriptor included more than the user population. However, as with the term Universal Design, Design for All was misinterpreted, and accused firstly of the impractical aim of trying to accommodate everyone without exception. Secondly, there was a confusion that Design for All in its insistence on minimizing adaptation, was advocating a \"one design fits all\" stance, evidenced by the question posed at the time \"Could you imagine a pair of shoes being designed in such way that everybody would want to wear them?\" A preferred term to Design for All in the English-speaking European literature is \"Inclusive Design\". Coined by Roger Coleman in 1994 RICA (Research Institute for Consumer Affairs) (2016) Inclusive Design: manufacturing, design, and retail expert views available from rica.org.uk. , this was an elegant apposition to the marketing term \"exclusive design\" as well as being a way of bringing in the notion of social equity that is part of the ethos of Universal Design and enshrined in the first of the seven Principles of Universal Design. Interestingly, in 2005, the British Standards Institute described Inclusive Design in its \"Guide to Managing Inclusive Design\" as \"comprehensive, integrated design which encompasses all aspects of a product used by consumers of diverse age and capability in a wide range of contexts\" BS 7000-6:2005, Design management systems. Managing inclusive design. Guide. . The emphasis is firmly on products, as noted by the compilers of the Inclusive Design Toolkit Inclusive Design Toolkit, What is Inclusive Design, Section: Comparison with Universal Design, http://www.inclusivedesigntoolkit.com/betterdesign2/whatis/whatis.html#p3b. . However in the guide itself, the definition is widened to include services: \"the design of mainstream products and/or services that are accessible to, and usable by, as many people as reasonably possible without the need for special adaptation or specialised design\" BS 7000-6, 2005. Design Management Systems: Managing Inclusive Design, BSi, London, UK. http://shop.bsigroup.com/en/ProductDetail/?pid=000000000030142267. . With the term Inclusive Design, the notion that the design is of mutual benefit to all stakeholders, as in Design for All, was lost. It is perhaps telling that the Scandinavian countries with their tradition of collaborative and participatory design are the main supporters of the term Design for All as the most appropriate one to use [6]. It is a term no longer in such evident use by the European Commission. However, it is notable that in the proposal for what is being more commonly referred to as the European Accessibility Act, there is a somewhat awkward paraphrase \"Accessibility following a 'design for all' approach\" Section 1.2, European Commission (2015) COM(2015) 615 final2015/0278 (COD) Proposal for a Directive of the European Parliament and of the Council on the approximation of the laws, regulations and administrative provisions of the Member States as regards the accessibility requirements for products and services. which could be understood as an attempt to keep the \"mutual benefit\" notion, since the Directive is not calling for human rights non-discriminatory legislation directly, but for support of the Single Market. (ABSTRACT TRUNCATED)", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.FM"}, {"primary_key": "3885120", "vector": [], "sparse_vector": [], "title": "Proceedings of the Twenty-Eighth Annual ACM-SIAM Symposium on Discrete Algorithms, SODA 2017, Barcelona, Spain, Hotel Porta Fira, January 16-19", "authors": ["<PERSON>"], "summary": "We consider the Online Boolean Matrix-Vector Multiplication (OMV) problem studied by <PERSON><PERSON><PERSON> et al. [STOC'15]: given an nfin Boolean matrix M, we receive n Boolean vectors v1; : : : ;vn one at a time, and are required to output Mvi (over the Boolean semiring) before seeing the vector vi+1, for all i. Previous known algorithms for this problem are combinatorial, running in O(n 3=log 2 n) time. <PERSON><PERSON><PERSON> et al. conjecture there is no O(n 3-ϵ ) time algorithm for OMV, for all e > 0; their OMV conjecture is shown to imply strong hardness results for many basic dynamic problems. We give a substantially faster method for computing OMV, running in n 3=2W( p logn) randomized time. In fact, after seeing 2w( p logn) vectors, we already achieve n2=2W( p logn) amortized time for matrix-vector multiplication. Our approach gives a way to reduce matrix-vector multiplication to solving a version of the Orthogonal Vectors problem, which in turn reduces to \"small\" algebraic matrix-matrix multiplication. Applications include faster independent set detection, partial match retrieval, and 2-CNF evaluation. We also show how a modification of our method gives a cell probe data structure for OMV with worst case O(n7=4= p w) time per query vector, where w is the word size. This result rules out an unconditional proof of the OMV conjecture using purely information-theoretic arguments.", "published": "2017-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************"}]