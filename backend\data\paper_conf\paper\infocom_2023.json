[{"primary_key": "1202663", "vector": [], "sparse_vector": [], "title": "Self-Adjusting Partially Ordered Lists.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce self-adjusting partially ordered lists, a generalization of self-adjusting lists where additionally there may be constraints for the relative order of some nodes in the list. The lists self-adjust to improve performance while serving input sequences exhibiting favorable properties, such as locality of reference, but the constraints must be respected.We design a deterministic adjusting algorithm that operates without any assumptions about the input distribution and without maintaining frequency statistics or timestamps. Despite the more general model, we show that our deterministic algorithm performs closely to optimum (it is 4-competitive). In addition, we design a family of randomized algorithms with improved competitive ratios, handling also a more general rearrangement cost model, scaled by an arbitrary constant d ≥1. Moreover, we observe that different constraints influence the competitiveness of online algorithms, and we shed light on this aspect with a lower bound.We investigate the applicability of our self-adjusting lists in the context of network packet classification. Our evaluations show that our classifier performs similarly to a static list for low-locality traffic, but significantly outperforms Efficuts (by factor 7x), CutSplit (3.6x) and the static list (14x) for high locality and small rulesets.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228937"}, {"primary_key": "1202665", "vector": [], "sparse_vector": [], "title": "Argosleep: Monitoring Sleep Posture from Commodity Millimeter-Wave Devices.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Sanjib Sur"], "summary": "We propose Argosleep, a millimeter-wave (mmWave) wireless sensors based sleep posture monitoring system that predicts the 3D location of body joints of a person during sleep. Argosleep leverages deep learning models and knowledge of human anatomical features to solve challenges with low-resolution, specularity, and aliasing in existing mmWave devices. Argosleep builds the model by learning the relationship between mmWave reflected signals and body postures from thousands of existing samples. Since practical sleep also involves sudden toss-turns, which could introduce errors in posture prediction, <PERSON><PERSON><PERSON><PERSON> designs a state machine based on the reflected signals to classify the sleeping states into rest or toss-turn, and predict the posture only during the rest states. We evaluate Argosleep with real data collected from COTS mmWave devices for 8 volunteers of diverse ages, gender, and height performing different sleep postures. We observe that <PERSON><PERSON><PERSON><PERSON> identifies the toss-turn events accurately and predicts 3D location of body joints with accuracy on par with the existing vision-based system, unlocking the potential of mmWave systems for privacy-noninvasive at-home healthcare applications.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228913"}, {"primary_key": "1202669", "vector": [], "sparse_vector": [], "title": "Flowrest: Practical Flow-Level Inference in Programmable Switches with Random Forests.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "User-plane machine learning facilitates low-latency, high-throughput inference at line rate. Yet, user planes are highly constrained environments, and restrictions are especially marked in programmable switches with limited memory and minimum support for mathematical operations or data types. Thus, current solutions for in-switch inference that are compatible with production-level hardware lack support for complex features or suffer from limited scalability, and hit performance barriers in complex tasks involving large decision spaces. To address this limitation, we present <PERSON><PERSON>, a first complete Random Forest (RF) model implementation that operates at the level of individual flows in commercial switches. Our solution builds on (i) an original framework to embed flow-level machine learning models into programmable switch ASICs, and (ii) novel guidelines for tailoring RF models to operations in programmable switches already at the design stage. We implement Flowrest as an open-source software using the P4 language, and assess its performance in an experimental platform based on Intel Tofino switches. Tests with tasks of unprecedented complexity show how our model can improve accuracy by up to 39% over previous approaches to implement RF models in real-world equipment.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229100"}, {"primary_key": "1202672", "vector": [], "sparse_vector": [], "title": "Protean: Adaptive Management of Shared-Memory in Datacenter Switches.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Datacenters rely on high-bandwidth networks that use inexpensive, shared-buffer switches. The combination of high bandwidth, bursty traffic patterns, and shallow buffers imply that switch buffer is a heavily contended resource and intelligent management of shared buffers among competing traffic (ports, traffic classes) becomes an important challenge. Dynamic Threshold (DT), which is the current state-of-the-art in buffer management, provides either high bandwidth utilization with poor burst absorption or good burst absorption with inferior utilization, but not both. We present Protean, which dynamically identifies bursty traffic and allocates more buffer space accordingly—Protean provides more space to queues that experience transient load spikes by observing the gradient of queue length but does not cause persistent unfairness as the gradient cannot continue to remain high in shallow buffered switches for long periods of time. We implemented Protean in today’s programmable switches and demonstrate their high performance with negligible overhead. Our at-scale ns-3 simulations show that Protean reduces the tail latency by a factor of 5 over DT on average across varying loads with realistic workloads.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229046"}, {"primary_key": "1202675", "vector": [], "sparse_vector": [], "title": "Tomography-based progressive network recovery and critical service restoration after massive failures.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Massive failures in communication networks are a consequence of natural disasters, heavy blackouts, military and cyber attacks. We tackle the problem of minimizing the time and number of interventions to sufficiently restore the communication network so as to support emergency services after large-scale failures. We propose PRoTOn (Progressive RecOvery and Tomography-based mONitoring), an efficient algorithm for progressive recovery of emergency services. Unlike previous work, assuming centralized routing and complete network observability, PRoTOn addresses the more realistic scenario in which the network relies on the existing routing protocols, and knowledge of the network state is partial and uncertain. Simulation results carried out on real topologies show that our algorithm outperforms previous solutions in terms of cumulative routed flow, repair costs and recovery time in both static and dynamic failure scenarios.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.********"}, {"primary_key": "1202676", "vector": [], "sparse_vector": [], "title": "Securing 5G OpenRAN with a Scalable Authorization Framework for xApps.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Haining <PERSON>"], "summary": "The ongoing transformation of mobile networks from proprietary physical network boxes to virtualized functions and deployment models has led to more scalable and flexible network architectures capable of adapting to specific use cases. As an enabler of this movement, the OpenRAN initiative promotes standardization allowing for a vendor-neutral radio access network with open APIs. Moreover, the O-RAN Alliance has begun specification efforts conforming to OpenRAN's definitions. This includes the near-real-time RAN Intelligent Controller (RIC) overseeing a group of extensible applications (xApps). The use of these potentially untrusted third-party applications introduces a new attack surface to the mobile network plane with fundamental security and system design requirements that are yet to be addressed. To secure the 5G O-RAN xApp model, we introduce the xApp Repository Function (XRF) framework, which implements scalable authentication, authorization, and discovery for xApps. We first present the framework's system design and implementation details, followed by operational benchmarks in a production-grade containerized environment. The evaluation results, centered on active processing and operation times, show that our proposed framework can scale efficiently in a multi-threaded Kubernetes microservice environment and support a large number of clients with minimal overhead.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228961"}, {"primary_key": "1202683", "vector": [], "sparse_vector": [], "title": "Meta Reinforcement Learning for Rate Adaptation.", "authors": ["<PERSON><PERSON><PERSON>", "May Lim", "<PERSON><PERSON><PERSON> N. <PERSON>", "<PERSON>", "<PERSON>"], "summary": "Adaptive bitrate (ABR) schemes enable streaming clients to adapt to time-varying network/device conditions to achieve a stall-free viewing experience. Most ABR schemes use manually tuned heuristics or learning-based methods. Heuristics are easy to implement but do not always perform well, whereas learning-based methods generally perform well but are difficult to deploy on low-resource devices. To make the most out of both worlds, we develop Ahaggar, a learning-based scheme running on the server side that provides quality-aware bitrate guidance to streaming clients running their own heuristics. <PERSON>aggar’s novelty is the meta reinforcement learning approach taking network conditions, clients’ statuses and device resolutions, and streamed content as input features to perform bitrate guidance. <PERSON><PERSON><PERSON> uses the new Common Media Client/Server Data (CMCD/SD) protocols to exchange the necessary metadata between the servers and clients. Experiments on an open-source system show that <PERSON>aggar adapts to unseen conditions fast and outperforms its competitors in several viewer experience metrics.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228951"}, {"primary_key": "1202691", "vector": [], "sparse_vector": [], "title": "OpticNet: Self-Adjusting Networks for ToR-Matching-ToR Optical Switching Architectures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Demand-aware reconfigurable datacenter networks can be modeled as a ToR-Matching-ToR (TMT) two-layer architecture, in which each top-of-rack (ToR) is represented by a static switch, and n ToRs are connected by a set of reconfigurable optical circuit switches (OCS). Each OCS internally connects a set of in-out ports via a matching that may be updated at runtime. The matching model is a formalization of such networks, where the datacenter topology is defined by the union of matchings over the set of nodes, each of which can be reconfigured at unit cost.In this work we propose a scalable matching model for scenarios where OCS have a constant number of ports. Furthermore, we present OpticNet, a framework that maps a set of n static ToR switches to a set of p-port OCS to form any constant-degree topology. We prove that OpticNet uses a minimal number of reconfigurable switches to realize any desired network topology and allows to apply any existing self-adjusting network (SAN) algorithm on top of it, also preserving amortized performance guarantees. Our experimental results based on real workloads show that OpticNet is a flexible and efficient framework to design efficient SANs.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228883"}, {"primary_key": "1202694", "vector": [], "sparse_vector": [], "title": "HeartPrint: Passive Heart Sounds Authentication Exploiting In-Ear Microphones.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Biometrics has been increasingly integrated into wearable devices to enhance data privacy and security in recent years. Meanwhile, the popularity of wearables in turn creates a unique opportunity for capturing novel biometrics leveraging various embedded sensing modalities. In this paper, we study a new intracorporal biometrics combining the uniqueness of i) heart motion, ii) bone conduction, and iii) body asymmetry. Specifically, we design HeartPrint as a passive yet secure user authentication system: it exploits the bone-conducted heart sounds captured by (widely available) dual in-ear microphones (IEMs) to authenticate users, while neatly leveraging IEMs renders itself transparent to users without impairing the normal functions of earphones. To suppress the interference from other body sounds and audio produced by the earphones, we develop a novel interference elimination method using modified non-negative matrix factorization to separate clean heart sounds from background interference. We further explore the uniqueness of IEM-recorded heart sounds in three aspects to extract a novel biometric representation, based on which HeartPrint leverages a convolutional neural model equipped with a continual learning method to achieve accurate authentication under drifting body conditions. Extensive experiments with 18 pairs of commercial earphones on 45 participants confirm that HeartPrint can achieve 1.6% FAR and 1.8% FRR, while effectively coping with major attacks, complicated interference, and hardware diversity.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228921"}, {"primary_key": "1202696", "vector": [], "sparse_vector": [], "title": "I Can Hear You Without a Microphone: Live Speech Eavesdropping From Earphone Motion Sensors.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Recent literature advances motion sensors mounted on smartphones and AR/VR headsets to speech eavesdropping due to their sensitivity to subtle vibrations. The popularity of motion sensors in earphones has fueled a rise in their sampling rate, which enables various enhanced features. This paper investigates a new threat of eavesdropping via motion sensors of earphones by developing EarSpy, which builds on our observation that the earphone's accelerometer can capture bone conduction vibrations (BCVs) and ear canal dynamic motions (ECDMs) associated with speaking; they enable EarSpy to derive unique information about the wearer's speech. Leveraging a study on the motion sensor measurements captured from earphones, EarSpy gains abilities to disentangle the wearer's live speech from interference caused by body motions and vibrations generated when the earphone's speaker plays audio. To enable user-independent attacks, EarSpy involves novel efforts, including a trajectory instability reduction method to calibrate the waveform of ECDMs and a data augmentation method to enrich the diversity of BCVs. Moreover, EarSpy explores effective representations from BCVs and ECDMs, and develops a convolutional neural model with Connectionist Temporal Classification (CTC) to realize accurate speech recognition. Extensive experiments involving 14 participants demonstrate that EarSpy reaches a promising recognition for the wearer's speech.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228943"}, {"primary_key": "1202697", "vector": [], "sparse_vector": [], "title": "SaTCP: Link-Layer Informed TCP Adaptation for Highly Dynamic LEO Satellite Networks.", "authors": ["<PERSON><PERSON> Cao", "<PERSON><PERSON><PERSON>"], "summary": "Low-Earth-orbit (LEO) satellite networking is a promising way of providing low-latency and high-throughput global Internet access. Unlike the static terrestrial network infrastructure, LEO satellites constantly revolve around the Earth and thus bring instability to their networks. Understanding the dynamics and properties of a LEO satellite network and developing mechanisms to address the dynamics become crucial. In this work, we first introduce a high-fidelity and highly configurable real-time emulator called LeoEM to capture detailed dynamics of LEO satellite networks. We then present SaTCP, a cross-layer solution that enables TCP to avoid overly conservative congestion control and improve its performance under high LEO link dynamics. As an upgrade to CUBIC TCP, SaTCP forecasts the time of disruptive events (i.e., satellite handovers or route updates) by tactfully utilizing the predictability of satellite locations, taking into account the prediction inaccuracy, and informs TCP to adapt its decision accordingly. Experiments across various scenarios show SaTCP increases the goodput by multi-folds compared with state-of-the-art protocols while preserving fairness.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.********"}, {"primary_key": "1202698", "vector": [], "sparse_vector": [], "title": "ASTrack: Automatic Detection and Removal of Web Tracking Code with Minimal Functionality Loss.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent advances in web technologies make it more difficult than ever to detect and block web tracking systems. In this work, we propose ASTrack, a novel approach to web tracking detection and removal. ASTrack uses an abstraction of the code structure based on Abstract Syntax Trees to selectively identify web tracking functionality shared across multiple web services. This new methodology allows us to: (i) effectively detect web tracking code even when using evasion techniques (e.g., obfuscation, minification, or webpackaging); and (ii) safely remove those portions of code related to tracking purposes without affecting the legitimate functionality of the website. Our evaluation with the top 10k most popular Internet domains shows that ASTrack can detect web tracking with high precision (98%), while discovering about 50k tracking code pieces and more than 3,400 new tracking URLs not previously recognized by most popular privacy-preserving tools (e.g., uBlock Origin). Moreover, ASTrack achieved a 36% reduction in functionality loss in comparison with the filter lists, one of the safest options available. Using a novel methodology that combines computer vision and manual inspection, we estimate that full functionality is preserved in more than 97% of the websites.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228902"}, {"primary_key": "1202706", "vector": [], "sparse_vector": [], "title": "Energy-Efficient 360-Degree Video Streaming on Multicore-Based Mobile Devices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "360° video streaming becomes increasingly popular on video platforms. However, streaming (downloading and processing) 360° video consumes a large amount of energy on mobile devices, but little work has been done to address this problem, especially considering recent advances in the mobile architecture. Through real measurements, we found that existing systems activate all processor cores during video streaming, which consumes a great deal of energy, but this is unnecessary since most heavy computations in 360° video processing are handled by the hardware accelerators such as hardware decoder, GPU, etc. To save energy, we propose to selectively activate the proper processor cluster and adaptively adjust the CPU frequency based on the video quality. We model the impacts of video resolution and CPU frequency on power consumption, and model the impacts of video features and network effects on Quality of Experience (QoE). With the developed models, we formulate the energy and QoE aware 360° video streaming problem as an optimization problem with the goal of maximizing QoE and minimizing energy. We then propose an efficient algorithm to solve this optimization problem. Evaluation results demonstrate that the proposed algorithm can significantly reduce the energy consumption while maintaining QoE.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228863"}, {"primary_key": "1202708", "vector": [], "sparse_vector": [], "title": "CURSOR: Configuration Update Synthesis Using Order Rules.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Network configuration updates are frequent nowadays to adapt to the rapid evolution of networks. To ensure the safety of the configuration update, network verification can be used to verify that network properties hold for the new configuration. However, configuration updates typically involve multiple routers changing their configurations. Changes on these routers cannot be applied simultaneously. This leads to intermediate configurations, which might violate network properties such as reachability. Configuration updates synthesis aims to find an order of applying changes on routers such that network properties hold for all intermediate configurations.Existing approaches synthesize a safe update order by traversing the update order space, which is time-consuming and does not scale to a large number of configuration updates. This paper proposes CURSOR, a configuration update synthesis that extracts rules that update orders should follow. We implement CURSOR and evaluate its performance with real-world configuration update scenarios. The experimental results show that we can accelerate the synthesis by an order of magnitude on large-scale configuration updates.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228930"}, {"primary_key": "1202710", "vector": [], "sparse_vector": [], "title": "AdaptSLAM: Edge-Assisted Adaptive SLAM with Resource Constraints via Uncertainty Minimization.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Edge computing is increasingly proposed as a solution for reducing resource consumption of mobile devices running simultaneous localization and mapping (SLAM) algorithms, with most edge-assisted SLAM systems assuming the communication resources between the mobile device and the edge server to be unlimited, or relying on heuristics to choose the information to be transmitted to the edge. This paper presents AdaptSLAM, an edge-assisted visual (V) and visual-inertial (VI) SLAM system that adapts to the available communication and computation resources, based on a theoretically grounded method we developed to select the subset of keyframes (the representative frames) for constructing the best local and global maps in the mobile device and the edge server under resource constraints. We implemented AdaptSLAM to work with the state-of-the-art open-source V-and VI-SLAM ORB-SLAM3 framework, and demonstrated that, under constrained network bandwidth, AdaptSLAM reduces the tracking error by 62% compared to the best baseline method.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229009"}, {"primary_key": "1202712", "vector": [], "sparse_vector": [], "title": "Excalibur: A Scalable and Low-Cost Traffic Testing Framework for Evaluating DDoS Defense Solutions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhou", "<PERSON><PERSON>"], "summary": "To date, security researchers evaluate their solutions of mitigating denial-of-service (DDoS) attacks via kernel-based or kernel-bypassing testing tools. However, kernel-based tools exhibit poor scalability in attack traffic generation while kernel-bypassing tools result in unacceptable monetary cost. We propose Excalibur, a scalable and low-cost testing framework for DDoS defense solutions. The key idea is to leverage the programmable switch to perform testing tasks with Tbps-level scalability and low cost. Specifically, Excalibur coordinates both a server and a programmable switch to jointly perform testing tasks. It realizes flexible attack traffic generation, which requires a large number of resources, in the server while using the switch to increase the sending rate of attack traffic to Tbps-level. Our experiments on a 64×100Gbps Tofino switch show that Excalibur achieves orders-of-magnitude higher scalability and lower cost than existing tools.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229080"}, {"primary_key": "1202713", "vector": [], "sparse_vector": [], "title": "Melody: Toward Resource-Efficient Packet Header Vector Encoding on Programmable Switches.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Qingjiang Xiao", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The programmable switch offers a limited capacity of packet header vector (PHV) words that store packet header fields and metadata fields defined by network functions. However, existing switch compilers employ inefficient strategies of encoding fields on PHV words. Their encoding wastes scarce PHV words and may result in failures when deploying network functions. In this paper, we propose Melody, a new framework that reuses PHV words for as many fields as possible to achieve resource-efficient PHV encoding. <PERSON> offers a field analyzer and an optimization framework. The analyzer identifies which fields can reuse PHV words while preserving the original packet processing logic. The framework integrates analysis results into its encoding to offer the resource-optimal decisions. We evaluate <PERSON> with production-scale network functions. Our results show that <PERSON> reduces the consumption of PHV words by up to 85%.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229056"}, {"primary_key": "1202714", "vector": [], "sparse_vector": [], "title": "ASR: Efficient and Adaptive Stochastic Resonance for Weak Signal Detection.", "authors": ["Xingyu Chen", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Weak-signal detection underlies a variety of ubiquitous computing applications, such as wireless sensing and machinery fault diagnosis. Stochastic resonance (SR) provides a new way for weak-signal detection by boosting undetectable signals with added white noise. However, existing work has to take a long time to search optimal parameter settings for SR, which cannot fit well some time-critical applications. In this paper, we propose an adaptive SR scheme (ASR) that can amplify the original signal at a low cost in time. The basic idea is that we find that the potential parameter is a key factor that determines the performance of SR. By treating the system as a feedback loop, we can dynamically adjust the potential parameters according to the output signals and make SR happen adaptively. ASR answered two technical questions: how can we evaluate the output signal and how can we tune the potential parameters quickly towards the optimal. In ASR, we first design a spectral-analysis based solution to examine whether SR happens using continuous wavelet transform. After that, we reduce the parameter tuning problem to a constrained non-linear optimization problem and use the sequential quadratic programming to iteratively optimize the potential parameters. We implement ASR and apply it in two ubiquitous computing applications: respiration-rate detection and machinery fault diagnosis. Extensive experiments show that ASR outperforms the state-of-the-art.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228979"}, {"primary_key": "1202716", "vector": [], "sparse_vector": [], "title": "Latency-Optimal Pyramid-based Joint Communication and Computation Scheduling for Distributed Edge Computing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "By combing edge computing and parallel computing, distributed edge computing has emerged as a new paradigm to accelerate computation at the edge. Considering the parallelism of both computation and communication, the problem of Minimum Latency joint Communication and Computation Scheduling (MLCCS) is studied recently. However, existing works have rigid assumptions that the communication time of each device is fixed and the workload can be split arbitrarily small. Aiming at making the work more practical and general, the MLCCS problem without the above assumptions is studied in this paper. Firstly, the MLCCS problem under a general model is formulated and proved to be NP-hard. Secondly, a pyramid-based computing model is proposed to consider the parallelism of communication and computation jointly, which has an approximation ratio of 1 + δ, where δ is related to devices' communication rates. An interesting property under such computing model is identified and proved, i.e., the optimal latency can be obtained under arbitrary scheduling order when all the devices share the same communication rate. When the devices own different communication rates, the optimal scheduling order is also obtained. Additionally, when the workload cannot be split arbitrarily, an approximation algorithm with ratio of at most 2 (1 + δ) is proposed. Finally, the theoretical analysis and simulation results verify that the proposed algorithm has high performance in terms of latency. Two testbed experiments are also conducted, which show that the proposed method outperforms the existing methods, reducing the latency by up to 29.2% in real-world applications.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228964"}, {"primary_key": "1202718", "vector": [], "sparse_vector": [], "title": "FlowBench: A Flexible Flow Table Benchmark for Comprehensive Algorithm Evaluation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Flow table is a fundamental and critical component in network data plane. Numerous algorithms and architectures have been devised for efficient flow table construction, lookup, and update. The diversity of flow tables and the difficulty to acquire real data sets make it challenging to give a fair and confident evaluation to a design. In the past, researchers rely on ClassBench and its improvements to synthesize flow tables, which become inadequate for today's networks. In this paper, we present a new flow table benchmark tool, FlowBench. Based on a novel design methodology, FlowBench can generate large-scale flow tables with arbitrary combination of matching types and fields in a short time, and yet keep accurate characteristics to reveal the real performance of the algorithms under evaluation. The open-source tool facilitates researchers to evaluate both existing and future algorithms with unprecedented flexibility.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229097"}, {"primary_key": "1202719", "vector": [], "sparse_vector": [], "title": "Wider is Better? Contact-free Vibration Sensing via Different COTS-RF Technologies.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Vibration sensing is crucial to human life and work, as vibrations indicate the status of their respective sources (e.g., heartbeat to human health condition). Given the inconvenience of contact sensing, both academia and industry have been intensively exploring contact-free vibration sensing, with several major developments leveraging radio-frequency (RF) technologies made very recently. However, a measurement study systematically comparing these options is still missing. In this paper, we choose to evaluate five representative commercial off-the-shelf (COTS) RF technologies with different carrier frequencies, bandwidths, and waveform designs. We first unify the sensing data format and processing pipeline, and also propose a novel metric v-SNR to quantify sensing quality. Then our extensive evaluations start from controlled experiments for benchmarking, followed by investigations on two real-world applications: machinery vibration measurement and vital sign monitoring. Our comprehensive study reveals that Wi-Fi performs the worst among all five technologies, while a lesser-known UWB-based technology achieves the best overall performance, and others have respective pros and cons in different scenarios.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229035"}, {"primary_key": "1202720", "vector": [], "sparse_vector": [], "title": "Crowd2: Multi-agent Bandit-based Dispatch for Video Analytics upon Crowdsourcing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Yuting Yan", "<PERSON><PERSON>", "<PERSON><PERSON>", "Mingtao Ji", "<PERSON><PERSON>"], "summary": "Many crowdsourcing platforms are emerging, leveraging the resources of recruited workers to execute various outsourcing tasks, mainly for those computing-intensive video analytics with high quality requirements. Although the profit of each platform is strongly related to the quality of analytics feedback, due to the uncertainty on diverse performance of workers and the conflicts of interest over platforms, it is non-trivial to determine the dispatch of tasks with maximum benefits. In this paper, we design a decentralized mechanism for a Crowd of Crowdsourcing platforms, denoted as Crowd 2 , optimizing the worker selection to maximize the social welfare of these platforms in a long-term scope, under the consideration of both proportional fairness and dynamic flexibility. Concretely, we propose a video analytics dispatch algorithm based on multi-agent bandit, for which the more accurate profit estimates are attained via the decoupling of multi-knapsack based mapping problem. Via rigorous proofs, a sub-linear regret bound for social welfare of crowdsourcing profits is achieved while both fairness and flexibility are ensured. Extensive trace-driven experiments demonstrate that Crowd 2 improves the social welfare by 36.8%, compared with other alternatives.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228927"}, {"primary_key": "1202721", "vector": [], "sparse_vector": [], "title": "ResMap: Exploiting Sparse Residual Feature Map for Accelerating Cross-Edge Video Analytics.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Yuting Yan", "<PERSON>", "<PERSON><PERSON>"], "summary": "Deploying deep convolutional neural network (CNN) to perform video analytics at edge poses a substantial system challenge, as running CNN inference incurs a prohibitive cost in computational resources. Model partitioning, as a promising approach, splits CNNs and distributes them to multiple edge devices in closer proximity to each other for serial inferences, however, it causes considerable cross-edge delay for transmitting intermediate feature maps. To overcome this challenge, we present ResMap, a new edge video analytics framework that significantly improves the cross-edge transmission and flexibly partitions the CNNs. Briefly, by exploiting the sparsity of the intermediate raw or residual feature map, ResMap effectively removes the redundant transmission, thereby decreasing the cross-edge transmission delay. In addition, ResMap incorporates an Online Data-Aware Scheduler to regularly update the CNN partitioning scheme so as to adapt to the time-varying edge runtime and video content. We have implemented ResMap fully based on COTS hardware, and the experimental results show that ResMap reduces the intermediate feature map volume by 14.93-46.12% and improves the average processing time by 17.43-30.6% compared to other alternative designs.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228990"}, {"primary_key": "1202722", "vector": [], "sparse_vector": [], "title": "Rebuffering but not Suffering: Exploring Continuous-Time Quantitative QoE by User&apos;s Exiting Behaviors.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Quality of Experience (QoE) is one of the most important quality indicators for video streaming applications. But it is still an open question how to assess QoE value objectively and quantitatively over continuous time both for academia and industry. In this paper, we carry out an extensive data study on user behaviors in one of the largest short-video service providers. The measurement data reveals that the user’s exiting behavior in viewing video streams is an appropriate choice as a continuous-time QoE metric. Secondly, we build a quantitative QoE model to objectively assess the quality of video playback by discretizing the playback session into the Markov chain. By collecting 7 billion viewing session logs which cover users from 20 CDN providers and 40 Internet service providers, the proposed state-chain-based model of State-Exiting Ratio (SER) is validated. The experimental results show that the modeling error of SER and session duration are less than 2% and 10s respectively. By using the proposed scheme to optimize adaptive video streaming, the average session duration is improved up to 60% to baseline, and 20% to the existing black-box-like machine learning methods.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.********"}, {"primary_key": "1202723", "vector": [], "sparse_vector": [], "title": "Designing Optimal Compact Oblivious Routing for Datacenter Networks in Polynomial Time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>k<PERSON><PERSON> So-In", "<PERSON><PERSON>"], "summary": "Recent datacenter network topologies are shifting towards heterogeneous and structured topologies for high throughput, low cost, and simple manageability. However, they rely on sub-optimal routing approaches that fail to achieve their designed capacity. This paper proposes a process for designing optimal oblivious routing that is programmed compactly on programmable switches. The process consists of three contributions in tandem. We first transform a robust optimization problem for designing oblivious routing into a linear program, which is solvable in polynomial time but cannot scale for datacenter topologies. We then prove that the repeated structures in a datacenter topology lead to a structured optimal solution. We use this insight to formulate a scalable linear program, so an optimal oblivious routing solution is obtained in polynomial time for large-scale topologies. For real-world deployment, the optimal solution is converted into forwarding rules for programmable switches with stringent memory. With this constraint, we utilize the repeated structures in the optimal solution to group the forwarding rules, resulting in compact forwarding rules with a much smaller memory requirement. Extensive evaluations show our process i) obtains optimal solutions faster and more scalable than a state-of-the-art technique and ii) reduces the memory requirement by no less than 90% for most considered topologies.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.********"}, {"primary_key": "1202725", "vector": [], "sparse_vector": [], "title": "A2-UAV: Application-Aware Content and Network Optimization of Edge-Assisted UAV Systems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Gaia Ma<PERSON>li", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "To perform advanced surveillance, Unmanned Aerial Vehicles (UAVs) require the execution of edge-assisted computer vision (CV) tasks. In multi-hop UAV networks, the successful transmission of these tasks to the edge is severely challenged due to severe bandwidth constraints. For this reason, we propose a novel A 2 -UAV framework to optimize the number of correctly executed tasks at the edge. In stark contrast with existing art, we take an application-aware approach and formulate a novel Application-Aware Task Planning Problem (A 2 -TPP) that takes into account (i) the relationship between deep neural network (DNN) accuracy and image compression for the classes of interest based on the available dataset, (ii) the target positions, (iii) the current energy/position of the UAVs to optimize routing, data pre-processing and target assignment for each UAV. We demonstrate A 2 -TPP is NP-Hard and propose a polynomial-time algorithm to solve it efficiently. We extensively evaluate A 2 -UAV through real-world experiments with a testbed composed by four DJI Mavic Air 2 UAVs. We consider state-of-the-art image classification tasks with four different DNN models (i.e., DenseNet, ResNet152, ResNet50 and MobileNet-V2) and object detection tasks using YoloV4 trained on the ImageNet dataset. Results show that A 2 -UAV attains on average around 38% more accomplished tasks than the state of the art, with 400% more accomplished tasks when the number of targets increase significantly. To allow full reproducibility, we pledge to share datasets and code with the research community.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.********"}, {"primary_key": "1202727", "vector": [], "sparse_vector": [], "title": "AutoManager: a Meta-Learning Model for Network Management from Intertwined Forecasts.", "authors": ["<PERSON>", "Antonio <PERSON>", "<PERSON>", "<PERSON>"], "summary": "A variety of network management and orchestration (MANO) tasks take advantage of predictions to support anticipatory decisions. In many practical scenarios, such predictions entail two largely overlooked challenges: (i) the exact relationship between the predicted values (e.g., reserved resources) and the performance objective (e.g., quality of experience of end users) is often tangled and cannot be known a priori, and (ii) the objective is linked in many cases to multiple predictions that contribute to it in an intertwined way (e.g., resources to reserved are limited and must be shared among competing flows). We present AutoManager, a novel meta-learning model that can support complex MANO tasks by addressing these two challenges. Our solution learns how multiple intertwined predictions affect a common performance goal, and steers them so as to attain the correct operation point under a-priori unknown loss functions. We demonstrate AutoManager in practical, complex use cases based on real-world traffic measurements; our experiments show that the model produces forecasts that are accurate and tailored to the MANO task in a fully automated way.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229064"}, {"primary_key": "1202733", "vector": [], "sparse_vector": [], "title": "SDN Application Backdoor: Disrupting the Service via Poisoning the Topology.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Software-Defined Networking (SDN) enables the deployment of diversified networking applications by providing global visibility and open programmability on a centralized controller. As SDN enters its second decade, several well-developed open source controllers have been widely adopted in industry, and various commercial SDN applications are built to meet the surging demand of network innovation. This complex ecosystem inevitably introduces new security threats, as malicious applications can significantly disrupt network operations. In this paper, we introduce a new vulnerability in existing SDN controllers that enable adversaries to create a backdoor and further deploy malicious applications to disrupt network service via a series of topology poisoning attacks. The root cause of this vulnerability is that SDN systems simply process received Packet-In messages without checking the integrity, and thus can be misguided by manipulated messages. We discover that five popular SDN controllers (i.e., Floodlight, ONOS, OpenDaylight, POX and Ryu) are potentially vulnerable to the disclosed attack, and further propose six new attacks exploiting this vulnerability to disrupt SDN services from different layers. We evaluate the effectiveness of these attacks with experiments in real SDN testbeds, and discuss feasible countermeasures.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229058"}, {"primary_key": "1202734", "vector": [], "sparse_vector": [], "title": "A Hierarchical Knowledge Transfer Framework for Heterogeneous Federated Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Federated learning (FL) enables distributed clients to collaboratively learn a shared model while keeping their raw data private. To mitigate the system heterogeneity issues of FL and overcome the resource constraints of clients, we investigate a novel paradigm in which heterogeneous clients learn uniquely designed models with different architectures, and transfer knowledge to the server to train a larger server model that in turn helps to enhance client models. For efficient knowledge transfer between client models and server model, we propose FedHKT, a Hierarchical Knowledge Transfer framework for FL. The main idea of FedHKT is to allow clients with similar data distributions to collaboratively learn to specialize in certain classes, then the specialized knowledge of clients is aggregated to a super knowledge covering all specialties to train the server model, and finally the server model knowledge is distilled to client models. Specifically, we tailor a hybrid knowledge transfer mechanism for FedHKT, where the model parameters based and knowledge distillation (KD) based methods are respectively used for client-edge and edge-cloud knowledge transfer, which can harness the pros and evade the cons of these two approaches in learning performance and resource efficiency. Besides, to efficiently aggregate knowledge for conducive server model training, we propose a weighted ensemble distillation scheme with server-assisted knowledge selection, which aggregates knowledge by its prediction confidence, selects qualified knowledge during server model training, and uses selected knowledge to help improve client models. Extensive experiments demonstrate the superior performance of FedHKT compared to state-of-the-art baselines.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228954"}, {"primary_key": "1202735", "vector": [], "sparse_vector": [], "title": "QueuePilot: Reviving Small Buffers With a Learned AQM Policy.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "There has been much research effort on using small buffers in backbone routers, to provide lower delays for users and free up capacity for vendors. Unfortunately, with small buffers, the droptail policy has an excessive loss rate, and existing AQM (active queue management) policies can be unreliable.We introduce QueuePilot, an RL (reinforcement learning)-based AQM that enables small buffers in backbone routers, trading off high utilization with low loss rate and short delay. QueuePilot automatically tunes the ECN (early congestion notification) marking probability. After training once offline with a variety of settings, QueuePilot produces a single lightweight policy that can be applied online without further learning. We evaluate QueuePilot on real networks with hundreds of TCP connections, and show how its performance in small buffers exceeds that of existing algorithms, and even exceeds their performance with larger buffers.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228975"}, {"primary_key": "1202737", "vector": [], "sparse_vector": [], "title": "Joint Participation Incentive and Network Pricing Design for Federated Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Federated learning protects users' data privacy though sharing users' local model parameters (instead of raw data) with a server. However, when massive users train a large machine learning model through federated learning, the dynamically varying and often heavy communication overhead can put significant pressure on the network operator. The operator may choose to dynamically change the network prices in response, which will eventually affect the payoffs of the server and users. This paper considers the under-explored yet important issue of the joint design of participation incentives (for encouraging users' contribution to federated learning) and network pricing (for managing network resources). Due to heterogeneous users' private information and multi-dimensional decisions, the optimization problems in Stage I of multi-stage games are non-convex. Nevertheless, we are able to analytically derive the corresponding optimal contract and pricing mechanism through proper transformations of constraints, variables, and functions, under both vertical and horizontal interaction structures of the participants. We show that the vertical structure is better than the horizontal one, as it avoids the interests misalignment between the server and the network operator. Numerical results based on real-world datasets show that our proposed mechanisms decrease server's cost by up to 24.87% comparing with the state-of-the-art benchmarks.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229084"}, {"primary_key": "1202738", "vector": [], "sparse_vector": [], "title": "Rotation Speed Sensing with mmWave Radar.", "authors": ["<PERSON><PERSON>", "<PERSON>ming Jin", "<PERSON><PERSON>"], "summary": "Machines with rotary parts are prevalent in industrial systems and our daily lives. Rotation speed measurement is a crucial task for monitoring machinery health. Previous approaches for rotation speed sensing are constrained by limited operation distance, strict requirement for illumination, or strong dependency on the target object's light reflectivity. In this work, we propose mRotate, a practical mmWave radar-based rotation speed sensing system liberated from all the above constraints. Specifically, mRotate separates the target signal reflected by the rotating object from the mixed reflection signals, extracts the high-quality rotation related features, and accurately obtains the rotation speed through the customized radar sensing mode and algorithm design. We implement mRotate on a commercial mmWave radar and extensively evaluate it in both lab environments and in a machining workshop for field tests. mRotate achieves an MAPE of 0.24% in accuracy test, which is 38% lower than that produced by the baseline device, a popular commercial laser tachometer. Besides, our experiments show that mRotate can measure a spindle whose diameter is only 5mm, maintain a high accuracy with a sensing distance as far as 2.5m, and simultaneously measure the rotation speeds of multiple objects.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229066"}, {"primary_key": "1202740", "vector": [], "sparse_vector": [], "title": "Mind Your Heart: <PERSON><PERSON><PERSON> Backdoor Attack on Dynamic Deep Neural Network in Edge Computing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Transforming off-the-shelf deep neural network (DNN) models into dynamic multi-exit architectures can achieve inference and transmission efficiency by fragmenting and distributing a large DNN model in edge computing scenarios (e.g., edge devices and cloud servers). In this paper, we propose a novel backdoor attack specifically on the dynamic multi-exit DNN models. Particularly, we inject a backdoor by poisoning one DNN model’s shallow hidden layers targeting not this vanilla DNN model but only its dynamically deployed multi-exit architectures. Our backdoored vanilla model behaves normally on performance and cannot be activated even with the correct trigger. However, the backdoor will be activated when the victims acquire this model and transform it into a dynamic multi-exit architecture at their deployment. We conduct extensive experiments to prove the effectiveness of our attack on three structures (ResNet-56, VGG-16, and MobileNet) with four datasets (CIFAR-10, SVHN, GTSRB, and Tiny-ImageNet) and our backdoor is stealthy to evade multiple state-of-the-art backdoor detection or removal methods.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229092"}, {"primary_key": "1202741", "vector": [], "sparse_vector": [], "title": "Latency-Oriented Elastic Memory Management at Task-Granularity for Stateful Streaming Processing.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In a streaming application, an operator is usually instantiated into multiple tasks for parallel processing. Tasks across operators have various memory demands due to different processing logic (e.g., stateful vs. stateless tasks). The memory demands of tasks from the same operator could also vary and fluctuate due to workload variability. Improper memory provision will cause some tasks to have relatively high latency, or even unbound latency that can eventually lead to system instability. We found that the task with the maximum latency of an operator has a significant and even decisive impact on the end-to-end latency.In this paper, we present our task-level memory manager. Based on our quantitative modeling of memory and task-level latency, the manager can adaptively allocate optimal memory size to each task for minimizing the end-to-end latency. We integrate our memory management on Apache Flink. The experiments show that our memory management could significantly reduce end-to-end latency for various applications at different scales and configurations, compared to the Flink native setting.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228963"}, {"primary_key": "1202742", "vector": [], "sparse_vector": [], "title": "A Better Cardinality Estimator with Fewer Bits, Constant Update Time, and Mergeability.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cardinality estimation is a fundamental problem with diverse practical applications. HyperLogLog (HLL) has become a standard in practice because it offers good memory efficiency, constant update time, and mergeability. Some recent work achieved better memory efficiency, but typically at the cost of impractical update time or losing mergeability, making them incompatible with applications like network-wide traffic measurement. This work presents SpikeSketch, a better cardinality estimator that reduces memory usage of HLL by 37% without sacrificing other crucial metrics. We adopt a bucket-based data structure to promise constant update time, design a smoothed log 4 ranking and a spike coding scheme to compress cardinality observables into buckets, and propose a lightweight mergeable lossy compression to balance memory usage, information loss, and mergeability. Then we derive an unbiased estimator for recovering cardinality from the lossy-compressed sketch. Theoretical and empirical results show that SpikeSketch can work as a drop-in replacement for HLL because it achieves a near-optimal MVP (memory-variance-product) of 4.08 (37% smaller than HLL) with constant update time and mergeability. Its memory efficiency even defeats ACPC and HLLL, the state-of-the-art lossless-compressed sketches using linear-time compression to reduce memory usage.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229088"}, {"primary_key": "1202744", "vector": [], "sparse_vector": [], "title": "WiseCam: <PERSON><PERSON>ning Wireless Pan-Tilt Cameras for Cost-Effective Moving Object Tracking.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Zhenhua Li", "<PERSON><PERSON>"], "summary": "With desired functionality of moving object tracking, wireless pan-tilt cameras are able to play critical roles in a growing diversity of surveillance environments. However, today's pan-tilt cameras oftentimes underperform when tracking frequently moving objects like humans – they are prone to lose sight of objects and bring about excessive mechanical rotations that are especially detrimental to those energy-constrained outdoor scenarios. The ineffectiveness and high cost of state-of-the-art tracking approaches are rooted in their adherence to the industry's simplicity principle, which leads to their stateless nature, performing gimbal rotations based only on the latest object detection. To address the issues, we design and implement WiseCam that wisely tunes the pan-tilt cameras to minimize mechanical rotation costs while maintaining long-term object tracking. We examine the performance of WiseCam by experiments on two types of pan-tilt cameras with different motors. Results show that WiseCam significantly outperforms the state-of-the-art tracking approaches on both tracking duration and power consumption.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228926"}, {"primary_key": "1202746", "vector": [], "sparse_vector": [], "title": "Dynamic Regret of Randomized Online Service Caching in Edge Computing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper studies an online service caching problem, where an edge server, equipped with a prediction window of future service request arrivals, needs to decide which services to host locally subject to limited storage capacity. The edge server aims to minimize the sum of a request forwarding cost (i.e., the cost of forwarding requests to remote data centers to process) and a service instantiating cost (i.e., that of retrieving and setting up a service). Considering request patterns are usually non-stationary in practice, the performance of the edge server is measured by dynamic regret, which compares the total cost with that of the dynamic optimal offline solution. To solve the problem, we propose a randomized online algorithm with low complexity and theoretically derive an upper bound on its expected dynamic regret. Simulation results show that our algorithm significantly outperforms other state-of-the-art policies in terms of the runtime and expected total cost.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229044"}, {"primary_key": "1202747", "vector": [], "sparse_vector": [], "title": "mmMIC: Multi-modal Speech Recognition based on mmWave Radar.", "authors": ["<PERSON> Fan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the proliferation of voice assistants, microphone-based speech recognition technology usually cannot achieve good performance in the situation of multiple sound sources and ambient noises. In this paper, we propose a novel mmWave-based solution to perform speech recognition to tackle the issues of multiple sound sources and ambient noises, by precisely extracting the multi-modal features from lip motion and vocal-cords vibration from the single channel of mmWave. We propose a difference-based method for feature extraction of lip motion to suppress the dynamic interference from body motion and head motion. We propose a speech detection method based on cross-validation of lip motion and vocal-cords vibration so as to avoid wasting computing resources on nonspeaking activities. We propose a multi-modal fusion framework for speech recognition by fusing the signal features from lip motion and vocal-cords vibration with the attention mechanism. We implemented a prototype system and evaluated the performance in real test-beds. Experiment results show that the average speech recognition accuracy is 92.8% in realistic environments.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.********"}, {"primary_key": "1202748", "vector": [], "sparse_vector": [], "title": "Nowhere to Hide: Detecting Live Video Forgery via Vision-WiFi Si<PERSON>ette Correspondence.", "authors": ["<PERSON><PERSON><PERSON> Fang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Jinsong Han", "<PERSON><PERSON>", "<PERSON>"], "summary": "For safety guard and crime prevention, video surveillance systems have been pervasively deployed in many security-critical scenarios, such as the residence, retail stores, and banks. However, these systems could be infiltrated by the adversary and the video streams would be modified or replaced, i.e., under the video forgery attack. The prevalence of Internet of Things (IoT) devices and the emergence of Deepfake-like techniques severely emphasize the vulnerability of video surveillance systems under such attacks. To secure existing surveillance systems, in this paper we propose a vision-WiFi cross-modal video forgery detection system, namely WiSil. Leveraging a theoretical model based on the principle of signal propagation, WiSil constructs wave front information of the object in the monitoring area from WiFi signals. With a well-designed deep learning network, WiSil further recovers silhouettes from the wave front information. Based on a Siamese network-based semantic feature extractor, WiSil can eventually determine whether a frame is manipulated by comparing the semantic feature vectors extracted from the video’s silhouette with those extracted from the WiFi’s silhouette. Extensive experiments show that WiSil can achieve 95% accuracy in detecting tampered frames. Moreover, WiSil is robust against environment and person changes.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228947"}, {"primary_key": "1202749", "vector": [], "sparse_vector": [], "title": "Efficient Verification of Timing-Related Network Functions in High-Speed Hardware.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "To achieve a line rate in the high-speed environment of modern networks, there is a continuing effort to offload network functions from software to programmable hardware (HW). Although the offloading effort has led to greater performance, it brings difficulty in the verification of timing-related network functions (Time-NFs) as well. Time-NFs use numerical timing values to perform various network tasks. For example, congestion control algorithm BBR uses round-trip time to improve throughput. Errors in Time-NFs could cause packet loss and poor throughput. However, verifying Time-NFs in HW often involves many clock cycles that can result in an exponentially increasing number of test cases. Current verification methods either do not scale or sacrifice soundness for scalability.In this paper, we propose an invariant-based method to improve the verification efficiency without losing soundness. Our method is motivated by an observation that most Time-NFs follow a few fixed patterns to use timing information. Based on these patterns, we develop a set of easy-to-validate invariants to constrain the examination space. According to experiments on real Time-NFs, our method can speed up verification by 7 times on average without losing the verification soundness.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228994"}, {"primary_key": "1202750", "vector": [], "sparse_vector": [], "title": "mmEavesdropper: Signal Augmentation-based Directional Eavesdropping with mmWave Radar.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the popularity of online meetings equipped with speakers, voice privacy security has drawn increasing attention because eavesdropping on the speakers can quickly obtain sensitive information. In this paper, we propose mmEavesdropper, a mmWave based eavesdropping system, which focuses on augmenting the micro-vibration signal via theoretical models for voice recovery. Particularly, to augment the receiving signal of the target vibration, we propose to use beam-forming to facilitate the directional augmentation by suppressing other orientations and use Chirp-Z transform to facilitate the distance augmentation by increasing the range resolution compared with traditional FFT. To augment the vibration signal in the IQ plane, we build a theoretical model to analyze the distortion and propose a segmentation-based fitting method to calibrate the vibration signal. To augment the spectrum for sound recovery, we propose to combine multiple channels and leverage an encoder-decoder based neural network to reconstruct the spectrogram for voice recovery. We perform extensive experiments on mmEavesdropper and the results show that mmEavesdropper can reach the accuracy of 93% on digit and letter recognition. Moreover, mmEavesdropper can reconstruct the voice with an average SNR of 5dB and peak SNR of 17dB.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.********"}, {"primary_key": "1202753", "vector": [], "sparse_vector": [], "title": "A Learning Approach to Minimum Delay Routing in Stochastic Queueing Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the minimum delay routing problem in stochastic queueing networks where the goal is to find the optimal static routing policy that minimizes the average delay in the network. Previous works on minimum delay routing rely on knowledge of the delay function that maps the routing policies to their corresponding average delay, which is typically unavailable in stochastic queueing networks due to the complex dependency of the delay function on the distributional characteristics of network links. In this paper, we propose a learning approach to the minimum delay routing problem, whereby instead of relying on aprior information on the delay function, we seek to learn the delay function through observations. We design an algorithm that leverages finite-time observations of network queue lengths to approximate the values of the delay function, uses the approximate values to estimate the gradient of the delay function, and performs gradient descent based on the estimated gradient to optimize the routing policy. We prove that our algorithm converges to the optimal static routing policy when the delay function is convex, which is a reasonable condition in practical settings. We conduct extensive simulations to evaluate the empirical performance of our algorithm, demonstrating its superior delay performance over static policies and even dynamic policies such as Join-the-Shortest-Queue and BackPressure.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.********"}, {"primary_key": "1202754", "vector": [], "sparse_vector": [], "title": "Your Locations May Be Lies: Selective-PRS-Spoofing Attacks and Defence on 5G NR Positioning Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hongwu Lv", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "5G positioning systems, as a solution for city-range integrated-sensing-and-communication (ISAC), are flooding into reality. However, the positioning security aspects of such an ISAC system have been overlooked, bringing threats to more than a billion 5G users. In this paper, we propose a new threat model for 5G positioning scenarios, namely the selective-PRS-spoofing attack (SPS), disabling the latest security enhancement method reported in 3GPP R18. In our attack pattern, the attacker first cracks the broadcast information of a 5G network and then poisons specific resource elements of the channel, which can introduce substantial localization errors at victims or even completely control the positioning results. Worse, such attacks are transparent to both the UE-end and the network-end due to their stealthiness and easily bypass the current 3GPP defense mechanisms. To solve this problem, a DL-based defense method called in-phase quadrature Network (IQ-Net) is proposed, which utilizes the hardware features of base stations to perform identification at the physical level, thereby thwarting SPS attacks on 5G positioning systems. Extensive experiments demonstrate that our method has 98% defense accuracy and good robustness to noise.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228877"}, {"primary_key": "1202755", "vector": [], "sparse_vector": [], "title": "Expelliarmus: Command Cancellation Attacks on Smartphones using Electromagnetic Interference.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jinsong Han"], "summary": "Human-machine interactions (HMIs), e.g., touchscreens, are essential for users to interact with mobile devices. They are also beneficial in resisting emerging active attacks, which aim at maliciously controlling mobile devices, e.g., smartphones and tablets. With touchscreen-like HMIs, users can notice and interrupt malicious actions conducted by the attackers timely and perform necessary countermeasures, e.g., tapping the 'Quit' button on the touchscreen. However, the effect of HMI-oriented active attacks has not been investigated yet. In this paper, we present a practical attack towards touch-based devices, namely Expelliarmus. It reveals a new attack surface of active attacks for hijacking users' operations and thus taking full control over victim devices. Expelliarmus neutralizes users' touch commands by producing a reverse current via electromagnetic interference (EMI). Since the reverse current offsets the current change caused by a touch, the touchscreen detects no current change and thus ignores users' commands. Besides this basic denial-of-service attack, we also realize a target cancellation attack, which can neutralize target commands, e.g., 'Quit' without interference in irrelevant operations. Thus, the active attack can be completely performed without interruption from users, even if they are alerted by the abnormal events. Extensive evaluations demonstrate the effectiveness of Expelliarmus on 29 off-the-shelf devices.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228859"}, {"primary_key": "1202756", "vector": [], "sparse_vector": [], "title": "High-speed Machine Learning-enhanced Receiver for Millimeter-Wave Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Machine Learning (ML) is a promising tool to design wireless physical layer (PHY) components. It is particularly interesting for millimeter-wave (mm-wave) frequencies and above, due to the more challenging hardware design and channel environment at these frequencies. Rather than building individual ML-components, in this paper, we design an entire ML-enhanced mm-wave receiver for frequency selective channels. Our ML-receiver jointly optimizes the channel estimation, equalization, phase correction and demapper using Convolutional Neural Networks. We also show that for mm-wave systems, the channel varies significantly even over short timescales, requiring frequent channel measurements, and this situation is exacerbated in mobile scenarios. To tackle this, we propose a new ML-channel estimation approach that refreshes the channel state information using the guard intervals (not intended for channel measurements) that are available for every block of symbols in communication packets. To the best of our knowledge, our ML-receiver is the first work to outperform conventional receivers in general scenarios, with simulation results showing up to 7 dB gains. We also provide an experimental validation of the ML-enhanced receiver with a 60 GHz FPGA-based testbed with phased antenna arrays, which shows a throughput increase by a factor of up to 6 over baseline schemes in mobile scenarios.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229087"}, {"primary_key": "1202757", "vector": [], "sparse_vector": [], "title": "Safehaul: Risk-Averse Learning for Reliable mmWave Self-Backhauling in 6G Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Wireless backhauling at millimeter-wave frequencies (mmWave) in static scenarios is a well-established practice in cellular networks. However, highly directional and adaptive beamforming in today’s mmWave systems have opened new possibilities for self-backhauling. Tapping into this potential, 3GPP has standardized Integrated Access and Backhaul (IAB) allowing the same base station to serve both access and backhaul traffic. Although much more cost-effective and flexible, resource allocation and path selection in IAB mmWave networks is a formidable task. To date, prior works have addressed this challenge through a plethora of classic optimization and learning methods, generally optimizing a Key Performance Indicator (KPI) such as throughput, latency, and fairness, and little attention has been paid to the reliability of the KPI. We propose Safehaul, a risk-averse learning-based solution for IAB mmWave networks. In addition to optimizing average performance, Safehaul ensures reliability by minimizing the losses in the tail of the performance distribution. We develop a novel simulator and show via extensive simulations that Safehaul not only reduces the latency by up to 43.2% compared to the benchmarks, but also exhibits significantly more reliable performance, e.g., 71.4% less variance in achieved latency.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228969"}, {"primary_key": "1202762", "vector": [], "sparse_vector": [], "title": "Time and Cost-Efficient Cloud Data Transmission based on Serverless Computing Compression.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>peng Dai", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yaofeng Tu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Nowadays, there exists a lot of cross-region data transmission demand on cloud. It is promising to use serverless computing for compressing data to save the transmission data amount. However, it is challenging to estimate the data transmission time and monetary cost with serverless compression. In addition, minimizing the data transmission cost is non-trivial due to enormous parameter space and joint optimization. This paper focuses on this problem and makes the following contributions: (1) We propose empirical data transmission time and monetary cost models based on serverless compression. (2) For single-task cloud data transmission, we propose two efficient parameter search methods based on Sequential Quadratic Programming (SQP ) and Eliminate then Divide and Conquer (EDC), which are theoretically proven with error upper bounds. (3) Furthermore, for multi-task cloud data transmission, a parameter search method based on dynamic programming and numerical computation is proposed to reduce the algorithm complexity from exponential to linear complexity. We have implemented the entire actual system and evaluated it with various workloads and application cases on the real-world AWS serverless computing platform. Experimental results on cross-region public cloud show that the proposed approach can improve the parameter search efficiency by more than 3× compared with the state-of-art parameter search methods and achieves better parameter quality. Compared with other competing cloud data transmission approaches, our approach is able to achieve higher time efficiency and lower monetary cost.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229090"}, {"primary_key": "1202763", "vector": [], "sparse_vector": [], "title": "LOPO: An Out-of-order Layer Pulling Orchestration Strategy for Fast Microservice Startup.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Container based microservices have been widely applied to promote the cloud elasticity. The mainstream Docker containers are structured in layers, which are organized in stack with bottom-up dependency. To start a microservice, the required layers are pulled from a remote registry and stored on its host server, following the layer dependency order. This incurs long microservice startup time and hinders the performance efficiency. In this paper, we discover that, for the first time, the layer pulling order can be adjusted to accelerate the microservice startup. Specifically, we address the problem on microservice layer pulling orchestration for startup time minimization and prove it as NP-hard. We propose a Longest-chain based Out-of-order layer Pulling Orchestration (LOPO) strategy with low computational complexity and guaranteed approximation ratio. Through extensive real-world trace driven experiments, we verify the efficiency of our LOPO and demonstrate that it reduces the microservice startup time by 22.71% on average in comparison with state-of-the-art solutions.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229072"}, {"primary_key": "1202764", "vector": [], "sparse_vector": [], "title": "Enabling Communication-Efficient Federated Learning via Distributed Compressed Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Tao Ren", "<PERSON><PERSON><PERSON>"], "summary": "Federated learning (FL) trains a shared global model by periodically aggregating gradients from local devices. Communication overhead becomes a principal bottleneck in FL since participating devices usually suffer from limited bandwidth and unreliable connections in uplink transmission. To address this problem, the gradient compression methods based on compressed sensing (CS) theory have been put forward recently. However, most existing CS-based works compress gradients independently, ignoring the gradient correlations between participants or adjacent communication rounds, which constrains the achievement of higher compression rates. In view of the above observation, we propose a novel gradient compression scheme named FedDCS, guided by distributed compressed sensing (DCS) theory. Following the design philosophy of separate encoding and joint decoding in DCS, FedDCS compresses gradients for participants in each round separately while reconstructing them at the central server jointly via fully exploiting correlated gradients from the previous round, which are known as side information (SI). Benefiting from this design, reconstruction performance is significantly improved with fewer decoding errors also iterations under the identical compression rate, and the total uploading bits to achieve model convergence are considerably reduced. Theoretical analysis and extensive experiments conducted on MNIST and Fashion-MNIST both verify the effectiveness of our approach.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229032"}, {"primary_key": "1202768", "vector": [], "sparse_vector": [], "title": "OPA: One-Predict-All For Efficient Deployment.", "authors": ["<PERSON><PERSON><PERSON>", "Shengqing Xia", "<PERSON><PERSON>"], "summary": "Deep neural network (DNN) is the de facto standard for running a variety of computer vision applications over mobile and embedded systems. Prior to deployment, a DNN is specialized by training to fit the target use scenario (depending on computing power and visual data input). To handle its costly training and meet diverse deployment needs, a \"Train Once, Deploy Everywhere\" paradigm has been recently proposed by training one super-network and selecting one out of many sub-networks (part of the super-network) for the target scenario; This empowers efficient DNN deployment at low training cost (training once). However, the existing studies tackle some deployment factors like computing power and source data but largely overlook the impact of their runtime dynamics (say, time-varying visual contents and GPU/CPU workloads). In this work, we propose OPA to cover all these deployment factors, particularly those along with runtime dynamics in visual data contents and computing resources. To quickly and accurately learn which sub-network runs \"best\" in the dynamic deployment scenario, we devise a \"One-Predict-All\" approach with no need to run all the candidate sub-networks. Instead, we first develop a shallow sub-network to test the water and then use its test results to predict the performance of all other deeper sub-networks. We have implemented and evaluated OPA. Compared to the state-of-the-art, OPA has achieved up to 26% higher Top-1 accuracy for a given latency requirement.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228928"}, {"primary_key": "1202770", "vector": [], "sparse_vector": [], "title": "DeeP4R: Deep Packet Inspection in P4 using Packet Recirculation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Software-defined networks are useful for multiple tasks, including firewalling, telemetry, and flow analysis. In particular, the P4 language makes it possible to carry out some simple packet processing tasks in the data plane, i.e., on the switch itself (without real-time support from the SDN controller or a server). However, owing to the limitations of packet parsing in P4, these tasks involve only the packet headers. In this paper, we present a novel approach that allows Deep Packet Inspection (DPI) – i.e., inspection of the packet payload – in the data plane, using P4 alone. We make use of the fact that in P4, a switch can clone and recirculate packets. One copy (clone) can be recirculated, slicing off a byte in each round, and using a finite-state machine to check if a target string has yet been seen. If the target string is found, the other copy (original packet) is discarded; if not, it is passed through. Our approach allows us to build the first application-layer firewall (URL filter) in the data plane, and to achieve essentially line-rate performance while filtering thousands of URLs, on a commodity programmable switch. It may in future also be used for other DPI tasks.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228996"}, {"primary_key": "1202776", "vector": [], "sparse_vector": [], "title": "SplitGP: Achieving Both Generalization and Personalization in Federated Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "A fundamental challenge to providing edge-AI services is the need for a machine learning (ML) model that achieves personalization (i.e., to individual clients) and generalization (i.e., to unseen data) properties concurrently. Existing techniques in federated learning (FL) have encountered a steep tradeoff between these objectives and impose large computational requirements on edge devices during training and inference. In this paper, we propose SplitGP, a new split learning solution that can simultaneously capture generalization and personalization capabilities for efficient inference across resource-constrained clients (e.g., mobile/IoT devices). Our key idea is to split the full ML model into client-side and server-side components, and impose different roles to them: the client-side model is trained to have strong personalization capability optimized to each client's main task, while the server-side model is trained to have strong generalization capability for handling all clients' out-of-distribution tasks. We analytically characterize the convergence behavior of SplitGP, revealing that all client models approach stationary points asymptotically. Further, we analyze the inference time in SplitGP and provide bounds for determining model split ratios. Experimental results show that SplitGP outperforms existing baselines by wide margins in inference time and test accuracy for varying amounts of out-of-distribution samples.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229027"}, {"primary_key": "1202777", "vector": [], "sparse_vector": [], "title": "BreathSign: Transparent and Continuous In-ear Authentication Using Bone-conducted Breathing Biometrics.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As one of the most natural physiological activities, breathing provides an effective and ubiquitous approach for continuous authentication. Inspired by that, this paper presents BreathSign, which reveals a novel biometric characteristic using bone-conducted human breathing sound and provides an anti-spoofing and transparent authentication mechanism based on inward-facing microphones on commercial earphones. To explore the breathing differences among persons, we first analyze how the breathing sound propagates in the body, and then derive unique body physics-level features from breathing-induced body sounds. Furthermore, to eliminate the impact of behavioral biometrics, we design a triple network model to reconstruct breathing behavior-independent features. Extensive experiments with 20 subjects over a period have been conducted to evaluate the accuracy, robustness, and vulnerability of BreathSign. The results show that our system accurately authenticates users with an average authentication accuracy rate of 95.17% via only one breathing cycle, and effectively defends against various spoofing attacks with an average spoofing attack detection rate of 98.25%. Compared with other continuous authentication solutions, BreathSign extracts hard-to-forge biometrics in the effortless human breathing activity for authentication and can be easily implemented on commercial earphones with high usability and enhanced security.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229037"}, {"primary_key": "1202778", "vector": [], "sparse_vector": [], "title": "Dynamic Demand-Aware Link Scheduling for Reconfigurable Datacenters.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Emerging reconfigurable datacenters allow to dynamically adjust the network topology in a demand-aware manner. These datacenters rely on optical switches which can be reconfigured to provide direct connectivity between racks, in the form of edge-disjoint matchings. While state-of-the-art optical switches in principle support microsecond reconfigurations, the demand-aware topology optimization constitutes a bottleneck.This paper proposes a dynamic algorithms approach to improve the performance of reconfigurable datacenter networks, by supporting faster reactions to changes in the traffic demand. This approach leverages the temporal locality of traffic patterns in order to update the interconnecting matchings incrementally, rather than recomputing them from scratch. In particular, we present six (batch-)dynamic algorithms and compare them to static ones. We conduct an extensive empirical evaluation on 176 synthetic and 39 real-world traces, and find that dynamic algorithms can both significantly improve the running time and reduce the number of changes to the configuration, especially in networks with high temporal locality, while retaining matching weight.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229050"}, {"primary_key": "1202781", "vector": [], "sparse_vector": [], "title": "DeepScheduler: Enabling Flow-Aware Scheduling in Time-Sensitive Networking.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Time-Sensitive Networking (TSN) has been considered the most promising network paradigm for time-critical applications (e.g., industrial control) and traffic scheduling is the core of TSN to ensure low latency and determinism. With the demand for flexible production increases, industrial network topologies and settings change frequently due to pipeline switches. As a result, there is a pressing need for a more efficient TSN scheduling algorithm. In this paper, we propose DeepScheduler, a fast and scalable flow-aware TSN scheduler based on deep reinforcement learning. In contrast to prior work that heavily relies on expert knowledge or problem-specific assumptions, DeepScheduler automatically learns effective scheduling policies from the complex dependency among data flows. We design a scalable neural network architecture that can process arbitrary network topologies with informative representations of the problem, and decompose the problem decision space for efficient model training. In addition, we develop a suite of TSN-compatible testbeds with hardware-software co-design and DeepScheduler integration. Extensive experiments on both simulation and physical testbeds show that DeepScheduler runs >150/5 times faster and improves the schedulability by 36%/39% compared to state-of-the-art heuristic/expert-based methods. With both efficiency and effectiveness, DeepScheduler makes scheduling no longer an obstacle towards flexible manufacturing.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228875"}, {"primary_key": "1202782", "vector": [], "sparse_vector": [], "title": "Network Adaptive Federated Learning: Congestion and Lossy Compression.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In order to achieve the dual goals of privacy and learning across distributed data, Federated Learning (FL) systems rely on frequent exchanges of large files (model updates) between a set of clients and the server. As such FL systems are exposed to, or indeed the cause of, congestion across a wide set of network resources. Lossy compression can be used to reduce the size of exchanged files and associated delays, at the cost of adding noise to model updates. By judiciously adapting clients' compression to varying network congestion, an FL application can reduce wall clock training time. To that end, we propose a Network Adaptive Compression (NAC-FL) policy, which dynamically varies the client's lossy compression choices to network congestion variations. We prove, under appropriate assumptions, that NAC-FL is asymptotically optimal in terms of directly minimizing the expected wall clock training time. Further, we show via simulation that NAC-FL achieves robust performance improvements with higher gains in settings with positively correlated delays across time.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228885"}, {"primary_key": "1202784", "vector": [], "sparse_vector": [], "title": "Prophet: Conflict-Free Sharding Blockchain via Byzantine-Tolerant Deterministic Ordering.", "authors": ["Zicong Hong", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Sharding scales throughput by splitting blockchain nodes into parallel groups. However, different shards' independent and random scheduling for cross-shard transactions results in numerous conflicts and aborts, since cross-shard transactions from different shards may access the same account. A deterministic ordering can eliminate conflicts by determining a global order for transactions before processing, as proved in the database field. Unfortunately, due to the intertwining of the Byzantine environment and information isolation among shards, there is no trusted party able to predetermine such an order for cross-shard transactions. To tackle this challenge, this paper proposes Prophet, a conflict-free sharding blockchain based on Byzantine-tolerant deterministic ordering. It first depends on untrusted self-organizing coalitions of nodes from different shards to pre-execute cross-shard transactions for prerequisite information about ordering. It then determines a trusted global order based on stateless ordering and post-verification for pre-executed results, through shard cooperation. Following the order, the shards thus orderly execute and commit transactions without conflicts. <PERSON> orchestrates the pre-execution, ordering, and execution processes in the sharding consensus for minimal overhead. We rigorously prove the determinism and serializability of transactions under the Byzantine and sharded environment. An evaluation of our prototype shows that <PERSON> improves the throughput by 3.11× and achieves nearly no aborts on 1 million Ethereum transactions compared with state-of-the-art sharding.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228939"}, {"primary_key": "1202785", "vector": [], "sparse_vector": [], "title": "Search in the Expanse: Towards Active and Global IPv6 Hitlists.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Global-scale IPv6 scan, critical for network measurement and management, is still a mission to be accomplished due to its vast address space. To tackle this challenge, IPv6 scan generally leverages pre-defined seed addresses to guide search directions. Under this general principle, however, the core problem of effectively using the seeds is largely open. In this work, we propose a novel IPv6 active search strategy, namely HMap6, which significantly improves the use of seeds, w.r.t. the marginal benefit, for large-scale active address discovery in various prefixes. Using a heuristic search strategy for efficient seed collection and alias prefix detection under a wide range of BGP prefixes, HMap6 can greatly expand the scan coverage. Real-world experiments over the Internet in billion-scale scans show that HMap6 can discover 29.39M unique /80 prefixes with active addresses, an 11.88% improvement over the state-of-the-art methods. Furthermore, the IPv6 hitlists from HMap6 include all-responsive IPv6 addresses with rich information. This result sharply differs from existing public IPv6 hitlists, which contain non-responsive and filtered addresses, and pushes the IPv6 hitlists from quantity to quality. To encourage and benefit further IPv6 measurement studies, we released our tool along with our IPv6 hitlists and the detected alias prefixes.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229089"}, {"primary_key": "1202786", "vector": [], "sparse_vector": [], "title": "One Shot for All: Quick and Accurate Data Aggregation for LPWANs.", "authors": ["<PERSON><PERSON><PERSON>", "Xianjin Xia", "<PERSON><PERSON>", "Yuan<PERSON> Zheng"], "summary": "This paper presents our design and implementation of a fast and accurate data aggregation strategy for LoRa networks named One-shot. To facilitate data aggregation, One-shot assigns distinctive chirps for different LoRa nodes to encode individual data. One-shot coordinates the nodes to concurrently transmit encoded packets. Receiving concurrent transmissions, One-shot gateway examines the frequencies of superimposed chirp signals and computes application-defined aggregate functions (e.g., sum, max, count, etc.), which give a quick overview of sensor data in a large monitoring area. One-shot develops techniques to handle a series of practical challenges involved in frequency and time synchronization of concurrent chirps. We evaluate the effectiveness of One-shot with extensive experiments. Results show that One-shot substantially outperforms state-of-the-art data aggregation methods in terms of aggregation accuracy as well as query efficiency.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229099"}, {"primary_key": "1202787", "vector": [], "sparse_vector": [], "title": "EAVS: Edge-assisted Adaptive Video Streaming with Fine-grained Serverless Pipelines.", "authors": ["Biao <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Fu"], "summary": "Recent years have witnessed video streaming gradually evolve into one of the most popular Internet applications. With the rapidly growing personalized demand for real-time video streaming services, maximizing their Quality of Experience (QoE) is a long-standing challenge. The emergence of the serverless computing paradigm has potential to meet this challenge through its fine-grained management and highly parallel computing structures. However, it is still ambiguous how to implement and configure serverless components to optimize video streaming services. In this paper, we propose EAVS, an Edge-assisted Adaptive Video streaming system with Serverless pipelines, which facilitates fine-grained management for multiple concurrent video transmission pipelines. Then, we design a chunk-level optimization scheme to address video bitrate adaptation. We propose a Deep Reinforcement Learning (DRL) algorithm based on Proximal Policy Optimization (PPO) with a trinal-clip mechanism to make bitrate decisions efficiently for better QoE. Finally, we implement the serverless video streaming system prototype and evaluate the performance of EAVS on various real-world network traces. Our results show that EAVS significantly improves QoE and reduces the video stall rate, achieving over 9.1% QoE improvement and 60.2% latency reduction compared to state-of-the-art solutions.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229102"}, {"primary_key": "1202789", "vector": [], "sparse_vector": [], "title": "CSI-StripeFormer: Exploiting Stripe Features for CSI Compression in Massive MIMO System.", "authors": ["Qingyong Hu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The massive MIMO gain for wireless communication has been greatly hindered by the feedback overhead of channel state information (CSI) growing linearly with the number of antennas. Recent efforts leverage the DNN-based encoder-decoder framework to exploit correlations within the CSI matrix for better CSI compression. However, existing works have not fully exploited the unique features of CSI, resulting in an unsatisfactory performance under high compression ratios and sensitivity to multipath effects. Instead of treating CSI as common 2D matrices like images, we reveal the intrinsic stripe-based correlation across the CSI matrix. Driven by this insight, we propose CSI-StripeFormer, a stripe-aware encoder-decoder framework to exploit the unique stripe feature for better CSI compression. We design a lightweight encoder with asymmetric convolution kernels to capture various shape features. We further incorporate novel designs tailored for stripe features, including a novel hierarchical Transformer backbone in the decoder and a hybrid attention mechanism to extract and fuse correlations in angular and delay domains. Our evaluation results show that our system achieves an over 7dB channel reconstruction gain under a high compression ratio of 64 in multipath-rich scenarios, significantly superior to current state-of-the-art approaches. This gain can be further improved to 17dB given the extended embedded dimension of our backbone.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229094"}, {"primary_key": "1202792", "vector": [], "sparse_vector": [], "title": "Adversarial Group Linear Bandits and Its Application to Collaborative Edge Inference.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Multi-armed bandits is a classical sequential decision-making under uncertainty problem. The majority of existing works study bandits problems in either the stochastic reward regime or the adversarial reward regime, but the intersection of these two regimes is much less investigated. In this paper, we study a new bandits problem, called adversarial group linear bandits (AGLB), that features reward generation as a joint outcome of both the stochastic process and the adversarial behavior. In particular, the reward that the learner receives is not only a noisy linear function of the arm that the learner selects within a group but also depends on the group-level attack decision by the adversary. Such problems are present in many real-world applications, e.g., collaborative edge inference and multi-site online ad placement. To combat the uncertainty in the coupled stochastic and adversarial rewards, we develop a new bandits algorithm, called EXPUCB, which marries the classical LinUCB and EXP3 algorithms, and prove its sublinear regret. We apply EXPUCB to the collaborative edge inference problem and evaluate its performance. Extensive simulation results verify the superior learning ability of EXPUCB under coupled stochastic and adversarial rewards.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228900"}, {"primary_key": "1202793", "vector": [], "sparse_vector": [], "title": "Buffer Awareness Neural Adaptive Video Streaming for Avoiding Extra Buffer Consumption.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Lifeng Sun"], "summary": "Adaptive video streaming has already been a major scheme to transmit videos with high quality of experience (QoE). However, the improvement of network traffics and the high compression efficiency of videos enable clients to accumulate too much buffer, which might cause colossal data waste if users close the session early before the session ends. In this paper, we consider buffer-aware adaptive bitrate (ABR) mechanisms to overcome the above concerns. Formulating the buffer-aware rate adaptation problem as multi-objective optimization, we propose DeepBuffer, a deep reinforcement learning-based approach that jointly takes proper bitrate and controls the maximum buffer. To deal with the challenges of learning-based buffer-aware ABR composition, such as infinite possible plans, multiple bitrate levels, and complex action space, we design adequate preference-driven inputs, separate action outputs, and invent high sample-efficiency training methodologies. We train DeepBuffer with a broad set of real-world network traces and provide a comprehensive evaluation in terms of various network scenarios and different video types. Experimental results indicate that DeepBuffer rivals or outperforms recent heuristics and learning-based ABR schemes in terms of QoE while heavily reducing the average buffer consumption by up to 90%. Extensive real-world experiments further demonstrate the substantial superiority of DeepBuffer.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229002"}, {"primary_key": "1202795", "vector": [], "sparse_vector": [], "title": "flexRLM: Flexible Radio Link Monitoring for Multi-User Downlink Millimeter-Wave Networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Exploiting millimeter-wave (mm-wave) for high-capacity multi-user networks is predicated on jointly performing beam management for seamless connectivity and efficient resource sharing among all users. Beam management in 5G-NR actively monitors candidate beam pair links (BPLs) on the serving cell to simply select the user's best beam, but neglects the multi-user resource sharing problem, potentially leading to severe throughput degradation on overloaded cells. We propose flexRLM, a coordinator-based flexible radio link monitoring (RLM) framework for multi-user downlink mm-wave networks. flexRLM enables flexible configuration of monitored BPLs on the serving and other candidate cells and beam selection jointly considering link quality and resource sharing. flexRLM is fully 5G-NR-compliant and uses the LTE coordinator in non-standalone mode to continuously update the monitored BPLs via measurement reports from periodic downlink control synchronization signals. We implement flexRLM in ns-3 and present full-stack simulations to demonstrate the superior performance of flexRLM over default 5G-NR RLM in multi-user networks. Our results show that flexRLM's continuous updating of monitored BPLs improves both link quality and stability. By monitoring BPLs on candidate cells other than the serving one, flexRLM also significantly decreases handover decision delays. Importantly, flexRLM's low-complexity coordinated load-balancing achieves a per-user throughput close to the single-user baseline.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229071"}, {"primary_key": "1202797", "vector": [], "sparse_vector": [], "title": "Online Dynamic Acknowledgement with Learned Predictions.", "authors": ["Sungjin Im", "<PERSON>", "<PERSON><PERSON> Xu", "<PERSON><PERSON><PERSON>"], "summary": "We revisit the online dynamic acknowledgment problem. In the problem, a sequence of requests arrive over time to be acknowledged, and all outstanding requests can be satisfied simultaneously by one acknowledgement. The goal of the problem is to minimize the total request delay plus acknowledgement cost. This elegant model studies the tradeoff between acknowledgement cost and waiting experienced by requests. The problem has been well studied and the tight competitive ratios have been determined. For this well-studied problem, we focus on how to effectively use machine-learned predictions to have better performance.We develop algorithms that perform arbitrarily close to the optimum with accurate predictions while concurrently having the guarantees arbitrarily close to what the best online algorithms can offer without access to predictions, thereby achieving simultaneous optimum consistency and robustness. This new result is enabled by our novel prediction error measure. No error measure was defined for the problem prior to our work, and natural measures failed due to the challenge that requests with different arrival times have different effects on the objective. We hope our ideas can be used for other online problems with temporal aspects that have been resisting proper error measures.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228882"}, {"primary_key": "1202798", "vector": [], "sparse_vector": [], "title": "Cost-Effective Live Expansion of Three-Stage Switching Networks without Blocking or Connection Rearrangement.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The rapid growth of datacenter traffic requires network expansion without interrupting communications within and between datacenters. Past studies on network expansion while carrying live traffic have focused on packet-switched networks such as Ethernet. Optical networks have been attracting attention recently for datacenters due to their great transmission capacity and power efficiency, but they are modeled as circuit-switched networks. To our knowledge, no practical live expansion method is known for circuit-switched networks; the Clos network, a nonblocking three-stage switching structure, can be expanded, but it involves connection rearrangement (interruption) or significant initial investment. This paper proposes a cost-effective network expansion method without relying on connection rearrangement. Our method expands a network by rewiring inactive links to include additional switches, which also avoids the blocking of new connection requests. Our method is designed to only require switches whose number is proportional to the network size, which suppresses the initial investment. Numerical experiments show that our method incrementally expands a circuit-switched network from 1,000 to 30,000 ports, sufficient to accommodate all racks in a today’s huge datacenter. The initial structure of our method only consists of three 1024×1024 switches, while that of a reference method requires 34 switches.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229107"}, {"primary_key": "1202800", "vector": [], "sparse_vector": [], "title": "mmFlexible: Flexible Directional Frequency Multiplexing for Multi-user mmWave Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Modern mmWave systems have limited scalability due to inflexibility in performing frequency multiplexing. All the frequency components in the signal are beamformed to one direction via pencil beams and cannot be streamed to other user directions. We present a new flexible mmWave system called mmFlexible that enables flexible directional frequency multiplexing, where different frequency components of the mmWave signal are beamformed in multiple arbitrary directions with the same pencil beam. Our system makes two key contributions: (1) We propose a novel mmWave front-end architecture called a delay-phased array that uses a variable delay and variable phase element to create the desired frequency-direction response. (2) We propose a novel algorithm called FSDA (Frequency-space to delay-antenna) to estimate delay and phase values for the real-time operation of the delay-phased array. Through evaluations with mmWave channel traces, we show that mmFlexible provides a 60-150% reduction in worst-case latency compared to baselines 1 .", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229065"}, {"primary_key": "1202803", "vector": [], "sparse_vector": [], "title": "Mixup Training for Generative Models to Defend Membership Inference Attacks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the popularity of machine learning, it has been a growing concern on the trained model revealing the private information of the training data. Membership inference attack (MIA) poses one of the threats by inferring whether a given sample participates in the training of the target model. Although MIA has been widely studied for discriminative models, for generative models, neither it nor its defense is extensively investigated. In this work, we propose a mixup training method for generative adversarial networks (GANs) as a defense against MIAs. Specifically, the original training data is replaced with their interpolations so that GANs would never overfit the original data. The intriguing part is an analysis from the hypothesis test perspective to theoretically prove our method could mitigate the AUC of the strongest likelihood ratio attack. Experimental results support that mixup training successfully defends the state-of-the-art MIAs for generative models, yet without model performance degradation or any additional training efforts, showing great promise to be deployed in practice.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229036"}, {"primary_key": "1202804", "vector": [], "sparse_vector": [], "title": "RED: Distributed Program Deployment for Resource-aware Programmable Switches.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Cheng<PERSON> Gao", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Programmable switches allow data plane to program how packets are processed, which enables flexibility for network management tasks, e.g., packet scheduling and flow measurement. Existing studies focus on program deployment at a single switch, while deployment across the whole data plane is still a challenging issue. In this paper, we present RED, a Resource-Efficient and Distributed program deployment solution for programmable switches. First of all, we compile the data plane programs to estimate the resource utilization and divide them into two categories for further processing. Then, the proposed merging and splitting algorithms are selectively applied to merge or split the pending programs. Finally, we consolidate the scarce resources of the whole data plane to deploy the programs. Extensive experiment results show that 1) RED improves the speedup by two orders of magnitude compared to P4Visor and merges 58.64% more nodes than SPEED; 2) RED makes the overwhelmed programs run normally at a single switch and reduces 3% latency of inter-device scheduling; 3) RED achieves network-wide resource balancing in a distributed way.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228974"}, {"primary_key": "1202806", "vector": [], "sparse_vector": [], "title": "RDladder: Resolution-Duration Ladder for VBR-encoded Videos via Imitation Learning.", "authors": ["Lianchen Jia", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>yang Li", "Lifeng Sun"], "summary": "With the rapid development of the streaming system, a large number of videos need to be transcoded to multiple copies according to the encoding ladder, which significantly increases the storage overhead than before. This scenario demonstrates the prosperity of streaming media but also presents new challenges about achieving the balance between better quality for users and less storage cost. In our work, we observe two significant points. The first one is selecting proper resolutions under certain network conditions can reduce storage costs while maintaining a great quality of experience. The second one is the segment duration is critical, especially in VBR-encoded videos. Considering these points, we propose RDladder, a resolution-duration ladder for VBR-encoded videos via imitation learning. We jointly optimize resolutions and duration using neural networks to determine the combination of these two metrics considering network capacity, video information, and storage cost. To get more faithful results, we use over 500 videos, encoded to over 2,000,000 chunks, and collect real-world network traces for more than 50 hours. We test RDladder in simulation, emulation, and real-world environments under various network conditions, and our method can achieve near-optimal performance. Furthermore, we discuss the influence between the RDladder and ABR algorithms and summarize some characteristics of RDladder.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228995"}, {"primary_key": "1202807", "vector": [], "sparse_vector": [], "title": "Communication-Aware DNN Pruning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose a Communication-aware Pruning (CaP) algorithm, a novel distributed inference framework for distributing DNN computations across a physical network. Departing from conventional pruning methods, CaP takes the physical network topology into consideration and produces DNNs that are communication-aware, designed for both accurate and fast execution over such a distributed deployment. Our experiments on CIFAR-10 and CIFAR-100, two deep learning benchmark datasets, show that CaP beats state of the art competitors by up to 4% w.r.t. accuracy on benchmarks. On experiments over real-world scenarios, it simultaneously reduces total execution time by 27%–68% at negligible performance decrease (less than 1%).", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229043"}, {"primary_key": "1202808", "vector": [], "sparse_vector": [], "title": "Economic Analysis of Joint Mobile Edge Caching and Peer Content Sharing.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Mobile edge caching (MEC) allows edge devices to cache popular contents and deliver them to end-users directly, which can effectively alleviate increasing backbone loads and improve end-users' quality of service. Peer content sharing (PCS) enables edge devices to share the cached contents with others, and can further increase the caching efficiency. While many efforts have been made to content caching or sharing technologies, it remains open to study the complicated technical and economic interplay between both technologies. In this paper, we propose a joint MEC-PCS framework, and focus on capturing the strategic interactions between different types of edge devices. Specifically, we model their interactions as a non-cooperative game, where each device (player) can choose to be an agent (who caches and shares contents with others) or a requester (who doesn't cache but requests contents from others) of each content. We analyze the existence and uniqueness of the game equilibrium systematically under the generic usage-based pricing (for PCS). To address the incomplete information issue, we further design a behavior rule that allows players to achieve the equilibrium via a dynamic learning algorithm. Simulations show that the joint MEC-PCS framework can reduce the total system cost by 60%, compared with the benchmark pure caching system without PCS.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229068"}, {"primary_key": "1202809", "vector": [], "sparse_vector": [], "title": "Heterogeneity-Aware Federated Learning with Adaptive Client Selection and Gradient Compression.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Federated learning (FL) allows multiple clients cooperatively train models without disclosing local data. However, the existing works fail to address all these practical concerns in FL: limited communication resources, dynamic network conditions and heterogeneous client properties, which slow down the convergence of FL. To tackle the above challenges, we propose a heterogeneity-aware FL framework, called FedCG, with adaptive client selection and gradient compression. Specifically, the parameter server (PS) selects a representative client subset considering statistical heterogeneity and sends the global model to them. After local training, these selected clients upload compressed model updates matching their capabilities to the PS for aggregation, which significantly alleviates the communication load and mitigates the straggler effect. We theoretically analyze the impact of both client selection and gradient compression on convergence performance. Guided by the derived convergence rate, we develop an iteration-based algorithm to jointly optimize client selection and compression ratio decision using submodular maximization and linear programming. Extensive experiments on both real-world prototypes and simulations show that FedCG can provide up to 5.3× speedup compared to other methods.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229029"}, {"primary_key": "1202810", "vector": [], "sparse_vector": [], "title": "Minimizing Age of Information in Spatially Distributed Random Access Wireless Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We analyze Age of Information (AoI) in wireless networks where nodes use a spatially adaptive random access scheme to send status updates to a central base station. We show that the set of achievable AoI in this setting is convex, and design policies to minimize weighted sum, min-max, and proportionally fair AoI by setting transmission probabilities as a function of node locations. We show that under the capture model, when the spatial topology of the network is considered, AoI can be significantly improved, and we obtain tight performance bounds on weighted sum and min-max AoI. Finally, we design a policy where each node sets its transmission probability based only on its own distance from the base station, when it does not know the positions of other nodes, and show that it converges to the optimal proportionally fair policy as the size of the network goes to infinity.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229041"}, {"primary_key": "1202815", "vector": [], "sparse_vector": [], "title": "Opportunistic Collaborative Estimation for Vehicular Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "As the automotive industry shifts towards enabling self-driving vehicles, real-time situational awareness is becoming a crucial requirement. This paper introduces a novel information-sharing mechanism to opportunistically improve the vehicles' local environment estimates via infrastructure-assisted collaborative sensing, while still allowing them to operate autonomously when no assistance is available. As vehicles might have different sensing capabilities, combining and sharing information from a judiciously selected subset is often sufficient to considerably improve all the vehicles' estimation errors. We develop an opportunistic framework for vehicular collaborative sensing determining (1) which nodes require assistance, (2) which ones are best suited to provide it, and (3) the corresponding information-sharing rates, so as to minimize the communication overheads while meeting the vehicles' target estimation error. We leverage the supermodularity of the problem to devise an efficient vehicle information sharing algorithm with suboptimality guarantees to solve this problem and make it suitable to deploy in dynamic environments where network conditions might fluctuate rapidly. We support our analysis with simulations showing evidence that vehicles can considerably benefit from the proposed opportunistic collaborative sensing framework compared to operating autonomously. Finally, we explore the value of information-sharing in vehicular collaborative sensing networks by evaluating the associated safe driving velocity gains.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228934"}, {"primary_key": "1202817", "vector": [], "sparse_vector": [], "title": "Secure Middlebox Channel over TLS and its Resiliency against Middlebox Compromise.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A large portion of Internet traffic passes through middleboxes that read or modify messages. However, as more traffic is protected with TLS, middleboxes are becoming unable to provide their functions. To leverage middlebox functionality while preserving communication security, secure middlebox channel protocols have been designed as extensions of TLS. A key idea is that the endpoints explicitly incorporate middleboxes into the TLS handshake and grant each middlebox either the read or the write permission for their messages. Because each middlebox has the least data access privilege, these protocols are resilient against the compromise of a single middlebox. However, the existing studies have not comprehensively analyzed the communication security under the scenarios where multiple middleboxes are compromised. In this paper, we present novel attacks that break the security of the existing protocols under such scenarios and then modify maTLS, the state-of-the-art protocol, so that all the attacks are prevented with marginal overhead.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229081"}, {"primary_key": "1202818", "vector": [], "sparse_vector": [], "title": "An Approximation for Job Scheduling on Cloud with Synchronization and Slowdown Constraints.", "authors": ["<PERSON>jun <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Yangguang Shi", "<PERSON><PERSON> Gao"], "summary": "Cloud computing develops rapidly in recent years and provides service to many applications, in which job scheduling becomes more and more important to improve the quality of service. Parallel processing on cloud requires different machines starting simultaneously on the same job and brings processing slowdown due to communications overhead, defined as synchronization constraint and parallel slowdown. This paper investigates a new job scheduling problem of makespan minimization on uniform machines and identical machines with synchronization constraint and parallel slowdown. We first conduct complexity analysis proving that the problem is difficult in the face of adversarial job allocation. Then we propose a novel job scheduling algorithm, United Wrapping Scheduling (UWS), and prove that UWS admits an O(logm)-approximation for makespan minimization over m uniform machines. For the special case of identical machines, UWS is simplified to Sequential Allocation, Refilling and Immigration algorithm (SARI), proved to have a constant approximation ratio of 8 (tight up to a factor of 4). Performance evaluation implies that UWS and SARI have better makespan and realistic approximation ratio of 2 compared to baseline methods United-LPT and FIFO, and lower bounds.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.********"}, {"primary_key": "1202822", "vector": [], "sparse_vector": [], "title": "Achieving Resilient and Performance-Guaranteed Routing in Space-Terrestrial Integrated Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Satellite routers in emerging space-terrestrial integrated networks (STINs) are operated in a failure-prone, intermittent and resource-constrained space environment, making it very critical but challenging to cope with various network failures effectively. Existing resilient routing approaches either suffer from continuous re-convergences with low network reachability, or involve prohibitive pre-computation and storage overhead due to the huge amount of possible failure scenarios in STINs.This paper presents StarCure, a novel resilient routing mechanism for futuristic STINs. StarCure aims at achieving fast and efficient routing restoration, while maintaining the low-latency, high-bandwidth service capabilities in failure-prone space environments. First, StarCure incorporates a new network model, called the topology-stabilizing model (TSM) to eliminate topological uncertainty by converting the topology variations caused by various failures to traffic variations. Second, StarCure adopts an adaptive hybrid routing scheme, collaboratively combining a constraint optimizer to efficiently handle predictable failures, together with a location-guided protection routing strategy to quickly deal with unexpected failures. Extensive evaluations driven by realistic constellation information show that, StarCure can protect routing against various failures, achieving close-to-100% reachability and better performance restoration with acceptable system overhead, as compared to other existing resilience solutions.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.********"}, {"primary_key": "1202823", "vector": [], "sparse_vector": [], "title": "Target Coverage and Connectivity in Directional Wireless Sensor Networks.", "authors": ["<PERSON>", "<PERSON>ng <PERSON><PERSON>"], "summary": "This paper discusses the problem of deploying a minimum number of directional sensors equipped with directional sensing units and directional communication antennas with beam-width ${\\theta _c} \\geq \\frac{\\pi }{2}$ such that the set of sensors covers a set of targets P in the 2D plane and the set of sensors forms a symmetric connected communication graph. As this problem is NP-hard, we propose an approximation algorithm that uses up to 3.5 times the number of omni-directional sensors required by the currently best approximation algorithm proposed by <PERSON> et al. [1]. This is a significant result since we have broken the barrier of $2\\pi /\\frac{\\pi }{2} = 4$ when switching from omni-directional sensors to directional ones. Moreover, we improve the approximation ratio of the Strip-based algorithm for the Geometric Sector Cover problem proposed by <PERSON> et al. [1] from 9 to 7, and we believe that this result is of interest in the area of Computation Geometry. Simulation results show that our algorithms only require around 3 times the number of sensors used by <PERSON> et al.'s algorithm and significantly outperform other heuristics in practice.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229093"}, {"primary_key": "1202825", "vector": [], "sparse_vector": [], "title": "i-NVMe: Isolated NVMe over TCP for a Containerized Environment.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Non-Volatile Memory Express (NVMe) over TCP is an efficient technology for accessing remote Solid State Drives (SSDs); however, it may cause a serious interference issue when used in a containerized environment. In this study, we propose a CPU isolation scheme for NVMe over TCP in such an environment. The proposed scheme measures the CPU usage of the NVMe over TCP worker, charges it to containers in proportion to their NVMe traffic, and schedules containers to ensure isolated sharing of the CPU. However, because the worker runs with a higher priority than normal containers, it may not be possible to achieve CPU isolation with container scheduling alone. To solve this problem, we also control the CPU usage of the worker by throttling NVMe over TCP traffic. The proposed scheme is implemented on a real testbed for evaluation. We perform extensive experiments with various workloads and demonstrate that the scheme can provide CPU isolation even in the presence of excessive NVMe traffic.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228889"}, {"primary_key": "1202826", "vector": [], "sparse_vector": [], "title": "Enable Batteryless Flex-sensors via RFID Tags.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Flex-angle detection of objects or human bodies has potential applications in robotic arm control, medical rehabilitation, and deformation detection. However, current solutions such as flex sensors and computer vision methods have limitations, such as the limited system lifetime of battery-powered flex sensors and the failure of computer vision methods in Non-Line-of-Sight (NLoS) scenarios. To overcome these limitations, we propose an RFID-based flex-sensor system, called RFlexor, which enables batteryless flex-angle detection in NLoS scenarios. RFlexor utilizes the change in tag phase and Received Signal Strength Indicator (RSSI) caused by the flexing of the tag to detect flex-angles. To extract the complex relationship between tag phase/RSSI and flex-angle, we train a multi-input AI model. We address the significant technical challenges by reformulating the phase and RSSI models, using phase difference and RSSI ratio as inputs, and applying multi-head attention to fuse phase and RSSI data. We implement the RFlexor system using Commercial-Off-The-Shelf (COTS) RFID devices and conduct extensive experiments. The results show that RFlexor achieves fine-grained flex-angle detection with a detection error of fewer than 10 degrees and a probability higher than 90% in most conditions. The average detection error is always less than 10 degrees across all experiments. Overall, RFlexor provides a promising solution for flex-angle detection in various scenarios.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228987"}, {"primary_key": "1202827", "vector": [], "sparse_vector": [], "title": "AnycostFL: Efficient On-Demand Federated Learning over Heterogeneous Edge Devices.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Mia<PERSON> Pan"], "summary": "In this work, we investigate the challenging problem of on-demand federated learning (FL) over heterogeneous edge devices with diverse resource constraints. We propose a cost-adjustable FL framework, named AnycostFL, that enables diverse edge devices to efficiently perform local updates under a wide range of efficiency constraints. To this end, we design the model shrinking to support local model training with elastic computation cost, and the gradient compression to allow parameter transmission with dynamic communication overhead. An enhanced parameter aggregation is conducted in an element-wise manner to improve the model performance. Focusing on AnycostFL, we further propose an optimization design to minimize the global training loss with personalized latency and energy constraints. By revealing the theoretical insights of the convergence analysis, personalized training strategies are deduced for different devices to match their locally available resources. Experiment results indicate that, when compared to the state-of-the-art efficient FL algorithms, our learning framework can reduce up to 1.9 times of the training latency and energy consumption for realizing a reasonable global testing accuracy. Moreover, the results also demonstrate that, our approach significantly improves the converged global accuracy.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229017"}, {"primary_key": "1202832", "vector": [], "sparse_vector": [], "title": "Dynamic Resource Allocation for Deep Learning Clusters with Separated Compute and Storage.", "authors": ["<PERSON><PERSON>", "Zhenhua Han", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Haisheng Tan"], "summary": "The separation of compute and storage in modern cloud services eases the deployment of general applications. However, with the development of accelerators such as GPU/TPU, Deep Learning (DL) training is suffering from potential IO bottlenecks when loading data from storage clusters. Therefore, DL training jobs need to either create local cache in the compute cluster to reduce the bandwidth demands or scale up the IO capacity with higher bandwidth cost. It is full of challenges to choose the best strategy due to the heterogeneous cache/IO preference of DL models, shared dataset among multiple jobs and dynamic GPU scaling of DL training. In this work, we exploit the job characteristics based on their training throughput, dataset size and scalability. For fixed GPU allocation of jobs, we propose CBA to minimize the training cost with a closed-form approach. For clusters that can automatically scale the GPU allocations of jobs, we extend CBA to AutoCBA to support diverse job utility functions and improve social welfare within a limited budget. Extensive experiments with production traces validate that CBA and AutoCBA can reduce IO cost and improve total social welfare by up to 20.5% and 2.27×, respectively, over the state-of-the-art schedulers for DL training.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228920"}, {"primary_key": "1202833", "vector": [], "sparse_vector": [], "title": "On Design and Performance of Offline Finding Network.", "authors": ["Tong Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recently, such industrial pioneers as Apple and Samsung have offered a new generation of offline finding network (OFN) that enables crowd search for missing devices without leaking private data. Specifically, OFN leverages nearby online finder devices to conduct neighbor discovery via Bluetooth Low Energy (BLE), so as to detect the presence of offline missing devices and report an encrypted location back to the owner via the Internet. The user experience in OFN is closely related to the success ratio (possibility) of finding the lost device, where the latency of the prerequisite stage, i.e., neighbor discovery, matters. However, the crowd-sourced finder devices show diversity in scan modes due to different power modes or different manufacturers, resulting in local optima of neighbor discovery performance. In this paper, we present a brand-new broadcast mode called ElastiCast to deal with the scan mode diversity issues. ElastiCast captures the key features of BLE neighbor discovery and globally optimizes the broadcast mode interacting with diverse scan modes. Experimental evaluation results and commercial product deployment experience demonstrate that ElastiCast is effective in achieving stable and bounded neighbor discovery latency within the power budget.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228880"}, {"primary_key": "1202835", "vector": [], "sparse_vector": [], "title": "RT-BLE: Real-time Multi-Connection Scheduling for Bluetooth Low Energy.", "authors": ["<PERSON><PERSON> Li", "Jiamei Lv", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Bluetooth Low Energy (BLE) is one of the popular wireless protocols to build IoT applications. However, the BLE suffers from three major issues that make it unable to provide reliable service to time-critical IoT applications. First, the BLE operates in the crowded 2.4GHz frequency band, which can lead to a high packet loss rate. Second, it is common for one device to connect with multiple BLE Peripherals, which can lead to severe collision issue. Third, there is a long delay to re-allocate time resource. In this paper, we propose RT-BLE: a real-time multi-connection scheduling scheme for BLE. We first formulate the BLE transmission latency in noisy RF environments considering the BLE retransmission mechanism. With this, RT-BLE can get a set of initial connection parameters. Then, RT-BLE uses collision tree based time resource scheduling technology to efficiently manage time resource. Finally, we propose a subrating-based fast connection re-scheduling method to update the connection parameters and the position of anchor points. The result shows RT-BLE can provide reliable service and the error of our model is less than 0.69%. Compare with existing works, the re-scheduling delay is reduced by up to 86.25% and the capacity is up to 4.33× higher.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229006"}, {"primary_key": "1202836", "vector": [], "sparse_vector": [], "title": "Eywa: A General Approach for Scheduler Design in AoI Optimization.", "authors": ["Chengzhang Li", "<PERSON><PERSON><PERSON>", "Qing<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Age of Information (AoI) is a metric that can be used to measure the freshness of information. Since its inception, there have been active research efforts on designing scheduling algorithms to various AoI-related optimization problems. For each problem, typically a custom-designed scheduler was developed. Instead of following the (custom-design) path, we pursue a general framework that can be applied to design a wide range of schedulers to solve AoI-related optimization problems. As a first step toward this vision, we present a general framework—Eywa, that can be applied to construct high-performance schedulers for a family of AoI-related optimization problems, all sharing a common setting of an IoT data collection network. We show how to apply Eywa to solve two important AoI-related problems: to minimize the weighted sum of AoIs and to minimize the bandwidth requirement under AoI constraints. We show that for each problem, Eywa can either offer a stronger performance guarantee than the state-of-the-art algorithms or provide new results that are not available in the literature.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228973"}, {"primary_key": "1202838", "vector": [], "sparse_vector": [], "title": "Cross-Camera Inference on the Constrained Edge.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The proliferation of edge devices has pushed computing from the cloud to the data sources, and video analytics is among the most promising applications of edge computing. Running video analytics is compute- and latency-sensitive, as video frames are analyzed by complex deep neural networks (DNNs) which put severe pressure on resource-constrained edge devices. To resolve the tension between inference latency and resource cost, we present <PERSON>, a cross-camera inference system that enables co-located cameras with different but overlapping fields of views (FoVs) to share inference results between one another, thus eliminating the redundant inference work for objects in the same physical area. <PERSON>'s design solves two basic challenges of cross-camera inference: how to identify overlapping FoVs automatically, and how to share inference results accurately across cameras. Evaluation on NVIDIA Jetson Nano with a real-world traffic surveillance dataset shows that <PERSON> reduces the inference latency by up to 71.4% while achieving almost the same detection accuracy with state-of-the-art systems.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229045"}, {"primary_key": "1202839", "vector": [], "sparse_vector": [], "title": "LightNestle: Quick and Accurate Neural Sequential Tensor Completion via Meta Learning.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Network operation and maintenance rely heavily on network traffic monitoring. Due to the measurement overhead reduction, lack of measurement infrastructure, and unexpected transmission error, network traffic monitoring systems suffer from incomplete observed data and high data sparsity problems. Recent studies model missing data recovery as a tensor completion task and show good performance. Although promising, the current tensor completion models adopted in network traffic data recovery lack an effective and efficient retraining scheme to adapt to newly arrived data while retaining historical information. To solve the problem, we propose LightNestle, a novel sequential tensor completion scheme based on meta-learning, which designs (1) an expressive neural network to transfer spatial knowledge from previous embeddings to current embeddings; (2) an attention-based module to transfer temporal patterns into current embeddings in linear complexity; and (3) meta-learning-based algorithms to iteratively recover missing data and update transfer modules to catch up with learned knowledge. We conduct extensive experiments on two real-world network traffic datasets to assess our performance. Results show that our proposed methods achieve both fast retraining and high recovery accuracy.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228967"}, {"primary_key": "1202840", "vector": [], "sparse_vector": [], "title": "CoChain: High Concurrency Blockchain Sharding via Consensus on Consensus.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Sharding is an effective technique to improve the scalability of blockchain. It splits nodes into multiple groups so that they can process transactions in parallel. To achieve higher parallelism and concurrency at large scales, it is desirable to maintain a large number of small shards. However, simply configuring small shards easily results in a higher fraction of malicious nodes inside shards, causing shard corruption and compromising system security. Existing sharding techniques hence demand large shards, at the expense of limited concurrency. To address this limitation, we propose CoChain: a blockchain sharding system that can securely configure small shards for enhanced concurrency. CoChain allows some shards to be corrupted. For security, each shard is monitored by multiple other shards. The latter reach a cross-shard Consensus on the Consensus results of their monitored shard. Once a corrupted shard is found, its subsequent consensus will be taken over by another shard, hence recovering the system. Via Consensus on Consensus, Co<PERSON>hain allows the existence of shards with more fraction of malicious nodes (<2/3) while securing the system, thus reducing the shard size safely. We implement <PERSON>Chain based on <PERSON> and conduct extensive experiments. Compared with Harmony, CoChain achieves 35x throughput gain with 6,000+ nodes.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228892"}, {"primary_key": "1202841", "vector": [], "sparse_vector": [], "title": "MagFingerprint: A Magnetic Based Device Fingerprinting in Wireless Charging.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Wireless charging is a promising solution for charging battery-driven devices pervasively. However, the wide deployment of wireless charging stations is vulnerable to the device masquerade attack, which causes financial loss when billing or charging system damages like overheating and explosion. Device fingerprinting is a classical technique to thwart the device masquerade attack. But existing works either are vulnerable to forging or require specialized equipment, which is not suitable for wireless charging.In this paper, we design a magnetic based fingerprinting system MagFingerprint, which utilizes the alternating magnetic signals as the fingerprint and is compatible with existing wireless charging systems. MagFingerprint is convenient for the user since it only employs commercial-off-the-shelf (COTS) magnetic sensors and requires no action from users. In particular, for the charging device, based on its intrinsic manufacturing errors, MagFingerprint generates a unique fingerprint according to the distinct magnetic changes during the wireless charging process. It is shown that MagFingerprint can achieve an accuracy of 98.90% on wireless charging exposed coils, while it is also effective on different commercial wireless charging pads of Apple, Huawei, and Xiaomi.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229033"}, {"primary_key": "1202842", "vector": [], "sparse_vector": [], "title": "FedSDG-FS: Efficient and Secure Feature Selection for Vertical Federated Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Vertical Federated Learning (VFL) enables multiple data owners, each holding a different subset of features about largely overlapping sets of data sample(s), to jointly train a useful global model. Feature selection (FS) is important to VFL. It is still an open research problem as existing FS works designed for VFL either assumes prior knowledge on the number of noisy features or prior knowledge on the post-training threshold of useful features to be selected, making them unsuitable for practical applications. To bridge this gap, we propose the Federated Stochastic Dual-Gate based Feature Selection (FedSDG-FS) approach. It consists of a Gaussian stochastic dual-gate to efficiently approximate the probability of a feature being selected, with privacy protection through Partially Homomorphic Encryption without a trusted third-party. To reduce overhead, we propose a feature importance initialization method based on Gini impurity, which can accomplish its goals with only two parameter transmissions between the server and the clients. Extensive experiments on both synthetic and real-world datasets show that FedSDG-FS significantly outperforms existing approaches in terms of achieving accurate selection of high-quality features as well as building global models with improved performance.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228895"}, {"primary_key": "1202843", "vector": [], "sparse_vector": [], "title": "FeatureSpy: Detecting Learning-Content Attacks via Feature Inspection in Secure Deduplicated Storage.", "authors": ["<PERSON>wei Li", "Yanjing Ren", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Secure deduplicated storage is a critical paradigm for cloud storage outsourcing to achieve both operational cost savings (via deduplication) and outsourced data confidentiality (via encryption). However, existing secure deduplicated storage designs are vulnerable to learning-content attacks, in which malicious clients can infer the sensitive contents of outsourced data by monitoring the deduplication pattern. We show via a simple case study that learning-content attacks are indeed feasible and can infer sensitive information in short time under a real cloud setting. To this end, we present FeatureSpy, a secure deduplicated storage system that effectively detects learning-content attacks based on the observation that such attacks often generate a large volume of similar data. FeatureSpy builds on two core design elements, namely (i) similarity-preserving encryption that supports similarity detection on encrypted chunks and (ii) shielded attack detection that leverages Intel SGX to accurately detect learning-content attacks without being readily evaded by adversaries. Trace-driven experiments on real-world and synthetic datasets show that our FeatureSpy prototype achieves high accuracy and low performance overhead in attack detection.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228971"}, {"primary_key": "1202844", "vector": [], "sparse_vector": [], "title": "Digital Twin-Enabled Service Satisfaction Enhancement in Edge Computing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The emerging digital twin technique enhances the network management efficiency and provides comprehensive insights, through mapping physical objects to their digital twins. The user satisfaction on digital twin-enabled query services relies on the freshness of digital twin data, which is measured by the Age of Information (AoI). Because the remote cloud faces challenges in providing data for users due to long service delays, Mobile Edge Computing (MEC), as a promising technology, offers real-time data communication between physical objects and their digital twins at the edge of the core network. However, the mobility of physical objects and dynamic query arrivals make efficient service provisioning in MEC become challenging. In this paper, we investigate the dynamic digital twin placement for improving user service satisfaction in MEC environments. We focus on two user service satisfaction augmentation problems under both static and dynamic digital twin placement schemes: the static and dynamic utility maximization problems. We first formulate an Integer Linear Programming (ILP) solution to the static utility maximization problem when the problem size is small; otherwise, we propose a performance- guaranteed approximation algorithm for it. We then devise an online algorithm for the dynamic utility maximization problem with a provable competitive ratio. Finally, we evaluate the performance of the proposed algorithms through experimental simulations. Simulation results demonstrate that the proposed algorithms outperform the comparison baseline algorithms, and the performance improvement is no less than 11.6%, compared with the baseline algorithms.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228893"}, {"primary_key": "1202848", "vector": [], "sparse_vector": [], "title": "Robustified Learning for Online Optimization with Memory Costs.", "authors": ["Peng<PERSON>i Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Online optimization with memory costs has many real-world applications, where sequential actions are made without knowing the future input. Nonetheless, the memory cost couples the actions over time, adding substantial challenges. Conventionally, this problem has been approached by various expert-designed online algorithms with the goal of achieving bounded worst-case competitive ratios, but the resulting average performance is often unsatisfactory. On the other hand, emerging machine learning (ML) based optimizers can improve the average performance, but suffer from the lack of worst-case performance robustness. In this paper, we propose a novel expert-robustified learning (ERL) approach, achieving both good average performance and robustness. More concretely, for robustness, ERL introduces a novel projection operator that robustifies ML actions by utilizing an expert online algorithm; for average performance, ERL trains the ML optimizer based on a recurrent architecture by explicitly considering downstream expert robustification. We prove that, for any λ ≥ 1, ERL can achieve λ-competitive against the expert algorithm and λ•C-competitive against the optimal offline algorithm (where C is the expert's competitive ratio). Additionally, we extend our analysis to a novel setting of multi-step memory costs. Finally, our analysis is supported by empirical experiments for an energy scheduling application.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228998"}, {"primary_key": "1202850", "vector": [], "sparse_vector": [], "title": "On Efficient Zygote Container Planning toward Fast Function Startup in Serverless Edge Cloud.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The cold startup of the container is regarded as a crucial problem to the performance of serverless computing, especially to the resource-capacitated edge clouds. Pre-warming hot containers has been proved as an efficient solution but is at the expense of high memory consumption. Instead of pre-warming a complete container for a function, recent studies advocate Zygote container, which pre-imports some packages and is able to import the other dependent packages at runtime, so as to avoid the cold startup problem. However, as different functions have different package dependencies, how to plan the Zygote generation and pre-warming in a resource-capacitated edge cloud becomes a critical challenge. In this paper, aiming to minimize the overall function startup time and subjective to the resource capacity constraints, we formulate this problem into a Quadratic Integer Programming (QIP) form. We further propose a Randomized Rounding based Zygote Planning (RRZP) algorithm. The performance efficiency of our algorithm is proved via both theoretical analysis and trace-driven simulations. The results show that our algorithm can significantly reduce the startup time by 25.6%.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228916"}, {"primary_key": "1202853", "vector": [], "sparse_vector": [], "title": "TapFinger: Task Placement and Fine-Grained Resource Allocation for Edge Machine Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Machine learning (ML) tasks are one of the major workloads in today's edge computing networks. Existing edge-cloud schedulers allocate the requested amounts of resources to each task, falling short of best utilizing the limited edge resources flexibly for ML task performance optimization. This paper proposes <PERSON><PERSON><PERSON><PERSON>, a distributed scheduler that minimizes the total completion time of ML tasks in a multi-cluster edge network, through co-optimizing task placement and fine-grained multi-resource allocation. To learn the tasks' uncertain resource sensitivity and enable distributed online scheduling, we adopt multi-agent reinforcement learning (MARL), and propose several techniques to make it efficient for our ML-task resource allocation. First, <PERSON><PERSON><PERSON><PERSON> uses a heterogeneous graph attention network as the MARL backbone to abstract inter-related state features into more learnable environmental patterns. Second, the actor network is augmented through a tailored task selection phase, which decomposes the actions and encodes the optimization constraints. Third, to mitigate decision conflicts among agents, we novelly combine <PERSON><PERSON>' theorem and masking schemes to facilitate our MARL model training. Extensive experiments using synthetic and test-bed ML task traces show that <PERSON><PERSON><PERSON><PERSON> can achieve up to 28.6% reduction in the average task completion time and improve resource efficiency as compared to state-of-the- art resource schedulers.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229031"}, {"primary_key": "1202855", "vector": [], "sparse_vector": [], "title": "Adaptive Configuration for Heterogeneous Participants in Decentralized Federated Learning.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Data generated at the network edge can be processed locally by leveraging the paradigm of edge computing (EC). Aided by EC, decentralized federated learning (DFL), which overcomes the single-point-of-failure problem in the parameter server (PS) based federated learning, is becoming a practical and popular approach for machine learning over distributed data. However, DFL faces two critical challenges, i.e., system heterogeneity and statistical heterogeneity introduced by edge devices. To ensure fast convergence with the existence of slow edge devices, we present an efficient DFL method, termed FedHP, which integrates adaptive control of both local updating frequency and network topology to better support the heterogeneous participants. We establish a theoretical relationship between local updating frequency and network topology regarding model training performance and obtain a convergence upper bound. Upon this, we propose an optimization algorithm, that adaptively determines local updating frequencies and constructs the network topology, so as to speed up convergence and improve the model accuracy. Evaluation results show that the proposed FedHP can reduce the completion time by about 51% and improve model accuracy by at least 5% in heterogeneous scenarios, compared with the baselines.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228945"}, {"primary_key": "1202856", "vector": [], "sparse_vector": [], "title": "Learning to Schedule Tasks with Deadline and Throughput Constraints.", "authors": ["<PERSON><PERSON><PERSON> Liu", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We consider the task scheduling scenario where the controller activates one from K task types at each time. Each task induces a random completion time, and a reward is obtained only after the task is completed. The statistics of the completion time and the reward distributions of all task types are unknown to the controller. The controller needs to learn to schedule tasks to maximize the accumulated reward within a given time horizon T . Motivated by the practical scenarios, we require the designed policy to satisfy a system throughput constraint. In addition, we introduce the interruption mechanism to terminate ongoing tasks that last longer than certain deadlines. To address this scheduling problem, we model it as an online learning problem with deadline and throughput constraints. Then, we characterize the optimal offline policy and develop efficient online learning algorithms based on the <PERSON><PERSON><PERSON><PERSON> method. We prove that our online learning algorithm achieves an $O(\\sqrt T )$ regret and zero constraint violations. We also conduct simulations to evaluate the performance of our developed learning algorithms.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228901"}, {"primary_key": "1202857", "vector": [], "sparse_vector": [], "title": "Communication-Efficient Federated Learning for Heterogeneous Edge Devices Based on Adaptive Gradient Quantization.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Federated learning (FL) enables geographically dispersed edge devices (i.e., clients) to learn a global model without sharing the local datasets, where each client performs gradient descent with its local data and uploads the gradients to a central server to update the global model. However, FL faces massive communication overhead resulted from uploading the gradients in each training round. To address this problem, most existing research compresses the gradients with fixed and unified quantization for all the clients, which neither seeks adaptive quantization due to the varying gradient norms at different rounds, nor exploits the heterogeneity of the clients to accelerate FL. In this paper, we propose a novel adaptive and heterogeneous gradient quantization algorithm (AdaGQ) for FL to minimize the wall-clock training time from two aspects: i) adaptive quantization which exploits the change of gradient norm to adjust the quantization resolution in each training round; and ii) heterogeneous quantization which assigns lower quantization resolution to slow clients to align their training time with other clients to mitigate the communication bottleneck, and higher quantization resolution to fast clients to achieve a better communication efficiency and accuracy tradeoff. Evaluations based on various models and datasets validate the benefits of AdaGQ, reducing the total training time by up to 52.1% compared to baseline algorithms (e.g., FedAvg, QSGD).", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228970"}, {"primary_key": "1202860", "vector": [], "sparse_vector": [], "title": "Libra: Contention-Aware GPU Thread Allocation for Data Parallel Training in High Speed Networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Zhao", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Overlapping gradient communication with backward computation is a popular technique to reduce communication cost in the widely adopted data parallel S-SGD training. However, the resource contention between computation and All-Reduce communication in GPU-based training reduces the benefits of overlap. With GPU cluster network evolving from low bandwidth TCP to high speed networks, more GPU resources are required to efficiently utilize the bandwidth, making the contention more noticeable. Existing communication libraries fail to account for such contention when allocating GPU threads and have suboptimal performance. In this paper, we propose to mitigate the contention by balancing the overlapped computation and communication time. We formulate an optimization problem that decides the communication thread allocation to reduce overall backward time. We develop a dynamic programming based near-optimal solution and extend it to co-optimize thread allocation with tensor fusion. We conduct simulated study and real-world experiment using an 8-node GPU cluster with 50Gb RDMA network training four representative DNN models. Results show that our method reduces backward time by 10%-20% compared with Horovod-NCCL, by 6%-13% compared with tensor-fusion-optimization-only methods. Simulation shows that our method achieves the best scalability with a training speedup of 1.2x over the best-performing baseline as we scale up cluster size.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228922"}, {"primary_key": "1202863", "vector": [], "sparse_vector": [], "title": "Joint Task Offloading and Resource Allocation in Heterogeneous Edge Environments.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Mobile edge computing is becoming one of the ubiquitous computing paradigms to support applications requiring low latency and high computing capability. FPGA-based reconfigurable accelerators have high energy efficiency and low latency compared to general-purpose servers. Therefore, it is natural to incorporate reconfigurable accelerators in mobile edge computing systems. This paper formulates and studies the problem of joint task offloading, access point selection, and resource allocation in heterogeneous edge environments for latency minimization. Due to the heterogeneity in edge computing devices and the coupling between offloading, access point selection, and resource allocation decisions, it is challenging to optimize over them simultaneously. We decomposed the proposed problem into two disjoint subproblems and developed algorithms for them. The first subproblem is to jointly determine offloading and computing resource allocation decisions and is NP-hard, where we developed an algorithm based on semidefinite relaxation. The second subproblem is to jointly determine access point selection and communication resource allocation decisions, where we proposed an algorithm with a provable approximation ratio of 2.62. We conducted extensive numerical simulations to evaluate the proposed algorithms. Results highlighted that the proposed algorithms outperformed baselines and were near-optimal over a wide range of settings.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229015"}, {"primary_key": "1202864", "vector": [], "sparse_vector": [], "title": "A Close Look at 5G in the Wild: Unrealized Potentials and Implications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper reports our in-depth measurement study of 5G experience with three US operators (AT&T, Verizon and T-Mobile). We not only quantitively characterize 5G coverage, availability and performance (over both mmWave and Sub-6GHz bands), but also identify several performance issues and analyze their root causes. We see that real 5G experience is not that satisfactory as anticipated. It is mainly because faster 5G is not used as it can and should. We have several surprising findings: Despite huge speed potentials (say, up to several hundreds of Mbps), more than half are not realized in practice; Such under-utilization is mainly stemmed from current practice and policies that manage radio resource in a performance-oblivious manner; 5G is even less used where 5G is co-deployed over both mmWave and Sub-6GHz bands; Transiently missing 5G is not uncommon and its negative impacts last much longer. Inspired by our findings, we design a patch solution called 5GBoost to fix the problems identified in legacy 5G operations. Our preliminary evaluation validates its effectiveness to realize more 5G potentials.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.********"}, {"primary_key": "1202865", "vector": [], "sparse_vector": [], "title": "Nimble: Fast and Safe Migration of Network Functions.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Theophilus <PERSON>"], "summary": "Network function (NF) migration alongside (and possibly because of) routing policy updates is a delicate task, making it difficult to ensure that all traffic is processed by its required network functions, in order. Indeed, all previous solutions to this problem adapt routing policy only after NFs have been migrated, in a serial fashion. This paper proposes a design called Nimble for interleaving these tasks to complete both more efficiently while ensuring complete processing of traffic by the required NFs, provided that the route-update protocol enforces a specific property that we define. We demonstrate the benefits of the Nimble design using an implementation in Open vSwitch and the Ryu controller, building on both known routing update protocols and a new protocol of our design that implements specifically the needed property.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.********"}, {"primary_key": "1202868", "vector": [], "sparse_vector": [], "title": "Variance-Adaptive Algorithm for Probabilistic Maximum Coverage Bandits with General <PERSON>.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Probabilistic maximum coverage (PMC) is an important problem that can model many network applications, including mobile crowdsensing, network content delivery, and dynamic channel allocation, where an operator chooses nodes in a graph that can probabilistically cover other nodes. In this paper, we study PMC under the online learning context: the PMC bandit. For PMC bandit where network parameters are not known a priori, the decision maker needs to learn the unknown parameters and the goal is to maximize the total rewards from the covered nodes. Though PMC bandit has been studied previously, the existing model and its corresponding algorithm can be significantly improved. First, we propose the PMC-G bandit whose feedback model generalizes existing semi-bandit feedback, allowing PMC bandit to model applications like online content delivery and online dynamic channel allocation. Next, we improve the existing combinatorial upper confidence bound (CUCB) algorithm by introducing the variance-adaptive algorithm, i.e., the VA-CUCB algorithm. We prove that VA-CUCB can achieve strictly better regret bounds, which improves CUCB by a factor of $\\tilde O(K)$, where K is the number of nodes selected in each round. Finally, experiments show our superior performance compared with benchmark algorithms on synthetic and real-world datasets.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228940"}, {"primary_key": "1202870", "vector": [], "sparse_vector": [], "title": "Hawkeye: A Dynamic and Stateless Multicast Mechanism with Deep Reinforcement Learning.", "authors": ["<PERSON>", "Qing Li", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Multicast traffic is growing rapidly due to the development of multimedia streaming. Lately, stateless multicast protocols, such as BIER, have been proposed to solve the excessive routing states problem of traditional multicast protocols. However, the high complexity of multicast tree computation and the limited scalability for concurrent requests still pose daunting challenges, especially under dynamic group membership. In this paper, we propose Hawkeye, a dynamic and stateless multicast mechanism with deep reinforcement learning (DRL) approach. For real-time responses to multicast requests, we leverage DRL enhanced by a temporal convolutional network (TCN) to model the sequential feature of dynamic group membership and thus is able to build multicast trees proactively for upcoming requests. Moreover, an innovative source aggregation mechanism is designed to help the DRL agent converge when faced with a large amount of multicast requests, and relieve ingress routers from excessive routing states. Evaluation with real-world topologies and multicast requests demonstrates that Hawkeye adapts well to dynamic multicast: it reduces the variation of path latency by up to 89.5% with less than 12% additional bandwidth consumption compared with the theoretical optimum.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.********"}, {"primary_key": "1202871", "vector": [], "sparse_vector": [], "title": "Effectively Learning Moiré QR Code Decryption from Simulated Data.", "authors": ["<PERSON>", "<PERSON><PERSON> Pan", "Feitong Tan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> He", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Moiré QR Code is a secure encrypted QR code system that can protect the user’s QR code displayed on the screen from being accessed by attackers. However, conventional decryption methods based on image processing techniques suffer from intensive computation and significant decryption latency in practical mobile applications. In this work, we propose a deep learning-based Moiré QR code decryption framework and achieve an excellent decryption performance. Considering the sensitivity of the Moiré phenomenon, collecting training data in the real world is extremely labor and material intensive. To overcome this issue, we develop a physical screen-imaging Moiré simulation methodology to generate a synthetic dataset that covers the entire Moiré-visible area. Extensive experiments show that the proposed decryption network can achieve a low decryption latency (0.02 seconds) and a high decryption rate (98.8%), compared with the previous decryption method with decryption latency (5.4 seconds) and decryption rate (98.6%).", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229000"}, {"primary_key": "1202872", "vector": [], "sparse_vector": [], "title": "DAGC: Data-Aware Adaptive Gradient Compression.", "authors": ["Rongwei Lu", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Gradient compression algorithms are widely used to alleviate the communication bottleneck in distributed ML. However, existing gradient compression algorithms suffer from accuracy degradation in Non-IID scenarios, because a uniform compression scheme is used to compress gradients at workers with different data distributions and volumes, since workers with larger volumes of data are forced to adapt to the same aggressive compression ratios as others. Assigning different compression ratios to workers with different data distributions and volumes is thus a promising solution. In this study, we first derive a function from capturing the correlation between the number of training iterations for a model to converge to the same accuracy, and the compression ratios at different workers; This function particularly shows that workers with larger data volumes should be assigned with higher compression ratios 1 to guarantee better accuracy. Then, we formulate the assignment of compression ratios to the workers as an n-variables chi-square nonlinear optimization problem under fixed and limited total communication constrain. We propose an adaptive gradient compression strategy called DAGC, which assigns each worker a different compression ratio according to their data volumes. Our experiments confirm that DAGC can achieve better performance facing highly imbalanced data volume distribution and restricted communication.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229053"}, {"primary_key": "1202875", "vector": [], "sparse_vector": [], "title": "TanGo: A Cost Optimization Framework for Tenant Task Placement in Geo-distributed Clouds.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Zhu<PERSON>ng Yu", "<PERSON><PERSON><PERSON>"], "summary": "Cloud infrastructure has gradually displayed a tendency of geographical distribution in order to provide anywhere, anytime connectivity to tenants all over the world. The tenant task placement in geo-distributed clouds comes with three critical and coupled factors: regional diversity in electricity prices, access delay for tenants, and traffic demand among tasks. However, existing works disregard either the regional difference in electricity prices or the tenant requirements in geo-distributed clouds, resulting in increased operating costs or low user QoS. To bridge the gap, we design a cost optimization framework for tenant task placement in geo-distributed clouds, called TanGo. However, it is non-trivial to achieve an optimization framework while meeting all the tenant requirements. To this end, we first formulate the electricity cost minimization for task placement problem as a constrained mixed-integer non-linear programming problem. We then propose a near-optimal algorithm with a tight approximation ratio (1 − 1/e) using an effective submodular-based method. Results of in-depth simulations based on real-world datasets show the effectiveness of our algorithm as well as the overall 10%-30% reduction in electricity expenses compared to commonly-adopted alternatives.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229004"}, {"primary_key": "1202876", "vector": [], "sparse_vector": [], "title": "Falcon: Towards Fast and Scalable Data Delivery for Emerging Earth Observation Constellations.", "authors": ["Mingyang Lyu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Exploiting a constellation of small satellites to realize continuous earth observations (EO) is gaining popularity. Large-volume EO data acquired from space needs to be transferred to the ground. However, existing EO delivery approaches are either: (a) efficiency-limited, suffering from long delivery completion time due to the intermittent ground-space communication, or (b) scalability-limited since they fail to support concurrent delivery for multiple satellites in an EO constellation.To make big data delivery for emerging EO constellations fast and scalable, we propose Falcon, a multi-path EO delivery framework that wisely exploits diverse paths in broadband constellations to collaboratively deliver EO data effectively. In particular, we formulate the constellation-wide EO data multi-path download (CEOMD) problem, which aims at minimizing the delivery completion time of requested data for all EO sources. We prove the hardness of solving CEOMD, and further present a heuristic multipath routing and bandwidth allocation mechanism to tackle the technical challenges caused by time-varying satellite dynamics and flow contention, and solve the CEO<PERSON> problem efficiently. Evaluation results based on public orbital data of real EO constellations show that as compared to other state-of-the-art approaches, Falcon can reduce at least 51% delivery completion time for various data requests in large EO constellations.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.********"}, {"primary_key": "1202877", "vector": [], "sparse_vector": [], "title": "Network Characteristics of LEO Satellite Constellations: A Starlink-Based Measurement from End Users.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Low Earth orbit Satellite Networks (LSNs) have been advocated as a key infrastructure for truly global coverage in the forthcoming 6G. This paper presents our initial measurement results and observations on the end-to-end network characteristics of Starlink, arguably the largest LSN constellation to date. Our findings confirm that LSNs are a promising solution towards ubiquitous Internet coverage over the Earth; yet, we also find that the users of Starlink experience much more dynamics in throughput and latency than terrestrial network users, and even frequent outages. Its user experiences are heavily affected by environmental factors such as terrain, solar storms, rain, clouds, and temperature, so is the power consumption. We further analyze Starlink’s current bent-pipe relay strategy and its limits, particularly for cross-ocean routes. We have also explored its mobility and portability potentials, and extended our experiments from urban cities to wild remote areas that are facing distinct practical and cultural challenges.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228912"}, {"primary_key": "1202878", "vector": [], "sparse_vector": [], "title": "Concurrent Charging with Wave Interference.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "To improve the charging performance, employing multiple wireless chargers to charge sensors concurrently is an effective way. In such charging scenarios, the radio waves radiated from multiple chargers will interfere with each other. Though a few work have realized the wave interference, they do not fully utilize the high power caused by constructive interference while avoiding the negative impacts brought by the destructive interference. In this paper, we aim to investigate the power distribution regularity of concurrent charging and take full advantage of the high power to enhance the charging efficiency. Specifically, we formulate a concurrent charGing utility mAxImizatioN (GAIN) problem and build a practical charging model with wave interference. Further, we propose a concurrent charging scheme, which not only can improve the power of interference enhanced regions by deploying chargers, but also find a set of points with the highest power to locate sensors. Finally, we conduct both simulations and field experiments to evaluate the proposed scheme. The results demonstrate that our scheme outperforms the comparison algorithms by 40.48% on average.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228965"}, {"primary_key": "1202879", "vector": [], "sparse_vector": [], "title": "Scalable Real-Time Bandwidth Fairness in Switches.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Network operators want to enforce fair bandwidth sharing between users without solely relying on congestion control running on end-user devices. However, in edge networks (e.g., 5G), the number of user devices sharing a bottleneck link far exceeds the number of queues supported by today's switch hardware; even accurately tracking per-user sending rates may become too resource-intensive. Meanwhile, traditional software-based queuing on CPUs struggles to meet the high throughput and low latency demanded by 5G users.We propose Approximate Hierarchical Allocation of Bandwidth (AHAB), a per-user bandwidth limit enforcer that runs fully in the data plane of commodity switches. AHAB tracks each user's approximate traffic rate and compares it against a bandwidth limit, which is iteratively updated via a real-time feedback loop to achieve max-min fairness across users. Using a novel sketch data structure, AHAB avoids storing per-user state, and therefore scales to thousands of slices and millions of users. Furthermore, AHAB supports network slicing, where each slice has a guaranteed share of the bandwidth that can be scavenged by other slices when under-utilized. Evaluation shows AHAB can achieve fair bandwidth allocation within 3.1ms, 13x faster than prior data-plane hierarchical schedulers.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228997"}, {"primary_key": "1202881", "vector": [], "sparse_vector": [], "title": "Matching DNN Compression and Cooperative Training with Resources and Data Availability.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "To make machine learning (ML) sustainable and apt to run on the diverse devices where relevant data is, it is essential to compress ML models as needed, while still meeting the required learning quality and time performance. However, how much and when an ML model should be compressed, and where its training should be executed, are hard decisions to make, as they depend on the model itself, the resources of the available nodes, and the data such nodes own. Existing studies focus on each of those aspects individually, however, they do not account for how such decisions can be made jointly and adapted to one another. In this work, we model the network system focusing on the training of DNNs, formalize the above multi-dimensional problem, and, given its NP-hardness, formulate an approximate dynamic programming problem that we solve through the PACT algorithmic framework. Importantly, PACT leverages a time-expanded graph representing the learning process, and a data-driven and theoretical approach for the prediction of the loss evolution to be expected as a consequence of training decisions. We prove that PACT’s solutions can get as close to the optimum as desired, at the cost of an increased time complexity, and that, in any case, such complexity is polynomial. Numerical results also show that, even under the most disadvantageous settings, PACT outperforms state-of-the-art alternatives and closely matches the optimal energy cost.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229076"}, {"primary_key": "1202883", "vector": [], "sparse_vector": [], "title": "Qubit Allocation for Distributed Quantum Computing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "With the advancements in quantum communication, optically connected quantum processors can form a distributed quantum computing system. Distributed quantum computing provides a scalable path to execute more complicated computational tasks that a single quantum processor cannot handle. Yet, distributed quantum computing needs a new compiler to map logical qubits of a quantum circuit to different quantum processors in the system. This paper formulates and studies the qubit allocation problem for distributed quantum computing (QA-DQC). We prove the NP-hardness of the formulated problem. Moreover, we show there is no polynomial-time na-approximation algorithm for any a < 1 unless P = NP, where n is the number of processors in the quantum network. We first propose a heuristic local search algorithm for QA-DQC. Furthermore, we design a multistage hybrid simulated annealing algorithm (MHSA) by combining the local search algorithm and a simulated annealing meta-heuristic algorithm. Lastly, we perform extensive simulations to evaluate the proposed MHSA under various real quantum circuits and different network topologies. Results show that MHSA outperforms popular baselines.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228915"}, {"primary_key": "1202884", "vector": [], "sparse_vector": [], "title": "Ant Colony based Online Learning Algorithm for Service Function Chain Deployment.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Network Function Virtualization (NFV) emerges as a promising paradigm with the potential for cost-efficiency, manage-convenience, and flexibility, where the service function chain (SFC) deployment scheme is a crucial technology. In this paper, we propose an Ant Colony Optimization (ACO) meta-heuristic algorithm for the Online SFC Deployment, called ACO-OSD, with the objectives of jointly minimizing the server operation cost and network latency. As a meta-heuristic algorithm, ACO-OSD performs better than the state-of-art heuristic algorithms, specifically 42.88% lower total cost on average. To reduce the time cost of ACO-OSD, we design two acceleration mechanisms: the Next-Fit (NF) strategy and the many-to-one model between SFC deployment schemes and ant-tours. Besides, for the scenarios requiring real-time decisions, we propose a novel online learning framework based on the ACO-OSD algorithm, called prior-based learning real-time placement (PLRP). It realizes near real-time SFC deployment with the time complexity of O(n), where n is the total number of VNFs of all newly arrived SFCs. It meanwhile maintains a performance advantage with 36.53% lower average total cost than the state-of-art heuristic algorithms. Finally, we perform extensive simulations to demonstrate the outstanding performance of ACO-OSD and PLRP compared with the benchmarks.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229012"}, {"primary_key": "1202885", "vector": [], "sparse_vector": [], "title": "Recovering Packet Collisions below the Noise Floor in Multi-gateway LoRa Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Min"], "summary": "LoRa has been widely applied in various vertical areas such as smart grids, smart cities, etc. Packet collisions caused by concurrent transmissions have become one of the major limitations of LoRa networks due to the ALOHA MAC protocol and dense deployment. The existing studies on packet recovery usually assume that the collided packet signals are above the noise floor. However, considering the large-scale deployment and low-power nature of LoRa communications, many collided packets are below the noise floor. Consequently, the existing schemes will suffer from significant performance degradation in practical LoRa networks. To address this issue, we propose CPR, a Cooperative Packet Recovery mechanism aiming at recovering the collided packets below the noise floor. CPR firstly employs the incoherence of signals and noises at multiple gateways to detect and extract the frequency features of the collided packets hidden in the noise. Then, CPR adopts a novel gateway selection strategy to select the most appropriate gateways based on their packet power domain features extracted from collision detection, such that the interference can be eliminated and the original packets can be recovered. Extensive experimental results demonstrate that CPR can significantly increase the symbol recovery ratio in low-SNR scenarios.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229010"}, {"primary_key": "1202888", "vector": [], "sparse_vector": [], "title": "De-anonymization Attacks on Metaverse.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Virtual reality (VR) can provide users with an immersive experience in the metaverse. One of the most promising properties of VR is that users' identities can be protected by changing their physical world appearances into arbitrary virtual avatars. However, recent proposed de-anonymization attacks demonstrate the feasibility of recognizing the user's identity behind the VR avatar's masking. In this paper, we propose AvatarHunter, a non-intrusive and user-unconscious de-anonymization attack based on victims' inherent movement signatures. AvatarHunter imperceptibly collects the victim avatar's gait information via recording videos from multiple views in the VR scenario without requiring any permission. A Unity-based feature extractor is designed that preserves the avatar's movement signature while immune to the avatar's appearance changes. Real-world experiments are conducted in VRChat, one of the most popular VR applications. The experimental results demonstrate that AvatarHunter can achieve attack success rates of 92.1% and 66.9% in closed-world and open-world avatar settings, respectively, which are much better than existing works.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229062"}, {"primary_key": "1202889", "vector": [], "sparse_vector": [], "title": "Secur-Fi: A Secure Wireless Sensing System Based on Commercial Wi-Fi Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Wi-Fi sensing technology plays an important role in numerous IoT applications such as virtual reality, smart homes and elder healthcare. The basic principle is to extract physical features from the Wi-Fi signals to depict the user’s locations or behaviors. However, current research focuses more on improving the sensing accuracy but neglects the security concerns. Specifically, current Wi-Fi router usually transmits a strong signal, so that we can access the Internet even through the wall. Accordingly, the outdoor adversaries are able to eavesdrop on this strong Wi-Fi signal, and infer the behavior of indoor users in a non-intrusive way, while the indoor users are unaware of this eavesdropping. To prevent outside eavesdropping, we propose Secur-Fi, a secure Wi-Fi sensing system. Our system meets the following two requirements: (1) we can generate fraud signals to block outside unauthorized Wi-Fi sensing; (2) we can recover the signal, and enable authorized Wi-Fi sensing. We implement the proposed system on commercial Wi-Fi devices and conduct experiments in three applications including passive tracking, behavior recognition, and breath detection. The experiment results show that our proposed approaches can reduce the accuracy of unauthorized sensing by 130% (passive tracking), 72% (behavior recognition), 86% (breath detection).", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229055"}, {"primary_key": "1202894", "vector": [], "sparse_vector": [], "title": "Spotting Deep Neural Network Vulnerabilities in Mobile Traffic Forecasting with an Explainable AI Lens.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Giulia <PERSON>sio", "<PERSON>", "<PERSON><PERSON>"], "summary": "The ability to forecast mobile traffic patterns is key to resource management for mobile network operators and planning for local authorities. Several Deep Neural Networks (DNN) have been designed to capture the complex spatio-temporal characteristics of mobile traffic patterns at scale. These models are complex black boxes whose decisions are inherently hard to explain. Even worse, they have proven vulnerable to adversarial attacks which undermine their applicability in production networks. In this paper, we conduct a first in-depth study of the vulnerabilities of DNNs for large-scale mobile traffic forecasting. We propose DeExp, a new tool that leverages EXplainable Artificial Intelligence (XAI) to understand which Base Stations (BSs) are more influential for forecasting from a spatio-temporal perspective. This is challenging as existing XAI techniques are usually applied to computer vision or natural language processing and need to be adapted to the mobile network context. Upon identifying the more influential BSs, we run state-of-the art Adversarial Machine Learning (AML) techniques on those BSs and measure the accuracy degradation of the predictors. Extensive evaluations with real-world mobile traffic traces pinpoint that attacking BSs relevant to the predictor significantly degrades its accuracy across all the scenarios.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228989"}, {"primary_key": "1202897", "vector": [], "sparse_vector": [], "title": "Enabling Direct Message Dissemination in Industrial Wireless Networks via Cross-Technology Communication.", "authors": ["Di <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Junyang Shi", "<PERSON>"], "summary": "IEEE 802.15.4 based industrial wireless networks have been widely deployed to connect sensors, actuators, and gateway in industrial facilities. Although wireless mesh networks work satisfactorily most of the time thanks to years of research, they are often complex and difficult to manage once the networks are deployed. Moreover, the deliveries of time-critical messages suffer long delay, because all messages have to go through hop-by-hop transport. Recent studies show that adding a low-power wide-area network (LPWAN) radio to each device in the network can effectively overcome such limitations, because network management and time-critical messages can be transmitted from gateway to field devices directly through long-distance LPWAN links. However, industry practitioners have shown a marked reluctance to embrace the new solution because of the high cost of hardware modification. This paper presents a novel system, namely DIrect MEssage dissemination (DIME) system, that leverages the cross-technology communication technique to enable the direct message dissemination from gateway to field devices in industrial wireless networks without the need to add a second radio to each field device. Experimental results show that our system effectively reduces the latency of delivering time-critical messages and improves network reliability compared to a state-of-the-art baseline.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228891"}, {"primary_key": "1202898", "vector": [], "sparse_vector": [], "title": "A Fast and Exact Evaluation Algorithm for the Expected Number of Connected Nodes: an Enhanced Network Reliability Measure.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Contemporary society survives on several network infrastructures, such as communication and transportation. These network infrastructures are required to keep all nodes connected, although these nodes are occasionally disconnected due to failures. Thus, the expected number of connected node pairs (ECP) during an operation period is a reasonable reliability measure in network design. However, no work has studied ECP due to its computational hardness; we have to solve the reliability evaluation problem, which is a computationally tough problem, for O(n 2 ) times where n is the number of nodes in a network. This paper proposes an efficient method that exactly computes ECP. Our method performs dynamic programming just once without explicit repetition for each node pair and obtains an exact ECP value weighted by the number of users at each node. A thorough complexity analysis reveals that our method is faster than an existing reliability evaluation method, which can be transferred to ECP computation, by O(n). Numerical experiments using real topologies show great efficiency; e.g., our method computes the ECP of an 821-link network in ten seconds; the existing method cannot complete it in an hour. This paper also presents two applications: critical link identification and optimal resource (e.g., a server) placement.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228897"}, {"primary_key": "1202900", "vector": [], "sparse_vector": [], "title": "Federated PCA on Grassmann Manifold for Anomaly Detection in IoT Networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the era of Internet of Things (IoT), network-wide anomaly detection is a crucial part of monitoring IoT networks due to the inherent security vulnerabilities of most IoT devices. Principal Components Analysis (PCA) has been proposed to separate network traffics into two disjoint subspaces corresponding to normal and malicious behaviors for anomaly detection. However, the privacy concerns and limitations of devices' computing resources compromise the practical effectiveness of PCA. We propose a federated PCA learning using Grassmann manifold optimization, which coordinates IoT devices to aggregate a joint profile of normal network behaviors for anomaly detection. First, we introduce a privacy-preserving federated PCA framework to simultaneously capture the profile of various IoT devices' traffic. Then, we investigate the alternating direction method of multipliers gradient-based learning on the Grassmann manifold to guarantee fast training and low detecting latency with limited computational resources. Finally, we show that the computational complexity of the Grassmann manifold-based algorithm is satisfactory for hardware-constrained IoT devices. Empirical results on the NSL-KDD dataset demonstrate that our method outperforms baseline approaches.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229026"}, {"primary_key": "1202904", "vector": [], "sparse_vector": [], "title": "Dynamic Edge-centric Resource Provisioning for Online and Offline Services Co-location.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Due to the penetration of edge computing, a wide variety of workloads are sunk down to the network edge to alleviate huge pressure of the cloud. With the presence of high input workload dynamics and intensive edge resource contention, it is highly non-trivial for an edge proxy to optimize the scheduling of heterogeneous services with diverse QoS requirements. In general, online services should be quickly completed in a quite stable running environment to meet their tight latency constraint, while offline services can be processed in a loose manner for their elastic soft deadlines. To well coordinate such services at the resource-limited edge cluster, in this paper, we study an edge-centric resource provisioning optimization for dynamic online and offline services co-location, where the proxy seeks to maximize timely online service performances while maintaining satisfactory long-term offline service performances. However, intricate hybrid couplings for provisioning decisions arise due to heterogeneous constraints of the co-located services and their different time-scale performances. We hence first propose a reactive provisioning approach without requiring a prior knowledge of future system dynamics, which leverages a Lagrange relaxation for devising constraint-aware stochastic subgradient algorithm to deal with the challenge of hybrid couplings. To further boost the performance by integrating the powerful machine learning techniques, we also advocate a predictive provisioning approach, where the future request arrivals can be estimated accurately. With rigorous theoretical analysis and extensive trace-driven evaluations, we show the superior performance of our proposed algorithms for online and offline services co-location at the edge.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228949"}, {"primary_key": "1202906", "vector": [], "sparse_vector": [], "title": "Marten: A Built-in Security DRL-Based Congestion Control Framework by Polishing the Expert.", "authors": ["Zhiyuan Pan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Deep reinforcement learning (DRL) has been proved to be an effective method to improve the congestion control algorithms (CCAs). However, the lack of training data and training scale affect the effectiveness of DRL model. Combining rule-based CCAs (such as BBR) as a guide for DRL is an effective way to improve learning-based CCAs. By experiment measurement, we find that the rule-based CCAs limit the action exploration and even cause DRL's excessive dependence to gain higher DRL's reward gain. To overcome the constraints, we propose Marten, a framework which improves the effectiveness of rule-based CCAs for DRL. <PERSON><PERSON> uses entropy as the degree of exploration and uses it to expand the exploration of DRL. Furthermore, <PERSON><PERSON> introduces the shielding mechanism to avoid wrong DRL actions. We have implemented <PERSON><PERSON> in both simulation platform OpenAI Gym and deployment platform QUIC. The experimental results in production network demonstrate Marten can improve throughput by 0.36% and reduce latency by 14.89% on average compared with Eagle, and improve throughput by 2.79% and reduce latency by 11.73% on average compared with BBR.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228952"}, {"primary_key": "1202907", "vector": [], "sparse_vector": [], "title": "On the Capacity Region of a Quantum Switch with Entanglement Purification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Quantum switches are envisioned to be an integral component of future entanglement distribution networks. They can provide high quality entanglement distribution service to end-users by performing quantum operations such as entanglement swapping and entanglement purification. In this work, we characterize the capacity region of such a quantum switch under noisy channel transmissions and imperfect quantum operations. We express the capacity region as a function of the channel and network parameters (link and entanglement swap success probability), entanglement purification yield and application level parameters (target fidelity threshold). In particular, we provide necessary conditions to verify if a set of request rates belong to the capacity region of the switch. We use these conditions to find the maximum achievable end-to-end user entanglement generation throughput by solving a set of linear optimization problems. We develop a max-weight scheduling policy and prove that the policy stabilizes the switch for all feasible request arrival rates. As we develop scheduling policies, we also generate new results for computing the conditional yield distribution of different classes of purification protocols. The conclusions obtained in this work can yield useful guidelines for subsequent quantum switch designs.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229003"}, {"primary_key": "1202908", "vector": [], "sparse_vector": [], "title": "Spotlight on 5G: Performance, Device Evolution and Challenges from a Mobile Operator Perspective.", "authors": ["Paniz <PERSON>", "<PERSON><PERSON>", "Özgü Alay", "<PERSON>", "<PERSON>"], "summary": "Fifth Generation (5G) has been acknowledged as a significant shift in cellular networks, expected to run significantly different classes of services and do so with outstanding performance in terms of low latency, high capacity, and extreme reliability. Managing the resulting complexity of mobile network architectures will depend on making efficient decisions at all network levels based on end-user requirements. However, to achieve this, it is critical to first understand the current mobile ecosystem and capture the device heterogeneity, which is one of the major challenges for ensuring the successful exploitation of 5G technologies.In this paper, we conduct a large-scale measurement study of a commercial mobile operator in the UK, focusing on bringing forward a real-world view on the available network resources, as well as how more than 30M end-user devices utilize the mobile network. We focus on the current status of the 5G Non-Standalone (NSA) deployment and the network-level performance and show how it caters to the prominent use cases that 5G promises to support. Finally, we demonstrate that a fine-granular set of requirements is, in fact, necessary to orchestrate the service to the diverse groups of 5G devices, some of which operate in permanent roaming.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229108"}, {"primary_key": "1202910", "vector": [], "sparse_vector": [], "title": "Crow API: Cross-device I/O Sharing in Web Applications.", "authors": ["Seonghoon Park", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Although cross-device input/output (I/O) sharing is useful for users who own multiple computing devices, previous solutions had a platform-dependency problem. The meta-platform characteristics of web applications could provide a viable solution. In this paper, we propose the Crow application programming interface (API) that allows web applications to access other devices’ I/O through standard web APIs without modifying operating systems or browsers. The provision of cross-device I/O should resolve two key challenges. First, the web environment lacks support for device discovery when making a device-to-device connection. This requires a significant effort for developers to implement and maintain signaling servers. To address this challenge, we propose a serverless Crow connectivity mechanism using devices’ I/O-specific communication schemes. Second, JavaScript runtimes have limitations in supporting cross-device inter-process communication (IPC). To solve the problem, we propose a web IPC scheme, called Crow IPC, which introduces a proxy interface that relays the cross-device IPC connection. Crow IPC also provides a mechanism for ensuring functional consistency. We implemented the Crow API as a JavaScript library with which developers can easily develop their applications. An extensive evaluation showed that the Crow API provides cross-device I/O sharing functionality effectively and efficiently on various web applications and platforms.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228950"}, {"primary_key": "1202911", "vector": [], "sparse_vector": [], "title": "SkyNet: Multi-Drone Cooperation for Real-Time Person Identification and Localization.", "authors": ["<PERSON><PERSON><PERSON>", "Qing Li", "Yuanzheng Tan", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Chen", "<PERSON><PERSON>", "<PERSON>"], "summary": "Aerial images from drones have been used to detect and track suspects in the crowd for the public safety purpose. However, using a single drone for human identification and localization faces many challenges including low accuracy and long latency, due to poor visibility, varying field of views (FoVs), and limited on-board computing resources. In this paper, we propose SkyNet, a multi-drone cooperative system for accurate and real-time human identification and localization. SkyNet computes the 3D position of a person by cross searching from multiple views. To achieve high accuracy in identification, SkyNet fuses aerial images of multiple drones according to their legibility. Moreover, by predicting the estimated finishing time of tasks, SkyNet schedules and balances workloads among edge devices and the cloud server to minimize processing latency. We implement and deploy SkyNet in real life, and evaluate the performance of identification and localization with 20 human participants. The results show that SkyNet can locate people with an average error within 0.18m on a square of 554m2. The identification accuracy is 91.36%. The localization and identification process is completed within 0.84s.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228978"}, {"primary_key": "1202913", "vector": [], "sparse_vector": [], "title": "Contrastive learning with self-reconstruction for channel-resilient modulation classification.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Despite the substantial success of deep learning for Automatic Modulation Classification (AMC), models trained on a specific transmitter configuration and channel model often fail to generalize well to other scenarios with different transmitter configurations, wireless fading channels, or receiver impairments such as clock offset. This paper proposes Contrastive Learning with Self-Reconstruction called CLSR-AMC to learn good representations of signals resilient to channel changes. While contrastive loss focuses on the differences between individual modulations, the reconstruction loss captures representative features of the signal. Additionally, we develop three data augmentation operators to emulate the impact of channel and hardware impairments without exhaustive modeling of different channel profiles. We perform extensive experimentation with commonly used realistic datasets. We show that CLSR-AMC outperforms its counterpart based on contrastive learning for the same amount of labeled data by significant average accuracy gains of 24.29%, 17.01%, and 15.97% in the Additive White Gaussian Noise (AWGN), Rayleigh, and Rician channels, respectively.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228908"}, {"primary_key": "1202914", "vector": [], "sparse_vector": [], "title": "Distributed Demand-aware Network Design using Bounded Square Root of Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "While the traditional design of network topologies is demand oblivious, recent advances in reconfigurable networks enable real-time and dynamic communication network topologies, e.g., in datacenter networks. This trend motivates a new paradigm where topologies can adjust to the demand they need to serve. We consider the static and distributed version of this network design problem where the input is a request distribution, $\\mathcal{D}$ (demand matrix), and a bound Δ, on the maximum degree of the output topology. In turn, the objective is to design an (undirected) demand-aware network N of bounded-degree Δ, which minimizes the expected path length (with respect to $\\mathcal{D}$).This paper draws a connection between the k-root of graphs and the network design problem and uses forest-decomposition of the demand matrix as the primary methodology. In turn, we provide new algorithms for demand-aware network design, including cases where our algorithms are (order) optimal and improve previous results. In addition, we provide, for the first time and for the case of bounded arboricity, (i) an efficient distributed algorithm for the CONGEST model and (ii) an efficient and PRAM-based parallel algorithm. We also present empirical results on real-world demand matrices where our algorithms produce both low-degree and low-expected path length network designs.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228932"}, {"primary_key": "1202918", "vector": [], "sparse_vector": [], "title": "SeedTree: A Dynamically Optimal and Local Self-Adjusting Tree.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider the fundamental problem of designing a self-adjusting tree, which efficiently and locally adapts itself towards the demand it serves (namely accesses to the items stored by the tree nodes), striking a balance between the benefits of such adjustments (enabling faster access) and their costs (reconfigurations). This problem finds applications, among others, in the context of emerging demand-aware and reconfigurable datacenter networks and features connections to self-adjusting data structures. Our main contribution is SeedTree, a dynamically optimal self-adjusting tree which supports local (i.e., greedy) routing, which is particularly attractive under highly dynamic demands. SeedTree relies on an innovative approach which defines a set of unique paths based on randomized item addresses, and uses a small constant number of items per node. We complement our analytical results by showing the benefits of SeedTree empirically, evaluating it on various synthetic and real-world communication traces.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.********"}, {"primary_key": "1202919", "vector": [], "sparse_vector": [], "title": "A Quantum Overlay Network for Efficient Entanglement Distribution.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Distributing quantum entanglements over long distances is essential for the realization of a global scale quantum Internet. Most of the prior work and proposals assume an on-demand distribution of entanglements which may result in significant network resource under-utilization. In this work, we introduce Quantum Overlay Networks (QONs) for efficient entanglement distribution in quantum networks. When the demand to create end-to-end user entanglements is low, QONs can generate and store maximally entangled Bell pairs (EPR pairs) at specific overlay storage nodes of the network. Later, during peak demands, requests can be served by performing entanglement swaps either over a direct path from the network or over a path using the storage nodes. We solve the link entanglement and storage resource allocation problem in such a QON using a centralized optimization framework. We evaluate the performance of our proposed QON architecture over a wide number of network topologies under various settings using extensive simulation experiments. Our results demonstrate that QONs fare well by a factor of 40% with respect to meeting surge and changing demands compared to traditional non-overlay proposals. QONs also show significant improvement in terms of average entanglement request service delay over non-overlay approaches.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228944"}, {"primary_key": "1202920", "vector": [], "sparse_vector": [], "title": "Network Slicing: Market Mechanism and Competitive Equilibria.", "authors": ["Panagiot<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Towards addressing spectral scarcity and enhancing resource utilization in 5G networks, network slicing is a promising technology to establish end-to-end virtual networks without requiring additional infrastructure investments.By leveraging Software Defined Networks (SDN) and Network Function Virtualization (NFV), we can realize slices completely isolated and dedicated to satisfy the users' diverse Quality of Service (QoS) prerequisites and Service Level Agreements (SLAs).This paper focuses on the technical and economic challenges that emerge from the application of the network slicing architecture to real-world scenarios.We consider a market where multiple Network Providers (NPs) own the physical infrastructure and offer their resources to multiple Service Providers (SPs).Then, the SPs offer those resources as slices to their associated users.We propose a holistic iterative model for the network slicing market along with a clock auction that converges to a robust ǫ-competitive equilibrium.At the end of each cycle of the market, the slices are reconfigured and the SPs aim to learn the private parameters of their users.Numerical results are provided that validate and evaluate the convergence of the clock auction and the capability of the proposed market architecture to express the incentives of the different entities of the system.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228856"}, {"primary_key": "1202921", "vector": [], "sparse_vector": [], "title": "SEM-O-RAN: Semantic and Flexible O-RAN Slicing for NextG Edge-Assisted Mobile Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "5G and beyond cellular networks (NextG) will support the continuous execution of resource-expensive edgeassisted deep learning (DL) tasks.To this end, Radio Access Network (RAN) resources will need to be carefully \"sliced\" to satisfy heterogeneous application requirements while minimizing RAN usage.Existing slicing frameworks treat each DL task as equal and inflexibly define the resources to assign to each task, which leads to sub-optimal performance.In this paper, we propose SEM-O-RAN, the first semantic and flexible slicing framework for NextG Open RANs.Our key intuition is that different DL classifiers can tolerate different levels of image compression, due to the semantic nature of the target classes.Therefore, compression can be semantically applied so that the networking load can be minimized.Moreover, flexibility allows SEM-O-RAN to consider multiple edge allocations leading to the same task-related performance, which significantly improves system-wide performance as more tasks can be allocated.First, we mathematically formulate the Semantic Flexible Edge Slicing Problem (SF-ESP), demonstrate that it is NP-hard, and provide an approximation algorithm to solve it efficiently.Then, we evaluate the performance of SEM-O-RAN through extensive numerical analysis with state-of-the-art multi-object detection (YOLOX) and image segmentation (BiSeNet V2), as well as realworld experiments on the Colosseum testbed.Our results show that SEM-O-RAN improves the number of allocated tasks by up to 169% with respect to the state of the art.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228870"}, {"primary_key": "1202922", "vector": [], "sparse_vector": [], "title": "Latency-First Smart Contract: Overclock the Blockchain for a while.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Weifeng Lyu"], "summary": "Blockchain systems can become overwhelmed by a large number of transactions, leading to increased latency. As a consequence, latency-sensitive users must bid against each other and pay higher fees to ensure that their transactions are processed in priority. However, most of the time of a blockchain system (78% in Ethereum), there is still a lot of unused computational power, with few users sending transactions. To address this issue and reduce latency for users, we propose the latency-first smart contract model in this paper, which optimistically accepts committed transactions. This allows users to submit a commitment during times of high demand, and then complete the rest of the work at a lower priority. From the perspective of the blockchain, this temporarily \"overclocks\" the system. We have developed a programming tool for our model, and our experiments show that the proposed latency-first smart contract model can greatly reduce latency during the periods of high demand.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228992"}, {"primary_key": "1202923", "vector": [], "sparse_vector": [], "title": "DIAMOND: Taming Sample and Communication Complexities in Decentralized Bilevel Optimization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Liu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON>", "<PERSON>"], "summary": "Decentralized bilevel optimization has received increasing attention recently due to its foundational role in many emerging multi-agent learning paradigms (e.g., multi-agent meta-learning and multi-agent reinforcement learning) over peer-to-peer edge networks. However, to work with the limited computation and communication capabilities of edge networks, a major challenge in developing decentralized bilevel optimization techniques is to lower sample and communication complexities. This motivates us to develop a new decentralized bilevel optimization called DIAMOND (decentralized single-timescale stochastic approximation with momentum and gradient-tracking). The contributions of this paper are as follows: i) our DIAMOND algorithm adopts a single-loop structure rather than following the natural double-loop structure of bilevel optimization, which offers low computation and implementation complexity; ii) compared to existing approaches, the DIAMOND algorithm does not require any full gradient evaluations, which further reduces both sample and computational complexities; iii) through a careful integration of momentum information and gradient tracking techniques, we show that the DIAMOND algorithm enjoys $\\mathcal{O}\\left( {{ \\in ^{ - 3/2}}} \\right)$ in sample and communication complexities for achieving an ϵ-stationary solution, both of which are independent of the dataset sizes and significantly outperform existing works. Extensive experiments also verify our theoretical findings.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228853"}, {"primary_key": "1202927", "vector": [], "sparse_vector": [], "title": "Lock-based or Lock-less: Which Is Fresh?", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We examine status updating systems in which time-stamped status updates are stored/written in shared-memory. Specifically, we compare Read-Copy-Update (RCU) and Readers-Writer lock (RWL) as shared-memory synchronization primitives on the update freshness. To demonstrate the tension between readers and writers accessing shared-memory, we consider a network scenario with a pair of coupled updating processes. Location updates of a mobile terminal are written to a shared-memory Forwarder Information Base (FIB) at a network forwarder. An application server sends \"app updates\" to the mobile terminal via the forwarder. Arriving app updates at forwarder are addressed (by reading the FIB) and forwarded to the mobile terminal. If a FIB read returns an outdated address, the misaddressed app update is lost in transit. We redesign these reader and writer processes using preemption mechanisms that improve the timeliness of updates. We present a Stochastic Hybrid System (SHS) framework to analyze location and app update age processes and show how these two age processes are coupled through synchronization primitives. Our analysis shows that using a lock-based primitive (RWL) can serve fresher app updates to the mobile terminal at higher location update rates while lock-less (RCU) mechanism favors timely delivery of app updates at lower location update rates.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229077"}, {"primary_key": "1202932", "vector": [], "sparse_vector": [], "title": "Age of Broadcast and Collection in Spatially Distributed Wireless Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider a wireless network with a base station broadcasting and collecting time-sensitive data to and from spatially distributed nodes in the presence of wireless interference. The Age of Information (AoI) is the time that has elapsed since the most-recently delivered packet was generated, and captures the freshness of information. In the context of broadcast and collection, we define the Age of Broadcast (AoB) to be the amount of time elapsed until all nodes receive a fresh update, and the Age of Collection (AoC) as the amount of time that elapses until the base station receives an update from all nodes. We quantify the average broadcast and collection ages in two scenarios: 1) instance-dependent, in which the locations of all nodes and interferers are known, and 2) instance-independent, in which they are not known but are located randomly, and expected age is characterized with respect to node locations. In the instance-independent case, we show that AoB and AoC scale super-exponentially with respect to the radius of the region surrounding the base station. Simulation results highlight how expected AoB and AoC are affected by network parameters such as network density, medium access probability, and the size of the coverage region.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228924"}, {"primary_key": "1202934", "vector": [], "sparse_vector": [], "title": "Communication Efficient Secret Sharing with Dynamic Communication-Computation Conversion.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Secret Sharing (SS) is widely adopted in secure Multi-Party Computation (MPC) with its simplicity and computational efficiency. However, SS-based MPC protocol introduces significant communication overhead due to interactive operations on secret sharings over the network. For instance, training a neural network model with SS-based MPC may incur tens of thousands of communication rounds among parties, making it extremely hard for real-world deployment.To reduce the communication overhead of SS, prior works statically convert interactive operations to equivalent non-interactive operations with extra computation cost. However, we show that such static conversion misses chances for optimization, and further present SOLAR, an SS-based MPC framework that aims to reduce the communication overhead through dynamic communication-computation conversion. At its heart, SOLAR converts interactive operations that involve communication among parties to equivalent non-interactive operations within each party with extra computations and introduces a speculative strategy to perform opportunistic conversion when CPU is idle for network transmission. We have implemented and evaluated SOLAR on several popular MPC applications, and achieved 1.6-8.1 times speedup in multi-thread setting compared to the basic SS and 1.2-8.6 times speedup over static conversion.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228872"}, {"primary_key": "1202935", "vector": [], "sparse_vector": [], "title": "Prism: High-throughput LoRa Backscatter with Non-linear Chirps.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "LoRa backscatter enables long-distance communication with ultra-low energy consumption. Enabling concurrent transmissions among many LoRa backscatter tags is desirable for large-scale backscatter networks. However, LoRa backscatter signals sitting on linear chirps easily interfere with each other degrading the throughput of concurrent transmissions. In this paper, we propose Prism that utilizes different types of non-linear chirps to modulate backscatter data allowing multiple backscatter tags to transmit concurrently in the same channel. By taking linear chirps from commercial-off-the-shelf (COTS) LoRa nodes as excitation sources, how to convert the linear chirps to their non-linear counterparts is not trivial on resource-limited backscatter tags. To solve this challenge, we design a lightweight and low-power method, including a frequency-shift function and hardware framework, to shift the frequency of the linear chirps to the non-linear chirps accurately. Moreover, we develop effective methods to calibrate various offsets and concentrate chirp energy to achieve reliable decoding. We implement Prism with customized low-cost hardware, process backscatter signals with USRP, and evaluate its performance in both indoor and outdoor environments. The results show that seven tags can transmit concurrently with less than 1% bit error rate by using seven different types of non-linear chirps in the same channel, resulting in a 6× higher transmission concurrency than state-of-the-art.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228960"}, {"primary_key": "1202936", "vector": [], "sparse_vector": [], "title": "FEAT: Towards Fast Environment-Adaptive Task Offloading and Power Allocation in MEC.", "authors": ["Tao Ren", "<PERSON><PERSON><PERSON>", "Hang He", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mobile edge computing (MEC) has been proposed to provide mobile devices with both satisfactory computing resources and latency. Key issues in MEC include task offloading and power allocation (TOPA), for which deep reinforcement learning (DRL) is becoming a popular methodology. However, most DRL-based TOPA approaches are typically developed in a certain environment, lacking the adaptability to unseen environments. Motivated by this, this paper proposes a Fast Environment-Adaptive TOPA (FEAT) approach that could adapt to unseen environments with little fine-tuning. Specifically, we first split MEC states into the internal state and environmental state. Then, based on these two types of states, we develop two main components of FEAT — a group of internal state-dependent TOPA meta-policies and an environmental state-embedded steerer. Meta-policies learn TOPA skills within the internal state space (allowing reusing meta-policies in different environments), while the steerer learns to choose appropriate meta-policies according to embedded environmental states. When encountering an unseen environment with the same internal state space, FEAT only needs to fine-tune the steerer using the newly embedded environmental state with few internal state explorations. Extensive experimental results on simulation and testbed demonstrate that FEAT outperforms the state-of-the-art by more than 16.4% in terms of fine-tuning speeds.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228946"}, {"primary_key": "1202937", "vector": [], "sparse_vector": [], "title": "Utilizing the Neglected <PERSON> Lobe for Mobile Charging.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Xu", "<PERSON><PERSON>", "<PERSON>"], "summary": "Benefitting from the breakthrough of wireless power transfer technology, the lifetime of Wireless Sensor Networks (WSNs) can be significantly prolonged by scheduling a mobile charger (MC) to charge sensors. Compared with omnidirectional charging, the MC equipped with directional antenna can concentrate energy in the intended direction, making charging more efficient. However, all prior arts ignore the considerable energy leakage behind the directional antenna (i.e., back lobe), resulting in energy wasted in vain. To address this issue, we study a fundamental problem of how to utilize the neglected back lobe and schedule the directional MC efficiently. Towards this end, we first build and verify a directional charging model considering both main and back lobes. Then, we focus on jointly optimizing the number of dead sensors and energy usage effectiveness. We achieve these by introducing a scheduling scheme that utilizes both main and back lobes to charge multiple sensors simultaneously. Finally, extensive simulations and field experiments demonstrate that our scheme reduces the number of dead sensors by 49.5% and increases the energy usage effectiveness by 10.2% on average as compared with existing algorithms.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228855"}, {"primary_key": "1202938", "vector": [], "sparse_vector": [], "title": "Secure and Robust Two Factor Authentication via Acoustic Fingerprinting.", "authors": ["Yanzhi Ren", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Hongbo Liu", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Hongwei Li"], "summary": "The two-factor authentication (2FA) has become pervasive as the mobile devices become prevalent. Existing 2FA solutions usually require some form of user involvement, which could severely affect user experience and bring extra burdens to users. In this work, we propose a secure 2FA that utilizes the individual acoustic fingerprint of the speaker/microphone on enrolled device as the second proof. The main idea behind our system is to use both magnitude and phase fingerprints derived from the frequency response of the enrolled device by emitting acoustic beep signals alternately from both enrolled and login devices and receiving their direct arrivals for 2FA. Given the input microphone samplings, our system designs an arrival time detection scheme to accurately identify the beginning point of the beep signal from the received signal. To achieve a robust authentication, we develop a new distance mitigation scheme to eliminate the impact of transmission distances from the sound propagation model for extracting stable fingerprint in both magnitude and phase domain. Our device authentication component then calculates a weighted correlation value between the device profile and fingerprints extracted from run-time measurements to conduct the device authentication for 2FA. Our experimental results show that our proposed system is accurate and robust to both random impersonation and Man-in-the-middle (MiM) attack across different scenarios and device models.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229054"}, {"primary_key": "1202941", "vector": [], "sparse_vector": [], "title": "On the Limit Performance of Floating Gossip.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper we investigate the limit performance of Floating Gossip, a new, fully distributed Gossip Learning scheme which relies on Floating Content to implement locationbased probabilistic evolution of machine learning models in an infrastructure-less manner.We consider dynamic scenarios where continuous learning is necessary, and we adopt a mean field approach to investigate the limit performance of Floating Gossip in terms of amount of data that users can incorporate into their models, as a function of the main system parameters.Different from existing approaches in which either communication or computing aspects of Gossip Learning are analyzed and optimized, our approach accounts for the compound impact of both aspects.We validate our results through detailed simulations, proving good accuracy.Our model shows that Floating Gossip can be very effective in implementing continuous training and update of machine learning models in a cooperative manner, based on opportunistic exchanges among moving users.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.********"}, {"primary_key": "1202942", "vector": [], "sparse_vector": [], "title": "Federated Learning under Heterogeneous and Correlated Client Availability.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The enormous amount of data produced by mobile and IoT devices has motivated the development of federated learning (FL), a framework allowing such devices (or clients) to collaboratively train machine learning models without sharing their local data. FL algorithms (like FedAvg) iteratively aggregate model updates computed by clients on their own datasets. Clients may exhibit different levels of participation, often correlated over time and with other clients. This paper presents the first convergence analysis for a FedAvg-like FL algorithm under heterogeneous and correlated client availability. Our analysis highlights how correlation adversely affects the algorithm's convergence rate and how the aggregation strategy can alleviate this effect at the cost of steering training toward a biased model. Guided by the theoretical analysis, we propose CA-Fed, a new FL algorithm that tries to balance the conflicting goals of maximizing convergence speed and minimizing model bias. To this purpose, CA-Fed dynamically adapts the weight given to each client and may ignore clients with low availability and large correlation. Our experimental results show that CA-Fed achieves higher time-average accuracy and a lower standard deviation than state-of-the-art AdaFed and F3AST, both on synthetic and real datasets.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228876"}, {"primary_key": "1202943", "vector": [], "sparse_vector": [], "title": "ICARUS: Learning on IQ and Cycle Frequencies for Detecting Anomalous RF Underlay Signals.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The RF environment in a secure space can be compromised by intentional transmissions of hard-to-detect underlay signals that overlap with a high-power baseline transmission. Specifically, we consider the case where a direct sequence spread spectrum (DSSS) signal is the underlay signal hiding within a baseline 4G Long-Term Evolution (LTE) signal. As compared to overt actions like jamming, the DSSS signal allows the LTE signal to be decodable, which makes it hard to detect. ICARUS presents a machine learning based framework that offers choices at the physical layer for inference with inputs of (i) in-phase and quadrature (IQ) samples only, (ii) cycle-frequency features obtained via cyclostationary signal processing (CSP), and (iii) fusion of both, to detect the underlay DSSS signal and its modulation type within LTE frames. ICARUS chooses the best inference method considering both the expected accuracy and the computational overhead. ICARUS is rigorously validated on multiple real-world datasets that include signals captured in cellular bands in the wild and the NSF POWDER testbed for advanced wireless research (PAWR). Results reveal that ICARUS can detect DSSS anomalies and its modulation scheme with 98-100% and 67 − 99% accuracy, respectively, while completing inference within 3 − 40 milliseconds on an NVIDIA A100 GPU platform.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228929"}, {"primary_key": "1202949", "vector": [], "sparse_vector": [], "title": "RIS-STAR: RIS-based Spatio-Temporal Channel Hardening for Single-Antenna Receivers.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Small form-factor single antenna devices, typically deployed within wireless sensor networks, lack many benefits of multi-antenna receivers like leveraging spatial diversity to enhance signal reception reliability. In this paper, we introduce the theory of achieving spatial diversity in such single-antenna systems by using reconfigurable intelligent surfaces (RIS). Our approach, called ‘RIS-STAR’, proposes a method of proactively perturbing the wireless propagation environment multiple times within the symbol time (that is less than the channel coherence time) through reconfiguring an RIS. By leveraging the stationarity of the channel, RIS-STAR ensures that the only source of perturbation is due to the chosen and controllable RIS configuration. We first formulate the problem to find the set of RIS configurations that maximizes channel hardening, which is a measure of link reliability. Our solution is independent of the transceiver’s relative location with respect to the RIS and does not require channel estimation, alleviating two key implementation concerns. We then evaluate the performance of RIS-STAR using a custom-simulator and an experimental testbed composed of PCB-fabricated RIS. Specifically, we demonstrate how a SISO link can be enhanced to perform similar to a SIMO link attaining an 84.6% channel hardening improvement in presence of strong multipath and non-line-of-sight conditions.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228868"}, {"primary_key": "1202950", "vector": [], "sparse_vector": [], "title": "On Data Processing through the Lenses of S3 Object Lambda.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Despite that Function-as-a-Service (FaaS) has settled down as one of the fundamental cloud programming models, it is still evolving quickly. Recently, Amazon has introduced S3 Object Lambda, which allows a user-defined function to be automatically invoked to process an object as it is being downloaded from S3. As with any new feature, careful study thereof is the key to elucidate if S3 Object Lambda, or more generally, if inline serverless data processing, is a valuable addition to the cloud. For this reason, we conduct an extensive measurement study of this novel service, in order to characterize its architecture and performance (in terms of coldstart latency, TTFB times, and more). We particularly put an eye on the streaming capabilities of this new form of function, as it may open the door to empower existing serverless systems with stream processing capacities. We discuss the pros and cons of this new capability through several workloads, concluding that S3 Object Lambda can go far beyond its original purpose and be leveraged as a building block for more complex abstractions.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228890"}, {"primary_key": "1202951", "vector": [], "sparse_vector": [], "title": "DisProTrack: Distributed Provenance Tracking over Serverless Applications.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Provenance tracking has been widely used in the recent literature to debug system vulnerabilities and find the root causes behind faults, errors, or crashes over a running system. However, the existing approaches primarily developed graph-based models for provenance tracking over monolithic applications running directly over the operating system kernel. In contrast, the modern DevOps-based service-oriented architecture relies on distributed platforms, like serverless computing that uses container-based sandboxing over the kernel. Provenance tracking over such a distributed micro-service architecture is challenging, as the application and system logs are generated asynchronously and follow heterogeneous nomenclature and logging formats. This paper develops a novel approach to combining system and micro-services logs together to generate a Universal Provenance Graph (UPG) that can be used for provenance tracking over serverless architecture. We develop a Loadable Kernel Module (LKM) for runtime unit identification over the logs by intercepting the system calls with the help from the control flow graphs over the static application binaries. Finally, we design a regular expression-based log optimization method for reverse query parsing over the generated UPG. A thorough evaluation of the proposed UPG model with different benchmarked serverless applications shows the system's effectiveness.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228884"}, {"primary_key": "1202952", "vector": [], "sparse_vector": [], "title": "A New Framework: Short-Term and Long-Term Returns in Stochastic Multi-Armed Bandit.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Stochastic Multi-Armed Bandit (MAB) has recently been studied widely due to its vast range of applications. The classic model considers the reward of a pulled arm to be observed after a time delay that is sampled from a random distribution assigned for each arm. In this paper, we propose an extended framework in which pulling an arm gives both an instant (short-term) reward and a delayed (long-term) reward at the same time. The distributions of reward values for short-term and long-term rewards are related with a previously known relationship. The distribution of time delay for an arm is independent of the reward distributions of the arm. In our work, we devise three UCB-based algorithms, where two of them are near-optimal-regret algorithms for this new model, with the corresponding regret analysis for each one of them. Additionally, the random distributions for time delay values are allowed to yield infinite time, which corresponds to a case where the arm only gives a short-term reward. Finally, we evaluate our algorithms and compare this paradigm with previously known models on both a synthetic data set and a real data set that would reflect one of the potential applications of this model.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228899"}, {"primary_key": "1202953", "vector": [], "sparse_vector": [], "title": "A Framework for Wireless Technology Classification using Crowdsensing Platforms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Spectrum crowdsensing systems do not provide labeled data near real-time yet. We propose a framework that addresses this challenge and relies solely on Power Spectrum Density (PSD) data collected by low-cost receivers. A major hurdle is to design a system that is computationally efficient for near real-time operation, yet using only the limited 2 MHz bandwidth of low-cost spectrum sensors. First, we present a method for unsupervised transmission detection that works with PSD data already collected by the backend of the crowdsensing platform, and that provides stable detection of transmission boundaries. Second, we introduce a data-driven deep learning solution to classify the wireless technology used by the transmitter, using transmission features in a compressed space extracted from single PSD measurements over at most 2 MHz band. We build an experimental platform, and evaluate our framework with real-world data collected from 47 different sensors deployed across Europe. We show that our framework yields an average classification accuracy close to 94.25% over the testing dataset, with a maximum latency of 3.4 seconds when integrated in the backend of a major crowdsensing network. Code and data have been released for reproducibility and further studies.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228867"}, {"primary_key": "1202955", "vector": [], "sparse_vector": [], "title": "On the Effective Capacity of RIS-enabled mmWave Networks with Outdated CSI.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Reconfigurable intelligent surfaces (RISs) have great potential to improve the coverage of mmWave networks; however, acquiring perfect channel state information (CSI) of a RIS-enabled mmWave network is very costly and should thus be done infrequently. At the same time, finding an optimal RIS configuration when CSI is outdated is challenging. To this end, this work aims to provide practical insights into the tradeoff between the outdatedness of the CSI and the system performance by using the effective capacity as analytical tool. We consider a RIS-enabled mmWave downlink where the base station (BS) operates under statistical quality-of-service (QoS) constraints. We find a closed-form expression for the effective capacity that incorporates the degree of optimism of packet scheduling and correlation strength between instantaneous and outdated CSI. Moreover, our analysis allows us to find optimal values of the signal-to-interference-plus-noise-ratio (SINR) distribution parameter and their impact on the effective capacity in different network scenarios. Simulation results demonstrate that better effective capacity can be achieved with suboptimal RIS configuration when the channel estimates are known to be outdated. It allows us to design system parameters that guarantee better performance while keeping the complexity and cost associated with channel estimation to a minimum.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229028"}, {"primary_key": "1202956", "vector": [], "sparse_vector": [], "title": "weBurst can be Harmless: Achieving Line-rate Software Traffic Shaping by Inter-flow Batching.", "authors": ["Danfeng Shan", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Fengyuan Ren"], "summary": "Traffic shaping is a common function at end hosts. Compared with hardware ones, software shapers are more flexible to be developed and deployed, and thus are very attractive. Nevertheless, software approaches are still unsatisfactory as they struggle to saturate 40Gbps and higher speed.While much effort has been made to reduce the intrinsic overhead of software traffic shaping, we find that it is the extrinsic overhead, such as PCIe communications and interrupts, that hinders shaping from achieving 40Gbps - 100Gbps speed. Batching is an effective way to amortize these overheads. However, blindly batching can degrade the network performance, as it introduces bursts into the network. Diving into the dilemma, we find that intra-flow burst is to blame for harming the network performance, while inter-flow burst, consisting of packets from different flows, can be naturally demultiplexed in the network.Based on the insight, we present FlowBundler, which can achieve efficient traffic shaping by inter-flow batching. Testbed experiments show that FlowBundler can achieve an accurate shaping of 98Gbps with a single CPU core, which is 2.6× better than state-of-the-art approaches. Large-scale simulations show that FlowBundler can batch packet transmissions without harming the network performance.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229082"}, {"primary_key": "1202957", "vector": [], "sparse_vector": [], "title": "Online Container Scheduling for Data-intensive Applications in Serverless Edge Computing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Huang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Introducing the emerging serverless paradigm into edge computing could avoid over- and under-provisioning of limited edge resources and make complex edge resource management transparent to application developers, which largely facilitates the cost-effectiveness, portability, and short time-to-market of edge applications. However, the computation/data dispersion and device/network heterogeneity of edge environments prevent current serverless computing platforms from acclimating to the network edge. In this paper, we address such challenges by formulating a container placement and data flow routing problem, which fully considers the heterogeneity of edge networks and the overhead of operating serverless platforms on resource-limited edge servers. We design an online algorithm to solve the problem. We further show its local optimum for each arriving container and prove its theoretical guarantee to the optimal offline solution. We also conduct extensive simulations based on practical experiment results to show the advantages of the proposed algorithm over existing baselines.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.********"}, {"primary_key": "1202958", "vector": [], "sparse_vector": [], "title": "StreamSwitch: Fulfilling Latency Service-Layer Agreement for Stateful Streaming.", "authors": ["<PERSON><PERSON>", "Yancan Mao", "Hailin Xiang", "<PERSON><PERSON>", "<PERSON>"], "summary": "Distributed stream systems provide low latency by processing data as it arrives. However, existing systems do not provide latency guarantee, a critical requirement of real-time analytics, especially for stateful operators under burst and skewed workload. We present StreamSwitch, a control plane for stream systems to bound operator latency while optimizing resource usage. Based on a novel stream switch abstraction that unifies dynamic scaling and load balancing into a holistic control framework, our design incorporates reactive and predictive metrics to deduce the healthiness of executors and prescribes practically optimal scaling and load balancing decisions in time. We implement a prototype of StreamSwitch and integrate it with Apache Flink and Samza. Experimental evaluations on real-world applications and benchmarks show that StreamSwitch provides cost-effective solutions for bounding latency and outperforms the state-of-the-art alternative solutions.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228903"}, {"primary_key": "1202959", "vector": [], "sparse_vector": [], "title": "GinApp: An Inductive Graph Learning based Framework for Mobile Application Usage Prediction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mobile application usage prediction aims to infer the possible applications (Apps) that a user will launch next. It is critical for many applications, e.g., system optimization and smartphone resource management. Recently, graph based App prediction approaches have been proved effective, but still suffer from several issues. First, these studies cannot naturally generalize to unseen Apps. Second, they do not model asymmetric transitions between Apps. Third, they are hard to differentiate the contributions of different App usage context on the prediction result. In this paper, we propose GinApp, an inductive graph representation learning based framework, to resolve these issues. Specifically, we first construct an attribute-aware directed graph based on App usage records, where the App-App transitions and times are modeled by directed weighed edges. Then, we develop an inductive graph learning based method to generate effective node representations for the unseen Apps via sampling and aggregating information from neighboring nodes. Finally, our App usage prediction task is formulated as a link prediction problem on graph to generate the Apps with the largest probabilities as prediction results. Extensive experiments on two large-scale App usage datasets reveal that GinApp provides the state-of-the-art performance for App usage prediction.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228935"}, {"primary_key": "1202960", "vector": [], "sparse_vector": [], "title": "SJA: Server-driven Joint Adaptation of Loss and Bitrate for Multi-Party Realtime Video Streaming.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The outbreak of COVID-19 has dramatically promoted the explosive proliferation of multi-party realtime video streaming (MRVS) services, represented by Zoom and Microsoft Teams. Different from Video-on-Demand (VoD) or live streaming, MRVS enables all-to-all realtime video communication, bringing significant challenges to service providing. First, unreliable network transmission can cause network loss, resulting in delay increase and visual quality degradation. Second, the transformation from two-party to multi-party communication makes resource scheduling much more difficult. Moreover, optimizing the overall QoE requires a global coordination, which is quite challenging given the various impact factors such as bitrate and loss.In this paper, we propose the SJA framework, which is, to our best knowledge, the first server-driven joint loss and bitrate adaptation framework in multi-party realtime video streaming services towards maximized QoE. We comprehensively design an appropriate QoE model for MRVS services to capture the interplay among perceptual quality, variations, bitrate mismatch, loss damage, and streaming delay. We mathematically formulate the QoE maximization problem in MRVS services. A Lyapunov-based relaxation and the SJA algorithm are further designed to address the optimization problem with close-to-optimal performance. Evaluations show that our framework can outperform the SOTA solutions by 18.4% ∼ 46.5%.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229083"}, {"primary_key": "1202962", "vector": [], "sparse_vector": [], "title": "PipeMoE: Accelerating Mixture-of-Experts through Adaptive Pipelining.", "authors": ["Shaohuai Shi", "Xinglin Pan", "<PERSON><PERSON>", "<PERSON>"], "summary": "Large models have attracted much attention in the AI area. The sparsely activated mixture-of-experts (MoE) technique pushes the model size to a trillion-level with a sub-linear increase of computations as an MoE layer can be equipped with many separate experts, but only one or two experts need to be trained for each input data. However, the feature of dynamically activating experts of MoE introduces extensive communications in distributed training. In this work, we propose PipeMoE to adaptively pipeline the communications and computations in MoE to maximally hide the communication time. Specifically, we first identify the root reason why a higher pipeline degree does not always achieve better performance in training MoE models. Then we formulate an optimization problem that aims to minimize the training iteration time. To solve this problem, we build performance models for computation and communication tasks in MoE and develop an optimal solution to determine the pipeline degree such that the iteration time is minimal. We conduct extensive experiments with 174 typical MoE layers and two real-world NLP models on a 64-GPU cluster. Experimental results show that our PipeMoE almost always chooses the best pipeline degree and outperforms state-of-the-art MoE training systems by 5%-77% in training time.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228874"}, {"primary_key": "1202964", "vector": [], "sparse_vector": [], "title": "A Bayesian Framework for Online Nonconvex Optimization over Distributed Processing Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Atilla E<PERSON>il<PERSON>z"], "summary": "In many applications such as statistical machine learning, reinforcement learning, and optimization for large data centers, the increasing data size and model complexity have made it impractical to run optimizations over a single machine. Therefore, solving the distributed optimization problem has become an important task. In this work, we consider a distributed processing network $G = \\left( {\\mathcal{V},\\mathcal{E}} \\right)$ with n nodes, where each node i can only evaluate the values of a local function (i.e., has zeroth-order information) and can only communicate with its neighbors. The objective is to reach consensus on the global optimizer of ${\\max _{x \\in \\mathcal{X}}}\\frac{1}{n}\\sum\\nolimits_{i = 1}^n {{f_i}(x)} $. Previous methods either assume first-order gradient information which is not suitable for many model-free learning scenarios, or consider the zeroth-order information but assume convexity of the objective functions and can only guarantee convergence to a stationary point for nonconvex objectives. To address these limitations, we drop both the known gradient assumption and convexity assumption. Instead, we propose a distributed Bayesian framework for the problem with only zeroth-order information and general nonconvex objective functions in a Matérn Reproducing Kernel Hilbert Space (RKHS). Under this framework, we propose an algorithm and show that with high probability it reaches consensus on all nodes and has a sublinear regret with regard to the global optimal. The results are validated under numerical studies.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228879"}, {"primary_key": "1202969", "vector": [], "sparse_vector": [], "title": "Plug and Power: Fingerprinting USB Powered Peripherals via Power Side-channel.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The literature and the news regularly report cases of exploiting Universal Serial Bus (USB) devices as attack tools for malware injections and private data exfiltration.To protect against such attacks, security researchers proposed different solutions to verify the identity of a USB device via sidechannel information (e.g., timing or electromagnetic emission).However, such solutions often make strong assumptions on the measurement (e.g., electromagnetic interference-free area around the device), on a device's state (e.g., only at the boot or during specific actions), or are limited to one particular type of USB device (e.g., flash drive or input devices).In this paper, we present PowerID, a novel method to fingerprint USB peripherals based on their power consumption.PowerID analyzes the power traces from a peripheral to infer its identity and properties.We evaluate the effectiveness of our method on an extensive power trace dataset collected from 82 USB peripherals, including 35 models and 8 types.Our experimental results show that PowerID accurately recognizes a peripheral type, model, activity, and identity.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229048"}, {"primary_key": "1202971", "vector": [], "sparse_vector": [], "title": "Constrained Bandit Learning with Switching Costs for Wireless Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Bandits with arm selection constraints and bandits with switching costs have both gained recent attention in wireless networking research. Pessimistic-optimistic algorithms, which combine bandit learning with virtual queues to track the constraints, are commonly employed in the former. Block-based algorithms, where switching is disallowed within a block, are commonly employed in the latter. While efficient algorithms have been developed for both problems, it remains challenging to guarantee low regret and constraint violation in a bandit problem that includes both arm selection constraints and switching costs due to the tight coupling between the two. Here, switching may be necessary to decrease the constraint violation but comes at the cost of increased switching regret. In this paper, we tackle the constrained bandits with switching costs problem, for which we design a block-based pessimistic-optimistic algorithm. We identify three timely wireless networking applications for this framework in edge computing, mobile crowdsensing, and wireless network selection. We also prove that our algorithm achieves sublinear regret and vanishing constraint violation and corroborate these results with synthetic simulations and extensive trace-based simulations in the wireless network selection setting.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228986"}, {"primary_key": "1202972", "vector": [], "sparse_vector": [], "title": "Asynchronous Federated Unlearning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Thanks to regulatory policies such as the General Data Protection Regulation (GDPR), it is essential to provide users with the right to erasure regarding their own private data, even if such data has been used to train a neural network model. Such a machine unlearning problem becomes even more challenging in the context of federated learning, where clients collaborate to train a global model with their private data. When a client requests its data to be erased, its effects have already gradually permeated through a large number of clients, as the server aggregates client updates over multiple communication rounds. All of these affected clients need to participate in the retraining process, leading to prohibitive retraining costs with respect to the wall-clock training time.In this paper, we present the design and implementation of Knot, a new clustered aggregation mechanism custom-tailored to asynchronous federated learning. The design of Knot is based upon our intuition that, with asynchronous federated learning, clients can be divided into clusters, and aggregation can be performed within each cluster only so that retraining due to data erasure can be limited to within each cluster as well. To optimize client-cluster assignment, we formulated a lexicographical minimization problem that could be transformed into a linear programming problem and solved efficiently. Over a variety of datasets and tasks, we have shown clear evidence that <PERSON><PERSON> outperformed the state-of-the-art federated unlearning mechanisms by up to 85% in the context of asynchronous federated learning.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229075"}, {"primary_key": "1202973", "vector": [], "sparse_vector": [], "title": "TomoID: A Scalable Approach to Device Free Indoor Localization via RFID Tomography.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Device-free localization methods allow users to benefit from location-aware services without the need to carry a transponder. However, conventional radio sensing approaches using active wireless devices require wired power or continual battery maintenance, limiting deployability. We present TomoID, a real-time multi-user UHF RFID tomographic localization system that uses low-level communication channel parameters such as RSSI, RF Phase, and Read Rate, to create probability heatmaps of users' locations. The heatmaps are passed to our custom-designed signal processing and machine learning pipeline to robustly predict users' locations. Results show that TomoID is highly accurate, with an average mean error of 17.1 cm for a stationary user and 18.9 cm when users are walking. With multiuser tracking, results showing an average mean error of <72 cm for five individuals in constant motion. Importantly, TomoID is specifically designed to work in real-world multipath-rich indoor environments. Our signal processing and machine learning pipeline allows a pre-trained localization model to be applied to new environments of different shapes and sizes, while maintaining good accuracy sufficient for indoor user localization and tracking. Ultimately, TomoID enables a scalable, easily deployable, and minimally intrusive method for locating uninstrumented users in indoor environments.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228938"}, {"primary_key": "1202974", "vector": [], "sparse_vector": [], "title": "Prophet: An Efficient Feature Indexing Mechanism for Similarity Data Sharing at Network Edge.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As a promising infrastructure, edge storage systems have drawn many attempts to efficiently distribute and share data among edge servers. However, it remains open to meeting the increasing demand for similarity retrieval across servers. The intrinsic reason is that the existing solutions can only return an exact data match for a query while more general edge applications require the data similar to a query input from any server. To fill this gap, this paper pioneers a new paradigm to support high-dimensional similarity search at network edges. Specifically, we propose Prophet, the first known architecture for similarity data indexing. We first divide the feature space of data into plenty of subareas, then project both subareas and edge servers into a virtual plane where the distances between any two points can reflect not only data similarity but also network latency. When any edge server submits a request for data insert, delete, or query, it computes the data feature and the virtual coordinates; then iteratively forwards the request through greedy routing based on the forwarding tables and the virtual coordinates. By <PERSON>, similar high-dimensional features would be stored by a common server or several nearby servers. Compared with distributed hash tables in P2P networks, <PERSON> requires logarithmic servers to access for a data request and reduces the network latency from the logarithmic to the constant level of the server number. Experimental results indicate that <PERSON> achieves comparable retrieval accuracy and shortens the query latency by 55%~70% compared with centralized schemes.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.********"}, {"primary_key": "1202976", "vector": [], "sparse_vector": [], "title": "Multi-Objective Order Dispatch for Urban Crowd Sensing with For-Hire Vehicles.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>ming Jin", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Lu <PERSON>"], "summary": "For-hire vehicle-enabled crowd sensing (FVCS) has become a promising paradigm to conduct urban sensing tasks in recent years. FVCS platforms aim to jointly optimize both the order-serving revenue as well as sensing coverage and quality. However, such two objectives are often conflicting and need to be balanced according to the platforms' preferences on both objectives. To address this problem, we propose a novel cooperative multi-objective multi-agent reinforcement learning framework, referred to as MOVDN, to serve as the first preference-configurable order dispatch mechanism for FVCS platforms. Specifically, MOVDN adopts a decomposed network structure, which enables agents to make distributed order selection decisions, and meanwhile aligns each agent's local decision with the global objectives of the FVCS platform. Then, we propose a novel algorithm to train a single universal MOVDN that is optimized over the space of all preferences. This allows our trained model to produce the optimal policy for any preference. Furthermore, we provide the theoretical convergence guarantee and sample efficiency analysis of our algorithm. Extensive experiments on three real-world ride-hailing order datasets demonstrate that MOVDN outperforms strong baselines and can support the platform in decision-making effectively.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229103"}, {"primary_key": "1202977", "vector": [], "sparse_vector": [], "title": "Charging Dynamic Sensors through Online Learning.", "authors": ["Yu Sun", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As a novel solution for IoT applications, wireless rechargeable sensor networks (WRSNs) have achieved widespread deployment in recent years. Existing WRSN scheduling methods have focused extensively on maximizing the network charging utility in the fixed node case. However, when sensor nodes are deployed in dynamic environments (e.g., maritime environments) where sensors move randomly over time, existing approaches are likely to incur significant performance loss or even fail to execute normally. In this work, we focus on serving dynamic nodes whose locations vary randomly and formalize the dynamic WRSN charging utility maximization problem (termed MATA problem). Through discretizing candidate charging locations and modeling the dynamic charging process, we propose a near-optimal algorithm for maximizing charging utility. Moreover, we point out the long-short-term conflict of dynamic sensors that their location distributions in the short-term usually deviate from the long-term expectations. To tackle this issue, we further design an online learning algorithm based on the combinatorial multi-armed bandit (CMAB) model. It iteratively adjusts the charging strategy and adapts well to nodes' short-term location deviations. Extensive experiments and simulations demonstrate that the proposed scheme can effectively charge dynamic sensors and achieve a higher charging utility compared to baseline algorithms in both long-term and short-term.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228955"}, {"primary_key": "1202979", "vector": [], "sparse_vector": [], "title": "Energy-aware Age Optimization: AoI Analysis in Multi-source Update Network Systems Powered by Energy Harvesting.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Junzhou Luo"], "summary": "This work studies the Age-of-Information (AoI) minimization problem in information-gathering network systems, where time-sensitive data updates are collected from multiple information sources, which are equipped with a battery and harvest energy from ambient energy sources. In such systems, an information source can deliver its data update only when 1) there is energy in the battery; and 2) this source is selected to transmit its data update based on the transmission scheduling policy. This work analyzes how the energy arrival pattern of each source and the transmission scheduling policy jointly influence the average AoI among multiple sources and develops the closed-form expression of average AoI by analyzing its theoretical properties. For the unit battery case, the closed-form expression of the average AoI under the Stationary Randomized Sampling (SRS) policy space is derived, and the optimal policy is proposed by analyzing the KKT conditions. For the arbitrary finite battery size, the closed-form expression of AoI under SRS policy space with infinite battery capacity is first analyzed. Then based on the result, we construct a proper weight function in <PERSON><PERSON><PERSON><PERSON> optimization to develop a policy named Max Energy-Aware Weight (MEAW), which is proved to achieve 2-approximation in the full policy space. Experimental results validate the theoretical results and show that MEAW performs close to the theoretical lower bound and outperforms the state-of-the-art schemes.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228894"}, {"primary_key": "1202980", "vector": [], "sparse_vector": [], "title": "Privacy-preserving Stable Crowdsensing Data Trading for Unknown Market.", "authors": ["He Sun", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "As a new paradigm of data trading, Crowdsensing Data Trading (CDT) has attracted widespread attention in recent years, where data collection tasks of buyers are crowdsourced to a group of mobile users as sellers through a platform as a broker for long-term data trading. The stability of the matching between buyers and sellers in the data trading market is one of the most important CDT issues. In this paper, we focus on the privacy-preserving stable CDT issue with unknown preference sequences of buyers. Our goal is to maximize the accumulative data quality for each task while protecting the data qualities of sellers and ensuring the stability of the CDT market. We model such privacy-preserving stable CDT issue with unknown preference sequences as a differentially private competing multi-player multi-armed bandit problem. We define a novel metric δ-stability and propose a privacy-preserving stable CDT mechanism based on differential privacy, stable matching theory, and competing bandit strategy, called DPS-CB, to solve this problem. Finally, we prove the security and the stability of the CDT market under the effect of privacy concerns and analyze the regret performance of DPS-CB. Also, the performance is demonstrated on a real-world dataset.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228966"}, {"primary_key": "1202984", "vector": [], "sparse_vector": [], "title": "Balancing Repair Bandwidth and Sub-Packetization in Erasure-Coded Storage via Elastic Transformation.", "authors": ["Kaicheng Tang", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Yuchong Hu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Erasure coding provides high fault-tolerant storage with significantly low redundancy overhead, at the expense of high repair bandwidth. While there exist access-optimal codes that theoretically minimize both the repair bandwidth and the amount of disk reads, they also incur a high sub-packetization level, thereby leading to non-sequential I/Os and degrading repair performance. We propose elastic transformation, a framework that transforms any base code into a new code with smaller repair bandwidth for all or a subset of nodes, such that it can be configured with a wide range of sub-packetization levels to limit the non-sequential I/O overhead. We prove the fault tolerance of elastic transformation and model numerically the repair performance with respect to a sub-packetization level. We further prototype and evaluate elastic transformation atop HDFS, and show how it reduces the single-block repair time of the base codes and access-optimal codes in a real network setting.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228984"}, {"primary_key": "1202985", "vector": [], "sparse_vector": [], "title": "Tackling System Induced Bias in Federated Learning: Stratification and Convergence Analysis.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In federated learning, clients cooperatively train a global model by training local models over their datasets under the coordination of a central server. However, clients may sometimes be unavailable for training due to their network connections and energy levels. Considering the highly non-independent and identically distributed (non-IID) degree of the clients’ datasets, the local models of the available clients being sampled for training may not represent those of all other clients. This is referred as system induced bias. In this work, we quantify the system induced bias due to time-varying client availability. The theoretical result shows that this bias occurs independently of the number of available clients and the number of clients being sampled in each training round. To address system induced bias, we propose a FedSS algorithm by incorporating stratified sampling and prove that the proposed algorithm is unbiased. We quantify the impact of system parameters on the algorithm performance and derive the performance guarantee of our proposed FedSS algorithm. Theoretical and experimental results on CIFAR-10 and MNIST datasets show that our proposed FedSS algorithm outperforms several benchmark algorithms by up to 5.1 times in terms of the algorithm convergence rate.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228873"}, {"primary_key": "1202986", "vector": [], "sparse_vector": [], "title": "Scalable RDMA Transport with Efficient Connection Sharing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Huichen Dai"], "summary": "RDMA provides extremely low-latency and high- throughput data transmission as its protocol stack is entirely offloaded into the RDMA NIC. However, the increasing scale of RDMA networks requires hosts to establish a large number of connections, e.g., process-level full mesh, which easily overwhelms the limited resource on RNICs and hence significantly degrades performance. This paper presents SRM, a scalable transport mode for RDMA that remarkably alleviates resource exhaustion on RNICs. SRM proposes a kernel-based solution to multiplex workloads from different applications over the same connection. Meanwhile, to preserve RDMA's performance benefits, SRM 1) avoids syscall overhead by sharing the working memory between user-space and kernel; 2) maintains high resource utilization through lock-free approach to avoid contention; 3) adopts multiple optimizations to mitigate the head-of-line blocking issue; 4) implements a rapid recovery mechanism to provide high system robustness. We evaluate SRM using extensive experiments and simulations. Testbed experiments reveal that SRM outperforms existing transports, including DCT, RC, and XRC, by 4x to 20x in latency for all-to-all communication pattern. Simulations of large-scale networks show that, compared with DCT, RC, and XRC, SRM achieves up to 4.42x/4.0x/3.7x speedups respectively in flow completion time while consuming the least memory.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.********"}, {"primary_key": "1202988", "vector": [], "sparse_vector": [], "title": "Resilient Routing Table Computation Based on Connectivity Preserving Graph Sequences.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Fast reroute (FRR) mechanisms that can instantly handle network failures in the data plane are gaining attention in packet-switched networks.In FRR no notification messages are required as the nodes adjacent to the failure are prepared with a routing table such that the packets are re-routed only based on local information.However, designing the routing algorithm for FRR is challenging because the number of possible sets of failed network links and nodes can be extremely high, while the algorithm should keep track of which nodes are aware of the failure.In this paper, we propose a generic algorithmic framework that combines the benefits of Integer Linear Programming (ILP) and an effective approach from graph theory related to constructive graph characterization of k-connected graphs, i.e., edge splittingoff.We illustrate these benefits through arborescence design for FRR and show that (i) due to the ILP we have great flexibility in defining the routing problem, while (ii) the problem can still be solved very fast.We demonstrate through simulations that our framework outperforms state-of-the-art FRR mechanisms and provides better resilience with shorter paths in the arborescences.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.********"}, {"primary_key": "1202989", "vector": [], "sparse_vector": [], "title": "How to Attack and Congest Delay-Sensitive Applications on the Cloud.", "authors": ["Jhonatan <PERSON>", "<PERSON><PERSON>"], "summary": "The delay and service-blocking experienced by users are critical measures of quality of service in real-time distributed systems. Attacks directed at such facilities aim at disrupting the service and hurting these metrics. Our goal is to characterize worst-case attacks on such systems.We use queueing models to study attackers who wish to maximize damage while constrained by attack resources. A key question pertaining to systems design is whether a damage maximizing attack should focus on heavily affecting a small number of facilities or spread its efforts over many facilities.We analyze attacks which damage the system resources (i.e., Capacity attacks) and show that optimal capacity attacks are concentrated. We further use a Max-Min (attacker and defender) analysis where the defender can migrate requests in response to the attack: An intriguing result is that under certain conditions an optimal attack will spread its efforts over many sites. This is in contrast to the attack concentration predictions of (agnostic to queueing delays) prior studies.We also address DDoS (or Flow) attacks where attackers create loads of dummy requests and send them to the system. We prove that concentrating the attack efforts is always the optimal strategy, regardless of whether the system reacts by migrating requests, in contrast to the capacity attacks.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229013"}, {"primary_key": "1202990", "vector": [], "sparse_vector": [], "title": "Congestion Control Safety via Comparative Statics.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "When congestion control algorithms compete on shared links, unfair outcomes can result, especially between algorithms that aim to prioritize different objectives. For example, a throughput-maximizing application could make the link completely unusable for a latency-sensitive application. In order to study these outcomes formally, we model the congestion control problem as a game in which applications have heterogeneous utility functions. We draw on the comparative statics literature in economics to derive simple and practically useful conditions under which all applications achieve at least ε utility at equilibrium, a minimal safety condition for the network to be useful for any application. Compared to prior analyses of similar games, we show that our framework supports a more realistic class of utility functions that includes highly latency-sensitive applications such as teleconferencing and online gaming.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229051"}, {"primary_key": "1202992", "vector": [], "sparse_vector": [], "title": "Minimizing Age of Information for Underwater Optical Wireless Sensor Networks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Bingxian Lu", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In Underwater Optical Wireless Sensor Networks (UOWSNs), the Age of information (AoI) is critical as it manifests information freshness, which is extremely useful for real-time monitoring applications. Prior works extensively focus on improving AoI in single-hop or multi-hop networks with fixed paths. However, the issue of multi-path transmission is overlooked. In this paper, we make the first attempt to address the issue of minimizing AoI through link scheduling in UOWSNs (termed MAKE problem). To minimize AoI, we propose an AoI model and corresponding link constraint model for multi-path and multi-hop UOWSNs. Then, we formalize the MAKE problem into a submodular function maximization problem, and propose a greedy method with an approximation ratio guarantee to solve it. Theoretical analyses are presented to explore the features of our scheme. Finally, to demonstrate the effectiveness of the proposed scheme, extensive simulations are conducted. The results reveal that the proposed scheme has 21.4% lower total average AoI compared with other baseline algorithms. Furthermore, test-bed experiments are carried out to verify the applicability of the proposed scheme in practical applications.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228991"}, {"primary_key": "1202993", "vector": [], "sparse_vector": [], "title": "WSTrack: A Wi-Fi and Sound Fusion System for Device-free Human Tracking.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The voice assistants benefit from the ability to localize users, especially, we can analyze the user’s habits from the historical trajectory to provide better services. However, current voice localization method requires the user to actively issue voice commands, which makes voice assistants unable to track silent users most of the time. This paper presents WSTrack, a Wi-Fi and Sound fusion tracking system for device-free human. In particular, current voice assistants naturally support both Wi-Fi and acoustic functions. Accordingly, we are able to build up the multi-modal prototype with just voice assistants and Wi-Fi routers. To track the movement of silent users, our insights are as follows: (1) the voice assistants can hear the sound of the user’s pace, and then extract which direction the user is in; (2) we can extract the user’s velocity from the Wi-Fi signal. By fusing multi-modal information, we are able to track users with a single voice assistant and Wi-Fi router. Our implementation and evaluation on commodity devices demonstrate that WSTrack achieves better performance than current systems, where the median tracking error is 0.37m.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228898"}, {"primary_key": "1202994", "vector": [], "sparse_vector": [], "title": "Fresh-CSMA: A Distributed Protocol for Minimizing Age of Information.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the design of distributed scheduling algorithms that minimize age of information in single-hop wireless networks. The centralized max-weight policy is known to be nearly optimal in this setting; hence, our goal is to design a distributed CSMA scheme that can mimic its performance. To that end, we propose a distributed protocol called Fresh-CSMA and show that in an idealized setting, Fresh-CSMA can match the scheduling decisions of the max-weight policy with high probability in each frame, and also match the theoretical performance guarantees of the max-weight policy over the entire time horizon. We then consider a more realistic setting and study the impact of protocol parameters on the probability of collisions and the overhead caused by the distributed nature of the protocol. Finally, we provide simulations that support our theoretical results and show that the performance gap between the ideal and realistic versions of Fresh-CSMA is small.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228917"}, {"primary_key": "1202995", "vector": [], "sparse_vector": [], "title": "WiSwarm: Age-of-Information-based Wireless Networking for Collaborative Teams of UAVs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Age-of-Information (AoI) metric has been widely studied in the theoretical communication networks and queuing systems literature. However, experimental evaluation of its applicability to complex real-world time-sensitive systems is largely lacking. In this work, we develop, implement, and evaluate an AoI-based application layer middleware that enables the customization of WiFi networks to the needs of time-sensitive applications. By controlling the storage and flow of information in the underlying WiFi network, our middleware can: (i) prevent packet collisions; (ii) discard stale packets that are no longer useful; and (iii) dynamically prioritize the transmission of the most relevant information. To demonstrate the benefits of our middleware, we implement a mobility tracking application using a swarm of UAVs communicating with a central controller via WiFi. Our experimental results show that, when compared to WiFi-UDP/WiFi-TCP, the middleware can improve information freshness by a factor of 109x/48x and tracking accuracy by a factor of 4x/6x, respectively. Most importantly, our results also show that the performance gains of our approach increase as the system scales and/or the traffic load increases.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228860"}, {"primary_key": "1202996", "vector": [], "sparse_vector": [], "title": "DeepFT: Fault-Tolerant Edge Computing using a Self-Supervised Deep Surrogate Model.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The emergence of latency-critical AI applications has been supported by the evolution of the edge computing paradigm. However, edge solutions are typically resource-constrained, posing reliability challenges due to heightened contention for compute capacities and faulty application behavior in the presence of overload conditions. Although a large amount of generated log data can be mined for fault prediction, labeling this data for training is a manual process and thus a limiting factor for automation. Due to this, many companies resort to unsupervised fault-tolerance models. Yet, failure models of this kind can incur a loss of accuracy when they need to adapt to non-stationary workloads and diverse host characteristics. Thus, we propose a novel modeling approach, DeepFT, to proactively avoid system overloads and their adverse effects by optimizing the task scheduling decisions. DeepFT uses a deep-surrogate model to accurately predict and diagnose faults in the system and co-simulation based self-supervised learning to dynamically adapt the model in volatile settings. Experimentation on an edge cluster shows that DeepFT can outperform state-of-the-art methods in fault-detection and QoS metrics. Specifically, DeepFT gives the highest F1 scores for fault-detection, reducing service deadline violations by up to 37% while also improving response time by up to 9%.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229049"}, {"primary_key": "1203005", "vector": [], "sparse_vector": [], "title": "Extracting Spatial Information of IoT Device Events for Smart Home Safety Monitoring.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Smart home IoT devices have been widely deployed and connected to many home networks for various applications such as intelligent home automation, connected healthcare, and security surveillance. The network traffic traces generated by IoT devices have enabled recent research advances in smart home network measurement. However, due to the cloud-based communication model of smart home IoT devices and the lack of traffic data collected at the cloud end, little effort has been devoted to extracting the spatial information of IoT device events to determine where a device event is triggered. In this paper, we examine why extracting IoT device events’ spatial information is challenging by analyzing the communication model of the smart home IoT system. We propose a system named IoTDuet for determining whether a device event is triggered locally or remotely by utilizing the fact that the controlling devices such as smartphones and tablets always communicate with cloud servers with relatively stable domain name information when issuing commands from the home network. We further show the importance of extracting spatial information of IoT device events by exploring its applications in smart home safety monitoring.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228993"}, {"primary_key": "1203006", "vector": [], "sparse_vector": [], "title": "Neural Constrained Combinatorial Bandits.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Constrained combinatorial contextual bandits have emerged as trending tools in intelligent systems and networks to model reward and cost signals under combinatorial decision-making. On one hand, both signals are complex functions of the context, e.g., in federated learning, training loss (negative reward) and energy consumption (cost) are nonlinear functions of edge devices' system conditions (context). On the other hand, there are cumulative constraints on costs, e.g., the accumulated energy consumption should be budgeted by energy resources. Besides, real-time systems often require such constraints to be guaranteed anytime or in each round, e.g., ensuring anytime fairness for task assignment to maintain the credibility of crowdsourcing platforms for workers. This setting imposes a challenge on how to simultaneously achieve reward maximization while subjecting to anytime cumulative constraints. To address such challenge, we propose a primal-dual algorithm (Neural-PD) whose primal component adopts multi-layer perceptrons to estimate reward and cost functions, and its dual component estimates the Lagrange multiplier with the virtual queue. By integrating neural tangent kernel theory and Lyapunov-drift techniques, we prove Neural-PD achieves a sharp regret bound and a zero constraint violation. We also show Neural-PD outperforms existing algorithms with extensive experiments on both synthetic and real-world datasets.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228958"}, {"primary_key": "1203007", "vector": [], "sparse_vector": [], "title": "FedMoS: Taming Client Drift in Federated Learning with Double Momentum and Adaptive Selection.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Yuqing Li", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Federated learning (FL) enables massive clients to collaboratively train a global model by aggregating their local updates without disclosing raw data. Communication has become one of the main bottlenecks that prolongs the training process, especially under large model variances due to skewed data distributions. Existing efforts mainly focus on either single momentum-based gradient descent, or random client selection for potential variance reduction, yet both often lead to poor model accuracy and system efficiency. In this paper, we propose FedMoS, a communication-efficient FL framework with coupled double momentum-based update and adaptive client selection, to jointly mitigate the intrinsic variance. Specifically, FedMoS maintains customized momentum buffers on both server and client sides, which track global and local update directions to alleviate the model discrepancy. Taking momentum results as input, we design an adaptive selection scheme to provide a proper client representation during FL aggregation. By optimally calibrating clients’ selection probabilities, we can effectively reduce the sampling variance, while ensuring unbiased aggregation. Through a rigid analysis, we show that FedMoS can attain the theoretically optimal ${{\\mathcal{O}}}\\left({{T^{ - 2/3}}}\\right)$ convergence rate. Extensive experiments using real-world datasets further validate the superiority of FedMoS, with 58%-87% communication reduction for achieving the same target performance compared to state-of-the-art techniques.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228957"}, {"primary_key": "1203008", "vector": [], "sparse_vector": [], "title": "More than Enough is Too Much: Adaptive Defenses against Gradient Leakage in Production Federated Learning.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With increasing concerns on privacy leakage from gradients, a variety of attack mechanisms emerged to recover private data from gradients at an honest-but-curious server, which challenged the primary advantage of privacy protection in federated learning. However, we cast doubt upon the real impact of these gradient attacks on production federated learning systems. By taking away several impractical assumptions that the literature has made, we find that gradient attacks pose a limited degree of threat to the privacy of raw data.Through a comprehensive evaluation on existing gradient attacks in a federated learning system with practical assumptions, we have systematically analyzed their effectiveness under a wide range of configurations. We present key priors required to make the attack possible or stronger, such as a narrow distribution of initial model weights, as well as inversion at early stages of training. We then propose a new lightweight defense mechanism that provides sufficient and self-adaptive protection against time-varying levels of the privacy leakage risk throughout the federated learning process. As a variation of gradient perturbation method, our proposed defense, called Outpost, selectively adds Gaussian noise to gradients at each update iteration according to the Fisher information matrix, where the level of noise is determined by the privacy leakage risk quantified by the spread of model weights at each layer. To limit the computation overhead and training performance degradation, Outpost only performs perturbation with iteration-based decay. Our experimental results demonstrate that Outpost can achieve a much better tradeoff than the state-of-the-art with respect to convergence performance, computational overhead, and protection against gradient attacks.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228919"}, {"primary_key": "1203009", "vector": [], "sparse_vector": [], "title": "Toward Sustainable AI: Federated Learning Demand Response in Cloud-Edge Systems via Auctions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhu", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cloud-edge systems are important Emergency Demand Response (EDR) participants that help maintain power grid stability and demand-supply balance. However, as users are increasingly executing artificial intelligence (AI) workloads in cloud-edge systems, existing EDR management has not been designed for AI workloads and thus faces the critical challenges of the complex trade-offs between energy consumption and AI model accuracy, the degradation of model accuracy due to AI model quantization, the restriction of AI training deadlines, and the uncertainty of AI task arrivals. In this paper, targeting Federated Learning (FL), we design an auction-based approach to overcome all these challenges. We firstly formulate a nonlinear mixed-integer program for the long-term social welfare optimization. We then propose a novel algorithmic approach that generates candidate training schedules, reformulates the original problem into a new schedule selection problem, and solves this new problem using an online primal-dual-based algorithm, with a carefully embedded payment design. We further rigorously prove that our approach achieves truthfulness and individual rationality, and leads to a constant competitive ratio for the long-term social welfare. Via extensive evaluations with real-world data and settings, we have validated the superior practical performance of our approach over multiple alternative methods.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229014"}, {"primary_key": "1203010", "vector": [], "sparse_vector": [], "title": "LigBee: Symbol-Level Cross-Technology Communication from LoRa to ZigBee.", "authors": ["<PERSON><PERSON>", "Linghe Kong", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Low-power wide-area networks (LPWAN) evolve rapidly with advanced communication primitives (e.g., coding, modulation) being continuously invented. This rapid iteration on LPWAN, however, forms a communication barrier between legacy wireless sensor nodes deployed years ago (e.g., ZigBee-based sensor node) with their latest competitor running a different communication protocol (e.g., LoRa-based IoT node): they work on the same frequency band but share different MAC- and PHY-layer regulations and thus cannot talk to each other directly. To break this barrier, we propose LigBee, a cross-technology communication (CTC) solution that enables symbol-level communication from the latest LPWAN LoRa node to legacy ZIGBEE node. We have implemented LigBee on both software-defined radios and commercial-off-the-shelf (COTS) LoRa and ZigBee nodes, and demonstrated that LigBee builds a reliable CTC link from LoRa node to ZigBee node on both platforms. Our experimental results show that i) LigBee achieves a bit error rate (BER) in the order of 10 −3 with 70 ∼ 80% frame reception ratio (FRR), ii) the range of LigBee link is over 300m, which is 6 ∼ 7.5× the typical range of legacy ZigBee and state-of-the-art solution, and iii) the throughput of LigBee link is maintained on the order of kbps, which is close to the LoRa's throughput.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229005"}, {"primary_key": "1203011", "vector": [], "sparse_vector": [], "title": "Online Distributed Optimization with Efficient Communication via Temporal Similarity.", "authors": ["Jun<PERSON> Wang", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider online distributed optimization in a networked system, where multiple devices assisted by a server collaboratively minimize the accumulation of a sequence of global loss functions that can vary over time. To reduce the amount of communication, the devices send quantized and compressed local decisions to the server, resulting in noisy global decisions. Therefore, there exists a tradeoff between the optimization performance and the communication overhead. Existing works separately optimize computation and communication. In contrast, we jointly consider computation and communication over time, by encouraging temporal similarity in the decision sequence to control the communication overhead. We propose an efficient algorithm, termed Online Distributed Optimization with Temporal Similarity (ODOTS), where the local decisions are both computation- and communication-aware. Furthermore, ODOTS uses a novel tunable virtual queue, which completely removes the commonly assumed <PERSON>'s condition through a modified Lyapunov drift analysis. ODOTS delivers provable performance bounds on both the optimization objective and constraint violation. As an example application, we apply ODOTS to enable communication-efficient federated learning. Our experimental results based on real-world image classification demonstrate that ODOTS obtains higher classification accuracy and lower communication overhead compared with the current best alternatives for both convex and non-convex loss functions.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229086"}, {"primary_key": "1203013", "vector": [], "sparse_vector": [], "title": "A Comprehensive and Long-term Evaluation of Tor V3 Onion Services.", "authors": ["Chun<PERSON>", "Junzhou Luo", "<PERSON><PERSON>", "Lan <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The version 3 (V3) Tor onion service protocol deeply hides onion service domain names to improve anonymity. Existing onion service analysis methods cannot be used any more to understand V3 onion services and the ecosystem such as benign services, abuses and black markets. To understand the scale of V3 onion services, we theoretically analyze the V3 onion service mechanism and propose an accurate onion service size estimation method, which is able to achieve an estimation deviation of 2.43% on a large-scale emulated Tor network. To understand onion website popularity, we build a system and collect more than two years of data of public onion websites. We develop an onion service popularity estimation algorithm using online rate and access rate to rank the onion services. To reduce the noise from the phishing websites, we cluster onion websites into groups based on the content and structure. To our surprise, we only find 487 core websites out of the collected 45,889 public onion websites. We further analyze the weighted popularity of each group using yellow page data and discover that 35,331 phishing onion websites spoof the 487 core websites.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229057"}, {"primary_key": "1203014", "vector": [], "sparse_vector": [], "title": "Spatiotemporal Transformer for Data Inference and Long Prediction in Sparse Mobile CrowdSensing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Chaocan <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mobile CrowdSensing (MCS) is a data sensing paradigm that recruits users carrying mobile terminals to collect data. As its variant, Sparse MCS has been further proposed for large-scale and fine-grained sensing task with the advantage of collecting only a few data to infer unsensed data. However, in many real-world scenarios, such as early prevention of epidemic, people are interested in not only the data at the current, but also in the future or even long-term future, and the latter may be more important. Long-term prediction not only reduces sensing cost, but also identifies trends or other characteristics of the data. In this paper, we propose a spatiotemporal model based on Transformer to infer and predict the data with sparse sensed data by utilizing spatiotemporal relationships. We design a spatiotemporal feature embedding to embed the prior spatiotemporal information of sensing map into the model to guide model learning. Moreover, we also design a novel multi-head spatiotemporal attention mechanism to dynamically capture spatiotemporal relationships among data. Extensive experiments have been conducted on three types of typical urban sensing tasks, which verify the effectiveness of our proposed algorithms in improving the inference and long-term prediction accuracy with the sparse sensed data.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228982"}, {"primary_key": "1203015", "vector": [], "sparse_vector": [], "title": "SVDFed: Enabling Communication-Efficient Federated Learning via Singular-Value-Decomposition.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Federated learning (FL) is an emerging paradigm of distributed machine learning. However, when applied to wireless network scenarios, FL usually suffers from high communication cost because clients need to transmit their updated gradients to a server in every training round. Although many gradient compression techniques like sparsification and quantization are proposed, they compress clients' gradients independently, without considering the correlations among gradients. In this paper, we propose SVDFed, a collaborative gradient compression framework for FL. SVDFed utilizes Singular Value Decomposition (SVD) to find a few basis vectors, whose linear combination can well represent clients' gradients at a certain round. Due to the correlations among gradients, these basis vectors can still well approximate new gradients in many subsequent rounds. With the help of basis vectors, clients only need to upload the coefficients of the linear combination to the server, which greatly reduces communication cost. In addition, SVDFed leverages the classical PID (Proportional, Integral, Derivative) control to determine the proper time to update basis vectors to maintain their representation ability. Through experiments, we demonstrate that SVDFed outperforms existing gradient compression methods in FL. For example, compared to a popular gradient quantization method QSGD, SVDFed can reduce the communication overhead by 66 % and pending time by 99 %.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229042"}, {"primary_key": "1203016", "vector": [], "sparse_vector": [], "title": "The Power of Age-based Reward in Fresh Information Acquisition.", "authors": ["<PERSON><PERSON><PERSON>", "Qingkai Meng", "<PERSON>", "<PERSON><PERSON>"], "summary": "Many Internet platforms collect fresh information of various points of interest (PoIs) relying on users who happen to be nearby the PoIs. The platform will offer reward to incentivize users and compensate their costs incurred from information acquisition. In practice, the user cost (and its distribution) is hidden to the platform, thus it is challenging to determine the optimal reward. In this paper, we investigate how the platform dynamically rewards the users, aiming to jointly reduce the age of information (AoI) and the operational expenditure (OpEx). Due to the hidden cost distribution, this is an online non-convex learning problem with partial feedback. To overcome the challenge, we first design an age-based rewarding scheme, which decouples the OpEx from the unknown cost distribution and enables the platform to accurately control its OpEx. We then take advantage of the age-based rewarding scheme and propose an exponentially discretizing and learning (EDAL) policy for platform operation. We prove that the EDAL policy performs asymptotically as well as the optimal decision (derived based on the cost distribution). Simulation results show that the age-based rewarding scheme protects the platform's OpEx from the influence of the user characteristics, and verify the asymptotic optimality of the EDAL policy.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229008"}, {"primary_key": "1203017", "vector": [], "sparse_vector": [], "title": "Federated Learning with Flexible Control.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Federated learning (FL) enables distributed model training from local data collected by users. In distributed systems with constrained resources and potentially high dynamics, e.g., mobile edge networks, the efficiency of FL is an important problem. Existing works have separately considered different configurations to make FL more efficient, such as infrequent transmission of model updates, client subsampling, and compression of update vectors. However, an important open problem is how to jointly apply and tune these control knobs in a single FL algorithm, to achieve the best performance by allowing a high degree of freedom in control decisions. In this paper, we address this problem and propose FlexFL – an FL algorithm with multiple options that can be adjusted flexibly. Our FlexFL algorithm allows both arbitrary rates of local computation at clients and arbitrary amounts of communication between clients and the server, making both the computation and communication resource consumption adjustable. We prove a convergence upper bound of this algorithm. Based on this result, we further propose a stochastic optimization formulation and algorithm to determine the control decisions that (approximately) minimize the convergence bound, while conforming to constraints related to resource consumption. The advantage of our approach is also verified using experiments.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229070"}, {"primary_key": "1203018", "vector": [], "sparse_vector": [], "title": "One Pass is Sufficient: A Solver for Minimizing Data Delivery Time over Time-varying Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "How to allocate network paths and their resources to minimize the delivery time of data transfer tasks over time-varying networks? Solving this MDDT (Minimizing Data Delivery Time) problem has important applications from data centers to delay-tolerant networking. In particular, with the rapid deployment of satellite networks in recent years, an efficient MDDT solver will serve as a key building block there.The MDDT problem can be solved in polynomial time by finding the maximum flow in a time-expanded graph. A binary-search-based solver incurs O(N•log N•Γ) time complexity, where N corresponds to time horizon and Γ is the time complexity to solve a maximum flow problem for one snapshot of the network. In this work, we design a one-pass solver that progressively expands the graph over time until it reaches the earliest time interval n to complete the delivery. By reusing the calculated maximum flow results from earlier iterations, it solves the MDDT problem while incurring only O(nΓ) time complexity for algorithms that can apply our technique. We apply the one-pass design to <PERSON><PERSON><PERSON><PERSON> algorithm and evaluate our solver using a network of 184 satellites from Starlink constellations. We demonstrate >75× speed-up in the running time and show that our solution can also enable advanced applications such as preemptive scheduling.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228959"}, {"primary_key": "1203020", "vector": [], "sparse_vector": [], "title": "Marginal Value-Based Edge Resource Pricing and Allocation for Deadline-Sensitive Tasks.", "authors": ["<PERSON><PERSON><PERSON>", "Zhouxing Sun", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In edge computing (EC), resource allocation is to allocate computing, storage and networking resources on the edge nodes (ENs) efficiently and reasonably to tasks generated by users. Due to the resource-limitation of ENs, the tasks often need to compete for the resources. Pricing mechanisms are widely used to deal with the resource allocation problem, and the valuations of tasks play a critical role in the price mechanisms. However, users naturally are not willing to expose the valuations of their tasks due to conflicts of interests. Current research works usually adopt truthful auctions to motivate the users to report honestly the valuations of their tasks. In this paper, we introduce the marginal value to estimate the valuations of tasks, and propose a marginal value-based pricing mechanism using the incentive theory, which motivates the tasks with higher marginal values to actively request more resources. The EC platform sets the resource prices using the price mechanism, and then the users determine their resource requests relying on the resource prices and the valuations of their tasks. After receiving the deadline-sensitive tasks from the users, the resource allocation can be modeled as a knapsack problem with the deadline constraints. Extensive experimental results demonstrate that our approach is computationally efficient and is promising in enhancing the utility of the EC platform and the tasks.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228866"}, {"primary_key": "1203022", "vector": [], "sparse_vector": [], "title": "AOCC-FL: Federated Learning with Aligned Overlapping via Calibrated Compensation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Fan", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Federated Learning enables collaboratively model training among a number of distributed devices with the coordination of a centralized server, where each device alternatively performs local gradient computation and communication to the server. FL suffers from significant performance degradation due to the excessive communication delay between the server and devices, especially when the network bandwidth of these devices is limited, which is common in edge environments. Existing methods overlap the gradient computation and communication to hide the communication latency to accelerate the FL training. However, the overlapping can also lead to an inevitable gap between the local model in each device and the global model in the server that seriously restricts the convergence rate of learning process. To address this problem, we propose a new overlapping method for FL, AOCC-FL, which aligns the local model with the global model via calibrated compensation such that the communication delay can be hidden without deteriorating the convergence performance. Theoretically, we prove that AOCC-FL admits the same convergence rate as the non-overlapping method. On both simulated and testbed experiments, we show that AOCC-FL achieves a comparable convergence rate relative to the non-overlapping method while outperforming the state-of-the-art overlapping methods.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229011"}, {"primary_key": "1203023", "vector": [], "sparse_vector": [], "title": "FacER: Contrastive Attention based Expression Recognition via Smartphone Earpiece Speaker.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Wang", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Facial expression recognition has enormous potential for downstream applications by revealing users' emotional status when interacting with digital content. Previous studies consider using cameras or wearable sensors for expression recognition. However, these approaches bring considerable privacy concerns or extra device burdens. Moreover, the recognition performance of camera-based methods deteriorates when users are wearing masks. In this paper, we propose FacER, an active acoustic facial expression recognition system. As a software solution on a smartphone, FacER avoids the extra costs of external microphone arrays. Facial expression features are extracted by modeling the echoes of emitted near-ultrasound signals between the earpiece speaker and the 3D facial contour. Besides isolating a range of background noises, FacER is designed to identify different expressions from various users with a limited set of training data. To achieve this, we propose a contrastive external attention-based model to learn consistent expression features across different users. Extensive experiments with 20 volunteers with or without masks show that FacER can recognize 6 common facial expressions with more than 85% accuracy, outperforming the state-of-the-art acoustic sensing approach by 10% in various real-life scenarios. FacER provides a more robust solution for recognizing facial expressions in a convenient and usable manner.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228985"}, {"primary_key": "1203024", "vector": [], "sparse_vector": [], "title": "TVFL: Tunable Vertical Federated Learning towards Communication-Efficient Model Serving.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Vertical federated learning (VFL) enables multiple participants with different data features and the same sample ID space to collaboratively train a model in a privacy-preserving way. However, the high computational and communication overheads hinder the adoption of VFL in many resource-limited or delay-sensitive applications. In this work, we focus on reducing the communication cost and delay incurred by the transmission of intermediate results in VFL model serving. We investigate the inference results, and find that a large portion of test samples can be predicted correctly by the active party alone, thus the corresponding communication for federated inference is dispensable. Based on this insight, we theoretically analyze the \"dispensable communication\" and propose a novel tunable vertical federated learning framework, named TVFL, to avoid \"dispensable communication\" in model serving as much as possible. TVFL can smartly switch between independent inference and federated inference based on the features of the input sample. We further reveal that such tunability is highly related to the importance of participants’ features. Our evaluations on seven datasets and three typical VFL models show that TVFL can save 57.6% communication cost and reduce 57.1% prediction latency with little performance degradation.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229061"}, {"primary_key": "1203028", "vector": [], "sparse_vector": [], "title": "DUNE: Improving Accuracy for Sketch-INT Network Measurement Systems.", "authors": ["<PERSON><PERSON><PERSON>ng Wei", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In-band Network Telemetry (INT) and sketching algorithms are two promising directions for measuring network traffic in real time. To combine sketch with INT and preserve their advantages, a representative approach is to use INT to send a switch sketch in small pieces (called sketchlets) to end-host for reconstructing an identical sketch. However, in this paper, we show that when naively selecting buckets to add to sketchlets, the end-host reconstructed sketch is inaccurate. To overcome this problem, we present DUNE, an innovative sketch-INT network measurement system. DUNE incorporates two key innovations: First, we design a novel scatter sketchlet that is more efficient in transferring measurement data by allowing a switch to select individual buckets to add to sketchlets; Second, we propose lightweight data structures for tracing \"freshness\" of the sketch buckets, and present algorithms for smartly selecting buckets that contain valuable measurement data to send to end-host. We theoretically prove the effectiveness of our proposed methods, and implement a prototype on commodity programmable switch. Results from extensive experiments driven by real-world traffic suggest that DUNE can substantially improve the measurement accuracy at a trivial cost.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229098"}, {"primary_key": "1203029", "vector": [], "sparse_vector": [], "title": "DTrust: Toward Dynamic Trust Levels Assessment in Time-Varying Online Social Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Yanzhi Ren", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The social trust assessment can spur extensive applications such as social recommendations, shopping, financial investment strategies, etc, but remain a challenging problem having limited exploration. Such explorations mainly limit their studies to static network topology or simplified dynamic networks, toward the social trust relationship prediction. In contrast, in this paper, we explore the social trust by taking into account the time-varying online social networks whereas the social trust relationship may vary over time. The DTrust, a dynamic graph neural network-based solution, will be proposed for accurate social trust prediction. In particular, DTrust is composed of a static aggregation unit and a dynamic unit, respectively responsible for capturing both the spatial dependence features and temporal dependence features. In the former unit, we stack multiple NNConv layers derived from the edge-conditioned convolution network for capturing the spatial dependence features correlated to the network topology and the observed social relationships. In the latter unit, a gated recurrent unit (GRU) is employed for learning the evolution law of social interaction and social trust relationships. Based on the extracted spatial and temporal features, we then employ a fully connected neural network for learning, able to predict the social trust relationships for both current and future time slots. Extensive experimental results exhibit that our DTrust can outperform the benchmark counterparts on two real-world datasets.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228962"}, {"primary_key": "1203030", "vector": [], "sparse_vector": [], "title": "MIA: A Transport-Layer Plugin for Immersive Applications in Millimeter Wave Access Networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The highly directional nature of the millimeter wave (mmWave) beams pose several challenges in using that spectrum for meeting the communication needs of immersive applications. In particular, the mmWave beams are susceptible to misalignments and blockages caused by user movements. As a result, mmWave channels are vulnerable to large fluctuations in quality, which in turn, cause disproportionate degradation in end-to-end performance of Transmission Control Protocol (TCP) based applications. In this paper, we propose a reinforcement learning (RL) integrated transport-layer plugin, Millimeter wave based Immersive Agent (MIA), for immersive content delivery over the mmWave link. MIA uses the RL model to predict mmWave link bandwidth based on the real-time measurement. Then, MIA cooperates with TCP's congestion control scheme to adapt the sending rate in accordance with the predictions of the mmWave bandwidth. To evaluate the effectiveness of the proposed MIA, we conduct experiments using a mmWave augmented immersive testbed and network simulations. The evaluation results show that MIA improves end-to-end immersive performance significantly on both throughput and latency.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228878"}, {"primary_key": "1203031", "vector": [], "sparse_vector": [], "title": "Joint Edge Aggregation and Association for Cost-Efficient Multi-Cell Federated Learning.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>peng Dai", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Federated learning (FL) has been proposed as a promising distributed learning paradigm to realize edge artificial intelligence (AI) without revealing the raw data.Nevertheless, it would incur inevitable costs in terms of training latency and energy consumption, due to periodical communication between user equipments (UEs) and the geographically remote central parameter server.Thus motivated, we study the joint edge aggregation and association problem to minimize the total cost, where the model aggregation over multiple cells just happens at the network edge.After proving its hardness with complex coupled variables, we transform it into a set function optimization problem and prove the objective function is neither submodular nor supermodular, which further complicates the problem.To tackle this difficulty, we first split it into multiple edge association subproblems, where the optimal solution to the computation resource allocation can be efficiently obtained in the closed form.We then construct a substitute function with the supermodularity and provable upper bound.On this basis, we reformulate an equivalent set function minimization problem under a matroid base constraint.We then propose an approximation algorithm to the original problem based on the two-stage search strategy with theoretical performance guarantee.Both extensive simulations and field experiments are conducted to validate the effectiveness of our proposed solution.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229060"}, {"primary_key": "1203032", "vector": [], "sparse_vector": [], "title": "FlyTracker: Motion Tracking and Obstacle Detection for Drones Using Event Cameras.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Danyang Li", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Location awareness in environments is one of the key parts for drones' applications and have been explored through various visual sensors. However, standard cameras easily suffer from motion blur under high moving speeds and low-quality image under poor illumination, which brings challenges for drones to perform motion tracking. Recently, a kind of bio-inspired sensors called event cameras emerge, offering advantages like high temporal resolution, high dynamic range and low latency, which motivate us to explore their potential to perform motion tracking in limited scenarios. In this paper, we propose FlyTracker, aiming at developing visual sensing ability for drones of both individual and circumambient location-relevant contextual, by using a monocular event camera. In FlyTracker, background-subtraction-based method is proposed to distinguish moving objects from background and fusion-based photometric features are carefully designed to obtain motion information. Through multilevel fusion of events and images, which are heterogeneous visual data, FlyTracker can effectively and reliably track the 6-DoF pose of the drone as well as monitor relative positions of moving obstacles. We evaluate performance of FlyTracker in different environments and the results show that FlyTracker is more accurate than the state-of-the-art baselines.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228976"}, {"primary_key": "1203033", "vector": [], "sparse_vector": [], "title": "Parallel Cross-technology Transmission from IEEE 802.11ax to Heterogeneous IoT Devices.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Huadong Ma"], "summary": "Cross-Technology Communication (CTC) is an emerging technique that enables direct interconnection among incompatible wireless technologies. However, for the downlink from WiFi to multiple IoT technologies, serially emulating and transmitting the data of each IoT technology has extremely low spectrum efficiency. Recent parallel CTC uses IEEE 802.11g to send emulated ZigBee signal and let the BLE receiver decodes its data from the emulated ZigBee signal with a dedicated codebook. It still has a low spectrum efficiency because IEEE 802.11g exclusively uses the whole channel. Besides, the codebook design hinders the reception on commodity BLE devices. In this paper, we propose WiCast, a parallel CTC that uses IEEE 802.11ax to emulate a composite signal that can be received by commodity BLE, ZigBee, and LoRa devices. By taking advantage of OFDMA in 802.11ax, WiCast uses a single Resource Unit (RU) for parallel CTC and sets other RUs free for high-rate WiFi users. But such a sophisticated composite signal is very easily distorted by emulation imperfections, dynamic channel noises, cyclic prefix, and center frequency offset. We propose a CTC link model that jointly models the emulation errors and channel distortions. Then we carve the emulated signal with elaborate compensations in both time and frequency domains to solve the above distortion problem. We implement a prototype of WiCast on the USRP platform and commodity devices. The extensive experiments demonstrate WiCast can achieve an efficient parallel transmission with the aggregated goodput up to 390.24kbps.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229073"}, {"primary_key": "1203035", "vector": [], "sparse_vector": [], "title": "AoI-aware Incentive Mechanism for Mobile Crowdsensing using Stackelberg Game.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Mobile CrowdSensing (MCS) is a mobile computing paradigm, through which a platform can coordinate a crowd of workers to accomplish large-scale data collection tasks using their mobile devices. Information freshness has attracted much focus on MCS research worldwide. In this paper, we investigate the incentive mechanism design in MCS systems that take the freshness of collected data and social benefits into concerns. First, we introduce the Age of Information (AoI) metric to measure the freshness of data. Then, we model the incentive mechanism design with AoI guarantees as a novel incomplete information two-stage Stackelberg game with multiple constraints. Next, we derive the optimal strategies of this game so as to determine the optimal reward paid by the platform and the optimal data update frequency for each worker. Moreover, we prove that these optimal strategies form a unique Stackelberg equilibrium. Based on the optimal strategies, we propose an AoI-Aware Incentive (AIAI) mechanism for the MCS system, whereby the platform and all workers can maximize their utilities simultaneously. Meanwhile, the system can ensure that the AoI values of all data uploaded to the platform are not larger than a given threshold to achieve high data freshness. Extensive simulations on real-world traces are conducted to demonstrate the significant performance of AIAI.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229079"}, {"primary_key": "1203036", "vector": [], "sparse_vector": [], "title": "From Ember to Blaze: Swift Interactive Video Adaptation via Meta-Reinforcement Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>uan Yan", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Maximizing quality of experience (QoE) for interactive video streaming has been a long-standing challenge, as its delay-sensitive nature makes it more vulnerable to bandwidth fluctuations. While reinforcement learning (RL) has demonstrated great potential, existing works are either limited by fixed models or require enormous data/time for online adaptation, which struggle to fit time-varying and diverse network states. Driven by these practical concerns, we perform large-scale measurements on WeChat for Business's interactive video service to study real-world network fluctuations. Surprisingly, our analysis shows that, compared to time-varying network metrics, network sequences exhibit noticeable short-term continuity, sufficient for few-shot learning requirement. We thus propose <PERSON>ammetta, the first meta-RL-based bitrate adaptation algorithm for interactive video streaming. Building on the short-term continuity, <PERSON><PERSON><PERSON><PERSON> accumulates learning experiences through offline meta-training and enables fast online adaptation to changing network states through few gradient updates. Moreover, Fiammetta innovatively incorporates a probing mechanism for real-time monitoring of network states, and proposes an adaptive meta-testing mechanism for seamless adaptation. We implement Fiammetta on a testbed whose end-to-end network follows the real-world WeChat for Business traces. The results show that <PERSON><PERSON>mett<PERSON> outperforms prior algorithms significantly, improving video bitrate by 3.6%-16.2% without increasing stalling rate.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228909"}, {"primary_key": "1203037", "vector": [], "sparse_vector": [], "title": "A Decentralized Truth Discovery Approach to the Blockchain Oracle Problem.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "When a blockchain application runs on data from the real world, it relies on an oracle mechanism that transports data from external sources to the blockchain. The blockchain oracle problem arises around the need to procure trustworthy data from external sources. Previous works have addressed data authenticity/integrity by building a secure channel between blockchain and external sources while employing a decentralized oracle network to avoid a single point of failure. However, the truthful data challenge, which emerges when legitimate external sources submit fraudulent or deceitful data, remains unsolved. In this paper, we introduce a new decentralized truth-discovering oracle architecture called DecenTruth to address the truthful data challenge using a data-centric approach. DecenTruth aims to elevate the \"truthfulness\" of external data input by enabling decentralized oracle nodes to discover and reach consensus on truthful values of common data objects from multi-sourced inputs in an off-chain manner. It harmonizes techniques in both the data plane and consensus plane—truth discovery (TD) and asynchronous BFT consensus—and enables nodes to finalize the same estimated truths on data objects with high accuracy, amid the harsh asynchronous network condition and presence of Byzantine sources and nodes. We implemented DecenTruth and evaluated its performance in a simulated oracle service scenario. The results demonstrate significantly higher Byzantine resilience and long-term data feed accuracy of DecenTruth, compared to existing median-based aggregation methods.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229019"}, {"primary_key": "1203038", "vector": [], "sparse_vector": [], "title": "Universal Targeted Adversarial Attacks Against mmWave-based Human Activity Recognition.", "authors": ["Yucheng Xie", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Human activity recognition (HAR) systems based on millimeter wave (mmWave) technology have evolved in recent years due to their better privacy protection and enhanced sensor resolution. With the ever-growing HAR system deployment, the vulnerability of such systems has been revealed. However, existing efforts in HAR adversarial attacks only focus on untargeted attacks. In this paper, we propose the first targeted adversarial attacks against mmWave-based HAR through designed universal perturbation. A practical iteration algorithm is developed to craft perturbations that generalize well across different activity samples without additional training overhead. Different from existing work that only develops adversarial attacks for a particular mmWave-based HAR model, we improve the practicability of our attacks by broadening our target to the two most common mmWave-based HAR models (i.e., voxel-based and heatmap-based). In addition, we consider a more challenging black-box scenario by addressing the information deficiency issue with knowledge distillation and solving the insufficient activity sample with a generative adversarial network. We evaluate the proposed attacks on two different mmWave-based HAR models designed for fitness tracking. The evaluation results demonstrate the efficacy, efficiency, and practicality of the proposed targeted attacks with an average success rate of over 90%.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228887"}, {"primary_key": "1203039", "vector": [], "sparse_vector": [], "title": "Push the Limit of LPWANs with Concurrent Transmissions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Low Power Wide Area Networks (LPWANs) have been shown promising in connecting large-scale low-cost devices with low-power long-distance communication. However, existing LPWANs cannot work well for real deployments due to severe packet collisions. We propose OrthoRa, a new technology which significantly improves the concurrency for low-power long-distance LPWAN transmission. The key of OrthoRa is a novel design, Orthogonal Scatter Chirp Spreading Spectrum (OSCSS), which enables orthogonal packet transmissions while providing low SNR communication in LPWANs. Different nodes can send packets encoded with different orthogonal scatter chirps, and the receiver can decode collided packets from different nodes. We theoretically prove that OrthoRa provides very high concurrency for low SNR communication under different scenarios. For real networks, we address practical challenges of multiple-packet detection for collided packets, scatter chirp identification for decoding each packet and accurate packet synchronization with Carrier Frequency Offset. We implement OrthoRa on HackRF One and extensively evaluate its performance. The evaluation results show that OrthoRa improves the network throughput and concurrency by 50× compared with LoRa.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228983"}, {"primary_key": "1203042", "vector": [], "sparse_vector": [], "title": "<PERSON>: A Scalable SIMD-based Multi-literal Pattern Matching Engine for Deep Packet Inspection.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Deep Packet Inspection (DPI) is a significant network security technique. It examines traffic workloads by searching for specific rules. Since every byte of packets needs to be examined by many literal rules, multi-literal matching becomes the performance bottleneck of DPI. FDR, the fastest multi-literal matching engine on CPUs, takes advantage of Single-Instruction-Multiple-Data (SIMD) to alleviate this bottleneck and achieves a performance boost over the widely-used Aho-Corasick (AC) algorithm. However, FDR does not deeply exploit the data-level parallelism of SIMD and its SIMD vector utilization is only 50%. Besides, limited by certain SIMD shift instructions, it cannot benefit from advanced SIMD instruction sets. To overcome these issues, we propose <PERSON>, a scalable and SIMD-based multi-literal matching engine. <PERSON> adopts a column-vector-based matching algorithm to improve the data-level parallelism and SIMD vector utilization. To support the algorithm, it takes two encoding methods to compress the mask table. Also, it utilizes shuffle instruction to implement shift. We implement Harry on commodity CPU and evaluate it with real network traffic and DPI rules. Our evaluation shows that <PERSON> reaches a throughput of 30∼70Gbit/s, up to 52x that of AC and 2.09x of FDR. It has been successfully deployed in Hyperscan.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229022"}, {"primary_key": "1203043", "vector": [], "sparse_vector": [], "title": "Enabling Age-Aware Big Data Analytics in Serverless Edge Clouds.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the fast development of artificial intelligence applications, large-volume big data generated in the edge of networks are waiting for real-time analysis, such that the valuable information is unveiled. Analytic developers for big data applications usually face the burden of managing the underlying cloud resources, which greatly drags the speed of analytic development. Serverless Computing is envisioned as an enabling technology to release the management burden of developers and to enable agile big data analytics. That is, big data analytics can be implemented in short-lived functions via the Function-as-a-Service (FaaS) programming paradigm. In this paper, we aim to fill the gap between serverless computing and mobile edge computing, via enabling query evaluations for big data analytics in short-lived functions of a serverless edge cloud (SEC). Specifically, we formulate novel age-aware big data query evaluation problems in an SEC so that the age of data is minimized, where the age of data is defined as the time difference between the current time and the generation time of the dataset. We propose approximation algorithms for the age-aware big data query evaluation problem with a single query, by proposing a novel parameterized virtualization technique that strives for a fine trade-off between short-lived functions and large resource demands of big data queries. We also devise an online learning algorithm with a bounded regret for the problem with multiple queries arriving dynamically and without prior knowledge of resource demands of the queries. We finally evaluate the performance of the proposed algorithms by extensive simulations. Simulation results show that the performance of our algorithms is promising.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228905"}, {"primary_key": "1203045", "vector": [], "sparse_vector": [], "title": "AGO: Boosting Mobile AI Inference Performance by Removing Constraints on Graph Optimization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Traditional deep learning compilers rely on heuristics for subgraph generation, which impose extra constraints on graph optimization, e.g., each subgraph can only contain at most one complex operator. In this paper, we propose AGO, a framework for graph optimization with arbitrary structures to boost the inference performance of deep models by removing such constraints. To create new optimization opportunities for complicated subgraphs, we propose intensive operator fusion, which effectively stitches multiple complex operators together for better performance. Further, we design a graph partitioning scheme that allows an arbitrary structure for each subgraph while guaranteeing the acyclic property among all generated subgraphs. Additionally, to enable efficient performance tuning for complicated subgraphs, we devise a divide-and-conquer tuning mechanism to orchestrate different system components. Through extensive experiments on various neural networks and mobile devices, we show that our system can improve the inference performance by up to 3.3× when compared with state-of-the-art vendor libraries and deep compilers.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228858"}, {"primary_key": "1203046", "vector": [], "sparse_vector": [], "title": "CLIP: Accelerating Features Deployment for Programmable Switch.", "authors": ["Tingting Xu", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cloud network serves a large number of tenants and a variety of applications. The continuously changing demands require a programmable data plane to achieve fast feature velocity. However, the years-long release cycle of traditional function-fixed switches can not meet this requirement. Emerging programmable switches provide the flexibility of packet processing without sacrificing hardware performance. Due to the trade-off between performance and flexibility, the current programmable switches make compromises in some aspects such as limited memory/computation resources, and lack of the capacity to realize complicated computation. The programmable switches can not satisfy the demand for network services and applications in production networks. We propose a framework that leverages host servers to extend the capability of network switches quickly, accelerates new feature deployment, and verifies new ideas in production networks. Specifically, to build the unified programmable data plane, we propose essential design and implementation challenges including a programming abstraction that allows automatically and effectively deploying network functions on switch and server clusters, allocating traffic to fully utilize the server resources, and supporting flexible scaling of the system. The quick deployment of self-defined functions in a realistic system has verified the feasibility and practicality of the proposed framework.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228857"}, {"primary_key": "1203047", "vector": [], "sparse_vector": [], "title": "ISAC: In-Switch Approximate Cache for IoT Object Detection and Recognition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In object detection and recognition, similar but nonidentical sensing data probably maps to the same result. Therefore, a cache preserving popular results to support approximate match for similar input requests can accelerate the task by avoiding the otherwise expensive deep learning model inferences. However, the current software and hardware practices carried on edge or cloud servers are inefficient in either cost or performance. Taking advantage of the on-path programmable switches, we propose In-Switch Approximate Cache (ISAC) to reduce the server workload and latency. The unique approximate matching requirement sets ISAC apart from a conventional exact-match cache. Equipped with efficient encoding and qualifying algorithms, ISAC in an on-path switch can fulfill most of the input requests with high accuracy. When adapting to a P4 programmable switch, it can sustain up to 194M frames per second and fulfill 60.3% of them, achieving a considerable reduction on detection latency, server cost, and power consumption. Readily deployable in existing network infrastructure, ISAC is the first-of-its-kind approximate cache that can be completely implemented in a switch to support a class of IoT applications.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229067"}, {"primary_key": "1203048", "vector": [], "sparse_vector": [], "title": "Online Learning for Adaptive Probing and Scheduling in Dense WLANs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Existing solutions to network scheduling typically assume that the instantaneous link rates are completely known before a scheduling decision is made or consider a bandit setting where the accurate link quality is discovered only after it has been used for data transmission. In practice, the decision maker can obtain (relatively accurate) channel information, e.g., through beamforming in mmWave networks, right before data transmission. However, frequent beamforming incurs a formidable overhead in densely deployed mmWave WLANs. In this paper, we consider the important problem of throughput optimization with joint link probing and scheduling. The problem is challenging even when the link rate distributions are pre-known (the offline setting) due to the necessity of balancing the information gains from probing and the cost of reducing the data transmission opportunity. We develop an approximation algorithm with guaranteed performance when the probing decision is non-adaptive and a dynamic programming-based solution for the more challenging adaptive setting. We further extend our solutions to the online setting with unknown link rate distributions and develop a contextual-bandit based algorithm and derive its regret bound. Numerical results using data traces collected from real-world mmWave deployments demonstrate the efficiency of our solutions.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228988"}, {"primary_key": "1203049", "vector": [], "sparse_vector": [], "title": "Fast Generation-Based Gradient Leakage Attacks against Highly Compressed Gradients.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Mengyu Ge", "<PERSON>wei Li", "<PERSON><PERSON>", "Hongwei Li"], "summary": "Federated learning (FL) is a distributed machine learning technology that preserves data privacy. However, it has been shown to be vulnerable to gradient leakage attacks (GLA), which can reconstruct private training data from public gradients with an overwhelming probability. Nevertheless, these attacks either require modification of the FL model (analytics-based) or take a long time to converge (optimization-based) and fail in dealing with highly compressed gradients in practical FL systems. In this paper, we pioneer a generation-based GLA method called FGLA that can reconstruct batches of user data, forgoing the optimization process. Specifically, we design a feature separation technique that extracts the feature of each data in a batch and then generates user data directly. Extensive experiments on multiple image datasets demonstrate that FGLA can reconstruct user images in milliseconds with a batch size of 256 from highly compressed gradients (0.8% compression ratio or higher), thus substantially outperforming state-of-the-art methods.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229091"}, {"primary_key": "1203050", "vector": [], "sparse_vector": [], "title": "Secure Device Trust Bootstrapping Against Collaborative Signal Modification Attacks.", "authors": ["<PERSON><PERSON>", "Shucheng Yu", "<PERSON>"], "summary": "Bootstrapping security among wireless devices without prior-shared secrets is frequently demanded in emerging wireless and mobile applications. One promising approach for this problem is to utilize in-band physical-layer radio-frequency (RF) signals for authenticated key establishment because of the efficiency and high usability. However, existing in-band authenticated key agreement (AKA) protocols are mostly vulnerable to Man-in-the-Middle (MitM) attacks, which can be launched by modifying the transmitted wireless signals over the air. By annihilating legitimate signals and injecting malicious signals, signal modification attackers are able to completely control the communication channels and spoof victim wireless devices. State-of-the-art (SOTA) techniques addressing such attacks require additional auxiliary hardware or are limited to single attackers. This paper proposes a novel in-band security bootstrapping technique that can thwart colluding signal modification attackers. Different from SOTA solutions, our design is compatible with commodity devices without requiring additional hardware. We achieve this based on the internal randomness of each device that is unpredictable to attackers. Any modification to RF signals will be detected with high probabilities. Extensive security analysis and experimentation on the USRP platform demonstrate the effectiveness of our design under various attack strategies.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229007"}, {"primary_key": "1203051", "vector": [], "sparse_vector": [], "title": "Transfer Beamforming via Beamforming for Transfer.", "authors": ["<PERSON><PERSON><PERSON>", "Z<PERSON><PERSON> An", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Although billions of battery-free backscatter devices (e.g., RFID tags) are intensively deployed nowadays, they are still unsatisfying in performance limitations (i.e., short reading range and high miss-reading rate) resulting from power harvesting inefficiency. However, applying classic beamforming technique to backscatter systems meets the deadlock start problem, i.e., without enough power, the backscatter cannot wake up to provide channel parameters; but, without channel parameters, the system cannot form beams to provide power. In this work, we propose a new beamforming paradigm called transfer beamforming (TBF), namely, beamforming strategies can be transferred from reference tags with known positions to power up unknown neighbor tags of interest. Transfer beamforming (is accomplished) via (launching) beamforming (to reference tags firstly) for (the purpose of) transfer. To do so, we adopt semi-active tags as reference tags, which can be easily powered up with a normal reader. Then beamforming is initiated and transferred to power up passive tags surrounded by reference tags. A prototype evaluation of TBF with 8 antennas presents a 99.9% inventory coverage rate in a crowded warehouse with 2,160 RFID tags. Our evaluation reveals that TBF improves the power transmission by 6.9 dB and boosts the inventory speed by 2 × compared with state-of-art methods.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229106"}, {"primary_key": "1203052", "vector": [], "sparse_vector": [], "title": "NFChain: A Practical Fingerprinting Scheme for NFC Tag Authentication.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Z<PERSON><PERSON> An", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "NFC tag authentication is highly demanded to avoid tag abuse.Recent fingerprinting methods employ the physicallayer signal, which embeds the tag hardware imperfections for authentication.However, existing NFC fingerprinting methods suffer from either low scalability for a large number of tags or incompatibility with NFC protocols, impeding the practical application of NFC authentication systems.To fill this gap, we propose NFChain, a new NFC fingerprinting scheme that excavates the tag hardware uniqueness from the protocol-agnostic tag response signal.Specifically, we harness an agile and compatible frequency band of NFC to extract the tag fingerprint from a chain of tag responses over multiple frequencies, which significantly improves fingerprint scalability.However, extracting the desired fingerprint encounters two practical challenges: (1) fingerprint inconsistency under different NFC reader and tag configurations and (2) fingerprint variations across multiple measurements of the same tag due to the signal noise in generic readers.To tackle these challenges, we first design an effective nulling method to eliminate the effect of device configurations.Second, we employ contrastive learning to reduce fingerprint variations for accurate authentication.Extensive experiments show we can achieve as low as 3.7% FRR and 4.1% FAR for over 600 tags.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229040"}, {"primary_key": "1203053", "vector": [], "sparse_vector": [], "title": "VoShield: Voice Liveness Detection with Sound Field Dynamics.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Yuan<PERSON> Zheng"], "summary": "Voice assistants are widely integrated into a variety of smart devices, enabling users to easily complete daily tasks and even critical operations like online transactions with voice commands. Thus, once attackers replay a secretly-recorded voice command by loudspeakers to compromise users’ voice assistants, this operation will cause serious consequences, such as information leakage and property loss. Unfortunately, most voice liveness detection approaches against replay attacks mainly rely on detecting lip motions or subtle physiological features in speech, which are limited within a very short range. In this paper, we propose VoShield to check whether a voice command is from a genuine user or a loudspeaker imposter. VoShield measures sound field dynamics, a feature that changes fast as the human mouths dynamically open and close. In contrast, it would remain rather stable for loudspeakers due to the fixed size. This feature enables VoShield to largely extend the working distance and remain resilient to user locations. Besides, sound field dynamics are extracted from the difference between multiple microphone channels, making this feature robust to voice volume. To evaluate VoShield, we conducted comprehensive experiments with various settings in different working scenarios. The results show that VoShield can achieve a detection accuracy of 98.2% and an Equal Error Rate of 2.0%, which serves as a promising complement to current voice authentication systems for smart devices.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229038"}, {"primary_key": "1203057", "vector": [], "sparse_vector": [], "title": "Gemini: Divide-and-Conquer for Practical Learning-Based Internet Congestion Control.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Guo"], "summary": "Learning-based Internet congestion control algorithms have attracted much attention due to their potential performance improvement over traditional algorithms. However, such performance improvement is usually at the expense of black-box design and high computational overhead, which prevent them from large-scale deployment over production networks. To address this problem, we propose a novel Internet congestion control algorithm called Gemini. It contains a parameterized congestion control module, which is white-box designed with low computational overhead, and an online parameter optimization module, which serves to adapt the parameterized congestion control module to different networks for higher transmission performance. Extensive trace-driven emulations reveal Gemini achieves better balances between delay and throughput than state-of-the-art algorithms. Moreover, we successfully deploy Gemini over production networks. The evaluation results show that the average throughput of Gemini is 5% higher than that of Cubic (4% higher than that of BBR) over a mobile application downloading service and 61% higher than that of Cubic (33% higher than that of BBR) over a commercial network speed-test benchmarking service.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229069"}, {"primary_key": "1203058", "vector": [], "sparse_vector": [], "title": "ChirpKey: A Chirp-level Information-based Key Generation Scheme for LoRa Networks via Perturbed Compressed Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hongbo Liu", "Xianjin Xia", "<PERSON>", "<PERSON> Gu", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Physical-layer key generation is promising in establishing a pair of cryptographic keys for emerging LoRa networks. However, existing key generation systems may perform poorly since the channel reciprocity is critically impaired due to low data rate and long range in LoRa networks. To bridge this gap, this paper proposes a novel key generation system for LoRa networks, named ChirpKey. We reveal that the underlying limitations are coarse-grained channel measurement and inefficient quantization process. To enable fine-grained channel information, we propose a novel LoRa-specific channel measurement method that essentially analyzes the chirp-level changes in LoRa packets. Additionally, we propose a LoRa channel state estimation algorithm to eliminate the effect of asynchronous channel sampling. Instead of using quantization process, we propose a novel perturbed compressed sensing based key delivery method to achieve a high level of robustness and security. Evaluation in different real-world environments shows that ChirpKey improves the key matching rate by 11.03–26.58% and key generation rate by 27–49× compared with the state-of-the-arts. Security analysis demonstrates that ChirpKey is secure against several common attacks. Moreover, we implement a ChirpKey prototype and demonstrate that it can be executed in 0.2 s.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228886"}, {"primary_key": "1203060", "vector": [], "sparse_vector": [], "title": "CaaS: Enabling Control-as-a-Service for Time-Sensitive Networking.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Flexible manufacturing is one of the core goals of Industry 4.0 and brings new challenges to current industrial control systems. Our detailed field study on auto glass industry revealed that existing production lines are laborious to reconfigure, difficult to upscale, and costly to upgrade during production switching. Such inflexibility arises from the tight coupling of devices, controllers, and control tasks. In this work, we propose a new architecture for industrial control systems named Control-as-a-Service (CaaS). CaaS transfers and distributes control tasks from dedicated controllers into Time-Sensitive Networking (TSN) switches. By combining control and transmission functions in switches, CaaS virtualizes the industrial TSN network to one Programmable Logic Controller (PLC). We propose a set of techniques that realize end-to-end determinism for in-network industrial control and a joint task and traffic scheduling algorithm. We evaluate the performance of CaaS on testbeds based on real-world networked control systems. The results show that the idea of CaaS is feasible and effective, and CaaS achieves absolute packet delivery, 42-45% lower latency, and three orders of magnitude lower jitter. We believe CaaS is a meaningful step towards the distribution, virtualization, and servitization of industrial control.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.********"}, {"primary_key": "1203061", "vector": [], "sparse_vector": [], "title": "Asynchronous Entanglement Provisioning and Routing for Distributed Quantum Computing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In Distributed Quantum Computing (DQC), quantum bits (qubits) used in a quantum circuit may be distributed on multiple Quantum Computers (QCs) connected by a Quantum Data Network (QDN). To perform a quantum gate operation involving two qubits on different QCs, we have to establish an Entanglement Connection (EC) between their host QCs. Existing EC establishment schemes result in a long EC establishment time, and low quantum resource utilization.In this paper, we propose an Asynchronous Entanglement Routing and Provisioning (AEPR) scheme to minimize the task completion time in DQC systems. AEPR has three distinct features: (i). Entanglement Paths (EPs) for a given SD pair are predetermined to eliminate the need for runtime calculation; (ii). Entanglement Links (ELs) are created proactively to reduce the time needed create EL on demand; and (iii). For a given EC request, quantum swapping along an EP is performed by a repeater whenever two adjacent ELs are created, so precious quantum resources at the repeater can be released immediately thereafter for other ELs and ECs. Extensive simulations show that AEPR can save up to 76.05% of the average task completion time in DQC systems compared with the state-of-the-art entanglement routing schemes designed to maximize QDN throughput.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.********"}, {"primary_key": "1203063", "vector": [], "sparse_vector": [], "title": "CoLUE: Collaborative TCAM Update in SDN Switches.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "With the rapidly changing network, rule update in TCAM has become the bottleneck for application performance. In traditional software-defined networks, some application policies are deployed at the edge switches, while the scarce TCAM spaces exacerbate the frequency and difficulty of rule updates. This paper proposes CoLUE, a framework which groups rules into switches in a balance and dependency minimum way. CoLUE is the first work that combines TCAM update and rule placement, making full use of TCAM in distributed switches. Not only does it accelerate update speed, it also keeps the TCAM space load-balance across switches. Composed of ruleset decomposition and subset distribution, CoLUE has an NP-completeness challenge. We propose heuristic algorithms to calculate a near-optimal rule placement scheme. Our evaluations show that CoLUE effectively balances TCAM space load and reduces the average update cost by more than 1.45 times and the worst-case update cost by up to 5.46 times, respectively.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229074"}, {"primary_key": "1203064", "vector": [], "sparse_vector": [], "title": "Stateful Switch: Optimized Time Series Release with Local Differential Privacy.", "authors": ["Qingqing Ye", "Haibo Hu", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Time series data have numerous applications in big data analytics. However, they often cause privacy issues when collected from individuals. To address this problem, most existing works perturb the values in the time series while retaining their temporal order, which may lead to significant distortion of the values. Recently, we propose TLDP model [45] that perturbs temporal perturbation to ensure privacy guarantee while retaining original values. It has shown great promise to achieve significantly higher utility than value perturbation mechanisms in many time series analysis. However, its practicability is still undermined by two factors, namely, utility cost of extra missing or empty values, and inflexibility of privacy budget settings. To address them, in this paper we propose switch as a new two-way operation for temporal perturbation, as opposed to the one-way dispatch operation in [45]. The former inherently eliminates the cost of missing, empty or repeated values. Optimizing switch operation in a stateful manner, we then propose StaSwitch mechanism for time series release under TLDP. Through both analytical and empirical studies, we show that StaSwitch has significantly higher utility for the published time series than any state-of-the-art temporal- or value-perturbation mechanism, while allowing any combination of privacy budget settings.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.********"}, {"primary_key": "1203065", "vector": [], "sparse_vector": [], "title": "Impact of International Submarine Cable on Internet Routing.", "authors": ["Honglin Ye", "<PERSON><PERSON>", "<PERSON>"], "summary": "International submarine cables (ISCs) connect various countries/regions worldwide, and serve as the foundation of Internet routing. However, little attention has been paid to studying the impact of ISCs on Internet routing. This study addresses two questions to bridge the gap between ISCs and Internet routing: (1) For a given ISC, which Autonomous Systems (ASes) are using it, and (2) How dependent is Internet routing on ISCs. To tackle the first question, we propose Topology to Topology (or T2T), a framework for the large-scale measurement of static mapping between ASes and ISCs, and apply T2T to the Internet to reveal the status, trends, and preferences of ASes using ISCs. We find that ISCs used by Tier-1 ASes are more than 30× of stub ASes. For the second question, we design an Internet routing simulator, and evaluate the behavior change of Internet routing when an ISC fails based on the mapping between ASes and ISCs. The results show that benefited from the complex mesh of ISCs, the failures of most ISCs have limited impact on Internet routing, while a few ISCs can have a significant impact. Finally, we analyze severely affected ASes and recommend how to improve the resilience of the Internet.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.********"}, {"primary_key": "1203066", "vector": [], "sparse_vector": [], "title": "LARRI: Learning-based Adaptive Range Routing for Highly Dynamic Traffic in WANs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Traffic Engineering (TE) has been widely used by network operators to improve network performance and provide better service quality to users. One major challenge for TE is how to generate good routing strategies adaptive to highly dynamic future traffic scenarios. Unfortunately, existing works could either experience severe performance degradation under unexpected traffic fluctuations or sacrifice performance optimality for guaranteeing the worst-case performance when traffic is relatively stable. In this paper, we propose LARRI, a learning-based TE to predict adaptive routing strategies for future unknown traffic scenarios. By learning and predicting a routing to handle an appropriate range of future possible traffic matrices, LARRI can effectively realize a trade-off between performance optimality and worst-case performance guarantee. This is done by integrating the prediction of future demand range and the imitation of optimal range routing into one step. Moreover, LARRI employs a scalable graph neural network architecture to greatly facilitate training and inference. Extensive simulation results on six real-world network topologies and traffic traces show that LARRI achieves near-optimal load balancing performance in future traffic scenarios with up to 43.3% worst-case performance improvement over state-of-the-art baselines, and also provides the lowest end-to-end delay under dynamic traffic fluctuations.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.********"}, {"primary_key": "1203068", "vector": [], "sparse_vector": [], "title": "SubScatter: Subcarrier-Level OFDM Backscatter.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "OFDM backscatter is crucial in passive IoT. Most of the existing works adopt phase-modulated schemes to embed tag data, which suffer from three drawbacks: symbol-level modulation limitation, heavy synchronization accuracy reliance, and small symbol time offset (STO) / carrier frequency (CFO) offset tolerability. We introduce SubScatter, the first subcarrier-level frequency-modulated OFDM backscatter which is able to tolerate bigger synchronization errors, STO, and CFO. The unique feature that sets SubScatter apart from the other backscatter systems is our subcarrier shift keying (SSK) modulation. This method pushes the modulation granularity to the subcarrier by encoding and mapping tag data into different subcarrier patterns. We also design a tandem frequency shift (TFS) scheme that enables SSK with low cost and low power. For decoding, we propose a correlation-based method that decodes tag data from the correlation between the original and backscatter OFDM symbols. We prototype and test SubScatter under 802.11g OFDM WiFi signals. Comprehensive evaluations show that our SubScatter outstands prior works in terms of effectiveness and robustness. Specifically, SubScatter has 743kbps throughput, 3.1× and 14.9× higher than RapidRider and MOXcatter, respectively. It also has a much lower BER under noise and interferences, which is over 6× better than RapidRider or MOXcatter.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228918"}, {"primary_key": "1203070", "vector": [], "sparse_vector": [], "title": "AccDecoder: Accelerated Decoding for Neural-enhanced Video Analytics.", "authors": ["Tingting Yuan", "<PERSON>", "<PERSON><PERSON>", "<PERSON>peng Dai", "<PERSON><PERSON> Fu"], "summary": "The quality of the video stream is key to neural network-based video analytics. However, low-quality video is inevitably collected by existing surveillance systems because of poor quality cameras or over-compressed/pruned video streaming protocols, e.g., as a result of upstream bandwidth limit. To address this issue, existing studies use quality enhancers (e.g., neural super-resolution) to improve the quality of videos (e.g., resolution) and eventually ensure inference accuracy. Nevertheless, directly applying quality enhancers does not work in practice because it will introduce unacceptable latency. In this paper, we present AccDecoder, a novel accelerated decoder for real-time and neural-enhanced video analytics. AccDecoder can select a few frames adaptively via Deep Reinforcement Learning (DRL) to enhance the quality by neural super-resolution and then up-scale the unselected frames that reference them, which leads to 6-21% accuracy improvement. AccDecoder provides efficient inference capability via filtering important frames using DRL for DNN-based inference and reusing the results for the other frames via extracting the reference relationship among frames and blocks, which results in a latency reduction of 20-80% than baselines.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228933"}, {"primary_key": "1203071", "vector": [], "sparse_vector": [], "title": "Privacy as a Resource in Differentially Private Federated Learning.", "authors": ["<PERSON><PERSON>g Yuan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Differential privacy (DP) enables model training with a guaranteed bound on privacy leakage, therefore is widely adopted in federated learning (FL) to protect the model update. However, each DP-enhanced FL job accumulates privacy leakage, which necessitates a unified platform to enforce a global privacy budget for each dataset owned by users. In this work, we present a novel DP-enhanced FL platform that treats privacy as a resource and schedules multiple FL jobs across sensitive data. It first introduces a novel notion of device-time blocks for distributed data streams. Such data abstraction enables fine-grained privacy consumption composition across multiple FL jobs. Regarding the non-replenishable nature of the privacy resource (that differs it from traditional hardware resources like CPU and memory), it further employs an allocation-then-recycle scheduling algorithm. Its key idea is to first allocate an estimated upper-bound privacy budget for each arrived FL job, and then progressively recycle the unused budget as training goes on to serve further FL jobs. Extensive experiments show that our platform is able to deliver up to 2.1× as many completed jobs while reducing the violation rate by up to 55.2% under limited privacy budget constraint.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228953"}, {"primary_key": "1203074", "vector": [], "sparse_vector": [], "title": "Layered Structure Aware Dependent Microservice Placement Toward Cost Efficient Edge Clouds.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Li"], "summary": "Although the containers are featured by light-weightness, it is still resource-consuming to pull and startup a large container image, especially in relatively resource-constrained edge cloud. Fortunately, <PERSON><PERSON>, as the most widely used container, provides a unique layered architecture that allows the same layer to be shared between microservices so as to lower the deployment cost. Meanwhile, it is highly desirable to deploy dependent microservices of an application together to lower the operation cost. Therefore, the balancing of microservice deployment cost and the operation cost should be considered comprehensively to achieve minimal overall cost of an on-demand application. In this paper, we first formulate this problem into a Quadratic Integer Programming form (QIP) and prove it as a NP-hard problem. We further propose a Randomized Rounding-based Microservice Deployment and Layer Pulling (RR-MDLP) algorithm with low computation complexity and guaranteed approximation ratio. Through extensive experiments, we verify the high efficiency of our algorithm by the fact that it significantly outperforms existing state-of-the-art microservice deployment strategies.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229030"}, {"primary_key": "1203079", "vector": [], "sparse_vector": [], "title": "CLP: A Community based Label Propagation Framework for Multiple Source Detection.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Given an aftermath of an information spreading, i.e., an infected network G N after the propagation of malicious rumors, malware or viruses, how can we identify the sources of the cascade? Answering this problem, which is known as the multiple source detection (MSD) problem, is critical whether for forensic use or insights to prevent future epidemics.Despite the recent considerable effort, most of them are built on a preset propagation model, which limits their application range. Some attempts aim to break this limitation via a label propagation scheme where the nodes surrounded by a large proportion of infected nodes are highlighted. Nonetheless, the detection accuracy may suffer since the node labels are simply integers with all infected or uninfected nodes sharing the same initialization setting respectively, which fall short of sufficiently distinguishing their structural properties. To this end, we propose a community based label propagation (CLP) framework that locates multiple sources through exploiting the community structures formed by infected subgraphs of different sources. Besides, CLP tries to enhance the detection accuracy by incorporating node prominence and exoneration effects, namely the nodes surrounded by larger proportions of infected nodes are more likely to be sources, and the uninfected nodes or infected ones in neighboring community hold the key in exonerating an infected node from being the source. As such, CLP is applicable in more propagation models, and is provably convergent. Experiments on both synthetic and real-world networks further validate the superiority of CLP to the state-of-the-art, boosting the F-score from few percents to approximately 68× in large-scale networks.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228907"}, {"primary_key": "1203080", "vector": [], "sparse_vector": [], "title": "Collaborative Streaming and Super Resolution Adaptation for Mobile Immersive Videos.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Tile-based streaming and super resolution are two representative technologies adopted to improve bandwidth efficiency of immersive video steaming. The former allows selective download of contents in the user viewport by splitting the video into multiple independently decodable tiles. The latter leverages client-side computation to reconstruct the received video into higher quality using advanced neural network models. In this work, we propose CASE, a collaborated adaptive streaming and enhancement framework for mobile immersive videos, which integrates super resolution with tile-based streaming to optimize user experience with dynamic bandwidth and limited computing capability. To coordinate the video transmission and reconstruction in CASE, we identify and address several key design issues including unified video quality assessment, computation complexity model for super resolution, and buffer analysis considering the interplay between transmission and reconstruction. We further formulate the quality-of-experience (QoE) maximization problem for mobile immersive video streaming and propose a rate adaptation algorithm to make the best decisions for download and for reconstruction based on the Lyapunov optimization theory. Extensive evaluation results validate the superiority of our proposed approach, which presents stable performance with considerable QoE improvement, while enabling trade-off between playback smoothness and video quality.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228906"}, {"primary_key": "1203081", "vector": [], "sparse_vector": [], "title": "Who is the Rising Star? Demystifying the Promising Streamers in Crowdsourced Live Streaming.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Lifeng Sun"], "summary": "Streamers are the core competency of the crowd-sourced live streaming (CLS) platform. However, little work has explored how different factors relate to their popularity evolution patterns. In this paper, we will investigate a critical problem, i.e., how to discover the promising streamers in their early stage? To tackle this problem, we first conduct large-scale measurement on a real-world CLS dataset. We find that streamers can indeed be clustered into two evolution types (i.e., rising type and normal type), and these two types of streamers will show differences in some inherent properties. Traditional time-sequential models cannot handle this problem, because they are unable to capture the complicated interactivity and extensive heterogeneity in CLS scenarios. To address their shortcomings, we further propose Niffler, a novel heterogeneous attention temporal graph framework (HATG) for predicting the evolution types of CLS streamers. Specifically, through the graph neural network (GNN) and gated-recurrent-unit (GRU) structure, <PERSON><PERSON><PERSON> can capture both the interactive features and the evolutionary dynamics. Moreover, by integrating the attention mechanism in the model design, <PERSON><PERSON><PERSON> can intelligently preserve the heterogeneity when learning different levels of node representations. We systematically compare <PERSON><PERSON><PERSON> against multiple baselines from different categories, and the experimental results show that our proposed model can achieve the best prediction performance.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228881"}, {"primary_key": "1203082", "vector": [], "sparse_vector": [], "title": "Realizing Uplink MU-MIMO Communication in mmWave WLANs: Bayesian Optimization and Asynchronous Transmission.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the rapid proliferation of mobile devices, the marriage of millimeter-wave (mmWave) and MIMO technologies is a natural trend to meet the communication demand of data-hungry applications. Following this trend, mmWave multiuser MIMO (MU-MIMO) has been standardized by the IEEE 802.11ay for its downlink to achieve multi-Gbps data rate. Yet, its uplink counterpart has not been well studied, and its way to wireless local area networks (WLANs) remains unclear. In this paper, we present a practical uplink MU-MIMO mmWave communication (UMMC) scheme for WLANs. UMMC has two key components: i) an efficient Bayesian optimization (BayOpt) framework for joint beam search over multiple directional antennas, and ii) a new MU-MIMO detector that can decode asynchronous data packets from multiple user devices. We have built a prototype of UMMC on a mmWave testbed and evaluated its performance through a blend of over-the-air experiments and extensive simulations. Experimental and simulation results confirm the efficiency of UMMC in practical network settings.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228888"}, {"primary_key": "1203083", "vector": [], "sparse_vector": [], "title": "An End-to-end Learning Framework for Joint Compensation of Impairments in Coherent Optical Communication Systems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Xuson<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The application of machine learning techniques in Coherent Optical Communication (COC) systems has gained increasing attention in recent years. One representative and successful application is to employ neural networks to compensate the signal impairments of devices in the COC system. However, existing studies usually concentrate on each individual device or one impairment, the various impairments sourced from multiple devices (e.g., non-linear distortion, memory and crosstalk effects) are not well investigated. More importantly, due to the impairment isolation caused by frequency offset, traditional studies only compensate the impairments of transmitter or receiver individually. In this paper, we consider a more practical and challenging experimental setup environment: joint compensation of multiple impairments associated with all devices of transmitter and receiver simultaneously. To this end, we propose an end-to-end compensation framework from the transmitter to the receiver in COC systems with three associated modules: an auxiliary channel neural network for impairment modeling, a pre-compensation neural network deployed in the transmitter, and a post-compensation neural network deployed in the receiver. Different from previous works, the proposed framework not only allows modeling all impairments of multiple devices, but also provides a new venue for joint compensation of the transmitter and receiver simultaneously. The solution has been successfully verified by the high baud rate (120Gbaud) coherent optical professional test platform and shows impressive optical Signalto-Noise Ratio (SNR) gains.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228854"}, {"primary_key": "1203084", "vector": [], "sparse_vector": [], "title": "Two-level Graph Caching for Expediting Distributed GNN Training.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Graph Neural Networks (GNNs) are increasingly popular due to excellent performance on learning graph-structured data in various domains. With fast expanding graph sizes and feature dimensions, distributed GNN training has been adopted, with multiple concurrent workers learning on different portions of a large graph. It has been observed that a main bottleneck in distributed GNN training lies in graph feature fetching across servers, which dominates the training time of each training iteration at each worker. This paper studies efficient feature caching on each worker to minimize feature fetching overhead, in order to expedite distributed GNN training. Current distributed GNN training systems largely adopt static caching of fixed neighbor nodes. We propose a novel two-level dynamic cache design exploiting both GPU memory and host memory at each worker, and design efficient two-level dynamic caching algorithms based on online optimization and a lookahead batching mechanism. Our dynamic caching algorithms consider node requesting probabilities and heterogeneous feature fetching costs from different servers, achieving an O(log3 k) competitive ratio in terms of overall feature-fetching communication cost (where k is the cache capacity). We evaluate practical performance of our caching design with testbed experiments, and show that our design achieves up to 5.4x convergence speed-up.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228911"}, {"primary_key": "1203085", "vector": [], "sparse_vector": [], "title": "Owl: A Pre-and Post-processing Framework for Video Analytics in Low-light Surroundings.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>yang Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Lifeng Sun"], "summary": "The low-light environment is an integral surrounding in real-world video analytic applications. Conventional wisdom claims that in order to adapt to the extensive computation requirement of the analytics model and achieve high inference accuracy, the overall pipeline should leverage a client-to-cloud framework that designs a cloud-based inference with on-demand video streaming. However, we show that due to the amplified noise, directly streaming the video in low-light scenarios can introduce significant bandwidth inefficiency.In this paper, we propose Owl, an intelligent framework to optimize the bandwidth utilization and inference accuracy for the low-light video analytic pipeline. The core idea of Owl is two-fold: on the one hand, we will deploy a light-weighted pre-processing module before transmission, through which we will get the denoised video and significantly reduce the transmitted data; on the other hand, we recover the information from the denoised video via an enhancement module in the server-side. Specifically, through well-designed training mechanism and content representation technique, Owl can dynamically select the best configuration for time-varying videos. Experiments with a variety of datasets and tasks show that Owl achieves significant bandwidth benefits, while consistently optimizing the inference accuracy.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229059"}, {"primary_key": "1203086", "vector": [], "sparse_vector": [], "title": "Accelerating Distributed K-FAC with Efficient Collective Communication and Scheduling.", "authors": ["<PERSON>", "Shaohuai Shi", "<PERSON>"], "summary": "Kronecker-factored approximate curvature (K-FAC) has been shown to achieve faster convergence than SGD in training deep neural networks. However, existing distributed K-FAC (D-KFAC) relies on the all-reduce collective for communications and scheduling, which incurs excessive communications in each iteration. In this work, we propose a new form of D-KFAC with a reduce-based alternative to eliminate redundant communications. This poses new challenges and opportunities in that the reduce collective requires a root worker to collect the results, which considerably complicates the communication scheduling. To this end, we formulate an optimization problem that determines tensor fusion and tensor placement simultaneously aiming to minimize the training iteration time. We develop novel communication scheduling strategies and propose a placement-aware D-KFAC (PAD-KFAC) training algorithm, which further improves communication efficiency. Our experimental results on a 64-GPU cluster interconnected with 10Gb/s and 100Gb/s Ethernet show that our PAD-KFAC can achieve an average of 27% and 17% improvement over state-of-the-art D-KFAC methods, respectively.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228871"}, {"primary_key": "1203090", "vector": [], "sparse_vector": [], "title": "Oblivion: Poisoning Federated Learning by Inducing Catastrophic Forgetting.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Xu", "<PERSON><PERSON><PERSON>"], "summary": "Federated learning is exposed to model poisoning attacks as compromised clients may submit malicious model updates to pollute the global model. To defend against such attacks, robust aggregation rules are designed for the centralized server to winnow out outlier updates, and to significantly reduce the effectiveness of existing poisoning attacks. In this paper, we develop an advanced model poisoning attack against defensive aggregation rules. In particular, we exploit the catastrophic forgetting phenomenon during the process of continual learning to destroy the memory of the global model. Our proposed framework, called Oblivion, features two special components. The first component prioritizes the weights that have the most influence on the model accuracy for poisoning, which induces a more significant degradation on the global model than equally perturbing all weights. The second component smooths malicious model updates based on the number of selected compromised clients in the current round, adjusting the degree of poisoning to suit the dynamics of each training round. We implement a fully-functional prototype of Oblivion in PLATO, a real-world scalable federated learning framework. Our extensive experiments over three datasets demonstrate that Oblivion can boost the attack performance of model poisoning attacks against unknown defensive aggregation rules.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228981"}, {"primary_key": "1203091", "vector": [], "sparse_vector": [], "title": "OmniSense: Towards Edge-Assisted Online Analytics for 360-Degree Videos.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Lin<PERSON> Shen", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the reduced hardware costs of omnidirectional cameras and the proliferation of various extended reality applications, more and more 360° videos are being captured. To fully unleash their potential, advanced video analytics is expected to extract actionable insights and situational knowledge without blind spots from the videos. In this paper, we present OmniSense, a novel edge-assisted framework for online immersive video analytics. OmniSense achieves both low latency and high accuracy, combating the significant computation and network resource challenges of analyzing 360° videos. Motivated by our measurement insights into 360° videos, OmniSense introduces a lightweight spherical region of interest (SRoI) prediction algorithm to prune redundant information in 360° frames. Incorporating the video content and network dynamics, it then smartly scales vision models to analyze the predicted SRoIs with optimized resource utilization. We implement a prototype of OmniSense with commodity devices and evaluate it on diverse real-world collected 360° videos. Extensive evaluation results show that compared to resource-agnostic baselines, it improves the accuracy by 19.8% – 114.6% with similar end-to-end latencies. Meanwhile, it hits 2.0× – 2.4× speedups while keeping the accuracy on par with the highest accuracy of baselines.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229105"}, {"primary_key": "1203093", "vector": [], "sparse_vector": [], "title": "Truthful Incentive Mechanism for Federated Learning with Crowdsourced Data Labeling.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Federated learning (FL) has recently emerged as a promising paradigm that trains machine learning (ML) models on clients' devices in a distributed manner without the need of transmitting clients' data to the FL server. In many applications of ML (e.g., image classification), the labels of training data need to be generated manually by human agents (e.g., recognizing and annotating objects in an image), which are usually costly and error-prone. In this paper, we study FL with crowdsourced data labeling where the local data of each participating client of FL are labeled manually by the client. We consider the strategic behavior of clients who may not make desired effort in their local data labeling and local model computation (quantified by the mini-batch size used in the stochastic gradient computation), and may misreport their local models to the FL server. We first characterize the performance bounds on the training loss as a function of clients' data labeling effort, local computation effort, and reported local models, which reveal the impacts of these factors on the training loss. With these insights, we devise Labeling and Computation Effort and local Model Elicitation (LCEME) mechanisms which incentivize strategic clients to make truthful efforts as desired by the server in local data labeling and local model computation, and also report true local models to the server. The truthful design of the LCEME mechanism exploits the non-trivial dependence of the training loss on clients' hidden efforts and private local models, and overcomes the intricate coupling in the joint elicitation of clients' efforts and local models. Under the LCEME mechanism, we characterize the server's optimal local computation effort assignments and analyze their performance. We evaluate the proposed FL algorithms with crowdsourced data labeling and the LCEME mechanism for the MNIST-based hand-written digit classification. The results corroborate the improved learning accuracy and cost-effectiveness of the proposed approaches.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228923"}, {"primary_key": "1203094", "vector": [], "sparse_vector": [], "title": "Enabling Switch Memory Management for Distributed Training with In-Network Aggregation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Distributed training (DT) in shared clusters usually deploys a scheduler for resource allocation to multiple concurrent jobs. Meanwhile, a recent acceleration primitive, In-Network Aggregation (INA), introduces switch memory as a new critical resource for DT jobs, out of the prior scheduler's management. Lacking switch memory management leads to inefficient cluster resource usage. We build INAlloc, a switch memory management system for DT job schedulers to improve INA-empowered DT jobs in shared clusters. INAlloc adds a switch memory management layer to organize the physical switch memory, allocate memory to jobs, and provide friendly interfaces to schedulers. INAlloc incorporates switch memory into modeling a job's completion time (JCT) and its resources, which assists the scheduler in deciding the switch memory allocation. INAlloc overcomes the challenges of consistent and nondisruptive runtime switch memory reallocation. Our prototype and evaluation on real-world traces show that INAlloc can reduce the jobs' deadline miss ratio by 75% and JCT by 27%.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228956"}, {"primary_key": "1203096", "vector": [], "sparse_vector": [], "title": "WakeUp: Fine-Grained Fatigue Detection Based on Multi-Information Fusion on Smart Speakers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "With the development of society and the gradual increase of life pressure, the number of people engaged in mental work and working hours have increased significantly, resulting in more and more people in a state of fatigue. It not only reduces people's work efficiency, but also causes health and safety related problems. The existing fatigue detection systems either have different shortcomings in diverse scenarios or are limited by proprietary equipment, which is difficult to be applied in real life. Motivated by this, we propose a multi-information fatigue detection system named WakeUp based on commercial smart speakers, which is the first to fuse physiological and behavioral information for fine-grained fatigue detection in a non-contact manner. We carefully design a method to simultaneously extract users' physiological and behavioral information based on the MobileViT network and VMD decomposition algorithm respectively. Then, we design a multi-information fusion method based on the statistical features of these two kinds of information. In addition, we adopt an SVM classifier to achieve fine-grained fatigue level. Extensive experiments with 20 volunteers show that WakeUp can detect fatigue with an accuracy of 97.28%. Meanwhile, WakeUp can maintain stability and robustness under different experimental settings.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229021"}, {"primary_key": "1203097", "vector": [], "sparse_vector": [], "title": "COIN: Cost-Efficient Traffic Engineering with Various Pricing Schemes in Clouds.", "authors": ["<PERSON><PERSON>", "Jingzhou Wang", "<PERSON><PERSON>", "Zhu<PERSON>ng Yu", "<PERSON><PERSON>"], "summary": "The rapid growth of cloud services has brought a significant increase in inter-datacenter traffic. To transfer data among geographically distributed datacenters, cloud providers need to purchase bandwidth from ISPs. The data transferring cost has become one of the major expenses for cloud providers. Therefore, it is essential for a cloud provider to carefully allocate inter-datacenter traffic among the ISPs' links to minimize the costs. Exiting solutions mainly focus on the situations where all links adopt the same pricing scheme. However, in practice, ISPs usually provide multiple pricing schemes for their links due to market competition, which makes the existing solutions nonoptimal. Thus, a new traffic engineering approach that considers various pricing schemes is needed. This paper presents COIN, a new framework for cost-efficient traffic engineering with various pricing schemes. We propose a partition rounding traffic engineering algorithm based on linear independence analysis. The approximation factors and time complexity are formally analyzed. We further conduct large-scale simulations with real- world topologies and datasets. Extensive simulation results show that COIN can save the data transferring cost by up to 54.54% compared with the state-of-the-art solutions.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229001"}, {"primary_key": "1203098", "vector": [], "sparse_vector": [], "title": "RecMon: A Deep Learning-based Data Recovery System for Network Monitoring.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Network monitoring systems struggle with the issue that the measurement data is incomplete, with only a subset of origin-destination (OD) pairs or time slots observed, due to the high deployment and measurement cost. Recent studies show that the missing data can be inferred from partial measurements using neural network models and tensor methods. However, these recovery approaches fail to achieve accuracy, adaptability and high speed, simultaneously. In this paper, we propose RecMon, a deep learning-based data recovery system that satisfies the above three criteria. A global spatio-temporal attention mechanism and a data augmentation algorithm are proposed to improve the recovery accuracy. A semi-supervised learning-based scheme is devised for fast and effective model updates. We conduct extensive experiments on three real-world datasets to compare RecMon with four state-of-the-art methods in terms of online recovery performance. The experimental results show that RecMon can adapt to the latest state of the network and accurately recover network measurement data in less than 100 milliseconds. When 90% of the data is missing, the recovery accuracy of RecMon improves over the strongest baseline method by 22.7%, 16.0%, and 8.2% in the three datasets, respectively.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229025"}, {"primary_key": "1203100", "vector": [], "sparse_vector": [], "title": "Galliot: Path Merging Based Betweenness Centrality Algorithm on GPU.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Betweenness centrality (BC) is widely used to measure a vertex’s significance by using the frequency of a vertex appearing in the shortest path between other vertices. However, most recent algorithms in BC computation suffer from the problem of high auxiliary memory consumption. To reduce BC computing’s memory consumption, we propose a path-mergingbased algorithm called <PERSON><PERSON>iot to calculate the BC values on GPU, which aims to minimize the on-board memory consumption and enable the BC computation of large-scale graphs. The proposed algorithm requires $\\mathcal{O}$(n) space and runs in $\\mathcal{O}$(mn) time on unweighted graphs. We present the theoretical principle for the proposed path merging method. Moreover, we propose a locality-oriented policy to maintain and update the worklist to improve GPU data locality. In addition, we conducted extensive experiments on NVIDIA GPUs to show the performance of <PERSON><PERSON><PERSON>. The results show that <PERSON><PERSON><PERSON> can process the larger graphs, which have 11.32× more vertices and 5.67× more edges than the graphs that recent works. Moreover, <PERSON><PERSON><PERSON> can achieve up to 38.77× speedup over the existing methods.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229018"}, {"primary_key": "1203103", "vector": [], "sparse_vector": [], "title": "HTNet: Dynamic WLAN Performance Prediction using Heterogenous Temporal GNN.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Predicting the throughput of WLAN deployments is a classic problem that occurs in the design of robust and high performance WLAN systems. However, due to the increasingly complex communication protocols and the increase in interference between devices in denser and denser WLAN deployments, traditional methods either have substantial runtime or enormous prediction error and hence cannot be applied in downstream tasks. Recently, Graph Neural Networks have been proven to be powerful graph analytic models and have been broadly applied to various networking problems such as link scheduling and power allocation. In this work, we propose HTNet, a specialized Heterogeneous Temporal Graph Neural Network that extracts features from dynamic WLAN deployments. Analyzing the unique graph structure of WLAN deployment graphs, we show that HTNet achieves the maximum expressive power on each snapshot. Based on a powerful message passing scheme, HTNet requires fewer number of layers compared with other GNN-based methods which entails less supporting data and runtime. To evaluate the performance of HTNet, we prepare six different setups with more than five thousands dense dynamic WLAN deployments that cover a wide range of real-world scenarios. HTNet achieves the lowest prediction error on all six setups with an average improvement of 25.3% over the state-of-the-art methods.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229047"}, {"primary_key": "1203106", "vector": [], "sparse_vector": [], "title": "A Reinforcement Learning Approach for Minimizing Job Completion Time in Clustered Federated Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>ng <PERSON>", "<PERSON><PERSON>"], "summary": "Federated Learning (FL) enables potentially a large number of clients to collaboratively train a global model with the coordination of a central cloud server without exposing client raw data. However, the FL model convergence performance, often measured by the job completion time, is hindered by two critical factors: non independent and identically distributed (non-IID) data across clients and the straggler effect. In this work, we propose a clustered FL framework, MCFL, to minimize the job completion time by mitigating the influence of non-IID data and the straggler effect while guaranteeing the FL model convergence performance. MCFL builds upon a two-stage operation: i) a clustering algorithm constructs clusters, each containing clients with similar computing and communications capabilities to combat the straggler effect within a cluster; ii) a deep reinforcement learning (DRL) algorithm based on soft actor-critic with discrete actions intelligently selects a subset of clients from each cluster to mitigate the impact of non-IID data, and derives the number of intra-cluster aggregation iterations for each cluster to reduce the straggler effect among clusters. Extensive testbed experiments are conducted under various configurations to verify the efficacy of MCFL. The results show that MCFL can reduce the job completion time by up to 70% compared with three state-of-the-art FL frameworks.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228925"}, {"primary_key": "1203107", "vector": [], "sparse_vector": [], "title": "Roland: Robust In-band Parallel Communication for Magnetic MIMO Wireless Power Transfer System.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In recent years, receiver (RX) feedback communication has attracted increasing attention to enhance the charging performance for magnetic resonant coupling (MRC) based wireless power transfer (WPT) systems. People prefer to adopt the in-band implementation with minimal overhead costs. However, the influence of RX-RX coupling couldn't be directly ignored like that in the RFID field, i.e., strong couplings and relay phenomenon. In order to solve these two critical issues, we propose a Robust layer-level in-band parallel communication protocol for MIMO MRC-WPT systems (called Roland). Technically, we first utilize the observed channel decomposability to construct group-level channel relationship graph for eliminating the interference caused by strong RX-RX couplings. Then, we generalize such method to deal with the RX dependency due to relay phenomenon. Finally, we conduct extensive experiments on a prototype testbed to evaluate the effectiveness of the proposed scheme. The results demonstrate that our Roland could provide ≥95% average decoding accuracy for concurrent feedback communication of 14 devices. Compared with the state-of-the-art solution, the proposed protocol Roland can achieve an average decoding accuracy improvement of 20.41%.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10229109"}, {"primary_key": "1203108", "vector": [], "sparse_vector": [], "title": "Mercury: Fast Transaction Broadcast in High Performance Blockchain Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Blockchain systems must be secure and offer high performance. These systems rely on transaction broadcast mechanisms to provide both of these features. Unfortunately, in today's systems, the broadcast mechanisms are highly inefficient.We present Mercury, a new transaction broadcast protocol designed for high performance blockchains. Mercury shortens the transaction propagation delay using two techniques: a virtual coordinate system and an early outburst strategy. Simulation results show that Mercury outperforms prior propagation schemes and decreases overall propagation latency by up to 44%. When implemented in Conflux, an open-source high-throughput blockchain system, Mercury reduces transaction propagation latency by over 50% with less than 5% bandwidth overhead.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228972"}, {"primary_key": "1203109", "vector": [], "sparse_vector": [], "title": "Breaking the Throughput Limit of LED-Camera Communication via Superposed Polarization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jinsong Han"], "summary": "With the popularity of LED infrastructure and the camera on smartphone, LED-Camera visible light communication (VLC) has become a realistic and promising technology. However, the existing LED-Camera VLC has limited throughput due to the sampling manner of camera. In this paper, by introducing a polarization dimension, we propose a hybrid modulation scheme with LED and polarization signals to boost throughput. Nevertheless, directly mixing LED and polarized signals may suffer from channel conflict. We exploit well-designed packet structure and Symmetric Return-to-Zero Inverted (SRZI) coding to overcome the conflict. In addition, in the demodulation of hybrid signal, we alleviate the noise caused by polarization on the LED signals by polarization background subtraction. We further propose a pixel-free approach to correct the perspective distortion caused by the shift of view angle by adding polarizers around the liquid crystal array. We build a prototype of this hybrid modulation scheme using off-the-shelf optical components. Extensive experimental results demonstrate that the hybrid modulation scheme can achieve reliable communication, achieving 13.4 kbps throughput, which is 400 % of the existing state-of-the-art LED-Camera VLC.", "published": "2023-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM53939.2023.10228936"}]