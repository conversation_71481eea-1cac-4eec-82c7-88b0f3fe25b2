# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
.pnp
.pnp.js
.log
/logs
logs/
tmp/
*.exe


# testing
coverage
# backend/test.ipynb

backend/data/aaa_crawler_code/output
backend/data/aaa_crawler_code_new/test_output
backend/data/aaa_crawler_code_new/sigkdd_papers
backend/pdf
backend/md

backend/data/acl-anthology-master
backend/data/continue
backend/data/database
backend/data/paper_acl
backend/data/paper_arxiv
backend/data/paper_deal
backend/data/paper_cv
backend/data/paper_jn
backend/data/papers
backend/data/tmp
backend/service/logs


public/data/papers

# next.js
.next/
out/
build
__pycache__/

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# turbo
.turbo

.vscode
backend/test_tags.ipynb
