import React, { useState } from 'react';
import J<PERSON>Z<PERSON> from 'jszip';
import { saveAs } from 'file-saver';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import type { CollectedPaper } from './collect';

interface DownloadDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  papers: CollectedPaper[];
  folderName: string;
}

function isPdfResponse(response: Response) {
  const contentType = response.headers.get('content-type');
  return contentType && contentType.toLowerCase().includes('application/pdf');
}

export const DownloadDialog: React.FC<DownloadDialogProps> = ({ open, onOpenChange, papers, folderName }) => {
  const [progress, setProgress] = useState(0);
  const [downloading, setDownloading] = useState(false);

  const handleDownload = async () => {
    setDownloading(true);
    setProgress(0);
    const zip = new JSZip();
    const notPdfUrls: string[] = [];
    let count = 0;
    for (let i = 0; i < papers.length; i++) {
      const paper = papers[i];
      const url = paper.pdf_url;
      const cn = paper.customName || paper.title_translation || '';
      const en = paper.title || '';

      // 如果 url 里边不包括 https 开头，只包含 http，则将其替换为 https
      if (url.startsWith('http')) {
        if (!url.startsWith('https')) {
          const newUrl = url.replace('http', 'https');
          papers[i].pdf_url = newUrl;
        }
      }

      // 文件名
      let fileName = '';
      if (cn && en && cn !== en) {
        fileName = `${cn}_${en}`;
      } else if (cn) {
        fileName = cn;
      } else {
        fileName = en || `paper_${i+1}`;
      }
      try {
        const response = await fetch(url);
        if (!response.ok) throw new Error('下载失败');
        const isPdf = isPdfResponse(response);
        const blob = await response.blob();
        if (isPdf) {
          zip.file(`${i+1}_${fileName.replace(/[/\\:*?"<>|]/g, '_')}.pdf`, blob);
        } else {
          notPdfUrls.push(url);
        }
      } catch (e) {
        notPdfUrls.push(url);
      }
      count++;
      setProgress(Math.round((count / papers.length) * 100));
    }
    if (notPdfUrls.length > 0) {
      zip.file('not_pdf_urls.txt', notPdfUrls.join('\n'));
    }
    const content = await zip.generateAsync({ type: 'blob' }, (metadata) => {
      setProgress(Math.round(metadata.percent));
    });
    saveAs(content, `${folderName || 'papers'}.zip`);
    setDownloading(false);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={downloading ? () => {} : onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>下载论文（{papers.length} 篇）</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          <div className="mb-2">将自动下载所有 PDF 论文，并将非 PDF 链接写入 txt 文件，全部打包为 zip。</div>
          <div className="w-full bg-gray-200 rounded h-3 mb-2">
            <div
              className="bg-blue-500 h-3 rounded"
              style={{ width: `${progress}%`, transition: 'width 0.2s' }}
            />
          </div>
          <div className="text-xs text-gray-500 mb-2">{progress}%</div>
        </div>
        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={downloading}>取消</Button>
          <Button onClick={handleDownload} disabled={downloading || papers.length === 0}>
            {downloading ? '下载中...' : '开始下载'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}; 