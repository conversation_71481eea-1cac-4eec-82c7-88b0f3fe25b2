[{"primary_key": "4403401", "vector": [], "sparse_vector": [], "title": "On Computing Nearest Neighbors with Applications to Decoding of Binary Linear Codes.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We propose a new decoding algorithm for random binary linear codes. The so-called information set decoding algorithm of <PERSON><PERSON><PERSON> (1962) achieves worst-case complexity\\(2^{0.121n}\\). In the late 80s, <PERSON> proposed a sort-and-match version for <PERSON><PERSON><PERSON>’s algorithm, on which all variants of the currently best known decoding algorithms are build. The fastest algorithm of <PERSON>, <PERSON>, <PERSON> and <PERSON> (2012) achieves running time\\(2^{0.102n}\\)in the full distance decoding setting and\\(2^{0.0494n}\\)with half (bounded) distance decoding. In this work we point out that the sort-and-match routine in <PERSON>’s algorithm is carried out in a non-optimal way, since the matching is done in a two step manner to realize anapproximate matchingup to a small number of error coordinates. Our observation is that such an approximate matching can be done by a variant of the so-called High Dimensional Nearest Neighbor Problem. Namely, out of two lists with entries from\\({\\mathbb F}_2^m\\)we have to find a pair with closest Hamming distance. We develop a new algorithm for this problem with sub-quadratic complexity which might be of independent interest in other contexts. Using our algorithm for full distance decoding improves <PERSON>’s complexity from\\(2^{0.117n}\\)to\\(2^{0.114n}\\). Since the techniques of <PERSON> et al apply for our algorithm as well, we eventually obtain the fastest decoding algorithm for binary linear codes with complexity\\(2^{0.097n}\\). In the half distance decoding scenario, we obtain a complexity of\\(2^{0.0473n}\\).", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_9"}, {"primary_key": "4403402", "vector": [], "sparse_vector": [], "title": "Disjunctions for Hash Proof Systems: New Constructions and Applications.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Hash Proof Systems were first introduced by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (Eurocrypt’02) as a tool to construct efficient chosen-ciphertext-secure encryption schemes. Since then, they have found many other applications, including password authenticated key exchange, oblivious transfer, and zero-knowledge arguments. One of the aspects that makes hash proof systems so interesting and powerful is that they can be seen as implicit proofs of membership for certain languages. As a result, by extending the family of languages that they can handle, one often obtains new applications or new ways to understand existing schemes. In this paper, we show how to construct hash proof systems for the disjunction of languages defined generically over cyclic, bilinear, and multilinear groups. Among other applications, this enables us to construct the most efficient one-time simulation-sound (quasi-adaptive) non-interactive zero-knowledge arguments for linear languages over cyclic groups, the first one-round group password-authenticated key exchange without random oracles, the most efficient threshold structure-preserving chosen- ciphertext-secure encryption scheme, and the most efficient one-round password authenticated key exchange in the UC framework.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46803-6_3"}, {"primary_key": "4403403", "vector": [], "sparse_vector": [], "title": "Twisted Polynomials and Forgery Attacks on GCM.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Polynomial hashing as an instantiation of universal hashing is a widely employed method for the construction of MACs and authenticated encryption (AE) schemes, the ubiquitous GCM being a prominent example. It is also used in recent AE proposals within the CAESAR competition which aim at providing nonce misuse resistance, such as POET. The algebraic structure of polynomial hashing has given rise to security concerns: At CRYPTO 2008, <PERSON><PERSON><PERSON> and <PERSON><PERSON>l describe key recovery attacks, and at FSE 2013, Procter and Cid provide a comprehensive framework for forgery attacks. Both approaches rely heavily on the ability to constructforgery polynomialshaving disjoint sets of roots, with many roots (“weak keys”) each. Constructing such polynomials beyond naïve approaches is crucial for these attacks, but still an open problem. In this paper, we comprehensively address this issue. We propose to usetwisted polynomialsfrom Ore rings as forgery polynomials. We show how to construct sparse forgery polynomials with full control over the sets of roots. We also achieve complete and explicit disjoint coverage of the key space by these polynomials. We furthermore leverage this new construction in an improved key recovery algorithm. As cryptanalytic applications of our twisted polynomials, we develop the first universal forgery attacks on GCM in the weak-key model that do not require nonce reuse. Moreover, we present universal weak-key forgeries for the nonce-misuse resistant AE scheme POET, which is a CAESAR candidate.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_29"}, {"primary_key": "4403404", "vector": [], "sparse_vector": [], "title": "Fully Structure-Preserving Signatures and Shrinking Commitments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Structure-preserving signatures are schemes in which public keys, messages, and signatures are all collections of source group elements of some bilinear groups. In this paper, we introduce fully structure-preserving signature schemes, with the additional requirement that even secret keys should be group elements. This new type of structure-preserving signatures allows for efficient non-interactive proofs of knowledge of the secret key and is useful in designing cryptographic protocols with strong security guarantees based on the simulation paradigm where the simulator has to extract the secret keys on-line. To gain efficiency, we construct shrinking structure-preserving trapdoor commitments. This is by itself an important primitive and of independent interest as it appears to contradict a known impossibility result. We argue that a relaxed binding property lets us circumvent the impossibility result while still retaining the usefulness of the primitive in important applications as mentioned above.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46803-6_2"}, {"primary_key": "4403405", "vector": [], "sparse_vector": [], "title": "How to Efficiently Evaluate RAM Programs with Malicious Security.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>ng Hu", "<PERSON><PERSON>", "<PERSON>"], "summary": "Secure 2-party computation (2PC) is becoming practical for some applications. However, most approaches are limited by the fact that the desired functionality must be represented as a boolean circuit. In response, random-access machines (RAM programs) have recently been investigated as a promising alternative representation. In this work, we present the first practical protocols for evaluating RAM programs with security against malicious adversaries. A useful efficiency measure is to divide the cost of malicious-secure evaluation of\\(f\\)by the cost of semi-honest-secure evaluation of\\(f\\). Our RAM protocols achieve ratios matching the state of the art for circuit-based 2PC. For statistical security\\(2^{-s}\\), our protocol without preprocessing achieves a ratio of\\(s\\); our online-offline protocol has a pre-processing phase and achieves online ratio\\(\\sim 2 s / \\log T\\), where\\(T\\)is the total execution time of the RAM program. To summarize, our solutions show that the “extra overhead” of obtaining malicious security for RAM programs (beyond what is needed for circuits) is minimal and does not grow with the running time of the program.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_27"}, {"primary_key": "4403406", "vector": [], "sparse_vector": [], "title": "Cryptographic Agents: Towards a Unified Theory of Computing on Encrypted Data.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We provide a new framework ofcryptographic agentsthat unifies various modern “cryptographic objects” — identity-based encryption, fully-homomorphic encryption, functional encryption, and various forms of obfuscation – similar to how the Universal Composition framework unifies various multi-party computation tasks like commitment, coin-tossing and zero-knowledge proofs. These cryptographic objects can all be cleanly modeled as “schemata” in our framework. Highlights of our framework include the following: We use a newindistinguishability preserving(IND-PRE) definition of security that interpolates indistinguishability and simulation style definitions, which (often) sidesteps the known impossibilities for the latter.IND-PRE-security is parameterized by the choice of the “test” family, such that by choosing different test families, one can obtain different levels of security for the same primitive (including various standard definitions in the literature). We present a notion ofreductionfrom one schema to another and a powerfulcomposition theoremwith respect to\\(\\mathsf{IND-PRE }\\)security. We show that obfuscation is a “complete” schema under this notion, under standard cryptographic assumptions. We also provide a stricter notion of reduction (\\(\\varDelta \\)-reduction) that composes even when security is only with respect to certain restricted test families of importance. Last but not the least, our framework can be used to model abstractions like the generic group model and the random oracle model, letting one translate a general class of constructions in these heuristic models to constructions based onstandard model assumptions. We also illustrate how our framework can be applied to specific primitives like obfuscation and functional encryption. We relate our definitions to existing definitions and also give new constructions and reductions between different primitives.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46803-6_17"}, {"primary_key": "4403407", "vector": [], "sparse_vector": [], "title": "Ciphers for MPC and FHE.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Designing an efficient cipher was always a delicate balance between linear and non-linear operations. This goes back to the design of DES, and in fact all the way back to the seminal work of <PERSON>. Here we focus, for the first time, on an extreme corner of the design space and initiate a study of symmetric-key primitives that minimize the multiplicative size and depth of their descriptions. This is motivated by recent progress in practical instantiations of secure multi-party computation (MPC), fully homomorphic encryption (FHE), and zero-knowledge proofs (ZK) where linear computations are, compared to non-linear operations, essentially “free”. We focus on the case of a block cipher, and propose the family of block ciphers “LowMC”, beating all existing proposals with respect to these metrics by far. We sketch several applications for such ciphers and give implementation comparisons suggesting that when encrypting larger amounts of data the new design strategy translates into improvements in computation and communication complexity by up to a factor of 5 compared to AES-128, which incidentally is one of the most competitive classical designs. Furthermore, we identify cases where “free XORs” can no longer be regarded as such but represent a bottleneck, hence refuting this commonly held belief with a practical example.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_17"}, {"primary_key": "4403408", "vector": [], "sparse_vector": [], "title": "More Efficient Oblivious Transfer Extensions with Security for Malicious Adversaries.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Oblivious transfer (OT) is one of the most fundamental primitives in cryptography and is widely used in protocols for secure two-party and multi-party computation. As secure computation becomes more practical, the need for practical large scale oblivious transfer protocols is becoming more evident. Oblivious transfer extensions are protocols that enable a relatively small number of “base-OTs” to be utilized to compute a very large number of OTs at low cost. In the semi-honest setting, <PERSON><PERSON> et al. (CRYPTO 2003) presented an OT extension protocol for which the cost of each OT (beyond the base-OTs) is just a few hash function operations. In the malicious setting, <PERSON> et al. (CRYPTO 2012) presented an efficient OT extension protocol for the setting of active adversaries, that is secure in the random oracle model. In this work, we present an OT extension protocol for the setting of malicious adversaries that is more efficient and uses less communication than previous works. In addition, our protocol can be proven secure in both the random oracle model, and in the standard model with a type of correlation robustness. Given the importance of OT in many secure computation protocols, increasing the efficiency of OT extensions is another important step forward to making secure computation practical.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_26"}, {"primary_key": "4403409", "vector": [], "sparse_vector": [], "title": "Inner Product Masking Revisited.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Masking is a popular countermeasure against side channel attacks. Many practical works use Boolean masking because of its simplicity, ease of implementation and comparably low performance overhead. Some recent works have explored masking schemes with higher algebraic complexity and have shown that they provide more security than Boolean masking at the cost of higher overheads. In particular, masking based on the inner product was shown to be practical, albeit not efficient, for a small security parameter, and at the same time provable secure in the domain of leakage resilient cryptography for a large security parameter. In this work we explore a security versus efficiency tradeoff and provide an improved and tweaked inner product masking. Our practical security evaluation shows that it is less secure than the original inner product masking but more secure than Boolean masking. Our performance evaluation shows that our scheme is only four times slower than Boolean masking and more than two times faster than the original inner product masking. Besides the practical security analysis we prove the security of our scheme and its masked operations in the threshold probing model.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_19"}, {"primary_key": "4403410", "vector": [], "sparse_vector": [], "title": "Cryptanalysis of SP Networks with Partial Non-Linear Layers.", "authors": ["<PERSON><PERSON>ya Bar-On", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Design of SP networks in which the non-linear layer is applied to only a part of the state in each round was suggested by <PERSON><PERSON> et al. at CHES 2013. Besides performance advantage on certain platforms, such a design allows for more efficient masking techniques that can mitigate side-channel attacks with a small performance overhead. In this paper we present generic techniques for differential and linear cryptanalysis of SP networks with partial non-linear layers, including an automated characteristic search tool and dedicated key-recovery algorithms. Our techniques can be used both for cryptanalysis of such schemes and for proving their security with respect to basic differential and linear cryptanalysis, succeeding where previous automated analysis tools seem to fail. We first apply our techniques to the block cipher <PERSON>or<PERSON> (designed by <PERSON><PERSON> et al. following their methodology), obtaining practical attacks on the cipher which where fully simulated on a single desktop PC in a few days. Then, we propose a mild change to Zor<PERSON>, and formally prove its security against basic differential and linear cryptanalysis. We conclude that there is no inherent flaw in the design strategy of <PERSON> et al., and it can be used in future designs, where our tools should prove useful.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_13"}, {"primary_key": "4403411", "vector": [], "sparse_vector": [], "title": "Improving NFS for the Discrete Logarithm Problem in Non-prime Finite Fields.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The aim of this work is to investigate the hardness of the discrete logarithm problem in fields GF(\\(p^n\\)) where\\(n\\)is a small integer greater than\\(1\\). Though less studied than the small characteristic case or the prime field case, the difficulty of this problem is at the heart of security evaluations for torus-based and pairing-based cryptography. The best known method for solving this problem is the Number Field Sieve (NFS). A key ingredient in this algorithm is the ability to find good polynomials that define the extension fields used in NFS. We design two new methods for this task, modifying the asymptotic complexity and paving the way for record-breaking computations. We exemplify these results with the computation of discrete logarithms over a field GF(\\(p^2\\)) whose cardinality is 180 digits (595 bits) long.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_6"}, {"primary_key": "4403412", "vector": [], "sparse_vector": [], "title": "Verified Proofs of Higher-Order Masking.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we study the problem of automatically verifying higher-order masking countermeasures. This problem is important in practice, since weaknesses have been discovered in schemes that were thought secure, but is inherently exponential: for\\(t\\)-order masking, it involves proving that every subset of\\(t\\)intermediate variables is distributed independently of the secrets. Some tools have been proposed to help cryptographers check their proofs, but are often limited in scope. We propose a new method, based on program verification techniques, to check the independence of sets of intermediate variables from some secrets. Our new language-based characterization of the problem also allows us to design and implement several algorithms that greatly reduce the number of sets of variables that need to be considered to prove this independence property onallvalid adversary observations. The result of these algorithms is either a proof of security or a set of observations on which the independence property cannot be proved. We focus on AES implementations to check the validity of our algorithms. We also confirm the tool’s ability to give useful information when proofs fail, by rediscovering existing attacks and discovering new ones.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_18"}, {"primary_key": "4403413", "vector": [], "sparse_vector": [], "title": "Mind the Gap: Modular Machine-Checked Proofs of One-Round Key Exchange Protocols.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "UsingEasyCrypt, we formalize a new modular security proof for one-round authenticated key exchange protocols in the random oracle model. Our proof improves earlier work by <PERSON><PERSON><PERSON> and <PERSON> (ASIACRYPT 2005) in three significant ways: we consider a stronger adversary model, we provide support tailored to protocols that utilize the\\(\\mathsf {Naxos}\\)trick, and we support proofs under the Computational DH assumption not relying on Gap oracles. Furthermore, our modular proof can be used to obtain concrete security proofs for protocols with or without adversarial key registration. We use this support to investigate, still usingEasyCrypt, the connection between proofs without Gap assumptions and adversarial key registration. For the case of honestly generated keys, we obtain the first proofs of the\\(\\mathsf {Naxos}\\)and\\(\\mathsf {Nets}\\)protocols under the Computational DH assumption. For the case of adversarial key registration, we obtain machine-checked and modular variants of the well-known proofs for\\(\\mathsf {Naxos}\\),\\(\\mathsf {Nets}\\), and\\(\\mathsf Naxos \\text {}\\)+.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46803-6_23"}, {"primary_key": "4403414", "vector": [], "sparse_vector": [], "title": "Resisting Randomness Subversion: Fast Deterministic and Hedged Public-Key Encryption in the Standard Model.", "authors": ["<PERSON><PERSON>", "Viet Tung Hoang"], "summary": "This paper provides the firstefficient,standard-model,fully-secureschemes for some related and challenging forms of public-key encryption (PKE), namely deterministic and hedged PKE. These forms of PKE defend against subversion of random number generators, an end given new urgency by recent revelations on the nature and extent of such subversion.We resolve the (recognized) technical challenges in reaching these goals via a new paradigm that combines UCEs (universal computational extractors) with LTDFs (lossy trapdoor functions). Crucially, we rely only on a weak form of UCE, namely security for statistically (rather than computationally) unpredictable sources. We then define and achieve unique-ciphertext PKE as a way to defend against implementation subversion via algorithm-substitution attacks.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46803-6_21"}, {"primary_key": "4403415", "vector": [], "sparse_vector": [], "title": "SPHINCS: Practical Stateless Hash-Based Signatures.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Zooko Wilcox-O&apos;Hearn"], "summary": "This paper introduces a high-security post-quantum stateless hash-based signature scheme that signs hundreds of messages per second on a modern 4-core 3.5GHz Intel CPU. Signatures are 41 KB, public keys are 1 KB, and private keys are 1 KB. The signature scheme is designed to provide long-term\\(2^{128}\\)security even against attackers equipped with quantum computers. Unlike most hash-based designs, this signature scheme is stateless, allowing it to be a drop-in replacement for current signature schemes.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_15"}, {"primary_key": "4403416", "vector": [], "sparse_vector": [], "title": "Hosting Services on an Untrusted Cloud.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider a scenario where a service provider has created a software service\\(S\\)and desires to outsource the execution of this service to an untrusted cloud. The software service contains secrets that the provider would like to keep hidden from the cloud. For example, the software might contain a secret database, and the service could allow users to make queries to different slices of this database depending on the user’s identity. This setting presents significant challenges not present in previous works on outsourcing or secure computation. Because secrets in the software itself must be protected against an adversary that has full control over the cloud that is executing this software, our notion implies indistinguishability obfuscation. Furthermore, we seek to protect knowledge of the software\\(S\\)to the maximum extent possible even if the cloud can collude with several corrupted users. In this work, we provide the first formalizations of security for this setting, yielding our definition of asecure cloud service scheme. We provide constructions of secure cloud service schemes assuming indistinguishability obfuscation, one-way functions, and non-interactive zero-knowledge proofs. At the heart of our paper are novel techniques to allow parties to simultaneously authenticate and securely communicate with an obfuscated program, while hiding this authentication and communication from the entity in possession of the obfuscated program.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46803-6_14"}, {"primary_key": "4403417", "vector": [], "sparse_vector": [], "title": "Semantically Secure Order-Revealing Encryption: Multi-input Functional Encryption Without Obfuscation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Deciding “greater-than” relations among data items just given their encryptions is at the heart of search algorithms on encrypted data, most notably, non-interactive binary search on encrypted data. Order-preserving encryption provides one solution, but provably provides only limited security guarantees. Two-input functional encryption is another approach, but requires the full power of obfuscation machinery and is currently not implementable. We construct the first implementable encryption system supporting greater-than comparisons on encrypted data that provides the “best-possible” semantic security. In our scheme there is a public algorithm that given two ciphertexts as input, reveals the order of the corresponding plaintexts and nothing else. Our constructions are inspired by obfuscation techniques, but do not use obfuscation. For example, to compare two 16-bit encrypted values (e.g., salaries or age) we only need a 9-way multilinear map. More generally, comparing\\(k\\)-bit values requires only a\\((k/2+1)\\)-way multilinear map. The required degree of multilinearity can be further reduced, but at the cost of increasing ciphertext size. Beyond comparisons, our results give an implementable secret-key multi-input functional encryption scheme for functionalities that can be expressed as (generalized) branching programs of polynomial length and width. Comparisons are a special case of this class, where for\\(k\\)-bit inputs the branching program is of length\\(k+1\\)and width\\(4\\).", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46803-6_19"}, {"primary_key": "4403418", "vector": [], "sparse_vector": [], "title": "Function Secret Sharing.", "authors": ["<PERSON><PERSON>", "<PERSON>v <PERSON>", "<PERSON><PERSON>"], "summary": "Motivated by the goal of securely searching and updating distributed data, we introduce and study the notion offunction secret sharing(FSS). This new notion is a natural generalization of distributed point functions (DPF), a primitive that was recently introduced by <PERSON><PERSON> and <PERSON><PERSON> (Eurocrypt 2014). Given a positive integer\\(p\\ge 2\\)and a class\\(\\mathcal F\\)of functions\\(f:\\{0,1\\}^n\\rightarrow \\mathbb G\\), where\\(\\mathbb G\\)is an Abelian group, a\\(p\\)-party FSS scheme for\\(\\mathcal F\\)allows one to split each\\(f\\in \\mathcal F\\)into\\(p\\)succinctly described functions\\(f_i:\\{0,1\\}^n\\rightarrow \\mathbb G\\),\\(1\\le i\\le p\\), such that: (1)\\(\\sum _{i=1}^p f_i=f\\), and (2) any strict subset of the\\(f_i\\)hides\\(f\\). Thus, an FSS for\\(\\mathcal F\\)can be thought of as method for succinctly performing an “additive secret sharing” of functions from\\(\\mathcal F\\). The original definition of DPF coincides with a two-party FSS for the class of point functions, namely the class of functions that have a nonzero output on at most one input. We present two types of results. First, we obtain efficiency improvements and extensions of the original DPF construction. Then, we initiate a systematic study of general FSS, providing some constructions and establishing relations with other cryptographic primitives. More concretely, we obtain the following main results: Improved DPF.We present an improved (two-party) DPF construction from a pseudorandom generator (PRG), reducing the length of the key describing each\\(f_i\\)from\\(O(\\lambda \\cdot n^{\\log _23})\\)to\\(O(\\lambda n)\\), where\\(\\lambda \\)is the PRG seed length. Multi-party DPF.We present the first nontrivial construction of a\\(p\\)-party DPF for\\(p\\ge 3\\), obtaining a near-quadratic improvement over a naive construction that additively shares the truth-table of\\(f\\). This constrcution too can be based on any PRG. FSS for simple functions.We present efficient PRG-based FSS constructions for natural function classes that extend point functions, including interval functions and partial matching functions. A study of general FSS.We show several relations between general FSS and other cryptographic primitives. These include a construction of general FSS via obfuscation, an indication for the implausibility of constructing general FSS from weak cryptographic assumptions such as the existence of one-way functions, a completeness result, and a relation with pseudorandom functions.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46803-6_12"}, {"primary_key": "4403419", "vector": [], "sparse_vector": [], "title": "On the Behaviors of Affine Equivalent Sboxes Regarding Differential and Linear Attacks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper investigates the effect of affine transformations of the Sbox on the maximal expected differential probability\\(\\mathrm {MEDP}\\)and linear potential\\(\\mathrm {MELP}\\)over two rounds of a substitution-permutation network, when the diffusion layer is linear over the finite field defined by the Sbox alphabet. It is mainly motivated by the fact that the\\(2\\)-round\\(\\mathrm {MEDP}\\)and\\(\\mathrm {MELP}\\)of the AES both increase when the AES Sbox is replaced by the inversion in\\(\\mathbf {F}_{2^8}\\). Most notably, we give new upper bounds on these two quantities which are not invariant under affine equivalence. Moreover, within a given equivalence class, these new bounds are maximal when the considered Sbox is an involution. These results point out that different Sboxes within the same affine equivalence class may lead to different two-round\\(\\mathrm {MEDP}\\)and\\(\\mathrm {MELP}\\). In particular, we exhibit some examples where the basis chosen for defining the isomorphism between\\(\\mathbf {F}_2^m\\)and\\(\\mathbf {F}_{2^m}\\)affects these values. For Sboxes with some particular properties, including all Sboxes of the form\\(A(x^s)\\)as in the AES, we also derive some lower and upper bounds for the\\(2\\)-round\\(\\mathrm {MEDP}\\)and\\(\\mathrm {MELP}\\)which hold for any MDS linear layer.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_3"}, {"primary_key": "4403420", "vector": [], "sparse_vector": [], "title": "Executable Proofs, Input-Size Hiding Secure Computation and a New Ideal World.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In STOC 1987, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [GMW87] proved a fundamental result: it is possible to securely evaluate any function. Their security formulation consisted of transforming a real-world adversary into an ideal-world one and became a de facto standard for assessing security of protocols. In this work we propose a new approach for the ideal world. Our new definition preserves the unconditional security of ideal-world executions and follows the spirit of the real/ideal world paradigm. Moreover we show that our definition is equivalent to that of [GMW87] when the input size is public, thus it is a strict generalization of [GMW87]. In addition, we prove that our new formulation is useful by showing that it allows the construction of protocols for input-size hiding secure two-party computation for any two-party functionality under standard assumptions and secure against malicious adversaries. More precisely we show that in our model, in addition to securely evaluating every two-party functionality, one can also protect the input-size privacy of one of the two players. Such an input-size hiding property is not implied by the standard definitions for two-party computation and is not satisfied by known constructions for secure computation. This positively answers a question posed by [LNO13] and  [CV12]. Finally, we show that obtaining such a security notion under a more standard definition (one with a more traditional ideal world) would imply a scheme for “proofs of polynomial work”, a primitive that seems unlikely to exist under standard assumptions. Along the way, we will introduce the notion of “executable proof”, which will be used in our ideal-world formulation and may be of independent interest.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46803-6_18"}, {"primary_key": "4403421", "vector": [], "sparse_vector": [], "title": "Improved Dual System ABE in Prime-Order Groups via Predicate Encodings.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>e"], "summary": "We present a modular framework for the design of efficient adaptively secure attribute-based encryption (ABE) schemes for a large class of predicates under the standard\\(k\\)-Lin assumption in prime-order groups; this is the first uniform treatment of dual system ABE across different predicates and across both composite and prime-order groups. Via this framework, we obtain concrete efficiency improvements for several ABE schemes. Our framework has three novel components over prior works: (i) new techniques for simulating composite-order groups in prime-order ones, (ii) a refinement of prior encodings framework for dual system ABE in composite-order groups, (iii) an extension to weakly attribute-hiding predicate encryption (which includes anonymous identity-based encryption as a special case).", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46803-6_20"}, {"primary_key": "4403422", "vector": [], "sparse_vector": [], "title": "Cryptanalysis of the Multilinear Map over the Integers.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We describe a polynomial-time cryptanalysis of the (approximate) multilinear map of <PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON> (CLT). The attack relies on an adaptation of the so-calledzeroizingattack against the Garg, Gentry and Halevi (GGH) candidate multilinear map. Zeroizing is much more devastating for CLT than for GGH. In the case of GGH, it allows to break generalizations of the Decision Linear and Subgroup Membership problems from pairing-based cryptography. For CLT, this leads to a total break: all quantities meant to be kept secret can be efficiently and publicly recovered.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_1"}, {"primary_key": "4403423", "vector": [], "sparse_vector": [], "title": "Fully Homomophic Encryption over the Integers Revisited.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Two main computational problems serve as security foundations of current fully homomorphic encryption schemes: <PERSON><PERSON>’s Learning With Errors problem (\\(\\mathrm {LWE}\\)) and <PERSON><PERSON><PERSON>’s Approximate Greatest Common Divisor problem (\\(\\mathrm {AGCD}\\)). Our first contribution is a reduction from\\(\\mathrm {LWE}\\)to\\(\\mathrm {AGCD}\\). As a second contribution, we describe a new\\(\\mathrm {AGCD}\\)-based fully homomorphic encryption scheme, which outperforms all prior\\(\\mathrm {AGCD}\\)-based proposals: its security does not rely on the presumed hardness of the so-called Sparse Subset Sum problem, and the bit-length of a ciphertext is only\\(\\widetilde{O}(\\lambda )\\), where\\(\\lambda \\)refers to the security parameter.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_20"}, {"primary_key": "4403424", "vector": [], "sparse_vector": [], "title": "Cluster Computing in Zero Knowledge.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Madars Virza"], "summary": "Large computations, when amenable to distributed parallel execution, are often executed on computer clusters, for scalability and cost reasons. Such computations are used in many applications, including, to name but a few, machine learning, webgraph mining, and statistical machine translation. Oftentimes, though, the input data is private and only the result of the computation can be published. Zero-knowledge proofs would allow, in such settings, to verify correctness of the output without leaking (additional) information about the input. In this work, we investigate theoretical and practical aspects ofzero-knowledge proofs for cluster computations. We design, build, and evaluate zero-knowledge proof systems for which: (i) a proof attests to the correct execution of a cluster computation; and (ii) generating the proof is itself a cluster computation that is similar in structure and complexity to the original one. Concretely, we focus on MapReduce, an elegant and popular form of cluster computing. Previous zero-knowledge proof systems can in principle prove a MapReduce computation’s correctness, via a monolithic NP statement that reasons about all mappers, all reducers, and shuffling. However, it is not clear how to generate the proof for such monolithic statements via parallel execution by a distributed system. Our work demonstrates, by theory and implementation, that proof generation can be similar in structure and complexity to the original cluster computation. Our main technique is a bootstrapping theorem for succinct non-interactive arguments of knowledge (SNARKs) that shows how, via recursive proof composition and Proof-Carrying Data, it is possible to transform any SNARK into adistributed SNARK for MapReducewhich proves, piecewise and in a distributed way, the correctness of every step in the original MapReduce computation as well as their global consistency.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46803-6_13"}, {"primary_key": "4403425", "vector": [], "sparse_vector": [], "title": "On the Provable Security of the Iterated Even-Mansour Cipher Against Related-Key and Chosen-Key Attacks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The iterated Even-Mansour cipher is a construction of a block cipher from\\(r\\)public permutations\\(P_1,\\ldots ,P_r\\)which abstracts in a generic way the structure of key-alternating ciphers. The indistinguishability of this construction from a truly random permutation by an adversary with oracle access to the inner permutations\\(P_1,\\ldots ,P_r\\)has been investigated in a series of recent papers. This construction has also been shown to be (fully) indifferentiable from an ideal cipher for a sufficient number of rounds (five or twelve depending on the assumptions on the key-schedule). In this paper, we extend this line of work by considering the resistance of the iterated Even-Mansour cipher to xor-induced related-key attacks (i.e., related-key attacks where the adversary is allowed to xor any constant of its choice to the secret key) and to chosen-key attacks. For xor-induced related-key attacks, we first provide a distinguishing attack for two rounds, assuming the key-schedule is linear. We then prove that for a linear key-schedule, three rounds yield a cipher which is secure against xor-induced related-key attacks up to\\( \\mathcal {O} (2^{\\frac{n}{2}})\\)queries of the adversary, whereas for a nonlinear key-schedule, one round is sufficient to obtain a similar security bound. We also show that the iterated Even-Mansour cipher with four rounds offers some form of provable resistance to chosen-key attacks, which is the minimal number of rounds to achieve this property. The main technical tool that we use to prove this result issequential indifferentiability, a weakened variant of (full) indifferentiability introduced by Mandalet al.(TCC 2010).", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_23"}, {"primary_key": "4403426", "vector": [], "sparse_vector": [], "title": "Linear Secret Sharing Schemes from Error Correcting Codes and Universal Hash Functions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present a novel method for constructing linear secret sharing schemes (LSSS) from linear error correcting codes and linear universal hash functions in a blackbox way. The main advantage of this new construction is that the privacy property of the resulting secret sharing scheme essentially becomes independent of the code we use, only depending on its rate. This allows us to fully harness the algorithmic properties of recent code constructions such as efficient encoding and decoding or efficient list-decoding. Choosing the error correcting codes and universal hash functions involved carefully, we obtain solutions to the following open problems: A linear near-threshold secret sharing scheme with both linear time sharing and reconstruction algorithms and large secrets (i.e. secrets of size\\(\\Omega (n)\\)). Thus, the computational overhead per shared bit in this scheme isconstant. An efficiently reconstructible robust secret sharing scheme for\\(n/3 \\le t < (1 - \\epsilon ) \\cdot n/2\\)corrupted players (for any constant\\(\\epsilon > 0\\)) with shares of optimal size\\(O(1 + \\lambda / n)\\)and secrets of size\\(\\Omega (n + \\lambda )\\), where\\(\\lambda \\)is the security parameter.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46803-6_11"}, {"primary_key": "4403427", "vector": [], "sparse_vector": [], "title": "Leakage-Resilient Circuits Revisited - Optimal Number of Computing Components Without Leak-Free Hardware.", "authors": ["<PERSON>-<PERSON>ed", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Side channel attacks – attacks that exploit implementation-dependent information of a cryptosystem – have been shown to be highly detrimental, and the cryptographic community has recently focused on developing techniques for securing implementations against such attacks. An important model calledOnly Computation Leaks(OCL) [Micali and <PERSON><PERSON>, TCC ’04] and its stronger variants were proposed to model a broad class of leakage attacks (a type of side-channel attack). These models allow for unbounded, arbitrary leakage as long as (1) information in each leakage observation is bounded, and (2) different parts of the computation leak independently. Various results and techniques have been developed for these models and we continue this line of research in the current work. We address the problem of compiling any circuit into a circuit secure against OCL attacks. In order to leverage the OCL assumption, the resulting circuit will be split into components, where at any point in time only a single component is active. Optimally, we would like to output a circuit that has only one component, and no part of the computation needs to be leak-free. However, this task is impossible due to the result of <PERSON> et al. [JACM ’12]. The current state-of-the-art constructions achieve either two components with additional leak-free hardware, or many components without leak-free hardware. In this work, we show how to achieve the best of both worlds: We construct two-component OCL schemes without relying on leak-free components. Our approach is general and modular – we develop generic techniques to remove the hardware component from hardware-based constructions, when the functionality provided by the hardware satisfies some properties. Our techniques use universal deniable encryption (recently constructed by Sahai and Water [STOC ’14] using indistinguishable obfuscation) and non-committing encryption in a novel way. Then, we observe that the functionalities of the hardware used in previous two-component constructions of Juma and Vahlis [Crypto ’10], and Dziembowski and Faust [TCC ’12] satisfy the required properties. The techniques developed in this paper have deep connections with adaptively secure and leakage tolerant multi-party computation (MPC). Our constructions immediately yield adaptively secure and leakage tolerant MPC protocols for any no-input randomized functionality in the semi-honest model. The result holds in the CRS model, without pre-processing. Our results also have implications to two-party leakage tolerant computation for arbitrary functionalities, which we obtain by combining our constructions with a recent result of Bitansky, Dachman-Soled, and Lin [Crypto ’14].", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46803-6_5"}, {"primary_key": "4403428", "vector": [], "sparse_vector": [], "title": "Cryptanalytic Time-Memory-Data Tradeoffs for FX-Constructions with Applications to PRINCE and PRIDE.", "authors": ["<PERSON><PERSON>"], "summary": "The FX-construction was proposed in 1996 by <PERSON><PERSON> and <PERSON><PERSON><PERSON> as a generalization of the DESX scheme. The construction increases the security of an\\(n\\)-bit core block cipher with a\\(\\kappa \\)-bit key by using two additional\\(n\\)-bit masking keys. Recently, several concrete instances of the FX-construction were proposed, including PRINCE (proposed at Asiacrypt 2012) and PRIDE (proposed at CRYPTO 2014). These ciphers have\\(n=\\kappa =64\\), and are proven to guarantee about\\(127-d\\)bits of security, assuming that their core ciphers are ideal, and the adversary can obtain at most\\(2^d\\)data. In this paper, we devise new cryptanalytic time-memory-data tradeoff attacks on FX-constructions. While our attacks do not contradict the security proof of PRINCE and PRIDE, nor pose an immediate threat to their users, some specific choices of tradeoff parameters demonstrate that the security margin of the ciphers against practical attacks is smaller than expected. Our techniques combine a special form of time-memory-data tradeoffs, typically applied to stream ciphers, with recent analysis of FX-constructions by <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_10"}, {"primary_key": "4403429", "vector": [], "sparse_vector": [], "title": "Cube Attacks and Cube-Attack-Like Cryptanalysis on the Round-Reduced Keccak Sponge Function.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we comprehensively study the resistance of keyed variants of SHA-3 (Keccak) against algebraic attacks. This analysis covers a wide range of key recovery, MAC forgery and other types of attacks, breaking up to 9 rounds (out of the full 24) of the Keccak internal permutation much faster than exhaustive search. Moreover, some of our attacks on the 6-round Keccak are completely practical and were verified on a desktop PC. Our methods combine cube attacks (an algebraic key recovery attack) and related algebraic techniques with structural analysis of the Keccak permutation. These techniques should be useful in future cryptanalysis of Keccak and similar designs. Although our attacks break more rounds than previously published techniques, the security margin of Keccak remains large. For Keyak – the Keccak-based authenticated encryption scheme – the nominal number of rounds is 12 and therefore its security margin is smaller (although still sufficient).", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_28"}, {"primary_key": "4403430", "vector": [], "sparse_vector": [], "title": "A Formal Treatment of Backdoored Pseudorandom Generators.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We provide a formal treatment of backdoored pseudorandom generators (PRGs). Here a saboteur chooses a PRG instance for which she knows a trapdoor that allows prediction of future (and possibly past) generator outputs. This topic was formally studied by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, but only in a limited form and not in the context of subverting cryptographic protocols. The latter has become increasingly important due to revelations about NIST’s backdoored Dual EC PRG and new results about its practical exploitability using a trapdoor. We show that backdoored PRGs are equivalent to public-key encryption schemes with pseudorandom ciphertexts. We use this equivalence to build backdoored PRGs that avoid a well known drawback of the Dual EC PRG, namely biases in outputs that an attacker can exploit without the trapdoor. Our results also yield a number of new constructions and an explanatory framework for why there are no reported observations in the wild of backdoored PRGs using only symmetric primitives. We also investigate folklore suggestions for countermeasures to backdoored PRGs, which we callimmunizers. We show that simply hashing PRG outputs is not an effective immunizer against an attacker that knows the hash function in use. Salting the hash, however, does yield a secure immunizer, a fact we prove using a surprisingly subtle proof in the random oracle model. We also give a proof in the standard model under the assumption that the hash function is a universal computational extractor (a recent notion introduced by <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>).", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_5"}, {"primary_key": "4403431", "vector": [], "sparse_vector": [], "title": "Making Masking Security Proofs Concrete - Or How to Evaluate the Security of Any Leaking Device.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We investigate the relationships between theoretical studies of leaking cryptographic devices and concrete security evaluations with standard side-channel attacks. Our contributions are in four parts. First, we connect the formal analysis of the masking countermeasure proposed by <PERSON><PERSON> et al. (Eurocrypt 2014) with the Eurocrypt 2009 evaluation framework for side-channel key recovery attacks. In particular, we re-state their main proof for the masking countermeasure based on a mutual information metric, which is frequently used in concrete physical security evaluations. Second, we discuss the tightness of the Eurocrypt 2014 bounds based on experimental case studies. This allows us to conjecture a simplified link between the mutual information metric and the success rate of a side-channel adversary, ignoring technical parameters and proof artifacts. Third, we introduce heuristic (yet well-motivated) tools for the evaluation of the masking countermeasure when its independent leakage assumption is not perfectly fulfilled, as it is frequently encountered in practice. Thanks to these tools, we argue that masking with non-independent leakages may provide improved security levels in certain scenarios. Eventually, we consider the tradeoff between measurement complexity and key enumeration in divide-and-conquer side-channel attacks, and show that it can be predicted based on the mutual information metric, by solving a non-linear integer programming problem for which efficient solutions exist. The combination of these observations enables significant reductions of the evaluation costs for certification bodies.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_16"}, {"primary_key": "4403432", "vector": [], "sparse_vector": [], "title": "Better Algorithms for LWE and LWR.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The Learning With Error problem (LWE) is becoming more and more used in cryptography, for instance, in the design of some fully homomorphic encryption schemes. It is thus of primordial importance to find the best algorithms that might solve this problem so that concrete parameters can be proposed. The BKW algorithm was proposed by <PERSON><PERSON> et al. as an algorithm to solve the Learning Parity with Noise problem (LPN), a subproblem of LWE. This algorithm was then adapted to LWE by <PERSON><PERSON> et al. In this paper, we improve the algorithm proposed by <PERSON><PERSON> et al. by using multidimensional Fourier transforms. Our algorithm is, to the best of our knowledge, the fastest LWE solving algorithm. Compared to the work of <PERSON><PERSON> et al. we greatly simplify the analysis, getting rid of integrals which were hard to evaluate in the final complexity. We also remove some heuristics on rounded Gaussians. Some of our results on rounded Gaussians might be of independent interest. Moreover, we also analyze algorithms solving LWE with discrete Gaussian noise. Finally, we apply the same algorithm to the Learning With Rounding problem (LWR) for prime\\(q\\), a deterministic counterpart to LWE. This problem is getting more and more attention and is used, for instance, to design pseudorandom functions. To the best of our knowledge, our algorithm is the first algorithm applied directly to LWR. Furthermore, the analysis of LWR contains some technical results of independent interest.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_8"}, {"primary_key": "4403433", "vector": [], "sparse_vector": [], "title": "FHEW: Bootstrapping Homomorphic Encryption in Less Than a Second.", "authors": ["Léo Du<PERSON>", "<PERSON><PERSON>"], "summary": "The main bottleneck affecting the efficiency of all known fully homomorphic encryption (FHE) schemes is Gentry’s bootstrapping procedure, which is required to refresh noisy ciphertexts and keep computing on encrypted data. Bootstrapping in the latest implementation of FHE, the HElib library of Halevi and Shoup (Crypto 2014), requires about six minutes. We present a new method to homomorphically compute simple bit operations, and refresh (bootstrap) the resulting output, which runs on a personal computer in just about half a second. We present a detailed technical analysis of the scheme (based on the worst-case hardness of standard lattice problems) and report on the performance of our prototype implementation.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_24"}, {"primary_key": "4403434", "vector": [], "sparse_vector": [], "title": "Noisy Leakage Revisited.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Physical side-channel leakages are an important threat for cryptographic implementations. One of the most prominent countermeasures against such leakage attacks is the use of a masking scheme. A masking scheme conceals the sensitive information by randomizing intermediate values thereby making the physical leakage independent of the secret. An important practical leakage model to analyze the security of a masking scheme is the so-called noisy leakage model of <PERSON>uff and Rivain (Eurocrypt’13). Unfortunately, security proofs in the noisy leakage model require a technically involved information theoretic argument. Very recently, <PERSON><PERSON> et al. (Eurocrypt’14) showed that security in the probing model of <PERSON><PERSON> et al. (Crypto’03) implies security in the noisy leakage model. Unfortunately, the reduction to the probing model is non-tight and requires a rather counter-intuitive growth of the amount of noise, i.e., the Prouff-Rivain bias parameter decreases proportional to the size of the set\\({\\mathcal X}\\)of the elements that are leaking (e.g., if the leaking elements are bytes, then\\(\\left| {\\mathcal X}\\right| = 256\\)). The main contribution of our work is to eliminate this non-optimality in the reduction by introducing an alternative leakage model, that we call theaverage probing model. We show a tight reduction between the noisy leakage model and the much simpler average random probing model; in fact, we show that these two models are essentially equivalent. We demonstrate the potential of this equivalence by two applications: We show security of the additive masking scheme used in many previous works for a constant bias parameter. We show that the compiler of Ishai et al. (Crypto’03) is secure in the average probing model (assuming a simple leak free component). This results into security with anoptimal bias parameterof the noisy leakage for the ISW construction.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46803-6_6"}, {"primary_key": "4403435", "vector": [], "sparse_vector": [], "title": "Privacy-Free Garbled Circuits with Applications to Efficient Zero-Knowledge.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In the last few years garbled circuits (GC) have been elevated from being merely a component in <PERSON>’s protocol for secure two-party computation, to a cryptographic primitive in its own right, following the growing number of applications that use GCs. Zero-Knowledge (ZK) protocols is one of these examples: In a recent paper Jawureket al.[J<PERSON>13] showed that GCs can be used to construct efficient ZK proofs for unstructured languages. In this work we show that due to the property of this particular scenario (i.e., one of the parties knows all the secret input bits, and therefore all intermediate values in the computation), we can construct more efficient garbling schemes specifically tailored to this goal. As a highlight of our result, in one of our constructions onlyone ciphertextper gate needs to be communicated and XOR gates never require any cryptographic operations. In addition to making a step forward towards more practical ZK, we believe that our contribution is also interesting from a conceptual point of view: in the terminology of <PERSON><PERSON> al.[BHR12] our garbling schemes achieve authenticity, but no privacy nor obliviousness, therefore representing the firstnaturalseparation between those notions.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46803-6_7"}, {"primary_key": "4403436", "vector": [], "sparse_vector": [], "title": "The Bitcoin Backbone Protocol: Analysis and Applications.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Bitcoin is the first and most popular decentralized cryptocurrency to date. In this work, we extract and analyze the core of the Bitcoin protocol, which we term the Bitcoinbackbone, and prove two of its fundamental properties which we callcommon prefixandchain qualityin the static setting where the number of players remains fixed. Our proofs hinge on appropriate and novel assumptions on the “hashing power” of the adversary relative to network synchronicity; we show our results to be tight under high synchronization. Next, we propose and analyze applications that can be built “on top” of the backbone protocol, specifically focusing on Byzantine agreement (BA) and on the notion of a public transaction ledger. Regarding BA, we observe that <PERSON><PERSON><PERSON>’s suggestion falls short of solving it, and present a simple alternative which works assuming that the adversary’s hashing power is bounded by\\(1/3\\). The public transaction ledger captures the essence of Bitcoin’s operation as a cryptocurrency, in the sense that it guarantees the liveness and persistence of committed transactions. Based on this notion we describe and analyze the Bitcoin system as well as a more elaborate BA protocol, proving them secure assuming high network synchronicity and that the adversary’s hashing power is strictly less than\\(1/2\\), while the adversarial bound needed for security decreases as the network desynchronizes.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46803-6_10"}, {"primary_key": "4403437", "vector": [], "sparse_vector": [], "title": "One-Out-of-Many Proofs: Or How to Leak a Secret and Spend a Coin.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We construct a 3-move public coin special honest verifier zero-knowledge proof, a so-called Sigma-protocol, for a list of commitments having at least one commitment that opens to 0. It is not required for the prover to know openings of the other commitments. The proof system is efficient, in particular in terms of communication requiring only the transmission of a logarithmic number of commitments. We use our proof system to instantiate both ring signatures and zerocoin, a novel mechanism for bitcoin privacy. We use our Sigma-protocol as a (linkable) ad-hoc group identification scheme where the users have public keys that are commitments and demonstrate knowledge of an opening for one of the commitments to unlinkably identify themselves (once) as belonging to the group. Applying the Fiat-Shamir transform on the group identification scheme gives rise to ring signatures, applying it to the linkable group identification scheme gives rise to zerocoin. Our ring signatures are very small compared to other ring signature schemes and we only assume the users’ secret keys to be the discrete logarithms of single group elements so the setup is quite realistic. Similarly, compared with the original zerocoin protocol we only rely on a weak cryptographic assumption and do not require a trusted setup. A third application of our Sigma protocol is an efficient proof of membership of a secret committed value belonging to a public list of values.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46803-6_9"}, {"primary_key": "4403438", "vector": [], "sparse_vector": [], "title": "Bootstrapping for HElib.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Gentry’s bootstrapping technique is still the only known method of obtaining fully homomorphic encryption where the system’s parameters do not depend on the complexity of the evaluated functions. Bootstrapping involves arecryptionprocedure where the scheme’s decryption algorithm is evaluated homomorphically. So far, there have been precious few implementations of recryption, and fewer still that can handle “packed ciphertexts” that encrypt vectors of elements. In the current work, we report on an implementation of recryption of fully-packed ciphertexts using theHEliblibrary for somewhat-homomorphic encryption. This implementation required extending the recryption algorithms from the literature, as well as many aspects of theHEliblibrary. Our implementation supports bootstrapping of packed ciphertexts over many extension fields/rings. One example that we tested involves ciphertexts that encrypt vectors of 1024 elements from\\({\\text {GF}}(2^{16})\\). In that setting, the recryption procedure takes under 5.5 minutes (at security-level\\(\\approx 76\\)) on a single core, and allows a depth-9 computation before the next recryption is needed.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_25"}, {"primary_key": "4403439", "vector": [], "sparse_vector": [], "title": "Robust Authenticated-Encryption AEZ and the Problem That It Solves.", "authors": ["Viet Tung Hoang", "<PERSON>", "<PERSON>"], "summary": "With a scheme forrobustauthenticated-encryption a user can select an arbitrary value\\(\\lambda \\!\\ge 0\\)and then encrypt a plaintext of any length into a ciphertext that’s\\(\\lambda \\)characters longer. The scheme must provide all the privacy and authenticity possible for the requested\\(\\lambda \\). We formalize and investigate this idea, and construct a well-optimized solution, AEZ, from the AES round function. Our scheme encrypts strings at almost the same rate as OCB-AES or CTR-AES (on Haswell, AEZ has a peak speed of about 0.7 cpb). To accomplish this we employ an approach we callprove-then-prune: prove security and then instantiate with ascaled-downprimitive (e.g., reducing rounds for blockcipher calls).", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_2"}, {"primary_key": "4403440", "vector": [], "sparse_vector": [], "title": "Universal Signature Aggregators.", "authors": ["<PERSON>", "Venkata Koppula", "<PERSON>"], "summary": "We introduce the concept of universal signature aggregators. In a universal signature aggregator system, a third party, using a set of common reference parameters, can aggregate a collection of signatures produced fromanyset of signing algorithms (subject to a chosen length constraint) into one short signature whose length is independent of the number of signatures aggregated. In prior aggregation works, signatures can only be aggregated if all signers use the same signing algorithm (e.g., BLS) and shared parameters. A universal aggregator can aggregate across schemes even in various algebraic settings (e.g., BLS, RSA, ECDSA), thus creating novel opportunities for compressing authentication overhead. It is especially compelling thatexistingpublic key infrastructures can be used and that the signers do not have to alter their behavior to enable aggregation of their signatures. We provide multiple constructions and proofs of universal signature aggregators based on indistinguishability obfuscation and other supporting primitives. We detail our techniques as well as the tradeoffs in features and security of our solutions.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46803-6_1"}, {"primary_key": "4403441", "vector": [], "sparse_vector": [], "title": "End-to-End Verifiable Elections in the Standard Model.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present the cryptographic implementation of “DEMOS”, a new e-voting system that isend-to-end verifiablein the standard model, i.e., without any additional “setup” assumption or access to a random oracle (RO). Previously known end-to-end verifiable e-voting systems required such additional assumptions (specifically, either the existence of a “randomness beacon” or were only shown secure in the RO model). In order to analyze our scheme, we also provide a modeling of end-to-end verifiability as well as privacy and receipt-freeness that encompasses previous definitions in the form of two concise attack games. Our scheme satisfies end-to-end verifiabilityinformation theoreticallyin the standard model and privacy/receipt-freeness under a computational assumption (subexponential Decisional <PERSON><PERSON><PERSON>). In our construction, we utilize a number of techniques used for the first time in the context of e-voting schemes that include utilizing randomness from bit-fixing sources, zero-knowledge proofs with imperfect verifier randomness and complexity leveraging.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46803-6_16"}, {"primary_key": "4403442", "vector": [], "sparse_vector": [], "title": "Quasi-Adaptive NIZK for Linear Subspaces Revisited.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>e"], "summary": "Non-interactive zero-knowledge (NIZK) proofs for algebraic relations in a group, such as the Groth-<PERSON> proofs, are an extremely powerful tool in pairing-based cryptography. A series of recent works focused on obtaining very efficient NIZK proofs for linear spaces in a weaker quasi-adaptive model. We revisit recent quasi-adaptive NIZK constructions, providing clean, simple, and improved constructions via a conceptually different approach inspired by recent developments in identity-based encryption. We then extend our techniques also to linearly homomorphic structure-preserving signatures, an object both of independent interest and with many applications.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46803-6_4"}, {"primary_key": "4403443", "vector": [], "sparse_vector": [], "title": "A Generic Approach to Invariant Subspace Attacks: Cryptanalysis of Robin, iSCREAM and Zorro.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Invariant subspace attacks were introduced at CRYPTO 2011 to cryptanalyzePRINTcipher. The invariant subspaces forPRINTcipherwere discovered in an ad hoc fashion, leaving a generic technique to discover invariant subspaces in other ciphers as an open problem. Here, based on a rather simple observation, we introduce a generic algorithm to detect invariant subspaces. We apply this algorithm to the CAESAR candidate iSCREAM, the closely related LS-design Robin, as well as the lightweight cipher Zorro. For all three candidates invariant subspaces were detected, and result in practical breaks of the ciphers. A closer analysis of independent interest reveals that these invariant subspaces are underpinned by a new type of self-similarity property. For all ciphers, our strongest attack shows the existence of a weak key set of density\\(2^{-32}\\). These weak keys lead to a simple property on the plaintexts going through the whole encryption process with probability one. All our attacks have been practically verified on reference implementations of the ciphers.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_11"}, {"primary_key": "4403444", "vector": [], "sparse_vector": [], "title": "The Sum Can Be Weaker Than Each Part.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we study the security of summing the outputs of twoindependenthash functions, in an effort to increase the security of the resulting design, or to hedge against the failure of one of the hash functions. The exclusive-or (XOR) combiner\\(H_1(M) \\oplus H_2(M)\\)is one of the two most classical combiners, together with the concatenation combiner\\(H_1(M) \\Vert H_2(M)\\). While the security of the concatenation of two hash functions is well understood since <PERSON><PERSON>’s seminal work on multicollisions, the security of the sum of two hash functions has been much less studied. The XOR combiner is well known as a good PRF and MAC combiner, and is used in practice in TLS versions 1.0 and 1.1. In a hash function setting, <PERSON><PERSON> and <PERSON><PERSON><PERSON> have shown that if the compression functions are modeled as random oracles, or evenweakrandom oracles (i.e.they can easily be inverted – in particular\\(H_1\\)and\\(H_2\\)offer no security),\\(H_1 \\oplus H_2\\)is indifferentiable from a random oracle up to the birthday bound. In this work, we focus on the preimage resistance of the sum of two narrow-pipe\\(n\\)-bit hash functions, following the Merkle-Damgård or HAIFA structure (the internal state size and the output size are both\\(n\\)bits).We show a rather surprising result: the sum of two such hash functions, e.g. SHA-512\\(\\oplus \\)Whirlpool, can never provide\\(n\\)-bit security for preimage resistance. More precisely, we present a generic preimage attack with a complexity of\\(\\tilde{O}(2^{5n/6})\\). While it is already known that the XOR combiner is not preserving for preimage resistance (i.e.there might besomeinstantiations where the hash functions are secure but the sum is not), our result is much stronger: foranynarrow-pipe functions, the sum is not preimage resistant. Besides, we also provide concrete preimage attacks on the XOR combiner (and the concatenation combiner) when one or both of the compression functions are weak; this complements Hoch and Shamir’s proof by showing its tightness for preimage resistance. Of independent interests, one of our main technical contributions is a novel structure to controlsimultaneouslythe behavior of independent hash computations which share the same input message. We hope that breaking the pairwise relationship between their internal states will have applications in related settings.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_14"}, {"primary_key": "4403445", "vector": [], "sparse_vector": [], "title": "Privacy Amplification in the Isolated Qubits Model.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Isolated qubits are a special class of quantum devices, which can be used to implement tamper-resistant cryptographic hardware such as one-time memories (OTM’s). Unfortunately, these OTM constructions leak some information, and standard methods for privacy amplification cannot be applied here, because the adversary has advance knowledge of the hash function that the honest parties will use. In this paper we show a stronger form of privacy amplification that solves this problem, using afixedhash function that is secure against all possible adversaries in the isolated qubits model. This allows us to construct single-bit OTM’s which only leak an exponentially small amount of information. We then study a natural generalization of the isolated qubits model, where the adversary is allowed to perform a polynomially-bounded number of entangling gates, in addition to unbounded local operations and classical communication (LOCC). We show that our technique for privacy amplification is also secure in this setting.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46803-6_26"}, {"primary_key": "4403446", "vector": [], "sparse_vector": [], "title": "KDM-CCA Security from RKA Secure Authenticated Encryption.", "authors": ["<PERSON><PERSON><PERSON>", "Bao Li", "<PERSON><PERSON><PERSON>"], "summary": "We propose an efficient public key encryption scheme which is key-dependent message secure against chosen ciphertext attacks (KDM-CCA) with respect to affine functions based on the decisional composite residuosity assumption. Technically, we achieve KDM-CCA security by enhancing a chosen ciphertext secure scheme based on the high entropy hash proof system with three tools: a key-dependent message encoding, an entropy filter and an authenticated encryption secure against related-key attacks.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_22"}, {"primary_key": "4403447", "vector": [], "sparse_vector": [], "title": "Quadratic Time, Linear Space Algorithms for Gram-Schmidt Orthogonalization and Gaussian Sampling in Structured Lattices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A procedure for sampling lattice vectors is at the heart of many lattice constructions, and the algorithm of <PERSON> (SODA 2000) and <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> (STOC 2008) is currently the one that produces the shortest vectors. But due to the fact that its most time-efficient (quadratic-time) variant requires the storage of the Gram-Schmidt basis, the asymptotic space requirements of this algorithm are the same for general and ideal lattices. The main result of the current work is a series of algorithms that ultimately lead to a sampling procedure producing the same outputs as the Klein/GPV one, but requiring only linear-storage when working on lattices used in ideal-lattice cryptography. The reduced storage directly leads to a reduction in key-sizes by a factor of\\(\\Omega (d)\\), and makes cryptographic constructions requiring lattice sampling much more suitable for practical applications. At the core of our improvements is a new, faster algorithm for computing the Gram-Schmidt orthogonalization of a set of vectors that are related via alinear isometry. In particular, for a linear isometry\\(r:\\mathbb {R}^d\\rightarrow \\mathbb {R}^d\\)which is computable in time\\(O(d)\\)and a\\(d\\)-dimensional vector\\( \\mathbf {b} \\), our algorithm for computing the orthogonalization of\\(( \\mathbf {b} ,r( \\mathbf {b} ),r^2( \\mathbf {b} ),\\ldots ,r^{d-1}( \\mathbf {b} ))\\)uses\\(O(d^2)\\)floating point operations. This is in contrast to\\(O(d^3)\\)such operations that are required by the standard Gram-Schmidt algorithm. This improvement is directly applicable to bases that appear in ideal-lattice cryptography because those bases exhibit such “isometric structure”. The above-mentioned algorithm improves on a previous one of Gama, Howgrave-Graham, Nguyen (EUROCRYPT 2006) which used different techniques to achieve only a constant-factor speed-up for similar lattice bases. Interestingly, our present ideas can be combined with those from Gama et al. to achieve an even an larger practical speed-up. We next show how this new Gram-Schmidt algorithm can be applied towards lattice sampling in quadratic time using only linear space. The main idea is that rather than pre-computing and storing the Gram-Schmidt vectors, one can compute them “on-the-fly” while running the sampling algorithm. We also rigorously analyze the required arithmetic precision necessary for achieving negligible statistical distance between the outputs of our sampling algorithm and the desired Gaussian distribution. The resu-lts of our experiments involving NTRU lattices show that the practical performance improvements of our algorithms are as predicted in theory.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_30"}, {"primary_key": "4403448", "vector": [], "sparse_vector": [], "title": "Cryptographic Reverse Firewalls.", "authors": ["<PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>"], "summary": "Recent revelations by <PERSON> [3,20,27] show that a user’s own hardware and software can be used against her in various ways (e.g., to leak her private information). And, a series of recent announcements has shown that widespread implementations of cryptographic software often contain serious bugs that cripple security (e.g., [12–14,22]). This motivates us to consider the following (seemingly absurd) question:How can we guarantee a user’s security when she may be using a malfunctioning or arbitrarily compromised machine?To that end, we introduce the notion of acryptographic reverse firewall(RF). Such a machine sits between the user’s computer and the outside world, potentially modifying the messages that she sends and receives as she engages in a cryptographic protocol. A good reverse firewall accomplishes three things: (1) itmaintains functionality, so that if the user’s computer is working correctly, the RF will not break the functionality of the underlying protocol; (2) itpreserves security, so that regardless of how the user’s machine behaves, the presence of the RF will provide the same security guarantees as the properly implemented protocol; and (3) itresists exfiltration, so that regardless of how the user’s machine behaves, the presence of the RF will prevent the machine from leaking any information to the outside world. Importantly, we do not model the firewall as a trusted party. It does not share any secrets with the user, and the protocol should be both secure and functional without the firewall (when the protocol’s implementation is correct). Our security definition for reverse firewalls depends on the security notion(s) of the underlying protocol. As such, our model generalizes much prior work (e.g., [5,7,26,32]) and provides a general framework for building cryptographic schemes that remain secure when run on compromised machine. It is also a modern take on a line of work that received considerable attention in the 80s and 90s (e.g., [7,9,11,15,16,30,31]). We show that our definition is achievable by constructing a private function evaluation protocol with a secure reverse firewall for each party. Along the way, we design an oblivious transfer protocol that also has a secure RF for each party, and a rerandomizable garbled circuit that is both more efficient and more secure than previous constructions. Finally, we show how to convertanyprotocol into a protocol with an exfiltration-resistant reverse firewall for all parties. (In other words, we provide a generic way to prevent a tampered machine from leaking information to an eavesdropper viaanyprotocol.)", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46803-6_22"}, {"primary_key": "4403449", "vector": [], "sparse_vector": [], "title": "(Batch) Fully Homomorphic Encryption over Integers for Non-Binary Message Spaces.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we construct a fully homomorphic encryption (FHE) scheme over integers with the message space\\(\\mathbb {Z}_Q\\)for any prime\\(Q\\). Even for the binary case\\(Q=2\\), our decryption circuit has a smaller degree than that of the previous scheme; the multiplicative degree is reduced from\\(O(\\lambda (\\log \\lambda )^2)\\)to\\(O(\\lambda )\\), where\\(\\lambda \\)is the security parameter. We also extend our FHE scheme to a batch FHE scheme.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_21"}, {"primary_key": "4403450", "vector": [], "sparse_vector": [], "title": "The Multiple Number Field Sieve with Conjugation and Generalized Joux-Lercier Methods.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper, we propose two variants of the Number Field Sieve (NFS) to compute discrete logarithms in medium characteristic finite fields. We consider algorithms that combine two ideas, namely the Multiple variant of the Number Field Sieve (MNFS) taking advantage of a large number of number fields in the sieving phase, and two recent polynomial selections for the classical Number Field Sieve. Combining MNFS with the Conjugation Method, we design the best asymptotic algorithm to compute discrete logarithms in the medium characteric case. The asymptotic complexity of our improved algorithm is\\(L_{p^n} (1/3, (8 (9+4 \\sqrt{6})/15)^{1/3}) \\approx L_{p^n}(1/3, 2.156) \\), where\\({\\mathbb F}_{p^n}\\)is the target finite field. This has to be compared with the complexity of the previous state-of-the-art algorithm for medium characteristic finite fields, NFS with Conjugation Method, that has a complexity of approximately\\(L_{p^n}(1/3, 2.201)\\). Similarly, combining MNFS with the Generalized Joux-Lercier method leads to an improvement on the asymptotic complexities in the boundary case between medium and high characteristic finite fields.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_7"}, {"primary_key": "4403451", "vector": [], "sparse_vector": [], "title": "A Provable-Security Analysis of Intel&apos;s Secure Key RNG.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We provide the first provable-security analysis of the Intel Secure Key hardware RNG (ISK-RNG), versions of which have appeared in Intel processors since late 2011. To model the ISK-RNG, we generalize the PRNG-with-inputs primitive, introduced by <PERSON><PERSON> et al. at CCS’13 for their/dev/[u]randomanalysis. The concrete security bounds we uncover tell a mixed story. We find that ISK-RNG lacks backward-security altogether, and that the forward-security bound for the “truly random” bits fetched by the\\(\\mathtt {RDSEED}\\)instruction is potentially worrisome. On the other hand, we are able to prove stronger forward-security bounds for the pseudorandom bits fetched by the\\(\\mathtt {RDRAND}\\)instruction. En route to these results, our main technical efforts focus on the way in which ISK-RNG employs CBCMAC as an entropy extractor.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_4"}, {"primary_key": "4403452", "vector": [], "sparse_vector": [], "title": "Structural Evaluation by Generalized Integral Property.", "authors": ["<PERSON><PERSON>"], "summary": "In this paper, we show structural cryptanalyses against two popular networks, i.e., the Feistel Network and the Substitute-Permutation Network (SPN). Our cryptanalyses are distinguishing attacks by an improved integral distinguisher. The integral distinguisher is one of the most powerful attacks against block ciphers, and it is usually constructed by evaluating the propagation characteristic of integral properties, e.g., the ALL or BALANCE property. However, the integral property does not derive useful distinguishers against block ciphers with non-bijective functions and bit-oriented structures. Moreover, since the integral property does not clearly exploit the algebraic degree of block ciphers, it tends not to construct useful distinguishers against block ciphers with low-degree functions. In this paper, we propose a new property calledthe division property, which is the generalization of the integral property. It can effectively construct the integral distinguisher even if the block cipher has non-bijective functions, bit-oriented structures, and low-degree functions. From viewpoints of the attackable number of rounds or chosen plaintexts, the division property can construct better distinguishers than previous methods. Although our attack is a generic attack, it can improve several integral distinguishers against specific cryptographic primitives. For instance, it can reduce the required number of chosen plaintexts for the\\(10\\)-round distinguisher onKeccak-\\(f\\)from\\(2^{1025}\\)to\\(2^{515}\\). For the Feistel cipher, it theoretically proves thatSimon32, 48, 64, 96, and 128 have\\(9\\)-,\\(11\\)-,\\(11\\)-,\\(13\\)-, and\\(13\\)-round integral distinguishers, respectively.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46800-5_12"}, {"primary_key": "4403453", "vector": [], "sparse_vector": [], "title": "Non-Interactive Zero-Knowledge Proofs in the Quantum Random Oracle Model.", "authors": ["<PERSON>"], "summary": "We present a construction for non-interactive zero-knowledge proofs of knowledge in the random oracle model from general sigma-protocols. Our construction is secure against quantum adversaries. Prior constructions (by Fiat-S<PERSON><PERSON> and by <PERSON><PERSON><PERSON>) are only known to be secure against classical adversaries, and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> (FOCS 2014) gave evidence that those constructions might not be secure against quantum adversaries in general. To prove security of our constructions, we additionally develop new techniques for adaptively programming the quantum random oracle.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46803-6_25"}, {"primary_key": "4403454", "vector": [], "sparse_vector": [], "title": "Generic Hardness of the Multiple Discrete Logarithm Problem.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We study generic hardness of the multiple discrete logarithm problem, where the solver has to solve\\(n\\)instances of the discrete logarithm problem simultaneously. There are known generic algorithms which perform\\(O(\\sqrt{np})\\)group operations, where\\(p\\)is the group order, but no generic lower bound was known other than the trivial bound. In this paper we prove the tight generic lower bound, showing that the previously known algorithms are asymptotically optimal. We establish the lower bound by studying hardness of a related computational problem which we call the search-by-hyperplane-queries problem, which may be of independent interest.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46803-6_27"}, {"primary_key": "4403455", "vector": [], "sparse_vector": [], "title": "Two Halves Make a Whole - Reducing Data Transfer in Garbled Circuits Using Half Gates.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The well-known classical constructions of garbled circuits use four ciphertexts per gate, although various methods have been proposed to reduce this cost. The best previously known methods for optimizing AND gates (two ciphertexts; <PERSON><PERSON> et al., ASIACRYPT 2009) and XOR gates (zero ciphertexts; <PERSON><PERSON><PERSON> and <PERSON>, ICALP 2008) were incompatible, so most implementations used the best known method compatible with free-XOR gates (three ciphertexts; <PERSON><PERSON><PERSON> and <PERSON>, ICALP 2008). In this work we show how to simultaneously garble AND gates using two ciphertexts and XOR gates using zero ciphertexts, resulting in smaller garbled circuits than any prior scheme. The main idea behind our construction is to break an AND gate into twohalf-gates— AND gates for which one party knows one input. Each half-gate can be garbled with a single ciphertext, so our construction uses two ciphertexts for each AND gate while being compatible with free-XOR gates. The price for the reduction in size is that the evaluator must perform two cryptographic operations per AND gate, rather than one as in previous schemes. We experimentally demonstrate that our garbling scheme leads to an overall decrease in time (up to 25%), bandwidth (up to 33%), and energy use (up to 20%) over several benchmark applications. We show that our construction is optimal for a large class of garbling schemes encompassing all known practical garbling techniques.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46803-6_8"}, {"primary_key": "4403456", "vector": [], "sparse_vector": [], "title": "Authenticated Key Exchange from Ideal Lattices.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper, we present a practical and provably secure two-pass authenticated key exchange protocol over ideal lattices, which is conceptually simple and has similarities to the Di<PERSON><PERSON>-<PERSON> based protocols such as HMQV (CRYPTO 2005) and OAKE (CCS 2013). Our method does not involve other cryptographic primitives—in particular, it does not use signatures—which simplifies the protocol and enables us to base the security directly on the hardness of the ring learning with errors problem. The security is proven in the Bellare-Rogaway model with weak perfect forward secrecy in the random oracle model. We also give a one-pass variant of our two-pass protocol, which might be appealing in specific applications. Several concrete choices of parameters are provided, and a proof-of-concept implementation shows that our protocols are indeed practical.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46803-6_24"}, {"primary_key": "4403457", "vector": [], "sparse_vector": [], "title": "How to Obfuscate Programs Directly.", "authors": ["<PERSON>"], "summary": "We propose a new way to obfuscate programs, via composite-order multilinear maps. Our construction operates directly on straight-line programs (arithmetic circuits), rather than converting them to matrix branching programs as in other known approaches. This yields considerable efficiency improvements. For an NC\\(^1\\)circuit of size\\(s\\)and depth\\(d\\), with\\(n\\)inputs, we require only\\(O(d^2s^2 + n^2)\\)multilinear map operations to evaluate the obfuscated circuit—as compared with other known approaches, for which the number of operations is exponential in\\(d\\). We prove virtual black-box (VBB) security for our construction in a generic model of multilinear maps of hidden composite order, extending previous models for the prime-order setting. Our scheme works either with “noisy” multilinear maps, which can only evaluate expressions of degree\\(\\lambda ^c\\)for pre-specified constant\\(c\\); or with “clean” multilinear maps, which can evaluate arbitrary expressions. With “noisy” maps, our new obfuscator applies only to NC\\(^1\\)circuits, requiring the additional assumption of FHE in order to bootstrap to P/poly (as in other obfuscation constructions). From “clean” multilinear maps, on the other hand (whose existence is still open), we present the first approach that would achieve obfuscation for P/poly directly, without FHE. Our construction is efficient enough that if “clean” multilinear maps were known, then general-purpose program obfuscation could become implementable in practice. Our results demonstrate that the question of “clean” multilinear maps is not a technicality, but a central open problem.", "published": "2015-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-46803-6_15"}]