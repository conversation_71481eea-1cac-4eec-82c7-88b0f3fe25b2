[{"primary_key": "2112593", "vector": [], "sparse_vector": [], "title": "New Approaches for Quantum Copy-Protection.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Quantum copy-protection uses the unclonability of quantum states to construct quantum software that provably cannot be pirated. copy-protection would be immensely useful, but unfortunately, little is known about achieving it in general. In this work, we make progress on this goal, by giving the following results: We show how to copy-protect any program that cannot be learned from its input-output behavior relative to aclassicaloracle. This construction improves on <PERSON><PERSON> (CCC 2009), which achieves the same relative to a quantum oracle. By instantiating the oracle with post-quantum candidate obfuscation schemes, we obtain a heuristic construction of copy-protection. We show, roughly, that any program which can be watermarked can be copydetected, a weaker version of copy-protection that does not prevent copying, but guarantees that any copying can be detected. Our scheme relies on the security of the assumed watermarking, plus the assumed existence of public-key quantum money. Our construction is publicly detectable and applicable to many recent watermarking schemes.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84242-0_19"}, {"primary_key": "2112594", "vector": [], "sparse_vector": [], "title": "Deniable Fully Homomorphic Encryption from Learning with Errors.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Mossel"], "summary": "We define and constructDeniable Fully Homomorphic Encryptionbased on the Learning With Errors (LWE) polynomial hardness assumption. Deniable FHE enables storing encrypted data in the cloud to be processed securely without decryption, maintaining deniability of the encrypted data, as well the prevention of vote-buying in electronic voting schemes where encrypted votes can be tallied without decryption. Our constructions achievecompactnessindependently of the level of deniability- both the size of the public key and the size of the ciphertexts are bounded by a fixed polynomial, independent of the detection probability achieved by the scheme. This is in contrast to all previous constructions of deniable encryption schemes (even without requiring homomorphisms) which are based on polynomial hardness assumptions, originating with the seminal work of <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON> (CRYPTO 1997) in which the ciphertext size grows with the inverse of the detection probability. Canettiet al.argued that this dependence “seems inherent”, but our constructions illustrate this is not the case. We note that the <PERSON>hai-<PERSON> (STOC 2014) construction of deniable encryption from indistinguishability obfuscation achieves compactness and can be easily modified to achieve deniable FHE as well, but it requires multiple, stronger sub-exponential hardness assumptions, which are furthermore not post-quantum secure. In contrast, our constructions rely only on the LWE polynomial hardness assumption, as currently required for FHE even without deniability. The running time of our encryption algorithm depends on the inverse of the detection probability, thus the scheme falls short of achieving simultaneously compactness, negligible deniability probabilityandpolynomial encryption time. Yet, we believe that achieving compactness is a fundamental step on the way to achieving all properties simultaneously as has been the historical journey for other primitives such as functional encryption. Our constructions support large message spaces, whereas previous constructions were bit by bit, and can be run in online-offline model of encryption, where the bulk of computation is independent of the message and may be performed in an offline pre-processing phase. This results in an efficient online phase whose running time is independent of the detection probability. At the heart of our constructions is a new way to use bootstrapping to obliviously generate FHE ciphertexts so that it supports faking under coercion.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84245-1_22"}, {"primary_key": "2112595", "vector": [], "sparse_vector": [], "title": "Multi-input Quadratic Functional Encryption from Pairings.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We construct the first multi-input functional encryption (MIFE) scheme for quadratic functions from pairings. Our construction supports polynomial number of users, where useri, for\\(i \\in [n]\\), encrypts input\\(\\mathbf{x}_i \\in \\mathbb {Z}^m\\)to obtain ciphertext\\(\\mathsf {CT}_i\\), the key generator provides a key\\(\\mathsf {SK}_\\mathbf{c}\\)for vector\\(\\mathbf{c} \\in \\mathbb {Z}^{({mn})^2}\\)and decryption, given\\(\\mathsf {CT}_1,\\ldots ,\\mathsf {CT}_n\\)and\\(\\mathsf {SK}_\\mathbf{c}\\), recovers\\(\\langle \\mathbf{c}, \\mathbf{x} \\otimes \\mathbf{x} \\rangle \\)and nothing else. We achieve indistinguishability-based (selective) security against unbounded collusions under the standard bilateral matrix Di<PERSON>ie-Hellman assumption. All previous MIFE schemes either support only inner products (linear functions) or rely on strong cryptographic assumptions such as indistinguishability obfuscation or multi-linear maps.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84259-8_8"}, {"primary_key": "2112596", "vector": [], "sparse_vector": [], "title": "Secure Computation from One-Way Noisy Communication, or: Anti-correlation via Anti-concentration.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Can a sender encode a pair of messages\\((m_0,m_1)\\)jointly, and send their encoding over (say) a binary erasure channel, so that the receiver can decode exactly one of the two messages and the sender does not know which one? <PERSON><PERSON><PERSON> et al. (Crypto 2015) showed that this is information-theoretically impossible. We show how to circumvent this impossibility by assuming that the receiver is computationally bounded, settling for an inverse-polynomial security error (which is provably necessary), and relying onideal obfuscation. Our solution creates a “computational anti-correlation” between the events of receiving\\(m_0\\)and receiving\\(m_1\\)by exploiting theanti-concentrationof the binomial distribution. The ideal obfuscation primitive in our construction can either be directly realized using (stateless) tamper-proof hardware, yielding an unconditional result, or heuristically instantiated in the plain model using existing indistinguishability obfuscation schemes. As a corollary, we get similar feasibility results forgeneral secure computationof sender-receiver functionalities by leveraging the completeness of the above “random oblivious transfer” functionality.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84245-1_5"}, {"primary_key": "2112597", "vector": [], "sparse_vector": [], "title": "Functional Encryption for Turing Machines with Dynamic Bounded Collusion from LWE.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The classic work of <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (CRYPTO 2012) and follow-ups provided constructions of bounded collusion Functional Encryption (FE) for circuits from mild assumptions. In this work, we improve the state of affairs for bounded collusion FE in several ways: New Security Notion.We introduce the notion ofdynamicbounded collusion FE, where the declaration of collusion bound is delayed to the time of encryption. This enables the encryptor to dynamically choose the collusion bound for different ciphertexts depending on their individual level of sensitivity. Hence, the ciphertext size grows linearly with its own collusion bound and the public key size is independent of collusion bound. In contrast, all prior constructions have public key and ciphertext size that grow at least linearly with a fixed boundQ. CPFE for circuits with Dynamic Bounded Collusion.We provide the first CPFE schemes for circuits enjoying dynamic bounded collusion security. By assuming identity based encryption (IBE), we construct CPFE for circuits ofunboundedsize satisfyingnon-adaptivesimulation based security. By strengthening the underlying assumption to IBE with receiver selective opening security, we obtain CPFE for circuits ofboundedsize enjoyingadaptivesimulation based security. Moreover, we show that IBE is a necessary assumption for these primitives. Furthermore, by relying on the Learning With Errors (LWE) assumption, we obtain the firstsuccinctCPFE for circuits, i.e. supporting circuits with unbounded size, but fixed output length and depth. This scheme achievesadaptivesimulation based security. KPFE for circuits with dynamic bounded collusion.We provide the first KPFE for circuits of unbounded size, but bounded depth and output length satisfying dynamic bounded collusion security from LWE. Our construction achievesadaptivesimulation security improving security of [20]. KP and CP FE for TM/NL with dynamic bounded collusion.We provide the first KPFE and CPFE constructions of bounded collusion functional encryption for Turing machines in the public key setting from LWE. Our constructions achieve non-adaptive simulation based security. Both the input and the machine in our construction can be ofunboundedpolynomial length. We provide a variant of the above scheme that satisfiesadaptivesecurity, but at the cost of supporting a smaller class of computation, namely Nondeterministic Logarithmic-space (NL). Since NL contains Nondeterministic Finite Automata (NFA), this result subsumesallprior work of bounded collusion FE for uniform models from standard assumptions [7,9].", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84259-8_9"}, {"primary_key": "2112598", "vector": [], "sparse_vector": [], "title": "Impossibility of Quantum Virtual Black-Box Obfuscation of Classical Circuits.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Virtual black-box obfuscation is a strong cryptographic primitive: it encrypts a circuit while maintaining its full input/output functionality. A remarkable result by <PERSON> et al. (Crypto 2001) shows that a general obfuscator that obfuscates classical circuits into classical circuits cannot exist. A promising direction that circumvents this impossibility result is to obfuscate classical circuits into quantum states, which would potentially be better capable of hiding information about the obfuscated circuit. We show that, under the assumption that Learning With Errors (LWE) is hard for quantum computers, this quantum variant of virtual black-box obfuscation of classical circuits is generally impossible. On the way, we show that under the presence of dependent classical auxiliary input, even the small class of classical point functions cannot be quantum virtual black-box obfuscated.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84242-0_18"}, {"primary_key": "2112599", "vector": [], "sparse_vector": [], "title": "Lattice Reduction with Approximate Enumeration Oracles - Practical Algorithms and Concrete Performance.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This work provides a systematic investigation of the use of approximate enumeration oracles in BKZ, building on recent technical progress on speeding-up lattice enumeration:relaxing(the search radius of) enumeration andextended preprocessingwhich preprocesses in a larger rank than the enumeration rank. First, we heuristically justify that relaxing enumeration with certain extreme pruning asymptotically achieves an exponential speed-up for reaching the same root Hermite factor (RHF). Second, we perform simulations/experiments to validate this and the performance for relaxed enumeration with numerically optimised pruning for both regular and extended preprocessing. Upgrading BKZ with such approximate enumeration oracles gives rise to our main result, namely a practical and faster (wrt. previous work) polynomial-space lattice reduction algorithm for reaching the same RHF in practical and cryptographic parameter ranges. We assess its concrete time/quality performance with extensive simulations and experiments.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84245-1_25"}, {"primary_key": "2112600", "vector": [], "sparse_vector": [], "title": "Subtractive Sets over Cyclotomic Rings - Limits of Schnorr-Like Arguments over Lattices.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We study when (dual) Vandermonde systems of the form\\(\\mathbf {V} _T^{{(\\intercal )}} \\cdot \\mathbf {z} = s\\cdot \\mathbf {w}\\)admit a solution\\(\\mathbf {z}\\)over a ring\\(\\mathcal {R}\\), where\\(\\mathbf {V} _T\\)is the Vandermonde matrix defined by a setTand where the “slack”sis a measure of the quality of solutions. To this end, we propose the notion of (s,t)-subtractive sets over a ring\\(\\mathcal {R}\\), with the property that ifSis (s,t)-subtractive then the above (dual) Vandermonde systems defined by anyt-subset\\(T \\subseteq S\\)are solvable over\\(\\mathcal {R}\\). The challenge is then to find large setsSwhile minimising (the norm of)swhen given a ring\\(\\mathcal {R}\\). By constructing families of (s,t)-subtractive setsSof size\\(n = \\textsf {poly}(\\lambda )\\)over cyclotomic rings\\(\\mathcal {R}= \\mathbb {Z}[\\zeta _{p^\\ell }]\\)for primep, we construct Schnorr-like lattice-based proofs of knowledge for the SIS relation\\(\\mathbf {A} \\cdot \\mathbf {x} = s \\cdot \\mathbf {y} \\bmod q\\)withO(1/n) knowledge error, and\\(s = 1\\)in case\\(p = \\textsf {poly}(\\lambda )\\). Our technique slots naturally into the lattice Bulletproof framework from Crypto’20, producing lattice-based succinct arguments for NP with better parameters. We then give matching impossibility results constrainingnrelative tos, which suggest that our Bulletproof-compatible protocols are optimal unless fundamentally new techniques are discovered. Noting that the knowledge error of lattice Bulletproofs is\\(\\varOmega (\\log k/n)\\)for witnesses in\\(\\mathcal {R}^k\\)and subtractive set size\\(n\\), our result represents a barrier to practically efficient lattice-based succinct arguments in the Bulletproof framework. Beyond these main results, the concept of (s,t)-subtractive sets bridges group-based threshold cryptography to lattice settings, which we demonstrate by relating it to distributed pseudorandom functions.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84245-1_18"}, {"primary_key": "2112601", "vector": [], "sparse_vector": [], "title": "Round Efficient Secure Multiparty Quantum Computation with Identifiable Abort.", "authors": ["Bar Alon", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A recent result by <PERSON><PERSON><PERSON>  et al. (EUROCRYPT 2020) showed a secure protocol for computing any quantum circuit even without the presence of an honest majority. Their protocol, however, is susceptible to a “denial of service” attack and allows even a single corrupted party to force an abort. We propose the first quantum protocol that admitssecurity-with-identifiable-abort, which allows the honest parties to agree on the identity of a corrupted party in case of an abort. Additionally, our protocol is the first to have the property that the number of rounds where quantum communication is required isindependent of the circuit complexity. Furthermore, if there exists a post-quantum secure classical protocol whose round complexity is independent of the circuit complexity, then our protocol has this property as well. Our protocol is secure under the assumption that classical quantum-resistant fully homomorphic encryption schemes with decryption circuit of logarithmic depth exist. Interestingly, our construction also admits a reduction from quantum fair secure computation to classical fair secure computation.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84242-0_16"}, {"primary_key": "2112602", "vector": [], "sparse_vector": [], "title": "Two-Round <PERSON> Multi-signatures via Delinearized Witnesses.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We construct a two-round Schnorr-based signature scheme (DWMS) by delinearizing two pre-commitments supplied by each signer. DWMS is a secure signature scheme in the algebraic group model (AGM) and the random oracle model (ROM) under the assumption of the hardness of the one-more discrete logarithm problem and the 2-entwined sum problem that we introduce in this paper. Our newm-entwined sumproblem tweaks thek-sum problem in a scalar field using the associated group. We prove the hardness of our new problem in the AGM assuming the hardness of the discrete logarithm problem in the associated group. We believe that our new problem simplifies the security proofs of multi-signature schemes that use the delinearization of commitments.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84242-0_7"}, {"primary_key": "2112603", "vector": [], "sparse_vector": [], "title": "On the Concurrent Composition of Quantum Zero-Knowledge.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Rolando L. La Placa"], "summary": "We study the notion of zero-knowledge secure against quantum polynomial-time verifiers (referred to as quantum zero-knowledge) in the concurrent composition setting. Despite being extensively studied in the classical setting, concurrent composition in the quantum setting has hardly been studied. We initiate a formal study of concurrent quantum zero-knowledge. Our results are as follows: Bounded Concurrent QZK for NP and QMA: Assuming post-quantum one-way functions, there exists a quantum zero-knowledge proof system for NP in the bounded concurrent setting. In this setting, we fix a priori the number of verifiers that can simultaneously interact with the prover. Under the same assumption, we also show that there exists a quantum zero-knowledge proof system for QMA in the bounded concurrency setting. Quantum Proofs of Knowledge: Assuming quantum hardness of learning with errors (QLWE), there exists a bounded concurrent zero-knowledge proof system for NP satisfying quantum proof of knowledge property. Our extraction mechanism simultaneously allows for extraction probability to be negligibly close to acceptance probability (extractability) and also ensures that the prover’s state after extraction is statistically close to the prover’s state after interacting with the verifier (simulatability). Even in the standalone setting, the seminal work of [Unruh EUROCRYPT’12], and all its followups, satisfied a weaker version of extractability property and moreover, did not achieve simulatability. Our result yields a proof ofquantum knowledgesystem for QMA with better parameters than prior works.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84242-0_13"}, {"primary_key": "2112604", "vector": [], "sparse_vector": [], "title": "Upslices, Downslices, and Secret-Sharing with Complexity of 1.5n.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "A secret-sharing scheme allows to distribute a secretsamongnparties such that only some predefined “authorized” sets of parties can reconstruct the secret, and all other “unauthorized” sets learn nothing abouts. The collection of authorized/unauthorized sets can be captured by a monotone function\\(f:\\{0,1\\}^n\\rightarrow \\{0,1\\}\\). In this paper, we focus on monotone functions that all their min-terms are sets of sizea, and on their duals – monotone functions whose max-terms are of sizeb. We refer to these classes as (a,n)-upslicesand (b,n)-downslices, and note that these natural families correspond to monotonea-regular DNFs and monotone\\((n-b)\\)-regular CNFs. We derive the following results. (General downslices) Every downslice can be realized with total share size of\\(1.5^{n+o(n)}<2^{0.585 n}\\). Since every monotone function can be cheaply decomposed intondownslices, we obtain a similar result for general access structures improving the previously known\\(2^{0.637n+o(n)}\\)complexity of <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON> (STOC 2020). We also achieve a minor improvement in the exponent of linear secrets sharing schemes. (Random mixture of upslices) Following <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (TCC 2020) who studied the complexity of random DNFs with constant-size terms, we consider the following general distributionFover monotone DNFs: For each width value\\(a\\in [n]\\), uniformly sample\\(k_a\\)monotone terms of sizea, where\\(\\mathbf{k}=(k_1,\\ldots ,k_n)\\)is an arbitrary vector of non-negative integers. We show that, except with exponentially small probability,Fcan be realized with share size of\\(2^{0.5 n+o(n)}\\)and can be linearly realized with an exponent strictly smaller than 2/3. Our proof also provides a candidate distribution for “exponentially-hard” access structure. We use our results to explore connections between several seemingly unrelated questions about the complexity of secret-sharing schemes such as worst-case vs. average-case, linear vs. non-linear and primal vs. dual access structures. We prove that, in at least one of these settings, there is a significant gap in secret-sharing complexity.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84252-9_21"}, {"primary_key": "2112605", "vector": [], "sparse_vector": [], "title": "Oblivious <PERSON> with Worst-Case Logarithmic Overhead.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present the first Oblivious RAM (ORAM) construction that forNmemory blocks supports accesses withworst-case\\(O(\\log N)\\)overhead for any block size\\(\\varOmega (\\log N)\\)while requiring a client memory of only a constant number of memory blocks. We rely on the existence of one-way functions and guarantee computational security. Our result closes a long line of research on fundamental feasibility results for ORAM constructions as logarithmic overhead is necessary. The previous best logarithmic overhead construction only guarantees it in anamortizedsense, i.e., logarithmic overhead is achieved only for long enough access sequences, where some of the individual accesses incur\\(\\varTheta (N)\\)overhead. The previously best ORAM in terms ofworst-caseoverhead achieves\\(O(\\log ^2 N/\\log \\log N)\\)overhead. Technically, we design a novel de-amortization framework for modern ORAM constructions that use the “shuffled inputs” assumption. Our framework significantly departs from all previous de-amortization frameworks, originating from Ostrovsky and Shoup (STOC ’97), that seem to be fundamentally too weak to be applied on modern ORAM constructions.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84259-8_21"}, {"primary_key": "2112606", "vector": [], "sparse_vector": [], "title": "Compressing Proofs of k-Out-Of-n Partial Knowledge.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In a proof of partial knowledge, introduced by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (CRYPTO 1994), a prover knowing witnesses for somek-subset ofngiven public statements can convince the verifier of this claim without revealing whichk-subset. Their solution combines\\(\\varSigma \\)-protocol theory and linear secret sharing, and achieves linear communication complexity for generalk,n. Especially the “one-out-of-n” case\\(k=1\\)has seen myriad applications during the last decades, e.g., in electronic voting, ring signatures, and confidential transaction systems. In this paper we focus on the discrete logarithm (DL) setting, where the prover claims knowledge of DLs ofk-out-of-ngiven elements. <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (EUROCRYPT 2015) have shown how to solve the special case\\(k=1\\)withlogarithmic(inn) communication, instead of linear as prior work. However, their method takes explicit advantage of\\(k=1\\)and does not generalize to\\(k>1\\). Alternatively, anindirectapproach for solving the considered problem is by translating thek-out-of-nrelation into a circuit and then applying communication-efficient circuit ZK. For\\(k = 1\\)this approach has been highly optimized, e.g., in ZCash. Our main contribution is a new, simple honest-verifier zero-knowledge proof protocol for proving knowledge ofkout ofnDLs withlogarithmiccommunication andfor general k and n, without requiring any generic circuit ZK machinery. Our solution puts forward a novel extension of thecompressed\\(\\varSigma \\)-protocol theory (CRYPTO 2020), which we then utilize to compress a new\\(\\varSigma \\)-protocol for proving knowledge ofk-out-of-nDL’s down to logarithmic size. The latter\\(\\varSigma \\)-protocol is inspired by the CRYPTO 1994 approach, but a careful re-design of the original protocol is necessary for the compression technique to apply. Interestingly,even for\\(k=1\\)and generalnour approach improves priordirectapproaches as it reduces prover complexity without increasing the communication complexity. Besides the conceptual simplicity, we also identify regimes of practical relevance where our approach achieves asymptotic and concrete improvements, e.g., in proof size and prover complexity, over the generic approach based on circuit-ZK. Finally, we show various extensions and generalizations of our core result. For instance, we extend our protocol to proofs of partial knowledge of Pedersen (vector) commitment openings, and/or to include a proof that the witness satisfies some additional constraint, and we show how to extend our results to non-threshold access structures.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84259-8_3"}, {"primary_key": "2112607", "vector": [], "sparse_vector": [], "title": "A Compressed $\\varSigma $-Protocol Theory for Lattices.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We show alattice-basedsolution for commit-and-prove transparent circuit zero-knowledge (ZK) withpolylog-communication, thefirstnot depending on PCPs. We start fromcompressed\\(\\varSigma \\)-protocol theory(CRYPTO 2020), which is built around basic\\(\\varSigma \\)-protocols for opening an arbitrary linear form on a long secret vector that is compactly committed to. These protocols are first compressed using a recursive “folding-technique” adapted from Bulletproofs, at the expense of logarithmic rounds. Proving in ZK that the secret vector satisfies a given constraint – captured by a circuit – is then by (blackbox) reduction to the linear case, via arithmetic secret-sharing techniques adapted from MPC. Commit-and-prove is also facilitated, i.e., when commitment(s) to the secret vector are created ahead of any circuit-ZK proof. On several platforms (incl. DL) this leads to logarithmic communication. Non-interactive versions follow from Fiat-Shamir. This abstract modular theory strongly suggests that it should somehow be supported by a lattice-platformas well. However, when going through the motions and trying to establish low communication (on a SIS-platform), a certain significant lack in current understanding of multi-round protocols is exposed. Namely, as opposed to the DL-case, the basic\\(\\varSigma \\)-protocol in question typically haspoly-small challengespace. Taking into account the compression-step – which yieldsnon-constantrounds – and the necessity for parallelization to reduce error, there is no known tight result that the compound protocol admits an efficient knowledge extractor. We resolve the state of affairs here by a combination of two novel results which are fully general and of independent interest. The first gives a tight analysis of efficient knowledge extraction in case of non-constant rounds combined with poly-small challenge space, whereas the second shows that parallel repetition indeed forces rapid decrease of knowledge error. Moreover, in our present context, arithmetic secret sharing is not defined over a large finite field but over a quotient of a number ring and this forces our careful adaptation of how the linearization techniques are deployed. We develop our protocols in an abstract framework that is conceptually simple and can be flexibly instantiated. In particular, the framework applies to arbitrary rings and norms.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84245-1_19"}, {"primary_key": "2112608", "vector": [], "sparse_vector": [], "title": "A Rational Protocol Treatment of 51% Attacks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Game-theoretic analyses of cryptocurrencies and—more generally—blockchain-based decentralized ledgers offer insight on their economic robustness and behavior when even their underpinning cryptographic assumptions fail. In this work we utilize the recently proposed blockchain adaptation of the rational protocol design (RPD) framework [EUROCRYPT ’18] to analyze 51% double-spending attacks against Nakamoto-style proof-of-work based cryptocurrencies. We first observe a property of the originally proposed utility class that yields an unnatural conclusion against such attacks, and show how to devise a utility that avoids this pitfall and makes predictions that match the observable behavior—i.e., that renders attacking a dominant strategy in settings where an attack was indeed observed in reality. We then propose a generic remedy to the underlying protocol parameters that provably deter adversaries controlling a majority of the system’s resources from attacks on blockchain consistency, including the 51% double-spending attack. This can be used as guidance to patch systems that have suffered such attacks, e.g., Ethereum Classic and Bitcoin Cash, and serves as a demonstration of the power of game-theoretic analyses.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84252-9_1"}, {"primary_key": "2112609", "vector": [], "sparse_vector": [], "title": "Provable Security Analysis of FIDO2.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We carry out the first provable security analysis of the new FIDO2 protocols, the promising FIDO Alliance’s proposal for a standard forpasswordlessuser authentication. Our analysis covers the core components of FIDO2: the W3C’s Web Authentication (WebAuthn) specification and the new Client-to-Authenticator Protocol (CTAP2). Our analysis ismodular. For WebAuthn and CTAP2, in turn, we propose appropriate security models that aim to capture their intended security goals and use the models to analyze their security. First, our proof confirms the authentication security of WebAuthn. Then, we show CTAP2 can only be proved secure in a weak sense; meanwhile, we identify a series of its design flaws and provide suggestions for improvement. To withstand stronger yet realistic adversaries, we propose a generic protocol called sPACA and prove its strong security; with proper instantiations, sPACA is also more efficient than CTAP2. Finally, we analyze the overall security guarantees provided by FIDO2 and WebAuthn+sPACA based on the security of their components. We expect that our models and provable security results will help clarify the security guarantees of the FIDO2 protocols. In addition, we advocate the adoption of our sPACA protocol as a substitute for CTAP2 for both stronger security and better performance.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84252-9_5"}, {"primary_key": "2112610", "vector": [], "sparse_vector": [], "title": "On the Round Complexity of Secure Quantum Computation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We construct the firstconstant-roundprotocols for secure quantum computation in the two-party (2PQC) and multi-party (MPQC) settings with security againstmaliciousadversaries. Our protocols are in the common random string (CRS) model. Assuming two-message oblivious transfer (OT), we obtain (i) three-message 2PQC, and (ii) five-round MPQC with only three rounds ofonline(input-dependent) communication; such OT is known from quantum-hard Learning with Errors (QLWE). Assuming sub-exponential hardness of QLWE, we obtain (i) three-round 2PQC with two online rounds and (ii) four-round MPQC with two online rounds. When only one (out of two) parties receives output, we achieveminimal interaction(two messages) from two-message OT; classically, such protocols are known as non-interactive secure computation (NISC), and our result constitutes the first maliciously-secure quantum NISC. Additionally assuming reusable malicious designated-verifier NIZK arguments for\\(\\mathsf {NP}\\)(MDV-NIZKs), we give the first MDV-NIZK for\\(\\mathsf {QMA}\\)that only requires one copy of the quantum witness. Finally, we perform a preliminary investigation intotwo-roundsecure quantum computation where each party must obtain output. On the negative side, we identify a broad class of simulation strategies that suffice forclassicaltwo-round secure computation that areunlikelyto work in the quantum setting. Next, as a proof-of-concept, we show that two-round secure quantum computation exists with respect to a quantum oracle.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84242-0_15"}, {"primary_key": "2112611", "vector": [], "sparse_vector": [], "title": "One-Way Functions Imply Secure Computation in a Quantum World.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We prove that quantum-hard one-way functions implysimulation-securequantum oblivious transfer (QOT), which is known to suffice for secure computation of arbitrary quantum functionalities. Furthermore, our construction only makesblack-boxuse of the quantum-hard one-way function. Our primary technical contribution is a construction ofextractable and equivocalquantum bit commitments based on the black-box use of quantum-hard one-way functions in the standard model. Instantiating the <PERSON><PERSON><PERSON><PERSON> (FOCS 1988) framework with these commitments yields simulation-secure QOT.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84242-0_17"}, {"primary_key": "2112612", "vector": [], "sparse_vector": [], "title": "Mac&apos;n&apos;Cheese: Zero-Knowledge Proofs for Boolean and Arithmetic Circuits with Nested Disjunctions.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Zero knowledge proofs are an important building block in many cryptographic applications. Unfortunately, when the proof statements become very large, existing zero-knowledge proof systems easily reach their limits: either the computational overhead, the memory footprint, or the required bandwidth exceed levels that would be tolerable in practice. We present an interactive zero-knowledge proof system for boolean and arithmetic circuits, called\\(\\mathsf {<PERSON>'n'Cheese}\\), with a focus on supporting large circuits. Our work follows the commit-and-prove paradigm instantiated using information-theoretic MACs based on vector oblivious linear evaluation to achieve high efficiency. We additionally show how to optimize disjunctions, with a general OR transformation for proving the disjunction ofmstatements that has communication complexity proportional to the longest statement (plus an additive term logarithmic inm). These disjunctions can further benested, allowing efficient proofs about complex statements with many levels of disjunctions. We also show how to make\\(\\mathsf {Mac'n'Cheese}\\)non-interactive (after a preprocessing phase) using the Fiat-Shamir transform, and with only a small degradation in soundness. We have implemented the online phase of\\(\\mathsf {Mac'n'Cheese}\\)and achieve a runtime of 144 ns per AND gate and 1.5\\(\\upmu \\)s per multiplication gate in\\(\\mathbb {F} _{2^{61} - 1} \\)when run over a network with a 95 ms latency and a bandwidth of 31.5 Mbps. In addition, we show that the disjunction optimization improves communication as expected: when proving a boolean circuit with eight branches and each branch containing roughly 1 billion multiplications,\\(\\mathsf {Mac'n'Ch<PERSON>}\\)requires only 75 more bytes to communicate than in the single branch case.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84259-8_4"}, {"primary_key": "2112613", "vector": [], "sparse_vector": [], "title": "Quadratic Secret Sharing and Conditional Disclosure of Secrets.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "There is a huge gap between the upper and lower bounds on the share size of secret-sharing schemes for arbitraryn-party access structures, and consistent with our current knowledge the optimal share size can be anywhere between polynomial innand exponential inn. For linear secret-sharing schemes, we know that the share size for almost alln-party access structures must be exponential inn. Furthermore, most constructions of efficient secret-sharing schemes are linear. We would like to study larger classes of secret-sharing schemes with two goals. On one hand, we want to prove lower bounds for larger classes of secret-sharing schemes, possibly shedding some light on the share size of general secret-sharing schemes. On the other hand, we want to construct efficient secret-sharing schemes for access structures that do not have efficient linear secret-sharing schemes. Given this motivation, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (ITC’20) defined and studied a new class of secret-sharing schemes in which the shares are generated by applying degree-dpolynomials to the secret and some random field elements. The special case\\(d=1\\)corresponds to linear and multi-linear secret-sharing schemes. We define and study two additional classes of polynomial secret-sharing schemes: (1) schemes in which for every authorized set the reconstruction of the secret is done using polynomials and (2) schemes in which both sharing and reconstruction are done by polynomials. For linear secret-sharing schemes, schemes with linear sharing and schemes with linear reconstruction are equivalent. We give evidence that for polynomial secret-sharing schemes, schemes with polynomial sharing are probably stronger than schemes with polynomial reconstruction. We also prove lower bounds on the share size for schemes with polynomial reconstruction. On the positive side, we provide constructions of secret-sharing schemes and conditional disclosure of secrets (CDS) protocols with quadratic sharing and reconstruction. We extend a construction of Liu et al. (CRYPTO’17) and construct optimal quadratick-server CDS protocols for functionswith message size\\(O(N^{(k-1)/3})\\). We show how to transform our quadratick-server CDS protocol to a robust CDS protocol, and use the robust CDS protocol to construct quadratic secret-sharing schemes for arbitrary access structures with share size\\(O(2^{0.705n})\\); this is better than the best known share size of\\(O(2^{0.7576n})\\)for linear secret-sharing schemes and worse than the best known share size of\\(O(2^{0.585n})\\)for general secret-sharing schemes.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84252-9_25"}, {"primary_key": "2112614", "vector": [], "sparse_vector": [], "title": "Linear Cryptanalysis of FF3-1 and FEA.", "authors": ["<PERSON>"], "summary": "Improved attacks on generic small-domain Feistel ciphers with alternating round tweaks are obtained using linear cryptanalysis. This results in practical distinguishing and message-recovery attacks on the United States format-preserving encryption standard FF3-1 and the South-Korean standards FEA-1 and FEA-2. The data complexity of the proposed attacks on FF3-1 and FEA-1 is\\(\\widetilde{\\mathcal {O}}(N^{r/2 - 1.5})\\), where\\(N^2\\)is the domain size andris the number of rounds. For example, FF3-1 with\\(N = 10^3\\)can be distinguished from an ideal tweakable block cipher with advantage\\(\\ge 1/10\\)using\\(2^{23}\\)encryption queries. Recovering the left half of a message with similar advantage requires\\(2^{24}\\)data. The analysis of FF3-1 serves as an interesting real-world application of (generalized) linear cryptanalysis over the group\\(\\mathbb {Z}/N\\mathbb {Z}\\).", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84242-0_3"}, {"primary_key": "2112615", "vector": [], "sparse_vector": [], "title": "Time- and Space-Efficient Arguments from Groups of Unknown Order.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We construct public-coin time- and space-efficient zero-knowledge arguments for\\(\\mathbf {NP} \\). For every timeTand spaceSnon-deterministic RAM computation, the prover runs in time\\(T \\cdot {{\\,\\mathrm{polylog}\\,}}(T)\\)and space\\(S \\cdot {{\\,\\mathrm{polylog}\\,}}(T)\\), and the verifier runs in time\\(n \\cdot {{\\,\\mathrm{polylog}\\,}}(T)\\), wherenis the input length. Our protocol relies on hidden order groups, which can be instantiated with a trusted setup from the hardness of factoring (products of safe primes), or without a trusted setup using class groups. The argument-system can heuristically be made non-interactive using the Fiat-Shamir transform. Our proof builds on DARK (<PERSON><PERSON><PERSON> et al., Eurocrypt 2020), a recent succinct and efficiently verifiablepolynomial commitment scheme. We show how to implement a variant of DARK in a time- and space-efficient way. Along the way we: Identify a significant gap in the proof of security of DARK. Give a non-trivial modification of the DARK scheme that overcomes the aforementioned gap. The modified version also relies on significantly weaker cryptographic assumptions than those in the original DARK scheme. Our proof utilizes ideas from the theory of integer lattices in a novel way. Generalize <PERSON><PERSON><PERSON>’s (ITCS 2019) proof of exponentiation (\\(\\mathsf {PoE}\\)) protocol to work with general groups of unknown order (without relying on any cryptographic assumption). In proving these results, we develop general-purpose techniques for working with (hidden order) groups, which may be of independent interest.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84259-8_5"}, {"primary_key": "2112616", "vector": [], "sparse_vector": [], "title": "Halo Infinite: Proof-Carrying Data from Additive Polynomial Commitments.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Polynomial commitment schemes (PCS) have recently been in the spotlight for their key role in building SNARKs. A PCS provides the ability to commit to a polynomial over a finite field and prove its evaluation at points. AsuccinctPCS has commitment and evaluation proof size sublinear in the degree of the polynomial. AnefficientPCS has sublinear proof verification. Any efficient and succinct PCS can be used to construct a SNARK with similar security and efficiency characteristics (in the random oracle model). Proof-carrying data (PCD) enables a set of parties to carry out an indefinitely long distributed computation where every step along the way is accompanied by a proof of correctness. It generalizesincrementally verifiable computationand can even be used to construct SNARKs. Until recently, however, the only known method for constructing PCD required expensive SNARK recursion. A system calledHalofirst demonstrated a new methodology for building PCD without SNARKs, exploiting an aggregation property of theBulletproofsinner-product argument. The construction washeuristicbecause it makes non-black-box use of a concrete instantiation of the Fiat-Shamir transform. We expand upon this methodology to show that PCD can be (heuristically) built from any homomorphic polynomial commitment scheme (PCS), even if the PCS evaluation proofs are neither succinct nor efficient. In fact, the Halo methodology extends to any PCS that has an even more general property, namely the ability to aggregate linear combinations of commitments into a new succinct commitment that can later be opened to this linear combination. Our results thus imply new constructions of SNARKs and PCD that were not previously described in the literature and serve as a blueprint for future constructions as well.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84242-0_23"}, {"primary_key": "2112617", "vector": [], "sparse_vector": [], "title": "Sumcheck Arguments and Their Applications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We introduce a class of interactive protocols, which we  callsumcheck arguments, that establishes a novel connection between the sumcheck protocol (<PERSON> et al. JACM 1992) and folding techniques for Pedersen commitments (<PERSON><PERSON><PERSON> et al. EUROCRYPT 2016). We define a class of sumcheck-friendly commitment schemes over modules that captures many examples of interest, and show that the sumcheck protocol applied to a polynomial associated with the commitment scheme yields a succinct argument of knowledge for openings of the commitment. Building on this, we additionally obtain succinct arguments for the NP-complete language R1CS over certain rings. Sumcheck arguments enable us to recover as a special case numerous prior works in disparate cryptographic settings (discrete logarithms, pairings, groups of unknown order, lattices), providing one framework to understand them all. Further, we answer open questions raised in prior works, such as obtaining a lattice-based succinct argument from the SIS assumption for satisfiability problems over rings.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84242-0_26"}, {"primary_key": "2112618", "vector": [], "sparse_vector": [], "title": "Thinking Outside the Superbox.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Designing a block cipher or cryptographic permutation can be approached in many different ways. One such approach, popularized by AES, consists in grouping the bits along the S-box boundaries, e.g., in bytes, and in consistently processing them in these groups. This aligned approach leads to hierarchical structures like superboxes that make it possible to reason about the differential and linear propagation properties using combinatorial arguments. In contrast, an unaligned approach avoids any such grouping in the design of transformations. However, without hierarchical structure, sophisticated computer programs are required to investigate the differential and linear propagation properties of the primitive. In this paper, we formalize this notion of alignment and study four primitives that are exponents of different design strategies. We propose a way to analyze the interactions between the linear and the nonlinear layers w.r.t. the differential and linear propagation, and we use it to systematically compare the four primitives using non-trivial computer experiments. We show that alignment naturally leads to different forms of clustering, e.g., of active bits in boxes, of two-round trails in activity patterns, and of trails in differentials and linear approximations.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84252-9_12"}, {"primary_key": "2112619", "vector": [], "sparse_vector": [], "title": "SSE and SSD: Page-Efficient Searchable Symmetric Encryption.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Searchable Symmetric Encryption (SSE) enables a client to outsource a database to an untrusted server, while retaining the ability to securely search the data. The performance bottleneck of classic SSE schemes typically does not come from their fast, symmetric cryptographic operations, but rather from the cost of memory accesses. To address this issue, many works in the literature have considered the notion of locality, a simple design criterion that helps capture the cost of memory accesses in traditional storage media, such as Hard Disk Drives. A common thread among many SSE schemes aiming to improve locality is that they are built on top of new memory allocation schemes, which form the technical core of the constructions. The starting observation of this work is that for newer storage media such as Solid State Drives (SSDs), which have become increasingly common, locality is not a good predictor of practical performance. Instead, SSD performance mainly depends onpage efficiency, that is, reading as few pages as possible. We define this notion, and identify a simple memory allocation problem,Data-Independent Packing(DIP), that captures the main technical challenge required to build page-efficient SSE. As our main result, we build a page-efficient and storage-efficient data-independent packing scheme, and deduce the\\(\\mathsf {Tethys}\\)SSE scheme, the first SSE scheme to achieve at once\\(\\mathcal {O}(1)\\)page efficiency and\\(\\mathcal {O}(1)\\)storage efficiency. The technical core of the result is a new generalization of cuckoo hashing to items of variable size. Practical experiments show that this new approach achieves excellent performance.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84252-9_6"}, {"primary_key": "2112620", "vector": [], "sparse_vector": [], "title": "Low-Complexity Weak Pseudorandom Functions in $\\mathtt {AC}0[\\mathtt {MOD}2]$.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>v <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Aweak pseudorandom function(WPRF) is a keyed function\\(f_k:\\{0,1\\}^n\\rightarrow \\{0,1\\}\\)such that, for a random keyk, a collection of samples\\((x, f_k(x))\\), foruniformly randominputsx, cannot be efficiently distinguished from totally random input-output pairs (x,y). We study WPRFs in\\(\\mathtt {AC}0[\\mathtt {MOD}2] \\), the class of functions computable by\\(\\mathtt {AC}0 \\)circuits with parity gates, making the following contributions. WPRF by sparse polynomials.We propose the first WPRF candidate that can be computed by sparse multivariate polynomials over\\(\\mathbb {F}_2\\). We prove that it has subexponential security against linear and algebraic attacks. WPRF in\\(\\mathtt {AC}0\\circ \\mathtt {MOD}2 \\).We study the existence of WPRFs computed by\\(\\mathtt {AC}0\\)circuitsoverparity gates. We propose a modified version of a previous WPRF candidate of <PERSON><PERSON><PERSON> et al. (ITCS 2014), and prove that it resists the algebraic attacks that were used by <PERSON><PERSON><PERSON><PERSON> and <PERSON> (ECCC 2017) to break the original candidate in quasipolynomial time. We give evidence against the possibility of usingpublicparity gates and relate this question to other conjectures. Between Lapland and Cryptomania.We show that WPRFs in\\(\\mathtt {AC}0[\\mathtt {MOD}2] \\)imply a variant of the Learning Parity with Noise (LPN) assumption. We further show that WPRFs in a subclass of\\(\\mathtt {AC}0[\\mathtt {MOD}2] \\)that includes a recent candidate by Boyle et al. (FOCS 2020) imply, under a seemingly weak additional conjecture, public-key encryption.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84259-8_17"}, {"primary_key": "2112621", "vector": [], "sparse_vector": [], "title": "Sublinear GMW-Style Compiler for MPC with Preprocessing.", "authors": ["<PERSON><PERSON>", "<PERSON>v <PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We consider the efficiency of protocols for secure multiparty computation (MPC) with a dishonest majority. A popular approach for the design of such protocols is to employpreprocessing. Before the inputs are known, the parties generate correlated secret randomness, which is consumed by a fast and possibly “information-theoretic” online protocol. A powerful technique for securing such protocols against malicious parties useshomomorphic MACsto authenticate the values produced by the online protocol. Compared to a baseline protocol, which is only secure against semi-honest parties, this involves a significant increase in the size of the correlated randomness, by a factor of up to a statistical security parameter. Different approaches for partially mitigating this extra storage cost come at the expense of increasing the online communication. In this work we propose a new technique for protecting MPC with preprocessing against malicious parties. We show that for circuit evaluation protocols that satisfy mild security and structural requirements, that are met by many standard protocols with semi-honest security, the extraadditivestorage and online communication costs are bothlogarithmicin the circuit size. This applies to Boolean circuits and to arithmetic circuits over fields or rings, and to both information-theoretic and computationally secure protocols. Our protocol can be viewed as a sublinear information-theoretic variant of the celebrated “GMW compiler” that applies to natural protocols for MPC with preprocessing. Our compiler makes a novel use of the techniques of <PERSON><PERSON> et al. (Crypto 2019) for sublinear distributed zero knowledge, which were previously only used in the setting ofhonest-majorityMPC.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84245-1_16"}, {"primary_key": "2112622", "vector": [], "sparse_vector": [], "title": "Proof-Carrying Data Without Succinct Arguments.", "authors": ["Benedikt <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Proof-carrying data (PCD) is a powerful cryptographic primitive that enables mutually distrustful parties to perform distributed computations that run indefinitely. Known approaches to construct PCD are based on succinct non-interactive arguments of knowledge (SNARKs) that have a succinct verifier or a succinct accumulation scheme. In this paper we show how to obtain PCD without relying on SNARKs. We construct a PCD scheme given any non-interactive argument of knowledge (e.g., with linear-size arguments) that has asplit accumulation scheme, which is a weak form of accumulation that we introduce. Moreover, we construct a transparent non-interactive argument of knowledge for R1CS whose split accumulation is verifiable via a (small)constant number of group and field operations. Our construction is proved secure in the random oracle model based on the hardness of discrete logarithms, and it leads, via the random oracle heuristic and our result above, to concrete efficiency improvements for PCD. Along the way, we construct a split accumulation scheme for Hadamard products under Pedersen commitments and for a simple polynomial commitment scheme based on Pedersen commitments. Our results are supported by a modular and efficient implementation.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84242-0_24"}, {"primary_key": "2112623", "vector": [], "sparse_vector": [], "title": "Towards Tight Random Probing Security.", "authors": ["G<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Proving the security of masked implementations in theoretical models that are relevant to practice and match the best known attacks of the side-channel literature is a notoriously hard problem. The random probing model is a promising candidate to contribute to this challenge, due to its ability to capture the continuous nature of physical leakage (contrary to the threshold probing model), while also being convenient to manipulate in proofs and to automate with verification tools. Yet, despite recent progress in the design of masked circuits with good asymptotic security guarantees in this model, existing results still fall short when it comes to analyze the security of concretely useful circuits under realistic noise levels and with low number of shares. In this paper, we contribute to this issue by introducing a new composability notion, theProbe Distribution Table (PDT), and a new tool (called STRAPS, for the Sampled Testing of the RAndom Probing Security). Their combination allows us to significantly improve the tightness of existing analyses in the most practical (low noise, low number of shares) region of the design space. We illustrate these improvements by quantifying the random probing security of an AES S-box circuit, masked with the popular multiplication gadget of Ishai, Sahai and Wagner from Crypto 2003, with up to six shares.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84252-9_7"}, {"primary_key": "2112624", "vector": [], "sparse_vector": [], "title": "Adaptive Extractors and Their Application to Leakage Resilient Secret Sharing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce Adaptive Extractors, which unlike traditional randomness extractors, guarantee security even when an adversary obtains leakage on the sourceafterobserving the extractor output. We make a compelling case for the study of such extractors by demonstrating their use in obtaining adaptive leakage in secret sharing schemes. Specifically, at FOCS 2020, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, built an adaptively secure leakage resilient secret sharing scheme (LRSS) with both rate and leakage rate being\\(\\mathcal {O}(1/n)\\), where\\(n\\)is the number of parties. In this work, we build an adaptively secure LRSS that offers an interesting trade-off between rate, leakage rate, and the total number of shares from which an adversary can obtain leakage. As a special case, when consideringt-out-of-nsecret sharing schemes for threshold\\(t = \\alpha n\\)(constant\\(0<\\alpha <1\\)), we build a scheme with a constant rate, constant leakage rate, and allow the adversary leakage from all but\\(t-1\\)of the shares, while giving her the remaining\\(t-1\\)shares completely in the clear. (Prior to this, constant rate LRSS scheme tolerating adaptive leakage was unknown foranythreshold.) Finally, we show applications of our techniques to both non-malleable secret sharing and secure message transmission.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84252-9_20"}, {"primary_key": "2112625", "vector": [], "sparse_vector": [], "title": "Compact Ring Signatures from Learning with Errors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Ring signatures allow a user to sign a message on behalf of a “ring” of signers, while hiding the true identity of the signer. As the degree of anonymity guaranteed by a ring signature is directly proportional to the size of the ring, an important goal in cryptography is to study constructions that minimize the size of the signature as a function of the number of ring members. In this work, we present the first compact ring signature scheme (i.e., where the size of the signature grows logarithmically with the size of the ring) from the (plain) learning with errors (LWE) problem. The construction is in the standard model and it does not rely on a common random string or on the random oracle heuristic. In contrast with the prior work of <PERSON><PERSON><PERSON> al.[EUROCRYPT’2019], our scheme does not rely on bilinear pairings, which allows us to show that the scheme is post-quantum secure assuming the quantum hardness of LWE. At the heart of our scheme is a new construction of compact and statistically witness indistinguishable ZAP arguments for NP\\(\\cap \\)coNP, that we show to be sound based on the plain LWE assumption. Prior to our work, statistical ZAPs (for all of NP) were known to exist only assumingsub-exponentialLWE. We believe that this scheme might find further applications in the future.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84242-0_11"}, {"primary_key": "2112626", "vector": [], "sparse_vector": [], "title": "Does Fiat-Shamir Require a Cryptographic Hash Function?", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The Fiat-Shamir transform is a general method for reducing interaction in public-coin protocols by replacing the random verifier messages with deterministic hashes of the protocol transcript. The soundness of this transformation is usuallyheuristicand lacks a formal security proof. Instead, to argue security, one can rely on therandom oracle methodology, which informally states that whenever a random oracle soundly instantiates Fiat-Shamir, a hash function that is “sufficiently unstructured” (such as fixed-length SHA-2) should suffice. Finally, for some special interactive protocols, it is known how to (1) isolate a concrete security property of a hash function that suffices to instantiate Fiat-Shamir and (2) build a hash function satisfying this property under a cryptographic assumption such as Learning with Errors. In this work, we abandon this methodology and ask whether Fiat-Shamir truly requires a cryptographic hash function. Perhaps surprisingly, we show that in two of its most common applications—building signature schemes as well as (general-purpose) non-interactive zero-knowledge arguments—there are sound Fiat-Shamir instantiations using extremely simple and non-cryptographic hash functions such as sum-mod-por bit decomposition. In some cases, we make idealized assumptions (i.e., we invoke the generic group model), while in others, we prove soundness in the plain model. On the negative side, we also identify important cases in which a cryptographic hash function is provably necessary to instantiate Fiat-Shamir. We hope this work leads to an improved understanding of the precise role of the hash function in the Fiat-Shamir transformation.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84259-8_12"}, {"primary_key": "2112627", "vector": [], "sparse_vector": [], "title": "MHz2k: MPC from HE over $\\mathbb {Z}_{2k}$ with New Packing, Simpler Reshare, and Better ZKP.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose a multi-party computation (MPC) protocol over\\(\\mathbb {Z}_{2^k}\\)secure against actively corrupted majority from somewhat homomorphic encryption. The main technical contributions are: (i) a new efficient packing method for\\(\\mathbb {Z}_{2^k}\\)-messages in lattice-based somewhat homomorphic encryption schemes, (ii) a simpler reshare protocol for level-dependent packings, (iii) a more efficient zero-knowledge proof of plaintext knowledge on cyclotomic rings\\({\\mathbb Z}[X]/\\varPhi _M(X)\\)withMbeing a prime. Integrating them, our protocol shows from 2.2x upto 4.8x improvements in amortized communication costs compared to the previous best results. Our techniques not only improve the efficiency of MPC over\\(\\mathbb {Z}_{2^k}\\)considerably, but also provide a toolkit that can be leveraged when designing other cryptographic primitives over\\(\\mathbb {Z}_{2^k}\\).", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84245-1_15"}, {"primary_key": "2112628", "vector": [], "sparse_vector": [], "title": "A Black-Box Approach to Post-Quantum Zero-Knowledge in Constant Rounds.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In a recent seminal work, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (STOC ’20) gave the first construction of a constant round zero-knowledge argument for\\(\\mathbf {NP}\\)secure against quantum attacks. However, their construction has several drawbacks compared to the classical counterparts. Specifically, their construction only achieves computational soundness, requires strong assumptions of quantum hardness of learning with errors (QLWE assumption) and the existence of quantum fully homomorphic encryption (QFHE), and relies on non-black-box simulation. In this paper, we resolve these issues at the cost of weakening the notion of zero-knowledge to what is called\\(\\epsilon \\)-zero-knowledge. Concretely, we construct the following protocols: – We construct a constant round interactive proof for\\(\\mathbf {NP}\\)that satisfiesstatisticalsoundness andblack-box\\(\\epsilon \\)-zero-knowledge against quantum attacks assuming the existence ofcollapsing hash functions, which is a quantum counterpart of collision-resistant hash functions. Interestingly, this construction is just an adapted version of the classical protocol by <PERSON><PERSON><PERSON> and <PERSON><PERSON> (JoC ’96) though the proof of\\(\\epsilon \\)-zero-knowledge property against quantum adversaries requires novel ideas. – We construct a constant round interactive argument for\\(\\mathbf {NP}\\)that satisfies computational soundness andblack-box\\(\\epsilon \\)-zero-knowledge against quantum attacks only assuming the existence of post-quantum one-way functions. At the heart of our results is a new quantum rewinding technique that enables a simulator to extract a committed message of a malicious verifier while simulating verifier’s internal state in an appropriate sense.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84242-0_12"}, {"primary_key": "2112629", "vector": [], "sparse_vector": [], "title": "Subquadratic SNARGs in the Random Oracle Model.", "authors": ["<PERSON>", "E<PERSON>"], "summary": "In a seminal work, <PERSON><PERSON><PERSON> (FOCS 1994) gave the first succinct non-interactive argument (SNARG) in the random oracle model (ROM). The construction combines a PCP and a cryptographic commitment, and has several attractive features: it is plausibly post-quantum; it can be heuristically instantiated via lightweight cryptography; and it has a transparent (public-coin) parameter setup. However, it also has a significant drawback: a large argument size. In this work, we provide a new construction that achieves a smaller argument size. This is the first progress on the Micali construction since it was introduced over 25 years ago. A SNARG in the ROM is\\((t,\\mathsf {\\epsilon })\\)-secureif every\\(t\\)-query malicious prover can convince the verifier of a false statement with probability at most\\(\\mathsf {\\epsilon }\\). For\\((t,\\mathsf {\\epsilon })\\)-security, the argument size of all known SNARGs in the ROM (including Micali’s) is\\(\\tilde{O}((\\log (t/\\mathsf {\\epsilon }))^2)\\)bits,evenif one were to rely on conjectured probabilistic proofs well beyond current techniques. In practice, these costs lead to SNARGs that are much larger than constructions based on other (pre-quantum and costly) tools. This has led many to believe that SNARGs in the ROM are inherently quadratic. We show that this is not the case. We present a SNARG in the ROM with a sub-quadratic argument size:\\(\\tilde{O}(\\log (t/\\mathsf {\\epsilon }) \\cdot \\log t)\\). Our construction relies on a strong soundness notion for PCPs and a weak binding notion for commitments. We hope that our work paves the way for understanding if a linear argument size, that is\\(O(\\log (t/\\mathsf {\\epsilon }))\\), is achievable in the ROM.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84242-0_25"}, {"primary_key": "2112630", "vector": [], "sparse_vector": [], "title": "Fluid MPC: Secure Multiparty Computation with Dynamic Participants.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Existing approaches to secure multiparty computation (MPC) require all participants to commit to the entire duration of the protocol. As interest in MPC continues to grow, it is inevitable that there will be a desire to use it to evaluate increasingly complex functionalities, resulting in computations spanning several hours or days. Such scenarios call for adynamicparticipation model for MPC where participants have the flexibility to go offline as needed and (re)join when they have available computational resources. Such a model would also democratize access to privacy-preserving computation by facilitating an “MPC-as-a-service” paradigm—the deployment of MPC in volunteer-operated networks (such as blockchains, where dynamism is inherent) that perform computation on behalf of clients. In this work, we initiate the study offluid MPC, where parties can dynamically join and leave the computation. The minimum commitment required from each participant is referred to asfluidity, measured in the number of rounds of communication that it must stay online. Our contributions are threefold: We provide a formal treatment of fluid MPC, exploring various possible modeling choices. We construct information-theoretic fluid MPC protocols in the honest-majority setting. Our protocols achievemaximal fluidity, meaning that a party can exit the computation after receiving and sending messages in one round. We implement our protocol and test it in multiple network settings.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84245-1_4"}, {"primary_key": "2112631", "vector": [], "sparse_vector": [], "title": "Non-interactive Batch Arguments for NP from Standard Assumptions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Zhengzhong Jin"], "summary": "We study the problem of designingnon-interactive batch argumentsfor\\(\\mathsf {NP}\\). Such an argument system allows an efficient prover to prove multiple\\(\\mathsf {NP}\\)statements, with size smaller than the combined witness length. We provide the first construction of such an argument system for\\(\\mathsf {NP}\\)in the common reference string model based on standard cryptographic assumptions. Prior works either require non-standard assumptions (or the random oracle model) or can only support private verification. At the heart of our result is a newdual modeinteractive batch argument system for\\(\\mathsf {NP}\\). We show how to apply the correlation-intractability framework for Fiat-Shamir – that has primarily been applied to proof systems – to such interactive arguments.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84259-8_14"}, {"primary_key": "2112632", "vector": [], "sparse_vector": [], "title": "Game-Theoretic Fairness Meets Multi-party Protocols: The Case of Leader Election.", "authors": ["<PERSON><PERSON><PERSON>", "T.<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Suppose thatnplayers want to elect a random leader and they communicate by posting messages to a common broadcast channel. This problem is called leader election, and it is fundamental to the distributed systems and cryptography literature. Recently, it has attracted renewed interests due to its promised applications in decentralized environments. In a game theoretically fair leader election protocol, roughly speaking, we want that even a majority coalition cannot increase its own chance of getting elected, nor hurt the chance of any honest individual. The folklore tournament-tree protocol, which completes in logarithmically many rounds, can easily be shown to satisfy game theoretic security. To the best of our knowledge, no sub-logarithmic round protocol was known in the setting that we consider. We show that by adopting an appropriate notion of approximate game-theoretic fairness, and under standard cryptographic assumption, we can achieve\\((1-1/2^{\\varTheta (r)})\\)-fairness inrrounds for\\(\\varTheta (\\log \\log n) \\le r \\le \\varTheta (\\log n)\\), wherendenotes the number of players. In particular, this means that we can approximately match the fairness of the tournament tree protocol using as few as\\(O(\\log \\log n)\\)rounds. We also prove a lower bound showing that logarithmically many rounds are necessary if we restrict ourselves to “perfect” game-theoretic fairness and protocols that are “very similar in structure” to the tournament-tree protocol. Although leader election is a well-studied problem in other contexts in distributed computing, our work is the first exploration of the round complexity ofgame-theoretically fairleader election in the presence of a possibly majority coalition. As a by-product of our exploration, we suggest a new, approximate game-theoretic fairness notion, called “approximate sequential fairness”, which provides a more desirable solution concept than some previously studied approximate fairness notions.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84245-1_1"}, {"primary_key": "2112633", "vector": [], "sparse_vector": [], "title": "Hidden Cosets and Applications to Unclonable Cryptography.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In 2012, <PERSON><PERSON> and <PERSON><PERSON> introduced the idea ofhidden subspace statesto build public-key quantum money [STOC ’12]. Since then, this idea has been applied to realize several other cryptographic primitives which enjoy some form of unclonability. In this work, we propose a generalization of hidden subspace states to hiddencosetstates. We study different unclonable properties of coset states and several applications: We show that, assuming indistinguishability obfuscation (\\(\\mathsf{iO}\\)), hidden coset states possess a certaindirect product hardnessproperty, which immediately implies a tokenized signature scheme in the plain model. Previously, a tokenized signature scheme was known only relative to an oracle, from a work of <PERSON><PERSON><PERSON> and <PERSON> [QCrypt ’17]. Combining a tokenized signature scheme with extractable witness encryption, we give a construction of an unclonable decryption scheme in the plain model. The latter primitive was recently proposed by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [ePrint ’20], who gave a construction relative to a classical oracle. We conjecture that coset states satisfy a certain natural (information-theoretic) monogamy-of-entanglement property. Assuming this conjecture is true, we remove the requirement for extractable witness encryption in our unclonable decryption construction, by relying instead on compute-and-compare obfuscation for the class of unpredictable distributions. As potential evidence in support of the monogamy conjecture, we prove a weaker version of this monogamy property, which we believe will still be of independent interest. Finally, we give the first construction of a copy-protection scheme for pseudorandom functions (PRFs) in the plain model. Our scheme is secure either assuming\\(\\mathsf{iO}\\),\\(\\mathsf{OWF}\\)and extractable witness encryption, or assuming\\(\\mathsf{iO}, \\mathsf{OWF}\\), compute-and-compare obfuscation for the class of unpredictable distributions, and the conjectured monogamy property mentioned above.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84242-0_20"}, {"primary_key": "2112634", "vector": [], "sparse_vector": [], "title": "Secure Wire Shuffling in the Probing Model.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper we describe the first improvement of the wire shuffling countermeasure against side-channel attacks described by <PERSON><PERSON>, <PERSON><PERSON> and <PERSON> at Crypto 2003. More precisely, we show how to get worst case statistical security againsttprobes with running time\\({\\mathcal O}(t)\\)instead of\\({\\mathcal O}(t \\log t)\\); our construction is also much simpler. Recall that the classical masking countermeasure achieves perfect security but with running time\\({\\mathcal O}(t^2)\\). We also describe a practical implementation for AES that outperforms the masking countermeasure for\\(t \\ge 6\\,000\\).", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84252-9_8"}, {"primary_key": "2112635", "vector": [], "sparse_vector": [], "title": "Silver: Silent VOLE and Oblivious Transfer from Hardness of Decoding Structured LDPC Codes.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We put forth new protocols for oblivious transfer extension and vector OLE, calledSilver, for SILent Vole and oblivious transfER. Silver offers extremely high performances: generating 10 million random OTs on one core of a standard laptop requires only 300 ms of computation and 122 KB of communication. This represents 37% less computation and\\(\\sim \\)1300\\(\\times \\)less communication than the standard IKNP protocol, as well as\\(\\sim \\)4\\(\\times \\)less computation and\\(\\sim \\)14\\(\\times \\)less communication than the recent protocol of <PERSON> et al. (CCS 2020). Silver issilent: after a one-time cheap interaction, two parties can store small seeds, from which they can laterlocallygenerate a large number of OTswhile remaining offline. Neither IKNP nor Yang et al. enjoys this feature; compared to the best known silent OT extension protocol of <PERSON> et al. (CCS 2019), upon which we build up, Silver has 19\\(\\times \\)less computation, and the same communication. Due to its attractive efficiency features, Silver yields major efficiency improvements in numerous MPC protocols. Our approach is a radical departure from the standard paradigm for building MPC protocols, in that we donotattempt to base our constructions on a well-studied assumption. Rather, we follow an approach closer in spirit to the standard paradigm in the design of symmetric primitives: we identify a set of fundamental structural properties that allow us to withstand all known attacks, and put forth a candidate design, guided by our analysis. We also rely on extensive experimentations to analyze our candidate and experimentally validate their properties. In essence, our approach boils down to constructing new families of linear codes with (plausibly) high minimum distance and extremely low encoding time. While further analysis is of course welcomed in order to gain total confidence in the security of Silver, we hope and believe that initiating this approach to the design of MPC primitives will pave the way to new secure primitives with extremely attractive efficiency features.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84252-9_17"}, {"primary_key": "2112636", "vector": [], "sparse_vector": [], "title": "Asymptotically-Good Arithmetic Secret Sharing over $\\mathbb {Z}/p{\\ell }\\mathbb {Z}$ with Strong Multiplication and Its Applications to Efficient MPC.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper studies information-theoretically secure multiparty computation (MPC) over rings\\(\\mathbb {Z}/p^{\\ell }\\mathbb {Z}\\). In the work of [Abs+19a, TCC’19], a protocol based on the Shamir secret sharing over\\(\\mathbb {Z}/p^{\\ell }\\mathbb {Z}\\)was presented. As in the field case, its limitation is that the share size grows as the number of players increases. Then several MPC protocols were developed in [Abs+20, Asiacrypt’20] to overcome this limitation. However, (i) their offline multiplication gate has super-linear communication complexity in the number of players; (ii) the share size is doubled for the most important case, namely over\\(\\mathbb {Z}/2^{\\ell }\\mathbb {Z}\\)due to infeasible lifting of self-orthogonal codes from fields to rings; (iii) most importantly, the BGW model could not be applied via the secret sharing given in [Abs+20, Asiacrypt’20] due to lack of strong multiplication. In this paper we overcome all the drawbacks mentioned above. Of independent interest, we establish an arithmetic secret sharing with strong multiplication, which is the most important primitive in the BGW model. Incidentally, our solution to (i) has some advantages over the concurrent one of [PS21, EC’21], since it is direct, is only one-page long, and furthermore carries over\\(\\mathbb {Z}/p^{\\ell }\\mathbb {Z}\\). Finally, we lift Reverse Multiplication Friendly Embeddings (RMFE) from fields to rings, with same (linear) complexity. Note that RMFE has become a standard technique for communication complexity in MPC in the regime over many instances of the same circuit, as in [Cas+18, Crypto’18] and [DLN19, Crypto’19]. We thus recover the same amortized complexity of MPC over\\(\\mathbb {Z}/2^{\\ell }\\mathbb {Z}\\)than over fields. To obtain our theoretical results, we use the existence of lifts of curves over rings, then use the known results stating that Riemann-Roch spaces are free modules. To make our scheme practical, we start from good algebraic geometry codes over finite fields obtained from existing computational techniques. Then we present, and implement, an efficient algorithm to Hensel-lift the generating matrix of the code, such that the multiplicative conditions are preserved over rings. On the other hand, a random lifting of codes over rings does not preserve multiplicativity in general. Finally we provide efficient methods for sharing and reconstruction over rings.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84252-9_22"}, {"primary_key": "2112637", "vector": [], "sparse_vector": [], "title": "Non-malleable Codes for Bounded Parallel-Time Tampering.", "authors": ["<PERSON>-<PERSON>ed", "<PERSON><PERSON>", "<PERSON>"], "summary": "Non-malleable codes allow one to encode data in such a way that once a codeword is being tampered with, the modified codeword is either an encoding of the original message, or a completely unrelated one. Since the introduction of this notion by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> (ICS ’10 and J. AC<PERSON> ’18), there has been a large body of works realizing such coding schemes secure against various classes of tampering functions. It is well known that there is no efficient non-malleable code secure against all polynomial size tampering functions. Nevertheless, no code which is non-malleable forboundedpolynomial size attackers is known and obtaining such a code has been a major open problem. We present the first construction of a non-malleable code secure against all polynomial size tampering functions that have bounded parallel time. This is an even larger class than all bounded polynomial size functions. In particular, this class includes all functions in non-uniform\\(\\mathbf {NC}\\)(and much more). Our construction is in the plain model (i.e., no trusted setup) and relies on several cryptographic assumptions such as keyless hash functions, time-lock puzzles, as well as other standard assumptions. Additionally, our construction has several appealing properties: the complexity of encoding is independent of the class of tampering functions and we can obtain (sub-)exponentially small error.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84252-9_18"}, {"primary_key": "2112638", "vector": [], "sparse_vector": [], "title": "Broadcast-Optimal Two Round MPC with an Honest Majority.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper closes the question of the possibility of two-round MPC protocols achieving different security guarantees with and without the availability of broadcast in any given round. <PERSON><PERSON> al.[CGZ20] study this question in the dishonest majority setting; we complete the picture by studying the honest majority setting. In the honest majority setting, given broadcast in both rounds, it is known that the strongest guarantee—guaranteed output delivery—is achievable [GLS15]. We show that, given broadcast in the first round only, guaranteed output delivery is still achievable. Given broadcast in the second round only, we give a new construction that achieves identifiable abort, and we show that fairness—and thus guaranteed output delivery—are not achievable in this setting. Finally, if only peer-to-peer channels are available, we show that the weakest guarantee—selective abort—is the only one achievable for corruption thresholds\\(t> 1\\)and for\\(t=1\\)and\\(n=3\\). On the other hand, it is already known that selective abort can be achieved in these cases. In the remaining cases, i.e.,\\(t= 1\\)and\\(n\\ge 4\\), it is known [IKP10,IKKP15] that guaranteed output delivery (and thus all weaker guarantees) are possible.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84245-1_6"}, {"primary_key": "2112639", "vector": [], "sparse_vector": [], "title": "MPC-Friendly Symmetric Cryptography from Alternating Moduli: Candidates, Protocols, and Applications.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Tzipora Hale<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study new candidates for symmetric cryptographic primitives that leverage alternation between linear functions over\\(\\mathbb {Z}_2\\)and\\(\\mathbb {Z}_3\\)to support fast protocols for secure multiparty computation (MPC). This continues the study of weak pseudorandom functions of this kind initiated by <PERSON><PERSON> et al. (TCC 2018) and <PERSON><PERSON> et al. (PKC 2021). We make the following contributions. Candidates.We propose new designs of symmetric primitives based on alternating moduli. These include candidate one-way functions, pseudorandom generators, and weak pseudorandom functions. We propose concrete parameters based on cryptanalysis. Protocols.We provide a unified approach for securely evaluating modulus-alternating primitives in different MPC models. For the original candidate of <PERSON><PERSON> et al., our protocols obtain at least 2x improvement in all performance measures. We report efficiency benchmarks of an optimized implementation. Applications.We showcase the usefulness of our candidates for a variety of applications. This includes short “Picnic-style” signature schemes, as well as protocols for oblivious pseudorandom functions, hierarchical key derivation, and distributed key generation for function secret sharing.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84259-8_18"}, {"primary_key": "2112640", "vector": [], "sparse_vector": [], "title": "No Time to Hash: On Super-Efficient Entropy Accumulation.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Real-world random number generators (RNGs) cannot afford to use (slow) cryptographic hashing every time they refresh their stateRwith a new entropic inputX. Instead, they use “superefficient” simple entropy-accumulation procedures, such as where\\(\\mathsf {rot}_{\\alpha ,n}\\)rotates ann-bit stateRby some fixed number\\(\\alpha \\). For example, Microsoft’s RNG uses\\(\\alpha =5\\)for\\(n=32\\)and\\(\\alpha =19\\)for\\(n=64\\). Where do these numbers come from? Are they good choices? Should rotation be replaced by a better permutation\\(\\pi \\)of the input bits? In this work we initiate a rigorous study of these pragmatic questions, by modeling the sequence of successive entropic inputs\\(X_1,X_2,\\ldots \\)asindependent(but otherwise adversarial) samples from some natural distribution family\\(\\mathcal{D}\\). Our contribution is as follows. We define2-monotone distributionsas a rich family\\(\\mathcal{D}\\)that includes relevant real-world distributions (Gaussian, exponential, etc.), but avoids trivial impossibility results. For any\\(\\alpha \\)with\\(\\gcd (\\alpha ,n)=1\\), we show that rotation accumulates\\(\\varOmega (n)\\)bits of entropy fromnindependent samples\\(X_1,\\ldots ,X_n\\)from any (unknown) 2-monotone distribution with entropy\\(k > 1\\). However, we also show some choices of\\(\\alpha \\)perform much better than others for a givenn. E.g., we show\\(\\alpha =19\\)is one of the best choices for\\(n=64\\); in contrast,\\(\\alpha =5\\)is good, but generally worse than\\(\\alpha =7\\), for\\(n=32\\). More generally, given a permutation\\(\\pi \\)and\\(k\\ge 1\\), we define a simple parameter, thecovering number\\(C_{\\pi ,k}\\), and show that it characterizes the number of steps before the rule accumulates nearlynbits of entropy from independent, 2-monotone samples of min-entropykeach. We build a simple permutation\\(\\pi ^*\\), which achieves nearly optimal\\(C_{\\pi ^*,k}\\approx n/k\\)for all values ofksimultaneously, and experimentally validate that it compares favorably with all rotations\\(\\mathsf {rot}_{\\alpha ,n}\\).", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84259-8_19"}, {"primary_key": "2112641", "vector": [], "sparse_vector": [], "title": "Meet-in-the-Middle Attacks Revisited: Key-Recovery, Collision, and Preimage Attacks.", "authors": ["<PERSON><PERSON> Dong", "<PERSON><PERSON><PERSON>", "<PERSON>wei Sun", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "At EUROCRYPT 2021, <PERSON><PERSON> et al. proposed an automatic method for systematically exploring the configuration space of meet-in-the-middle (MITM) preimage attacks. We further extend it into a constraint-based framework for finding exploitable MITM characteristics in the context of key-recovery and collision attacks by taking the subtle peculiarities of both scenarios into account. Moreover, to perform attacks based on MITM characteristics with nonlinear constrained neutral words, which have not been seen before, we present a procedure for deriving the solution spaces of neutral words without solving the corresponding nonlinear equations or increasing the overall time complexities of the attack. We apply our method to concrete symmetric-key primitives, includingSKINNY,ForkSkinny,Romulus-H,Saturnin,Grøstl,WHIRLPOOL, and hashing modes withAES-256. As a result, we identify the first 23-round key-recovery attack onSKINNY-n-3nand the first 24-round key-recovery attack onForkSkinny-n-3nin the single-key model. Moreover, improved (pseudo) preimage or collision attacks on round-reducedWHIRLPOOL,Grøstl, and hashing modes withAES-256 are obtained. In particular, employing the new representation of theAESkey schedule due to <PERSON><PERSON><PERSON> and <PERSON><PERSON> (EUROCRYPT 2021), we identify the first preimage attack on 10-roundAES-256 hashing.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84252-9_10"}, {"primary_key": "2112642", "vector": [], "sparse_vector": [], "title": "Non-interactive Secure Multiparty Computation for Symmetric Functions, Revisited: More Efficient Constructions and Extensions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Non-interactive secure multiparty computation (NIMPC) is a variant of secure computation which allows each ofnplayers to send only a single message depending on his input and correlated randomness. Abelian programs, which can realize any symmetric function, are defined as functions on the sum of the players’ inputs over an abelian group and provide useful functionalities for real-world applications. We improve and extend the previous results in the following ways: We present NIMPC protocols for abelian programs that improve the best known communication complexity. If inputs take any value of an abelian group\\(\\mathbb {G}\\), our protocol achieves the communication complexity\\(O(|\\mathbb {G}|(\\log |\\mathbb {G}|)^2)\\)improving\\(O(|\\mathbb {G}|^2n^2)\\)of <PERSON><PERSON><PERSON> et al. (Crypto 2014). If players are limited to inputs from subsets of size at mostd, our protocol achieves\\(|\\mathbb {G}|(\\log |\\mathbb {G}|)^2(\\max \\{n,d\\})^{(1+o(1))t}\\)wheretis a corruption threshold. This result improves\\(|\\mathbb {G}|^3(nd)^{(1+o(1))t}\\)of <PERSON><PERSON><PERSON> et al. (Crypto 2014), and even\\(|\\mathbb {G}|^{\\log n+O(1)}n\\)of <PERSON><PERSON><PERSON><PERSON> et al. (Crypto 2017) if\\(t=o(\\log n)\\)and\\(|\\mathbb {G}|=n^{\\varTheta (1)}\\). We propose for the first time NIMPC protocols for linear classifiers that are more efficient than those obtained from the generic construction. We revisit a known transformation of Benhamouda et al. (Crypto 2017) from Private Simultaneous Messages (PSM) to NIMPC, which we repeatedly use in the above results. We reveal that a sub-protocol used in the transformation does not satisfy the specified security. We also fix their protocol with only constant overhead in the communication complexity. As a byproduct, we obtain an NIMPC protocol for indicator functions with asymptotically optimal communication complexity with respect to the input length.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84245-1_11"}, {"primary_key": "2112643", "vector": [], "sparse_vector": [], "title": "Efficient Information-Theoretic Multi-party Computation over Non-commutative Rings.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We construct the first efficient, unconditionally secure MPC protocol that only requires black-box access to a non-commutative ringR. Previous results in the same setting were efficient only either for a constant number of corruptions or when computing branching programs and formulas. Our techniques are based on a generalization of <PERSON><PERSON><PERSON>’s secret sharing to non-commutative rings, which we derive from the work on Reed Solomon codes by <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON> (IEEE Transactions on Information Theory, 2013). When the center of the ring contains a set\\(A = \\{\\alpha _0, \\ldots , \\alpha _n\\}\\)such that\\(\\forall i \\ne j, \\alpha _i \\,-\\, \\alpha _j \\in R^*\\), the resulting secret sharing scheme is strongly multiplicative and we can generalize existing constructions over finite fields without much trouble. Most of our work is devoted to the case where the elements ofAdo not commute with all ofR, but they just commute with each other. For such rings, the secret sharing scheme cannot be linear “on both sides” and furthermore it is not multiplicative. Nevertheless, we are still able to build MPC protocols with a concretely efficient online phase and black-box access toR. As an example we consider the ring\\(\\mathcal {M}_{m\\times m}(\\mathbb {Z}/2^k\\mathbb {Z})\\), for which when\\(m > \\log (n+1)\\), we obtain protocols that require around\\(\\lceil \\log (n+1)\\rceil /2\\)less communication and\\(2\\lceil \\log (n+1)\\rceil \\)less computation than the state of the art protocol based on Circuit Amortization Friendly Encodings (Dalskov, Lee and Soria-Vazquez,ASIACRYPT 2020). In this setting with a “less commutative”A, our black-box preprocessing phase has a less practical complexity of\\(\\mathsf {poly}(n)\\). We fix this by additionally providing specialized, concretely efficient preprocessing protocols for\\(\\mathcal {M}_{m\\times m}(\\mathbb {Z}/2^k\\mathbb {Z})\\)that exploit the structure of the matrix ring.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84245-1_12"}, {"primary_key": "2112644", "vector": [], "sparse_vector": [], "title": "Witness Authenticating NIZKs and Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We initiate the study of witness authenticating NIZK proof systems (waNIZKs), in which one can use a witnesswof a statementxto identify whether a valid proof forxis indeed generated usingw. Such a new identification functionality enables more diverse applications, and it also puts new requirements on soundness that: (1) no adversary can generate a valid proof that will not be identified by any witness; (2) or forge a proof using her valid witness to frame others. To work around the obvious obstacle towards conventional zero-knowledgeness, we define entropic zero-knowledgeness that requires the proof to leak no partial information, if the witness has sufficient computational entropy. We give a formal treatment of this new primitive. The modeling turns out to be quite involved and multiple subtle points arise and particular cares are required. We present general constructions from standard assumptions. We also demonstrate three applications in non-malleable (perfectly one-way) hash, group signatures with verifier-local revocations and plaintext-checkable public-key encryption. Our waNIZK provides a new tool to advance the state of the art in all these applications.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84259-8_1"}, {"primary_key": "2112645", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON><PERSON> with Stateless Deterministic Signing from Standard Assumptions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Schnorr’s signature scheme permits an elegant threshold signing protocol due to its linear signing equation. However each new signature consumes fresh randomness, which can be a major attack vector in practice. Sources of randomness in deployments are frequently either unreliable, or requirestate continuity, i.e. reliable fresh state resilient to rollbacks. State continuity is a notoriously difficult guarantee to achieve in practice, due to system crashes caused by software errors, malicious actors, or power supply interruptions (<PERSON><PERSON> et al., S&P ’11). This is a non-issue for Schnorr variants such as EdDSA, which is specified to derive nonces deterministically as a function of the message and the secret key. However, it is challenging to translate these benefits to the threshold setting, specifically to construct a threshold Schnorr scheme where signing neither requires parties to consume fresh randomness nor update long-term secret state. In this work, we construct a dishonest majority threshold Schnorr protocol that enables such stateless deterministic nonce derivation using standardized block ciphers. Our core technical ingredients are new tools for the zero-knowledge from garbled circuits (ZKGC) paradigm to aid in verifying correct nonce derivation: A mechanism based on UC Commitments that allows a prover to commit once to a witness, and prove an unbounded number of statements online with only cheap symmetric key operations. A garbling gadget to translate intermediate garbled circuit wire labels to arithmetic encodings. A proof per our scheme requires only a small constant number of exponentiations.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84242-0_6"}, {"primary_key": "2112646", "vector": [], "sparse_vector": [], "title": "Oblivious Key-Value Stores and Amplification for Private Set Intersection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many recent private set intersection (PSI) protocols encode input sets as polynomials. We consider the more general notion of an oblivious key-value store (OKVS), which is a data structure that compactly represents a desired mapping\\(k_i \\mapsto v_i\\). When the\\(v_i\\)values are random, the OKVS data structure hides the\\(k_i\\)values that were used to generate it. The simplest (and size-optimal) OKVS is a polynomialpthat is chosen using interpolation such that\\(p(k_i)=v_i\\). We initiate the formal study of oblivious key-value stores, and show new constructions resulting in the fastest OKVS to date. Similarly to cuckoo hashing, current analysis techniques are insufficient for findingconcreteparameters to guarantee a small failure probability for our OKVS constructions. Moreover, it would cost too much to run experiments to validate a small upperbound on the failure probability. We therefore show novel techniques to amplify an OKVS construction which has a failure probabilityp, to an OKVS with a similar overhead and failure probability\\(p^c\\). Settingpto be moderately small enables to validate it by running a relatively small number ofO(1/p) experiments. This validates a\\(p^c\\)failure probability for the amplified OKVS. Finally, we describe how OKVS can significantly improve the state of the art of essentially all variants of PSI. This leads to the fastest two-party PSI protocols to date, for both the semi-honest and the malicious settings. Specifically, in networks with moderate bandwidth (e.g., 30–300 Mbps) our malicious two-party PSI protocol has 40% less communication and is 20–40% faster than the previous state of the art protocol, even though the latter only has heuristic confidence.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84245-1_14"}, {"primary_key": "2112647", "vector": [], "sparse_vector": [], "title": "YOSO: You Only Speak Once - Secure MPC with Stateless Ephemeral Roles.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The inherent difficulty of maintaining stateful environments over long periods of time gave rise to the paradigm ofserverless computing, where mostly stateless components are deployed on demand to handle computation tasks, and are torn down once their task is complete. Serverless architecture could offer the added benefit of improved resistance to targeted denial-of-service attacks, by hiding from the attacker the physical machines involved in the protocol until after they complete their work. Realizing such protection, however, requires that the protocol only uses stateless parties, where each party sends only one message and never needs to speaks again. Perhaps the most famous example of this style of protocols is the Nakamoto consensus protocol used in Bitcoin: A peer can win the right to produce the next block by running a local lottery (mining) while staying covert. Once the right has been won, it is executed by sending asinglemessage. After that, the physical entity never needs to send more messages. We refer to this as the You-Only-Speak-Once (YOSO) property, and initiate the formal study of it within a new model that we call the YOSO model. Our model is centered around the notion ofroles, which are stateless parties that can only send a single message. Crucially, our modelling separates the protocol design, that only uses roles, from the role-assignment mechanism, that assigns roles to actual physical entities. This separation enables studying these two aspects separately, and our YOSO model in this work only deals with the protocol-design aspect. We describe several techniques for achieving YOSO MPC; both computational and information theoretic. Our protocols are synchronous and provide guaranteed output delivery (which is important for application domains such as blockchains), assuming honest majority of roles in every time step. We describe a practically efficient computationally-secure protocol, as well as a proof-of-concept information theoretically secure protocol.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84245-1_3"}, {"primary_key": "2112648", "vector": [], "sparse_vector": [], "title": "Tight State-Restoration Soundness in the Algebraic Group Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Most efficient zero-knowledge arguments lack a concrete security analysis, making parameter choices and efficiency comparisons challenging. This is even more true for non-interactive versions of these systems obtained via the Fiat-Shamir transform, for which the security guarantees generically derived from the interactive protocol are often too weak, even when assuming a random oracle. This paper initiates the study ofstate-restoration soundnessin the algebraic group model (AGM) of <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> (CRYPTO ’18). This is a stronger notion of soundness for an interactive proof or argument which allows the prover to rewind the verifier, and which is tightly connected with the concrete soundness of the non-interactive argument obtained via the Fiat-Shamir transform. We propose a general methodology to prove tight bounds on state-restoration soundness, and apply it to variants of Bulletproofs (Bo<PERSON><PERSON> et al., S&P ’18) and Sonic (<PERSON> et al., CCS ’19). To the best of our knowledge, our analysis of Bulletproofs gives thefirstnon-trivial concrete security analysis for a non-constant round argument combined with the Fiat-Shamir transform.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84252-9_3"}, {"primary_key": "2112649", "vector": [], "sparse_vector": [], "title": "ATLAS: Efficient and Scalable MPC in the Honest Majority Setting.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>gon<PERSON> Polychron<PERSON>", "<PERSON><PERSON>"], "summary": "In this work, we address communication, computation, and round efficiency of unconditionally secure multi-party computation for arithmetic circuits in the honest majority setting. We achieve both algorithmic and practical improvements: The best known result in the semi-honest setting has been due to <PERSON>g<PERSON><PERSON> and <PERSON> (CRYPTO 2007). Over the last decade, their construction has played an important role in the progress of efficient secure computation. However despite a number of follow-up works, any significant improvements to the basic semi-honest protocol have been hard to come by. We show\\(33\\%\\)improvement in communication complexity of this protocol. We show how to generalize this result to the malicious setting, leading to the best known unconditional honest majority MPC with malicious security. We focus on the round complexity of the Damgård and Nielsen protocol and improve it by a factor of 2. Our improvement relies on a novel observation relating to an interplay between Damgård and Nielsen multiplication and Beaver triple multiplication. An implementation of our constructions shows an execution run time improvement compared to the state of the art ranging from\\(30\\%\\)to\\(50\\%\\).", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84245-1_9"}, {"primary_key": "2112650", "vector": [], "sparse_vector": [], "title": "Unconditional Communication-Efficient MPC via Hall&apos;s <PERSON> Theorem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>gon<PERSON> Polychron<PERSON>", "<PERSON><PERSON>"], "summary": "The best knownnparty unconditional multiparty computation protocols with an optimal corruption threshold communicatesO(n) field elements per gate. This has been the case even in the semi-honest setting despite over a decade of research on communication complexity in this setting. Going to the slightly sub-optimal corruption setting, the work of <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON> (EUROCRYPT 2010) provided the first protocol for a single circuit achieving communication complexity of\\(O(\\log |C|)\\)elements per gate. While a number of works have improved upon this result, obtaining a protocol withO(1) field elements per gate has been an open problem. In this work, we construct the first unconditional multi-party computation protocol evaluating a single arithmetic circuit with amortized communication complexity ofO(1) elements per gate.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84245-1_10"}, {"primary_key": "2112651", "vector": [], "sparse_vector": [], "title": "Traceable Secret Sharing and Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Consider a scenario where <PERSON> stores some secret datasonnservers using at-out-of-nsecret sharing scheme. <PERSON><PERSON><PERSON> (the collector) is interested in the secret data of <PERSON> and is willing to pay for it. <PERSON><PERSON><PERSON> publishes an advertisement on the internet which describes an elaborate cryptographic scheme to collect the shares from thenservers. Each server who decides to submit its share is paid a hefty monetary reward and is guaranteed “immunity” from being caught or prosecuted in a court for violating its service agreement with <PERSON>. <PERSON> is one of the servers and sees this advertisement. On examining the collection scheme closely, <PERSON> concludes that there is no way for <PERSON> to prove anything in a court that he submitted his share. Indeed, if <PERSON> is rational, he might use the cryptographic scheme in the advertisement and submit his share since there are no penalties and no fear of being caught and prosecuted. Can we design a secret sharing scheme which <PERSON> can use to avoid such a scenario? We introduce a new primitive called asTraceable Secret Sharingto tackle this problem. In particular, a traceable secret sharing scheme guarantees that a cheating server always runs the risk of getting traced and prosecuted by providing a valid evidence (which can be examined in a court of law) implicating its dishonest behavior. We explore various definitional aspects and show how they are highly non-trivial to construct (even ignoring efficiency aspects). We then give an efficient construction of traceable secret sharing assuming the existence of a secure two-party computation protocol. We also show an application of this primitive in constructing traceable protocols for multi-server delegation of computation.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84252-9_24"}, {"primary_key": "2112652", "vector": [], "sparse_vector": [], "title": "KHAPE: Asymmetric PAKE from Key-Hiding Key Exchange.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "OPAQUE [<PERSON><PERSON><PERSON> et al., Eurocrypt 2018] is an asymmetric password authenticated key exchange (aPAKE) protocol that is being developed as an Internet standard and for use within TLS 1.3. OPAQUE combines an Oblivious PRF (OPRF) with an authenticated key exchange to provide strong security properties, including security against pre-computation attacks (called saPAKE security). However, the security of OPAQUE relies crucially on the security of the OPRF. If the latter breaks (by cryptanalysis, quantum attacks or security compromise), the user’s password is exposed to an offline dictionary attack. To address this weakness, we present KHAPE, a variant of OPAQUE that does not require the use of an OPRF to achieve aPAKE security, resulting in improved resilience and near-optimal computational performance. An OPRF can beoptionallyadded to KHAPE, for enhanced saPAKE security, but without opening the password to an offline dictionary attack upon OPRF compromise. In addition to resilience to OPRF compromise, a DH-based implementation of KHAPE (using HMQV) offers the best performance among aPAKE protocols in terms of exponentiations with less than the cost of an exponentiation on top of an UNauthenticated Di<PERSON>ie-Hell<PERSON> exchange. KHAPE uses three messages if the server initiates the exchange or four when the client does (one more than OPAQUE in the latter case). All results in the paper are proven within the UC framework in the ideal cipher model. Of independent interest is our treatment ofkey-hiding AKEwhich KHAPE uses as a main component as well as our UC proofs of AKE security for protocols 3DH (a basis of Signal), HMQV and SKEME, that we use as efficient instantiations of KHAPE.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84259-8_24"}, {"primary_key": "2112653", "vector": [], "sparse_vector": [], "title": "Authenticated Key Exchange and Signatures with Tight Security in the Standard Model.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We construct the first authenticated key exchange protocols that achieve tight security in thestandard model. Previous works either relied on techniques that seem to inherently require a random oracle, or achieved only “Multi-Bit-Guess” security, which is not known to compose tightly, for instance, to build a secure channel. Our constructions are generic, based on digital signatures and key encapsulation mechanisms (KEMs). The main technical challenges we resolve is to determine suitable KEM security notions which on the one hand are strong enough to yield tight security, but at the same time weak enough to be efficiently instantiable in the standard model, based on standard techniques such as universal hash proof systems. Digital signature schemes with tight multi-user security in presence of adaptive corruptions are a central building block, which is used in all known constructions of tightly-secure AKE with full forward security. We identify a subtle gap in the security proof of the only previously known efficient standard model scheme by <PERSON><PERSON><PERSON> al.(TCC 2015). We develop a new variant, which yields the currently most efficient signature scheme that achieves this strong security notion without random oracles and based on standard hardness assumptions.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84259-8_23"}, {"primary_key": "2112654", "vector": [], "sparse_vector": [], "title": "MoSS: Modular Security Specifications Framework.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Applied cryptographic protocols have to meet a rich set of security requirements under diverse environments and against diverse adversaries. However, currently used security specifications, based on either simulation [11,27] (e.g., ‘ideal functionality’ in UC) or games [8,29], aremonolithic, combining together different aspects of protocol requirements, environment and assumptions. Such security specifications are complex, error-prone, and foil reusability, modular analysis and incremental design. We present theModular Security Specifications (MoSS) framework, which cleanly separates thesecurity requirements(goals) which a protocol should achieve, from themodels(assumptions) under which each requirement should be ensured. This modularity allows us to reuse individual models and requirements across different protocols and tasks, and to compare protocols for the same task, either under different assumptions or satisfying different sets of requirements. MoSS is flexible and extendable, e.g., it can support bothasymptoticandconcretedefinitions for security. So far, we confirmed the applicability of MoSS to two applications: secure broadcast protocols and PKI schemes.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84252-9_2"}, {"primary_key": "2112655", "vector": [], "sparse_vector": [], "title": "Counterexamples to New Circular Security Assumptions Underlying iO.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study several strengthening of classical circular security assumptions which were recently introduced in four new lattice-based constructions of indistinguishability obfuscation: Brakerski-<PERSON>ling-Garg-<PERSON>olta (Eurocrypt 2020), Gay-Pass (STOC 2021), Brakerski-Dö<PERSON>ling-Garg-<PERSON>avolta (Eprint 2020) and Wee-<PERSON><PERSON>s (Eprint 2020). We provide explicit counterexamples to the 2-circular shielded randomness leakageassumption w.r.t. the Gentry-Sahai-Waters fully homomorphic encryption scheme proposed by Gay-Pass, and thehomomorphic pseudorandom LWE samplesconjecture proposed by Wee-Wichs. Our work suggests a separation between classical circular security of the kind underlying un-levelled fully-homomorphic encryption from the strengthened versions underlying recent iO constructions, showing that they are not (yet) on the same footing. Our counterexamples exploit the flexibility to choose specific implementations of circuits, which is explicitly allowed in the Gay-Pass assumption and unspecified in the Wee-Wichs assumption. Their indistinguishabilty obfuscation schemes are still unbroken. Our work shows that the assumptions, at least, need refinement. In particular, generic leakage-resilient circular security assumptions are delicate, and their security is sensitive to the specific structure of the leakages involved.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84245-1_23"}, {"primary_key": "2112656", "vector": [], "sparse_vector": [], "title": "On Tight Quantum Security of HMAC and NMAC in the Quantum Random Oracle Model.", "authors": ["<PERSON><PERSON><PERSON>", "Tetsu Iwata"], "summary": "HMAC and NMAC are the most basic and important constructions to convert Merkle-Damgård hash functions into message authentication codes (MACs) or pseudorandom functions (PRFs). In the quantum setting, at CRYPTO 2017, <PERSON> and <PERSON> showed that HMAC and NMAC are quantum pseudorandom functions (qPRFs) under the standard assumption that the underlying compression function is a qPRF. Their proof guarantees security up to\\(O(2^{n/5})\\)or\\(O(2^{n/8})\\)quantum queries when the output length of HMAC and NMAC isnbits. However, there is a gap between the provable security bound and a simple distinguishing attack that uses\\(O(2^{n/3})\\)quantum queries. This paper settles the problem of closing the gap. We show that the tight bound of the number of quantum queries to distinguish HMAC or NMAC from a random function is\\(\\Theta (2^{n/3})\\)in the quantum random oracle model, where compression functions are modeled as quantum random oracles. To give the tight quantum bound, based on an alternative formalization of <PERSON><PERSON><PERSON>’s compressed oracle technique, we introduce a new proof technique focusing on the symmetry of quantum query records.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84242-0_21"}, {"primary_key": "2112657", "vector": [], "sparse_vector": [], "title": "Quantum Collision Attacks on Reduced SHA-256 and SHA-512.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we study dedicated quantum collision attacks on SHA-256 and SHA-512 for the first time. The attacks reach 38 and 39 steps, respectively, which significantly improve the classical attacks for 31 and 27 steps. Both attacks adopt the framework of the previous work that converts many semi-free-start collisions into a 2-block collision, and are faster than the generic attack in the cost metric of time-space tradeoff. We observe that the number of required semi-free-start collisions can be reduced in the quantum setting, which allows us to convert the previous classical 38 and 39 step semi-free-start collisions into a collision. The idea behind our attacks is simple and will also be applicable to other cryptographic hash functions.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84242-0_22"}, {"primary_key": "2112658", "vector": [], "sparse_vector": [], "title": "On the Round Complexity of Black-Box Secure MPC.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We consider the question of minimizing theround complexityof secure multiparty computation (MPC) protocols that make ablack-boxuse of simple cryptographic primitives with security against any number of malicious parties. In the plain model, previous black-box protocols required a high constant number of rounds (>15). This is far from the known lower bound of 4 rounds for protocols with black-box simulators. When allowing random oblivious transfer (OT) correlations, 2-round protocols making black-box use of a pseudorandom generator were known. However, such protocols were obtained via a round-collapsing “protocol garbling” technique that has poor concrete efficiency and makes non-black-box use of an underlying maliciously secure protocol. We improve this state of affairs by presenting the following types of black-box protocols. 4-round “pairwise MPC” in the plain model.This round-optimal protocol enables each ordered pair of parties to compute a function of both inputs whose output is delivered to the second party. The protocol makes black-box use of any public-key encryption (\\(\\mathsf {PKE}\\)) with pseudorandom public keys. As a special case, we get a black-box round-optimal realization of secure (copies of) OT between every ordered pair of parties. 2-round MPC from OT correlations.This round-optimal protocol makes a black-box use of anygeneral2-round MPC protocol satisfying an augmented notion ofsemi-honestsecurity. In the two-party case, this yields new kinds of 2-round black-box protocols. 5-round MPC in the plain model.This protocol makes a black-box use of\\(\\mathsf {PKE}\\)with pseudorandom public keys, and 2-round oblivious transfer with “semi-malicious” security. A key technical tool for the first result is a novel combination of split-state non-malleable codes (Dziembowski, Pietrzak, and Wichs, JACM’18) with standalone securetwo-partyprotocols to constructnon-malleable two-party protocols. The second result is based on a new round-optimized variant of the “IPS compiler” (Ishai, Prabhakaran and Sahai, Crypto’08). The third result is obtained via a specialized combination of these two techniques.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84245-1_8"}, {"primary_key": "2112659", "vector": [], "sparse_vector": [], "title": "Limits on the Adaptive Security of Yao&apos;s Garbling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON>’s garbling scheme is one of the most fundamental cryptographic constructions. <PERSON><PERSON> and <PERSON><PERSON> (Journal of Cryptograhy 2009) gave a formal proof of security in theselectivesetting where the adversary chooses the challenge inputs before seeing the garbled circuit assuming secure symmetric-key encryption (and hence one-way functions). This was followed by results, both positive and negative, concerning its security in the, stronger,adaptivesetting. <PERSON><PERSON> et al. (Crypto 2013) showed that it cannot satisfy adaptive security as is, due to a simple incompressibility argument. <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (TCC 2017) considered a natural adaptation of <PERSON>’s scheme (where the output mapping is sent in theonline phase, together with the garbled input) that circumvents this negative result, and proved that it is adaptively secure, at least for shallow circuits. In particular, they showed that for the class of circuits of depth\\(\\delta \\), the loss in security is at most exponential in\\(\\delta \\). The above results all concern thesimulation-basednotion of security. In this work, we show that the upper bound of <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> is basically optimal in a strong sense. As our main result, we show that there exists a family of Boolean circuits, one for each depth\\(\\delta \\in \\mathbb {N}\\), such thatanyblack-box reduction proving the adaptiveindistinguishabilityof the natural adaptation of <PERSON>’s scheme from any symmetric-key encryption has to lose a factor that is exponential in\\(\\sqrt{\\delta }\\). Since indistinguishability is a weaker notion than simulation, our bound also applies to adaptive simulation. To establish our results, we build on the recent approach of Kamath et al. (Eprint 2021), which uses pebbling lower bounds in conjunction with oracle separations to prove fine-grained lower bounds on loss in cryptographic security.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84245-1_17"}, {"primary_key": "2112660", "vector": [], "sparse_vector": [], "title": "Separating Adaptive Streaming from Oblivious Streaming Using the Bounded Storage Model.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Streaming algorithms are algorithms for processing large data streams, using only a limited amount of memory. Classical streaming algorithms typically work under the assumption that the input stream is chosen independently from the internal state of the algorithm. Algorithms that utilize this assumption are calledobliviousalgorithms. Recently, there is a growing interest in studying streaming algorithms that maintain utility also when the input stream is chosen by anadaptive adversary, possibly as a function of previous estimates given by the streaming algorithm. Such streaming algorithms are said to beadversarially-robust. By combining techniques fromlearning theorywith cryptographic tools from thebounded storage model, we separate the oblivious streaming model from the adversarially-robust streaming model. Specifically, we present a streaming problem for which every adversarially-robust streaming algorithm must use polynomial space, while there exists a classical (oblivious) streaming algorithm that uses only polylogarithmic space. This is the first general separation between the capabilities of these two models, resolving one of the central open questions in adversarial robust streaming.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84252-9_4"}, {"primary_key": "2112661", "vector": [], "sparse_vector": [], "title": "A New Simple Technique to Bootstrap Various Lattice Zero-Knowledge Proofs to QROM Secure NIZKs.", "authors": ["<PERSON><PERSON>"], "summary": "Many of the recent advanced lattice-based\\(\\varSigma \\)-/public-coin honest verifier (HVZK) interactive protocols based on the techniques developed by <PERSON><PERSON><PERSON><PERSON><PERSON> (Asiacrypt’09, Eurocrypt’12) can be transformed into a non-interactive zero-knowledge (NIZK) proof in the random oracle model (ROM) using the <PERSON>-<PERSON><PERSON><PERSON> transform. Unfortunately, although they are known to be secure in theclassicalROM, existing proof techniques are incapable of proving them secure in thequantumROM (QROM). Alternatively, while we could instead rely on the Unruh transform (Eurocrypt’15), the resulting QROM secure NIZK will incur a large overhead compared to the underlying interactive protocol. In this paper, we present a new simple semi-generic transform that compiles many existing lattice-based\\(\\varSigma \\)-/public-coin HVZK interactive protocols into QROM secure NIZKs. Our transform builds on a new primitive calledextractable linear homomorphic commitmentprotocol. The resulting NIZK has several appealing features: it is not only a proof of knowledge but also straight-line extractable; the proof overhead is smaller compared to the Unruh transform; it enjoys a relatively small reduction loss; and it requires minimal background on quantum computation. To illustrate the generality of our technique, we show how to transform the recent <PERSON><PERSON><PERSON> et al.’s 5-round protocol with an exact sound proof (Crypto’19) into a QROM secure NIZK by increasing the proof size by a factor of 2.6. This compares favorably to the Unruh transform that requires a factor of more than 50.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84245-1_20"}, {"primary_key": "2112662", "vector": [], "sparse_vector": [], "title": "Composition with Knowledge Assumptions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Zero-knowledge succinct non-interactive arguments (zk-SNARKs) rely onknowledge assumptionsfor their security. Meanwhile, as the complexity and scale of cryptographic systems continues to grow, the composition of secure protocols is of vital importance. The current gold standards of composable security, the Universal Composability and Constructive Cryptography frameworks cannot capture knowledge assumptions, as their core proofs of composition prohibit white-box extraction. In this paper, we present a formal model allowing the composition of knowledge assumptions. Despite showing impossibility for the general case, we demonstrate the model’s usefulness when limiting knowledge assumptions to few instances of protocols at a time. We finish by providing the first instance of a simultaneously succinct and composable zk-SNARK, by using existing results within our framework.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84259-8_13"}, {"primary_key": "2112663", "vector": [], "sparse_vector": [], "title": "Improved Computational Extractors and Their Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Recent exciting breakthroughs have achieved the first two-source extractors that operate in the low min-entropy regime. Unfortunately, these constructions suffer from non-negligible error, and reducing the error to negligible remains an important open problem. In recent work, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> (GKK, Eurocrypt 2020) investigated a meaningful relaxation of this problem to the computational setting, in the presence of a common random string (CRS). In this relaxed model, their work built explicit two-source extractors for a restricted class of unbalanced sources with min-entropy\\(n^{\\gamma }\\)(for some constant\\(\\gamma \\)) and negligible error, under the sub-exponential DDH assumption. In this work, we investigate whether computational extractors in the CRS model be applied to more challenging environments. Specifically, we study network extractor protocols (<PERSON><PERSON> et al., FOCS 2008) and extractors for adversarial sources (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., STOC 2020) in the CRS model. We observe that these settings require extractors that work well for balanced sources, making the GKK results inapplicable. We remedy this situation by obtaining the following results, all of which are in the CRS model and assume the sub-exponential hardness of DDH. We obtain “optimal” computational two-source and non-malleable extractors for balanced sources: requiring both sources to have only poly-logarithmic min-entropy, and achieving negligible error. To obtain this result, we perform a tighter and arguably simpler analysis of the GKK extractor. We obtain a single-round network extractor protocol for poly-logarithmic min-entropy sources that tolerates an optimal number of adversarial corruptions. Prior work in the information-theoretic setting required sources with high min-entropy rates, and in the computational setting had round complexity that grew with the number of parties, required sources with linear min-entropy, and relied on exponential hardness (albeit without a CRS). We obtain an “optimal”adversarial source extractorfor poly-logarithmic min-entropy sources, where the number of honest sources is only 2 and each corrupted source can depend on either one of the honest sources. Prior work in the information-theoretic setting had to assume a large number of honest sources.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84252-9_19"}, {"primary_key": "2112664", "vector": [], "sparse_vector": [], "title": "Towards Faster Polynomial-Time Lattice Reduction.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Thelllalgorithm is a polynomial-time algorithm for reducingd-dimensional lattice with exponential approximation factor. Currently, the most efficient variant oflll, by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, has a theoretical running time in\\(d^4\\cdot B^{1+o\\left( 1\\right) }\\)whereBis the bitlength of the entries, but has never been implemented. This work introduces new asymptotically fast, parallel, yetheuristic, reduction algorithms with their optimized implementations. Our algorithms are recursive and fully exploit fast matrix multiplication. We experimentally demonstrate that by carefully controlling the floating-point precision during the recursion steps, we can reduce euclidean lattices of rankdin time\\(\\tilde{O}(d^\\omega \\cdot C)\\), i.e., almost a constant number of matrix multiplications, where\\(\\omega \\)is the exponent of matrix multiplication andCis the log of the condition number of the matrix. For cryptographic applications,Cis close toB, while it can be up todtimes larger in the worst case. It improves the running-time of the state-of-the-art implementationfplllby a multiplicative factor of order\\(d^2\\cdot B\\). Further, we show that we can reduce structured lattices, the so-called knapsack lattices, in time\\(\\tilde{O}(d^{\\omega -1}\\cdot C)\\)with a progressive reduction strategy. Besides allowing reducing huge lattices, our implementation can break several instances of Fully Homomorphic Encryption schemes based on large integers in dimension 2,230 with 4 millions of bits.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84245-1_26"}, {"primary_key": "2112665", "vector": [], "sparse_vector": [], "title": "Lower Bounds on La<PERSON>ce Sieving and Information Set Decoding.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In two of the main areas of post-quantum cryptography, based on lattices and codes, nearest neighbor techniques have been used to speed up state-of-the-art cryptanalytic algorithms, and to obtain the lowest asymptotic cost estimates to date [May–<PERSON><PERSON><PERSON>, Eurocrypt’15; <PERSON>–<PERSON>, SODA’16]. These upper bounds are useful for assessing the security of cryptosystems against known attacks, but to guarantee long-term security one would like to have closely matching lower bounds, showing that improvements on the algorithmic side will not drastically reduce the security in the future. As existing lower bounds from the nearest neighbor literature do not apply to the nearest neighbor problems appearing in this context, one might wonder whether further speedups to these cryptanalytic algorithms can still be found by only improving the nearest neighbor subroutines. We derive new lower bounds on the costs of solving the nearest neighbor search problems appearing in these cryptanalytic settings. For the Euclidean metric we show that for random data sets on the sphere, the locality-sensitive filtering approach of [<PERSON>–<PERSON>, SODA 2016] using spherical caps is optimal, and hence within a broad class of lattice sieving algorithms covering almost all approaches to date, their asymptotic time complexity of\\(2^{0.292d + o(d)}\\)is optimal. Similar conditional optimality results apply to lattice sieving variants, such as the\\(2^{0.265d + o(d)}\\)complexity for quantum sieving [<PERSON><PERSON><PERSON><PERSON>, PhD thesis 2016] and previously derived complexity estimates for tuple sieving [<PERSON><PERSON>hanova–<PERSON>arhoven, PKC 2018]. For the Hamming metric we derive new lower bounds for nearest neighbor searching which almost match the best upper bounds from the literature [May–Ozerov, Eurocrypt 2015]. As a consequence we derive conditional lower bounds on decoding attacks, showing that also here one should search for improvements elsewhere to significantly undermine security estimates from the literature.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84245-1_27"}, {"primary_key": "2112666", "vector": [], "sparse_vector": [], "title": "A Logarithmic Lower Bound for Oblivious RAM (for All Parameters).", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "An Oblivious RAM (ORAM), introduced by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (<PERSON><PERSON> 1996), is a (probabilistic) RAM that hides its access pattern, i.e., for every input the observed locations accessed are similarly distributed. In recent years there has been great progress both in terms of upper bounds as well as in terms of lower bounds, essentially pinning down the smallest overhead possible in various settings of parameters. We observe that there is a very natural setting of parameters in whichnonon-trivial lower bound is known, even not ones in restricted models of computation (like the so called balls and bins model). LetNand\\({\\boldsymbol{w}}\\)be the number of cells and bit-size of cells, respectively, in the RAM that we wish to simulate obliviously. Denote by\\({\\boldsymbol{b}}\\)the cell bit-size of the ORAM.Allprevious ORAM lower bounds have a multiplicative\\({\\boldsymbol{w}}/{\\boldsymbol{b}}\\)factor which makes them trivial in many settings of parameters of interest. In this work, we prove a new ORAM lower bound that captures this setting (and in all other settings it is at least as good as previous ones, quantitatively). We show that any ORAM must make (amortized) memory probes for every logical operation. Here,mdenotes the bit-size of the local storage of the ORAM. Our lower bound implies that logarithmic overhead in accesses is necessary, even if\\( {\\boldsymbol{b}}\\gg {\\boldsymbol{w}}\\). Our lower bound is tight forallsettings of parameters, up to the\\(\\log ({\\boldsymbol{b}}/{\\boldsymbol{w}})\\)factor. Our bound also extends to the non-colluding multi-server setting. As an application, we derive the first (unconditional) separation between the overhead needed for ORAMs in theonlinevs.offlinemodels. Specifically, we show that when\\({\\boldsymbol{w}}=\\log N\\)and, there exists an offline ORAM that makes (on average)o(1) memory probes per logical operation while every online one must make\\(\\varOmega (\\log N/\\log \\log N)\\)memory probes per logical operation. No such previous separation was known for any setting of parameters, not even in the balls and bins model.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84259-8_20"}, {"primary_key": "2112667", "vector": [], "sparse_vector": [], "title": "Towards a Unified Approach to Black-Box Constructions of Zero-Knowledge Proofs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "General-purpose zero-knowledge proofs for all\\(\\mathsf {NP} \\)languages greatly simplify secure protocol design. However, they inherently require the code of the underlying relation. If the relation contains black-box calls to a cryptographic function, the code of that function must be known to use the ZK proof, even if both the relation and the proof require only black-box access to the function. <PERSON><PERSON><PERSON><PERSON> (Crypto’12) shows that non-trivial proofs for even simple statements, such as membership in the range of a one-way function, require non-black-box access. We propose an alternative approach to bypass <PERSON><PERSON><PERSON><PERSON>’s impossibility result. Instead of asking for a ZK proof directly for the given one-way functionf, we seek to construct anewone-way functionFgiven only black-box access tof,andan associated ZK protocol for proving non-trivial statements, such as range membership, over its output. We say thatF, along with its proof system, is aproof-basedone-way function. We similarly define proof-based versions of other primitives, specifically pseudo-random generators and collision-resistant hash functions. We show how to construct proof-based versions of each of the primitives mentioned above from their ordinary counterparts under mild but necessary restrictions over the input. More specifically, We first show that if the prover entirely chooses the input, then proof-based pseudo-random generators cannot be constructed from ordinary ones in a black-box manner, thus establishing that some restrictions over the input are necessary. We next present black-box constructions handling inputs of the form (x,r) whereris chosen uniformly by the verifier. This is similar to the restrictions in the widely used Goldreich-Levin theorem. The associated ZK proofs support range membership over the output as well as arbitrary predicates over prefixes of the input. Our results open up the possibility that general-purpose ZK proofs for relations that require black-box access to the primitives above may be possible in the future without violating their black-box nature by instantiating them using proof-based primitives instead of ordinary ones.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84259-8_2"}, {"primary_key": "2112668", "vector": [], "sparse_vector": [], "title": "Cryptanalysis of Full LowMC and LowMC-M with Algebraic Techniques.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we revisit the difference enumeration technique for LowMC and develop new algebraic techniques to achieve efficient key-recovery attacks. In the original difference enumeration attack framework, an inevitable step is to precompute and store a set of intermediate state differences for efficient checking via the binary search. Our first observation is that <PERSON><PERSON><PERSON> et al.’s general algebraic technique developed for SPNs with partial nonlinear layers can be utilized to fulfill the same task, which can make the memory complexity negligible as there is no need to store a huge set of state differences any more. Benefiting from this technique, we could significantly improve the attacks on LowMC when the block size is much larger than the key size and even break LowMC with such a kind of parameter. On the other hand, with our new key-recovery technique, we could significantly improve the time to retrieve the full key if given only a single pair of input and output messages together with the difference trail that they take, which was stated as an interesting question by <PERSON><PERSON><PERSON> et al. at ToSC 2018. Combining both techniques, with only 2 chosen plaintexts, we could break 4 rounds of LowMC adopting a full S-Box layer with block size of 129, 192 and 255 bits, respectively, which are the 3 recommended parameters for Picnic3, an alternative third-round candidate in NIST’s Post-Quantum Cryptography competition. We have to emphasize that our attacks do not indicate that Picnic3 is broken as the Picnic use-case is very different and an attacker cannot even freely choose 2 plaintexts to encrypt for a concrete LowMC instance. However, such parameters are deemed as secure in the latest LowMC. Moreover, much more rounds of seven instances of the backdoor cipher LowMC-M as proposed by <PERSON>eyrin and <PERSON> in CRYPTO 2020 can be broken without finding the backdoor by making full use of the allowed\\(2^{64}\\)data. The above mentioned attacks are all achieved with negligible memory.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84252-9_13"}, {"primary_key": "2112669", "vector": [], "sparse_vector": [], "title": "Differential-Linear Cryptanalysis from an Algebraic Perspective.", "authors": ["Mei<PERSON> Liu", "<PERSON><PERSON><PERSON>", "Dongdai Lin"], "summary": "The differential-linear cryptanalysis is an important cryptanalytic tool in cryptography, and has been extensively researched since its discovery by <PERSON><PERSON> and <PERSON> in 1994. There are nevertheless very few methods to study the middle part where the differential and linear trail connect. In this paper, we study differential-linear cryptanalysis from an algebraic perspective. We first introduce a technique called Differential Algebraic Transitional Form (DATF) for differential-linear cryptanalysis, then develop a new theory of estimation of the differential-linear bias and techniques for key recovery in differential-linear cryptanalysis. The techniques are applied to the CAESAR and LWC finalistAscon, the AES finalistSerpent, and the eSTREAM finalistGrain v1. The bias of the differential-linear approximation is estimated forAsconandSerpent. The theoretical estimates of the bias are more accurate than that obtained by the Differential-Linear Connectivity Table (Bar-Onet al., EUROCRYPT 2019), and the techniques can be applied with more rounds. Our general techniques can also be used to estimate the bias ofGrain v1in differential cryptanalysis, and have a markedly better performance than the Differential Engine tool tailor-made for the cipher. The improved key recovery attacks on round-reduced variants of these ciphers are then proposed. To the best of our knowledge, they are thus far the best known cryptanalysis ofSerpent, as well as the best differential-linear cryptanalysis ofAsconand the best initialization analysis ofGrain v1. The results have been fully verified by experiments. Notably, security analysis ofSerpentis one of the most important applications of differential-linear cryptanalysis in the last two decades. The results in this paper update the differential-linear cryptanalysis ofSerpent-128 andSerpent-256 with one more round after the work of Biham, Dunkelman and Keller in 2003.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84252-9_9"}, {"primary_key": "2112670", "vector": [], "sparse_vector": [], "title": "On the Possibility of Basing Cryptography on EXP≠ BPP.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON> and <PERSON> (FOCS’20) recently demonstrated an equivalence between the existence of one-way functions (OWFs) and mild average-case hardness of the time-bounded Kolmogorov complexity problem. In this work, we establish a similar equivalence but to a different form of time-bounded Kolmogorov Complexity—namely, <PERSON>’s notion of Kolmogorov Complexity—whose hardness is closely related to the problem of whether\\(\\mathsf{EXP}\\ne \\mathsf{BPP}\\). In more detail, letKt(x) denote the Levin-Kolmogorov Complexity of the stringx; that is,\\(Kt(x) = \\min _{{\\varPi }\\in \\{0,1\\}^*, t \\in \\mathbb {N}}\\{|{\\varPi }| + \\lceil \\log t \\rceil : U({\\varPi }, 1^t) = x\\}\\), whereUis a universal Turing machine, and\\(U({\\varPi },1^t)\\)denotes the output of the program\\(\\varPi \\)aftertsteps, and let\\(\\mathsf{MKtP}\\)denote the language of pairs (x,k) having the property that\\(Kt(x) \\le k\\). We demonstrate that: \\(\\mathsf{MKtP}\\notin \\mathsf{Heur}_{\\mathsf{neg}}\\mathsf{BPP}\\)(i.e.,\\(\\mathsf{MKtP}\\)is infinitely-oftentwo-sided errormildly average-case hard) iff infinitely-often OWFs exist. \\(\\mathsf{MKtP}\\notin \\mathsf{Avg}_{\\mathsf{neg}}\\mathsf{BPP}\\)(i.e.,\\(\\mathsf{MKtP}\\)is infinitely-oftenerrorlessmildly average-case hard) iff\\(\\mathsf{EXP}\\ne \\mathsf{BPP}\\). Thus, the only “gap” towards getting (infinitely-often) OWFs from the assumption that\\(\\mathsf{EXP}\\ne \\mathsf{BPP}\\)is the seemingly “minor” technical gap between two-sided error and errorless average-case hardness of the\\(\\mathsf{MKtP}\\)problem. As a corollary of this result, we additionally demonstrate that any reduction from errorless to two-sided error average-case hardness for\\(\\mathsf{MKtP}\\)implies (unconditionally) that\\(\\mathsf{NP}\\ne \\mathsf{P}\\). We finally consider other alternative notions of Kolmogorov complexity—including space-bounded Kolmogorov complexity and conditional Kolmogorov complexity—and show how average-case hardness of problems related to them characterize log-space computable OWFs, or OWFs in\\(\\mathsf{NC}^{0}\\).", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84242-0_2"}, {"primary_key": "2112671", "vector": [], "sparse_vector": [], "title": "The t-wise Independence of Substitution-Permutation Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Block ciphers such as the Advanced Encryption Standard (Rijndael) are used extensively in practice, yet our understanding of their security continues to be highly incomplete. This paper promotes and continues a research program aimed atprovingthe security of block ciphers against important and well-studied classes of attacks. In particular, we initiate the study of (almost)t-wise independence of concrete block-cipher construction paradigms such as substitution-permutation networks and key-alternating ciphers. Sufficiently strong (almost) pairwise independence already suffices to resist (truncated) differential attacks and linear cryptanalysis, and hence this is a relevant and meaningful target. Our results are two-fold. Our first result concerns substitution-permutation networks (SPNs) that model ciphers such as AES. We prove the almost pairwise-independence of an SPN instantiated with concrete S-boxes together with an appropriate linear mixing layer, given sufficiently many rounds and independent sub-keys. Our proof relies on acharacterizationof S-box computation on input differences in terms of sampling output differences from certain subspaces, and a new randomness extraction lemma (which we prove with Fourier-analytic techniques) that establishes when such sampling yields uniformity. We use our techniques in particular to prove almost pairwise-independence for sufficiently many rounds of both the AES block cipher (which uses a variant of the patched inverse function\\(x \\mapsto x^{-1}\\)as theS-box) and the MiMC block cipher (which uses the cubing function\\(x \\mapsto x^3\\)as theS-box), assuming independent sub-keys. Secondly, we show that instantiating a key-alternating cipher (which can be thought of as a degenerate case of SPNs) with most permutations gives us (almost)t-wise independence in\\(t + o(t)\\)rounds. In order to do this, we use the probabilistic method to develop two new lemmas, anindependence-amplification lemmaand adistance amplification lemma, that allow us to reason about the evolution of key-alternating ciphers.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84259-8_16"}, {"primary_key": "2112672", "vector": [], "sparse_vector": [], "title": "Pushing the Limits of Valiant&apos;s Universal Circuits: <PERSON><PERSON>, <PERSON><PERSON>er and More Compact.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Zhenkai Hu"], "summary": "A universal circuit (UC) is a general-purpose circuit that can simulate arbitrary circuits (up to a certain sizen). Valiant provides ak-way recursive construction of UCs (STOC 1976), wherektunes the complexity of the recursion. More concretely, Valiant gives theoretical constructions of 2-way and 4-way UCs of asymptotic (multiplicative) sizes\\(5n\\log n\\)and\\(4.75 n\\log n\\)respectively, which matches the asymptotic lower bound\\(\\varOmega (n\\log n)\\)up to some constant factor. Motivated by various privacy-preserving cryptographic applications, <PERSON> et al. (Eurocrypt 2016) validated the practicality of 2-way universal circuits by giving example implementations for private function evaluation. <PERSON><PERSON><PERSON><PERSON> et al. (Asiacrypt 2017) and <PERSON><PERSON><PERSON> et al. (J. Cryptology 2020) implemented the 2-way/4-way hybrid UCs with various optimizations in place towards making universal circuits more practical. <PERSON> et al. (Asiacrypt 2019) optimized Val<PERSON>’s 4-way UC to asymptotic size\\(4.5 n\\log n\\)and proved a lower bound\\(3.64 n\\log n\\)for UCs under Val<PERSON>’s framework. As the scale of computation goes beyond 10-million-gate (\\(n=10^7\\)) or even billion-gate level (\\(n=10^9\\)), the constant factor in UC’s size plays an increasingly important role in application performance. In this work, we investigate Valiant’s universal circuits and present an improved framework for constructing universal circuits with the following advantages. Simplicity.Parameterization is no longer needed. In contrast to those previous implementations that resorted to a hybrid construction combining\\(k=2\\)and\\(k=4\\)for a tradeoff between fine granularity and asymptotic size-efficiency, our construction gets the best of both worlds when configured at the lowest complexity (i.e.,\\(k=2\\)). Compactness.Our universal circuits have asymptotic size\\(3n\\log n\\), improving upon the best previously known\\(4.5n\\log n\\)by 33% and beating the\\(3.64n\\log n\\)lower bound for UCs constructed under Valiant’s framework (Zhao et al., Asiacrypt 2019). Tightness.We show that under our new framework the UC’s size is lower bounded by\\(2.95 n\\log n\\), which almost matches the\\(3n\\log n\\)circuit size of our 2-way construction. We implement the 2-way universal circuit and evaluate its performance with other implementations, which confirms our theoretical analysis.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84245-1_13"}, {"primary_key": "2112673", "vector": [], "sparse_vector": [], "title": "The Cost to Break SIKE: A Comparative Hardware-Based Analysis with AES and SHA-3.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This work presents a detailed study of the classical security of the post-quantum supersingular isogeny key encapsulation (SIKE) protocol using a realistic budget-based cost model that considers the actual computing and memory costs that are needed for cryptanalysis. In this effort, we design especially-tailored hardware accelerators for the time-critical multiplication and isogeny computations that we use to model an ASIC-powered instance of the van Oorschot-Wiener (vOW) parallel collision search algorithm. We then extend the analysis to AES and SHA-3 in the context of the NIST post-quantum cryptography standardization process to carry out a parameter analysis based on our cost model. This analysis, together with the state-of-the-art quantum security analysis of SIKE, indicates that the current SIKE parameters offer higher practical security than currently believed, closing an open issue on the suitability of the parameters to match NIST’s security levels. In addition, we explore the possibility of using significantly smaller primes to enable more efficient and compact implementations with reduced bandwidth. Our improved cost model and analysis can be applied to other cryptographic settings and primitives, and can have implications for other post-quantum candidates in the NIST process.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84252-9_14"}, {"primary_key": "2112674", "vector": [], "sparse_vector": [], "title": "SMILE: Set Membership from Ideal Lattices with Applications to Ring Signatures and Confidential Transactions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In a set membership proof, the public information consists of a set of elements and a commitment. The prover then produces a zero-knowledge proof showing that the commitment is indeed to some element from the set. This primitive is closely related to concepts like ring signatures and “one-out-of-many” proofs that underlie many anonymity and privacy protocols. The main result of this work is a new succinct lattice-based set membership proof whose size is logarithmic in the size of the set. We also give a transformation of our set membership proof to a ring signature scheme. The ring signature size is also logarithmic in the size of the public key set and has size\\(16\\)KB for a set of\\(2^5\\)elements, and\\(22\\)KB for a set of size\\(2^{25}\\). At an approximately 128-bit security level, these outputs are between 1.5\\(\\times \\)and 7\\(\\times \\)smaller than the current state of the art succinct ring signatures of <PERSON><PERSON><PERSON> et al. (Asiacrypt 2020) and <PERSON><PERSON><PERSON> et al. (CCS 2019). We then show that our ring signature, combined with a few other techniques and optimizations, can be turned into a fairly efficient Monero-like confidential transaction system based on the MatRiCT framework of <PERSON><PERSON><PERSON> et al. (CCS 2019). With our new techniques, we are able to reduce the transaction proof size by factors of about 4X - 10X over the aforementioned work. For example, a transaction with two inputs and two outputs, where each input is hidden among\\(2^{15}\\)other accounts, requires approximately 30KB in our protocol\n.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84245-1_21"}, {"primary_key": "2112675", "vector": [], "sparse_vector": [], "title": "Constructing Locally Leakage-Resilient Linear Secret-Sharing Schemes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Innovative side-channel attacks have repeatedly falsified the assumption that cryptographic implementations are opaque black-boxes. Therefore, it is essential to ensure cryptographic constructions’ security even when information leaks via unforeseen avenues. One such fundamental cryptographic primitive is the secret-sharing schemes, which underlies nearly all threshold cryptography. Our understanding of the leakage-resilience of secret-sharing schemes is still in its preliminary stage. This work studies locally leakage-resilient linear secret-sharing schemes. An adversary can leakmbits of arbitrary local leakage from eachnsecret shares. However, in a locally leakage-resilient secret-sharing scheme, the leakage’s joint distribution reveals no additional information about the secret. For every constantm, we prove that the Massey secret-sharing scheme corresponding to a random linear code of dimensionk(over sufficiently large prime fields) is locally leakage-resilient, where\\(k/n > 1/2\\)is a constant. The previous best construction by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> (CRYPTO–2018) needed\\(k/n > 0.907\\). A technical challenge arises because the number of all possiblem-bit local leakage functions is exponentially larger than the number of random linear codes. Our technical innovation begins with identifying an appropriate pseudorandomness-inspired family of tests; passing them suffices to ensure leakage-resilience. We show that most linear codes pass all tests in this family. This Monte-Carlo construction of linear secret-sharing scheme that is locally leakage-resilient has applications to leakage-resilient secure computation. Furthermore, we highlight a crucial bottleneck for all the analytical approaches in this line of work. Benhamouda et al. introduced an analytical proxy to study the leakage-resilience of secret-sharing schemes; if the proxy is small, then the scheme is leakage-resilient. However, we present a one-bit local leakage function demonstrating that the converse is false, motivating the need for new analytically well-behaved functions that capture leakage-resilience more accurately. Technically, the analysis involves probabilistic and combinatorial techniques and (discrete) Fourier analysis. The family of new “tests” capturing local leakage functions, we believe, is of independent and broader interest.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84252-9_26"}, {"primary_key": "2112676", "vector": [], "sparse_vector": [], "title": "Computational Hardness of Optimal Fair Computation: Beyond Minicrypt.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Secure multi-party computation allows mutually distrusting parties to compute securely over their private data. However, guaranteeing output delivery to honest parties when the adversarial parties may abort the protocol has been a challenging objective. As a representative task, this work considers two-party coin-tossing protocols with guaranteed output delivery, a.k.a., fair coin-tossing. In the information-theoretic plain model, as in two-party zero-sum games, one of the parties can force an output with certainty. In the commitment-hybrid, anyr-message coin-tossing protocol is\\({1/\\sqrt{r}}\\)-unfair, i.e., the adversary can change the honest party’s output distribution by\\(1/\\sqrt{r}\\)in the statistical distance. <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> (TCC–2009) constructed the first 1/r-unfair protocol in the oblivious transfer-hybrid. No further security improvement is possible because <PERSON><PERSON><PERSON> (STOC–1986) proved that 1/r-unfairness is unavoidable. Therefore, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>’s coin-tossing protocol is optimal. However, is oblivious transfer necessary for optimal fair coin-tossing? <PERSON><PERSON> and <PERSON> (CRYPTO–2020) proved that any coin-tossing protocol using one-way functions in a black-box manner is at least\\(1/\\sqrt{r}\\)-unfair. That is, optimal fair coin-tossing is impossible in Minicrypt. Our work focuses on tightly characterizing the hardness of computation assumption necessary and sufficient for optimal fair coin-tossing within Cryptomania, outside Minicrypt. <PERSON>, Makriya<PERSON>ia, <PERSON>ssim, Omri, Shaltiel, and Silbak (FOCS–2018 and TCC–2018) proved that better than\\(1/\\sqrt{r}\\)-unfairness, for any constantr, implies the existence of a key-agreement protocol. We prove that any coin-tossing protocol using public-key encryption (or, multi-round key agreement protocols) in a black-box manner must be\\(1/\\sqrt{r}\\)-unfair. Next, our work entirely characterizes the additional power of secure function evaluation functionalities for optimal fair coin-tossing. We augment the model with an idealized secure function evaluation off, a.k.a., thef-hybrid. Iffis complete, that is, oblivious transfer is possible in thef-hybrid, then optimal fair coin-tossing is also possible in thef-hybrid. On the other hand, iffis not complete, then a coin-tossing protocol using public-key encryption in a black-box manner in thef-hybrid is at least\\(1/\\sqrt{r}\\)-unfair.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84245-1_2"}, {"primary_key": "2112677", "vector": [], "sparse_vector": [], "title": "How to Meet Ternary LWE Keys.", "authors": ["<PERSON>"], "summary": "The LWE problem with its ring variants is today the most prominent candidate for building efficient public key cryptosystems resistant to quantum computers. NTRU-type cryptosystems use an LWE-type variant with small max-norm secrets, usually with ternary coefficients from the set\\(\\{-1,0,1\\}\\). The presumably best attack on these schemes is a hybrid attack that combines lattice reduction techniques with <PERSON><PERSON><PERSON><PERSON>’s Meet-in-the-Middle approach. <PERSON><PERSON><PERSON><PERSON>’s algorithm is a classical combinatorial attack that for key space size\\(\\mathcal{S}\\)runs in time\\(\\mathcal{S}^{0.5}\\). We substantially improve on this Meet-in-the-Middle approach, using the representation technique developed for subset sum algorithms. Asymptotically, our heuristic Meet-in-the-Middle attack runs in time roughly\\(\\mathcal{S}^{0.25}\\), which also beats the\\(\\mathcal{S}^{\\frac{1}{3}}\\)complexity of the best known quantum algorithm. For the round-3 NIST post-quantum encryptions NTRU and NTRU Prime we obtain non-asymptotic instantiations of our attack with complexity roughly\\(\\mathcal{S}^{0.3}\\). As opposed to other combinatorial attacks, our attack benefits from larger LWE field sizesq, as they are often used in modern lattice-based signatures. For example, for BLISS and GLP signatures we obtain non-asymptotic combinatorial attacks around\\(\\mathcal{S}^{0.28}\\). Our attacks do not invalidate the security claims of the aforementioned schemes. However, they establish improved combinatorial upper bounds for their security. We leave it is an open question whether our new Meet-in-the-Middle attack in combination with lattice reduction can be used to speed up the hybrid attack.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84245-1_24"}, {"primary_key": "2112678", "vector": [], "sparse_vector": [], "title": "MuSig2: Simple Two-Round Schnorr Multi-signatures.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Multi-signatures enable a group of signers to produce a joint signature on a joint message. Recently, <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>.(S&P’19) showed that all thus far proposed two-round multi-signature schemes in the pure DL setting (without pairings) are insecure under concurrent signing sessions. While <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>.proposed a secure two-round scheme, this efficiency in terms of rounds comes with the price of having signatures that are more than twice as large as Schnorr signatures, which are becoming popular in cryptographic systems due to their practicality (e.g., they will likely be adopted in Bitcoin). If one needs a multi-signature scheme that can be used as a drop-in replacement for Schnorr signatures, then one is forced to resort either to a three-round scheme or to sequential signing sessions, both of which are undesirable options in practice. In this work, we propose\\(\\mathsf {MuSig2} \\), a simple and highly practical two-round multi-signature scheme. This is the first scheme that simultaneouslyi)is secure under concurrent signing sessions,ii)supports key aggregation,iii)outputs ordinary Schnorr signatures,iv)needs only two communication rounds, andv)has similar signer complexity as ordinary Schnorr signatures. Furthermore, it is the first multi-signature scheme in the pure DL setting that supports preprocessing of all but one rounds, effectively enabling a non-interactive signing process without forgoing security under concurrent sessions. We prove the security of\\(\\mathsf {MuSig2} \\)in the random oracle model, and the security of a more efficient variant in the combination of the random oracle and the algebraic group model. Both our proofs rely on a weaker variant of the OMDL assumption.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84242-0_8"}, {"primary_key": "2112679", "vector": [], "sparse_vector": [], "title": "Three-Round Secure Multiparty Computation from Black-Box Two-Round Oblivious Transfer.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We give constructions of three-round secure multiparty computation (MPC) protocols for general functions that makeblack-boxuse of a two-round oblivious transfer (OT). For the case of semi-honest adversaries, we make use of a two-round, semi-honest secure OT in the plain model. This resolves the round-complexity of black-box (semi-honest) MPC protocols from minimal assumptions and answers an open question of <PERSON> et al. (ITCS 2020). For the case of malicious adversaries, we make use of a two-round maliciously-secure OT in the common random/reference string model that satisfies a (mild) variant of adaptive security for the receiver.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84245-1_7"}, {"primary_key": "2112680", "vector": [], "sparse_vector": [], "title": "Targeted Lossy Functions and Applications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Lossy trapdoor functions, introduced by <PERSON><PERSON><PERSON><PERSON> and <PERSON> (STOC ’08), can be initialized in one of two indistinguishable modes: in injective mode, the function preserves all information about its input, and can be efficiently inverted given a trapdoor, while in lossy mode, the function loses some information about its input. Such functions have found countless applications in cryptography, and can be constructed from a variety of Cryptomania assumptions. In this work, we introducetargeted lossy functions (TLFs), which relax lossy trapdoor functions along two orthogonal dimensions. Firstly, they do not require an inversion trapdoor in injective mode. Secondly, the lossy mode of the function is initialized with some target input, and the function is only required to lose information about this particular target. The injective and lossy modes should be indistinguishable even given the target. We construct TLFs from Minicrypt assumptions, namely, injective pseudorandom generators, or even one-way functions under a natural relaxation of injectivity. We then generalize TLFs to incorporatebranches, and constructall-injective-but-oneandall-lossy-but-onevariants. We show a wide variety of applications of targeted lossy functions. In several cases, we get the first Minicrypt constructions of primitives that were previously only known under Cryptomania assumptions. Our applications include: Pseudo-entropy functionsfrom one-way functions. Deterministic leakage-resilient message-authentication codes and improved leakage-resilient symmetric-key encryption from one-way functions. Extractors forextractor-dependent sourcesfrom one-way functions. Selective-opening secure symmetric-key encryption from one-way functions. A new construction of CCA PKE from (exponentially secure) trapdoor functions and injective pseudorandom generators. We also discuss a fascinating connection to distributed point functions.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84259-8_15"}, {"primary_key": "2112681", "vector": [], "sparse_vector": [], "title": "Improved Torsion-Point Attacks on SIDH Variants.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "SIDH is a post-quantum key exchange algorithm based on the presumed difficulty of finding isogenies between supersingular elliptic curves. However, SIDH and related cryptosystems also reveal additional information: the restriction of a secret isogeny to a subgroup of the curve (torsion-point information). <PERSON> [31] was the first to demonstrate that torsion-point information could noticeably lower the difficulty of finding secret isogenies. In particular, <PERSON> showed that “overstretched” parameterizations of SIDH could be broken in polynomial time. However, this did not impact the security of any cryptosystems proposed in the literature. The contribution of this paper is twofold: First, we strengthen the techniques of [31] by exploiting additional information coming from a dual and a Frobenius isogeny. This extends the impact of torsion-point attacks considerably. In particular, our techniques yield a classical attack that completely breaks then-party group key exchange of [2], first introduced as GSIDH in [17], for 6 parties or more, and a quantum attack for 3 parties or more that improves on the best known asymptotic complexity. We also provide a Magma implementation of our attack for 6 parties. We give the full range of parameters for which our attacks apply. Second, we construct SIDH variants designed to be weak against our attacks; this includes backdoor choices of starting curve, as well as backdoor choices of base-field prime. We stress that our results do not degrade the security of, or reveal any weakness in, the NIST submission SIKE [20].", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84252-9_15"}, {"primary_key": "2112682", "vector": [], "sparse_vector": [], "title": "An Algebraic Framework for Universal and Updatable SNARKs.", "authors": ["<PERSON>", "Arantxa <PERSON>"], "summary": "We introduce Checkable Subspace Sampling Arguments, a new information theoretic interactive proof system in which the prover shows that a vector has been sampled in a subspace according to the verifier’s coins. We show that this primitive provides a unifying view that explains the technical core of most of the constructions of universal and updatable pairing-based (zk)SNARKs. This characterization is extended to a fully algebraic framework for designing such SNARKs in a modular way. We propose new constructions of CSS arguments that lead to SNARKs with different performance trade-offs.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84242-0_27"}, {"primary_key": "2112683", "vector": [], "sparse_vector": [], "title": "Three Halves Make a Whole? Beating the Half-Gates Lower Bound for Garbled Circuits.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We describe a garbling scheme for boolean circuits, in which XOR gates are free and AND gates require communication of\\(1.5\\kappa + 5\\)bits. This improves over the state-of-the-art “half-gates” scheme of <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON> (Eurocrypt 2015), in which XOR gates are free and AND gates cost\\(2\\kappa \\)bits. The half-gates paper proved a lower bound of\\(2\\kappa \\)bits per AND gate, in a model that captured all known garbling techniques at the time. We bypass this lower bound with a novel technique that we callslicing and dicing, which involves slicing wire labels in half and operating separately on those halves. <PERSON><PERSON> is the first to bypass the lower bound while being fully compatible with free-XOR, making it a drop-in replacement for half-gates. Our construction is proven secure from a similar assumption to prior free-XOR garbling (circular correlation-robust hash), and uses only slightly more computation than half-gates.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84242-0_5"}, {"primary_key": "2112684", "vector": [], "sparse_vector": [], "title": "Tighter Security for Schnorr Identification and Signatures: A High-Moment Forking Lemma for ${\\varSigma }$-Protocols.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The Schnorr identification and signature schemes have been amongst the most influential cryptographic protocols of the past three decades. Unfortunately, although the best-known attacks on these two schemes are via discrete-logarithm computation, the known approaches for basing their security on the hardness of the discrete logarithm problem encounter the “square-root barrier”. In particular, in any group of orderpwhere <PERSON><PERSON><PERSON>’s generic hardness result for the discrete logarithm problem is believed to hold (and is thus used for setting concrete security parameters), the best-knownt-time attacks on the Schnorr identification and signature schemes have success probability\\(t^2/p\\), whereas existing proofs of security only rule out attacks with success probabilities\\((t^2/p)^{1/2}\\)and\\((q_{\\mathsf {H}} \\cdot t^2/p)^{1/2}\\), respectively, where\\(q_{\\mathsf {H}}\\)denotes the number of random-oracle queries issued by the attacker. We establish tighter security guarantees for identification and signature schemes which result from\\(\\varSigma \\)-protocols with special soundness based on the hardness of their underlying relation, and in particular for <PERSON><PERSON><PERSON><PERSON>’s schemes based on the hardness of the discrete logarithm problem. We circumvent the square-root barrier by introducing a high-moment generalization of the classic forking lemma, relying on the assumption that the underlying relation is “d-moment hard”: The success probability of any algorithm in the task of producing a witness for a random instance is dominated by thed-th moment of the algorithm’s running time. In the concrete context of the discrete logarithm problem, already Shoup’s original proof shows that the discrete logarithm problem is 2-moment hard in the generic-group model, and thus our assumption can be viewed as a highly-plausible strengthening of the discrete logarithm assumption in any group where no better-than-generic algorithms are currently known. Applying our high-moment forking lemma in this context shows that, assuming the 2-moment hardness of the discrete logarithm problem, anyt-time attacker breaks the security of the Schnorr identification and signature schemes with probabilities at most\\((t^2/p)^{2/3}\\)and\\((q_\\mathsf {H}\\cdot t^2/p)^{2/3}\\), respectively.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84242-0_9"}, {"primary_key": "2112685", "vector": [], "sparse_vector": [], "title": "Large Message Homomorphic Secret Sharing from DCR and Applications.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present the first homomorphic secret sharing (HSS) construction that simultaneously (1) has negligible correctness error, (2) supports integers from an exponentially large range, and (3) relies on an assumption not known to imply FHE—specifically, the Decisional Composite Residuosity (DCR) assumption. This resolves an open question posed by <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> (Crypto 2016). Homomorphic secret sharing is analogous to fully-homomorphic encryption, except the ciphertexts are shared across two non-colluding evaluators. Previous constructions of HSS either had non-negligible correctness error and polynomial-size plaintext space or were based on the stronger LWE assumption. We also present two applications of our technique: a two server ORAM with constant bandwidth overhead, and a rate-1 trapdoor hash function with negligible error rate.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84252-9_23"}, {"primary_key": "2112686", "vector": [], "sparse_vector": [], "title": "Revisiting the Security of DbHtS MACs: Beyond-Birthday-Bound in the Multi-user Setting.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Double-block Hash-then-Sum (DbHtS) MACs are a class of MACs that aim for achieving beyond-birthday-bound security, includingSUM-ECBC,PMAC_Plus,3kf9andLightMAC_Plus. Recently <PERSON><PERSON> et al. (FSE’19), and then <PERSON> et al. (Eurocrypt’20) prove thatDbHtSconstructions are secure beyond the birthday bound in the single-user setting. However, by a generic reduction, their results degrade to (or even worse than) the birthday bound in the multi-user setting. In this work, we revisit the security ofDbHtSMACs in the multi-user setting. We propose a generic framework to prove beyond-birthday-bound security forDbHtSconstructions. We demonstrate the usability of this framework with applications to key-reduced variants ofDbHtSMACs, including2k-SUM-ECBC,2k-PMAC_Plusand2k-LightMAC_Plus. Our results show that the security of these constructions will not degrade as the number of users grows. On the other hand, our results also indicate that these constructions are secure \nbeyond the birthday bound in both single-user and multi-user setting without additional domain separation, which is used in the prior work to simplify the analysis. Moreover, we find a critical flaw in2kf9, which is proved to be secure beyond the birthday bound by <PERSON><PERSON> et al. (FSE’19). We can successfully forge a tag with probability 1 without making any queries. We go further to show attacks with birthday-bound complexity on several variants of2kf9.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84252-9_11"}, {"primary_key": "2112687", "vector": [], "sparse_vector": [], "title": "Puncturable Pseudorandom Sets and Private Information Retrieval with Near-Optimal Online Bandwidth and Time.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Imagine one or more non-colluding servers each holding a large public database, e.g., the repository of DNS entries. Clients would like to access entries in this database without disclosing their queries to the servers. Classical private information retrieval (PIR) schemes achieve polylogarithmic bandwidth per query, but require the server to perform linear computation per query, which is a significant barrier towards deployment. Several recent works showed, however, that by introducing a one-time, per-client, off-line preprocessing phase, anunboundednumber of client queries can be subsequently served with sublinear online computation time per query (and the cost of the preprocessing can be amortized over the unboundedly many queries). Existing preprocessing PIR schemes (supporting unbounded queries), unfortunately, make undesirable tradeoffs to achieve sublinear online computation: they are either significantly non-optimal in online time or bandwidth, or require the servers to store a linear amount of state per client or even per query, or require polylogarithmically many non-colluding servers. We propose a novel 2-server preprocessing PIR scheme that achieves\\(\\widetilde{O}(\\sqrt{n})\\)online computation per query and\\(\\widetilde{O}(\\sqrt{n})\\)client storage, while preserving the polylogarithmic online bandwidth of classical PIR schemes. Both the online bandwidth and computation are optimal up to a poly-logarithmic factor. In our construction, each server stores only the original database and nothing extra, and each online query is served within a single round trip. Our construction relies on the standard LWE assumption. As an important stepping stone, we propose new, more generalized definitions for a cryptographic object called a Privately Puncturable Pseudorandom Set, and give novel constructions that depart significantly from prior approaches.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84259-8_22"}, {"primary_key": "2112688", "vector": [], "sparse_vector": [], "title": "Multi-theorem Designated-Verifier NIZK for QMA.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We present a designated-verifier non-interactive zero-knowledge argument system for QMA with multi-theorem security under the Learning with Errors Assumption. All previous such protocols for QMA are only single-theorem secure. We also relax the setup assumption required in previous works. We prove security in the malicious designated-verifier (MDV-NIZK) model (Quach, <PERSON>lum, and Wichs, EUROCRYPT 2019), where the setup consists of a mutually trusted random string and an untrusted verifier public key. Our main technical contribution is a general compiler that given a NIZK for NP and a quantum sigma protocol for QMA generates an MDV-NIZK protocol for QMA.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84242-0_14"}, {"primary_key": "2112689", "vector": [], "sparse_vector": [], "title": "Efficient Key Recovery for All HFE Signature Variants.", "authors": ["Chengdong Tao", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The HFE cryptosystem is one of the most popular multi- variate schemes. Especially in the area of digital signatures, the HFEv- variant offers short signatures and high performance. Recently, an instance of the HFEv- signature scheme called GeMSS was selected as one of the alternative candidates for signature schemes in the third round of the NIST Post-Quantum Crypto (PQC) Standardization Project. In this paper, we propose a new key recovery attack on the HFEv- signature scheme. Our attack shows that both the Minus and the <PERSON>egar modification do not enhance the security of the basic HFE scheme significantly. This shows that it is very difficult to build a secure and efficient signature scheme on the basis of HFE. In particular, we use our attack to show that the proposed parameters of the GeMSS scheme are not as secure as claimed.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84242-0_4"}, {"primary_key": "2112691", "vector": [], "sparse_vector": [], "title": "Receiver-Anonymity in Rerandomizable RCCA-Secure Cryptosystems Resolved.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this work we resolve the open problem raised by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> at CRYPTO 2007, and present thefirstanonymous, rerandomizable, Replayable-CCA (RCCA) secure public-key encryption scheme. This solution opens the door to numerous privacy-oriented applications with a highly desired RCCA security level. At the core of our construction is a non-trivial extension of smooth projective hash functions (<PERSON><PERSON><PERSON> and <PERSON>, EUROCRYPT 2002), and a modular generic framework developed for constructing rerandomizable RCCA-secure encryption schemes with receiver-anonymity. The framework gives an enhanced abstraction of the original <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>’s scheme (which was the first construction of rerandomizable RCCA-secure encryption in the standard model), where the most crucial enhancement is the first realization of the desirable property of receiver-anonymity, essential to privacy settings. It also serves as a conceptually more intuitive and generic understanding of RCCA security, which leads, for example, to new implementations of the notion. Finally, note that (since CCA security is not applicable to the privacy applications motivating our work) the concrete results and the conceptual advancement presented here, seem to substantially expand the power and relevance of the notion of rerandomizable RCCA-secure encryption.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84259-8_10"}, {"primary_key": "2112692", "vector": [], "sparse_vector": [], "title": "Fine-Grained Secure Attribute-Based Encryption.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Fine-grained cryptography is constructing cryptosystems in a setting where an adversary’s resource is a-prior bounded and an honest party has less resource than an adversary. Currently, only simple form of encryption schemes, such as secret-key and public-key encryption, are constructed in this setting. In this paper, we enrich the available tools in fine-grained cryptography by proposing thefirstfine-grained secure attribute-based encryption (ABE) scheme. Our construction is adaptively secure under the widely accepted worst-case assumption,\\(\\mathsf {NC^1}\\subsetneq \\mathsf{\\oplus L/poly}\\), and it is presented in a generic manner using the notion of predicate encodings (<PERSON><PERSON>, TCC’14). By properly instantiating the underlying encoding, we can obtain different types of ABE schemes, including identity-based encryption. Previously, all of these schemes were unknown in fine-grained cryptography. Our main technical contribution is constructing ABE schemes without using pairing or the <PERSON><PERSON><PERSON>-<PERSON> assumption. Hence, our results show that, even if one-way functions do not exist, we still have ABE schemes with meaningful security. For more application of our techniques, we construct an efficient (quasi-adaptive) non-interactive zero-knowledge (QA-NIZK) proof system.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84259-8_7"}, {"primary_key": "2112693", "vector": [], "sparse_vector": [], "title": "Broadcast Encryption with Size N1/3 and More from k-Lin.", "authors": ["<PERSON><PERSON><PERSON>e"], "summary": "We present the first pairing-based ciphertext-policy attribute-based encryption (CP-ABE) scheme for the class of degree 3 polynomials with compact parameters: the public key, ciphertext and secret keys compriseO(n) group elements, wherenis input length for the function. As an immediate corollary, we obtain a pairing-based broadcast encryption scheme forNusers with\\(O(N^{1/3})\\)-sized parameters, breaking the long-standing\\(\\sqrt{N}\\)barrier for pairing-based broadcast encryption. All of our constructions achieve adaptive security against unbounded collusions, and rely on the (bilateral)k-Lin assumption in prime-order bilinear groups.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84259-8_6"}, {"primary_key": "2112694", "vector": [], "sparse_vector": [], "title": "Smoothing Out Binary Linear Codes and Worst-Case Sub-exponential Hardness for LPN.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Learning parity with noise (LPN) is a notorious (average-case) hard problem that has been well studied in learning theory, coding theory and cryptography since the early 90’s. It further inspires the Learning with Errors (LWE) problem [<PERSON><PERSON>, STOC 2005], which has become one of the central building blocks for post-quantum cryptography and advanced cryptographic primitives. Unlike LWE whose hardness can be reducible from worst-case lattice problems, no corresponding worst-case hardness results were known for LPN until very recently. At Eurocrypt 2019, <PERSON><PERSON><PERSON><PERSON> et al. [BLVW19] established the first feasibility result that the worst-case hardness of nearest codeword problem (NCP) (on balanced linear code) at the extremely low noise rate\\(\\frac{\\log ^2 n}{n}\\)implies the quasi-polynomial hardness of LPN at the high noise rate\\(1/2-1/\\mathsf {poly}(n)\\). It remained open whether a worst-case to average-case reduction can be established for standard (constant-noise) LPN, ideally with sub-exponential hardness. We start with a simple observation that the hardness of high-noise LPN over large fields is implied by that of the LWE of the same modulus, and is thus reducible from worst-case hardness of lattice problems. We then revisit [BLVW19], which is the main focus of this work. We first expand the underlying binary linear codes (of the NCP) to not only the balanced code considered in [BLVW19] but also to another code (with a minimum dual distance). At the core of our reduction is a new variant of smoothing lemma (for both binary codes) that circumvents the barriers (inherent in the underlying worst-case randomness extraction) and admits tradeoffs for a wider spectrum of parameter choices. In addition to similar worst-case hardness result obtained in [BLVW19], we show that for any constant\\(0<c<1\\)the constant-noise LPN problem is (\\(T=2^{\\varOmega (n^{1-c})},\\epsilon =2^{-\\varOmega (n^{\\min (c,1-c)})},q=2^{\\varOmega (n^{\\min (c,1-c)})}\\))-hard assuming that the NCP at the low-noise rate\\(\\tau =n^{-c}\\)is (\\(T'={2^{\\varOmega (\\tau n)}}\\),\\(\\epsilon '={2^{-\\varOmega (\\tau n)}}\\),\\(m={2^{\\varOmega (\\tau n)}}\\))-hard in the worst case, whereT,\\(\\epsilon \\),qandmare time complexity, success rate, sample complexity, and codeword length respectively. Moreover, refuting the worst-case hardness assumption would imply arbitrary polynomial speedups over the current state-of-the-art algorithms for solving the NCP (and LPN), which is a win-win result. Unfortunately, public-key encryptions and collision resistant hash functions need constant-noise LPN with (\\(T={2^{\\omega (\\sqrt{n})}}\\),\\(\\epsilon '={2^{-\\omega (\\sqrt{n})}}\\),\\(q={2^{\\sqrt{n}}}\\))-hardness (Yu et al. CRYPTO 2016 & ASIACRYPT 2019), which is almost (up to an arbitrary\\(\\omega (1)\\)factor in the exponent) what is reducible from the worst-case NCP when\\(c= 0.5\\). We leave it as an open problem whether the gap can be closed or there is a separation in place.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84252-9_16"}, {"primary_key": "2112695", "vector": [], "sparse_vector": [], "title": "DualRing: Generic Construction of Ring Signatures with Efficient Instantiations.", "authors": ["<PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce a novel generic ring signature construction, calledDualRing, which can be built from several canonical identification schemes (such as Schnorr identification).DualRingdiffers from the classical ring signatures by its formation oftworings: a ring of commitments and a ring of challenges. It has a structural difference from the common ring signature approaches based on accumulators or zero-knowledge proofs of the signer index. Comparatively,DualRinghas a number of unique advantages. Considering the DL-based setting by using Schnorr identification scheme, ourDualRingstructure allows the signature size to be compressed into logarithmic size via an argument of knowledge system such as Bulletproofs. We further improve on the Bulletproofs argument system to eliminate about half of the computation while maintaining the same proof size. We call thisSum Argumentand it can be of independent interest. This DL-based construction, named DualRing-EC, using Schnorr identification withSum Argumenthas the shortest ring signature size in the literature without using trusted setup. Considering the lattice-based setting, we instantiateDualRingby a canonical identification based on M-LWE and M-SIS. In practice, we achieve the shortest lattice-based ring signature, named DualRing-LB, when the ring size is between 4 and 2000. DualRing-LB is also 5\\(\\times \\)faster in signing and verification than the fastest lattice-based scheme by <PERSON><PERSON><PERSON> et al. (CRYPTO’19).", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84242-0_10"}, {"primary_key": "2112696", "vector": [], "sparse_vector": [], "title": "White Box Traitor Tracing.", "authors": ["<PERSON>"], "summary": "Traitor tracing aims to identify the source of leaked decryption keys. Since the “traitor” can try to hide their key within obfuscated code in order to evade tracing, the tracing algorithm should work for general, potentially obfuscated, decoderprograms. In the setting of such general decoder programs, prior work usesblack boxtracing: the tracing algorithm ignores the implementation of the decoder, and instead traces just by making queries to the decoder and observing the outputs. We observe that, in some settings, such black box tracing leads to consistency and user privacy issues. On the other hand, these issues do not appear inherent towhite boxtracing, where the tracing algorithm actually inspects the decoder implementation. We therefore develop new white box traitor tracing schemes providing consistency and/or privacy. Our schemes can be instantiated under various assumptions ranging from public key encryption and NIZKs to indistinguishability obfuscation, with different trade-offs. To the best of our knowledge, ours is the first work to consider white box tracing in the general decoder setting.", "published": "2021-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-84259-8_11"}]