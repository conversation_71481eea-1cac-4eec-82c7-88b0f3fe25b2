[{"primary_key": "3119578", "vector": [], "sparse_vector": [], "title": "File systems unfit as distributed storage backends: lessons from 10 years of Ceph evolution.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "For a decade, the Ceph distributed file system followed the conventional wisdom of building its storage backend on top of local file systems. This is a preferred choice for most distributed file systems today because it allows them to benefit from the convenience and maturity of battle-tested code. <PERSON><PERSON>'s experience, however, shows that this comes at a high price. First, developing a zero-overhead transaction mechanism is challenging. Second, metadata performance at the local level can significantly affect performance at the distributed level. Third, supporting emerging storage hardware is painstakingly slow.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359656"}, {"primary_key": "3119579", "vector": [], "sparse_vector": [], "title": "Aegean: replication beyond the client-server model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents Aegean, a new approach that allows fault-tolerant replication to be implemented beyond the confines of the client-server model. In today's computing, where services are rarely standalone, traditional replication protocols such as Primary-Backup, Paxos, and PBFT are not directly applicable, as they were designed for the client-server model. When services interact, these protocols run into a number of problems, affecting both correctness and performance. In this paper, we rethink the design of replication protocols in the presence of interactions between services and introduce new techniques that accommodate such interactions safely and efficiently. Our evaluation shows that a prototype implementation of Aegean not only ensures correctness in the presence of service interactions, but can further improve throughput by an order of magnitude.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359663"}, {"primary_key": "3119580", "vector": [], "sparse_vector": [], "title": "Risk based planning of network changes in evolving data centers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Data center networks evolve as they serve customer traffic. When applying network changes, operators risk impacting customer traffic because the network operates at reduced capacity and is more vulnerable to failures and traffic variations. The impact on customer traffic ultimately translates to operator cost (e.g., refunds to customers). However, planning a network change while minimizing the risks is challenging as we need to adapt to a variety of traffic dynamics and cost functions while scaling to large networks and large changes. Today, operators often use plans that maximize the residual capacity (MRC), which often incurs a high cost under different traffic dynamics. Instead, we propose Janus, which searches the large planning space by leveraging the high degree of symmetry in data center networks. Our evaluation on large Clos networks and Facebook traffic traces shows that <PERSON><PERSON> generates plans in real-time only needing 33~71% of the cost of MRC planners while adapting to a variety of settings.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359664"}, {"primary_key": "3119581", "vector": [], "sparse_vector": [], "title": "Notary: a device for secure transaction approval.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Notary is a new hardware and software architecture for running isolated approval agents in the form factor of a USB stick with a small display and buttons. Approval agents allow factoring out critical security decisions, such as getting the user's approval to sign a Bitcoin transaction or to delete a backup, to a secure environment. The key challenge addressed by Not<PERSON> is to securely switch between agents on the same device. Prior systems either avoid the problem by building single-function devices like a USB U2F key, or they provide weak isolation that is susceptible to kernel bugs, side channels, or Rowhammer-like attacks. Notary achieves strong isolation using reset-based switching, along with the use of physically separate systems-on-a-chip for agent code and for the kernel, and a machine-checked proof of both the hardware's register-transfer-level design and software, showing that reset-based switching leaks no state. Notary also provides a trustworthy I/O path between the agent code and the user, which prevents an adversary from tampering with the user's screen or buttons.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359661"}, {"primary_key": "3119587", "vector": [], "sparse_vector": [], "title": "Verifying concurrent, crash-safe systems with Perennial.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper introduces Perennial, a framework for verifying concurrent, crash-safe systems. <PERSON><PERSON> extends the Iris concurrency framework with three techniques to enable crash-safety reasoning: recovery leases, recovery helping, and versioned memory. To ease development and deployment of applications, <PERSON><PERSON> provides <PERSON>, a subset of Go and a translator from that subset to a model in Perennial with support for reasoning about Go threads, data structures, and file-system primitives. We implemented and verified a crash-safe, concurrent mail server using <PERSON><PERSON> and <PERSON> that achieves speedup on multiple cores. Both <PERSON><PERSON> and <PERSON> use the Coq proof assistant, and the mail server and the framework's proofs are machine checked.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359632"}, {"primary_key": "3119588", "vector": [], "sparse_vector": [], "title": "Taiji: managing global user traffic for large-scale internet services at the edge.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>ng Ha", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present Taiji, a new system for managing user traffic for large-scale Internet services that accomplishes two goals: 1) balancing the utilization of data centers and 2) minimizing network latency of user requests.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359655"}, {"primary_key": "3119589", "vector": [], "sparse_vector": [], "title": "Performance and protection in the ZoFS user-space NVM file system.", "authors": ["Mingkai Dong", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Chen"], "summary": "Non-volatile memory (NVM) can be directly accessed in user space without going through the kernel. This encourages several recent studies on building user-space NVM file systems. However, for the sake of file system protection, none of the existing file systems grant user-space file system libraries with direct control over both metadata and data of the NVM, leaving fast NVM resources underexploited.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359637"}, {"primary_key": "3119590", "vector": [], "sparse_vector": [], "title": "ShortCut: accelerating mostly-deterministic code regions.", "authors": ["Xianzheng Dou", "<PERSON>", "<PERSON>"], "summary": "Applications commonly perform repeated computations that are mostly, but not exactly, similar. If a subsequent computation were identical to the original, the operating system could improve performance via memoization, i.e., capturing the differences in program state caused by the computation and applying the differences in lieu of re-executing the computation. However, opportunities for generic memoization are limited by a myriad of differences that arise during execution, e.g., timestamps differ and communication yields non-deterministic responses. Such difference cause memoization to produce incorrect state.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359659"}, {"primary_key": "3119595", "vector": [], "sparse_vector": [], "title": "TASO: optimizing deep learning computation with automatic generation of graph substitutions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Existing deep neural network (DNN) frameworks optimize the computation graph of a DNN by applying graph transformations manually designed by human experts. This approach misses possible graph optimizations and is difficult to scale, as new DNN operators are introduced on a regular basis.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359630"}, {"primary_key": "3119596", "vector": [], "sparse_vector": [], "title": "SplitFS: reducing software overhead in file systems for persistent memory.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present SplitFS, a file system for persistent memory (PM) that reduces software overhead significantly compared to state-of-the-art PM file systems. SplitFS presents a novel split of responsibilities between a user-space library file system and an existing kernel PM file system. The user-space library file system handles data operations by intercepting POSIX calls, memory-mapping the underlying file, and serving the read and overwrites using processor loads and stores. Metadata operations are handled by the kernel PM file system (ext4 DAX). SplitFS introduces a new primitive termed relink to efficiently support file appends and atomic data operations. SplitFS provides three consistency modes, which different applications can choose from, without interfering with each other. SplitFS reduces software overhead by up-to 4x compared to the NOVA PM file system, and 17x compared to ext4 DAX. On a number of micro-benchmarks and applications such as the LevelDB key-value store running the YCSB benchmark, SplitFS increases application performance by up to 2x compared to ext4 DAX and NOVA while providing similar consistency guarantees.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359631"}, {"primary_key": "3119597", "vector": [], "sparse_vector": [], "title": "Scalable and practical locking with shuffling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Chang<PERSON><PERSON> Min", "<PERSON><PERSON><PERSON>"], "summary": "Locks are an essential building block for high-performance multicore system software. To meet performance goals, lock algorithms have evolved towards specialized solutions for architectural characteristics (e.g., NUMA). However, inpractice, applications run on different server platforms and exhibit widely diverse behaviors that evolve with time (e.g., number of threads, number of locks). This creates performance and scalability problems for locks optimized for a single scenario and platform. For example, popular spinlocks suffer from excessive cache-line bouncing in NUMA systems, while scalable, NUMA-aware locks exhibit sub-par single-thread performance.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359629"}, {"primary_key": "3119598", "vector": [], "sparse_vector": [], "title": "Finding semantic bugs in file systems with an extensible fuzzing framework.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "File systems are too large to be bug free. Although handwritten test suites have been widely used to stress file systems, they can hardly keep up with the rapid increase in file system size and complexity, leading to new bugs being introduced and reported regularly. These bugs come in various flavors: simple buffer overflows to sophisticated semantic bugs. Although bug-specific checkers exist, they generally lack a way to explore file system states thoroughly. More importantly, no turnkey solution exists that unifies the checking effort of various aspects of a file system under one umbrella.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359662"}, {"primary_key": "3119600", "vector": [], "sparse_vector": [], "title": "Parity models: erasure-coded resilience for prediction serving systems.", "authors": ["<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Machine learning models are becoming the primary work-horses for many applications. Services deploy models through prediction serving systems that take in queries and return predictions by performing inference on models. Prediction serving systems are commonly run on many machines in cluster settings, and thus are prone to slowdowns and failures that inflate tail latency. Erasure coding is a popular technique for achieving resource-efficient resilience to data unavailability in storage and communication systems. However, existing approaches for imparting erasure-coded resilience to distributed computation apply only to a severely limited class of functions, precluding their use for many serving workloads, such as neural network inference.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359654"}, {"primary_key": "3119602", "vector": [], "sparse_vector": [], "title": "Yodel: strong metadata security for voice calls.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Yodel is the first system for voice calls that hides metadata (e.g., who is communicating with whom) from a powerful adversary that controls the network and compromises servers. Voice calls require sub-second message latency, but low latency has been difficult to achieve in prior work where processing each message requires an expensive public key operation at each hop in the network. Yodel avoids this expense with the idea of self-healing circuits, reusable paths through a mix network that use only fast symmetric cryptography. Once created, these circuits are resilient to passive and active attacks from global adversaries. Creating and connecting to these circuits without leaking metadata is another challenge that <PERSON><PERSON> addresses with the idea of guarded circuit exchange, where each user creates a backup circuit in case an attacker tampers with their traffic. We evaluate Yodel across the internet and it achieves acceptable voice quality with 990 ms of latency for 5 million simulated users.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359648"}, {"primary_key": "3119603", "vector": [], "sparse_vector": [], "title": "Privacy accounting and quality control in the sage differentially private ML platform.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Companies increasingly expose machine learning (ML) models trained over sensitive user data to untrusted domains, such as end-user devices and wide-access model stores. This creates a need to control the data's leakage through these models. We present Sage, a differentially private (DP) ML platform that bounds the cumulative leakage of training data through models. <PERSON> builds upon the rich literature on DP ML algorithms and contributes pragmatic solutions to two of the most pressing systems challenges of global DP: running out of privacy budget and the privacy-utility tradeoff. To address the former, we develop block composition, a new privacy loss accounting method that leverages the growing database regime of ML workloads to keep training models endlessly on a sensitive data stream while enforcing a global DP guarantee for the stream. To address the latter, we develop privacy-adaptive training, a process that trains a model on growing amounts of data and/or with increasing privacy parameters until, with high probability, the model meets developer-configured quality criteria. <PERSON>'s methods are designed to integrate with TensorFlow-Extended, Google's open-source ML platform. They illustrate how a systems focus on characteristics of ML workloads enables pragmatic solutions that are not apparent when one focuses on individual algorithms, as most DP ML literature does.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359639"}, {"primary_key": "3119605", "vector": [], "sparse_vector": [], "title": "Recipe: converting concurrent DRAM indexes to persistent-memory indexes.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present Recipe, a principled approach for converting concurrent DRAM indexes into crash-consistent indexes for persistent memory (PM). The main insight behind Recipe is that isolation provided by a certain class of concurrent in-memory indexes can be translated with small changes to crash-consistency when the same index is used in PM. We present a set of conditions that enable the identification of this class of DRAM indexes, and the actions to be taken to convert each index to be persistent. Based on these conditions and conversion actions, we modify five different DRAM indexes based on B+ trees, tries, radix trees, and hash tables to their crash-consistent PM counterparts. The effort involved in this conversion is minimal, requiring 30-200 lines of code. We evaluated the converted PM indexes on Intel DC Persistent Memory, and found that they outperform state-of-the-art, hand-crafted PM indexes in multi-threaded workloads by up-to 5.2x. For example, we built P-CLHT, our PM implementation of the CLHT hash table by modifying only 30 LOC. When running YCSB workloads, P-CLHT performs up to 2.4x better than Cacheline-Conscious Extendible Hashing (CCEH), the state-of-the-art PM hash table.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359635"}, {"primary_key": "3119606", "vector": [], "sparse_vector": [], "title": "KVell: the design and implementation of a fast persistent key-value store.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Modern block-addressable NVMe SSDs provide much higher bandwidth and similar performance for random and sequential access. Persistent key-value stores (KVs) designed for earlier storage devices, using either Log-Structured Merge (LSM) or B trees, do not take full advantage of these new devices. Logic to avoid random accesses, expensive operations for keeping data sorted on disk, and synchronization bottlenecks make these KVs CPU-bound on NVMe SSDs.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359628"}, {"primary_key": "3119607", "vector": [], "sparse_vector": [], "title": "Efficient scalable thread-safety-violation detection: finding thousands of concurrency bugs during testing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Concurrency bugs are hard to find, reproduce, and debug. They often escape rigorous in-house testing, but result in large-scale outages in production. Existing concurrency-bug detection techniques unfortunately cannot be part of industry's integrated build and test environment due to some open challenges: how to handle code developed by thousands of engineering teams that uses a wide variety of synchronization mechanisms, how to report little/no false positives, and how to avoid excessive testing resource consumption.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359638"}, {"primary_key": "3119609", "vector": [], "sparse_vector": [], "title": "Teechain: a secure payment network with asynchronous blockchain access.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Blockchains such as Bitcoin and Ethereum execute payment transactions securely, but their performance is limited by the need for global consensus. Payment networks overcome this limitation through off-chain transactions. Instead of writing to the blockchain for each transaction, they only settle the final payment balances with the underlying blockchain. When executing off-chain transactions in current payment networks, parties must access the blockchain within bounded time to detect misbehaving parties that deviate from the protocol. This opens a window for attacks in which a malicious party can steal funds by deliberately delaying other parties' blockchain access and prevents parties from using payment networks when disconnected from the blockchain.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359627"}, {"primary_key": "3119610", "vector": [], "sparse_vector": [], "title": "Fast and secure global payments with Stellar.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "International payments are slow and expensive, in part because of multi-hop payment routing through heterogeneous banking systems. Stellar is a new global payment network that can directly transfer digital money anywhere in the world in seconds. The key innovation is a secure transaction mechanism across untrusted intermediaries, using a new Byzantine agreement protocol called SCP. With SCP, each institution specifies other institutions with which to remain in agreement; through the global interconnectedness of the financial system, the whole network then agrees on atomic transactions spanning arbitrary institutions, with no solvency or exchange-rate risk from intermediary asset issuers or market makers. We present SCP's model, protocol, and formal verification; describe the Stellar payment network; and finally evaluate Stellar empirically through benchmarks and our experience with several years of production use.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359636"}, {"primary_key": "3119611", "vector": [], "sparse_vector": [], "title": "CrashTuner: detecting crash-recovery bugs in cloud systems via meta-info analysis.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Crash-recovery bugs (bugs in crash-recovery-related mechanisms) are among the most severe bugs in cloud systems and can easily cause system failures. It is notoriously difficult to detect crash-recovery bugs since these bugs can only be exposed when nodes crash under special timing conditions. This paper presents CrashTuner, a novel fault-injection testing approach to combat crash-recovery bugs. The novelty of CrashTuner lies in how we identify fault-injection points (crash points) that are likely to expose errors. We observe that if a node crashes while accessing meta-info variables, i.e., variables referencing high-level system state information (e.g., an instance of node or task), it often triggers crash-recovery bugs. Hence, we identify crash points by automatically inferring meta-info variables via a log-based static program analysis. Our approach is automatic and no manual specification is required.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359645"}, {"primary_key": "3119612", "vector": [], "sparse_vector": [], "title": "I4: incremental inference of inductive invariants for verification of distributed protocols.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Designing and implementing distributed systems correctly is a very challenging task. Recently, formal verification has been successfully used to prove the correctness of distributed systems. At the heart of formal verification lies a computer-checked proof with an inductive invariant. Finding this inductive invariant, however, is the most difficult part of the proof. Alas, current proof techniques require inductive invariants to be found manually---and painstakingly---by the developer.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359651"}, {"primary_key": "3119613", "vector": [], "sparse_vector": [], "title": "Snap: a microkernel approach to host networking.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents our design and experience with a microkernel-inspired approach to host networking called Snap. Snap is a userspace networking system that supports Google's rapidly evolving needs with flexible modules that implement a range of network functions, including edge packet switching, virtualization for our cloud platform, traffic shaping policy enforcement, and a high-performance reliable messaging and RDMA-like service. Snap has been running in production for over three years, supporting the extensible communication needs of several large and critical systems.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359657"}, {"primary_key": "3119615", "vector": [], "sparse_vector": [], "title": "AutoMine: harmonizing high-level abstraction and high performance for graph mining.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Graph mining algorithms that aim at identifying structural patterns of graphs are typically more complex than graph computation algorithms such as breadth first search. Researchers have implemented several systems with high-level and flexible interfaces customized for tackling graph mining problems. However, we find that for triangle counting, one of the simplest graph mining problems, such systems can be several times slower than a single-threaded implementation of a straightforward algorithm.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359633"}, {"primary_key": "3119617", "vector": [], "sparse_vector": [], "title": "PipeDream: generalized pipeline parallelism for DNN training.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "DNN training is extremely time-consuming, necessitating efficient multi-accelerator parallelization. Current approaches to parallelizing training primarily use intra-batch parallelization, where a single iteration of training is split over the available workers, but suffer from diminishing returns at higher worker counts. We present PipeDream, a system that adds inter-batch pipelining to intra-batch parallelism to further improve parallel training throughput, helping to better overlap computation with communication and reduce the amount of communication when possible. Unlike traditional pipelining, DNN training is bi-directional, where a forward pass through the computation graph is followed by a backward pass that uses state and intermediate data computed during the forward pass. Naïve pipelining can thus result in mismatches in state versions used in the forward and backward passes, or excessive pipeline flushes and lower hardware efficiency. To address these challenges, PipeDream versions model parameters for numerically correct gradient computations, and schedules forward and backward passes of different minibatches concurrently on different workers with minimal pipeline stalls. PipeDream also automatically partitions DNN layers among workers to balance work and minimize communication. Extensive experimentation with a range of DNN tasks, models, and hardware configurations shows that PipeDream trains models to high accuracy up to 5.3X faster than commonly used intra-batch parallelism techniques.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359646"}, {"primary_key": "3119618", "vector": [], "sparse_vector": [], "title": "Gerenuk: thin computation over big native data using speculative program transformation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Guoqing <PERSON>"], "summary": "Big Data systems are typically implemented in object-oriented languages such as Java and Scala due to the quick development cycle they provide. These systems are executed on top of a managed runtime such as the Java Virtual Machine (JVM), which requires each data item to be represented as an object before it can be processed. This representation is the direct cause of many kinds of severe inefficiencies.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359643"}, {"primary_key": "3119619", "vector": [], "sparse_vector": [], "title": "Scaling symbolic evaluation for automated verification of systems code with <PERSON><PERSON>.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents Serval, a framework for developing automated verifiers for systems software. Serval provides an extensible infrastructure for creating verifiers by lifting interpreters under symbolic evaluation, and a systematic approach to identifying and repairing verification performance bottlenecks using symbolic profiling and optimizations.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359641"}, {"primary_key": "3119620", "vector": [], "sparse_vector": [], "title": "Optimizing data-intensive computations in existing libraries with split annotations.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Data movement between main memory and the CPU is a major bottleneck in parallel data-intensive applications. In response, researchers have proposed using compilers and intermediate representations (IRs) that apply optimizations such as loop fusion under existing high-level APIs such as NumPy and TensorFlow. Even though these techniques generally do not require changes to user applications, they require intrusive changes to the library itself: often, library developers must rewrite each function using a new IR. In this paper, we propose a new technique called split annotations (SAs) that enables key data movement optimizations over unmodified library functions. SAs only require developers to annotate functions and implement an API that specifies how to partition data in the library. The annotation and API describe how to enable cross-function data pipelining and parallelization, while respecting each function's correctness constraints. We implement a parallel runtime for SAs in a system called Mozart. We show that <PERSON> can accelerate workloads in libraries such as Intel MKL and Pandas by up to 15x, with no library modifications. <PERSON> also provides performance gains competitive with solutions that require rewriting libraries, and can sometimes outperform these systems by up to 2x by leveraging existing hand-optimized code.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359652"}, {"primary_key": "3119621", "vector": [], "sparse_vector": [], "title": "A generic communication scheduler for distributed DNN training acceleration.", "authors": ["Yanghua Peng", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present ByteScheduler, a generic communication scheduler for distributed DNN training acceleration. ByteScheduler is based on our principled analysis that partitioning and rearranging the tensor transmissions can result in optimal results in theory and good performance in real-world even with scheduling overhead. To make ByteScheduler work generally for various DNN training frameworks, we introduce a unified abstraction and a Dependency Proxy mechanism to enable communication scheduling without breaking the original dependencies in framework engines. We further introduce a Bayesian Optimization approach to auto-tune tensor partition size and other parameters for different training models under various networking conditions. ByteScheduler now supports TensorFlow, PyTorch, and MXNet without modifying their source code, and works well with both Parameter Server (PS) and all-reduce architectures for gradient synchronization, using either TCP or RDMA. Our experiments show that ByteScheduler accelerates training with all experimented system configurations and DNN models, by up to 196% (or 2.96X of original speed).", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359642"}, {"primary_key": "3119622", "vector": [], "sparse_vector": [], "title": "An analysis of performance evolution of Linux&apos;s core operations.", "authors": ["<PERSON><PERSON> (<PERSON>) <PERSON>", "<PERSON>", "<PERSON><PERSON> Chen", "<PERSON>", "<PERSON>", "<PERSON>g <PERSON>"], "summary": "This paper presents an analysis of how Linux's performance has evolved over the past seven years. Unlike recent works that focus on OS performance in terms of scalability or service of a particular workload, this study goes back to basics: the latency of core kernel operations (e.g., system calls, context switching, etc.). To our surprise, the study shows that the performance of many core operations has worsened or fluctuated significantly over the years. For example, the select system call is 100% slower than it was just two years ago. An in-depth analysis shows that over the past seven years, core kernel subsystems have been forced to accommodate an increasing number of security enhancements and new features. These additions steadily add overhead to core kernel operations but also frequently introduce extreme slowdowns of more than 100%. In addition, simple misconfigurations have also severely impacted kernel performance. Overall, we find most of the slowdowns can be attributed to 11 changes.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359640"}, {"primary_key": "3119624", "vector": [], "sparse_vector": [], "title": "Honeycrisp: large-scale differentially private aggregation without a trusted core.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recently, a number of systems have been deployed that gather sensitive statistics from user devices while giving differential privacy guarantees. One prominent example is the component in Apple's macOS and iOS devices that collects information about emoji usage and new words. However, these systems have been criticized for making unrealistic assumptions, e.g., by creating a very high \"privacy budget\" for answering queries, and by replenishing this budget every day, which results in a high worst-case privacy loss. However, it is not obvious whether such assumptions can be avoided if one requires a strong threat model and wishes to collect data periodically, instead of just once.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359660"}, {"primary_key": "3119625", "vector": [], "sparse_vector": [], "title": "Nexus: a GPU cluster engine for accelerating DNN-based video analysis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Bingyu Kong", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We address the problem of serving Deep Neural Networks (DNNs) efficiently from a cluster of GPUs. In order to realize the promise of very low-cost processing made by accelerators such as GPUs, it is essential to run them at sustained high utilization. Doing so requires cluster-scale resource management that performs detailed scheduling of GPUs, reasoning about groups of DNN invocations that need to be co-scheduled, and moving from the conventional whole-DNN execution model to executing fragments of DNNs. Nexus is a fully implemented system that includes these innovations. In large-scale case studies on 16 GPUs, when required to stay within latency constraints at least 99% of the time, Nexus can process requests at rates 1.8-12.7X higher than state of the art systems can. A long-running multi-application deployment stays within 84% of optimal utilization and, on a 100-GPU cluster, violates latency SLOs on 0.27% of requests.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359658"}, {"primary_key": "3119627", "vector": [], "sparse_vector": [], "title": "Lineage stash: fault tolerance off the critical path.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Ion <PERSON>"], "summary": "As cluster computing frameworks such as Spark, Dryad, Flink, and Ray are being deployed in mission critical applications and on larger and larger clusters, their ability to tolerate failures is growing in importance. These frameworks employ two broad approaches for fault tolerance: checkpointing and lineage. Checkpointing exhibits low overhead during normal operation but high overhead during recovery, while lineage-based solutions make the opposite tradeoff.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359653"}, {"primary_key": "3119628", "vector": [], "sparse_vector": [], "title": "Niijima: sound and automated computation consolidation for efficient multilingual data-parallel pipelines.", "authors": ["Guoqing <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Multilingual data-parallel pipelines, such as Microsoft's Scope and Apache Spark, are widely used in real-world analytical tasks. While the involvement of multiple languages (often including both managed and native languages) provides much convenience in data manipulation and transformation, it comes at a performance cost --- managed languages need a managed runtime, incurring much overhead. In addition, each switch from a managed to a native runtime (and vice versa) requires marshalling or unmarshalling of an ocean of data objects, taking a large fraction of the execution time. This paper presents Niijima, an optimizing compiler for Microsoft's Scope/Cosmos, which can consolidate C#-based user-defined operators (UDOs) across SQL statements, thereby reducing the number of dataflow vertices that require the managed runtime, and thus the amount of C# computations and the data marshalling cost. We demonstrate that Niijima has reduced job latency by an average of 24% and up to 3.3x, on a series of production jobs.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359649"}, {"primary_key": "3119629", "vector": [], "sparse_vector": [], "title": "KnightKing: a fast distributed graph random walk engine.", "authors": ["<PERSON>", "<PERSON><PERSON> Zhang", "<PERSON>", "<PERSON>song Ma", "<PERSON>", "<PERSON>"], "summary": "Random walk on graphs has recently gained immense popularity as a tool for graph data analytics and machine learning. Currently, random walk algorithms are developed as individual implementations and suffer significant performance and scalability problems, especially with the dynamic nature of sophisticated walk strategies.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359634"}, {"primary_key": "3119630", "vector": [], "sparse_vector": [], "title": "Verifying software network functions with no verification expertise.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present the design and implementation of Vigor, a software stack and toolchain for building and running software network middleboxes that are guaranteed to be correct, while preserving competitive performance and developer productivity. Developers write the core of the middlebox---the network function (NF)---in C, on top of a standard packet-processing framework, putting persistent state in data structures from Vigor's library; the Vigor toolchain then automatically verifies that the resulting software stack correctly implements a specification, which is written in Python.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359647"}, {"primary_key": "3119631", "vector": [], "sparse_vector": [], "title": "The inflection point hypothesis: a principled debugging approach for locating the root cause of a failure.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>g <PERSON>"], "summary": "The end goal of failure diagnosis is to locate the root cause. Prior root cause localization approaches almost all rely on statistical analysis. This paper proposes taking a different approach based on the observation that if we model an execution as a totally ordered sequence of instructions, then the root cause can be identified by the first instruction where the failure execution deviates from the non-failure execution that has the longest instruction sequence prefix in common with that of the failure execution. Thus, root cause analysis is transformed into a principled search problem to identify the non-failure execution with the longest common prefix. We present Kairux, a tool that does just that. It is, in most cases, capable of pinpointing the root cause of a failure in a distributed system, in a fully automated way. Kairux uses tests from the system's rich unit test suite as building blocks to construct the non-failure execution that has the longest common prefix with the failure execution in order to locate the root cause. By evaluating Kairux on some of the most complex, real-world failures from HBase, HDFS, and ZooKeeper, we show that Kairux can accurately pinpoint each failure's respective root cause.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359650"}, {"primary_key": "3119632", "vector": [], "sparse_vector": [], "title": "Using concurrent relational logic with helpers for verifying the AtomFS file system.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Chen"], "summary": "Concurrent file systems are pervasive but hard to correctly implement and formally verify due to nondeterministic interleavings. This paper presents AtomFS, the first formally-verified, fine-grained, concurrent file system, which provides linearizable interfaces to applications. The standard way to prove linearizability requires modeling linearization point of each operation---the moment when its effect becomes visible atomically to other threads. We observe that path inter-dependency, where one operation (like rename) breaks the path integrity of other operations, makes the linearization point external and thus poses a significant challenge to prove linearizability.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3341301.3359644"}, {"primary_key": "3140702", "vector": [], "sparse_vector": [], "title": "Proceedings of the 27th ACM Symposium on Operating Systems Principles, SOSP 2019, Huntsville, ON, Canada, October 27-30, 2019.", "authors": ["<PERSON>", "<PERSON>"], "summary": "SOSP is the flagship conference of ACM SIGOPS. Held every two years, it brings together the leading researchers and practitioners interested in the design, implementation, and evaluation of computer systems software.", "published": "2019-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": ""}]