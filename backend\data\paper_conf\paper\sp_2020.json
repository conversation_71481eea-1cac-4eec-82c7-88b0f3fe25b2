[{"primary_key": "2683823", "vector": [], "sparse_vector": [], "title": "CrypTFlow: Secure TensorFlow Inference.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present CrypTFlow, a first of its kind system that converts TensorFlow inference code into Secure Multi-party Computation (MPC) protocols at the push of a button. To do this, we build three components. Our first component, <PERSON><PERSON>, is an end-to-end compiler from TensorFlow to a variety of semihonest MPC protocols. The second component, Porthos, is an improved semi-honest 3-party protocol that provides significant speedups for TensorFlow like applications. Finally, to provide malicious secure MPC protocols, our third component, Aramis, is a novel technique that uses hardware with integrity guarantees to convert any semi-honest MPC protocol into an MPC protocol that provides malicious security. The malicious security of the protocols output by <PERSON><PERSON> relies on integrity of the hardware and semi-honest security of MPC. Moreover, our system matches the inference accuracy of plaintext TensorFlow.We experimentally demonstrate the power of our system by showing the secure inference of real-world neural networks such as ResNet50 and DenseNet121 over the ImageNet dataset with running times of about 30 seconds for semi-honest security and under two minutes for malicious security. Prior work in the area of secure inference has been limited to semi-honest security of small networks over tiny datasets such as MNIST or CIFAR. Even on MNIST/CIFAR, CrypTFlow outperforms prior work.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00092"}, {"primary_key": "2683824", "vector": [], "sparse_vector": [], "title": "Sync HotStuff: Simple and Practical Synchronous State Machine Replication.", "authors": ["<PERSON><PERSON> Abraham", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Synchronous solutions for Byzantine Fault Tolerance (BFT) can tolerate up to minority faults. In this work, we present Sync HotStuff, a surprisingly simple and intuitive synchronous BFT solution that achieves consensus with a latency of 2Δ in the steady state (where Δ is a synchronous message delay upper bound). In addition, Sync HotStuff ensures safety in a weaker synchronous model in which the synchrony assumption does not have to hold for all replicas all the time. Moreover, Sync HotStuff has optimistic responsiveness, i.e., it advances at network speed when less than one-quarter of the replicas are not responding. Borrowing from practical partially synchronous BFT solutions, Sync HotStuff has a two-phase leader-based structure, and has been fully prototyped under the standard synchrony assumption. When tolerating a single fault, Sync HotStuff achieves a throughput of over 280 Kops/sec under typical network performance, which is comparable to the best known partially synchronous solution.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00044"}, {"primary_key": "2683825", "vector": [], "sparse_vector": [], "title": "Partially Observable Games for Secure Autonomy.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Technology development efforts in autonomy and cyber-defense have been evolving independently of each other, over the past decade. In this paper, we report our ongoing effort to integrate these two presently distinct areas into a single framework. To this end, we propose the two-player partially observable stochastic game formalism to capture both high-level autonomous mission planning under uncertainty and adversarial decision making subject to imperfect information. We show that synthesizing sub-optimal strategies for such games is possible under finite-memory assumptions for both the autonomous decision maker and the cyber-adversary. We then describe an experimental testbed to evaluate the efficacy of the proposed framework.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00046"}, {"primary_key": "2683826", "vector": [], "sparse_vector": [], "title": "MarkUs: Drop-in use-after-free prevention for low-level languages.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Use-after-free vulnerabilities have plagued software written in low-level languages, such as C and C++, becoming one of the most frequent classes of exploited software bugs. Attackers identify code paths where data is manually freed by the programmer, but later incorrectly reused, and take advantage by reallocating the data to themselves. They then alter the data behind the program's back, using the erroneous reuse to gain control of the application and, potentially, the system. While a variety of techniques have been developed to deal with these vulnerabilities, they often have unacceptably high performance or memory overheads, especially in the worst case.We have designed MarkUs, a memory allocator that prevents this form of attack at low overhead, sufficient for deployment in real software, even under allocation- and memory-intensive scenarios. We prevent use-after-free attacks by quarantining data freed by the programmer and forbidding its reallocation until we are sure that there are no dangling pointers targeting it. To identify these we traverse live-objects accessible from registers and memory, marking those we encounter, to check whether quarantined data is accessible from any currently allocated location. Unlike garbage collection, which is unsafe in C and C++, MarkUs ensures safety by only freeing data that is both quarantined by the programmer and has no identifiable dangling pointers. The information provided by the programmer's allocations and frees further allows us to optimise the process by freeing physical addresses early for large objects, specialising analysis for small objects, and only performing marking when sufficient data is in quarantine. Using MarkUs, we reduce the overheads of temporal safety in low-level languages to 1.1× on average for SPEC CPU2006, with a maximum slowdown of only 2×, vastly improving upon the state-of-the-art.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00058"}, {"primary_key": "2683827", "vector": [], "sparse_vector": [], "title": "Armor Within: Defending Against Vulnerabilities in Third-Party Libraries.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Vulnerabilities in third-party software modules have resulted in severe security flaws, including remote code execution and denial of service. However, current approaches to securing such libraries suffer from one of two problems. First, they do not perform sufficiently well to be applicable in practice and incur high CPU and memory overheads. Second, they are also harder to apply to legacy and proprietary systems when the source code of the application is not available. There is, therefore, a dire need to secure the internal boundaries within an application to ensure vulnerable software modules are not exploitable via crafted input attacks. We present a novel approach to secure third-party software modules without requiring access to the source code of the program. First, using the foundations of language-theoretic security, we build a validation filter for the vulnerable module. Using the foundations of linking and loading, we present two different ways to insert that filter between the main code and the vulnerable module. Finally, using the foundations of ELF-based access control, we ensure any entry into the vulnerable module must first go through the filter. We evaluate our approaches using three known real-world exploits in two popular libraries-libpng and libxml. We were able to successfully prevent all three exploits from executing.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00063"}, {"primary_key": "2683828", "vector": [], "sparse_vector": [], "title": "Research Report: Building a Wide Reach Corpus for Secure Parser Development.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Computer software that parses electronic files is often vulnerable to maliciously crafted input data. Rather than relying on developers to implement ad hoc defenses against such data, the Language-theoretic security (LangSec) philosophy offers formally correct and verifiable input handling throughout the software development lifecycle. Whether developing from a specification or deriving parsers from samples, LangSec parser developers require wide-reach corpora of their target file format in order to identify key edge cases or common deviations from the format's specification. In this research report, we provide the details of several methods we have used to gather approximately 30 million files, extract features and make these features amenable to search and use in analytics. Additionally, we provide documentation on opportunities and limitations of some popular open-source datasets and annotation tools that will benefit researchers which need to efficiently gather a large file corpus for the purposes of LangSec parser development.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00066"}, {"primary_key": "2683829", "vector": [], "sparse_vector": [], "title": "The Last Mile: High-Assurance and High-Speed Cryptographic Implementations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We develop a new approach for building cryptographic implementations. Our approach goes the last mile and delivers assembly code that is provably functionally correct, protected against side-channels, and as efficient as hand-written assembly. We illustrate our approach using ChaCha20-Poly1305, one of the two ciphersuites recommended in TLS 1.3, and deliver formally verified vectorized implementations which outperform the fastest non-verified code.We realize our approach by combining the Jasmin framework, which offers in a single language features of high-level and low-level programming, and the EasyCrypt proof assistant, which offers a versatile verification infrastructure that supports proofs of functional correctness and equivalence checking. Neither of these tools had been used for functional correctness before. Taken together, these infrastructures empower programmers to develop efficient and verified implementations by \"game hopping\", starting from reference implementations that are proved functionally correct against a specification, and gradually introducing program optimizations that are proved correct by equivalence checking.We also make several contributions of independent interest, including a new and extensible verified compiler for Jasmin, with a richer memory model and support for vectorized instructions, and a new embedding of <PERSON><PERSON><PERSON> in EasyCrypt.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00028"}, {"primary_key": "2683830", "vector": [], "sparse_vector": [], "title": "A Privacy Filter Framework for Internet of Robotic Things Applications.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Traditionally robots have been stand-alone systems. In recent years, however, they have increasingly been connected to external knowledge resources through the Internet of Things (IoT). These robots are thus becoming part of IoT and can realistically allocate Internet of Robotic Things (IoRT) technologies. IoRT can facilitate Human-Robot Interaction (HRI) at functional (commanding and programming) and social levels, as well as a means for remote-interaction. IoRT-HRI can cause privacy issues for humans, in part because robots can collect data using IoT and move in the real world, partly because robots can learn to read human social cues and adapt or correct their behavior accordingly. In this paper, we address the topic of privacy-preserving for IoRT- Hri applications. The objective is to design a data release framework called a Privacy Filter (PF) that can prevent an adversary from private mining information from the released data while keeping utility data. In the experiments, we test our framework on two accessible datasets: MNIST (hand-written digits) and UCI-HAR (activity recognition from motion). Our experimental results on these datasets show that PF is highly effective in removing private information from the dataset while allowing utility data to be mined effectively.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00059"}, {"primary_key": "2683831", "vector": [], "sparse_vector": [], "title": "Influencing Photo Sharing Decisions on Social Media: A Case of Paradoxical Findings.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We investigate the effects of perspective taking, privacy cues, and portrayal of photo subjects (i.e., photo valence) on decisions to share photos of people via social media. In an online experiment we queried 379 participants about 98 photos (that were previously rated for photo valence) in three conditions: (1) Baseline: participants judged their likelihood of sharing each photo; (2) Perspective-taking: participants judged their likelihood of sharing each photo when cued to imagine they are the person in the photo; and (3) Privacy: participants judged their likelihood to share after being cued to consider the privacy of the person in the photo. While participants across conditions indicated a lower likelihood of sharing photos that portrayed people negatively, they - surprisingly - reported a higher likelihood of sharing photos when primed to consider the privacy of the person in the photo. Frequent photo sharers on real-world social media platforms and people without strong personal privacy preferences were especially likely to want to share photos in the experiment, regardless of how the photo portrayed the subject. A follow-up study with 100 participants explaining their responses revealed that the Privacy condition led to a lack of concern with others' privacy. These findings suggest that developing interventions for reducing photo sharing and protecting the privacy of others is a multivariate problem in which seemingly obvious solutions can sometimes go awry.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00006"}, {"primary_key": "2683832", "vector": [], "sparse_vector": [], "title": "Private resource allocators and their applications.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper introduces a new cryptographic primitive called a private resource allocator (PRA) that can be used to allocate resources (e.g., network bandwidth, CPUs) to a set of clients without revealing to the clients whether any other clients received resources. We give several constructions of PRAs that provide guarantees ranging from information-theoretic to differential privacy. PRAs are useful in preventing a new class of attacks that we call allocation-based side-channel attacks. These attacks can be used, for example, to break the privacy guarantees of anonymous messaging systems that were designed specifically to defend against side-channel and traffic analysis attacks. Our implementation of PRAs in Alpenhorn, which is a recent anonymous messaging system, shows that PRAs increase the network resources required to start a conversation by up to 16× (can be made as low as 4× in some cases), but add no overhead once the conversation has been established.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00065"}, {"primary_key": "2683833", "vector": [], "sparse_vector": [], "title": "BIAS: Bluetooth Impersonation AttackS.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON>"], "summary": "Bluetooth (BR/EDR) is a pervasive technology for wireless communication used by billions of devices. The Bluetooth standard includes a legacy authentication procedure and a secure authentication procedure, allowing devices to authenticate to each other using a long term key. Those procedures are used during pairing and secure connection establishment to prevent impersonation attacks. In this paper, we show that the Bluetooth specification contains vulnerabilities enabling to perform impersonation attacks during secure connection establishment. Such vulnerabilities include the lack of mandatory mutual authentication, overly permissive role switching, and an authentication procedure downgrade. We describe each vulnerability in detail, and we exploit them to design, implement, and evaluate master and slave impersonation attacks on both the legacy authentication procedure and the secure authentication procedure. We refer to our attacks as Bluetooth Impersonation AttackS (BIAS).Our attacks are standard compliant, and are therefore effective against any standard compliant Bluetooth device regardless the Bluetooth version, the security mode (e.g., Secure Connections), the device manufacturer, and the implementation details. Our attacks are stealthy because the Bluetooth standard does not require to notify end users about the outcome of an authentication procedure, or the lack of mutual authentication. To confirm that the BIAS attacks are practical, we successfully conduct them against 31 Bluetooth devices (28 unique Bluetooth chips) from major hardware and software vendors, implementing all the major Bluetooth versions, including Apple, Qualcomm, Intel, Cypress, Broadcom, Samsung, and CSR.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00093"}, {"primary_key": "2683834", "vector": [], "sparse_vector": [], "title": "Ijon: Exploring Deep State Spaces via Fuzzing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Although current fuzz testing (fuzzing) methods are highly effective, there are still many situations such as complex state machines where fully automated approaches fail. State-of-the-art fuzzing methods offer very limited ability for a human to interact and aid the fuzzer in such cases. More specifically, most current approaches are limited to adding a dictionary or new seed inputs to guide the fuzzer. When dealing with complex programs, these mechanisms are unable to uncover new parts of the code base.In this paper, we propose Ijon, an annotation mechanism that a human analyst can use to guide the fuzzer. In contrast to the two aforementioned techniques, this approach allows a more systematic exploration of the program's behavior based on the data representing the internal state of the program. As a consequence, using only a small (usually one line) annotation, a user can help the fuzzer to solve previously unsolvable challenges. We extended various AFL-based fuzzers with the ability to annotate the source code of the target application with guidance hints. Our evaluation demonstrates that such simple annotations are able to solve problems that-to the best of our knowledge- no other current fuzzer or symbolic execution based tool can overcome. For example, with our extension, a fuzzer is able to play and solve games such as Super Mario Bros. or resolve more complex patterns such as hash map lookups. To further demonstrate the capabilities of our annotations, we use AFL combined with Ijon to uncover both novel security issues and issues that previously required a custom and comprehensive grammar to be uncovered. Lastly, we show that using Ijon and AFL, one can solve many challenges from the CGC data set that resisted all fully automated and human guided attempts so far.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00117"}, {"primary_key": "2683835", "vector": [], "sparse_vector": [], "title": "Binary Analysis with Architecture and Code Section Detection using Supervised Machine Learning.", "authors": ["<PERSON>", "<PERSON>"], "summary": "When presented with an unknown binary, which may or may not be complete, having the ability to determine information about it is critical to future reverse engineering, particularly in discovering the binary's intended use and potential malicious nature. This paper details techniques to both identify the machine architecture of the binary, as well as to locate the important code segments within the file. This identification of unknown binaries makes use of a technique called byte histogram in addition to various machine learning (ML) techniques, which we call \"What is it Binary\" or WiiBin. Benefits of byte histograms reflect the simplicity of calculation and do not rely on file headers or metadata, allowing for acceptable results when only a small portion of the original file is available; e.g., when encrypted and/or compressed sections are present in a binary. Utilizing WiiBin, we were able to accurately (>80%) determine the architecture of test binaries with as little as a 20% contagious portion of the file present. We were also able to determine the location of code sections within a binary by utilizing the WiiBin framework. Ultimately, the more information that can be gleaned from a binary file, the easier it is to successfully reverse engineer.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00041"}, {"primary_key": "2683836", "vector": [], "sparse_vector": [], "title": "Flaw Label: Exploiting IPv6 Flow Label.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The IPv6 protocol was designed with security in mind. One of the changes that IPv6 has introduced over IPv4 is a new 20-bit flow label field in its protocol header.We show that remote servers can use the flow label field in order to assign a unique ID to each device when communicating with machines running Windows 10 (versions 1703 and higher), and Linux and Android (kernel versions 4.3 and higher). The servers are then able to associate the respective device IDs with subsequent transmissions sent from those machines. This identification is done by exploiting the flow label field generation logic and works across all browsers regardless of network changes. Furthermore, a variant of this attack also works passively, namely without actively triggering traffic from those machines.To design the attack we reverse-engineered and cryptanalyzed the Windows flow label generation code and inspected the Linux kernel flow label generation code. We provide a practical technique to partially extract the key used by each of these algorithms, and observe that this key can identify individual devices across networks, VPNs, browsers and privacy settings. We deployed a demo (for both Windows and Linux/Android) showing that key extraction and machine fingerprinting works in the wild, and tested it from networks around the world.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00075"}, {"primary_key": "2683837", "vector": [], "sparse_vector": [], "title": "Can Voters Detect Malicious Manipulation of Ballot Marking Devices?", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Ballot marking devices (BMDs) allow voters to select candidates on a computer kiosk, which prints a paper ballot that the voter can review before inserting it into a scanner to be tabulated. Unlike paperless voting machines, BMDs provide voters an opportunity to verify an auditable physical record of their choices, and a growing number of U.S. jurisdictions are adopting them for all voters. However, the security of BMDs depends on how reliably voters notice and correct any adversarially induced errors on their printed ballots. In order to measure voters' error detection abilities, we conducted a large study (N = 241) in a realistic polling place setting using real voting machines that we modified to introduce an error into each printout. Without intervention, only 40% of participants reviewed their printed ballots at all, and only 6.6% told a poll worker something was wrong. We also find that carefully designed interventions can improve verification performance. Verbally instructing voters to review the printouts and providing a written slate of candidates for whom to vote both significantly increased review and reporting rates-although the improvements may not be large enough to provide strong security in close elections, especially when BMDs are used by all voters. Based on these findings, we make several evidence-based recommendations to help better defend BMD-based elections.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00118"}, {"primary_key": "2683838", "vector": [], "sparse_vector": [], "title": "Trusted Confidence Bounds for Learning Enabled Cyber-Physical Systems.", "authors": ["<PERSON><PERSON>", "Xenofon D<PERSON>"], "summary": "Cyber-physical systems (CPS) can benefit by the use of learning enabled components (LECs) such as deep neural networks (DNNs) for perception and decision making tasks. However, DNNs are typically non-transparent making reasoning about their predictions very difficult, and hence their application to safety-critical systems is very challenging. LECs could be integrated easier into CPS if their predictions could be complemented with a confidence measure that quantifies how much we trust their output. The paper presents an approach for computing confidence bounds based on Inductive Conformal Prediction (ICP). We train a Triplet Network architecture to learn representations of the input data that can be used to estimate the similarity between test examples and examples in the training data set. Then, these representations are used to estimate the confidence of set predictions from a classifier that is based on the neural network architecture used in the triplet. The approach is evaluated using a robotic navigation benchmark and the results show that we can computed trusted confidence bounds efficiently in real-time.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00053"}, {"primary_key": "2683839", "vector": [], "sparse_vector": [], "title": "ZEXE: Enabling Decentralized Private Computation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Ledger-based systems that support rich applications often suffer from two limitations. First, validating a transaction requires re-executing the state transition that it attests to. Second, transactions not only reveal which application had a state transition but also reveal the application's internal state.We design, implement, and evaluate ZEXE, a ledger-based system where users can execute offline computations and subsequently produce transactions, attesting to the correctness of these computations, that satisfy two main properties. First, transactions hide all information about the offline computations. Second, transactions can be validated in constant time by anyone, regardless of the offline computation.The core of ZEXE is a construction for a new cryptographic primitive that we introduce, decentralized private computation (DPC) schemes. In order to achieve an efficient implementation of our construction, we leverage tools in the area of cryptographic proofs, including succinct zero knowledge proofs and recursive proof composition. Overall, transactions in ZEXE are 968 bytes regardless of the offline computation, and generating them takes less than 1min plus a time that grows with the offline computation.We demonstrate how to use ZEXE to realize privacy-preserving analogues of popular applications: private user-defined assets and private decentralized exchanges for these assets.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00050"}, {"primary_key": "2683840", "vector": [], "sparse_vector": [], "title": "JIT Leaks: Inducing Timing Side Channels through Just-In-Time Compilation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Side-channel vulnerabilities in software are caused by an observable imbalance in resource usage across different program paths. We show that just-in-time (JIT) compilation, which is crucial to the runtime performance of modern interpreted languages, can introduce timing side channels in cases where the input distribution to the program is non-uniform. Such timing channels can enable an attacker to infer potentially sensitive information about predicates on the program input.We define three attack models under which such side channels are harnessable and five vulnerability templates to detect susceptible code fragments and predicates. We also propose profiling algorithms to generate the representative statistical information necessary for the attacker to perform accurate inference.We systematically evaluate the strength of these JIT-based side channels on the java.lang.String, java.lang.Math, and java.math.BigInteger classes from the Java standard library, and on the JavaScript built-in objects String, Math, and Array. We carry out our evaluation using two widely adopted, open-source, JIT-enhanced runtime engines for the Java and JavaScript languages: the Oracle HotSpot Java Virtual Machine and the Google V8 JavaScript engine, respectively.Finally, we demonstrate a few examples of JIT-based side channels in the Apache Shiro security framework and the GraphHopper route planning server, and show that they are observable over the public Internet.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00007"}, {"primary_key": "2683841", "vector": [], "sparse_vector": [], "title": "LVI: Hijacking Transient Execution through Microarchitectural Load Value Injection.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Berk Sunar", "<PERSON>", "<PERSON>"], "summary": "The recent Spectre attack first showed how to inject incorrect branch targets into a victim domain by poisoning microarchitectural branch prediction history. In this paper, we generalize injection-based methodologies to the memory hierarchy by directly injecting incorrect, attacker-controlled values into a victim's transient execution. We propose Load Value Injection (LVI) as an innovative technique to reversely exploit Meltdown-type microarchitectural data leakage. LVI abuses that faulting or assisted loads, executed by a legitimate victim program, may transiently use dummy values or poisoned data from various microarchitectural buffers, before eventually being re-issued by the processor. We show how LVI gadgets allow to expose victim secrets and hijack transient control flow. We practically demonstrate LVI in several proof-of-concept attacks against Intel SGX enclaves, and we discuss implications for traditional user process and kernel isolation. State-of-the-art Meltdown and Spectre defenses, including widespread silicon-level and microcode mitigations, are orthogonal to our novel LVI techniques. LVI drastically widens the spectrum of incorrect transient paths. Fully mitigating our attacks requires serializing the processor pipeline with lfence instructions after possibly every memory load. Additionally and even worse, due to implicit loads, certain instructions have to be blacklisted, including the ubiquitous x86 ret instruction. Intel plans compiler and assembler-based full mitigations that will allow at least SGX enclave programs to remain secure on LVI-vulnerable systems. Depending on the application and optimization strategy, we observe extensive overheads of factor 2 to 19 for prototype implementations of the full mitigation.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00089"}, {"primary_key": "2683842", "vector": [], "sparse_vector": [], "title": "FlyClient: Super-Light Clients for Cryptocurrencies.", "authors": ["Benedikt <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "To validate transactions, cryptocurrencies such as Bitcoin and Ethereum require nodes to verify that a blockchain is valid. This entails downloading and verifying all blocks, taking hours and requiring gigabytes of bandwidth and storage. Hence, clients with limited resources cannot verify transactions independently without trusting full nodes. Bitcoin and Ethereum offer light clients known as simplified payment verification (SPV) clients, that can verify the chain by downloading only the block headers. Unfortunately, the storage and bandwidth requirements of SPV clients still increase linearly with the chain length. For example, as of July 2019, an SPV client in Ethereum needs to download and store about 4 GB of data.Recently, <PERSON><PERSON><PERSON> et al. proposed a solution known as noninteractive proofs of proof-of-work (NIPoPoW) that allows a light client to download and store only a polylogarithmic number of block headers in expectation. Unfortunately, NIPoPoWs are succinct only as long as no adversary influences the honest chain, and can only be used in chains with fixed block difficulty, contrary to most cryptocurrencies which adjust block difficulty frequently according to the network hashrate.We introduce FlyClient, a novel transaction verification light client for chains of variable difficulty. FlyClient is efficient both asymptotically and practically and requires downloading only a logarithmic number of block headers while storing only a single block header between executions. Using an optimal probabilistic block sampling protocol and Merkle Mountain Range (MMR) commitments, FlyClient overcomes the limitations of NIPoPoWs and generates shorter proofs over all measured parameters. In Ethereum, FlyClient achieves a synchronization proof size of less than 500 KB which is roughly 6,600x smaller than SPV proofs. We finally discuss how FlyClient can be deployed with minimal changes to the existing cryptocurrencies via an uncontentious velvet fork.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00049"}, {"primary_key": "2683843", "vector": [], "sparse_vector": [], "title": "Detecting Adversarial Examples in Learning-Enabled Cyber-Physical Systems using Variational Autoencoder for Regression.", "authors": ["Feiyang Cai", "<PERSON><PERSON>", "Xenofon D<PERSON>"], "summary": "Learning-enabled components (LECs) are widely used in cyber-physical systems (CPS) since they can handle the uncertainty and variability of the environment and increase the level of autonomy. However, it has been shown that LECs such as deep neural networks (DNN) are not robust and adversarial examples can cause the model to make a false prediction. The paper considers the problem of efficiently detecting adversarial examples in LECs used for regression in CPS. The proposed approach is based on inductive conformal prediction and uses a regression model based on variational autoencoder. The architecture allows to take into consideration both the input and the neural network prediction for detecting adversarial, and more generally, out-of-distribution examples. We demonstrate the method using an advanced emergency braking system implemented in an open source simulator for self-driving cars where a DNN is used to estimate the distance to an obstacle. The simulation results show that the method can effectively detect adversarial examples with a short detection delay.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00050"}, {"primary_key": "2683844", "vector": [], "sparse_vector": [], "title": "SoK: Understanding the Prevailing Security Vulnerabilities in TrustZone-assisted TEE Systems.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Hundreds of millions of mobile devices worldwide rely on Trusted Execution Environments (TEEs) built with Arm TrustZone for the protection of security-critical applications (e.g., DRM) and operating system (OS) components (e.g., Android keystore). TEEs are often assumed to be highly secure; however, over the past years, TEEs have been successfully attacked multiple times, with highly damaging impact across various platforms. Unfortunately, these attacks have been possible by the presence of security flaws in TEE systems. In this paper, we aim to understand which types of vulnerabilities and limitations affect existing TrustZone-assisted TEE systems, what are the main challenges to build them correctly, and what contributions can be borrowed from the research community to overcome them. To this end, we present a security analysis of popular TrustZone-assisted TEE systems (targeting Cortex-A processors) developed by Qualcomm, Trustonic, Huawei, Nvidia, and Linaro. By studying publicly documented exploits and vulnerabilities as well as by reverse engineering the TEE firmware, we identified several critical vulnerabilities across existing systems which makes it legitimate to raise reasonable concerns about the security of commercial TEE implementations.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00061"}, {"primary_key": "2683845", "vector": [], "sparse_vector": [], "title": "Automated Decision Systems for Cybersecurity and Infrastructure Security.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper describes and discusses the impact of using automated decision systems (ADS), or decision automation, on the spectrum from decision support systems (DSS), where a human makes decisions based on analytics generated by the system, to intelligent decision systems based on analytics performed by Artificial Intelligence (AI) and Machine Learning (ML), and further, to fully autonomous intelligent decision systems, where a machine independently makes decisions based on its AI and ML capabilities. Specifically, we examine the use of decision automation in cybersecurity and infrastructure security and present a methodology for determining which decisions should be automated and at which level of autonomy.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00048"}, {"primary_key": "2683846", "vector": [], "sparse_vector": [], "title": "A Capability for Autonomous IoT System Security: Pushing IoT Assurance to the Edge.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Aviel D<PERSON> Rubin", "<PERSON><PERSON><PERSON>"], "summary": "Complex systems of IoT devices (SIoTD) are systems that have a single purpose but are made up of multiple IoT devices. These systems are becoming ubiquitous, have complex security requirements, and face a diverse and ever-changing array of cyber threats. Issues of privacy and bandwidth will preclude sending all the data from these systems to a central place, and so these systems cannot totally rely on a centralized cloud-based service for their security. The security of these systems must be provided locally and in an autonomous fashion. In this paper, we describe a capability to address this problem, explain specifications for the system, present our work on SIoTD assurance, and show initial results of a novel edge-based application of machine learning to build this capability.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00058"}, {"primary_key": "2683847", "vector": [], "sparse_vector": [], "title": "HopSkipJumpAttack: A Query-Efficient Decision-Based Attack.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The goal of a decision-based adversarial attack on a trained model is to generate adversarial examples based solely on observing output labels returned by the targeted model. We develop HopSkipJumpAttack, a family of algorithms based on a novel estimate of the gradient direction using binary information at the decision boundary. The proposed family includes both untargeted and targeted attacks optimized for ℓ and ℓ ∞ similarity metrics respectively. Theoretical analysis is provided for the proposed algorithms and the gradient direction estimate. Experiments show HopSkipJumpAttack requires significantly fewer model queries than several state-of-the-art decision-based adversarial attacks. It also achieves competitive performance in attacking several widely-used defense mechanisms.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00045"}, {"primary_key": "2683848", "vector": [], "sparse_vector": [], "title": "SAVIOR: Towards Bug-Driven Hybrid Testing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Hybrid testing combines fuzz testing and concolic execution. It leverages fuzz testing to test easy-to-reach code regions and uses concolic execution to explore code blocks guarded by complex branch conditions. As a result, hybrid testing is able to reach deeper into program state space than fuzz testing or concolic execution alone. Recently, hybrid testing has seen significant advancement. However, its code coverage-centric design is inefficient in vulnerability detection. First, it blindly selects seeds for concolic execution and aims to explore new code continuously. However, as statistics show, a large portion of the explored code is often bug-free. Therefore, giving equal attention to every part of the code during hybrid testing is a non-optimal strategy. It slows down the detection of real vulnerabilities by over 43%. Second, classic hybrid testing quickly moves on after reaching a chunk of code, rather than examining the hidden defects inside. It may frequently miss subtle vulnerabilities despite that it has already explored the vulnerable code paths.We propose SAVIOR, a new hybrid testing framework pioneering a bug-driven principle. Unlike the existing hybrid testing tools, SAVIOR prioritizes the concolic execution of the seeds that are likely to uncover more vulnerabilities. Moreover, SAVIOR verifies all vulnerable program locations along the executing program path. By modeling faulty situations using SMT constraints, SAVIOR reasons the feasibility of vulnerabilities and generates concrete test cases as proofs. Our evaluation shows that the bug-driven approach outperforms mainstream automated testing techniques, including state-of-the-art hybrid testing systems driven by code coverage. On average, SAVIOR detects vulnerabilities 43.4% faster than DRILLER and 44.3% faster than QSYM, leading to the discovery of 88 and 76 more unique bugs, respectively. According to the evaluation on 11 well fuzzed benchmark programs, within the first 24 hours, SAVIOR triggers 481 UBSAN violations, among which 243 are real bugs.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00002"}, {"primary_key": "2683849", "vector": [], "sparse_vector": [], "title": "ELF Analyzer Demo: Online Identification for IoT Malwares with Multiple Hardware Architectures.", "authors": ["<PERSON><PERSON><PERSON>", "Tao Ban", "Jr-<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This demonstration presents an automatic IoT runtime platform with a web interface, ELF Analyzer, where suspicious ELF files uploaded by users could be executed and dynamically analyzed for malicious behavior identification. The key component of our platform is a crafted IoT sandbox, where multiple hardware architectures are emulated using QEMU. With the introduction of strace functionality, we demonstrate that system call and traffic logs of an uploaded ELF file with different hardware architectures can be generated successfully. After proper analysis, malicious ELF files can be identified.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00036"}, {"primary_key": "2683850", "vector": [], "sparse_vector": [], "title": "Gesture Authentication for Smartphones: Evaluation of Gesture Password Selection Policies.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Touchscreen gestures are attracting research attention as an authentication method. While studies have showcased their usability, it has proven more complex to determine, let alone enhance, their security. Problems stem both from the small scale of current data sets and the fact that gestures are matched imprecisely - by a distance metric. This makes it challenging to assess entropy with traditional algorithms. To address these problems, we captured a large set of gesture passwords (N=2594) from crowd workers, and developed a security assessment framework that can calculate partial guessing entropy estimates, and generate dictionaries that crack 23.13% or more gestures in online attacks (within 20 guesses). To improve the entropy of gesture passwords, we designed novel blacklist and lexical policies to, respectively, restrict and inspire gesture creation. We close by validating both our security assessment framework and policies in a new crowd-sourced study (N=4000). Our blacklists increase entropy and resistance to dictionary based guessing attacks.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00034"}, {"primary_key": "2683851", "vector": [], "sparse_vector": [], "title": "SentiNet: Detecting Localized Universal Attacks Against Deep Learning Systems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "SentiNet is a novel detection framework for localized universal attacks on neural networks. These attacks restrict adversarial noise to contiguous portions of an image and are reusable with different images-constraints that prove useful for generating physically-realizable attacks. Unlike most other works on adversarial detection, SentiNet does not require training a model or preknowledge of an attack prior to detection. Our approach is appealing due to the large number of possible mechanisms and attack-vectors that an attack-specific defense would have to consider. By leveraging the neural network's susceptibility to attacks and by using techniques from model interpretability and object detection as detection mechanisms, SentiNet turns a weakness of a model into a strength. We demonstrate the effectiveness of SentiNet on three different attacks-i.e., data poisoning attacks, trojaned networks, and adversarial patches (including physically realizable attacks)-and show that our defense is able to achieve very competitive performance metrics for all three threats. Finally, we show that SentiNet is robust against strong adaptive adversaries, who build adversarial patches that specifically target the components of SentiNet's architecture.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00025"}, {"primary_key": "2683852", "vector": [], "sparse_vector": [], "title": "On Using Camera-based Visible Light Communication for Security Protocols.", "authors": ["<PERSON><PERSON><PERSON>", "Ting<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In security protocol design, Visible Light Communication (VLC) has often been abstracted as an ideal channel that is resilient to eavesdropping, manipulation, and jamming. Camera Communication (CamCom), a subcategory of VLC, further strengthens the level of security by providing a visually verifiable association between the transmitter and the extracted information. However, the ideal security guarantees of visible light channels may not hold in practice due to limitations and tradeoffs introduced by hardware, software, configuration, environment, etc. This paper presents our experience and lessons learned from implementing CamCom for security protocols. We highlight CamCom's security-enhancing properties and security applications that it enables. Backed by real implementation and experiments, we also systematize the practical considerations of CamCom-based security protocols.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00034"}, {"primary_key": "2683853", "vector": [], "sparse_vector": [], "title": "Pseudorandom Black Swans: Cache Attacks on CTR_DRBG.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Modern cryptography requires the ability to securely generate pseudorandom numbers. However, despite decades of work on side-channel attacks, there is little discussion of their application to pseudorandom number generators (PRGs). In this work we set out to address this gap, empirically evaluating the side-channel resistance of common PRG implementations.We find that hard-learned lessons about side-channel leakage from encryption primitives have not been applied to PRGs, at all abstraction levels. At the design level, the NIST-recommended CTR_DRBG does not have forward security if an attacker is able to compromise the state (e.g., via a side-channel). At the primitive level, popular implementations of CTR_DRBG such as OpenSSL's FIPS module and NetBSD's kernel use leaky T-table AES as their underlying cipher, enabling cache side-channel attacks. Finally, we find that many implementations make parameter choices that enable an attacker to fully exploit side-channels and recover secret keys from TLS connections.We empirically demonstrate our attack in two scenarios. First, we carry out a cache attack that recovers the private state from vulnerable CTR_DRBG implementations when the TLS client connects to an attacker-controlled server. We then subsequently use the recovered state to compute the client's long-term authentication keys, thereby allowing the attacker to impersonate the client. In the second scenario, we show that an attacker can exploit the high temporal resolution provided by Intel SGX to carry out a blind attack to recover CTR_DRBG's state within three AES encryptions, without viewing output, and thus decrypt passively collected TLS connections from the victim.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00046"}, {"primary_key": "2683854", "vector": [], "sparse_vector": [], "title": "Are We Susceptible to Rowhammer? An End-to-End Methodology for Cloud Providers.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Cloud providers are concerned that Rowhammer poses a potentially critical threat to their servers, yet today they lack a systematic way to test whether the DRAM used in their servers is vulnerable to Rowhammer attacks. This paper presents an endto-end methodology to determine if cloud servers are susceptible to these attacks. With our methodology, a cloud provider can construct worst-case testing conditions for DRAM.We apply our methodology to three classes of servers from a major cloud provider. Our findings show that none of the CPU instruction sequences used in prior work to mount Rowhammer attacks create worst-case DRAM testing conditions. To address this limitation, we develop an instruction sequence that leverages microarchitectural side-effects to \"hammer\" DRAM at a near-optimal rate on modern Intel Skylake and Cascade Lake platforms. We also design a DDR4 fault injector that can reverse engineer row adjacency for any DDR4 DIMM. When applied to our cloud provider's DIMMs, we find that DRAM rows do not always follow a linear map.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00085"}, {"primary_key": "2683855", "vector": [], "sparse_vector": [], "title": "Even Black Cats Cannot Stay Hidden in the Dark: Full-band De-anonymization of Bluetooth Classic Devices.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Bluetooth Classic (BT) remains the de facto connectivity technology in car stereo systems, wireless headsets, laptops, and a plethora of wearables, especially for applications that require high data rates, such as audio streaming, voice calling, tethering, etc. Unlike in Bluetooth Low Energy (BLE), where address randomization is a feature available to manufactures, BT addresses are not randomized because they are largely believed to be immune to tracking attacks. We analyze the design of BT and devise a robust de-anonymization technique that hinges on the apparently benign information leaking from frame encoding, to infer a piconet's clock, hopping sequence, and ultimately the Upper Address Part (UAP) of the master device's physical address, which are never exchanged in clear. Used together with the Lower Address Part (LAP), which is present in all frames transmitted, this enables tracking of the piconet master, thereby debunking the privacy guarantees of BT. We validate this attack by developing the first Software-defined Radio (SDR) based sniffer that allows full BT spectrum analysis (79 MHz) and implements the proposed de-anonymization technique. We study the feasibility of privacy attacks with multiple testbeds, considering different numbers of devices, traffic regimes, and communication ranges. We demonstrate that it is possible to track BT devices up to 85 meters from the sniffer, and achieve more than 80% device identification accuracy within less than 1 second of sniffing and 100% detection within less than 4 seconds. Lastly, we study the identified privacy attack in the wild, capturing BT traffic at a road junction over 5 days, demonstrating that our system can re-identify hundreds of users and infer their commuting patterns.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00091"}, {"primary_key": "2683856", "vector": [], "sparse_vector": [], "title": "Research Report: ICARUS: Understanding De Facto Formats by Way of Feathers and Wax.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "When a data format achieves a significant level of adoption, the presence of multiple format implementations expands the original specification in often-unforeseen ways. This results in an implicitly defined, de facto format, which can create vulnerabilities in programs handling the associated data files. In this paper we present our initial work on ICARUS: a toolchain for dealing with the problem of understanding and hardening de facto file formats. We show the results of our work in progress in the following areas: labeling and categorizing a corpora of data format samples to understand accepted variations of a format; the detection of sublanguages within the de facto format using both entropy- and taint-tracking-based methods, as a means of breaking down the larger problem of learning how the grammar has evolved; grammar inference via reinforcement learning, as a means of tying together the learned sublanguages; and the defining of both safe subsets of the de facto grammar, as well as translations from unsafe regions of the de facto grammar into safe regions. Real-world data formats evolve as they find use in real-world applications, and a comprehensive ICARUS toolchain for understanding and hardening the resulting de facto formats can identify and address security risks arising from this evolution.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00067"}, {"primary_key": "2683857", "vector": [], "sparse_vector": [], "title": "Flash Boys 2.0: Frontrunning in Decentralized Exchanges, Miner Extractable Value, and Consensus Instability.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Blockchains, and specifically smart contracts, have promised to create fair and transparent trading ecosystems.Unfortunately, we show that this promise has not been met. We document and quantify the widespread and rising deployment of arbitrage bots in blockchain systems, specifically in decentralized exchanges (or \"DEXes\"). Like high-frequency traders on Wall Street, these bots exploit inefficiencies in DEXes, paying high transaction fees and optimizing network latency to frontrun, i.e., anticipate and exploit, ordinary users' DEX trades.We study the breadth of DEX arbitrage bots in a subset of transactions that yield quantifiable revenue to these bots. We also study bots' profit-making strategies, with a focus on blockchain-specific elements. We observe bots engage in what we call priority gas auctions (PGAs), competitively bidding up transaction fees in order to obtain priority ordering, i.e., early block position and execution, for their transactions. PGAs present an interesting and complex new continuous-time, partial-information, game-theoretic model that we formalize and study. We release an interactive web portal, frontrun.me, to provide the community with real-time data on PGAs. We additionally show that high fees paid for priority transaction ordering poses a systemic risk to consensus-layer security. We explain that such fees are just one form of a general phenomenon in DEXes and beyond-what we call miner extractable value (MEV)-that poses concrete, measurable, consensus-layer security risks. We show empirically that MEV poses a realistic threat to Ethereum today. Our work highlights the large, complex risks created by transaction-ordering dependencies in smart contracts and the ways in which traditional forms of financial-market exploitation are adapting to and penetrating blockchain economies.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00040"}, {"primary_key": "2683858", "vector": [], "sparse_vector": [], "title": "SoK: Cyber Insurance - Technical Challenges and a System Security Roadmap.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cyber attacks have increased in number and complexity in recent years, and companies and organizations have accordingly raised their investments in more robust infrastructure to preserve their data, assets and reputation. However, the full protection against these countless and constantly evolving threats is unattainable by the sole use of preventive measures. Therefore, to handle residual risks and contain business losses in case of an incident, firms are increasingly adopting a cyber insurance as part of their corporate risk management strategy.As a result, the cyber insurance sector - which offers to transfer the financial risks related to network and computer incidents to a third party - is rapidly growing, with recent claims that already reached a $100M dollars. However, while other insurance sectors rely on consolidated methodologies to accurately predict risks, the many peculiarities of the cyber domain resulted in carriers to often resort to qualitative approaches based on experts opinions.This paper looks at past research conducted in the area of cyber insurance and classifies previous studies in four different areas, focused respectively on studying the economical aspects, the mathematical models, the risk management methodologies, and the predictions of cyber events. We then identify, for each insurance phase, a group of practical research problems where security experts can help develop new data-driven methodologies and automated tools to replace the existing qualitative approaches.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00019"}, {"primary_key": "2683859", "vector": [], "sparse_vector": [], "title": "Binsec/Rel: Efficient Relational Symbolic Execution for Constant-Time at Binary-Level.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The constant-time programming discipline (CT) is an efficient countermeasure against timing side-channel attacks, requiring the control flow and the memory accesses to be independent from the secrets. Yet, writing CT code is challenging as it demands to reason about pairs of execution traces (2-hypersafety property) and it is generally not preserved by the compiler, requiring binary-level analysis. Unfortunately, current verification tools for CT either reason at higher level (C or LLVM), or sacrifice bug-finding or bounded-verification, or do not scale. We tackle the problem of designing an efficient binary-level verification tool for CT providing both bug-finding and bounded-verification. The technique builds on relational symbolic execution enhanced with new optimizations dedicated to information flow and binary-level analysis, yielding a dramatic improvement over prior work based on symbolic execution. We implement a prototype, BINSEC/REL, and perform extensive experiments on a set of 338 cryptographic implementations, demonstrating the benefits of our approach in both bug-finding and bounded-verification. Using BINSEC/REL, we also automate a previous manual study of CT preservation by compilers. Interestingly, we discovered that gcc -O0 and backend passes of clang introduce violations of CT in implementations that were previously deemed secure by a state-of-the-art CT verification tool operating at LLVM level, showing the importance of reasoning at binary-level.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00074"}, {"primary_key": "2683860", "vector": [], "sparse_vector": [], "title": "Electromagnetic Sensor and Actuator Attacks on Power Converters for Electric Vehicles.", "authors": ["Gökçen Yilma<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Alleviating range anxiety for electric vehicles (i.e., whether such vehicles can be relied upon to travel long distances in a timely manner) is critical for sustainable transportation. Extremely fast charging (XFC), whereby electric vehicles (EV) can be quickly recharged in the time frame it takes to refuel an internal combustion engine, has been proposed to alleviate this concern. A critical component of these chargers is the efficient and proper operation of power converters that convert AC to DC power and otherwise regulate power delivery to vehicles. These converters rely on the integrity of sensor and actuation signals. In this work the operation of state-of-the art XFC converters is assessed in adversarial conditions, specifically against Intentional Electromagnetic Interference Attacks (IEMI). The targeted system is analyzed with the goal of determining possible weak points for IEMI, viz. voltage and current sensor outputs and gate control signals. This work demonstrates that, with relatively low power levels, an adversary is able to manipulate the voltage and current sensor outputs necessary to ensure the proper operation of the converters. Furthermore, in the first attack of its kind, it is shown that the gate signal that controls the converter switches can be manipulated, to catastrophic effect; i.e., it is possible for an attacker to control the switching state of individual transistors to cause irreparable damage to the converter and associated systems. Finally, a discussion of countermeasures for hardware designers to mitigate IEMI-based attacks is provided.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00032"}, {"primary_key": "2683861", "vector": [], "sparse_vector": [], "title": "Kobold: Evaluating Decentralized Access Control for Remote NSXPC Methods on iOS.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Apple uses several access control mechanisms to prevent third party applications from directly accessing security sensitive resources, including sandboxing and file access control. However, third party applications may also indirectly access these resources using inter-process communication (IPC) with system daemons. If these daemons fail to properly enforce access control on IPC, confused deputy vulnerabilities may result. Identifying such vulnerabilities begins with an enumeration of all IPC services accessible to third party applications. However, the IPC interfaces and their corresponding access control policies are unknown and must be reverse engineered at a large scale. In this paper, we present the Kobold framework to study NSXPC-based system services using a combination of static and dynamic analysis. Using Kobold, we discovered multiple NSXPC services with confused deputy vulnerabilities and daemon crashes. Our findings include the ability to activate the microphone, disable access to all websites, and leak private data stored in iOS File Providers.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00023"}, {"primary_key": "2683862", "vector": [], "sparse_vector": [], "title": "Browsing Unicity: On the Limits of Anonymizing Web Tracking Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cross domain tracking has become the rule, rather than the exception, and scripts that collect behavioral data from visitors across sites have become ubiquitous on the Web. The collections form comprehensive profiles of browsing patterns and contain personal, sensitive information. This data can easily be linked back to the tracked individuals, most of whom are likely unaware of this information's mere existence, let alone its perpetual storage and processing. As public pressure has increased, tracking companies like Google, Facebook, or Baidu now claim to anonymize their datasets, thus limiting or eliminating the possibility of linking it back to data subjects.In cooperation with Europe's largest audience measurement association we use access to a comprehensive tracking dataset to assess both identifiability and the possibility of convincingly anonymizing browsing data. Our results show that anonymization through generalization does not sufficiently protect anonymity. Reducing unicity of browsing data to negligible levels would necessitate removal of all client and web domain information as well as click timings. In tangible adversary scenarios, supposedly anonymized datasets are highly vulnerable to dataset enrichment and shoulder surfing adversaries, with almost half of all browsing sessions being identified by just two observations. We conclude that while it may be possible to store single coarsened clicks anonymously, any collection of higher complexity will contain large amounts of pseudonymous data.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00018"}, {"primary_key": "2683863", "vector": [], "sparse_vector": [], "title": "RetroWrite: Statically Instrumenting COTS Binaries for Fuzzing and Sanitization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Analyzing the security of closed source binaries is currently impractical for end-users, or even developers who rely on third-party libraries. Such analysis relies on automatic vulnerability discovery techniques, most notably fuzzing with sanitizers enabled. The current state of the art for applying fuzzing or sanitization to binaries is dynamic binary translation, which has prohibitive performance overhead. The alternate technique, static binary rewriting, cannot fully recover symbolization information and hence has difficulty modifying binaries to track code coverage for fuzzing or to add security checks for sanitizers.The ideal solution for binary security analysis would be a static rewriter that can intelligently add the required instrumentation as if it were inserted at compile time. Such instrumentation requires an analysis to statically disambiguate between references and scalars, a problem known to be undecidable in the general case. We show that recovering this information is possible in practice for the most common class of software and libraries: 64-bit, position independent code. Based on this observation, we develop RetroWrite, a binary-rewriting instrumentation to support American Fuzzy Lop (AFL) and Address Sanitizer (ASan), and show that it can achieve compiler-level performance while retaining precision. Binaries rewritten for coverage-guided fuzzing using RetroWrite are identical in performance to compiler-instrumented binaries and outperform the default QEMU-based instrumentation by 4.5x while triggering more bugs. Our implementation of binary-only Address Sanitizer is 3x faster than Valgrind's memcheck, the state-of-the-art binary-only memory checker, and detects 80% more bugs in our evaluation.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00009"}, {"primary_key": "2683864", "vector": [], "sparse_vector": [], "title": "Detecting Cyber Threats in Non-English Hacker Forums: An Adversarial Cross-Lingual Knowledge Transfer Approach.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Yidong <PERSON>i", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The regularity of devastating cyber-attacks has made cybersecurity a grand societal challenge. Many cybersecurity professionals are closely examining the international Dark Web to proactively pinpoint potential cyber threats. Despite its potential, the Dark Web contains hundreds of thousands of non-English posts. While machine translation is the prevailing approach to process non-English text, applying MT on hacker forum text results in mistranslations. In this study, we draw upon Long-Short Term Memory (LSTM), Cross-Lingual Knowledge Transfer (CLKT), and Generative Adversarial Networks (GANs) principles to design a novel Adversarial CLKT (A-CLKT) approach. A-CLKT operates on untranslated text to retain the original semantics of the language and leverages the collective knowledge about cyber threats across languages to create a language invariant representation without any manual feature engineering or external resources. Three experiments demonstrate how A-CLKT outperforms state-of-the-art machine learning, deep learning, and CLKT algorithms in identifying cyber-threats in French and Russian forums.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00021"}, {"primary_key": "2683865", "vector": [], "sparse_vector": [], "title": "A Security Analysis of the Facebook Ad Library.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Actors engaged in election disinformation are using online advertising platforms to spread political messages. In response to this threat, online advertising networks have started making political advertising on their platforms more transparent in order to enable third parties to detect malicious advertisers. We present a set of methodologies and perform a security analysis of Facebook's U.S. Ad Library, which is their political advertising transparency product. Unfortunately, we find that there are several weaknesses that enable a malicious advertiser to avoid accurate disclosure of their political ads. We also propose a clustering-based method to detect advertisers engaged in undeclared coordinated activity. Our clustering method identified 16 clusters of likely inauthentic communities that spent a total of over four million dollars on political advertising. This supports the idea that transparency could be a promising tool for combating disinformation. Finally, based on our findings, we make recommendations for improving the security of advertising transparency on Facebook and other platforms.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00084"}, {"primary_key": "2683866", "vector": [], "sparse_vector": [], "title": "A Smart City Internet for Autonomous Systems.", "authors": ["<PERSON>"], "summary": "A smart city involves critical infrastructure systems that have been digitally enabled. Increasingly, many smart city cyber-physical systems are becoming automated. The extent of automation ranges from basic logic gates to sophisticated, artificial intelligence (AI) that enables fully autonomous systems. Because of modern society's reliance on autonomous systems in smart cities, it is crucial for them to operate in a safe manner; otherwise, it is feasible for these systems to cause considerable physical harm or even death. Because smart cities could involve thousands of autonomous systems operating in concert in densely populated areas, safety assurances are required. Challenges abound to consistently manage the safety of such autonomous systems due to their disparate developers, manufacturers, operators and users. A novel network and a sample of associated network functions for autonomous systems is proposed that aims to provide a baseline of safety for autonomous systems in a smart city ecosystem. A proposed network called the Assured Autonomous Cyber-Physical Ecosystem (AACE) would be separate from the Internet, and enforces certain functions that enable safety through active networking. Each smart city could dictate the functions for their own AACE, providing a means for enforcing safety policies across disparate autonomous systems operating in the city's jurisdiction. Such a network design sits at the margins of the end-to-end principle, which is warranted considering the safety of autonomous systems is at stake as is argued in this paper. Without a scalable safety strategy for autonomous systems as proposed, assured autonomy in smart cities will remain elusive.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00051"}, {"primary_key": "2683867", "vector": [], "sparse_vector": [], "title": "Cornucopia: Temporal Safety for CHERI Heaps.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Use-after-free violations of temporal memory safety continue to plague software systems, underpinning many high-impact exploits. The CHERI capability system shows great promise in achieving C and C++ language spatial memory safety, preventing out-of-bounds accesses. Enforcing language-level temporal safety on CHERI requires capability revocation, traditionally achieved either via table lookups (avoided for performance in the CHERI design) or by identifying capabilities in memory to revoke them (similar to a garbage-collector sweep). CHERIvoke, a prior feasibility study, suggested that CHERI's tagged capabilities could make this latter strategy viable, but modeled only architectural limits and did not consider the full implementation or evaluation of the approach.Cornucopia is a lightweight capability revocation system for CHERI that implements non-probabilistic C/C++ temporal memory safety for standard heap allocations. It extends the CheriBSD virtual-memory subsystem to track capability flow through memory and provides a concurrent kernel-resident revocation service that is amenable to multi-processor and hardware acceleration. We demonstrate an average overhead of less than 2% and a worst-case of 8.9% for concurrent revocation on compatible SPEC CPU2006 benchmarks on a multi-core CHERI CPU on FPGA, and we validate Cornucopia against the Juliet test suite's corpus of temporally unsafe programs. We test its compatibility with a large corpus of C programs by using a revoking allocator as the system allocator while booting multi-user CheriBSD. Cornucopia is a viable strategy for always-on temporal heap memory safety, suitable for production environments.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00098"}, {"primary_key": "2683868", "vector": [], "sparse_vector": [], "title": "I Know Where You Parked Last Summer : Automated Reverse Engineering and Privacy Analysis of Modern Cars.", "authors": ["<PERSON>", "Sohyeon Park", "<PERSON>"], "summary": "Nowadays, cars are equipped with hundreds of sensors and dozens of computers that process data. Unfortunately, due to the very secret nature of the automotive industry, there is no official nor objective source of information as to what data exactly their vehicles collect. Anecdotal evidence suggests that OEMs are collecting huge amounts of personal data about their drivers, which they suddenly reveal when requested in court.In this paper, we present our tool AutoCAN for privacy and security analysis of cars that reveals what data cars collect by tapping into in-vehicle networks and extracting time series of data and automatically making sense of them by establishing relationships based on laws of physics. These algorithms work irrespective of make, model or used protocols. Our results show that car makers track the GPS position, the number of occupants, their weight, usage statistics of doors, lights, and AC. We also reveal that OEMs embed functions to remotely disable the car or get an alert when the driver is speeding.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00081"}, {"primary_key": "2683869", "vector": [], "sparse_vector": [], "title": "TRRespass: Exploiting the Many Sides of Target Row Refresh.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "After a plethora of high-profile RowHammer attacks, CPU and DRAM vendors scrambled to deliver what was meant to be the definitive hardware solution against the RowHammer problem: Target Row Refresh (TRR). A common belief among practitioners is that, for the latest generation of DDR4 systems that are protected by TRR, RowHammer is no longer an issue in practice. However, in reality, very little is known about TRR. How does TRR exactly prevent RowHammer? Which parts of a system are responsible for operating the TRR mechanism? Does TRR completely solve the RowHammer problem or does it have weaknesses? In this paper, we demystify the inner workings of TRR and debunk its security guarantees. We show that what is advertised as a single mitigation mechanism is actually a series of different solutions coalesced under the umbrella term Target Row Refresh. We inspect and disclose, via a deep analysis, different existing TRR solutions and demonstrate that modern implementations operate entirely inside DRAM chips. Despite the difficulties of analyzing in-DRAM mitigations, we describe novel techniques for gaining insights into the operation of these mitigation mechanisms. These insights allow us to build TRRespass, a scalable black-box RowHammer fuzzer that we evaluate on 42 recent DDR4 modules. TRRespass shows that even the latest generation DDR4 chips with in-DRAM TRR, immune to all known RowHammer attacks, are often still vulnerable to new TRR-aware variants of RowHammer that we develop. In particular, TRRespass finds that, on present-day DDR4 modules, RowHammer is still possible when many aggressor rows are used (as many as 19 in some cases), with a method we generally refer to as Many-sided RowHammer. Overall, our analysis shows that 13 out of the 42 modules from all three major DRAM vendors (i.e., Samsung, Micron, and Hynix) are vulnerable to our TRR-aware RowHammer access patterns, and thus one can still mount existing state-of-the-art system-level RowHammer attacks. In addition to DDR4, we also experiment with LPDDR4(X) 1 chips and show that they are susceptible to RowHammer bit flips too. Our results provide concrete evidence that the pursuit of better RowHammer mitigations must continue.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00090"}, {"primary_key": "2683870", "vector": [], "sparse_vector": [], "title": "An Analysis of Pre-installed Android Software.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>-<PERSON>"], "summary": "The open-source nature of the Android OS makes it possible for manufacturers to ship custom versions of the OS along with a set of pre-installed apps, often for product differentiation. Some device vendors have recently come under scrutiny for potentially invasive private data collection practices and other potentially harmful or unwanted behavior of the preinstalled apps on their devices. Yet, the landscape of preinstalled software in Android has largely remained unexplored, particularly in terms of the security and privacy implications of such customizations. In this paper, we present the first large- scale study of pre-installed software on Android devices from more than 200 vendors. Our work relies on a large dataset of real-world Android firmware acquired worldwide using crowd-sourcing methods. This allows us to answer questions related to the stakeholders involved in the supply chain, from device manufacturers and mobile network operators to third- party organizations like advertising and tracking services, and social network platforms. Our study allows us to also uncover relationships between these actors, which seem to revolve primarily around advertising and data-driven services. Overall, the supply chain around Android's open source model lacks transparency and has facilitated potentially harmful behaviors and backdoored access to sensitive data and services without user consent or awareness. We conclude the paper with recommendations to improve transparency, attribution, and accountability in the Android ecosystem.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00013"}, {"primary_key": "2683871", "vector": [], "sparse_vector": [], "title": "C3APSULe: Cross-FPGA Covert-Channel Attacks through Power Supply Unit Leakage.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Field-Programmable Gate Arrays (FPGAs) are versatile, reconfigurable integrated circuits that can be used as hardware accelerators to process highly-sensitive data. Leaking this data and associated cryptographic keys, however, can undermine a system's security. To prevent potentially unintentional interactions that could break separation of privilege between different data center tenants, FPGAs in cloud environments are currently dedicated on a per-user basis. Nevertheless, while the FPGAs themselves are not shared among different users, other parts of the data center infrastructure are. This paper specifically shows for the first time that powering FPGAs, CPUs, and GPUs through the same power supply unit (PSU) can be exploited in FPGA-to-FPGA, CPU-to-FPGA, and GPU-to-FPGA covert channels between independent boards. These covert channels can operate remotely, without the need for physical access to, or modifications of, the boards. To demonstrate the attacks, this paper uses a novel combination of \"sensing\" and \"stressing\" ring oscillators as receivers on the sink FPGA. Further, ring oscillators are used as transmitters on the source FPGA. The transmitting and receiving circuits are used to determine the presence of the leakage on off-the-shelf Xilinx boards containing Artix 7 and Kintex 7 FPGA chips. Experiments are conducted with PSUs by two vendors, as well as CPUs and GPUs of different generations. Moreover, different sizes and types of ring oscillators are also tested. In addition, this work discusses potential countermeasures to mitigate the impact of the cross-board leakage. The results of this paper highlight the dangers of shared power supply units in local and cloud FPGAs, and therefore a fundamental need to re-think FPGA security for shared infrastructures.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00070"}, {"primary_key": "2683872", "vector": [], "sparse_vector": [], "title": "Spectector: Principled Detection of Speculative Information Flows.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Since the advent of Spectre, a number of counter-measures have been proposed and deployed. Rigorously reasoning about their effectiveness, however, requires a well-defined notion of security against speculative execution attacks, which has been missing until now.In this paper (1) we put forward speculative non-interference, the first semantic notion of security against speculative execution attacks, and (2) we develop Spectector, an algorithm based on symbolic execution to automatically prove speculative non-interference, or to detect violations.We implement Spectector in a tool, which we use to detect subtle leaks and optimizations opportunities in the way major compilers place Spectre countermeasures. A scalability analysis indicates that checking speculative non-interference does not exhibit fundamental bottlenecks beyond those inherited by symbolic execution.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00011"}, {"primary_key": "2683873", "vector": [], "sparse_vector": [], "title": "Efficient and Secure Multiparty Computation from Fixed-Key Block Ciphers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many implementations of secure computation use fixed-key AES (modeled as a random permutation); this results in substantial performance benefits due to existing hardware support for AES and the ability to avoid recomputing the AES key schedule.Surveying these implementations, however, we find that most utilize AES in a heuristic fashion; in the best case this leaves a gap in the security proof, but in many cases we show it allows for explicit attacks.Motivated by this unsatisfactory state of affairs, we initiate a comprehensive study of how to use fixed-key block ciphers for secure computation-in particular for OT extension and circuit garbling-efficiently and securely.Specifically:• We consider several notions of pseudorandomness for hash functions (e.g., correlation robustness), and show provably secure schemes for OT extension, garbling, and other applications based on hash functions satisfying these notions.• We provide provably secure constructions, in the (nonprogrammable) random-permutation model, of hash functions satisfying the different notions of pseudorandomness we consider.Taken together, our results provide end-to-end security proofs for implementations of secure-computation protocols based on fixedkey block ciphers (modeled as random permutations).Perhaps surprisingly, at the same time our work also results in noticeable performance improvements over the state-of-the-art.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00016"}, {"primary_key": "2683874", "vector": [], "sparse_vector": [], "title": "Fooling A Deep-Learning Based Gait Behavioral Biometric System.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xiangyang Li", "<PERSON><PERSON>"], "summary": "We leverage deep learning algorithms on various user behavioral information gathered from end-user devices to classify a subject of interest. In spite of the ability of these techniques to counter spoofing threats, they are vulnerable to adversarial learning attacks, where an attacker adds adversarial noise to the input samples to fool the classifier into false acceptance. Recently, a handful of mature techniques like Fast Gradient Sign Method (FGSM) have been proposed to aid white-box attacks, where an attacker has a complete knowledge of the machine learning model. On the contrary, we exploit a black-box attack to a behavioral biometric system based on gait patterns, by using FGSM and training a shadow model that mimics the target system. The attacker has limited knowledge on the target model and no knowledge of the real user being authenticated, but induces a false acceptance in authentication. Our goal is to understand the feasibility of a black-box attack and to what extent FGSM on shadow models would contribute to its success. Our results manifest that the performance of FGSM highly depends on the quality of the shadow model, which is in turn impacted by key factors including the number of queries allowed by the target system in order to train the shadow model. Our experimentation results have revealed strong relationships between the shadow model and FGSM performance, as well as the effect of the number of FGSM iterations used to create an attack instance. These insights also shed light on deep-learning algorithms' model shareability that can be exploited to launch a successful attack.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00052"}, {"primary_key": "2683875", "vector": [], "sparse_vector": [], "title": "Identifying Ubiquitious Third-Party Libraries in Compiled Executables Using Annotated and Translated Disassembled Code with Supervised Machine Learning.", "authors": ["<PERSON><PERSON><PERSON>", "Sage Havens"], "summary": "The size and complexity of the software ecosystem is a major challenge for vendors, asset owners and cybersecurity professionals who need to understand the security posture of these systems. Annotated and Translated Disassembled Code is a graph based datastore designed to organize firmware and software analysis data across builds, packages and systems, providing a highly scalable platform enabling automated binary software analysis tasks including corpora construction and storage for machine learning. This paper describes an approach for the identification of ubiquitous third-party libraries in firmware and software using Annotated and Translated Disassembled Code and supervised machine learning. Annotated and Translated Disassembled Code provide matched libraries, function names and addresses of previously unidentified code in software as it is being automatically analyzed. This data can be ingested by other software analysis tools to improve accuracy and save time. Defenders can add the identified libraries to their vulnerability searches and add effective detection and mitigation into their operating environment.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00042"}, {"primary_key": "2683876", "vector": [], "sparse_vector": [], "title": "How not to prove your election outcome.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The Scytl/SwissPost e-voting solution was intended to provide complete verifiability for Swiss government elections. We show failures in both individual verifiability and universal verifiability (as defined in Swiss Federal Ordinance 161.116), based on mistaken implementations of cryptographic components. These failures allow for the construction of \"proofs\" of an accurate election outcome that pass verification though the votes have been manipulated. Using sophisticated cryptographic protocols without a proper consideration of what properties they offer, and under which conditions, can introduce opportunities for undetectable fraud even though the system appears to allow verification of the outcome.Our findings are immediately relevant to systems in use in Switzerland and Australia, and probably also elsewhere.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00048"}, {"primary_key": "2683877", "vector": [], "sparse_vector": [], "title": "Toward Automated Grammar Extraction via Semantic Labeling of Parser Implementations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper introduces a new approach for labeling the semantic purpose of the functions in a parser. An input file with a known syntax tree is passed to a copy of the target parser that has been instrumented for universal taint tracking. A novel algorithm is used to merge that syntax tree ground truth with the observed taint and control-flow information from the parser's execution, producing a mapping from types in the file format to the set of functions most specialized in operating on that type. The resulting mapping has applications in mutational fuzzing, reverse engineering, differential analysis, as well as automated grammar extraction. We demonstrate that even a single execution of an instrumented parser with a single input file can lead to a mapping that a human would identify as intuitively correct. We hope that this approach will lead to both safer subsets of file formats and safer parsers.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00061"}, {"primary_key": "2683878", "vector": [], "sparse_vector": [], "title": "Automatically Detecting Bystanders in Photos to Reduce Privacy Risks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Photographs taken in public places often contain bystanders - people who are not the main subject of a photo. These photos, when shared online, can reach a large number of viewers and potentially undermine the bystanders' privacy. Furthermore, recent developments in computer vision and machine learning can be used by online platforms to identify and track individuals. To combat this problem, researchers have proposed technical solutions that require bystanders to be proactive and use specific devices or applications to broadcast their privacy policy and identifying information to locate them in an image.We explore the prospect of a different approach - identifying bystanders solely based on the visual information present in an image. Through an online user study, we catalog the rationale humans use to classify subjects and bystanders in an image, and systematically validate a set of intuitive concepts (such as intentionally posing for a photo) that can be used to automatically identify bystanders. Using image data, we infer those concepts and then use them to train several classifier models. We extensively evaluate the models and compare them with human raters. On our initial dataset, with a 10-fold cross validation, our best model achieves a mean detection accuracy of 93% for images when human raters have 100% agreement on the class label and 80% when the agreement is only 67%. We validate this model on a completely different dataset and achieve similar results, demonstrating that our model generalizes well.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00097"}, {"primary_key": "2683879", "vector": [], "sparse_vector": [], "title": "Tactical Provenance Analysis for Endpoint Detection and Response Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Endpoint Detection and Response (EDR) tools provide visibility into sophisticated intrusions by matching system events against known adversarial behaviors. However, current solutions suffer from three challenges: 1) EDR tools generate a high volume of false alarms, creating backlogs of investigation tasks for analysts; 2) determining the veracity of these threat alerts requires tedious manual labor due to the overwhelming amount of low-level system logs, creating a \"needle-in-a-haystack\" problem; and 3) due to the tremendous resource burden of log retention, in practice the system logs describing long-lived attack campaigns are often deleted before an investigation is ever initiated.This paper describes an effort to bring the benefits of data provenance to commercial EDR tools. We introduce the notion of Tactical Provenance Graphs (TPGs) that, rather than encoding low-level system event dependencies, reason about causal dependencies between EDR-generated threat alerts. TPGs provide compact visualization of multi-stage attacks to analysts, accelerating investigation. To address EDR's false alarm problem, we introduce a threat scoring methodology that assesses risk based on the temporal ordering between individual threat alerts present in the TPG. In contrast to the retention of unwieldy system logs, we maintain a minimally-sufficient skeleton graph that can provide linkability between existing and future threat alerts. We evaluate our system, RapSheet, using the Symantec EDR tool in an enterprise environment. Results show that our approach can rank truly malicious TPGs higher than false alarm TPGs. Moreover, our skeleton graph reduces the long-term burden of log retention by up to 87%.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00096"}, {"primary_key": "2683880", "vector": [], "sparse_vector": [], "title": "A Framework for the Analysis of Deep Neural Networks in Autonomous Aerospace Applications using Bayesian Statistics.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Deep Neural Networks (DNNs) are considered to be key components in many autonomous systems. Applications range from vision-based obstacle avoidance to intelligent/learning control and planning. Safety-critical applications as found in the aerospace domain require that the behavior of the DNN is validated and tested rigorously for safety of the autonomous system (AUS). In this paper, we present a framework to support testing of DNNs and the analysis of the network structure. Our framework employs techniques from statistical modeling and active learning to effectively generate test cases for DNN safety testing and performance analysis. We will present results of a case study on a physics-based Deep recurrent residual neural network (DR-RNN), which has been trained to emulate the aerodynamics behavior of a fixed-wing aircraft.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00054"}, {"primary_key": "2683881", "vector": [], "sparse_vector": [], "title": "TextExerciser: Feedback-driven Text Input Exercising for Android Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Cao", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Dynamic analysis of Android apps is often used together with an exerciser to increase its code coverage.One big obstacle in designing such Android app exercisers comes from the existence of text-based inputs, which are often constrained by the nature of the input field, such as the length and character restrictions.In this paper, we propose TextExerciser, an iterative, feedback-driven text input exerciser, which generates text inputs for Android apps.Our key insight is that Android apps often provide feedback, called hints, for malformed inputs so that our system can utilize such hints to improve the input generation.We implemented a prototype of TextExerciser and evaluated it by comparing TextExerciser with state-of-the-art exercisers, such as The Monkey and DroidBot.Our evaluation shows that TextExerciser can achieve significantly higher code coverage and trigger more sensitive behaviors than these tools.We also combine TextExerciser with dynamic analysis tools and show they are able to detect more privacy leaks and vulnerabilities with TextExerciser than with existing exercisers.Particularly, existing tools, under the help of TextExerciser, find several new vulnerabilities, such as one user credential leak in a popular social app with more than 10,000,000 downloads.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00071"}, {"primary_key": "2683882", "vector": [], "sparse_vector": [], "title": "Language-agnostic Injection Detection.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Formal languages are ubiquitous wherever software systems need to exchange or store data. Unparsing into and parsing from such languages is an error-prone process that has spawned an entire class of security vulnerabilities. There has been ample research into finding vulnerabilities on the parser side, but outside of language specific approaches, few techniques targeting unparser vulnerabilities exist. This work presents a language-agnostic approach for spotting injection vulnerabilities in unparsers. It achieves this by mining unparse trees using dynamic taint analysis to extract language keywords, which are leveraged for guided fuzzing. Vulnerabilities can thus be found without requiring prior knowledge about the formal language, and in fact, the approach is even applicable where no specification thereof exists at all. This empowers security researchers and developers alike to gain deeper understanding of unparser implementations through examination of the unparse trees generated by the approach, as well as enabling them to find new vulnerabilities in poorly-understood software. This work presents a language-agnostic approach for spotting injection vulnerabilities in unparsers. It achieves this by mining unparse trees using dynamic taint analysis to extract language keywords, which are leveraged for guided fuzzing. Vulnerabilities can thus be found without requiring prior knowledge about the formal language, and in fact, the approach is even applicable where no specification thereof exists at all. This empowers security researchers and developers alike to gain deeper understanding of unparser implementations through examination of the unparse trees generated by the approach, as well as enabling them to find new vulnerabilities in poorly-understood software.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00060"}, {"primary_key": "2683883", "vector": [], "sparse_vector": [], "title": "Combating Dependence Explosion in Forensic Analysis Using Alternative Tag Propagation Semantics.", "authors": ["<PERSON><PERSON> <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We are witnessing a rapid escalation in targeted cyber-attacks called Advanced and Persistent Threats (APTs). Carried out by skilled adversaries, these attacks take place over extended time periods, and remain undetected for months. A common approach for retracing the attacker's steps is to start with one or more suspicious events from system logs, and perform a dependence analysis to uncover the rest of attacker's actions. The accuracy of this analysis suffers from the dependence explosion problem, which causes a very large number of benign events to be flagged as part of the attack. In this paper, we propose two novel techniques, tag attenuation and tag decay, to mitigate dependence explosion. Our techniques take advantage of common behaviors of benign processes, while providing a conservative treatment of processes and data with suspicious provenance. Our system, called Morse, is able to construct a compact scenario graph that summarizes attacker activity by sifting through millions of system events in a matter of seconds. Our experimental evaluation, carried out using data from two government-agency sponsored red team exercises, demonstrates that our techniques are (a) effective in identifying stealthy attack campaigns, (b) reduce the false alarm rates by more than an order of magnitude, and (c) yield compact scenario graphs that capture the vast majority of the attack, while leaving out benign background activity.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00064"}, {"primary_key": "2683884", "vector": [], "sparse_vector": [], "title": "A Case Study of the Security Vetting Process of Smart-home Assistant Applications.", "authors": ["Hang Hu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The popularity of smart-home assistant systems such as Amazon Alexa and Google Home leads to a booming third-party application market (over 70,000 applications across the two stores). While existing works have revealed security issues in these systems, it is not well understood how to help application developers to enforce security requirements. In this paper, we perform a preliminary case study to examine the security vetting mechanisms adopted by Amazon Alexa and Google Home app stores. With a focus on the authentication mechanisms between Alexa/Google cloud and third-party application servers (i.e. endpoints), we show the current security vetting is insufficient as developers' mistakes cannot be effectively detected and notified. A weak authentication would allow attackers to spoof the cloud to insert/retrieve data into/from the application endpoints. We validate the attack through ethical proof-of-concept experiments. To confirm vulnerable applications have indeed passed the security vetting and entered the markets, we develop a heuristic-based searching method. We find 219 real-world Alexa endpoints that carry the vulnerability, many of which are related to critical applications that control smart home devices and electronic cars. We have notified Amazon and Google about our findings and offered our suggestions to mitigate the issue.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00029"}, {"primary_key": "2683885", "vector": [], "sparse_vector": [], "title": "Pangolin: Incremental Hybrid Fuzzing with Polyhedral Path Abstraction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Qingkai Shi", "<PERSON>"], "summary": "Hybrid fuzzing, which combines the merits of both fuzzing and concolic execution, has become one of the most important trends in coverage-guided fuzzing techniques. Despite the tremendous research on hybrid fuzzers, we observe that existing techniques are still inefficient. One important reason is that these techniques, which we refer to as non-incremental fuzzers, cache and reuse few computation results and, thus, lose many optimization opportunities. To be incremental, we propose \"polyhedral path abstraction\", which preserves the exploration state in the concolic execution stage and allows more effective mutation and constraint solving over existing techniques. We have implemented our idea as a tool, namely Pangolin, and evaluated it using LAVA-M as well as nine real-world programs. The evaluation results showed that Pangolin outperforms the state-of-the-art fuzzing techniques with the improvement of coverage rate ranging from 10% to 30%. Moreover, Pangolin found 400 more bugs in LAVA-M and discovered 41 unseen bugs with 8 of them assigned with the CVE IDs.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00063"}, {"primary_key": "2683886", "vector": [], "sparse_vector": [], "title": "The Geometry of Syntax and Semantics for Directed File Transformations.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We introduce a conceptual framework that associates syntax and semantics with vertical and horizontal directions in principal bundles and related constructions. This notion of geometry corresponds to a mechanism for performing goal-directed file transformations such as \"eliminate unsafe syntax\" and suggests various engineering practices.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00062"}, {"primary_key": "2683887", "vector": [], "sparse_vector": [], "title": "Privacy-preserving Continuous Tumour Relapse Monitoring Using In-body Radio Signals.", "authors": ["<PERSON>", "Wenqing Yan", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Early detection and treatment of cancerous tumours significantly improve the lives of cancer patients, as well as increase their chance of surviving and reduce treatment cost. A novel study has utilised the human adipose (fat) tissue as a propagation channel for radio frequency communication within the human body. A notable application of this technology is the continuous monitoring of the growth of perturbants, such as tumours, in the channel. This paper addresses the privacy issues associated with the deployment of this monitoring technology. Our work departs from previous studies in that we consider the privacy of the sensing process itself, rather than the privacy of sensed data. We study the information leakage associated with the deployment of this technology and propose and evaluate a set of privacy-enhancing techniques that reduces information leakage. Finally, we propose and evaluate an approach that combines these techniques and, thereby, protects patient's privacy.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00030"}, {"primary_key": "2683888", "vector": [], "sparse_vector": [], "title": "AdGraph: A Graph-Based Approach to Ad and Tracker Blocking.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "User demand for blocking advertising and tracking online is large and growing. Existing tools, both deployed and described in research, have proven useful, but lack either the completeness or robustness needed for a general solution. Existing detection approaches generally focus on only one aspect of advertising or tracking (e.g. URL patterns, code structure), making existing approaches susceptible to evasion.In this work we present AdGraph, a novel graph-based machine learning approach for detecting advertising and tracking resources on the web. AdGraph differs from existing approaches by building a graph representation of the HTML structure, network requests, and JavaScript behavior of a webpage, and using this unique representation to train a classifier for identifying advertising and tracking resources. Because AdGraph considers many aspects of the context a network request takes place in, it is less susceptible to the single-factor evasion techniques that flummox existing approaches.We evaluate AdGraph on the Alexa top-10K websites, and find that it is highly accurate, able to replicate the labels of human-generated filter lists with 95.33% accuracy, and can even identify many mistakes in filter lists. We implement AdGraph as a modification to Chromium. AdGraph adds only minor overhead to page loading and execution, and is actually faster than stock Chromium on 42% of websites and AdBlock Plus on 78% of websites. Overall, we conclude that AdGraph is both accurate enough and performant enough for online use, breaking comparable or fewer websites than popular filter list based approaches.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00005"}, {"primary_key": "2683889", "vector": [], "sparse_vector": [], "title": "A Non-Cooperative Game based Model for the Cybersecurity of Autonomous Systems.", "authors": ["<PERSON><PERSON>", "Weiqing Sun", "<PERSON><PERSON><PERSON>"], "summary": "Autonomous systems (AS) would soon revolutionize the way we live and work. The days are not so far when these systems, from delivery drones to driverless cars, would be seen around us. These systems are connected and rely heavily on the communication network for the information exchange, hence prone to several attacks. Human lives will be at risk if these systems are compromised. Cybersecurity modeling and attack analysis of AS needs the utmost attention of the research community. Primarily, a typical AS has three modules - perception, cognition, and control - and each one of them comes with their own vulnerabilities. In this work, we propose a new AS architecture that may prove useful in AS cybersecurity modeling. We also model the attacks on them, and defense mechanisms applied to these modules using a non-cooperative non-zero sum game. Finally, we solve this game to obtain optimal strategies to maintain a secure system state.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00049"}, {"primary_key": "2683890", "vector": [], "sparse_vector": [], "title": "Throwing Darts in the Dark? Detecting Bots with Limited Data using Neural Data Augmentation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Jiameng Pu", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Machine learning has been widely applied to building security applications. However, many machine learning models require the continuous supply of representative labeled data for training, which limits the models' usefulness in practice. In this paper, we use bot detection as an example to explore the use of data synthesis to address this problem. We collected the network traffic from 3 online services in three different months within a year (23 million network requests). We develop a stream-based feature encoding scheme to support machine learning models for detecting advanced bots. The key novelty is that our model detects bots with extremely limited labeled data. We propose a data synthesis method to synthesize unseen (or future) bot behavior distributions. The synthesis method is distribution-aware, using two different generators in a Generative Adversarial Network to synthesize data for the clustered regions and the outlier regions in the feature space. We evaluate this idea and show our method can train a model that outperforms existing methods with only 1% of the labeled data. We show that data synthesis also improves the model's sustainability over time and speeds up the retraining. Finally, we compare data synthesis and adversarial retraining and show they can work complementary with each other to improve the model generalizability.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00079"}, {"primary_key": "2683891", "vector": [], "sparse_vector": [], "title": "Never Ending Story: Authentication and Access Control Design Flaws in Shared IoT Devices.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "Internet-of-Things (IoT) devices implement weak authentication and access control schemes. The on-demand nature of IoT devices requires a responsive communications channel, which is often at odds with thorough authentication and access control. This paper seeks to better understand IoT device security by examining the design of authentication and access control schemes. In this work, we explore the challenge of propagating credential revocation and access control list modifications in a shared IoT ecosystem. We evaluate the vulnerability of 19 popular security cameras and doorbells against a straightforward user-interface bound adversary attack. Our results demonstrate that 16 of 19 surveyed devices suffer from flaws that enable unauthorized access after credential modification or revocation. We conclude by discussing these findings and propose a means for balancing authentication and access control schemes while still offering responsive communications channels.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00033"}, {"primary_key": "2683892", "vector": [], "sparse_vector": [], "title": "Adversarial Attacks Against LipNet: End-to-End Sentence Level Lipreading.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Visual adversarial attacks inspired by <PERSON><PERSON><PERSON><PERSON> targeted audiovisual attacks can fool the state-of-the-art Google DeepMind LipNet model to subtitle anything with over 99% similarity. We explore several methods of visual adversarial attacks, including the vanilla fast gradient sign method (FGSM), the L∞ iterative fast gradient sign method, and the L2 modified <PERSON><PERSON><PERSON> attacks. The feasibility of these attacks raise privacy and false information threats, as video transcriptions are used to recommend and inform people worldwide and on social media.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00020"}, {"primary_key": "2683893", "vector": [], "sparse_vector": [], "title": "Burglars&apos; IoT Paradise: Understanding and Mitigating Security Risks of General Messaging Protocols on IoT Clouds.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "With the increasing popularity of the Internet of Things (IoT), many IoT cloud platforms have emerged to help the IoT manufacturers connect their devices to their users. Serving the device-user communication is general messaging protocol deployed on the platforms. Less clear, however, is whether such protocols, which are not designed to work in the adversarial environment of IoT, introduce new risks. In this paper, we report the first systematic study on the protection of major IoT clouds (e.g., AWS, Microsoft, IBM) put in place for the arguably most popular messaging protocol - MQTT. We found that these platforms' security additions to the protocol are all vulnerable, allowing the adversary to gain control of the device, launch a large-scale denial-of-service attack, steal the victim's secrets data and fake the victim's device status for deception. We successfully performed end-to-end attacks on these popular IoT clouds and further conducted a measurement study, which demonstrates that the security impacts of our attacks are real, severe and broad. We reported our findings to related parties, which all acknowledged the importance. We further propose new design principles and an enhanced access model MOUCON. We implemented our protection on a popular open-source MQTT server. Our evaluation shows its high effectiveness and negligible performance overhead.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00051"}, {"primary_key": "2683894", "vector": [], "sparse_vector": [], "title": "Semantic Understanding of Smart Contracts: Executable Operational Semantics of Solidity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Bitcoin has been a popular research topic recently. Ethereum (ETH), a second generation of cryptocurrency, extends Bitcoin's design by offering a Turing-complete programming language called Solidity to develop smart contracts. Smart contracts allow creditable execution of contracts on EVM (Ethereum Virtual Machine) without third parties. Developing correct and secure smart contracts is challenging due to the decentralized computation nature of the blockchain. Buggy smart contracts may lead to huge financial loss. Furthermore, smart contracts are very hard, if not impossible, to patch once they are deployed. Thus, there is a recent surge of interest in analyzing and verifying smart contracts. While most of the existing works either focus on EVM bytecode or translate Solidity smart contracts into programs in intermediate languages, we argue that it is important and necessary to understand and formally define the semantics of Solidity since programmers write and reason about smart contracts at the level of source code. In this work, we develop a formal semantics for Solidity which provides a formal specification of smart contracts to define semantic-level security properties for the high-level verification. Furthermore, the proposed semantics defines correct and secure high-level execution behaviours of smart contracts to reason about compiler bugs and assist developers in writing secure smart contracts.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00066"}, {"primary_key": "2683895", "vector": [], "sparse_vector": [], "title": "Attributing and Detecting Fake Images Generated by Known GANs.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The quality of GAN-generated fake images has improved significantly, and recent GAN approaches, such as StyleGAN, achieve near indistinguishability from real images for the naked eye. As a result, adversaries are attracted to using GAN-generated fake images for disinformation campaigns and fraud on social networks. However, training an image generation network to produce realistic-looking samples remains a time-consuming and difficult problem, so adversaries are more likely to use published GAN models to generate fake images. In this paper, we analyze the frequency domain to attribute and detect fake images generated by a known GAN model. We derive a similarity metric on the frequency domain and develop a new approach for GAN image attribution. We conduct experiments on four trained GAN models and two real image datasets. Our results show high attribution accuracy against real images and those from other GAN models. We further analyze our method under evasion attempts and find the frequency-based approach is comparatively robust. In this paper, we analyze the frequency domain to attribute and detect fake images generated by a known GAN model. We derive a similarity metric on the frequency domain and develop a new approach for GAN image attribution. We conduct experiments on four trained GAN models and two real image datasets. Our results show high attribution accuracy against real images and those from other GAN models. We further analyze our method under evasion attempts and find the frequency-based approach is comparatively robust.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00019"}, {"primary_key": "2683896", "vector": [], "sparse_vector": [], "title": "TARDIS: Rolling Back The Clock On CMS-Targeting Cyber Attacks.", "authors": ["<PERSON><PERSON><PERSON>", "Yiting Sun", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Over 55% of the world's websites run on Content Management Systems (CMS). Unfortunately, this huge user population has made CMS-based websites a high-profile target for hackers. Worse still, the vast majority of the website hosting industry has shifted to a \"backup and restore\" model of security, which relies on error-prone AV scanners to prompt users to roll back to a pre-infection nightly snapshot. This research had the opportunity to study these nightly backups for over 300,000 unique production websites. In doing so, we measured the attack landscape of CMS-based websites and assessed the effectiveness of the backup and restore protection scheme. To our surprise, we found that the evolution of tens of thousands of attacks exhibited clear long-lived multi-stage attack patterns. We now propose TARDIS, an automated provenance inference technique, which enables the investigation and remediation of CMS-targeting attacks based on only the nightly backups already being collected by website hosting companies. With the help of our industry collaborator, we applied TARDIS to the nightly backups of those 300K websites and found 20,591 attacks which lasted from 6 to 1,694 days, some of which were still yet to be detected.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00116"}, {"primary_key": "2683897", "vector": [], "sparse_vector": [], "title": "Modeling and Assessment of IoT Supply Chain Security Risks: The Role of Structural and Parametric Uncertainties.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Supply chain security threats pose new challenges to security risk modeling techniques for complex ICT systems such as the IoT. With established techniques drawn from attack trees and reliability analysis providing needed points of reference, graph-based analysis can provide a framework for considering the role of suppliers in such systems. We present such a framework here while highlighting the need for a component-centered model. Given resource limitations when applying this model to existing systems, we study various classes of uncertainties in model development, including structural uncertainties and uncertainties in the magnitude of estimated event probabilities. Using case studies, we find that structural uncertainties constitute a greater challenge to model utility and as such should receive particular attention. Best practices in the face of these uncertainties are proposed.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00043"}, {"primary_key": "2683898", "vector": [], "sparse_vector": [], "title": "EM Fingerprints: Towards Identifying Unauthorized Hardware Substitutions in the Supply Chain Jungle.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper proposes a system capable of branding digital device components based on the EM signals typically emitted during their normal operational cycles. Such signals contain digital artifacts that are unique, which may act as an identifier of a particular device component e.g., its CPU, or the entire device if one chooses to take into account a combination of multiple such components. In real-life scenarios, this \"bio-metrical\" fingerprinting of hardware has to be conducted only once, possibly as part of an initial device configuration process with minimum additional maintenance time and cost, by the network administrators. At a subsequent stage, devices can get \"authenticated\" by comparing their newly emitted signals against the preexisting database during routine checks. The experimental results attest that the proposed approach can effectively protect a network against unrecognized potentially rogue devices posing as benign or malicious substitutions of hardware components at the chip level with near-perfect accuracy. One may view the proposed system as a technical solution to verify the trustworthiness of digital parts as well as the actors involved in certain stages of the supply chain.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00040"}, {"primary_key": "2683899", "vector": [], "sparse_vector": [], "title": "Meddling Middlemen: Empirical Analysis of the Risks of Data-Saving Mobile Browsers.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Mobile browsers have become one of the main mediators of our online activities. However, as web pages continue to increase in size and streaming media on-the-go has become commonplace, mobile data plan constraints remain a significant concern for users. As a result, data-saving features can be a differentiating factor when selecting a mobile browser. In this paper, we present a comprehensive exploration of the security and privacy threat that data-saving functionality presents to users. We conduct the first analysis of Android's data-saving browser (DSB) ecosystem across multiple dimensions, including the characteristics of the various browsers' infrastructure, their application and protocol-level behavior, and their effect on users' browsing experience. Our research unequivocally demonstrates that enabling data-saving functionality in major browsers results in significant degradation of the user's security posture by introducing severe vulnerabilities that are not otherwise present in the browser during normal operation. In summary, our experiments show that enabling data savings exposes users to (i) proxy servers running outdated software, (ii) man-in-the-middle attacks due to problematic validation of TLS certificates, (iii) weakened TLS cipher suite selection, (iv) lack of support of security headers like HSTS, and (v) a higher likelihood of being labelled as bots. While the discovered issues can be addressed, we argue that data-saving functionality presents inherent risks in an increasingly-encrypted Web, and users should be alerted of the critical savings-vs-security trade-off that they implicitly accept every time they enable such functionality.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00077"}, {"primary_key": "2683900", "vector": [], "sparse_vector": [], "title": "The State of the Uniform: Attacks on Encrypted Databases Beyond the Uniform Query Distribution.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recent foundational work on leakage-abuse attacks on encrypted databases has broadened our understanding of what an adversary can accomplish with a standard leakage profile. Nevertheless, all known value reconstruction attacks succeed under strong assumptions that may not hold in the real world. The most prevalent assumption is that queries are issued uniformly at random by the client. We present the first value reconstruction attacks that succeed without any knowledge about the query or data distribution. Our approach uses the search-pattern leakage, which exists in all known structured encryption schemes but has not been fully exploited so far. At the core of our method lies a support size estimator, a technique that utilizes the repetition of search tokens with the same response to estimate distances between encrypted values without any assumptions about the underlying distribution. We develop distribution-agnostic reconstruction attacks for both range queries and k-nearest-neighbor (k-NN) queries based on information extracted from the search-pattern leakage. Our new range attack follows a different algorithmic approach than state-of-the-art attacks, which are fine-tuned to succeed under the uniformly distributed queries. Instead, we reconstruct plaintext values under a variety of skewed query distributions and even outperform the accuracy of previous approaches under the uniform query distribution. Our new k-NN attack succeeds with far fewer samples than previous attacks and scales to much larger values of k. We demonstrate the effectiveness of our attacks by experimentally testing them on a wide range of query distributions and database densities, both unknown to the adversary.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00029"}, {"primary_key": "2683901", "vector": [], "sparse_vector": [], "title": "SpecCFI: Mitigating Spectre Attacks using CFI Informed Speculation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Spectre attacks and their many subsequent variants are a new vulnerability class affecting modern CPUs. The attacks rely on the ability to misguide speculative execution, generally by exploiting the branch prediction structures, to execute a vulnerable code sequence speculatively. In this paper, we propose to use Control-Flow Integrity (CFI), a security technique used to stop control-flow hijacking attacks, on the committed path, to prevent speculative control-flow from being hijacked to launch the most dangerous variants of the Spectre attacks (Spectre-BTB and Spectre-RSB). Specifically, CFI attempts to constrain the possible targets of an indirect branch to a set of legal targets defined by a pre-calculated control-flow graph (CFG). As CFI is being adopted by commodity software (e.g., Windows and Android) and commodity hardware (e.g., Intel's CET and ARM's BTI), the CFI information becomes readily available through the hardware CFI extensions. With the CFI information, we apply CFI principles to also constrain illegal control-flow during speculative execution. Specifically, our proposed defense, SpecCFI, ensures that control flow instructions target legal destinations to constrain dangerous speculation on forward control-flow paths (indirect calls and branches). We augment this protection with a precise speculation-aware hardware stack to constrain speculation on backward control-flow edges (returns). We combine this solution with existing solutions against branch target predictor attacks (Spectre-PHT) to close all known non-vendor-specific Spectre vulnerabilities. We show that SpecCFI results in small overheads both in terms of performance and additional hardware complexity.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00033"}, {"primary_key": "2683902", "vector": [], "sparse_vector": [], "title": "Breaking and (Partially) Fixing Provably Secure Onion Routing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "After several years of research on onion routing, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, in an attempt at rigorous analysis, defined an ideal functionality in the universal composability model, together with properties that protocols have to meet to achieve provable security. A whole family of systems based their security proofs on this work. However, analyzing HORNET and Sphinx, two instances from this family, we show that this proof strategy is broken. We discover a previously unknown vulnerability that breaks anonymity completely, and explain a known one. Both should not exist if privacy is proven correctly.In this work, we analyze and fix the proof strategy used for this family of systems. After proving the efficacy of the ideal functionality, we show how the original properties are flawed and suggest improved, effective properties in their place. Finally, we discover another common mistake in the proofs. We demonstrate how to avoid it by showing our improved properties for one protocol, thus partially fixing the family of provably secure onion routing protocols.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00039"}, {"primary_key": "2683903", "vector": [], "sparse_vector": [], "title": "Adversarial Machine Learning-Industry Perspectives.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Based on interviews with 28 organizations, we found that industry practitioners are not equipped with tactical and strategic tools to protect, detect and respond to attacks on their Machine Learning (ML) systems. We leverage the insights from the interviews and enumerate the gaps in securing machine learning systems when viewed in the context of traditional software security development. We write this paper from the perspective of two personas: developers/ML engineers and security incident responders. The goal of this paper is to layout the research agenda to amend the Security Development Lifecycle for industrial-grade software in the adversarial ML era.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00028"}, {"primary_key": "2683904", "vector": [], "sparse_vector": [], "title": ": Practical Cache Attacks from the Network.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Increased peripheral performance is causing strain on the memory subsystem of modern processors. For example, available DRAM throughput can no longer sustain the traffic of a modern network card. Scrambling to deliver the promised performance, instead of transferring peripheral data to and from DRAM, modern Intel processors perform I/O operations directly on the Last Level Cache (LLC). While Direct Cache Access (DCA) instead of Direct Memory Access (DMA) is a sensible performance optimization, it is unfortunately implemented without care for security, as the LLC is now shared between the CPU and all the attached devices, including the network card.In this paper, we reverse engineer the behavior of DCA, widely referred to as Data-Direct I/O (DDIO), on recent Intel processors and present its first security analysis. Based on our analysis, we present NetCAT, the first Network-based PRIME+PROBE Cache Attack on the processor's LLC of a remote machine. We show that NetCAT not only enables attacks in cooperative settings where an attacker can build a covert channel between a network client and a sandboxed server process (without network), but more worryingly, in general adversarial settings. In such settings, NetCAT can enable disclosure of network timing-based sensitive information. As an example, we show a keystroke timing attack on a victim SSH connection belonging to another client on the target server. Our results should caution processor vendors against unsupervised sharing of (additional) microarchitectural components with peripherals exposed to malicious input.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00082"}, {"primary_key": "2683905", "vector": [], "sparse_vector": [], "title": "RAMBleed: Reading Bits in Memory Without Accessing Them.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The Rowhammer bug is a reliability issue in DRAM cells that can enable an unprivileged adversary to flip the values of bits in neighboring rows on the memory module. Previous work has exploited this for various types of fault attacks across security boundaries, where the attacker flips inaccessible bits, often resulting in privilege escalation. It is widely assumed however, that bit flips within the adversary's own private memory have no security implications, as the attacker can already modify its private memory via regular write operations.We demonstrate that this assumption is incorrect, by employing Rowhammer as a read side channel. More specifically, we show how an unprivileged attacker can exploit the data dependence between Rowhammer induced bit flips and the bits in nearby rows to deduce these bits, including values belonging to other processes and the kernel. Thus, the primary contribution of this work is to show that Rowhammer is a threat to not only integrity, but to confidentiality as well.Furthermore, in contrast to Rowhammer write side channels, which require persistent bit flips, our read channel succeeds even when ECC memory detects and corrects every bit flip. Thus, we demonstrate the first security implication of successfully-corrected bit flips, which were previously considered benign.To demonstrate the implications of this read side channel, we present an end-to-end attack on OpenSSH 7.9 that extracts an RSA-2048 key from the root level SSH daemon. To accomplish this, we develop novel techniques for massaging memory from user space into an exploitable state, and use the DRAM rowbuffer timing side channel to locate physically contiguous memory necessary for double-sided Rowhammering. Unlike previous Rowhammer attacks, our attack does not require the use of huge pages, and it works on Ubuntu Linux under its default configuration settings.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00020"}, {"primary_key": "2683906", "vector": [], "sparse_vector": [], "title": "Learning from Context: A Multi-View Deep Learning Architecture for Malware Detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Machine learning (ML) classifiers used for malware detection typically employ numerical representations of the content of each file when making malicious/benign determinations. However, there is also relevant information that can be gleaned from the context in which the file was seen which is often ignored. One source of contextual information is the file's location on disk. For example, a malicious file masquerading as a known benign file (e.g., a Windows system DLL) is more likely to appear suspicious if the detector can intelligibly utilize information about the path at which it resides. Knowledge of the file path information could also make it easier to detect files which try to evade disk scans by placing themselves in specific locations. File paths are also available with little overhead and can seamlessly be integrated into a multi-view static ML detector, potentially yielding higher detection rates at very high throughput and minimal infrastructural changes. In this work, we propose a multi-view deep neural network architecture, which takes feature vectors from the PE file content as well as corresponding file paths as inputs and outputs a detection score. We perform an evaluation on a commercial-scale dataset of approximately 10 million samples - files and file paths from user endpoints serviced by an actual security vendor. We then conduct an interpretability analysis via LIME modeling to ensure that our classifier has learned a sensible representation and examine how the file path contributes to change in the classifier's score in different cases. We find that our model learns useful aspects of the file path for classification, resulting in a 26.6% improvement in the true positive rate at a 0.001 false positive rate (FPR) and a 64.6% improvement at 0.0001 FPR, compared to a model that operates on PE file content only.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00018"}, {"primary_key": "2683907", "vector": [], "sparse_vector": [], "title": "Replicated state machines without replicated execution.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper introduces a new approach to reduce end-to-end costs in large-scale replicated systems built under a Byzantine fault model. Specifically, our approach transforms a given replicated state machine (RSM) to another RSM where nodes incur lower costs by delegating state machine execution: an untrusted prover produces succinct cryptographic proofs of correct state transitions along with state changes, which nodes in the transformed RSM verify and apply respectively.To realize our approach, we build Piperine, a system that makes the proof machinery profitable in the context of RSMs. Specifically, <PERSON><PERSON> reduces the costs of both proving and verifying the correctness of state machine execution while retaining liveness-a distinctive requirement in the context of RSMs. Our experimental evaluation demonstrates that, for a payment service, employing Piper<PERSON> is more profitable than naive reexecution of transactions as long as there are > 10 4 nodes. When we apply Piperine to ERC-20 transactions in Ethereum (a real-world RSM with up to 10 5 nodes), it reduces per-transaction costs by 5.4× and network costs by 2.7×.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00068"}, {"primary_key": "2683908", "vector": [], "sparse_vector": [], "title": "Message Time of Arrival Codes: A Fundamental Primitive for Secure Distance Measurement.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Secure distance measurement and therefore secure Time-of-Arrival (ToA) measurement is critical for applications such as contactless payments, passive-keyless entry and start systems, and navigation systems. This paper initiates the study of Message Time of Arrival Codes (MTACs) and their security. MTACs represent a core primitive in the construction of systems for secure ToA measurement. By surfacing MTACs in this way, we are able for the first time to formally define the security requirements of physical-layer measures that protect ToA measurement systems against attacks. Our viewpoint also enables us to provide a unified presentation of existing MTACs (such as those proposed in distance-bounding protocols and in a secure distance measurement standard) and to propose basic principles for protecting ToA measurement systems against attacks that remain unaddressed by existing mechanisms. We also use our perspective to systematically explore the tradeoffs between security and performance that apply to all signal modulation techniques enabling ToA measurements.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00010"}, {"primary_key": "2683909", "vector": [], "sparse_vector": [], "title": "WaveSpy: Remote and Through-wall Screen Attack via mmWave Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Baicheng Chen", "Lu <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Digital screens, such as liquid crystal displays (LCDs), are vulnerable to attacks (e.g., \"shoulder surfing\") that can bypass security protection services (e.g., firewall) to steal confidential information from intended victims. The conventional practice to mitigate these threats is isolation. An isolated zone, without accessibility, proximity, and line-of-sight, seems to bring personal devices to a truly secure place.In this paper, we revisit this historical topic and re-examine the security risk of screen attacks in an isolation scenario mentioned above. Specifically, we identify and validate a new and practical side-channel attack for screen content via liquid crystal nematic state estimation using a low-cost radio-frequency sensor. By leveraging the relationship between the screen content and the states of liquid crystal arrays in displays, we develop WaveSpy, an end-to-end portable through-wall screen attack system. WaveSpy comprises a low-cost, energy-efficient and light-weight millimeter-wave (mmWave) probe which can remotely collect the liquid crystal state response to a set of mmWave stimuli and facilitate screen content inference, even when the victim's screen is placed in an isolated zone. We intensively evaluate the performance and practicality of WaveSpy in screen attacks, including over 100 different types of content on 30 digital screens of modern electronic devices. WaveSpy achieves an accuracy of 99% in screen content type recognition and a success rate of 87.77% in Top-3 sensitive information retrieval under real-world scenarios, respectively. Furthermore, we discuss several potential defense mechanisms to mitigate screen eavesdropping similar to WaveSpy.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00004"}, {"primary_key": "2683910", "vector": [], "sparse_vector": [], "title": "On the Robustness of Cooperative Multi-Agent Reinforcement Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In cooperative multi-agent reinforcement learning (c-MARL), agents learn to cooperatively take actions as a team to maximize a total team reward. We analyze the robustness of c-MARL to adversaries capable of attacking one of the agents on a team. Through the ability to manipulate this agent's observations, the adversary seeks to decrease the total team reward. Attacking c-MARL is challenging for three reasons: first, it is difficult to estimate team rewards or how they are impacted by an agent mispredicting; second, models are non-differentiable; and third, the feature space is low-dimensional. Thus, we introduce a novel attack. The attacker first trains a policy network with reinforcement learning to find a wrong action it should encourage the victim agent to take. Then, the adversary uses targeted adversarial examples to force the victim to take this action. Our results on the StartCraft II multi-agent benchmark demonstrate that c-MARL teams are highly vulnerable to perturbations applied to one of their agent's observations. By attacking a single agent, our attack method has highly negative impact on the overall team reward, reducing it from 20 to 9.4. This results in the team's winning rate to go down from 98.9% to 0%.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00027"}, {"primary_key": "2683911", "vector": [], "sparse_vector": [], "title": "Case Study: Safety Verification of an Unmanned Underwater Vehicle.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Hoang-Dung Tran", "<PERSON>"], "summary": "This manuscript evaluates the safety of a neural network controller that seeks to ensure that an Unmanned Underwater Vehicle (UUV) does not collide with a static object in its path. To achieve this, we utilize methods that can determine the exact output reachable set of all the UUV's components through the use of star-sets. The star-set is a computationally efficient set representation adept at characterizing large input spaces. It supports cheap and efficient computation of affine mapping operations and intersections with half-spaces. The system under consideration in this work represents a more complex system than Neural Network Control Systems (NNCS) previously considered in other works, and consists of a total of four components. Our experimental evaluation uses four different scenarios to show that our star-set based methods are scalable and can be efficiently used to analyze the safety of real-world cyber-physical systems (CPS).", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00047"}, {"primary_key": "2683912", "vector": [], "sparse_vector": [], "title": "Is FIDO2 the Kingslayer of User Authentication? A Comparative Usability Study of FIDO2 Passwordless Authentication.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The newest contender for succeeding passwords as the incumbent web authentication scheme is the FIDO2 standard. Jointly developed and backed by the FIDO Alliance and the W3C, FIDO2 has found support in virtually every browser, finds increasing support by service providers, and has adoptions beyond browser-software on its way. While it supports MFA and 2FA, its single-factor, passwordless authentication with security tokens has received the bulk of attention and was hailed by its supporters and the media as the solution that will replace text-passwords on the web. Despite its obvious security and deployability benefits—a setting that no prior solution had in this strong combination—the paradigm shift from a familiar knowledge factor to purely a possession factor raises questions about the acceptance of passwordless authentication by end-users.This paper presents the first large-scale lab study of FIDO2 single-factor authentication to collect insights about end-users' perception, acceptance, and concerns about passwordless authentication. Through hands-on tasks our participants gather first-hand experience with passwordless authentication using a security key, which they afterwards reflect on in a survey. Our results show that users are willing to accept a direct replacement of text-based passwords with a security key for single-factor authentication. That is an encouraging result in the quest to replace passwords. But, our results also identify new concerns that can potentially hinder the widespread adoption of FIDO2 passwordless authentication. In order to mitigate these factors, we derive concrete recommendations to try to help in the ongoing proliferation of passwordless authentication on the web.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00047"}, {"primary_key": "2683913", "vector": [], "sparse_vector": [], "title": "Using Taint Analysis and Reinforcement Learning (TARL) to Repair Autonomous Robot Software.", "authors": ["<PERSON>", "Saba B. Zahra"], "summary": "It is important to be able to establish formal performance bounds for autonomous systems. However, formal verification techniques require a model of the environment in which the system operates; a challenge for autonomous systems, especially those expected to operate over longer timescales. This paper describes work in progress to automate the monitor and repair of ROS-based autonomous robot software written for an apriori partially known and possibly incorrect environment model. A taint analysis method is used to automatically extract the dataflow sequence from input topic to publish topic, and instrument that code. A unique reinforcement learning approximation of MDP utility is calculated, an empirical and non-invasive characterization of the inherent objectives of the software designers. By comparing design (a-priori) utility with deploy (deployed system) utility, we show, using a small but real ROS example, that it's possible to monitor a performance criterion and relate violations of the criterion to parts of the software. The software is then patched using automated software repair techniques and evaluated against the original off-line utility.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00045"}, {"primary_key": "2683914", "vector": [], "sparse_vector": [], "title": "SPIDER: Enabling Fast Patch Propagation In Related Software Repositories.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Despite the effort of software maintainers, patches to open-source repositories are propagated from the main codebase to all the related projects (e.g., forks) with a significant delay. Previous work shows that this is true also for security patches, which represents a critical problem. Vulnerability databases, such as the CVE database, were born to speed-up the application of critical patches; however, patches associated with CVE entries (i.e., CVE patches) are still applied with a delay, and some security fixes lack the corresponding CVE entries. Because of this, project maintainers could miss security patches when upgrading software.In this paper, we are the first to define safe patches (sps). An sp is a patch that does not disrupt the intended functionality of the program (on valid inputs), meaning that it can be applied with no testing; we argue that most security fixes fall into this category. Furthermore, we show a technique to identify sps, and implement SPIDER 1 , a tool based on such a technique that works by analyzing the source code of the original and patched versions of a file. We performed a large-scale evaluation on 341,767 patches from 32 large and popular source code repositories as well as on 809 CVE patches. Results show that SPIDER was able to identify 67,408 sps and that most of the CVE patches are sps. In addition, SPIDER identified 2,278 patches that fix vulnerabilities lacking a CVE; 229 of these are still unpatched in different vendor kernels, which can be considered as potential unfixed vulnerabilities.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00038"}, {"primary_key": "2683915", "vector": [], "sparse_vector": [], "title": "Towards a Natural Perspective of Smart Homes for Practical Security and Safety Analyses.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Designing practical security systems for the smart home is challenging without the knowledge of realistic home usage. This paper describes the design and implementation of Hεlion, a framework that generates natural home automation scenarios by identifying the regularities in user-driven home automation sequences, which are in turn generated from routines created by end-users. Our key hypothesis is that smart home event sequences created by users exhibit inherent semantic patterns, or naturalness that can be modeled and used to generate valid and useful scenarios. To evaluate our approach, we first empirically demonstrate that this naturalness hypothesis holds, with a corpus of 30,518 home automation events, constructed from 273 routines collected from 40 users. We then demonstrate that the scenarios generated by Hεlion seem valid to end-users, through two studies with 16 external evaluators. We further demonstrate the usefulness of Hεlion's scenarios by addressing the challenge of policy specification, and using Hεlion to generate 17 security/safety policies with minimal effort. We distill 16 key findings from our results that demonstrate the strengths of our approach, surprising aspects of home automation, as well as challenges and opportunities in this rapidly growing domain.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00062"}, {"primary_key": "2683916", "vector": [], "sparse_vector": [], "title": "This PIN Can Be Easily Guessed: Analyzing the Security of Smartphone Unlock PINs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We provide the first comprehensive study of user-chosen 4- and 6-digit PINs (n = 1220) collected on smartphones with participants being explicitly primed for device unlocking. We find that against a throttled attacker (with 10, 30, or 100 guesses, matching the smartphone unlock setting), using 6-digit PINs instead of 4-digit PINs provides little to no increase in security, and surprisingly may even decrease security. We also study the effects of blacklists, where a set of \"easy to guess\" PINs is disallowed during selection. Two such blacklists are in use today by iOS, for 4-digits (274 PINs) as well as 6-digits (2910 PINs). We extracted both blacklists compared them with four other blacklists, including a small 4-digit (27 PINs), a large 4-digit (2740 PINs), and two placebo blacklists for 4- and 6-digit PINs that always excluded the first-choice PIN. We find that relatively small blacklists in use today by iOS offer little or no benefit against a throttled guessing attack. Security gains are only observed when the blacklists are much larger, which in turn comes at the cost of increased user frustration. Our analysis suggests that a blacklist at about 10 % of the PIN space may provide the best balance between usability and security.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00100"}, {"primary_key": "2683917", "vector": [], "sparse_vector": [], "title": "Do <PERSON>ie Banners Respect my Choice? : Measuring Legal Compliance of Banners from IAB Europe&apos;s Transparency and Consent Framework.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As a result of the GDPR and the ePrivacy Directive, European users encounter cookie banners on almost every website. Many of such banners are implemented by Consent Management Providers (CMPs), who respect IAB Europe's Transparency and Consent Framework (TCF). Via cookie banners, CMPs collect and disseminate user consent to third parties. In this work, we systematically study IAB Europe's TCF and analyze consent stored behind the user interface of TCF cookie banners. We analyze the GDPR and the ePrivacy Directive to identify potential legal violations in implementations of cookie banners based on the storage of consent and detect such suspected violations by crawling 1 426 websites that contains TCF banners, found among 28 257 crawled European websites. With two automatic and semi-automatic crawl campaigns, we detect suspected violations, and we find that: 141 websites register positive consent even if the user has not made their choice; 236 websites nudge the users towards accepting consent by pre-selecting options; and 27 websites store a positive consent even if the user has explicitly opted out. Performing extensive tests on 560 websites, we find at least one suspected violation in 54% of them. Finally, we provide a browser extension to facilitate manual detection of suspected violations for regular users and Data Protection Authorities.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00076"}, {"primary_key": "2683918", "vector": [], "sparse_vector": [], "title": "Security Analysis of Networked 3D Printers.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Networked 3D printers are an emerging trend in manufacturing. However, many have poor security controls, allowing attackers to cause physical hazards, create defective safety-critical parts, steal proprietary data, and halt costly operations. Prior work has given limited attention to identifying if a network attacker is able to achieve these goals. In this work, we present C3PO, an open-source network security analysis tool that systematically identifies security threats to networked 3D printers. C3PO's design is guided by industry standards and best practices, identifying potential vulnerabilities in data transfer, the printing application, availability, and exposed network services. Furthermore, C3PO analyzes how a network deployment impacts a 3D printer's security, such as an attacker compromising an IoT camera in order to send malicious commands to a networked 3D printer. We use C3PO to analyze 13 networked 3D printers and 5 real-world manufacturing network deployments. We identified 8 types of network security vulnerabilities such as a susceptibility to low-rate denial of service attacks, the transmission of unencrypted data, and publicly accessible network deployments.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00035"}, {"primary_key": "2683919", "vector": [], "sparse_vector": [], "title": "On-Chip Randomization for Memory Protection Against Hardware Supply Chain Attacks to DRAM.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Dynamic Random Access Memory (DRAM) is widely used for data storage and, when a computer system is in operation, the DRAM can contain sensitive information such as passwords and cryptographic keys. Therefore, the DRAM is a prime target for hardware-based cryptanalytic attacks. These attacks can be performed in the supply chain to capture default key mechanisms enabling a later cyber attack or predisposition the system to remote effects. Two prominent attack classes against memory are the Cold Boot attack which recovers the data from the DRAM even after a supposed power-down and Rowhammer attack which violates memory integrity by influencing the stored bits to flip. In this paper, we propose an on-chip technique that obfuscates the memory addresses and data and provides a fast detect-response to defend against these hardware-based security attacks on DRAM. We advance the prior hardware security research by making two contributions. First, the key material is detected and erased before the Cold Boot attacker can extract the memory data. Second, our solution is on-chip and does not require nor depend on additional hardware or software which are open to additional supply chain attack vectors. We analyze the efficacy of our scheme through circuit simulation and compare the results to the previous mitigation approaches based on DRAM write operations. Our simulation and analysis results show that purging key information used for address and data randomization can be achieved much faster and with lower power than with typical DRAM write techniques used for sanitizing memory content. We demonstrate through circuit simulation of the key register design a technique that clears key information within 2.4ns which is faster by more than two orders magnitude compared to typical DRAM write operations for 180nm technology, and with a power consumption of 0.15 picoWatts.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00044"}, {"primary_key": "2683920", "vector": [], "sparse_vector": [], "title": "Security Update Labels: Establishing Economic Incentives for Security Patching of IoT Consumer Products.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the expansion of the Internet of Things (IoT), the number of security incidents due to insecure and misconfigured IoT devices is increasing. Especially on the consumer market, manufacturers focus on new features and early releases at the expense of a comprehensive security strategy. Hence, experts have started calling for regulation of the IoT consumer market, while policymakers are seeking for suitable regulatory approaches. We investigate how manufacturers can be incentivized to increase sustainable security efforts for IoT products. We propose mandatory security update labels that inform consumers during buying decisions about the willingness of the manufacturer to provide security updates in the future. Mandatory means that the labels explicitly state when security updates are not guaranteed. We conducted a user study with more than 1,400 participants to assess the importance of security update labels for the consumer choice by means of a conjoint analysis. The results show that the availability of security updates (until which date the updates are guaranteed) accounts for 8% to 35% impact on overall consumers' choice, depending on the perceived security risk of the product category. For products with a high perceived security risk, this availability is twice as important as other high-ranked product attributes. Moreover, provisioning time for security updates (how quickly the product will be patched after a vulnerability is discovered) additionally accounts for 7% to 25% impact on consumers' choices. The proposed labels are intuitively understood by consumers, do not require product assessments by third parties before release, and have a potential to incentivize manufacturers to provide sustainable security support.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00021"}, {"primary_key": "2683921", "vector": [], "sparse_vector": [], "title": "Research Report: The Parsley Data Format Definition Language.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Any program that reads formatted input relies on parsing software to check the input for validity and transform it into a representation suitable for further processing. Many security vulnerabilities can be attributed to poorly defined grammars, incorrect parsing, and sloppy input validation. In contrast to programming languages, grammars for even common data formats such as ELF and PDF are typically context-sensitive and heterogenous. However, as in programming languages, a standard notation or language to express these data format grammars can address poor or ambiguous definitions, and the automated generation of correct-by-construction parsers from such grammar specifications can yield correct and type- and memory-safe data parsing routines. We present our ongoing work on developing such a data format description language. <PERSON><PERSON>ley is a declarative data format definition language that combines grammars and constraints in a modular way. We show how it can be used to capture data formats such as MAVLink, PDF and ELF. We briefly describe the processing pipeline we are designing to generate verified parsers from these specifications.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00064"}, {"primary_key": "2683922", "vector": [], "sparse_vector": [], "title": "Plundervolt: Software-based Fault Injection Attacks against Intel SGX.", "authors": ["<PERSON>", "<PERSON>", "Flavio D<PERSON> Garcia", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Dynamic frequency and voltage scaling features have been introduced to manage ever-growing heat and power consumption in modern processors. Design restrictions ensure frequency and voltage are adjusted as a pair, based on the current load, because for each frequency there is only a certain voltage range where the processor can operate correctly. For this purpose, many processors (including the widespread Intel Core series) expose privileged software interfaces to dynamically regulate processor frequency and operating voltage.In this paper, we demonstrate that these privileged interfaces can be reliably exploited to undermine the system's security. We present the Plundervolt attack, in which a privileged software adversary abuses an undocumented Intel Core voltage scaling interface to corrupt the integrity of Intel SGX enclave computations. Plundervolt carefully controls the processor's supply voltage during an enclave computation, inducing predictable faults within the processor package. Consequently, even Intel SGX's memory encryption/authentication technology cannot protect against Plundervolt. In multiple case studies, we show how the induced faults in enclave computations can be leveraged in real-world attacks to recover keys from cryptographic algorithms (including the AES-NI instruction set extension) or to induce memory safety vulnerabilities into bug-free enclave code. We finally discuss why mitigating Plundervolt is not trivial, requiring trusted computing base recovery through microcode updates or hardware changes.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00057"}, {"primary_key": "2683923", "vector": [], "sparse_vector": [], "title": "Ask the Experts: What Should Be on an IoT Privacy and Security Label?", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Information about the privacy and security of Internet of Things (IoT) devices is not readily available to consumers who want to consider it before making purchase decisions. While legislators have proposed adding succinct, consumer accessible, labels, they do not provide guidance on the content of these labels. In this paper, we report on the results of a series of interviews and surveys with privacy and security experts, as well as consumers, where we explore and test the design space of the content to include on an IoT privacy and security label. We conduct an expert elicitation study by following a three-round Delphi process with 22 privacy and security experts to identify the factors that experts believed are important for consumers when comparing the privacy and security of IoT devices to inform their purchase decisions. Based on how critical experts believed each factor is in conveying risk to consumers, we distributed these factors across two layers-a primary layer to display on the product package itself or prominently on a website, and a secondary layer available online through a web link or a QR code. We report on the experts' rationale and arguments used to support their choice of factors. Moreover, to study how consumers would perceive the privacy and security information specified by experts, we conducted a series of semi-structured interviews with 15 participants, who had purchased at least one IoT device (smart home device or wearable). Based on the results of our expert elicitation and consumer studies, we propose a prototype privacy and security label to help consumers make more informed IoT-related purchase decisions.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00043"}, {"primary_key": "2683924", "vector": [], "sparse_vector": [], "title": "ICLab: A Global, Longitudinal Internet Censorship Measurement Platform.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Cho", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Researchers have studied Internet censorship for nearly as long as attempts to censor contents have taken place. Most studies have however been limited to a short period of time and / or a few countries; the few exceptions have traded off detail for breadth of coverage. Collecting enough data for a comprehensive, global, longitudinal perspective remains challenging.In this work, we present ICLab, an Internet measurement platform specialized for censorship research. It achieves a new balance between breadth of coverage and detail of measurements, by using commercial VPNs as vantage points distributed around the world. ICLab has been operated continuously since late 2016. It can currently detect DNS manipulation and TCP packet injection, and overt \"block pages\" however they are delivered. ICLab records and archives raw observations in detail, making retrospective analysis with new techniques possible. At every stage of processing, ICLab seeks to minimize false positives and manual validation.Within 53,906,532 measurements of individual web pages, collected by ICLab in 2017 and 2018, we observe blocking of 3,602 unique URLs in 60 countries. Using this data, we compare how different blocking techniques are deployed in different regions and/or against different types of content. Our longitudinal monitoring pinpoints changes in censorship in India and Turkey concurrent with political shifts, and our clustering techniques discover 48 previously unknown block pages. ICLab's broad and detailed measurements also expose other forms of network interference, such as surveillance and malware injection.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00014"}, {"primary_key": "2683925", "vector": [], "sparse_vector": [], "title": "Rigorous engineering for hardware security: Formal modelling and proof in the CHERI design and implementation process.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The root causes of many security vulnerabilities include a pernicious combination of two problems, often regarded as inescapable aspects of computing. First, the protection mechanisms provided by the mainstream processor architecture and C/C++ language abstractions, dating back to the 1970s and before, provide only coarse-grain virtual-memory-based protection. Second, mainstream system engineering relies almost exclusively on test-and-debug methods, with (at best) prose specifications. These methods have historically sufficed commercially for much of the computer industry, but they fail to prevent large numbers of exploitable bugs, and the security problems that this causes are becoming ever more acute.In this paper we show how more rigorous engineering methods can be applied to the development of a new security-enhanced processor architecture, with its accompanying hardware implementation and software stack. We use formal models of the complete instruction-set architecture (ISA) at the heart of the design and engineering process, both in lightweight ways that support and improve normal engineering practice - as documentation, in emulators used as a test oracle for hardware and for running software, and for test generation - and for formal verification. We formalise key intended security properties of the design, and establish that these hold with mechanised proof. This is for the same complete ISA models (complete enough to boot operating systems), without idealisation.We do this for CHERI, an architecture with hardware capabilities that supports fine-grained memory protection and scalable secure compartmentalisation, while offering a smooth adoption path for existing software. CHERI is a maturing research architecture, developed since 2010, with work now underway on an Arm industrial prototype to explore its possible adoption in mass-market commercial processors. The rigorous engineering work described here has been an integral part of its development to date, enabling more rapid and confident experimentation, and boosting confidence in the design.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00055"}, {"primary_key": "2683926", "vector": [], "sparse_vector": [], "title": "Privacy Risks of General-Purpose Language Models.", "authors": ["Xudong Pan", "<PERSON>", "Shouling Ji", "<PERSON>"], "summary": "Recently, a new paradigm of building general-purpose language models (e.g., Google's Bert and OpenAI's GPT-2) in Natural Language Processing (NLP) for text feature extraction, a standard procedure in NLP systems that converts texts to vectors (i.e., embeddings) for downstream modeling, has arisen and starts to find its application in various downstream NLP tasks and real world systems (e.g., Google's search engine [6]). To obtain general-purpose text embeddings, these language models have highly complicated architectures with millions of learnable parameters and are usually pretrained on billions of sentences before being utilized. As is widely recognized, such a practice indeed improves the state-of-the-art performance of many downstream NLP tasks. However, the improved utility is not for free. We find the text embeddings from general-purpose language models would capture much sensitive information from the plain text. Once being accessed by the adversary, the embeddings can be reverse-engineered to disclose sensitive information of the victims for further harassment. Although such a privacy risk can impose a real threat to the future leverage of these promising NLP tools, there are neither published attacks nor systematic evaluations by far for the mainstream industry-level language models. To bridge this gap, we present the first systematic study on the privacy risks of 8 state-of-the-art language models with 4 diverse case studies. By constructing 2 novel attack classes, our study demonstrates the aforementioned privacy risks do exist and can impose practical threats to the application of general-purpose language models on sensitive data covering identity, genome, healthcare and location. For example, we show the adversary with nearly no prior knowledge can achieve about 75% accuracy when inferring the precise disease site from <PERSON> embeddings of patients' medical descriptions. As possible countermeasures, we propose 4 different defenses (via rounding, differential privacy, adversarial training and subspace projection) to obfuscate the unprotected embeddings for mitigation purpose. With extensive evaluations, we also provide a preliminary analysis on the utility-privacy trade-off brought by each defense, which we hope may foster future mitigation researches.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00095"}, {"primary_key": "2683927", "vector": [], "sparse_vector": [], "title": "Fuzzing JavaScript Engines with Aspect-preserving Mutation.", "authors": ["Soyeon Park", "<PERSON>", "Insu Yun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Fuzzing is a practical, widely-deployed technique to find bugs in complex, real-world programs like JavaScript engines. We observed, however, that existing fuzzing approaches, either generative or mutational, fall short in fully harvesting high-quality input corpora such as known proof of concept (PoC) exploits or unit tests. Existing fuzzers tend to destruct subtle semantics or conditions encoded in the input corpus in order to generate new test cases because this approach helps in discovering new code paths of the program. Nevertheless, for JavaScript-like complex programs, such a conventional design leads to test cases that tackle only shallow parts of the complex codebase and fails to reach deep bugs effectively due to the huge input space.In this paper, we advocate a new technique, called an aspect-preserving mutation, that stochastically preserves the desirable properties, called aspects, that we prefer to be maintained across mutation. We demonstrate the aspect preservation with two mutation strategies, namely, structure and type preservation, in our fully-fledged JavaScript fuzzer, called Die. Our evaluation shows that <PERSON>'s aspect-preserving mutation is more effective in discovering new bugs (5.7× more unique crashes) and producing valid test cases (2.4× fewer runtime errors) than the state-of-the-art JavaScript fuzzers. <PERSON> newly discovered 48 high-impact bugs in ChakraCore, JavaScriptCore, and V8 (38 fixed with 12 CVEs assigned as of today). The source code of <PERSON> is publicly available as an open-source project. 1", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00067"}, {"primary_key": "2683928", "vector": [], "sparse_vector": [], "title": "A Tale of Sea and Sky On the Security of Maritime VSAT Communications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Very Small Aperture Terminals (VSAT) have revolutionized maritime operations. However, the security dimensions of maritime VSAT services are not well understood. Historically, high equipment costs have acted as a barrier to entry for both researchers and attackers. In this paper we demonstrate a substantial change in threat model, proving practical attacks against maritime VSAT networks with less than $400 of widely-available television equipment. This is achieved through GSExtract, a purpose-built forensic tool which enables the extraction of IP traffic from highly corrupted VSAT data streams.The implications of this threat are assessed experimentally through the analysis of more than 1.3 TB of real-world maritime VSAT recordings encompassing 26 million square kilometers of coverage area. The underlying network platform employed in these systems is representative of more than 60% of the global maritime VSAT services market. We find that sensitive data belonging to some of the world's largest maritime companies is regularly leaked over VSAT ship-to-shore communications. This threat is contextualized through illustrative case studies ranging from the interception and alteration of navigational charts to theft of passport and credit card details. Beyond this, we demonstrate the ability to arbitrarily intercept and modify TCP sessions under certain network configurations, enabling man-in-the-middle and denial of service attacks against ships at sea. The paper concludes with a brief discussion of the unique requirements and challenges for encryption in VSAT environments.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00056"}, {"primary_key": "2683929", "vector": [], "sparse_vector": [], "title": "VerX: Safety Verification of Smart Contracts.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON>", "<PERSON>"], "summary": "We present VerX, the first automated verifier able to prove functional properties of Ethereum smart contracts. VerX addresses an important problem as all real-world contracts must satisfy custom functional specifications.VerX is based on a careful combination of three techniques, enabling it to automatically verify temporal properties of infinite- state smart contracts: (i) reduction of temporal property verification to reachability checking, (ii) a new symbolic execution engine for the Ethereum Virtual Machine that is precise and efficient for a practical fragment of Ethereum contracts, and (iii) delayed predicate abstraction which uses symbolic execution during transactions and abstraction at transaction boundaries.Our extensive experimental evaluation on 83 temporal properties and 12 real-world projects, including popular crowdsales and libraries, demonstrates that VerX is practically effective.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00024"}, {"primary_key": "2683930", "vector": [], "sparse_vector": [], "title": "Intriguing Properties of Adversarial ML Attacks in the Problem Space.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recent research efforts on adversarial ML have investigated problem-space attacks, focusing on the generation of real evasive objects in domains where, unlike images, there is no clear inverse mapping to the feature space (e.g., software). However, the design, comparison, and real-world implications of problem-space attacks remain underexplored.This paper makes two major contributions. First, we propose a novel formalization for adversarial ML evasion attacks in the problem-space, which includes the definition of a comprehensive set of constraints on available transformations, preserved semantics, robustness to preprocessing, and plausibility. We shed light on the relationship between feature space and problem space, and we introduce the concept of side-effect features as the byproduct of the inverse feature-mapping problem. This enables us to define and prove necessary and sufficient conditions for the existence of problem-space attacks. We further demonstrate the expressive power of our formalization by using it to describe several attacks from related literature across different domains.Second, building on our formalization, we propose a novel problem-space attack on Android malware that overcomes past limitations. Experiments on a dataset with 170K Android apps from 2017 and 2018 show the practical feasibility of evading a state-of-the-art malware classifier along with its hardened version. Our results demonstrate that \"adversarial-malware as a service\" is a realistic threat, as we automatically generate thousands of realistic and inconspicuous adversarial applications at scale, where on average it takes only a few minutes to generate an adversarial app. Yet, out of the 1600+ papers on adversarial ML published in the past six years, roughly 40 focus on malware [15]-and many remain only in the feature space.Our formalization of problem-space attacks paves the way to more principled research in this domain. We responsibly release the code and dataset of our novel attack to other researchers, to encourage future work on defenses in the problem space.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00073"}, {"primary_key": "2683931", "vector": [], "sparse_vector": [], "title": "Research Report: Formally-Verified ASN.1 Protocol C-language Stack.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We describe our approach and progress in verification of a mature open-source ASN.1 compiler, ASN1C, using the Coq proof assistant. Once completed, our project will provide state-of-the-art high assurance suitable for mission-critical systems. Furthermore, since formal verification will be layered atop a well-tested ASN.1 stack, it will combine the benefits of high-performance portable stack implementation with formal correctness guarantees. As an essential step in our approach, the project will also provide a formalization of a key part of the ASN.1 standard. Such formal specification could subsequently be used by others to analyze ASN.1 properties and validate other implementations.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00065"}, {"primary_key": "2683932", "vector": [], "sparse_vector": [], "title": "xMP: Selective Memory Protection for Kernel and User Space.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Attackers leverage memory corruption vulnerabilities to establish primitives for reading from or writing to the address space of a vulnerable process. These primitives form the foundation for code-reuse and data-oriented attacks. While various defenses against the former class of attacks have proven effective, mitigation of the latter remains an open problem. In this paper, we identify various shortcomings of the x86 architecture regarding memory isolation, and leverage virtualization to build an effective defense against data-oriented attacks. Our approach, called xMP, provides (in-guest) selective memory protection primitives that allow VMs to isolate sensitive data in user or kernel space in disjoint xMP domains. We interface the Xen altp2m subsystem with the Linux memory management system, lending VMs the flexibility to define custom policies. Contrary to conventional approaches, xMP takes advantage of virtualization extensions, but after initialization, it does not require any hypervisor intervention. To ensure the integrity of in-kernel management information and pointers to sensitive data within isolated domains, xMP protects pointers with HMACs bound to an immutable context, so that integrity validation succeeds only in the right context. We have applied xMP to protect the page tables and process credentials of the Linux kernel, as well as sensitive data in various user-space applications. Overall, our evaluation shows that xMP introduces minimal overhead for real-world workloads and applications, and offers effective protection against data-oriented attacks.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00041"}, {"primary_key": "2683933", "vector": [], "sparse_vector": [], "title": "EverCrypt: A Fast, Verified, Cross-Platform Cryptographic Provider.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Santiago Zanella-Béguelin"], "summary": "We present EverCrypt: a comprehensive collection of verified, high-performance cryptographic functionalities available via a carefully designed API. The API provably supports agility (choosing between multiple algorithms for the same functionality) and multiplexing (choosing between multiple implementations of the same algorithm). Through abstraction and zero-cost generic programming, we show how agility can simplify verification without sacrificing performance, and we demonstrate how C and assembly can be composed and verified against shared specifications. We substantiate the effectiveness of these techniques with new verified implementations (including hashes, Curve25519, and AES-GCM) whose performance matches or exceeds the best unverified implementations. We validate the API design with two high-performance verified case studies built atop EverCrypt, resulting in line-rate performance for a secure network protocol and a Merkle-tree library, used in a production blockchain, that supports 2.7 million insertions/sec. Altogether, EverCrypt consists of over 124K verified lines of specs, code, and proofs, and it produces over 29K lines of C and 14K lines of assembly code.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00114"}, {"primary_key": "2683934", "vector": [], "sparse_vector": [], "title": "Ex-vivo dynamic analysis framework for Android device drivers.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The ability to execute and analyze code makes many security tasks such as exploit development, reverse engineering, and vulnerability detection much easier. However, on embedded devices such as Android smartphones, executing code in-vivo, on the device, for analysis is limited by the need to acquire such devices, the speed of the device, and in some cases the need to flash custom code onto the devices. The other option is to execute the code ex-vivo, off the device, but this approach either requires porting or complex hardware emulation. In this paper, we take advantage of the observation that many execution paths in drivers are only superficially dependent on both the hardware and kernel on which the driver executes, to create an ex-vivo dynamic driver analysis framework for Android devices that requires neither porting nor emulation. We achieve this by developing a generic evasion framework that enables driver initialization by evading hardware and kernel dependencies instead of precisely emulating them, and then developing a novel Ex-vivo AnalySIs framEwoRk (EASIER) that enables off-device analysis with the initialized driver state. Compared to on-device analysis, our approach enables the use of userspace tools and scales with the number of available commodity CPU's, not the number of smartphones. We demonstrate the usefulness of our framework by targeting privilege escalation vulnerabilities in system call handlers in platform device drivers. We find it can load 48/62 (77%) drivers from three different Android kernels: MSM, Xiaomi, and Huawei. We then confirm that it is able to reach and detect 21 known vulnerabilities. Finally, we have discovered 12 new bugs which we have reported and confirmed.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00094"}, {"primary_key": "2683935", "vector": [], "sparse_vector": [], "title": "Backdooring and Poisoning Neural Networks with Image-Scaling Attacks.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Backdoors and poisoning attacks are a major threat to the security of machine-learning and vision systems. Often, however, these attacks leave visible artifacts in the images that can be visually detected and weaken the efficacy of the attacks. In this paper, we propose a novel strategy for hiding backdoor and poisoning attacks. Our approach builds on a recent class of attacks against image scaling. These attacks enable manipulating images such that they change their content when scaled to a specific resolution. By combining poisoning and image-scaling attacks, we can conceal the trigger of backdoors as well as hide the overlays of clean-label poisoning. Furthermore, we consider the detection of image-scaling attacks and derive an adaptive attack. In an empirical evaluation, we demonstrate the effectiveness of our strategy. First, we show that backdoors and poisoning work equally well when combined with image-scaling attacks. Second, we demonstrate that current detection defenses against image-scaling attacks are insufficient to uncover our manipulations. Overall, our work provides a novel means for hiding traces of manipulations, being applicable to different poisoning approaches.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00024"}, {"primary_key": "2683936", "vector": [], "sparse_vector": [], "title": "Karonte: Detecting Insecure Multi-binary Interactions in Embedded Firmware.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Low-power, single-purpose embedded devices (e.g., routers and IoT devices) have become ubiquitous. While they automate and simplify many aspects of users' lives, recent large-scale attacks have shown that their sheer number poses a severe threat to the Internet infrastructure. Unfortunately, the software on these systems is hardware-dependent, and typically executes in unique, minimal environments with non-standard configurations, making security analysis particularly challenging. Many of the existing devices implement their functionality through the use of multiple binaries. This multi-binary service implementation renders current static and dynamic analysis techniques either ineffective or inefficient, as they are unable to identify and adequately model the communication between the various executables. In this paper, we present Karonte, a static analysis approach capable of analyzing embedded-device firmware by modeling and tracking multi-binary interactions. Our approach propagates taint information between binaries to detect insecure interactions and identify vulnerabilities. We first evaluated Karonte on 53 firmware samples from various vendors, showing that our prototype tool can successfully track and constrain multi-binary interactions. This led to the discovery of 46 zero-day bugs. Then, we performed a large-scale experiment on 899 different samples, showing that Karonte scales well with firmware samples of different size and complexity.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00036"}, {"primary_key": "2683937", "vector": [], "sparse_vector": [], "title": "The Many Kinds of Creepware Used for Interpersonal Attacks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Technology increasingly facilitates interpersonal attacks such as stalking, abuse, and other forms of harassment. While prior studies have examined the ecosystem of software designed for stalking, there exists an unstudied, larger landscape of apps-what we call creepware-used for interpersonal attacks. In this paper, we initiate a study of creepware using access to a dataset detailing the mobile apps installed on over 50 million Android devices. We develop a new algorithm, CreepRank, that uses the principle of guilt by association to help surface previously unknown examples of creepware, which we then characterize through a combination of quantitative and qualitative methods. We discovered apps used for harassment, impersonation, fraud, information theft, concealment, and even apps that purport to defend victims against such threats. As a result of our work, the Google Play Store has already removed hundreds of apps for policy violations. More broadly, our findings and techniques improve understanding of the creepware ecosystem, and will inform future efforts that aim to mitigate interpersonal attacks.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00069"}, {"primary_key": "2683938", "vector": [], "sparse_vector": [], "title": "HydRand: Efficient Continuous Distributed Randomness.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A reliable source of randomness is not only an essential building block in various cryptographic, security, and distributed systems protocols, but also plays an integral part in the design of many new blockchain proposals. Consequently, the topic of publicly-verifiable, bias-resistant and unpredictable randomness has recently enjoyed increased attention. In particular random beacon protocols, aimed at continuous operation, can be a vital component for current Proof-of-Stake based distributed ledger proposals. We improve upon previous random beacon approaches with HydRand, a novel distributed protocol based on publicly-verifiable secret sharing (PVSS) to ensure unpredictability, bias-resistance, and public-verifiability of a continuous sequence of random beacon values. Furthermore, HydRand provides guaranteed output delivery of randomness at regular and predictable intervals in the presence of adversarial behavior and does not rely on a trusted dealer for the initial setup. Compared to existing PVSS based approaches that strive to achieve similar properties, our solution improves scalability by lowering the communication complexity from $\\mathcal{O}\\left( {{n^3}} \\right)$ to $\\mathcal{O}\\left( {{n^2}} \\right)$ . Furthermore, we are the first to present a detailed comparison of recently described schemes and protocols that can be used for implementing random beacons.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00003"}, {"primary_key": "2683939", "vector": [], "sparse_vector": [], "title": "Humpty Dumpty: Controlling Word Meanings via Corpus Poisoning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Word embeddings, i.e., low-dimensional vector representations such as GloVe and SGNS, encode word \"meaning\" in the sense that distances between words' vectors correspond to their semantic proximity. This enables transfer learning of semantics for a variety of natural language processing tasks.Word embeddings are typically trained on large public corpora such as Wikipedia or Twitter. We demonstrate that an attacker who can modify the corpus on which the embedding is trained can control the \"meaning\" of new and existing words by changing their locations in the embedding space. We develop an explicit expression over corpus features that serves as a proxy for distance between words and establish a causative relationship between its values and embedding distances. We then show how to use this relationship for two adversarial objectives: (1) make a word a top-ranked neighbor of another word, and (2) move a word from one semantic cluster to another.An attack on the embedding can affect diverse downstream tasks, demonstrating for the first time the power of data poisoning in transfer learning scenarios. We use this attack to manipulate query expansion in information retrieval systems such as resume search, make certain names more or less visible to named entity recognition models, and cause new words to be translated to a particular target word regardless of the language. Finally, we show how the attacker can generate linguistically likely corpus modifications, thus fooling defenses that attempt to filter implausible sentences from the corpus using a language model.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00115"}, {"primary_key": "2683940", "vector": [], "sparse_vector": [], "title": "RTA3: A Real Time Adversarial Attack on Recurrent Neural Networks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recurrent neural networks are widely used in machine learning systems that process time series data including health monitoring, object tracking in video, and automatic speech recognition (ASR). While much work has been done demonstrating the vulnerability of deep neural networks to socalled adversarial perturbations, the majority of this work has focused on convolutional neural networks that process non-sequential data for tasks like image recognition. We propose that the unique memory and parameter sharing properties of recurrent neural networks make them susceptible to periodic adversarial perturbations that can exploit these unique features. In this paper, we demonstrate a general application of deep reinforcement learning to the generation of periodic adversarial perturbations in a black-box approach to attack recurrent neural networks processing sequential data. We successfully learn an attack policy to generate adversarial perturbations against the DeepSpeech ASR system and further demonstrate that this attack policy generalizes to a set of unseen examples in real time.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00022"}, {"primary_key": "2683941", "vector": [], "sparse_vector": [], "title": "Towards an AI-Based After-Collision Forensic Analysis Protocol for Autonomous Vehicles.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Safety-critical applications in the cooperative vehicular networks are built to improve safety, traffic efficiency and handle emergencies by communicating the road condition captured using data from sensors (camera, LiDAR, RADAR, etc.). These cyber-physical systems maintain records of the data received from its sensors to make decisions while driving on road. Such proliferation of data opens possibilities of scenarios where attackers can forge into the system with unrestricted access to the internal network of the vehicle and perform malicious acts. Due to the possibility of such acts, it is crucial how forensic analysis should be carried out in case of traffic accidents that include autonomous vehicles (AV). In this paper, we propose a forensic investigation protocol on autonomous vehicles, specifically to investigate if there was an attack that targeted the vehicle sensors. The proposed process consists of three main phases: data curation, analysis and decision making. We argue that, by using supervised deep neural network-based architecture YOLO trained in the Darknet framework and tested with SORT, an effective model to detect traffic data can be built to perform forensic investigations.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00055"}, {"primary_key": "2683942", "vector": [], "sparse_vector": [], "title": "Neutaint: Efficient Dynamic Taint Analysis with Neural Networks.", "authors": ["<PERSON><PERSON> She", "<PERSON><PERSON><PERSON> Chen", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Ray", "<PERSON><PERSON>"], "summary": "Dynamic taint analysis (DTA) is widely used by various applications to track information flow during runtime execution. Existing DTA techniques use rule-based taint-propagation, which is neither accurate (i.e., high false positive rate) nor efficient (i.e., large runtime overhead). It is hard to specify taint rules for each operation while covering all corner cases correctly. Moreover, the overtaint and undertaint errors can accumulate during the propagation of taint information across multiple operations. Finally, rule-based propagation requires each operation to be inspected before applying the appropriate rules resulting in prohibitive performance overhead on large real-world applications.In this work, we propose Neutaint, a novel end-to-end approach to track information flow using neural program embeddings. The neural program embeddings model the target's programs computations taking place between taint sources and sinks, which automatically learns the information flow by observing a diverse set of execution traces. To perform lightweight and precise information flow analysis, we utilize saliency maps to reason about most influential sources for different sinks. Neutaint constructs two saliency maps, a popular machine learning approach to influence analysis, to summarize both coarse-grained and fine-grained information flow in the neural program embeddings.We compare Neutaint with 3 state-of-the-art dynamic taint analysis tools. The evaluation results show that Neutaint can achieve 68% accuracy, on average, which is 10% improvement while reducing 40× runtime overhead over the second-best taint tool Libdft on 6 real world programs. Neutaint also achieves 61% more edge coverage when used for taint-guided fuzzing indicating the effectiveness of the identified influential bytes. We also evaluate Neutaint's ability to detect real world software attacks. The results show that Neutaint can successfully detect different types of vulnerabilities including buffer/heap/integer overflows, division by zero, etc. Lastly, Neutaint can detect 98.7% of total flows, the highest among all taint analysis tools.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00022"}, {"primary_key": "2683943", "vector": [], "sparse_vector": [], "title": "Path Oblivious Heap: Optimal and Practical Oblivious Priority Queue.", "authors": ["<PERSON>"], "summary": "We propose Path Oblivious Heap, an extremely simple, practical, and optimal oblivious priority queue. Our construction also implies a practical and optimal oblivious sorting algorithm which we call Path Oblivious Sort. Not only are our algorithms asymptotically optimal, we show that their practical performance is only a small constant factor worse than insecure baselines. More specificially, assuming roughly logarithmic client private storage, Path Oblivious Heap consumes 2× to 7× more bandwidth than the ordinary insecure binary heap; and Path Oblivious Sort consumes 4.5× to 6× more bandwidth than the insecure Merge Sort. We show that these performance results improve existing works by 1-2 orders of magnitude. Finally, we evaluate our algorithm for a multi-party computation scenario and show 7x to 8x reduction in the number of symmetric encryptions relative to the state of the art 1 .", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00037"}, {"primary_key": "2683944", "vector": [], "sparse_vector": [], "title": "Mission Assurance for Autonomous Undersea Vehicles.", "authors": ["<PERSON>", "Aviel D<PERSON> Rubin", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Autonomous vehicles are all but inevitable, and assurance that they will behave safely with respect to passengers, as well as bystanders incidentally exposed to them, is moving forward, albeit slowly. The state of the art often involves stopping the vehicle, perhaps after diverting it to a nearby safe place. While this is good news, it does not fully realize the benefits of autonomy. Autonomous vehicles are built for a purpose; call it a mission. Being able to perform the mission, or part of it, while experiencing faults (or cyber-attack) should be a factor in determining the vehicle's suitability for the mission. This paper explores the state of the art in achieving autonomous mission assurance in the context of autonomous undersea vehicles (AUVs). It identifies gaps in the literature and proposes a novel plan to address certain gaps.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00056"}, {"primary_key": "2683945", "vector": [], "sparse_vector": [], "title": "Minimum-Norm Adversarial Examples on KNN and KNN based Models.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We study the robustness against adversarial examples of kNN classifiers and classifiers that combine kNN with neural networks. The main difficulty lies in the fact that finding an optimal attack on kNN is intractable for typical datasets. In this work, we propose a gradient-based attack on kNN and kNN-based defenses, inspired by the previous work by <PERSON><PERSON><PERSON> & Wagner [1]. We demonstrate that our attack outperforms their method on all of the models we tested with only a minimal increase in the computation time. The attack also beats the state-of-the-art attack [2] on kNN when using less than 1% of its running time. We hope that this attack can be used as a new baseline for evaluating the robustness of kNN and its variants.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00023"}, {"primary_key": "2683946", "vector": [], "sparse_vector": [], "title": "VERISMART: A Highly Precise Safety Verifier for Ethereum Smart Contracts.", "authors": ["Sunbeom So", "<PERSON><PERSON><PERSON>", "Jisu Park", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present VERISMART, a highly precise verifier for ensuring arithmetic safety of Ethereum smart contracts. Writing safe smart contracts without unintended behavior is critically important because smart contracts are immutable and even a single flaw can cause huge financial damage. In particular, ensuring that arithmetic operations are safe is one of the most important and common security concerns of Ethereum smart contracts nowadays. In response, several safety analyzers have been proposed over the past few years, but state-of-the-art is still unsatisfactory; no existing tools achieve high precision and recall at the same time, inherently limited to producing annoying false alarms or missing critical bugs. By contrast, VERISMART aims for an uncompromising analyzer that performs exhaustive verification without compromising precision or scalability, thereby greatly reducing the burden of manually checking undiscovered or incorrectly-reported issues. To achieve this goal, we present a new domain-specific algorithm for verifying smart contracts, which is able to automatically discover and leverage transaction invariants that are essential for precisely analyzing smart contracts. Evaluation with real-world smart contracts shows that VERISMART can detect all arithmetic bugs with a negligible number of false alarms, far outperforming existing analyzers.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00032"}, {"primary_key": "2683947", "vector": [], "sparse_vector": [], "title": "Assessment of Cyber Security Implications of New Technology Integrations into Military Supply Chains.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Military supply chains play a critical role in the acquisition and movement of goods for defence purposes. The disruption of these supply chain processes can have potentially devastating affects to the operational capability of military forces. The introduction and integration of new technologies into defence supply chains can serve to increase their effectiveness. However, the benefits posed by these technologies may be outweighed by significant consequences to the cyber security of the entire defence supply chain. Supply chains are complex Systems of Systems, and the introduction of an insecure technology into such a complex ecosystem may induce cascading system-wide failure, and have catastrophic consequences to military mission assurance. Subsequently, there is a need for an evaluative process to determine the extent to which a new technology will affect the cyber security of military supply chains. This work proposes a new model, the Military Supply Chain Cyber Implications Model (M-SCCIM), that serves to aid military decision makers in understanding the potential cyber security impact of introducing new technologies to supply chains. M-SCCIM is a multiphase model that enables understanding of cyber security and supply chain implications through the lenses of theoretical examinations, pilot applications and system wide implementations.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00038"}, {"primary_key": "2683948", "vector": [], "sparse_vector": [], "title": "Toward a Trustable, Self-Hosting Computer System.", "authors": ["<PERSON>"], "summary": "Due to the extremely rapid growth of the computing and IT technology market, commercial hardware made for the civilian, consumer sector is increasingly (and inevitably) deployed in security-sensitive environments. With the growing threat of hardware Trojans and backdoors, an adversary could perpetrate a full system compromise, or privilege escalation attack, even if the software is presumed to be perfectly secure. We propose a method of field stripping a computer system by empirically proving an equivalence between the trustability of the fielded system on one hand, and its comprehensive set of sources (including those of all toolchains used in its construction) on the other. In the long run, we hope to facilitate comprehensive verification and validation of fielded computer systems from fully self-contained hard-ware+software sources, as a way of mitigating against the lack of control over (and visibility into) the hardware supply chain.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00039"}, {"primary_key": "2683949", "vector": [], "sparse_vector": [], "title": "OAT: Attesting Operation Integrity of Embedded Devices.", "authors": ["Zhichuang Sun", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Due to the wide adoption of IoT/CPS systems, embedded devices (IoT frontends) become increasingly connected and mission-critical, which in turn has attracted advanced attacks (e.g., control-flow hijacks and data-only attacks). Unfortunately, IoT backends (e.g., remote controllers or in-cloud services) are unable to detect if such attacks have happened while receiving data, service requests, or operation status from IoT devices (remotely deployed embedded devices). As a result, currently, IoT backends are forced to blindly trust the IoT devices that they interact with.To fill this void, we first formulate a new security property for embedded devices, called \"Operation Execution Integrity\" or OEI. We then design and build a system, OAT, that enables remote OEI attestation for ARM-based bare-metal embedded devices. Our formulation of OEI captures the integrity of both control flow and critical data involved in an operation execution. Therefore, satisfying OEI entails that an operation execution is free of unexpected control and data manipulations, which existing attestation methods cannot check. Our design of OAT strikes a balance between prover's constraints (embedded devices' limited computing power and storage) and verifier's requirements (complete verifiability and forensic assistance). OAT uses a new control-flow measurement scheme, which enables lightweight and space-efficient collection of measurements (97% space reduction from the trace-based approach). OAT performs the remote control-flow verification through abstract execution, which is fast and deterministic. OAT also features lightweight integrity checking for critical data (74% less instrumentation needed than previous work). Our security analysis shows that OAT allows remote verifiers or IoT backends to detect both controlflow hijacks and data-only attacks that affect the execution of operations on IoT devices. In our evaluation using real embedded programs, OAT incurs a runtime overhead of 2.7%.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00042"}, {"primary_key": "2683950", "vector": [], "sparse_vector": [], "title": "Out-of-Distribution Detection in Multi-Label Datasets using Latent Space of β-VAE.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Learning Enabled Components (LECs) are widely being used in a variety of perceptions based autonomy tasks like image segmentation, object detection, end-to-end driving, etc. These components are trained with large image datasets with multimodal factors like weather conditions, time-of-day, traffic-density, etc. The LECs learn from these factors during training, and while testing if there is variation in any of these factors, the components get confused resulting in low confidence predictions. Those images with factor values, not seen, during training are commonly referred to as Out-of-Distribution (OOD). For safe autonomy, it is important to identify the OOD images, so that a suitable mitigation strategy can be performed. Classical one-class classifiers like SVM and SVDD are used to perform OOD detection. However, multiple labels attached to images in these datasets restrict the direct application of these techniques. We address this problem using the latent space of the β -Variational Autoencoder ( β -VAE). We use the fact that compact latent space generated by an appropriately selected β - VAE will encode the information about these factors in a few latent variables, and that can be used for quick and computationally inexpensive detection. We evaluate our approach on the nuScenes dataset, and our results show the latent space of β - VAE is sensitive to encode changes in the values of the generative factor.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00057"}, {"primary_key": "2683951", "vector": [], "sparse_vector": [], "title": "Towards Scalable Threshold Cryptosystems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Abraham", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The resurging interest in Byzantine fault tolerant systems will demand more scalable threshold cryptosystems. Unfortunately, current systems scale poorly, requiring time quadratic in the number of participants. In this paper, we present techniques that help scale threshold signature schemes (TSS), verifiable secret sharing (VSS) and distributed key generation (DKG) protocols to hundreds of thousands of participants and beyond. First, we use efficient algorithms for evaluating polynomials at multiple points to speed up computing Lagrange coefficients when aggregating threshold signatures. As a result, we can aggregate a 130,000 out of 260,000 BLS threshold signature in just 6 seconds (down from 30 minutes). Second, we show how \"authenticating\" such multipoint evaluations can speed up proving polynomial evaluations, a key step in communication-efficient VSS and DKG protocols. As a result, we reduce the asymptotic (and concrete) computational complexity of VSS and DKG protocols from quadratic time to quasilinear time, at a small increase in communication complexity. For example, using our DKG protocol, we can securely generate a key for the BLS scheme above in 2.3 hours (down from 8 days). Our techniques improve performance for thresholds as small as 255 and generalize to any Lagrange-based threshold scheme, not just threshold signatures. Our work has certain limitations: we require a trusted setup, we focus on synchronous VSS and DKG protocols and we do not address the worst-case complaint overhead in DKGs. Nonetheless, we hope it will spark new interest in designing large-scale distributed systems.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00059"}, {"primary_key": "2683952", "vector": [], "sparse_vector": [], "title": "Are anonymity-seekers just like everybody else? An analysis of contributions to Wikipedia from Tor.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Champion", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "User-generated content sites routinely block contributions from users of privacy-enhancing proxies like Tor because of a perception that proxies are a source of vandalism, spam, and abuse. Although these blocks might be effective, collateral damage in the form of unrealized valuable contributions from anonymity seekers is invisible. One of the largest and most important user-generated content sites, Wikipedia, has attempted to block contributions from Tor users since as early as 2005. We demonstrate that these blocks have been imperfect and that thousands of attempts to edit on Wikipedia through Tor have been successful. We draw upon several data sources and analytical techniques to measure and describe the history of Tor editing on Wikipedia over time and to compare contributions from Tor users to those from other groups of Wikipedia users. Our analysis suggests that although Tor users who slip through Wikipedia's ban contribute content that is more likely to be reverted and to revert others, their contributions are otherwise similar in quality to those from other unregistered participants and to the initial contributions of registered users.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00053"}, {"primary_key": "2683953", "vector": [], "sparse_vector": [], "title": "A Stealthier Partitioning Attack against Bitcoin Peer-to-Peer Network.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Anh V. Vu", "<PERSON>"], "summary": "Network adversaries, such as malicious transit autonomous systems (ASes), have been shown to be capable of partitioning the Bitcoin's peer-to-peer network via routing-level attacks; e.g., a network adversary exploits a BGP vulnerability and performs a prefix hijacking attack (viz. <PERSON><PERSON><PERSON><PERSON> et al. [3]). Due to the nature of BGP operation, such a hijacking is globally observable and thus enables immediate detection of the attack and the identification of the perpetrator. In this paper, we present a stealthier attack, which we call the EREBUS attack, that partitions the Bitcoin network without any routing manipulations, which makes the attack undetectable to control-plane and even to data-plane detectors. The novel aspect of EREBUS is that it makes the adversary AS a natural man-in-the-middle network of all the peer connections of one or more targeted Bitcoin nodes by patiently influencing the targeted nodes' peering decision. We show that affecting the peering decision of a Bitcoin node, which is believed to be infeasible after a series of bug patches against the earlier Eclipse attack [29], is possible for the network adversary that can use abundant network address resources (e.g., spoofing millions of IP addresses in many other ASes) reliably for an extended period of time at a negligible cost. The EREBUS attack is readily available for large ASes, such as Tier-1 and large Tier-2 ASes, against the vast majority of 10K public Bitcoin nodes with only about 520 bit/s of attack traffic rate per targeted Bitcoin node and a modest (e.g., 5-6 weeks) attack execution period. The EREBUS attack can be mounted by nation-state adversaries who would be willing to execute sophisticated attack strategies patiently to compromise cryptocurrencies (e.g., control the consensus, take down a cryptocurrency, censor transactions). As the attack exploits the topological advantage of being a network adversary but not the specific vulnerabilities of Bitcoin core, no quick patches seem to be available. We discuss that some naive solutions (e.g., whitelisting, rate-limiting) are ineffective and third-party proxy solutions may worsen the Bitcoin's centralization problem. We provide some suggested modifications to the Bitcoin core and show that they effectively make the EREBUS attack significantly harder; yet, their non-trivial changes to the Bitcoin's network operation (e.g., peering dynamics, propagation delays) should be examined thoroughly before their wide deployment.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00027"}, {"primary_key": "2683954", "vector": [], "sparse_vector": [], "title": "ICAS: an Extensible Framework for Estimating the Susceptibility of IC Layouts to Additive Trojans.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The transistors used to construct Integrated Circuits (ICs) continue to shrink. While this shrinkage improves performance and density, it also reduces trust: the price to build leading-edge fabrication facilities has skyrocketed, forcing even nation states to outsource the fabrication of high-performance ICs. Outsourcing fabrication presents a security threat because the black-box nature of a fabricated IC makes comprehensive inspection infeasible. Since prior work shows the feasibility of fabrication-time attackers' evasion of existing post-fabrication defenses, IC designers must be able to protect their physical designs before handing them off to an untrusted foundry. To this end, recent work suggests methods to harden IC layouts against attack. Unfortunately, no tool exists to assess the effectiveness of the proposed defenses, thus leaving defensive gaps.This paper presents an extensible IC layout security analysis tool called IC Attack Surface (ICAS) that quantifies defensive coverage. For researchers, ICAS identifies gaps for future defenses to target, and enables the quantitative comparison of existing and future defenses. For practitioners, ICAS enables the exploration of the impact of design decisions on an IC's resilience to fabrication-time attack. ICAS takes a set of metrics that encode the challenge of inserting a hardware Trojan into an IC layout, a set of attacks that the defender cares about, and a completed IC layout and reports the number of ways an attacker can add each attack to the design. While the ideal score is zero, practically, we find that lower scores correlate with increased attacker effort.To demonstrate ICAS' ability to reveal defensive gaps, we analyze over 60 layouts of three real-world hardware designs (a processor, AES and DSP accelerators), protected with existing defenses. We evaluate the effectiveness of each circuit-defense combination against three representative attacks from the literature. Results show that some defenses are ineffective and others, while effective at reducing the attack surface, leave 10's to 1000's of unique attack implementations that an attacker can exploit.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00083"}, {"primary_key": "2683955", "vector": [], "sparse_vector": [], "title": "SoK: Differential Privacy as a Causal Property.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present formal models of the associative and causal views of differential privacy. Under the associative view, the possibility of dependencies between data points precludes a simple statement of differential privacy's guarantee as conditioning upon a single changed data point. However, we show that a simple characterization of differential privacy as limiting the effect of a single data point does exist under the causal view, without independence assumptions about data points. We believe this characterization resolves disagreement and confusion in prior work about the consequences of differential privacy. The associative view needing assumptions boils down to the contrapositive of the maxim that correlation doesn't imply causation: differential privacy ensuring a lack of (strong) causation does not imply a lack of (strong) association. Our characterization also opens up the possibility of applying results from statistics, experimental design, and science about causation while studying differential privacy.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00012"}, {"primary_key": "2683956", "vector": [], "sparse_vector": [], "title": "Dragonblood: Analyzing the Dragonfly Handshake of WPA3 and EAP-pwd.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The WPA3 certification aims to secure home networks, while EAP-pwd is used by certain enterprise Wi-Fi networks to authenticate users. Both use the Dragonfly handshake to provide forward secrecy and resistance to dictionary attacks. In this paper, we systematically evaluate Dragonfly's security. First, we audit implementations, and present timing leaks and authentication bypasses in EAP-pwd and WPA3 daemons. We then study Dragonfly's design and discuss downgrade and denial-of-service attacks. Our next and main results are side-channel attacks against Dragonfly's password encoding method (e.g. hash-to-curve). We believe that these side-channel leaks are inherent to Dragonfly. For example, after our initial disclosure, patched software was still affected by a novel side-channel leak. We also analyze the complexity of using the leaked information to brute-force the password. For instance, brute-forcing a dictionary of size 10 10 requires less than $1 in Amazon EC2 instances. These results are also of general interest due to ongoing standardization efforts on Dragonfly as a TLS handshake, Password-Authenticated Key Exchanges (PAKEs), and hash-to-curve. Finally, we discuss backwards-compatible defenses, and propose protocol fixes that prevent attacks. Our work resulted in a new draft of the protocols incorporating our proposed design changes.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00031"}, {"primary_key": "2683957", "vector": [], "sparse_vector": [], "title": "A Programming Framework for Differential Privacy with Accuracy Concentration Bounds.", "authors": ["Elisabet Lobo Vesga", "<PERSON>", "<PERSON>"], "summary": "Differential privacy offers a formal framework for reasoning about privacy and accuracy of computations on private data. It also offers a rich set of building blocks for constructing private data analyses. When carefully calibrated, these analyses simultaneously guarantee the privacy of the individuals contributing their data, and the accuracy of the data analyses results, inferring useful properties about the population. The compositional nature of differential privacy has motivated the design and implementation of several programming languages aimed at helping a data analyst in programming differentially private analyses. However, most of the programming languages for differential privacy proposed so far provide support for reasoning about privacy but not for reasoning about the accuracy of data analyses. To overcome this limitation, in this work we present DPella, a programming framework providing data analysts with support for reasoning about privacy, accuracy and their trade-offs. The distinguishing feature of DPella is a novel component which statically tracks the accuracy of different data analyses. In order to make tighter accuracy estimations, this component leverages taint analysis for automatically inferring statistical independence of the different noise quantities added for guaranteeing privacy. We evaluate our approach by implementing several classical queries from the literature and showing how data analysts can figure out the best manner to calibrate privacy to meet the accuracy requirements.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00086"}, {"primary_key": "2683958", "vector": [], "sparse_vector": [], "title": "SEIMI: Efficient and Secure SMAP-Enabled Intra-process Memory Isolation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhang", "<PERSON><PERSON> Lai", "<PERSON>", "<PERSON>"], "summary": "Memory-corruption attacks such as code-reuse attacks and data-only attacks have been a key threat to systems security. To counter these threats, researchers have proposed a variety of defenses, including control-flow integrity (CFI), code-pointer integrity (CPI), and code (re-)randomization. All of them, to be effective, require a security primitive—intra-process protection of confidentiality and/or integrity for sensitive data (such as CFI's shadow stack and CPI's safe region).In this paper, we propose SEIMI, a highly efficient intra-process memory isolation technique for memory-corruption defenses to protect their sensitive data. The core of SEIMI is to use the efficient Supervisor-mode Access Prevention (SMAP), a hardware feature that is originally used for preventing the kernel from accessing the user space, to achieve intra-process memory isolation. To leverage SMAP, SEIMI creatively executes the user code in the privileged mode. In addition to enabling the new design of the SMAP-based memory isolation, we further develop multiple new techniques to ensure secure escalation of user code, e.g., using the descriptor caches to capture the potential segment operations and configuring the Virtual Machine Control Structure (VMCS) to invalidate the execution result of the control registers related operations. Extensive experimental results show that SEIMI outperforms existing isolation mechanisms, including both the Memory Protection Keys (MPK) based scheme and the Memory Protection Extensions (MPX) based scheme, while providing secure memory isolation.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00087"}, {"primary_key": "2683959", "vector": [], "sparse_vector": [], "title": "High Precision Open-World Website Fingerprinting.", "authors": ["<PERSON>"], "summary": "Traffic analysis attacks to identify which web page a client is browsing, using only her packet metadata - known as website fingerprinting (WF) - has been proven effective in closed-world experiments against privacy technologies like Tor. We want to investigate their usefulness in the real open world. Several WF attacks claim to have high recall and low false positive rate, but they have only been shown to succeed against high base rate pages. We explicitly incorporate the base rate into precision and call it r-precision. Using this metric, we show that the best previous attacks have poor precision when the base rate is realistically low; we study such a scenario (r = 1000), where the maximum r-precision achieved was only 0.14.To improve r-precision, we propose three novel classes of precision optimizers that can be applied to any classifier to increase precision. For r = 1000, our best optimized classifier can achieve a precision of at least 0.86, representing a precision increase by more than 6 times. For the first time, we show a WF classifier that can scale to any open world set size. We also investigate the use of precise classifiers to tackle realistic objectives in website fingerprinting, including different types of websites, identification of sensitive clients, and defeating website fingerprinting defenses.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00015"}, {"primary_key": "2683960", "vector": [], "sparse_vector": [], "title": "SEVurity: No Security Without Integrity : Breaking Integrity-Free Memory Encryption with Minimal Assumptions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "One reason for not adopting cloud services is the required trust in the cloud provider: As they control the hypervisor, any data processed in the system is accessible to them. Full memory encryption for Virtual Machines (VM) protects against curious cloud providers as well as otherwise compromised hypervisors. AMD Secure Encrypted Virtualization (SEV) is the most prevalent hardware-based full memory encryption for VMs. Its newest extension, SEV-ES, also protects the entire VM state during context switches, aiming to ensure that the host neither learns anything about the data that is processed inside the VM, nor is able to modify its execution state. Several previous works have analyzed the security of SEV and have shown that, by controlling I/O, it is possible to exfiltrate data or even gain control over the VM's execution. In this work, we introduce two new methods that allow us to inject arbitrary code into SEV-ES secured virtual machines. Due to the lack of proper integrity protection, it is sufficient to reuse existing ciphertext to build a high-speed encryption oracle. As a result, our attack no longer depends on control over the I/O, which is needed by prior attacks. As I/O manipulation is highly detectable, our attacks are stealthier. In addition, we reverse-engineer the previously unknown, improved Xor-Encrypt-Xor (XEX) based encryption mode, that AMD is using on updated processors, and show, for the first time, how it can be overcome by our new attacks.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00080"}, {"primary_key": "2683961", "vector": [], "sparse_vector": [], "title": "On the Feasibility of Acoustic Attacks Using Commodity Smart Devices.", "authors": ["<PERSON>", "Emiliano De Cristofaro", "<PERSON>"], "summary": "Sound at frequencies above (ultrasonic) or below (infrasonic) the range of human hearing can, in some settings, cause adverse physiological and psychological effects to individuals. We investigate the feasibility of cyber-attacks that could make smart consumer devices produce possibly imperceptible sound at both high (17-21kHz) and low (60-100Hz) frequencies, at the maximum available volume setting, potentially turning them into acoustic cyber-weapons. To do so, we deploy attacks targeting different smart devices and take sound measurements in an anechoic chamber. For comparison, we also test possible attacks on traditional devices. Overall, we find that some of the devices tested are capable of reproducing frequencies within both high and low ranges, at levels exceeding those recommended in published guidelines. Generally speaking, such attacks are often trivial to develop and in many cases could be added to existing malware payloads, as they may be attractive to adversaries with specific motivations or targets. Finally, we suggest a number of countermeasures for detection and prevention.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00031"}, {"primary_key": "2683962", "vector": [], "sparse_vector": [], "title": "The Value of Collaboration in Convex Machine Learning with Differential Privacy.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we apply machine learning to distributed private data owned by multiple data owners, entities with access to non-overlapping training datasets. We use noisy, differentially-private gradients to minimize the fitness cost of the machine learning model using stochastic gradient descent. We quantify the quality of the trained model, using the fitness cost, as a function of privacy budget and size of the distributed datasets to capture the trade-off between privacy and utility in machine learning. This way, we can predict the outcome of collaboration among privacy-aware data owners prior to executing potentially computationally-expensive machine learning algorithms. Particularly, we show that the difference between the fitness of the trained machine learning model using differentially-private gradient queries and the fitness of the trained machine model in the absence of any privacy concerns is inversely proportional to the size of the training datasets squared and the privacy budget squared. We successfully validate the performance prediction with the actual performance of the proposed privacy-aware learning algorithms, applied to: financial datasets for determining interest rates of loans using regression; and detecting credit card frauds using support vector machines.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00025"}, {"primary_key": "2683963", "vector": [], "sparse_vector": [], "title": "Unexpected Data Dependency Creation and Chaining: A New Attack to SDN.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Software-Defined Networking (SDN) is an emerging network architecture that provides programmable networking through a logically centralized controller. As SDN becomes more prominent, its security vulnerabilities become more evident than ever. Serving as the \"brain\" of a software-defined network, how the control plane (of the network) is exposed to external inputs (i.e., data plane messages) is directly correlated with how secure the network is. Fortunately, due to some unique SDN design choices (e.g., control plane and data plane separation), attackers often struggle to find a reachable path to those vulnerable logic hidden deeply within the control plane.In this paper, we demonstrate that it is possible for a weak adversary who only controls a commodity network device (host or switch) to attack previously unreachable control plane components by maliciously increasing reachability in the control plane. We introduce D 2 C 2 (data dependency creation and chaining) attack, which leverages some widely-used SDN protocol features (e.g., custom fields) to create and chain unexpected data dependencies in order to achieve greater reachability. We have developed a novel tool, SVHunter, which can effectively identify D 2 C 2 vulnerabilities. Till now we have evaluated SV<PERSON>unter on three mainstream open-source SDN controllers (i.e., ONOS, Floodlight, and Opendaylight) as well as one security-enhanced controller (i.e., SE-Floodlight). <PERSON><PERSON>unt<PERSON> detects 18 previously unknown vulnerabilities, all of which can be exploited remotely to launch serious attacks such as executing arbitrary commands, exfiltrating confidential files, and crashing SDN services.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00017"}, {"primary_key": "2683964", "vector": [], "sparse_vector": [], "title": "Towards Effective Differential Privacy Communication for Users&apos; Data Sharing Decision and Comprehension.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Differential privacy protects an individual's privacy by perturbing data on an aggregated level (DP) or individual level (LDP). We report four online human-subject experiments investigating the effects of using different approaches to communicate differential privacy techniques to laypersons in a health app data collection setting. Experiments 1 and 2 investigated participants' data disclosure decisions for low-sensitive and high-sensitive personal information when given different DP or LDP descriptions. Experiments 3 and 4 uncovered reasons behind participants' data sharing decisions, and examined participants' subjective and objective comprehensions of these DP or LDP descriptions. When shown descriptions that explain the implications instead of the definition/processes of DP or LDP technique, participants demonstrated better comprehension and showed more willingness to share information with LDP than with DP, indicating their understanding of LDP's stronger privacy guarantee compared with DP.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00088"}, {"primary_key": "2683965", "vector": [], "sparse_vector": [], "title": "Krace: Data Race Fuzzing for Kernel File Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hanqing Zhao", "<PERSON><PERSON><PERSON>"], "summary": "Data races occur when two threads fail to use proper synchronization when accessing shared data. In kernel file systems, which are highly concurrent by design, data races are common mistakes and often wreak havoc on the users, causing inconsistent states or data losses. Prior fuzzing practices on file systems have been effective in uncovering hundreds of bugs, but they mostly focus on the sequential aspect of file system execution and do not comprehensively explore the concurrency dimension and hence, forgo the opportunity to catch data races.In this paper, we bring coverage-guided fuzzing to the concurrency dimension with three new constructs: 1) a new coverage tracking metric, alias coverage, specially designed to capture the exploration progress in the concurrency dimension; 2) an evolution algorithm for generating, mutating, and merging multi-threaded syscall sequences as inputs for concurrency fuzzing; and 3) a comprehensive lockset and happens-before modeling for kernel synchronization primitives for precise data race detection. These components are integrated into Krace, an end-to-end fuzzing framework that has discovered 23 data races in ext4, btrfs, and the VFS layer so far, and 9 are confirmed to be harmful.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00078"}, {"primary_key": "2683966", "vector": [], "sparse_vector": [], "title": "SoK: A Minimalist Approach to Formalizing Analog Sensor Security.", "authors": ["<PERSON>", "Hocheol Shin", "<PERSON>", "<PERSON><PERSON> Xu", "<PERSON><PERSON>", "<PERSON>"], "summary": "Over the last six years, several papers demonstrated how intentional analog interference based on acoustics, RF, lasers, and other physical modalities could induce faults, influence, or even control the output of sensors. Damage to the availability and integrity of sensor output carries significant risks to safety-critical systems that make automated decisions based on trusted sensor measurement. Established signal processing models use transfer functions to express reliability and dependability characteristics of sensors, but existing models do not provide a deliberate way to express and capture security properties meaningfully.Our work begins to fill this gap by systematizing knowledge of analog attacks against sensor circuitry and defenses. Our primary contribution is a simple sensor security model such that sensor engineers can better express analog security properties of sensor circuitry without needing to learn significantly new notation. Our model introduces transfer functions and a vector of adversarial noise to represent adversarial capabilities at each stage of a sensor's signal conditioning chain. The primary goals of the systematization are (1) to enable more meaningful quantification of risk for the design and evaluation of past and future sensors, (2) to better predict new attack vectors, and (3) to establish defensive design patterns that make sensors more resistant to analog attacks.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00026"}, {"primary_key": "2683967", "vector": [], "sparse_vector": [], "title": "Poster: Radiometric Signatures for Wireless Device Identification over Dynamic Channels.", "authors": ["Wenqing Yan", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Radiometric signatures have been shown effective in identifying wireless devices based on imperfections in their electronics, also known as fingerprinting. Previous work mainly considered static channel conditions. In this work, we experimentally study the impact of movement and dynamic channel conditions on the radiometric signatures. We demonstrate the feasibility of fingerprinting when channels are dynamic.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00037"}, {"primary_key": "2683968", "vector": [], "sparse_vector": [], "title": "PMP: Cost-effective Forced Execution with Probabilistic Memory Pre-planning.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Malware is a prominent security threat and exposing malware behavior is a critical challenge.Recent malware often has payload that is only released when certain conditions are satisfied.It is hence difficult to fully disclose the payload by simply executing the malware.In addition, malware samples may be equipped with cloaking techniques such as VM detectors that stop execution once detecting that the malware is being monitored.Forced execution is a highly effective method to penetrate malware self-protection and expose hidden behavior, by forcefully setting certain branch outcomes.However, an existing state-of-the-art forced execution technique X-Force is very heavyweight, requiring tracing individual instructions, reasoning about pointer alias relations on-the-fly, and repairing invalid pointers by on-demand memory allocation.We develop a light-weight and practical forced execution technique.Without losing analysis precision, it avoids tracking individual instructions and on-demand allocation.Under our scheme, a forced execution is very similar to a native one.It features a novel memory pre-planning phase that pre-allocates a large memory buffer, and then initializes the buffer, and variables in the subject binary, with carefully crafted values in a random fashion before the real execution.The pre-planning is designed in such a way that dereferencing an invalid pointer has a very large chance to fall into the pre-allocated region and hence does not cause any exception, and semantically unrelated invalid pointer dereferences highly likely access disjoint (pre-allocated) memory regions, avoiding state corruptions with probabilistic guarantees.Our experiments show that our technique is 84 times faster than X-Force, has 6.5X and 10% fewer false positives and negatives for program dependence detection, respectively, and can expose 98% more malicious behaviors in 400 recent malware samples.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00035"}, {"primary_key": "2683969", "vector": [], "sparse_vector": [], "title": "OHIE: <PERSON><PERSON>in Scaling Made Simple.", "authors": ["Haifeng Yu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many blockchain consensus protocols have been proposed recently to scale the throughput of a blockchain with available bandwidth. However, these protocols are becoming increasingly complex, making it more and more difficult to produce proofs of their security guarantees. We propose a novel permissionless blockchain protocol OHIE which explicitly aims for simplicity. OHIE composes as many parallel instances of Bitcoin's original (and simple) backbone protocol as needed to achieve excellent throughput. We formally prove the safety and liveness properties of OHIE. We demonstrate its performance with a prototype implementation and large-scale experiments with up to 50,000 nodes. In our experiments, OHIE achieves linear scaling with available bandwidth, providing about 4-10Mbps transaction throughput (under 8-20Mbps per-node available bandwidth configurations) and at least about 20x better decentralization over prior works.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00008"}, {"primary_key": "2683970", "vector": [], "sparse_vector": [], "title": "Detection of Electromagnetic Interference Attacks on Sensor Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Sensor systems are used every time a microcontroller needs to interact with the physical world. They are abundant in home automation, factory control systems, critical infrastructure, transport systems and many, many other things.In a sensor system, a sensor transforms a physical quantity into an analog signal which is sent to an ADC and a microcontroller for digitization and further processing. Once the measurement is in digital form, the microcontroller can execute tasks according to the measurement. Electromagnetic interference (EMI) can affect a measurement as it is transferred to the microcontroller. An attacker can manipulate the sensor output by intentionally inducing EMI in the wire between the sensor and the microcontroller. The nature of the analog channel between the sensor and the microcontroller means that the microcontroller cannot authenticate whether the measurement is from the sensor or the attacker. If the microcontroller includes incorrect measurements in its control decisions, it could have disastrous consequences.We present a novel detection system for these low-level electromagnetic interference attacks. Our system is based on the idea that if the sensor is turned off, the signal read by the microcontroller should be 0V (or some other known value). We use this idea to modulate the sensor output in a way that is unpredictable to the adversary. If the microcontroller detects fluctuations in the sensor output, the attacking signal can be detected. Our proposal works with a minimal amount of extra components and is thus cheap and easy to implement.We present the working mechanism of our detection method and prove the detection guarantee in the context of a strong attacker model. We implement our approach in order to detect adversarial EMI signals, both in a microphone system and a temperature sensor system, and we show that our detection mechanism is both effective and robust.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00001"}, {"primary_key": "2683971", "vector": [], "sparse_vector": [], "title": "Transys: Leveraging Common Security Properties Across Hardware Designs.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents Transys, a tool for translating security critical properties written for one hardware design to analogous properties suitable for a second design. Transys works in three passes adjusting the variable names, arithmetic expressions, logical preconditions, and timing constraints of the original property to retain the intended semantics of the property while making it valid for the second design. We evaluate Transys by translating 27 assertions written in a temporal logic and 9 properties written for use with gate level information flow tracking across 38 AES designs, 3 RSA designs, and 5 RISC processor designs. Transys successfully translates 96% of the properties. Among these, the translation of 23 (64%) of the properties achieved a semantic equivalence rate of above 60%. The average translation time per property is about 70 seconds.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00030"}, {"primary_key": "2683972", "vector": [], "sparse_vector": [], "title": "Transparent Polynomial Delegation and Its Applications to Zero Knowledge Proof.", "authors": ["<PERSON><PERSON><PERSON>", "Tiancheng Xie", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a new succinct zero knowledge argument scheme for layered arithmetic circuits without trusted setup. The prover time is O(C + nlogn) and the proof size is O(D logC +log 2 n) for a D-depth circuit with n inputs and C gates. The verification time is also succinct, O(D logC + log 2 n), if the circuit is structured. Our scheme only uses lightweight cryptographic primitives such as collision-resistant hash functions and is plausibly post-quantum secure. We implement a zero knowledge argument system, Virgo, based on our new scheme and compare its performance to existing schemes. Experiments show that it only takes 53 seconds to generate a proof for a circuit computing a Merkle tree with 256 leaves, at least an order of magnitude faster than all other succinct zero knowledge argument schemes. The verification time is 50ms, and the proof size is 253KB, both competitive to existing systems.Underlying Virgo is a new transparent zero knowledge verifiable polynomial delegation scheme with logarithmic proof size and verification time. The scheme is in the interactive oracle proof model and may be of independent interest.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00052"}, {"primary_key": "2683973", "vector": [], "sparse_vector": [], "title": "Clipped BagNet: Defending Against Sticker Attacks with Clipped Bag-of-features.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many works have demonstrated that neural networks are vulnerable to adversarial examples. We examine the adversarial sticker attack, where the attacker places a sticker somewhere on an image to induce it to be misclassified. We take a first step towards defending against such attacks using clipped BagNet, which bounds the influence that any limited-size sticker can have on the final classification. We evaluate our scheme on ImageNet and show that it provides strong security against targeted PGD attacks and gradient-free attacks, and yields certified security for a 95% of images against a targeted 20 × 20 pixel attack.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SPW50608.2020.00026"}, {"primary_key": "2683974", "vector": [], "sparse_vector": [], "title": "Leveraging EM Side-Channel Information to Detect Rowhammer Attacks.", "authors": ["<PERSON><PERSON><PERSON> Zhang", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Xenofon D<PERSON>"], "summary": "The rowhammer bug belongs to software-induced hardware faults, and has been exploited to form a wide range of powerful rowhammer attacks. Yet, how to effectively detect such attacks remains a challenging problem. In this paper, we propose a novel approach named RADAR (Rowhammer Attack Detection via A Radio) that leverages certain electromagnetic (EM) signals to detect rowhammer attacks. In particular, we have found that there are recognizable hammering-correlated sideband patterns in the spectrum of the DRAM clock signal. As such patterns are inevitable physical side effects of hammering the DRAM, they can \"expose\" any potential rowhammer attacks including the extremely elusive ones hidden inside encrypted and isolated environments like Intel SGX enclaves. However, the patterns of interest may become unapparent due to the common use of spread-spectrum clocking (SSC) in computer systems. We propose a de-spreading method that can reassemble the hammering-correlated sideband patterns scattered by SSC. Using a common classification technique, we can achieve both effective and robust detection-based defense against rowhammer attacks, as evaluated on a RADAR prototype under various scenarios. In addition, our RADAR does not impose any performance overhead on the protected system. There has been little prior work that uses physical side-channel information to perform rowhammer defenses, and to the best of our knowledge, this is the first investigation on leveraging EM side-channel information for this purpose.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00060"}, {"primary_key": "2683975", "vector": [], "sparse_vector": [], "title": "Automatic Uncovering of Hidden Behaviors From Input Validation in Mobile Apps.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Mobile applications (apps) have exploded in popularity, with billions of smartphone users using millions of apps available through markets such as the Google Play Store or the Apple App Store. While these apps have rich and useful functionality that is publicly exposed to end users, they also contain hidden behaviors that are not disclosed, such as backdoors and blacklists designed to block unwanted content. In this paper, we show that the input validation behavior-the way the mobile apps process and respond to data entered by users-can serve as a powerful tool for uncovering such hidden functionality. We therefore have developed a tool, InputScope, that automatically detects both the execution context of user input validation and also the content involved in the validation, to automatically expose the secrets of interest. We have tested InputScope with over 150,000 mobile apps, including popular apps from major app stores and preinstalled apps shipped with the phone, and found 12,706 mobile apps with backdoor secrets and 4,028 mobile apps containing blacklist secrets.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00072"}, {"primary_key": "2683976", "vector": [], "sparse_vector": [], "title": "Enabling Rack-scale Confidential Computing using Heterogeneous Trusted Execution Environment.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Cao", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "With its huge real-world demands, large-scale confidential computing still cannot be supported by today's Trusted Execution Environment (TEE), due to the lack of scalable and effective protection of high-throughput accelerators like GPUs, FPGAs, and TPUs etc. Although attempts have been made recently to extend the CPU-like enclave to GPUs, these solutions require change to the CPU or GPU chips, may introduce new security risks due to the side-channel leaks in CPU-GPU communication and are still under the resource constraint of today's CPU TEE.To address these problems, we present the first Heterogeneous TEE design that can truly support large-scale compute or data intensive (CDI) computing, without any chip-level change. Our approach, called HETEE, is a device for centralized management of all computing units (e.g., GPUs and other accelerators) of a server rack. It is uniquely designed to work with today's data centres and clouds, leveraging modern resource pooling technologies to dynamically compartmentalize computing tasks, and enforce strong isolation and reduce TCB through hardware support. More specifically, HETEE utilizes the PCIe ExpressFabric to allocate its accelerators to the server node on the same rack for a non-sensitive CDI task, and move them back into a secure enclave in response to the demand for confidential computing. Our design runs a thin TCB stack for security management on a security controller (SC), while leaving a large set of software (e.g., AI runtime, GPU driver, etc.) to the integrated microservers that operate enclaves. An enclaves is physically isolated from others through hardware and verified by the SC at its inception. Its microserver and computing units are restored to a secure state upon termination.We implemented HETEE on a real hardware system, and evaluated it with popular neural network inference and training tasks. Our evaluations show that HETEE can easily support the CDI tasks on the real-world scale and incurred a maximal throughput overhead of 2.17% for inference and 0.95% for training on ResNet152.", "published": "2020-01-01", "category": "sp", "pdf_url": "", "sub_summary": "", "source": "sp", "doi": "10.1109/SP40000.2020.00054"}]