[{"primary_key": "4480430", "vector": [], "sparse_vector": [], "title": "Identifying Cross-origin Resource Status Using Application Cache.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "HTML5 Application Cache (AppCache) allows web applications to cache their same- and cross-origin resources in the local storage of a web browser to enable offline access. However, cross-origin resource caching in AppCache can cause security and privacy problems. In this paper, we consider a novel web privacy attack that exploits cross-origin AppCache. Our attack allows a remote web attacker to exploit a victim web browser to exactly identify the status of target URLs: existence, redirection, or error. Especially, our attack can be performed without using client-side scripts, can concurrently identify the status of multiple URLs, and can exactly identify the redirections of target URLs. We further demonstrate advanced attacks that leverage the basic attack to de-anonymize or fingerprint victims. First, we determine the login status of a victim web browser by identifying URL redirections or errors due to absent or erroneous login information. Second, we probe internal web servers located in the local network of a victim web browser by identifying URL existence. We also suggest effective countermeasures to mitigate the proposed attacks.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480431", "vector": [], "sparse_vector": [], "title": "Seven Months&apos; Worth of Mistakes: A Longitudinal Study of Typosquatting Abuse.", "authors": ["<PERSON>", "W<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Typosquatting, defined as the act of registering in bad faith a domain name likely to result from making a typing mistake in a domain name belonging to someone else, has been known and studied for over 15 years. Nevertheless, this practice and its many variants are still thoroughly practiced up until this day. While previous typosquatting studies have always taken a snapshot of the typosquatting landscape, we present the first longitudinal study of typosquatting, i.e., a study in time. We collected data about the typosquatting domains of the 500 most popular sites of the Internet every day, for a period of seven months and we use this data to both establish whether previously discovered typosquatting trends still hold today, and to provide new results and insights in the typosquatting landscape. In particular we reveal that, even though 95% of the domains we investigated are actively targeted by typosquatters, only few trademark owners protect themselves against this practice by proactively registering typosquatting domains themselves. We take advantage of the longitudinal aspect of our study to show, among other results, that typosquatting domains change hands from typosquatters to legitimate owners and vice versa, and that typosquatters vary their monetization strategy by hosting different types of pages over time. Our study also reveals that a large fraction of typosquatting domains can be traced back to a small group of typosquatting page hosters and that certain top-level domains are much more prone to typosquatting than others.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480432", "vector": [], "sparse_vector": [], "title": "The Devil is in the Constants: Bypassing Defenses in Browser JIT Engines.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Return-oriented programming (ROP) has become the dominant form of vulnerability exploitation in both user and kernel space. Many defenses against ROP exist, which can significantly raise the bar against the attacker. Although protecting existing code, such as applications and the kernel, might be possible, taking countermeasures against dynamic code, i.e., code that is generated only at run-time, is much harder. Attackers have already started exploiting Just-in-Time (JIT) engines, available in all modern browsers, for introducing their (shell)code (either native code or re-usable gadgets) during JIT compilation and then take advantage of it. Recognizing the immediate threat, browser vendors started employing defenses for hardening their JIT engines. In this paper, we show that – no matter the employed defenses – JIT engines are still exploitable using solely dynamically generated gadgets. We demonstrate that dynamic ROP pay- load construction is possible in two modern web browsers without utilizing any of the available gadgets contained in the browser binary or linked libraries. First, we exploit an open source JIT engine (Mozilla Firefox) by feeding it malicious JavaScript, which once processed produces all required gadgets for running any shellcode successfully. Second, we exploit a proprietary JIT engine, the one in the 64-bit Microsoft Internet Explorer, which employs many undocumented, specially crafted defenses against JIT exploitation. We manage to bypass all of them and create the required gadgets for running any shellcode successfully. All defensive techniques are documented in this paper to assist other researchers. Furthermore, we do not only show how to construct the ROP gadgets on-the-fly, but also how to discover them on-the-fly, rendering current randomization schemes ineffective. Last but not least, we perform an analysis of the most important defense currently employed, namely constant blinding, which shields all three-byte or larger immediate values in the JIT buffer for prohibiting the construction of ROP gadgets. Our analysis suggests that extending constant blinding to all immediate values (i.e., shielding 1-byte and 2-byte constants) dramati- cally decreases the JIT engine’s performance, introducing an overhead of up to %80.M", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480433", "vector": [], "sparse_vector": [], "title": "Run-time Monitoring and Formal Analysis of Information Flows in Chromium.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Web browsers are a key enabler of a wide range of online services, from shopping and email, to banking and health services. Because these services frequently involve handling sensitive data, a wide range of web browser security policies and mechanisms has been implemented or proposed to mitigate the dangers posed by malicious code and sites. This paper describes the development of a framework for specifying and enforcing flexible information-flow policies on the Chromium web browser. Complementing efforts that focus on information-flow enforcement on JavaScript, the framework described in this paper focuses on an existing browser and encompasses a broad range of browser features, from pages and scripts to DOM elements, events, persistent state, and extensions. In this framework, entities in the browser are annotated with information-flow labels that specify policy. Our framework is formally modeled and developed in parallel with a running prototype on top of the Chromium web browser. We demonstrate how the framework can be used to represent many existing browser policies. Experiments with our prototype confirm that the framework can enforce practically useful policies beyond those enforceable in standard web browsers, and a formal proof of noninterference validates the framework’s design.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480434", "vector": [], "sparse_vector": [], "title": "Verified Contributive Channel Bindings for Compound Authentication.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Compound authentication protocols, such as EAP in IKEv2 or SASL over TLS, bind application-level authentication to a transport-level authenticated channel in order to obtain strong composite authentication under weak trust assumptions. Despite their wide deployment, these protocols remain poorly understood, leading to several credential forwarding man-in-the-middle attacks. We present the first formal models for several compound authentication protocols, and analyze them against a rich threat model that includes compromised certificates, leaked session keys, and <PERSON><PERSON><PERSON><PERSON> small subgroup confinement. Our analysis uncovers new compound authentication attacks on TLS renegotiation, SSH re-exchange, IKEv2 resumption, and a number of other channel binding proposals. We propose new channel bindings and formally evaluate their effectiveness using the automated symbolic cryptographic protocol verifier, ProVerif. Our automated analysis is the first to reconstruct the recently published triple handshake attacks on TLS, and the first to provide rigorous guarantees for its proposed countermeasure.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480435", "vector": [], "sparse_vector": [], "title": "Predicting Users&apos; Motivations behind Location Check-Ins and Utility Implications of Privacy Protection Mechanisms.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Location check-ins contain both geographical and semantic information about the visited venues, in the form of tags (e.g., \"restaurant\").Such data might reveal some personal information about users beyond what they actually want to disclose, hence their privacy is threatened.In this paper, we study users' motivations behind location check-ins, and we quantify the effect of a privacy-preserving technique (i.e., generalization) on the perceived utility of check-ins.By means of a targeted userstudy on Foursquare (N = 77), we show that the motivation behind Foursquare check-ins is a mediator of the loss of utility caused by generalization.Using these findings, we propose a machinelearning method for determining the motivation behind each check-in, and we design a motivation-based predictive model for utility.Our results show that the model accurately predicts the loss of utility caused by semantic and geographical generalization; this model enables the design of utility-aware, privacy-enhancing mechanisms in location-based social networks.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": "10.14722/NDSS.2015.23032"}, {"primary_key": "4480436", "vector": [], "sparse_vector": [], "title": "Spaced Repetition and Mnemonics Enable Recall of Multiple Strong Passwords.", "authors": ["<PERSON>", "Saranga <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We report on a user study that provides evidence that spaced repetition and a specific mnemonic technique enable users to successfully recall multiple strong passwords over time. Remote research participants were asked to memorize 4 Person-Action-Object (PAO) stories where they chose a famous person from a drop-down list and were given machine-generated random action-object pairs. Users were also shown a photo of a scene and asked to imagine the PAO story taking place in the scene (e.g., <PERSON> swallowing bike on a beach). Subsequently, they were asked to recall the action-object pairs when prompted with the associated scene-person pairs following a spaced repetition schedule over a period of 100+ days. While we evaluated several spaced repetition schedules, the best results were obtained when users initially returned after 12 hours and then in 1.5x increasing intervals: 77.1% of the participants successfully recalled all 4 stories in all 9 tests over a period of 102 days. Much of the forgetting happened in the first test period (12 hours): on average 94.9% of the participants who had remembered the stories in earlier rounds successfully remembered them in subsequent rounds. These findings, coupled with recent results on naturally rehearsing password schemes, suggest that 4 PAO stories could be used to create usable and strong passwords for 14 sensitive accounts following this spaced repetition schedule, possibly with a few extra upfront rehearsals. In addition, we find statistically significant evidence that initially (8 tests over 64 days) users who were asked to memorize 4 PAO stories outperform users who are given 4 random action-object pairs, but eventually (9 tests over 128 days) the advantage is not significant. Furthermore, there is an interference effect across multiple PAO stories: the recall rate of 100% for participants who were asked to memorize 1 or 2 PAO stories is significantly better than that for 4 PAO stories. These findings yield concrete advice for improving constructions of password management schemes and future user studies.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480437", "vector": [], "sparse_vector": [], "title": "Integro: Leveraging Victim Prediction for Robust Fake Account Detection in OSNs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Detecting fake accounts in online social networks (OSNs) protects OSN operators and their users from various malicious activities. Most detection mechanisms attempt to predict and classify user accounts as real (i.e., benign, honest) or fake (i.e., malicious, Sybil) by analyzing user-level activities or graph-level structures. These mechanisms, however, are not robust against adversarial attacks in which fake accounts cloak their operation with patterns resembling real user behavior. We herein demonstrate that victims, benign users who control real accounts and have befriended fakes, form a distinct classification category that is useful for designing robust detection mechanisms. First, as attackers have no control over victim accounts and cannot alter their activities, a victim account classifier which relies on user-level activities is relatively harder to circumvent. Second, as fakes are directly connected to victims, a fake account detection mechanism that integrates victim prediction into graph-level structures is more robust against manipulations of the graph. To validate this new approach, we designed Integro, a scalable defense system that helps OSNs detect fake accounts using a meaningful a user ranking scheme. Integro starts by predicting victim accounts from user-level activities. After that, it integrates these predictions into the graph as weights, so that edges incident to predicted victims have much lower weights than others. Finally, Integro ranks user accounts based on a modified random walk that starts from a known real account. Integro guarantees that most real accounts rank higher than fakes so that OSN operators can take actions against low-ranking fake accounts. We implemented Integro using widely-used, open-source distributed computing platforms in which it scaled nearly linearly. We evaluated Integro against SybilRank, the state-of-the-art in fake account detection, using real-world datasets and a large-scale deployment at Tuenti, the largest OSN in Spain. We show that Integro significantly outperforms SybilRank in user ranking quality, where the only requirement is to employ a victim classifier is better than random. Moreover, the deployment of Integro at Tuenti resulted in up to an order of magnitude higher precision in fake accounts detection, as compared to SybilRank.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480438", "vector": [], "sparse_vector": [], "title": "Machine Learning Classification over Encrypted Data.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Machine learning classification is used in numerous settings nowadays, such as medical or genomics predictions, spam detection, face recognition, and financial predictions. Due to privacy concerns, in some of these applications, it is important that the data and the classifier remain confidential. In this work, we construct three major classification protocols that satisfy this privacy constraint: hyperplane decision, Naive Bayes, and decision trees. We also enable these protocols to be combined with AdaBoost. At the basis of these constructions is a new library of building blocks for constructing classifiers securely; we demonstrate that this library can be used to construct other classifiers as well, such as a multiplexer and a face detection classifier. We implemented and evaluated our library and classifiers. Our protocols are efficient, taking milliseconds to a few seconds to perform a classification when running on real medical datasets.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480439", "vector": [], "sparse_vector": [], "title": "EdgeMiner: Automatically Detecting Implicit Control Flow Transitions through the Android Framework.", "authors": ["<PERSON><PERSON> Cao", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A wealth of recent research proposes static data flow analysis for the security analysis of Android applications. One of the building blocks that these analysis systems rely upon is the computation of a precise control flow graph. The callback mechanism provided and orchestrated by the Android framework makes the correct generation of the control flow graph a challenging endeavor. From the analysis’ point of view, the invocation of a callback is an implicit control flow transition facilitated by the framework. Existing static analysis tools model callbacks either through manually curated lists or ad-hoc heuristics. This work demonstrates that both approaches are insufficient, and allow malicious applications to evade detection by state-of-theart analysis systems. To address the challenge of implicit control flow transitions (i.e., callbacks) through the Android framework, we are the first to propose, implement, and evaluate a systematic treatment of this aspect. Our implementation, called EDGEMINER, statically analyzes the entire Android framework to automatically generate API summaries that describe implicit control flow transitions through the Android framework. We use EDGEMINER to analyze three major versions of the Android framework. EDGEMINER identified 19,647 callbacks in Android 4.2, suggesting that a manual treatment of this challenge is likely infeasible. Our evaluation demonstrates that the current insufficient treatment of callbacks in state-of-the-art analysis tools results in unnecessary imprecision. For example, FlowDroid misses a variety of leaks of privacy sensitive data from benign off-the-shelf Android applications because of its inaccurate handling of callbacks. Of course, malicious applications can also leverage this blind spot in current analysis systems to evade detection at will. To alleviate these drawbacks, we make our results publicly available and demonstrate how these results can easily be integrated into existing state-of-the-art analysis tools. Our modifications allow existing tools to comprehensively address the challenge of callbacks and identify previously undetected leakage of privacy sensitive data.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480440", "vector": [], "sparse_vector": [], "title": "StackArmor: Comprehensive Protection From Stack-based Memory Error Vulnerabilities for Binaries.", "authors": ["<PERSON>", "Asia Slowinska", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "StackArmor is a comprehensive protection technique for stack-based memory error vulnerabilities in binaries. It relies on binary analysis and rewriting techniques to drastically reduce the uniquely high spatial and temporal memory predictability of traditional call stack organizations. Unlike prior solutions, StackArmor can protect against arbitrary stack-based attacks, requires no access to the source code, and offers a policy-driven protection strategy that allows end users to tune the security-performance tradeoff according to their needs. We present an implementation of StackArmor for x86-64 Linux and provide a detailed experimental analysis of our prototype on popular server programs and standard benchmarks (SPEC CPU2006). Our results demonstrate that StackArmor offers better security than prior binary- and source-level approaches, at the cost of only modest performance and memory overhead even with full protection.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480441", "vector": [], "sparse_vector": [], "title": "Thwarting Cache Side-Channel Attacks Through Dynamic Software Diversity.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We explore software diversity as a defense against side-channel attacks by dynamically and systematically randomizing the control flow of programs. Existing software diversity techniques transform each program trace identically. Our diversity based technique instead transforms programs to make each program trace unique. This approach offers probabilistic protection against both online and off-line side-channel attacks. In particular, we create a large number of unique program execution paths by automatically generating diversified replicas for parts of an input program. Replicas derived from the same original program fragment have different implementations, but perform semantically equivalent computations. At runtime we then randomly and frequently switch between the replicas. We evaluate how well our approach thwarts cache-based side-channel attacks, in which an attacker strives to recover cryptographic keys by analyzing side-effects of program execution. Our method requires no manual effort or hardware changes, has a reasonable performance impact, and reduces side-channel information leakage significantly.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480442", "vector": [], "sparse_vector": [], "title": "Isomeron: Code Randomization Resilient to (Just-In-Time) Return-Oriented Programming.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Until recently, it was widely believed that code randomization (such as fine-grained ASLR) can effectively mitigate code reuse attacks. However, a recent attack strategy, dubbed just-in-time return oriented programming (JIT-ROP), circumvents code randomization by disclosing the (randomized) content of many memory pages at runtime. In order to remedy this situation, new and improved code randomization defenses have been proposed. The contribution of this paper is twofold: first, we conduct a security analysis of a recently proposed fine-grained ASLR scheme that aims at mitigating JIT-ROP based on hiding direct code references in branch instructions. In particular, we demonstrate its weaknesses by constructing a novel JIT-ROP attack that is solely based on exploiting code references residing on the stack and heap. Our attack stresses that designing code randomization schemes resilient to memory disclosure is highly challenging. Second, we present a new and hybrid defense approach, dubbed Isomeron, that combines code randomization with execution-path randomization to mitigate conventional ROP and JIT-ROP attacks. Our reference implementation of Isomeron neither requires source code nor a static analysis phase. We evaluated its efficiency based on SPEC benchmarks and discuss its effectiveness against various kinds of code reuse attacks.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480443", "vector": [], "sparse_vector": [], "title": "What&apos;s in Your Dongle and Bank Account? Mandatory and Discretionary Protection of Android External Resources.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON> Naveed", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The pervasiveness of security-critical external re- sources (e.g accessories, online services) poses new challenges to Android security. Prior research reveals that given the BLUETOOTH and BLUETOOTH_ADMIN permissions, a malicious app on an authorized phone gains unfettered access to any Bluetooth device (e.g., Blood Glucose meter, etc.). Our study further shows that sensitive text messages from online banking services and social networks (account balance, password reset links, etc.) are completely exposed to any app with either the RECEIVE_SMS or the READ_SMS permission. Similar security risks are present in other channels (Internet, Audio and NFC) extensively used to connect the phone to assorted external devices or services. Fundamentally, the current permission-based Discre- tionary Access Control (DAC) and SEAndroid-based Mandatory Access Control (MAC) are too coarse-grained to protect those resources: whoever gets the permission to use a channel is automatically allowed to access all resources attached to it. To address this challenge, we present in this paper SEACAT, a new security system for fine-grained, flexible protection on external resources. SEACAT supports both MAC and DAC, and integrates their enforcement mechanisms across the Android middleware and the Linux kernel. It extends SEAndroid for specifying policies on external resources, and also hosts a DAC policy base. Both sets of policies are managed under the same policy engine and Access Vector Cache that support policy checks within the security hooks distributed across the framework and the Linux kernel layers, over different channels. This integrated security model was carefully designed to ensure that miscon- figured DAC policies will not affect the enforcement of MAC policies, which manufacturers and system administrators can leverage to define their security rules. In the meantime, a policy management service is offered to the ordinary Android users for setting policies that protect the resources provided by the third party. This service translates simple user selections into SELinux- compatible policies in the background. Our implementation is capable of thwarting all known attacks on external resources at a negligible performance cost.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480444", "vector": [], "sparse_vector": [], "title": "ABY - A Framework for Efficient Mixed-Protocol Secure Two-Party Computation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Secure computation enables multiple mutually distrusting parties to jointly evaluate functions on their private inputs without revealing anything but the result. Generic secure computation protocols in the semi-honest model have been studied extensively and several best practices have evolved. In this work, we design and implement a mixed-protocol framework, called ABY, that efficiently combines secure computation schemes based on Arithmetic sharing, Boolean sharing, and <PERSON>’s garbled circuits and that makes available best practice solutions in secure two-party computation. Our framework allows to pre-compute almost all cryptographic operations and provides novel highly efficient conversions between secure computation schemes based on pre-computed oblivious transfer extensions. ABY supports several standard operations and we perform benchmarks on a local network and in a public intercontinental cloud. From our benchmarks we deduce new insights on the efficient design of secure computation protocols, most prominently that oblivious transfer-based multiplications are much more efficient than using homomorphic encryption. We use our framework to construct mixed-protocols for three example applications, private set intersection, biometric matching, and modular exponentiation, and show that they are much more efficient than using a single protocol.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480445", "vector": [], "sparse_vector": [], "title": "SPHINX: Detecting Security Attacks in Software-Defined Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Software-defined networks (SDNs) allow greater control over network entities by centralizing the control plane, but place great burden on the administrator to manually ensure security and correct functioning of the entire network. We list several attacks on SDN controllers that can be mounted by compromised network entities, such as end hosts and soft switches, and demonstrate their feasibility on four popular SDN controllers. We propose SPHINX to detect both known and potentially unknown attacks originating within an SDN. SPHINX dynamically assimilates new network behavior and raises alerts when it detects suspicious changes to existing network control plane behavior. Our evaluation shows that SPHINX is capable of detecting attacks in SDNs in realtime with low performance overheads, and requires no changes to the SDN controllers for deployment.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480446", "vector": [], "sparse_vector": [], "title": "Preventing Lunchtime Attacks: Fighting Insider Threats With Eye Movement Biometrics.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce a novel biometric based on distinctive eye movement patterns. The biometric consists of 21 features that allow us to reliably distinguish users based on differences in these patterns. We leverage this distinguishing power along with the ability to gauge the users’ task familiarity, i.e., level of knowledge, to address insider threats. In a controlled experiment we test how both time and task familiarity influence eye movements and feature stability, and how different subsets of features affect the classifier performance. These feature subsets can be used to tailor the eye movement biometric to different authentication methods and threat models. Our results show that eye movement biometrics support reliable and stable identification and authentication of users. We investigate different approaches in which an attacker could attempt to use inside knowledge to mimic the legitimate user. Our results show that while this advance knowledge is measurable, it does not increase the likelihood of successful impersonation. In order to determine the time stability of our features we repeat the experiment twice within two weeks. The results indicate that we can reliably authenticate users over the entire period. We show that the classification decision depends on all features and mimicking a few of them will not be sufficient to trick the classifier. We discuss the advantages and limitations of our approach in detail and give practical insights on the use of this biometric in a real-world environment.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480447", "vector": [], "sparse_vector": [], "title": "EKHunter: A Counter-Offensive Toolkit for Exploit Kit Infiltration.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Venkat <PERSON>", "<PERSON><PERSON>"], "summary": "The emergence of exploit kits is one of the most important developments in modern cybercrime. Much of cybersecurity research in the recent years has been devoted towards defending citizens from harm delivered through exploit kits. In this paper, we examine an alternate, counter-offensive strategy towards combating cyber-crime launched through exploit kits. Towards this goal, we survey a wide range of 30 real-world exploit kits and analyze a counter-offensive adversarial model against the kits and kit operator. Guided by our analysis, we present a systematic methodology for examining a given kit to determine where vulnerabilities may reside within its serverside implementation. In our experiments, we found over 180 vulnerabilities among our surveyed 16 exploit kits, and were able to automatically synthesize exploits for infiltrating 6 of them. The results validate our hypothesis that exploit kits largely lack of sophistication necessary to resist counter-offensive activities. We then propose the design of EKHUNTER, a system that is capable of automatically detecting the presence of exploit vulnerabilities and deriving laboratory test cases that can compromise both the integrity of a fielded exploit kit, and even the identity of the kit operator.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480448", "vector": [], "sparse_vector": [], "title": "NSEC5: Provably Preventing DNSSEC Zone Enumeration.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper uses cryptographic techniques to study the problem of zone enumeration in DNSSEC. DNSSEC is designed to prevent network attackers from tampering with domain name system (DNS) messages. The cryptographic machinery used in DNSSEC, however, also creates a new vulnerability, zone enumeration, enabling an adversary to use a small number of online DNSSEC queries combined with offline dictionary attacks to learn which domain names are present or absent in a DNS zone. We prove that the design underlying current DNSSEC standard, with NSEC and NSEC3 records, inherently suffers from zone enumeration: specifically, we show that security against network attackers and privacy against zone enumeration cannot be satisfied simultaneously unless the DNSSEC server performs online public-key cryptographic operations. We then propose NSEC5, a new cryptographic construction that solves the problem of DNSSEC zone enumeration while remaining faithful to the operational realities of DNSSEC. NSEC5 can be thought of as a variant of NSEC3 in which an the unkeyed hash function is replaced with an RSA-based keyed hashing scheme.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480449", "vector": [], "sparse_vector": [], "title": "Information Flow Analysis of Android Applications in DroidSafe.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present DroidSafe, a static information flow analysis tool that reports potential leaks of sensitive information in Android applications. DroidSafe includes a comprehensive model of the Android API and runtime, built on top of the Android Open Source Project implementation of the Android API. This model accurately captures the data-flow and aliasing semantics of API calls, life-cycle event handlers, callback handlers, and native methods. DroidSafe includes an analysis to statically resolve dynamic inter-component communication linkage mechanisms, enabling DroidSafe to precisely track intent- and message- and RPC-mediated information flows that traverse multiple Android components. The DroidSafe information flow analysis has high-depth heap and method object-sensitivity, and the analysis considers all possible interleavings of life-cycle events and callback handlers. We also present several domain-specific analyses that significantly enhance DroidSafe’s ability to successfully analyze Android applications. We evaluate DroidSafe on a suite of 24 real-world Android applications that contain malicious information leaks. These applications were developed by independent, hostile Red Team organizations. The malicious flows in these applications were designed specifically to evade or overwhelm information flow analysis tools. DroidSafe detects all of the malicious flows in all 24 applications. We compare DroidSafe to a current state-of-the-art analysis, which detects malicious flows in only 3 of these applications. We also evaluate DroidSafe on DroidBench version 1.2, a suite of 65 independently-developed Android micro-applications designed to evaluate the capabilities of information flow analysis systems. We report the highest information flow precision and recall to date for DroidBench 1.2.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480450", "vector": [], "sparse_vector": [], "title": "Knock Yourself Out: Secure Authentication with Short Re-Usable Passwords.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present Knock Yourself Out (KYO), a password generator that enables secure authentication against a computationally unbounded adversary. Master passwords can be surprisingly short and may be re-used for multiple service accounts even in the event of client compromises and multiple server compromises. At the same time, KYO is transparent to service operators and backwards-compatible. Master passwords are fully client-manageable while secrets shared with service operators can be kept constant. Likewise, secrets can be changed without having to change one’s passwords. KYO does not rely on collision-resistant hash functions and can be implemented with fast non-cryptographic hash functions. We detail the design of KYO and we analyze its security mathematically in a random hash function model. In our empirical evaluation we find that KYO remains secure even if small sets of hash functions are used instead, in other words, KYO requires minimal storage and is highly practical.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480451", "vector": [], "sparse_vector": [], "title": "Phoneypot: Data-driven Understanding of Telephony Threats.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Cyber criminals are increasingly using robocalling, voice phishing and caller-id spoofing to craft attacks that are being used to scam unsuspecting users who have traditionally trusted the telephone. It is necessary to better understand telephony threats to effectively combat them. Although there exist crowd sourced complaint datasets about telephony abuse, such complaints are often filed after a user receives multiple calls over a period of time, and sometimes they lack important information. We believe honeypot technologies can be used to augment telephony abuse intelligence and improve its quality. However, a telephony honeypot presents several new challenges that do not arise in other traditional honeypot settings. We present Phoneypot, a large scale telephony honeypot, that allowed us to explore ways to address these challenges. By presenting a concrete implementation of Phoneypot using a cloud infrastructure and close to 39,696 phone numbers (phoneytokens), we provide evidence of the value of telephony honeypots. Phoneypot received 1.3 million calls from 250K unique sources over a seven week period. We detected several debt collectors and telemarketers calling patterns and an instance of a telephony denial-of-service attack. This provides us with new insights into telephony abuse and attack patterns.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480452", "vector": [], "sparse_vector": [], "title": "Poisoning Network Visibility in Software-Defined Networks: New Attacks and Countermeasures.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Software-Defined Networking (SDN) is a new networking paradigm that grants a controller and its applications an omnipotent power to have holistic network visibility and flexible network programmability, thus enabling new innovations in network protocols and applications. One of the core advantages of SDN is its logically centralized control plane to provide the entire network visibility, on which many SDN applications rely. For the first time in the literature, we propose new attack vectors unique to SDN that seriously challenges this foundation. Our new attacks are somewhat similar in spirit to spoofing attacks in legacy networks (e.g., ARP poisoning attack), however with significant differences in exploiting unique vulnerabilities how current SDN operates differently from legacy networks. The successful attacks can effectively poison the network topology information, a fundamental building block for core SDN components and topology-aware SDN applications. With the poisoned network visibility, the upper layer OpenFlow Controller services/apps may be totally misled, leading to serious hijacking, denial of service or man-in-the-middle attacks. According to our study, all current major SDN Controllers we find in the market (e.g., Floodlight, OpenDaylight, Beacon, POX) are affected, i.e., they are subject to the Network Topology Poisoning Attacks. We then investigate the mitigation methods against the Network Topology Poisoning Attacks and present OFTOPOSEC, a new security extension to SDN Controllers, which provides automatic and real-time detection of Network Topology Poisoning Attacks. Our evaluation on a prototype implementation of OFTOPOSEC in Floodlight Controller shows that the defense solution can effectively secure network topology while introducing only a minor impact on normal operation of OpenFlow Controllers.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480453", "vector": [], "sparse_vector": [], "title": "SeCReT: Secure Channel between Rich Execution Environment and Trusted Execution Environment.", "authors": ["<PERSON>", "Sunjune Kong", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "ARM TrustZone, which provides a Trusted Execution Environment (TEE), normally plays a role in keeping security-sensitive resources safe. However, to properly control access to the resources, it is not enough to just isolate them from the Rich Execution Environment (REE). In addition to the isolation, secure communication should be guaranteed between security-critical resources in the TEE and legitimate REE processes that are permitted to use them. Even though there is a TEE security solution – namely, a kernel-integrity monitor – it aims to protect the REE kernel’s static regions, not to secure communication between the REE and TEE. We propose SeCReT to ameliorate this problem. SeCReT is a framework that builds a secure channel between the REE and TEE by enabling REE processes to use session keys in the REE that is regarded as unsafe region. SeCReT provides the session key to a requestor process only when the requestor’s code and control flow integrity are verified. To prevent the key from being exposed to an attacker who already compromised the REE kernel, SeCReT flushes the key from the memory every time the processor switches into kernel mode. In this paper, we present the design and implementation of SeCReT to show how it protects the key in the REE. Our prototype is implemented on Arndale board, which offers a Cortex-A15 dual-core processor with TrustZone as its security extension. We performed a security analysis by using a kernel rootkit and also ran LMBench microbenchmark to evaluate the performance overhead imposed by SeCReT.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480454", "vector": [], "sparse_vector": [], "title": "On Your Social Network De-anonymizablity: Quantification and Large Scale Evaluation with Seed Knowledge.", "authors": ["Shouling Ji", "Weiqing Li", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we conduct the first comprehensive quantification on the perfect de-anonymizability and partial de-anonymizability of real world social networks with seed information in general scenarios, where a social network can follow an arbitrary distribution model. This quantification provides the theoretical foundation for existing structure based de-anonymization attacks (e.g., \\cite{bacdwowww07}\\cite{narshmsp09}\\cite{srihicccs12}) and closes the gap between de-anonymization practice and theory. Besides that, our quantification can serve as a testing-stone for the effectiveness of anonymization techniques, i.e., researchers can employ our quantified structural conditions to evaluate the potential de-anonymizability of the anonymized social networks. Based on our quantification, we conduct a large scale evaluation on the de-anonymizability of 24 various real world social networks by quantitatively showing: 1) the conditions for perfect and (1-\\epsilon) de-anonymization of a social network, where \\epsilon specifies the tolerated de-anonymization error, and 2) how many users of a social network can be successfully de-anonymized. Furthermore, we show that, both theoretically and experimentally, the overall structural information based de-anonymization attack is much more powerful than the seed knowledge-only based de-anonymization attack, and even without any seed information, a social network can be perfectly or partially de-anonymized. Finally, we discuss the implications of this work. Our findings are expected to shed light on the future research of the structural data anonymization and de-anonymization area, and to help data owners evaluate their structural data vulnerability before data sharing and publishing.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480455", "vector": [], "sparse_vector": [], "title": "Principled <PERSON><PERSON> for Anomaly Detection.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a technique and implemented system, <PERSON>, for obtaining probabilistic bounds on false positive rates for anomaly detectors that process Internet data. Using a probability distribution based on PageRank and an efficient algorithm to draw samples from that probability distribution, <PERSON> computes an estimated false positive rate and a probabilistic bound on the accuracy of the estimated rate. By drawing test samples from a well defined distribution that correlates well with data seen in practice, <PERSON> improves on ad hoc methods for estimating the false positive rate of anomaly detectors. Specifically, our methods give bounds that are reproducible, comparable across different anomaly detectors, and theoretically sound. Experimental results from applying <PERSON> to three anomaly detectors (SIFT, SOAP, and JSAND) show that <PERSON> is efficientenough to use in practice — <PERSON> can sample enough inputs to obtain tight false positive rate bounds in an acceptable amount of time. These results indicate that <PERSON> can, in practice, help place anomaly detection on a stronger theoretical foundation and help practitioners better understand the behavior and consequences of the anomaly detectors that they deploy.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480456", "vector": [], "sparse_vector": [], "title": "Upgrading HTTPS in mid-air: An empirical study of strict transport security and key pinning.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We have conducted the first in-depth empirical study of two important new web security features, strict transport security (HSTS) and public-key pinning. Both have been added to the web platform to harden HTTPS, the prevailing standard for secure web browsing. While HSTS is further along, both features still have very limited deployment at a few large websites and a long tail of small security-conscious sites. We find evidence of developers not understanding the correct use of these features, with a substantial portion using them in invalid or illogical ways. We also identify a number of subtle but important errors in practical deployments which often undermine the security these new features are meant to provide. For example, the majority of pinned domains undermine the security benefits of pinning by loading non-pinned resources with the ability to hijack the page. A substantial portion of HSTS domains and nearly all pinned domains leaked cookie values, including login cookies, due to the poorly-understood interaction between HTTP cookies and the same-origin policy. Our findings highlight that the web platform, as well as modern web sites, are large and complicated enough to make even conceptually simple security upgrades challenging to deploy in practice.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480457", "vector": [], "sparse_vector": [], "title": "P2C: Understanding Output Data Files via On-the-Fly Transformation from Producer to Consumer Executions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In cyber attack analysis, it is often highly desirable to understand the meaning of an unknown file or a network message in the absence of their consumer (i.e. the program that parses and understands the file/message). For example, a malware may stealthily collect information from a victim machine, store them as a file and later send it to a remote server. P2C is a novel technique that can parse and understand unknown files and network messages. Given a file/message that was generated in the past without the presence of any monitoring techniques, and a set of potential producers of the file/message, P2C systematically explores the execution paths in the producers without requiring any inputs. In the mean time, it tries to transform a producer execution to a consumer execution that closely resembles the ideal consumer execution that can parse the given unknown file/message. In particular, when a write operation is encountered in the original execution, P2C performs the opposite read operation on the unknown file/message and patches the original execution with the loaded value. In order to handle correlations between data fields in the file/message, P2C follows a trial-and-error approach to look for the correct transformation until the file/message can be parsed and the meaning of their fields can be disclosed. Our experiments on a set of real world applications demonstrate P2C is highly effective.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480458", "vector": [], "sparse_vector": [], "title": "Preventing Use-after-free with Dangling Pointers Nullification.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON> Lee", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Jang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Many system components and network applications are written in the unsafe C/C++ languages, and there have been countless cases where simple mistakes by developers resulted in memory corruption vulnerabilities and consequently security exploits. While there have been tremendous research efforts to mitigate these vulnerabilities, use-after-free still remains one of the most critical and popular attack vectors because existing proposals have not adequately addressed the challenging program analysis and runtime performance issues. In this paper, we present DangNull, a system that detects temporal memory safety violations, in particular, use-after-free or double-free, during runtime. DangNull relies on the key observation that the root cause of these violations is that the pointers are not nullified after the target object is freed. Based on this observation, DangNull automatically traces the object’s relationships via pointers, and automatically nullifies all pointers when the target object is freed. DangNull offers several benefits. First, DangNull addresses the origin (or, the root cause) of temporal memory safety violations. That is, it does not rely on the side effects of violations, which vary and may be masked by attacks. Thus, DangNull is effective against even the most sophisticated exploitation techniques. Second, DangNull checks the object relationship information using runtime object range analysis on pointers, and thus is able to keep track of pointer semantics more robustly even in complex and large scale software. Lastly, DangNull does not require numerous explicit sanity checks on memory access because it can detect a violation with implicit exception handling, and thus its detection capabilities only incur moderate performance overheads.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480459", "vector": [], "sparse_vector": [], "title": "Too LeJIT to Quit: Extending JIT Spraying to ARM.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In the face of widespread DEP and ASLR deployment, JIT spraying brings together the best of code injection and code reuse attacks to defeat both defenses. However, to date, JIT spraying has been an x86-only attack thanks to its reliance on variable-length, unaligned instructions. In this paper, we finally extend JIT spraying to a RISC architecture by introducing a novel technique called gadget chaining, whereby high level code invokes short sequences of unintended and intended instructions called gadgets just like a function call. We demonstrate gadget chaining in an end-to-end JIT spraying attack against WebKit’s JavaScriptCore JS engine on ARM and found that existing JIT spray mitigations that were sufficient against the x86 version of the JIT spraying attack fall short in the face of gadget chaining.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480460", "vector": [], "sparse_vector": [], "title": "Checking More and Alerting Less: Detecting Privacy Leakages via Enhanced Data-flow Analysis and Peer Voting.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Serious concerns have been raised about stealthy disclosures of private user data in smartphone apps, and recent research efforts in mobile security have studied various types of detection of privacy disclosures. However, existing approaches are not effective in informing users and security analysts about potential privacy leakage threats. This is because these methods largely fail to: 1) provide highly accurate and inclusive detection of privacy disclosures; 2) filter out the legitimate privacy disclosures that usually dominate the detection results and in turn obscure the true threats. In this paper, we propose AAPL, an automated system that detects privacy leaks (i.e., truly suspicious privacy disclosures) in Android apps. AAPL is based on multiple special static analysis techniques that we’ve developed for Android apps, including conditional flow identification and joint flow tracking. Furthermore, AAPL employs a new approach called peer voting to filter out most of the legitimate privacy disclosures from the results, purifying the detection results for automatic and easy interpretation. We implemented AAPL and evaluated it over 40,456 apps. The results show that, on average, AAPL achieves an accuracy of 88.68%. For particular disclosures, e.g., contacts, the accuracy is up to 94.64%. Using AAPL, we successfully revealed a collection of apps that cause privacy leaks. The throughput of our privacy disclosure analysis module is 4.5 apps per minute on a three- machine cluster.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480461", "vector": [], "sparse_vector": [], "title": "Integrated Circuit (IC) Decamouflaging: Reverse Engineering Camouflaged ICs within Minutes.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Circuit camouflaging is a recently proposed defense mechanism to protect digital integrated circuits (ICs) from reverse engineering attacks by using camouflaged gates, i.e., logic gates whose functionality cannot be precisely determined by the attacker. Recent work in CCS’13 appears to establish that an attacker requires time that is exponential in the number of camouflaged gates to reverse engineer a circuit, if the gates that are camouflaged are chosen using a procedure proposed in that work. Consequently, it appears to be the case that even by camouflaging a relatively small number of gates in the circuit, the attacker is forced to undertake several thousands of years of work. In this paper, we refute such claims. With an underlying complexity-theoretic mindset, we show that the same benchmark circuits with the camouflaged gates chosen the same way as prior work, we can decamouflage the circuit in minutes, and not years. As part of constructing our attack, we provide a precise characterization of two problems that the attacker seeks to solve to carry out his attack, and their computational complexity. A composition of solvers for the two problems is our attack procedure. We show that the two problems are co-NP-complete and NP-complete respectively, and our reduction to boolean satisfiability (SAT) and the use of off-the-shelf SAT solvers results in a highly effective attack. We also propose a new notion that we call a discriminating set of input patterns, that soundly captures the attacker’s difficulty. Our extensive empirical studies reveal that the discriminating sets of inputs for realistic circuits are surprising small, thereby providing an explanation for the effectiveness of our attack. We provide additional insights by comparing the procedure of choosing gates to be camouflaged proposed in prior work to simply choosing them randomly. After presenting the results from our attack, we provide insights into the fundamental effectiveness of IC camouflaging. Our work serves as a strong caution to designers of ICs that seek security through IC camouflaging.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480462", "vector": [], "sparse_vector": [], "title": "Opaque Control-Flow Integrity.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A new binary software randomization and Control-Flow Integrity (CFI) enforcement system is presented, which is the first to resist code-reuse attacks launched by informed adversaries who possess full knowledge of the in-memory code layout of victim programs. While fine-grained code randomization continues to be an effective defense against brute-force code-reuse attacks such as Return-Oriented Programming (ROP), researchers have recently demonstrated the feasibility of implementation disclosure attacks that can potentially divulge most or all of the in-memory code layout of victim processes. Such attacks defeat fine-grained randomization defenses by revealing the randomized locations of the code gadgets that attackers abuse to effect code-reuse attacks. Opaque CFI (O-CFI) is the first exploit mitigation technique that resists this latest wave of attacks against fine-grained code randomization. By combining fine-grained code-randomization with coarse-grained integrity checks, it conceals the graph of hijackable control-flow edges even from attackers who can view the complete stack, heap, and binary code of the victim process. For maximal efficiency, the integrity checks are implemented using instructions that will soon be hardware-accelerated on commodity x86-x64 processors. The approach is highly practical since it does not require a modified compiler and can protect legacy binaries without access to source code. Experiments using our fully functional prototype implementation show that O-CFI provides significant probabilistic protection against ROP attacks launched by adversaries with complete code layout knowledge, and exhibits only 4.97% mean performance overhead on current hardware (with further overhead reductions to follow on forthcoming Intel processors).", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480463", "vector": [], "sparse_vector": [], "title": "Bloom Cookies: Web Search Personalization without User Tracking.", "authors": ["<PERSON><PERSON><PERSON>", "Oriana Riva", "<PERSON><PERSON>", "<PERSON>"], "summary": "We propose Bloom cookies that encode a user’s profile in a compact and privacy-preserving way, without preventing online services from using it for personalization purposes. The Bloom cookies design is inspired by our analysis of a large set of web search logs that shows drawbacks of two profile obfuscation techniques, namely profile generalization and noise injection, today used by many privacy-preserving personalization systems. We find that profile generalization significantly hurts personalization and fails to protect users from a server linking user sessions over time. Noise injection can address these problems, but only at the cost of a high communication overhead and a noise dictionary generated by a trusted third party. In contrast, Bloom cookies leverage Bloom filters as a privacy-preserving data structure to provide a more convenient privacy, personalization, and network efficiency tradeoff: they provide similar (or better) personalization and privacy than noise injection (and profile generalization), but with an order of magnitude lower communication cost and no noise dictionary. We discuss how Bloom cookies can be used for personalized web search, present an algorithm to automatically configure the noise in Bloom cookies given a user’s privacy and personalization goals, and evaluate their performance compared to the state-of-the-art.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480464", "vector": [], "sparse_vector": [], "title": "Privacy Preserving Payments in Credit Networks: Enabling trust with privacy in online marketplaces.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A credit network models trust between agents in a distributed environment and enables payments between arbitrary pairs of agents. With their flexible design and robustness against intrusion, credit networks form the basis of several Sybil-tolerant social networks, spam-resistant communication protocols, and payment systems. Existing systems, however, expose agents’ trust links as well as the existence and volumes of payment transactions, which is considered sensitive information in social environments or in the financial world. This raises a challenging privacy concern, which has largely been ignored by the research on credit networks so far. This paper presents PrivPay, the first provably secure privacy-preserving payment protocol for credit networks. The distinguishing feature of PrivPay is the obliviousness of transactions, which entails strong privacy guarantees for the network links. PrivPay does not require any trusted third party, maintains a high accuracy of the transactions, and provides an economical solution to network service providers. It is also general-purpose and applicable to all credit network-based systems. We implemented PrivPay and demonstrated its practicality by privately emulating transactions performed in the Ripple payment system over a period of four months.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480465", "vector": [], "sparse_vector": [], "title": "I Do Not Know What You Visited Last Summer: Protecting users from stateful third-party web tracking with TrackingFree browser.", "authors": ["Xiang Pan", "<PERSON><PERSON> Cao", "<PERSON>"], "summary": "Stateful third-party web tracking has drawn the attention of public media given its popularity among top Alexa web sites. A tracking server can associate a unique identifier from the client side with the private information contained in the referer header of the request to the tracking server, thus recording the client’s behavior. Faced with the significant problem, existing works either disable setting tracking identifiers or blacklist third-party requests to certain servers. However, neither of them can completely block stateful web tracking. In this paper, we propose TrackingFree, the first anti-tracking browser by mitigating unique identifiers. Instead of disabling those unique identifiers, we isolate them into different browser principals so that the identifiers still exist but are not unique among different web sites. By doing this, we fundamentally cut off the tracking chain for third-party web tracking. Our evaluation shows that TrackingFree can block all the 647 trackers found in Alexa Top 500 web sites while still preserving web sites’ functionalities, and we formally verified the anti-tracking ability of TrackingFree by Alloy.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480466", "vector": [], "sparse_vector": [], "title": "DEFY: A Deniable, Encrypted File System for Log-Structured Storage.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "While solutions for file system encryption can prevent an adversary from determining the contents of files, in situations where a user wishes to hide the existence of data, encryption alone is not sufficient. Indeed, encryption may draw attention to those files, as they may likely contain information the user wishes to keep secret; adversarial coercion may motivate the owner to surrender their encryption keys, under duress. This paper presents DEFY, a deniable file system designed to work within the technical constraints imposed by solid-state drives, such as those found in mobile devices. These have consequential properties that previous work largely ignores. Further, DEFY provides features not offered by prior work, including: authenticated encryption, fast secure deletion and support for multiple layers of deniability. We consider security against a snapshot adversary, the strongest deniable filesystem adversary considered by prior literature. We have implemented a prototype based on YAFFS and an evaluation shows DEFY exhibits performance degradation comparable to the encrypted file system for flash, WhisperYAFFS.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480467", "vector": [], "sparse_vector": [], "title": "Securing the Software Defined Network Control Layer.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Software-defined networks (SDNs) pose both an opportunity and challenge to the network security community. The opportunity lies in the ability of SDN applications to express intelligent and agile threat mitigation logic against hostile flows, without the need for specialized inline hardware. However, the SDN community lacks a secure control-layer to manage the interactions between the application layer and the switch infrastructure (the data plane). There are no available SDN controllers that provide the key security features, trust models, and policy mediation logic, necessary to deploy multiple SDN applications into a highly sensitive computing environment. We propose the design of security extensions at the control layer to provide the security management and arbitration of conflicting flow rules that arise when multiple applications are deployed within the same network. We present a prototype of our design as a Security Enhanced version of the widely used OpenFlow Floodlight Controller, which we call SE-Floodlight. SE-Floodlight extends Floodlight with a security-enforcement kernel (SEK) layer, whose functions are also directly applicable to other OpenFlow controllers. The SEK adds a unique set of secure application management features, including an authentication service, role-based authorization, a permission model for mediating all configuration change requests to the data-plane, inline flow-rule conflict resolution, and a security audit service. We demonstrate the robustness of our system implementation both through a comprehensive functionality testing and a performance evaluation that illustrates its sub-linear scaling properties.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480468", "vector": [], "sparse_vector": [], "title": "vfGuard: Strict Protection for Virtual Function Calls in COTS C++ Binaries.", "authors": ["<PERSON><PERSON><PERSON>", "Xunchao Hu", "<PERSON><PERSON>"], "summary": "Control flow integrity is an important security property that needs to be enforced to prevent control-flow hijacking attacks. The existing CFI protections for COTS binaries are too permissive, and still vulnerable to sophisticated code reusing attacks. In this paper, we aim to provide more stringent protection for COTS C++ binaries, with respect to their virtual function calls. To achieve this goal, we need to reliably recover C++ semantics, including VTables and virtual callsites. With the extracted C++ semantics, we construct a sound CFI policy and further improve the policy precision by devising two filters. We implemented a prototype system called vfGuard, and evaluated its effectiveness with realworld large C++ binaries, such as web browsers. Our experiments demonstrated that we can construct sound and much more precise CFI policies to protect virtual function calls in realworld large C++ binaries.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480469", "vector": [], "sparse_vector": [], "title": "Firmalice - Automatic Detection of Authentication Bypass Vulnerabilities in Binary Firmware.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Embedded devices have become ubiquitous, and they are used in a range of privacy-sensitive and security-critical applications. Most of these devices run proprietary software, and little documentation is available about the software’s inner workings. In some cases, the cost of the hardware and protection mechanisms might make access to the devices themselves infeasible. Analyzing the software that is present in such environments is challenging, but necessary, if the risks associated with software bugs and vulnerabilities must be avoided. As a matter of fact, recent studies revealed the presence of backdoors in a number of embedded devices available on the market. In this paper, we present Firmalice, a binary analysis framework to support the analysis of firmware running on embedded devices. Firmalice builds on top of a symbolic execution engine, and techniques, such as program slicing, to increase its scalability. Furthermore, Firmalice utilizes a novel model of authentication bypass flaws, based on the attacker’s ability to determine the required inputs to perform privileged operations. We evaluated Firmalice on the firmware of three commercially-available devices, and were able to detect authentication bypass backdoors in two of them. Additionally, Firmalice was able to determine that the backdoor in the third firmware sample was not exploitable by an attacker without knowledge of a set of unprivileged credentials.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480470", "vector": [], "sparse_vector": [], "title": "Exploiting and Protecting Dynamic Code Generation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Many mechanisms have been proposed and deployed to prevent exploits against software vulnerabilities. One of the most effective and efficient is W xor X that prevents memory pages from being writable and executable at the same time. This enforcement completely renders the decades old shellcode injection technique infeasible. In this paper, we demonstrate that the traditional shellcode injection attack can be revived through a code cache injection technique. Specifically, dynamic code generation, a technique widely used in just-in-time (JIT) compilation and dynamic binary translation (DBT), generates and modifies code on the fly, in order to promote performance or security. The dynamically generated code fragments are stored in code cache, which is writable and executable either at the same time or alternately, resulting in an opportunity for exploit. This threat is especially realistic when the generated code is multi-threaded, because simply switching between writable and executable leaves a time window for exploit. To illustrate this threat, we have crafted a proof-of-concept exploit against modern browsers that support Web Worker. To mitigate this code cache injection threat, we propose a new dynamic code generation architecture. This new architecture relocates the dynamic code generator to a separate process, in which the code cache can be modified. At the same time, in the original process where the generated code is executed, the code cache remains read-only. The code cache is synchronized between the writing process and the execution process through shared memory. Any interaction between the code generator and the generated code is handled transparently through remote-procedure call (RPC). We have ported Google V8 JavaScript engine and Strata DBT to this new architecture. Our implementation experience showed that the engineering effort for porting to this new architecture is very small. Evaluation of our prototype implementation showed that this new architecture can defeat the code cache injection attack with small performance overhead.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480471", "vector": [], "sparse_vector": [], "title": "CopperDroid: Automatic Reconstruction of Android Malware Behaviors.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Today mobile devices and their application marketplaces drive the entire economy of the mobile landscape. For instance, Android platforms alone have produced staggering revenues exceeding 5 billion USD, which unfortunately attracts cybercriminals with malware now hitting the Android markets at an alarmingly rising pace. To better understand this slew of threats, we present The System, an automatic VMI-based dynamic analysis system to reconstruct the behavior of Android malware. Based on the key observation that all interesting behaviors are eventually expressed through system calls, The System presents a novel unified analysis able to capture both low-level OS-specific and high-level Android-specific behaviors. To this end, The System presents an automatic system call-centric analysis that faithfully reconstructs events of interests, including IPC and RPC interactions and complex Android objects, to describe the behavior of Android malware regardless of whether it is initiated from Java or native code execution. The System’s analysis generates detailed behavioral profiles that abstract a large stream of low-level—sometimes uninteresting—events into concise high-level semantics, which are well-suited to provide effective insights. We carried out an extensive evaluation to assess the capabilities and performance of The System on more than 2,900 Android malware samples. Our experiments show that The System faithfully reconstructs OS- and Android-specific behaviors and, through the use of a simple yet effective app stimulation technique, successfully triggers and discloses additional behaviors on more than 60% (on average) of the analyzed malware samples, qualitatively improving code coverage of dynamic-based analyses.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480472", "vector": [], "sparse_vector": [], "title": "Mind Your Blocks: On the Stealthiness of Malicious BGP Hijacks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Some recent research presented evidence of blocks of IP addresses being stolen by BGP hijackers to launch spam campaigns [Ramachandran:SIGCOMM:2006]. This was the first time BGP hijacks were seen in the wild. Since then, only a very few anecdotal cases have been reported as if hackers were not interested in running these attacks. However, it is a common belief among network operators and ISPs that these attacks could be taking place but, so far, no one has produced evidence to back up that claim. In this paper, we analyse 18 months of data collected by an infrastructure specifically built to answer that question: are intentional stealthy BGP hijacks routinely taking place in the Internet? The identification of what we believe to be more than 2,000 malicious hijacks leads to a positive answer. The lack of ground truth is, of course, a problem but we managed to get confirmation of some of our findings thanks to an ISP unwittingly involved in hijack cases we have spotted. This paper aims at being an eye opener for the community by shedding some light on this undocumented threat. We also hope that it will spur new research to understand why these hijacks are taking place and how they can be mitigated. Depending on how BGP attacks are carried out, they can be very disruptive for the whole Internet and should be looked at very closely. As of today, as much as 20% of the whole IPv4 address space is currently allocated but not publicly announced, which makes it potentially vulnerable to such malicious BGP hijacks.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480473", "vector": [], "sparse_vector": [], "title": "Parking Sensors: Analyzing and Detecting Parked Domains.", "authors": ["<PERSON>", "W<PERSON><PERSON>", "<PERSON>"], "summary": "A parked domain is a domain which has no content other than automatically computed advertising banners and links. Despite the popularity of this practice, little is known about parked domains and domain parking services that assist domain owners in parking and monetizing their unused domains. In this paper, we explore the ecosystem of domain parking services from a security point of view, focusing mostly on everyday users who land on parked pages. By collecting data from over 8 million parked domains, we are able to map out the entities that build up the ecosystem and analyze the domain owners, parking services and advertisement syndicators involved. Furthermore, we show that users who land on parked websites are exposed to malware, inappropriate content, and elaborate scams, such as fake antivirus warnings and costly remote “technicians”. At the same time, we find a significant number of parked domains to be abusing popular names and trademarks through typosquatting and domain names confusingly similar to authoritative ones. Given the extent of observed abuse, we propose a set of features that are representative of parked pages and build a robust client-side classifier which achieves high accuracy with a negligible percentage of false positives.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480474", "vector": [], "sparse_vector": [], "title": "Efficient RAM and control flow in verifiable outsourced computation.", "authors": ["Riad S. Wahby", "<PERSON><PERSON>", "Zuocheng Ren", "<PERSON>", "<PERSON>"], "summary": "Recent work on proof-based verifiable computation has resulted in built systems that employ tools from complexity theory and cryptography to address a basic problem in systems security: allowing a local computer to outsource the execution of a program while providing the local computer with a guarantee of integrity and the remote computer with a guarantee of privacy. However, support for programs that use RAM and complicated control flow has been problematic. State of the art systems either restrict the use of these constructs (e.g., requiring static loop bounds), incur sizable overhead on every step to support these constructs, or pay tremendous costs when the constructs are invoked. This paper describes Buffet, a built system that solves these problems by providing inexpensive “a la carte” RAM and dynamic control flow constructs. Buffet builds on an elegant prior approach to RAM combined with a novel adaptation of techniques from the compiler community. The result is a system that allows the programmer to express programs in an expansive subset of C (disallowing only “goto” and function pointers), can handle essentially any example in the verifiable computation literature, and achieves the best performance in the area by multiple orders of magnitude.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480475", "vector": [], "sparse_vector": [], "title": "DeepDroid: Dynamically Enforcing Enterprise Policy on Android Devices.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "It is becoming a global trend for company employees to bring their own mobile devices into workplace and access company’s assets. Besides enterprise apps, lots of personal apps from various untrusted app stores have been installed on those devices. To secure the business environment, policy enforcement on whether a certain app is allowed to access system resources or how to access are required by enterprise IT. However, Android, as the largest mobile platform with a market share of 81.9%, provides very restricted interfaces for external policy enforcement. In this paper, we present DeepDroid, a dynamic enterprise security policy enforcement scheme on Android device. Different from most existing approaches, DeepDroid is implemented by dynamic memory instrumentation of several central system processes without any firmware modification. Thus, DeepDroid could be easily deployed on various Android platforms and is free from any following impacts after an absolute remove. Moreover, by introducing Android Binder intercepting, more fine-grained context-aware supervision policy may be enforced. The security and reliability of DeepDroid are also fully considered. We develop a prototype of DeepDroid and evaluate it on different devices with a variety of Android OS versions. Evaluation results show that we can effectively enforce policies on resource-related operations with negligible performance overhead.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480476", "vector": [], "sparse_vector": [], "title": "No More Gotos: Decompilation Using Pattern-Independent Control-Flow Structuring and Semantic-Preserving Transformations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Decompilation is important for many security applications; it facilitates the tedious task of manual malware reverse engineering and enables to use source-code based security tools on binary code. This includes tools to find vulnerabilities and perform taint tracking. Recovering high-level control constructs is essential for decompilation in order to produce structured code that is suitable for human analysts and source-based program analysis techniques. State-of-the-art decompilers rely on structural analysis, a pattern-matching approach over the control flow graph, to recover control constructs from binary code. Whenever no match is found they generate goto statements and thus produce unstructured decompiled output. Those statements are problematic because they make decompiled code harder to understand and less suitable for security analysis. In this paper, we present DREAM, the first decompiler to offer a goto free output. DREAM uses a novel patten-independent control-flow structuring algorithm, which can recover all control constructs in binary programs and produce structured decompiled code without any goto statement. We also present semanticpreserving transformations which can transform unstructured control flow graphs into structured graphs. We demonstrate the correctness of our algorithms show that we outperform both the leading industry and academic decompilers: Hex-Rays and Phoenix. We use the the GNU coreutils suite of utilities as a benchmark. Apart from reducing the number of goto statements to zero DREAM also produced more compact code (less lines of code) for 72,7% of decompiled functions compared to Hex-Rays and 98,8% compared to Phoenix. We also present a comparison of Hex-Rays and DREAM when decompiling three samples from Credix, ZeusP2P and SpyEye malware families.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480477", "vector": [], "sparse_vector": [], "title": "FreeSentry: protecting against use-after-free vulnerabilities due to dangling pointers.", "authors": ["<PERSON>"], "summary": "Use-after-free vulnerabilities have become an important class of security problems due to the existence of mitigations that protect against other types of vulnerabilities. The effects of their exploitation can be just as devastating as exploiting a buffer overflow, potentially resulting in full code execution within the vulnerable program. Few protections exist against these types of vulnerabilities and they are particularly hard to fix through manual code inspection. In this paper we present FreeSentry: a mitigation that protects against use-after- free vulnerabilities by inserting dynamic runtime checks that invalidate pointers when the associated memory is released. If such an invalidated pointer is accessed, the program will subsequently crash, preventing an attacker from exploiting the vulnerability. When checking dynamically allocated memory, our approach has a moderate performance overhead on the SPEC CPU benchmarks: running with a geometric mean performance impact of around 25%. It has no overhead when deployed on widely used server side daemons such as OpenSSH or the Apache HTTP daemon. FreeSentry also discovered a previously unknown use-after-free vulnerability in one of the programs in SPEC CPU2000 benchmarks: perlbmk. This vulnerability seems to have been missed by other mitigations.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480478", "vector": [], "sparse_vector": [], "title": "VTint: Protecting Virtual Function Tables&apos; Integrity.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Chen", "<PERSON>"], "summary": "Since researchers have proposed lots of defenses to protect control data (e.g., return addresses saved on the stack) from corruption, most traditional control flow hijacking attacks become infeasible. Attackers, however, can bypass these defenses by launching advanced attacks that corrupt other data, e.g., pointers to control data. Virtual table pointers (vfptr) in C++ objects, which point to virtual function tables (vtable) consisting of virtual function pointers, now become popular targets to corrupt. Attackers can exploit use-after-free or other vulnerabilities to overwrite the vfptr to point to a fake vtable, causing further virtual function calls to be hijacked (vtable hijacking). In this paper we propose a lightweight defense solution VTint to defend binary executables against vtable hijacking attacks. It uses binary rewriting to instrument security checks before virtual function dispatches to validate vtables’ integrity. Experiments show that it only introduces a small performance overhead (less than 2%), and it can effectively protect real-world vtable hijacking attacks.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "4480479", "vector": [], "sparse_vector": [], "title": "Gracewipe: Secure and Verifiable Deletion under Coercion.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "For users in possession of password-protected encrypted data in persistent storage (i.e., ‘data at rest’), an obvious problem is that the password may be extracted by an adversary through dictionary attacks or by coercing the user. Techniques such as multi-level hidden volumes with plausible deniability, or software/hardware-based full disk encryption (FDE) cannot adequately address such an attacker. For these threats, making data verifiably inaccessible in a quick fashion may be the preferred choice, specifically for users such as government/corporate agents, journalists, and human rights activists with highly confidential secrets, when caught and interrogated in a hostile territory. Using secure storage on a Trusted Platform Module (TPM) and modern CPU’s trusted execution mode (e.g., Intel TXT), we design Gracewipe to enable secure and verifiable deletion of encryption keys through a special deletion password. An attacker cannot distinguish between a deletion and real password. He can guess the real password to unlock the target encryption key only through the valid Gracewipe environment; guessing the deletion password will trigger deletion of the real key. When coerced, a user can fake compliance, and enter the deletion password; and then the user can prove to the attacker that <PERSON><PERSON><PERSON> has been executed and the real key is no longer available (through a TPM quote), hoping that a reasonable adversary then will find no reason to keep holding the victim, and may even release her. We implement two prototypes of Gracewipe: software-based FDE system with plausible deniability (using TrueCrypt with hidden volume), hardware-based FDE (using a Seagate self-encrypting drive (SED)). Our choice of booting Windows at the end of a Gracewipe session (for the possibility of immediate adoption), poses some unique challenges. Through the design and prototypes of Gracewipe, we hope to raise awareness of a special but critical use-case of FDE systems.", "published": "2015-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}]