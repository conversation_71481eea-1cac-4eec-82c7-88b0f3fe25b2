[{"primary_key": "197007", "vector": [], "sparse_vector": [], "title": "Approximating Traveling Salesman Problems Using a Bridge Lemma.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We give improved approximations for two metric Traveling Salesman Problem (TSP) variants. In Ordered TSP (OTSP) we are given a linear ordering on a subset of nodes $o_1, \\ldots, o_k$. The TSP solution must have that $o_{i+1}$ is visited at some point after $o_i$ for each $1 \\leq i<k$. This is the special case of Precedence-Constrained TSP ($PTSP$) in which the precedence constraints are given by a single chain on a subset of nodes. In $k$-Person TSP Path (k-TSPP), we are given pairs of nodes $(s_1,t_1), \\ldots, (s_k,t_k)$. The goal is to find an $s_i$-$t_i$ path with minimum total cost such that every node is visited by at least one path. We obtain a $3/2 + e^{-1}<1.878$ approximation for OTSP, the first improvement over a trivial $\\alpha+1$ approximation where $\\alpha$ is the current best TSP approximation. We also obtain a $1 + 2 \\cdot e^{-1/2}<2.214$ approximation for k-TSPP, the first improvement over a trivial $3$-approximation. These algorithms both use an adaptation of the Bridge Lemma that was initially used to obtain improved Steiner Tree approximations [<PERSON><PERSON> et al., 2013]. Roughly speaking, our variant states that the cost of a cheapest forest rooted at a given set of terminal nodes will decrease by a substantial amount if we randomly sample a set of non-terminal nodes to also become terminals such provided each non-terminal has a constant probability of being sampled. We believe this view of the Bridge Lemma will find further use for improved vehicle routing approximations beyond this paper.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.34"}, {"primary_key": "197008", "vector": [], "sparse_vector": [], "title": "Tolls for Dynamic Equilibrium Flows.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider dynamic network flows and study the following question: Which dynamic edge flows can be implemented as tolled dynamic equilibrium flows? We study this question for the \"heterogeneous-user\" model, where the flow particles are partitioned into populations having different valuations of travel time and money spent. As our main result, we give the first characterization of this type of implementability showing that for single-source single-destination networks and heterogeneous users, a dynamic edge flow is implementable by tolls if and only if the induced subgraph of the edge flow contains no cycle of positive length containing the destination. For the proof of this result we make several technical contributions: We formulate a novel infinite dimensional optimization problem, where the goal is to minimize the weighted travel times with respect to the fixed network loading induced by the given edge flow. Using the recently introduced concept of parameterized network loadings (cf. [23]), we prove existence of optimal solutions, strong duality, and a characterization of special optimal solutions for which an inequality is tight. These results are then all used for the proof of the above mentioned main characterization.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.84"}, {"primary_key": "197012", "vector": [], "sparse_vector": [], "title": "Beyond 2-Approximation for k-Center in Graphs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Virginia Vassilevska Williams", "<PERSON>"], "summary": "We consider the classical $k$-Center problem in undirected graphs. The problem is known to have a polynomial-time 2-approximation. There are even $(2+\\varepsilon)$-approximations running in near-linear time. The conventional wisdom is that the problem is closed, as $(2-\\varepsilon)$-approximation is NP-hard when $k$ is part of the input, and for constant $k\\geq 2$ it requires $n^{k-o(1)}$ time under SETH. Our first set of results show that one can beat the multiplicative factor of $2$ in undirected unweighted graphs if one is willing to allow additional small additive error, obtaining $(2-\\varepsilon,O(1))$ approximations. We provide several algorithms that achieve such approximations for all integers $k$ with running time $O(n^{k-\\delta})$ for $\\delta>0$. For instance, for every $k\\geq 2$, we obtain an $O(mn + n^{k/2+1})$ time $(2 - \\frac{1}{2k-1}, 1 - \\frac{1}{2k-1})$-approximation to $k$-Center. For $2$-Center we also obtain an $\\tilde{O}(mn^{\\omega/3})$ time $(5/3,2/3)$-approximation algorithm. Notably, the running time of this $2$-Center algorithm is faster than the time needed to compute APSP. Our second set of results are strong fine-grained lower bounds for $k$-Center. We show that our $(3/2,O(1))$-approximation algorithm is optimal, under SETH, as any $(3/2-\\varepsilon,O(1))$-approximation algorithm requires $n^{k-o(1)}$ time. We also give a time/approximation trade-off: under SETH, for any integer $t\\geq 1$, $n^{k/t^2-1-o(1)}$ time is needed for any $(2-1/(2t-1),O(1))$-approximation algorithm for $k$-Center. This explains why our $(2-\\varepsilon,O(1))$ approximation algorithms have $k$ appearing in the exponent of the running time. Our reductions also imply that, assuming ETH, the approximation ratio 2 of the known near-linear time algorithms cannot be improved by any algorithm whose running time is a polynomial independent of $k$, even if one allows additive error.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.6"}, {"primary_key": "197016", "vector": [], "sparse_vector": [], "title": "Balancing Notions of Equity: Trade-offs Between Fair Portfolio Sizes and Achievable Guarantees.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Motivated by fairness concerns, we study the `portfolio problem': given an optimization problem with set $D$ of feasible solutions, a class $\\mathbf{C}$ of fairness objective functions on $D$, and an approximation factor $\\alpha \\ge 1$, a set $X \\subseteq D$ of feasible solutions is an $\\alpha$-approximate portfolio if for each objective $f \\in \\mathbf{C}$, there is an $\\alpha$-approximation for $f$ in $X$. Choosing the classes of top-$k$ norms, ordered norms, and symmetric monotonic norms as our equity objectives, we study the trade-off between the size $|X|$ of the portfolio and its approximation factor $\\alpha$ for various combinatorial problems. For the problem of scheduling identical jobs on unidentical machines, we characterize this trade-off for ordered norms and give an exponential improvement in size for symmetric monotonic norms over the general upper bound. We generalize this result as the OrderAndCount framework that obtains an exponential improvement in portfolio sizes for covering polyhedra with a constant number of constraints. Our framework is based on a novel primal-dual counting technique that may be of independent interest. We also introduce a general IterativeOrdering framework for simultaneous approximations or portfolios of size $1$ for symmetric monotonic norms, which generalizes and extends existing results for problems such as scheduling, $k$-clustering, set cover, and routing.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.33"}, {"primary_key": "197017", "vector": [], "sparse_vector": [], "title": "Top-k Document Retrieval in Compressed Space.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Let 𝓓 be a collection of D strings of total length n over an alphabet of size σ. We consider the so-called top-k document retrieval problem: given a short string P and an integer k, list the identifiers of k strings in 𝓓 most relevant to P, in decreasing order of relevance. Relevance may be a fixed value associated with the strings where P occurs, or the number of times P occurs in the strings. While RAM-optimal solutions using O (n log n ) bits and O (|P|/logσ n + k ) time exist, solving the problem optimally within space close to O (n log σ ) bits is open.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.137"}, {"primary_key": "197019", "vector": [], "sparse_vector": [], "title": "On the Locality of Hall&apos;s Theorem.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The last five years of research on distributed graph algorithms have seen huge leaps of progress, both regarding algorithmic improvements and impossibility results: new strong lower bounds have emerged for many central problems and exponential improvements over the state of the art have been achieved for the runtimes of many algorithms. Nevertheless, there are still large gaps between the best known upper and lower bounds for many important problems.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.143"}, {"primary_key": "197026", "vector": [], "sparse_vector": [], "title": "Partitioning a Polygon Into Small Pieces.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the problem of partitioning a given simple polygon P into a minimum number of connected polygonal pieces, each of bounded size. We describe a general technique for constructing such partitions that works for several notions of ‘bounded size,’ namely that each piece must be contained in an axis-aligned or arbitrarily rotated unit square or a unit disk, or that each piece has bounded perimeter, straight-line diameter or geodesic diameter. The problems are motivated by practical settings in manufacturing, finite element analysis, collision detection, vehicle routing, shipping and laser capture microdissection. The version where each piece should be contained in an axis-aligned unit square is already known to be NP- hard [<PERSON> and <PERSON>, FOCS, 2024], and the other versions seem no easier. Our main result is to develop constant-factor approximation algorithms, which means that the number of pieces in the produced partition is at most a constant factor larger than the cardinality of an optimal partition. Existing algorithms [<PERSON> and <PERSON>, Algorith<PERSON>a, 2004] do not allow Steiner points, which means that all corners of the produced pieces must also be corners of P. This has the disappointing consequence that a partition often does not exist, whereas our algorithms always produce meaningful partitions. Furthermore, an optimal partition without Steiner points may require Ω(n ) pieces for polygons with n corners where a partition consisting of just 2 pieces exists when Steiner points are allowed. Other existing algorithms [<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON><PERSON>, ESA, 2020] only allow P to be split along chords (and aim to minimize the number of chords instead of the number of pieces), whereas we make no constraints on the boundaries of the pieces. In a related problem, we are given a polygon P and positive real values a1,…,ak whose sum equals the area of P. The goal is to partition P into exactly k pieces Q1,…, Qk such that the area of Qi is ai. Such a partition always exists, and an algorithm with running time O (nk) has previously been described [Bast and Hert, CCCG, 2000]. We improve on this result and give an algorithm with optimal running time O (n + k ) for simple polygons and a running time of O (n log n + k ) for polygons with holes.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.118"}, {"primary_key": "197027", "vector": [], "sparse_vector": [], "title": "A Cell Probe Lower Bound for the Predecessor Search Problem in PRAM.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the predecessor search problem in the classical PRAM model of computation. In this problem, the input is a set of n (cid:96) -bit integers and the goal is to store the input in a data structure of size S ( n ) such that given a query value q , the predecessor of q can be found eﬃciently. This is a very classical problem with an extensive history. We prove a lower bound for this problem in the strongest CRCW PRAM model. A simpliﬁed version of the lower bound states that in a K -processor PRAM model with O (log n )-bit registers, the query requires Ω(log K log n ) worst-case time under the realistic setting where the space is near-linear.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.136"}, {"primary_key": "197029", "vector": [], "sparse_vector": [], "title": "Efficient Approximation Algorithm for Computing Wasserstein Barycenter under Euclidean Metric.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Given a set of probability distributions, the Wasserstein barycenter problem asks to compute a distribution that minimizes the average Wasserstein distance, or optimal transport cost, from all the input distributions. Wasserstein barycenters preserve common geometric features of the input distributions, making them useful in machine learning and data analytics tasks.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.163"}, {"primary_key": "197030", "vector": [], "sparse_vector": [], "title": "A coarse Erdős-<PERSON> theorem.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "An induced packing of cycles in a graph is a set of vertex-disjoint cycles with no edges between them. We generalise the classic Erdős-<PERSON> theorem to induced packings of cycles. More specifically, we show that there exists a function f (k) = O (k log k ) such that for every positive integer k, every graph G contains either an induced packing of k cycles or a set X of at most f (k ) vertices such that the closed neighbourhood of X intersects all cycles in G. Our proof is constructive and yields a polynomial-time algorithm finding either the induced packing of cycles or the set X. Furthermore, we show that for every positive integer d, if a graph G does not contain two cycles at distance more than d, then G contains sets X1, X2 ⊆ V (G ) with |X1| ≤ 12(d + 1) and |X2| ≤ 12 such that, after removing the ball of radius 2d around X1 or the ball of radius 3d around X2, the resulting graphs are forests.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.109"}, {"primary_key": "197033", "vector": [], "sparse_vector": [], "title": "Sublinear-Round Broadcast without Trusted Setup.", "authors": ["Andreea B<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": ",", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.141"}, {"primary_key": "197036", "vector": [], "sparse_vector": [], "title": "Low Degree Local Correction Over the Boolean Cube.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Madhu <PERSON>"], "summary": "In this work, we show that the class of multivariate degree-d polynomials mapping {0,1}n to any Abelian group G is locally correctable with Õd((log n )d) queries for up to a fraction of errors approaching half the minimum distance of the underlying code. In particular, this result holds even for polynomials over the reals or the rationals, special cases that were previously not known. Further, we show that they are locally list correctable up to a fraction of errors approaching the minimum distance of the code. These results build on and extend the prior work of <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Paraashar, Srinivasan, and Sudan [1] (STOC 2024) who considered the case of linear polynomials (d = 1) and gave analogous results. Low-degree polynomials over the Boolean cube {0,1}n arise naturally in Boolean circuit complexity and learning theory, and our work furthers the study of their coding-theoretic properties. Extending the results of [1] from linear polynomials to higher-degree polynomials involves several new challenges and handling them gives us further insights into properties of low-degree polynomials over the Boolean cube. For local correction, we construct a set of points in the Boolean cube that lie between two exponentially close parallel hyperplanes and is moreover an interpolating set for degree-d polynomials. To show that the class of degree-d polynomials is list decodable up to the minimum distance, we stitch together results on anti-concentration of low-degree polynomials, the Sunflower lemma, and the Footprint bound for counting common zeroes of polynomials. Analyzing the local list corrector of [1] for higher degree polynomials involves understanding random restrictions of non-zero degree-d polynomials on a Hamming slice. In particular, we show that a simple random restriction process for reducing the dimension of the Boolean cube is a suitably good sampler for Hamming slices. Thus our exploration unearths several new techniques that are useful in understanding the combinatorial structure of low-degree polynomials over {0, 1}n.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.187"}, {"primary_key": "197046", "vector": [], "sparse_vector": [], "title": "Forall-exist statements in pseudopolynomial time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Given a convex set $Q \\subseteq R^m$ and an integer matrix $W \\in Z^{m \\times n}$, we consider statements of the form $ \\forall b \\in Q \\cap Z^m$ $\\exists x \\in Z^n$ s.t. $Wx \\leq b$. Such statements can be verified in polynomial time with the algorithm of <PERSON><PERSON><PERSON> and its improvements if $n$ is fixed and $Q$ is a polyhedron. The running time of the best-known algorithms is doubly exponential in~$n$. In this paper, we provide a pseudopolynomial-time algorithm if $m$ is fixed. Its running time is $(m \\Delta)^{O(m^2)}$, where $\\Delta = \\|W\\|_\\infty$. Furthermore it applies to general convex sets $Q$.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.73"}, {"primary_key": "197060", "vector": [], "sparse_vector": [], "title": "Even Faster (Δ + 1)-Edge Coloring via Shorter Multi-Step Vizing Chains.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>'s Theorem from 1964 states that any n-vertex m-edge graph with maximum degree Δ can be edge colored using at most Δ + 1 colors. For over 40 years, the state-of-the-art running time for computing such a coloring, obtained independently by <PERSON><PERSON><PERSON><PERSON><PERSON> [1982] and by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON> [1985], was . Very recently, this time bound was improved in two independent works, by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON> to , and by <PERSON><PERSON><PERSON> to Õ (n2).", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.167"}, {"primary_key": "197072", "vector": [], "sparse_vector": [], "title": "Bounding ε-scatter dimension via metric sparsity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A recent work of <PERSON><PERSON> et al. [FOCS 2023] introduced the notion of ε-scatter dimension of a metric space and showed a general framework for efficient parameterized approximation schemes (so-called EPASes) for a wide range of clustering problems in classes of metric spaces that admit a bound on the ε-scatter dimension. Our main result is such a bound for metrics induced by graphs from any fixed proper minor-closed graph class. The bound is double-exponential in ε-1 and the Had<PERSON>er number of the graph class and is accompanied by a nearly tight lower bound that holds even in graph classes of bounded treewidth.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.101"}, {"primary_key": "197099", "vector": [], "sparse_vector": [], "title": "A Tight VC-Dimension Analysis of Clustering Coresets with Applications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider coresets for k-median problems, where the goal is to assign points to centers minimizing the sum of distances. Given a point set P, a coreset Ω is a small weighted subset that approximates the cost of P for all candidate solutions up to a (1 ± ε) multiplicative factor. In this paper, we give a sharp VC-dimension based analysis for k-median coreset construction. As a consequence, we obtain improved k-median coreset bounds for the following metrics: • Coresets of size Õ (kε−2) for shortest path metrics in planar graphs, improving over the bounds Õ (kε−6) by [<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, STOC’21] and Õ (k2ε−4) by [<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, SODA’21]. • Coresets of size Õ (kdℓε−2 log m) for clustering d-dimensional polygonal curves of length at most m with curves of length at most ℓ with respect to Frechet metrics, improving over the bounds Õ (k3dℓε−3 log m) by [<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, FOCS’22] and Õ (k2dℓε−2 log mlog |P|) by [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>C<PERSON><PERSON>24].", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.162"}, {"primary_key": "197102", "vector": [], "sparse_vector": [], "title": "Average-Case Hardness of Parity Problems: Orthogonal Vectors, k-SUM and More.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Virginia Vassilevska Williams"], "summary": "This work establishes conditional lower bounds for average-case {\\em parity}-counting versions of the problems $k$-XOR, $k$-SUM, and $k$-OV. The main contribution is a set of self-reductions for the problems, providing the first specific distributions, for which: $\\mathsf{parity}\\text{-}k\\text{-}OV$ is $n^{\\Omega(\\sqrt{k})}$ average-case hard, under the $k$-OV hypothesis (and hence under SETH), $\\mathsf{parity}\\text{-}k\\text{-}SUM$ is $n^{\\Omega(\\sqrt{k})}$ average-case hard, under the $k$-SUM hypothesis, and $\\mathsf{parity}\\text{-}k\\text{-}XOR$ is $n^{\\Omega(\\sqrt{k})}$ average-case hard, under the $k$-XOR hypothesis. Under the very believable hypothesis that at least one of the $k$-OV, $k$-SUM, $k$-XOR or $k$-Clique hypotheses is true, we show that parity-$k$-XOR, parity-$k$-SUM, and parity-$k$-OV all require at least $n^{\\Omega(k^{1/3})}$ (and sometimes even more) time on average (for specific distributions). To achieve these results, we present a novel and improved framework for worst-case to average-case fine-grained reductions, building on the work of Dalirooyfard, Lincoln, and Vassilevska Williams, FOCS 2020.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.158"}, {"primary_key": "197108", "vector": [], "sparse_vector": [], "title": "Almost Tight Bounds for Differentially Private Densest Subgraph.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the Densest Subgraph (DSG) problem under the additional constraint of differential privacy. DSG is a fundamental theoretical question which plays a central role in graph analytics, and so privacy is a natural requirement. All known private algorithms for Densest Subgraph lose constant multiplicative factors, despite the existence of non-private exact algorithms. We show that, perhaps surprisingly, this loss is not necessary: in both the classic differential privacy model and the LEDP model (local edge differential privacy, introduced recently by <PERSON><PERSON><PERSON><PERSON> et al. [FOCS 2022]), we give $(\\epsilon, \\delta)$-differentially private algorithms with no multiplicative loss whatsoever. In other words, the loss is \\emph{purely additive}. Moreover, our additive losses match or improve the best-known previous additive loss (in any version of differential privacy) when $1/\\delta$ is polynomial in $n$, and are almost tight: in the centralized setting, our additive loss is $O(\\log n /\\epsilon)$ while there is a known lower bound of $\\Omega(\\sqrt{\\log n / \\epsilon})$. We also give a number of extensions. First, we show how to extend our techniques to both the node-weighted and the directed versions of the problem. Second, we give a separate algorithm with pure differential privacy (as opposed to approximate DP) but with worse approximation bounds. And third, we give a new algorithm for privately computing the optimal density which implies a separation between the structural problem of privately computing the densest subgraph and the numeric problem of privately computing the density of the densest subgraph.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.94"}, {"primary_key": "197110", "vector": [], "sparse_vector": [], "title": "Certificates in P and Subquadratic-Time Computation of Radius, Diameter, and all Eccentricities in Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In the context of fine-grained complexity, we investigate the notion of certificate enabling faster polynomial-time algorithms. We specifically target radius (minimum eccentricity), diameter (maximum eccentricity), and all-eccentricity computations for which quadratic-time lower bounds are known under plausible conjectures. In each case, we introduce a notion of certificate as a specific set of nodes from which appropriate bounds on all eccentricities can be derived in subquadratic time when this set has sublinear size. The existence of small certificates is a barrier against SETH-based lower bounds for these problems. We indeed prove that for graph classes with small certificates, there exist randomized subquadratic-time algorithms for computing the radius, the diameter, and all eccentricities respectively.Moreover, these notions of certificates are tightly related to algorithms probing the graph through one-to-all distance queries and allow to explain the efficiency of practical radius and diameter algorithms from the literature. Our formalization enables a novel primal-dual analysis of a classical approach for diameter computation that leads to algorithms for radius, diameter and all eccentricities with theoretical guarantees with respect to certain graph parameters. This is complemented by experimental results on various types of real-world graphs showing that these parameters appear to be low in practice. Finally, we obtain refined results for several graph classes.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.70"}, {"primary_key": "197114", "vector": [], "sparse_vector": [], "title": "Computing the second and third systoles of a combinatorial surface.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Given a weighted, undirected graph $G$ cellularly embedded on a topological surface $S$, we describe algorithms to compute the second shortest and third shortest closed walks of $G$ that are homotopically non-trivial in $S$. Our algorithms run in $O(n^2\\log n)$ time for the second shortest walk and in $O(n^3)$ time for the third shortest walk. We also show how to reduce the running time for the second shortest homotopically non-trivial closed walk to $O(n\\log n)$ when both the genus and the number of boundaries are fixed. Our algorithms rely on a careful analysis of the configurations of the first three shortest homotopically non-trivial curves in $S$. As an intermediate step, we also describe how to compute a shortest essential arc between \\emph{one} pair of vertices or between \\emph{all} pairs of vertices of a given boundary component of $S$ in $O(n^2)$ time or $O(n^3)$ time, respectively.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.119"}, {"primary_key": "197115", "vector": [], "sparse_vector": [], "title": "A Tight (3/2 + ∈ )-Approximation Algorithm for Demand Strip Packing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We consider the Demand Strip Packing problem (DSP), in which we are given a set of jobs, each specified by a processing time and a demand. The task is to schedule all jobs such that they are finished before some deadline D while minimizing the peak demand, i.e., the maximum total demand of tasks executed at any point in time. DSP is closely related to the Strip Packing problem (SP), in which we are given a set of axis-aligned rectangles that must be packed into a strip of fixed width while minimizing the maximum height. DSP and SP are known to be NP-hard to approximate to within a factor below", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.20"}, {"primary_key": "197122", "vector": [], "sparse_vector": [], "title": "Fixed-Parameter Tractability of Hedge Cut.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In the Hedge Cut problem, the edges of a graph are partitioned into groups called hedges, and the question is what is the minimum number of hedges to delete to disconnect the graph. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> [SODA 2017] showed that Hedge Cut can be solved in quasipolynomial-time, raising the hope for a polynomial time algorithm. <PERSON><PERSON><PERSON><PERSON>, Lima, Masarík, <PERSON><PERSON>, and Souza [SODA 2023] complemented this result by showing that assuming the Exponential Time Hypothesis (ETH), no polynomial-time algorithm exists. In this paper, we show that Hedge Cut is fixed-parameter tractable parameterized by the solution size ` by providing an algorithm with running time (O(log`n)+`) · mO(1), which can be upper bounded by c` · (n + m)O(1) for any constant c > 1. This running time captures at the same time the fact that the problem is quasipolynomial-time solvable, and that it is fixed-parameter tractable parameterized by `. We further generalize this algorithm to an algorithm with running time (O(k log`n)+`) · nO(k) · mO(1) for Hedge k-Cut.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.43"}, {"primary_key": "197123", "vector": [], "sparse_vector": [], "title": "Dynamic Consistent k-Center Clustering with Optimal Recourse.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Given points from an arbitrary metric space and a sequence of point updates sent by an adversary, what is the minimum recourse per update (i.e., the minimum number of changes needed to the set of centers after an update), in order to maintain a constant-factor approximation to a k-clustering problem? This question has received attention in recent years under the name consistent clustering.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.7"}, {"primary_key": "197127", "vector": [], "sparse_vector": [], "title": "Finding irrelevant vertices in linear time on bounded-genus graphs.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Dimitrios M. Thilikos"], "summary": "The irrelevant vertex technique provides a powerful tool for the design of parameterized algorithms for a wide variety of problems on graphs. A common characteristic of these problems, permitting the application of this technique on surface-embedded graphs, is the fact that every graph of large enough treewidth contains a vertex that is irrelevant, in the sense that its removal yields an equivalent instance of the problem. The straightforward application of this technique yields algorithms with running time that is quadratic in the size of the input graph. This running time is due to the fact that it takes linear time to detect one irrelevant vertex and the total number of irrelevant vertices to be detected is linear as well. Using advanced techniques, sub-quadratic algorithms have been designed for particular problems, even in general graphs. However, designing a general framework for linear-time algorithms has been open, even for the bounded-genus case. In this paper we introduce a general framework that enables finding in linear time an entire set of irrelevant vertices whose removal yields a bounded-treewidth graph, provided that the input graph has bounded genus. Our technique consists of decomposing any surface-embedded graph into a tree-structured collection of bounded-treewidth subgraphs where detecting globally irrelevant vertices can be done locally and independently. Our method is applicable to a wide variety of known graph containment or graph modification problems where the irrelevant vertex technique applies. Examples include the (Induced) Minor Folio problem, the (Induced) Disjoint Paths problem, and the $\\mathcal{F}$-Minor-Deletion problem.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.126"}, {"primary_key": "197129", "vector": [], "sparse_vector": [], "title": "More Efficient Approximate k-wise Independent Permutations from Random Reversible Circuits via log-Sobolev Inequalities.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We prove that the permutation computed by a reversible circuit with (cid:101) O ( nk · log(1 /ε )) random 3 -bit gates is ε -approximately k -wise independent. Our bound improves on currently known bounds in the regime when the approximation error ε is not too small. We obtain our results by analyzing the log-Sobolev constants of appropriate Markov chains rather than their spectral gaps.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.191"}, {"primary_key": "197149", "vector": [], "sparse_vector": [], "title": "An analogue of Reed&apos;s conjecture for digraphs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON> in 1998 conjectured that every graph $G$ satisfies $\\chi(G) \\leq \\lceil \\frac{\\Delta(G)+1+\\omega(G)}{2} \\rceil$. As a partial result, he proved the existence of $\\varepsilon > 0$ for which every graph $G$ satisfies $\\chi(G) \\leq \\lceil (1-\\varepsilon)(\\Delta(G)+1)+\\varepsilon\\omega(G) \\rceil$. We propose an analogue conjecture for digraphs. Given a digraph $D$, we denote by $\\vec{\\chi}(D)$ the dichromatic number of $D$, which is the minimum number of colours needed to partition $D$ into acyclic induced subdigraphs. We let $\\overleftrightarrow{\\omega}(D)$ denote the size of the largest biclique (a set of vertices inducing a complete digraph) of $D$ and $\\tilde{\\Delta}(D) = \\max_{v\\in V(D)} \\sqrt{d^+(v) \\cdot d^-(v)}$. We conjecture that every digraph $D$ satisfies $\\vec{\\chi}(D) \\leq \\lceil \\frac{\\tilde{\\Delta}(D)+1+\\overleftrightarrow{\\omega}(D)}{2} \\rceil$, which if true implies <PERSON>'s conjecture. As a partial result, we prove the existence of $\\varepsilon >0$ for which every digraph $D$ satisfies $\\vec{\\chi}(D) \\leq \\lceil (1-\\varepsilon)(\\tilde{\\Delta}(D)+1)+\\varepsilon\\overleftrightarrow{\\omega}(D) \\rceil$. This implies both Reed's result and an independent result of Harutyunyan and Mohar for oriented graphs. To obtain this upper bound on $\\vec{\\chi}$, we prove that every digraph $D$ with $\\overleftrightarrow{\\omega}(D) > \\frac{2}{3}(\\Delta_{\\max}(D)+1)$, where $\\Delta_{\\max}(D) = \\max_{v\\in V(D)} \\max(d^+(v),d^-(v))$, admits an acyclic set of vertices intersecting each biclique of $D$, which generalises a result of King.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.106"}, {"primary_key": "197154", "vector": [], "sparse_vector": [], "title": "Rényi-infinity constrained sampling with d3 membership queries.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Uniform sampling over a convex body is a fundamental algorithmic problem, yet the convergence in KL or Rényi divergence of most samplers remains poorly understood. In this work, we propose a constrained proximal sampler, a principled and simple algorithm that possesses elegant convergence guarantees. Leveraging the uniform ergodicity of this sampler, we show that it converges in the Rényi-infinity divergence (𝓡∞) with no query complexity overhead when starting from a warm start. This is the strongest of commonly considered performance metrics, implying rates in {𝓡q, KL} convergence as special cases.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.181"}, {"primary_key": "197156", "vector": [], "sparse_vector": [], "title": "Efficient d-ary <PERSON><PERSON>hing at High Load Factors by Bubbling Up.", "authors": ["<PERSON>", "<PERSON>"], "summary": "A d-ary cuckoo hash table is an open-addressed hash table that stores each key x in one of d random positions h1 (x ), h 2(x ),…, hd(x ). In the offline setting, where all items are given and keys need only be matched to locations, it is possible to support a load factor of 1 — ϵ while using hashes. The online setting, where keys are moved as new keys arrive sequentially, has the additional challenge of the time to insert new keys, and it has not been known whether one can use d = O (ln ϵ-1) hashes to support poly(ϵ-1) expected-time insertions.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.133"}, {"primary_key": "197161", "vector": [], "sparse_vector": [], "title": "On Estimating the Trace of Quantum State Powers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We investigate the computational complexity of estimating the trace of quantum state powers $\\text{tr}(\\rho^q)$ for an $n$-qubit mixed quantum state $\\rho$, given its state-preparation circuit of size $\\text{poly}(n)$. This quantity is closely related to and often interchangeable with the Tsallis entropy $\\text{S}_q(\\rho) = \\frac{1-\\text{tr}(\\rho^q)}{q-1}$, where $q = 1$ corresponds to the von Neumann entropy. For any non-integer $q \\geq 1 + \\Omega(1)$, we provide a quantum estimator for $\\text{S}_q(\\rho)$ with time complexity $\\text{poly}(n)$, exponentially improving the prior best results of $\\exp(n)$ due to <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON> (ISIT 2019), <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON> (TIT 2024), and <PERSON>, <PERSON>, and <PERSON> (TIT 2024), and <PERSON> and <PERSON> (ESA 2024). Our speedup is achieved by introducing efficiently computable uniform approximations of positive power functions into quantum singular value transformation. Our quantum algorithm reveals a sharp phase transition between the case of $q=1$ and constant $q>1$ in the computational complexity of the Quantum $q$-Tsallis Entropy Difference Problem (TsallisQED$_q$), particularly deciding whether the difference $\\text{S}_q(\\rho_0) - \\text{S}_q(\\rho_1)$ is at least $0.001$ or at most $-0.001$: - For any $1+\\Omega(1) \\leq q \\leq 2$, TsallisQED$_q$ is $\\mathsf{BQP}$-complete, which implies that Purity Estimation is also $\\mathsf{BQP}$-complete. - For any $1 \\leq q \\leq 1 + \\frac{1}{n-1}$, TsallisQED$_q$ is $\\mathsf{QSZK}$-hard, leading to hardness of approximating the von Neumann entropy because $\\text{S}_q(\\rho) \\leq \\text{S}(\\rho)$, as long as $\\mathsf{BQP} \\subsetneq \\mathsf{QSZK}$. The hardness results are derived from reductions based on new inequalities for the quantum $q$-Jensen-(Shannon-)Tsallis divergence with $1\\leq q \\leq 2$, which are of independent interest.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.28"}, {"primary_key": "197163", "vector": [], "sparse_vector": [], "title": "Parameterized Approximation for Capacitated d-Hitting Set with Hard Capacities.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the CAPACITATED d-HlTTING Set problem input is a universe U equipped with a capacity function cap : U → ℕ, and a collection A of subsets of U, each of size at most d. The task is to find a minimum size subset S of U and an assignment φ : A → S such that, for every set A ∈ A we have φ (Α ) ∈ A and for every x ∈ U we have |φ-1(χ)| ≤ cap(x ). Here φ-1(χ) is the collection of sets in A mapped to x by φ. Such a set S is called a capacitated hitting set. When d = 2 the problem is known under the name CAPACITATED VERTEX COVER. In Weighted Capacitated d-HlTTING Set each element of U has a positive integer weight and the goal is to find a capacitated hitting set of minimum weight.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.48"}, {"primary_key": "197166", "vector": [], "sparse_vector": [], "title": "A topological proof of the Hell-Nešetřil dichotomy.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We provide a new proof of a theorem of Hell and Ne\\v{s}et\\v{r}il [<PERSON><PERSON>. Theory B, 48(1):92-110, 1990] using tools from topological combinatorics based on ideas of Lov\\'asz [<PERSON><PERSON>. Theory, Ser. A, 25(3):319-324, 1978]. The Hell-Ne\\v{s}et\\v{r}il Theorem provides a dichotomy of the graph homomorphism problem. It states that deciding whether there is a graph homomorphism from a given graph to a fixed graph $H$ is in P if $H$ is bipartite (or contains a self-loop), and is NP-complete otherwise. In our proof we combine topological combinatorics with the algebraic approach to constraint satisfaction problem.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.154"}, {"primary_key": "197169", "vector": [], "sparse_vector": [], "title": "Locally Testable Tree Codes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Tree codes (<PERSON><PERSON><PERSON>, STOC 93', IEEE Transactions on Information Theory 96') are codes designed for interactive communication. Encoding in a tree code is done in an online manner: the i-th codeword symbol depends only on the first i message symbols. Codewords should have good tree distance meaning that for any two codewords, starting at the first point of divergence, they should have large Hamming distance.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.189"}, {"primary_key": "197172", "vector": [], "sparse_vector": [], "title": "Complexity of polytope diameters via perfect matchings.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The (monotone) diameter of a polytope is a fundamental parameter with important connections to the efficiency of the simplex method. Despite the central role played by this parameter in discrete and linear optimization, determining the precise complexity of computing the diameter of an input polytope remains a long-standing open problem. In 1984 <PERSON><PERSON><PERSON> and <PERSON><PERSON> [FT94] proved the first cornerstone result in this direction by establishing that computing the diameter of an input polytope is weakly NP-hard. In a recent breakthrough- paper, <PERSON><PERSON> (FOCS 2018, [San18]) studied the diameter of a special class of graph-based polytopes, known as fractional matching polytopes, and showed that determining their diameters is NP-hard, thus establishing strong NP-hardness of computing the diameter of polytopes.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.74"}, {"primary_key": "197177", "vector": [], "sparse_vector": [], "title": "Polynomial-Time Classical Simulation of Noisy IQP Circuits with <PERSON><PERSON> De<PERSON>h.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Sampling from the output distributions of quantum computations comprising only commuting gates, known as instantaneous quantum polynomial (IQP) computations, is believed to be intractable for classical computers, and hence this task has become a leading candidate for testing the capabilities of quantum devices. Here we demonstrate that for an arbitrary IQP circuit undergoing dephasing or depolarizing noise, whose depth is greater than a critical $O(1)$ threshold, the output distribution can be efficiently sampled by a classical computer. Unlike other simulation algorithms for quantum supremacy tasks, we do not require assumptions on the circuit's architecture, on anti-concentration properties, nor do we require $\\Omega(\\log(n))$ circuit depth. We take advantage of the fact that IQP circuits have deep sections of diagonal gates, which allows the noise to build up predictably and induce a large-scale breakdown of entanglement within the circuit. Our results suggest that quantum supremacy experiments based on IQP circuits may be more susceptible to classical simulation than previously thought.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.30"}, {"primary_key": "197186", "vector": [], "sparse_vector": [], "title": "Testing Approximate Stationarity Concepts for Piecewise Affine Functions.", "authors": ["<PERSON>", "<PERSON>-<PERSON>"], "summary": "We study the basic computational problem of detecting approximate stationary points for continuous piecewise affine (PA) functions. Our contributions span multiple aspects, including complexity, regularity, and algorithms. Specifically, we show that testing first-order approximate stationarity concepts, as defined by commonly used generalized subdifferentials, is computationally intractable unless P=NP. To facilitate computability, we consider a polynomial-time solvable relaxation by abusing the convex subdifferential sum rule and establish a tight characterization of its exactness. Furthermore, addressing an open issue motivated by the need to terminate the subgradient method in finite time, we introduce the first oracle-polynomial-time algorithm to detect so-called near-approximate stationary points for PA functions. A notable byproduct of our development in regularity is the first necessary and sufficient condition for the validity of an equality-type (Clarke) subdifferential sum rule. Our techniques revolve around two new geometric notions for convex polytopes and may be of independent interest in nonsmooth analysis. Moreover, some corollaries of our work on complexity and algorithms for stationarity testing address open questions in the literature. To demonstrate the versatility of our results, we complement our findings with applications to a series of structured piecewise smooth functions, including $\\rho$-margin-loss SVM, piecewise affine regression, and nonsmooth neural networks.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.72"}, {"primary_key": "197188", "vector": [], "sparse_vector": [], "title": "A Discrete Analog of Tutte&apos;s Barycentric Embeddings on Surfaces.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>'s celebrated barycentric embedding theorem describes a natural way to build straight-line embeddings (crossing-free drawings) of a (3-connected) planar graph: map the vertices of the outer face to the vertices of a convex polygon, and ensure that each remaining vertex is in convex position, namely, a barycenter with positive coefficients of its neighbors. Actually computing an embedding then boils down to solving a system of linear equations. A particularly appealing feature of this method is the flexibility given by the choice of the barycentric weights. Generalizations of <PERSON><PERSON>'s theorem to surfaces of nonpositive curvature are known, but due to their inherently continuous nature, they do not lead to an algorithm. In this paper, we propose a purely discrete analog of <PERSON><PERSON>'s theorem for surfaces (with or without boundary) of nonpositive curvature, based on the recently introduced notion of reducing triangulations. We prove a <PERSON><PERSON> theorem in this setting: every drawing homotopic to an embedding such that each vertex is harmonious (a discrete analog of being in convex position) is a weak embedding (arbitrarily close to an embedding). We also provide a polynomial-time algorithm to make an input drawing harmonious without increasing the length of any edge, in a similar way as a drawing can be put in convex position without increasing the edge lengths.", "published": "2025-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.174"}]