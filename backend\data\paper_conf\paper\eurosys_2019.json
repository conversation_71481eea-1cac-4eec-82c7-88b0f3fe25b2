[{"primary_key": "3005135", "vector": [], "sparse_vector": [], "title": "Grapple: A Graph System for Static Finite-State Property Checking of Large-Scale Systems Code.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Pan", "Shenming Lu", "<PERSON>", "Guoqing <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many real-world bugs in large-scale systems are related to object state that is supposed to obey a specified finite state machine (FSM). They are triggered when unexpected events occur on objects in certain states, making these objects transition in a way that violates their specifications. Detecting such FSM-related bugs with static analysis is challenging, especially in distributed systems that have large codebases.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303972"}, {"primary_key": "3005136", "vector": [], "sparse_vector": [], "title": "Keeping Master Green at Scale.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Giant monolithic source-code repositories are one of the fundamental pillars of the back end infrastructure in large and fast-paced software companies. The sheer volume of everyday code changes demands a reliable and efficient change management system with three uncompromisable key requirements --- always green master, high throughput, and low commit turnaround time. Green refers to a master branch that always successfully compiles and passes all build steps, the opposite being red. A broken master (red) leads to delayed feature rollouts because a faulty code commit needs to be detected and rolled backed. Additionally, a red master has a cascading effect that hampers developer productivity--- developers might face local test/build failures, or might end up working on a codebase that will eventually be rolled back.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303970"}, {"primary_key": "3005138", "vector": [], "sparse_vector": [], "title": "Serving Mobile Apps: A Slice at a Time.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON> Gavrilovska"], "summary": "End users wanting to do more and more with mobile apps has led to explosive growth in the number of available apps. This has widened the gap between developers making apps available and end users being able to install all the apps they want on their device. To address this, Google introduced Instant Apps for Android where users can access selective app features on demand without having to download and install entire apps. But this requires developers to refactor apps and limits the apps' functionality.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303989"}, {"primary_key": "3005139", "vector": [], "sparse_vector": [], "title": "ConfLLVM: A Compiler for Enforcing Data Confidentiality in Low-Level Code.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a compiler-based scheme to protect the confidentiality of sensitive data in low-level applications (e.g. those written in C) in the presence of an active adversary. In our scheme, the programmer marks sensitive data by lightweight annotations on the top-level definitions in the source code. The compiler then uses a combination of static dataflow analysis, runtime instrumentation, and a novel taint-aware form of control-flow integrity to prevent data leaks even in the presence of low-level attacks. To reduce runtime overheads, the compiler uses a novel memory layout.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303952"}, {"primary_key": "3005141", "vector": [], "sparse_vector": [], "title": "Runtime Object Lifetime Profiler for Latency Sensitive Big Data Applications.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Latency sensitive services such as credit-card fraud detection and website targeted advertisement rely on Big Data platforms which run on top of memory managed runtimes, such as the Java Virtual Machine (JVM). These platforms, however, suffer from unpredictable and unacceptably high pause times due to inadequate memory management decisions (e.g., allocating objects with very different lifetimes next to each other, resulting in severe memory fragmentation). This leads to frequent and long application pause times, breaking Service Level Agreements (SLAs). This problem has been previously identified, and results show that current memory management techniques are ill-suited for applications that hold in memory massive amounts of long-lived objects (which is the case for a wide spectrum of Big Data applications).", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303988"}, {"primary_key": "3005142", "vector": [], "sparse_vector": [], "title": "When eXtended Para - Virtualization (XPV) Meets NUMA.", "authors": ["<PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper addresses the problem of efficiently virtualizing NUMA architectures. The major challenge comes from the fact that the hypervisor regularly reconfigures the placement of a virtual machine (VM) over the NUMA topology. However, neither guest operating systems (OSes) nor system runtime libraries (e.g., Hotspot) are designed to consider NUMA topology changes at runtime, leading end user applications to unpredictable performance. This paper presents eXtended Para-Virtualization (XPV), a new principle to efficiently virtualize a NUMA architecture. XPV consists in revisiting the interface between the hypervisor and the guest OS, and between the guest OS and system runtime libraries (SRL) so that they can dynamically take into account NUMA topology changes. The paper presents a methodology for systematically adapting legacy hypervisors, OSes, and SRLs. We have applied our approach with less than 2k line of codes in two legacy hypervisors (Xen and KVM), two legacy guest OSes (Linux and FreeBSD), and three legacy SRLs (Hotspot, TCMalloc, and jemalloc). The evaluation results showed that XPV outperforms all existing solutions by up to 304%.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303960"}, {"primary_key": "3005144", "vector": [], "sparse_vector": [], "title": "Scalable RDMA RPC on Reliable Connection with Efficient Resource Sharing.", "authors": ["<PERSON><PERSON>", "Youyou Lu", "<PERSON><PERSON>"], "summary": "RDMA provides extremely low latency and high bandwidth to distributed systems. Unfortunately, it fails to scale and suffers from performance degradation when transferring data to an increasing number of targets on Reliable Connection (RC). We observe that the above scalability issue has its root in the resource contention in the NIC cache, the CPU cache and the memory of each server. In this paper, we propose ScaleRPC, an efficient RPC primitive using one-sided RDMA verbs on reliable connection to provide scalable performance. To effectively alleviate the resource contention, ScaleRPC introduces 1) connection grouping to organize the network connections into groups, so as to balance the saturation and thrashing of the NIC cache; 2) virtualized mapping to enable a single message pool to be shared by different groups of connections, which reduces CPU cache misses and improve memory utilization. Such scalable connection management provides substantial performance benefits: By deploying ScaleRPC both in a distributed file system and a distributed transactional system, we observe that it achieves high scalability and respectively improves performance by up to 90% and 160% for metadata accessing and SmallBank transaction processing.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303968"}, {"primary_key": "3005147", "vector": [], "sparse_vector": [], "title": "Compact NUMA-aware Locks.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Modern multi-socket architectures exhibit non-uniform memory access (NUMA) behavior, where access by a core to data cached locally on a socket is much faster than access to data cached on a remote socket. Prior work offers several efficient NUMA-aware locks that exploit this behavior by keeping the lock ownership on the same socket, thus reducing remote cache misses and inter-socket communication. Virtually all those locks, however, are hierarchical in their nature, thus requiring space proportional to the number of sockets. The increased memory cost renders NUMA-aware locks unsuitable for systems that are conscious to space requirements of their synchronization constructs, with the Linux kernel being the chief example.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303984"}, {"primary_key": "3005148", "vector": [], "sparse_vector": [], "title": "Make the Most out of Last Level Cache in Intel Processors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>.", "<PERSON><PERSON>"], "summary": "In modern (Intel) processors, Last Level Cache (LLC) is divided into multiple slices and an undocumented hashing algorithm (aka Complex Addressing) maps different parts of memory address space among these slices to increase the effective memory bandwidth. After a careful study of Intel's Complex Addressing, we introduce a slice-aware memory management scheme, wherein frequently used data can be accessed faster via the LLC. Using our proposed scheme, we show that a key-value store can potentially improve its average performance ~12.2% and ~11.4% for 100% & 95% GET workloads, respectively. Furthermore, we propose CacheDirector, a network I/O solution which extends Direct Data I/O (DDIO) and places the packet's header in the slice of the LLC that is closest to the relevant processing core. We implemented CacheDirector as an extension to DPDK and evaluated our proposed solution for latency-critical applications in Network Function Virtualization (NFV) systems. Evaluation results show that CacheDirector makes packet processing faster by reducing tail latencies (90-99th percentiles) by up to 119 μs (~21.5%) for optimized NFV service chains that are running at 100 Gbps. Finally, we analyze the effectiveness of slice-aware memory management to realize cache isolation.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303977"}, {"primary_key": "3005151", "vector": [], "sparse_vector": [], "title": "Time Protection: The Missing OS Abstraction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Timing channels enable data leakage that threatens the security of computer systems, from cloud platforms to smartphones and browsers executing untrusted third-party code. Preventing unauthorised information flow is a core duty of the operating system, however, present OSes are unable to prevent timing channels. We argue that OSes must provide time protection, the temporal equivalent of the established memory protection, for isolating security domains. We examine the requirements of time protection, present a design and its implementation in the seL4 microkernel, and evaluate efficacy and cost on x86 and Arm processors.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303976"}, {"primary_key": "3005152", "vector": [], "sparse_vector": [], "title": "Per-Application Power Delivery.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Datacenter servers are often under-provisioned for peak power consumption due to the substantial cost of providing power. When there is insufficient power for the workload, servers can lower voltage and frequency levels to reduce consumption, but at the cost of performance. Current processors provide power limiting mechanisms, but they generally apply uniformly to all CPUs on a chip. For servers running heterogeneous jobs, though, it is necessary to differentiate the power provided to different jobs. This prevents interference when a job may be throttled by another job hitting a power limit. While some recent CPUs support per-CPU power management, there are no clear policies on how to distribute power between applications. Current hardware power limiters, such as Intel's RAPL throttle the fastest core first, which harms high-priority applications.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303981"}, {"primary_key": "3005154", "vector": [], "sparse_vector": [], "title": "AnDrone: Virtual Drone Computing in the Cloud.", "authors": ["Alexander <PERSON>&apo<PERSON>;t <PERSON>f", "<PERSON>"], "summary": "With the continued proliferation of drones, unmanned aerial vehicles, additional uses for them are growing and the demand for their services is on the rise. We present AnDrone, a drone-as-a-service solution that makes drones accessible in the cloud. AnDrone pairs a cloud service with the first drone virtualization architecture. This enables a physical drone to run multiple virtual drones simultaneously in an isolated and secure manner at little additional cost, as computational costs are cheap compared to the operational and energy costs of putting a drone in the air. AnDrone virtualizes drones using a novel Linux container architecture. Android Things virtual drone containers provide a familiar user and development environment that can run existing Android apps. A real-time Linux flight controller container supports existing drone flight software and provides virtual drones with geofenced flight control. A device container transparently multiplexes access from virtual drones to a full range of drone hardware devices, including cameras and other sensors. Upon flight completion, virtual drones and their data can be uploaded to the cloud for offline access. We have implemented an AnDrone prototype based on Raspberry Pi 3 drone hardware. We demonstrate that it incurs minimal runtime performance and energy overhead, supports real-time virtual drone flight control, and runs untrusted third-party software in virtual drones in a secure manner while ensuring that the service provider maintains control of the drone hardware.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303969"}, {"primary_key": "3005155", "vector": [], "sparse_vector": [], "title": "GRNN: Low-Latency and Scalable RNN Inference on GPUs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Feng Yan", "<PERSON>"], "summary": "Recurrent neural networks (RNNs) have gained significant attention due to their effectiveness in modeling sequential data, such as text and voice signal. However, due to the complex data dependencies and limited parallelism, current inference libraries for RNNs on GPUs produce either high latency or poor scalability, leading to inefficient resource utilization. Consequently, companies like Microsoft and Facebook use CPUs to serve RNN models.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303949"}, {"primary_key": "3005156", "vector": [], "sparse_vector": [], "title": "I Sent It: Where Does Slow Data Go to Wait?", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Shinik Park", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Emerging applications like virtual reality (VR), augmented reality (AR), and 360-degree video aim to exploit the unprecedentedly low latencies promised by technologies like the tactile Internet and mobile 5G networks. Yet these promises are still unrealized. In order to fulfill them, it is crucial to understand where packet delays happen, which impacts protocol performance such as throughput and latency. In this work, we empirically find that sender-side protocol stack delays can cause high end-to-end latencies, though existing solutions primarily address network delays. Unfortunately, however, current latency diagnosis tools cannot even distinguish between delays on network links and delays in the end hosts. To close this gap, we present ELEMENT, a latency diagnosis framework that decomposes end-to-end TCP latency into endhost and network delays, without requiring admin privileges at the sender or receiver.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303961"}, {"primary_key": "3005157", "vector": [], "sparse_vector": [], "title": "Matrix Algebra Framework for Portable, Scalable and Efficient Query Engines for RDF Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Existing query engines for RDF graphs follow one of two design paradigms: relational or graph-based. We explore sparse matrix algebra as a third paradigm and propose MAGiQ: a framework for implementing SPARQL query engines that are portable on various hardware architectures, scalable over thousands of compute nodes, and efficient for very large RDF datasets. MAGiQ represents the RDF graph as a sparse matrix and defines a domain-specific language of algebraic operations. SPARQL queries are translated into matrix algebra programs that are oblivious to the underlying computing infrastructure. Existing matrix algebra libraries, optimized for each particular architecture, are called to execute the program and handle the performance issues. We present three case studies of matrix algebra back-end libraries: SuiteSparse, MATLAB, and CombBLAS; we demonstrate how MAGiQ can effortlessly be ported on a variety of architectures such as Intel CPUs, NVIDIA GPUs, and Cray XC40 supercomputers. Our experiments on large-scale real and synthetic datasets show that MAGiQ performs comparably to or better than existing specialized SPARQL query engines for data-intensive queries, scales to very large computing infrastructures, and handles datasets with up to 512 billion triples.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303962"}, {"primary_key": "3005159", "vector": [], "sparse_vector": [], "title": "Hourglass: Leveraging Transient Resources for Time-Constrained Graph Processing in the Cloud.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper addresses the key problems that emerge when one attempts to use transient resources to reduce the cost of running time-constrained jobs in the cloud. Previous works fail to address these problems and are either not able to offer significant savings or miss termination deadlines. First, the fact that transient resources can be evicted, requiring the job to be re-started (even if not from scratch) may lead provisioning policies to fall-back to expensive on-demand configurations more often than desirable, or even to miss deadlines. Second, when a job is restarted, the new configuration can be different from the previous, which might make eviction recovery costly, e.g., transferring the state of graph data between the old and new configurations. We present HOURGLASS, a system that addresses these issues by combining two novel techniques: a slack-aware provisioning strategy that selects configurations considering the remaining time before the job's termination deadline, and a fast reload mechanism to quickly recover from evictions. By switching to an on-demand configuration when (but only if) the target deadline is at risk of not being met, we are able to obtain significant cost savings while always meeting the deadlines. Our results show that, unlike previous work, HOURGLASS is able to significantly reduce the operating costs in the order of 60-70% while guaranteeing that deadlines are met.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303964"}, {"primary_key": "3005160", "vector": [], "sparse_vector": [], "title": "GrandSLAm: Guaranteeing SLAs for Jobs in Microservices Execution Frameworks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>rama<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The microservice architecture has dramatically reduced user effort in adopting and maintaining servers by providing a catalog of functions as services that can be used as building blocks to construct applications. This has enabled datacenter operators to look at managing datacenter hosting microservices quite differently from traditional infrastructures. Such a paradigm shift calls for a need to rethink resource management strategies employed in such execution environments. We observe that the visibility enabled by a microservices execution framework can be exploited to achieve high throughput and resource utilization while still meeting Service Level Agreements, especially in multi-tenant execution scenarios.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303958"}, {"primary_key": "3005162", "vector": [], "sparse_vector": [], "title": "TAS: TCP Acceleration as an OS Service.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "As datacenter network speeds rise, an increasing fraction of server CPU cycles is consumed by TCP packet processing, in particular for remote procedure calls (RPCs). To free server CPUs from this burden, various existing approaches have attempted to mitigate these overheads, by bypassing the OS kernel, customizing the TCP stack for an application, or by offloading packet processing to dedicated hardware. In doing so, these approaches trade security, agility, or generality for efficiency. Neither trade-off is fully desirable in the fast-evolving commodity cloud.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303985"}, {"primary_key": "3005163", "vector": [], "sparse_vector": [], "title": "μLayer: Low Latency On-Device Inference Using Cooperative Single-Layer Acceleration and Processor-Friendly Quantization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Emerging mobile services heavily utilize Neural Networks (NNs) to improve user experiences. Such NN-assisted services depend on fast NN execution for high responsiveness, demanding mobile devices to minimize the NN execution latency by efficiently utilizing their underlying hardware resources. To better utilize the resources, existing mobile NN frameworks either employ various CPU-friendly optimizations (e.g., vectorization, quantization) or exploit data parallelism using heterogeneous processors such as GPUs and DSPs. However, their performance is still bounded by the performance of the single target processor, so that realtime services such as voice-driven search often fail to react to user requests in time. It is obvious that this problem will become more serious with the introduction of more demanding NN-assisted services.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303950"}, {"primary_key": "3005164", "vector": [], "sparse_vector": [], "title": "ShieldStore: Shielded In-memory Key-value Storage with SGX.", "authors": ["<PERSON><PERSON><PERSON>", "Joongun Park", "Jaewook Woo", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The shielded computation of hardware-based trusted execution environments such as Intel Software Guard Extensions (SGX) can provide secure cloud computing on remote systems under untrusted privileged system software. However, hardware overheads for securing protected memory restrict its capacity to a modest size of several tens of megabytes, and more demands for protected memory beyond the limit cause costly demand paging. Although one of the widely used applications benefiting from the enhanced security of SGX, is the in-memory key-value store, its memory requirements are far larger than the protected memory limit. Furthermore, the main data structures commonly use fine-grained data items such as pointers and keys, which do not match well with the coarse-grained paging of the SGX memory extension technique. To overcome the memory restriction, this paper proposes a new in-memory key-value store designed for SGX with application-specific data security management. The proposed key-value store, called ShieldStore, maintains the main data structures in unprotected memory with each key-value pair individually encrypted and integrity-protected by its secure component running inside an enclave. Based on the enclave protection by SGX, ShieldStore provides secure data operations much more efficiently than the baseline SGX key-value store, achieving 8--11 times higher throughput with 1 thread, and 24--30 times higher throughput with 4 threads.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303951"}, {"primary_key": "3005165", "vector": [], "sparse_vector": [], "title": "Parallax: Sparsity-aware Data Parallel Training of Deep Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "Gyeong-In Yu", "Hojin Park", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "The employment of high-performance servers and GPU accelerators for training deep neural network models have greatly accelerated recent advances in deep learning (DL). DL frameworks, such as TensorFlow, MXNet, and Caffe2, have emerged to assist DL researchers to train their models in a distributed manner. Although current DL frameworks scale well for image classification models, there remain opportunities for scalable distributed training on natural language processing (NLP) models. We found that current frameworks show relatively low scalability on training NLP models due to the lack of consideration to the difference in sparsity of model parameters. In this paper, we propose Parallax, a framework that optimizes data parallel training by utilizing the sparsity of model parameters. Parallax introduces a hybrid approach that combines Parameter Server and AllReduce architectures to optimize the amount of data transfer according to the sparsity. Experiments show that Parallax built atop Tensor-Flow achieves scalable training throughput on both dense and sparse models while requiring little effort from its users. Parallax achieves up to 2.8x, 6.02x speedup for NLP models than TensorFlow and Horovod with 48 GPUs, respectively. The training speed for the image classification models is equal to Horovod and 1.53x faster than TensorFlow.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303957"}, {"primary_key": "3005167", "vector": [], "sparse_vector": [], "title": "Why <PERSON><PERSON> Can Encrypt: Easy Email Encryption with Easy Key Management.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Email privacy is of crucial importance. Existing email encryption approaches are comprehensive but seldom used due to their complexity and inconvenience. We take a new approach to simplify email encryption and improve its usability by implementing receiver-controlled encryption: newly received messages are transparently downloaded and encrypted to a locally-generated key; the original message is then replaced. To avoid the problem of moving a single private key between devices, we implement per-device key pairs: only public keys need be synchronized via a simple verification step. Compromising an email account or server only provides access to encrypted emails. We implemented this scheme on several platforms, showing it works with PGP and S/MIME, is compatible with widely used mail clients and email services including Gmail, has acceptable overhead, and that users consider it intuitive and easy to use.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303980"}, {"primary_key": "3005169", "vector": [], "sparse_vector": [], "title": "URSA: Hybrid Block Storage for Cloud-Scale Virtual Disks.", "authors": ["Huiba Li", "<PERSON><PERSON>", "Dongsheng Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents URSA, a hybrid block store that provides virtual disks for various applications to run efficiently on cloud VMs. Trace analysis shows that the I/O patterns served by block storage have limited locality to exploit. Therefore, instead of using SSDs as a cache layer, URSA proposes an SSD-HDD-hybrid storage structure that directly stores primary replicas on SSDs and replicates backup replicas on HDDs, using journals to bridge the performance gap between SSDs and HDDs. URSA integrates the hybrid structure with designs for high reliability, scalability, and availability. Experiments show that URSA in its hybrid mode achieves almost the same performance as in its SSD-only mode (storing all replicas on SSDs), and outperforms other block stores (Ceph and Sheepdog) even in their SSD-only mode while achieving much higher CPU efficiency (performance per core). We also discuss some practical issues in our deployment.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303967"}, {"primary_key": "3005171", "vector": [], "sparse_vector": [], "title": "Efficient and Safe Network Updates with Suffix Causal Consistency.", "authors": ["<PERSON><PERSON>", "Theophilus <PERSON>", "<PERSON>"], "summary": "Though centrally managed by a controller, a software-defined network (SDN) can still encounter routing inconsistencies among its switches due to the non-atomic updates to their forwarding tables. In this paper, we propose a new method to rectify these inconsistencies that is inspired by causal consistency, a consistency model for shared-memory systems. Applied to SDNs, causal consistency would imply that once a packet is matched to (\"reads\") a forwarding rule in a switch, it can be matched in downstream switches only to rules that are equally or more up-to-date. We propose and analyze a relaxed but functionally equivalent version of this property called suffix causal consistency (SCC) and evaluate an implementation of SCC in Open vSwitch and P4 switches, in conjunction with the Ryu and P4Runtime controllers. Our results show that SCC provides greater efficiency than competing consistent-update alternatives while offering consistency that is strong enough to ensure high-level routing properties (black-hole freedom, bounded looping, etc.).", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303965"}, {"primary_key": "3005172", "vector": [], "sparse_vector": [], "title": "LockDoc: Trace-Based Analysis of Locking in the Linux Kernel.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "For fine-grained synchronization of application and kernel threads, the Linux kernel provides a multitude of different locking mechanisms that are being used on various individually locked data structures. Understanding which locks are required in which order for a particular member variable of a kernel data structure has become truly difficult, even for Linux-kernel experts themselves.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303948"}, {"primary_key": "3005173", "vector": [], "sparse_vector": [], "title": "FlyMC: Highly Scalable Testing of Complex Interleavings in Distributed Systems.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Ce<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Feng Ye", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a fast and scalable testing approach for datacenter/cloud systems such as Cassandra, Hadoop, Spark, and ZooKeeper. The uniqueness of our approach is in its ability to overcome the path/state-space explosion problem in testing workloads with complex interleavings of messages and faults. We introduce three powerful algorithms: state symmetry, event independence, and parallel flips, which collectively makes our approach on average 16x (up to 78x) faster than other state-of-the-art solutions. We have integrated our techniques with 8 popular datacenter systems, successfully reproduced 12 old bugs, and found 10 new bugs --- all were done without random walks or manual checkpoints.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303986"}, {"primary_key": "3005174", "vector": [], "sparse_vector": [], "title": "Efficient, Consistent Distributed Computation with Predictive Treaties.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "To achieve good performance, modern applications often partition their state across multiple geographically distributed nodes. While this approach reduces latency in the common case, it can be challenging for programmers to use correctly, especially in applications that require strong consistency. We introduce predictive treaties, a mechanism that can significantly reduce distributed coordination without losing strong consistency. The central insight behind our approach is that many computations can be expressed in terms of predicates over distributed state that can be partitioned and enforced locally. Predictive treaties improve on previous work by allowing the locally enforced predicates to depend on time. Intuitively, by predicting the evolution of system state, coordination can be significantly reduced compared to static approaches. We implemented predictive treaties in a distributed system that exposes them in an intuitive programming model. We evaluate performance on several benchmarks, including TPC-C, showing that predictive treaties can significantly increase performance by orders of magnitude and can even outperform customized algorithms.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303987"}, {"primary_key": "3005175", "vector": [], "sparse_vector": [], "title": "GraphBolt: Dependency-Driven Synchronous Processing of Streaming Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Efficient streaming graph processing systems leverage incremental processing by updating computed results to reflect the change in graph structure for the latest graph snapshot. Although certain monotonic path-based algorithms produce correct results by refining intermediate values via numerical comparisons, directly reusing values that were computed before mutation does not work correctly for algorithms that require BSP semantics. Since structural mutations in streaming graphs render the intermediate results unusable, exploiting incremental computation while simultaneously providing synchronous processing guarantees is challenging.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303974"}, {"primary_key": "3005177", "vector": [], "sparse_vector": [], "title": "SkyBridge: Fast and Secure Inter-Process Communication for Microkernels.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Chen"], "summary": "Microkernels have been extensively studied over decades. However, IPC (Inter-Process Communication) is still a major factor of runtime overhead, where fine-grained isolation usually leads to excessive IPCs. The main overhead of IPC comes from the involvement of the kernel, which includes the direct cost of mode switches and address space changes, as well as indirect cost due to the pollution of processor structures.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303946"}, {"primary_key": "3005178", "vector": [], "sparse_vector": [], "title": "Teaching Rigorous Distributed Systems With Efficient Model Checking.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Writing correct distributed systems code is difficult, especially for novice programmers. The inherent asynchrony and need for fault-tolerance make errors almost inevitable. Industrial-strength testing and model checking have been shown to be effective at uncovering bugs, but they come at a cost --- in both time and effort --- that is far beyond what students can afford. To address this, we have developed an efficient model checking framework and visual debugger for distributed systems, with the goal of helping students find and fix bugs in near real-time. We identify two novel techniques for reducing the search state space to more efficiently find bugs in student implementations. We report our experiences using these tools to help over two hundred students build a correct, linearizable, fault-tolerant, dynamically-sharded key--value store.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303947"}, {"primary_key": "3005179", "vector": [], "sparse_vector": [], "title": "Managing Tail Latency in Datacenter-Scale File Systems Under Production Constraints.", "authors": ["Pulkit A<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Distributed file systems often exhibit high tail latencies, especially in large-scale datacenters and in the presence of competing (and possibly higher priority) workloads. This paper introduces techniques for managing tail latencies in these systems, while addressing the practical challenges inherent in production datacenters (e.g., hardware heterogeneity, interference from other workloads, the need to maximize simplicity and maintainability). We implement our techniques in a scalable distributed file system (an extension of HDFS) used in production at Microsoft. Our evaluation uses 70k servers in 3 datacenters, and shows that our techniques reduce tail latency significantly for production workloads.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303973"}, {"primary_key": "3005180", "vector": [], "sparse_vector": [], "title": "Deferred Runtime Pipelining for contentious multicore software transactions.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "DRP is a new concurrency control protocol for software transactional memory that achieves high throughput, even for skewed workloads that exhibit high contention. DRP builds on prior works that chop transactions into pieces to expose more concurrency opportunities, but unlike these works, DRP performs no static analyses and supports arbitrary workloads. DRP achieves a high degree of concurrency across most workloads and guarantees deadlock freedom, strict serializability, and opacity. We incorporate DRP into the software transactional objects library STO [18] and find that DRP improves STO's throughput on several STAMP benchmarks by up to 3.6x. Additionally, an in-memory multicore database implemented with our modified variant of STO outperforms databases that use OCC or transaction chopping for concurrency control. Specifically, DRP achieves 6.6x higher throughput than OCC when contention is high. Compared to transaction chopping, our DRP achieves 3.3x higher throughput when contention is medium or low. Furthermore, our implementation achieves comparable performance to OCC and transaction chopping at other contention levels.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303966"}, {"primary_key": "3005182", "vector": [], "sparse_vector": [], "title": "CoPart: Coordinated Partitioning of Last-Level Cache and Memory Bandwidth for Fairness-Aware Workload Consolidation on Commodity Servers.", "authors": ["Jinsu Park", "Seongbeom Park", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Workload consolidation is a widely-used technique to maximize server resource utilization in cloud and datacenter computing. Recent commodity CPUs support last-level cache (LLC) and memory bandwidth partitioning functionalities that can be used to ensure the fairness of the consolidated workloads. While prior work has proposed a variety of resource partitioning techniques, it still remains unexplored to characterize the impact of LLC and memory bandwidth partitioning on the fairness of the consolidated workloads and investigate system software support to dynamically control LLC and memory bandwidth partitioning in a coordinated manner.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303963"}, {"primary_key": "3005184", "vector": [], "sparse_vector": [], "title": "Multiverse: Compiler-Assisted Management of Dynamic Variability in Low-Level System Software.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "System software, such as the Linux kernel, typically provides a high degree of versatility by means of static and dynamic variability. While static variability can be completely resolved at compile time, dynamic variation points come at a cost arising from extra tests and branches in the control flow. Kernel developers use it (a) only sparingly and (b) try to mitigate its overhead by run-time binary code patching, for which several problem/architecture-specific mechanisms have become part of the kernel.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303959"}, {"primary_key": "3005185", "vector": [], "sparse_vector": [], "title": "A Lightweight Framework for Fine-Grained Lifecycle Control of Android Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The lifecycle of Android apps is dynamically managed by the system in an ad hoc manner, which leads to apps' abusing lifecycle entry points to automatically start up and gaming the priority-based memory management mechanism to evade being killed. Such apps exhibit diehard behaviors that keep them long-running in the background, resulting in excessive battery consumption and device performance degradation. Existing battery-saving features are far from being effective in restricting diehard behaviors, due to the lack of systematic, fine-grained control of app lifecycle.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303956"}, {"primary_key": "3005186", "vector": [], "sparse_vector": [], "title": "Resource Deflation: A New Approach For Transient Resource Reclamation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Data centers and clouds are increasingly offering low-cost computational resources in the form of transient virtual machines. Whenever demand for computational resources exceeds their availability, transient resources can reclaimed by preempting the transient VMs. Conventionally, these transient VMs are used by low-priority applications that can tolerate the disruption caused by preemptions.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303945"}, {"primary_key": "3005188", "vector": [], "sparse_vector": [], "title": "The Case For In-Network Computing On Demand.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Programmable network hardware can run services traditionally deployed on servers, resulting in orders-of-magnitude improvements in performance. Yet, despite these performance improvements, network operators remain skeptical of in-network computing. The conventional wisdom is that the operational costs from increased power consumption outweigh any performance benefits. Unless in-network computing can justify its costs, it will be disregarded as yet another academic exercise.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303979"}, {"primary_key": "3005190", "vector": [], "sparse_vector": [], "title": "Conclave: secure multi-party computation on big data.", "authors": ["<PERSON><PERSON>", "Mal<PERSON>", "<PERSON>", "Mayank Varia", "<PERSON>", "<PERSON><PERSON>"], "summary": "Secure Multi-Party Computation (MPC) allows mutually distrusting parties to run joint computations without revealing private data. Current MPC algorithms scale poorly with data size, which makes MPC on “big data” prohibitively slow and inhibits its practical use. Many relational analytics queries can maintain MPC’s end-to-end security guarantee without using cryptographic MPC techniques for all operations. Conclave is a query compiler that accelerates such queries by transforming them into a combination of data-parallel, local cleartext processing and small MPC steps. When parties trust others with specific subsets of the data, Conclave applies new hybrid MPC-cleartext protocols to run additional steps outside of MPC and improve scalability further. Our Conclave prototype generates code for cleartext processing in Python and Spark, and for secure MPC using the Sharemind and Obliv-C frameworks. Conclave scales to data sets between three and six orders of magnitude larger than state-of-the-art MPC frameworks support on their own. Thanks to its hybrid protocols and additional optimizations, Conclave also substantially outperforms SMCQL, the most similar existing system.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303982"}, {"primary_key": "3005191", "vector": [], "sparse_vector": [], "title": "Supporting Very Large Models using Automatic Dataflow Graph Partitioning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> Li"], "summary": "This paper presents Tofu, a system that partitions very large DNN models across multiple GPU devices to reduce per-GPU memory footprint. Tofu is designed to partition a dataflow graph of fine-grained tensor operators in order to work transparently with a general-purpose deep learning platform like MXNet. In order to automatically partition each operator, we propose to describe the semantics of an operator in a simple language which represents tensors as lambda functions mapping from tensor coordinates to values. To optimally partition different operators in a dataflow graph, Tofu uses a recursive search algorithm that minimizes the total communication cost. Our experiments on an 8-GPU machine show that <PERSON><PERSON> enables the training of very large CNN and RNN models. It also achieves 25% - 400% speedup over alternative approaches to train very large models.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303953"}, {"primary_key": "3005192", "vector": [], "sparse_vector": [], "title": "Replayable Execution Optimized for Page Sharing for a Managed Runtime Environment.", "authors": ["Kai-<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present Replayable Execution, a system for improving the efficiency of Function-as-a-Service (FaaS) frameworks. It takes advantage of standard kernel features to reduce memory usage and accelerate cold startup speed without changes to the OS kernel, language runtimes, and the surrounding FaaS deployment environment. Replayable Execution exploits the intensive-deflated execution characteristics of the majority of target applications. It uses checkpointing to save an image of an application, allowing this image to be shared across containers and resulting in speedy restoration at service startup. We apply Replayable Execution to a representative FaaS Java framework to create a ReplayableJVM execution, which together with benefits from deterministic execution of a warmed up runtime, offers 2X memory footprint reduction, and over 10X startup time improvement.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303978"}, {"primary_key": "3005193", "vector": [], "sparse_vector": [], "title": "Project Almanac: A Time-Traveling Solid-State Drive.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Chance C. Coats", "<PERSON><PERSON>"], "summary": "Preserving the history of storage states is critical to ensuring system reliability and security. It facilitates system functions such as debugging, data recovery, and forensics. Existing software-based approaches like data journaling, logging, and backups not only introduce performance and storage cost, but also are vulnerable to malware attacks, as adversaries can obtain kernel privileges to terminate or destroy them.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303983"}, {"primary_key": "3005194", "vector": [], "sparse_vector": [], "title": "Automating Dependence-Aware Parallelization of Machine Learning Training on Distributed Shared Memory.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Machine learning (ML) training is commonly parallelized using data parallelism. A fundamental limitation of data parallelism is that conflicting (concurrent) parameter accesses during ML training usually diminishes or even negates the benefits provided by additional parallel compute resources. Although it is possible to avoid conflicting parameter accesses by carefully scheduling the computation, existing systems rely on programmer manual parallelization and it remains a question when such parallelization is possible.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303954"}, {"primary_key": "3005195", "vector": [], "sparse_vector": [], "title": "Wormhole: A Fast Ordered Index for In-memory Data Management.", "authors": ["<PERSON><PERSON><PERSON> Wu", "<PERSON>", "<PERSON>"], "summary": "In-memory data management systems, such as key-value stores, have become an essential infrastructure in today's big-data processing and cloud computing. They rely on efficient index structures to access data. While unordered indexes, such as hash tables, can perform point search with O(1) time, they cannot be used in many scenarios where range queries must be supported. Many ordered indexes, such as B+ tree and skip list, have a O(log N) lookup cost, where N is number of keys in an index. For an ordered index hosting billions of keys, it may take more than 30 key-comparisons in a lookup, which is an order of magnitude more expensive than that on a hash table. With availability of large memory and fast network in today's data centers, this O(log N) time is taking a heavy toll on applications that rely on ordered indexes.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303955"}, {"primary_key": "3005196", "vector": [], "sparse_vector": [], "title": "VStore: A Data Store for Analytics on Large Videos.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present VStore, a data store for supporting fast, resource-efficient analytics over large archival videos. VStore manages video ingestion, storage, retrieval, and consumption. It controls video formats along the video data path. It is challenged by i) the huge combinatorial space of video format knobs; ii) the complex impacts of these knobs and their high profiling cost; iii) optimizing for multiple resource types. It explores an idea called backward derivation of configuration: in the opposite direction along the video data path, VStore passes the video quantity and quality expected by analytics backward to retrieval, to storage, and to ingestion. In this process, VStore derives an optimal set of video formats, optimizing for different resources in a progressive manner. VStore automatically derives large, complex configurations consisting of more than one hundred knobs over tens of video formats. In response to queries, VStore selects video formats catering to the executed operators and the target accuracy. It streams video data from disks through decoder to operators. It runs queries as fast as 362x of video realtime.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303971"}, {"primary_key": "3005197", "vector": [], "sparse_vector": [], "title": "Fast Distributed Deep Learning over RDMA.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep learning emerges as an important new resource-intensive workload and has been successfully applied in computer vision, speech, natural language processing, and so on. Distributed deep learning is becoming a necessity to cope with growing data and model sizes. Its computation is typically characterized by a simple tensor data abstraction to model multi-dimensional matrices, a dataflow graph to model computation, and iterative executions with relatively frequent synchronizations, thereby making it substantially different from Map/Reduce style distributed big data computation.", "published": "2019-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3302424.3303975"}]