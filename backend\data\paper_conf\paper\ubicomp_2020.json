[{"primary_key": "2427298", "vector": [], "sparse_vector": [], "title": "Wearable Computing Technology for Assessment of Cognitive Functioning of Bipolar Patients and Healthy Controls.", "authors": ["Pegah <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Mobile cognitive tests have been emerged to first, bring the assessments outside the clinics and second, frequently measure individuals' cognitive performance in their free-living environment. Patients with Bipolar Disorder (BD) suffer from cognitive impairments and poor sleep quality negatively affects their cognitive performance. Wearables are capable of unobtrusively collecting multivariate data including activity and sleep features. In this study, we analyzed daily attention, working memory, and executive functions of patients with BD and healthy controls by using a smartwatch-based tool called UbiCAT to 1) investigate its concurrent validity and feasibility, 2) identify digital phenotypes of mental health using cognitive and mobile sensor data, and 3) classify patients and healthy controls on the basis of their daily cognitive and mobile data. Our findings demonstrated that UbiCAT is feasible with valid measures for in-The-wild cognitive assessments the analysis showed that the patients responded more slowly during the attention task than the healthy controls, which could indicate a lower alertness of this group. Furthermore, sleep duration correlated positively with participants' working memory performance the next day. Statistical analysis showed that features including cognitive measures of attention and executive functions, sleep duration, time in bed, awakening frequency and duration, and step counts are the digital phenotypes of mental health diagnosis. Supervised learning models was used to classify individuals' mental health diagnosis using their daily observations. Overall, we achieved accuracy of approximately 74% using K-Nearest Neighbour (KNN) method.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432219"}, {"primary_key": "2427310", "vector": [], "sparse_vector": [], "title": "Designing Interactions Beyond Conscious Control: A New Model for Wearable Interfaces.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent research in psychology distinguishes levels of consciousness into a tripartite model - conscious, unconscious and metaconscious. HCI technologies largely focus on the conscious pathway for computer-to-human interaction, requiring explicit user attention and action. In contrast, the other two pathways provide opportunities to create new interfaces that can alter emotion, cognition and behavior without demands on attentional resources. These direct interfaces connect to cognitive processes that are in our perception but outside our conscious control. In this work, we feature two sub-categories, namely preconscious and metasomatic within the tripartite model of consciousness. Our goal is to provide a finer categorization of cognitive processes that can better help classify HCI research related to activating non-conscious cognitive pathways. We present the design of two wearable devices, MoveU and Frisson. From lessons learned during the iterative design process and the user studies, we present four design considerations that can be used to aid HCI researchers of future devices that influence the mind. With this work we aim to highlight that awareness of consciousness levels can be a valuable design element that can help to expand the range of computer-to-human interface devices we build.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411829"}, {"primary_key": "2427405", "vector": [], "sparse_vector": [], "title": "Extending Coverage of Stationary Sensing Systems with Mobile Sensing Systems for Human Mobility Modeling.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Human mobility modeling has many applications in location-based services, mobile networking, city management, and epidemiology. Previous sensing approaches for human mobility are mainly categorized into two types: stationary sensing systems (e.g., surveillance cameras and toll booths) and mobile sensing systems (e.g., smartphone apps and vehicle tracking devices). However, stationary sensing systems only provide mobility information of human in limited coverage (e.g., camera-equipped roads) and mobile sensing systems only capture a limited number of people (e.g., people using a particular smartphone app). In this work, we design a novel system Mohen to model human mobility with a heterogeneous sensing system. The key novelty of <PERSON><PERSON> is to fundamentally extend the sensing coverage of a large-scale stationary sensing system with a small-scale sensing system. Based on the evaluation on data from real-world urban sensing systems, our system outperforms them by 35% and achieves a competitive result to an Oracle method.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411827"}, {"primary_key": "2427408", "vector": [], "sparse_vector": [], "title": "Mobileportation: Nomadic Telepresence for Mobile Devices.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The desire to stay connected to one another over large distances has guided decades of telepresence research. Most focuses on stationary solutions that can deliver high-fidelity telepresence experiences, but these are usually impractical for the wider population who cannot afford the necessary proprietary equipment or are unwilling to regress to non-mobile communication. In this paper we present Mobileportation, a nomadic telepresence prototype that takes advantage of recent developments in mobile technology to provide immersive experiences wherever the user desires by allowing for seamless transitions between ego- and exocentric views within a mutually shared three-dimensional environment. The results of a user study are also discussed that show Mobileportation's ability to induce a sense of presence within this environment and with the remote communication partner, as well as the potential of this platform for future telepresence research.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397331"}, {"primary_key": "2427260", "vector": [], "sparse_vector": [], "title": "Understanding Physical Practices and the Role of Technology in Manual Self-Tracking.", "authors": ["Parastoo Abtahi", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Self-tracking practices enable users to record and analyze their personal data. In recent years, non-digital forms of manual self-tracking, such as bullet journaling, have gained popularity. We conduct a survey (N = 404) and follow-up interviews (N = 18) to better understand users' motivations for physical tracking, the challenges they face with their current practices, and their perceptions of both digital and physical tracking tools. We find that for some users, physical practices are a structured and constructive creative outlet and a form of artistic expression. While the resulting physical artifacts may not easily enable retrospective reflection over long-term data, they preserve personal traces in a unique and tangible form that is meaningful to users. Moreover, the reflective power of physical tracking stems from the interaction with the physical materiality, the slow pace of these practices, the creative exploration they facilitate, and the associated digital disconnect. We conclude with design implications for future technologies, including ways digital tools might extend current physical practices and support richly reflective self-tracking.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432236"}, {"primary_key": "2427265", "vector": [], "sparse_vector": [], "title": "Automated Detection of Stressful Conversations Using Wearable Physiological and Inertial Sensors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Stressful conversation is a frequently occurring stressor in our daily life. Stressors not only adversely affect our physical and mental health but also our relationships with family, friends, and coworkers. In this paper, we present a model to automatically detect stressful conversations using wearable physiological and inertial sensors. We conducted a lab and a field study with cohabiting couples to collect ecologically valid sensor data with temporally-precise labels of stressors. We introduce the concept of stress cycles, i.e., the physiological arousal and recovery, within a stress event. We identify several novel features from stress cycles and show that they exhibit distinguishing patterns during stressful conversations when compared to physiological response due to other stressors. We observe that hand gestures also show a distinct pattern when stress occurs due to stressful conversations. We train and test our model using field data collected from 38 participants. Our model can determine whether a detected stress event is due to a stressful conversation with an F1-score of 0.83, using features obtained from only one stress cycle, facilitating intervention delivery within 3.9 minutes since the start of a stressful conversation.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432210"}, {"primary_key": "2427270", "vector": [], "sparse_vector": [], "title": "AiSee: An Assistive Wearable Device to Support Visually Impaired Grocery Shoppers.", "authors": ["<PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON>", "Sur<PERSON>"], "summary": "People with visual impairments (PVI) experience simple tasks, such as grocery shopping, to be an essential difficulty. Although the recent emergence of AI-technology has been dramatically improving visual recognition capabilities, the application to the daily life of PVI is still complex and erroneous. For example, image recognition engines require a clear shot of the targeted object and a contextual understanding of the information the user requires. In this paper, we aimed to understand the PVI's needs and their pain points in the task of identifying grocery items. Following a user-centered design process, we iteratively", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432196"}, {"primary_key": "2427272", "vector": [], "sparse_vector": [], "title": "A Multi-perspective Analysis of Social Context and Personal Factors in Office Settings for the Design of an Effective Mobile Notification System.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this study, we investigate the effects of social context, personal and mobile phone usage on the inference of work engagement/challenge levels of knowledge workers and their responsiveness to well-being related notifications. Our results show that mobile application usage is associated to the responsiveness and work engagement/challenge levels of knowledge workers. We also developed multi-level (within- and between-subjects) models for the inference of attentional states and engagement/challenge levels with mobile application usage indicators as inputs, such as the number of applications used prior to notifications, the number of switches between applications, and application category usage. The results of our analysis show that the following features are effective for the inference of attentional states and engagement/challenge levels: the number of switches between mobile applications in the last 45 minutes and the duration of application usage in the last 5 minutes before users' response to ESM messages.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3381000"}, {"primary_key": "2427273", "vector": [], "sparse_vector": [], "title": "Hello There! Is Now a Good Time to Talk?: Opportune Moments for Proactive Interactions with Smart Speakers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Cheul Young Park", "<PERSON><PERSON>", "Mingyu Park", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Increasing number of researchers and designers are envisioning a wide range of novel proactive conversational services for smart speakers such as context-aware reminders and restocking household items. When initiating conversational interactions proactively, smart speakers need to consider users' contexts to minimize disruption. In this work, we aim to broaden our understanding of opportune moments for proactive conversational interactions in domestic contexts. Toward this goal, we built a voice-based experience sampling device and conducted a one-week field study with 40 participants living in university dormitories. From 3,572 in-situ user experience reports, we proposed 19 activity categories to investigate contextual factors related to interruptibility. Our data analysis results show that the key determinants for opportune moments are closely related to both personal contextual factors such as busyness, mood, and resource conflicts for dual-tasking, and the other contextual factors associated with the everyday routines at home, including user mobility and social presence. Based on these findings, we discuss the need for designing context-aware proactive conversation management features that dynamically control conversational interactions based on users' contexts and routines.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411810"}, {"primary_key": "2427276", "vector": [], "sparse_vector": [], "title": "SmokingOpp: Detecting the Smoking &apos;Opportunity&apos; Context Using Mobile Sensors.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Context plays a key role in impulsive adverse behaviors such as fights, suicide attempts, binge-drinking, and smoking lapse. Several contexts dissuade such behaviors, but some may trigger adverse impulsive behaviors. We define these latter contexts as 'opportunity' contexts, as their passive detection from sensors can be used to deliver context-sensitive interventions. In this paper, we define the general concept of 'opportunity' contexts and apply it to the case of smoking cessation. We operationalize the smoking 'opportunity' context, using self-reported smoking allowance and cigarette availability. We show its clinical utility by establishing its association with smoking occurrences using Granger causality. Next, we mine several informative features from GPS traces, including the novel location context of smoking spots, to develop the SmokingOpp model for automatically detecting the smoking 'opportunity' context. Finally, we train and evaluate the SmokingOpp model using 15 million GPS points and 3,432 self-reports from 90 newly abstinent smokers in a smoking cessation study.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3380987"}, {"primary_key": "2427280", "vector": [], "sparse_vector": [], "title": "METIER: A Deep Multi-Task Learning Based Activity and User Recognition Model Using Wearable Sensors.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Activity recognition (AR) and user recognition (UR) using wearable sensors are two key tasks in ubiquitous and mobile computing. Currently, they still face some challenging problems. For one thing, due to the variations in how users perform activities, the performance of a well-trained AR model typically drops on new users. For another, existing UR models are powerless to activity changes, as there are significant differences between the sensor data in different activity scenarios. To address these problems, we propose METIER (deep multi-task learning based activity and user recognition) model, which solves AR and UR tasks jointly and transfers knowledge across them. User-related knowledge from UR task helps AR task to model user characteristics, and activity-related knowledge from AR task guides UR task to handle activity changes. METIER softly shares parameters between AR and UR networks, and optimizes these two networks jointly. The commonalities and differences across tasks are exploited to promote AR and UR tasks simultaneously. Furthermore, mutual attention mechanism is introduced to enable AR and UR tasks to exploit their knowledge to highlight important features for each other. Experiments are conducted on three public datasets, and the results show that our model can achieve competitive performance on both tasks.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3381012"}, {"primary_key": "2427290", "vector": [], "sparse_vector": [], "title": "SenCAPTCHA: A Mobile-First CAPTCHA Using Orientation Sensors.", "authors": ["<PERSON><PERSON>", "Qing Cao", "<PERSON><PERSON> Qi", "<PERSON>"], "summary": "CAPTCHAs are used to distinguish between human- and computer-generated (i.e., bot) online traffic. As there is an ever-increasing amount of online traffic from mobile devices, it is necessary to design CAPTCHAs that work well on mobile devices. In this paper, we present SenCAPTCHA, a mobile-first CAPTCHA that leverages the device's orientation sensors. SenCAPTCHA works by showing users an image of an animal and asking them to tilt their device to guide a red ball into the center of that animal's eye. SenCAPTCHA is especially useful for devices with small screen sizes (e.g., smartphones, smartwatches). In this paper, we describe the design of SenCAPTCHA and demonstrate that it is resilient to various machine learning based attacks. We also report on two usability studies of SenCAPTCHA involving a total of 472 participants; our results show that SenCAPTCHA is viewed as an \"enjoyable\" CAPTCHA and that it is preferred by over half of the participants to other existing CAPTCHA systems.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397312"}, {"primary_key": "2427291", "vector": [], "sparse_vector": [], "title": "PMF: A Privacy-preserving Human Mobility Prediction Framework via Federated Learning.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Funing Sun", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "With the popularity of mobile devices and location-based social network, understanding and modelling the human mobility becomes an important topic in the field of ubiquitous computing. With the model developing from personal models with own information to the joint models with population information, the prediction performance of proposed models become better and better. Meanwhile, the privacy issues of these models come into the view of community and the public: collecting and uploading private data to the centralized server without enough regulation. In this paper, we propose PMF, a privacy-preserving mobility prediction framework via federated learning, to solve this problem without significantly sacrificing the prediction performance. In our framework, based on the deep learning mobility model, no private data is uploaded into the centralized server and the only uploaded thing is the updated model parameters which are difficult to crack and thus more secure. Furthermore, we design a group optimization method for the training on local devices to achieve better trade-off between performance and privacy. Finally, we propose a fine-tuned personal adaptor for personal modelling to further improve the prediction performance. We conduct extensive experiments on three real-life mobility datasets to demonstrate the superiority and effectiveness of our methods in privacy protection settings.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3381006"}, {"primary_key": "2427299", "vector": [], "sparse_vector": [], "title": "Mobile Device Batteries as Thermometers.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The ability to sense ambient temperature pervasively, albeit crucial for many applications, is not yet available, causing problems such as degraded indoor thermal comfort and unexpected/premature shutoffs of mobile devices. To enable pervasive sensing of ambient temperature, we propose use of mobile device batteries as thermometers based on (i) the fact that people always carry their battery-powered smart phones, and (ii) our empirical finding that the temperature of mobile devices' batteries is highly correlated with that of their operating environment. Specifically, we design and implement Batteries-as-Thermometers (BaT), a temperature sensing service based on the information of mobile device batteries, expanding the ability to sense the device's ambient temperature without requiring additional sensors or taking up the limited on-device space. We have evaluated BaT on 6 Android smartphones using 19 laboratory experiments and 36 real-life field-tests, showing an average of 1.25°C error in sensing the ambient temperature.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3381015"}, {"primary_key": "2427307", "vector": [], "sparse_vector": [], "title": "ScraTouch: Extending Interaction Technique Using Fingernail on Unmodified Capacitive Touch Surfaces.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present ScraTouch, an interaction technique using fingernails, as a new input modality by leveraging capacitive touch sensing. Differentiating between fingertip and fingernail touches requires only tens of milliseconds worth of shunt current data from unmodified capacitive touch surfaces, thus requires no hardware modification. ScraTouch is simple but practical technique for command invocation and mode switching. An evaluation using a point-and-select task on a touchpad showed that although the switching between the finger and nail in ScraTouch required a little more time compared with the baseline (finger touching without mode switching), in overall the operations, ScraTouch was just as fast as the baseline, and on average, 29 % faster than a long press with 500-ms threshold. We also confirmed that setting a simple threshold on the measured shunt current for recognition works robustly across users (97 % accuracy).", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411831"}, {"primary_key": "2427308", "vector": [], "sparse_vector": [], "title": "An Automated Mobile Game-based Screening Tool for Patients with Alcohol Dependence.", "authors": ["Jittrapol Intarasirisawat", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Traditional methods for screening and diagnosis of alcohol dependence are typically administered by trained clinicians in medical settings and often rely on interview responses. These self-reports can be unintentionally or deliberately false, and misleading answers can, in turn, lead to inaccurate assessment and diagnosis. In this study, we examine the use of user-game interaction patterns on mobile games to develop an automated diagnostic and screening tool for alcohol-dependent patients. Our approach relies on the capture of interaction patterns during gameplay, while potential patients engage with popular mobile games on smartphones. The captured signals include gameplay performance, touch gestures, and device motion, with the intention of identifying patients with alcohol dependence. We evaluate the classification performance of various supervised learning algorithms on data collected from 40 patients and 40 age-matched healthy adults. The results show that patients with alcohol dependence can be automatically identified accurately using the ensemble of touch, device motion, and gameplay performance features on 3-minute samples (accuracy=0.95, sensitivity=0.95, and specificity=0.95). The present findings provide strong evidence suggesting the potential use of user-game interaction metrics on existing mobile games as discriminant features for developing an implicit measure to identify alcohol dependence conditions. In addition to supporting healthcare professionals in clinical decision-making, the game-based self-screening method could be used as a novel strategy to promote alcohol dependence screening, especially outside of clinical settings.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411837"}, {"primary_key": "2427314", "vector": [], "sparse_vector": [], "title": "Foundations for Systematic Evaluation and Benchmarking of a Mobile Food Logger in a Large-scale Nutrition Study.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>-<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Mobile food logging is important but people find it tedious and difficult to do. Our work tackles the challenging aspect of searching a large food database on a small mobile screen. We describe the design of the EaT (Eat and Track) app with its Search-Accelerator to support searches on &gt;6,000 foods. We designed a study to harness data from a large nutrition study to provide insights about the use and user experience of EaT. We report the results of our evaluation: a 12-participant lab study and a public health research field study where 1,027-participants entered their nutrition intake for 3 days, logging 30,715 food items. We also analysed 1,163 user-created food entries from 670 participants to gain insights about the causes of failures in the food search. Our core contributions are: 1) the design and evaluation of EaT's support for accurate and detailed food logging; 2) our study design that harnesses a nutrition research study to provide insights about timeliness of logging and the strengths and weaknesses of the search; 3) new performance benchmarks for mobile food logging.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397327"}, {"primary_key": "2427315", "vector": [], "sparse_vector": [], "title": "User-Based Collaborative Filtering Mobile Health System.", "authors": ["Hsien-<PERSON>", "Shen Yan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Mobile health systems predict health conditions based on multimodal signals. Users are often reluctant to provide their health status over privacy concerns. It is challenging to make health predictions without sufficient historical data from the users. In this paper, we propose a user-based collaborative filtering mobile health system. The system requests users to provide a few health labels. These labels are used to determine cohort similarity and discarded afterward to ensure privacy protection. The cohorts are designed to maximize user similarity across health labels, variable relationships, and sensor data. Our system predicts users based on the health information from their cohort. We empirically evaluate the system by conducting a ten-week longitudinal study to assess the health conditions of 212 hospital workers using mobile devices, wearables, and sensors. The results show successful cohort assignments with five health labels. Health predictions achieve promising performance without historical data. Our system demonstrates strong interpretability, predictability, and usability.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432703"}, {"primary_key": "2427319", "vector": [], "sparse_vector": [], "title": "Dyadic Mirror: Everyday Second-person Live-view for Empathetic Reflection upon Parent-child Interaction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A parent's capacity to understand the mental states of both him/herself and the child is considered to play a significant role in various aspects of parent-child relationship-e.g., lowering parental stress and supporting cognitive development of the child. We propose Dyadic Mirror, a wearable smart mirror which is designed to foster the aforementioned parental capacity in everyday parent-child interaction. Its key feature is to provide a parent with a second-person live-view from the child, i.e., the parent's own face as seen by the child, during their face-to-face interaction. Dyadic Mirror serves as a straightforward cue that helps the parent be aware of (1) his/her emotional state, and (2) the way he/she would be now being seen by the child, thereby facilitate the parent to infer the child's mental state. To evaluate Dyadic Mirror under unconstrained parent-child interactions in real-life, we implemented the working prototype of Dyadic Mirror and deployed it to 6 families over 4 weeks. The participating parents reported extensive experiences with Dyadic Mirror, supporting that Dyadic Mirror has helped them be aware of their recurring but unconscious behaviors, understand their children's feelings, reason with the children's behaviors, and find self-driven momenta to better their attitude and expressions towards their children.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411815"}, {"primary_key": "2427323", "vector": [], "sparse_vector": [], "title": "Aquilis: Using Contextual Integrity for Privacy Protection on Mobile Devices.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON> <PERSON><PERSON>", "Pan Hui"], "summary": "Smartphones are nowadays the dominant end-user device. As a result, they have become gateways to all users' communications, including sensitive personal data. In this paper, we present Aquilis, a privacy-preserving system for mobile platforms following the principles of contextual integrity to define the appropriateness of an information flow. <PERSON><PERSON><PERSON> takes the form of a keyboard that reminds users of potential privacy leakages through a simple three-colour code. <PERSON><PERSON><PERSON> considers the instantaneous privacy risk related to posting information (Local Sensitivity), the risk induced by repeating information over time (Longitudinal Sensitivity) and on different platforms (Cross-platform Sensitivity). Considering 50% of Aquilis warnings decreases the proportion of inappropriate information by up to 30%. Repeating information over time or in a broader exposure context increases the risk by 340% in a one-to-one context. We develop our own labeled privacy dataset of over 1000 input texts to evaluate <PERSON><PERSON><PERSON>' accuracy. <PERSON><PERSON><PERSON> significantly outperforms other state-of-the-art methods (F-1-0.76). Finally, we perform a user study with 35 highly privacy-aware participants. Aquilis privacy metric is close to users' privacy preferences (average divergence of 1.28/5). Users found <PERSON><PERSON><PERSON> useful (4.41/5), easy to use (4.4/5), and agreed that <PERSON><PERSON><PERSON> improves their online privacy awareness (4.04/5).", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432205"}, {"primary_key": "2427329", "vector": [], "sparse_vector": [], "title": "Extraction and Interpretation of Deep Autoencoder-based Temporal Features from Wearables for Forecasting Personalized Mood, Health, and Stress.", "authors": ["Boning Li", "Akane Sano"], "summary": "Continuous wearable sensor data in high resolution contain physiological and behavioral information that can be utilized to predict human health and wellbeing, establishing the foundation for developing early warning systems to eventually improve human health and wellbeing. We propose a deep neural network framework, the Locally Connected Long Short-Term Memory Denoising AutoEncoder (LC-LSTM-DAE), to automatically extract features from passively collected raw sensor data and perform personalized prediction of self-reported mood, health, and stress scores with high precision. We enabled personalized learning of features by finetuning the general representation model with participant-specific data. The framework was evaluated using wearable sensor data and wellbeing labels collected from college students (total 6391 days from N=239). Sensor data include skin temperature, skin conductance, and acceleration; wellbeing labels include self-reported mood, health and stress scored 0 - 100. Compared to the prediction performance based on hand-crafted features, the proposed framework achieved higher precision with a smaller number of features. We also provide statistical interpretation and visual explanation to the automatically learned features and the prediction models. Our results show the possibility of predicting self-reported mood, health, and stress accurately using an interpretable deep learning framework, ultimately for developing real-time health and wellbeing monitoring and intervention systems that can benefit various populations.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397318"}, {"primary_key": "2427330", "vector": [], "sparse_vector": [], "title": "How Does Fitbit Measure Brainwaves: A Qualitative Study into the Credibility of Sleep-tracking Technologies.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Consumer sleep-tracking devices provide an unobtrusive and affordable way to learn about personal sleep habits. Recent research focused primarily on the information provided by such devices, i.e., whether the information is accurate and meaningful to people. However, little is known about how people judge the credibility of such information, and how the functionality and the design may influence such judgements. Hence, the aim of this research was to examine how consumers assess the credibility of sleep-tracking devices. We conducted a qualitative study with 22 participants who tracked their sleep for 3 nights with three different devices: Fitbit Charge 2, Neuroon EEG, and SleepScope, a medical sleep monitor. Based on semi-structured interviews, we found that people assess the credibility of sleep-tracking devices based not only on the credibility of sleep data per se, but also on device functionality, interface design and physical appearance. People found it difficult to judge credibility, because of the complexities of sleep stages and micro-arousals (sleep fallacy) and the black boxed nature of devices (black box fallacy), and also because of the misalignment between objective sleep measures and subjective sleep quality. We discuss the significance of design and functionality on the credibility of personal health technologies and highlight design challenges and opportunities to enhance their credibility.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3380994"}, {"primary_key": "2427332", "vector": [], "sparse_vector": [], "title": "HealthWalks: Sensing Fine-grained Individual Health Condition via Mobility Data.", "authors": ["<PERSON><PERSON><PERSON>", "Shiqing Lyu", "Hancheng Cao", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Can health conditions be inferred from an individual's mobility pattern? Existing research has discussed the relationship between individual physical activity/mobility and well-being, yet no systematic study has been done to investigate the predictability of fine-grained health conditions from mobility, largely due to the unavailability of data and unsatisfactory modelling techniques. Here, we present a large-scale longitudinal study, where we collect the health conditions of 747 individuals who visit a hospital and tracked their mobility for 2 months in Beijing, China. To facilitate fine-grained individual health condition sensing, we propose HealthWalks, an interpretable machine learning model that takes user location traces, the associated points of interest, and user social demographics as input, at the core of which a Deterministic Finite Automaton (DFA) model is proposed to auto-generate explainable features to capture useful signals. We evaluate the effectiveness of our proposed model, which achieves 40.29% in micro-F1 and 31.63% in Macro-F1 for the 8-class disease category prediction, and outperforms the best baseline by 22.84% in Micro-F1 and 31.79% in Macro-F1. In addition, deeper analysis based on the SHapley Additive exPlanations (SHAP) showcases that HealthWalks can derive meaningful insights with regard to the correlation between mobility and health conditions, which provide important research insights and design implications for mobile sensing and health informatics.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432229"}, {"primary_key": "2427335", "vector": [], "sparse_vector": [], "title": "BitLight: Turning DLP Projections into an Interactive Surface through Bit-level Light Encoding.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents BitLight, a novel paradigm that uses the rapid flashing of a digital light processing (DLP) projector to encode an imperceptible mask temporally that, when sensed by a photodiode, uniquely specifies where the photodiode is located on the projected image. BitLight is inspired by the psychophysical phenomenon that the human visual system (HVS) cannot resolve rapid temporal changes in optical signals, so redundant optical signals could be inserted for tracking with some or little compromise on the original human perceived visual content. BitLight is the first to devise a bit-level temporal encoding to display RGB colors while also embedding tracking signals in a digital fashion. Compared to traditional visible-light-communication (VLC) systems that use frame-level encoding techniques such as luminance changes [10, 25, 26, 32] and alpha channel [16], the bit-level encoding of BitLight makes better use of the ultra-fine temporal division capabilities of DLP projectors to embed a much higher tracking data throughput and thus achieve faster localization speed. With our current prototype hardwares of a low-end microcontroller, cheap photodiodes, and a commercial off-the-shelfDLP projector, evaluation results have demonstrated an average of only 9.5ms to localize the sensor, versus 200ms by a comparison testbed that uses simple frame-level encodings.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432228"}, {"primary_key": "2427336", "vector": [], "sparse_vector": [], "title": "Finger Gesture Tracking for Interactive Applications: A Pilot Study with Sign Languages.", "authors": ["<PERSON><PERSON>", "Fengyang Jiang", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents FinGTrAC, a system that shows the feasibility of fine grained finger gesture tracking using low intrusive wearable sensor platform (smart-ring worn on the index finger and a smart-watch worn on the wrist). The key contribution is in scaling up gesture recognition to hundreds of gestures while using only a sparse wearable sensor set where prior works have been able to only detect tens of hand gestures. Such sparse sensors are convenient to wear but cannot track all fingers and hence provide under-constrained information. However application specific context can fill the gap in sparse sensing and improve the accuracy of gesture classification. Rich context exists in a number of applications such as user-interfaces, sports analytics, medical rehabilitation, sign language translation etc. This paper shows the feasibility of exploiting such context in an application of American Sign Language (ASL) translation. Noisy sensor data, variations in gesture performance across users and the inability to capture data from all fingers introduce non-trivial challenges. FinGTrAC exploits a number of opportunities in data preprocessing, filtering, pattern matching, context of an ASL sentence to systematically fuse the available sensory information into a Bayesian filtering framework. Culminating into the design of a Hidden Markov Model, a Viterbi decoding scheme is designed to detect finger gestures and the corresponding ASL sentences in real time. Extensive evaluation on 10 users shows a recognition accuracy of 94.2% for 100 most frequently used ASL finger gestures over different sentences. When the size of the dictionary is extended to 200 words, the accuracy is degrades gracefully to 90% thus indicating the robustness and scalability of the multi-stage optimization framework.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3414117"}, {"primary_key": "2427344", "vector": [], "sparse_vector": [], "title": "Fine-Grained Air Pollution Inference with Mobile Sensing Systems: A Weather-Related Deep Autoencoder Model.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Air pollution is a global health threat. Except static official air quality stations, mobile sensing systems are deployed for urban air pollution monitoring to achieve larger sensing coverage and greater sampling granularity. However, the data sparsity and irregularity also bring great challenges for pollution map recovery. To address these problems, we propose a deep autoencoder framework based inference algorithm. Under the framework, a partially observed pollution map formed by the irregular samples are input into the model, then an encoder and a decoder work together to recover the entire pollution map. Inside the decoder, we adopt a convolutional long short-term memory (ConvLSTM) model by revealing its physical interpretation with an atmospheric dispersion model, and further present a weather-related ConvLSTM to enable quasi real-time applications. To evaluate our algorithm, a half-year data collection was deployed with a real-world system on a coastal area including the Sino-Singapore Tianjin Eco-city in north China. With the resolution of 500 m x 500 m x 1 h, our offline method is proved to have high robustness against low sampling coverage and accidental sensor errors, obtaining 14.9% performance improvement over existing methods. Our quasi real-time model better captures the spatiotemporal dependencies in the pollution map with unevenly distributed samples than other real-time approaches, obtaining 4.2% error reduction.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397322"}, {"primary_key": "2427353", "vector": [], "sparse_vector": [], "title": "Detecting Job Promotion in Information Workers Using Mobile Sensing.", "authors": ["Subigya Nepal", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Pino G. Audia", "<PERSON>", "<PERSON>"], "summary": "Most people desire promotions in the workplace. Typically, rising through the ranks comes with increased demands, better salary and higher status among peers. However, promoted workers have to deal with new challenges, such as, adjusting to new roles and responsibilities, which can in turn impact their physical and mental wellbeing. In this year long study, we use mobile sensing to track physiological and behavioral patterns of N=141 information workers who are promoted. We show that the workers experience a change in their physiological and behavioral patterns after promotion captured by passive sensing from phones, wearables and Bluetooth beacons. Furthermore, we use a random convolutions based approach to extract patterns from multivariate time series signals and evaluate the performance of different models to classify a worker's mobile sensing data as belonging to a promoted or non-promoted period with an AUC of 0.72. As a result, we report for the first time that mobile sensing can detect job promotion events by modeling physiological and behavioral changes of information workers in an objective manner.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3414118"}, {"primary_key": "2427356", "vector": [], "sparse_vector": [], "title": "Predicting Brain Functional Connectivity Using Mobile Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Brain circuit functioning and connectivity between specific regions allow us to learn, remember, recognize and think as humans. In this paper, we ask the question if mobile sensing from phones can predict brain functional connectivity. We study the brain resting-state functional connectivity (RSFC) between the ventromedial prefrontal cortex (vmPFC) and the amygdala, which has been shown by neuroscientists to be associated with mental illness such as anxiety and depression. We discuss initial results and insights from the NeuroSence study, an exploratory study of 105 first year college students using neuroimaging and mobile sensing across one semester. We observe correlations between several behavioral features from students' mobile phones and connectivity between vmPFC and amygdala, including conversation duration (r=0.365, p&lt;0.001), sleep onset time (r=0.299, p&lt;0.001) and the number of phone unlocks (r=0.253, p=0.029). We use a support vector classifier and 10-fold cross validation and show that we can classify whether students have higher (i.e., stronger) or lower (i.e., weaker) vmPFC-amygdala RSFC purely based on mobile sensing data with an F1 score of 0.793. To the best of our knowledge, this is the first paper to report that resting-state brain functional connectivity can be predicted using passive sensing data from mobile phones.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3381001"}, {"primary_key": "2427364", "vector": [], "sparse_vector": [], "title": "MIMU: Mobile WiFi Usage Inference by Mining Diverse User Behaviors.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mobile WiFi is a newly emerging service in recent years, which provides convenience for users to access online resources and increases revenues for operators via services such as advertisements and application promotions. However, in practice, the prohibitively high system implementation and operational costs, especially the costs of perpetual data traffic, hinder the further deployment of mobile WiFi services. In this paper, we present MIMU, a usage inference system for data traffic saving suitable for ubiquitous mobile WiFi services. We demonstrate the performance of the system via an example from the real-world nationwide edge computing mobile WiFi infrastructure. To address the impact of diverse user behaviors, we investigate the WiFi network usage from the perspective of users and devices, focusing on two unique features of mobile WiFi: user mobility regularity and access irregularity. In particular, we first design a deep learning-based two-dimension usage predictor to infer the future mobile WiFi usage with 1) a user dimension model with temporal attention addressing dominant users with heavy bus WiFi usage, and 2) a device dimension model with spatial attention addressing diverse WiFi usage and connection. Based on the results of the predictor, an application of content caching is implemented in an iterative fashion to save the data traffic. We evaluate MIMU by real-world bus WiFi system data sets of three major cities with 6,643 bus WiFi devices and 150k daily active users in total. Our results show that MIMU outperforms state-of-the-art methods in terms of usage inference. Moreover, we summarize the lessons learned from our large-scale bus WiFi system investigation.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432226"}, {"primary_key": "2427366", "vector": [], "sparse_vector": [], "title": "Predicting Subjective Measures of Social Anxiety from Sparsely Collected Mobile Sensor Data.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Exploiting the capabilities of smartphones for monitoring social anxiety shows promise for advancing our ability to both identify indicators of and treat social anxiety in natural settings. Smart devices allow researchers to collect passive data unobtrusively through built-in sensors and active data using subjective, self-report measures with Ecological Momentary Assessment (EMA) studies. Prior work has established the potential to predict subjective measures from passive data. However, the majority of the past work on social anxiety has focused on a limited subset of self-reported measures. Furthermore, the data collected in real-world studies often results in numerous missing values in one or more data streams, which ultimately reduces the usable data for analysis and limits the potential of machine learning algorithms. We explore several approaches for addressing these problems in a smartphone based monitoring and intervention study of eighty socially anxious participants over a five week period. Our work complements and extends prior work in two directions: (i) we show the predictability of seven different self-reported dimensions of social anxiety, and (ii) we explore four imputation methods to handle missing data and evaluate their effectiveness in the prediction of subjective measures from the passive data. Our evaluation shows imputation of missing data reduces prediction error by as much as 22%. We discuss the implications of these results for future research.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411823"}, {"primary_key": "2427369", "vector": [], "sparse_vector": [], "title": "Dynamic Public Resource Allocation Based on Human Mobility Prediction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Yanhua Li", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The objective of public resource allocation, e.g., the deployment of billboards, surveillance cameras, base stations, trash bins, is to serve more people. However, due to the dynamics of human mobility patterns, people are distributed unevenly on the spatial and temporal domains. As a result, in many cases, redundant resources have to be deployed to meet the crowd coverage requirements, which leads to high deployment costs and low usage. Fortunately, with the development of unmanned vehicles, the dynamic allocation of those public resources becomes possible. To this end, we provide the first attempt to design an effective and efficient scheduling algorithm for the dynamic public resource allocation. We formulate the problem as a novel multi-agent long-term maximal coverage scheduling (MALMCS) problem, which considers the crowd coverage and the energy limitation during a whole day. Two main components are employed in the system: 1) multi-step crowd flow prediction, which makes multi-step crowd flow prediction given the current crowd flows and external factors; and 2) energy adaptive scheduling, which employs a two-step heuristic algorithm, i.e., energy adaptive scheduling (EADS), to generate a scheduling plan that maximizes the crowd coverage within the service time for agents. Extensive experiments based on real crowd flow data in Happy Valley (a popular theme park in Beijing) demonstrate the effectiveness and efficiency of our approach.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3380986"}, {"primary_key": "2427376", "vector": [], "sparse_vector": [], "title": "Weakly Supervised Multi-Task Representation Learning for Human Activity Analysis Using Wearables.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Sensor data streams from wearable devices and smart environments are widely studied in areas like human activity recognition (HAR), person identification, or health monitoring. However, most of the previous works in activity and sensor stream analysis have been focusing on one aspect of the data, e.g. only recognizing the type of the activity or only identifying the person who performed the activity. We instead propose an approach that uses a weakly supervised multi-output siamese network that learns to map the data into multiple representation spaces, where each representation space focuses on one aspect of the data. The representation vectors of the data samples are positioned in the space such that the data with the same semantic meaning in that aspect are closely located to each other. Therefore, as demonstrated with a set of experiments, the trained model can provide metrics for clustering data based on multiple aspects, allowing it to address multiple tasks simultaneously and even to outperform single task supervised methods in many situations. In addition, further experiments are presented that in more detail analyze the effect of the architecture and of using multiple tasks within this framework, that investigate the scalability of the model to include additional tasks, and that demonstrate the ability of the framework to combine data for which only partial relationship information with respect to the target tasks is available.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397330"}, {"primary_key": "2427379", "vector": [], "sparse_vector": [], "title": "MIFF: Human Mobility Extractions with Cellular Signaling Data under Spatio-temporal Uncertainty.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Wenqing Qiu", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Human Mobility Extraction with cellular Signaling Data (SD) is essential for human mobility understanding, epidemic control, and wireless network planning. SD log the detailed interactions between cellphones and cellular towers, but suffer from a spatio-temporal uncertainty problem due to cellular network tower-level load rebalancing (switching users between towers) and cellphone usage activities. To date, most models focus on utilizing better data like RSSI or GPS, do not directly address uncertainty. To address the SD uncertainty issue, we utilize two insights based on (i) individuals' regular mobility patterns and (ii) common co-movement mobility patterns between cellphone users as suggested by fundamental human mobility nature. Accordingly, we design a Multi-Information Fusion Framework (MIFF) to assist in extracting road-level human mobility based on cell-tower level traces. To evaluate the effectiveness of MIFF, we conduct experiments on one-month SD obtained from a cellular service operator, and SD manually collected by handheld mobile devices in two cities in China. Four transportation modes, namely railways, cars, buses, and bikes are evaluated. Experimental results show that with MIFF, our road-level trajectory extraction accuracy can be improved by 5.0% on Point correct matching index and 68.5% on Geographic Error on average.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432238"}, {"primary_key": "2427383", "vector": [], "sparse_vector": [], "title": "OptiStructures: Fabrication of Room-Scale Interactive Structures with Embedded Fiber Bragg Grating Optical Sensors and Displays.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A recent topic of considerable interest in the \"smart building\" community involves building interactive devices using sensors, and rapidly creating these objects using new fabrication methods. However, much of this work has been done at what might be called hand scale, with less attention paid to larger objects and structures (at furniture or room scales) despite the fact that we are very often literally surrounded by such objects. In this work, we present a new set of techniques for creating interactive objects at these scales. We demonstrate fabrication of both input sensors and displays directly into cast materials -those formed from a liquid or paste which solidifies in a mold; including, for example: concrete, plaster, polymer resins, and composites. Through our novel set of sensing and fabrication techniques, we enable human activity recognition at room scale and across a variety of materials. Our techniques create objects that appear the same as typical passive objects, but contain internal fiber optics for both input sensing and simple displays. We use a new fabrication device to inject optical fibers into CNC milled molds. Fiber Bragg Grating optical sensors configured as very sensitive vibration sensors are embedded in these objects. These require no internal power, can be placed at multiple locations along a single fiber, and can be interrogated from the end of the fiber. We evaluate the performance of our system by creating two full-scale application prototypes: an interactive wall, and an interactive table. With these prototypes, we demonstrate the ability of our system to sense a variety of human activities across eight different users. Our tests show that with suitable materials these sensors can detect and classify both direct interactions (such as tapping) and more subtle vibrations caused by activities such as walking across the floor nearby.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397310"}, {"primary_key": "2427384", "vector": [], "sparse_vector": [], "title": "Mobile Mood Tracking: An Investigation of Concise and Adaptive Measurement Instruments.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Commonly used mood measures are either lengthy or too complicated for repeated use. Mood tracking research is, therefore, associated with challenges such as user dissatisfaction, fatigue, or dropouts from studies. Previous efforts to improve user experience are mostly ambiguous concerning their validity and the extent of improvement they provide (e.g., compared to established measures, such as PANAS). This paper investigates the shortening of a self-reported mood measure using smartphones with four independent samples, and provides a baseline for comparing the usability and accuracy of future measures. It first examines whether user self-assessment of overall positive and negative activations with a two-item measure can capture mood as well as I-PANAS-SF. It next examines user's learning effect in repeated usage of the measure. Finally, it introduces the design of an adaptive mood measure that reduces the number of questions based on its prediction of user mood fluctuations. This adaptive measure can potentially capture specific mood states, as well as overall mood. The paper then explores user satisfaction and compliance with this measure in a longitudinal study. The results of this paper reveal that the investigated two-item measure is a valid and reliable tool for capturing a user's overall mood and mood fluctuations. The negative activation from this measure is associated with stress. Our results suggest that the association between mood and stress generally depends on the measure of mood and its items. We discovered that a non-complex self-explanatory measure is fairly resilient for repeated use with respect to the required effort and the accuracy of the measure in both daily and weekly evaluations. Adaptively reducing the length of a mood measure does not seem to impact user compliance but may slightly improve usability. We also noticed that positive and negative activations have a slightly different pattern of behavior with reference to the preceding mood states.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432207"}, {"primary_key": "2427387", "vector": [], "sparse_vector": [], "title": "UbiquiTouch: Self Sustaining Ubiquitous Touch Interfaces.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present UbiquiTouch, an ultra low power wireless touch interface. With an average power consumption of 30.91μW, UbiquiTouch can run on energy harvested from ambient light. It achieves this performance through low power touch sensing and passive communication to a nearby smartphone using ambient FM backscatter. This approach allows UbiquiTouch to be deployed in mobile situations both in indoor and outdoor locations, without the need for any additional infrastructure for operation. To demonstrate the potential of this technology, we evaluate it in several different and realistic scenarios. Finally, we address the future application space for this technology.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3380989"}, {"primary_key": "2427391", "vector": [], "sparse_vector": [], "title": "Understanding User Behavior in Car Sharing Services Through The Lens of Mobility: Mixing Qualitative and Quantitative Studies.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Qualitative and quantitative user studies can reveal valuable insights into user behavior, which in turn can assist system designers in providing better user experiences. Car sharing (e.g., Zipcar and car2go), as an emerging App-based online shared mobility mode, has been increasing dramatically worldwide in recent years. However, to date, comprehensive user behavior in car sharing systems has not been investigated, which is essential for understanding their characteristics and promotion roadblocks. With the goal of understanding various facets of user behavior in online car sharing systems, in this paper, we performed a qualitative and quantitative user study by adopting a mixed-methods approach. We first designed an attitude-aware online survey with a set of qualitative questions to perceive people's subjective attitudes to online car sharing, where a total of 185 participants (68 females) completed the survey. Next, we quantitatively analyzed a one-year real-world car sharing operation dataset collected from the Chinese city Beijing, which involves over 68,000 unique users and over 587,850 usage records. We dissected this attitude-free dataset to understand the objective car sharing user behavior from different dimensions, e.g., spatial, temporal, and demographic. Furthermore, we conducted a comparative study by utilizing one-year data from other two representative Chinese city Fuzhou and Lanzhou to show if the obtained findings from Beijing data may be generalizable to other cities having different urban features, e.g., different city size, population density, wealth, and climate conditions. We also do a case study by designing a user behavior-aware usage prediction model (i.e., BeXGBoost) based on findings from our user study (e.g., unbalanced spatiotemporal usage patterns, weekly regularity, demographic-related usage difference, and low-frequency revisitation), which is the basis for car sharing service station deployment and vehicle rebalancing. Finally, we summarize a set of findings obtained from our study about the unique user behavior in online car sharing systems, combined with some detailed discussions about implications for design.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432200"}, {"primary_key": "2427394", "vector": [], "sparse_vector": [], "title": "mSense: Towards Mobile Material Sensing with a Single Millimeter-Wave Radio.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "Target material sensing in ubiquitous contexts plays an important role in various applications. Recently, a few wireless sensing systems have been proposed for material identification. Yet, prior work usually requires to capture the signals penetrating a target (with devices set up on both sides of the target) or to instrument the target (e.g., by attaching an RFID tag), relies on multiple transceivers, and/or involves unexplainable feature engineering. In this paper, we explore the feasibility of material identification by analyzing only the signals reflected off the target, rather than those penetrating it, with a single RF radio. We present mSense, a mobile material sensing system using a single millimeter-wave (mmWave) radio. At the core of mSense is the insight that different materials reflect RF signals in distinct ways. We propose a novel and easy-to-measure material reflection feature that quantitatively characterizes the material's reflectivity. A set of techniques are then devised to achieve accurate and robust material identification despite various factors, including device mobility, hardware defects of commodity mmWave radios, environmental interferences, and etc. Experiments using commercial mmWave networking chipsets demonstrate an average accuracy of 93% in categorizing five common types of materials: aluminum, ceramic, plastic, wood, and water, regardless of their different sizes and thicknesses. The accuracy retains about 90% even in mobile scenarios (i.e., a user holds and moves the radio to perform sensing), which shows the great potential of mSense for mobile applications. A case study on 21 daily objects of various materials, shapes, and textures over different days further validates the performance in differentiating real-life objects.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411822"}, {"primary_key": "2427397", "vector": [], "sparse_vector": [], "title": "When Sharing Economy Meets IoT: Towards Fine-grained Urban Air Quality Monitoring through Mobile Crowdsensing on Bike-share System.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Air pollution is a serious global issue impacting public health and social economy. In particular, exposure to small particulate matter of 2.5 microns or less in diameter (PM2.5) can cause cardiovascular and respiratory diseases, and cancer. Fine-grained urban air quality monitoring is crucial yet difficult to achieve. In this paper, we present the design, implementation, and evaluation of an ambient environment aware system, namely UbiAir, which can support fine-grained urban air quality monitoring through mobile crowdsensing on a bike-sharing system. We have built specific IoT box configured with multiple pollutant sensors and attached on shared bikes to sample micro-scale air quality data in the monitoring space that is split by a scalable grid structure. Both hardware and software data calibration methods are exploited in UbiAir to make the sampled data reliable. Then, we use Bayesian compressive sensing (BCS) as an inference model that leverages the calibrated samples to recover data points without direct measurements and reconstruct an accurate air quality map covering the entire monitoring space. In addition, red envelope based incentive schemes and differential rewarding strategies have been designed in UbiAir, and an adaptive BCS algorithm is proposed to deploy the red envelopes at the most informative positions to facilitate data sampling and inference. We have tested our system on campus with over 100k data measurements collected by 36 students through 18 days. Our real-world experiments show that UbiAir is a light-weight, low-cost, accurate and scalable system for fine-grained air quality monitoring, as compared with other solutions.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397328"}, {"primary_key": "2427399", "vector": [], "sparse_vector": [], "title": "SUME: Semantic-enhanced Urban Mobility Network Embedding for User Demographic Inference.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recent years have witnessed a rapid proliferation of personalized mobile applications, which poses a pressing need for accurate user demographics inference. Facilitated by the prevalent smart devices, the ubiquitously collected mobility trace presents a promising opportunity to infer user demographics at large-scale. In this paper, we propose a novel Semantic-enhanced Urban Mobility Embedding (SUME) model, which learns dense representation vectors for user demographic inference by jointly modelling the physical mobility patterns and the semantic of urban mobility. Specifically, SUME models urban mobility as a heterogeneous network of users and locations, with various types of edges denoting the physical visitation and semantic similarities. Moreover, SUME optimizes the node representation vectors with two alternating objective functions that preserve the feature in physical and semantic domains, respectively. As a result, it is able to capture the effective signals in the heterogeneous urban mobility network. Empirical experiments on two real-world mobility traces show the proposed model significantly out-performs all state-of-the-art baselines with an accuracy margin of 8.6%~14.3% for occupation, gender, age, education and income inference. In addition, further experiments show SUME is able to reveal meaningful correlations between user demographics and the mobility patterns in spatial, temporal and urban structure domain.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411807"}, {"primary_key": "2427400", "vector": [], "sparse_vector": [], "title": "Recognizing Unintentional Touch on Interactive Tabletop.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A multi-touch interactive tabletop is designed to embody the benefits of a digital computer within the familiar surface of a physical tabletop. However, the nature of current multi-touch tabletops to detect and react to all forms of touch, including unintentional touches, impedes users from acting naturally on them. In our research, we leverage gaze direction, head orientation and screen contact data to identify and filter out unintentional touches, so that users can take full advantage of the physical properties of an interactive tabletop, e.g., resting hands or leaning on the tabletop during the interaction. To achieve this, we first conducted a user study to identify behavioral pattern differences (gaze, head and touch) between completing usual tasks on digital versus physical tabletops. We then compiled our findings into five types of spatiotemporal features, and train a machine learning model to recognize unintentional touches with an F1 score of 91.3%, outperforming the state-of-the-art model by 4.3%. Finally we evaluated our algorithm in a real-time filtering system. A user study shows that our algorithm is stable and the improved tabletop effectively screens out unintentional touches, and provide more relaxing and natural user experience. By linking their gaze and head behavior to their touch behavior, our work sheds light on the possibility of future tabletop technology to improve the understanding of users' input intention.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3381011"}, {"primary_key": "2427420", "vector": [], "sparse_vector": [], "title": "OptoSense: Towards Ubiquitous Self-Powered Ambient Light Sensing Surfaces.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>-<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Ubiquitous computing requires robust and sustainable sensing techniques to detect users for explicit and implicit inputs. Existing solutions with cameras can be privacy-invasive. Battery-powered sensors require user maintenance, preventing practical ubiquitous sensor deployment. We present OptoSense, a general-purpose self-powered sensing system which senses ambient light at the surface level of everyday objects as a high-fidelity signal to infer user activities and interactions. To situate the novelty of OptoSense among prior work and highlight the generalizability of the approach, we propose a design framework of ambient light sensing surfaces, enabling implicit activity sensing and explicit interactions in a wide range of use cases with varying sensing dimensions (0D, 1D, 2D), fields of view (wide, narrow), and perspectives (egocentric, allocentric). OptoSense supports this framework through example applications ranging from object use and indoor traffic detection, to liquid sensing and multitouch input. Additionally, the system can achieve high detection accuracy while being self-powered by ambient light. On-going improvements that replace Optosense's silicon-based sensors with organic semiconductors (OSCs) enable devices that are ultra-thin, flexible, and cost effective to scale.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411826"}, {"primary_key": "2427422", "vector": [], "sparse_vector": [], "title": "TouchID: User Authentication on Mobile Devices via Inertial-Touch Gesture Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Due to the widespread use of mobile devices, it is essential to authenticate users on mobile devices to prevent sensitive information leakage. In this paper, we propose TouchID, which combinedly uses the touch sensor and the inertial sensor for gesture analysis, to provide a touch gesture based user authentication scheme. Specifically, TouchID utilizes the touch sensor to analyze the on-screen gesture while using the inertial sensor to analyze the device's motion caused by the touch gesture, and then combines the unique features from the on-screen gesture and the device's motion for user authentication. To mitigate the intra-class difference and reduce the inter-class similarity, we propose a spatial alignment method for sensor data and segment the touch gesture into multiple sub-gestures in space domain, to keep the stability of the same user and enhance the discriminability of different users. To provide a uniform representation of touch gestures with different topological structures, we present a four-part based feature selection method, which classifies a touch gesture into a start node, an end node, the turning node(s), and the smooth paths, and then select effective features from these parts based on Fisher Score. In addition, considering the uncertainty of user's postures, which may change the sensor data of same touch gesture, we propose a multi-threshold kNN based model to adaptively tolerate the posture difference for user authentication. Finally, we implement TouchID on commercial smartphones and conduct extensive experiments to evaluate TouchID. The experiment results show that TouchID can achieve a good performance for user authentication, i.e., having a low equal error rate of 4.90%.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432192"}, {"primary_key": "2427423", "vector": [], "sparse_vector": [], "title": "SyncWISE: Window Induced Shift Estimation for Synchronization of Video and Accelerometry from Wearable Sensors.", "authors": ["Yun <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Bonnie <PERSON>", "<PERSON>", "Nabil <PERSON>"], "summary": "The development and validation of computational models to detect daily human behaviors (e.g., eating, smoking, brushing) using wearable devices requires labeled data collected from the natural field environment, with tight time synchronization of the micro-behaviors (e.g., start/end times of hand-to-mouth gestures during a smoking puff or an eating gesture) and the associated labels. Video data is increasingly being used for such label collection. Unfortunately, wearable devices and video cameras with independent (and drifting) clocks make tight time synchronization challenging. To address this issue, we present the Window Induced Shift Estimation method for Synchronization (SyncWISE) approach. We demonstrate the feasibility and effectiveness of our method by synchronizing the timestamps of a wearable camera and wearable accelerometer from 163 videos representing 45.2 hours of data from 21 participants enrolled in a real-world smoking cessation study. Our approach shows significant improvement over the state-of-the-art, even in the presence of high data loss, achieving 90% synchronization accuracy given a synchronization tolerance of 700 milliseconds. Our method also achieves state-of-the-art synchronization performance on the CMU-MMAC dataset.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411824"}, {"primary_key": "2427261", "vector": [], "sparse_vector": [], "title": "The Effect of Goal Moderation on the Achievement and Satisfaction of Physical Activity Goals.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Many self-trackers lose interest in, disengage from and ultimately withdraw from tracking. Reasons for this include poor motivation, unmet expectations and difficulty in attaining daily goals. To support users in reflecting on their goals more realistically, we developed FitReflect, an app that moderates physical activity goals by factoring in users' confidence in achieving the goals. The app also encourages users to reflect on their goals regularly and think about the factors affecting their achievement. We conducted a 4-week field experiment where we trialled the app with fourteen Fitbit users. We found that, compared with a non-moderated goal condition, participants with moderated goals achieved their goals more often, got closer to them, and adjusted them more frequently. Crucially, they were also more satisfied with their physical activity. More frequent goal updates were key to align user goals with their confidence and capabilities in achieving them.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432209"}, {"primary_key": "2427262", "vector": [], "sparse_vector": [], "title": "CARIN: Wireless CSI-based Driver Activity Recognition under the Interference of Passengers.", "authors": ["<PERSON><PERSON> Bai", "<PERSON><PERSON><PERSON>"], "summary": "Recent studies have proposed to use the Channel State Information (CSI) of WiFi wireless channel for human gesture recognition. As an important application, CSI-based driver activity recognition in passenger vehicles has received increasing research attention. However, a serious limitation of almost all the existing WiFi-based recognition solutions is that they can only recognize the activity of a single person at a time, because the activities of other people (if performed at the same time) can interfere with the WiFi signals. In a sharp contrast, there can often be one or more passengers in any vehicles. In this paper, we propose CARIN, CSI-based driver Activity Recognition under the INterference of passengers. CARIN features a combination-based solution that profiles all the possible activity combinations of driver and (one or more) passengers in offline training and then performs recognition online. To attack possible combination explosion, we first leverage in-car pressure sensors to significantly reduce combinations, because there are only limited seating options in a passenger vehicle. We then formulate a distance minimization problem for fast runtime recognition. In addition, a period analysis methodology is designed based on the kNN classifier to recognize activities that have a sequence of body movements, like continuous head nodding due to driver fatigue. Our results in a real car with 3,000 real-world traces show that CARIN can achieve an overall F1 score of 90.9%, and outperforms the three state-of-the-art solutions by 32.2%.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3380992"}, {"primary_key": "2427263", "vector": [], "sparse_vector": [], "title": "Adversarial Multi-view Networks for Activity Recognition.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Salil S<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Human activity recognition (HAR) plays an irreplaceable role in various applications and has been a prosperous research topic for years. Recent studies show significant progress in feature extraction (i.e., data representation) using deep learning techniques. However, they face significant challenges in capturing multi-modal spatial-temporal patterns from the sensory data, and they commonly overlook the variants between subjects. We propose a Discriminative Adversarial MUlti-view Network (DAMUN) to address the above issues in sensor-based HAR. We first design a multi-view feature extractor to obtain representations of sensory data streams from temporal, spatial, and spatio-temporal views using convolutional networks. Then, we fuse the multi-view representations into a robust joint representation through a trainable Hadamard fusion module, and finally employ a Siamese adversarial network architecture to decrease the variants between the representations of different subjects. We have conducted extensive experiments under an iterative left-one-subject-out setting on three real-world datasets and demonstrated both the effectiveness and robustness of our approach.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397323"}, {"primary_key": "2427264", "vector": [], "sparse_vector": [], "title": "Calm Commute: Guided Slow Breathing for Daily Stress Management in Drivers.", "authors": ["<PERSON>", "<PERSON>", "So Yeon Park", "<PERSON>", "<PERSON>"], "summary": "Commutes provide an opportune time and space for interventions that mitigate stress-particularly stress accumulated during the workday. In this study, we test the efficacy and safety of haptic guided slow breathing interventions of short duration while driving. We also present design and experimental implications for evolving these interventions from prior simulator to moving vehicle scenarios. We ran a controlled study (N=24) testing a haptic guided breathing system in a closed circuit under normal and stressful driving conditions. Results show the intervention to be successful in both user adoption and system effectiveness with an 82% rate of engagement in intervention and clear reduction of breathing rate and physiological arousal, with no effect on driving safety and minimal effect on performance. The haptic intervention received positive acceptance from the participants: all indicated a willingness to engage with the intervention in the future and all rated the intervention as safe for traffic applications. The results of this study encourage further investigations exploring the use of the intervention on public roads and monitoring for longitudinal health benefits.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3380998"}, {"primary_key": "2427266", "vector": [], "sparse_vector": [], "title": "Where You Go Matters: A Study on the Privacy Implications of Continuous Location Tracking.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Data gathered from smartphones enables service providers to infer a wide range of personal information about their users, such as their traits, their personality, and their demographics. This personal information can be made available to third parties, such as advertisers, sometimes unbeknownst to the users. Leveraging location information, advertisers can serve ads micro-targeted to users based on the places they visited. Understanding the types of information that can be extracted from location data and implications in terms of user privacy is of critical importance. In this context, we conducted an extensive in-the-wild research study to shed light on the range of personal information that can be inferred from the places visited by users, as well as privacy sensitivity of the personal information. To this end, we developed TrackingAdvisor, a mobile application that continuously collects user location and extracts personal information from it. The app also provides an interface to give feedback about the relevance of the personal information inferred from location data and its corresponding privacy sensitivity. Our findings show that, while some personal information such as social activities is not considered private, other information such as health, religious belief, ethnicity, political opinions, and socio-economic status is considered private by the participants of the study. This study paves the way to the design of privacy-preserving systems that provide contextual recommendations and explanations to help users further protect their privacy by making them aware of the consequences of sharing their personal data.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432699"}, {"primary_key": "2427267", "vector": [], "sparse_vector": [], "title": "Assumptions Checked: How Families Learn About and Use the Echo Dot.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Users of voice assistants often report that they fall into patterns of using their device for a limited set of interactions, like checking the weather and setting alarms. However, it's not clear if limited use is, in part, due to lack of learning about the device's functionality. We recruited 10 diverse families to participate in a one-month deployment study of the Echo Dot, enabling us to investigate: 1) which features families are aware of and engage with, and 2) how families explore, discover, and learn to use the Echo Dot. Through audio recordings of families' interactions with the device and pre- and post-deployment interviews, we find that families' breadth of use decreases steadily over time and that families learn about functionality through trial and error, asking the Echo Dot about itself, and through outside influencers such as friends and family. Formal outside learning influencers, such as manufacturer emails, are less influential. Drawing from diffusion of innovation theory, we describe how a home-based voice interface might be positioned as a near-peer to the user, and that by describing its own functionality using just-in-time learning, the home-based voice interface becomes a trustworthy learning influencer from which users can discover new functionalities.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3380993"}, {"primary_key": "2427268", "vector": [], "sparse_vector": [], "title": "Countering Acoustic Adversarial Attacks in Microphone-equipped Smart Home Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Deep neural networks (DNNs) continue to demonstrate superior generalization performance in an increasing range of applications, including speech recognition and image understanding. Recent innovations in compression algorithms, design of efficient architectures and hardware accelerators have prompted a rapid growth in deploying DNNs on mobile and IoT devices to redefine user experiences. Relying on the superior inference quality of DNNs, various voice-enabled devices have started to pervade our everyday lives and are increasingly used for, e.g., opening and closing doors, starting or stopping washing machines, ordering products online, and authenticating monetary transactions. As the popularity of these voice-enabled services increases, so does their risk of being attacked. Recently, DNNs have been shown to be extremely brittle under adversarial attacks and people with malicious intentions can potentially exploit this vulnerability to compromise DNN-based voice-enabled systems. Although some existing work already highlights the vulnerability of audio models, very little is known of the behaviour of compressed on-device audio models under adversarial attacks. This paper bridges this gap by investigating thoroughly the vulnerabilities of compressed audio DNNs and makes a stride towards making compressed models robust. In particular, we propose a stochastic compression technique that generates compressed models with greater robustness to adversarial attacks. We present an extensive set of evaluations on adversarial vulnerability and robustness of DNNs in two diverse audio recognition tasks, while considering two popular attack algorithms: FGSM and PGD. We found that error rates of conventionally trained audio DNNs under attack can be as high as 100%. Under both white- and black-box attacks, our proposed approach is found to decrease the error rate of DNNs under attack by a large margin.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397332"}, {"primary_key": "2427269", "vector": [], "sparse_vector": [], "title": "It Didn&apos;t Sound Good with My Cochlear Implants: Understanding the Challenges of Using Smart Assistants for Deaf and Hard of Hearing Users.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "How do deaf and hard of hearing (DHH) individuals use smart assistants (SAs)? Does the prominent use of voice interfaces in most SAs pose unique challenges for DHH users? In this work, we aim to answer these questions by conducting 4 in-depth interviews, as well as collecting survey data from 73 DHH individuals. Our findings show that individuals, even with profound deafness, can leverage SAs to accomplish complex daily tasks. However, we also identified a number of common challenges DHH individuals face when interacting with SAs (e.g., high pitch used in the default SA voice interfaces can be incompatible with hearing aids, difficulty using mobile SAs in public places with loud background noise). Based on these insights, we provide a set of suggestions for designing SAs that can better accommodate a wide range of hearing abilities. Specifically, SAs should provide more customization options to allow the user to tailor their SA to meet their hearing needs over time. For example, using a pitch-frequency test feature, much like audiograms conducted by audiologists, could allow users to calibrate their SA's voice to fit within their optimal range. We also see a need to provide more clear and actionable error messages conveyed beyond audio notifications, such as more meaningful light notifications. These recommendations and findings provide the first step forward toward a more inclusive SA by addressing accessibility needs unique to this group.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432194"}, {"primary_key": "2427271", "vector": [], "sparse_vector": [], "title": "Teaching RF to Sense without RF Training Measurements.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Chitra R. <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we propose a novel, generalizable, and scalable idea that eliminates the need for collecting Radio Frequency (RF) measurements, when training RF sensing systems for human-motion-related activities. Existing learning-based RF sensing systems require collecting massive RF training data, which depends heavily on the particular sensing setup/involved activities. Thus, new data needs to be collected when the setup/activities change, significantly limiting the practical deployment of RF sensing systems. On the other hand, recent years have seen a growing, massive number of online videos involving various human activities/motions. In this paper, we propose to translate such already-available online videos to instant simulated RF data for training any human-motion-based RF sensing system, in any given setup. To validate our proposed framework, we conduct a case study of gym activity classification, where CSI magnitude measurements of three WiFi links are used to classify a person's activity from 10 different physical exercises. We utilize YouTube gym activity videos and translate them to RF by simulating the WiFi signals that would have been measured if the person in the video was performing the activity near the transceivers. We then train a classifier on the simulated data, and extensively test it with real WiFi data of 10 subjects performing the activities in 3 areas. Our system achieves a classification accuracy of 86% on activity periods, each containing an average of 5.1 exercise repetitions, and 81% on individual repetitions of the exercises. This demonstrates that our approach can generate reliable RF training data from already-available videos, and can successfully train an RF sensing system without any real RF measurements. The proposed pipeline can also be used beyond training and for analysis and design of RF sensing systems, without the need for massive RF data collection.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432224"}, {"primary_key": "2427274", "vector": [], "sparse_vector": [], "title": "Prompto: Investigating Receptivity to Prompts Based on Cognitive Load from Memory Training Conversational Agent.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Sur<PERSON>"], "summary": "Prospective memory lapses, which involve forgetting to perform intended actions, affect independent living in older adults. Although memory training using smartphone applications could address them, users are sometimes unaware of available times for training or forget about it, presenting a need for proactive prompts. Existing applications mostly provide time-based prompts and prompts based on users' cognitive contexts remain an under-explored area. We developed Prompto, a conversational memory coach that detects physiological signals to suggest training sessions when users are relaxed and potentially more receptive. Our study with 21 older adults showed that users were more receptive to prompts and memory training under low cognitive load than under high cognitive load. Interviews and an in-the-wild deployment of Prompto indicated that majority of users appreciated the concept, found it helpful and were likely to respond to its prompts. We contribute towards developing technologies with cognitive context-aware prompting based on users' physiological readings.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432190"}, {"primary_key": "2427275", "vector": [], "sparse_vector": [], "title": "A Systematic Study of Unsupervised Domain Adaptation for Robust Human-Activity Recognition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Wearable sensors are increasingly becoming the primary interface for monitoring human activities. However, in order to scale human activity recognition (HAR) using wearable sensors to million of users and devices, it is imperative that HAR computational models are robust against real-world heterogeneity in inertial sensor data. In this paper, we study the problem of wearing diversity which pertains to the placement of the wearable sensor on the human body, and demonstrate that even state-of-the-art deep learning models are not robust against these factors. The core contribution of the paper lies in presenting a first-of-its-kind in-depth study of unsupervised domain adaptation (UDA) algorithms in the context of wearing diversity -- we develop and evaluate three adaptation techniques on four HAR datasets to evaluate their relative performance towards addressing the issue of wearing diversity. More importantly, we also do a careful analysis to learn the downsides of each UDA algorithm and uncover several implicit data-related assumptions without which these algorithms suffer a major degradation in accuracy. Taken together, our experimental findings caution against using UDA as a silver bullet for adapting HAR models to new domains, and serve as practical guidelines for HAR practitioners as well as pave the way for future research on domain adaptation in HAR.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3380985"}, {"primary_key": "2427277", "vector": [], "sparse_vector": [], "title": "ContAuth: Continual Learning Framework for Behavioral-based User Authentication.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON> <PERSON><PERSON>", "Pan Hui", "<PERSON>"], "summary": "User authentication is key in user authorization on smart and personal devices. Over the years, several authentication mechanisms have been proposed: these also include behavioral-based biometrics. However, behavioral-based biometrics suffer from two issues: they are prone to degradation in performance (accuracy) over time (e.g., due to data distribution changes arising from user behavior) and the need to learn the machine learning model from scratch, when adding new users. In this paper, we propose ContAuth, a system that can enhance the robustness of behavioral-based authentication. ContAuth continuously adapts to new incoming data (data incremental learning) and is able to add new users without retraining (class incremental learning). Specifically, ContAuth combines deep learning models with online learning models to achieve learning on the fly, thereby preventing a severe drop in the accuracy between sessions (over time). To add new users, ContAuth employs class incremental learning methods. We evaluate ContAuth on multiple behavior-based user authentication modalities: breathing, gait. and EMG. Our results show that our framework can help True Positive Rate (TPR) to remain high (&gt;85 %) compared to other methods for all the modalities except EMG (&gt;70%) across the sessions while keeping False Positive Rates (FPR) at a minimum (0-10%). It can achieve up to 35% improvement in TPR over a traditional deep learning model. Additionally, iCaRL (an incremental learning method) enables ContAuth to allow the addition of new users by alleviating catastrophic forgetting, to a large extent. Finally, we also show that ContAuth can be deployed efficiently and effectively on device, further providing data privacy.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432203"}, {"primary_key": "2427278", "vector": [], "sparse_vector": [], "title": "Will You Come Back / Check-in Again?: Understanding Characteristics Leading to Urban Revisitation and Re-check-in.", "authors": ["<PERSON><PERSON><PERSON>", "Hancheng Cao", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recent years have witnessed much work unraveling human mobility patterns through urban visitation and location check-in data. Traditionally, user visitation and check-in have been assumed as the same behavior, yet this fundamental assumption can be questionable and lacks supporting evidence. In this paper, we seek to understand the similarities and differences of visitation and check-in by presenting a large-scale systematic analysis under the specific setting of urban revisitation and re-check-in, which demonstrate people's periodic behaviors and regularities. Leveraging a localization dataset to model urban revisitation and a Foursqaure dataset to delineate re-check-in, we identify features concerning POI visitation patterns, POI background information, user visitation patterns, user preference and users' behavioral characteristics to understand their effects on urban revisitation and re-check-in. We examine the relationship between revisitation/re-check-in rate and the features we identify, highlighting the similarities and differences between urban revisitation and re-check-in. We demonstrate the prediction effectiveness of the identified characteristics utilizing machine learning models, with an overall ROC AUC of 0.92 for urban revisitation and 0.82 for re-check-in, respectively. This study has important research implications, including improved modeling of human mobility and better understanding of human behavior, and sheds light on designing novel ubiquitous computing applications.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411812"}, {"primary_key": "2427279", "vector": [], "sparse_vector": [], "title": "Listen to Your Fingers: User Authentication Based on Geometry Biometrics of Touch Gesture.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Inputting a pattern or PIN code on the touch screen is a popular method to prevent unauthorized access to mobile devices. However, these sensitive tokens are highly susceptible to being inferred by various types of side-channel attacks, which can compromise the security of the private data stored in the device. This paper presents a second-factor authentication method, TouchPrint, which relies on the user's hand posture shape traits (dependent on the individual different posture type and unique hand geometry biometrics) when the user inputs PIN or pattern. It is robust against the behavioral variability of inputting a passcode and places no restrictions on input manner (e.g., number of the finger touching the screen, moving speed, or pressure). To capture the spatial characteristic of the user's hand posture shape when input the PIN or pattern, <PERSON><PERSON>rint performs active acoustic sensing to scan the user's hand posture when his/her finger remains static at some reference positions on the screen (e.g., turning points for the pattern and the number buttons for the PIN code), and extracts the multipath effect feature from the echo signals reflected by the hand. Then, TouchPrint fuses with the spatial multipath feature-based identification results generated from the multiple reference positions to facilitate a reliable and secure MFA system. We build a prototype on smartphone and then evaluate the performance of TouchPrint comprehensively in a variety of scenarios. The experiment results demonstrate that TouchPrint can effectively defend against the replay attacks and imitate attacks. Moreover, TouchPrint can achieve an authentication accuracy of about 92% with only ten training samples.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411809"}, {"primary_key": "2427281", "vector": [], "sparse_vector": [], "title": "Silver Tape: Inkjet-Printed Circuits Peeled-and-Transferred on Versatile Substrates.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Youngwook Do", "<PERSON>", "Tung D. Ta", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Oh"], "summary": "We propose Silver Tape, a simple yet novel fabrication technique to transfer inkjet-printed silver traces from paper onto versatile substrates, without time-/space- consuming processes such as screen printing or heat sintering. This allows users to quickly implement silver traces with a variety of properties by exploiting a wide range of substrates. For instance, high flexibility can be achieved with Scotch tape, high transparency with polydimethylsiloxane (PDMS), heat durability with Kapton polyimide tape, water solubility with 3M water-soluble tape, and beyond. Many of these properties are not achievable with conventional substrates that are used for inkjet-printing conductive traces. Specifically, our technique leverages the commonly undesired low adhesion property of the inkjet printing films and repurposes these films as temporary transfer media. We describe our fabrication methods with a library of materials we can utilize, evaluate the mechanical and electrical properties of the transferred traces, and conclude with several demonstrative applications. We believe Silver Tape enriches novel interactions for the ubiquitous computing domain, by enabling digital fabrication of electronics on versatile materials, surfaces, and shapes.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3381013"}, {"primary_key": "2427282", "vector": [], "sparse_vector": [], "title": "ComFeel: Productivity is a Matter of the Senses Too.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Hongwei Li", "<PERSON><PERSON>", "<PERSON>"], "summary": "Indoor environmental quality has been found to impact employees' productivity in the long run, yet it is unclear its meeting-level impact in the short term. We studied the relationship between sensorial pleasantness of a meeting's room and the meeting's productivity. By administering a 28-item questionnaire to 363 online participants, we indeed found that three factors captured 62% of people's experience of meetings: (a) productivity; (b) psychological safety; and (c) room pleasantness. To measure room pleasantness, we developed and deployed ComFeel, an indoor environmental sensing infrastructure, which captures light, temperature, and gas resistance readings through miniaturized and unobtrusive devices we built and named 'Geckos'. Across 29 real-world meetings, using ComFeel, we collected 1373 minutes of readings. For each of these meetings, we also collected whether each participant felt the meeting to have been productive, the setting to be psychologically safe, and the meeting room to be pleasant. As one expects, we found that, on average, the probability of a meeting being productive increased by 35% for each standard deviation increase in the psychological safety participants experienced. Importantly, that probability increased by as much as 25% for each increase in room pleasantness, confirming the significant short-term impact of the indoor environment on meetings' productivity.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432234"}, {"primary_key": "2427283", "vector": [], "sparse_vector": [], "title": "FORTNIoT: Intelligible Predictions to Improve User Understanding of Smart Home Behavior.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Ubiquitous environments, such as smart homes, are becoming more intelligent and autonomous. As a result, their behavior becomes harder to grasp and unintended behavior becomes more likely. Researchers have contributed tools to better understand and validate an environments' past behavior (e.g. logs, end-user debugging), and to prevent unintended behavior. There is, however, a lack of tools that help users understand the future behavior of such an environment. Information about the actions it will perform, and why it will perform them, remains concealed. In this paper, we contribute FORTNIoT, a well-defined approach that combines self-sustaining predictions (e.g. weather forecasts) and simulations of trigger-condition-action rules to deduce when these rules will trigger in the future and what state changes they will cause to connected smart home entities. We implemented a proof-of-concept of this approach, as well as a visual demonstrator that shows such predictions, including causes and effects, in an overview of a smart home's behavior. A between-subject evaluation with 42 participants indicates that FORTNIoT predictions lead to a more accurate understanding of the future behavior, more confidence in that understanding, and more appropriate trust in what the system will (not) do. We envision a wide variety of situations where predictions about the future are beneficial to inhabitants of smart homes, such as debugging unintended behavior and managing conflicts by exception, and hope to spark a new generation of intelligible tools for ubiquitous environments.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432225"}, {"primary_key": "2427284", "vector": [], "sparse_vector": [], "title": "Quantifying the Relationships between Everyday Objects and Emotional States through Deep Learning Based Image Analysis Using Smartphones.", "authors": ["Victor<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "There has been an increasing interest in the problem of inferring emotional states of individuals using sensor and user-generated information as diverse as GPS traces, social media data and smartphone interaction patterns. One aspect that has received little attention is the use of visual context information extracted from the surroundings of individuals and how they relate to it. In this paper, we present an observational study of the relationships between the emotional states of individuals and objects present in their visual environment automatically extracted from smartphone images using deep learning techniques. We developed MyMood, a smartphone application that allows users to periodically log their emotional state together with pictures from their everyday lives, while passively gathering sensor measurements. We conducted an in-the-wild study with 22 participants and collected 3,305 mood reports with photos. Our findings show context-dependent associations between objects surrounding individuals and self-reported emotional state intensities. The applications of this work are potentially many, from the design of interior and outdoor spaces to the development of intelligent applications for positive behavioral intervention, and more generally for supporting computational psychology studies.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3380997"}, {"primary_key": "2427285", "vector": [], "sparse_vector": [], "title": "ESPRESSO: Entropy and ShaPe awaRe timE-Series SegmentatiOn for Processing Heterogeneous Sensor Data.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Extracting informative and meaningful temporal segments from high-dimensional wearable sensor data, smart devices, or IoT data is a vital preprocessing step in applications such as Human Activity Recognition (HAR), trajectory prediction, gesture recognition, and lifelogging. In this paper, we propose ESPRESSO (Entropy and ShaPe awaRe timE-Series SegmentatiOn), a hybrid segmentation model for multi-dimensional time-series that is formulated to exploit the entropy and temporal shape properties of time-series. ESPRESSO differs from existing methods that focus upon particular statistical or temporal properties of time-series exclusively. As part of model development, a novel temporal representation of time-series $WCAC$ was introduced along with a greedy search approach that estimate segments based upon the entropy metric. ESPRESSO was shown to offer superior performance to four state-of-the-art methods across seven public datasets of wearable and wear-free sensing. In addition, we undertake a deeper investigation of these datasets to understand how ESPRESSO and its constituent methods perform with respect to different dataset characteristics. Finally, we provide two interesting case-studies to show how applying ESPRESSO can assist in inferring daily activity routines and the emotional state of humans.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411832"}, {"primary_key": "2427286", "vector": [], "sparse_vector": [], "title": "The StoryTeller: Scalable Building- and AP-independent Deep Learning-based Floor Prediction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Due to the recent proliferation of location-based services indoors, the need for an accurate floor estimation technique that is easy to deploy in any typical multi-story building is higher than ever. Current approaches that attempt to solve the floor localization problem include sensor-based systems and 3D fingerprinting. Nevertheless, these systems incur high deployment and maintenance overhead, suffer from sensor drift and calibration issues, and/or are not available to all users. In this paper, we propose StoryTeller, a deep learning-based technique for floor prediction in multi-story buildings. StoryTeller leverages the ubiquitous WiFi signals to generate images that are input to a Convolutional Neural Network (CNN) which is trained to predict loors based on detected patterns in visible WiFi scans. Input images are created such that they capture the current WiFi-scan in an AP-independent manner. In addition, a novel virtual building concept is used to normalize the information in order to make them building-independent. This allows StoryTeller to reuse a trained network for a completely new building, significantly reducing the deployment overhead. We have implemented and evaluated StoryTeller using three different buildings with a side-by-side comparison with the state-of-the-art floor estimation techniques. The results show that StoryTeller can estimate the user's floor at least 98.3% within one floor of the actual ground truth floor. This accuracy is consistent across the different testbeds and for scenarios where the models used were trained in a completely different building than the tested building. This highlights StoryTeller's ability to generalize to new buildings and its promise as a scalable, low-overhead, high-accuracy floor localization system.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3380979"}, {"primary_key": "2427287", "vector": [], "sparse_vector": [], "title": "VibroMap: Understanding the Spacing of Vibrotactile Actuators across the Body.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In spite of the great potential of on-body vibrotactile displays for a variety of applications, research lacks an understanding of the spacing between vibrotactile actuators. Through two experiments, we systematically investigate vibrotactile perception on the wrist, forearm, upper arm, back, torso, thigh, and leg, each in transverse and longitudinal body orientation. In the first experiment, we address the maximum distance between vibration motors that still preserves the ability to generate phantom sensations. In the second experiment, we investigate the perceptual accuracy of localizing vibrations in order to establish the minimum distance between vibration motors. Based on the results, we derive VibroMap, a spatial map of the functional range of inter-motor distances across the body. VibroMap supports hardware and interaction designers with design guidelines for constructing body-worn vibrotactile displays.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432189"}, {"primary_key": "2427288", "vector": [], "sparse_vector": [], "title": "Mapping and Taking Stock of the Personal Informatics Literature.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Qingyang Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The research community on the study and design of systems for personal informatics has grown over the past decade. To take stock of what the topics the field has studied and methods the field has used, we map and label 523 publications from ACM's library, IEEE Xplore, and PubMed. We surface that the literature has focused on studying and designing for health and wellness domains, an emphasis on understanding and overcoming barriers to data collection and reflection, and progressively fewer contributions involving artifacts being made. Our mapping review suggests directions future research could explore, such as identifying and resolving barriers to tracking stages beyond collection and reflection, engaging more with domain experts, and further discussing the privacy and ethical concerns around tracked data.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432231"}, {"primary_key": "2427289", "vector": [], "sparse_vector": [], "title": "PrivateBus: Privacy Identification and Protection in Large-Scale Bus WiFi Systems.", "authors": ["<PERSON><PERSON><PERSON>", "Boyang Fu", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, the ubiquity of mobile devices leads to an increasing demand of public network services, e.g., WiFi hot spots. As a part of this trend, modern transportation systems are equipped with public WiFi devices to provide Internet access for passengers as people spend a large amount of time on public transportation in their daily life. However, one of the key issues in public WiFi spots is the privacy concern due to its open access nature. Existing works either studied location privacy risk in human traces or privacy leakage in private networks such as cellular networks based on the data from cellular carriers. To the best of our knowledge, none of these work has been focused on bus WiFi privacy based on large-scale real-world data. In this paper, to explore the privacy risk in bus WiFi systems, we focus on two key questions how likely bus WiFi users can be uniquely re-identified if partial usage information is leaked and how we can protect users from the leaked information. To understand the above questions, we conduct a case study in a large-scale bus WiFi system, which contains 20 million connection records and 78 million location records from 770 thousand bus WiFi users during a two-month period. Technically, we design two models for our uniqueness analyses and protection, i.e., a PB-FIND model to identify the probability a user can be uniquely re-identified from leaked information; a PB-HIDE model to protect users from potentially leaked information. Specifically, we systematically measure the user uniqueness on users' finger traces (i.e., connection URL and domain), foot traces (i.e., locations), and hybrid traces (i.e., both finger and foot traces). Our measurement results reveal (i) 97.8% users can be uniquely re-identified by 4 random domain records of their finger traces and 96.2% users can be uniquely re-identified by 5 random locations on buses; (ii) 98.1% users can be uniquely re-identified by only 2 random records if both their connection records and locations are leaked to attackers. Moreover, the evaluation results show our PB-HIDE algorithm protects more than 95% users from the potentially leaked information by inserting only 1.5% synthetic records in the original dataset to preserve their data utility.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3380990"}, {"primary_key": "2427292", "vector": [], "sparse_vector": [], "title": "EchoWhisper: Exploring an Acoustic-based Silent Speech Interface for Smartphone Users.", "authors": ["<PERSON>", "Yincheng Jin", "<PERSON><PERSON> Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "With the rapid growth of artificial intelligence and mobile computing, intelligent speech interface has recently become one of the prevalent trends and has already presented huge potentials to the public. To address the privacy leakage issue during the speech interaction or accommodate some special demands, silent speech interfaces have been proposed to enable people's communication without vocalizing their sound (e.g., lip reading, tongue tracking). However, most existing silent speech mechanisms require either background illuminations or additional wearable devices. In this study, we propose the EchoWhisper as a novel user-friendly, smartphone-based silent speech interface. The proposed technique takes advantage of the micro-Doppler effect of the acoustic wave resulting from mouth and tongue movements and assesses the acoustic features of beamformed reflected echoes captured by the dual microphones in the smartphone. Using human subjects who perform a daily conversation task with over 45 different words, our system can achieve a WER (word error rate) of 8.33%, which shows the effectiveness of inferring silent speech content. Moreover, EchoWhisper has also demonstrated its reliability and robustness to a variety of configuration settings and environmental factors, such as smartphone orientations and distances, ambient noises, body motions, and so on.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411830"}, {"primary_key": "2427293", "vector": [], "sparse_vector": [], "title": "n-Gage: Predicting in-class Emotional, Behavioural and Cognitive Engagement in the Wild.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The study of student engagement has attracted growing interests to address problems such as low academic performance, disaffection, and high dropout rates. Existing approaches to measuring student engagement typically rely on survey-based instruments. While effective, those approaches are time-consuming and labour-intensive. Meanwhile, both the response rate and quality of the survey are usually poor. As an alternative, in this paper, we investigate whether we can infer and predict engagement at multiple dimensions, just using sensors. We hypothesize that multidimensional student engagement level can be translated into physiological responses and activity changes during the class, and also be affected by the environmental changes. Therefore, we aim to explore the following questions: Can we measure the multiple dimensions of high school student's learning engagement including emotional, behavioural and cognitive engagement with sensing data in the wild? Can we derive the activity, physiological, and environmental factors contributing to the different dimensions of student learning engagement? If yes, which sensors are the most useful in differentiating each dimension of the engagement? Then, we conduct an in-situ study in a high school from 23 students and 6 teachers in 144 classes over 11 courses for 4 weeks. We present the n-Gage, a student engagement sensing system using a combination of sensors from wearables and environments to automatically detect student in-class multidimensional learning engagement. Extensive experiment results show that n-Gage can accurately predict multidimensional student engagement in real-world scenarios with an average mean absolute error (MAE) of 0.788 and root mean square error (RMSE) of 0.975 using all the sensors. We also show a set of interesting findings of how different factors (e.g., combinations of sensors, school subjects, CO2 level) affect each dimension of the student learning engagement.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411813"}, {"primary_key": "2427294", "vector": [], "sparse_vector": [], "title": "He Is Just Like Me: A Study of the Long-Term Use of Smart Speakers by Parents and Children.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Over the past few years, the technological vision of the HCI and UbiComp communities regarding conversational devices has become manifest in the form of smart speakers such as Google Home and Amazon Echo. Even though millions of households have adopted and integrated these devices into their daily lives, we lack a deep understanding of how different members of a household use such devices. To this end, we conducted interviews with 18 families and collected their Google Home Activity logs to understand the usage patterns of adults and children. Our findings reveal that there are substantial differences in the ways smart speakers are used by adults and children in families over an extended period of time. We report on how parents influence children's use and how different users perceive the devices. Finally, we discuss the implications of our findings and provide guidelines for improving the design of future smart speakers and conversational agents.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3381002"}, {"primary_key": "2427295", "vector": [], "sparse_vector": [], "title": "Detection of Artifacts in Ambulatory Electrodermal Activity Data.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Vedant <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent wearable devices enable continuous and unobtrusive monitoring of human's physiological parameters, like e.g., electrodermal activity and heart rate, over long periods of time in everyday life settings. Continuous monitoring of these parameters enables the creation of systems able to predict affective states and stress with the goal of providing feedback to improve them. Deployment of such systems in everyday life settings is still complex and prone to errors due to the low quality of the collected data impacted by the presence of artifacts. In this paper we present an automatic approach to detect artifacts in electrodermal activity (EDA) signals collected in-the-wild over long periods of time. To this end we first perform a systematic literature review and compile a set of guidelines for human annotators to label artifacts manually and we use these labels as ground-truth to test our automatic approach. To evaluate our approach, we collect physiological data from 13 participants in-the-wild and two human annotators label 107.56 hours of this data set. We make the data set publicly available to other researchers upon request. Our model achieves a recall of 98% for clean and shape artifacts classification on data collected in-the-wild using leave-one-subject-out cross-validation, which is 42 percentage points higher than the baseline. We show that state of the art approaches do not generalize well when tested with completely in-the-wild data and identify only 17% of the artifacts present in our data set, even after manual adaption. We further test the robustness of our approach over time using leave-one-day-out and achieve very similar performance. We then introduce a new metric to evaluate the quality of EDA segments that considers the impact of not only artifacts in the shape of EDA but also artifacts generated by environmental temperature changes or user's high intensity movement. Our results imply that we can eliminate the need for human annotators or significantly reduce the time they need to label data. Also, our approach can be used in an online manner to automatically detect artifacts in EDA signals.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397316"}, {"primary_key": "2427296", "vector": [], "sparse_vector": [], "title": "Acoustic Strength-based Motion Tracking.", "authors": ["Linfei Ge", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Accurate device motion tracking enables many applications like Virtual Reality (VR) and Augmented Reality (AR). To make these applications available in people's daily life, low-cost acoustic-based motion tracking methods are proposed. However, existing acoustic-based methods are all based on distance estimation. These methods measure the distance between a speaker and a microphone. With a speaker or microphone array, it can get multiple estimated distances and further achieve multidimensional motion tracking. The weakness of distance-based motion tracking methods is that they need large array size to get accurate results. Some systems even require an array larger than 1 m. This weakness limits the adoption of existing solutions in a single device like a smart speaker. To solve this problem, we propose Acoustic Strength-based Angle Tracking (ASAT) System and further implement a motion tracking system based on ASAT. ASAT achieves angle tracking by creating a periodically changing sound field. A device with a microphone will sense the periodically changing sound strength in the sound field. When the device moves, the period of received sound strength will change. Thus we can derive the angle change and achieve angle tracking. The ASAT-based system can obtain the localization accuracy as 5 cm when the distance between the speaker and the microphone is in the range of 3 m.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432215"}, {"primary_key": "2427297", "vector": [], "sparse_vector": [], "title": "QwertyRing: Text Entry on Physical Surfaces Using a Ring.", "authors": ["Yizheng Gu", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The software keyboard is widely used on digital devices such as smartphones, computers, and tablets. The software keyboard operates via touch, which is efficient, convenient, and familiar to users. However, some emerging technology devices such as AR/VR headsets and smart TVs do not support touch-based text entry. In this paper, we present QwertyRing, a technique that supports text entry on physical surfaces using an IMU (Inertial Measurement Unit) ring. Users wear the ring on the middle phalanx of the index finger and type on any desk-like surface, as if there is a QWERTY keyboard on the surface. While typing, users do not focus on monitoring the hand motions. They receive text feedback on a separate screen, e.g., an AR/VR headset or a digital device display, such as a computer monitor. The basic idea of QwertyRing is to detect touch events and predict users' desired words by the orientation of the IMU ring. We evaluate the performance of QwertyRing through a five-day user study. Participants achieved a speed of 13.74 WPM in the first 40 minutes and reached 20.59 WPM at the end. The speed outperforms other ring-based techniques [24, 30, 45, 68] and is 86.48% of the speed of typing on a smartphone with an index finger. The results show that QwertyRing enables efficient touch-based text entry on physical surfaces.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432204"}, {"primary_key": "2427300", "vector": [], "sparse_vector": [], "title": "Authenticating Drivers Using Automotive Batteries.", "authors": ["<PERSON>", "Yuanchao Shu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Automakers have been improving, or even trying to replace, key-based driver authentication solutions, owing to their vulnerability to cyber attacks and single-point-of-failures, as well as their inability of driver identification. In line with this effort, we design a novel driver authentication system using automotive batteries, called Batteries-as-Authenticators (BAuth), to mitigate the limitations of key-based solutions by providing a second-factor authentication. BAuth is an add-on module installed between vehicles and their batteries, which uses the batteries as sensors to validate drivers' identities and actuators to enable/disable the cranking of vehicle's engine. We have prototyped and evaluated BAuth on 6 regular/hybrid/electric vehicles. Our evaluation shows BAuth to authenticate the drivers with a 98.17 (2.84)% averaged true (false) positive rates and tolerate the dynamics caused by the aging/temperature/state-of-charge of batteries. Our user study corroborates BAuth's attractiveness to car owners.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432198"}, {"primary_key": "2427301", "vector": [], "sparse_vector": [], "title": "Deriving Effective Human Activity Recognition Systems through Objective Task Complexity Assessment.", "authors": ["<PERSON><PERSON><PERSON><PERSON> <PERSON>", "<PERSON>"], "summary": "Research in sensor based human activity recognition (HAR) has been a core concern of the mobile and ubiquitous computing community. Sophisticated systems have been developed with the main view on applications of HAR methods in research settings. This work addresses a related yet practically different problem that mainly focuses on users of HAR technology. We acknowledge that practitioners from outside the core HAR research community are motivated to employ HAR methods for practical deployments. Even though standard processing approaches exist, arguably, often times substantial modifications are necessary to derive effective analysis systems. It is not always clear a-priori how challenging a HAR task actually is and what dimensions of an analysis pipeline are crucial for successful automated assessments. In practice this can lead to disappointing results or disproportionate efforts that have to be invested into the optimization of data analysis pipelines, that were supposed to work \"out of the box\". We present a framework for the objective complexity assessment of HAR tasks that directly supports practitioners' decision making of whether and how to employ HAR for their deployments. We map a HAR task onto a vectorial representation that allows us to analyse the inherent challenges of the task and to draw conclusions through similarity analysis with regards to existing tasks. We validate our complexity assessment framework on 23 HAR datasets and derive a data-driven categorization of human activity recognition. We demonstrate how our objective analysis can be used to inform the deployment of HAR systems in practical scenarios.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432227"}, {"primary_key": "2427302", "vector": [], "sparse_vector": [], "title": "FluSense: A Contactless Syndromic Surveillance Platform for Influenza-Like Illness in Hospital Waiting Areas.", "authors": ["Forsad <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We developed a contactless syndromic surveillance platform FluSense that aims to expand the current paradigm of influenza-like illness (ILI) surveillance by capturing crowd-level bio-clinical signals directly related to physical symptoms of ILI from hospital waiting areas in an unobtrusive and privacy-sensitive manner. FluSense consists of a novel edge-computing sensor system, models and data processing pipelines to track crowd behaviors and influenza-related indicators, such as coughs, and to predict daily ILI and laboratory-confirmed influenza caseloads. FluSense uses a microphone array and a thermal camera along with a neural computing engine to passively and continuously characterize speech and cough sounds along with changes in crowd density on the edge in a real-time manner. We conducted an IRB-approved 7 month-long study from December 10, 2018 to July 12, 2019 where we deployed FluSense in four public waiting areas within the hospital of a large university. During this period, the FluSense platform collected and analyzed more than 350,000 waiting room thermal images and 21 million non-speech audio samples from the hospital waiting areas. FluSense can accurately predict daily patient counts with a Pearson correlation coefficient of 0.95. We also compared signals from FluSense with the gold standard laboratory-confirmed influenza case data obtained in the same facility and found that our sensor-based features are strongly correlated with laboratory-confirmed influenza trends.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3381014"}, {"primary_key": "2427303", "vector": [], "sparse_vector": [], "title": "ShadowSense: Detecting Human Touch in a Social Robot Using Shadow Image Classification.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper proposes and evaluates the use of image classification for detailed, full-body human-robot tactile interaction. A camera positioned below a translucent robot skin captures shadows generated from human touch and infers social gestures from the captured images. This approach enables rich tactile interaction with robots without the need for the sensor arrays used in traditional social robot tactile skins. It also supports the use of touch interaction with non-rigid robots, achieves high-resolution sensing for robots with different sizes and shape of surfaces, and removes the requirement of direct contact with the robot. We demonstrate the idea with an inflatable robot and a standing-alone testing device, an algorithm for recognizing touch gestures from shadows that uses Densely Connected Convolutional Networks, and an algorithm for tracking positions of touch and hovering shadows. Our experiments show that the system can distinguish between six touch gestures under three lighting conditions with 87.5 - 96.0% accuracy, depending on the lighting, and can accurately track touch positions as well as infer motion activities in realistic interaction conditions. Additional applications for this method include interactive screens on inflatable robots and privacy-maintaining robots for the home.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432202"}, {"primary_key": "2427304", "vector": [], "sparse_vector": [], "title": "FingerTrak: Continuous 3D Hand Pose Tracking by Deep Learning Hand Silhouettes Captured by Miniature Thermal Cameras on Wrist.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Xu", "<PERSON>", "<PERSON>"], "summary": "In this paper, we present FingerTrak, a minimal-obtrusive wristband that enables continuous 3D finger tracking and hand pose estimation with four miniature thermal cameras mounted closely on a form-fitting wristband. FingerTrak explores the feasibility of continuously reconstructing the entire hand postures (20 finger joints positions) without the needs of seeing all fingers. We demonstrate that our system is able to estimate the entire hand posture by observing only the outline of the hand, i.e., hand silhouettes from the wrist using low-resolution (32 x 24) thermal cameras. A customized deep neural network is developed to learn to \"stitch\" these multi-view images and estimate 20 joints positions in 3D space. Our user study with 11 participants shows that the system can achieve an average angular error of 6.46° when tested under the same background, and 8.06° when tested under a different background. FingerTrak also shows encouraging results with the re-mounting of the device and has the potential to reconstruct some of the complicated poses. We conclude this paper with further discussions of the opportunities and challenges of this technology.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397306"}, {"primary_key": "2427305", "vector": [], "sparse_vector": [], "title": "IoT Inspector: Crowdsourcing Labeled Network Traffic from Smart Home Devices at Scale.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The proliferation of smart home devices has created new opportunities for empirical research in ubiquitous computing, ranging from security and privacy to personal health. Yet, data from smart home deployments are hard to come by, and existing empirical studies of smart home devices typically involve only a small number of devices in lab settings. To contribute to data-driven smart home research, we crowdsource the largest known dataset of labeled network traffic from smart home devices from within real-world home networks. To do so, we developed and released IoT Inspector, an open-source tool that allows users to observe the traffic from smart home devices on their own home networks. Since April 2019, 4,322 users have installed IoT Inspector, allowing us to collect labeled network traffic from 44,956 smart home devices across 13 categories and 53 vendors. We demonstrate how this data enables new research into smart homes through two case studies focused on security and privacy. First, we find that many device vendors use outdated TLS versions and advertise weak ciphers. Second, we discover about 350 distinct third-party advertiser and tracking domains on smart TVs. We also highlight other research areas, such as network management and healthcare, that can take advantage of IoT Inspector's dataset. To facilitate future reproducible research in smart homes, we will release the IoT Inspector data to the public.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397333"}, {"primary_key": "2427306", "vector": [], "sparse_vector": [], "title": "Soil-Monitoring Sensor Powered by Temperature Difference between Air and Shallow Underground Soil.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Energy harvesting (EH) technologies are useful for the semi-permanent operation of wireless sensor networks, especially, for agricultural monitoring as the networks need to be installed in large areas where power supply is unavailable. In this paper, we propose a battery-free soil-monitoring sensor for agriculture, which leverages the temperature difference between near-surface air and shallow underground soil using a thermoelectric generator (TEG). The performance of systems driven by the TEG mainly depends on the average temperature between the hot and cold sides of the TEG (T) and the temperature difference across the TEG (ΔT). If T is low and ΔT is small, it is challenging to earn enough power to drive wireless microcontroller unit; however, with our dedicated electric circuit, and thermal designs including impedance matching of thermal circuit and suppression of heat loss, the sensor can harvest more than a hundred microwatt on average from the temperature difference between the air and underground soil at a depth of 30 cm. The performance of the energy harvester is evaluated both by numerical analysis using temperature data collected from various farm fields and by a prototype implementation. Moreover, the prototype was deployed to farm fields in Japan and India. Our field experiment results revealed that the prototype could harvest 100 μW-370 μW on average, and drive a wireless microcontroller unit to perform soil monitoring.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3380995"}, {"primary_key": "2427309", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON><PERSON>: Time-Sensitive On-Device Deep Inference and Adaptation on Intermittently-Powered Systems.", "authors": ["Bashima Islam", "<PERSON><PERSON><PERSON>"], "summary": "We propose Zygarde --- which is an energy- and accuracy-aware soft real-time task scheduling framework for batteryless systems that flexibly execute deep learning tasks1 that are suitable for running on microcontrollers. The sporadic nature of harvested energy, resource constraints of the embedded platform, and the computational demand of deep neural networks (DNNs) pose a unique and challenging real-time scheduling problem for which no solutions have been proposed in the literature. We empirically study the problem and model the energy harvesting pattern as well as the trade-off between the accuracy and execution of a DNN. We develop an imprecise computing-based scheduling algorithm that improves the timeliness of DNN tasks on intermittently powered systems. We evaluate Zygarde using four standard datasets as well as by deploying it in six real-life applications involving audio and camera sensor systems. Results show that Zygarde decreases the execution time by up to 26% and schedules 9% -- 34% more tasks with up to 21% higher inference accuracy, compared to traditional schedulers such as the earliest deadline first (EDF).", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411808"}, {"primary_key": "2427311", "vector": [], "sparse_vector": [], "title": "InSight: Monitoring the State of the Driver in Low-Light Using Smartphones.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Venkat <PERSON>"], "summary": "Road safety is a major public health issue across the globe and over two-thirds of the road accidents occur at nighttime under low-light conditions or darkness. The state of the driver and her/his actions are the key factors impacting road safety. How can we monitor these in a cost-effective manner and in low-light conditions? RGB cameras present in smartphones perform poorly in low-lighting conditions due to lack of information captured. Hence, existing monitoring solutions rely upon specialized hardware such as infrared cameras or thermal cameras in low-light conditions, but are limited to only high-end vehicles owing to the cost of the hardware. We present InSight, a windshield-mounted smartphone-based system that can be retrofitted to the vehicle to monitor the state of the driver, specifically driver fatigue (based on frequent yawning and eye closure) and driver distraction (based on their direction of gaze). Challenges arise from designing an accurate, yet low-cost and non-intrusive system to continuously monitor the state of the driver. In this paper, we present two novel and practical approaches for continuous driver monitoring in low-light conditions: (i) Image synthesis: enabling monitoring in low-light conditions using just the smartphone RGB camera by synthesizing a thermal image from RGB with a Generative Adversarial Network, and (ii) Near-IR LED: using a low-cost near-IR (NIR) LED attachment to the smartphone, where the NIR LED acts as a light source to illuminate the driver's face, which is not visible to the human eyes, but can be captured by standard smartphone cameras without any specialized hardware. We show that the proposed techniques can capture the driver's face accurately in low-lighting conditions to monitor driver's state. Further, since NIR and thermal imagery is significantly different than RGB images, we present a systematic approach to generate labelled data, which is used to train existing computer vision models. We present an extensive evaluation of both the approaches with data collected from 15 drivers in controlled basement area and on real roads in low-light conditions. The proposed NIR LED setup has an accuracy (Fl-score) of 85% and 93.8% in detecting driver fatigue and distraction, respectively in low-light.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411819"}, {"primary_key": "2427312", "vector": [], "sparse_vector": [], "title": "SmileAuth: Using Dental Edge Biometrics for User Authentication on Smartphones.", "authors": ["Hongbo Jiang", "Hangcheng Cao", "Dai<PERSON> Liu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "User authentication is crucial for security and privacy protection on smartphones. While a variety of authentication schemes are available on smartphones, security flaws have been continuously discovered. Fingerprint films can deceive fingerprint sensors and anti-surveillance prosthetic masks can spoof face recognition. In this paper, we propose a novel user authentication system SmileAuth that leverages the unique features of people's dental edge biometrics for reliable and convenient user authentication. SmileAuth extracts a series of dental edge features by slightly moving the smartphone to capture a few images from different camera angles. These unique features are determined by the tooth size, shape, position and surface abrasion. SmileAuth is robust against image spoofing, video-based attack, physically forced attack and denture attack. We implemented the prototype of SmileAuth on Android smartphones and comprehensively evaluated its performance by recruiting more than 300 volunteers. Experimental results show that SmileAuth can achieve an overall 99.74% precision, 98.69% F-score, 2.31% FNR and 0.25% FPR in diverse scenarios. Additional experiments with two pairs of twins demonstrate that dental edge biometrics are unique enough to effectively distinguish twins.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411806"}, {"primary_key": "2427313", "vector": [], "sparse_vector": [], "title": "Acoussist: An Acoustic Assisting Tool for People with Visual Impairments to Cross Uncontrolled Streets.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "To cross uncontrolled roadways, where no traffic-halting signal devices are present, pedestrians with visual impairments must rely on their other senses to detect oncoming vehicles and estimate the correct crossing interval in order to avoid potentially fatal collisions. To overcome the limitations of human auditory performance, which can be particularly impacted by weather or background noise, we develop an assisting tool called Acoussist, which relies on acoustic ranging to provide an additional layer of protection for pedestrian safety. The vision impaired can use the tool to double-confirm surrounding traffic conditions before they proceed through a non-signaled crosswalk. The Acoussist tool is composed of vehicle-mounted external speakers that emit acoustic chirps at a frequency range imperceptible by human ears, but detectable by smartphones operating the Acoussist app. This app would then communicate to the user when it is safe to cross the roadway. Several challenges exist when applying the acoustic ranging to traffic detection, including measuring multiple vehicles' instant velocities and directions with the presence many of them who emit homogeneous signals simultaneously. We address these challenges by leveraging insights from formal analysis on received signals' time-frequency (t-f) profiles. We implement a proof-of-concept of Acoussist using commercial off-the-shelf (COTS) portable speakers and smartphones. Extensive in-field experiments have been conducted to validate the effectiveness of <PERSON><PERSON>ussist in improving mobility for people with visual impairments.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432216"}, {"primary_key": "2427316", "vector": [], "sparse_vector": [], "title": "Relacks: Reliable Backscatter Communication in Indoor Environments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The increasing number of embedded, plugged-in radios around homes and offices in everyday objects such as appliances, TVs, and smart speakers provides an excellent opportunity to bring ultra-low-power backscatter connectivity to billions of devices. However, backscatter links suffer from high loss due to two consecutive propagations and have to operate on narrow link-budget margins, which makes them more susceptible to multipath losses in indoor environments. To address this, we propose a closed-loop backscatter system that exploits diversity sources such as communication frequency and transceivers antennas based on the channel metrics to deliver reliable coverage over an entire area. We prototype a backscatter system with Bluetooth Low Energy (BLE) transceivers and BLE compatible tags and deploy it in several multipath rich indoor environments. Our evaluations show that we can successfully communicate with a backscatter tag in a 50m2 indoor area. The proposed algorithm for selecting communication parameters achieves an average 2.7 x success rate compared to the random selection while satisfying FCC output power requirements for frequency hopping transceivers.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397314"}, {"primary_key": "2427317", "vector": [], "sparse_vector": [], "title": "Guiding Blind Pedestrians in Public Spaces by Understanding Walking Behavior of Nearby Pedestrians.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a guiding system to help blind people walk in public spaces while making their walking seamless with nearby pedestrians. Blind users carry a rolling suitcase-shaped system that has two RGBD Cameras, an inertial measurement unit (IMU) sensor, and light detection and ranging (LiDAR) sensor. The system senses the behavior of surrounding pedestrians, predicts risks of collisions, and alerts users to help them avoid collisions. It has two modes: the \"on-path\" mode that helps users avoid collisions without changing their path by adapting their walking speed; and the \"off-path\" mode that navigates an alternative path to go around pedestrians standing in the way Auditory and tactile modalities have been commonly used for non-visual navigation systems, so we implemented two interfaces to evaluate the effectiveness of each modality for collision avoidance. A user study with 14 blind participants in public spaces revealed that participants could successfully avoid collisions with both modalities. We detail the characteristics of each modality.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411825"}, {"primary_key": "2427318", "vector": [], "sparse_vector": [], "title": "Understanding User Contexts and Coping Strategies for Context-aware Phone Distraction Management System Design.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Nematjon Narziev", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Smartphones are often distraction for everyday life activities. In this work, we envision designing a context-aware system that helps users better manage smartphone distractions. This system design requires us to have an in-depth understanding of users' contexts of smartphone distractions and their coping strategies. However, there is a lack of knowledge regarding the contexts in which users perceive that smartphones are distracting in their everyday lives. Furthermore, prior studies did not systematically examine users' preferred coping strategies for handling interruptions caused by smartphones, possibly supported by context-aware systems that proactively manage smartphone distraction. To bridge this gap, we collect in-situ user contexts and their corresponding levels of perceived smartphone distraction as well as analyze the daily contexts in which users perceive smartphones as distracting. Moreover, we also explore how users want to manage phone distraction by asking them to write simple if-then rules. Our results on user contexts and coping strategies provide important implications for designing and implementing context-aware distraction management systems.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432213"}, {"primary_key": "2427320", "vector": [], "sparse_vector": [], "title": "Interruptibility for In-vehicle Multitasking: Influence of Voice Task Demands and Adaptive Behaviors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As a countermeasure to visual-manual distractions, auditory-verbal (voice) interfaces are becoming increasingly popular for in-vehicle systems. This opens up new opportunities for drivers to receive proactive personalized services from various service domains. However, prior studies warned that such interactions can cause cognitive distractions due to the nature of concurrent multitasking with a limited amount of cognitive resources. In this study, we examined (1) how the varying demands of proactive voice tasks under diverse driving situations impact driver interruptibility, and (2) how drivers adapt their concurrent multitasking of driving and proactive voice tasks, and how the adaptive behaviors are related to driver interruptibility. Our quantitative and qualitative analyses showed that in addition to the driving-task demand, the voice-task demand and adaptive behaviors are also significantly related to driver interruptibility. Additionally, we discuss how our findings can be used to design and realize three types of flow-control mechanisms for voice interactions that can improve driver interruptibility.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3381009"}, {"primary_key": "2427321", "vector": [], "sparse_vector": [], "title": "BFree: Enabling Battery-free Sensor Prototyping with Python.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Building and programming tiny battery-free energy harvesting embedded computer systems is hard for the average maker because of the lack of tools, hard to comprehend programming models, and frequent power failures. With the high ecologic cost of equipping the next trillion embedded devices with batteries, it is critical to equip the makers, hobbyists, and novice embedded systems programmers with easy-to-use tools supporting battery-free energy harvesting application development. This way, makers can create untethered embedded systems that are not plugged into the wall, the desktop, or even a battery, providing numerous new applications and allowing for a more sustainable vision of ubiquitous computing. In this paper, we present BFree, a system that makes it possible for makers, hobbyists, and novice embedded programmers to develop battery-free applications using Python programming language and widely available hobbyist maker platforms. BFree provides energy harvesting hardware and a power failure resilient version of Python, with durable libraries that enable common coding practice and off the shelf sensors. We develop demonstration applications, benchmark BFree against battery-powered approaches, and evaluate our system in a user study. This work enables makers to engage with a future of ubiquitous computing that is useful, long-term, and environmentally responsible.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432191"}, {"primary_key": "2427322", "vector": [], "sparse_vector": [], "title": "Not Quite Yourself Today: Behaviour-Based Continuous Authentication in IoT Environments.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The shortcomings of the traditional password-based authentication mechanism are becoming increasingly apparent as we transition from \"one user - one device\" to a richer \"multiple users - multiple devices\" computing paradigm. The currently dominant research direction focuses on on-device biometrics, which require sensitive information, such as images of a user's face, to be constantly streamed from a single recording source, often the device on which a user is getting authenticated. Instead, in this work we explore the possibilities offered by heterogeneous devices that opportunistically collect non-sensitive data in smart environments. We construct an IoT testbed in which we gather data pertaining to a person's movement in space, interaction with certain physical objects, PC terminal usage, and keyboard typing, and construct machine learning models capturing the person's behaviour traits. We commence our examination with models constructed from data sensed during a previously-completed task run and with such models we achieve up to 68% user identification accuracy (c.f. 7% baseline) among up to 20 individuals. Taking into account the limits of behaviour persistence we then revise our approach to continuously refine the model with the most recently sampled sensor data. This method allows us to achieve 99.3% user verification accuracy and successfully prevent a session takeover attack within 12 seconds with less than 1% of false attack detection.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432206"}, {"primary_key": "2427324", "vector": [], "sparse_vector": [], "title": "IMUTube: Automatic Extraction of Virtual on-body Accelerometry from Video for Human Activity Recognition.", "authors": ["HyeokHyen <PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The lack of large-scale, labeled data sets impedes progress in developing robust and generalized predictive models for on-body sensor-based human activity recognition (HAR). Labeled data in human activity recognition is scarce and hard to come by, as sensor data collection is expensive, and the annotation is time-consuming and error-prone. To address this problem, we introduce IMUTube, an automated processing pipeline that integrates existing computer vision and signal processing techniques to convert videos of human activity into virtual streams of IMU data. These virtual IMU streams represent accelerometry at a wide variety of locations on the human body. We show how the virtually-generated IMU data improves the performance of a variety of models on known HAR datasets. Our initial results are very promising, but the greater promise of this work lies in a collective approach by the computer vision, signal processing, and activity recognition communities to extend this work in ways that we outline. This should lead to on-body, sensor-based HAR becoming yet another success story in large-dataset breakthroughs in recognition.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411841"}, {"primary_key": "2427325", "vector": [], "sparse_vector": [], "title": "A Multi-Sensor Approach to Automatically Recognize Breaks and Work Activities of Knowledge Workers in Academia.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Personal informatics systems for the work environment can help improving workers' well-being and productivity. Using both self-reported data logged manually by the users and information automatically inferred from sensor measurements, such systems may track users' activities at work and help them reflect on their work habits through insightful data visualizations. They can further support interventions like, e.g., blocking distractions during work activities or suggest the user to take a break. The ability to automatically recognize when the user is engaged in a work activity or taking a break is thus a fundamental primitive such systems need to implement. In this paper, we explore the use of data collected from personal devices -- smartwatches, laptops, and smartphones -- to automatically recognize when users are working or taking breaks. We collect a data set of of continuous streams of sensor data captured from personal devices along with labels indicating whether a user is working or taking a break. We use multiple instruments to facilitate the collection of users' self-reported labels and discuss our experience with this approach. We analyse the available data -- 449 labelled activities of nine knowledge workers collected during a typical work week -- using machine learning techniques and show that user-independent models can achieve a (F1 score) of 94% for the identification of work activities and of 69% for breaks, outperforming baseline methods by 5-10 and 12-54 percentage points, respectively.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411821"}, {"primary_key": "2427326", "vector": [], "sparse_vector": [], "title": "What If Conversational Agents Became Invisible?: Comparing Users&apos; Mental Models According to Physical Entity of AI Speaker.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The popularity of conversational agents (CAs) in the form of AI speakers that support ubiquitous smart homes has increased because of their seamless interaction. However, recent studies have revealed that the use of AI speakers decreases over time, which shows that current agents do not fully support smart homes. Because of this problem, the possibility of unobtrusive, invisible intelligence without a physical device has been suggested. To explore CA design direction that enhances the user experience in smart homes, we aimed to understand each feature by comparing an invisible agent with visible ones embedded in stand-alone AI speakers. We conducted a drawing study to examine users' mental models formed through communicating with two different physical entities (i.e., visible and invisible CAs). From the drawings, interviews, and surveys, we identified how users' mental models and interactions differed depending on the presence of a physical entity. We found that a physical entity affected users' perceptions, expectations, and interactions toward the agent.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411840"}, {"primary_key": "2427327", "vector": [], "sparse_vector": [], "title": "Using Sonar for Liveness Detection to Protect Smart Speakers against Remote Attackers.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Smart speakers, which wait for voice commands and complete tasks for users, are becoming part of common households. While voice commands came with basic functionalities in the earlier days, as the market grew, various commands with critical functionalities were developed; e.g., access banking services, send money, open front door. Such voice commands can cause serious consequences once smart speakers are attacked. Recent research shows that smart speakers are vulnerable to malicious voice commands sent from other speakers (e.g., TV, baby monitor, radio) in the same area. In this work, we propose the Speaker-Sonar, a sonar-based liveness detection system for smart speakers. Our approach aims to protect the smart speakers from remote attackers that leverage network-connected speakers to send malicious commands. The key idea of our approach is to make sure that the voice command is indeed coming from the user. For this purpose, the Speaker-Sonar emits an inaudible sound and tracks the user's direction to compare it with the direction of the received voice command. The Speaker-Sonar does not require additional action from the user and works through an automatic consistency check. We built the Speaker-Sonar on a raspberry pi 3b, a circular microphone array, and a commodity speaker by imitating the Amazon Echo. Our evaluation shows that the Speaker-Sonar can reject remote voice attacks with an average accuracy of 95.5% in 2 meters, which significantly raises the bar for remote attackers. To the best of our knowledge, our defense is able to defend against known remote voice attack techniques.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3380991"}, {"primary_key": "2427328", "vector": [], "sparse_vector": [], "title": "WiBorder: Precise Wi-Fi based Boundary Sensing via Through-wall Discrimination.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Qin Lv", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "Recent research has shown great potential of exploiting Channel State Information (CSI) retrieved from commodity Wi-Fi devices for contactless human sensing in smart homes. Despite much work on Wi-Fi based indoor localization and motion/intrusion detection, no prior solution is capable of detecting a person entering a room with a precise sensing boundary, making room-based services infeasible in the real world. In this paper, we present WiBorder, an innovative technique for accurate determination of Wi-Fi sensing boundary. The key idea is to harness antenna diversity to effectively eliminate random phase shifts while amplifying through-wall amplitude attenuation. By designing a novel sensing metric and correlating it with human's through-wall discrimination, WiBorder is able to precisely determine Wi-Fi sensing boundaries by leveraging walls in our daily environments. To demonstrate the effectiveness of WiBorder, we have developed an intrusion detection system and an area detection system. Extensive results in real-life scenarios show that our intrusion detection system achieves a high detection rate of 99.4% and a low false alarm rate of 0.68%, and the area detection system's accuracy can be as high as 97.03%. To the best of our knowledge, WiBorder is the first work that enables precise sensing boundary determination via through-wall discrimination, which can immediately benefit other Wi-Fi based applications.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411834"}, {"primary_key": "2427331", "vector": [], "sparse_vector": [], "title": "Personalized HeartSteps: A Reinforcement Learning Algorithm for Optimizing Physical Activity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Predrag <PERSON>", "<PERSON>"], "summary": "With the recent proliferation of mobile health technologies, health scientists are increasingly interested in developing just-in-time adaptive interventions (JITAIs), typically delivered via notifications on mobile devices and designed to help users prevent negative health outcomes and to promote the adoption and maintenance of healthy behaviors. A JITAI involves a sequence of decision rules (i.e., treatment policies) that take the user's current context as input and specify whether and what type of intervention should be provided at the moment. In this work, we describe a reinforcement learning (RL) algorithm that continuously learns and improves the treatment policy embedded in the JITAI as data is being collected from the user. This work is motivated by our collaboration on designing an RL algorithm for HeartSteps V2 based on data collected HeartSteps V1. HeartSteps is a physical activity mobile health application. The RL algorithm developed in this work is being used in HeartSteps V2 to decide, five times per day, whether to deliver a context-tailored activity suggestion.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3381007"}, {"primary_key": "2427333", "vector": [], "sparse_vector": [], "title": "KEHKey: Kinetic Energy Harvester-based Authentication and Key Generation for Body Area Network.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "For kinetic-powered body area networks, we explore the feasibility of converting energy harvesting patterns for device authentication and symmetric secret keys generation continuously. The intuition is that at any given time, multiple wearable devices harvest kinetic energy from the same user activity, such as walking, which allows them to independently observe a common secret energy harvesting pattern not accessible to outside devices. Such continuous KEH-based authentication and key generation is expected to be highly power efficient as it obviates the need to employ any extra sensors, such as accelerometer, to precisely track the walking patterns. Unfortunately, lack of precise activity tracking introduces bit mismatches between the independently generated keys, which makes KEH-based authentication and symmetric key generation a challenging problem. We propose KEHKey, a KEH-based authentication and key generation system that employs a compressive sensing-based information reconciliation protocol for wearable devices to effectively correct any mismatches in generated keys. We implement KEHKey using off-the-shelf piezoelectric energy harvesting products and evaluate its performance with data collected from 24 subjects wearing the devices on different body locations including head, torso and hands. Our results show that KEHKey is able to generate the same key for two KEH-embedded devices at a speed of 12.57 bps while reducing energy consumption by 59% compared to accelerometer-based methods, which makes it suitable for continuous operation. Finally, we demonstrate that KEHKey can successfully defend against typical adversarial attacks. In particular, KEHKey is found to be more resilient to video side channel attacks than its accelerometer-based counterparts.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3381754"}, {"primary_key": "2427334", "vector": [], "sparse_vector": [], "title": "Keep the Phone in Your Pocket: Enabling Smartphone Operation with an IMU Ring for Visually Impaired People.", "authors": ["<PERSON><PERSON><PERSON>", "Yizheng Gu", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous studies have shown that visually impaired users face a unique set of pain points in smartphone interaction including locating and removing the phone from a pocket, two-handed interaction while holding a cane, and keeping personal data private in a public setting. In this paper, we present a ring-based input interaction that enables in-pocket smartphone operation. By wearing a ring with an Inertial Measurement Unit on the index finger, users can perform gestures on any surface (e.g., tables, thighs) using subtle, one-handed gestures and receive auditory feedback via earphones. We conducted participatory studies to obtain a set of versatile commands and corresponding gestures. We subsequently trained an SVM model to recognize these gestures and achieved a mean accuracy of 95.5% on 15 classifications. Evaluation results showed that our ring interaction is more efficient than some baseline phone interactions and is easy, private, and fun to use.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397308"}, {"primary_key": "2427337", "vector": [], "sparse_vector": [], "title": "PMC: A Privacy-preserving Deep Learning Model Customization Framework for Edge Computing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Deep learning models have been deployed to a wide range of edge devices. Since the data distribution on edge devices may differ from the cloud where the model was trained, it is typically desirable to customize the model for each edge device to improve accuracy. However, such customization is hard because collecting data from edge devices is usually prohibited due to privacy concerns. In this paper, we propose PMC, a privacy-preserving model customization framework to effectively customize a CNN model from the cloud to edge devices without collecting raw data. Instead, we introduce a method to extract statistical information from the edge, which contains adequate domain-related knowledge for model customization. PMC uses Gaussian distribution parameters to describe the edge data distribution, reweights the cloud data based on the parameters, and uses the reweighted data to train a specialized model for the edge device. During this process, differential privacy can be enforced by adding computed noises to the Gaussian parameters. Experiments on public datasets show that PMC can improve model accuracy by a large margin through customization. Finally, a study on user-generated data demonstrates the effectiveness of PMC in real-world settings.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432208"}, {"primary_key": "2427338", "vector": [], "sparse_vector": [], "title": "Real-time Arm Gesture Recognition in Smart Home Scenarios via Millimeter Wave Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Huadong Ma"], "summary": "\"In air\" gesture recognition using millimeter wave (mmWave) radar and its applications in natural human-computer-interaction for smart home has shown its potential. However, the state-of-the-art works still fall short in terms of limited gesture space, vulnerable to surrounding interference, and off-line recognition. In this paper, we propose mHomeGes, a real-time mmWave arm gesture recognition system for practical smart home-usage. To this end, we first distill arm gesture's position and dynamic variation, and then custom-design a lightweight convolution neural network to recognize fine-grained gestures. Next, we propose a user discovery method to focus on the target human gesture, thus eliminating the adverse impact of surrounding interference. Finally, we design a hidden Markov model-based voting mechanism to handle continuous gesture signals at run-time, which leads to continuous gesture recognition in real-time. We implement mHomeGes on a commodity mmWave radar and also perform a user study, which demonstrates that mHomeGes achieves high recognition accuracy above 95.30% in real-time across various smart home scenarios, regardless of the impact of surrounding movements, concurrent gestures, human physiological conditions, and outer packing materials. Moreover, we have also publicly archived a mmWave gesture data-set collected during developing mHomeGes, which consists of about 22,000 instances from 25 persons and may have an independent value of facilitating future research.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432235"}, {"primary_key": "2427339", "vector": [], "sparse_vector": [], "title": "Handling Missing Sensors in Topology-Aware IoT Applications with Gated Graph Neural Network.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Reliable data collection, transmission, and delivery on Internet of Things (IoT) systems is crucial in order to provide high-quality intelligent services. However, sensor data delivery can be interrupted for various reasons, such as sensor malfunction, network failures, and external attacks. Thus, only data from a partial set of sensors may be available. We call it the missing sensor problem. This problem can lead to severe performance degradation at inference time by neural-network-based recognition models trained on the complete sensor set. This paper enhances the robustness of neural network models to the missing sensor problem by introducing a novel feature reconstruction module, named the graph recovery module, that handles missing sensors directly inside the network. Specifically, we consider topology-aware IoT applications, where sensors are placed on a physically interconnected network. We design a novel neural message passing mechanism that logically mimics physical network topology, based on recent advances in graph neural networks (GNNs). We rely on a spatial locality assumption, where only correlations between physically connected sensors are explicitly explored. When encountering missing sensors, information is passed from available sensors to missing sensors to be used to reconstruct their features. Moreover, at each message passing step, we utilize a gating mechanism inspired by Gated Recurrent Units (GRUs) to automatically control information flow between available sensors and missing sensors. We empirically evaluate the reconstruction performance of the graph recovery module with two representative IoT applications; human activity recognition (HAR) and electroencephalogram (EEG)-based motor-imagery classification, on three public datasets. Two different backbone networks are utilized for the tasks. Our design is shown to effectively maintain model performance, suffering only 7% to 18% accuracy loss when as much as 90% of sensors are removed, compared to a drop of 15% to 47% in the accuracy of competing state-of-the-art algorithms under the same conditions. The accuracy gap is largest when more sensors are missing.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411818"}, {"primary_key": "2427340", "vector": [], "sparse_vector": [], "title": "GIobalFusion: A Global Attentional Deep Learning Framework for Multisensor Information Fusion.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The paper enhances deep-neural-network-based inference in sensing applications by introducing a lightweight attention mechanism called the global attention module for multi-sensor information fusion. This mechanism is capable of utilizing information collected from higher layers of the neural network to selectively amplify the influence of informative features and suppress unrelated noise at the fusion layer. We successfully integrate this mechanism into a new end-to-end learning framework, called GIobalFusion, where two global attention modules are deployed for spatial fusion and sensing modality fusion, respectively. Through an extensive evaluation on four public human activity recognition (HAR) datasets, we successfully demonstrate the effectiveness of GlobalFusion at improving information fusion quality. The new approach outperforms the state-of-the-art algorithms on all four datasets with a clear margin. We also show that the learned attention weights agree well with human intuition. We then validate the efficiency of GlobalFusion by testing its inference time and energy consumption on commodity IoT devices. Only a negligible overhead is induced by the global attention modules.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3380999"}, {"primary_key": "2427341", "vector": [], "sparse_vector": [], "title": "Designing and Evaluating Hand-to-Hand Gestures with Dual Commodity Wrist-Worn Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Hand gestures provide a natural and easy-to-use way to input commands. However, few works have studied the design space of bimanual hand gestures or attempted to infer gestures that involve devices on both hands. We explore the design space of hand-to-hand gestures, a group of gestures that are performed by touching one hand with the other hand. Hand-to-hand gestures are easy to perform and provide haptic feedback on both hands. Moreover, hand-to-hand gestures generate simultaneous vibration on two hands that can be sensed by dual off-the-shelf wrist-worn devices. In this work, we derive a hand-to-hand gesture vocabulary with subjective ratings from users and select gesture sets for real-life scenarios. We also take advantage of devices on both wrists to demonstrate their gesture-sensing capability. Our results show that the recognition accuracy for fourteen gestures is 94.6% when the user is stationary, and the accuracy for five gestures is 98.4% or 96.3% when the user is walking or running, respectively. This is significantly more accurate than a single device worn on either wrist. Our further evaluation also validates that users can easily remember hand-to-hand gestures and use our technique to invoke commands in real-life contexts.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3380984"}, {"primary_key": "2427342", "vector": [], "sparse_vector": [], "title": "VocalLock: Sensing Vocal Tract for Passphrase-Independent User Authentication Leveraging Acoustic Signals on Smartphones.", "authors": ["Li Lu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Recent years have witnessed the surge of biometric-based user authentication for mobile devices due to its promising security and convenience. As a natural and widely-existed behavior, human speaking has been exploited for user authentication. Existing voice-based user authentication explores the unique characteristics from either the voiceprint or mouth movements, which is vulnerable to replay attacks and mimic attacks. During speaking, the vocal tract, including the static shape and dynamic movements, also exhibits the individual uniqueness, and they are hardly eavesdropped and imitated by adversaries. Hence, our work aims to employ the individual uniqueness of vocal tract to realize user authentication on mobile devices. Moreover, most voice-based user authentications are passphrase-dependent, which significantly degrade the user experience. Thus, such user authentications are pressed to be implemented in a passphrase-independent manner while being able to resist various attacks. In this paper, we propose a user authentication system, VocalLock, which senses the whole vocal tract during speaking to identify different individuals in a passphrase-independent manner on smartphones leveraging acoustic signals. VocalLock first utilizes FMCW on acoustic signals to characterize both the static shape and dynamic movements of the vocal tract during speaking, and then constructs a passphrase-independent user authentication model based on the unique characteristics of vocal tract through GMM-UBM. The proposed VocalLock can resist various spoofing attacks, while achieving a satisfactory user experience. Extensive experiments in real environments demonstrate VocalLock can accurately authenticate user identity in a passphrase-independent manner and successfully resist various attacks.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397320"}, {"primary_key": "2427343", "vector": [], "sparse_vector": [], "title": "D3P: Data-driven Demand Prediction for Fast Expanding Electric Vehicle Sharing Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> Zhu", "<PERSON><PERSON>", "Hongkai Wen"], "summary": "The future of urban mobility is expected to be shared and electric. It is not only a more sustainable paradigm that can reduce emissions, but can also bring societal benefits by offering a more affordable on-demand mobility option to the general public. Many car sharing service providers as well as automobile manufacturers are entering the competition by expanding both their EV fleets and renting/returning station networks, aiming to seize a share of the market and to bring car sharing to the zero emissions level. During their fast expansion, one determinant for success is the ability of predicting the demand of stations as the entire system is growing continuously. There are several challenges in this demand prediction problem: First, unlike most of the existing work which predicts demand only for static systems or at few stages of expansion, in the real world we often need to predict the demand as or even before stations are being deployed or closed, to provide information and decision support. Second, for the new stations to be deployed, there is no historical data available to help the prediction of their demand. Finally, the impact of deploying/closing stations on the other stations in the system can be complex. To address these challenges, we formulate the demand prediction problem in the context of fast expanding electric vehicle sharing systems, and propose a data-driven demand prediction approach which aims to model the expansion dynamics directly from the data. We use a local temporal encoding process to handle the historical data for each existing station, and a dynamic spatial encoding process to take correlations between stations into account with Graph Convolutional Neural Networks (GCN). The encoded features are fed to a multi-scale predictor, which forecasts both the long-term expected demand of the stations and their instant demand in the near future. We evaluate the proposed approach with real-world data collected from a major EV sharing platform for one year. Experimental results demonstrate that our approach significantly outperforms the state of the art, showing up to three-fold performance gain in predicting demand for the expanding EV sharing systems.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3381005"}, {"primary_key": "2427345", "vector": [], "sparse_vector": [], "title": "CarOSense: Car Occupancy Sensing with the Ultra-Wideband Keyless Infrastructure.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Ultra-Wideband (UWB) is a popular technology to provide high accuracy localization, asset tracking and access control applications. Due to the accurate ranging feature and robustness to relay attacks, car manufacturers are upgrading the keyless entry infrastructure to UWB. As car occupancy monitoring is an essential step to support regulatory requirements and provide customized user experience, we build CarOSense to explore the possibility of reusing UWB keyless infrastructure as an orthogonal sensing modality to detect per-seat car occupancy. CarOSense uses a novel deep learning model, MaskMIMO, to learn spatial/time features by 2D convolutions and per-seat attentions by a multi-task mask. We collect UWB data from 10 car locations with up to 16 occupancy states in each location. We implement CarOSense as a cross-platform demo and evaluate it in 15 different scenarios, including leave-one-out test of unknown car locations and stress test of unseen scenarios. Results show that the average accuracy is 94.6% for leave-one-out test and 87.0% for stress test. CarOSense is robust in a large set of untrained scenarios with the model trained on a small set of training data. We also benchmark the computation cost and demonstrate that CarOSense is lightweight and can run smoothly in real-time on embedded devices.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411820"}, {"primary_key": "2427346", "vector": [], "sparse_vector": [], "title": "Teacher Tracking with Integrity: What Indoor Positioning Can Reveal About Instructional Proxemics.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Automatic tracking of activity and location in the classroom is becoming increasingly feasible and inexpensive. However, although there is a growing interest in creating classrooms embedded with tracking capabilities using computer vision and wearables, more work is still needed to understand teachers' perceived opportunities and concerns about using indoor positioning data to reflect on their practice. This paper presents results from a qualitative study, conducted across three authentic educational settings, investigating the potential of making positioning traces available to teachers. Positioning data from 28 classes taught by 10 university teachers was captured using sensors in three different collaborative classroom spaces in the disciplines of design, health and science. The contributions of this paper to ubiquitous computing are the documented reflections of teachers from different disciplines provoked by visual representations of their classroom positioning data and that of others. These reflections point to: i) the potential benefit of using these digital traces to support teaching; and ii) concerns to be considered in the design of meaningful analytics systems for instructional proxemics.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3381017"}, {"primary_key": "2427347", "vector": [], "sparse_vector": [], "title": "DeepRange: Acoustic Ranging via Deep Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Acoustic ranging is a technique for estimating the distance between two objects using acoustic signals, which plays a critical role in many applications, such as motion tracking, gesture/activity recognition, and indoor localization. Although many ranging algorithms have been developed, their performance still degrades significantly under strong noise, interference and hardware limitations. To improve the robustness of the ranging system, in this paper we develop a Deep learning based Ranging system, called DeepRange. We first develop an effective mechanism to generate synthetic training data that captures noise, speaker/mic distortion, and interference in the signals and remove the need of collecting a large volume of training data. We then design a deep range neural network (DRNet) to estimate distance. Our design is inspired by signal processing that ultra-long convolution kernel sizes help to combat the noise and interference. We further apply an ensemble method to enhance the performance. Moreover, we analyze and visualize the network neurons and filters, and identify a few important findings that can be useful for improving the design of signal processing algorithms. Finally, we implement and evaluate DeepRangeusing 11 smartphones with different brands and models, 4 environments (i.e., a lab, a conference room, a corridor, and a cubic area), and 10 users. Our results show that DRNet significantly outperforms existing ranging algorithms.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432195"}, {"primary_key": "2427348", "vector": [], "sparse_vector": [], "title": "Incremental Real-Time Personalization in Human Activity Recognition Using Domain Adaptive Batch Normalization.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Human Activity Recognition (HAR) from devices like smartphone accelerometers is a fundamental problem in ubiquitous computing. Machine learning based recognition models often perform poorly when applied to new users that were not part of the training data. Previous work has addressed this challenge by personalizing general recognition models to the unique motion pattern of a new user in a static batch setting. They require target user data to be available upfront. The more challenging online setting has received less attention. No samples from the target user are available in advance, but they arrive sequentially. Additionally, the motion pattern of users may change over time. Thus, adapting to new and forgetting old information must be traded off. Finally, the target user should not have to do any work to use the recognition system by, say, labeling any activities. Our work addresses all of these challenges by proposing an unsupervised online domain adaptation algorithm. Both classification and personalization happen continuously and incrementally in real time. Our solution works by aligning the feature distributions of all subjects, be they sources or the target, in hidden neural network layers. To this end, we normalize the input of a layer with user-specific mean and variance statistics. During training, these statistics are computed over user-specific batches. In the online phase, they are estimated incrementally for any new target user.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432230"}, {"primary_key": "2427349", "vector": [], "sparse_vector": [], "title": "Knitted Sensors: Designs and Novel Approaches for Real-Time, Real-World Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recent work has shown the feasibility of producing knitted capacitive touch sensors through digital fabrication with little human intervention in the textile production process. Such sensors can be designed and manufactured at scale and require only two connection points, regardless of the touch sensor form factor and size of the fabric, opening many possibilities for new designs and applications in textile sensors. To bring this technology closer to real-world use, we go beyond previous work on coarse touch discrimination to enable fine, accurate touch localization on a knitted sensor, using a recognition model able to capture the temporal behavior of the sensor. Moreover, signal acquisition and processing are performed in real-time, using swept frequency Bode analysis to quantify distortion from induced capacitance. After training our network model, we conducted a study with new users, and achieved a subject-independent accuracy of 66% in identifying the touch location on the 36-button sensor, while chance accuracy is approximately 3%. Additionally, we conducted a study demonstrating the viability of taking this solution closer to real-world scenarios by testing the sensor's resistance to potential deformation from everyday conditions. We also introduce several other knitted designs and related application prototypes to explore potential uses of the technology.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432201"}, {"primary_key": "2427350", "vector": [], "sparse_vector": [], "title": "Exploring User Expectations of Proactive AI Systems.", "authors": ["<PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent advances in artificial intelligence (AI) enabled digital assistants to evolve towards proactive user support. However, expectations as to when and to what extent assistants should take the initiative are still unclear; discrepancies to the actual system behavior might negatively affect user acceptance. In this paper, we present an in-the-wild study for exploring user expectations of such user-supporting AI systems in terms of different proactivity levels and use cases. We collected 3,168 in-situ responses from 272 participants through a mixed method of automated user tracking and context-triggered surveying. Using a data-driven approach, we gain insights into initial expectations and how they depend on different human factors and contexts. Our insights can help to design AI systems with varying degree of proactivity and preset to meet individual expectations.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432193"}, {"primary_key": "2427351", "vector": [], "sparse_vector": [], "title": "Evaluating the Reproducibility of Physiological Stress Detection Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recent advances in wearable sensor technologies have led to a variety of approaches for detecting physiological stress. Even with over a decade of research in the domain, there still exist many significant challenges, including a near-total lack of reproducibility across studies. Researchers often use some physiological sensors (custom-made or off-the-shelf), conduct a study to collect data, and build machine-learning models to detect stress. There is little effort to test the applicability of the model with similar physiological data collected from different devices, or the efficacy of the model on data collected from different studies, populations, or demographics. This paper takes the first step towards testing reproducibility and validity of methods and machine-learning models for stress detection. To this end, we analyzed data from 90 participants, from four independent controlled studies, using two different types of sensors, with different study protocols and research goals. We started by evaluating the performance of models built using data from one study and tested on data from other studies. Next, we evaluated new methods to improve the performance of stress-detection models and found that our methods led to a consistent increase in performance across all studies, irrespective of the device type, sensor type, or the type of stressor. Finally, we developed and evaluated a clustering approach to determine the stressed/not-stressed classification when applying models on data from different studies, and found that our approach performed better than selecting a threshold based on training data. This paper's thorough exploration of reproducibility in a controlled environment provides a critical foundation for deeper study of such methods, and is a prerequisite for tackling reproducibility in free-living conditions.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432220"}, {"primary_key": "2427352", "vector": [], "sparse_vector": [], "title": "GAME: Game As a Measurement Environment: Scheme to Evaluate Interfaces and Game Contents Based on Test Theories.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Although many computer games have become diversified in recent years, much effort and ingenuity are needed to produce games that persons with visual impairments can enjoy. However, game developers face difficulties in adjusting the parameters of these games, including difficulty and interface features, because the control skills and disability conditions of gamers vary. Thus, it is essential to establish a scheme to evaluate game interfaces and stages with individuals trials with a short time. In this study, our purpose is to propose and establish the concept of GAME (Game As a Measurement Environment) that can evaluate game interfaces and stages. This concept aims to divide a game stage into substages and then randomly allocate them to the stage. Then, the generated stage records the play logs and play conditions and then analyzes them logs based on test theories, including classical test theory (CTT) and item response theory (IRT). Based on the analysis of the gathered play logs of side-scroller action games, our concept could clarify the relationship between the stage contents, interface effects, and players skills and disabilities.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432702"}, {"primary_key": "2427354", "vector": [], "sparse_vector": [], "title": "(M)ad to See Me?: Intelligent Advertisement Placement: Balancing User Annoyance and Advertising Effectiveness.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Pan Hui", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Advertising is an unavoidable albeit a frustrating part of mobile interactions. Due to limited form factor, mobile advertisements often resort to intrusive strategies where they temporarily block the user's view in an attempt to increase effectiveness by forcing the user's attention. While such strategies contribute to advertising awareness and effectiveness, they do so at the cost of degrading the user's overall experience and can lead to frustration and annoyance. In this paper, we contribute by developing Perceptive Ads as an intelligent advertisement placement strategy that minimizes disruptions caused by ads while preserving their effectiveness. Our work is the first to simultaneously consider the needs of users, app developers, and advertisers. Ensuring the needs of all stakeholders are taken into account is essential for the adoption of advertising strategies as users (and indirectly developers) would reject strategies that are disruptive but effective, while advertisers would reject strategies that are non-disruptive but inefficient. We demonstrate the effectiveness of our technique through a user study with N = 16 participants and two representative examples of mobile apps that commonly integrate advertisements: a game and a news app. Results from the study demonstrate that our approach improves perception towards advertisements by 43.75% without affecting application interactivity while at the same time increasing advertisement effectiveness by 37.5% compared to a state-of-the-art baseline.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397324"}, {"primary_key": "2427355", "vector": [], "sparse_vector": [], "title": "MAIL: Multi-Scale Attention-Guided Indoor Localization Using Geomagnetic Sequences.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Suining He", "<PERSON><PERSON>", "<PERSON>"], "summary": "Knowing accurate indoor locations of pedestrians has great social and commercial values, such as pedestrian heatmapping and targeted advertising. Location estimation with sequential inputs (e.g., geomagnetic sequences) has received much attention lately, mainly because they enhance the localization accuracy with temporal correlations. Nevertheless, it is challenging to realize accurate localization with geomagnetic sequences due to environmental factors, such as non-uniform ferromagnetic disturbances. To address this, we propose MAIL, a multi-scale attention-guided indoor localization network, which turns these challenges into favorable advantages. Our key contributions are as follows. First, instead of extracting a single holistic feature from an input sequence directly, we design a scale-based feature extraction unit that takes variational anomalies at different scales into consideration. Second, we propose an attention generation scheme that identifies attention values for different scales. Rather than setting fixed numbers, MAIL learns them adaptively with the input sequence, thus increasing its adaptability and generality. Third, guided by attention values, we fuse multi-scale features by paying more attention to prominent ones and estimate current location with the fused feature. We evaluate the performance of MAIL in three different trial sites. Evaluation results show that MAIL reduces the mean localization error by more than 36% compared with the state-of-the-art competing schemes.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397335"}, {"primary_key": "2427357", "vector": [], "sparse_vector": [], "title": "Uncovering Practical Security and Privacy Threats for Connected Glasses with Embedded Video Cameras.", "authors": ["<PERSON><PERSON>", "Radu<PERSON><PERSON>"], "summary": "We address in this work security and privacy threats for connected camera glasses, for which very few investigations have been conducted so far, despite the considerable attention to understanding security concerns for other wearables, such as smartwatches and fitness trackers. To raise awareness about such threats, we present the results of a case study involving a low-cost spy camera glasses device, readily available on the market, that can be used to record and stream live video, for which we demonstrate infringement of several privacy requirements (regarding the camera glasses device itself, the data collected by the embedded video camera, the wearer of the device, and for bystanders as well) that lead to corresponding security threats (e.g., data confidentiality, integrity, availability, and access control). To foster replicability and reproducibility of our empirical results, investigation method, and implementation of attacks, we describe our case study in the form of a detailed activity log and release full C++ code implementing our approach. Furthermore, we present our findings to three IT security experts and summarize their recommendations for designing more secure connected camera glasses.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432700"}, {"primary_key": "2427358", "vector": [], "sparse_vector": [], "title": "SAFER: Development and Evaluation of an IoT Device Risk Assessment Framework in a Multinational Organization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Users of Internet of Things (IoT) devices are often unaware of their security risks and cannot sufficiently factor security considerations into their device selection. This puts networks, infrastructure and users at risk. We developed and evaluated SAFER, an IoT device risk assessment framework designed to improve users' ability to assess the security of connected devices. We deployed SAFER in a large multinational organization that permits use of private devices. To evaluate the framework, we conducted a mixed-method study with 20 employees. Our findings suggest that SAFER increases users' awareness of security issues. It provides valuable advice and impacts device selection. Based on our findings, we discuss implications for the design of device risk assessment tools, with particular regard to the relationship between risk communication and user perceptions of device complexity.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3414173"}, {"primary_key": "2427359", "vector": [], "sparse_vector": [], "title": "The OnHW Dataset: Online Handwriting Recognition from IMU-Enhanced Ballpoint Pens with Machine Learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>rn M<PERSON>", "<PERSON>"], "summary": "This paper presents a handwriting recognition (HWR) system that deals with online character recognition in real-time. Our sensor-enhanced ballpoint pen delivers sensor data streams from triaxial acceleration, gyroscope, magnetometer and force signals at 100 Hz. As most existing datasets do not meet the requirements of online handwriting recognition and as they have been collected using specific equipment under constrained conditions, we propose a novel online handwriting dataset acquired from 119 writers consisting of 31,275 uppercase and lowercase English alphabet character recordings (52 classes) as part of the UbiComp 2020 Time Series Classification Challenge. Our novel OnHW-chars dataset allows for the evaluations of uppercase, lowercase and combined classification tasks, on both writer-dependent (WD) and writer-independent (WI) classes and we show that properly tuned machine learning pipelines as well as deep learning classifiers (such as CNNs, LSTMs, and BiLSTMs) yield accuracies up to 90 % for the WD task and 83 % for the WI task for uppercase characters. Our baseline implementations together with the rich and publicly available OnHW dataset serve as a baseline for future research in that area.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411842"}, {"primary_key": "2427360", "vector": [], "sparse_vector": [], "title": "HeartQuake: Accurate Low-Cost Non-Invasive ECG Monitoring Using Bed-Mounted Geophones.", "authors": ["Jaeyeon Park", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "JeongGil Ko"], "summary": "This work presents HeartQuake, a low cost, accurate, non-intrusive, geophone-based sensing system for extracting accurate electrocardiogram (ECG) patterns using heartbeat vibrations that penetrate through a bed mattress. In HeartQuake, cardiac activity-originated vibration patterns are captured on a geophone and sent to a server, where the data is filtered to remove the sensor's internal noise and passed on to a bidirectional long short term memory (Bi-LSTM) deep learning model for ECG waveform estimation. To the best of our knowledge, this is the first solution that can non-intrusively provide accurate ECG waveform characteristics instead of more basic abstract features such as the heart rate using bed-mounted geophone sensors. Our extensive experimental results with a baseline dataset collected from 21 study participants and a longitudinal dataset from 15 study participants suggest that HeartQuake, even when using a general non-personalized model, can detect all five ECG peaks (e.g., P, Q, R, S, T) with an average error of 13 msec when participants are stationary on the bed. Furthermore, clinically used ECG metrics such as the RR interval and QRS segment width can be estimated with errors 3 msec and 10 msec, respectively. When additional noise factors are present (e.g., external vibration and various sleeping habits), the estimation error increases, but can be mitigated by using a personalized model. Finally, a qualitative study with 11 physicians on the clinically perceived quality of HeartQuake-generated ECG signals suggests that HeartQuake can effectively serve as a screening tool for detecting and diagnosing abnormal cardiovascular conditions. In addition, HeartQuake's low-cost and non-intrusive nature allow it to be deployed in larger scales compared to current ECG monitoring solutions.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411843"}, {"primary_key": "2427361", "vector": [], "sparse_vector": [], "title": "Making It Personal: Addressing Individual Audience Members in Oral Presentations Using Augmented Reality.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Public-speaking situations such as classroom lectures, seminars, and meetings, where speakers must actively engage the audience, require considerable effort from the speaker in gathering verbal and non-verbal feedback from the audience. Garnering feedback can be made easier by technologies such as augmented reality (AR) capable of displaying information in 3D space surrounding the speaker. We present an AR-enabled presentation display system to provide real-time feedback from the audience to the speaker during a presentation. The feedback includes names and affective states of the audience, icons requesting a change in volume and rate of speech, and annotated questions and comments. The design of the feedback system was informed by findings from an exploratory study with academic professionals experienced in delivering presentations. In a between-subjects study, we evaluated presentations displaying feedback information spatially overlaid above the heads of the audience members in one condition and in the periphery of the presenter's view in another condition, as compared with a no-AR control condition. Results showed that the presenters in the overlay condition called upon the audience by name significantly more often than in the peripheral and the control conditions, and they rated the overlay condition as more reliable, helpful, and wanting to use it for future presentations compared to the peripheral condition. Overall, AR feedback was considered useful by both the presenters and the audience and did not negatively impact speaker confidence and state anxiety of the presenter.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397336"}, {"primary_key": "2427362", "vector": [], "sparse_vector": [], "title": "3D Point Cloud Generation with Millimeter-Wave Radar.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> He", "<PERSON><PERSON><PERSON>"], "summary": "Emerging autonomous driving systems require reliable perception of 3D surroundings. Unfortunately, current mainstream perception modalities, i.e., camera and Lidar, are vulnerable under challenging lighting and weather conditions. On the other hand, despite their all-weather operations, today's vehicle Radars are limited to location and speed detection. In this paper, we introduce MILLIPOINT, a practical system that advances the Radar sensing capability to generate 3D point clouds. The key design principle of MILLIPOINT lies in enabling synthetic aperture radar (SAR) imaging on low-cost commodity vehicle Radars. To this end, MILLIPOINT models the relation between signal variations and Radar movement, and enables self-tracking of Radar at wavelength-scale precision, thus realize coherent spatial sampling. Furthermore, MILLIPOINT solves the unique problem of specular reflection, by properly focusing on the targets with post-imaging processing. It also exploits the Radar's built-in antenna array to estimate the height of reflecting points, and eventually generate 3D point clouds. We have implemented MILLIPOINT on a commodity vehicle Radar. Our evaluation results show that MILLIPOINT effectively combats motion errors and specular reflections, and can construct 3D point clouds with much higher density and resolution compared with the existing vehicle Radar solutions.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432221"}, {"primary_key": "2427363", "vector": [], "sparse_vector": [], "title": "CellPred: A Behavior-aware Scheme for Cellular Data Usage Prediction.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cellular data usage consumption prediction is an important topic in cellular networks related researches. Accurately predicting future data usage can benefit both the cellular operators and the users, which can further enable a wide range of applications. Different from previous work focusing on statistical approaches, in this paper, we propose a scheme called CellPred to predict cellular data usage from an individual user perspective considering user behavior patterns. Specifically, we utilize explicit user behavioral tags collected from subscription data to function as an external aid to enhance the user's mobility and usage prediction. Then we aggregate individual user data usage to cell tower level to obtain the final prediction results. To our knowledge, this is the first work studying cellular data usage prediction from an individual user behavior-aware perspective based on large-scale cellular signaling and behavior tags from the subscription data. The results show that our method improves the data usage prediction accuracy compared to the state-of-the-art methods; we also comprehensively demonstrate the impacts of contextual factors on CellPred performance. Our work can shed light on broad cellular networks researches related to human mobility and data usage. Finally, we discuss issues such as limitations, applications of our approach, and insights from our work.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3380982"}, {"primary_key": "2427365", "vector": [], "sparse_vector": [], "title": "Robust Unsupervised Factory Activity Recognition with Body-worn Accelerometer Using Temporal Structure of Multiple Sensor Data Motifs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents a robust unsupervised method for recognizing factory work using sensor data from body-worn acceleration sensors. In line-production systems, each factory worker repetitively performs a predefined work process with each process consisting of a sequence of operations. Because of the difficulty in collecting labeled sensor data from each factory worker, unsupervised factory activity recognition has been attracting attention in the ubicomp community. However, prior unsupervised factory activity recognition methods can be adversely affected by any outlier activities performed by the workers. In this study, we propose a robust factory activity recognition method that tracks frequent sensor data motifs, which can correspond to particular actions performed by the workers, that appear in each iteration of the work processes. Specifically, this study proposes tracking two types of motifs: period motifs and action motifs, during the unsupervised recognition process. A period motif is a unique data segment that occurs only once in each work period (one iteration of an overall work process). An action motif is a data segment that occurs several times in each work period, corresponding to an action that is performed several times in each period. Tracking multiple period motifs enables us to roughly capture the temporal structure and duration of the work period even when outlier activities occur. Action motifs, which are spread throughout the work period, permit us to precisely detect the start time of each operation. We evaluated the proposed method using sensor data collected from workers in actual factories and achieved state-of-the-art performance.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411836"}, {"primary_key": "2427367", "vector": [], "sparse_vector": [], "title": "Liquid Level Sensing Using Commodity WiFi in a Smart Home Environment.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The popularity of Internet-of-Things (IoT) has provided us with unprecedented opportunities to enable a variety of emerging services in a smart home environment. Among those services, sensing the liquid level in a container is critical to building many smart home and mobile healthcare applications that improve the quality of life. This paper presents LiquidSense, a liquid level sensing system that is low-cost, high accuracy, widely applicable to different daily liquids and containers, and can be easily integrated with existing smart home networks. LiquidSense uses existing home WiFi network and a low-cost transducer that attached to the container to sense the resonance of the container for liquid level detection. In particular, our system mounts a low-cost transducer on the surface of the container and emits a well-designed chirp signal to make the container resonant, which introduces subtle changes to the home WiFi signals. By analyzing the subtle phase changes of the WiFi signals, LiquidSense extracts the resonance frequency as a feature for liquid level detection. Our system constructs prediction models for both continuous and discrete predictions using curve fitting and SVM respectively. We evaluate LiquidSense in home environments with containers of three different materials and six types of liquids. Results show that LiquidSense achieves an overall accuracy of 97% for continuous prediction and an overall F-score of 0.968 for discrete predication. Results also show that our system has a large coverage in a home environment and works well under non-line-of-sight (NLOS) scenarios.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3380996"}, {"primary_key": "2427368", "vector": [], "sparse_vector": [], "title": "End-User Development of Experience Sampling Smartphone Apps -Recommendations and Requirements.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Professional programmers are significantly outnumbered by end-users of software, making it problematic to predict the diverse, dynamic needs of these users in advance. An end-user development (EUD) approach, supporting the creation and modification of software independent of professional developers, is one potential solution. EUD activities are applicable to the work practices of psychology researchers and clinicians, who increasingly rely on software for assessment of participants and patients, but must also depend on developers to realise their requirements. In practice, however, the adoption of EUD technology by these two end-user groups is contingent on various contextual factors that are not well understood. In this paper, we therefore establish recommendations for the design of EUD tools allowing non-programmers to develop apps to collect data from participants in their everyday lives, known as \"experience sampling\" apps. We first present interviews conducted with psychology researchers and practising clinicians on their current working practices and motivation to adopt EUD tools. We then describe our observation of a chronic disease management clinic. Finally, we describe three case studies of psychology researchers using our EUD tool Jeeves to undertake experience sampling studies, and synthesise recommendations and requirements for tools allowing the EUD of experience sampling apps.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397307"}, {"primary_key": "2427370", "vector": [], "sparse_vector": [], "title": "How Do You Feel Online: Exploiting Smartphone Sensors to Detect Transitory Emotions during Social Media Use.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Hwajung Hong", "<PERSON>"], "summary": "Emotions are an intrinsic part of the social media user experience that can evoke negative behaviors such as cyberbullying and trolling. Detecting the emotions of social media users may enable responding to and mitigating these problems. Prior work suggests this may be achievable on smartphones: emotions can be detected via built-in sensors during prolonged input tasks. We extend these ideas to a social media context featuring sparse input interleaved with more passive browsing and media consumption activities. To achieve this, we present two studies. In the first, we elicit participant's emotions using images and videos and capture sensor data from a mobile device, including data from a novel passive sensor: its built-in eye-tracker. Using this data, we construct machine learning models that predict self-reported binary affect, achieving 93.20% peak accuracy. A follow-up study extends these results to a more ecologically valid scenario in which participants browse their social media feeds. The study yields high accuracies for both self-reported binary valence (94.16%) and arousal (92.28%). We present a discussion of the sensors, features and study design choices that contribute to this high performance and that future designers and researchers can use to create effective and accurate smartphone-based affect detection systems.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432223"}, {"primary_key": "2427371", "vector": [], "sparse_vector": [], "title": "mmASL: Environment-Independent ASL Gesture Recognition Using 60 GHz Millimeter-wave Signals.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> <PERSON>"], "summary": "Home assistant devices such as Amazon Echo and Google Home have become tremendously popular in the last couple of years. However, due to their voice-controlled functionality, these devices are not accessible to Deaf and Hard-of-Hearing (DHH) people. Given that over half a million people in the United States communicate using American Sign Language (ASL), there is a need of a home assistant system that can recognize ASL. The objective of this work is to design a home assistant system for DHH users (referred to as mmASL) that can perform ASL recognition using 60 GHz millimeter-wave wireless signals. mmASL has two important components. First, it can perform reliable wake-word detection using spatial spectrograms. Second, using a scalable and extensible multi-task deep learning model, mmASL can learn the phonological properties of ASL signs and use them to accurately recognize the ASL signs. We implement mmASL on 60 GHz software radio platform with phased array, and evaluate it using a large-scale data collection from 15 signers, 50 ASL signs and over 12K sign instances. We show that mmASL is tolerant to the presence of other interfering users and their activities, change of environment and different user positions. We compare mmASL with a well-studied Kinect and RGB camera based ASL recognition systems, and find that it can achieve a comparable performance (87% average accuracy of sign recognition), validating the feasibility of using 60 GHz mmWave system for ASL sign recognition.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3381010"}, {"primary_key": "2427372", "vector": [], "sparse_vector": [], "title": "Recognizing Running Movement Changes with Quaternions on a Sports Watch.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Sports watches are popular amongst runners but limited in terms of sensor locations (i.e., one location at the wrist). Thus, apps on the watch cannot directly sense movement changes in arbitrary body locations. This, in turn, severely limits training support services, contextual awareness and seamless interaction mechanisms. Our approach addresses this gap and connects the watch with two strategically placed inertial measurement units (IMUs) via bluetooth low energy (BLE). Our prototypical app for Wear OS receives orientation (quaternion) data and matches the sensors to the arm or leg segments using a flexible and simple procedure. We collected data from eight runners and used support vector machines (SVMs) to recognize different movements. Findings from our evaluation with different parameters indicate feasible recognition and low false-positive rates for two to three different movements, for both placements. Our approach can thus help to improve applications that support training and thus contributes to developing motion capture for personal use; it also enables movement-based interaction while running.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432197"}, {"primary_key": "2427373", "vector": [], "sparse_vector": [], "title": "Leveraging Free-Hand Sketches for Potential Screening of PTSD.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>"], "summary": "Post-traumatic stress disorder (PTSD) negatively influences a person's ability to cope and increases psychiatric morbidity. The existing diagnostic tools of PTSD are often difficult to administer within marginalized communities due to language and cultural barriers, lack of skilled clinicians, and stigma around disclosing traumatic experiences. We present an initial proof of concept for a novel, low-cost, and creative method to screen the potential cases of PTSD based on free-hand sketches within three different communities in Bangladesh: Rohingya refugees (n = 44), slum-dwellers (n = 35), and engineering students (n = 85). Due to the low overhead and nonverbal nature of sketching, our proposed method potentially overcomes communication and resource barriers. Using corner and edge detection algorithms, we extracted three features (number of corners, number and average length of strokes) from the images of free-hand sketches. We used these features along with sketch themes, participants' gender and group to train multiple logistic regression models for potentially screening PTSD (accuracy: 82.9-87.9%). We improved the accuracy (99.29%) by integrating EEG data with sketch features in a Random Forest model for the refugee population. Our proposed initial assessment method of PTSD based on sketches could potentially be integrated with phones and EEG headsets, making it widely accessible to the underrepresented communities.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411835"}, {"primary_key": "2427374", "vector": [], "sparse_vector": [], "title": "Teaching American Sign Language in Mixed Reality.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Xinyu Shi", "<PERSON>ist<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Lorna <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents a holistic system to scale up the teaching and learning of vocabulary words of American Sign Language (ASL). The system leverages the most recent mixed-reality technology to allow the user to perceive her own hands in an immersive learning environment with first- and third-person views for motion demonstration and practice. Precise motion sensing is used to record and evaluate motion, providing real-time feedback tailored to the specific learner. As part of this evaluation, learner motions are matched to features derived from the Hamburg Notation System (HNS) developed by sign-language linguists. We develop a prototype to evaluate the efficacy of mixed-reality-based interactive motion teaching. Results with 60 participants show a statistically significant improvement in learning ASL signs when using our system, in comparison to traditional desktop-based, non-interactive learning. We expect this approach to ultimately allow teaching and guided practice of thousands of signs.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432211"}, {"primary_key": "2427375", "vector": [], "sparse_vector": [], "title": "Assessing Cognitive Performance Using Physiological and Facial Features: Generalizing across Contexts.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> N<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Sensing and machine learning advances have enabled the unobtrusive measurement of physiological responses and facial expressions so as to estimate one's cognitive performance. This often boils down to mapping the states of the cognitive processes underpinning human cognition: physiological responses (e.g., heart rate) and facial expressions (e.g., frowning) often reflect the states of our cognitive processes. However, it remains unclear whether physiological responses and facial expressions used in one particular task (e.g., gaming) can reliably assess cognitive performance in another task (e.g., coding), because complex and diverse tasks often require varying levels and combinations of cognitive processes. In this paper, we measure the cross-task reliability of physiological and facial responses. Specifically, we assess cognitive performance based on physiological responses and facial expressions for 123 participants in 4 independent studies (3 studies for out-of-sampling training and testing, and 1 study for evaluation only): (1) a Pac-Man game, (2) an adaptive-assessment task, (3) a code-debugging task, and (4) a gaze-based game. We follow an ensemble learning approach after cross-training and cross-testing with all possible combinations of the 3 first datasets. We save the 4th dataset only for testing purposes, and we showcase how to engineer generalizable features that predict cognitive performance. Our results show that the extracted features do generalize, and can reliably predict cognitive performance across a diverse set of cognitive tasks that require different combinations of problem-solving, decision-making, and learning processes for their completion.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411811"}, {"primary_key": "2427377", "vector": [], "sparse_vector": [], "title": "VerHealth: Vetting Medical Voice Applications through Policy Enforcement.", "authors": ["<PERSON><PERSON>", "Hang Hu", "<PERSON>", "<PERSON>"], "summary": "Healthcare applications on Voice Personal Assistant System (e.g., Amazon Alexa), have shown a great promise to deliver personalized health services via a conversational interface. However, concerns are also raised about privacy, safety, and service quality. In this paper, we propose VerHealth, to systematically assess health-related applications on Alexa for how well they comply with existing privacy and safety policies. VerHealth contains a static module and a dynamic module based on machine learning that can trigger and detect violation behaviors hidden deep in the interaction threads. We use VerHealth to analyze 813 health-related applications on Alexa by sending over 855,000 probing questions and analyzing 863,000 responses. We also consult with three medical school students (domain experts) to confirm and assess the potential violations. We show that violations are quite common, e.g., 86.36% of them miss disclaimers when providing medical information; 30.23% of them store user physical or mental health data without approval. Domain experts believe that the applications' medical suggestions are often factually-correct but are of poor relevance, and applications should have asked more questions before providing suggestions for over half of the cases. Finally, we use our results to discuss possible directions for improvements.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432233"}, {"primary_key": "2427378", "vector": [], "sparse_vector": [], "title": "Ready, <PERSON>eady, Touch!: Sensing Physical Contact with a Finger-Mounted IMU.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>g<PERSON>g <PERSON>", "Sur<PERSON>"], "summary": "A finger held in the air exhibits microvibrations, which are reduced when it touches a static object. When a finger moves along a surface, the friction between them produces vibrations, which can not be produced with a free-moving finger in the air. With an inertial measurement unit (IMU) capturing such motion characteristics, we demonstrate the feasibility to detect contact between the finger and static objects. We call our technique ActualTouch. Studies show that a single nail-mounted IMU on the index finger provides sufficient data to train a binary touch status classifier (i.e., touch vs. no-touch), with an accuracy above 95%, generalised across users. This model, trained on a rigid tabletop surface, was found to retain an average accuracy of 96% for 7 other types of everyday surfaces with varying rigidity, and in walking and sitting scenarios where no touch occurred. ActualTouch can be combined with other interaction techniques, such as in a uni-stroke gesture recogniser on arbitrary surfaces, where touch status from ActualTouch is used to delimit the motion gesture data that feed into the recogniser. We demonstrate the potential of ActualTouch in a range of scenarios, such as interaction for augmented reality applications, and leveraging daily surfaces and objects for ad-hoc interactions.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397309"}, {"primary_key": "2427380", "vector": [], "sparse_vector": [], "title": "MM-Fit: Multimodal Deep Learning for Automatic Exercise Logging across Sensing Devices.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Fitness tracking devices have risen in popularity in recent years, but limitations in terms of their accuracy and failure to track many common exercises presents a need for improved fitness tracking solutions. This work proposes a multimodal deep learning approach to leverage multiple data sources for robust and accurate activity segmentation, exercise recognition and repetition counting. For this, we introduce the MM-Fit dataset; a substantial collection of inertial sensor data from smartphones, smartwatches and earbuds worn by participants while performing full-body workouts, and time-synchronised multi-viewpoint RGB-D video, with 2D and 3D pose estimates. We establish a strong baseline for activity segmentation and exercise recognition on the MM-Fit dataset, and demonstrate the effectiveness of our CNN-based architecture at extracting modality-specific spatial temporal features from inertial sensor and skeleton sequence data. We compare the performance of unimodal and multimodal models for activity recognition across a number of sensing devices and modalities. Furthermore, we demonstrate the effectiveness of multimodal deep learning at learning cross-modal representations for activity recognition, which achieves 96% accuracy across all sensing modalities on unseen subjects in the MM-Fit dataset; 94% using data from the smartwatch only; 85% from the smartphone only; and 82% on data from the earbud device. We strengthen single-device performance by using the zeroing-out training strategy, which phases out the other sensing modalities. Finally, we implement and evaluate a strong repetition counting baseline on our MM-Fit dataset. Collectively, these tasks contribute to recognising, segmenting and timing exercise and non-exercise activities for automatic exercise logging.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432701"}, {"primary_key": "2427381", "vector": [], "sparse_vector": [], "title": "VibroSense: Recognizing Home Activities by Deep Learning Subtle Vibrations on an Interior Surface of a House from a Single Point Using Laser Doppler Vibrometry.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Smart homes of the future are envisioned to have the ability to recognize many types of home activities such as running a washing machine, flushing the toilet, and using a microwave. In this paper, we present a new sensing technology, VibroSense, which is able to recognize 18 different types of activities throughout a house by observing structural vibration patterns on a wall or ceiling using a laser Doppler vibrometer. The received vibration data is processed and sent to a deep neural network which is trained to distinguish between 18 activities. We conducted a system evaluation, where we collected data of 18 home activities in 5 different houses for 2 days in each house. The results demonstrated that our system can recognize 18 home activities with an average accuracy of up to 96.6%. After re-setup of the device on the second day, the average recognition accuracy decreased to 89.4%. We also conducted follow-up experiments, where we evaluated VibroSense under various scenarios to simulate real-world conditions. These included simulating online recognition, differentiating between specific stages of a device's activity, and testing the effects of shifting the laser's position during re-setup. Based on these results, we discuss the opportunities and challenges of applying VibroSense in real-world applications.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411828"}, {"primary_key": "2427382", "vector": [], "sparse_vector": [], "title": "GAN-based Style Transformation to Improve Gesture-recognition Accuracy.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Gesture recognition and human-activity recognition from multi-channel sensory data are important tasks in wearable and ubiquitous computing. In these tasks, increasing both the number of recognizable activity classes and the recognition accuracy is essential. However, this is usually an ill-posed problem because individual differences in the same gesture class may affect the discrimination of different gesture classes. One promising solution is to use personal classifiers, but this requires personal gesture samples for re-training the classifiers. We propose a method of solving this issue that obtains personal gesture classifiers using few user gesture samples, thus, achieving accurate gesture recognition for an increased number of gesture classes without requiring extensive user calibration. The novelty of our method is introducing a generative adversarial network (GAN)-based style transformer to 'generate' a user's gesture data. The method synthesizes the gesture examples of the target class of a target user by transforming of a) gesture data into another class of the same user (intra-user transformation) or b) gesture data of the same class of another user (inter-user transformation). The synthesized data are then used to train the personal gesture classifier. We conducted comprehensive experiments using 1) different classifiers including SVM and CNN, 2) intra- and inter-user transformations, 3) various data-missing patterns, and 4) two different types of sensory data. Results showed that the proposed method had an increased performance. Specially, the CNN-based classifiers increased in average accuracy from 0.747 to 0.822 in the CheekInput dataset and from 0.856 to 0.899 in the USC-HAD dataset. Moreover, the experimental results with various data-missing conditions revealed a relation between the number of missing gesture classes and the accuracy of the existing and proposed methods, and we were able to clarify several advantages of the proposed method. These results indicate the potential of considerably reducing the number of required training samples of target users.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432199"}, {"primary_key": "2427385", "vector": [], "sparse_vector": [], "title": "Leveraging Polarization of WiFi Signals to Simultaneously Track Multiple People.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper presents WiPolar, an approach that simultaneously tracks multiple people using commodity WiFi devices. While two recent papers have also demonstrated multi-person tracking using commodity devices, they either require the people to continuously keep moving without stopping, and/or require the number of people to be input manually, and/or keep the WiFi devices from performing their primary function of data communication. Motivated by the increasing availability of polarized antennas on modern WiFi devices, WiPolar leverages signal polarization to perform accurate multi-person tracking using commodity devices while addressing the three limitations of prior work mentioned above. The key insight that WiPolar is based on is that different people expose different instantaneous horizontal and vertical radar cross-sections to WiFi transmitters due to differences in their physiques and orientations with respect to the transmitter. This enables WiPolar to accurately separate the multipaths reflected from different people, which, in turn, allows it to track them simultaneously. To the best of our knowledge, this is the first work that leverages polarization of WiFi signals to localize and track people. We implement WiPolar using commodity WiFi devices and extensively evaluate it for tracking up to five people in three different environments. Our results show that WiPolar achieved a median tracking error of just 56cm across all experiments. It also accurately tracks people even when they were not moving. WiPolar achieved a median tracking error of 74cm for people that were either stationary or just taking a small pause.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397317"}, {"primary_key": "2427386", "vector": [], "sparse_vector": [], "title": "Rataplan: Resilient Automation of User Interface Actions with Multi-modal Proxies.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present Rataplan, a robust and resilient pixel-based approach for linking multi-modal proxies to automated sequences of actions in graphical user interfaces (GUIs). With Rataplan, users demonstrate a sequence of actions and answer human-readable follow-up questions to clarify their desire for automation. After demonstrating a sequence, the user can link a proxy input control to the action which can then be used as a shortcut for automating a sequence. Alternatively, output proxies use a notification model in which content is pushed when it becomes available. As an example use case, Rataplan uses keyboard shortcuts and tangible user interfaces (TUIs) as input proxies, and TUIs as output proxies. Instead of relying on available APIs, Rataplan automates GUIs using pixel-based reverse engineering. This ensures our approach can be used with all applications that offer a GUI, including web applications. We implemented a set of important strategies to support robust automation of modern interfaces that have a flat and minimal style, have frequent data and state changes, and have dynamic viewports.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397329"}, {"primary_key": "2427388", "vector": [], "sparse_vector": [], "title": "Will Online Digital Footprints Reveal Your Relationship Status?: An Empirical Study of Social Applications for Sexual-Minority Men.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhang", "<PERSON>", "Qin Lv"], "summary": "With the increasing social acceptance and openness, more and more sexual-minority men (SMM) have succeeded in creating and sustaining steady relationships in recent years. Maintaining steady relationships is beneficial to the wellbeing of SMM both mentally and physically. However, the relationship maintaining for them is also challenging due to the much less supports compared to the heterosexual couples, so that it is important to identify those SMM in steady relationship and provide corresponding personalized assistance. Furthermore, knowing SMM's relationship and the correlations with other visible features is also beneficial for optimizing the social applications' functionalities in terms of privacy preserving and friends recommendation. With the prevalence of SMM-oriented social apps (called SMMSA for short), this paper investigates the relationship status of SMM from a new perspective, that is, by introducing the SMM's online digital footprints left on SMMSA (e.g., presented profile, social interactions, expressions, sentiment, and mobility trajectories). Specifically, using a filtered dataset containing 2,359 active SMMSA users with their self-reported relationship status and publicly available app usage data, we explore the correlations between SMM's relationship status and their online digital footprints on SMMSA and present a set of interesting findings. Moreover, we demonstrate that by utilizing such correlations, it has the potential to construct machine-learning-based models for relationship status inference. Finally, we elaborate on the implications of our findings from the perspective of better understanding the SMM community and improving their social welfare.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3380978"}, {"primary_key": "2427389", "vector": [], "sparse_vector": [], "title": "MorphingCircuit: An Integrated Design, Simulation, and Fabrication Workflow for Self-morphing Electronics.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Manufacturing nonplanar electronics often requires the integration of functions and forms through embedding circuit boards into three-dimensional (3D) shapes. While most popular solutions rely on cavities where electronics reside in forms of rigid circuit boards, other alternative approaches leverage 3D printing or layer lamination to create 3D electronics that often require expensive manufacturing processes and materials. Furthermore, many conventional methods are incompatible with complex geometries (e.g., surfaces that twist or have local minima). In response, we introduce MorphingCircuit, an integrated design, simulation, and fabrication workflow that combines electronic functions with forms through four-dimensional (4D) printing, which effectively reduces cost, production time, and e-waste. Specifically, we start by printing a flat substrate and assembling functional electronics on top of it. The flat structure will then self-morph into a preprogrammed 3D shape when triggered by external heating. Overall, our comprehensive 3D electronics fabrication pipeline encompasses the design, simulation, fabrication, and transformation, with which we hope to inspire designers, researchers, and makers to create conformal electronics on complex substrate geometries that were previously difficult or impossible to design or manufacture.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432232"}, {"primary_key": "2427390", "vector": [], "sparse_vector": [], "title": "AssessBlocks: Exploring Toy Block Play Features for Assessing Stress in Young Children after Natural Disasters.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Natural disasters cause long-lasting mental health problems such as PTSD in children. Following the 2011 Earthquake and Tsunami in Japan, we witnessed a shift of toy block play behavior in young children who suffered from stress after the disaster. The behavior reflected their emotional responses to the traumatic event. In this paper, we explore the feasibility of using data captured from block-play to assess children's stress after a major natural disaster. We prototyped sets of sensor-embedded toy blocks, AssessBlocks, that automate quantitative play data acquisition. During a three-year period, the blocks were dispatched to fifty-two post-disaster children. Within a free play session, we captured block features, a child's playing behavior, and stress evaluated by several methods. The result from our analysis reveal correlations between block play features and stress measurements and show initial promise of using the effectiveness of using AssessBlocks to assess children's stress after a disaster. We provide detailed insights into the potential as well as the challenges of our approach and unique conditions. From these insights we summarize guidelines for future research in automated play assessment systems that support children's mental health.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3381016"}, {"primary_key": "2427392", "vector": [], "sparse_vector": [], "title": "FairCharge: A Data-Driven Fairness-Aware Charging Recommendation System for Large-Scale Electric Taxi Fleets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Our society is witnessing a rapid taxi electrification process. Compared to conventional gas taxis, a key drawback of electric taxis is their prolonged charging time, which potentially reduces drivers' daily operation time and income. In addition, insufficient charging stations, intensive charging peaks, and heuristic-based charging station choice of drivers also significantly decrease the charging efficiency of electric taxi charging networks. To improve the charging efficiency (e.g., reduce queuing time in stations) of electric taxi charging networks, in this paper, we design a fairness-aware Pareto efficient charging recommendation system called FairCharge, which aims to minimize the total charging idle time (traveling time + queuing time) in a fleet-oriented fashion combined with fairness constraints. Different from existing works, FairCharge considers fairness as a constraint to potentially achieve long-term social benefits. In addition, our FairCharge considers not only current charging requests, but also possible charging requests of other nearby electric taxis in a near-future duration. More importantly, we simulate and evaluate FairCharge with real-world streaming data from the Chinese city Shenzhen, including GPS data and transaction data from more than 16,400 electric taxis, coupled with the data of 117 charging stations, which constitute, to our knowledge, the largest electric taxi network in the world. The extensive experimental results show that our fairness-aware FairCharge effectively reduces queuing time and idle time of the Shenzhen electric taxi fleet by 80.2% and 67.7%, simultaneously.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3381003"}, {"primary_key": "2427393", "vector": [], "sparse_vector": [], "title": "Battery-Free Game Boy.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We present ENGAGE, the first battery-free, personal mobile gaming device powered by energy harvested from the gamer actions and sunlight. Our design implements a power failure resilient Nintendo Game Boy emulator that can run off-the-shelf classic Game Boy games like Tetris or Super Mario Land. This emulator is capable of intermittent operation by tracking memory usage, avoiding the need for always checkpointing all volatile memory, and decouples the game loop from user interface mechanics allowing for restoration after power failure. We build custom hardware that harvests energy from gamer button presses and sunlight, and leverages a mixed volatility memory architecture for efficient intermittent emulation of game binaries. Beyond a fun toy, our design represents the first battery-free system design for continuous user attention despite frequent power failures caused by intermittent energy harvesting. We tackle key challenges in intermittent computing for interaction including seamless displays and dynamic incentive-based gameplay for energy harvesting. This work provides a reference implementation and framework for a future of battery-free mobile gaming in a more sustainable Internet of Things.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411839"}, {"primary_key": "2427395", "vector": [], "sparse_vector": [], "title": "FingerDraw: Sub-wavelength Level Finger Motion Tracking with WiFi Signals.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON> Gu", "<PERSON><PERSON> Zhang"], "summary": "This paper explores the possibility of tracking finger drawings in the air leveraging WiFi signals from commodity devices. Prior solutions typically require user to hold a wireless transmitter, or need proprietary wireless hardware. They can only recognize a small set of pre-defined hand gestures. This paper introduces FingerDraw, the first sub-wavelength level finger motion tracking system using commodity WiFi devices, without attaching any sensor to finger. FingerDraw can reconstruct finger drawing trajectory such as digits, alphabets, and symbols with the setting of one WiFi transmitter and two WiFi receivers. It uses a two-antenna receiver to sense the sub-wavelength scale displacement of finger motion in each direction. The theoretical underpinning of FingerDraw is our proposed CSI-quotient model, which uses the channel quotient between two antennas of the receiver to cancel out the noise in CSI amplitude and the random offsets in CSI phase, and quantifies the correlation between CSI value dynamics and object displacement. This channel quotient is sensitive to and enables us to detect small changes in In-phase and Quadrature parts of channel state information due to finger movement. Our experimental results show that the overall median tracking accuracy is 1.27 cm, and the recognition of drawing ten digits in the air achieves an average accuracy of over 93.0%.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3380981"}, {"primary_key": "2427396", "vector": [], "sparse_vector": [], "title": "Auto-Key: Using Autoencoder to Speed Up Gait-based Key Generation in Body Area Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "With the rising popularity of wearable devices and sensors, shielding Body Area Networks (BANs) from eavesdroppers has become an urgent problem to solve. Since the conventional key distribution systems are too onerous for resource-constrained wearable sensors, researchers are pursuing a new light-weight key generation approach that enables two wearable devices attached at different locations of the user body to generate an identical key simultaneously simply from their independent observations of user gait. A key challenge for such gait-based key generation lies in matching the bits of the keys generated by independent devices despite the noisy sensor measurements, especially when the devices are located far apart on the body affected by different sources of noise. To address the challenge, we propose a novel machine learning framework, called Auto-Key, that uses an autoencoder to help one device predict the gait observations at another distant device attached to the same body and generate the key using the predicted sensor data. We prototype the proposed method and evaluate it using a public acceleration dataset collected from 15 real subjects wearing accelerometers attached to seven different locations of the body. Our results show that, on average, Auto-Key increases the matching rate of independently generated bits from two sensors attached at two different locations by 16.5%, which speeds up the successful generation of fully-matching symmetric keys at independent wearable sensors by a factor of 1.9. In the proposed framework, a subject-specific model can be trained with 50% fewer data and 88% less time by retraining a pre-trained general model when compared to training a new model from scratch. The reduced training complexity makes Auto-Key more practical for edge computing, which provides better privacy protection to biometric and behavioral data compared to cloud-based training.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3381004"}, {"primary_key": "2427398", "vector": [], "sparse_vector": [], "title": "RISC: Resource-Constrained Urban Sensing Task Scheduling Based on Commercial Fleets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the trend of vehicles becoming increasingly connected and potentially autonomous, vehicles are being equipped with rich sensing and communication devices. Various vehicular services based on shared real-time sensor data of vehicles from a fleet have been proposed to improve the urban efficiency, e.g., HD-live map, and traffic accident recovery. However, due to the high cost of data uploading (e.g., monthly fees for a cellular network), it would be impractical to make all well-equipped vehicles to upload real-time sensor data constantly. To better utilize these limited uploading resources and achieve an optimal road segment sensing coverage, we present a real-time sensing task scheduling framework, i.e., RISC, for Resource-Constraint modeling for urban sensing by scheduling sensing tasks of commercial vehicles with sensors based on the predictability of vehicles' mobility patterns. In particular, we utilize the commercial vehicles, including taxicabs, buses, and logistics trucks as mobile sensors to sense urban phenomena, e.g., traffic, by using the equipped vehicular sensors, e.g., dash-cam, lidar, automotive radar, etc. We implement RISC on a Chinese city Shenzhen with one-month real-world data from (i) a taxi fleet with 14 thousand vehicles; (ii) a bus fleet with 13 thousand vehicles; (iii) a truck fleet with 4 thousand vehicles. Further, we design an application, i.e., track suspect vehicles (e.g., hit-and-run vehicles), to evaluate the performance of RISC on the urban sensing aspect based on the data from a regular vehicle (i.e., personal car) fleet with 11 thousand vehicles. The evaluation results show that compared to the state-of-the-art solutions, we improved sensing coverage (i.e., the number of road segments covered by sensing vehicles) by 10% on average.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397337"}, {"primary_key": "2427401", "vector": [], "sparse_vector": [], "title": "Virtual Paving: Rendering a Smooth Path for People with Visual Impairment through Vibrotactile and Audio Feedback.", "authors": ["<PERSON><PERSON> Xu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Tactile pavings are public works for visually impaired people, designed to indicate a particular path to follow by providing haptic cues underfoot. However, they face many limitations such as installation errors, obstructions, degradation, and limited coverage. To address these issues, we propose Virtual Paving, which aims to assist independent navigation by rendering a smooth path to visually impaired people through multi-modal feedback. This work assumes that a path has been planned to avoid obstacles and focuses on the feedback design to guide users along the path safely, smoothly, and efficiently. Firstly, we extracted the design guidelines of Virtual Paving based on an investigation into visually impaired people's current practices and issues with tactile pavings. Next, we developed a multi-modal solution through co-design and evaluation with visually impaired users. This solution included (1) vibrotactile feedback on the shoulders and waist to give readily-perceivable directional cues and (2) audio feedback to describe road conditions ahead of the user. Finally, we evaluated the proposed solution through user tests. Guided by the designed feedback, 16 visually impaired participants successfully completed 127 out of 128 trials with 2.1m-wide basic paths, including straight and curved paths. Subjective feedback indicated that our solution to render Virtual Paving was easy for users to learn, and it also enabled them to walk smoothly. The feasibility and potential limitations for Virtual Paving to support independent navigation in real environments are discussed.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411814"}, {"primary_key": "2427402", "vector": [], "sparse_vector": [], "title": "DeepMV: Multi-View Deep Learning for Device-Free Human Activity Recognition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Lu <PERSON>"], "summary": "Recently, significant efforts are made to explore device-free human activity recognition techniques that utilize the information collected by existing indoor wireless infrastructures without the need for the monitored subject to carry a dedicated device. Most of the existing work, however, focuses their attention on the analysis of the signal received by a single device. In practice, there are usually multiple devices \"observing\" the same subject. Each of these devices can be regarded as an information source and provides us an unique \"view\" of the observed subject. Intuitively, if we can combine the complementary information carried by the multiple views, we will be able to improve the activity recognition accuracy. Towards this end, we propose DeepMV, a unified multi-view deep learning framework, to learn informative representations of heterogeneous device-free data. DeepMV can combine different views' information weighted by the quality of their data and extract commonness shared across different environments to improve the recognition performance. To evaluate the proposed DeepMV model, we set up a testbed using commercialized WiFi and acoustic devices. Experiment results show that DeepMV can effectively recognize activities and outperform the state-of-the-art human activity recognition methods.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3380980"}, {"primary_key": "2427403", "vector": [], "sparse_vector": [], "title": "Toward Lightweight In-situ Self-reporting: An Exploratory Study of Alternative Smartwatch Interface Designs in Context.", "authors": ["<PERSON><PERSON><PERSON> (Erica) Yan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sun Young Park", "<PERSON>"], "summary": "In-situ self-reporting is an important measurement method used for capturing daily experience data right-in-the-moment in dynamic contexts. Research has been conducted to reduce the demand placed on users for manually reporting data in context. In this regard, smartwatches offer inherent benefits for making self-reporting more convenient and facilitate data gathering. However, self-reporting on the small touchscreen under various contextual conditions can be burdensome and challenging. In this study, to gain insights into designing smartwatch-based self-report interfaces, we conducted an exploratory user study with eight design probes and twenty-four participants under three simulated scenarios: walking, gaming, and social chatting. Findings showed that users' subjective perception of interface features (e.g., input methods and option layouts) varied with changes in context. Participants leveraged different features (e.g., hierarchical layout and discrete input) to micro-schedule self-report tasks (i.e., create one or multiple opportune moments) or to conduct eyes-free interaction with the assistance of smartwatch attributes (e.g., the physical frame of a smartwatch). We discuss implications for smartwatch-based self-report interface designs by considering context and designing interface features to support users' coping strategies.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432212"}, {"primary_key": "2427404", "vector": [], "sparse_vector": [], "title": "HeadCross: Exploring Head-Based Crossing Selection on Head-Mounted Displays.", "authors": ["Yukang Yan", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose HeadCross, a head-based interaction method to select targets on VR and AR head-mounted displays (HMD). Using HeadCross, users control the pointer with head movements and to select a target, users move the pointer into the target and then back across the target boundary. In this way, users can select targets without using their hands, which is helpful when users' hands are occupied by other tasks, e.g., while holding the handrails. However, a major challenge for head-based methods is the false positive problems: unintentional head movements may be incorrectly recognized as Head<PERSON><PERSON> gestures and trigger the selections. To address this issue, we first conduct a user study (Study 1) to observe user behavior while performing HeadCross and identify the behavior differences between Head<PERSON>ross and other types of head movements. Based on the results, we discuss design implications, extract useful features, and develop the recognition algorithm for HeadCross. To evaluate Head<PERSON><PERSON>, we conduct two user studies. In Study 2, we compared HeadCross to the dwell-based selection method, button-press method, and mid-air gesture-based method. Two typical target selection tasks (text entry and menu selection) are tested on both VR and AR interfaces. Results showed that compared to the dwell-based method, Head<PERSON><PERSON> improved the sense of control; and compared to two hand-based methods, HeadC<PERSON> improved the interaction efficiency and reduced fatigue. In Study 3, we compared HeadCross to three alternative designs of head-only selection methods. Results show that HeadCross was perceived to be significantly faster than the alternatives. We conclude with the discussion on the interaction potential and limitations of HeadCross.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3380983"}, {"primary_key": "2427406", "vector": [], "sparse_vector": [], "title": "How Subtle Can It Get?: A Trimodal Study of Ring-sized Interfaces for One-Handed Drone Control.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pan Hui"], "summary": "Flying drones have become common objects in our daily lives, serving a multitude of purposes. Many of these purposes involve outdoor scenarios where the user combines drone control with another activity. Traditional interaction methods rely on physical or virtual joysticks that occupy both hands, thus restricting drone usability. In this paper, we investigate one-handed human-to-drone-interaction by leveraging three modalities: force, touch, and IMU. After prototyping three different combinations of these modalities on a smartphone, we evaluate them against the current commercial standard through two user experiments. These experiments help us to find the combination of modalities that strikes a compromise between user performance, perceived task load, wrist rotation, and interaction area size. Accordingly, we select a method that achieves faster task completion times than the two-handed commercial baseline by 16.54% with the merits of subtle user behaviours inside a small-size ring-form device and implements this method within the ring-form device. The last experiment involving 12 participants shows that thanks to its small size and weight, the ring device displays better performance than the same method implemented on a mobile phone. Furthermore, users unanimously found the device useful for controlling a drone in mobile scenarios (AVG = 3.92/5), easy to use (AVG = 3.58/5) and easy to learn (AVG = 3.58/5). Our findings give significant design clues in search of subtle and effective interaction through finger augmentation devices with drone control. The users with our prototypical system and a multi-modal on-finger device can control a drone with subtle wrist rotation (pitch gestures: 43.24° amplitude and roll gestures: 46.35° amplitude) and unnoticeable thumb presses within a miniature-sized area of (1.08 * 0.61 cm2).", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397319"}, {"primary_key": "2427407", "vector": [], "sparse_vector": [], "title": "Learning to Recognize Handwriting Input with Acoustic Features.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Guangyuan Su", "<PERSON>", "<PERSON>", "Huadong Ma"], "summary": "For mobile or wearable devices with a small touchscreen, handwriting input (instead of typing on the touchscreen) is highly desirable for efficient human-computer interaction. Previous passive acoustic-based handwriting solutions mainly focus on print-style capital input, which is inconsistent with people's daily habits and thus causes inconvenience. In this paper, we propose WritingRecorder, a novel universal text entry system that enables free-style lowercase handwriting recognition. WritingRecorder leverages the built-in microphone of the smartphones to record the handwritten sound, and then designs an adaptive segmentation method to detect letter fragments in real-time from the recorded sound. Then we design a neural network named Inception-LSTM to extract the hidden and unique acoustic pattern associated with the writing trajectory of each letter and thus classify each letter. Moreover, we adopt a word selection method based on language model, so as to recognize legislate words from all possible letter combinations. We implement WritingRecorder as an APP on mobile phones and conduct the extensive experimental evaluation. The results demonstrate that WritingRecorder works in real-time and can achieve 93.2% accuracy even for new users without collecting and training on their handwriting samples, under a series of practical scenarios.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397334"}, {"primary_key": "2427409", "vector": [], "sparse_vector": [], "title": "Semantic-aware Spatio-temporal App Usage Representation via Graph Convolutional Network.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Recent years have witnessed a rapid proliferation of personalized mobile Apps, which poses a pressing need for user experience improvement. A promising solution is to model App usage by learning semantic-aware App usage representations which can capture the relation among time, locations and Apps. However, it is non-trivial due to the complexity, dynamics, and heterogeneity characteristics of App usage. To smooth over these obstacles and achieve the goal, we propose SA-GCN, a novel representation learning model to map Apps, location, and time units into dense embedding vectors considering spatio-temporal characteristics and unit properties simultaneously. To handle complexity and dynamics, we build an App usage graph by regarding App, time, and location units as nodes and their co-occurrence relations as edges. For heterogeneity, we develop a Graph Convolutional Network with meta path-based objective function to combine the structure of the graph and the attribute of units into the semantic-aware representations. We evaluate the performance of SA-GCN via a large-scale real-world dataset. In-depth analysis shows that SA-GCN characterizes the complex relationships among different units and recover meaningful spatio-temporal patterns. Moreover, we make use of the learned representations in App usage prediction task without post-training and achieve 8.3% of the performance gain compared with state-of-the-art baselines.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411817"}, {"primary_key": "2427410", "vector": [], "sparse_vector": [], "title": "BodyCompass: Monitoring Sleep Posture with Wireless Signals.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Monitoring sleep posture is important for avoiding bedsores after surgery, reducing apnea events, tracking the progression of <PERSON>'s disease, and even alerting epilepsy patients to potentially fatal sleep postures. Today, there is no easy way to track sleep postures. Past work has proposed installing cameras in the bedroom, mounting accelerometers on the subject's chest, or embedding pressure sensors in their bedsheets. Unfortunately, such solutions jeopardize either the privacy of the user or their sleep comfort. In this paper, we introduce BodyCompass, the first RF-based system that provides accurate sleep posture monitoring overnight in the user's own home. BodyCompass works by studying the RF reflections in the environment. It disentangles RF signals that bounced off the subject's body from other multipath signals. It then analyzes those signals via a custom machine learning algorithm to infer the subject's sleep posture. BodyCompass is easily transferable and can apply to new homes and users with minimal effort. We empirically evaluate BodyCompass using over 200 nights of sleep data from 26 subjects in their own homes. Our results show that, given one week, one night, or 16 minutes of labeled data from the subject, BodyCompass's corresponding accuracy is 94%, 87%, and 84%, respectively.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397311"}, {"primary_key": "2427411", "vector": [], "sparse_vector": [], "title": "Evaluating the Gradual Delivery of Knowledge-focused and Mindset-focused Messages for Facilitating the Acceptance of COPD.", "authors": ["<PERSON>", "<PERSON>", "Khai N. <PERSON>", "<PERSON>"], "summary": "Chronic Obstructive Pulmonary Disease (COPD) is a terminal, progressive lung condition which mainly affects older adults. The onset of symptoms, obstacles, and impairments brought about by COPD often necessitates a grieving process for patients. Acceptance is the stage of the grieving process in which the patient has healthily integrated the condition into his or her lifestyle and identity. Because of the progressive nature of COPD, the process of acceptance is a perpetual journey in which patients must continuously shift their mindsets and lifestyles to adapt to the increasing severity of the condition. Using the health belief model as a theoretical foundation, we explore the usage of daily automated SMS messages as an engaging and accessible means of facilitating and maintaining a patient's acceptance of COPD. The results of our investigation show that SMS messages serve as an effective tool for improving patients' acceptance of COPD while also reducing patients' proclivities to the nonacceptance stages of the grieving process.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432218"}, {"primary_key": "2427412", "vector": [], "sparse_vector": [], "title": "MultiSense: Enabling Multi-person Respiration Sensing with Commodity WiFi.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "In recent years, we have seen efforts made to simultaneously monitor the respiration of multiple persons based on the channel state information (CSI) retrieved from commodity WiFi devices. Existing approaches mainly rely on spectral analysis of the CSI amplitude to obtain respiration rate information, leading to multiple limitations: (1) spectral analysis works when multiple persons exhibit dramatically different respiration rates, however, it fails to resolve similar rates; (2) spectral analysis can only obtain the average respiration rate over a period of time, and it is unable to capture the detailed rate change over time; (3) they fail to sense the respiration when a target is located at the \"blind spots\" even the target is close to the sensing devices. To overcome these limitations, we propose MultiSense, the first WiFi-based system that can robustly and continuously sense the detailed respiration patterns of multiple persons even they have very similar respiration rates and are physically closely located. The key insight of our solution is that the commodity WiFi hardware nowadays is usually equipped with multiple antennas. Thus, each individual antenna can receive a different mix copy of signals reflected from multiple persons. We successfully prove that the reflected signals are linearly mixed at each antenna and propose to model the multi-person respiration sensing as a blind source separation (BSS) problem. Then, we solve it using independent component analysis (ICA) to separate the mixed signal and obtain the reparation information of each person. Extensive experiments show that with only one pair of transceivers, each equipped with three antennas, MultiSense is able to accurately monitor respiration even in the presence of four persons, with the mean absolute respiration rate error of 0.73 bpm (breaths per minute).", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411816"}, {"primary_key": "2427413", "vector": [], "sparse_vector": [], "title": "Making Sense of Sleep: Multimodal Sleep Stage Classification in a Large, Diverse Population Using Movement and Cardiac Sensing.", "authors": ["<PERSON>", "<PERSON>", "Emma <PERSON><PERSON>", "<PERSON><PERSON>", "Yu <PERSON>"], "summary": "Traditionally, sleep monitoring has been performed in hospital or clinic environments, requiring complex and expensive equipment set-up and expert scoring. Wearable devices increasingly provide a viable alternative for sleep monitoring and are able to collect movement and heart rate (HR) data. In this work, we present a set of algorithms for sleep-wake and sleep-stage classification based upon actigraphy and cardiac sensing amongst 1,743 participants. We devise movement and cardiac features that could be extracted from research-grade wearable sensors and derive models and evaluate their performance in the largest open-access dataset for human sleep science. Our results demonstrated that neural network models outperform traditional machine learning methods and heuristic models for both sleep-wake and sleep-stage classification. Convolutional neural networks (CNNs) and long-short term memory (LSTM) networks were the best performers for sleep-wake and sleep-stage classification, respectively. Using SHAP (SHapley Additive exPlanation) with Random Forest we identified that frequency features from cardiac sensors are critical to sleep-stage classification. Finally, we introduced an ensemble-based approach to sleep-stage classification, which outperformed all other baselines, achieving an accuracy of 78.2% and F1 score of 69.8% on the classification task for three sleep stages. Together, this work represents the first systematic multimodal evaluation of sleep-wake and sleep-stage classification in a large, diverse population. Alongside the presentation of an accurate sleep-stage classification approach, the results highlight multimodal wearable sensing approaches as scalable methods for accurate sleep-classification, providing guidance on optimal algorithm deployment for automated sleep assessment. The code used in this study can be found online at: https://github.com/bzhai/multimodal_sleep_stage_benchmark.git", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397325"}, {"primary_key": "2427414", "vector": [], "sparse_vector": [], "title": "Real-time Travel Time Estimation with Sparse Reliable Surveillance Information.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Chuancai Ge", "<PERSON><PERSON><PERSON>"], "summary": "Origin-destination (OD) travel time estimation is of paramount importance for applications such as intelligent transportation. In this work, we propose a new solution for OD travel time estimation, with road surveillance camera data. The surveillance information supports accurate and reliable observations at camera-equipped intersections, but is associated with missing and incomplete surveillance records at the camera-free intersections. To overcome this, we propose a modified version of multi-layer graph convolutional networks. The camera surveillance data is used to extract the traffic flow of each intersection, the extracted information serves as the input of the multi-layer GCN based model, based on which the real-time traffic status can be predicted. To enhance the estimation accuracy, we address the effects of various features for the travel time estimation with encoder-decoder networks and embedding techniques. We further improve the generalization of our model by using multi-task learning. Extensive experiments on real datasets are done to verify the effectiveness of our proposals.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3380988"}, {"primary_key": "2427415", "vector": [], "sparse_vector": [], "title": "Peer-to-Peer Localization for Single-Antenna Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Some important indoor localization applications, such as localizing a lost kid in a shopping mall, call for a new peer-to-peer localization technique that can localize an individual's smartphone or wearables by directly using another's on-body devices in unknown indoor environments. However, current localization solutions either require pre-deployed infrastructures or multiple antennas in both transceivers, impending their wide-scale application. In this paper, we present P2PLocate, a peer-to-peer localization system that enables a single-antenna device co-located with a batteryless backscatter tag to localize another single-antenna device with decimeter-level accuracy. P2PLocate leverages the multipath variations intentionally created by an on-body backscatter tag, coupled with spatial information offered by user movements, to accomplish this objective without relying on any pre-deployed infrastructures or pre-training. P2PLocate incorporates novel algorithms to address two major challenges: (i) interference with strong direct-path signal while extracting multipath variations, and (ii) lack of direction information while using single-antenna transceivers. We implement P2PLocate on commercial off-the-shelf Google Nexus 6p, Intel 5300 WiFi card, and Raspberry Pi B4. Real-world experiments reveal that P2PLocate can localize both static and mobile targets with a median accuracy of 0.88 m.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411833"}, {"primary_key": "2427416", "vector": [], "sparse_vector": [], "title": "Exploring LoRa for Long-range Through-wall Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Qin Lv", "<PERSON><PERSON> Zhang"], "summary": "Wireless signals have been extensively utilized for contactless sensing in the past few years. Due to the intrinsic nature of employing the weak target-reflected signal for sensing, the sensing range is limited. For instance, WiFi and RFID can achieve 3-6 meter sensing range while acoustic-based sensing is limited to less than one meter. In this work, we identify exciting sensing opportunities with LoRa, which is the new long-range communication technology designed for IoT communication. We explore the sensing capability of LoRa, both theoretically and experimentally. We develop the sensing model to characterize the relationship between target movement and signal variation, and propose novel techniques to increase LoRa sensing range to over 25 meters for human respiration sensing. We further build a prototype system which is capable of sensing both coarse-grained and fine-grained human activities. Experimental results show that (1) human respiration can still be sensed when the target is 25 meters away from the LoRa devices, and 15 meters away with a wall in between; and (2) human walking (both displacement and direction) can be tracked accurately even when the target is 30 meters away from the LoRa transceiver pair.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397326"}, {"primary_key": "2427417", "vector": [], "sparse_vector": [], "title": "Trace2TAP: Synthesizing Trigger-Action Programs from Traces of Behavior.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> U<PERSON>"], "summary": "Two common approaches for automating IoT smart spaces are having users write rules using trigger-action programming (TAP) or training machine learning models based on observed actions. In this paper, we unite these approaches. We introduce and evaluate Trace2TAP, a novel method for automatically synthesizing TAP rules from traces (time-stamped logs of sensor readings and manual actuations of devices). We present a novel algorithm that uses symbolic reasoning and SAT-solving to synthesize TAP rules from traces. Compared to prior approaches, our algorithm synthesizes generalizable rules more comprehensively and fully handles nuances like out-of-order events. Trace2TAP also iteratively proposes modified TAP rules when users manually revert automations. We implemented our approach on Samsung SmartThings. Through formative deployments in ten offices, we developed a clustering/ranking system and visualization interface to intelligibly present the synthesized rules to users. We evaluated Trace2TAP through a field study in seven additional offices. Participants frequently selected rules ranked highly by our clustering/ranking system. Participants varied in their automation priorities, and they sometimes chose rules that would seem less desirable by traditional metrics like precision and recall. Trace2TAP supports these differing priorities by comprehensively synthesizing TAP rules and bringing humans into the loop during automation.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3411838"}, {"primary_key": "2427418", "vector": [], "sparse_vector": [], "title": "Endophasia: Utilizing Acoustic-Based Imaging for Issuing Contact-Free Silent Speech Commands.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Using silent speech to issue commands has received growing attention, as users can utilize existing command sets from voice-based interfaces without attracting other people's attention. Such interaction maintains privacy and social acceptance from others. However, current solutions for recognizing silent speech mainly rely on camera-based data or attaching sensors to the throat. Camera-based solutions require 5.82 times larger power consumption or have potential privacy issues; attaching sensors to the throat is not practical for commercial-off-the-shell (COTS) devices because additional sensors are required. In this paper, we propose a sensing technique that only needs a microphone and a speaker on COTS devices, which not only consumes little power but also has fewer privacy concerns. By deconstructing the received acoustic signals, a 2D motion profile can be generated. We propose a classifier based on convolutional neural networks (CNN) to identify the corresponding silent command from the 2D motion profiles. The proposed classifier can adapt to users and is robust when tested by environmental factors. Our evaluation shows that the system achieves 92.5% accuracy in classifying 20 commands.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3381008"}, {"primary_key": "2427419", "vector": [], "sparse_vector": [], "title": "Towards Real-time Cooperative Deep Inference over the Cloud and Edge End Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Deep neural networks (DNNs) have been widely used in many intelligent applications such as object recognition and automatic driving due to their superior performance in conducting inference tasks. However, DNN models are usually heavyweight in computation, hindering their utilization on the resource-constraint Internet of Things (IoT) end devices. To this end, cooperative deep inference is proposed, in which a DNN model is adaptively partitioned into two parts and different parts are executed on different devices (cloud or edge end devices) to minimize the total inference latency. One important issue is thus to find the optimal partition of the deep model subject to network dynamics in a real-time manner. In this paper, we formulate the optimal DNN partition as a min-cut problem in a directed acyclic graph (DAG) specially derived from the DNN and propose a novel two-stage approach named quick deep model partition (QDMP) to solve it. QDMP exploits the fact that the optimal partition of a DNN model must be between two adjacent cut vertices in the corresponding DAG. It first identifies the two cut vertices and considers only the subgraph in between when calculating the min-cut. QDMP can find the optimal model partition with response time less than 300ms even for large DNN models containing hundreds of layers (up to 66.3x faster than the state-of-the-art solution), and thus enables real-time cooperative deep inference over the cloud and edge end devices. Moreover, we observe one important fact that is ignored in all previous works: As many deep learning frameworks optimize the execution of DNN models, the execution latency of a series of layers in a DNN does not equal to the summation of each layer's independent execution latency. This results in inaccurate inference latency estimation in existing works. We propose a new execution latency measurement method, with which the inference latency can be accurately estimated in practice. We implement QDMP on real hardware and use a real-world self-driving car video dataset to evaluate its performance. Experimental results show that QDMP outperforms the state-of-the-art solution, reducing inference latency by up to 1.69x and increasing throughput by up to 3.81x.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397315"}, {"primary_key": "2427421", "vector": [], "sparse_vector": [], "title": "Your Smart Speaker Can &quot;Hear&quot; Your Heartbeat!", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "Vital sign monitoring is a common practice amongst medical professionals, and plays a key role in patient care and clinical diagnosis. Traditionally, dedicated equipment is employed to monitor these vital signs. For example, electrocardiograms (ECG) with 3-12 electrodes are attached to the target chest for heartbeat monitoring. In the last few years, wireless sensing becomes a hot research topic and wireless signal itself is utilized for sensing purposes without requiring the target to wear any sensors. The contact-free nature of wireless sensing makes it particularly appealing in current COVID-19 pandemic. Recently, promising progress has been achieved and the sensing granularity has been pushed to millimeter level, fine enough to monitor respiration which causes a chest displacement of 5 mm. While a great success with respiration monitoring, it is still very challenging to monitor heartbeat due to the extremely subtle chest displacement (0.1 - 0.5 mm) - smaller than 10% of that caused by respiration. What makes it worse is that the tiny heartbeat-caused chest displacement is buried inside the respiration-caused displacement. In this paper, we show the feasibility of employing the popular smart speakers (e.g., Amazon Echo) to monitor an individual's heartbeats in a contact-free manner. To extract the submillimeter heartbeat motion in the presence of other interference movements, a series of novel signal processing schemes are employed. We successfully prototype the first real-time heartbeat monitoring system using a commodity smart speaker. Experiment results show that the proposed system can monitor a target's heartbeat accurately, achieving a median heart rate estimation error of 0.75 beat per minute (bpm), and a median heartbeat interval estimation error of 13.28 ms (less than 1.8%), outperforming even some popular commodity products available on the market.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432237"}, {"primary_key": "2427424", "vector": [], "sparse_vector": [], "title": "NeckSense: A Multi-Sensor Necklace for Detecting Eating Activities in Free-Living Conditions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "D<PERSON>ng <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Nabil <PERSON>"], "summary": "We present the design, implementation, and evaluation of a multi-sensor, low-power necklace,", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397313"}, {"primary_key": "2427425", "vector": [], "sparse_vector": [], "title": "D2Park: Diversified Demand-aware On-street Parking Guidance.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Huadong Ma"], "summary": "To address the increasingly serious parking pain, numerous mobile Apps have emerged to help drivers to find a convenient parking spot with various auxiliary information. However, the phenomenon of \"multiple cars chasing the same spot\" still exists, especially for on-street parking. Existing reservation-based resource allocation solutions could address the parking competition issue to some extent, but it is impractical to treat all spots as reservable resources. This paper first conducts a qualitative investigation based on the online survey data, which identifies diversified parking requirements involving i) reserved users, who request guaranteed spots with a reservation fee, ii) normal users, who request non-guaranteed spots with a \"best-effort\" service, and iii) external users, who do not use any guidance service. To this end, we design the D2Park system for diversified demand-aware parking guidance services. We formulate the problem as a novel Heterogeneous-Agent Dynamic Resource Allocation (HADRA) problem, which considers both current and future parking demands, and different constraints for diversified requirements. Two main modules are used in the system: 1) multi-step parking prediction, which makes multi-step parking inflow and occupancy rate predictions given the current parking events data and external factors; and 2) diversified parking guidance, which integrates the cooperation-based and competition-based resource allocation mechanisms based on a model predictive control framework to achieve a better performance balance among different user groups. Extensive experiments with a four-month real-world on-street parking dataset from the Chinese city Shenzhen demonstrate the effectiveness and efficiency of D2Park.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432214"}, {"primary_key": "2427426", "vector": [], "sparse_vector": [], "title": "V2iFi: in-Vehicle Vital Sign Monitoring via Compact RF Sensing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Given the significant amount of time people spend in vehicles, health issues under driving condition have become a major concern. Such issues may vary from fatigue, asthma, stroke, to even heart attack, yet they can be adequately indicated by vital signs and abnormal activities. Therefore, in-vehicle vital sign monitoring can help us predict and hence prevent these issues. Whereas existing sensor-based (including camera) methods could be used to detect these indicators, privacy concern and system complexity both call for a convenient yet effective and robust alternative. This paper aims to develop V2iFi, an intelligent system performing monitoring tasks using a COTS impulse radio mounted on the windshield. V2iFi is capable of reliably detecting driver's vital signs under driving condition and with the presence of passengers, thus allowing for potentially inferring corresponding health issues. Compared with prior work based on Wi-Fi CSI, V2iFi is able to distinguish reflected signals from multiple users, and hence provide finer-grained measurements under more realistic settings. We evaluate V2iFi both in lab environments and during real-life road tests; the results demonstrate that respiratory rate, heart rate, and heart rate variability can all be estimated accurately. Based on these estimation results, we further discuss how machine learning models can be applied on top of V2iFi so as to improve both physiological and psychological wellbeing in driving environments.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3397321"}, {"primary_key": "2427427", "vector": [], "sparse_vector": [], "title": "Blin<PERSON>ey: A Two-Factor User Authentication Method for Virtual Reality Devices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Virtual Reality (VR) has shown promising potentials in many applications, such as e-business, healthcare, and social networking. Rich information regarding user's activities and their online accounts is stored in VR devices. If it is carelessly unattended, then attackers, including insiders, can make use of the stored information to, for example, perform in-app purchases at the legitimate owner's expenses. Current solutions, mostly following schemes designed for general personal devices, have been proved vulnerable to shoulder-surfing attacks due to the sight blocking caused by the headset. Although there have been efforts trying to fill this gap, they either rely on some highly advanced equipment, such as electrodes to read brainwaves, or introduce heavy cognitive load that has users perform a series of cumbersome authentication tasks. Therefore, an authentication method for VR devices that is robust and convenient is in dire need. In this paper, we present the design, implementation, and evaluation of a two-factor user authentication scheme, Blin<PERSON>ey, for VR devices that are equipped with an eye tracker. A user's secret passcode is a set of recorded rhythms when he/she blinks, together with the unique pupil size variation pattern. We call this passcode as a blinkey, which can be jointly characterized by knowledge-based and biometric features. To examine the performances, BlinKey is implemented on an HTC Vive Pro with a Pupil Labs eye tracker. Through extensive experimental evaluations with 52 participants, we show that our scheme can achieve the average EER as low as 4.0% with only 6 training samples. Besides, it is robust against various types of attacks. <PERSON><PERSON><PERSON><PERSON> also exhibits satisfactory usability in terms of login attempts, memorability, and impact of user motions. We also carry out questionnaire-based pre-/post-studies. The survey result indicates that BlinKey is well accepted as a user authentication scheme for VR devices.", "published": "2020-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432217"}]