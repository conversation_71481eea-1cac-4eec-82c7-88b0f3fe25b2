[{"primary_key": "3736376", "vector": [], "sparse_vector": [], "title": "Security and Machine Learning.", "authors": ["<PERSON>"], "summary": "Machine learning has seen increasing use for a wide range of practical applications. What are the security implications of relying upon machine learning in these settings? Recent research suggests that modern machine learning methods are fragile and easily attacked, which raises concerns about their use in security-critical settings. This talk will explore several attacks on machine learning and survey directions for making machine learning more robust against attack.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134108"}, {"primary_key": "3736378", "vector": [], "sparse_vector": [], "title": "walk2friends: Inferring Social Links from Mobility Profiles.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The development of positioning technologies has resulted in an increasing amount of mobility data being available. While bringing a lot of convenience to people's life, such availability also raises serious concerns about privacy. In this paper, we concentrate on one of the most sensitive information that can be inferred from mobility data, namely social relationships. We propose a novel social relation inference attack that relies on an advanced feature learning technique to automatically summarize users' mobility features. Compared to existing approaches, our attack is able to predict any two individuals' social relation, and it does not require the adversary to have any prior knowledge on existing social relations. These advantages significantly increase the applicability of our attack and the scope of the privacy assessment. Extensive experiments conducted on a large dataset demonstrate that our inference attack is effective, and achieves between 13% to 20% improvement over the best state-of-the-art scheme. We propose three defense mechanisms -- hiding, replacement and generalization -- and evaluate their effectiveness for mitigating the social link privacy risks stemming from mobility data sharing. Our experimental results show that both hiding and replacement mechanisms outperform generalization. Moreover, hiding and replacement achieve a comparable trade-off between utility and privacy, the former preserving better utility and the latter providing better privacy.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133972"}, {"primary_key": "3736379", "vector": [], "sparse_vector": [], "title": "Bolt: Anonymous Payment Channels for Decentralized Currencies.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Bitcoin owes its success to the fact that transactions are transparently recorded in the blockchain, a global public ledger that removes the need for trusted parties. Unfortunately, recording every transaction in the blockchain causes privacy, latency, and scalability issues. Building on recent proposals for \"micropayment channels\" --- two party associations that use the ledger only for dispute resolution --- we introduce techniques for constructing anonymous payment channels. Our proposals allow for secure, instantaneous and private payments that substantially reduce the storage burden on the payment network. Specifically, we introduce three channel proposals, including a technique that allows payments via untrusted intermediaries. We build a concrete implementation of our scheme and show that it can be deployed via a soft fork to existing anonymous currencies such as ZCash.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134093"}, {"primary_key": "3736381", "vector": [], "sparse_vector": [], "title": "Cliptography: Post-Snowden Cryptography.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This tutorial will present a systematic overview of {\\em kleptography}: stealing information subliminally from black-box cryptographic implementations; and {\\em cliptography}: defending mechanisms that clip the power of kleptographic attacks via specification re-designs (without altering the underlying algorithms). Despite the laudatory history of development of modern cryptography, applying cryptographic tools to reliably provide security and privacy in practice is notoriously difficult. One fundamental practical challenge, guaranteeing security and privacy without explicit trust in the algorithms and implementations that underlie basic security infrastructure, remains. While the dangers of entertaining adversarial implementation of cryptographic primitives seem obvious, the ramifications of such attacks are surprisingly dire: it turns out that -- in wide generality -- adversarial implementations of cryptographic (both deterministic and randomized) algorithms may leak private information while producing output that is statistically indistinguishable from that of a faithful implementation. Such attacks were formally studied in Kleptography. <PERSON>den revelations has shown us how security and privacy can be lost at a very large scale even when traditional cryptography seems to be used to protect Internet communication, when Kleptography was not taken into consideration. We will first explain how the above-mentioned Kleptographic attacks can be carried out in various settings. We will then introduce several simple but rigorous immunizing strategies that were inspired by folklore practical wisdoms to protect different algorithms from implementation subversion. Those strategies can be applied to ensure security of most of the fundamental cryptographic primitives such as PRG, digital signatures, public key encryptions against kleptographic attacks when they are implemented accordingly. Our new design principles may suggest new standardization methods that help reducing the threats of subverted implementation. We also hope our tutorial to stimulate a community-wise efforts to further tackle the fundamental challenge mentioned at the beginning.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3136065"}, {"primary_key": "3736383", "vector": [], "sparse_vector": [], "title": "Where the Wild Warnings Are: Root Causes of Chrome HTTPS Certificate Errors.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Parisa Tabriz"], "summary": "HTTPS error warnings are supposed to alert browser users to network attacks. Unfortunately, a wide range of non-attack circumstances trigger hundreds of millions of spurious browser warnings per month. Spurious warnings frustrate users, hinder the widespread adoption of HTTPS, and undermine trust in browser warnings. We investigate the root causes of HTTPS error warnings in the field, with the goal of resolving benign errors.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134007"}, {"primary_key": "3736385", "vector": [], "sparse_vector": [], "title": "Efficient Public Trace and Revoke from Standard Assumptions: Extended Abstract.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We provide efficient constructions for trace-and-revoke systems with public traceability in the black-box confirmation model. Our constructions achieve adaptive security, are based on standard assumptions and achieve significant efficiency gains compared to previous constructions.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134041"}, {"primary_key": "3736386", "vector": [], "sparse_vector": [], "title": "FAME: Fast Attribute-based Message Encryption.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Time and again, attribute-based encryption has been shown to be the natural cryptographic tool for building various types of conditional access systems with far-reaching applications, but the deployment of such systems has been very slow. A central issue is the lack of an encryption scheme that can operate on sensitive data very efficiently and, at the same time, provides features that are important in practice.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134014"}, {"primary_key": "3736387", "vector": [], "sparse_vector": [], "title": "Poster: Adversarial Examples for Classifiers in High-Dimensional Network Data.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Many machine learning methods make assumptions about the data, such as, data stationarity and data independence, for an efficient learning process that requires less data. However, these assumptions may give rise to vulnerabilities if violated by smart adversaries. In this paper, we propose a novel algorithm to craft the input samples by modifying a certain fraction of input features as small as in order to bypass the decision boundary of widely used binary classifiers using Support Vector Machine (SVM). We show that our algorithm can reliably produce adversarial samples which are misclassified with 98% success rate while modifying 22% of the input features on average. Our goal is to evaluate the robustness of classification algorithms for high demensional network data by intentionally performing evasion attacks with carefully designed adversarial examples. The proposed algorithm is evaluated using real network traffic datasets (CAIDA 2007 and CAIDA 2016).", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138853"}, {"primary_key": "3736393", "vector": [], "sparse_vector": [], "title": "Economic Factors of Vulnerability Trade and Exploitation.", "authors": ["<PERSON>"], "summary": "Cybercrime markets support the development and diffusion of new attack technologies, vulnerability exploits, and malware. Whereas the revenue streams of cyber attackers have been studied multiple times in the literature, no quantitative account currently exists on the economics of attack acquisition and deployment. Yet, this understanding is critical to characterize the production of (traded) exploits, the economy that drives it, and its effects on the overall attack scenario. In this paper we provide an empirical investigation of the economics of vulnerability exploitation, and the effects of market factors on likelihood of exploit. Our data is collected first-handedly from a prominent Russian cybercrime market where the trading of the most active attack tools reported by the security industry happens. Our findings reveal that exploits in the underground are priced similarly or above vulnerabilities in legitimate bug-hunting programs, and that the refresh cycle of exploits is slower than currently often assumed. On the other hand, cybercriminals are becoming faster at introducing selected vulnerabilities, and the market is in clear expansion both in terms of players, traded exploits, and exploit pricing. We then evaluate the effects of these market variables on likelihood of attack realization, and find strong evidence of the correlation between market activity and exploit deployment. We discuss implications on vulnerability metrics, economics, and exploit measurement.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133960"}, {"primary_key": "3736394", "vector": [], "sparse_vector": [], "title": "Jasmin: High-Assurance and High-Speed Cryptography.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Jasmin is a framework for developing high-speed and high-assurance cryptographic software. The framework is structured around the Jasmin programming language and its compiler. The language is designed for enhancing portability of programs and for simplifying verification tasks. The compiler is designed to achieve predictability and efficiency of the output code (currently limited to x64 platforms), and is formally verified in the <PERSON>q proof assistant. Using the supercop framework, we evaluate the Jasmin compiler on representative cryptographic routines and conclude that the code generated by the compiler is as efficient as fast, hand-crafted, implementations. Moreover, the framework includes highly automated tools for proving memory safety and constant-time security (for protecting against cache-based timing attacks). We also demonstrate the effectiveness of the verification tools on a large set of cryptographic routines.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134078"}, {"primary_key": "3736395", "vector": [], "sparse_vector": [], "title": "A Fast and Verified Software Stack for Secure Function Evaluation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present a high-assurance software stack for secure function evaluation (SFE). Our stack consists of three components: i. a verified compiler (CircGen) that translates C programs into Boolean circuits; ii. a verified implementation of <PERSON>'s SFE protocol based on garbled circuits and oblivious transfer; and iii. transparent application integration and communications via FRESCO, an open-source framework for secure multiparty computation (MPC). CircGen is a general purpose tool that builds on CompCert, a verified optimizing compiler for C. It can be used in arbitrary Boolean circuit-based cryptography deployments. The security of our SFE protocol implementation is formally verified using EasyCrypt, a tool-assisted framework for building high-confidence cryptographic proofs, and it leverages a new formalization of garbled circuits based on the framework of Bellare, Hoang, and Rogaway (CCS 2012). We conduct a practical evaluation of our approach, and conclude that it is competitive with state-of-the-art (unverified) approaches. Our work provides concrete evidence of the feasibility of building efficient, verified, implementations of higher-level cryptographic systems. All our development is publicly available.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134017"}, {"primary_key": "3736396", "vector": [], "sparse_vector": [], "title": "Practical Graphs for Optimal Side-Channel Resistant Memory-Hard Functions.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A memory-hard function (MHF) ƒn with parameter n can be computed in sequential time and space n. Simultaneously, a high amortized parallel area-time complexity (aAT) is incurred per evaluation. In practice, MHFs are used to limit the rate at which an adversary (using a custom computational device) can evaluate a security sensitive function that still occasionally needs to be evaluated by honest users (using an off-the-shelf general purpose device). The most prevalent examples of such sensitive functions are Key Derivation Functions (KDFs) and password hashing algorithms where rate limits help mitigate off-line dictionary attacks. As the honest users' inputs to these functions are often (low-entropy) passwords special attention is given to a class of side-channel resistant MHFs called iMHFs.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134031"}, {"primary_key": "3736398", "vector": [], "sparse_vector": [], "title": "Attribute-Based Encryption in the Generic Group Model: Automated Proofs and New Constructions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>e"], "summary": "Attribute-based encryption (ABE) is a cryptographic primitive which supports fine-grained access control on encrypted data, making it an appealing building block for many applications. In this paper, we propose, implement, and evaluate fully automated methods for proving security of ABE in the Generic Bilinear Group Model (<PERSON><PERSON>, <PERSON>, and <PERSON>, 2005, <PERSON><PERSON>, 2008), an idealized model which admits simpler and more efficient constructions, and can also be used to find attacks. Our method is applicable to Rational-Fraction Induced ABE, a large class of ABE that contains most of the schemes from the literature, and relies on a Master Theorem, which reduces security in the GGM to a (new) notion of symbolic security, which is amenable to automated verification using constraint-based techniques. We relate our notion of symbolic security for Rational-Fraction Induced ABE to prior notions for Pair Encodings. Finally, we present several applications, including automated proofs for new schemes.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134088"}, {"primary_key": "3736399", "vector": [], "sparse_vector": [], "title": "Ligero: Lightweight Sublinear Arguments Without a Trusted Setup.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We design and implement a simple zero-knowledge argument protocol for NP whose communication complexity is proportional to the square-root of the verification circuit size. The protocol can be based on any collision-resistant hash function. Alternatively, it can be made non-interactive in the random oracle model, yielding concretely efficient zk-SNARKs that do not require a trusted setup or public-key cryptography.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134104"}, {"primary_key": "3736407", "vector": [], "sparse_vector": [], "title": "Identity Related Threats, Vulnerabilities and Risk Mitigation in Online Social Networks: A Tutorial.", "authors": ["<PERSON><PERSON>"], "summary": "This tutorial provides a thorough review of the main research directions in the field of identity management and identity related security threats in Online Social Networks (OSNs). The continuous increase in the numbers and sophistication levels of fake accounts constitutes a big threat to the privacy and to the security of honest OSN users. Uninformed OSN users could be easily fooled into accepting friendship links with fake accounts, giving them by that access to personal information they intend to exclusively share with their real friends. Moreover, these fake accounts subvert the security of the system by spreading malware, connecting with honest users for nefarious goals such as sexual harassment or child abuse, and make the social computing environment mostly untrustworthy. The tutorial introduces the main available research results available in this area, and presents our work on collaborative identity validation techniques to estimate OSN profiles trustworthiness.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3136066"}, {"primary_key": "3736411", "vector": [], "sparse_vector": [], "title": "Defending Against Key Exfiltration: Efficiency Improvements for Big-Key Cryptography via Large-Alphabet Subkey Prediction.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Towards advancing the use of big keys as a practical defense against key exfiltration, this paper provides efficiency improvements for cryptographic schemes in the bounded retrieval model (BRM). We identify probe complexity (the number of scheme accesses to the slow storage medium storing the big key) as the dominant cost. Our main technical contribution is what we call the large-alphabet subkey prediction lemma. It gives good bounds on the predictability under leakage of a random sequence of blocks of the big key, as a function of the block size. We use it to significantly reduce the probe complexity required to attain a given level of security. Together with other techniques, this yields security-preserving performance improvements for BRM symmetric encryption schemes and BRM public-key identification schemes.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133965"}, {"primary_key": "3736412", "vector": [], "sparse_vector": [], "title": "Identity-Based Format-Preserving Encryption.", "authors": ["<PERSON><PERSON>", "Viet Tung Hoang"], "summary": "We introduce identity-based format-preserving encryption (IB-FPE) as a way to localize and limit the damage to format-preserving encryption (FPE) from key exposure. We give definitions, relations between them, generic attacks and two transforms of FPE schemes to IB-FPE schemes. As a special case, we introduce and cover identity-based tweakable blockciphers. We apply all this to analyze DFF, an FPE scheme proposed to NIST for standardization.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133995"}, {"primary_key": "3736413", "vector": [], "sparse_vector": [], "title": "Better Than Advertised: Improved Collision-Resistance Guarantees for MD-Based Hash Functions.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The MD transform that underlies the MD and SHA families iterates a compression function h to get a hash function H. The question we ask is, what property X of h guarantees collision resistance (CR) of H? The classical answer is that X itself be CR. We show that weaker conditions X, in particular forms of what we call constrained-CR, suffice. This reduces demands on compression functions, to the benefit of security, and also, forensically, explains why collision-finding attacks on compression functions have not, historically, lead to immediate breaks of the corresponding hash functions. We obtain our results via a definitional framework called RS security, and a parameterized treatment of MD, that also serve to unify prior work and variants of the transform.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134087"}, {"primary_key": "3736415", "vector": [], "sparse_vector": [], "title": "IoT S&amp;P 2017: First Workshop on Internet of Things Security and Privacy.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "The First Workshop on Internet of Things Security and Privacy is held in Dallas, TX, USA on November 3, 2017, co-located with the ACM Conference on Computer and Communications Security (CCS). The workshop aims to address the security and privacy challenges of the emerging Internet-of-Things landscape. The workshop aims to bring together academic and industrial researchers, and to that end, we have put together an exciting program offering a a mix of current and potential challenges. The workshop will also features 12 papers, 4 posters, and an invited keynote.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3137053"}, {"primary_key": "3736416", "vector": [], "sparse_vector": [], "title": "Algorithm Substitution Attacks from a Steganographic Perspective.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The goal of an algorithm substitution attack (ASA), also called a subversion attack (SA), is to replace an honest implementation of a cryptographic tool by a subverted one which allows to leak private information while generating output indistinguishable from the honest output. <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> provided at CRYPTO '14 a formal security model to capture this kind of attacks and constructed practically implementable ASAs against a large class of symmetric encryption schemes. At CCS'15, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> extended this model to allow the attackers to work in a fully-adaptive and continuous fashion and proposed subversion attacks against digital signature schemes. Both papers also showed the impossibility of ASAs in cases where the cryptographic tools are deterministic. Also at CCS'15, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> strengthened the original model and proposed a universal ASA against sufficiently random encryption schemes. In this paper we analyze ASAs from the perspective of steganography - the well known concept of hiding the presence of secret messages in legal communications. While a close connection between ASAs and steganography is known, this lacks a rigorous treatment. We consider the common computational model for secret-key steganography and prove that successful ASAs correspond to secure stegosystems on certain channels and vice versa. This formal proof allows us to conclude that ASAs are stegosystems and to \"rediscover\" several results concerning ASAs known in the steganographic literature.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133981"}, {"primary_key": "3736419", "vector": [], "sparse_vector": [], "title": "Web Tracking Technologies and Protection Mechanisms.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Billions of users browse the Web on a daily basis, leaving their digital traces on millions of websites. Every such visit, every mouse move or button click may trigger a wide variety of hidden data exchanges across multiple tracking companies. As a result, these companies collect a vast amount of user's data, preferences and habits, that are extremely useful for online advertisers and profitable for data brokers, however very worrisome for the privacy of the users. In this \\emph{3-hours tutorial} we will cover the vide variety of Web tracking technologies, ranging from simple cookies to advanced cross-device fingerprinting. We will describe the main mechanisms behind web tracking and what users can do to protect themselves. Moreover, we will discuss solutions Web developers can use to automatically eliminate tracking from the third-party content they include in their applications. This tutorial will be of interest to a \\emph{general audience} of computer scientists, and \\emph{we do not require any specific prerequisite knowledge} for attendees. We will cover the following tracking mechanisms: \\begin{itemize} \\item third-party cookie tracking, and other stateful tracking techniques that enables tracking across multiple websites, \\item cookie respawning that is used to re-create deleted user cookies, \\item cookie synching that allows trackers and ad agencies to synchronise user IDs across different companies, \\item browser fingerprinting, including Canvas, WebRTC and AudioContext fingerprinting \\item cross-browser device fingerprinting, allowing trackers to recognise users across several devices. \\end{itemize} We will then demonstrate prevalence of such techniques on the Web, based on previous research. We will present the advertisement ecosystem and explain how Web technologies are used in advertisement, in particular in Real-Time-Bidding (RTB). We will explain how cookie synching is used in RTB and present recent analysis on how much a user's tracking data is worth. We will discuss the mechanisms the website owners use to automatically interact with the ad agencies, and explain its consequences on user's security and privacy. To help users protect themselves from Web tracking, we will give an overview of existing solutions. We'll start with the browser settings, and show that basic third-party cookie tracking is still possible even in the private browser mode of most common Web browsers. We then present privacy-protecting browser extensions and compare how efficient they are in protection from Web tracking. Then, we'll present possible protection mechanisms based on browser randomisation to protect from advanced fingerprinting techniques. Finally, we will present solutions for Web developers, who want to include third-party content in their websites, but would like to automatically remove any tracking of their users. In particular, we will discuss simple solutions that exist today for social plugins integration, and propose more advanced server-side based solutions that are a result of our own research.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3136067"}, {"primary_key": "3736420", "vector": [], "sparse_vector": [], "title": "PLAS 2017: ACM SIGSAC Workshop on Programming Languages and Analysis for Security.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The 12th ACM SIGSAC Workshop on Programming Languages and Analysis for Security (PLAS 2017) is co-located with the ACM Conference on Computer and Communications Security (CCS). Over its now more than ten-year history, PLAS has provided a unique forum for researchers and practitioners to exchange ideas about programming language and program analysis techniques with the goal of improving the security of software systems. PLAS aims to provide a forum for exploring and evaluating ideas on using programming language and program analysis techniques to improve the security of software systems. Strongly encouraged are proposals of new, speculative ideas, evaluations of new or known techniques in practical settings, and discussions of emerging threats and important problems.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3137045"}, {"primary_key": "3736421", "vector": [], "sparse_vector": [], "title": "10th International Workshop on Artificial Intelligence and Security (AISec 2017).", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Artificial Intelligence (AI) and Machine Learning (ML) provide a set of useful analytic and decision-making techniques that are being leveraged by an ever-growing community of practitioners, including many whose applications have security-sensitive elements. However, while security researchers often utilize such techniques to address problems and AI/ML researchers develop techniques for Big Data analytics applications, neither community devotes much attention to the other. Within security research, AI/ML components are usually regarded as black-box solvers. Conversely, the learning community seldom considers the security/privacy implications entailed in the application of their algorithms when they are designing them. While these two communities generally focus on different directions, where these two fields do meet, interesting problems appear. Researchers working in this intersection have raised many novel questions for both communities and created a new branch of research known as secure learning. The AISec workshop has become the primary venue for this unique fusion of research.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3137048"}, {"primary_key": "3736422", "vector": [], "sparse_vector": [], "title": "RiskTeller: Predicting the Risk of Cyber Incidents.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Matteo Dell&apos;Amico"], "summary": "The current evolution of the cyber-threat ecosystem shows that no system can be considered invulnerable. It is therefore important to quantify the risk level within a system and devise risk prediction methods such that proactive measures can be taken to reduce the damage of cyber attacks. We present RiskTeller, a system that analyzes binary file appearance logs of machines to predict which machines are at risk of infection months in advance. Risk prediction models are built by creating, for each machine, a comprehensive profile capturing its usage patterns, and then associating each profile to a risk level through both fully and semi-supervised learning methods. We evaluate RiskTeller on a year-long dataset containing information about all the binaries appearing on machines of 18 enterprises. We show that RiskTeller can use the machine profile computed for a given machine to predict subsequent infections with the highest prediction precision achieved to date.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134022"}, {"primary_key": "3736424", "vector": [], "sparse_vector": [], "title": "CPS-SPC 2017: Third Workshop on Cyber-Physical Systems Security and PrivaCy.", "authors": ["Rakesh B. Bobba", "<PERSON><PERSON><PERSON>"], "summary": "Cyber-Physical Systems (CPS) are becoming increasingly critical for the well-being of society (e.g., electricity generation and distribution, water treatment, implantable medical devices etc.). While the convergence of computing, communications and physical control in such systems provides benefits in terms of efficiency and convenience, the attack surface resulting from this convergence poses unique security and privacy challenges. These systems represent the new frontier for cyber risk. CPS-SPC is an annual forum, in its 3rd edition this year, that aims to provide a focal point for the research community to begin addressing the security and privacy challenges of CPS in a comprehensive and multidisciplinary manner and, in tandem with other efforts, build a comprehensive research road map.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3137051"}, {"primary_key": "3736425", "vector": [], "sparse_vector": [], "title": "Directed Greybox Fuzzing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Existing Greybox Fuzzers (GF) cannot be effectively directed, for instance, towards problematic changes or patches, towards critical system calls or dangerous locations, or towards functions in the stack-trace of a reported vulnerability that we wish to reproduce. In this paper, we introduce Directed Greybox Fuzzing (DGF) which generates inputs with the objective of reaching a given set of target program locations efficiently. We develop and evaluate a simulated annealing-based power schedule that gradually assigns more energy to seeds that are closer to the target locations while reducing energy for seeds that are further away. Experiments with our implementation AFLGo demonstrate that DGF outperforms both directed symbolic-execution-based whitebox fuzzing and undirected greybox fuzzing. We show applications of DGF to patch testing and crash reproduction, and discuss the integration of AFLGo into Google's continuous fuzzing platform OSS-Fuzz. Due to its directedness, AFLGo could find 39 bugs in several well-fuzzed, security-critical projects like LibXML2. 17 CVEs were assigned.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134020"}, {"primary_key": "3736426", "vector": [], "sparse_vector": [], "title": "Practical Secure Aggregation for Privacy-Preserving Machine Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We design a novel, communication-efficient, failure-robust protocol for secure aggregation of high-dimensional data. Our protocol allows a server to compute the sum of large, user-held data vectors from mobile devices in a secure manner (i.e. without learning each user's individual contribution), and can be used, for example, in a federated learning setting, to aggregate user-provided model updates for a deep neural network. We prove the security of our protocol in the honest-but-curious and active adversary settings, and show that security is maintained even if an arbitrarily chosen subset of users drop out at any time. We evaluate the efficiency of our protocol and show, by complexity analysis and a concrete implementation, that its runtime and communication overhead remain low even on large data sets and client pools. For 16-bit input values, our protocol offers $1.73 x communication expansion for 210 users and 220-dimensional vectors, and 1.98 x expansion for 214 users and 224-dimensional vectors over sending data in the clear.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133982"}, {"primary_key": "3736427", "vector": [], "sparse_vector": [], "title": "Forward and Backward Private Searchable Encryption from Constrained Cryptographic Primitives.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Using dynamic Searchable Symmetric Encryption, a user with limited storage resources can securely outsource a database to an untrusted server, in such a way that the database can still be searched and updated efficiently. For these schemes, it would be desirable that updates do not reveal any information a priori about the modifications they carry out, and that deleted results remain inaccessible to the server a posteriori. If the first property, called forward privacy, has been the main motivation of recent works, the second one, backward privacy, has been overlooked.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133980"}, {"primary_key": "3736429", "vector": [], "sparse_vector": [], "title": "Homomorphic Secret Sharing: Optimizations and Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>v <PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We continue the study of Homomorphic Secret Sharing (HSS), recently introduced by <PERSON> et al. (Crypto 2016, Eurocrypt 2017). A (2-party) HSS scheme splits an input x into shares (x0,x1) such that (1) each share computationally hides x, and (2) there exists an efficient homomorphic evaluation algorithm $\\Eval$ such that for any function (or \"program\") from a given class it holds that Eval(x0,P)+Eval(x1,P)=P(x). <PERSON> et al. show how to construct an HSS scheme for branching programs, with an inverse polynomial error, using discrete-log type assumptions such as DDH.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134107"}, {"primary_key": "3736431", "vector": [], "sparse_vector": [], "title": "POSTER: An Empirical Measurement Study on Multi-tenant Deployment Issues of CDNs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Content delivery network (CDN) has been playing an important role in accelerating users' visit speed, bring good experience for popular web sites around the world. It has become a common security enhance service for CDN providers to offer HTTPS support to tenants. When several tenants are deployed to share a same IP address due to resource efficiency and cost, CDN providers should make comprehensive settings to ensure that all tenants' sites work correctly on users' requests. Otherwise, issues can take place such as denial of service (DOS) and privacy leakage, causing very bad user experience to users as well as potential economic loss for tenants, especially under the situation of hybrid deployment of HTTP and HTTPS. We examine the deployments of typical multi-tenant CDN providers by active measurement and find that CDN providers, namely Akaimai and ChinaCenter, have configuration problems which can result in DOS by certificate name mismatch error. Several advices are given to help to mitigate the issue. We believe that our study is meaningful for improving the security and the robustness of CDN.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138852"}, {"primary_key": "3736433", "vector": [], "sparse_vector": [], "title": "Practical UC-Secure Delegatable Credentials with Attributes and Their Application to Blockchain.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Certification of keys and attributes is in practice typically realized by a hierarchy of issuers. Revealing the full chain of issuers for certificate verification, however, can be a privacy issue since it can leak sensitive information about the issuer's organizational structure or about the certificate owner. Delegatable anonymous credentials solve this problem and allow one to hide the full delegation (issuance) chain, providing privacy during both delegation and presentation of certificates. However, the existing delegatable credentials schemes are not efficient enough for practical use.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134025"}, {"primary_key": "3736434", "vector": [], "sparse_vector": [], "title": "Zero-Knowledge Contingent Payments Revisited: Attacks and Payments for Services.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Zero Knowledge Contingent Payment (ZKCP) protocols allow fair exchange of sold goods and payments over the Bitcoin network. In this paper we point out two main shortcomings of current proposals for ZKCP, and propose ways to address them.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134060"}, {"primary_key": "3736436", "vector": [], "sparse_vector": [], "title": "Deterministic Browser.", "authors": ["<PERSON><PERSON> Cao", "<PERSON><PERSON><PERSON>", "Song Li", "Shujiang Wu"], "summary": "Timing attacks have been a continuous threat to users' privacy in modern browsers. To mitigate such attacks, existing approaches, such as <PERSON> Browser and Fermata, add jitters to the browser clock so that an attacker cannot accurately measure an event. However, such defenses only raise the bar for an attacker but do not fundamentally mitigate timing attacks, i.e., it just takes longer than previous to launch a timing attack. In this paper, we propose a novel approach, called deterministic browser, which can provably prevent timing attacks in modern browsers. Borrowing from Physics, we introduce several concepts, such as an observer and a reference frame. Specifically, a snippet of JavaScript, i.e., an observer in JavaScript reference frame, will always obtain the same, fixed timing information so that timing attacks are prevented; at contrast, a user, i.e., an oracle observer, will perceive the JavaScript differently and do not experience the performance slowdown. We have implemented a prototype called DeterFox and our evaluation shows that the prototype can defend against browser-related timing attacks.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133996"}, {"primary_key": "3736437", "vector": [], "sparse_vector": [], "title": "POSTER: Actively Detecting Implicit Fraudulent Transactions.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Cao", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON> (Alan) Qi", "<PERSON>"], "summary": "In this work, we propose to actively detect implicit fraudulent transactions. A novel machine learning method is introduced to distinguish anomalous electronic transactions based on the historical records. The transferor will be alerted during the on-going payment when the fraud probability is recognized as large enough. Compared with elaborative rule-based approaches, our model is much more effective in fraud detection.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138822"}, {"primary_key": "3736439", "vector": [], "sparse_vector": [], "title": "5Gen-C: Multi-input Functional Encryption and Program Obfuscation for Arithmetic Circuits.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Program obfuscation is a powerful security primitive with many applications. White-box cryptography studies a particular subset of program obfuscation targeting keyed pseudorandom functions (PRFs), a core component of systems such as mobile payment and digital rights management. Although the white-box obfuscators currently used in practice do not come with security proofs and are thus routinely broken, recent years have seen an explosion of cryptographic techniques for obfuscation, with the goal of avoiding this build-and-break cycle.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133983"}, {"primary_key": "3736442", "vector": [], "sparse_vector": [], "title": "Nonmalleable Information Flow Control.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Noninterference is a popular semantic security condition because it offers strong end-to-end guarantees, it is inherently compositional, and it can be enforced using a simple security type system. Unfortunately, it is too restrictive for real systems. Mechanisms for downgrading information are needed to capture real-world security requirements, but downgrading eliminates the strong compositional security guarantees of noninterference. We introduce nonmalleable information flow, a new formal security condition that generalizes noninterference to permit controlled downgrading of both confidentiality and integrity. While previous work on robust declassification prevents adversaries from exploiting the downgrading of confidentiality, our key insight is transparent endorsement, a mechanism for downgrading integrity while defending against adversarial exploitation. Robust declassification appeared to break the duality of confidentiality and integrity by making confidentiality depend on integrity, but transparent endorsement makes integrity depend on confidentiality, restoring this duality. We show how to extend a security-typed programming language with transparent endorsement and prove that this static type system enforces nonmalleable information flow, a new security property that subsumes robust declassification and transparent endorsement. Finally, we describe an implementation of this type system in the context of Flame, a flow-limited authorization plugin for the Glasgow Haskell Compiler.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134054"}, {"primary_key": "3736443", "vector": [], "sparse_vector": [], "title": "Solidus: Confidential Distributed Ledger Transactions via PVORM.", "authors": ["<PERSON>", "<PERSON>", "Yan Ji", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Blockchains and more general distributed ledgers are becoming increasingly popular as efficient, reliable, and persistent records of data and transactions. Unfortunately, they ensure reliability and correctness by making all data public, raising confidentiality concerns that eliminate many potential uses.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134010"}, {"primary_key": "3736445", "vector": [], "sparse_vector": [], "title": "Efficient, Constant-Round and Actively Secure MPC: Beyond the Three-Party Case.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "While the feasibility of constant-round and actively secure MPC has been known for over two decades, the last few years have witnessed a flurry of designs and implementations that make its deployment a palpable reality. To our knowledge, however, existing concretely efficient MPC constructions are only for up to three parties.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134100"}, {"primary_key": "3736446", "vector": [], "sparse_vector": [], "title": "ASHES 2017: Workshop on Attacks and Solutions in Hardware Security.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The workshop on \"attacks and solutions in hardware security\" (ASHES) deals with all aspects of hardware security, including any recent attacks and solutions in the area. Besides mainstream research in hardware security, it also covers new, alternative or emerging application scenarios, such as the internet of things, nuclear weapons inspections, satellite security, or consumer and supply chain security. It also puts some focus on special purpose hardware and novel methodological solutions, such as particularly lightweight, small, low-cost, and energy-efficient devices, or even non-electronic security systems. Finally, ASHES welcomes any theoretical works that systematize and structure the area, and so-called \"Wild-and-Crazy\" papers that describe and distribute seminal ideas at an early conceptual stage to the community.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3137049"}, {"primary_key": "3736447", "vector": [], "sparse_vector": [], "title": "Post-Quantum Zero-Knowledge and Signatures from Symmetric-Key Primitives.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose a new class of post-quantum digital signature schemes that: (a) derive their security entirely from the security of symmetric-key primitives, believed to be quantum-secure, and (b) have extremely small keypairs, and, (c) are highly parameterizable. In our signature constructions, the public key is an image y = f (x) of a one-way function f and secret key x. A signature is a non-interactive zero-knowledge proof of x, that incorporates a message to be signed. For this proof, we leverage recent progress of <PERSON><PERSON><PERSON><PERSON> et al. (USENIX'16) in constructing an efficient Ó-protocol for statements over general circuits. We improve this Ó-protocol to reduce proof sizes by a factor of two, at no additional computational cost. While this is of independent interest as it yields more compact proofs for any circuit, it also decreases our signature sizes. We consider two possibilities to make the proof non-interactive: The Fiat-Shamir transform and <PERSON><PERSON><PERSON>'s transform (EUROCRYPT'12, '15,'16). The former has smaller signatures, while the latter has a security analysis in the quantum-accessible random oracle model. By customizing <PERSON><PERSON><PERSON>'s transform to our application, the overhead is reduced to 1.6x when compared to the Fiat-Shamir transform, which does not have a rigorous post-quantum security analysis. We implement and benchmark both approaches and explore the possible choice of f , taking advantage of the recent trend to strive for practical symmetric ciphers with a particularly low number of multiplications and end up using LowMC (EUROCRYPT'15).", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133997"}, {"primary_key": "3736448", "vector": [], "sparse_vector": [], "title": "The TypTop System: Personalized Typo-Tolerant Password Checking.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Password checking systems traditionally allow login only if the correct password is submitted. Recent work on typo-tolerant password checking suggests that usability can be improved, with negligible security loss, by allowing a small number of typographical errors. Existing systems, however, can only correct a handful of errors, such as accidentally leaving caps lock on or incorrect capitalization of the first letter in a password. This leaves out numerous kinds of typos made by users, such as transposition errors, substitutions, or capitalization errors elsewhere in a password. Some users therefore receive no benefit from existing typo-tolerance mechanisms.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134000"}, {"primary_key": "3736451", "vector": [], "sparse_vector": [], "title": "Precise Detection of Side-Channel Vulnerabilities using Quantitative Cartesian Hoare Logic.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents Themis, an end-to-end static analysis tool for finding resource-usage side-channel vulnerabilities in Java applications. We introduce the notion of epsilon-bounded non-interference, a variant and relaxation of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>'s well-known non-interference principle. We then present Quantitative Cartesian Hoare Logic (QCHL), a program logic for verifying epsilon-bounded non-interference. Our tool, Themis, combines automated reasoning in CHL with lightweight static taint analysis to improve scalability. We evaluate Themis on well known Java applications and demonstrate that Themis can find unknown side-channel vulnerabilities in widely-used programs. We also show that Themis can verify the absence of vulnerabilities in repaired versions of vulnerable programs and that Themis compares favorably against Blazer, a state-of-the-art static analysis tool for finding timing side channels in Java applications.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134058"}, {"primary_key": "3736454", "vector": [], "sparse_vector": [], "title": "Fast Private Set Intersection from Homomorphic Encryption.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Private Set Intersection (PSI) is a cryptographic technique that allows two parties to compute the intersection of their sets without revealing anything except the intersection. We use fully homomorphic encryption to construct a fast PSI protocol with a small communication overhead that works particularly well when one of the two sets is much smaller than the other, and is secure against semi-honest adversaries.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134061"}, {"primary_key": "3736456", "vector": [], "sparse_vector": [], "title": "PeGaSus: Data-Adaptive Differentially Private Stream Processing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Individuals are continually observed by an ever-increasing number of sensors that make up the Internet of Things. The resulting streams of data, which are analyzed in real time, can reveal sensitive personal information about individuals. Hence, there is an urgent need for stream processing solutions that can analyze these data in real time with provable guarantees of privacy and low error.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134102"}, {"primary_key": "3736457", "vector": [], "sparse_vector": [], "title": "Practical Attacks Against Graph-based Clustering.", "authors": ["<PERSON><PERSON><PERSON> Chen", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Graph modeling allows numerous security problems to be tackled in a general way, however, little work has been done to understand their ability to withstand adversarial attacks. We design and evaluate two novel graph attacks against a state-of-the-art network-level, graph-based detection system. Our work highlights areas in adversarial machine learning that have not yet been addressed, specifically: graph-based clustering techniques, and a global feature space where realistic attackers without perfect knowledge must be accounted for (by the defenders) in order to be practical. Even though less informed attackers can evade graph clustering with low cost, we show that some practical defenses are possible.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134083"}, {"primary_key": "3736458", "vector": [], "sparse_vector": [], "title": "Client-side Name Collision Vulnerability in the New gTLD Era: A Systematic Study.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The recent unprecedented delegation of new generic top-level domains (gTLDs) has exacerbated an existing, but fallow, problem called name collisions. One concrete exploit of such problem was discovered recently, which targets internal namespaces and enables Man in the Middle (MitM) attacks against end-user devices from anywhere on the Internet. Analysis of the underlying problem shows that it is not specific to any single service protocol, but little attention has been paid to understand the vulnerability status and the defense solution space at the service level. In this paper, we perform the first systematic study of the robustness of internal network services under name collision attacks.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134084"}, {"primary_key": "3736459", "vector": [], "sparse_vector": [], "title": "Mass Discovery of Android Traffic Imprints through Instantiated Partial Execution.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Monitoring network behaviors of mobile applications, controlling their resource access and detecting potentially harmful apps are becoming increasingly important for the security protection within today's organizational, ISP and carriers. For this purpose, apps need to be identified from their communication, based upon their individual traffic signatures (called imprints in our research). Creating imprints for a large number of apps is nontrivial, due to the challenges in comprehensively analyzing their network activities at a large scale, for millions of apps on today's rapidly-growing app marketplaces. Prior research relies on automatic exploration of an app's user interfaces (UIs) to trigger its network activities, which is less likely to scale given the cost of the operation (at least 5 minutes per app) and its effectiveness (limited coverage of an app's behaviors).", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134009"}, {"primary_key": "3736462", "vector": [], "sparse_vector": [], "title": "POSTER: Semi-supervised Classification for Dynamic Android Malware Detection.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Manually labeling the large number of samples of Android APKs into benign or different malicious families requires tremendous human effort, while it is comparably easy and cheap to obtain a large amount of unlabeled APKs from various sources. Moreover, the fast-paced evolution of Android malware continuously generates derivative and new malware families. These families often contain new signatures, which can escape detection that uses static analysis. These practical challenges can also cause classical supervised machine learning algorithms to degrade in performance.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138838"}, {"primary_key": "3736463", "vector": [], "sparse_vector": [], "title": "POSTER: Detection of CPS Program Anomalies by Enforcing Cyber-Physical Execution Semantics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> (Daphne) Yao"], "summary": "In this work, we present a new program behavior model, i.e., the event-aware finite-state automaton ( eFSA ), which takes advantage of the event-driven nature of control programs in cyber-physical systems (CPS) and incorporates event checking in anomaly detection. eFSA provides new detection capabilities to detect data-oriented attacks in CPS control programs, including attacks on control intensity (i.e., hijacked for/while-loops) and attacks on control branch (i.e., conditional branches). We implement a prototype of our approach on Raspberry Pi and evaluate eFSA 's performance by conducting CPS case studies. Results show that it is able to effectively detect different CPS attacks in our experiments.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138839"}, {"primary_key": "3736464", "vector": [], "sparse_vector": [], "title": "Standardizing Bad Cryptographic Practice: A Teardown of the IEEE Standard for Protecting Electronic-design Intellectual Property.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Domenic Forte", "<PERSON>"], "summary": "We provide an analysis of IEEE standard P1735, which describes methods for encrypting electronic-design intellectual property (IP), as well as the management of access rights for such IP. We find a surprising number of cryptographic mistakes in the standard. In the most egregious cases, these mistakes enable attack vectors that allow us to recover the entire underlying plaintext IP. Some of these attack vectors are well-known, e.g. padding-oracle attacks. Others are new, and are made possible by the need to support the typical uses of the underlying IP; in particular, the need for commercial system-on-chip (SoC) tools to synthesize multiple pieces of IP into a fully specified chip design and to provide syntax errors. We exploit these mistakes in a variety of ways, leveraging a commercial SoC tool as a black-box oracle.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134040"}, {"primary_key": "3736465", "vector": [], "sparse_vector": [], "title": "Viden: Attacker Identification on In-Vehicle Networks.", "authors": ["Kyong<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Various defense schemes --- which determine the presence of an attack on the in-vehicle network --- have recently been proposed. However, they fail to identify which Electronic Control Unit (ECU) actually mounted the attack. Clearly, pinpointing the attacker ECU is essential for fast/efficient forensic, isolation, security patch, etc. To meet this need, we propose a novel scheme, called Viden (Voltage-based attacker identification), which can identify the attacker ECU by measuring and utilizing voltages on the in-vehicle network. The first phase of Viden, called ACK learning, determines whether or not the measured voltage signals really originate from the genuine message transmitter. Viden then exploits the voltage measurements to construct and update the transmitter ECUs' voltage profiles as their fingerprints. It finally uses the voltage profiles to identify the attacker ECU. Since Viden adapts its profiles to changes inside/outside of the vehicle, it can pinpoint the attacker ECU under various conditions. Moreover, its efficiency and design-compliance with modern in-vehicle network implementations make V<PERSON> practical and easily deployable. Our extensive experimental evaluations on both a CAN bus prototype and two real vehicles have shown that Viden can accurately fingerprint ECUs based solely on voltage measurements and thus identify the attacker ECU with a low false identification rate of 0.2%.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134001"}, {"primary_key": "3736469", "vector": [], "sparse_vector": [], "title": "Fairness in an Unfair World: Fair Multiparty Computation from Public Bulletin Boards.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Secure multiparty computation allows mutually distrusting parties to compute a function on their private inputs such that nothing but the function output is revealed. Achieving fairness --- that all parties learn the output or no one does -- is a long studied problem with known impossibility results in the standard model if a majority of parties are dishonest. We present a new model for achieving fairness in MPC against dishonest majority by using public bulletin boards implemented via existing infrastructure such as blockchains or Google's certificate transparency logs. We present both theoretical and practical constructions using either witness encryption or trusted hardware (such as Intel SGX). Unlike previous works that either penalize an aborting party or achieve weaker notions such as $\\Delta$-fairness, we achieve complete fairness using existing infrastructure.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134092"}, {"primary_key": "3736473", "vector": [], "sparse_vector": [], "title": "DIFUZE: Interface Aware Fuzzing for Kernel Drivers.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Device drivers are an essential part in modern Unix-like systems to handle operations on physical devices, from hard disks and printers to digital cameras and Bluetooth speakers. The surge of new hardware, particularly on mobile devices, introduces an explosive growth of device drivers in system kernels. Many such drivers are provided by third-party developers, which are susceptible to security vulnerabilities and lack proper vetting. Unfortunately, the complex input data structures for device drivers render traditional analysis tools, such as fuzz testing, less effective, and so far, research on kernel driver security is comparatively sparse. In this paper, we present DIFUZE, an interface-aware fuzzing tool to automatically generate valid inputs and trigger the execution of the kernel drivers. We leverage static analysis to compose correctly-structured input in the userspace to explore kernel drivers. DIFUZE is fully automatic, ranging from identifying driver handlers, to mapping to device file names, to constructing complex argument instances. We evaluate our approach on seven modern Android smartphones. The results show that DIFUZE can effectively identify kernel driver bugs, and reports 32 previously unknown vulnerabilities, including flaws that lead to arbitrary code execution.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134069"}, {"primary_key": "3736474", "vector": [], "sparse_vector": [], "title": "A Type System for Privacy Properties.", "authors": ["Véronique Cortier", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Mature push button tools have emerged for checking trace properties (e.g. secrecy or authentication) of security protocols. The case of indistinguishability-based privacy properties (e.g. ballot privacy or anonymity) is more complex and constitutes an active research topic with several recent propositions of techniques and tools.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133998"}, {"primary_key": "3736475", "vector": [], "sparse_vector": [], "title": "A Comprehensive Symbolic Analysis of TLS 1.3.", "authors": ["Cas Cremers", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The TLS protocol is intended to enable secure end-to-end communication over insecure networks, including the Internet. Unfortunately, this goal has been thwarted a number of times throughout the protocol's tumultuous lifetime, resulting in the need for a new version of the protocol, namely TLS 1.3. Over the past three years, in an unprecedented joint design effort with the academic community, the TLS Working Group has been working tirelessly to enhance the security of TLS.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134063"}, {"primary_key": "3736478", "vector": [], "sparse_vector": [], "title": "POSTER: A Comprehensive Study of Forged Certificates in the Wild.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Junzheng Shi"], "summary": "With the widespread use of SSL, many issues have been exposed as well. Forged certificates used for MITM attacks or proxies can make SSL encryption useless easily, leading to privacy disclosure and property loss of careless victims. In this paper, we implement a large scale of passive measurement of SSL/TLS and analyze the forged certificates in the wild comprehensively. We measured SSL/TLS connection for 16 months on two large research networks, which provided a total of 100 Gbps bandwidth. We gathered nearly 135 million leaf certificates and studied the forged ones. Our findings reveal main reasons of signing forged certificates, and show the preference of them. Finally, we find out several suspicious servers that might be used for MITM.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138851"}, {"primary_key": "3736479", "vector": [], "sparse_vector": [], "title": "Evading Classifiers by Morphing in the Dark.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Learning-based systems have been shown to be vulnerable to evasion through adversarial data manipulation. These attacks have been studied under assumptions that the adversary has certain knowledge of either the target model internals, its training dataset or at least classification scores it assigns to input samples. In this paper, we investigate a much more constrained and realistic attack scenario wherein the target classifier is minimally exposed to the adversary, revealing on its final classification decision (e.g., reject or accept an input sample). Moreover, the adversary can only manipulate malicious samples using a blackbox morpher. That is, the adversary has to evade the target classifier by morphing malicious samples \"in the dark\". We present a scoring mechanism that can assign a real-value score which reflects evasion progress to each sample based on the limited information available. Leveraging on such scoring mechanism, we propose an evasion method -- EvadeHC -- and evaluate it against two PDF malware detectors, namely PDFRate and Hidost. The experimental evaluation demonstrates that the proposed evasion attacks are effective, attaining $100\\%$ evasion rate on the evaluation dataset. Interestingly, EvadeHC outperforms the known classifier evasion technique that operates based on classification scores output by the classifiers. Although our evaluations are conducted on PDF malware classifier, the proposed approaches are domain-agnostic and is of wider application to other learning-based systems.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133978"}, {"primary_key": "3736481", "vector": [], "sparse_vector": [], "title": "Use Privacy in Data-Driven Systems: Theory and Experiments with Machine Learnt Programs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Gihyuk Ko", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents an approach to formalizing and enforcing a class of use privacy properties in data-driven systems. In contrast to prior work, we focus on use restrictions on proxies (i.e. strong predictors) of protected information types. Our definition relates proxy use to intermediate computations that occur in a program, and identify two essential properties that characterize this behavior: 1) its result is strongly associated with the protected information type in question, and 2) it is likely to causally affect the final output of the program. For a specific instantiation of this definition, we present a program analysis technique that detects instances of proxy use in a model, and provides a witness that identifies which parts of the corresponding program exhibit the behavior. Recognizing that not all instances of proxy use of a protected information type are inappropriate, we make use of a normative judgment oracle that makes this inappropriateness determination for a given witness. Our repair algorithm uses the witness of an inappropriate proxy use to transform the model into one that provably does not exhibit proxy use, while avoiding changes that unduly affect classification accuracy. Using a corpus of social datasets, our evaluation shows that these algorithms are able to detect proxy use instances that would be difficult to find using existing techniques, and subsequently remove them while maintaining acceptable classification performance.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134097"}, {"primary_key": "3736485", "vector": [], "sparse_vector": [], "title": "Keep me Updated: An Empirical Study of Third-Party Library Updatability on Android.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Third-party libraries in Android apps have repeatedly been shown to be hazards to the users' privacy and an amplification of their host apps' attack surface. A particularly aggravating factor to this situation is that the libraries' version included in apps are very often outdated.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134059"}, {"primary_key": "3736487", "vector": [], "sparse_vector": [], "title": "POSTER: Rust SGX SDK: Towards Memory Safety in Intel SGX Enclave.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Intel SGX is the next-generation trusted computing infrastructure. It can e effctively protect data inside enclaves from being stolen. Similar to traditional programs, SGX enclaves are likely to have security vulnerabilities and can be exploited as well. This gives an adversary a great opportunity to steal secret data or perform other malicious operations. Rust is one of the system programming languages with promising security properties. It has powerful checkers and guarantees memory-safety and thread-safety. In this paper, we show Rust SGX SDK, which combines Intel SGX and Rust programming language together. By using Rust SGX SDK, developers could write memory-safe secure enclaves easily, eliminating the most possibility of being pwned through memory vulnerabilities. What's more, the Rust enclaves are able to run as fast as the ones written in C/C++.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138824"}, {"primary_key": "3736489", "vector": [], "sparse_vector": [], "title": "Scaling ORAM for Secure Computation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We design and implement a Distributed Oblivious Random Access Memory (DORAM) data structure that is optimized for use in two-party secure computation protocols. We improve upon the access time of previous constructions by a factor of up to ten, their memory overhead by a factor of one hundred or more, and their initialization time by a factor of thousands. We are able to instantiate ORAMs that hold 234 bytes, and perform operations on them in seconds, which was not previously feasible with any implemented scheme.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133967"}, {"primary_key": "3736490", "vector": [], "sparse_vector": [], "title": "Betrayal, Distrust, and Rationality: Smart Counter-Collusion Contracts for Verifiable Cloud Computing.", "authors": ["Changyu Dong", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Cloud computing has become an irreversible trend. Together comes the pressing need for verifiability, to assure the client the correctness of computation outsourced to the cloud. Existing verifiable computation techniques all have a high overhead, thus if being deployed in the clouds, would render cloud computing more expensive than the on-premises counterpart. To achieve verifiability at a reasonable cost, we leverage game theory and propose a smart contract based solution. In a nutshell, a client lets two clouds compute the same task, and uses smart contracts to stimulate tension, betrayal and distrust between the clouds, so that rational clouds will not collude and cheat. In the absence of collusion, verification of correctness can be done easily by crosschecking the results from the two clouds. We provide a formal analysis of the games induced by the contracts, and prove that the contracts will be effective under certain reasonable assumptions. By resorting to game theory and smart contracts, we are able to avoid heavy cryptographic protocols. The client only needs to pay two clouds to compute in the clear, and a small transaction fee to use the smart contracts. We also conducted a feasibility study that involves implementing the contracts in Solidity and running them on the official Ethereum network.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134032"}, {"primary_key": "3736491", "vector": [], "sparse_vector": [], "title": "TinyOLE: Efficient Actively Secure Two-Party Computation from Oblivious Linear Function Evaluation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce a new approach to actively secure two-party computation based on so-called oblivious linear function evaluation (OLE), a natural generalisation of oblivious transfer (OT) and a special case of the notion of oblivious polynomial evaluation introduced by <PERSON><PERSON> and <PERSON><PERSON> at STOC 1999. OLE works over a finite field F. In an OLE the sender inputs two field elements a ϵ F and b ϵ F, and the receiver inputs a field element x ϵ F and learns only f (x) = ax +b. Our protocol can evaluate an arithmetic circuit over a finite field F given black-box access to OLE for F. The protocol is unconditionally secure and consumes only a constant number of OLEs per multiplication gate. An OLE over a field F of size O(2 k) can be implemented with communication O(ffiÈ). This gives a protocol with communication complexity O(|C|È) for large enough fields, where C is an arithmetic circuit computing the desired function. This asymptotically matches the best previous protocols, but our protocol at the same time obtains significantly smaller constants hidden by the big-O notation, yielding a highly practical protocol. Conceptually our techniques lift the techniques for basing practical actively secure 2PC of Boolean circuits on OT introduced under the name TinyOT by Nielsen, <PERSON>t, <PERSON><PERSON><PERSON> and Burr<PERSON> at Crypto 2012 to the arithmetic setting. In doing so we develop several novel techniques for generating various flavours of OLE and combining these. We believe that the efficiency of our protocols, both in asymptotic and practical terms, establishes OLE and its variants as an important foundation for efficient actively secure 2PC.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134024"}, {"primary_key": "3736493", "vector": [], "sparse_vector": [], "title": "DeepLog: Anomaly Detection and Diagnosis from System Logs through Deep Learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Anomaly detection is a critical step towards building a secure and trustworthy system. The primary purpose of a system log is to record system states and significant events at various critical points to help debug system failures and perform root cause analysis. Such log data is universally available in nearly all computer systems. Log data is an important and valuable resource for understanding system status and performance issues; therefore, the various system logs are naturally excellent source of information for online monitoring and anomaly detection. We propose DeepLog, a deep neural network model utilizing Long Short-Term Memory (LSTM), to model a system log as a natural language sequence. This allows DeepLog to automatically learn log patterns from normal execution, and detect anomalies when log patterns deviate from the model trained from log data under normal execution. In addition, we demonstrate how to incrementally update the DeepLog model in an online fashion so that it can adapt to new log patterns over time. Furthermore, DeepLog constructs workflows from the underlying system log so that once an anomaly is detected, users can diagnose the detected anomaly and perform root cause analysis effectively. Extensive experimental evaluations over large log data have shown that DeepLog has outperformed other existing log-based anomaly detection methods based on traditional data mining methodologies.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134015"}, {"primary_key": "3736494", "vector": [], "sparse_vector": [], "title": "Identifying Open-Source License Violation and 1-day Security Risk at Large Scale.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With millions of apps available to users, the mobile app market is rapidly becoming very crowded. Given the intense competition, the time to market is a critical factor for the success and profitability of an app. In order to shorten the development cycle, developers often focus their efforts on the unique features and workflows of their apps and rely on third-party Open Source Software (OSS) for the common features. Unfortunately, despite their benefits, careless use of OSS can introduce significant legal and security risks, which if ignored can not only jeopardize security and privacy of end users, but can also cause app developers high financial loss. However, tracking OSS components, their versions, and interdependencies can be very tedious and error-prone, particularly if an OSS is imported with little to no knowledge of its provenance.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134048"}, {"primary_key": "3736502", "vector": [], "sparse_vector": [], "title": "Side-Channel Attacks on BLISS Lattice-Based Signatures: Exploiting Branch Tracing against strongSwan and Electromagnetic Emanations in Microcontrollers.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we investigate the security of the BLISS lattice-based signature scheme, one of the most promising candidates for postquantum-secure signatures, against side-channel attacks. Several works have been devoted to its efficient implementation on various platforms, from desktop CPUs to microcontrollers and FPGAs, and more recent papers have also considered its security against certain types of physical attacks, notably fault injection and cache attacks. We turn to more traditional side-channel analysis, and describe several attacks that can yield a full key recovery.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134028"}, {"primary_key": "3736507", "vector": [], "sparse_vector": [], "title": "Distributed Measurement with Private Set-Union Cardinality.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper introduces a cryptographic protocol for efficiently aggregating a count of unique items across a set of data parties privately - that is, without exposing any information other than the count. Our protocol allows for more secure and useful statistics gathering in privacy-preserving distributed systems such as anonymity networks; for example, it allows operators of anonymity networks such as Tor to securely answer the questions: how many unique users are using the distributed service? and how many hidden services are being accessed?. We formally prove the correctness and security of our protocol in the Universal Composability framework against an active adversary that compromises all but one of the aggregation parties. We also show that the protocol provides security against adaptive corruption of the data parties, which prevents them from being victims of targeted compromise. To ensure safe measurements, we also show how the output can satisfy differential privacy.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134034"}, {"primary_key": "3736509", "vector": [], "sparse_vector": [], "title": "Verifying Security Policies in Multi-agent Workflows with Loops.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We consider the automatic verification of information flow security policies of web-based workflows, such as conference submission systems like EasyChair. Our workflow description language allows for loops, non-deterministic choice, and an unbounded number of participating agents. The information flow policies are specified in a temporal logic for hyperproperties. We show that the verification problem can be reduced to the satisfiability of a formula of first-order linear-time temporal logic, and provide decidability results for relevant classes of workflows and specifications. We report on experimental results obtained with an implementation of our approach on a series of benchmarks.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134080"}, {"primary_key": "3736510", "vector": [], "sparse_vector": [], "title": "IRON: Functional Encryption using Intel SGX.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Functional encryption (FE) is an extremely powerful cryptographic mechanism that lets an authorized entity compute on encrypted data, and learn the results in the clear. However, all current cryptographic instantiations for general FE are too impractical to be implemented. We construct IRON, a provably secure, and practical FE system using Intel's recent Software Guard Extensions (SGX). We show that IRON can be applied to complex functionalities, and even for simple functions, outperforms the best known cryptographic schemes. We argue security by modeling FE in the context of hardware elements, and prove that IRON satisfies the security model.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134106"}, {"primary_key": "3736511", "vector": [], "sparse_vector": [], "title": "JITGuard: Hardening Just-in-time Compilers with SGX.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Memory-corruption vulnerabilities pose a serious threat to modern computer security. Attackers exploit these vulnerabilities to manipulate code and data of vulnerable applications to generate malicious behavior by means of code-injection and code-reuse attacks. Researchers already demonstrated the power of data-only attacks by disclosing secret data such as cryptographic keys in the past. A large body of literature has investigated defenses against code-injection, code-reuse, and data-only attacks. Unfortunately, most of these defenses are tailored towards statically generated code and their adaption to dynamic code comes with the price of security or performance penalties. However, many common applications, like browsers and document viewers, embed just-in-time compilers to generate dynamic code. The contribution of this paper is twofold: first, we propose a generic data-only attack against JIT compilers, dubbed DOJITA. In contrast to previous data-only attacks that aimed at disclosing secret data, DOJITA enables arbitrary code-execution. Second, we propose JITGuard, a novel defense to mitigate code-injection, code-reuse, and data-only attacks against just-in-time compilers (including DOJITA). JITGuard utilizes Intel's Software Guard Extensions (SGX) to provide a secure environment for emitting the dynamic code to a secret region, which is only known to the JIT compiler, and hence, inaccessible to the attacker. Our proposal is the first solution leveraging SGX to protect the security critical JIT compiler operations, and tackles a number of difficult challenges. As proof of concept we implemented JITGuard for Firefox's JIT compiler SpiderMon<PERSON>. Our evaluation shows reasonable overhead of 9.8% for common benchmarks.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134037"}, {"primary_key": "3736512", "vector": [], "sparse_vector": [], "title": "POSTER: Finding Vulnerabilities in P4 Programs with Assertion-based Verification.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Current trends in SDN extend network programmability to the data plane through the use of programming languages such as P4. In this context, the chance of introducing errors and consequently software vulnerabilities in the network increases significantly. Existing data plane verification mechanisms are unable to model P4 programs or present severe restrictions in the set of modeled properties. To overcome these limitations and make programmable data planes more secure, we present a P4 program verification technique based on assertion checking and symbolic execution. First, P4 programs are annotated with assertions expressing general correctness and security properties. Then, the annotated programs are transformed into C code and all their possible paths are symbolically executed. Results show that it is possible to prove properties in just a few seconds using the proposed technique. Moreover, we were able to uncover two potential vulnerabilities in a large scale P4 production application.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138837"}, {"primary_key": "3736515", "vector": [], "sparse_vector": [], "title": "Quantifying the Pressure of Legal Risks on Third-party Vulnerability Research.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Product vendors and vulnerability researchers work with the same underlying artifacts, but can be motivated by goals that are distinct and, at times, disjoint. This potential for conflict, coupled with the legal instruments available to product vendors (e.g., EULAs, DMCA, CFAA, etc.) drive a broad concern that there are \"chilling effects\" that dissuade vulnerability researchers from vigorously evaluating product security. Indeed, there are well-known examples of legal action taken against individual researchers. However, these are inherently anecdotal in nature and skeptics of the chilling-effects hypothesis argue that there is no systematic evidence to justify such concerns. This paper is motivated by precisely this tussle. We present some of the first work to address this issue on a quantitative and empirical footing, illuminating the sentiments of both product vendors and vulnerability researchers. First, we canvas a range of product companies for explicit permission to conduct security assessments and thus characterize the degree to which the broad software vendor community is supportive of vulnerability research activities and how this varies based on the nature of the researcher. Second, we conduct an online sentiment survey of vulnerability researchers to understand the extent to which they have abstract concerns or concrete experience with legal threats and the extent to which this mindset shapes their choices.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134047"}, {"primary_key": "3736516", "vector": [], "sparse_vector": [], "title": "May the Fourth Be With You: A Microarchitectural Side Channel Attack on Several Real-World Applications of Curve25519.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In recent years, applications increasingly adopt security primitives designed with better countermeasures against side channel attacks. A concrete example is Libgcrypt's implementation of ECDH encryption with Curve25519. The implementation employs the Montgomery ladder scalar-by-point multiplication, uses the unified, branchless Montgomery double-and-add formula and implements a constant-time argument swap within the ladder. However, Libgcrypt's field arithmetic operations are not implemented in a constant-time side-channel-resistant fashion.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134029"}, {"primary_key": "3736524", "vector": [], "sparse_vector": [], "title": "Better Bounds for Block Cipher Modes of Operation via Nonce-Based Key Derivation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Block cipher modes of operation provide a way to securely encrypt using a block cipher. The main factors in analyzing modes of operation are the \\emph{level of security} achieved (chosen-plaintext security, authenticated encryption, nonce-misuse resistance, and so on) and \\textit{performance}. When measuring the security level of a mode of operation, it does not suffice to consider asymptotics, and a concrete analysis is necessary. This is especially the case today, when encryption rates can be very high, and so birthday bounds may be approached or even reached. In this paper, we show that key-derivation at every encryption significantly improves the security bounds in many cases. We present a new key-derivation method that utilizes a \\emph{truncated block cipher}, and show that this is far better than standard block-cipher based key derivation. We prove that by using our key derivation method, we obtain greatly improved bounds for many modes of operation, with a result that the lifetime of a key can be significantly extended. We demonstrate this for AES-CTR (CPA-security), AES-GCM (authenticated encryption) and AES-GCM-SIV (nonce-misuse resistance). Finally, we demonstrate that when using modern hardware with AES instructions (AES-NI), the performance penalty of deriving keys at each encryption is insignificant for most uses.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133992"}, {"primary_key": "3736527", "vector": [], "sparse_vector": [], "title": "Querying for Queries: Indexes of Queries for Efficient and Expressive IT-PIR.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We propose indexes of queries, a novel mechanism for supporting efficient, expressive, and information-theoretically private single-round queries over multi-server PIR databases. Our approach decouples the way that users construct their requests for data from the physical layout of the remote data store, thereby enabling users to fetch data using \"contextual\" queries that specify which data they seek, as opposed to \"positional\" queries that specify where those data happen to reside. For example, an open-access eprint repository could employ indexes of queries to let researchers fetch academic articles via PIR queries such as for \"this year's 5 most cited papers about PIR\" or \"the 3 most recently posted papers about PIR\". Our basic approach is compatible with any PIR protocol in the ubiquitous \"vector-matrix\" model for PIR, though the most sophisticated and useful of our constructions rely on some nice algebraic properties of <PERSON>'s IT-PIR protocol (Oakland 2007). We have implemented our techniques as an extension to Percy++, an open-source implementation of <PERSON>'s IT-PIR protocol. Our experiments indicate that the new techniques can greatly improve not only utility for private information retrievers but also efficiency for private information retrievers and servers alike.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134008"}, {"primary_key": "3736530", "vector": [], "sparse_vector": [], "title": "Implementing BP-Obfuscation Using Graph-Induced Encoding.", "authors": ["<PERSON><PERSON>", "Tzipora Hale<PERSON>", "<PERSON>", "<PERSON>-<PERSON><PERSON>"], "summary": "We implemented (a simplified version of) the branching-program obfuscator due to <PERSON><PERSON> et al. (GGH15), which is itself a variation of the first obfuscation candidate by <PERSON><PERSON><PERSON> et al. (GGHRSW13). To keep within the realm of feasibility, we had to give up on some aspects of the construction, specifically the \"multiplicative bundling\" factors that protect against mixed-input attacks. Hence our implementation can only support read-once branching programs.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133976"}, {"primary_key": "3736531", "vector": [], "sparse_vector": [], "title": "Workshop on Multimedia Privacy and Security.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This workshop addresses the technical challenges arising from our current interconnected society. Multitudes of devices and people can be connected to each other by intelligent algorithms, apps, social networks, and the infrastructure set by Internet of Things (IoT). As more people and their devices are connected without much restriction, the issues of security, privacy, and trust remain a challenge. Multimedia in IoT services should provide a robust and resilient security platforms and solutions against any unauthorized access. Recent literature shows increased concerns about hacking, security breaches, data manipulation, social engineering, and new attack methods. Malware can be hidden within multimedia files and visiting infected websites can trigger its download to victims' machines. There are a multitude of techniques to steal personal information and other sensitive media for unauthorized dissemination; imposters/identity thefts are common in social networks. In order to demonstrate the effectiveness of resilient security and privacy solutions, methods such as new standards, advance cryptography, improved algorithms for intrusion detection, personalized privacy, and isolation of questionable or malicious files can be used independently or all together to minimize the threats.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3137043"}, {"primary_key": "3736532", "vector": [], "sparse_vector": [], "title": "IMF: Inferred Model-based Fuzzer.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Kernel vulnerabilities are critical in security because they naturally allow attackers to gain unprivileged root access. Although there has been much research on finding kernel vulnerabilities from source code, there are relatively few research on kernel fuzzing, which is a practical bug finding technique that does not require any source code. Existing kernel fuzzing techniques involve feeding in random input values to kernel API functions. However, such a simple approach does not reveal latent bugs deep in the kernel code, because many API functions are dependent on each other, and they can quickly reject arbitrary parameter values based on their calling context. In this paper, we propose a novel fuzzing technique for commodity OS kernels that leverages inferred dependence model between API function calls to discover deep kernel bugs. We implement our technique on a fuzzing system, called IMF. IMF has already found 32 previously unknown kernel vulnerabilities on the latest macOS version 10.12.3 (16D32) at the time of this writing.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134103"}, {"primary_key": "3736533", "vector": [], "sparse_vector": [], "title": "Watch Me, but <PERSON>&apos;t Touch Me! Contactless Control Flow Monitoring via Electromagnetic Emanations.", "authors": ["Yi Han", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Trustworthy operation of industrial control systems depends on secure and real-time code execution on the embedded programmable logic controllers (PLCs). The controllers monitor and control the critical infrastructures, such as electric power grids and healthcare platforms, and continuously report back the system status to human operators. We present Zeus, a contactless embedded controller security monitor to ensure its execution control flow integrity. Zeus leverages the electromagnetic emission by the PLC circuitry during the execution of the controller programs. Zeus's contactless execution tracking enables non-intrusive monitoring of security-critical controllers with tight real-time constraints. Those devices often cannot tolerate the cost and performance overhead that comes with additional traditional hardware or software monitoring modules. Furthermore, Zeus provides an air-gap between the monitor (trusted computing base) and the target (potentially compromised) PLC. This eliminates the possibility of the monitor infection by the same attack vectors.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134081"}, {"primary_key": "3736539", "vector": [], "sparse_vector": [], "title": "BBA+: Improving the Security and Applicability of Privacy-Preserving Point Collection.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Black-box accumulation (BBA) has recently been introduced as a building-block for a variety of user-centric protocols such as loyalty, refund, and incentive systems. Loosely speaking, this building block may be viewed as a cryptographic \"piggy bank\" that allows a user to collect points (aka incentives, coins, etc.) in an anonymous and unlinkable way. A piggy bank may be \"robbed\" at some point by a user, letting her spend the collected points, thereby only revealing the total amount inside the piggy bank and its unique serial number.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134071"}, {"primary_key": "3736544", "vector": [], "sparse_vector": [], "title": "Composing Differential Privacy and Secure Computation: A Case Study on Scaling Private Record Linkage.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Private record linkage (PRL) is the problem of identifying pairs of records that are similar as per an input matching rule from databases held by two parties that do not trust one another. We identify three key desiderata that a PRL solution must ensure: (1) perfect precision and high recall of matching pairs, (2) a proof of end-to-end privacy, and (3) communication and computational costs that scale subquadratically in the number of input records. We show that all of the existing solutions for PRL? including secure 2-party computation (S2PC), and their variants that use non-private or differentially private (DP) blocking to ensure subquadratic cost -- violate at least one of the three desiderata. In particular, S2PC techniques guarantee end-to-end privacy but have either low recall or quadratic cost. In contrast, no end-to-end privacy guarantee has been formalized for solutions that achieve subquadratic cost. This is true even for solutions that compose DP and S2PC: DP does not permit the release of any exact information about the databases, while S2PC algorithms for PRL allow the release of matching records.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134030"}, {"primary_key": "3736545", "vector": [], "sparse_vector": [], "title": "Tutorial: Private Information Retrieval.", "authors": ["<PERSON>"], "summary": "Private information retrieval (PIR) is a cryptographic primitive that facilitates the seemingly impossible task of letting users fetch records from untrusted and remote database servers without revealing to those servers which records are being fetched. The research literature on PIR is vast; in the over two decades since its 1995 introduction by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>levitz, and Sudan, the cryptography, privacy, and theoretical computer science research communities have studied PIR intensively and from a variety of perspectives. Alas, despite a series of significant advances, most privacy practitioners and theoreticians alike fall into one of two camps: (i) those who believe that PIR is so inefficient and abstruse as to make it all-but-useless in practice, and (ii) those who remain blissfully unaware that PIR even exists. Indeed, to date not even one of the numerous PIR-based applications proposed in the research literature has been deployed at scale to protect the privacy of users \"in the wild\". This tutorial targets both of the above camps, presenting a bird's-eye overview of the current state of PIR research. Topics covered will span the spectrum from purely theoretical through imminently applicable and all the high points in between, thereby providing participants with an awareness of what modern PIR techniques have (and do not have) to offer, dispelling the myth of PIR's inherent impracticality, and hopefully inspiring participants to identify practical use cases for PIR within their own niche areas of expertise. This introductory tutorial will be accessible to anyone comfortable with college-level mathematics (basic linear algebra and some elementary probability and number theory).", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3136069"}, {"primary_key": "3736546", "vector": [], "sparse_vector": [], "title": "FirmUSB: Vetting USB Device Firmware using Domain Informed Symbolic Execution.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> (Jing) <PERSON><PERSON>", "Tuba Yavuz", "<PERSON>"], "summary": "The USB protocol has become ubiquitous, supporting devices from high-powered computing devices to small embedded devices and control systems. USB's greatest feature, its openness and expandability, is also its weakness, and attacks such as BadUSB exploit the unconstrained functionality afforded to these devices as a vector for compromise. Fundamentally, it is virtually impossible to know whether a USB device is benign or malicious. This work introduces FirmUSB, a USB-specific firmware analysis framework that uses domain knowledge of the USB protocol to examine firmware images and determine the activity that they can produce. Embedded USB devices use microcontrollers that have not been well studied by the binary analysis community, and our work demonstrates how lifters into popular intermediate representations for analysis can be built, as well as the challenges of doing so. We develop targeting algorithms and use domain knowledge to speed up these processes by a factor of 7 compared to unconstrained fully symbolic execution. We also successfully find malicious activity in embedded 8051 firmwares without the use of source code. Finally, we provide insights into the challenges of symbolic analysis on embedded architectures and provide guidance on improving tools to better handle this important class of devices.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134050"}, {"primary_key": "3736547", "vector": [], "sparse_vector": [], "title": "New Techniques for Structural Batch Verification in Bilinear Groups with Applications to Groth-Sahai Proofs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Bilinear groups form the algebraic setting for a multitude of important cryptographic protocols including anonymous credentials, e-cash, e-voting, e-coupon, and loyalty systems. It is typical of such crypto protocols that participating parties need to repeatedly verify that certain equations over bilinear groups are satisfied, e.g., to check that computed signatures are valid, commitments can be opened, or non-interactive zero-knowledge proofs verify correctly. Depending on the form and number of equations this part can quickly become a performance bottleneck due to the costly evaluation of the bilinear map.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134068"}, {"primary_key": "3736551", "vector": [], "sparse_vector": [], "title": "Deep Models Under the GAN: Information Leakage from Collaborative Deep Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Deep Learning has recently become hugely popular in machine learning for its ability to solve end-to-end learning systems, in which the features and the classifiers are learned simultaneously, providing significant improvements in classification accuracy in the presence of highly-structured and large databases.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134012"}, {"primary_key": "3736552", "vector": [], "sparse_vector": [], "title": "POSTER: Covert Channel Based on the Sequential Analysis in Android Systems.", "authors": ["<PERSON>-<PERSON>", "KyungRok Won", "<PERSON><PERSON>"], "summary": "Due to the wide spread of android smartphones, different types of attacks have emerged against android systems and accordingly many researches have been accomplished in the android security. In particular, a variety of covert channels have been recently developed in android systems. They are usually built up by utilizing physical media and distinct characteristics of systems in the literature. To the best of our information, however, we do not find out any research work establishing covert channels in android systems on basis of the sequential analysis, which is a kind of statistical decision theory. This is mainly because the sequential analysis has been conventionally treated as defense technique in terms of security. In contrast to this common application of the sequential analysis, we discover a new covert channel based on the sequential analysis in android systems. The key idea of newly devised covert channel is to harness the sequential analysis in order to encode (resp. decode) private information bits to (resp. from) multiple sequences of randomly selected data. Through simulation, we demonstrate that our developed covert channel works efficiently and thus it could be substantial threat to android systems.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138847"}, {"primary_key": "3736553", "vector": [], "sparse_vector": [], "title": "S3ORAM: A Computation-Efficient and Constant Client Bandwidth Blowup ORAM with S<PERSON>ir Secret Sharing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Oblivious Random Access Machine (ORAM) enables a client to access her data without leaking her access patterns. Existing client-efficient ORAMs either achieve O(log N) client-server communication blowup without heavy computation, or O(1) blowup but with expensive homomorphic encryptions. It has been shown that O(log N) bandwidth blowup might not be practical for certain applications, while schemes with O(1) communication blowup incur even more delay due to costly homomorphic operations.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134090"}, {"primary_key": "3736554", "vector": [], "sparse_vector": [], "title": "POSTER: Why Are You Going That Way? Measuring Unnecessary Exposure of Network Traffic to Nation States.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this work, we examine to what extent the Internet's routing infrastructure needlessly exposes network traffic to nations geographically irrelevant to packet transmission. We quantify what countries are geographically logical to see on a network path traveling between two nations through the use of convex hulls circumscribing major population centers, and then compare that to the nation states observed in utilized paths. Our preliminary results show that the majority of paths, 52%, unnecessarily expose traffic to at least one nation. We also explore which nation states are disproportionately allowed to observe and manipulate a larger fraction of Internet traffic than they otherwise should.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138842"}, {"primary_key": "3736559", "vector": [], "sparse_vector": [], "title": "The ART of App Compartmentalization: Compiler-based Library Privilege Separation on Stock Android.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Third-party libraries are commonly used by app developers for alleviating the development efforts and for monetizing their apps. On Android, the host app and its third-party libraries reside in the same sandbox and share all privileges awarded to the host app by the user, putting the users' privacy at risk of intrusions by third-party libraries. In this paper, we introduce a new privilege separation approach for third-party libraries on stock Android. Our solution partitions Android applications at compile-time into isolated, privilege-separated compartments for the host app and the included third-party libraries. A particular benefit of our approach is that it leverages compiler-based instrumentation available on stock Android versions and thus abstains from modification of the SDK, the app bytecode, or the device firmware. A particular challenge for separating libraries from their host apps is the reconstruction of the communication channels and the preservation of visual fidelity between the now separated app and its libraries. We solve this challenge through new IPC-based protocols to synchronize layout and lifecycle management between different sandboxes. Finally, we demonstrate the efficiency and effectiveness of our solution by applying it to real world apps from the Google Play Store that contain advertisements.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134064"}, {"primary_key": "3736560", "vector": [], "sparse_vector": [], "title": "FlashGuard: Leveraging Intrinsic Flash Properties to Defend Against Encryption Ransomware.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Encryption ransomware is a malicious software that stealthily encrypts user files and demands a ransom to provide access to these files. Several prior studies have developed systems to detect ransomware by monitoring the activities that typically occur during a ransomware attack. Unfortunately, by the time the ransomware is detected, some files already undergo encryption and the user is still required to pay a ransom to access those files. Furthermore, ransomware variants can obtain kernel privilege, which allows them to terminate software-based defense systems, such as anti-virus. While periodic backups have been explored as a means to mitigate ransomware, such backups incur storage overheads and are still vulnerable as ransomware can obtain kernel privilege to stop or destroy backups. Ideally, we would like to defend against ransomware without relying on software-based solutions and without incurring the storage overheads of backups.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134035"}, {"primary_key": "3736563", "vector": [], "sparse_vector": [], "title": "Exploiting a Thermal Side Channel for Power Attacks in Multi-Tenant Data Centers.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The power capacity of multi-tenant data centers is typically oversubscribed in order to increase the utilization of expensive power infrastructure. This practice can create dangerous situations and compromise data center availability if the designed power capacity is exceeded. This paper demonstrates that current safeguards are vulnerable to well-timed power attacks launched by malicious tenants (i.e., attackers). Further, we demonstrate that there is a physical side channel --- a thermal side channel due to hot air recirculation --- that contains information about the benign tenants' runtime power usage and can enable a malicious tenant to time power attacks effectively. In particular, we design a state-augmented Kalman filter to extract this information from the side channel and guide an attacker to use its maximum power at moments that coincide with the benign tenants' high power demand, thus overloading the shared power capacity. Our experimental results show that an attacker can capture 54% of all attack opportunities, significantly compromising the data center availability. Finally, we discuss a set of possible defense strategies to safeguard the data center infrastructure against power attacks.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133994"}, {"primary_key": "3736566", "vector": [], "sparse_vector": [], "title": "HexType: Efficient Detection of Type Confusion Errors for C++.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON> Lee", "<PERSON>"], "summary": "Type confusion, often combined with use-after-free, is the main attack vector to compromise modern C++ software like browsers or virtual machines. Typecasting is a core principle that enables modularity in C++. For performance, most typecasts are only checked statically, i.e., the check only tests if a cast is allowed for the given type hierarchy, ignoring the actual runtime type of the object. Using an object of an incompatible base type instead of a derived type results in type confusion. Attackers abuse such type confusion issues to attack popular software products including Adobe Flash, PHP, Google Chrome, or Firefox. We propose to make all type checks explicit, replacing static checks with full runtime type checks. To minimize the performance impact of our mechanism HexType, we develop both low-overhead data structures and compiler optimizations. To maximize detection coverage, we handle specific object allocation patterns, e.g., placement new or reinterpret_cast which are not handled by other mechanisms. Our prototype results show that, compared to prior work, HexType has at least 1.1 -- 6.1 times higher coverage on Firefox benchmarks. For SPEC CPU2006 benchmarks with overhead, we show a 2 -- 33.4 times reduction in overhead. In addition, HexType discovered 4 new type confusion bugs in Qt and Apache Xerces-C++.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134062"}, {"primary_key": "3736567", "vector": [], "sparse_vector": [], "title": "RAIN: Refinable Attack Investigation with On-demand Inter-Process Information Flow Tracking.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "As modern attacks become more stealthy and persistent, detecting or preventing them at their early stages becomes virtually impossible. Instead, an attack investigation or provenance system aims to continuously monitor and log interesting system events with minimal overhead. Later, if the system observes any anomalous behavior, it analyzes the log to identify who initiated the attack and which resources were affected by the attack and then assess and recover from any damage incurred. However, because of a fundamental tradeoff between log granularity and system performance, existing systems typically record system-call events without detailed program-level activities (e.g., memory operation) required for accurately reconstructing attack causality or demand that every monitored program be instrumented to provide program-level information.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134045"}, {"primary_key": "3736568", "vector": [], "sparse_vector": [], "title": "DEFTL: Implementing Plausibly Deniable Encryption in Flash Translation Layer.", "authors": ["<PERSON><PERSON><PERSON>", "Luning Xia", "<PERSON>", "<PERSON><PERSON>"], "summary": "Mobile devices today have been increasingly used to store and process sensitive information. To protect sensitive data, mobile operating systems usually incorporate a certain level of encryption to protect sensitive data. However, conventional encryption cannot defend against a coercive attacker who can capture the device owner, and force the owner to disclose keys used for decrypting sensitive information. To defend against such a coercive adversary, Plausibly Deniable Encryption (PDE) was introduced to allow the device owner to deny the very existence of sensitive data stored on his/her device. The existing PDE systems, built on flash storage devices, are problematic, since they either neglect the special nature of the underlying storage medium (which is usually NAND flash), or suffer from deniability compromises.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134011"}, {"primary_key": "3736569", "vector": [], "sparse_vector": [], "title": "POSTER: PriReMat: A Distributed Tool for Privacy Preserving Record Linking in Healthcare.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Medical institutions must comply with various federal and state policies when they share sensitive medical data with others. Traditionally, such sharing is performed by sanitizing the identifying information from individual records. However, such sanitization removes the ability to later link the records belonging to the same patient across multiple institutions which is essential for medical cohort discovery. Currently, human honest brokers assume stewardship of non sanitized data and manually facilitate such cohort discovery. However, this is slow and prone to error, not to mention that any compromise of the honest broker breaks the system. In this work, we describe PriReMat, a toolset that we have developed for privacy preserving record linkage. The underlying protocol is based on strong security primitives that we had presented earlier. This work describes the distributed implementation over untrusted machines and networks.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138845"}, {"primary_key": "3736571", "vector": [], "sparse_vector": [], "title": "CCSW&apos;17: 2017 ACM Cloud Computing Security.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The use and prevalence of cloud and large-scale computing infrastructures is increasing. They are projected to be a dominant trend in computing for the foreseeable future: major cloud operators are now estimated to house millions of machines each and to host substantial (and growing) fractions of corporate and government IT and web infrastructure. CCSW is a forum for bringing together researchers and practitioners to discuss the challenges and implications of current and future trends to the security of cloud operators, tenants, and the larger Internet community. Of special interest are the security challenges from the integration of cloud infrastructures with IoT and mobile application deployments. CCSW welcomes submissions on new threats, countermeasures, and opportunities brought about by the move to cloud computing, with a preference for unconventional approaches, as well as measurement studies and case studies that shed light on the security implications of cloud infrastructure and use cases.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3137050"}, {"primary_key": "3736574", "vector": [], "sparse_vector": [], "title": "POSTER: AFL-based Fuzzing for Java with Kelinci.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Corina S<PERSON>"], "summary": "Grey-box fuzzing is a random testing technique that has been shown to be effective at finding security vulnerabilities in software. The technique leverages program instrumentation to gather information about the program with the goal of increasing the code coverage during fuzzing, which makes gray-box fuzzers extremely efficient vulnerability detection tools. One such tool is AFL, a grey-box fuzzer for C programs that has been used successfully to find security vulnerabilities and other critical defects in countless software products. We present Kelinci, a tool that interfaces AFL with instrumented Java programs. The tool does not require modifications to AFL and is easily parallelizable. Applying AFL-type fuzzing to Java programs opens up the possibility of testing Java based applications using this powerful technique. We show the effectiveness of <PERSON><PERSON><PERSON> by applying it on the image processing library Apache Commons Imaging, in which it identified a bug within one hour.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138820"}, {"primary_key": "3736575", "vector": [], "sparse_vector": [], "title": "Revive: Rebalancing Off-Blockchain Payment Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Scaling the transaction throughput of decentralized blockchain ledgers such as Bitcoin and Ethereum has been an ongoing challenge. Two-party duplex payment channels have been designed and used as building blocks to construct linked payment networks, which allow atomic and trust-free payments between parties without exhausting the resources of the blockchain.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134033"}, {"primary_key": "3736577", "vector": [], "sparse_vector": [], "title": "Certified Malware: Measuring Breaches of Trust in the Windows Code-Signing PKI.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Digitally signed malware can bypass system protection mechanisms that install or launch only programs with valid signatures. It can also evade anti-virus programs, which often forego scanning signed binaries. Known from advanced threats such as Stuxnet and Flame, this type of abuse has not been measured systematically in the broader malware landscape. In particular, the methods, effectiveness window, and security implications of code-signing PKI abuse are not well understood. We propose a threat model that highlights three types of weaknesses in the code-signing PKI. We overcome challenges specific to code-signing measurements by introducing techniques for prioritizing the collection of code signing certificates that are likely abusive. We also introduce an algorithm for distinguishing among different types of threats. These techniques allow us to study threats that breach the trust encoded in the Windows code signing PKI. The threats include stealing the private keys associated with benign certificates and using them to sign malware or by impersonating legitimate companies that do not develop software and, hence, do not own code-signing certificates. Finally, we discuss the actionable implications of our findings and propose concrete steps for improving the security of the code-signing ecosystem.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133958"}, {"primary_key": "3736578", "vector": [], "sparse_vector": [], "title": "Forward Secure Dynamic Searchable Symmetric Encryption with Efficient Updates.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Je <PERSON> Park", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "The recently proposed file-injection type attacks are highlighting the importance of forward security in dynamic searchable symmetric encryption (DSSE). Forward security enables to thwart those attacks by hiding the information about the newly added files matching a previous search query. However, there are still only a few DSSE schemes that provide forward security, and they have factors that hinder efficiency. In particular, all of these schemes do not support actual data deletion, which increments both storage space and computational complexity. In this paper, we design and implement a forward secure DSSE scheme with optimal search and update complexity, for both computation and communication point of view. As a starting point, we propose a new, simple, theoretical data structure, called dual dictionary that can take advantage of both the inverted and the forward indexes at the same time. This data structure allows to delete data explicitly and in real time, which greatly improves efficiency compared to previous works. In addition, our scheme provides forward security by encrypting the newly added data with fresh keys not related with the previous search tokens. We implemented our scheme for Enron email and Wikipedia datasets and measured its performance. The comparison with Sophos shows that our scheme is very efficient in practice, for both searches and updates in dynamic environments.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133970"}, {"primary_key": "3736579", "vector": [], "sparse_vector": [], "title": "POSTER: Rethinking Fingerprint Identification on Smartphones.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Lee", "Taekyoung Kwon"], "summary": "Modern smartphones popularly adopt a small touch sensor for fingerprint identification of a user, but it captures only a partial limited portion of a fingerprint. Recently we have studied a gap between actual risk and user perception of latent fingerprints remaining on a smartphone, and developed a fake fingerprint attack that exploits the latent fingerprints as actual risk. We successfully reconstructed a fake fingerprint image in good quality for small touch sensors. In this paper, we subsequently conduct post hoc experimental studies on the facts that we have missed or have since learned. First of all, we examine that the presented attack is not conceptual but realistic. We employ the reconstructed image and make its fake fingerprint, using a conductive printing or a silicon-like glue, to pass directly the touch sensor of real smartphones. Our target smartphones are Samsung Galaxy S6, S7 and iPhone 5s, 6, 7. Indeed we have succeeded in passing Galaxy S6, S7, and now work on the remaining smartphones. We also conduct an experimental study for one of our mitigation methods to see how it can reduce actual risk. Finally, we perform a user survey study to understand user perception on the fake fingerprint attacks and the mitigation methods.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138832"}, {"primary_key": "3736580", "vector": [], "sparse_vector": [], "title": "CCS&apos;17 Tutorial Abstract / SGX Security and Privacy.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Chia-Che Tsai"], "summary": "In this tutorial, we will first introduce the basic concepts of Intel SGX, its development workflows, potential applications and performance characteristics. Then, we will explain known security concerns, including cache/branch side-channel attacks and memory safety issues, and corresponding defenses with various working demos. Last but not least, we will introduce various ways to quickly start writing SGX applications, especially by utilizing library OSes or thin shielding layers; we will explain the pros and cons of each approach in terms of security and usability.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3136068"}, {"primary_key": "3736582", "vector": [], "sparse_vector": [], "title": "FEAST 2017: The Second Workshop on Forming an Ecosystem Around Software Transformation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Second Workshop on Forming an Ecosystem Around Software Transformation (FEAST 2017) is held in conjunction with the 24th ACM Conference on Computer and Communications Security (CCS 2017) on November 3, 2017 in Dallas, Texas. The workshop is geared toward discussion and understanding of several critical topics surrounding software executable transformation for improving the security and efficiency of all software used in security-critical applications. The scope of discussion for this workshop includes topics that may be necessary to fully exploit the power and impact of late-stage software customization effort.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3137052"}, {"primary_key": "3736583", "vector": [], "sparse_vector": [], "title": "Hiding in Plain Sight: A Longitudinal Study of Combosquatting Abuse.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Chen", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Domain squatting is a common adversarial practice where attackers register domain names that are purposefully similar to popular domains. In this work, we study a specific type of domain squatting called \"combosquatting,\" in which attackers register domains that combine a popular trademark with one or more phrases (e.g., betterfacebook[.]com, youtube-live[.]com). We perform the first large-scale, empirical study of combosquatting by analyzing more than 468 billion DNS records - collected from passive and active DNS data sources over almost six years. We find that almost 60% of abusive combosquatting domains live for more than 1,000 days, and even worse, we observe increased activity associated with combosquatting year over year. Moreover, we show that combosquatting is used to perform a spectrum of different types of abuse including phishing, social engineering, affiliate abuse, trademark abuse, and even advanced persistent threats. Our results suggest that combosquatting is a real problem that requires increased scrutiny by the security community.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134002"}, {"primary_key": "3736585", "vector": [], "sparse_vector": [], "title": "POSTER: X-Ray Your DNS.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We design and develop DNS X-Ray which performs analyses of DNS platforms on the networks where it is invoked. The analysis identifies the caches and the IP addresses used by the DNS platform, fingerprints the DNS software on the caches, and evaluates vulnerabilities allowing injection of spoofed records into the caches. DNS X-Ray is the first tool to perform an extensive analysis of the caching component on the DNS platforms. In addition, DNS X-Ray also provides statistics from previous invocations, enabling networks to check which for popular DNS software on the caches, the number of caches typically used on DNS platforms and more. We set up DNS X-Ray online, it can be accessed via a website http://www.dns.xray.sit.fraunhofer.de.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138821"}, {"primary_key": "3736587", "vector": [], "sparse_vector": [], "title": "T/Key: Second-Factor Authentication From Secure Hash Chains.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Time-based one-time password (TOTP) systems in use today require storing secrets on both the client and the server. As a result, an attack on the server can expose all second factors for all users in the system. We present T/Key, a time-based one-time password system that requires no secrets on the server. Our work modernizes the classic S/Key system and addresses the challenges in making such a system secure and practical. At the heart of our construction is a new lower bound analyzing the hardness of inverting hash chains composed of independent random functions, which formalizes the security of this widely used primitive. Additionally, we develop a near-optimal algorithm for quickly generating the required elements in a hash chain with little memory on the client. We report on our implementation of T/Key as an Android application. T/Key can be used as a replacement for current TOTP systems, and it remains secure in the event of a server-side compromise. The cost, as with S/Key, is that one-time passwords are longer than the standard six characters used in TOTP.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133989"}, {"primary_key": "3736588", "vector": [], "sparse_vector": [], "title": "POSTER: Hidden in Plain Sight: A Filesystem for Data Integrity and Confidentiality.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A filesystem capable of curtailing data theft and ensuring file integrity protection through deception is introduced and evaluated. The deceptive filesystem transparently creates multiple levels of stacking to protect the base filesystem and monitor file accesses, hide and redact sensitive files with baits, and inject decoys onto fake system views purveyed to untrusted subjects, all while maintaining a pristine state to legitimate processes. Our prototype implementation leverages a kernel hot-patch to seamlessly integrate the new filesystem module into live and existing environments. We demonstrate the utility of our approach with a use case on the nefarious Erebus ransomware. We also show that the filesystem adds no I/O overhead for legitimate users.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138841"}, {"primary_key": "3736590", "vector": [], "sparse_vector": [], "title": "Practical Multi-party Private Set Intersection from Symmetric-Key Techniques.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a new paradigm for multi-party private set intersection (PSI) that allows $n$ parties to compute the intersection of their datasets without revealing any additional information. We explore a variety of instantiations of this paradigm. Our protocols avoid computationally expensive public-key operations and are secure in the presence of any number of semi-honest participants (i.e., without an honest majority).", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134065"}, {"primary_key": "3736591", "vector": [], "sparse_vector": [], "title": "DUPLO: Unifying Cut-and-Choose for Garbled Circuits.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Cut-and-choose (CandC) is the standard approach to making <PERSON>'s garbled circuit two-party computation (2PC) protocol secure against malicious adversaries. Traditional cut-and-choose operates at the level of entire circuits, whereas the LEGO paradigm (<PERSON> & <PERSON>i, TCC 2009) achieves asymptotic improvements by performing cut-and-choose at the level of individual gates. In this work we propose a unified approach called DUPLO that spans the entire continuum between these two extremes. The cut-and-choose step in our protocol operates on the level of arbitrary circuit \"components,\" which can range in size from a single gate to the entire circuit itself. With this entire continuum of parameter values at our disposal, we find that the best way to scale 2PC to computations of realistic size is to use C&C components of intermediate size, and not at the extremes. On computations requiring several millions of gates or more, our more general approach to C&C gives between 4-7x improvement over existing approaches. In addition to our technical contributions of modifying and optimizing previous protocol techniques to work with general C&C components, we also provide an extension of the recent Frigate circuit compiler (<PERSON><PERSON> et al, EuroS&P 2016) to effectively express any C-style program in terms of components which can be processed efficiently using our protocol.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133991"}, {"primary_key": "3736593", "vector": [], "sparse_vector": [], "title": "Capturing Malware Propagations with Code Injections and Code-Reuse Attacks.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Defending against malware involves analysing large amounts of suspicious samples. To deal with such quantities we rely heavily on automatic approaches to determine whether a sample is malicious or not. Unfortunately, complete and precise automatic analysis of malware is far from an easy task. This is because malware is often designed to contain several techniques and countermeasures specifically to hinder analysis. One of these techniques is for the malware to propagate through the operating system so as to execute in the context of benign processes. The malware does this by writing memory to a given process and then proceeds to have this memory execute. In some cases these propagations are trivial to capture because they rely on well-known techniques. However, in the cases where malware deploys novel code injection techniques, rely on code-reuse attacks and potentially deploy dynamically generated code, the problem of capturing a complete and precise view of the malware execution is non-trivial.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134099"}, {"primary_key": "3736595", "vector": [], "sparse_vector": [], "title": "Synthesis of Probabilistic Privacy Enforcement.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Existing probabilistic privacy enforcement approaches permit the execution of a program that processes sensitive data only if the information it leaks is within the bounds specified by a given policy. Thus, to extract any information, users must manually design a program that satisfies the policy. In this work, we present a novel synthesis approach that automatically transforms a program into one that complies with a given policy. Our approach consists of two ingredients. First, we phrase the problem of determining the amount of leaked information as Bayesian inference, which enables us to leverage existing probabilistic programming engines. Second, we present two synthesis procedures that add uncertainty to the program's outputs as a way of reducing the amount of leaked information: an optimal one based on SMT solving and a greedy one with quadratic running time. We implemented and evaluated our approach on 10 representative programs from multiple application domains. We show that our system can successfully synthesize a permissive enforcement mechanism for all examples.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134079"}, {"primary_key": "3736599", "vector": [], "sparse_vector": [], "title": "Be Selfish and Avoid Dilemmas: Fork After Withholding (FAW) Attacks on Bitcoin.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yun<PERSON><PERSON> Son", "<PERSON>", "<PERSON><PERSON>"], "summary": "In the Bitcoin system, participants are rewarded for solving cryptographic puzzles. In order to receive more consistent rewards over time, some participants organize mining pools and split the rewards from the pool in proportion to each participant's contribution. However, several attacks threaten the ability to participate in pools. The block withholding (BWH) attack makes the pool reward system unfair by letting malicious participants receive unearned wages while only pretending to contribute work. When two pools launch BWH attacks against each other, they encounter the miner's dilemma: in a Nash equilibrium, the revenue of both pools is diminished. In another attack called selfish mining, an attacker can unfairly earn extra rewards by deliberately generating forks. In this paper, we propose a novel attack called a fork after withholding (FAW) attack. FAW is not just another attack. The reward for an FAW attacker is always equal to or greater than that for a BWH attacker, and it is usable up to four times more often per pool than in BWH attack. When considering multiple pools - the current state of the Bitcoin network - the extra reward for an FAW attack is about 56% more than that for a BWH attack. Furthermore, when two pools execute FAW attacks on each other, the miner's dilemma may not hold: under certain circumstances, the larger pool can consistently win. More importantly, an FAW attack, while using intentional forks, does not suffer from practicality issues, unlike selfish mining. We also discuss partial countermeasures against the FAW attack, but finding a cheap and efficient countermeasure remains an open problem. As a result, we expect to see FAW attacks among mining pools.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134019"}, {"primary_key": "3736600", "vector": [], "sparse_vector": [], "title": "16th Workshop on Privacy in the Electronic Society (WPES 2017).", "authors": ["<PERSON>"], "summary": "The 16th Workshop on Privacy in the Electronic Society was held on October 30, 2017 in conjunction with the 24th ACM Conference on Computer and Communications Security (CCS 2017) in Dallas, Texas, USA. The goal of WPES is to bring together a diverse group of privacy researchers and practitioners to discuss privacy problems that arise in global, interconnected societies, and potential solutions to them. The program for the workshop contains 14 full papers and 5 short papers selected from a total of 56 submissions. Specific topics covered in the program include but are not limited to: de-anonymization, fingerprinting and profiling, location privacy, and private memory systems.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3137047"}, {"primary_key": "3736602", "vector": [], "sparse_vector": [], "title": "POSTER: Watch Out Your Smart Watch When Paired.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Taekyoung Kwon"], "summary": "We coin a new term called \\textit{data transfusion} as a phenomenon that a user experiences when pairing a wearable device with the host device. A large amount of data stored in the host device (e.g., a smartphone) is forcibly copied to the wearable device (e.g., a smart watch) due to pairing while the wearable device is usually less attended. To the best of knowledge, there is no previous work that manipulates how sensitive data is transfused even without user's consent and how users perceive and behave regarding such a phenomenon for smart watches. We tackle this problem by conducting an experimental study of data extraction from commodity devices, such as in Android Wear, watchOS, and Tizen platforms, and a following survey study with 205 smart watch users, in two folds. The experimental studies have shown that a large amount of sensitive data was transfused, but there was not enough user notification. The survey results have shown that users have lower perception on smart watches for security and privacy than smartphones, but they tend to set the same passcode on both devices when needed. Based on the results, we perform risk assessment and discuss possible mitigation that involves volatile transfusion.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138831"}, {"primary_key": "3736603", "vector": [], "sparse_vector": [], "title": "Vulnerable Implicit Service: A Revisit.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The services in Android applications can be invoked either explicitly or implicitly before Android 5.0. However, since the implicit service invocations suffer service hijacking attacks and thus lead to sensitive information leakage, they have been forbidden since Android 5.0. Thereafter since the Android system will simply throw an exception and crash the application that still invokes services implicitly, it was expected that application developers will be forced to convert the implicit service invocations to explicit ones by specifying the package name of the service to be called.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133975"}, {"primary_key": "3736604", "vector": [], "sparse_vector": [], "title": "Code-Reuse Attacks for the Web: Breaking Cross-Site Scripting Mitigations via Script Gadgets.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Cross-Site Scripting (XSS) is an unremitting problem for the Web. Since its initial public documentation in 2000 until now, XSS has been continuously on top of the vulnerability statistics. Even though there has been a considerable amount of research and developer education to address XSS on the source code level, the overall number of discovered XSS problems remains high. Because of this, various approaches to mitigate XSS have been proposed as a second line of defense, with HTML sanitizers, Web Application Firewalls, browser-based XSS filters, and the Content Security Policy being some prominent examples. Most of these mechanisms focus on script tags and event handlers, either by removing them from user-provided content or by preventing their script code from executing.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134091"}, {"primary_key": "3736605", "vector": [], "sparse_vector": [], "title": "Rewriting History: Changing the Archived Web from the Present.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Internet Archive's Wayback Machine is the largest modern web archive, preserving web content since 1996. We discover and analyze several vulnerabilities in how the Wayback Machine archives data, and then leverage these vulnerabilities to create what are to our knowledge the first attacks against a user's view of the archived web. Our vulnerabilities are enabled by the unique interaction between the Wayback Machine's archives, other websites, and a user's browser, and attackers do not need to compromise the archives in order to compromise users' views of a stored page. We demonstrate the effectiveness of our attacks through proof-of-concept implementations. Then, we conduct a measurement study to quantify the prevalence of vulnerabilities in the archive. Finally, we explore defenses which might be deployed by archives, website publishers, and the users of archives, and present the prototype of a defense for clients of the Wayback Machine, ArchiveWatcher.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134042"}, {"primary_key": "3736607", "vector": [], "sparse_vector": [], "title": "A Large-Scale Empirical Study of Security Patches.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Given how the \"patching treadmill\" plays a central role for enabling sites to counter emergent security concerns, it behooves the security community to understand the patch development process and characteristics of the resulting fixes. Illumination of the nature of security patch development can inform us of shortcomings in existing remediation processes and provide insights for improving current practices. In this work we conduct a large-scale empirical study of security patches, investigating more than 4,000 bug fixes for over 3,000 vulnerabilities that affected a diverse set of 682 open-source software projects. For our analysis we draw upon the National Vulnerability Database, information scraped from relevant external references, affected software repositories, and their associated security fixes. Leveraging this diverse set of information, we conduct an analysis of various aspects of the patch development life cycle, including investigation into the duration of impact a vulnerability has on a code base, the timeliness of patch development, and the degree to which developers produce safe and reliable fixes. We then characterize the nature of security fixes in comparison to other non-security bug fixes, exploring the complexity of different types of patches and their impact on code bases.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134072"}, {"primary_key": "3736608", "vector": [], "sparse_vector": [], "title": "No-Match Attacks and Robust Partnering Definitions: Defining Trivial Attacks for Security Protocols is Not Trivial.", "authors": ["<PERSON>", "<PERSON>"], "summary": "An essential cornerstone of the definition of security for key exchange protocols is the notion of partnering. The de-facto standard definition of partnering is that of (partial) matching conversations (MC), which essentially states that two processes are partnered if every message sent by the first is actually received by the second and vice versa. We show that proving security under MC-based definitions is error-prone. To this end, we introduce no-match attacks, a new class of attacks that renders many existing security proofs invalid. We show that no-match attacks are often hard to avoid in MC-based security definitions without a) modifications of the original protocol or b) resorting to the use of cryptographic primitives with special properties. Finally, we show several ways to thwart no-match attacks. Most notably and as one of our major contributions, we provide a conceptually new definition of partnering that circumvents the problems of a MC-based partnering notion while preserving all its advantages. Our new notion of partnering not only makes security definitions for key exchange model practice much more closely. In contrast to many other security notions of key exchange it also adheres to the high standards of good cryptographic definitions: it is general, supports cryptographic intuition, allows for efficient falsification, and provides a fundamental composition property that MC-based notions lack.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134006"}, {"primary_key": "3736609", "vector": [], "sparse_vector": [], "title": "Unleashing the Walking Dead: Understanding Cross-App Remote Infections on Mobile WebViews.", "authors": ["<PERSON><PERSON> Li", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As a critical feature for enhancing user experience, cross-app URL invocation has been reported to cause unauthorized execution of app components. Although protection has already been put in place, little has been done to understand the security risks of navigating an app's WebView through an URL, a legitimate need for displaying the app's UI during cross-app interactions. In our research, we found that the current design of such cross-WebView navigation actually opens the door to a cross-app remote infection, allowing a remote adversary to spread malicious web content across different apps' WebView instances and acquire stealthy and persistent control of these apps. This new threat, dubbed Cross-App WebView Infection (XAWI), enables a series of multi-app, colluding attacks never thought before, with significant real world impacts. Particularly, we found that the remote adversary can collectively utilize multiple infected apps' individual capabilities to escalate his privileges on a mobile device or orchestrate a highly realistic remote Phishing attack (e.g., running a malicious script in Chrome to stealthily change Twitter's WebView to fake Twitter's own login UI). We show that the adversary can easily find such attack \"building blocks\" (popular apps whose WebViews can be redirected by another app) through an automatic fuzz, and discovered about 7.4% of the most popular apps subject to the XAWI attacks, including Facebook, Twitter, Amazon and others. Our study reveals the contention between the demand for convenient cross-WebView communication and the need for security control on the channel, and makes the first step toward building OS-level protection to safeguard this fast-growing technology.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134021"}, {"primary_key": "3736610", "vector": [], "sparse_vector": [], "title": "POSTER: Intrusion Detection System for In-vehicle Networks using Sensor Correlation and Integration.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The increasing utilization of Electronic Control Units (ECUs) and wireless connectivity in modern vehicles has favored the emergence of security issues. Recently, several attacks have been demonstrated against in-vehicle networks therefore drawing significant attention. This paper presents an Intrusion Detection System (IDS) based on a regression learning approach which estimates certain parameters by using correlated/redundant data. The estimated values are compared to observed ones to identify abnormal contexts that would indicate intrusion. Experiments performed with real-world vehicular data have shown that more than 90% of vehicle speed data can be precisely estimated within the error bound of 3 kph. The proposed IDS is capable of detecting and localizing attacks in real-time, which is fundamental to achieve automotive security.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138843"}, {"primary_key": "3736611", "vector": [], "sparse_vector": [], "title": "POSTER: Practical Fraud Transaction Prediction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Nowadays, online payment systems play more and more important roles in people's daily lives. A key component of these systems is to detect and prevent fraud transactions. In industrial practice, such a task is separated into two phases: 1) mining evidential features to describe users, 2) building an effective model based on these features. Generally speaking, the most popular fraud transaction detection systems use elaborately designed features to build tree based models, sometimes a subsequent linear model is added to improve the behaviour. However, the designed features usually contains only static features, while dynamic features are not considered. In addition, the subsequent model can only learn a linear combination, which may always be unsatisfactory. To address these issues, we present a systematic method, which extracts not only users' static features but also dynamic features based on their recent behaviors. Moreover, N-GRAM model is employed to handle the dynamic features so that time series information is addressed. Based on the extracted features, a tree based model is applied and the outputs of it are regarded as new generated feature representations, which will be further inputted into a Deep Neural Network (DNN) to learn the complex relationships and form the final classification model. Extensive experiments show that our proposed model (with both static and dynamic features) significantly outperforms the existing methods.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138826"}, {"primary_key": "3736613", "vector": [], "sparse_vector": [], "title": "POSTER: Vulnerability Discovery with Function Representation Learning from Unlabeled Projects.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In cybersecurity, vulnerability discovery in source code is a fundamental problem. To automate vulnerability discovery, Machine learning (ML) based techniques has attracted tremendous attention. However, existing ML-based techniques focus on the component or file level detection, and thus considerable human effort is still required to pinpoint the vulnerable code fragments. Using source code files also limit the generalisability of the ML models across projects. To address such challenges, this paper targets at the function-level vulnerability discovery in the cross-project scenario. A function representation learning method is proposed to obtain the high-level and generalizable function representations from the abstract syntax tree (AST). First, the serialized ASTs are used to learn project independence features. Then, a customized bi-directional LSTM neural network is devised to learn the sequential AST representations from the large number of raw features. The new function-level representation demonstrated promising performance gain, using a unique dataset where we manually labeled 6000+ functions from three open-source projects. The results confirm that the huge potential of the new AST-based function representation learning.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138840"}, {"primary_key": "3736614", "vector": [], "sparse_vector": [], "title": "A Framework for Constructing Fast MPC over Arithmetic Circuits with Malicious Adversaries and an Honest-Majority.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Protocols for secure multiparty computation enable a set of parties to compute a function of their inputs without revealing anything but the output. The security properties of the protocol must be preserved in the presence of adversarial behavior. The two classic adversary models considered are semi-honest (where the adversary follows the protocol specification but tries to learn more than allowed by examining the protocol transcript) and malicious (where the adversary may follow any arbitrary attack strategy). Protocols for semi-honest adversaries are often far more efficient, but in many cases the security guarantees are not strong enough.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133999"}, {"primary_key": "3736615", "vector": [], "sparse_vector": [], "title": "POSTER: Neural Network-based Graph Embedding for Malicious Accounts Detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a neural network based graph embedding method for detecting malicious accounts at Alipay, one of the world's leading mobile payment platform. Our method adaptively learns discriminative embeddings from an account-device graph based on two fundamental weaknesses of attackers, i.e. device aggregation and activity aggregation. Experiments show that our method achieves outstanding precision-recall curve compared with existing methods.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138827"}, {"primary_key": "3736616", "vector": [], "sparse_vector": [], "title": "Oblivious Neural Network Predictions via MiniONN Transformations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>. <PERSON>"], "summary": "Machine learning models hosted in a cloud service are increasingly popular but risk privacy: clients sending prediction requests to the service need to disclose potentially sensitive information. In this paper, we explore the problem of privacy-preserving predictions: after each prediction, the server learns nothing about clients' input and clients learn nothing about the model.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134056"}, {"primary_key": "3736617", "vector": [], "sparse_vector": [], "title": "Don&a<PERSON>s;t Let One Rotten Apple Spoil the Whole Barrel: Towards Automated Detection of Shadowed Domains.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Haining <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Domain names have been exploited for illicit online activities for decades. In the past, miscreants mostly registered new domains for their attacks. However, the domains registered for malicious purposes can be deterred by existing reputation and blacklisting systems. In response to the arms race, miscreants have recently adopted a new strategy, called domain shadowing, to build their attack infrastructures. Specifically, instead of registering new domains, miscreants are beginning to compromise legitimate ones and spawn malicious subdomains under them. This has rendered almost all existing countermeasures ineffective and fragile because subdomains inherit the trust of their apex domains, and attackers can virtually spawn an infinite number of shadowed domains.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134049"}, {"primary_key": "3736621", "vector": [], "sparse_vector": [], "title": "PtrSplit: Supporting General Pointers in Automatic Program Partitioning.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Partitioning a security-sensitive application into least-privileged components and putting each into a separate protection domain have long been a goal of security practitioners and researchers. However, a stumbling block to automatically partitioning C/C++ applications is the presence of pointers in these applications. Pointers make calculating data dependence, a key step in program partitioning, difficult and hard to scale; furthermore, C/C++ pointers do not carry bounds information, making it impossible to automatically marshall and unmarshall pointer data when they are sent across the boundary of partitions. In this paper, we propose a set of techniques for supporting general pointers in automatic program partitioning. Our system, called PtrSplit, constructs a Program Dependence Graph (PDG) for tracking data and control dependencies in the input program and employs a parameter-tree approach for representing data of pointer types; this approach is modular and avoids global pointer analysis. Furthermore, it performs selective pointer bounds tracking to enable automatic marshalling/unmarshalling of pointer data, even when there is circularity and arbitrary aliasing. As a result, PtrSplit can automatically generate executable partitions for C applications that contain arbitrary pointers.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134066"}, {"primary_key": "3736622", "vector": [], "sparse_vector": [], "title": "VibWrite: Towards Finger-input Authentication on Ubiquitous Surfaces via Physical Vibration.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The goal of this work is to enable user authentication via finger inputs on ubiquitous surfaces leveraging low-cost physical vibration. We propose VibWrite that extends finger-input authentication beyond touch screens to any solid surface for smart access systems (e.g., access to apartments, vehicles or smart appliances). It integrates passcode, behavioral and physiological characteristics, and surface dependency together to provide a low-cost, tangible and enhanced security solution. VibWrite builds upon a touch sensing technique with vibration signals that can operate on surfaces constructed from a broad range of materials. It is significantly different from traditional password-based approaches, which only authenticate the password itself rather than the legitimate user, and the behavioral biometrics-based solutions, which usually involve specific or expensive hardware (e.g., touch screen or fingerprint reader), incurring privacy concerns and suffering from smudge attacks. VibWrite is based on new algorithms to discriminate fine-grained finger inputs and supports three independent passcode secrets including PIN number, lock pattern, and simple gestures by extracting unique features in the frequency domain to capture both behavioral and physiological characteristics such as contacting area, touching force, and etc. VibWrite is implemented using a single pair of low-cost vibration motor and receiver that can be easily attached to any surface (e.g., a door panel, a desk or an appliance). Our extensive experiments demonstrate that VibWrite can authenticate users with high accuracy (e.g., over 95% within two trials), low false positive rate (e.g., less 3%) and is robust to various types of attacks.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133964"}, {"primary_key": "3736625", "vector": [], "sparse_vector": [], "title": "POSTER: A Unified Framework of Differentially Private Synthetic Data Release with Generative Adversarial Network.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Many differentially private data release solutions have been proposed for different types of data with the sacrifice of inherent correlation structure. Here, we propose a unified framework of releasing differentially private data. In particular, our proposed generative adversarial network (GAN)-based framework learns the input distribution, irrespective of tabular data and graphs, and generates synthetic data in a differentially private manner. Our preliminary results show the acceptable utility of the synthetic dataset.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138823"}, {"primary_key": "3736626", "vector": [], "sparse_vector": [], "title": "Hindsight: Understanding the Evolution of UI Vulnerabilities in Mobile Browsers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>ov", "<PERSON><PERSON>", "<PERSON>"], "summary": "Much of recent research on mobile security has focused on malicious applications. Although mobile devices have powerful browsers that are commonly used by users and are vulnerable to at least as many attacks as their desktop counterparts, mobile web security has not received the attention that it deserves from the community. In particular, there is no longitudinal study that investigates the evolution of mobile browser vulnerabilities over the diverse set of browsers that are available out there. In this paper, we undertake the first such study, focusing on UI vulnerabilities among mobile browsers. We investigate and quantify vulnerabilities to 27 UI-related attacks---compiled from previous work and augmented with new variations of our own---across 128 browser families and 2,324 individual browser versions spanning a period of more than 5 years. In the process, we collect an extensive dataset of browser versions, old and new, from multiple sources. We also design and implement a browser-agnostic testing framework, called Hindsight, to automatically expose browsers to attacks and evaluate their vulnerabilities. We use Hindsight to conduct the tens of thousands of individual attacks that were needed for this study. We discover that 98.6% of the tested browsers are vulnerable to at least one of our attacks and that the average mobile web browser is becoming less secure with each passing year. Overall, our findings support the conclusion that mobile web security has been ignored by the community and must receive more attention.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133987"}, {"primary_key": "3736630", "vector": [], "sparse_vector": [], "title": "Concurrency and Privacy with Payment-Channel Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Permissionless blockchains protocols such as Bitcoin are inherently limited in transaction throughput and latency. Current efforts to address this key issue focus on off-chain payment channels that can be combined in a Payment-Channel Network (PCN) to enable an unlimited number of payments without requiring to access the blockchain other than to register the initial and final capacity of each channel. While this approach paves the way for low latency and high throughput of payments, its deployment in practice raises several privacy concerns as well as technical challenges related to the inherently concurrent nature of payments that have not been sufficiently studied so far. In this work, we lay the foundations for privacy and concurrency in PCNs, presenting a formal definition in the Universal Composability framework as well as practical and provably secure solutions. In particular, we present <PERSON><PERSON><PERSON> and <PERSON><PERSON>. Fulgor is the first payment protocol for PCNs that provides provable privacy guarantees for PCNs and is fully compatible with the Bitcoin scripting system. However, Fulgor is a blocking protocol and therefore prone to deadlocks of concurrent payments as in currently available PCNs. Instead, Rayo is the first protocol for PCNs that enforces non-blocking progress (i.e., at least one of the concurrent payments terminates). We show through a new impossibility result that non-blocking progress necessarily comes at the cost of weaker privacy. At the core of <PERSON><PERSON><PERSON> and <PERSON>o is Multi-Hop HTLC, a new smart contract, compatible with the Bitcoin scripting system, that provides conditional payments while reducing running time and communication overhead with respect to previous approaches. Our performance evaluation of <PERSON><PERSON><PERSON> and <PERSON><PERSON> shows that a payment with 10 intermediate users takes as few as 5 seconds, thereby demonstrating their feasibility to be deployed in practice.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134096"}, {"primary_key": "3736637", "vector": [], "sparse_vector": [], "title": "POSTER: TOUCHFLOOD: A Novel Class of Attacks against Capacitive Touchscreens.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a novel class of attacks against capacitive touchscreens, which are common in devices such as smartphones and tablet computers. The attack we named TOUCHFLOOD aims to scatter touch events, alternating the selection of buttons on a screen. The key idea of TOUCHFLOOD is to intentionally cause a malfunction by injecting intentional noise signals from an external source. This paper describes the attack as well as the experimental results that clarify the conditions for successful attacks. The demo videos of the experiments using a smartphone are available at https://goo.gl/56G79e.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138829"}, {"primary_key": "3736638", "vector": [], "sparse_vector": [], "title": "POSTER: TouchTrack: How Unique are your Touch Gestures?", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper studies a privacy threat induced by the collection and monitoring of a user's touch gestures on touchscreen devices. The threat is a new form of persistent tracking which we refer to as \"touch-based tracking\". It goes beyond tracking of virtual identities and has the potential for cross-device tracking as well as identifying multiple users using the same device. To demonstrate the likelihood of touch-based tracking, we propose an information theoretic method that quantifies the amount of information revealed by individual features of gestures, samples of gestures as well as samples of gesture combinations, when modelled as feature vectors. We have also developed a purpose-built app, named \"TouchTrack\" that collects data from users and informs them on how unique they are when interacting with their touch devices. Our results from 89 different users indicate that writing samples and left swipes can reveal 73.7% and 68.6% of user information, respectively. Combining different combinations of gestures results in higher uniqueness, with the combination of keystrokes, swipes and writing revealing up to 98.5% of information about users. We correctly re-identify returning users with a success rate of more than 90%.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138850"}, {"primary_key": "3736639", "vector": [], "sparse_vector": [], "title": "A Touch of Evil: High-Assurance Cryptographic Hardware from Untrusted Components.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The semiconductor industry is fully globalized and integrated circuits (ICs) are commonly defined, designed and fabricated in different premises across the world. This reduces production costs, but also exposes ICs to supply chain attacks, where insiders introduce malicious circuitry into the final products. Additionally, despite extensive post-fabrication testing, it is not uncommon for ICs with subtle fabrication errors to make it into production systems. While many systems may be able to tolerate a few byzantine components, this is not the case for cryptographic hardware, storing and computing on confidential data. For this reason, many error and backdoor detection techniques have been proposed over the years. So far all attempts have been either quickly circumvented, or come with unrealistically high manufacturing costs and complexity.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133961"}, {"primary_key": "3736641", "vector": [], "sparse_vector": [], "title": "MagNet: A Two-Pronged Defense against Adversarial Examples.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep learning has shown impressive performance on hard perceptual problems. However, researchers found deep learning systems to be vulnerable to small, specially crafted perturbations that are imperceptible to humans. Such perturbations cause deep learning systems to mis-classify adversarial examples, with potentially disastrous consequences where safety or security is crucial. Prior defenses against adversarial examples either targeted specific attacks or were shown to be ineffective.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134057"}, {"primary_key": "3736650", "vector": [], "sparse_vector": [], "title": "POSTER: PenJ1939: An Interactive Framework for Design and Dissemination of Exploits for Commercial Vehicles.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Vehicle security has been receiving a lot of attention from both the black hat and white hat community of late. Research in this area has already led to the fabrication of different attacks, of which some have been shown to have potentially grave consequences. Vehicle vendors and original equipment manufacturers (OEM)s are thus presented with the additional responsibility of ensuring in-vehicular communication level security. In this poster paper, we present a framework, which allows any individual to write, test, and store exploit scripts which could then be run by any interested party on in-vehicular networks of commercial vehicles like trucks and buses.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138844"}, {"primary_key": "3736651", "vector": [], "sparse_vector": [], "title": "SafeConfig&apos;17: Applying the Scientific Method to Active Cyber Defense Research.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The focus of this workshop is the application of scientific practices to cyber security research. The objective of this workshop is examine the implementation of science practices in cyber defense research and understand the ramification of tradeoffs between simplifications to obtain interpretable results vs. observational studies of systems in the wild where the results can lead to ambiguous interpretations. The research papers accepted addressed a wide variety of technical questions in the cyber domain and the maturity of the work spanned the range of initial ideas and proofs of concept to mature work that is ready for operational implementation. Papers were evaluated for the reproducibility of the work as represented by the documentation of methods and testing environments.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3137054"}, {"primary_key": "3736654", "vector": [], "sparse_vector": [], "title": "Why Do Developers Get Password Storage Wrong?: A Qualitative Usability Study.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Passwords are still a mainstay of various security systems, as well as the cause of many usability issues. For end-users, many of these issues have been studied extensively, highlighting problems and informing design decisions for better policies and motivating research into alternatives. However, end-users are not the only ones who have usability problems with passwords! Developers who are tasked with writing the code by which passwords are stored must do so securely. Yet history has shown that this complex task often fails due to human error with catastrophic results. While an end-user who selects a bad password can have dire consequences, the consequences of a developer who forgets to hash and salt a password database can lead to far larger problems. In this paper we present a first qualitative usability study with 20 computer science students to discover how developers deal with password storage and to inform research into aiding developers in the creation of secure password systems.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134082"}, {"primary_key": "3736655", "vector": [], "sparse_vector": [], "title": "Compressive Traffic Analysis: A New Paradigm for Scalable Traffic Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Traffic analysis is the practice of inferring sensitive information from communication patterns, particularly packet timings and packet sizes. Traffic analysis is increasingly becoming relevant to security and privacy with the growing use of encryption and other evasion techniques that render content-based analysis of network traffic impossible. The literature has investigated traffic analysis for various application scenarios, from tracking stepping stone cybercriminals to compromising anonymity systems.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134074"}, {"primary_key": "3736656", "vector": [], "sparse_vector": [], "title": "The Waterfall of Liberty: <PERSON>oy Routing Circumvention that Resists Routing Attacks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Decoy routing is an emerging approach for censorship circumvention in which circumvention is implemented with help from a number of volunteer Internet autonomous systems, called decoy ASes. Recent studies on decoy routing consider all decoy routing systems to be susceptible to a fundamental attack -- regardless of their specific designs--in which the censors re-route traffic around decoy ASes, thereby preventing censored users from using such systems. In this paper, we propose a new architecture for decoy routing that, by design, is significantly stronger to rerouting attacks compared to all previous designs. Unlike previous designs, our new architecture operates decoy routers only on the downstream traffic of the censored users; therefore we call it downstream-only decoy routing. As we demonstrate through Internet-scale BGP simulations, downstream-only decoy routing offers significantly stronger resistance to rerouting attacks, which is intuitively because a (censoring) ISP has much less control on the downstream BGP routes of its traffic.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134075"}, {"primary_key": "3736657", "vector": [], "sparse_vector": [], "title": "The Return of Coppersmith&apos;s Attack: Practical Factorization of Widely Used RSA Moduli.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We report on our discovery of an algorithmic flaw in the construction of primes for RSA key generation in a widely-used library of a major manufacturer of cryptographic hardware. The primes generated by the library suffer from a significant loss of entropy. We propose a practical factorization method for various key lengths including 1024 and 2048 bits. Our method requires no additional information except for the value of the public modulus and does not depend on a weak or a faulty random number generator. We devised an extension of <PERSON><PERSON>'s factorization attack utilizing an alternative form of the primes in question. The library in question is found in NIST FIPS 140-2 and CC~EAL~5+ certified devices used for a wide range of real-world applications, including identity cards, passports, Trusted Platform Modules, PGP and tokens for authentication or software signing. As the relevant library code was introduced in 2012 at the latest (and probably earlier), the impacted devices are now widespread. Tens of thousands of such keys were directly identified, many with significant impacts, especially for electronic identity documents, software signing, Trusted Computing and PGP. We estimate the number of affected devices to be in the order of at least tens of millions.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133969"}, {"primary_key": "3736658", "vector": [], "sparse_vector": [], "title": "A Stitch in Time: Supporting Android Developers in WritingSecure Code.", "authors": ["<PERSON><PERSON>", "Dominik <PERSON>rmke", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Despite security advice in the official documentation and an extensive body of security research about vulnerabilities and exploits, many developers still fail to write secure Android applications. Frequently, Android developers fail to adhere to security best practices, leaving applications vulnerable to a multitude of attacks. We point out the advantage of a low-time-cost tool both to teach better secure coding and to improve app security. Using the FixDroid IDE plug-in, we show that professional and hobby app developers can work with and learn from an in-environment tool without it impacting their normal work; and by performing studies with both students and professional developers, we identify key UI requirements and demonstrate that code delivered with such a tool by developers previously inexperienced in security contains significantly less security problems. Perfecting and adding such tools to the Android development environment is an essential step in getting both security and privacy for the next generation of apps.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133977"}, {"primary_key": "3736661", "vector": [], "sparse_vector": [], "title": "POISED: Spotting Twitter Spam Off the Beaten Paths.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Cybercriminals have found in online social networks a propitious medium to spread spam and malicious content. Existing techniques for detecting spam include predicting the trustworthiness of accounts and analyzing the content of these messages. However, advanced attackers can still successfully evade these defenses.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134055"}, {"primary_key": "3736665", "vector": [], "sparse_vector": [], "title": "MTD 2017: Fourth ACM Workshop on Moving Target Defense (MTD).", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The fourth ACM Workshop on Moving Target Defense (MTD) is held in Dallas, Texas, USA on October 30, 2017, co-located with the 24th ACM Conference on Computer and Communications Security (CCS). The main objective of the workshop is to discuss novel randomization, diversification, and dynamism techniques for computer systems and networks, new metric and analysis frameworks to assess and quantify the effectiveness of MTD, and discuss challenges and opportunities that such defenses provide. We have constructed an exciting and diverse program of nine refereed papers and two invited keynote talks that will provide the participant with a vibrant and thought-provoking set of ideas and insights.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3137044"}, {"primary_key": "3736666", "vector": [], "sparse_vector": [], "title": "POSTER: Cyber Attack Prediction of Threats from Unconventional Resources (CAPTURE).", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper outlines the design, implementation and evaluation of CAPTURE - a novel automated, continuously working cyber attack forecast system. It uses a broad range of unconventional signals from various public and private data sources and a set of signals forecasted via the Auto-Regressive Integrated Moving Average (ARIMA) model. While generating signals, auto cross correlation is used to find out the optimum signal aggregation and lead times. Generated signals are used to train a Bayesian classifier against the ground truth of each attack type. We show that it is possible to forecast future cyber incidents using CAPTURE and the consideration of the lead time could improve forecast performance.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138834"}, {"primary_key": "3736668", "vector": [], "sparse_vector": [], "title": "How Unique is Your .onion?: An Analysis of the Fingerprintability of Tor Onion Services.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent studies have shown that Tor onion (hidden) service websites are particularly vulnerable to website fingerprinting attacks due to their limited number and sensitive nature. In this work we present a multi-level feature analysis of onion site fingerprintability, considering three state-of-the-art website fingerprinting methods and 482 Tor onion services, making this the largest analysis of this kind completed on onion services to date.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134005"}, {"primary_key": "3736669", "vector": [], "sparse_vector": [], "title": "Back to the Drawing Board: Revisiting the Design of Optimal Location Privacy-preserving Mechanisms.", "authors": ["<PERSON>", "Carmela Tron<PERSON>o", "<PERSON>"], "summary": "In the last years we have witnessed the appearance of a variety of strategies to design optimal location privacy-preserving mechanisms, in terms of maximizing the adversary's expected error with respect to the users' whereabouts. In this work, we take a closer look at the defenses created by these strategies and show that, even though they are indeed optimal in terms of adversary's correctness, not all of them offer the same protection when looking at other dimensions of privacy. To avoid \"bad\" choices, we argue that the search for optimal mechanisms must be guided by complementary criteria. We provide two example auxiliary metrics that help in this regard: the conditional entropy, that captures an information-theoretic aspect of the problem; and the worst-case quality loss, that ensures that the output of the mechanism always provides a minimum utility to the users. We describe a new mechanism that maximizes the conditional entropy and is optimal in terms of average adversary error, and compare its performance with previously proposed optimal mechanisms using two real datasets. Our empirical results confirm that no mechanism fares well on every privacy criteria simultaneously, making apparent the need for considering multiple privacy dimensions to have a good understanding of the privacy protection a mechanism provides.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134004"}, {"primary_key": "3736672", "vector": [], "sparse_vector": [], "title": "POSTER: Towards Precise and Automated Verification of Security Protocols in Coq.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Security protocol verification using commonly-used model-checkers or symbolic protocol verifiers has several intrinsic limitations. Spin suffers the state explosion problem; Proverif may report false attacks. An alternative approach is to use Coq. However, the effort required to verify protocols in Coq is high for two main reasons: correct protocol and property specification is a non-trivial task, and security proofs lack automation. This work claims that (1) using Coq for verification of cryptographic protocols can sometimes yield better results than Spin and Proverif, and (2) the verification process in Coq can be greatly alleviated if specification and proof engineering techniques are applied. Our approach is evaluated by verifying several representative case studies. Preliminary results are encouraging, we were able to verify two protocols that give imprecise results in Spin and Proverif, respectively. Further, we have automated proofs of secrecy and authentication for an important class of protocols.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138846"}, {"primary_key": "3736675", "vector": [], "sparse_vector": [], "title": "POSTER: Probing Tor Hidden Service with Dockers.", "authors": ["Jonghyeon Park", "<PERSON><PERSON><PERSON>"], "summary": "Tor is a commonly used anonymous network that provides the hidden services. As the number of hidden services using Tor's anonymous network has been steadily increasing every year, so does the number of services that abuse Tor's anonymity. The existing research on the Tor is mainly focused on Tor's security loopholes and anonymity. As a result, how to collect and analyze the contents of Tor's hidden services is not yet in full swing. In addition, due to the slow access speed of the Tor browser, it is difficult to observe the dynamics of the hidden services. In this work, we present a tool that can monitor the status of hidden services for the analysis of authentic hidden service contents, and have automated our tool with the virtualization software, Docker, to improve the crawling performance. From August 12, 2017 to August 20, 2017, we collected a total of 9,176 sites and analyzed contents for 100 pages.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138849"}, {"primary_key": "3736678", "vector": [], "sparse_vector": [], "title": "Let&apos;s Go in for a Closer Look: Observing Passwords in Their Natural Habitat.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Text passwords---a frequent vector for account compromise, yet still ubiquitous---have been studied for decades by researchers attempting to determine how to coerce users to create passwords that are hard for attackers to guess but still easy for users to type and memorize. Most studies examine one password or a small number of passwords per user, and studies often rely on passwords created solely for the purpose of the study or on passwords protecting low-value accounts. These limitations severely constrain our understanding of password security in practice, including the extent and nature of password reuse, password behaviors specific to categories of accounts (e.g., financial websites), and the effect of password managers and other privacy tools.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133973"}, {"primary_key": "3736680", "vector": [], "sparse_vector": [], "title": "Deemon: Detecting CSRF with Dynamic Analysis and Property Graphs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Cross-Site Request Forgery (CSRF) vulnerabilities are a severe class of web vulnerabilities that have received only marginal attention from the research and security testing communities. While much effort has been spent on countermeasures and detection of XSS and SQLi, to date, the detection of CSRF vulnerabilities is still performed predominantly manually.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133959"}, {"primary_key": "3736681", "vector": [], "sparse_vector": [], "title": "To BLISS-B or not to be: Attacking strongSwan&apos;s Implementation of Post-Quantum Signatures.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In the search for post-quantum secure alternatives to RSA and ECC, lattice-based cryptography appears to be an attractive and efficient option. A particularly interesting lattice-based signature scheme is BLISS, offering key and signature sizes in the range of RSA moduli. A range of works on efficient implementations of BLISS is available, and the scheme has seen a first real-world adoption in strongSwan, an IPsec-based VPN suite. In contrast, the implementation-security aspects of BLISS, and lattice-based cryptography in general, are still largely unexplored.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134023"}, {"primary_key": "3736682", "vector": [], "sparse_vector": [], "title": "SlowFuzz: Automated Domain-Independent Detection of Algorithmic Complexity Vulnerabilities.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Algorithmic complexity vulnerabilities occur when the worst-case time/space complexity of an application is significantly higher than the respective average case for particular user-controlled inputs. When such conditions are met, an attacker can launch Denial-of-Service attacks against a vulnerable application by providing inputs that trigger the worst-case behavior. Such attacks have been known to have serious effects on production systems, take down entire websites, or lead to bypasses of Web Application Firewalls. Unfortunately, existing detection mechanisms for algorithmic complexity vulnerabilities are domain-specific and often require significant manual effort. In this paper, we design, implement, and evaluate SlowFuzz, a domain-independent framework for automatically finding algorithmic complexity vulnerabilities. SlowFuzz automatically finds inputs that trigger worst-case algorithmic behavior in the tested binary. SlowFuzz uses resource-usage-guided evolutionary search techniques to automatically find inputs that maximize computational resource utilization for a given application.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134073"}, {"primary_key": "3736684", "vector": [], "sparse_vector": [], "title": "Practical Quantum-Safe Voting from Lattices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose a lattice-based electronic voting scheme, EVOLVE (Electronic Voting from Lattices with Verification), which is conjectured to resist attacks by quantum computers. Our protocol involves a number of voting authorities so that vote privacy is maintained as long as at least one of the authorities is honest, while the integrity of the result is guaranteed even when all authorities collude. Furthermore, the result of the vote can be independently computed by any observer. At the core of the protocol is the utilization of a homomorphic commitment scheme with strategically orchestrated zero-knowledge proofs: voters use approximate but efficient \"Fiat-Shamir with Aborts\" proofs to show the validity of their vote, while the authorities use amortized exact proofs to show that the commitments are well-formed. We also present a novel efficient zero-knowledge proof that one of two lattice-based statements is true (so-called OR proof) and a new mechanism to control the size of the randomness when applying the homomorphism to commitments. We give concrete parameter choices to securely instantiate and evaluate the efficiency of our scheme. Our prototype implementation shows that the voters require $8$ milliseconds to submit a vote of size about $20$KB to each authority and it takes each authority $0.15$ seconds per voter to create a proof that his vote was valid. The size of the vote share that each authority produces is approximately $15$KB per voter, which we believe is well within the practical bounds for a large-scale election.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134101"}, {"primary_key": "3736688", "vector": [], "sparse_vector": [], "title": "Generating Synthetic Decentralized Social Graphs with Local Differential Privacy.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A large amount of valuable information resides in decentralized social graphs, where no entity has access to the complete graph structure. Instead, each user maintains locally a limited view of the graph. For example, in a phone network, each user keeps a contact list locally in her phone, and does not have access to other users' contacts. The contact lists of all users form an implicit social graph that could be very useful to study the interaction patterns among different populations. However, due to privacy concerns, one could not simply collect the unfettered local views from users and reconstruct a decentralized social network.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134086"}, {"primary_key": "3736697", "vector": [], "sparse_vector": [], "title": "Malicious-Secure Private Set Intersection via Dual Execution.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Private set intersection (PSI) allows two parties, who each hold a set of items, to compute the intersection of those sets without revealing anything about other items. Recent advances in PSI have significantly improved its performance for the case of semi-honest security, making semi-honest PSI a practical alternative to insecure methods for computing intersections. However, the semi-honest security model is not always a good fit for real-world problems.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134044"}, {"primary_key": "3736700", "vector": [], "sparse_vector": [], "title": "Deterministic, Stash-Free Write-Only ORAM.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Write-Only Oblivious RAM (WoORAM) protocols provide privacy by encrypting the contents of data and also hiding the pattern of write operations over that data. WoORAMs provide better privacy than plain encryption and better performance than more general ORAM schemes (which hide both writing and reading access patterns), and the write-oblivious setting has been applied to important applications of cloud storage synchronization and encrypted hidden volumes. In this paper, we introduce an entirely new technique for Write-Only ORAM, called DetWoORAM. Unlike previous solutions, DetWoORAM uses a deterministic, sequential writing pattern without the need for any \"stashing\" of blocks in local state when writes fail. Our protocol, while conceptually simple, provides substantial improvement over prior solutions, both asymptotically and experimentally. In particular, under typical settings the DetWoORAM writes only 2 blocks (sequentially) to backend memory for each block written to the device, which is optimal. We have implemented our solution using the BUSE (block device in user-space) module and tested DetWoORAM against both an encryption only baseline of dm-crypt and prior, randomized WoORAM solutions, measuring only a 3x-14x slowdown compared to an encryption-only baseline and around 6x-19x speedup compared to prior work.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134051"}, {"primary_key": "3736703", "vector": [], "sparse_vector": [], "title": "Generic Semantic Security against a Kleptographic Adversary.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Notable recent security incidents have generated intense interest in adversaries which attempt to subvert---perhaps covertly---crypto\\-graphic algorithms. In this paper we develop (IND-CPA) Semantically Secure encryption in this challenging setting. This fundamental encryption primitive has been previously studied in the \"kleptographic setting,\" though existing results must relax the model by introducing trusted components or otherwise constraining the subversion power of the adversary: designing a Public Key System that is kletographically semantically secure (with minimal trust) has remained elusive to date. In this work, we finally achieve such systems, even when all relevant cryptographic algorithms are subject to adversarial (kleptographic) subversion. To this end we exploit novel inter-component randomized cryptographic checking techniques (with an offline checking component), combined with common and simple software engineering modular programming techniques (applied to the system's black box specification level). Moreover, our methodology yields a strong generic technique for the preservation of any semantically secure cryptosystem when incorporated into the strong kleptographic adversary setting.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133993"}, {"primary_key": "3736717", "vector": [], "sparse_vector": [], "title": "Faulds: A Non-Parametric Iterative Classifier for Internet-Wide OS Fingerprinting.", "authors": ["<PERSON><PERSON>", "Daren B. H. Cline", "<PERSON><PERSON><PERSON>"], "summary": "Recent work in OS fingerprinting has focused on overcoming random distortion in network and user features during Internet-scale SYN scans. These classification techniques work under an assumption that all parameters of the profiled network are known a-priori -- the likelihood of packet loss, the popularity of each OS, the distribution of network delay, and the probability of user modification to each default TCP/IP header value. However, it is currently unclear how to obtain realistic versions of these parameters for the public Internet and/or customize them to a particular network being analyzed. To address this issue, we derive a non-parametric Expectation-Maximization (EM) estimator, which we call Faulds, for the unknown distributions involved in single-probe OS fingerprinting and demonstrate its significantly higher robustness to noise compared to methods in prior work. We apply Faulds to a new scan of 67M webservers and discuss its findings.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133963"}, {"primary_key": "3736719", "vector": [], "sparse_vector": [], "title": "Tail Attacks on Web Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Wang", "Calton Pu"], "summary": "As the extension of Distributed Denial-of-Service (DDoS) attacks to application layer in recent years, researchers pay much interest in these new variants due to a low-volume and intermittent pattern with a higher level of stealthiness, invaliding the state-of-the-art DDoS detection/defense mechanisms. We describe a new type of low-volume application layer DDoS attack--Tail Attacks on Web Applications. Such attack exploits a newly identified system vulnerability of n-tier web applications (millibottlenecks with sub-second duration and resource contention with strong dependencies among distributed nodes) with the goal of causing the long-tail latency problem of the target web application (e.g., 95th percentile response time > 1 second) and damaging the long-term business of the service provider, while all the system resources are far from saturation, making it difficult to trace the cause of performance degradation.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133968"}, {"primary_key": "3736720", "vector": [], "sparse_vector": [], "title": "SGX-BigMatrix: A Practical Encrypted Data Analytic Framework With Trusted Processors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Recently, using secure processors for trusted computing in cloud has attracted a lot of attention. Over the past few years, efficient and secure data analytic tools (e.g., map-reduce framework, machine learning models, and SQL querying) that can be executed over encrypted data using the trusted hardware have been developed. However, these prior efforts do not provide a simple, secure and high level language based framework that is suitable for enabling generic data analytics for non-security experts who do not have concepts such as \"oblivious execution\". In this paper, we thus provide such a framework that allows data scientists to perform the data analytic tasks with secure processors using a Python/Matlab-like high level language. Our framework automatically compiles programs written in our language to optimal execution code by managing issues such as optimal data block sizes for I/O, vectorized computations to simplify much of the data processing, and optimal ordering of operations for certain tasks. Furthermore, many language constructs such as if-statements are removed so that a non-expert user is less likely to create a piece of code that may reveal sensitive information while allowing oblivious data processing (i.e., hiding access patterns). Using these design choices, we provide guarantees for efficient and secure data analytics. We show that our framework can be used to run the existing big data benchmark queries over encrypted data using the Intel SGX efficiently. Our empirical results indicate that our proposed framework is orders of magnitude faster than the general oblivious execution alternatives.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134095"}, {"primary_key": "3736722", "vector": [], "sparse_vector": [], "title": "POSTER: Evaluating Reflective Deception as a Malware Mitigation Strategy.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Reflective Deception is a class of deception techniques designed to disrupt cyber attacks by confusing and frustrating the adversary. The technique is effective even in the absence of any detective capability. This poster will describe Reflective Deception and propose a testing platform for evaluating its efficacy and performance in the mitigation malware.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138833"}, {"primary_key": "3736724", "vector": [], "sparse_vector": [], "title": "CCCP: Closed Caption Crypto Phones to Resist MITM Attacks, Human Errors and Click-Through.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Crypto Phones aim to establish end-to-end secure voice (and text) communications based on human-centric (usually) short checksum validation. They require end users to perform: (1) checksum comparison to detect traditional data-based man-in-the-middle (data MITM) attacks, and, optionally, (2) speaker verification to detect sophisticated voice-based man-in-the-middle (voice MITM) attacks. However, research shows that both tasks are prone to human errors making Crypto Phones highly vulnerable to MITM attacks, especially to data MITM given the prominence of these attacks. Further, human errors under benign settings undermine usability since legitimate calls would often need to be rejected.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134013"}, {"primary_key": "3736726", "vector": [], "sparse_vector": [], "title": "Rise of the HaCRS: Augmenting Autonomous Cyber Reasoning Systems with Human Assistance.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Software permeates every aspect of our world, from our homes to the infrastructure that provides mission-critical services.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134105"}, {"primary_key": "3736728", "vector": [], "sparse_vector": [], "title": "Detecting Structurally Anomalous Logins Within Enterprise Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Many network intrusion detection systems use byte sequences to detect lateral movements that exploit remote vulnerabilities. Attackers bypass such detection by stealing valid credentials and using them to transmit from one computer to another without creating abnormal network traffic. We call this method Credential-based Lateral Movement. To detect this type of lateral movement, we develop the concept of a Network Login Structure that specifies normal logins within a given network. Our method models a network login structure by automatically extracting a collection of login patterns by using a variation of the market-basket algorithm. We then employ an anomaly detection approach to detect malicious logins that are inconsistent with the enterprise network's login structure. Evaluations show that the proposed method is able to detect malicious logins in a real setting. In a simulated attack, our system was able to detect 82% of malicious logins, with a 0.3% false positive rate. We used a real dataset of millions of logins over the course of five months within a global financial company for evaluation of this work.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134003"}, {"primary_key": "3736731", "vector": [], "sparse_vector": [], "title": "FreeGuard: A Faster Secure Heap Allocator.", "authors": ["<PERSON>", "Hong<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In spite of years of improvements to software security, heap-related attacks still remain a severe threat. One reason is that many existing memory allocators fall short in a variety of aspects. For instance, performance-oriented allocators are designed with very limited countermeasures against attacks, but secure allocators generally suffer from significant performance overhead, e.g., running up to 10x slower. This paper, therefore, introduces FreeGuard, a secure memory allocator that prevents or reduces a wide range of heap-related security attacks, such as heap overflows, heap over-reads, use-after-frees, as well as double and invalid frees. FreeGuard has similar performance to the default Linux allocator, with less than 2% overhead on average, but provides significant improvement to security guarantees.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133957"}, {"primary_key": "3736732", "vector": [], "sparse_vector": [], "title": "POSTER: Improving Anonymity of Services Deployed Over Tor by Changing Guard Selection.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Many P2P applications are emerging that use Tor to ensure anonymity of their users. Each user in such an application creates an onion service so that the user can receive requests from other users. Such large-scale use of onion services leak a lot of sensitive information to guards in Tor. The cause of these leaks is diversity in guards' resources and the guard selection algorithm in Tor that is designed to use guards' resources efficiently. We describe a preliminary approach for selecting guards which reduces the amount of sensitive information leaked to guards while using guards' resources with same efficiency. Experiments in the context of a P2P publish/subscribe application shows that the approach reduces information leaked to guards by 25%.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138835"}, {"primary_key": "3736735", "vector": [], "sparse_vector": [], "title": "DEMO: Akatosh: Automated Cyber Incident Verification and Impact Analysis.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Akatosh, a U.S. Department of Homeland Security Transition to Practice Program (TTP) project developed by Oak Ridge National Laboratory with industry and academic partnership, enables automated, real-time forensic analysis of endpoints after malware-attacks and other cyber security incidents by automatically maintaining detailed snapshots of host-level activity on endpoints over time. It achieves this by integrating intrusion detection systems (IDS) with forensic tools. The combination allows Akatosh to collect vast amounts of endpoint data and assists in verifying, tracking, and analyzing endpoints in real time. This provides operations personnel and analysts as well as managers and executives with continuous feedback on the impact of malicious software and other security incidents on endpoints in their network.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138854"}, {"primary_key": "3736736", "vector": [], "sparse_vector": [], "title": "Most Websites Don&apos;t Need to Vibrate: A Cost-Benefit Approach to Improving Browser Security.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Modern web browsers have accrued an incredibly broad set of features since being invented for hypermedia dissemination in 1990. Many of these features benefit users by enabling new types of web applications. However, some features also bring risk to users' privacy and security, whether through implementation error, unexpected composition, or unintended use. Currently there is no general methodology for weighing these costs and benefits. Restricting access to only the features which are necessary for delivering desired functionality on a given website would allow users to enforce the principle of lease privilege on use of the myriad APIs present in the modern web browser.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133966"}, {"primary_key": "3736739", "vector": [], "sparse_vector": [], "title": "POSTER: Inaudible Voice Commands.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Voice assistants like <PERSON><PERSON> enable us to control IoT devices conveniently with voice commands, however, they also provide new attack opportunities for adversaries. Previous papers attack voice assistants with obfuscated voice commands by leveraging the gap between speech recognition system and human voice perception. The limitation is that these obfuscated commands are audible and thus conspicuous to device owners. In this poster, we propose a novel mechanism to directly attack the microphone used for sensing voice data with inaudible voice commands. We show that the adversary can exploit the microphone's non-linearity and play well-designed inaudible ultrasounds to cause the microphone to record normal voice commands, and thus control the victim device inconspicuously. We demonstrate via end-to-end real-world experiments that our inaudible voice commands can attack an Android phone and an Amazon Echo device with high success rates at a range of 2-3 meters.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138836"}, {"primary_key": "3736740", "vector": [], "sparse_vector": [], "title": "Machine Learning Models that Remember Too Much.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Machine learning (ML) is becoming a commodity. Numerous ML frameworks and services are available to data holders who are not ML experts but want to train predictive models on their data. It is important that ML models trained on sensitive inputs (e.g., personal images or documents) not leak too much information about the training data.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134077"}, {"primary_key": "3736743", "vector": [], "sparse_vector": [], "title": "A Formal Foundation for Secure Remote Execution of Enclaves.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent proposals for trusted hardware platforms, such as Intel SGX and the MIT Sanctum processor, offer compelling security features but lack formal guarantees. We introduce a verification methodology based on a trusted abstract platform (TAP), a formalization of idealized enclave platforms along with a parameterized adversary. We also formalize the notion of secure remote execution and present machine-checked proofs showing that the TAP satisfies the three key security properties that entail secure remote execution: integrity, confidentiality and secure measurement. We then present machine-checked proofs showing that SGX and Sanctum are refinements of the TAP under certain parameterizations of the adversary, demonstrating that these systems implement secure enclaves for the stated adversary models.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134098"}, {"primary_key": "3736747", "vector": [], "sparse_vector": [], "title": "Herding Vulnerable Cats: A Statistical Approach to Disentangle Joint Responsibility for Web Security in Shared Hosting.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "W<PERSON><PERSON>", "<PERSON>"], "summary": "Hosting providers play a key role in fighting web compromise, but their ability to prevent abuse is constrained by the security practices of their own customers. Shared hosting, offers a unique perspective since customers operate under restricted privileges and providers retain more control over configurations. We present the first empirical analysis of the distribution of web security features and software patching practices in shared hosting providers, the influence of providers on these security practices, and their impact on web compromise rates. We construct provider-level features on the global market for shared hosting -- containing 1,259 providers -- by gathering indicators from 442,684 domains. Exploratory factor analysis of 15 indicators identifies four main latent factors that capture security efforts: content security, webmaster security, web infrastructure security and web application security. We confirm, via a fixed-effect regression model, that providers exert significant influence over the latter two factors, which are both related to the software stack in their hosting environment. Finally, by means of GLM regression analysis of these factors on phishing and malware abuse, we show that the four security and software patching factors explain between 10% and 19% of the variance in abuse at providers, after controlling for size. For web-application security for instance, we found that when a provider moves from the bottom 10% to the best-performing 10%, it would experience 4 times fewer phishing incidents. We show that providers have influence over patch levels--even higher in the stack, where CMSes can run as client-side software--and that this influence is tied to a substantial reduction in abuse levels.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133971"}, {"primary_key": "3736748", "vector": [], "sparse_vector": [], "title": "On the Power of Optical Contactless Probing: Attacking Bitstream Encryption of FPGAs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Modern Integrated Circuits (ICs) employ several classes of countermeasures to mitigate physical attacks. Recently, a powerful semi-invasive attack relying on optical contactless probing has been introduced, which can assist the attacker in circumventing the integrated countermeasures and probe the secret data on a chip. This attack can be mounted using IC debug tools from the backside of the chip. The first published attack based on this technique was conducted against a proof-of-concept hardware implementation on a Field Programmable Gate Array (FPGA). Therefore, the success of optical probing techniques against a real commercial device without any knowledge of the hardware implementation is still questionable. The aim of this work is to assess the threat of optical contactless probing in a real attack scenario. To this end, we conduct an optical probing attack against the bitstream encryption feature of a common FPGA. We demonstrate that the adversary is able to extract the plaintext data containing sensitive design information and intellectual property (IP). In contrast to previous optical attacks from the IC backside, our attack does not require any device preparation or silicon polishing, which makes it a non-invasive attack. Additionally, we debunk the myth that small technology sizes are unsusceptible to optical attacks, as we use an optical resolution of about 1 um to successfully attack a 28 nm device. Based on our time measurements, an attacker needs less than 10 working days to conduct the optical analysis and reverse-engineer the security-related parts of the hardware. Finally, we propose and discuss potential countermeasures, which could make the attack more challenging.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134039"}, {"primary_key": "3736756", "vector": [], "sparse_vector": [], "title": "Data Breaches, Phishing, or Malware?: Understanding the Risks of Stolen Credentials.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we present the first longitudinal measurement study of the underground ecosystem fueling credential theft and assess the risk it poses to millions of users. Over the course of March, 2016--March, 2017, we identify 788,000 potential victims of off-the-shelf keyloggers; 12.4 million potential victims of phishing kits; and 1.9 billion usernames and passwords exposed via data breaches and traded on blackmarket forums. Using this dataset, we explore to what degree the stolen passwords---which originate from thousands of online services---enable an attacker to obtain a victim's valid email credentials---and thus complete control of their online identity due to transitive trust. Drawing upon Google as a case study, we find 7--25% of exposed passwords match a victim's Google account. For these accounts, we show how hardening authentication mechanisms to include additional risk signals such as a user's historical geolocations and device profiles helps to mitigate the risk of hijacking. Beyond these risk metrics, we delve into the global reach of the miscreants involved in credential theft and the blackhat tools they rely on. We observe a remarkable lack of external pressure on bad actors, with phishing kit playbooks and keylogger capabilities remaining largely unchanged since the mid-2000s.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134067"}, {"primary_key": "3736758", "vector": [], "sparse_vector": [], "title": "Certified Verification of Algebraic Properties on Low-Level Mathematical Constructs in Cryptographic Programs.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mathematical constructs are necessary for computation on the underlying algebraic structures of cryptosystems. They are often written in assembly language and optimized manually for efficiency. We develop a certified technique to verify low-level mathematical constructs in X25519, the default elliptic curve Di<PERSON>ie-Hellman key exchange protocol used in OpenSSH. Our technique translates an algebraic specification of mathematical constructs into an algebraic problem. The algebraic problem in turn is solved by the computer algebra system Singular. The proof assistant <PERSON><PERSON> certifies the translation and solution to algebraic problems. Specifications about output ranges and potential program overflows are translated to SMT problems and verified by SMT solvers. We report our case studies on verifying arithmetic computation over a large finite field and the Montgomery Ladderstep, a crucial loop in X25519.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134076"}, {"primary_key": "3736767", "vector": [], "sparse_vector": [], "title": "Key Reinstallation Attacks: Forcing Nonce Reuse in WPA2.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce the key reinstallation attack. This attack abuses design or implementation flaws in cryptographic protocols to reinstall an already-in-use key. This resets the key's associated parameters such as transmit nonces and receive replay counters. Several types of cryptographic Wi-Fi handshakes are affected by the attack. All protected Wi-Fi networks use the 4-way handshake to generate a fresh session key. So far, this 14-year-old handshake has remained free from attacks, and is even proven secure. However, we show that the 4-way handshake is vulnerable to a key reinstallation attack. Here, the adversary tricks a victim into reinstalling an already-in-use key. This is achieved by manipulating and replaying handshake messages. When reinstalling the key, associated parameters such as the incremental transmit packet number (nonce) and receive packet number (replay counter) are reset to their initial value. Our key reinstallation attack also breaks the PeerKey, group key, and Fast BSS Transition (FT) handshake. The impact depends on the handshake being attacked, and the data-confidentiality protocol in use. Simplified, against AES-CCMP an adversary can replay and decrypt (but not forge) packets. This makes it possible to hijack TCP streams and inject malicious data into them. Against WPA-TKIP and GCMP the impact is catastrophic: packets can be replayed, decrypted, and forged. Because GCMP uses the same authentication key in both communication directions, it is especially affected. Finally, we confirmed our findings in practice, and found that every Wi-Fi device is vulnerable to some variant of our attacks. Notably, our attack is exceptionally devastating against Android 6.0: it forces the client into using a predictable all-zero encryption key.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134027"}, {"primary_key": "3736770", "vector": [], "sparse_vector": [], "title": "The Dynamics of Innocent Flesh on the Bone: Code Reuse Ten Years Later.", "authors": ["<PERSON>", "<PERSON>", "Manolis Stamatogiannakis", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In 2007, <PERSON><PERSON><PERSON><PERSON> published a seminal paper on Return-Oriented Programming (ROP), the first systematic formulation of code reuse. The paper has been highly influential, profoundly shaping the way we still think about code reuse today: an attacker analyzes the \"geometry\" of victim binary code to locate gadgets and chains these to craft an exploit. This model has spurred much research, with a rapid progression of increasingly sophisticated code reuse attacks and defenses over time. After ten years, the common perception is that state-of-the-art code reuse defenses are effective in significantly raising the bar and making attacks exceedingly hard.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134026"}, {"primary_key": "3736772", "vector": [], "sparse_vector": [], "title": "The Wolf of Name Street: Hijacking Domains Through Their Nameservers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "W<PERSON><PERSON>", "<PERSON>"], "summary": "The functionality and security of all domain names are contingent upon their nameservers. When these nameservers, or requests to them, are compromised, all domains that rely on them are affected. In this paper, we study the exploitation of configuration issues (typosquatting and outdated WHOIS records) and hardware errors (bitsquatting) to seize control over nameservers' requests to hijack domains. We perform a large-scale analysis of 10,000 popular nameserver domains, in which we map out existing abuse and vulnerable entities. We confirm the capabilities of these attacks through real-world measurements. Overall, we find that over 12,000 domains are susceptible to near-immediate compromise, while 52.8M domains are being targeted by nameserver bitsquatters that respond with rogue IP addresses. Additionally, we determine that 1.28M domains are at risk of a denial-of-service attack by relying on an outdated nameserver.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133988"}, {"primary_key": "3736773", "vector": [], "sparse_vector": [], "title": "Full Accounting for Verifiable Outsourcing.", "authors": ["Riad S. Wahby", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Systems for verifiable outsourcing incur costs for a prover, a verifier, and precomputation; outsourcing makes sense when the combination of these costs is cheaper than not outsourcing. Yet, when prior works impose quantitative thresholds to analyze whether outsourcing is justified, they generally ignore prover costs. Verifiable ASICs (VA)---in which the prover is a custom chip---is the other way around: its cost calculations ignore precomputation.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133984"}, {"primary_key": "3736774", "vector": [], "sparse_vector": [], "title": "POSTER: Is Active Electromagnetic Side-channel Attack Practical?", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Radio-frequency (RF) retroreflector attack (RFRA) is an {\\em active} electromagnetic side-channel attack that aims to leak the target's internal signals by irradiating the targeted device with a radio wave, where an attacker has embedded a malicious circuit (RF retroreflector) in the device in advance. As the retroreflector consists of small and cheap electrical elements such as a field-effect transistor (FET) chip and a wire that can work as a dipole antenna, the reflector can be embedded into various kinds of electric devices that carry unencrypted, sensitive information; e.g., keyboard, display monitor, microphone, speaker, USB, and so on. Only a few studies have addressed the basic mechanism of RFRA and demonstrated the success of the attack. The conditions for a successful attack have not been adequately explored before, and therefore, assessing the feasibility of the attack remains an open issue. In the present study, we aim to investigate empirically the conditions for a successful RFRA through field experiments. Understanding attack limitations should help to develop effective countermeasures against it. In particular, with regard to the conditions for a successful attack, we studied the distance between the attacker and the target, and the target signal frequencies. Through the extensive experiments using off-the-shelf hardware including software-defined radio (SDR) equipment, we revealed that the required conditions for a successful attack are (1) up to a 10-Mbps of target signal and (2) up to a distance of 10 meters. These results demonstrate the importance of the RFRA threat in the real world.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138830"}, {"primary_key": "3736776", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON> on the Dark Land: Understanding Memory Side-Channel Hazards in SGX.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Side-channel risks of Intel's SGX have recently attracted great attention. Under the spotlight is the newly discovered page-fault attack, in which an OS-level adversary induces page faults to observe the page-level access patterns of a protected process running in an SGX enclave. With almost all proposed defense focusing on this attack, little is known about whether such efforts indeed raises the bar for the adversary, whether a simple variation of the attack renders all protection ineffective, not to mention an in-depth understanding of other attack surfaces in the SGX system. In the paper, we report the first step toward systematic analyses of side-channel threats that SGX faces, focusing on the risks associated with its memory management. Our research identifies 8 potential attack vectors, ranging from TLB to DRAM modules. More importantly, we highlight the common misunderstandings about SGX memory side channels, demonstrating that high frequent AEXs can be avoided when recovering EdDSA secret key through a new page channel and fine-grained monitoring of enclave programs (at the level of 64B) can be done through combining both cache and cross-enclave DRAM channels. Our findings reveal the gap between the ongoing security research on SGX and its side-channel weaknesses, redefine the side-channel threat model for secure enclaves, and can provoke a discussion on when to use such a system and how to use it securely.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134038"}, {"primary_key": "3736779", "vector": [], "sparse_vector": [], "title": "Authenticated Garbling and Efficient Maliciously Secure Two-Party Computation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose a simple and efficient framework for obtaining efficient constant-round protocols for maliciously secure two-party computation. Our framework uses a function-independent preprocessing phase to generate authenticated information for the two parties; this information is then used to construct a single \"authenticated\" garbled circuit which is transmitted and evaluated. We also show how to efficiently instantiate the preprocessing phase with a new, highly optimized version of the TinyOT protocol by Nielsen et al.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134053"}, {"primary_key": "3736780", "vector": [], "sparse_vector": [], "title": "Global-Scale Secure Multiparty Computation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose a new, constant-round protocol for multi-party computation of boolean circuits that is secure against an arbitrary number of malicious corruptions. At a high level, we extend and generalize recent work of <PERSON> et al. in the two-party setting. Namely, we design an efficient preprocessing phase that allows the parties to generate authenticated information; we then show how to use this information to distributively construct a single \"authenticated\" garbled circuit that is evaluated by one party.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133979"}, {"primary_key": "3736782", "vector": [], "sparse_vector": [], "title": "Object Flow Integrity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Object flow integrity (OFI) augments control-flow integrity (CFI) and software fault isolation (SFI) protections with secure, first-class support for binary object exchange across inter-module trust boundaries. This extends both source-aware and source-free CFI and SFI technologies to a large class of previously unsupported software: those containing immutable system modules with large, object-oriented APIs---which are particularly common in component-based, event-driven consumer software. It also helps to protect these inter-module object exchanges against confused deputy-assisted vtable corruption and counterfeit object-oriented programming attacks.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133986"}, {"primary_key": "3736783", "vector": [], "sparse_vector": [], "title": "Cryptographically Secure Information Flow Control on Key-Value Stores.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present Clio, an information flow control (IFC) system that transparently incorporates cryptography to enforce confidentiality and integrity policies on untrusted storage. <PERSON>lio insulates developers from explicitly manipulating keys and cryptographic primitives by leveraging the policy language of the IFC system to automatically use the appropriate keys and correct cryptographic operations. We prove that Clio is secure with a novel proof technique that is based on a proof style from cryptography together with standard programming languages results. We present a prototype Clio implementation and a case study that demonstrates <PERSON><PERSON>'s practicality.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134036"}, {"primary_key": "3736789", "vector": [], "sparse_vector": [], "title": "STACCO: Differentially Analyzing Side-Channel Traces for Detecting SSL/TLS Vulnerabilities in Secure Enclaves.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Intel Software Guard Extension (SGX) offers software applications enclave to protect their confidentiality and integrity from malicious operating systems. The SSL/TLS protocol, which is the de facto standard for protecting transport-layer network communications, has been broadly deployed for a secure communication channel. However, in this paper, we show that the marriage between SGX and SSL may not be smooth sailing. Particularly, we consider a category of side-channel attacks against SSL/TLS implementations in secure enclaves, which we call the control-flow inference attacks. In these attacks, the malicious operating system kernel may perform a powerful man-in-the-kernel attack to collect execution traces of the enclave programs at page, cacheline, or branch level, while positioning itself in the middle of the two communicating parties. At the center of our work is a differential analysis framework, dubbed Stacco, to dynamically analyze the SSL/TLS implementations and detect vulnerabilities that can be exploited as decryption oracles. Surprisingly, we found exploitable vulnerabilities in the latest versions of all the SSL/TLS libraries we have examined. To validate the detected vulnerabilities, we developed a man-in-the-kernel adversary to demonstrate Bleichenbacher attacks against the latest OpenSSL library running in the SGX enclave (with the help of Graphene) and completely broke the PreMasterSecret encrypted by a 4096-bit RSA public key with only 57286 queries. We also conducted CBC padding oracle attacks against the latest GnuTLS running in Graphene-SGX and an open-source SGX-implementation of mbedTLS (i.e., mbedTLS-SGX) that runs directly inside the enclave, and showed that it only needs 48388 and 25717 queries, respectively, to break one block of AES ciphertext. Empirical evaluation suggests these man-in-the-kernel attacks can be completed within 1 or 2 hours.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134016"}, {"primary_key": "3736790", "vector": [], "sparse_vector": [], "title": "POSTER: BGPCoin: A Trustworthy Blockchain-based Resource Management Solution for BGP Security.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Wang"], "summary": "Origin authentication is one of the most concentrated and advocated BGP security approach against IP prefix hijacking. However, the potential risk of centralized authority abuse and the fragile infrastructure may lead a sluggish deployment of such BGP security approach currently. We propose BGPCoin, a trustworthy blockchain-based Internet resource management solution which provides compliant resource allocations and revocations, and a reliable origin advertisement source. By means of a smart contract to perform and supervise resource assignments on the tamper-resistant Ethereum blockchain, BGPCoin yields significant benefits in the secure origin advertisement and the dependable infrastructure for object repository compared with RPKI. We demonstrate through an Ethereum prototype implementation that the deployment incentives and increased security are technically and economically viable.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138828"}, {"primary_key": "3736791", "vector": [], "sparse_vector": [], "title": "Designing New Operating Primitives to Improve Fuzzing Performance.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Chang<PERSON><PERSON> Min", "<PERSON><PERSON><PERSON>"], "summary": "Fuzzing is a software testing technique that finds bugs by repeatedly injecting mutated inputs to a target program. Known to be a highly practical approach, fuzzing is gaining more popularity than ever before. Current research on fuzzing has focused on producing an input that is more likely to trigger a vulnerability.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134046"}, {"primary_key": "3736792", "vector": [], "sparse_vector": [], "title": "Neural Network-based Graph Embedding for Cross-Platform Binary Code Similarity Detection.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The problem of cross-platform binary code similarity detection aims at detecting whether two binary functions coming from different platforms are similar or not. It has many security applications, including plagiarism detection, malware detection, vulnerability search, etc. Existing approaches rely on approximate graph matching algorithms, which are inevitably slow and sometimes inaccurate, and hard to adapt to a new task. To address these issues, in this work, we propose a novel neural network-based approach to compute the embedding, i.e., a numeric vector, based on the control flow graph of each binary function, then the similarity detection can be done efficiently by measuring the distance between the embeddings for two functions. We implement a prototype called Gemini. Our extensive evaluation shows that Gemini outperforms the state-of-the-art approaches by large margins with respect to similarity detection accuracy. Further, Gemini can speed up prior art's embedding generation time by 3 to 4 orders of magnitude and reduce the required training time from more than 1 week down to 30 minutes to 10 hours. Our real world case studies demonstrate that Gemini can identify significantly more vulnerable firmware images than the state-of-the-art, i.e., Genius. Our research showcases a successful application of deep learning on computer security problems.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134018"}, {"primary_key": "3736795", "vector": [], "sparse_vector": [], "title": "POSTER: Who was Behind the Camera? - Towards Some New Forensics.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We motivate a new line of image forensics, and propose a novel approach to photographer identification, a rarely explored authorship attribution problem. A preliminary proof-of-concept study shows the feasibility of our method. Our contribution is a forensic method for photographer de-anonymisation, and the method also imposes a novel privacy threat.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138848"}, {"primary_key": "3736797", "vector": [], "sparse_vector": [], "title": "CCS 2017: Women in Cyber Security (CyberW) Workshop.", "authors": ["<PERSON><PERSON> (Daphne) Yao", "<PERSON><PERSON>"], "summary": "The CyberW workshop is motivated by the significant gender imbalance in all security conferences, in terms of the number of publishing authors, PC members, organizers, and attendees. What causes this gender imbalance remains unclear. However, multiple research studies have shown that a diverse group is more creative, diligent, and productive than a homogeneous group. Achieving cyber security requires a diverse group. To maintain a sustainable and creative workforce, substantial efforts need to be made by the security community to broaden the participation from underrepresented groups in cyber security research conferences. We hope this workshop can attract all underrepresented cybersecurity professionals, students, and researchers to attend top security and privacy conferences, engage in cutting-edge security and privacy research, excel in cyber security professions, and ultimately take on leadership positions.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3137041"}, {"primary_key": "3736798", "vector": [], "sparse_vector": [], "title": "Automated Crowdturfing Attacks and Defenses in Online Review Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Malicious crowdsourcing forums are gaining traction as sources of spreading misinformation online, but are limited by the costs of hiring and managing human workers. In this paper, we identify a new class of attacks that leverage deep learning language models (Recurrent Neural Networks or RNNs) to automate the generation of fake online reviews for products and services. Not only are these attacks cheap and therefore more scalable, but they can control rate of content output to eliminate the signature burstiness that makes crowdsourced campaigns easy to detect.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133990"}, {"primary_key": "3736799", "vector": [], "sparse_vector": [], "title": "Provably-Secure Logic Locking: From Theory To Practice.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Logic locking has been conceived as a promising proactive defense strategy against intellectual property (IP) piracy, counterfeiting, hardware Trojans, reverse engineering, and overbuilding attacks. Yet, various attacks that use a working chip as an oracle have been launched on logic locking to successfully retrieve its secret key, undermining the defense of all existing locking techniques. In this paper, we propose stripped-functionality logic locking (SFLL), which strips some of the functionality of the design and hides it in the form of a secret key(s), thereby rendering on-chip implementation functionally different from the original one. When loaded onto an on-chip memory, the secret keys restore the original functionality of the design. Through security-aware synthesis that creates a controllable mismatch between the reverse-engineered netlist and original design, SFLL provides a quantifiable and provable resilience trade-off between all known and anticipated attacks. We demonstrate the application of SFLL to large designs (>100K gates) using a computer-aided design (CAD) framework that ensures attaining the desired security level at minimal implementation cost, 8%, 5%, and 0.5% for area, power, and delay, respectively. In addition to theoretical proofs and simulation confirmation of SFLL's security, we also report results from the silicon implementation of SFLL on an ARM Cortex-M0 microprocessor in 65nm technology.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133985"}, {"primary_key": "3736800", "vector": [], "sparse_vector": [], "title": "Verified Correctness and Security of mbedTLS HMAC-DRBG.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We have formalized the functional specification of HMAC-DRBG (NIST 800-90A), and we have proved its cryptographic security-that its output is pseudorandom--using a hybrid game-based proof. We have also proved that the mbedTLS implementation (C program) correctly implements this functional specification. That proof composes with an existing C compiler correctness proof to guarantee, end-to-end, that the machine language program gives strong pseudorandomness. All proofs (hybrid games, C program verification, compiler, and their composition) are machine-checked in the Coq proof assistant. Our proofs are modular: the hybrid game proof holds on any implementation of HMAC-DRBG that satisfies our functional specification. Therefore, our functional specification can serve as a high-assurance reference.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133974"}, {"primary_key": "3736801", "vector": [], "sparse_vector": [], "title": "MIST 2017: 9th International Workshop on Managing Insider Security Threats.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper introduces the 9th International Workshop on Managing Insider Security Threats (MIST 2017), which takes place in conjunction with the 24th ACM Conference on Computer and Communications Security (ACM CCS 2017). Its objective is to present recent challenges and advanced technologies in managing insider security threats by publishing high-quality work that will be a trigger for further research related to this subject.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3148525"}, {"primary_key": "3736802", "vector": [], "sparse_vector": [], "title": "SemFuzz: Semantics-based Automatic Generation of Proof-of-Concept Exploits.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Pan Bian", "<PERSON>"], "summary": "Patches and related information about software vulnerabilities are often made available to the public, aiming to facilitate timely fixes. Unfortunately, the slow paces of system updates (30 days on average) often present to the attackers enough time to recover hidden bugs for attacking the unpatched systems. Making things worse is the potential to automatically generate exploits on input-validation flaws through reverse-engineering patches, even though such vulnerabilities are relatively rare (e.g., 5% among all Linux kernel vulnerabilities in last few years). Less understood, however, are the implications of other bug-related information (e.g., bug descriptions in CVE), particularly whether utilization of such information can facilitate exploit generation, even on other vulnerability types that have never been automatically attacked.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134085"}, {"primary_key": "3736806", "vector": [], "sparse_vector": [], "title": "<PERSON>ache Side Channels: State of the Art and Research Opportunities.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Cache side channels are a type of attack vectors through which an adversary infers secret information of a running program by observing its use of CPU caches or other caching hardware. The study of cache side channels, particularly access-driven cache side channels, is gaining traction among security researchers in recent years. A large volume of papers on cache side-channel attacks or defenses is being published in both security and computer architecture conferences each year. However, due to the diversity of the research goals, methods, and perspectives, it becomes much harder for researchers new to this field to keep track of the frontiers of this research topic. As such, in this tutorial, we will provide a high-level overview of the studies of cache side channels to help other security researchers to comprehend the state of the art of this research area, and to identify research problems that have not been addressed by the community. We also hope to bridge the gaps between the security community and the computer architecture community on this specific research topic by summarizing research papers from both sides.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3136064"}, {"primary_key": "3736807", "vector": [], "sparse_vector": [], "title": "Presence Attestation: The Missing Link in Dynamic Trust Bootstrapping.", "authors": ["Zhang<PERSON> Zhang", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Many popular modern processors include an important hardware security feature in the form of a DRTM (Dynamic Root of Trust for Measurement) that helps bootstrap trust and resists software attacks. However, despite substantial body of prior research on trust establishment, security of DRTM was treated without involvement of the human user, who represents a vital missing link. The basic challenge is: how can a human user determine whether an expected DRTM is currently active on her device?", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134094"}, {"primary_key": "3736808", "vector": [], "sparse_vector": [], "title": "POSTER: A PU Learning based System for Potential Malicious URL Detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Yu<PERSON> Liu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper describes a PU learning (Positive and Unlabeled learning) based system for potential URL attack detection. Previous machine learning based solutions for this task mainly formalize it as a supervised learning problem. However, in some scenarios, the data obtained always contains only a handful of known attack URLs, along with a large number of unlabeled instances, making the supervised learning paradigms infeasible. In this work, we formalize this setting as a PU learning problem, and solve it by combining two different strategies (two-stage strategy and cost-sensitive strategy). Experimental results show that the developed system can effectively find potential URL attacks. This system can either be deployed as an assistance for existing system or be employed to help cyber-security engineers to effectively discover potential attack mode so that they can improve the existing system with significantly less efforts.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3138825"}, {"primary_key": "3736809", "vector": [], "sparse_vector": [], "title": "Hearing Your Voice is Not Enough: An Articulatory Gesture Based Liveness Detection for Voice Authentication.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Voice biometrics is drawing increasing attention as it is a promising alternative to legacy passwords for mobile authentication. Recently, a growing body of work shows that voice biometrics is vulnerable to spoofing through replay attacks, where an adversary tries to spoof voice authentication systems by using a pre-recorded voice sample collected from a genuine user. In this work, we propose VoiceGesture, a liveness detection system for replay attack detection on smartphones. It detects a live user by leveraging both the unique articulatory gesture of the user when speaking a passphrase and the mobile audio hardware advances. Specifically, our system re-uses the smartphone as a Doppler radar, which transmits a high frequency acoustic sound from the built-in speaker and listens to the reflections at the microphone when a user speaks a passphrase. The signal reflections due to user's articulatory gesture result in Doppler shifts, which are then analyzed for live user detection. VoiceGesture is practical as it requires neither cumbersome operations nor additional hardware but a speaker and a microphone that are commonly available on smartphones. Our experimental evaluation with 21 participants and different types of phones shows that it achieves over 99% detection accuracy at around 1% Equal Error Rate (EER). Results also show that it is robust to different phone placements and is able to work with different sampling frequencies.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3133962"}, {"primary_key": "3736812", "vector": [], "sparse_vector": [], "title": "DolphinAttack: Inaudible Voice Commands.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Xiaoyu Ji", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Xu"], "summary": "Speech recognition (SR) systems such as Siri or Google Now have become an increasingly popular human-computer interaction method, and have turned various systems into voice controllable systems (VCS). Prior work on attacking VCS shows that the hidden voice commands that are incomprehensible to people can control the systems. Hidden voice commands, though \"hidden\", are nonetheless audible. In this work, we design a totally inaudible attack, DolphinAttack, that modulates voice commands on ultrasonic carriers (e.g., f > 20 kHz) to achieve inaudibility. By leveraging the nonlinearity of the microphone circuits, the modulated low-frequency audio commands can be successfully demodulated, recovered, and more importantly interpreted by the speech recognition systems. We validated DolphinAttack on popular speech recognition systems, including Siri, Google Now, Samsung S Voice, Huawei HiVoice, Cortana and Alexa. By injecting a sequence of inaudible voice commands, we show a few proof-of-concept attacks, which include activating <PERSON><PERSON> to initiate a FaceTime call on iPhone, activating Google Now to switch the phone to the airplane mode, and even manipulating the navigation system in an Audi automobile. We propose hardware and software defense solutions, and suggest to re-design voice controllable systems to be resilient to inaudible voice command attacks.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134052"}, {"primary_key": "3736816", "vector": [], "sparse_vector": [], "title": "Pool: Scalable On-Demand Secure Computation Service Against Malicious Adversaries.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper considers the problem of running a long-term on-demand service for executing actively-secure computations. We examined state-of-the-art tools and implementations for actively-secure computation and identified a set of key features indispensable to offer meaningful service like this. Since no satisfactory tools exist for the purpose, we developed Pool, a new tool for building and executing actively-secure computation protocols at extreme scales with nearly zero offline delay. With Pool, we are able to obliviously execute, for the first time, reactive computations like ORAM in the malicious threat model. Many technical benefits of Pool can be attributed to the concept of pool-based cut-and-choose. We show with experiments that this idea has significantly improved the scalability and usability of JIMU, a state-of-the-art LEGO protocol.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134070"}, {"primary_key": "3736817", "vector": [], "sparse_vector": [], "title": "HACL*: A Verified Modern Cryptographic Library.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "HACL* is a verified portable C cryptographic library that implements modern cryptographic primitives such as the ChaCha20 and Salsa20 encryption algorithms, Poly1305 and HMAC message authentication, SHA-256 and SHA-512 hash functions, the Curve25519 elliptic curve, and Ed25519 signatures.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134043"}, {"primary_key": "3736818", "vector": [], "sparse_vector": [], "title": "AUTHSCOPE: Towards Automatic Discovery of Vulnerable Authorizations in Online Services.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "When accessing online private resources (e.g., user profiles, photos, shopping carts) from a client (e.g., a desktop web-browser or a mobile app), the service providers must implement proper access control, which typically involves both authentication and authorization. However, not all of the service providers follow the best practice, resulting in various access control vulnerabilities. To understand such a threat in a large scale, and identify the vulnerable access control implementations in online services, this paper introduces AuthScope, a tool that is able to automatically execute a mobile app and pinpoint the vulnerable access control implementations, particularly the vulnerable authorizations, in the corresponding online service. The key idea is to use differential traffic analysis to recognize the protocol fields and then automatically substitute the fields and observe the server response. One of the key challenges for a large scale study lies in how to obtain the post-authentication request-and-response messages for a given app. We have thus developed a targeted dynamic activity explorer to perform an in-context analysis and drive the app execution to automatically log in the service. We have tested AuthScope with 4,838 popular mobile apps from Google Play, and identified 597 0-day vulnerable authorizations that map to 306 apps.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956.3134089"}, {"primary_key": "3882996", "vector": [], "sparse_vector": [], "title": "Proceedings of the 2017 ACM SIGSAC Conference on Computer and Communications Security, CCS 2017, Dallas, TX, USA, October 30 - November 03, 2017", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Welcome to the 24th ACM Conference on Computer and Communications Security! Since 1993, CCS has been the ACM's flagship conference for research in all aspects of computing and communications security and privacy. This year's conference attracted a record number of 836 reviewed research paper submissions, of which a record number of 151 papers were selected for presentation at the conference and inclusion in the proceedings. The papers were reviewed by a Program Committee of 146 leading researchers from academic, government, and industry from around the world. Reviewing was done in three rounds, with every paper being reviewed by two PC members in the first round, and additional reviews being assigned in later rounds depending on the initial reviews. Authors had an opportunity to respond to reviews received in the first two rounds. We used a subset of PC members, designated as the Discussion Committee, to help ensure that reviewers reconsidered their reviews in light of the author responses and to facilitate substantive discussions among the reviewers. Papers were discussed extensively on-line in the final weeks of the review process, and late reviews were requested from both PC members and external reviewers when additional expertise or perspective was needed to reach a decision. We are extremely grateful to the PC members for all their hard work in the review process, and to the external reviewers that contributed to selecting the papers for CCS. Before starting the review process, of the 842 submissions the PC chairs removed six papers that clearly violated submission requirements or were duplicates, leaving 836 papers to review. In general, we were lenient on the requirements, only excluding papers that appeared to deliberately disregard the submission requirements. Instead of excluding papers which carelessly deanonymized the authors, or which abused appendices in the opinion of the chairs, we redacted (by modifying the submitted PDF) the offending content and allowed the papers to be reviewed, and offered to make redacted content in appendices available to reviewers upon request. Our review process involved three phases. In the first phase, each paper was assigned two reviewers. Following last year's practice, we adopted the Toronto Paper Matching System (TPMS) for making most of the review assignments, which were then adjusted based on technical preferences declared by reviewers. Each reviewer had about 3 weeks to complete reviews for around 12 papers. Based on the results of these reviews, an additional reviewer was assigned to every paper that had at least one positive-leaning review. Papers where both initial reviews were negative, but with low confidence or significant positive aspects, were also assigned additional reviews. At the conclusion of the second reviewing round, authors had an opportunity to see the initial reviews and to submit a short rebuttal. To ensure that all the authors' responses were considered seriously by the reviewers, the Discussion Committee members worked closely with the reviewers to make sure that they considered and responded to the authors' rebuttals. When reviewers could not reach an agreement, or additional expertise was needed, we solicited additional reviews. The on-line discussion period was vibrant and substantive, and at the end of this process the 151 papers you find here were selected for CCS 2017.", "published": "2017-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3133956"}]