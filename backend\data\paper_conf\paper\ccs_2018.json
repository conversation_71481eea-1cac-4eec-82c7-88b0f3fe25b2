[{"primary_key": "3373933", "vector": [], "sparse_vector": [], "title": "The Web&apos;s Sixth Sense: A Study of Scripts Accessing Smartphone Sensors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present the first large-scale measurement of smartphone sensor API usage and stateless tracking on the mobile web. We extend the OpenWPM web privacy measurement tool to develop OpenWPM-Mobile, adding the ability to emulate plausible sensor values for different smartphone sensors such as motion, orientation, proximity and light. Using OpenWPM-Mobile we find that one or more sensor APIs are accessed on 3695 of the top 100K websites by scripts originating from 603 distinct domains. We also detect fingerprinting attempts on mobile platforms, using techniques previously applied in the desktop setting. We find significant overlap between fingerprinting scripts and scripts accessing sensor data. For example, 63% of the scripts that access motion sensors also engage in browser fingerprinting. To better understand the real-world uses of sensor APIs, we cluster JavaScript programs that access device sensors and then perform automated code comparison and manual analysis. We find a significant disparity between the actual and intended use cases of device sensor as drafted by W3C. While some scripts access sensor data to enhance user experience, such as orientation detection and gesture recognition, tracking and analytics are the most common use cases among the scripts we analyzed. We automated the detection of sensor data exfiltration and observed that the raw readings are frequently sent to remote servers for further analysis. Finally, we evaluate available countermeasures against the misuse of sensor APIs. We find that popular tracking protection lists such as EasyList and Disconnect commonly fail to block most tracking scripts that misuse sensors. Studying nine popular mobile browsers we find that even privacy-focused browsers, such as Brave and Firefox Focus, fail to implement mitigations suggested by W3C, which includes limiting sensor access from insecure contexts and cross-origin iframes. We have reported these issues to the browser vendors.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243860"}, {"primary_key": "3373935", "vector": [], "sparse_vector": [], "title": "Tight on Budget?: Tight Bounds for r-Fold Approximate Differential Privacy.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Many applications, such as anonymous communication systems, privacy-enhancing database queries, or privacy-enhancing machine-learning methods, require robust guarantees under thousands and sometimes millions of observations. The notion of r-fold approximate differential privacy (ADP) offers a well-established framework with a precise characterization of the degree of privacy after r observations of an attacker. However, existing bounds for r-fold ADP are loose and, if used for estimating the required degree of noise for an application, can lead to over-cautious choices for perturbation randomness and thus to suboptimal utility or overly high costs. We present a numerical and widely applicable method for capturing the privacy loss of differentially private mechanisms under composition, which we call privacy buckets. With privacy buckets we compute provable upper and lower bounds for ADP for a given number of observations. We compare our bounds with state-of-the-art bounds for r-fold ADP, including <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>'s composition theorem (KOV), concentrated differential privacy and the moments accountant. While KOV proved optimal bounds for heterogeneous adaptive k-fold composition, we show that for concrete sequences of mechanisms tighter bounds can be derived by taking the mechanisms' structure into account. We compare previous bounds for the Laplace mechanism, the Gauss mechanism, for a timing leakage reduction mechanism, and for the stochastic gradient descent and we significantly improve over their results (except that we match the KOV bound for the Laplace mechanism, for which it seems tight). Our lower bounds almost meet our upper bounds, showing that no significantly tighter bounds are possible.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243765"}, {"primary_key": "3373937", "vector": [], "sparse_vector": [], "title": "WAHC&apos;18: 6th Workshop on Encrypted Computing and Applied Homomorphic Cryptography.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The 6th Workshop on Encrypted Computing and Applied Homomorphic Cryptography is held in Toronto, ON, Canada on October 19th, 2018, co-located with the ACM Conference on Computer and Communications Security (CCS). The workshop aims to bring together professionals, researchers and practitioners from academia, industry and government in the area of computer security and applied cryptography with an interest in practical applications of homomorphic encryption, encrypted computing, functional encryption and secure function evaluation, private information retrieval and searchable encryption. The workshop will feature 6 exciting talks on different aspects of secure computation and a forum to discuss current and future challenges.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243882"}, {"primary_key": "3373938", "vector": [], "sparse_vector": [], "title": "Facilitating Privacy-preserving Recommendation-as-a-Service with Machine Learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Machine-Learning-as-a-Service has become increasingly popular, with Recommendation-as-a-Service as one of the representative examples. In such services, providing privacy protection for the users is an important topic. Reviewing privacy-preserving solutions which were proposed in the past decade, privacy and machine learning are often seen as two competing goals at stake. Though improving cryptographic primitives (e.g., secure multi-party computation (SMC) or homomorphic encryption (HE)) or devising sophisticated secure protocols has made a remarkable achievement, but in conjunction with state-of-the-art recommender systems often yields far-from-practical solutions. We tackle this problem from the direction of machine learning. We aim to design crypto-friendly recommendation algorithms, thus to obtain efficient solutions by directly using existing cryptographic tools. In particular, we propose an HE-friendly recommender system, refer to as CryptoRec, which (1) decouples user features from latent feature space, avoiding training the recommendation model on encrypted data; (2) only relies on addition and multiplication operations, making the model straightforwardly compatible with HE schemes. The properties turn recommendation-computations into a simple matrix-multiplication operation. To further improve efficiency, we introduce a sparse-quantization-reuse method which reduces the recommendation-computation time by $9\\times$ (compared to using CryptoRec directly), without compromising the accuracy. We demonstrate the efficiency and accuracy of CryptoRec on three real-world datasets. CryptoRec allows a server to estimate a user's preferences on thousands of items within a few seconds on a single PC, with the user's data homomorphically encrypted, while its prediction accuracy is still competitive with state-of-the-art recommender systems computing over clear data. Our solution enables Recommendation-as-a-Service on large datasets in a nearly real-time (seconds) level.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278504"}, {"primary_key": "3373939", "vector": [], "sparse_vector": [], "title": "Precise Android API Protection Mapping Derivation and Reasoning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Android research community has long focused on building an Android API permission specification, which can be leveraged by app developers to determine the optimum set of permissions necessary for a correct and safe execution of their app. However, while prominent existing efforts provide a good approximation of the permission specification, they suffer from a few shortcomings. Dynamic approaches cannot generate complete results, although accurate for the particular execution. In contrast, static approaches provide better coverage, but produce imprecise mappings due to their lack of path-sensitivity. In fact, in light of Android's access control complexity, the approximations hardly abstract the actual co-relations between enforced protections. To address this, we propose to precisely derive Android protection specification in a path-sensitive fashion, using a novel graph abstraction technique. We further showcase how we can apply the generated maps to tackle security issues through logical satisfiability reasoning. Our constructed maps for 4 Android Open Source Project (AOSP) images highlight the significance of our approach, as ~41% of APIs' protections cannot be correctly modeled without our technique.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243842"}, {"primary_key": "3373940", "vector": [], "sparse_vector": [], "title": "When Good Components Go Bad: Formally Secure Compilation Despite Dynamic Compromise.", "authors": ["Carmine <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose a new formal criterion for evaluating secure compilation schemes for unsafe languages, expressing end-to-end security guarantees for software components that may become compromised after encountering undefined behavior---for example, by accessing an array out of bounds. Our criterion is the first to model dynamic compromise in a system of mutually distrustful components with clearly specified privileges. It articulates how each component should be protected from all the others---in particular, from components that have encountered undefined behavior and become compromised. Each component receives secure compilation guarantees---in particular, its internal invariants are protected from compromised components---up to the point when this component itself becomes compromised, after which we assume an attacker can take complete control and use this component's privileges to attack other components. More precisely, a secure compilation chain must ensure that a dynamically compromised component cannot break the safety properties of the system at the target level any more than an arbitrary attacker-controlled component (with the same interface and privileges, but without undefined behaviors) already could at the source level. To illustrate the model, we construct a secure compilation chain for a small unsafe language with buffers, procedures, and components, targeting a simple abstract machine with built-in compartmentalization. We give a careful proof (mostly machine-checked in Coq) that this compiler satisfies our secure compilation criterion. Finally, we show that the protection guarantees offered by the compartmentalized abstract machine can be achieved at the machine-code level using either software fault isolation or a tag-based reference monitor.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243745"}, {"primary_key": "3373942", "vector": [], "sparse_vector": [], "title": "Large-Scale and Language-Oblivious Code Authorship Identification.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Efficient extraction of code authorship attributes is key for successful identification. However, the extraction of such attributes is very challenging, due to various programming language specifics, the limited number of available code samples per author, and the average code lines per file, among others. To this end, this work proposes a Deep Learning-based Code Authorship Identification System (DL-CAIS) for code authorship attribution that facilitates large-scale, language-oblivious, and obfuscation-resilient code authorship identification. The deep learning architecture adopted in this work includes TF-IDF-based deep representation using multiple Recurrent Neural Network (RNN) layers and fully-connected layers dedicated to authorship attribution learning. The deep representation then feeds into a random forest classifier for scalability to de-anonymize the author. Comprehensive experiments are conducted to evaluate DL-CAIS over the entire Google Code Jam (GCJ) dataset across all years (from 2008 to 2016) and over real-world code samples from 1987 public repositories on GitHub. The results of our work show the high accuracy despite requiring a smaller number of files per author. Namely, we achieve an accuracy of 96% when experimenting with 1,600 authors for GCJ, and 94.38% for the real-world dataset for 745 C programmers. Our system also allows us to identify 8,903 authors, the largest-scale dataset used by far, with an accuracy of 92.3%. Moreover, our technique is resilient to language-specifics, and thus it can identify authors of four programming languages (e.g. C, C++, Java, and Python), and authors writing in mixed languages (e.g. Java/C++, Python/C++). Finally, our system is resistant to sophisticated obfuscation (e.g. using C Tigress) with an accuracy of 93.42% for a set of 120 authors.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243738"}, {"primary_key": "3373944", "vector": [], "sparse_vector": [], "title": "11th International Workshop on Artificial Intelligence and Security (AISec 2018).", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "No abstract available.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243872"}, {"primary_key": "3373948", "vector": [], "sparse_vector": [], "title": "PASTA: PASsword-based Threshold Authentication.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Token-based authentication is commonly used to enable a single-sign-on experience on the web, in mobile applications and on enterprise networks using a wide range of open standards and network authentication protocols: clients sign on to an identity provider using their username/password to obtain a cryptographic token generated with a master secret key, and store the token for future accesses to various services and applications. The authentication server(s) are single point of failures that if breached, enable attackers to forge arbitrary tokens or mount offline dictionary attacks to recover client credentials. Our work is the first to introduce and formalize the notion of password-based threshold token-based authentication which distributes the role of an identity provider among n servers. Any t servers can collectively verify passwords and generate tokens, while no t-1 servers can forge a valid token or mount offline dictionary attacks. We then introduce PASTA, a general framework that can be instantiated using any threshold token generation scheme, wherein clients can \"sign-on\" using a two-round (optimal) protocol that meets our strong notions of unforgeability and password-safety. We instantiate and implement our framework in C++ using two threshold message authentication codes (MAC) and two threshold digital signatures with different trade-offs. Our experiments show that the overhead of protecting secrets and credentials against breaches in PASTA, i.e. compared to a naive single server solution, is extremely low (1-5%) in the most likely setting where client and servers communicate over the internet. The overhead is higher in case of MAC-based tokens over a LAN (though still only a few milliseconds) due to public-key operations in PASTA. We show, however, that this cost is inherent by proving a symmetric-key only solution impossible.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243839"}, {"primary_key": "3373949", "vector": [], "sparse_vector": [], "title": "DiSE: Distributed Symmetric-key Encryption.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Threshold cryptography provides a mechanism for protecting secret keys by sharing them among multiple parties, who then jointly perform cryptographic operations. An attacker who corrupts up to a threshold number of parties cannot recover the secrets or violate security. Prior works in this space have mostly focused on definitions and constructions for public-key cryptography and digital signatures, and thus do not capture the security concerns and efficiency challenges of symmetric-key based applications which commonly use long-term (unprotected) master keys to protect data at rest, authenticate clients on enterprise networks, and secure data and payments on IoT devices. We put forth the first formal treatment for distributed symmetric-key encryption, proposing new notions of correctness, privacy and authenticity in presence of malicious attackers. We provide strong and intuitive game-based definitions that are easy to understand and yield efficient constructions. We propose a generic construction of threshold authenticated encryption based on any distributed pseudorandom function (DPRF). When instantiated with the two different DPRF constructions proposed by <PERSON><PERSON>, <PERSON> and <PERSON> (Eurocrypt 1999) and our enhanced versions, we obtain several efficient constructions meeting different security definitions. We implement these variants and provide extensive performance comparisons. Our most efficient instantiation uses only symmetric-key primitives and achieves a throughput of upto 1 million encryptions/decryptions per seconds, or alternatively a sub-millisecond latency with upto 18 participating parties.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243774"}, {"primary_key": "3373952", "vector": [], "sparse_vector": [], "title": "Secure Search on Encrypted Data via Multi-Ring Sketch.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We consider the secure search problem of retrieving from an unsorted data cost=(x_1,...,xm) an item (i,xi) matching a given lookup value l (for a generic matching criterion either hardcoded or given as part of the query), where both input and output are encrypted by a Fully Homomorphic Encryption (FHE). The secure search problem is central in applications of secure outsourcing to an untrusted party (\"the cloud\"). Prior secure search algorithms on FHE encrypted data are realized by polynomials of degree Ømega(m), evaluated in Ømega(log m) sequential homomorphic multiplication steps (ie., multiplicative depth) even using an unbounded number of parallel processors. This is too slow with current FHE implementations, especially as the size of the array grows. We present the first secure search algorithm that is realized by a polynomial of logarithmic degree, log3 m, evaluated in O(log log m) sequential homomorphic multiplication steps (ie., multiplicative depth) using m parallel processors. We implemented our algorithm in an open source library based on HElib and ran experiments on Amazon's EC2 cloud with up to 100 processors. Our experiments show that we can securely search in m= millions of entries in less than an hour on a standard EC2 64-cores machine. We achieve our result by: (1) Employing modern data summarization techniques known as sketching for returning as output (the encryption of) a short sketch C from which the matching item (i,xi) can be decoded in time polynomial in log m. (2) Designing for this purpose a novel sketch that returns the first strictly-positive entry in a (not necessarily sparse) array of non-negative integers; this sketch may be of independent interest. (3) Suggesting a multi-ring evaluation of FHE for degree reduction from linear to logarithmic.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243810"}, {"primary_key": "3373953", "vector": [], "sparse_vector": [], "title": "A Re-evaluation of Intrusion Detection Accuracy: Alternative Evaluation Strategy.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This work tries to evaluate the existing approaches used to benchmark the performance of machine learning models applied to network-based intrusion detection systems (NIDS). First, we demonstrate that we can reach a very high accuracy with most of the traditional machine learning and deep learning models by using the existing performance evaluation strategy. It just requires the right hyperparameter tuning to outperform the existing reported accuracy results in deep learning models. We further question the value of the existing evaluation methods in which the same datasets are used for training and testing the models. We are proposing the use of an alternative strategy that aims to evaluate the practicality and the performance of the models and datasets as well. In this approach, different datasets with compatible sets of features are used for training and testing. When we evaluate the models that we created with the proposed strategy, we demonstrate that the performance is very bad. Thus, models have no practical usage, and it performs based on a pure randomness. This research is important for security-based machine learning applications to re-think about the datasets and the model's quality.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278490"}, {"primary_key": "3373958", "vector": [], "sparse_vector": [], "title": "MTD 2018: 5th ACM Workshop on Moving Target Defense (MTD).", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> Huang"], "summary": "The objective of the 5th ACM Workshop on Moving Target Defense (MTD 2018) - held in Toronto, Canada on October 15, 2018, in conjunction with the 24th ACM Conference on Computer and Communications Security (ACM CCS 2018) - is to bring together researchers from academia, government, and industry to discuss novel randomization, diversification, and dynamism techniques for improving the security of computer systems and network, and new metric and analytical frameworks to assess and quantify the effectiveness of MTD techniques. As in previous editions, the 2018 workshop offers a forum to discuss the challenges and opportunities that such defenses provide. We have assembled an exciting and diverse program including nine refereed papers and one invited keynote talk that will provide participants with a vibrant and thought-provoking set of ideas and insights.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243877"}, {"primary_key": "3373959", "vector": [], "sparse_vector": [], "title": "Prime and Prejudice: Primality Testing Under Adversarial Conditions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This work provides a systematic analysis of primality testing under adversarial conditions, where the numbers being tested for primality are not generated randomly, but instead provided by a possibly malicious party. Such a situation can arise in secure messaging protocols where a server supplies Diffie-Hellman parameters to the peers, or in a secure communications protocol like TLS where a developer can insert such a number to be able to later passively spy on client-server data. We study a broad range of cryptographic libraries and assess their performance in this adversarial setting. As examples of our findings, we are able to construct 2048-bit composites that are declared prime with probability (1/16) by OpenSSL's primality testing in its default configuration; the advertised performance is (2-80). We can also construct 1024-bit composites that always pass the primality testing routine in GNU GMP when configured with the recommended minimum number of rounds. And, for a number of libraries (Cryptlib, LibTomCrypt, JavaScript Big Number, WolfSSL), we can construct composites that always pass the supplied primality tests. We explore the implications of these security failures in applications, focusing on the construction of malicious Di<PERSON>ie-Hellman parameters. We show that, unless careful primality testing is performed, an adversary can supply parameters (p,q,g) which on the surface look secure, but where the discrete logarithm problem in the subgroup of order q generated by g is easy. We close by making recommendations for users and developers. In particular, we promote the Baillie-PSW primality test which is both efficient and conjectured to be robust even in the adversarial setting for numbers up to a few thousand bits.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243787"}, {"primary_key": "3373961", "vector": [], "sparse_vector": [], "title": "PLAS 2018 - ACM SIGSAC Workshop on Programming Languages and Analysis for Security.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The 13th ACM SIGSAC Workshop on Programming Languages and Analysis for Security (PLAS 2018) is co-located with the 25th ACM Conference on Computer and Communications Security (ACM CCS 2018). Over its now more than ten-year history, PLAS has provided a unique forum for researchers and practitioners to exchange ideas about programming language and program analysis techniques with the goal of improving the security of software systems. PLAS aims to provide a forum for exploring and evaluating ideas on using programming language and program analysis techniques to improve the security of software systems. Strongly encouraged are proposals of new, speculative ideas, evaluations of new or known techniques in practical settings, and discussions of emerging threats and important problems.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243879"}, {"primary_key": "3373963", "vector": [], "sparse_vector": [], "title": "Towards Verified, Constant-time Floating Point Operations.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The runtimes of certain floating-point instructions can vary up to two orders of magnitude with instruction operands, allowing attackers to break security and privacy guarantees of real systems (\\eg browsers). To prevent attacks due to such floating-point timing channels, we introduce CTFP, an efficient, machine-checked, and extensible system that transforms unsafe floating-point operations into safe, constant-time computations. CTFP relies on two observations. First, that it is possible to execute floating-point computations in constant-time by emulating them in software; and second, that most security critical applications do not require full IEEE-754 floating-point precision. We use these observations to: eliminate certain classes of dangerous values from ever reaching floating-point hardware; emulate floating-point operations on dangerous values when eliminating them would severely alter application semantics; and, leverage fast floating-point hardware when it is safe to do so. We implement the constant-time transformations with our own domain-specific language that produces LLVM bitcode. Since the transformations themselves equate to bit surgery on already complicated floating-point arithmetic, we use a satisfiability modulo theories (SMT) solver to ensure that their behavior fits our specifications. Finally, we find that CTFP neither breaks real world applications nor incurs overwhelming overhead.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243766"}, {"primary_key": "3373965", "vector": [], "sparse_vector": [], "title": "Phishing Attacks on Modern Android.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Modern versions of Android have introduced a number of features in the name of convenience. This paper shows how two of these features, mobile password managers and Instant Apps, can be abused to make phishing attacks that are significantly more practical than existing ones. We have studied the leading password managers for mobile and we uncovered a number of design issues that leave them open to attacks. For example, we show it is possible to trick password managers into auto-suggesting credentials associated with arbitrary attacker-chosen websites. We then show how an attacker can abuse the recently introduced Instant Apps technology to allow a remote attacker to gain full UI control and, by abusing password managers, to implement an end-to-end phishing attack requiring only few user's clicks. We also found that mobile password managers are vulnerable to \"hidden fields\" attacks, which makes these attacks even more practical and problematic. We conclude this paper by proposing a new secure-by-design API that avoids common errors and we show that the secure implementation of autofill functionality will require a community-wide effort, which this work hopes to inspire.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243778"}, {"primary_key": "3373966", "vector": [], "sparse_vector": [], "title": "Truth Will Out: Departure-Based Process-Level Detection of Stealthy Attacks on Control Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Recent incidents have shown that Industrial Control Systems (ICS) are becoming increasingly susceptible to sophisticated and targeted attacks initiated by adversaries with high motivation, domain knowledge, and resources. Although traditional security mechanisms can be implemented at the IT-infrastructure level of such cyber-physical systems, the community has acknowledged that it is imperative to also monitor the process-level activity, as attacks on ICS may very well influence the physical process. In this paper, we present PASAD, a novel stealthy-attack detection mechanism that monitors time series of sensor measurements in real time for structural changes in the process behavior. We demonstrate the effectiveness of our approach through simulations and experiments on data from real systems. Experimental results show that PASAD is capable of detecting not only significant deviations in the process behavior, but also subtle attack-indicating changes, significantly raising the bar for strategic adversaries who may attempt to maintain their malicious manipulation within the noise level.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243781"}, {"primary_key": "3373967", "vector": [], "sparse_vector": [], "title": "Generalizing the SPDZ Compiler For Other Protocols.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Protocols for secure multiparty computation (MPC) enable a set of mutually distrusting parties to compute an arbitrary function of their inputs while preserving basic security properties like privacy and correctness. The study of MPC was initiated in the 1980s where it was shown that any function can be securely computed, thus demonstrating the power of this notion. However, these proofs of feasibility were theoretical in nature and it is only recently that MPC protocols started to become efficient enough for use in practice. Today, we have protocols that can carry out large and complex computations in very reasonable time (and can even be very fast, depending on the computation and the setting). Despite this amazing progress, there is still a major obstacle to the adoption and use of MPC due to the huge expertise needed to design a specific MPC execution. In particular, the function to be computed needs to be represented as an appropriate Boolean or arithmetic circuit, and this requires very specific expertise. In order to overcome this, there has been considerable work on compilation of code to (typically) Boolean circuits. One work in this direction takes a different approach, and this is the SPDZ compiler (not to be confused with the SPDZ protocol) that takes high-level Python code and provides an MPC run-time environment for securely executing that code. The SPDZ compiler can deal with arithmetic and non-arithmetic operations and is extremely powerful. However, until now, the SPDZ compiler could only be used for the specific SPDZ family of protocols, making its general applicability and usefulness very limited. In this paper, we extend the SPDZ compiler so that it can work with general underlying protocols. Our SPDZ extensions were made in mind to enable the use of SPDZ for arbitrary protocols and to make it easy for others to integrate existing and new protocols. We integrated three different types of protocols, an honest-majority protocol for computing arithmetic circuits over a field (for any number of parties), a three-party honest majority protocol for computing arithmetic circuits over the ring of integers Z2n, and the multiparty BMR protocol for computing Boolean circuits. We show that a single high-level SPDZ-Python program can be executed using all of these underlying protocols (as well as the original SPDZ protocol), thereby making SPDZ a true general run-time MPC environment.In order to be able to handle both arithmetic and non-arithmetic operations, the SPDZ compiler relies on conversions from field elements to bits and back. However, these conversions do not apply to ring elements (in particular, they require element division), and we therefore introduce new bit decomposition and recomposition protocols for the ring over integers with replicated secret sharing. These conversions are of independent interest and utilize the structure of Z2n (which is much more amenable to bit decomposition than prime-order fields), and are thus much more efficient than all previous methods. We demonstrate our compiler extensions by running a complex SQL query and a decision tree evaluation over all protocols.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243854"}, {"primary_key": "3373968", "vector": [], "sparse_vector": [], "title": "How to Choose Suitable Secure Multiparty Computation Using Generalized SPDZ.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A variety of secure multiparty computation (MPC) protocols have been proposed up to now. Since their performance characteristics are incomparable, the most suitable MPC protocol may be completely different depending on the given computational task and environment. It is tedious work to compare all the possibility to choose the most suitable MPC. The paper \" Generalizing the SPDZ Compiler For Other Protocols'' in this ACM-CCS 2018 shows a framework for adding MPC protocols to a development tool of MPC program called \"SPDZ'', which enables to compare multiple protocols easily. This poster and demo show how this framework is useful for choosing the suitable protocol for given target computation and environment.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278510"}, {"primary_key": "3373970", "vector": [], "sparse_vector": [], "title": "Ouroboros Genesis: Composable Proof-of-Stake Blockchains with Dynamic Availability.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a novel Proof-of-Stake (PoS) protocol, Ouroboros Genesis, that enables parties to safely join (or rejoin) the protocol execution using only the genesis block information. Prior to our work, PoS protocols either required parties to obtain a trusted \"checkpoint\" block upon joining and, furthermore, to be frequently online or required an accurate estimate of the number of online parties to be hardcoded into the protocol logic. This ability of new parties to \"bootstrap from genesis\" was a hallmark property of the Bitcoin blockchain and was considered an important advantage of PoW-based blockchains over PoS-based blockchains since it facilitates robust operation in a setting with dynamic availability, i.e., the natural setting---without external trusted objects such as checkpoint blocks---where parties come and go arbitrarily, may join at any moment, or remain offline for prolonged periods of time. We prove the security of Ouroboros Genesis against a fully adaptive adversary controlling less than half of the total stake in a partially synchronous network with unknown message delay and unknown, varying levels of party availability. Our security proof is in the Universally Composable setting assuming the most natural abstraction of a hash function, known as the strict Global Random Oracle (ACM-CCS 2014); this highlights an important advantage of PoS blockchains over their PoW counterparts in terms of composability with respect to the hash function formalisation: rather than a strict GRO, PoW-based protocol security requires a \"local\" random oracle. Finally, proving the security of our construction against an adaptive adversary requires a novel martingale technique that may be of independent interest in the analysis of blockchain protocols.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243848"}, {"primary_key": "3373971", "vector": [], "sparse_vector": [], "title": "Hardware Acceleration of Searchable Encryption.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Searchable symmetric encryption (SSE) allows a client to outsource the storage of her data to an (untrusted) server in a private manner, while maintaining the ability to selectively search over it. A key feature of all existing SSE schemes is the tradeoff between security (in terms of the information leakage to the server) and efficiency (in terms of the operational and storage overhead on the server and client sides). The premise of this work is that SSE schemes typically offer scope for massively parallel implementations with improved efficiency without compromising security. Based on this idea, we propose a highly scalable framework for parallelized SSE implementations using hardware-based crypto-accelerators, interfaced with a software-based control unit and a memory controller unit. We choose field programmable gate arrays (FPGAs) as the platform for the crypto-accelerators due to their flexibility, reconfigurability, low time-to-market and low maintenance overheads. As a case study, we illustrate how the recently proposed SSE scheme of Lai et al. (CCS'18) may be implemented as per our framework, and the benefits thereof, including shorter preprocessing time and reduced query-response latency as compared to a software implementation.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278509"}, {"primary_key": "3373972", "vector": [], "sparse_vector": [], "title": "An End-to-End System for Large Scale P2P MPC-as-a-Service and Low-Bandwidth MPC for Weak Participants.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Protocols for secure multiparty computation enable a set of parties to compute a joint function of their inputs, while preserving privacy, correctness and more. In theory, secure computation has broad applicability and can be used to solve many of the modern concerns around utilization of data and privacy. Huge steps have been made towards this vision in the past few years, and we now have protocols that can carry out large computations extremely efficiently, especially in the setting of an honest majority. However, in practice, there are still major barriers to widely deploying secure computation, especially in a decentralized manner. In this paper, we present the first end-to-end automated system for deploying large-scale MPC protocols between end users, called MPSaaS (for MPC system-as-a-service ). Our system enables parties to pre-enroll in an upcoming MPC computation, and then participate by either running software on a VM instance (e.g., in Amazon), or by running the protocol on a mobile app, in Javascript in their browser, or even on an IoT device. Our system includes an automation system for deploying MPC protocols, an administration component for setting up an MPC computation and inviting participants, and an end-user component for running the MPC protocol in realistic end-user environments. We demonstrate our system for a specific application of running secure polls and surveys, where the secure computation is run end-to-end with each party actually running the protocol (i.e., without relying on a set of servers to run the protocol for them). This is the first such system constructed, and is a big step forward to the goal of commoditizing MPC. One of the cryptographic difficulties that arise in this type of setting is due to the fact that end users may have low bandwidth connections, making it a challenge to run an MPC protocol with high bandwidth. We therefore present a protocol based on Beerliova-Trubiniova and Hirt (TCC 2008) with many optimizations, that has very low concrete communication, and the lowest published for small fields. Our protocol is secure as long as less than a third of the parties are malicious, and is well suited for computing both arithmetic and Boolean circuits. We call our protocol HyperMPC and show that it has impressive performance. In particular, 150 parties can compute statistics---mean, standard deviation and regression---on 4,000,000 inputs (with a circuit of size 16,000,000 gates of which 6,000,000 are multiplication) in just 45 seconds, and 150 parties can compute a circuit over GF[28] (which can be used for a Boolean computation) with 1,000,000 multiplication gates and depth-20 in just 2 seconds. Although our end-to-end system can be used to run any MPC protocol (and we have incorporated numerous protocols already), we demonstrate it for our new protocol that is optimized for end-users without~high~bandwidth.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243801"}, {"primary_key": "3373973", "vector": [], "sparse_vector": [], "title": "Symbolic Proofs for Lattice-Based Cryptography.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Symbolic methods have been used extensively for proving security of cryptographic protocols in the Dolev-Yao model, and more recently for proving security of cryptographic primitives and constructions in the computational model. However, existing methods for proving security of cryptographic constructions in the computational model often require significant expertise and interaction, or are fairly limited in scope and expressivity. This paper introduces a symbolic approach for proving security of cryptographic constructions based on the Learning With Errors assumption (<PERSON><PERSON>, STOC 2005). Such constructions are instances of lattice-based cryptography and are extremely important due to their potential role in post-quantum cryptography. Following (<PERSON><PERSON>, <PERSON> and <PERSON>, CCS 2015), our approach combines a computational logic and deducibility problems---a standard tool for representing the adversary's knowledge, the Dolev-Yao model. The computational logic is used to capture (indistinguishability-based) security notions and drive the security proofs whereas deducibility problems are used as side-conditions to control that rules of the logic are applied correctly. We then use AutoLWE, an implementation of the logic, to deliver very short or even automatic proofs of several emblematic constructions, including CPA-PKE (Gentry et al., STOC 2008), (Hierarchical) Identity-Based Encryption (<PERSON><PERSON><PERSON> et al. Eurocrypt 2010), Inner Product Encryption (<PERSON><PERSON><PERSON> et al. Asiacrypt 2011), CCA-PKE (<PERSON><PERSON><PERSON><PERSON> et al., Eurocrypt 2012). The main technical novelty beyond AutoLWE is a set of (semi-)decision procedures for deducibility problems, using extensions of Gröbner basis computations for subalgebras in the (non-)commutative setting (instead of ideals in the commutative setting). Our procedures cover the theory of matrices, which is required for lattice-based assumption, as well as the theory of non-commutative rings, fields, and Diffie-Hellman exponentiation, in its standard, bilinear and multilinear forms. Additionally, AutoLWE supports oracle-relative assumptions, which are used specifically to apply (advanced forms of) the Leftover Hash Lemma, an information-theoretical tool widely used in lattice-based proofs.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243825"}, {"primary_key": "3373974", "vector": [], "sparse_vector": [], "title": "BitML: A Calculus for Bitcoin Smart Contracts.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce BitML, a domain-specific language for specifying contracts that regulate transfers of bitcoins among participants, without relying on trusted intermediaries. We define a symbolic and a computational model for reasoning about BitML security. In the symbolic model, participants act according to the semantics of BitML, while in the computational model they exchange bitstrings, and read/append transactions on the Bitcoin blockchain. A compiler is provided to translate contracts into standard Bitcoin transactions. Participants can execute a contract by appending these transactions on the Bitcoin blockchain, according to their strategies. We prove the correctness of our compiler, showing that computational attacks on compiled contracts are also observable in the symbolic model.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243795"}, {"primary_key": "3373975", "vector": [], "sparse_vector": [], "title": "A Formal Analysis of 5G Authentication.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Mobile communication networks connect much of the world's population. The security of users' calls, SMSs, and mobile data depends on the guarantees provided by the Authenticated Key Exchange protocols used. For the next-generation network (5G), the 3GPP group has standardized the 5G AKA protocol for this purpose. We provide the first comprehensive formal model of a protocol from the AKA family: 5G AKA. We also extract precise requirements from the 3GPP standards defining 5G and we identify missing security goals. Using the security protocol verification tool Tamarin, we conduct a full, systematic, security evaluation of the model with respect to the 5G security goals. Our automated analysis identifies the minimal security assumptions required for each security goal and we find that some critical security goals are not met, except under additional assumptions missing from the standard. Finally, we make explicit recommendations with provably secure fixes for the attacks and weaknesses we found.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243846"}, {"primary_key": "3373976", "vector": [], "sparse_vector": [], "title": "If This Then What?: Controlling Flows in IoT Apps.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "IoT apps empower users by connecting a variety of otherwise unconnected services. These apps (or applets ) are triggered by external information sources to perform actions on external information sinks. We demonstrate that the popular IoT app platforms, including IFTTT (If This Then That), Zapier, and Microsoft Flow are susceptible to attacks by malicious applet makers, including stealthy privacy attacks to exfiltrate private photos, leak user location, and eavesdrop on user input to voice-controlled assistants. We study a dataset of 279,828 IFTTT applets from more than 400 services, classify the applets according to the sensitivity of their sources, and find that 30% of the applets may violate privacy. We propose two countermeasures for short- and longterm protection: access control and information flow control. For short-term protection, we suggest that access control classifies an applet as either exclusively private or exclusively public, thus breaking flows from private sources to sensitive sinks. For longterm protection, we develop a framework for information flow tracking in IoT apps. The framework models applet reactivity and timing behavior, while at the same time faithfully capturing the subtleties of attacker observations caused by applet output. We show how to implement the approach for an IFTTT-inspired setting leveraging state-of-the-art information flow tracking techniques for JavaScript based on the JSFlow tool and evaluate its effectiveness on a collection of applets.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243841"}, {"primary_key": "3373978", "vector": [], "sparse_vector": [], "title": "Secure Multi-Party Computation.", "authors": ["<PERSON><PERSON><PERSON>", "Marina Blanton"], "summary": "Secure multi-party computation (SMC) is an emerging topic which has been drawing growing attention during recent decades. There are many examples which show importance of SMC constructions in practice, such as privacy-preserving decision making and machine learning, auctions, private set intersection, and others. In this tutorial, we provide a comprehensive coverage of SMC techniques, starting from precise definitions and fundamental techniques. Consequently, a significant portion of the tutorial focuses on recent advances in general SMC constructions. We cover garbled circuit evaluation (GCE) and linear secret sharing (LSS) which are commonly used for secure two-party and multi-party computation, respectively. The coverage includes both standard adversarial models: semi-honest and malicious. For GCE, we start with the original <PERSON>'s garbled circuits construction [30] for semi-honest adversaries and consequently cover its recent optimizations such as the \"free XOR,'' the garbled row reduction, the half-gates optimization, and the use of AES NI techniques. We follow with a discussion of techniques for making GCE resilient to malicious behavior, which includes the cut-and-choose approach and additional techniques to deter known attacks in the presence of malicious participants. In addition, we include the-state-of-the-art protocols for oblivious transfer (OT) and OT extension in the presence of semi-honest and malicious users. For LSS, we start from standard solutions for the semi-honest adversarial model including [5, 28] and consequently move to recent efficient constructions for semi-honest and malicious adversarial models. The coverage includes different types of corruption thresholds (with and without honest majority), which imply different guarantees with respect to abort.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3264419"}, {"primary_key": "3373979", "vector": [], "sparse_vector": [], "title": "TACHYON: Fast Signatures from Compact Knapsack.", "authors": ["Rouzbeh Behnia", "Muslum Ozgur Ozmen", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We introduce a simple, yet efficient digital signature scheme which offers post-quantum security promise. Our scheme, named TACHYON, is based on a novel approach for extending one-time hash-based signatures to (polynomially bounded) many-time signatures, using the additively homomorphic properties of generalized compact knapsack functions. Our design permits TACHYON~to achieve several key properties. First, its signing and verification algorithms are the fastest among its current counterparts with a higher level of security. This allows TACHYON~to achieve the lowest end-to-end delay among its counterparts, while also making it suitable for resource-limited signers. Second, its private keys can be as small as κ bits, where κ is the desired security level. Third, unlike most of its lattice-based counterparts, TACHYON~does not require any Gaussian sampling during signing, and therefore, is free from side-channel attacks targeting this process. We also explore various speed and storage trade-offs for TACHYON, thanks to its highly tunable parameters. Some of these trade-offs can speed up TACHYON signing in exchange for larger keys, thereby permitting TACHYON~to further improve its end-to-end delay.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243819"}, {"primary_key": "3373985", "vector": [], "sparse_vector": [], "title": "DP-Finder: Finding Differential Privacy Violations by Sampling and Optimization.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present DP-Finder, a novel approach and system that automatically derives lower bounds on the differential privacy enforced by algorithms. Lower bounds are practically useful as they can show tightness of existing upper bounds or even identify incorrect upper bounds. Computing a lower bound involves searching for a counterexample, defined by two neighboring inputs and a set of outputs, that identifies a large privacy violation. This is an inherently hard problem as finding such a counterexample involves inspecting a large (usually infinite) and sparse search space. To address this challenge, DP-Finder relies on two key insights. First, we introduce an effective and precise correlated sampling method to estimate the privacy violation of a counterexample. Second, we show how to obtain a differentiable version of the problem, enabling us to phrase the search task as an optimization objective to be maximized with state-of-the-art numerical optimizers. This allows us to systematically search for large privacy violations. Our experimental results indicate that DP-Finder is effective in computing differential privacy lower bounds for a number of randomized algorithms. For instance, it finds tight lower bounds in algorithms that obfuscate their input in a non-trivial fashion.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243863"}, {"primary_key": "3373987", "vector": [], "sparse_vector": [], "title": "Wild Patterns: Ten Years After the Rise of Adversarial Machine Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deep neural networks and machine-learning algorithms are pervasively used in several applications, ranging from computer vision to computer security. In most of these applications, the learning algorithm has to face intelligent and adaptive attackers who can carefully manipulate data to purposely subvert the learning process. As these algorithms have not been originally designed under such premises, they have been shown to be vulnerable to well-crafted, sophisticated attacks, including training-time poisoning and test-time evasion attacks (also known as adversarial examples). The problem of countering these threats and learning secure classifiers in adversarial settings has thus become the subject of an emerging, relevant research field known as adversarial machine learning. The purposes of this tutorial are: (a) to introduce the fundamentals of adversarial machine learning to the security community; (b) to illustrate the design cycle of a learning-based pattern recognition system for adversarial tasks; (c) to present novel techniques that have been recently proposed to assess performance of pattern classifiers and deep learning algorithms under attack, evaluate their vulnerabilities, and implement defense strategies that make learning algorithms more robust to attacks; and (d) to show some applications of adversarial machine learning to pattern recognition tasks like object recognition in images, biometric identity recognition, spam and malware detection.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3264418"}, {"primary_key": "3373988", "vector": [], "sparse_vector": [], "title": "Bandwidth-Hard Functions: Reductions and Lower Bounds.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Memory Hard Functions (MHFs) have been proposed as an answer to the growing inequality between the computational speed of general purpose CPUs and Application Specific Integrated Circuits (ASICs). MHFs have seen widespread applications including password hashing, key stretching and proofs of work. Several metrics have been proposed to quantify the \"memory hardness\" of a function. Cumulative memory complexity (CMC) (or amortized Area × Time complexity ) attempts to quantify the cost to acquire/build the hardware to evaluate the function - after normalizing the time it takes to evaluate the function. By contrast, bandwidth hardness attempts to quantify the amortized energy costs of evaluating this function on hardware - which in turn is largely dominated by the number of cache misses. Ideally, a good MHF would be both bandwidth hard and have high cumulative memory complexity. While the cumulative memory complexity of leading MHF candidates is well understood, little is known about the bandwidth hardness of many prominent MHF candidates. Our contributions are as follows: First, we provide the first reduction proving that, in the parallel random oracle model, the bandwidth hardness of a Data-Independent Memory Hard Function (iMHF) is described by the red-blue pebbling cost of the directed acyclic graph (DAG) associated with that iMHF. Second, we show that the goals of designing an MHF with high CMC/bandwidth hardness are well aligned. In particular, we prove that any function with high CMC also has relatively high energy costs. This result leads to the first unconditional lower bound on the energy cost of scrypt in the parallel random oracle model. Third, we analyze the bandwidth hardness of several prominent iMHF candidates such as Argon2i, winner of the password hashing competition, aATSample and DRSample - the first practical iMHF with essentially asymptotically optimal CMC. We show Argon2i, aATSample and DRSample are maximally bandwidth hard under appropriate cache size. Finally, we show that the problem of finding a red-blue pebbling with minimum energy cost is NP-hard.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243773"}, {"primary_key": "3373993", "vector": [], "sparse_vector": [], "title": "Compressing Vector OLE.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>v <PERSON>", "<PERSON><PERSON>"], "summary": "Oblivious linear-function evaluation (OLE) is a secure two-party protocol allowing a receiver to learn any linear combination of a pair of field elements held by a sender. OLE serves as a common building block for secure computation of arithmetic circuits, analogously to the role of oblivious transfer (OT) for boolean circuits. A useful extension of OLE is vector OLE (VOLE), allowing the receiver to learn any linear combination of two vectors held by the sender. In several applications of OLE, one can replace a large number of instances of OLE by a smaller number of instances of VOLE. This motivates the goal of amortizing the cost of generating long instances of VOLE. We suggest a new approach for fast generation of pseudo-random instances of VOLE via a deterministic local expansion of a pair of short correlated seeds and no interaction. This provides the first example of compressing a non-trivial and cryptographically useful correlation with good concrete efficiency. Our VOLE generators can be used to enhance the efficiency of a host of cryptographic applications. These include secure arithmetic computation and non-interactive zero-knowledge proofs with reusable preprocessing. Our VOLE generators are based on a novel combination of function secret sharing (FSS) for multi-point functions and linear codes in which decoding is intractable. Their security can be based on variants of the learning parity with noise (LPN) assumption over large fields that resist known attacks. We provide several constructions that offer tradeoffs between different efficiency measures and the underlying intractability assumptions.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243868"}, {"primary_key": "3373994", "vector": [], "sparse_vector": [], "title": "Domain Validation++ For MitM-Resilient PKI.", "authors": ["<PERSON>", "<PERSON><PERSON>xiang Dai", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The security of Internet-based applications fundamentally relies on the trustworthiness of Certificate Authorities (CAs). We practically demonstrate for the first time that even a weak off-path attacker can effectively subvert the trustworthiness of popular commercially used CAs. Our attack targets CAs which use Domain Validation (DV) for authenticating domain ownership; collectively these CAs control 99% of the certificates market. The attack utilises DNS Cache poisoning and tricks the CA into issuing fraudulent certificates for domains the attacker does not legitimately own -- namely certificates binding the attacker's public key to a victim domain. We discuss short and long term defences, but argue that they fall short of securing DV. To mitigate the threats we propose Domain Validation++ (DV++). DV++ replaces the need in cryptography through assumptions in distributed systems. While retaining the benefits of DV (automation, efficiency and low costs) DV++ is secure even against Man-in-the-Middle (MitM) attackers. Deployment of DV++ is simple and does not require changing the existing infrastructure nor systems of the CAs. We demonstrate security of DV++ under realistic assumptions and provide open source access to DV++ implementation.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243790"}, {"primary_key": "3373995", "vector": [], "sparse_vector": [], "title": "Practical Fault Attack on Deep Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Dirmanto Jap", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "As deep learning systems are widely adopted in safety- and security-critical applications, such as autonomous vehicles, banking systems, etc., malicious faults and attacks become a tremendous concern, which potentially could lead to catastrophic consequences. In this paper, we initiate the first study of leveraging physical fault injection attacks on Deep Neural Networks (DNNs), by using laser injection technique on embedded systems. In particular, our exploratory study targets four widely used activation functions in DNNs development, that are the general main building block of DNNs that creates non-linear behaviors -- ReLu, softmax, sigmoid, and tanh. Our results show that by targeting these functions, it is possible to achieve a misclassification by injecting faults into the hidden layer of the network. Such result can have practical implications for real-world applications, where faults can be introduced by simpler means (such as altering the supply voltage).", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278519"}, {"primary_key": "3373998", "vector": [], "sparse_vector": [], "title": "Nemesis: Studying Microarchitectural Timing Leaks in Rudimentary CPU Interrupt Logic.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent research on transient execution vulnerabilities shows that current processors exceed our levels of understanding. The prominent Meltdown and Spectre attacks abruptly revealed fundamental design flaws in CPU pipeline behavior and exception handling logic, urging the research community to systematically study attack surface from microarchitectural interactions. We present Nemesis, a previously overlooked side-channel attack vector that abuses the CPU's interrupt mechanism to leak microarchitectural instruction timings from enclaved execution environments such as Intel SGX, Sancus, and TrustLite. At its core, Nemesis abuses the same subtle microarchitectural behavior that enables Meltdown, i.e., exceptions and interrupts are delayed until instruction retirement. We show that by measuring the latency of a carefully timed interrupt, an attacker controlling the system software is able to infer instruction-granular execution state from hardware-enforced enclaves. In contrast to speculative execution vulnerabilities, our novel attack vector is applicable to the whole computing spectrum, from small embedded sensor nodes to high-end commodity x86 hardware. We present practical interrupt timing attacks against the open-source Sancus embedded research processor, and we show that interrupt latency reveals microarchitectural instruction timings from off-the-shelf Intel SGX enclaves. Finally, we discuss challenges for mitigating Nemesis-type attacks at the hardware and software levels.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243822"}, {"primary_key": "3374000", "vector": [], "sparse_vector": [], "title": "HyCC: Compilation of Hybrid Protocols for Practical Secure Computation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "While secure multi-party computation (MPC) is a vibrant research topic and a multitude of practical MPC applications have been presented recently, their development is still a tedious task that requires expert knowledge. Previous works have made first steps in compiling high-level descriptions from various source descriptions into MPC protocols, but only looked at a limited set of protocols. In this work we present HyCC, a tool-chain for automated compilation of ANSI C programs into hybrid protocols that efficiently and securely combine multiple MPC protocols with optimizing compilation, scheduling, and partitioning. As a result, our compiled protocols are able to achieve performance numbers that are comparable to hand-built solutions. For the MiniONN neural network (<PERSON> et al., CCS 2017), our compiler improves performance of the resulting protocol by more than a factor of $3$. Thus, for the first time, highly efficient hybrid MPC becomes accessible for developers without cryptographic background.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243786"}, {"primary_key": "3374001", "vector": [], "sparse_vector": [], "title": "A Discussion on Security Education in Academia.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This panel will explore how security topics are integrated into academic programs and future directions for improvements. It will address how early in time security should be introduced in programs like computer science and software engineering; and identify the critical takeaways that each graduating student should learn. We will try to separate out the important, practical concepts from the purely academic ones. We will also consider how well security programs translate to industry-focused needs: do students emerge with an understanding that is both deep and broad enough to be useful? In general, we will try to identify the pitfalls of current security education and how we can move forward as an academic community, in tandem with industry and government.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3284027"}, {"primary_key": "3374002", "vector": [], "sparse_vector": [], "title": "Fast Secure Computation for Small Population over the Internet.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Secure Multi-Party Computation (MPC) with small number of parties is an interesting area of research, primarily due to its ability to model most real-life MPC applications and the simplicity and efficiency of the resulting protocols. In this work, we present efficient, constant-round 3-party (3PC) and 4-party (4PC) protocols in the honest-majority setting that achieve strong security notions of fairness (corrupted parties receive their output only if all honest parties receive output) and guaranteed output delivery (corrupted parties cannot prevent honest parties from receiving their output). Being constant-round, our constructions are suitable for Internet-like high-latency networks and are built from garbled circuits (GC). Assuming the minimal model of pairwise-private channels, we present two protocols that involve computation and communication of a single GC-- (a) a 4-round 3PC with fairness, (b) a 5-round 4PC with guaranteed output delivery. Empirically, our protocols are on par with the best known 3PC protocol of <PERSON><PERSON><PERSON> et al. [CCS 2015] that only achieves security with selective abort, in terms of the computation time, LAN runtime, WAN runtime and communication cost. In fact, our 4PC outperforms the 3PC of <PERSON><PERSON><PERSON> et al. significantly in terms of per-party computation and communication cost. With an extra GC, we improve the round complexity of our 4PC to four rounds. The only 4PC in our setting, given by <PERSON><PERSON> et al. [CRYPTO 2015], involves 12 GCs. Assuming an additional broadcast channel, we present a 5-round 3PC with guaranteed output delivery that involves computation and communication of a single GC. A broadcast channel is inevitable in this setting for achieving guaranteed output delivery, owing to an impossibility result in the literature. The overall broadcast communication of our protocol is nominal and most importantly, is independent of the circuit size. This protocol too induces a nominal overhead compared to the protocol of Mohassel et al.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243784"}, {"primary_key": "3374004", "vector": [], "sparse_vector": [], "title": "Screaming Channels: When Electromagnetic Side Channels Meet Radio Transceivers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper presents a new side channel that affects mixed-signal chips used in widespread wireless communication protocols, such as Bluetooth and WiFi. This increasingly common type of chip includes the radio transceiver along with digital logic on the same integrated circuit. In such systems, the radio transmitter may unintentionally broadcast sensitive information from hardware cryptographic components or software executing on the CPU. The well-known electromagnetic (EM) leakage from digital logic is inadvertently mixed with the radio carrier, which is amplified and then transmitted by the antenna. We call the resulting leak screaming channels. Attacks exploiting such a side channel may succeed over a much longer distance than attacks exploiting usual EM side channels. The root of the problem is that mixed-signal chips include both digital circuits and analog circuits on the same silicon die in close physical proximity. While processing data, the digital circuits on these chips generate noise, which can be picked up by noise-sensitive analog radio components, ultimately leading to leakage of sensitive information. We investigate the physical reasons behind the channel, we measure it on several popular devices from different vendors (including Nordic Semiconductor nRF52832, and Qualcomm Atheros AR9271), and we demonstrate a complete key recovery attack against the nRF52832 chip. In particular, we retrieve the full key from the AES-128 implementation in tinyAES at a distance of 10 m using template attacks. Additionally, we recover the key used by the AES-128 implementation in mbedTLS at a distance of 1 m with a correlation attack. Screaming channel attacks change the threat models of devices with mixed-signal chips, as those devices are now vulnerable from a distance. More specifically, we argue that protections against side channels (such as masking or hiding) need to be used on this class of devices. Finally, chips implementing other widespread protocols (e.g., 4G/LTE, RFID) need to be inspected to determine whether they are vulnerable to screaming channel attacks.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243802"}, {"primary_key": "3374011", "vector": [], "sparse_vector": [], "title": "FlowNotation: An Annotation System for Statically Enforcing Information Flow Policies in C.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Programmers often need to enforce high-level policies on their cryptographic applications written in C; for instance, that private data is not sent over public channels, trusted data is not modified by untrusted functions, and that the ordering of protocol steps is maintained. These secrecy, integrity, and sequencing policies can be cumbersome to check with existing general-purpose tools. We have developed a novel means of specifying and checking these policies that allows for a much lighter-weight approach than previous tools; requiring less work from programmers. Further, we have modeled our policy annotations as an information flow type system and proved a noninterference guarantee. We embed the policy annotations in C's type system via a source-to-source translation and leverage existing C type checkers to enforce our policies, achieving high performance and scalability. We show through case studies of cryptographic libraries from both industry and recent literature that our work expresses detailed policies for large bodies of C code with little annotation burden, and finds subtle implementation bugs.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278508"}, {"primary_key": "3374013", "vector": [], "sparse_vector": [], "title": "New Constructions for Forward and Backward Private Symmetric Searchable Encryption.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Rasool Jalili"], "summary": "We study the problem of dynamic symmetric searchable encryption. In that setting, it is crucial to minimize the information revealed to the server as a result of update operations (insertions and deletions). Two relevant privacy properties have been defined in that context: forward and backward privacy. The first makes it hard for the server to link an update operation with previous queries and has been extensively studied in the literature. The second limits what the server can learn about entries that were deleted from the database, from queries that happen after the deletion. Backward privacy was formally studied only recently (<PERSON><PERSON> et al., CCS 2017) in a work that introduced a formal definition with three variable types of leakage (Type-I to Type-III ordered from most to least secure), as well as the only existing schemes that satisfy this property. In this work, we introduce three novel constructions that improve previous results in multiple ways. The first scheme achieves Type-II backward privacy and our experimental evaluation shows it has 145-253X faster search computation times than previous constructions with the same leakage. Surprisingly, it is faster even than schemes with Type-III leakage which makes it the most efficient implementation of a forward and backward private scheme so far. The second one has search time that is asymptotically within a polylogarithmic multiplicative factor of the theoretical optimal (i.e., the result size of a search), and it achieves the strongest level of backward privacy (Type-I). All previous Type-I constructions require time that is at least linear in the total number of updates for the requested keywords, even the (arbitrarily many) previously deleted ones. Our final scheme improves upon the second one by reducing the number of roundtrips for a search at the cost of extra leakage (Type-III).", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243833"}, {"primary_key": "3374014", "vector": [], "sparse_vector": [], "title": "ASHES 2018- Workshop on Attacks and Solutions in Hardware Security.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "As in the successful first edition, the second Workshop on Attacks and Solutions in Hardware Security (ASHES) 2018 deals with all aspects of hardware security. Among others, this year, the workshop particularly highlights emerging techniques and methods as well as recent application areas within the field. These include new attack vectors, attack countermeasures, and novel designs and implementations on the methodological side, as well as the Internet of Things, automotive security, smart homes, pervasive and wearable computing on the applications side. In order to meet the requirements of these rapidly developing subareas, ASHES calls for paper submissions in four categories: 1) classical full papers; 2) classical short papers; 3) systematization of knowledge papers which overview, structure, and categorize a subarea; and 4) wild and crazy papers whose purpose is rapid dissemination of promising, potentially game-changing ideas.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243873"}, {"primary_key": "3374015", "vector": [], "sparse_vector": [], "title": "MVG Mechanism: Differential Privacy under Matrix-Valued Query.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Differential privacy mechanism design has traditionally been tailored for a scalar-valued query function. Although many mechanisms such as the Laplace and Gaussian mechanisms can be extended to a matrix-valued query function by adding i.i.d. noise to each element of the matrix, this method is often suboptimal as it forfeits an opportunity to exploit the structural characteristics typically associated with matrix analysis. To address this challenge, we propose a novel differential privacy mechanism called the Matrix-Variate Gaussian (MVG) mechanism, which adds a matrix-valued noise drawn from a matrix-variate Gaussian distribution, and we rigorously prove that the MVG mechanism preserves (ε,δ)-differential privacy. Furthermore, we introduce the concept of directional noise made possible by the design of the MVG mechanism. Directional noise allows the impact of the noise on the utility of the matrix-valued query function to be moderated. Finally, we experimentally demonstrate the performance of our mechanism using three matrix-valued queries on three privacy-sensitive datasets. We find that the MVG mechanism can notably outperforms four previous state-of-the-art approaches, and provides comparable utility to the non-private baseline.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243750"}, {"primary_key": "3374016", "vector": [], "sparse_vector": [], "title": "Labeled PSI from Fully Homomorphic Encryption with Malicious Security.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Private Set Intersection (PSI) allows two parties, the sender and the receiver, to compute the intersection of their private sets without revealing extra information to each other. We are interested in the unbalanced PSI setting, where (1) the receiver's set is significantly smaller than the sender's, and (2) the receiver (with the smaller set) has a low-power device. Also, in a Labeled PSI setting, the sender holds a label per each item in its set, and the receiver obtains the labels from the items in the intersection. We build upon the unbalanced PSI protocol of Chen, Laine, and Rindal (CCS~2017) in several ways: we add efficient support for arbitrary length items, we construct and implement an unbalanced Labeled PSI protocol with small communication complexity, and also strengthen the security model using Oblivious Pseudo-Random Function (OPRF) in a pre-processing phase. Our protocols outperform previous ones: for an intersection of 220 and $512$ size sets of arbitrary length items our protocol has a total online running time of just $1$~second (single thread), and a total communication cost of 4 MB. For a larger example, an intersection of 228 and 1024 size sets of arbitrary length items has an online running time of $12$ seconds (multi-threaded), with less than 18 MB of total communication.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243836"}, {"primary_key": "3374017", "vector": [], "sparse_vector": [], "title": "Mystique: Uncovering Information Leakage from Browser Extensions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Browser extensions are small JavaScript, CSS and HTML programs that run inside the browser with special privileges. These programs, often written by third parties, operate on the pages that the browser is visiting, giving the user a programmatic way to configure the browser. The privacy implications that arise by allowing privileged third-party code to execute inside the users' browser are not well understood. In this paper, we develop a taint analysis framework for browser extensions and use it to perform a large scale study of extensions in regard to their privacy practices. We first present a hybrid approach to traditional taint analysis: by leveraging the fact that extension source code is available to the runtime JavaScript engine, we implement as well as enhance traditional taint analysis using information gathered from static data flow and control-flow analysis of the JavaScript source code. Based on this, we further modify the Chromium browser to support taint tracking for extensions. We analyzed 178,893 extensions crawled from the Chrome Web Store between September 2016 and March 2018, as well as a separate set of all available extensions (2,790 in total) for the Opera browser at the time of analysis. From these, our analysis flagged 3,868 (2.13%) extensions as potentially leaking privacy-sensitive information. The top 10 most popular Chrome extensions that we confirmed to be leaking privacy-sensitive information have more than 60 million users combined. We ran the analysis on a local Kubernetes cluster and were able to finish within a month, demonstrating the feasibility of our approach for large-scale analysis of browser extensions. At the same time, our results emphasize the threat browser extensions pose to user privacy, and the need for countermeasures to safeguard against misbehaving extensions that abuse their privileges.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243823"}, {"primary_key": "3374019", "vector": [], "sparse_vector": [], "title": "Hawkeye: Towards a Desired Directed Grey-box Fuzzer.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Yuekang Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Grey-box fuzzing is a practically effective approach to test real-world programs. However, most existing grey-box fuzzers lack directedness, i.e. the capability of executing towards user-specified target sites in the program. To emphasize existing challenges in directed fuzzing, we propose Hawkeye to feature four desired properties of directed grey-box fuzzers. Owing to a novel static analysis on the program under test and the target sites, <PERSON><PERSON> precisely collects the information such as the call graph, function and basic block level distances to the targets. During fuzzing, <PERSON><PERSON> evaluates exercised seeds based on both static information and the execution traces to generate the dynamic metrics, which are then used for seed prioritization, power scheduling and adaptive mutating. These strategies help Hawkeye to achieve better directedness and gravitate towards the target sites. We implemented Hawkeye as a fuzzing framework and evaluated it on various real-world programs under different scenarios. The experimental results showed that Hawkeye can reach the target sites and reproduce the crashes much faster than state-of-the-art grey-box fuzzers such as AFL and AFLGo. Specially, Hawkeye can reduce the time to exposure for certain vulnerabilities from about 3.5 hours to 0.5 hour. By now, Hawkeye has detected more than 41 previously unknown crashes in projects such as Oniguruma, MJS with the target sites provided by vulnerability prediction tools; all these crashes are confirmed and 15 of them have been assigned CVE IDs.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243849"}, {"primary_key": "3374020", "vector": [], "sparse_vector": [], "title": "Cracking the TSCH Channel Hopping in IEEE 802.15.4e.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Industrial networks typically connect hundreds or thousands of sensors and actuators in industrial facilities, such as steel mills and oil refineries. Although the typical industrial applications operate at low data rates, they pose unique challenges because of their critical demands for reliable and real-time communication in harsh industrial environments. IEEE 802.15.4 based Wireless Sensor-Actuator Networks (WSANs) technology is appealing for use to construct industrial networks because it can be deployed and maintained inexpensively. Battery-powered wireless modules easily and inexpensively retrofit existing sensors and actuators in industrial facilities without running cabling for communication and power. To address the stringent real-time and reliability requirements, WSANs adopt a set of novel design choices such as employing the Time-Synchronized Channel Hopping (TSCH) technology that distinguish themselves from traditional Wireless Sensor Networks (WSNs) that require only best effort services. The equation-based channel hopping used in TSCH simplifies the network operations at the cost of security. Our case study shows that an attacker can reverse engineer the channel hopping sequence by silently observing the channel activities and then perform smart collision attacks. In this poster proposal, we describe our target problem and present our case study based on a publicly accessible implementation of TSCH.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278502"}, {"primary_key": "3374021", "vector": [], "sparse_vector": [], "title": "Towards Paving the Way for Large-Scale Windows Malware Analysis: Generic Binary Unpacking with Orders-of-Magnitude Performance Boost.", "authors": ["<PERSON><PERSON>", "Jiang <PERSON>", "Jianming Fu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Binary packing, encoding binary code prior to execution and decoding them at run time, is the most common obfuscation adopted by malware authors to camouflage malicious code. Especially, most packers recover the original code by going through a set of \"written-then-executed\" layers, which renders determining the end of the unpacking increasingly difficult. Many generic binary unpacking approaches have been proposed to extract packed binaries without the prior knowledge of packers. However, the high runtime overhead and lack of anti-analysis resistance have severely limited their adoptions. Over the past two decades, packed malware is always a veritable challenge to anti-malware landscape. This paper revisits the long-standing binary unpacking problem from a new angle: packers consistently obfuscate the standard use of API calls. Our in-depth study on an enormous variety of Windows malware packers at present leads to a common property: malware's Import Address Table (IAT), which acts as a lookup table for dynamically linked API calls, is typically erased by packers for further obfuscation; and then unpacking routine, like a custom dynamic loader, will reconstruct IAT before original code resumes execution. During a packed malware execution, if an API is invoked through looking up a rebuilt IAT, it indicates that the original payload has been restored. This insight motivates us to design an efficient unpacking approach, called BinUnpack. Compared to the previous methods that suffer from multiple \"written-then-executed\" unpacking layers, BinUnpack is free from tedious memory access monitoring, and therefore it introduces very small runtime overhead. To defeat a variety of ever-evolving evasion tricks, we design BinUnpack's API monitor module via a novel kernel-level DLL hijacking technique. We have evaluated BinUnpack's efficacy extensively with more than 238K packed malware and multiple Windows utilities. BinUnpack's success rate is significantly better than that of existing tools with several orders of magnitude performance boost. Our study demonstrates that BinUnpack can be applied to speeding up large-scale malware analysis.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243771"}, {"primary_key": "3374025", "vector": [], "sparse_vector": [], "title": "EviHunter: Identifying Digital Evidence in the Permanent Storage of Android Devices via Static Analysis.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Crimes, both physical and cyber, increasingly involve smartphones due to their ubiquity. Therefore, digital evidence on smartphones plays an increasingly important role in crime investigations. Digital evidence could reside in the memory and permanent storage of a smartphone. While we have witnessed significant progresses on memory forensics recently, identifying evidence in the permanent storage is still an underdeveloped research area. Most existing studies on permanent-storage forensics rely on manual analysis or keyword-based scanning of the permanent storage. Manual analysis is costly, while keyword matching often misses the evidentiary data that do not have interesting keywords. In this work, we develop a tool called EviHunter to automatically identify evidentiary data in the permanent storage of an Android device. There could be thousands of files on the permanent storage of a smartphone. A basic question a forensic investigator often faces is which files could store evidentiary data. <PERSON>viHunter aims to answer this question. Our intuition is that the evidentiary data were produced by apps; and an app's code has rich information about the types of data the app may write to a permanent storage and the files the data are written to. Therefore, <PERSON><PERSON><PERSON><PERSON><PERSON> first pre-computes an App Evidence Database (AED) via static analysis of a large number of apps. The AED includes the types of evidentiary data and files that store them for each app. Then, <PERSON><PERSON><PERSON><PERSON><PERSON> matches the files on a smartphone's permanent storage against the AED to identify the files that could store evidentiary data. We evaluate <PERSON><PERSON><PERSON><PERSON><PERSON> on benchmark apps and 8,690 real-world apps. Our results show that <PERSON><PERSON><PERSON><PERSON><PERSON> can precisely identify both the types of evidentiary data and the files that store them.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243808"}, {"primary_key": "3374026", "vector": [], "sparse_vector": [], "title": "Towards Usable Checksums: Automating the Integrity Verification of Web Downloads for the Masses.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Internet users can download software for their computers from app stores (e.g., Mac App Store and Windows Store) or from other sources, such as the developers' websites. Most Internet users in the US rely on the latter, according to our representative study, which makes them directly responsible for the content they download. To enable users to detect if the downloaded files have been corrupted, developers can publish a checksum together with the link to the program file; users can then manually verify that the checksum matches the one they obtain from the downloaded file. In this paper, we assess the prevalence of such behavior among the general Internet population in the US (N=2,000), and we develop easy-to-use tools for users and developers to automate both the process of checksum verification and generation. Specifically, we propose an extension to the recent W3C specification for sub-resource integrity in order to provide integrity protection for download links. Also, we develop an extension for the popular Chrome browser that computes and verifies checksums of downloaded files automatically, and an extension for the WordPress CMS that developers can use to easily attach checksums to their remote content. Our in situ experiments with 40participants demonstrate the usability and effectiveness issues of checksums verification, and shows user desirability for our extension.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243746"}, {"primary_key": "3374028", "vector": [], "sparse_vector": [], "title": "Detecting Attacks Against Robotic Vehicles: A Control Invariant Approach.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Robotic vehicles (RVs), such as drones and ground rovers, are a type of cyber-physical systems that operate in the physical world under the control of computing components in the cyber world. Despite RVs' robustness against natural disturbances, cyber or physical attacks against RVs may lead to physical malfunction and subsequently disruption or failure of the vehicles' missions. To avoid or mitigate such consequences, it is essential to develop attack detection techniques for RVs. In this paper, we present a novel attack detection framework to identify external, physical attacks against RVs on the fly by deriving and monitoring Control Invariants (CI). More specifically, we propose a method to extract such invariants by jointly modeling a vehicle's physical properties, its control algorithm and the laws of physics. These invariants are represented in a state-space form, which can then be implemented and inserted into the vehicle's control program binary for runtime invariant check. We apply our CI framework to eleven RVs, including quadrotor, hexarotor, and ground rover, and show that the invariant check can detect three common types of physical attacks -- including sensor attack, actuation signal attack, and parameter attack -- with very low runtime overhead.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243752"}, {"primary_key": "3374031", "vector": [], "sparse_vector": [], "title": "Public Order Preserving Cipher Generation Scheme for Distributed Computing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Ordering is a widely used operation in distributed settings. However certain distributed settings like an on-line auction, place unique requirements on the protocol design. Firstly, all entities participate in the ordering with a communication channel(s) only with a coordinator(s), completely oblivious to other participants. This lack of intra-party communication channels makes traditional secure multi-party computations unsuitable for this scenario. Secondly, the security and functionality of the protocol should not depend on a single piece of secret information such as a secret symmetric key, ( as in the case of order-preserving encryption, OPE ). It is so because now every participating entity has to be communicated the secret key in order for them to encrypt their private data. However this means that even if just one of the entities is corrupt, the security of all the honest entities is compromised. These restrictions render both SMPC and OPE ill-suited for the above distributed setting. In this paper we propose a public order-preserving cipher generation scheme (POPC) that addresses the aforementioned challenges. POPC encodes a transform of the plaintext using a public order-preserving probabilistic encoding and generates the cipher in a two round interactive protocol. In POPC neither the correctness nor the security of the scheme depends on the possession of a single secret key. Moreover POPC needs no intra-party communication for its execution. We show POPC achieves the ideal security guarantee for any total order-preserving scheme, which is to reveal no information about the plaintexts beside the order, with a ciphertext space that is polynomial in size of the plaintext.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278530"}, {"primary_key": "3374033", "vector": [], "sparse_vector": [], "title": "On Ends-to-Ends Encryption: Asynchronous Group Messaging with Strong Security Guarantees.", "authors": ["<PERSON><PERSON>-<PERSON>", "Cas Cremers", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In the past few years secure messaging has become mainstream, with over a billion active users of end-to-end encryption protocols such as Signal. The Signal Protocol provides a strong property called post-compromise security to its users. However, it turns out that many of its implementations provide, without notification, a weaker property for group messaging: an adversary who compromises a single group member can read and inject messages indefinitely. We show for the first time that post-compromise security can be achieved in realistic, asynchronous group messaging systems. We present a design called Asynchronous Ratcheting Trees (ART), which uses tree-based Diffie-Hellman key exchange to allow a group of users to derive a shared symmetric key even if no two are ever online at the same time. ART scales to groups containing thousands of members, while still providing provable security guarantees. It has seen significant interest from industry, and forms the basis for two draft IETF RFCs and a chartered working group. Our results show that strong security guarantees for group messaging are practically achievable in a modern setting.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243747"}, {"primary_key": "3374034", "vector": [], "sparse_vector": [], "title": "Practical State Recovery Attacks against Legacy RNG Implementations.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The ANSI X9.17/X9.31 pseudorandom number generator design was first standardized in 1985, with variants incorporated into numerous cryptographic standards over the next three decades. The design uses timestamps together with a statically keyed block cipher to produce pseudo-random output. It has been known since 1998 that the key must remain secret in order for the output to be secure. However, neither the FIPS 140-2 standardization process nor NIST's later descriptions of the algorithm specified any process for key generation. We performed a systematic study of publicly available FIPS 140- 2 certifications for hundreds of products that implemented the ANSI X9.31 random number generator, and found twelve whose certification documents use of static, hard-coded keys in source code, leaving the implementation vulnerable to an attacker who can learn this key from the source code or binary. In order to demonstrate the practicality of such an attack, we develop a full passive decryption attack against FortiGate VPN gateway products using FortiOS v4 that recovers the private key in seconds. We measure the prevalence of this vulnerability on the visible Internet using active scans, and demonstrate state recovery and full private key recovery in the wild. Our work highlights the extent to which the validation and certification process has failed to provide even modest security guarantees.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243756"}, {"primary_key": "3374038", "vector": [], "sparse_vector": [], "title": "Voting: You Can&apos;t Have Privacy without Individual Verifiability.", "authors": ["Véronique Cortier", "<PERSON>"], "summary": "Electronic voting typically aims at two main security goals: vote privacy and verifiability. These two goals are often seen as antagonistic and some national agencies even impose a hierarchy between them: first privacy, and then verifiability as an additional feature. Verifiability typically includes individual verifiability (a voter can check that her ballot is counted); universal verifiability (anyone can check that the result corresponds to the published ballots); and eligibility verifiability (only legitimate voters may vote). We show that actually, privacy implies individual verifiability. In other words, systems without individual verifiability cannot achieve privacy (under the same trust assumptions). To demonstrate the generality of our result, we show this implication in two different settings, namely cryptographic and symbolic models, for standard notions of privacy and individual verifiability. Our findings also highlight limitations in existing privacy definitions in cryptographic settings.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243762"}, {"primary_key": "3374039", "vector": [], "sparse_vector": [], "title": "Secure Opportunistic Multipath Key Exchange.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The security of today's widely used communication security protocols is based on trust in Certificate Authorities (CAs). However, the real security of this approach is debatable, since certificate handling is tedious and many recent attacks have undermined the trust in CAs. On the other hand, opportunistic encryption protocols such as Tcpcrypt, which are currently gaining momentum as an alternative to no encryption, have similar security to using untrusted CAs or self-signed certificates: they only protect against passive attackers. In this paper, we present a key exchange protocol, Secure Multipath Key Exchange (SMKEX), that enables all the benefits of opportunistic encryption (no need for trusted third parties or pre-established secrets), as well as proven protection against some classes of active attackers. Furthermore, SMKEX can be easily extended to a trust-on-first-use setting and can be easily integrated with TLS, providing the highest security for opportunistic encryption to date while also increasing the security of standard TLS. We show that SMKEX is made practical by the current availability of path diversity between different AS-es. We also show a method to create path diversity with encrypted tunnels without relying on the network topology. These allow SMKEX to provide protection against most adversaries for a majority of Alexa top 100 web sites. We have implemented SMKEX using a modified Multipath TCP kernel implementation and a user library that overwrites part of the socket API, allowing unmodified applications to take advantage of the security provided by SMKEX.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243791"}, {"primary_key": "3374041", "vector": [], "sparse_vector": [], "title": "ALCHEMY: A Language and Compiler for Homomorphic Encryption Made easY.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Fully Homomorphic Encryption (FHE) is a cryptographic \"holy grail\" that allows a worker to perform arbitrary computations on client-encrypted data, without learning anything about the data itself. Since the first plausible construction in 2009, a variety of FHE implementations have been given and used for particular applications of interest. Unfortunately, using FHE is currently very complicated, and a great deal of expertise is required to properly implement nontrivial homomorphic computations. This work introduces ALCHEMY, a modular and extensible system that simplifies and accelerates the use of FHE. ALCHEMY compiles \"in-the-clear\" computations on plaintexts, written in a modular domain-specific language~(DSL), into corresponding homomorphic computations on ciphertexts---with no special knowledge of FHE required of the programmer. The compiler automatically chooses (most of the) parameters by statically inferring ciphertext noise rates, generates keys and \"key-switching hints,\" schedules appropriate ciphertext \"maintenance\" operations, and more. In addition, its components can be combined modularly to provide other useful functionality, such logging the empirical noise rates of ciphertexts throughout a computation, without requiring any changes to the original DSL code. As a testbed application, we demonstrate fast homomorphic evaluation of a pseudorandom function~(PRF) based on Ring-LWR, whose entire implementation is only a few dozen lines of simple DSL code. For a single (non-batched) evaluation, our unoptimized implementation takes only about 10 seconds on a commodity PC, which is more than an order of magnitude faster than state-of-the-art homomorphic evaluations of other PRFs, including some specifically designed for amenability to homomorphic evaluation.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243828"}, {"primary_key": "3374042", "vector": [], "sparse_vector": [], "title": "Off-Path Attacks Against PKI.", "authors": ["<PERSON><PERSON>xiang Dai", "<PERSON><PERSON>", "<PERSON>"], "summary": "The security of Internet-based applications fundamentally relies on the trustworthiness of Certificate Authorities (CAs). We practically demonstrate for the first time that even a very weak attacker, namely, an off-path attacker, can effectively subvert the trustworthiness of popular commercially used CAs. We demonstrate an attack against one popular CA which uses Domain Validation (DV) for authenticating domain ownership. The attack exploits DNS Cache Poisoning and tricks the CA into issuing fraudulent certificates for domains the attacker does not legitimately own -- namely certificates binding the attacker's public key to a victim domain.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278516"}, {"primary_key": "3374044", "vector": [], "sparse_vector": [], "title": "The Influence of Code Coverage Metrics on Automated Testing Efficiency in Android.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Code coverage is an important metric that is used by automated Android testing and security analysis tools to guide the exploration of applications and to assess efficacy. Yet, there are many different variants of this metric and there is no agreement within the Android community on which are the best to work with. In this paper, we report on our preliminary study using the state-of-the-art automated test design tool Sapienz. Our results suggest a viable hypothesis that combining different granularities of code coverage metrics can be beneficial for achieving better results in automated testing of Android applications.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278524"}, {"primary_key": "3374048", "vector": [], "sparse_vector": [], "title": "Investigating System Operators&apos; Perspective on Security Misconfigurations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Nowadays, security incidents have become a familiar \"nuisance,\" and they regularly lead to the exposure of private and sensitive data. The root causes for such incidents are rarely complex attacks. Instead, they are enabled by simple misconfigurations, such as authentication not being required, or security updates not being installed. For example, the leak of over 140 million Americans' private data from Equifax's systems is among most severe misconfigurations in recent history: The underlying vulnerability was long known, and a security patch had been available for months, but was never applied. Ultimately, <PERSON><PERSON>fa<PERSON> blamed an employee for forgetting to update the affected system, highlighting his personal responsibility. In this paper, we investigate the operators' perspective on security misconfigurations to approach the human component of this class of security issues. We focus our analysis on system operators, who have not received significant attention by prior research. Hence, we investigate their perspective with an inductive approach and apply a multi-step empirical methodology: (i), a qualitative study to understand how to approach the target group and measure the misconfiguration phenomenon (ii) a quantitative survey rooted in the qualitative data. We then provide the first analysis of system operators' perspective on security misconfigurations, and we determine the factors that operators perceive as the root causes. Based on our findings, we provide practical recommendations on how to reduce security misconfigurations' frequency and impact.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243794"}, {"primary_key": "3374049", "vector": [], "sparse_vector": [], "title": "On the Safety of IoT Device Physical Interaction Control.", "authors": ["<PERSON><PERSON>", "Hong<PERSON> Hu"], "summary": "Emerging Internet of Things (IoT) platforms provide increased functionality to enable human interaction with the physical world in an autonomous manner. The physical interaction features of IoT platforms allow IoT devices to make an impact on the physical environment. However, such features also bring new safety challenges, where attackers can leverage stealthy physical interactions to launch attacks against IoT systems. In this paper, we propose a framework called IoTMon that discovers any possible physical interactions and generates all potential interaction chains across applications in the IoT environment. IoTMon also includes an assessment of the safety risk of each discovered inter-app interaction chain based on its physical influence. To demonstrate the feasibility of our approach, we provide a proof-of-concept implementation of IoTMon and present a comprehensive system evaluation on the Samsung SmartThings platform. We study 185 official SmartThings applications and find they can form 162 hidden inter-app interaction chains through physical surroundings. In particular, our experiment reveals that 37 interaction chains are highly risky and could be potentially exploited to impact the safety of the IoT~environment.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243865"}, {"primary_key": "3374050", "vector": [], "sparse_vector": [], "title": "Detecting Violations of Differential Privacy.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhang", "<PERSON>"], "summary": "The widespread acceptance of differential privacy has led to the publication of many sophisticated algorithms for protecting privacy. However, due to the subtle nature of this privacy definition, many such algorithms have bugs that make them violate their claimed privacy. In this paper, we consider the problem of producing counterexamples for such incorrect algorithms. The counterexamples are designed to be short and human-understandable so that the counterexample generator can be used in the development process -- a developer could quickly explore variations of an algorithm and investigate where they break down. Our approach is statistical in nature. It runs a candidate algorithm many times and uses statistical tests to try to detect violations of differential privacy. An evaluation on a variety of incorrect published algorithms validates the usefulness of our approach: it correctly rejects incorrect algorithms and provides counterexamples for them within a few seconds.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243818"}, {"primary_key": "3374051", "vector": [], "sparse_vector": [], "title": "AIM-SDN: Attacking Information Mismanagement in SDN-datastores.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Zim<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Network Management is a critical process for an enterprise to configure and monitor the network devices using cost effective methods. It is imperative for it to be robust and free from adversarial or accidental security flaws. With the advent of cloud computing and increasing demands for centralized network control, conventional management protocols like SNMP appear inadequate and newer techniques like NMDA and NETCONF have been invented. However, unlike SNMP which underwent improvements concentrating on security, the new data management and storage techniques have not been scrutinized for the inherent security flaws. In this paper, we identify several vulnerabilities in the widely used critical infrastructures which leverage the Network Management Datastore Architecture design (NMDA). Software Defined Networking (SDN), a proponent of NMDA, heavily relies on its datastores to program and manage the network. We base our research on the security challenges put forth by the existing datastore's design as implemented by the SDN controllers. The vulnerabilities identified in this work have a direct impact on the controllers like OpenDayLight, Open Network Operating System and their proprietary implementations (by CISCO, Ericsson, RedHat, Brocade, Juniper, etc). Using our threat detection methodology, we demonstrate how the NMDA-based implementations are vulnerable to attacks which compromise availability, integrity, and confidentiality of the network. We finally propose defense measures to address the security threats in the existing design and discuss the challenges faced while employing these countermeasures.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243799"}, {"primary_key": "3374053", "vector": [], "sparse_vector": [], "title": "BEAT: Asynchronous BFT Made Practical.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present BEAT, a set of practical Byzantine fault-tolerant (BFT) protocols for completely asynchronous environments. BEAT is flexible, versatile, and extensible, consisting of five asynchronous BFT protocols that are designed to meet different goals (e.g., different performance metrics, different application scenarios). Due to modularity in its design, features of these protocols can be mixed to achieve even more meaningful trade-offs between functionality and performance for various applications. Through a 92-instance, five-continent deployment of BEAT on Amazon EC2, we show that BEAT is efficient: roughly, all our BEAT instances significantly outperform, in terms of both latency and throughput, HoneyBadgerBFT, the most efficient asynchronous BFT known.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243812"}, {"primary_key": "3374056", "vector": [], "sparse_vector": [], "title": "FairSwap: How To Fairly Exchange Digital Goods.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce FairSwap -- an efficient protocol for fair exchange of digital goods using smart contracts. A fair exchange protocol allows a sender S to sell a digital commodity x for a fixed price p to a receiver R. The protocol is said to be secure if <PERSON> only pays if he receives the correct x. Our solution guarantees fairness by relying on smart contracts executed over decentralized cryptocurrencies, where the contract takes the role of an external judge that completes the exchange in case of disagreement. While in the past there have been several proposals for building fair exchange protocols over cryptocurrencies, our solution has two distinctive features that makes it particular attractive when users deal with large commodities. These advantages are: (1) minimizing the cost for running the smart contract on the blockchain, and (2) avoiding expensive cryptographic tools such as zero-knowledge proofs. In addition to our new protocols, we provide formal security definitions for smart contract based fair exchange, and prove security of our construction. Finally, we illustrate several applications of our basic protocol and evaluate practicality of our approach via a prototype implementation for fairly selling large files over the cryptocurrency Ethereum.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243857"}, {"primary_key": "3374057", "vector": [], "sparse_vector": [], "title": "General State Channel Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "One of the fundamental challenges that hinder further adaption of decentralized cryptocurrencies is scalability. Because current cryptocurrencies require that all transactions are processed and stored on a distributed ledger -- the so-called blockchain -- transaction throughput is inherently limited. An important proposal to significantly improve scalability are off-chain protocols, where the massive amount of transactions is executed without requiring the costly interaction with the blockchain. Examples of off-chain protocols include payment channels and networks, which are currently deployed by popular cryptocurrencies such as Bitcoin and Ethereum. A further extension of payment networks envisioned for cryptocurrencies are so-called state channel networks. In contrast to payment networks that only support off-chain payments between users, state channel networks allow execution of arbitrary complex smart contracts. The main contribution of this work is to give the first full specification for general state channel networks. Moreover, we provide formal security definitions and prove the security of our construction against powerful adversaries. An additional benefit of our construction is the use of channel virtualization, which further reduces latency and costs in complex channel networks.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243856"}, {"primary_key": "3374062", "vector": [], "sparse_vector": [], "title": "No Training Hurdles: Fast Training-Agnostic Attacks to Infer Your Typing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Shang<PERSON> Zhao", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Traditional methods to eavesdrop keystrokes leverage some malware installed in a target computer to record the keystrokes for an adversary. Existing research work has identified a new class of attacks that can eavesdrop the keystrokes in a non-invasive way without infecting the target computer to install a malware. The common idea is that pressing a key of a keyboard can cause a unique and subtle environmental change, which can be captured and analyzed by the eavesdropper to learn the keystrokes. For these attacks, however, a training phase must be accomplished to establish the relationship between an observed environmental change and the action of pressing a specific key. This significantly limits the impact and practicality of these attacks. In this paper, we discover that it is possible to design keystroke eavesdropping attacks without requiring the training phase. We create this attack based on the channel state information extracted from wireless signal. To eavesdrop keystrokes, we establish a mapping between typing each letter and its respective environmental change by exploiting the correlation among observed changes and known structures of dictionary words. We implement this attack on software-defined radio platforms and conduct a suite of experiments to validate the impact of this attack. We point out that this paper does not propose to use wireless signal for inferring keystrokes, since such work already exists. Instead, the main goal of this paper is to propose new techniques to remove the training process, which can make existing work unpractical.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243755"}, {"primary_key": "3374064", "vector": [], "sparse_vector": [], "title": "HyperFlow: A Processor Architecture for Nonmalleable, Timing-Safe Information Flow Security.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents HyperFlow, a processor that enforces secure information flow, including control over timing channels. The design and implementation of HyperFlow offer security assurance because it is implemented using a security-typed hardware description language that enforces secure information flow. Unlike prior processors that aim to enforce simple information-flow policies such as noninterference, HyperFlow allows complex information flow policies that can be configured at run time. Its fine-grained, decentralized information flow mechanisms allow controlled communication among mutually distrusting processes and system calls into different security domains. We address the significant challenges in designing such a processor architecture with contributions in both the hardware architecture and the security type system. The paper discusses the architecture decisions that make the processor secure and describes ChiselFlow, a new secure hardware description language supporting lightweight information-flow enforcement. The HyperFlow architecture is prototyped on a full-featured processor that offers a complete RISC-V instruction set, and is shown to add moderate overhead to area and performance.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243743"}, {"primary_key": "3374071", "vector": [], "sparse_vector": [], "title": "Peeling the Onion&apos;s User Experience Layer: Examining Naturalistic Use of the Tor Browser.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The strength of an anonymity system depends on the number of users. Therefore, User eXperience (UX) and usability of these systems is of critical importance for boosting adoption and use. To this end, we carried out a study with 19 non-expert participants to investigate how users experience routine Web browsing via the Tor Browser, focusing particularly on encountered problems and frustrations. Using a mixed-methods quantitative and qualitative approach to study one week of naturalistic use of the Tor Browser, we uncovered a variety of UX issues, such as broken Web sites, latency, lack of common browsing conveniences, differential treatment of Tor traffic, incorrect geolocation, operational opacity, etc. We applied this insight to suggest a number of UX improvements that could mitigate the issues and reduce user frustration when using the Tor Browser.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243803"}, {"primary_key": "3374073", "vector": [], "sparse_vector": [], "title": "Property Inference Attacks on Fully Connected Neural Networks using Permutation Invariant Representations.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "With the growing adoption of machine learning, sharing of learned models is becoming popular. However, in addition to the prediction properties the model producer aims to share, there is also a risk that the model consumer can infer other properties of the training data the model producer did not intend to share. In this paper, we focus on the inference of global properties of the training data, such as the environment in which the data was produced, or the fraction of the data that comes from a certain class, as applied to white-box Fully Connected Neural Networks (FCNNs). Because of their complexity and inscrutability, FCNNs have a particularly high risk of leaking unexpected information about their training sets; at the same time, this complexity makes extracting this information challenging. We develop techniques that reduce this complexity by noting that FCNNs are invariant under permutation of nodes in each layer. We develop our techniques using representations that capture this invariance and simplify the information extraction task. We evaluate our techniques on several synthetic and standard benchmark datasets and show that they are very effective at inferring various data properties. We also perform two case studies to demonstrate the impact of our attack. In the first case study we show that a classifier that recognizes smiling faces also leaks information about the relative attractiveness of the individuals in its training set. In the second case study we show that a classifier that recognizes Bitcoin mining from performance counters also leaks information about whether the classifier was trained on logs from machines that were patched for the Meltdown and Spectre attacks.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243834"}, {"primary_key": "3374077", "vector": [], "sparse_vector": [], "title": "Fast Multiparty Threshold ECDSA with Fast Trustless Setup.", "authors": ["<PERSON>", "<PERSON>"], "summary": "A threshold signature scheme enables distributed signing among n players such that any subgroup of size $t+1$ can sign, whereas any group with t or fewer players cannot. While there exist previous threshold schemes for the ECDSA signature scheme, we are the first protocol that supports multiparty signatures for any $t łeq n$ with an efficient dealerless key generation. Our protocol is faster than previous solutions and significantly reduces the communication complexity as well. We prove our scheme secure against malicious adversaries with a dishonest majority. We implemented our protocol, demonstrating its efficiency and suitability to be deployed in practice.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243859"}, {"primary_key": "3374078", "vector": [], "sparse_vector": [], "title": "Lattice-Based zk-SNARKs from Square Span Programs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Zero-knowledge SNARKs (zk-SNARKs) are non-interactive proof systems with short and efficiently verifiable proofs. They elegantly resolve the juxtaposition of individual privacy and public trust, by providing an efficient way of demonstrating knowledge of secret information without actually revealing it. To this day, zk-SNARKs are being used for delegating computation, electronic cryptocurrencies, and anonymous credentials. However, all current SNARKs implementations rely on pre-quantum assumptions and, for this reason, are not expected to withstand cryptanalitic efforts over the next few decades. In this work, we introduce the first designated-verifier zk-SNARK based on lattice assumptions, which are believed to be post-quantum secure. We provide a generalization in the spirit of <PERSON><PERSON><PERSON> et al. (Eurocrypt'13) to the SNARK of <PERSON> et al. (Asiacrypt'14) that is based on Square Span Programs (SSPs) and relies on weaker computational assumptions. We focus on designated-verifier proofs and propose a protocol in which a proof consists of just 5 LWE encodings. We provide a concrete choice of parameters as well as extensive benchmarks on a C implementation, showing that our construction is practically instantiable.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243845"}, {"primary_key": "3374082", "vector": [], "sparse_vector": [], "title": "Toward Trustworthy Deep Learning in Security.", "authors": ["Wooyoung Go", "<PERSON><PERSON><PERSON>"], "summary": "In the security area, there has been an increasing tendency to apply deep learning, which is perceived as a black box method because of the lack of understanding of its internal functioning. Can we trust deep learning models when they achieve high test accuracy? Using a visual explanation method, we find that deep learning models used in security tasks can easily focus on semantically non-discriminative parts of input data even though they produce the right answers. Furthermore, when a model is re-trained without any change in the learning procedure (i.e., no change in training/validation data, initialization/optimization methods and hyperparameters), it can focus on significantly different parts of many samples while producing the same answers. For trustworthy deep learning in security, therefore, we argue that it is necessary to verify the classification criteria of deep learning models before deploying them, even though they successfully achieve high test accuracy.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278526"}, {"primary_key": "3374083", "vector": [], "sparse_vector": [], "title": "On the Accuracy of Password Strength Meters.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Password strength meters are an important tool to help users choose secure passwords. Strength meters can only then provide reasonable guidance when they are accurate, i.e., their score correctly reflect password strength. A strength meter with low accuracy may do more harm than good and guide the user to choose passwords with a high score but low actual security. While a substantial number of different strength meters is proposed in the literature and deployed in practice, we are lacking a clear picture of which strength meters provide high accuracy, and thus are most helpful for guiding users. Furthermore, we lack a clear understanding of how to compare accuracies of strength meters. In this work, (i) we propose a set of properties that a strength meter needs to fulfill to be considered to have high accuracy, (ii) we use these properties to select a suitable measure that can determine the accuracy of strength meters, and (iii) we use the selected measure to compare a wide range of strength meters proposed in the academic literature, provided by password managers, operating systems, and those used on websites. We expect our work to be helpful in the selection of good password strength meters by service operators, and to aid the further development of improved strength meters.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243769"}, {"primary_key": "3374084", "vector": [], "sparse_vector": [], "title": "&quot;What was that site doing with my Facebook password?&quot;: Designing Password-Reuse Notifications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> U<PERSON>"], "summary": "Password reuse is widespread, so a breach of one provider's password database threatens accounts on other providers. When companies find stolen credentials on the black market and notice potential password reuse, they may require a password reset and send affected users a notification. Through two user studies, we provide insight into such notifications. In Study 1, 180 respondents saw one of six representative notifications used by companies in situations potentially involving password reuse. Respondents answered questions about their reactions and understanding of the situation. Notifications differed in the concern they elicited and intended actions they inspired. Concerningly, less than a third of respondents reported intentions to change any passwords. In Study 2, 588 respondents saw one of 15 variations on a model notification synthesizing results from Study 1. While the variations' impact differed in small ways, respondents' intended actions across all notifications would leave them vulnerable to future password-reuse attacks. We discuss best practices for password-reuse notifications and how notifications alone appear insufficient in solving password reuse.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243767"}, {"primary_key": "3374090", "vector": [], "sparse_vector": [], "title": "Pump up the Volume: Practical Database Reconstruction from Volume Leakage on Range Queries.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present attacks that use only the volume of responses to range queries to reconstruct databases. Our focus is on practical attacks that work for large-scale databases with many values and records, without requiring assumptions on the data or query distributions. Our work improves on the previous state-of-the-art due to <PERSON><PERSON><PERSON> et al. (CCS 2016) in all of these dimensions. Our main attack targets reconstruction of database counts and involves a novel graph-theoretic approach. It generally succeeds when R , the number of records, exceeds $N^2/2$, where N is the number of possible values in the database. For a uniform query distribution, we show that it requires volume leakage from only O(N2 łog N) queries (cf. O(N4łog N) in prior work). We present two ancillary attacks. The first identifies the value of a new item added to a database using the volume leakage from fresh queries, in the setting where the adversary knows or has previously recovered the database counts. The second shows how to efficiently recover the ranges involved in queries in an online fashion, given an auxiliary distribution describing the database. Our attacks are all backed with mathematical analyses and extensive simulations using real data.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243864"}, {"primary_key": "3374093", "vector": [], "sparse_vector": [], "title": "LEMNA: Explaining Deep Learning based Security Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Mu", "<PERSON>", "Purui Su", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While deep learning has shown a great potential in various domains, the lack of transparency has limited its application in security or safety-critical areas. Existing research has attempted to develop explanation techniques to provide interpretable explanations for each classification decision. Unfortunately, current methods are optimized for non-security tasks ( e.g., image analysis). Their key assumptions are often violated in security applications, leading to a poor explanation fidelity. In this paper, we propose LEMNA, a high-fidelity explanation method dedicated for security applications. Given an input data sample, LEMNA generates a small set of interpretable features to explain how the input sample is classified. The core idea is to approximate a local area of the complex deep learning decision boundary using a simple interpretable model. The local interpretable model is specially designed to (1) handle feature dependency to better work with security applications ( e.g., binary code analysis); and (2) handle nonlinear local boundaries to boost explanation fidelity. We evaluate our system using two popular deep learning applications in security (a malware classifier, and a function start detector for binary reverse-engineering). Extensive evaluations show that LEMNA's explanation has a much higher fidelity level compared to existing methods. In addition, we demonstrate practical use cases of LEMNA to help machine learning developers to validate model behavior, troubleshoot classification errors, and automatically patch the errors of the target models.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243792"}, {"primary_key": "3374096", "vector": [], "sparse_vector": [], "title": "Utility-Aware Synthesis of Differentially Private and Attack-Resilient Location Traces.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As mobile devices and location-based services become increasingly ubiquitous, the privacy of mobile users' location traces continues to be a major concern. Traditional privacy solutions rely on perturbing each position in a user's trace and replacing it with a fake location. However, recent studies have shown that such point-based perturbation of locations is susceptible to inference attacks and suffers from serious utility losses, because it disregards the moving trajectory and continuity in full location traces. In this paper, we argue that privacy-preserving synthesis of complete location traces can be an effective solution to this problem. We present AdaTrace, a scalable location trace synthesizer with three novel features: provable statistical privacy, deterministic attack resilience, and strong utility preservation. AdaTrace builds a generative model from a given set of real traces through a four-phase synthesis process consisting of feature extraction, synopsis learning, privacy and utility preserving noise injection, and generation of differentially private synthetic location traces. The output traces crafted by AdaTrace preserve utility-critical information existing in real traces, and are robust against known location trace attacks. We validate the effectiveness of AdaTrace by comparing it with three state of the art approaches (ngram, DPT, and SGLT) using real location trace datasets (Geolife and Taxi) as well as a simulated dataset of 50,000 vehicles in Oldenburg, Germany. AdaTrace offers up to 3-fold improvement in trajectory utility, and is orders of magnitude faster than previous work, while preserving differential privacy and attack resilience.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243741"}, {"primary_key": "3374098", "vector": [], "sparse_vector": [], "title": "Advanced Cryptography: Promise and Challenges.", "authors": ["<PERSON><PERSON>"], "summary": "I will discuss \"advanced cryptography\", namely cryptographic techniques beyond communication security, including things like zero knowledge, secure multi-party computation, homomorphic encryption, and the like. I will make the case that advanced cryptography is (a) needed, (b) fast enough to be useful, and (c) Not \"generally usable\" yet.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3268995"}, {"primary_key": "3374104", "vector": [], "sparse_vector": [], "title": "2nd International Workshop on Multimedia Privacy and Security.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This workshop addresses the security and privacy issues that have developed as our society has become more interconnected, specifically with respect to multimedia data generated in the context of the Internet of Things (IoT) and Web 2.0/3.0. The word \"multimedia\" here has expanded beyond its original scope. With the rise of social media and online P2P sharing services, large quantities of multimedia data (e.g., pictures, videos, audio, and computer graphics) are being created and shared constantly. When all these data are stored in a networked environment, many people can connect to it for viewing, sharing, commenting, and storing information. Particularly, multimedia data in IoT networks serves a significant purpose as many people's status, locations, and live actions can be seen, disseminated, tracked, commented on, and monitored in real time. IoT opens up many possibilities for attacks since more people can broadcast themselves and allow their networks and networks' networks to view and share in their lives.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243876"}, {"primary_key": "3374105", "vector": [], "sparse_vector": [], "title": "Building Applications with Homomorphic Encryption.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In 2009, <PERSON> introduced the first \"fully\" homomorphic encryption scheme allowing arbitrary circuits to be evaluated on encrypted data. Homomorphic encryption is a very powerful cryptographic primitive, though it has often been viewed by practitioners as too inefficient for practical applications. However, the performance of these encryption schemes has come a long way from that of <PERSON><PERSON>'s original work: there are now several well-maintained libraries implementing homomorphic encryption schemes and protocols demonstrating impressive performance results, alongside an ongoing standardization effort by the community. In this tutorial we survey the existing homomorphic encryption landscape, providing both a general overview of the state of the art, as well as a deeper dive into several of the existing libraries. We aim to provide a thorough introduction to homomorphic encryption accessible by the broader computer security community. Several of the presenters are core developers of well-known publicly available homomorphic encryption libraries, and organizers of the homomorphic encryption standardization effort \\hrefhttp://homomorphicencryption.org/. This tutorial is targeted at application developers, security researchers, privacy engineers, graduate students, and anyone else interested in learning the basics of modern homomorphic encryption.The tutorial is divided into two parts: Part I is accessible by everyone comfortable with basic college-level math; Part II will cover more advanced topics, including descriptions of some of the different homomorphic encryption schemes and libraries, concrete example applications and code samples, and a deeper discussion on implementation challenges. Part II requires the audience to be familiar with modern C++.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3264420"}, {"primary_key": "3374107", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON>: Predicting Debug Information in Stripped Binaries.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a novel approach for predicting debug information in stripped binaries. Using machine learning, we first train probabilistic models on thousands of non-stripped binaries and then use these models to predict properties of meaningful elements in unseen stripped binaries. Our focus is on recovering symbol names, types and locations, which are critical source-level information wiped off during compilation and stripping. Our learning approach is able to distinguish and extract key elements such as register-allocated and memory-allocated variables usually not evident in the stripped binary. To predict names and types of extracted elements, we use scalable structured prediction algorithms in probabilistic graphical models with an extensive set of features which capture key characteristics of binary code. Based on this approach, we implemented an automated tool, called Debin, which handles ELF binaries on three of the most popular architectures: x86, x64 and ARM. Given a stripped binary, <PERSON><PERSON> outputs a binary augmented with the predicted debug information. Our experimental results indicate that <PERSON>bin is practically useful: for x64, it predicts symbol names and types with 68.8% precision and 68.3% recall. We also show that <PERSON><PERSON> is helpful for the task of inspecting real-world malware -- it revealed suspicious library usage and behaviors such as DNS resolver reader.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243866"}, {"primary_key": "3374109", "vector": [], "sparse_vector": [], "title": "Effective Program Debloating via Reinforcement Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Prevalent software engineering practices such as code reuse and the \"one-size-fits-all\" methodology have contributed to significant and widespread increases in the size and complexity of software. The resulting software bloat has led to decreased performance and increased security vulnerabilities. We propose a system called Chisel to enable programmers to effectively customize and debloat programs. <PERSON><PERSON> takes as input a program to be debloated and a high-level specification of its desired functionality. The output is a reduced version of the program that is correct with respect to the specification. <PERSON><PERSON> significantly improves upon existing program reduction systems by using a novel reinforcement learning-based approach to accelerate the search for the reduced program and scale to large programs. Our evaluation on a suite of 10 widely used UNIX utility programs each comprising 13-90 KLOC of C source code demonstrates that <PERSON><PERSON> is able to successfully remove all unwanted functionalities and reduce attack surfaces. Compared to two state-of-the-art program reducers C-<PERSON><PERSON> and <PERSON><PERSON>, which time out on 6 programs and 2 programs espectively in 12 hours, <PERSON><PERSON> runs up to 7.1x and 3.7x faster and finishes on all programs.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243838"}, {"primary_key": "3374111", "vector": [], "sparse_vector": [], "title": "Fraud De-Anonymization for Fun and Profit.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The persistence of search rank fraud in online, peer-opinion systems, made possible by crowdsourcing sites and specialized fraud workers, shows that the current approach of detecting and filtering fraud is inefficient. We introduce a fraud de-anonymization approach to disincentivize search rank fraud: attribute user accounts flagged by fraud detection algorithms in online peer-opinion systems, to the human workers in crowdsourcing sites, who control them. We model fraud de-anonymization as a maximum likelihood estimation problem, and introduce UODA, an unconstrained optimization solution. We develop a graph based deep learning approach to predict ownership of account pairs by the same fraudster and use it to build discriminative fraud de-anonymization (DDA) and pseudonymous fraudster discovery algorithms (PFD). To address the lack of ground truth fraud data and its pernicious impacts on online systems that employ fraud detection, we propose the first cheating-resistant fraud de-anonymization validation protocol, that transforms human fraud workers into ground truth, performance evaluation oracles. In a user study with 16 human fraud workers, UODA achieved a precision of 91%. On ground truth data that we collected starting from other 23 fraud workers, our co-ownership predictor significantly outperformed a state-of-the-art competitor, and enabled DDA and PFD to discover tens of new fraud workers, and attribute thousands of suspicious user accounts to existing and newly discovered fraudsters.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243770"}, {"primary_key": "3374112", "vector": [], "sparse_vector": [], "title": "The Multi-user Security of GCM, Revisited: Tight Bounds for Nonce Randomization.", "authors": ["Viet Tung Hoang", "<PERSON>", "Aishwarya Thiruvengadam"], "summary": "Multi-user (mu) security considers large-scale attackers (e.g., state actors) that given access to a number of sessions, attempt to compromise at least one of them. Mu security of authenticated encryption (AE) was explicitly considered in the development of TLS 1.3. This paper revisits the mu security of GCM, which remains to date the most widely used dedicated AE mode. We provide new concrete security bounds which improve upon previous work by adopting a refined parameterization of adversarial resources that highlights the impact on security of (1) nonce re-use across users and of (2) re-keying. As one of the main applications, we give tight security bounds for the nonce-randomization mechanism adopted in the record protocol of TLS 1.3 as a mitigation of large-scale multi-user attacks. We provide tight security bounds that yield the first validation of this method. In particular, we solve the main open question of <PERSON><PERSON> and <PERSON> (CRYPTO '16), who only considered restricted attackers which do not attempt to violate integrity, and only gave non-tight bounds.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243816"}, {"primary_key": "3374114", "vector": [], "sparse_vector": [], "title": "How You Get Shot in the Back: A Systematical Study about Cryptojacking in the Real World.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "As a new mechanism to monetize web content, cryptocurrency mining is becoming increasingly popular. The idea is simple: a webpage delivers extra workload (JavaScript) that consumes computational resources on the client machine to solve cryptographic puzzles, typically without notifying users or having explicit user consent. This new mechanism, often heavily abused and thus considered a threat termed \"cryptojacking\", is estimated to affect over 10 million web users every month; however, only a few anecdotal reports exist so far and little is known about its severeness, infrastructure, and technical characteristics behind the scene. This is likely due to the lack of effective approaches to detect cryptojacking at a large-scale (e.g., VirusTotal). In this paper, we take a first step towards an in-depth study over cryptojacking. By leveraging a set of inherent characteristics of cryptojacking scripts, we build CMTracker, a behavior-based detector with two runtime profilers for automatically tracking Cryptocurrency Mining scripts and their related domains. Surprisingly, our approach successfully discovered 2,770 unique cryptojacking samples from 853,936 popular web pages, including 868 among top 100K in Alexa list. Leveraging these samples, we gain a more comprehensive picture of the cryptojacking attacks, including their impact, distribution mechanisms, obfuscation, and attempts to evade detection. For instance, a diverse set of organizations benefit from cryptojacking based on the unique wallet ids. In addition, to stay under the radar, they frequently update their attack domains (fastflux) on the order of days. Many attackers also apply evasion techniques, including limiting the CPU usage, obfuscating the code, etc.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243840"}, {"primary_key": "3374115", "vector": [], "sparse_vector": [], "title": "Enforcing Unique Code Target Property for Control-Flow Integrity.", "authors": ["Hong Hu", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The goal of control-flow integrity (CFI) is to stop control-hijacking attacks by ensuring that each indirect control-flow transfer (ICT) jumps to its legitimate target. However, existing implementations of CFI have fallen short of this goal because their approaches are inaccurate and as a result, the set of allowable targets for an ICT instruction is too large, making illegal jumps possible. In this paper, we propose the Unique Code Target (UCT) property for CFI. Namely, for each invocation of an ICT instruction, there should be one and only one valid target. We develop a prototype called uCFI to enforce this new property. During compilation, uCFI identifies the sensitive instructions that influence ICT and instruments the program to record necessary execution context. At runtime, uCFI monitors the program execution in a different process, and performs points-to analysis by interpreting sensitive instructions using the recorded execution context in a memory safe manner. It checks runtime ICT targets against the analysis results to detect CFI violations. We apply uCFI to SPEC benchmarks and 2 servers (nginx and vsftpd) to evaluate its efficacy of enforcing UCT and its overhead. We also test uCFI against control-hijacking attacks, including 5 real-world exploits, 1 proof of concept COOP attack, and 2 synthesized attacks that bypass existing defenses. The results show that uCFI strictly enforces the UCT property for protected programs, successfully detects all attacks, and introduces less than 10% performance overhead.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243797"}, {"primary_key": "3374118", "vector": [], "sparse_vector": [], "title": "Audio Hotspot Attack: An Attack on Voice Assistance Systems Using Directional Sound Beams.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose a novel attack named \"Audio Hotspot Attack'', which performs an inaudible malicious voice command attack, targeting voice assistance systems, e.g., smart speakers or in-car navigation systems. This attack leverages directional sound beams generated from parametric loudspeakers, which emit AM-modulated ultrasounds that will be self-demodulated in the air. It can succeed in the attack on a long distance (2--4 meters in a small room and 10+ meter in a long hallway). To evaluate the feasibility of the attack, we performed extensive in-lab experiments and a user study involving 20 participants. The results demonstrate that the attack is feasible in a real-world setting.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278497"}, {"primary_key": "3374119", "vector": [], "sparse_vector": [], "title": "Adversarial Traces for Website Fingerprinting Defense.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Website Fingerprinting (WF) is a traffic analysis attack that enables an eavesdropper to infer the victim's web activity even when encrypted and even when using the Tor anonymity system. Using deep learning classifiers, the attack can reach up to 98% accuracy. Existing WF defenses are either too expensive in terms of bandwidth and latency overheads (e.g. 2-3 times as large or slow) or ineffective against the latest attacks. In this work, we explore a novel defense based on the idea of adversarial examples that have been shown to undermine machine learning classifiers in other domains. Our Adversarial Traces defense adds padding to a Tor traffic trace in a manner that reliably fools the classifier into classifying it as coming from a different site. The technique drops the accuracy of the state-of-the-art attack from 98% to 60%, while incurring a reasonable 47% bandwidth overhead, showing its promise as a possible defense for Tor.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278493"}, {"primary_key": "3374120", "vector": [], "sparse_vector": [], "title": "Ohm&apos;s Law in Data Centers: A Voltage Side Channel for Timing Power Attacks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Maliciously-injected power load, a.k.a. power attack, has recently surfaced as a new egregious attack vector for dangerously compromising the data center availability. This paper focuses on the emerging threat of power attacks in a multi-tenant colocation data center, an important type of data center where multiple tenants house their own servers and share the power distribution system. Concretely, we discover a novel physical side channel --- a voltage side channel --- which leaks the benign tenants' power usage information at runtime and helps an attacker precisely time its power attacks. The key idea we exploit is that, due to the Ohm's Law, the high-frequency switching operation (40~100kHz) of the power factor correction circuit universally built in today's server power supply units creates voltage ripples in the data center power lines. Importantly, without overlapping the grid voltage in the frequency domain, the voltage ripple signals can be easily sensed by the attacker to track the benign tenants' runtime power usage and precisely time its power attacks. We evaluate the timing accuracy of the voltage side channel in a real data center prototype, demonstrating that the attacker can extract benign tenants' power pattern with a great accuracy (correlation coefficient = 0.90+) and utilize 64% of all the attack opportunities without launching attacks randomly or consecutively. Finally, we highlight a few possible defense strategies and extend our study to more complex three-phase power distribution systems used in large multi-tenant data centers.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243744"}, {"primary_key": "3374121", "vector": [], "sparse_vector": [], "title": "Block Oriented Programming: Automating Data-Only Attacks.", "authors": ["Kyriakos K. <PERSON>lou", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "With the widespread deployment of Control-Flow Integrity (CFI), control-flow hijacking attacks, and consequently code reuse attacks, are significantly more difficult. CFI limits control flow to well-known locations, severely restricting arbitrary code execution. Assessing the remaining attack surface of an application under advanced control-flow hijack defenses such as CFI and shadow stacks remains an open problem. We introduce BOPC, a mechanism to automatically assess whether an attacker can execute arbitrary code on a binary hardened with CFI/shadow stack defenses. BOPC computes exploits for a target program from payload specifications written in a Turing-complete, high-level language called SPL that abstracts away architecture and program-specific details. SPL payloads are compiled into a program trace that executes the desired behavior on top of the target binary. The input for BOPC is an SPL payload, a starting point (e.g., from a fuzzer crash) and an arbitrary memory write primitive that allows application state corruption. To map SPL payloads to a program trace, BOPC introduces Block Oriented Programming (BOP), a new code reuse technique that utilizes entire basic blocks as gadgets along valid execution paths in the program, i.e., without violating CFI or shadow stack policies. We find that the problem of mapping payloads to program traces is NP-hard, so BOPC first reduces the search space by pruning infeasible paths and then uses heuristics to guide the search to probable paths. BOPC encodes the BOP payload as a set of memory writes. We execute 13 SPL payloads applied to 10 popular applications. BOPC successfully finds payloads and complex execution traces -- which would likely not have been found through manual analysis -- while following the target's Control-Flow Graph under an ideal CFI policy in 81% of the cases.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243739"}, {"primary_key": "3374123", "vector": [], "sparse_vector": [], "title": "On the Security of the PKCS#1 v1.5 Signature Scheme.", "authors": ["<PERSON><PERSON><PERSON>", "Saqib <PERSON><PERSON>", "<PERSON>"], "summary": "The RSA PKCS#1 v1.5 signature algorithm is the most widely used digital signature scheme in practice. Its two main strengths are its extreme simplicity, which makes it very easy to implement, and that verification of signatures is significantly faster than for DSA or ECDSA. Despite the huge practical importance of RSA PKCS#1 v1.5 signatures, providing formal evidence for their security based on plausible cryptographic hardness assumptions has turned out to be very difficult. Therefore the most recent version of PKCS#1 (RFC 8017) even recommends a replacement the more complex and less efficient scheme RSA-PSS, as it is provably secure and therefore considered more robust. The main obstacle is that RSA PKCS#1 v1.5 signatures use a deterministic padding scheme, which makes standard proof techniques not applicable. We introduce a new technique that enables the first security proof for RSA-PKCS#1 v1.5 signatures. We prove full existential unforgeability against adaptive chosen-message attacks (EUF-CMA) under the standard RSA assumption. Furthermore, we give a tight proof under the Phi-Hiding assumption. These proofs are in the random oracle model and the parameters deviate slightly from the standard use, because we require a larger output length of the hash function. However, we also show how RSA-PKCS#1 v1.5 signatures can be instantiated in practice such that our security proofs apply. In order to draw a more complete picture of the precise security of RSA PKCS#1 v1.5 signatures, we also give security proofs in the standard model, but with respect to weaker attacker models (key-only attacks) and based on known complexity assumptions. The main conclusion of our work is that from a provable security perspective RSA PKCS#1 v1.5 can be safely used, if the output length of the hash function is chosen appropriately.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243798"}, {"primary_key": "3374124", "vector": [], "sparse_vector": [], "title": "Privacy-Preserving Dynamic Learning of Tor Network Traffic.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Experimentation tools facilitate exploration of Tor performance and security research problems and allow researchers to safely and privately conduct Tor experiments without risking harm to real Tor users. However, researchers using these tools configure them to generate network traffic based on simplifying assumptions and outdated measurements and without understanding the efficacy of their configuration choices. In this work, we design a novel technique for dynamically learning Tor network traffic models using hidden Markov modeling and privacy-preserving measurement techniques. We conduct a safe but detailed measurement study of Tor using 17 relays (~2% of Tor bandwidth) over the course of 6 months, measuring general statistics and models that can be used to generate a sequence of streams and packets. We show how our measurement results and traffic models can be used to generate traffic flows in private Tor networks and how our models are more realistic than standard and alternative network traffic generation~methods.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243815"}, {"primary_key": "3374125", "vector": [], "sparse_vector": [], "title": "Towards Understanding the Dynamics of Adversarial Attacks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "An intriguing property of deep neural networks (DNNs) is their inherent vulnerability to adversarial inputs, which significantly hinder the application of DNNs in security-critical domains. Despite the plethora of work on adversarial attacks and defenses, many important questions regarding the inference behaviors of adversarial inputs remain mysterious. This work represents a solid step towards answering those questions by investigating the information flows of normal and adversarial inputs within various DNN models and conducting in-depth comparative analysis of their discriminative patterns. Our work points to several promising directions for designing more effective defense mechanisms.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278528"}, {"primary_key": "3374126", "vector": [], "sparse_vector": [], "title": "Model-Reuse Attacks on Deep Learning Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shouling Ji", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Many of today's machine learning (ML) systems are built by reusing an array of, often pre-trained, primitive models, each fulfilling distinct functionality (e.g., feature extraction). The increasing use of primitive models significantly simplifies and expedites the development cycles of ML systems. Yet, because most of such models are contributed and maintained by untrusted sources, their lack of standardization or regulation entails profound security implications, about which little is known thus far. In this paper, we demonstrate that malicious primitive models pose immense threats to the security of ML systems. We present a broad class of model-reuse attacks wherein maliciously crafted models trigger host ML systems to misbehave on targeted inputs in a highly predictable manner. By empirically studying four deep learning systems (including both individual and ensemble systems) used in skin cancer screening, speech recognition, face verification, and autonomous steering, we show that such attacks are (i) effective - the host systems misbehave on the targeted inputs as desired by the adversary with high probability, (ii) evasive - the malicious models function indistinguishably from their benign counterparts on non-targeted inputs, (iii) elastic - the malicious models remain effective regardless of various system design choices and tuning strategies, and (iv) easy - the adversary needs little prior knowledge about the data used for system tuning or inference. We provide analytical justification for the effectiveness of model-reuse attacks, which points to the unprecedented complexity of today's primitive models. This issue thus seems fundamental to many ML systems. We further discuss potential countermeasures and their challenges, which lead to several promising research directions.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243757"}, {"primary_key": "3374127", "vector": [], "sparse_vector": [], "title": "Secure Outsourced Matrix Computation and Application to Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Homomorphic Encryption (HE) is a powerful cryptographic primitive to address privacy and security issues in outsourcing computation on sensitive data to an untrusted computation environment. Comparing to secure Multi-Party Computation (MPC), HE has advantages in supporting non-interactive operations and saving on communication costs. However, it has not come up with an optimal solution for modern learning frameworks, partially due to a lack of efficient matrix computation mechanisms. In this work, we present a practical solution to encrypt a matrix homomorphically and perform arithmetic operations on encrypted matrices. Our solution includes a novel matrix encoding method and an efficient evaluation strategy for basic matrix operations such as addition, multiplication, and transposition. We also explain how to encrypt more than one matrix in a single ciphertext, yielding better amortized performance. Our solution is generic in the sense that it can be applied to most of the existing HE schemes. It also achieves reasonable performance for practical use; for example, our implementation takes 9.21 seconds to multiply two encrypted square matrices of order 64 and 2.56 seconds to transpose a square matrix of order 64. Our secure matrix computation mechanism has a wide applicability to our new framework E2DM, which stands for encrypted data and encrypted model. To the best of our knowledge, this is the first work that supports secure evaluation of the prediction phase based on both encrypted data and encrypted model, whereas previous work only supported applying a plain model to encrypted data. As a benchmark, we report an experimental result to classify handwritten images using convolutional neural networks (CNN). Our implementation on the MNIST dataset takes 28.59 seconds to compute ten likelihoods of 64 input images simultaneously, yielding an amortized rate of 0.45 seconds per image.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243837"}, {"primary_key": "3374129", "vector": [], "sparse_vector": [], "title": "17th Workshop on Privacy in the Electronic Society (WPES 2018).", "authors": ["<PERSON>", "<PERSON>"], "summary": "The 17th Workshop on Privacy in the Electronic Society (WPES 2018) was held on 15 October, 2018, in conjunction with the 25th ACM Conference on Computer and Communications Security (CCS 2018) in Toronto, Canada. The goal of WPES is to bring together privacy researchers and practitioners to discuss the privacy problems that arise in an interconnected society and solutions to those problems. The program for the workshop contains 11 full papers and 8 short papers selected from a total of 52 submissions. Specific topics covered in the program include but are not limited to: communication privacy, data anonymization, privacy engineering, secure computation, and Web privacy.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243883"}, {"primary_key": "3374130", "vector": [], "sparse_vector": [], "title": "Reinforcing System-Assigned Passphrases Through Implicit Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "People tend to choose short and predictable passwords that are vulnerable to guessing attacks. Passphrases are passwords consisting of multiple words, initially introduced as more secure authentication keys that people could recall. Unfortunately, people tend to choose predictable natural language patterns in passphrases, again resulting in vulnerability to guessing attacks. One solution could be system-assigned passphrases, but people have difficulty recalling them. With the goal of improving the usability of system-assigned passphrases, we propose a new approach of reinforcing system-assigned passphrases using implicit learning techniques. We design and test a system that implements this approach using two implicit learning techniques: contextual cueing and semantic priming. In a 780-participant online study, we explored the usability of 4-word system-assigned passphrases using our system compared to a set of control conditions. Our study showed that our system significantly improves usability of system-assigned passphrases, both in terms of recall rates and login time.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243764"}, {"primary_key": "3374135", "vector": [], "sparse_vector": [], "title": "Circular AON: A Very Fast Scheme to Protect Encrypted Data Against Key Exposure.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this poster, we introduce CAON: a novel variation of an all-ornothing transform that aims at protecting encrypted data against exposure of cryptographic material.We improve the fastest relevant scheme by reducing the number of exclusive-or operations made in addition to encryption by almost a half. We believe that CAON can be easily integrated inside modern distributed storage systems or multi-cloud data solutions in order to reinforce confidentiality level of the stored data at the cost of a very small performance overhead.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278501"}, {"primary_key": "3374138", "vector": [], "sparse_vector": [], "title": "CipherLocker: Encrypted File Sharing with Ranked Search https: //cipherlocker.com.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Today's (predominantly cloud-based) File sharing products leave users at the mercy of providers and nation-state adversaries with subpoena and National Security Letter (NSL) powers. In-transit and provider-side at-rest encryption do little to handle this.Almost-weekly breaches [7-13, 17] and NSL revelations [2] show that the problem becomes only worse with increasingly privacy-unfriendly regulation [14]. We believe it is important to provide hype-free, easy-to-use strongly-secure solutions that protect individual privacy while also defeating cloud breaches and compromises. CipherLocker provides practical, easy-to-use, client-side encrypted File sharing with integrated ranked search. All data and metadata is strongly encrypted before leaving the client. Users can securely store, share, sync, and search. The design does not allow even a compromised or compelled cloud provider to ever access user data or search queries. CipherLocker shows that highly-scalable, fast ranked search on encrypted data is possible without the deployment of expensive and often insecure server-side search-on-encrypted-data cryptography which would require 3-5 orders of magnitude more resources and cannot scale to even thousands of users, or the simplest sharing scenarios without breaking security. CipherLocker is the result of several years of work and it cannot be exhaustively detailed and analyzed in this space. This is the first of a series of papers discussing CipherLocker design, implementation and security properties. The main goal here is to briefly overview and introduce key design decisions and behaviors.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278500"}, {"primary_key": "3374139", "vector": [], "sparse_vector": [], "title": "SysTEX&apos;18: 2018 Workshop on System Software for Trusted Execution.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The rise of new processor hardware extensions that permit fine-grained and flexible trusted execution, such as Intel's SGX, ARM's TrustZone, or AMD's SEV, introduces numerous novel challenges and opportunities for developers of secure applications. There is a burning need for cross-cutting systems support of such Trusted Execution Environments (TEEs) that spans all the layers of the software stack, from the OS through runtime to compilers and programming models. The 3rd Workshop on System Software for Trusted Execution (SysTEX) will focus on systems research challenges related to TEEs, and explore new ideas and strategies for the implementation of trustworthy systems with TEEs. The workshop is also open to papers exploring attacks on current TEEs and strategies for mitigating such attacks.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243880"}, {"primary_key": "3374140", "vector": [], "sparse_vector": [], "title": "Improved Non-Interactive Zero Knowledge with Applications to Post-Quantum Signatures.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent work, including ZKBoo, ZKB++, and Ligero, has developed efficient non-interactive zero-knowledge proofs of knowledge (NIZKPoKs) for Boolean circuits based on symmetric-key primitives alone, using the \"MPC-in-the-head\" paradigm of <PERSON><PERSON> et al. We show how to instantiate this paradigm with MPC protocols in the preprocessing model; once optimized, this results in an NIZKPoK with shorter proofs (and comparable computation) as in prior work for circuits containing roughly 300--100,000 AND~gates. In contrast to prior work, our NIZKPoK also supports witness-independent preprocessing, which allows the prover to shift most of its work to an offline phase before the witness is known. We use our NIZKPoK to construct a signature scheme based only on symmetric-key primitives (and hence with \"post-quantum\" security). The resulting scheme has shorter signatures than the scheme built using ZKB++ (and comparable signing/verification time), and is even competitive with hash-based signature schemes. To further highlight the flexibility and power of our NIZKPoK, we also use it to build efficient ring and group signatures based on symmetric-key primitives alone. To our knowledge, the resulting schemes are the most efficient constructions of these primitives that offer post-quantum security.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243805"}, {"primary_key": "3374141", "vector": [], "sparse_vector": [], "title": "A Better Method to Analyze Blockchain Consistency.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The celebrated Nakamoto consensus protocol [16] ushered in several new consensus applications including cryptocurrencies. A few recent works [7, 17] have analyzed important properties of blockchains, including most significantly, consistency, which is a guarantee that all honest parties output the same sequence of blocks throughout the execution of the protocol. To establish consistency, the prior analysis of <PERSON>, <PERSON><PERSON> and <PERSON><PERSON> [17] required a careful counting of certain combinatorial events that was difficult to apply to variations of <PERSON><PERSON><PERSON>. The work of <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> [7] provides another method of analyzing the blockchain under the simplifying assumption that the network was synchronous. The contribution of this paper is the development of a simple Markov-chain based method for analyzing consistency properties of blockchain protocols. The method includes a formal way of stating strong concentration bounds as well as easy ways to concretely compute the bounds. We use our new method to answer a number of basic questions about consistency of blockchains: Our new analysis provides a tighter guarantee on the consistency property of <PERSON><PERSON><PERSON>'s protocol, including for parameter regimes which [17] could not consider; We analyze a family of delaying attacks first presented in [17], and extend them to other protocols; We analyze how long a participant should wait before considering a high-value transaction \"confirmed\"; We analyze the consistency of <PERSON><PERSON><PERSON>hai<PERSON>, a variation of the Chainweb [14] system; We provide the first rigorous consistency analysis of GHOST [20] and also analyze a folklore \"balancing\"-attack. In each case, we use our framework to experimentally analyze the consensus bounds for various network delay parameters and adversarial computing percentages. We hope our techniques enable authors of future blockchain proposals to provide a more rigorous analysis of their schemes.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243814"}, {"primary_key": "3374145", "vector": [], "sparse_vector": [], "title": "Evaluating Fuzz Testing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Fuzz testing has enjoyed great success at discovering security critical bugs in real software. Recently, researchers have devoted significant effort to devising new fuzzing techniques, strategies, and algorithms. Such new ideas are primarily evaluated experimentally so an important question is: What experimental setup is needed to produce trustworthy results? We surveyed the recent research literature and assessed the experimental evaluations carried out by 32 fuzzing papers. We found problems in every evaluation we considered. We then performed our own extensive experimental evaluation using an existing fuzzer. Our results showed that the general problems we found in existing experimental evaluations can indeed translate to actual wrong or misleading assessments. We conclude with some guidelines that we hope will help improve experimental evaluations of fuzz testing algorithms, making reported results more robust.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243804"}, {"primary_key": "3374146", "vector": [], "sparse_vector": [], "title": "Scission: Signal Characteristic-Based Sender Identification and Intrusion Detection in Automotive Networks.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Increased connectivity increases the attack vector. This also applies to connected vehicles in which vulnerabilities not only threaten digital values but also humans and the environment. Typically, attackers try to exploit the Controller Area Network (CAN) bus, which is the most widely used standard for internal vehicle communication. Once an Electronic Control Unit (ECU) connected to the CAN bus is compromised, attackers can manipulate messages at will. The missing sender authentication by design of the CAN bus enables adversarial access to vehicle functions with severe consequences. In order to address this problem, we propose Scission, an Intrusion Detection System (IDS) which uses fingerprints extracted from CAN frames, enabling the identification of sending ECUs. Scission utilizes physical characteristics from analog values of CAN frames to assess whether it was sent by the legitimate ECU. In addition, to detect comprised ECUs, the proposed system is able to recognize attacks from unmonitored and additional devices. We show that <PERSON>ission is able to identify the sender with an average probability of 99.85%, during the evaluation on two series production cars and a prototype setup. Due to the robust design of the system, the evaluation shows that all false positives were prevented. Compared to previous approaches, we have significantly reduced hardware costs and increased identification rates, which enables a broad application of this technology.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243751"}, {"primary_key": "3374148", "vector": [], "sparse_vector": [], "title": "An Exploratory Analysis of Microcode as a Building Block for System Defenses.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Microcode is an abstraction layer used by modern x86 processors that interprets user-visible CISC instructions to hardware-internal RISC instructions. The capability to update x86 microcode enables a vendor to modify CPU behavior in-field, and thus patch erroneous microarchitectural processes or even implement new features. Most prominently, the recent <PERSON>pectre and Meltdown vulnerabilities were mitigated by Intel via microcode updates. Unfortunately, microcode is proprietary and closed source, and there is little publicly available information on its inner workings. In this paper, we present new reverse engineering results that extend and complement the public knowledge of proprietary microcode. Based on these novel insights, we show how modern system defenses and tools can be realized in microcode on a commercial, off-the-shelf AMD x86 CPU. We demonstrate how well-established system security defenses such as timing attack mitigations, hardware-assisted address sanitization, and instruction set randomization can be realized in microcode. We also present a proof-of-concept implementation of a microcode-assisted instrumentation framework. Finally, we show how a secure microcode update mechanism and enclave functionality can be implemented in microcode to realize a small trusted execution environment. All microcode programs and the whole infrastructure needed to reproduce and extend our results are publicly available.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243861"}, {"primary_key": "3374149", "vector": [], "sparse_vector": [], "title": "MineSweeper: An In-depth Look into Drive-by Cryptocurrency Mining and Its Defense.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A wave of alternative coins that can be effectively mined without specialized hardware, and a surge in cryptocurrencies' market value has led to the development of cryptocurrency mining ( cryptomining ) services, such as Coinhive, which can be easily integrated into websites to monetize the computational power of their visitors. While legitimate website operators are exploring these services as an alternative to advertisements, they have also drawn the attention of cybercriminals: drive-by mining (also known as cryptojacking ) is a new web-based attack, in which an infected website secretly executes JavaScript code and/or a WebAssembly module in the user's browser to mine cryptocurrencies without her consent. In this paper, we perform a comprehensive analysis on Alexa's Top 1 Million websites to shed light on the prevalence and profitability of this attack. We study the websites affected by drive-by mining to understand the techniques being used to evade detection, and the latest web technologies being exploited to efficiently mine cryptocurrency. As a result of our study, which covers 28 Coinhive-like services that are widely being used by drive-by mining websites, we identified 20 active cryptomining campaigns. Motivated by our findings, we investigate possible countermeasures against this type of attack. We discuss how current blacklisting approaches and heuristics based on CPU usage are insufficient, and present MineSweeper, a novel detection technique that is based on the intrinsic characteristics of cryptomining code, and, thus, is resilient to obfuscation. Our approach could be integrated into browsers to warn users about silent cryptomining when visiting websites that do not ask for their consent.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243858"}, {"primary_key": "3374152", "vector": [], "sparse_vector": [], "title": "A Guided Approach to Behavioral Authentication.", "authors": ["<PERSON>eu<PERSON> Ku", "<PERSON>", "<PERSON><PERSON><PERSON>", "Taekyoung Kwon"], "summary": "User's behavioral biometrics are promising as authentication factors in particular if accuracy is sufficiently guaranteed. They can be used to augment security in combination with other authentication factors. A gesture-based pattern lock system is a good example of such multi-factor authentication, using touch dynamics in a smartphone. However, touch dynamics can be significantly affected by a shape of gestures with regard to the performance and accuracy, and our concern is that user-chosen patterns are likely far from producing such a good shape of gestures. In this poster, we raise this problem and show our experimental study conducted in this regard. We investigate if there is a reproducible correlation between shape and accuracy and if we can derive effective attribute values for user guidance, based on the gesture-based pattern lock system. In more general, we discuss a guided approach to behavioral authentication.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278488"}, {"primary_key": "3374156", "vector": [], "sparse_vector": [], "title": "Result Pattern Hiding Searchable Encryption for Conjunctive Queries.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Shifeng Sun", "Dong<PERSON> Liu", "<PERSON><PERSON>"], "summary": "The recently proposed Oblivious Cross-Tags (OXT) protocol (CRYPTO 2013) has broken new ground in designing efficient searchable symmetric encryption (SSE) protocol with support for conjunctive keyword search in a single-writer single-reader framework. While the OXT protocol offers high performance by adopting a number of specialised data-structures, it also trades-off security by leaking 'partial' database information to the server. Recent attacks have exploited similar partial information leakage to breach database confidentiality. Consequently, it is an open problem to design SSE protocols that plug such leakages while retaining similar efficiency. In this paper, we propose a new SSE protocol, called Hidden Cross-Tags (HXT), that removes 'Keyword Pair Result Pattern' (KPRP) leakage for conjunctive keyword search. We avoid this leakage by adopting two additional cryptographic primitives - Hidden Vector Encryption (HVE) and probabilistic (Bloom filter) indexing into the HXT protocol. We propose a 'lightweight' HVE scheme that only uses efficient symmetric-key building blocks, and entirely avoids elliptic curve-based operations. At the same time, it affords selective simulation-security against an unbounded number of secret-key queries. Adopting this efficient HVE scheme, the overall practical storage and computational overheads of HXT over OXT are relatively small (no more than 10% for two keywords query, and 21% for six keywords query), while providing a higher level of security.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243753"}, {"primary_key": "3374161", "vector": [], "sparse_vector": [], "title": "Pride and Prejudice in Progressive Web Apps: Abusing Native App-like Features in Web Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Junghwan Park", "Insik Shin", "<PERSON><PERSON>"], "summary": "Progressive Web App (PWA) is a new generation of Web application designed to provide native app-like browsing experiences even when a browser is offline. PWAs make full use of new HTML5 features which include push notification, cache, and service worker to provide short-latency and rich Web browsing experiences. We conduct the first systematic study of the security and privacy aspects unique to PWAs. We identify security flaws in main browsers as well as design flaws in popular third-party push services, that exacerbate the phishing risk. We introduce a new side-channel attack that infers the victim's history of visited PWAs. The proposed attack exploits the offline browsing feature of PWAs using a cache. We demonstrate a cryptocurrency mining attack which abuses service workers. Defenses and recommendations to mitigate the identified security and privacy risks are suggested with in-depth understanding.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243867"}, {"primary_key": "3374163", "vector": [], "sparse_vector": [], "title": "Lord of the x86 Rings: A Portable User Mode Privilege Separation Architecture on x86.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Modern applications often involve processing of sensitive information. However, the lack of privilege separation within the user space leaves sensitive application secret such as cryptographic keys just as unprotected as a \"hello world\" string. Cutting-edge hardware-supported security features are being introduced. However, the features are often vendor-specific or lack compatibility with older generations of the processors. The situation leaves developers with no portable solution to incorporate protection for the sensitive application component. We propose LOTRx86, a fundamental and portable approach for user-space privilege separation. Our approach creates a more privileged user execution layer called PrivUser by harnessing the underused intermediate privilege levels on the x86 architecture. The PrivUser memory space, a set of pages within process address space that are inaccessible to user mode, is a safe place for application secrets and routines that access them. We implement the LOTRx86 ABI that exports the privcall interface to users to invoke secret handling routines in Priv<PERSON>ser. This way, sensitive application operations that involve the secrets are performed in a strictly controlled manner. The memory access control in our architecture is privilege-based, accessing the protected application secret only requires a change in the privilege, eliminating the need for costly remote procedure calls or change in address space. We evaluated our platform by developing a proof-of-concept LOTRx86-enabled web server that employs our architecture to securely access its private key during an SSL connection. We conducted a set of experiments including a performance measurement on the PoC on both Intel and AMD PCs, and confirmed that LOTRx86 incurs only a limited performance overhead.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243748"}, {"primary_key": "3374165", "vector": [], "sparse_vector": [], "title": "Measuring Information Leakage in Website Fingerprinting Attacks and Defenses.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Tor provides low-latency anonymous and uncensored network access against a local or network adversary. Due to the design choice to minimize traffic overhead (and increase the pool of potential users) Tor allows some information about the client's connections to leak. Attacks using (features extracted from) this information to infer the website a user visits are called Website Fingerprinting (WF) attacks. We develop a methodology and tools to measure the amount of leaked information about a website. We apply this tool to a comprehensive set of features extracted from a large set of websites and WF defense mechanisms, allowing us to make more fine-grained observations about WF attacks and defenses.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243832"}, {"primary_key": "3374166", "vector": [], "sparse_vector": [], "title": "vNIDS: Towards Elastic Security with Safe and Efficient Virtualization of Network Intrusion Detection Systems.", "authors": ["Hongda Li", "Hong<PERSON> Hu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Traditional Network Intrusion Detection Systems (NIDSes) are generally implemented on vendor proprietary appliances or middleboxes with poor versatility and flexibility. Emerging Network Function Virtualization (NFV) and Software-Defined Networking (SDN) technologies can virtualize NIDSes and elastically scale them to deal with attack traffic variations. However, such an elasticity feature must not come at the cost of decreased detection effectiveness and expensive provisioning. In this paper, we propose an innovative NIDS architecture, vNIDS, to enable safe and efficient virtualization of NIDSes. vNIDS addresses two key challenges with respect to effective intrusion detection and non-monolithic NIDS provisioning in virtualizing NIDSes. The former challenge is addressed by detection state sharing while minimizing the sharing overhead in virtualized environments. In particular, static program analysis is employed to determine which detection states need to be shared. vNIDS addresses the latter challenge by provisioning virtual NIDSes as microservices and employing program slicing to partition the detection logic programs so that they can be executed by each microservice separately. We implement a prototype of vNIDS to demonstrate the feasibility of our approach. Our evaluation results show that vNIDS could offer both effective intrusion detection and efficient provisioning for NIDS virtualization.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243862"}, {"primary_key": "3374169", "vector": [], "sparse_vector": [], "title": "K-Hunt: Pinpointing Insecure Cryptographic Keys from Execution Traces.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The only secrets in modern cryptography (crypto for short) are the crypto keys. Understanding how crypto keys are used in a program and discovering insecure keys is paramount for crypto security. This paper presents K-Hunt, a system for identifying insecure keys in binary executables. K-Hunt leverages the properties of crypto operations for identifying the memory buffers where crypto keys are stored. And, it tracks their origin and propagation to identify insecure keys such as deterministically generated keys, insecurely negotiated keys, and recoverable keys. K-Hunt does not use signatures to identify crypto operations, and thus can be used to identify insecure keys in unknown crypto algorithms and proprietary crypto implementations. We have implemented K-Hunt and evaluated it with 10 cryptographic libraries and 15 applications that contain crypto operations. Our evaluation results demonstrate that K-<PERSON> locates the keys in symmetric ciphers, asymmetric ciphers, stream ciphers, and digital signatures, regardless if those algorithms are standard or proprietary. More importantly, K-Hunt discovers insecure keys in 22 out of 25 evaluated programs including well-developed crypto libraries such as Libsodium, Nettle, TomCrypt, and WolfSSL.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243783"}, {"primary_key": "3374170", "vector": [], "sparse_vector": [], "title": "PrinTracker: Fingerprinting 3D Printers using Commodity Scanners.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As 3D printing technology begins to outpace traditional manufacturing, malicious users increasingly have sought to leverage this widely accessible platform to produce unlawful tools for criminal activities. Therefore, it is of paramount importance to identify the origin of unlawful 3D printed products using digital forensics. Traditional countermeasures, including information embedding or watermarking, rely on supervised manufacturing process and are impractical for identifying the origin of 3D printed tools in criminal applications. We argue that 3D printers possess unique fingerprints, which arise from hardware imperfections during the manufacturing process, causing discrepancies in the line formation of printed physical objects. These variations appear repeatedly and result in unique textures that can serve as a viable fingerprint on associated 3D printed products. To address the challenge of traditional forensics in identifying unlawful 3D printed products, we present PrinTracker, the 3D printer identification system, which can precisely trace the physical object to its source 3D printer based on their fingerprint. Results indicate that PrinTracker provides a high accuracy using 14 different 3D printers. Under unfavorable conditions (e.g. restricted sample area, location and process), the PrinTracker can still achieve an acceptable accuracy of 92%. Furthermore, we examine the effectiveness, robustness, reliability and vulnerabilities of the PrinTracker in multiple real-world scenarios.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243735"}, {"primary_key": "3374173", "vector": [], "sparse_vector": [], "title": "Fast Secure Multiparty ECDSA with Practical Distributed Key Generation and Applications to Cryptocurrency Custody.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "ECDSA is a standardized signing algorithm that is widely used in TLS, code signing, cryptocurrency and more. Due to its importance, the problem of securely computing ECDSA in a distributed manner (known as threshold signing) has received considerable interest. However, despite this interest, there is still no full threshold solution for more than 2 parties (meaning that any t -out-of- n parties can sign, security is preserved for any t-1 or fewer corrupted parties, and tłeq n can be any value thus supporting an honest minority) that has practical key distribution. This is due to the fact that all previous solutions for this utilize Paillier homomorphic encryption, and efficient distributed Paillier key generation for more than two parties is not known. In this paper, we present the first truly practical full threshold ECDSA signing protocol that has both fast signing and fast key distribution. This solves a years-old open problem, and opens the door to practical uses of threshold ECDSA signing that are in demand today. One of these applications is the construction of secure cryptocurrency wallets (where key shares are spread over multiple devices and so are hard to steal) and cryptocurrency custody solutions (where large sums of invested cryptocurrency are strongly protected by splitting the key between a bank/financial institution, the customer who owns the currency, and possibly a third-party trustee, in multiple shares at each). There is growing practical interest in such solutions, but prior to our work these could not be deployed today due to the need for distributed key generation.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243788"}, {"primary_key": "3374177", "vector": [], "sparse_vector": [], "title": "A Robust and Efficient Defense against Use-after-Free Exploits via Concurrent Pointer Sweeping.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Haining <PERSON>"], "summary": "Applications in C/C++ are notoriously prone to memory corruptions. With significant research efforts devoted to this area of study, the security threats posed by previously popular vulnerabilities, such as stack and heap overflows, are not as serious as before. Instead, we have seen the meteoric rise of attacks exploiting use-after-free (UaF) vulnerabilities in recent years, which root in pointers pointing to freed memory (i.e., dangling pointers). Although various approaches have been proposed to harden software against UaF, none of them can achieve robustness and efficiency at the same time. In this paper, we present a novel defense called pSweeper to robustly protect against UaF exploits with low overhead, and pinpoint the root-causes of UaF vulnerabilities with one safe crash. The success of pSweeper lies in its two unique and innovative design ideas, concurrent pointer sweeping (CPW) and object origin tracking (OOT). CPW exploits the increasingly available multi-cores on modern PCs and outsources the heavyweight security checks and enforcement to dedicated threads that can run on spare cores. Specifically, CPW iteratively sweeps all live pointers in a concurrent thread to find dangling pointers. This design is quite different from previous work that requires to track every pointer propagation to maintain accurate point-to relationship between pointers and objects. OOT can help to pinpoint the root-causes of UaF by informing developers of how a dangling pointer is created, i.e., how the problematic object is allocated and freed. We implement a prototype of pSweeper and validate its efficacy in real scenarios. Our experimental results show that pSweeper is effective in defeating real-world UaF exploits and efficient when deployed in production runs.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243826"}, {"primary_key": "3374179", "vector": [], "sparse_vector": [], "title": "Cryptographically Secure Detection of Injection Attacks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Direct Memory Access (DMA) attacks can allow attackers to access memory directly, bypassing OS supervision or software protections. In this work, we put forth and benchmark a cryptographically secure attestation scheme, which detects DMA attacks. In fact, our scheme detects any attack in a more general class of attacks which we call \"direct injection\". We prove security of our scheme under a realistic machine model which extends in a non-trivial manner a cryptographic model proposed by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> (ICALP 2016.) Despite the fact that our scheme, in its current form, protects against write-only attacks, both our security model and our scheme can be extended to allow the attacker to have additional read access to memory---thereby capturing leakage---as well as detecting more types of memory corruptions such as bit flips.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278503"}, {"primary_key": "3374183", "vector": [], "sparse_vector": [], "title": "ret2spec: Speculative Execution Using Return Stack Buffers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Speculative execution is an optimization technique that has been part of CPUs for over a decade. It predicts the outcome and target of branch instructions to avoid stalling the execution pipeline. However, until recently, the security implications of speculative code execution have not been studied. In this paper, we investigate a special type of branch predictor that is responsible for predicting return addresses. To the best of our knowledge, we are the first to study return address predictors and their consequences for the security of modern software. In our work, we show how return stack buffers (RSBs), the core unit of return address predictors, can be used to trigger misspeculations. Based on this knowledge, we propose two new attack variants using RSBs that give attackers similar capabilities as the documented Spectre attacks. We show how local attackers can gain arbitrary speculative code execution across processes, e.g., to leak passwords another user enters on a shared system. Our evaluation showed that the recent Spectre countermeasures deployed in operating systems can also cover such RSB-based cross-process attacks. Yet we then demonstrate that attackers can trigger misspeculation in JIT environments in order to leak arbitrary memory content of browser processes. Reading outside the sandboxed memory region with JIT-compiled code is still possible with 80\\% accuracy on average.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243761"}, {"primary_key": "3374190", "vector": [], "sparse_vector": [], "title": "Can Browser Add-Ons Protect Your Children from Online Tracking?", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Online tracking of children by third-parties is strictly regulated by law in many regions of the world (e.g., COPPA in USA and GDPR in EU), and in a large number of situations constitutes criminal activity. Unfortunately, the existence of these laws does not seem to be an effective deterrence. In this paper, we provide a brief summary of our findings pertaining to the effectiveness of four popular browser add-ons in protecting against third-party tracking on a select number of children-oriented Web-sites. The obtain results show that protection from tracking by a browser add-on is generally achieved at the expense of Web-page performance. In other words, add-ons that are effective at blocking third-party trackers will often adversely affect the normal functioning of the visited Web-page(s). In addition, our results also show that when it comes to user/children tracking by well-known 'tech giants', all four add-ons are likely to provide only limited protection.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278498"}, {"primary_key": "3374191", "vector": [], "sparse_vector": [], "title": "Secure Computation with Differentially Private Access Patterns.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We explore a new security model for secure computation on large datasets. We assume that two servers have been employed to compute on private data that was collected from many users, and, in order to improve the efficiency of their computation, we establish a new tradeoff with privacy. Specifically, instead of claiming that the servers learn nothing about the input values, we claim that what they do learn from the computation preserves the differential privacy of the input. Leveraging this relaxation of the security model allows us to build a protocol that leaks some information in the form of access patterns to memory, while also providing a formal bound on what is learned from the leakage. We then demonstrate that this leakage is useful in a broad class of computations. We show that computations such as histograms, PageRank and matrix factorization, which can be performed in common graph-parallel frameworks such as MapReduce or Pregel, benefit from our relaxation. We implement a protocol for securely executing graph-parallel computations, and evaluate the performance on the three examples just mentioned above. We demonstrate marked improvement over prior implementations for these computations.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243851"}, {"primary_key": "3374192", "vector": [], "sparse_vector": [], "title": "Spartan Networks: Self-Feature-Squeezing Networks for Increased Robustness in Adversarial Settings.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Deep Learning Models are vulnerable to adversarial inputs, samples modified in order to maximize error of the system. We hereby introduce Spartan Networks, Deep Learning models that are inherently more resistant to adverarial examples, without doing any input preprocessing out of the network or adversarial training. These networks have an adversarial layer within the network designed to starve the network of information, using a new activation function to discard data. This layer trains the neural network to filter-out usually-irrelevant parts of its input. These models thus have a slightly lower precision, but report a higher robustness under attack than unprotected models.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278486"}, {"primary_key": "3374193", "vector": [], "sparse_vector": [], "title": "SecArch&apos;18: 1st Workshop of Security-Oriented Designs of Computer Architectures and Processors.", "authors": ["<PERSON>"], "summary": "The theme of this workshop is security-oriented computer systems designs and optimizations. And through this workshop, we hope to explore more secure computer systems for PCs, Servers, IoT nodes, and more.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243878"}, {"primary_key": "3374194", "vector": [], "sparse_vector": [], "title": "Aggregated Machine Learning on Indicators of Compromise in Android Devices.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Stefanie S. F. Chang"], "summary": "Malware mitigation for mobile technology is a long-standing problem for which there is not yet a good solution. In this paper, we focus on identifying malicious applications, and verifying the absence of malicious or vulnerable code in applications that agencies seek to utilize. Our analysis toolbox includes static analysis and permissions risk scoring as pre-installation vetting techniques designed to prevent malware from being installed on devices on an enterprise network. However, dynamic code-loading techniques and changing security requirements mean that applications which previously passed the static analysis verification process, and have been installed on devices, may no longer meet security standards, and may be malicious. To identify these apps, and prevent their future malfeasance, we propose a crowd-sourced behavioral analysis (CSBA) technique, using machine learning to identify anomalous activity by examining patterns in power consumption, network behavior, and sequences of system calls. These techniques apply effectively to a single user's device over time, as well as to individual devices within an enterprise network.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278494"}, {"primary_key": "3374195", "vector": [], "sparse_vector": [], "title": "Preserving Both Privacy and Utility in Network Trace Anonymization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Yuan Hong", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As network security monitoring grows more sophisticated, there is an increasing need for outsourcing such tasks to third-party analysts. However, organizations are usually reluctant to share their network traces due to privacy concerns over sensitive information, e.g., network and system configuration, which may potentially be exploited for attacks. In cases where data owners are convinced to share their network traces, the data are typically subjected to certain anonymization techniques, e.g., CryptoPAn, which replaces real IP addresses with prefix-preserving pseudonyms. However, most such techniques either are vulnerable to adversaries with prior knowledge about some network flows in the traces, or require heavy data sanitization or perturbation, both of which may result in a significant loss of data utility. In this paper, we aim to preserve both privacy and utility through shifting the trade-off from between privacy and utility to between privacy and computational cost. The key idea is for the analysts to generate and analyze multiple anonymized views of the original network traces; those views are designed to be sufficiently indistinguishable even to adversaries armed with prior knowledge, which preserves the privacy, whereas one of the views will yield true analysis results privately retrieved by the data owner, which preserves the utility. We present the general approach and instantiate it based on CryptoPAn. We formally analyze the privacy of our solution and experimentally evaluate it using real network traces provided by a major ISP. The results show that our approach can significantly reduce the level of information leakage (e.g., less than 1\\% of the information leaked by CryptoPAn) with comparable utility.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243809"}, {"primary_key": "3374196", "vector": [], "sparse_vector": [], "title": "ABY3: A Mixed Protocol Framework for Machine Learning.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Machine learning is widely used to produce models for a range of applications and is increasingly offered as a service by major technology companies. However, the required massive data collection raises privacy concerns during both training and prediction stages. In this paper, we design and implement a general framework for privacy-preserving machine learning and use it to obtain new solutions for training linear regression, logistic regression and neural network models. Our protocols are in a three-server model wherein data owners secret share their data among three servers who train and evaluate models on the joint data using three-party computation (3PC). Our main contribution is a new and complete framework ($\\textABY ^3$) for efficiently switching back and forth between arithmetic, binary, and Yao 3PC which is of independent interest. Many of the conversions are based on new techniques that are designed and optimized for the first time in this paper. We also propose new techniques for fixed-point multiplication of shared decimal values that extends beyond the three-party case, and customized protocols for evaluating piecewise polynomial functions. We design variants of each building block that is secure against \\em malicious adversaries who deviate arbitrarily. We implement our system in C++. Our protocols are up to \\em four orders of magnitude faster than the best prior work, hence significantly reducing the gap between privacy-preserving and plaintext training.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243760"}, {"primary_key": "3374201", "vector": [], "sparse_vector": [], "title": "Knowledge Seeking on The Shadow Brokers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Seungwon Shin"], "summary": "The Shadow Brokers (TSB) are an infamous group of hackers responsible for major cybercrime incidents. There are currently few studies on TSB, and to prevent their attacks in the future, we believe it is necessary to study them beforehand. This study constructs a relation graph of all the entities concerning TSB using identifiers in the Web. We introduce a systematic approach to finding relations among entities using a case study based on identifiers and clearness of relations. Our investigation covers data from both the Surface Web and Dark Web, with our Dark Web data consisting of over 40 million Dark Web webpages. We have uncovered many hacking forums, hacking groups, and individuals having a relation with TSB using our method. The relation graph of TSB will become a stepping stone in developing a knowledge base of TSB.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278512"}, {"primary_key": "3374202", "vector": [], "sparse_vector": [], "title": "Rendered Insecure: GPU Side Channel Attacks are Practical.", "authors": ["Hoda Naghibijouybari", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Graphics Processing Units (GPUs) are commonly integrated with computing devices to enhance the performance and capabilities of graphical workloads. In addition, they are increasingly being integrated in data centers and clouds such that they can be used to accelerate data intensive workloads. Under a number of scenarios the GPU can be shared between multiple applications at a fine granularity allowing a spy application to monitor side channels and attempt to infer the behavior of the victim. For example, OpenGL and WebGL send workloads to the GPU at the granularity of a frame, allowing an attacker to interleave the use of the GPU to measure the side-effects of the victim computation through performance counters or other resource tracking APIs. We demonstrate the vulnerability using two applications. First, we show that an OpenGL based spy can fingerprint websites accurately, track user activities within the website, and even infer the keystroke timings for a password text box with high accuracy. The second application demonstrates how a CUDA spy application can derive the internal parameters of a neural network model being used by another CUDA application, illustrating these threats on the cloud. To counter these attacks, the paper suggests mitigations based on limiting the rate of the calls, or limiting the granularity of the returned information.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243831"}, {"primary_key": "3374203", "vector": [], "sparse_vector": [], "title": "Assessing Non-Visual SSL Certificates with Desktop and Mobile Screen Readers.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Effective SSL warnings can point out network attacks or potential phishing sites. Much focus has been placed on tweaking text, colours, and symbols to improve users' comprehension and adhesion. These optimized visualizations do not necessarily aid people with visual disabilities who hear warnings rather than see them. To assess the non-visual aspects of these security warnings, we conducted an expert evaluation of Google Chrome and Mozilla Firefox's SSL certificate dialogues with JAWS and Apple VoiceOver screen readers. Our findings suggest that warnings are mostly unreadable with assistive technology and, when accessible, do not effectively describe threat sources, at-risk data, or false positives. Future work will explore the effectiveness of potential non-visual redesigns through usability studies with visually impaired screen reader users.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278495"}, {"primary_key": "3374206", "vector": [], "sparse_vector": [], "title": "DeepCorr: Strong Flow Correlation Attacks on Tor Using Deep Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Flow correlation is the core technique used in a multitude of deanonymization attacks on Tor. Despite the importance of flow correlation attacks on Tor, existing flow correlation techniques are considered to be ineffective and unreliable in linking Tor flows when applied at a large scale, i.e., they impose high rates of false positive error rates or require impractically long flow observations to be able to make reliable correlations. In this paper, we show that, unfortunately, flow correlation attacks can be conducted on Tor traffic with drastically higher accuracies than before by leveraging emerging learning mechanisms. We particularly design a system, called DeepCorr, that outperforms the state-of-the-art by significant margins in correlating Tor connections. DeepCorr leverages an advanced deep learning architecture to learn a flow correlation function tailored to Tor's complex network- this is in contrast to previous works' use of generic statistical correlation metrics to correlate Tor flows. We show that with moderate learning, DeepCorr can correlate Tor connections (and therefore break its anonymity) with accuracies significantly higher than existing algorithms, and using substantially shorter lengths of flow observations. For instance, by collecting only about 900 packets of each target Tor flow (roughly 900KB of Tor data), DeepCorr provides a flow correlation accuracy of 96% compared to 4% by the state-of-the-art system of RAPTOR using the same exact setting. We hope that our work demonstrates the escalating threat of flow correlation attacks on Tor given recent advances in learning algorithms, calling for the timely deployment of effective countermeasures by the Tor community.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243824"}, {"primary_key": "3374207", "vector": [], "sparse_vector": [], "title": "Machine Learning with Membership Privacy using Adversarial Regularization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Machine learning models leak significant amount of information about their training sets, through their predictions. This is a serious privacy concern for the users of machine learning as a service. To address this concern, in this paper, we focus on mitigating the risks of black-box inference attacks against machine learning models. We introduce a mechanism to train models with membership privacy, which ensures indistinguishability between the predictions of a model on its training data and other data points (from the same distribution). This requires minimizing the accuracy of the best black-box membership inference attack against the model. We formalize this as a min-max game, and design an adversarial training algorithm that minimizes the prediction loss of the model as well as the maximum gain of the inference attacks. This strategy, which can guarantee membership privacy (as prediction indistinguishability), acts also as a strong regularizer and helps generalizing the model. We evaluate the practical feasibility of our privacy mechanism on training deep neural networks using benchmark datasets. We show that the min-max strategy can mitigate the risks of membership inference attacks (near random guess), and can achieve this with a negligible drop in the model's prediction accuracy (less than 4%).", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243855"}, {"primary_key": "3374208", "vector": [], "sparse_vector": [], "title": "Achieving Meaningful Privacy in Digital Systems.", "authors": ["<PERSON>"], "summary": "Across a range of subfields, computer scientists and engineers have responded to society's call to safeguard privacy through technology, yielding scientific progress and impressive innovation. As the fields of privacy science and engineering mature, however, it's worth taking a moment to ask what ideas of privacy explicitly or implicitly underlie this work and whether they are meaningful, that is to say, whether they map onto ideas of privacy that provoked societal concerns, in the first place. If not, it is unclear, at best, what ends these scientific efforts are actually serving. In my lecture, I argue that privacy as Contextual Integrity (CI) is meaningful in this sense - philosophically sound while being accessible to formal representation. As such, CI could serve as a much-needed a bridge between technical approaches and ethical and policy approaches. I will identify promising directions for future work based on interesting technical applications of CI that have already emerged.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3268996"}, {"primary_key": "3374210", "vector": [], "sparse_vector": [], "title": "The House That Knows You: User Authentication Based on IoT Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Home-based Internet of Things (IoT) devices have gained in popularity and many households became \"smart'' by using devices such as smart sensors, locks, and voice-based assistants. Given the limitations of existing authentication techniques, we explore new opportunities for user authentication in smart home environments. Specifically, we design a novel authentication method in IoT-enabled smart homes. We perform initial experiments and a user study leveraging network traffic collected from 8 IoT devices in our university lab. Preliminary results show that our LSTM model achieves a maximum accuracy of 75% in identifying users.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278523"}, {"primary_key": "3374211", "vector": [], "sparse_vector": [], "title": "A Storage-level Detection Mechanism against Crypto-Ransomware.", "authors": ["<PERSON><PERSON>-<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Ransomware represents a significant threat to both individuals and organizations. Moreover, the emergence of ransomware that exploits kernel vulnerabilities poses a serious detection challenge. In this paper, we propose a novel ransomware detection mechanism at a storage device, especially a flash-based storage device. To this end, we design a new buffer management policy that allows our detector to identify ransomware behaviors. Our mechanism detects a realistic ransomware sample with little negative impacts on the hit ratios of the buffers internally located in a storage device.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278491"}, {"primary_key": "3374216", "vector": [], "sparse_vector": [], "title": "Runtime Analysis of Whole-System Provenance.", "authors": ["<PERSON>-<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON>"], "summary": "Identifying the root cause and impact of a system intrusion remains a foundational challenge in computer security. Digital provenance provides a detailed history of the flow of information within a computing system, connecting suspicious events to their root causes. Although existing provenance-based auditing techniques provide value in forensic analysis, they assume that such analysis takes place only retrospectively. Such post-hoc analysis is insufficient for realtime security applications; moreover, even for forensic tasks, prior provenance collection systems exhibited poor performance and scalability, jeopardizing the timeliness of query responses. We present CamQuery, which provides inline, realtime provenance analysis, making it suitable for implementing security applications. CamQuery is a Linux Security Module that offers support for both userspace and in-kernel execution of analysis applications. We demonstrate the applicability of CamQuery to a variety of runtime security applications including data loss prevention, intrusion detection, and regulatory compliance. In evaluation, we demonstrate that CamQuery reduces the latency of realtime query mechanisms, while imposing minimal overheads on system execution. CamQuery thus enables the further deployment of provenance-based technologies to address central challenges in computer security.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243776"}, {"primary_key": "3374217", "vector": [], "sparse_vector": [], "title": "Private Stateful Information Retrieval.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Private information retrieval (PIR) is a fundamental tool for preserving query privacy when accessing outsourced data. All previous PIR constructions have significant costs preventing widespread use. In this work, we present private stateful information retrieval (PSIR), an extension of PIR, allowing clients to be stateful and maintain information between multiple queries. Our design of the PSIR primitive maintains three important properties of PIR: multiple clients may simultaneously query without complex concurrency primitives, query privacy should be maintained if the server colludes with other clients, and new clients should be able to enroll into the system by exclusively interacting with the server. We present a PSIR framework that reduces an online query to performing one single-server PIR on a sub-linear number of database records. All other operations beyond the single-server PIR consist of cryptographic hashes or plaintext operations. In practice, the dominating costs of resources occur due to the public-key operations involved with PIR. By reducing the input database to PIR, we are able to limit expensive computation and avoid transmitting large ciphertexts. We show that various instantiations of PSIR reduce server CPU by up to 10x and online network costs by up to 10x over the previous best PIR construction.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243821"}, {"primary_key": "3374218", "vector": [], "sparse_vector": [], "title": "Partially Specified Channels: The TLS 1.3 Record Layer without Elision.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We advance the study of secure stream-based channels (<PERSON><PERSON><PERSON> et al., CRYPTO '15) by considering the multiplexing of many data streams over a single channel, an essential feature of real world protocols such as TLS. Our treatment adopts the definitional perspective of Rogaway and Stegers (CSF '09), which offers an elegant way to reason about what standardizing documents actually provide: a partial specification of a protocol that admits a collection of compliant, fully realized implementations. We formalize partially specified channels as the component algorithms of two parties communicating over a channel. Each algorithm has an oracle that provides specification details ; the algorithms abstract the things that must be explicitly specified, while the oracle abstracts the things that need not be. Our security notions, which capture a variety of privacy and integrity goals, allow the adversary to respond to these oracle queries; security relative to these notions implies that the channel withstands attacks in the presence of worst-case (i.e., adversarial) realizations of the specification details. We apply this framework to a formal treatment of the TLS 13 record and, in doing so, show that its security hinges crucially upon details left unspecified by the standard.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243789"}, {"primary_key": "3374219", "vector": [], "sparse_vector": [], "title": "The Price of Privacy in Collaborative Learning.", "authors": ["Balázs <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Machine learning algorithms have reached mainstream status and are widely deployed in many applications. The accuracy of such algorithms depends significantly on the size of the underlying training dataset; in reality a small or medium sized organization often does not have enough data to train a reasonably accurate model. For such organizations, a realistic solution is to train machine learning models based on a joint dataset (which is a union of the individual ones). Unfortunately, privacy concerns prevent them from straightforwardly doing so. While a number of privacy-preserving solutions exist for collaborating organizations to securely aggregate the parameters in the process of training the models, we are not aware of any work that provides a rational framework for the participants to precisely balance the privacy loss and accuracy gain in their collaboration. In this paper, we model the collaborative training process as a two-player game where each player aims to achieve higher accuracy while preserving the privacy of its own dataset. We introduce the notion of Price of Privacy, a novel approach for measuring the impact of privacy protection on the accuracy in the proposed framework. Furthermore, we develop a game-theoretical model for different player types, and then either find or prove the existence of a Nash Equilibrium with regard to the strength of privacy protection for each player.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278525"}, {"primary_key": "3374220", "vector": [], "sparse_vector": [], "title": "Enabling Fair ML Evaluations for Security.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Machine learning is widely used in security research to classify malicious activity, ranging from malware to malicious URLs and network traffic. However, published performance numbers often seem to leave little room for improvement and, due to a wide range of datasets and configurations, cannot be used to directly compare alternative approaches; moreover, most evaluations have been found to suffer from experimental bias which positively inflates results. In this manuscript we discuss the implementation of Tesseract, an open-source tool to evaluate the performance of machine learning classifiers in a security setting mimicking a deployment with typical data feeds over an extended period of time. In particular, Tesseract allows for a fair comparison of different classifiers in a realistic scenario, without disadvantaging any given classifier. Tesseract is available as open-source to provide the academic community with a way to report sound and comparable performance results, but also to help practitioners decide which system to deploy under specific budget constraints.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278505"}, {"primary_key": "3374221", "vector": [], "sparse_vector": [], "title": "Towards Architecture and OS-Independent Malware Detection via Memory Forensics.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this work, we take a fundamentally different approach to the problem of analyzing a device for compromises via malware; our approach is OS and instruction architecture independent and relies only on having the raw binary data extracted from the memory dump of a device. Our system leverages a multi-hundred TB dataset of both compromised host memory dumps extracted from the MalRec dataset [8] and the first known dataset of benign host memory dumps running normal, non-compromised software. After an average of 30 to 45 seconds of pre-processing on a single memory dump, our system leverages both traditional machine learning and deep learning algorithms to achieve an average of 98% accuracy of detecting a compromised host.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278527"}, {"primary_key": "3374222", "vector": [], "sparse_vector": [], "title": "An Effective Android Code Coverage Tool.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Artsiom Kushniarou"], "summary": "The deluge of Android apps from third-party developers calls for sophisticated security testing and analysis techniques to inspect suspicious apps without accessing their source code. Code coverage is an important metric used in these techniques to evaluate their effectiveness, and even as a fitness function to help achieving better results in evolutionary and fuzzy approaches. Yet, so far there are no reliable tools for measuring fine-grained bytecode coverage of Android apps. In this work we present ACVTool that instruments Android apps and measures the smali code coverage at the level of classes, methods, and instructions. Tool repository: https://github.com/pilgun/acvtool", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278484"}, {"primary_key": "3374223", "vector": [], "sparse_vector": [], "title": "Lattice-Based Group Signatures and Zero-Knowledge Proofs of Automorphism Stability.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a group signature scheme, based on the hardness of lattice problems, whose outputs are more than an order of magnitude smaller than the currently most efficient schemes in the literature. Since lattice-based schemes are also usually non-trivial to efficiently implement, we additionally provide the first experimental implementation of lattice-based group signatures demonstrating that our construction is indeed practical -- all operations take less than half a second on a standard laptop. A key component of our construction is a new zero-knowledge proof system for proving that a committed value belongs to a particular set of small size. The sets for which our proofs are applicable are exactly those that contain elements that remain stable under Galois automorphisms of the underlying cyclotomic number field of our lattice-based protocol. We believe that these proofs will find applications in other settings as well. The motivation of the new zero-knowledge proof in our construction is to allow the efficient use of the selectively-secure signature scheme (i.e. a signature scheme in which the adversary declares the forgery message before seeing the public key) of <PERSON><PERSON><PERSON> et al. (Eurocrypt 2010) in constructions of lattice-based group signatures and other privacy protocols. For selectively-secure schemes to be meaningfully converted to standard signature schemes, it is crucial that the size of the message space is not too large. Using our zero-knowledge proofs, we can strategically pick small sets for which we can provide efficient zero-knowledge proofs of membership.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243852"}, {"primary_key": "3374225", "vector": [], "sparse_vector": [], "title": "ClickShield: Are You Hiding Something? Towards Eradicating Clickjacking on Android.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the context of mobile-based user-interface (UI) attacks, the common belief is that clickjacking is a solved problem. On the contrary, this paper shows that clickjacking is still an open problem for mobile devices. In fact, all known academic and industry solutions are either not effective or not applicable in the real-world for backward compatibility reasons. This work shows that, as a consequence, even popular and sensitive apps like Google Play Store remain, to date, completely unprotected from clickjacking attacks. After gathering insights into how apps use the user interface, this work performs a systematic exploration of the design space for an effective and practical protection against clickjacking attacks. We then use this exploration to guide the design of ClickShield, a new defensive mechanism. To address backward compatibility issues, our design allows for overlays to cover the screen, and we employ image analysis techniques to determine whether the user could be confused. We have implemented a prototype and we have tested it against ClickBench, a newly developed benchmark specifically tailored to stress-test clickjacking protection solutions. This dataset is constituted by 104 test cases, and it includes real-world and simulated benign and malicious examples that evaluate the system across a wide range of legitimate and attack scenarios. The results show that our system is able to address backward compatibility concerns, to detect all known attacks (including a never-seen-before real-world malware that was published after we have developed our solution), and it introduces a negligible overhead.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243785"}, {"primary_key": "3374226", "vector": [], "sparse_vector": [], "title": "CPS-SPC 2018: Fourth Workshop on Cyber-Physical Systems Security and PrivaCy.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON>"], "summary": "Cyber-Physical Systems (CPS) are becoming increasingly critical for the well-being of society (e.g., electricity generation and distribution, water treatment, implantable medical devices etc.). While the convergence of computing, communications and physical control in such systems provides benefits in terms of efficiency and convenience, the attack surface resulting from this convergence poses unique security and privacy challenges. These systems represent the new frontier for cyber risk. CPS-SPC is an annual forum in its 4th edition this year, that aims to provide a focal point for the research community to begin addressing the security and privacy challenges of CPS in a comprehensive and multidisciplinary manner and, in tandem with other efforts, build a comprehensive research road map.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243874"}, {"primary_key": "3374228", "vector": [], "sparse_vector": [], "title": "Equitable Security: Optimizing Distribution of Nudges and Resources.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Security behaviors can help users avoid incidents, but can also increase costs, both to users -- in time and mental effort -- and to platforms -- in user engagement and engineering resources. As such, we should consider when it is most efficient and effective to encourage security behaviors. Recent work has shown that users attempt to make security decisions based on cost benefit tradeoffs (boundedly, rationally). Yet, sometimes security nudges (e.g., create unique passwords for every website) encourage users toward irrational behavior: creating strong, unique passwords even for those sites that contain no personal data. In this work-in-progress, we present a mechanism design (a framework) that can be used to optimize the distribution of security nudges and requirements among users with different levels of risk or different levels of investment in a given system. Further, we introduce a new paradigm: the distribution of resources (e.g., ubikeys) that can lower the cost of security behaviors to those users with the most need (the highest time cost from 2FA or lowest Internet skill). Future work will involve simulations showing the value of optimizing distribution of nudges and resources using this framework, and evaluating such an approach in a live test.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278507"}, {"primary_key": "3374229", "vector": [], "sparse_vector": [], "title": "Asking for a Friend: Evaluating Response Biases in Security User Studies.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The security field relies on user studies, often including survey questions, to query end users' general security behavior and experiences, or hypothetical responses to new messages or tools. Self-report data has many benefits -- ease of collection, control, and depth of understanding -- but also many well-known biases stemming from people's difficulty remembering prior events or predicting how they might behave, as well as their tendency to shape their answers to a perceived audience. Prior work in fields like public health has focused on measuring these biases and developing effective mitigations; however, there is limited evidence as to whether and how these biases and mitigations apply specifically in a computer-security context. In this work, we systematically compare real-world measurement data to survey results, focusing on an exemplar, well-studied security behavior: software updating. We align field measurements about specific software updates (n=517,932) with survey results in which participants respond to the update messages that were used when those versions were released (n=2,092). This allows us to examine differences in self-reported and observed update speeds, as well as examining self-reported responses to particular message features that may correlate with these results. The results indicate that for the most part, self-reported data varies consistently and systematically with measured data. However, this systematic relationship breaks down when survey respondents are required to notice and act on minor details of experimental manipulations. Our results suggest that many insights from self-report security data can, when used with care, translate to real-world environments; however, insights about specific variations in message texts or other details may be more difficult to assess with surveys.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243740"}, {"primary_key": "3374233", "vector": [], "sparse_vector": [], "title": "Pseudo Constant Time Implementations of TLS Are Only Pseudo Secure.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Today, about 10% of TLS connections are still using CBC-mode cipher suites, despite a long history of attacks and the availability of better options (e.g. AES-GCM). In this work, we present three new types of attack against four popular fully patched implementations of TLS (Amazon's s2n, GnuTLS, mbed TLS and wolfSSL) which elected to use \"pseudo constant time\" countermeasures against the Lucky 13 attack on CBC-mode. Our attacks combine several variants of the PRIME+PROBE cache timing technique with a new extension of the original Lucky 13 attack. They apply in a cross-VM attack setting and are capable of recovering most of the plaintext whilst requiring only a moderate number of TLS connections. Along the way, we uncovered additional serious (but easy to patch) bugs in all four of the TLS implementations that we studied; in three cases, these bugs lead to Lucky 13 style attacks that can be mounted remotely with no access to a shared cache. Our work shows that adopting pseudo constant time countermeasures is not sufficient to attain real security in TLS implementations in CBC mode.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243775"}, {"primary_key": "3374234", "vector": [], "sparse_vector": [], "title": "AST-Based Deep Learning for Detecting Malicious PowerShell.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Una-<PERSON>;<PERSON>"], "summary": "With the celebrated success of deep learning, some attempts to develop effective methods for detecting malicious PowerShell programs employ neural nets in a traditional natural language processing setup while others employ convolutional neural nets to detect obfuscated malicious commands at a character level. While these representations may express salient PowerShell properties, our hypothesis is that tools from static program analysis will be more effective. We propose a hybrid approach combining traditional program analysis (in the form of abstract syntax trees) and deep learning. This poster presents preliminary results of a fundamental step in our approach: learning embeddings for nodes of PowerShell ASTs. We classify malicious scripts by family type and explore embedded program vector representations.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278496"}, {"primary_key": "3374236", "vector": [], "sparse_vector": [], "title": "Clock Around the Clock: Time-Based Device Fingerprinting.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Physical device fingerprinting exploits hardware features to uniquely identify a machine. This technique has been used for authentication, license binding, or attackers identification, among other tasks. More recently, hardware features have also been introduced to identify web users and perform web tracking. A particular type of hardware fingerprint exploits differences in the computer internal clock signals. However, previous methods to test for these differences relied on complex experiments performed by running native code in the target machine. In this paper, we show a new way to compute a hardware finger- printing, based on timing the execution of sequences of instructions readily available in API functions. Due to its simplicity, this method can also be performed remotely by simply timing few seemingly innocuous lines of JavaScript code. We tested our approach with different functions, such as common string manipulation or widespread cryptographic routines, and found that several of them can be used as basic blocks for fingerprinting. Using this technique, we implemented a tool called CryptoFP. We tested its native implementation in a homogeneous scenario, to distinguish among a perfectly identical (both in software and hardware) set of computers. CryptoFP was able to correctly discriminate all the identical computers in this scenario and recognize the same computer also under different CPU load configurations, outperforming every other hardware fingerprinting method. We then show how CryptoFP can be implemented using a combination of the HTML5 Cryptography API and standard timing API for web device fingerprinting. In this case, we compared our method, both in the same homogeneous scenario and by performing an experiment with real-world users running heterogeneous devices, against other state-of-the-art web device fingerprinting solutions. In both cases, our approach clearly outperforms all existing methods.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243796"}, {"primary_key": "3374238", "vector": [], "sparse_vector": [], "title": "Lawful Device Access without Mass Surveillance Risk: A Technical Design Discussion.", "authors": ["<PERSON>"], "summary": "This paper proposes a systems-oriented design for supporting court-ordered data access to locked\" devices with system-encrypted storage, while explicitly resisting large-scale surveillance use. We describe a design that focuses entirely on passcode self-escrow (i.e., storing a copy of the user passcode into a write-only component on the device) and thus does not require any changes to underlying cryptographic algorithms. Further, by predicating any lawful access on extended-duration physical seizure, we foreclose mass-surveillance use cases while still supporting reasonable investigatory interests. Moreover, by couching per-device authorization protocols with the device manufacturer, this design avoids creating new trusted authorities or organizations while providing particularity (i.e., no \"master keys\" exist). Finally, by providing a concrete description of one such approach, we hope to encourage further technical consideration of the possibilities and limitations of trade-offs in this design space.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243758"}, {"primary_key": "3374239", "vector": [], "sparse_vector": [], "title": "End-to-End Secure Mobile Group Messaging with Conversation Integrity and Minimal Metadata Leakage.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Existing End-to-End secure messaging applications trust a single service provider to deliver messages in a consistent order to a consistent group of conversation members. We propose a protocol that removes this single point of failure by using multiple service providers, enforcing conversation integrity as long as one service provider out of N behave honestly. However, this approach could potentially increase the number of entities that learn the metadata for a conversation. In this work we discuss the challenges and provide a protocol that limits the metadata leakage to that of existing messaging applications while still providing strong conversation integrity.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278506"}, {"primary_key": "3374240", "vector": [], "sparse_vector": [], "title": "Faceted Secure Multi Execution.", "authors": ["<PERSON>", "<PERSON>", "Cormac Flanagan", "<PERSON>"], "summary": "To enforce non-interference, both Secure Multi-Execution (SME) and Multiple Facets (MF) rely on the introduction of multi-executions. The attractiveness of these techniques is that they are precise: secure programs running under SME or MF do not change their behavior. Although MF was intended as an optimization for SME, it does provide a weaker security guarantee for termination leaks. This paper presents Faceted Secure Multi Execution (FSME), a novel synthesis of MF and SME that combines the stronger security guarantees of SME with the optimizations of MF. The development of FSME required a unification of the ideas underlying MF and SME into a new multi-execution framework (Multef), which can be parameterized to provide MF, SME, or our new approach FSME, thus enabling an apples-to-apples comparison and benchmarking of all three approaches. Unlike the original work on MF and SME, Multef supports arbitrary (and possibly infinite) lattices necessary for decentralized labeling models---a feature needed in order to make possible the writing of applications where each principal can impose confidentiality and integrity requirements on data. We provide some micro-benchmarks for evaluating Multef and write a file hosting service, called ProtectedBox, whose functionality can be securely extended via third-party plugins.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243806"}, {"primary_key": "3374242", "vector": [], "sparse_vector": [], "title": "Situational Access Control in the Internet of Things.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Access control in the Internet of Things (IoT) often depends on a situation --- for example, \"the user is at home'' --- that can only be tracked using multiple devices. In contrast to the (well-studied) smartphone frameworks, enforcement of situational constraints in the IoT poses new challenges because access control is fundamentally decentralized. It takes place in multiple independent frameworks, subjects are often external to the enforcement system, and situation tracking requires cross-framework interaction and permissioning. Existing IoT frameworks entangle access-control enforcement and situation tracking. This results in overprivileged, redundant, inconsistent, and inflexible implementations. We design and implement a new approach to IoT access control. Our key innovation is to introduce \"environmental situation oracles'' (ESOs) as first-class objects in the IoT ecosystem. An ESO encapsulates the implementation of how a situation is sensed, inferred, or actuated. IoT access-control frameworks can use ESOs to enforce situational constraints, but ESOs and frameworks remain oblivious to each other's implementation details. A single ESO can be used by multiple access-control frameworks across the ecosystem. This reduces inefficiency, supports consistent enforcement of common policies, and --- because ESOs encapsulate sensitive device-access rights --- reduces overprivileging. ESOs can be deployed at any layer of the IoT software stack where access control is applied. We implemented prototype ESOs for the IoT resource layer, based on the IoTivity framework, and for the IoT Web services, based on the Passport middleware.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243817"}, {"primary_key": "3374243", "vector": [], "sparse_vector": [], "title": "Using Logic Programming to Recover C++ Classes and Methods from Compiled Executables.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "High-level C++ source code abstractions such as classes and methods greatly assist human analysts and automated algorithms alike when analyzing C++ programs. Unfortunately, these abstractions are lost when compiling C++ source code, which impedes the understanding of C++ executables. In this paper, we propose a system, OOAnalyzer, that uses an innovative new design to statically recover detailed C++ abstractions from executables in a scalable manner. OOAnalyzer's design is motivated by the observation that many human analysts reason about C++ programs by recognizing simple patterns in binary code and then combining these findings using logical inference, domain knowledge, and intuition. We codify this approach by combining a lightweight symbolic analysis with a flexible Prolog-based reasoning system. Unlike most existing work, OOAnalyzer is able to recover both polymorphic and non-polymorphic C++ classes. We show in our evaluation that OOAnalyzer assigns over 78% of methods to the correct class on our test corpus, which includes both malware and real-world software such as Firefox and MySQL. These recovered abstractions can help analysts understand the behavior of C++ malware and cleanware, and can also improve the precision of program analyses on C++ executables.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243793"}, {"primary_key": "3374244", "vector": [], "sparse_vector": [], "title": "Not All Pixels are Born Equal: An Analysis of Evasion Attacks under Locality Constraints.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deep neural networks (DNNs) have enabled success in learning tasks such as image classification, semantic image segmentation and steering angle prediction which can be key components of the computer vision pipeline of safety-critical systems such as autonomous vehicles. However, previous work has demonstrated the feasibility of using physical adversarial examples to attack image classification systems. \\par In this work, we argue that the success of realistic adversarial examples is highly dependent on both the structure of the training data and the learning objective. In particular, realistic, physical-world attacks on semantic segmentation and steering angle prediction constrain the adversary to add localized perturbations, since it is very difficult to add perturbations in the entire field of view of input sensors such as cameras for applications like autonomous vehicles. We empirically study the effectiveness of adversarial examples generated under strict locality constraints imposed by the aforementioned applications. Even with image classification, we observe that the success of the adversary under locality constraints depends on the training dataset. With steering angle prediction, we observe that adversarial perturbations localized to an off-road patch are significantly less successful compared to those on-road. For semantic segmentation, we observe that perturbations localized to small patches are only effective at changing the label in and around those patches, making non-local attacks difficult for an adversary. We further provide a comparative evaluation of these localized attacks over various datasets and deep learning models for each task.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278515"}, {"primary_key": "3374251", "vector": [], "sparse_vector": [], "title": "Predicting Impending Exposure to Malicious Content from User Behavior.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Many computer-security defenses are reactive---they operate only when security incidents take place, or immediately thereafter. Recent efforts have attempted to predict security incidents before they occur, to enable defenders to proactively protect their devices and networks. These efforts have primarily focused on long-term predictions. We propose a system that enables proactive defenses at the level of a single browsing session. By observing user behavior, it can predict whether they will be exposed to malicious content on the web seconds before the moment of exposure, thus opening a window of opportunity for proactive defenses. We evaluate our system using three months' worth of HTTP traffic generated by 20,645 users of a large cellular provider in 2017 and show that it can be helpful, even when only very low false positive rates are acceptable, and despite the difficulty of making \"on-the-fly'' predictions. We also engage directly with the users through surveys asking them demographic and security-related questions, to evaluate the utility of self-reported data for predicting exposure to malicious content. We find that self-reported data can help forecast exposure risk over long periods of time. However, even on the long-term, self-reported data is not as crucial as behavioral measurements to accurately predict exposure.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243779"}, {"primary_key": "3374252", "vector": [], "sparse_vector": [], "title": "Post-quantum Security using Channel Noise.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Post-quantum secure communication has attracted much interest in recent years. Known computationally secure post-quantum key agreement protocols are resource intensive for small devices. These devices may need to securely send frequent short messages, for example to report the measurement of a sensor. Secure communication using physical assumptions provides information-theoretic security (and so quantum-safe) with small computational over-head. Security and efficiency analysis of these systems however is asymptotic. In this poster we consider two secure message communication systems, and derive and compare their security and efficiency for finite length messages. Our results show that these systems indeed provide an attractive alternative for post-quantum security.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278517"}, {"primary_key": "3374253", "vector": [], "sparse_vector": [], "title": "Image Disguising for Privacy-preserving Deep Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Due to the high training costs of deep learning, model developers often rent cloud GPU servers to achieve better efficiency. However, this practice raises privacy concerns. An adversarial party may be interested in 1) personal identifiable information encoded in the training data and the learned models, 2) misusing the sensitive models for its own benefits, or 3) launching model inversion (MIA) and generative adversarial network (GAN) attacks to reconstruct replicas of training data (e.g., sensitive images). Learning from encrypted data seems impractical due to the large training data and expensive learning algorithms, while differential-privacy based approaches have to make significant trade-offs between privacy and model quality. We investigate the use of image disguising techniques to protect both data and model privacy. Our preliminary results show that with block-wise permutation and transformations, surprisingly, disguised images still give reasonably well performing deep neural networks (DNN). The disguised images are also resilient to the deep-learning enhanced visual discrimination attack and provide an extra layer of protection from MIA and GAN attacks.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278511"}, {"primary_key": "3374254", "vector": [], "sparse_vector": [], "title": "Privacy-Preserving Boosting with Random Linear Classifiers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose SecureBoost, a privacy-preserving predictive modeling framework, that allows service providers (SPs) to build powerful boosting models over encrypted or randomly masked user submitted data. SecureBoost uses random linear classifiers (RLCs) as the base classifiers. A Cryptographic Service Provider (CSP) manages keys and assists the SP's processing to reduce the complexity of the protocol constructions. The SP learns only the base models (i.e., RLCs) and the CSP learns only the weights of the base models and a limited leakage function. This separated parameter holding avoids any party from abusing the final model or conducting model-based attacks. We evaluate two constructions of SecureBoost: HE+GC and SecSh+GC using combinations of primitives - homomorphic encryption, garbled circuits, and random masking. We show that SecureBoost efficiently learns high-quality boosting models from protected user-generated data with practical costs.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278520"}, {"primary_key": "3374255", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON>: Predicting Security Events Through Deep Learning.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "With the increased complexity of modern computer attacks, there is a need for defenders not only to detect malicious activity as it happens, but also to predict the specific steps that will be taken by an adversary when performing an attack. However this is still an open research problem, and previous research in predicting malicious events only looked at binary outcomes (e.g., whether an attack would happen or not), but not at the specific steps that an attacker would undertake. To fill this gap we present Tiresias, a system that leverages Recurrent Neural Networks (RNNs) to predict future events on a machine, based on previous observations. We test Tiresias on a dataset of 3.4 billion security events collected from a commercial intrusion prevention system, and show that our approach is effective in predicting the next event that will occur on a machine with a precision of up to 0.93. We also show that the models learned by <PERSON><PERSON><PERSON> are reasonably stable over time, and provide a mechanism that can identify sudden drops in precision and trigger a retraining of the system. Finally, we show that the long-term memory typical of RNNs is key in performing event prediction, rendering simpler methods not up to the task.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243811"}, {"primary_key": "3374256", "vector": [], "sparse_vector": [], "title": "Restricting Control Flow During Speculative Execution.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Speculative execution is one of the key techniques that modern processors use to boost performance. However, recent research shows that speculative execution can be used to steal sensitive data. We present a software-based solution to mitigate Spectre attacks by restricting the control flow of speculatively-executed instructions.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278522"}, {"primary_key": "3374257", "vector": [], "sparse_vector": [], "title": "Unveiling Hardware-based Data Prefetcher, a Hidden Source of Information Leakage.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Junbeom Hur"], "summary": "Data prefetching is a hardware-based optimization mechanism used in most of the modern microprocessors. It fetches data to the cache before it is needed. In this paper, we present a novel microarchitectural attack that exploits the prefetching mechanism. Our attack targets Instruction pointer (IP)-based stride prefetching in Intel processors. Stride prefetcher detects memory access patterns with a regular stride, which are likely to be found in lookup table-based cryptographic implementations. By monitoring the prefetching activities near the lookup table, attackers can extract sensitive information such as secret keys from victim applications. This kind of leakage from prefetching has never been considered in the design of constant time algorithm to prevent side-channel attacks. We show the potential of the proposed attack by applying it against the Elliptic Curve Diff<PERSON>-<PERSON>man (ECDH) algorithm built upon the latest version of OpenSSL library. To the best of our knowledge, this is the first microarchitectural side-channel attack exploiting the hardware prefetching of modern microprocessors.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243736"}, {"primary_key": "3374258", "vector": [], "sparse_vector": [], "title": "FEAST&apos;18 - 2018 Workshop on Forming an Ecosystem around Software Transformation.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "It is time for us to welcome you to the Third Workshop on Forming an Ecosystem Around Software Transformation (FEAST 2018)! This year's workshop is held in conjunction with the 25th ACM Conference on Computer and Communications Security (CCS) on October 19th, 2018 in Toronto, Canada. As always, the workshop is geared toward discussion and understanding of several critical topics surrounding late-stage software transformation for improving the security and efficiency of all software used in security-critical applications. The scope of discussion for this workshop will include topics that may be necessary to fully explore the power and impact of late-stage software customization efforts, on binary code and beyond. The call for papers attracted strong submissions from both academy and industry. In total, we accepted 5 full technical papers, and augmented them with a number of exciting invited talks, short talks, and planned discussions. Putting together FEAST'18 is a team effort, and we could not have done it without the authors (and their excellent submissions), the program committee (for their insightful reviews), and the Office of Naval Resea rch for the creation of this workshop in the first place. We are excited about this workshop, and we are sure that you will be as well! Please enjoy the programs, the discussions, and the resulting spread and creation of new ideas!", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243875"}, {"primary_key": "3374259", "vector": [], "sparse_vector": [], "title": "Removing the Bottleneck for Practical 2PC.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Secure Two Party Computation (2PC) has the potential to facilitate a wide range of real life applications where privacy of the computation and participants is critical. Nevertheless, this potential has not translated to widespread industry acceptance due to performance issues. Over the years a significant research effort has focused on optimising the performance of 2PC. The computation complexity has been continually improved and recently, following circuit optimisations and hardware support for cryptographic operations, evaluations of 2PC on a single host currently produce efficient results. Unfortunately, when evaluated on remote hosts, the performance remains prohibitive for practical purposes. The bottleneck is believed to be the bandwidth. In this work we explore the networking layer of 2PC implementations and show that the performance bottleneck is inherent in the usage of TCP sockets in implementations of 2PC schemes. Through experimental evaluations, we demonstrate that other transport protocols can significantly improve the performance of 2PC, making it suitable for practical applications.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278521"}, {"primary_key": "3374260", "vector": [], "sparse_vector": [], "title": "Threat Intelligence Computing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cyber threat hunting is the process of proactively and iteratively formulating and validating threat hypotheses based on security-relevant observations and domain knowledge. To facilitate threat hunting tasks, this paper introduces threat intelligence computing as a new methodology that models threat discovery as a graph computation problem. It enables efficient programming for solving threat discovery problems, equipping threat hunters with a suite of potent new tools for agile codifications of threat hypotheses, automated evidence mining, and interactive data inspection capabilities. A concrete realization of a threat intelligence computing platform is presented through the design and implementation of a domain-specific graph language with interactive visualization support and a distributed graph database. The platform was evaluated in a two-week DARPA competition for threat detection on a test bed comprising a wide variety of systems monitored in real time. During this period, sub-billion records were produced, streamed, and analyzed, dozens of threat hunting tasks were dynamically planned and programmed, and attack campaigns with diverse malicious intent were discovered. The platform exhibited strong detection and analytics capabilities coupled with high efficiency, resulting in a leadership position in the competition. Additional evaluations on comprehensive policy reasoning are outlined to demonstrate the versatility of the platform and the expressiveness of the language.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243829"}, {"primary_key": "3374263", "vector": [], "sparse_vector": [], "title": "Deep Fingerprinting: Undermining Website Fingerprinting Defenses with Deep Learning.", "authors": ["Payap Sirinam", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Website fingerprinting enables a local eavesdropper to determine which websites a user is visiting over an encrypted connection. State-of-the-art website fingerprinting attacks have been shown to be effective even against Tor. Recently, lightweight website fingerprinting defenses for Tor have been proposed that substantially degrade existing attacks: WTF-PAD and Walkie-Talkie. In this work, we present Deep Fingerprinting (DF), a new website fingerprinting attack against Tor that leverages a type of deep learning called Convolutional Neural Networks (CNN) with a sophisticated architecture design, and we evaluate this attack against WTF-PAD and Walkie-Talkie. The DF attack attains over 98% accuracy on Tor traffic without defenses, better than all prior attacks, and it is also the only attack that is effective against WTF-PAD with over 90% accuracy. <PERSON>ie-<PERSON><PERSON> remains effective, holding the attack to just 49.7% accuracy. In the more realistic open-world setting, our attack remains effective, with 0.99 precision and 0.94 recall on undefended traffic. Against traffic defended with WTF-PAD in this setting, the attack still can get 0.96 precision and 0.68 recall. These findings highlight the need for effective defenses that protect against this new attack and that could be deployed in Tor.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243768"}, {"primary_key": "3374265", "vector": [], "sparse_vector": [], "title": "PrivacyBuDe: Privacy Buckets Demo Tight Differential Privacy Guarantees made Simple.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Computing differential privacy guarantees is an important task for a wide variety of applications. The tighter the guarantees are, the more difficult it seems to be to compute them: naive bounds are simple additions, whereas modern composition theorems require either a search over a potentially confusing parameter space (for the composition theorem of <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>), require computing many moments (for Rényi DP) or finding parameters of a function that limits the privacy loss (for concentrated DP). The best known approach for tight differential privacy bounds, called Privacy Buckets, provides requires running a fairly complex implementation of numerical approximations. In this work, we provide an easy-to-use interface for computing state-of-the-art differential privacy guarantees by simply accessing a website. Guarantees for the widely used Laplace mechanism and for the similarly popular Gauss mechanism can be computed by simply stating the scale parameter of the noise, the sensitivity, and the number of compositions. Privacy guarantees for more complex distributions can be computed by uploading two histograms. This work bridges the gap between the best known theoretical results for computing differential privacy guarantees and privacy analysts and users benefiting from such guarantees.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278485"}, {"primary_key": "3374266", "vector": [], "sparse_vector": [], "title": "DeepMem: Learning Graph Neural Network Models for Fast and Robust Memory Forensic Analysis.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Kernel data structure detection is an important task in memory forensics that aims at identifying semantically important kernel data structures from raw memory dumps. It is primarily used to collect evidence of malicious or criminal behaviors. Existing approaches have several limitations: 1) list-traversal approaches are vulnerable to DKOM attacks, 2) robust signature-based approaches are not scalable or efficient, because it needs to search the entire memory snapshot for one kind of objects using one signature, and 3) both list-traversal and signature-based approaches all heavily rely on domain knowledge of operating system. Based on the limitations, we propose DeepMem, a graph-based deep learning approach to automatically generate abstract representations for kernel objects, with which we could recognize the objects from raw memory dumps in a fast and robust way. Specifically, we implement 1) a novel memory graph model that reconstructs the content and topology information of memory dumps, 2) a graph neural network architecture to embed the nodes in the memory graph, and 3) an object detection method that cross-validates the evidence collected from different parts of objects. Experiments show that DeepMem achieves high precision and recall rate in identify kernel objects from raw memory dumps. Also, the detection strategy is fast and scalable by using the intermediate memory graph representation. Moreover, DeepMem is robust against attack scenarios, like pool tag manipulation and DKOM process hiding.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243813"}, {"primary_key": "3374269", "vector": [], "sparse_vector": [], "title": "Practical Backward-Secure Searchable Encryption from Symmetric Puncturable Encryption.", "authors": ["Shifeng Sun", "<PERSON><PERSON><PERSON>g Yuan", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Viet Vo", "Surya Nepal"], "summary": "Symmetric Searchable Encryption (SSE) has received wide attention due to its practical application in searching on encrypted data. Beyond search, data addition and deletion are also supported in dynamic SSE schemes. Unfortunately, these update operations leak some information of updated data. To address this issue, forward-secure SSE is actively explored to protect the relations of newly updated data and previously searched keywords. On the contrary, little work has been done in backward security, which enforces that search should not reveal information of deleted data. In this paper, we propose the first practical and non-interactive backward-secure SSE scheme. In particular, we introduce a new form of symmetric encryption, named symmetric puncturable encryption (SPE), and construct a generic primitive from simple cryptographic tools. Based on this primitive, we then present a backward-secure SSE scheme that can revoke a server's searching ability on deleted data. We instantiate our scheme with a practical puncturable pseudorandom function and implement it on a large dataset. The experimental results demonstrate its efficiency and scalability. Compared to the state-of-the-art, our scheme achieves a speedup of almost 50x in search latency, and a saving of 62% in server storage consumption.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243782"}, {"primary_key": "3374271", "vector": [], "sparse_vector": [], "title": "NodeMerge: Template Based Efficient Data Reduction For Big-Data Causality Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Kangkook Jee", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Xu", "<PERSON><PERSON>"], "summary": "Today's enterprises are exposed to sophisticated attacks, such as Advanced Persistent Threats~(APT) attacks, which usually consist of stealthy multiple steps. To counter these attacks, enterprises often rely on causality analysis on the system activity data collected from a ubiquitous system monitoring to discover the initial penetration point, and from there identify previously unknown attack steps. However, one major challenge for causality analysis is that the ubiquitous system monitoring generates a colossal amount of data and hosting such a huge amount of data is prohibitively expensive. Thus, there is a strong demand for techniques that reduce the storage of data for causality analysis and yet preserve the quality of the causality analysis. To address this problem, in this paper, we propose NodeMerge, a template based data reduction system for online system event storage. Specifically, our approach can directly work on the stream of system dependency data and achieve data reduction on the read-only file events based on their access patterns. It can either reduce the storage cost or improve the performance of causality analysis under the same budget. Only with a reasonable amount of resource for online data reduction, it nearly completely preserves the accuracy for causality analysis. The reduced form of data can be used directly with little overhead. To evaluate our approach, we conducted a set of comprehensive evaluations, which show that for different categories of workloads, our system can reduce the storage capacity of raw system dependency data by as high as 75.7 times, and the storage capacity of the state-of-the-art approach by as high as 32.6 times. Furthermore, the results also demonstrate that our approach keeps all the causality analysis information and has a reasonably small overhead in memory and hard disk.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243763"}, {"primary_key": "3374274", "vector": [], "sparse_vector": [], "title": "Cashing in on the File-System Cache.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider the disk cache (file-system cache) information channel, and show how it can be exploited on various systems to yield potentially sensitive information. Our approach can be used locally by an unprivileged adversary to detect whether another user is writing to disk, and if so, the rate at which data is being written. Further, we also show how an attacker can detect whether specific files have been recently accessed by the victim. We then extend this attack to remote access through a web server, using timing analysis to identify recent access of chosen pages.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278499"}, {"primary_key": "3374277", "vector": [], "sparse_vector": [], "title": "The Gap Game.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Blockchain-based cryptocurrencies secure a decentralized consensus protocol by incentives. The protocol participants, called miners, generate (mine) a series of blocks, each containing monetary transactions created by system users. As incentive for participation, miners receive newly minted currency and transaction fees paid by transaction creators. Blockchain bandwidth limits lead users to pay increasing fees in order to prioritize their transactions. However, most prior work focused on models where fees are negligible. In a notable exception, <PERSON><PERSON> et al. [17] postulated that if incentives come only from fees then a mining gap would form~--- miners would avoid mining when the available fees are insufficient. In this work, we analyze cryptocurrency security in realistic settings, taking into account all elements of expenses and rewards. To study when gaps form, we analyze the system as a game we call the gap game. We analyze the game with a combination of symbolic and numeric analysis tools in a wide range of scenarios. Our analysis confirms <PERSON><PERSON> et al.'s postulate; indeed, we show that gaps form well before fees are the only incentive, and analyze the implications on security. Perhaps surprisingly, we show that different miners choose different gap sizes to optimize their utility, even when their operating costs are identical. Alarmingly, we see that the system incentivizes large miner coalitions, reducing system decentralization. We describe the required conditions to avoid the incentive misalignment, providing guidelines for future cryptocurrency design.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243737"}, {"primary_key": "3374278", "vector": [], "sparse_vector": [], "title": "Securify: Practical Security Analysis of Smart Contracts.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Permissionless blockchains allow the execution of arbitrary programs (called smart contracts), enabling mutually untrusted entities to interact without relying on trusted third parties. Despite their potential, repeated security concerns have shaken the trust in handling billions of USD by smart contracts. To address this problem, we present Securify, a security analyzer for Ethereum smart contracts that is scalable, fully automated, and able to prove contract behaviors as safe/unsafe with respect to a given property. Securify's analysis consists of two steps. First, it symbolically analyzes the contract's dependency graph to extract precise semantic information from the code. Then, it checks compliance and violation patterns that capture sufficient conditions for proving if a property holds or not. To enable extensibility, all patterns are specified in a designated domain-specific language. Securify is publicly released, it has analyzed >18K contracts submitted by its users, and is regularly used to conduct security audits by experts. We present an extensive evaluation of Securify over real-world Ethereum smart contracts and demonstrate that it can effectively prove the correctness of smart contracts and discover critical violations.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243780"}, {"primary_key": "3374279", "vector": [], "sparse_vector": [], "title": "Cross-App Poisoning in Software-Defined Networking.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Software-defined networking (SDN) continues to grow in popularity because of its programmable and extensible control plane realized through network applications (apps). However, apps introduce significant security challenges that can systemically disrupt network operations, since apps must access or modify data in a shared control plane state. If our understanding of how such data propagate within the control plane is inadequate, apps can co-opt other apps, causing them to poison the control plane's integrity. We present a class of SDN control plane integrity attacks that we call cross-app poisoning (CAP), in which an unprivileged app manipulates the shared control plane state to trick a privileged app into taking actions on its behalf. We demonstrate how role-based access control (RBAC) schemes are insufficient for preventing such attacks because they neither track information flow nor enforce information flow control (IFC). We also present a defense, ProvSDN, that uses data provenance to track information flow and serves as an online reference monitor to prevent CAP attacks. We implement ProvSDN on the ONOS SDN controller and demonstrate that information flow can be tracked with low-latency overheads.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243759"}, {"primary_key": "3374281", "vector": [], "sparse_vector": [], "title": "Release the Kraken: New KRACKs in the 802.11 Standard.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We improve key reinstallation attacks (KRACKs) against 802.11 by generalizing known attacks, systematically analyzing all handshakes, bypassing 802.11's official countermeasure, auditing (flawed) patches, and enhancing attacks using implementation-specific bugs. Last year it was shown that several handshakes in the 802.11 standard were vulnerable to key reinstallation attacks. These attacks manipulate handshake messages to reinstall an already-in-use key, leading to both nonce reuse and replay attacks. We extend this work in several directions. First, we generalize attacks against the 4-way handshake so they no longer rely on hard-to-win race conditions, and we employ a more practical method to obtain the required man-in-the-middle (MitM) position. Second, we systematically investigate the 802.11 standard for key reinstallation vulnerabilities, and show that the Fast Initial Link Setup (FILS) and Tunneled direct-link setup PeerKey (TPK) handshakes are also vulnerable to key reinstallations. These handshakes increase roaming speed, and enable direct connectivity between clients, respectively. Third, we abuse Wireless Network Management (WNM) power-save features to trigger reinstallations of the group key. Moreover, we bypass (and improve) the official countermeasure of 802.11. In particular, group key reinstallations were still possible by combining EAPOL-Key and WNM-Sleep frames. We also found implementation-specific flaws that facilitate key reinstallations. For example, some devices reuse the ANonce and SNonce in the 4-way handshake, accept replayed message 4's, or improperly install the group key. We conclude that preventing key reinstallations is harder than expected, and believe that (formally) modeling 802.11 would help to better secure both implementations and the standard itself.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243807"}, {"primary_key": "3374282", "vector": [], "sparse_vector": [], "title": "Mitigating Risk while Complying with Data Retention Laws.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Data breaches represent a significant threat to organizations. While the general problem of protecting data has received much attention, one large (and growing) class has not - data that must be kept due to mandatory retention laws. Such data is often of little use to an organization, is rarely accessed, and represents a significant potential liability, yet cannot be discarded. Protecting such data entails an unusual combination of practical constraints (such as providing verification to a party that may be unknown) and thus requires functionality that is not well addressed by traditional cryptographic primitives. We propose to mitigate the risk to such data through a new system called Dragchute, which creates a time window during which locked data cannot be accessed by anyone. Based on a verifiable non-interactive, non-parallelizable, time-delay key escrow mechanism, Dragchute is novel in that it requires that no cryptographic material capable of providing early access to the data be retained, yet provides verification for multiple properties. We define a base construction for Dragchute, show possible extensions that help meet additional verification requirements, and characterize its performance. Our results show that Dragchute systems offer verifiable, customizable, computational protection against data exposure for encryption costs similar to traditional methods (e.g., less than 6% overhead compared to AEAD). We thus show that Dragchute systems provide a critical new means for protecting data that must be retained long term due to mandatory retention laws.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243800"}, {"primary_key": "3374288", "vector": [], "sparse_vector": [], "title": "Medical Devices are at Risk: Information Security on Diagnostic Imaging System.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Diagnostic Imaging System like X-Ray and CT are widely used in hospitals. And for the reason that health data of patients is stored and transferred digitally, security and privacy on diagnostic imaging systems are of great significance to both patients and hospitals. Although many works on diagnostic imaging system have been studied currently, it's hard to find a detailed analysis on current research progress or a thorough survey on the security controls that should be implemented on diagnostic imaging system. In this paper, we evaluate medical devices from different vendors by the model above. Finally, we conclude the evaluation and analysed the future research directions. The survey of results shows that most of diagnostic imaging systems are under risk and their information security should be further improved.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278513"}, {"primary_key": "3374292", "vector": [], "sparse_vector": [], "title": "Check It Again: Detecting Lacking-Re<PERSON>ck Bugs in OS Kernels.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Operating system kernels carry a large number of security checks to validate security-sensitive variables and operations. For example, a security check should be embedded in a code to ensure that a user-supplied pointer does not point to the kernel space. Using security-checked variables is typically safe. However, in reality, security-checked variables are often subject to modification after the check. If a recheck is lacking after a modification, security issues may arise, e.g., adversaries can control the checked variable to launch critical attacks such as out-of-bound memory access or privilege escalation. We call such cases lacking-recheck (LRC) bugs, a subclass of TOCTTOU bugs, which have not been explored yet. In this paper, we present the first in-depth study of LRC bugs and develop LRSan, a static analysis system that systematically detects LRC bugs in OS kernels. Using an inter-procedural analysis and multiple new techniques, LRSan first automatically identifies security checks, critical variables, and uses of the checked variables, and then reasons about whether a modification is present after a security check. A case in which a modification is present but a recheck is lacking is an LRC bug. We apply LRSan to the latest Linux kernel and evaluate the effectiveness of LRSan. LRSan reports thousands of potential LRC cases, and we have confirmed 19 new LRC bugs. We also discuss patching strategies of LRC bugs based on our study and bug-fixing experience.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243844"}, {"primary_key": "3374294", "vector": [], "sparse_vector": [], "title": "Towards Fine-grained Network Security Forensics and Diagnosis in the SDN Era.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Phakpoom Chinprutthiwong", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Diagnosing network security issues in traditional networks is difficult. It is even more frustrating in the emerging Software Defined Networks. The data/control plane decoupling of the SDN framework makes the traditional network troubleshooting tools unsuitable for pinpointing the root cause in the control plane. In this paper, we propose ForenGuard, which provides flow-level forensics and diagnosis functions in SDN networks. Unlike traditional forensics tools that only involve either network level or host level, ForenGuard monitors and records the runtime activities and their causal dependencies involving both the SDN control plane and data plane. Starting with a forwarding problem (e.g., disconnection) which could be caused by a security issue, ForenGuard can backtrack the previous activities in both the control and data plane through causal relationships and pinpoint the root cause of the problem. ForenGuard also provides a user-friendly interface that allows users to specify the detection point and diagnose complicated network problems. We implement a prototype system of ForenGuard on top of the Floodlight controller and use it to diagnose several real control plane attacks. We show that ForenGuard can quickly display causal relationships of activities and help to narrow down the range of suspicious activities that could be the root causes. Our performance evaluation shows that ForenGuard will add minor runtime overhead to the SDN control plane and can scale well in various network workloads.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243749"}, {"primary_key": "3374295", "vector": [], "sparse_vector": [], "title": "Revery: From Proof-of-Concept to Exploitable.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Automatic exploit generation is an open challenge. Existing solutions usually explore in depth the crashing paths, i.e., paths taken by proof-of-concept (POC) inputs triggering vulnerabilities, and generate exploits when exploitable states are found along the paths. However, exploitable states do not always exist in crashing paths. Moreover, existing solutions heavily rely on symbolic execution and are not scalable in path exploration and exploit generation. In addition, few solutions could exploit heap-based vulnerabilities. In this paper, we propose a new solution revery to search for exploitable states in paths diverging from crashing paths, and generate control-flow hijacking exploits for heap-based vulnerabilities. It adopts three novel techniques:(1) a digraph to characterize a vulnerability's memory layout and its contributor instructions;(2) a fuzz solution to explore diverging paths, which have similar memory layouts as the crashing paths, in order to search more exploitable states and generate corresponding diverging inputs;(3) a stitch solution to stitch crashing paths and diverging paths together, and synthesize EXP inputs able to trigger both vulnerabilities and exploitable states. We have developed a prototype of revery based on the binary analysis engine angr, and evaluated it on a set of 19 real world CTF (capture the flag) challenges. Experiment results showed that it could generate exploits for 9 (47%) of them, and generate EXP inputs able to trigger exploitable states for another 5 (26%) of them.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243847"}, {"primary_key": "3374296", "vector": [], "sparse_vector": [], "title": "JN-SAF: Precise and Efficient NDK/JNI-aware Inter-language Static Analysis Framework for Security Vetting of Android Applications with Native Code.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Android allows application developers to use native language (C/C++) to implement a part or the complete program. Recent research and our own statistics show that native payloads are commonly used in both benign and malicious apps. Current state-of-the-art Android static analysis tools, such as Amandroid, FlowDroid, DroidSafe, IccTA, and CHEX avoid handling native method invocation and apply conservative models for their data-flow behavior. None of those tools have capability to capture the inter-language dataflow. We propose a new approach to conduct inter-language dataflow analysis for security vetting of Android apps, and build an analysis framework, called JN-SAF to compute flow and context-sensitive inter-language points-to information in an efficient way. We show that: 1) Precise and efficient inter-language dataflow analysis is completely feasible with support of a summary-based bottom-up dataflow analysis (SBDA) algorithm, 2) A comprehensive model of Java Native Interface (JNI) and Native Development Kit (NDK) for binary analysis is essential as none of the existing binary analysis frameworks is able to handle Android binaries, 3) JN-SAF is capable of capturing inter-language security issues in real-world Android apps as demonstrated by our evaluation result.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243835"}, {"primary_key": "3374298", "vector": [], "sparse_vector": [], "title": "VMHunt: A Verifiable Approach to Partially-Virtualized Binary Code Simplification.", "authors": ["<PERSON><PERSON><PERSON>", "Jiang <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Code virtualization is a highly sophisticated obfuscation technique adopted by malware authors to stay under the radar. However, the increasing complexity of code virtualization also becomes a \"double-edged sword\" for practical application. Due to its performance limitations and compatibility problems, code virtualization is seldom used on an entire program. Rather, it is mainly used only to safeguard the key parts of code such as security checks and encryption keys. Many techniques have been proposed to reverse engineer the virtualized code, but they share some common limitations. They assume the scope of virtualized code is known in advance and mainly focus on the classic structure of code emulator. Also, few work verifies the correctness of their deobfuscation results. In this paper, with fewer assumptions on the type and scope of code virtualization, we present a verifiable method to address the challenge of partially-virtualized binary code simplification. Our key insight is that code virtualization is a kind of process-level virtual machine (VM), and the context switch patterns when entering and exiting the VM can be used to detect the VM boundaries. Based on the scope of VM boundary, we simplify the virtualized code. We first ignore all the instructions in a given virtualized snippet that do not affect the final result of that snippet. To better revert the data obfuscation effect that encodes a variable through bitwise operations, we then run a new symbolic execution called multiple granularity symbolic execution to further simplify the trace snippet. The generated concise symbolic formulas facilitate the correctness testing of our simplification results. We have implemented our idea as an open source tool, VMHunt, and evaluated it with real-world applications and malware. The encouraging experimental results demonstrate that VMHunt is a significant improvement over the state of the art.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243827"}, {"primary_key": "3374300", "vector": [], "sparse_vector": [], "title": "Practical and Privacy-Aware Truth Discovery in Mobile Crowd Sensing Systems.", "authors": ["<PERSON><PERSON>", "Hongwei Li", "<PERSON><PERSON><PERSON> Lu"], "summary": "We design a Practical and Privacy-Aware Truth Discovery (PPATD) approach in mobile crowd sensing systems, which supports users to go offline at any time while still achieving practical efficiency under working process. More notably, our PPATD is the first solution under single server setting to resolve the problem that users must be online at all times during the truth discovery. Moreover, we design a double-masking with one-time pads protocol to further ensure the strong security of users' privacy even if there is a collusion between the cloud server and multiple users.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278529"}, {"primary_key": "3374301", "vector": [], "sparse_vector": [], "title": "MORPH: Enhancing System Security through Interactive Customization of Application and Communication Protocol Features.", "authors": ["Hong<PERSON> Xue", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The ongoing expansion and addition of new features in software development bring inefficiency and vulnerabilities into programs, resulting in an increased attack surface with higher possibility of exploitation. Creating customized software systems that contain just-enough features and yet satisfy specific user needs is currently an extremely slow, build-to-order process. In this paper, we propose MORPH, an Interactive Program Feature Customization framework to provide broad capabilities for automated program feature identification and feature customization. Our preliminary results show that MORPH can identify program features at an average accuracy of 92.7% and swiftly generate variations of self-contained, customized programs in an unsupervised fashion.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278518"}, {"primary_key": "3374303", "vector": [], "sparse_vector": [], "title": "Milkomeda: Safeguarding the Mobile GPU Interface Using WebGL Security Checks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "GPU-accelerated graphics is commonly used in mobile applications. Unfortunately, the graphics interface exposes a large amount of potentially vulnerable kernel code (i.e., the GPU device driver) to untrusted applications. This broad attack surface has resulted in numerous reported vulnerabilities that are exploitable from unprivileged mobile apps. We observe that web browsers have faced and addressed the exact same problem in WebGL, a framework used by web apps for graphics acceleration. Web browser vendors have developed and deployed a plethora of security checks for the WebGL interface. We introduce Milkomeda, a system solution for automatically repurposing WebGL security checks to safeguard the mobile graphics interface. We show that these checks can be used with minimal modifications (which we have automated using a tool called CheckGen), significantly reducing the engineering effort. Moreover, we demonstrate an in-process shield space for deploying these checks for mobile applications. Compared to the multi-process architecture used by web browsers to protect the integrity of the security checks, our solution improves the graphics performance by eliminating the need for Inter-Process Communication and shared memory data transfer, while providing integrity guarantees for the evaluation of security checks. Our evaluation shows that Milkomeda achieves close-to-native GPU performance at reasonably increased CPU utilization.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243772"}, {"primary_key": "3374305", "vector": [], "sparse_vector": [], "title": "Yet Another Text Captcha Solver: A Generative Adversarial Network Based Approach.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yansong Feng", "<PERSON><PERSON><PERSON><PERSON>", "Xiao<PERSON> Chen", "<PERSON>"], "summary": "Despite several attacks have been proposed, text-based CAPTCHAs are still being widely used as a security mechanism. One of the reasons for the pervasive use of text captchas is that many of the prior attacks are scheme-specific and require a labor-intensive and time-consuming process to construct. This means that a change in the captcha security features like a noisier background can simply invalid an earlier attack. This paper presents a generic, yet effective text captcha solver based on the generative adversarial network. Unlike prior machine-learning-based approaches that need a large volume of manually-labeled real captchas to learn an effective solver, our approach requires significantly fewer real captchas but yields much better performance. This is achieved by first learning a captcha synthesizer to automatically generate synthetic captchas to learn a base solver, and then fine-tuning the base solver on a small set of real captchas using transfer learning. We evaluate our approach by applying it to 33 captcha schemes, including 11 schemes that are currently being used by 32 of the top-50 popular websites including Microsoft, Wikipedia, eBay and Google. Our approach is the most capable attack on text captchas seen to date. It outperforms four state-of-the-art text-captcha solvers by not only delivering a significant higher accuracy on all testing schemes, but also successfully attacking schemes where others have zero chance. We show that our approach is highly efficient as it can solve a captcha within 0.05 second using a desktop GPU. We demonstrate that our attack is generally applicable because it can bypass the advanced security features employed by most modern text captcha schemes. We hope the results of our work can encourage the community to revisit the design and practical use of text captchas.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243754"}, {"primary_key": "3374306", "vector": [], "sparse_vector": [], "title": "A Bitstream Reverse Engineering Tool for FPGA Hardware Trojan Detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Taekyoung Kwon"], "summary": "Since FPGAs are field-programmable and reconfigurable integrated circuits, there are many security concerns that malicious functions like hardware Trojans can be infiltrated into circuits not only in development stages but also in deployment stages -- malicious fabrication and modification are possible even after deployment. To detect hardware Trojans effectively, we must be able to deal with the netlists available at development stages and the bitstreams available at deployment stages -- it is highly desired to reverse-engineer the bitstreams to the netlists, but unfortunately greatly challenging. In this poster, we introduce our project aiming at hardware Trojans detection at both stages in FPGAs, and present our bitstream reverse engineering tool called BRET, recently developed for Xilinx Virtex-5 bitstreams. We also discuss the prospective results and directions.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278487"}, {"primary_key": "3374308", "vector": [], "sparse_vector": [], "title": "Pinto: Enabling Video Privacy for Commodity IoT Cameras.", "authors": ["Hyunwoo Yu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Suk-<PERSON><PERSON>"], "summary": "With various IoT cameras today, sharing of their video evidences, while benefiting the public, threatens the privacy of individuals in the footage. However, protecting visual privacy without losing video authenticity is challenging. The conventional post-process blurring would open the door for posterior fabrication, whereas the realtime blurring results in poor quality, low-frame-rate videos due to the limited processing power of commodity cameras. This paper presents Pinto, a software-based solution for producing privacy-protected, forgery-proof, and high-frame-rate videos using low-end IoT cameras. Pinto records a realtime video stream at a fast rate and allows post-processing for privacy protection prior to sharing of videos while keeping their original, realtime signatures valid even after the post blurring, guaranteeing no content forgery since the time of their recording. Pinto is readily implementable in today's commodity cameras. Our prototype on three different embedded devices, each deployed in a specific application context---on-site, vehicular, and aerial surveillance---demonstrates the production of privacy-protected, forgery-proof videos with frame rates of 17--24 fps, comparable to those of HD videos.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243830"}, {"primary_key": "3374309", "vector": [], "sparse_vector": [], "title": "RapidChain: Scaling Blockchain via Full Sharding.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A major approach to overcoming the performance and scalability limitations of current blockchain protocols is to use sharding which is to split the overheads of processing transactions among multiple, smaller groups of nodes. These groups work in parallel to maximize performance while requiring significantly smaller communication, computation, and storage per node, allowing the system to scale to large networks. However, existing sharding-based blockchain protocols still require a linear amount of communication (in the number of participants) per transaction, and hence, attain only partially the potential benefits of sharding. We show that this introduces a major bottleneck to the throughput and latency of these protocols. Aside from the limited scalability, these protocols achieve weak security guarantees due to either a small fault resiliency (e.g., 1/8 and 1/4) or high failure probability, or they rely on strong assumptions (e.g., trusted setup) that limit their applicability to mainstream payment systems. We propose RapidChain, the first sharding-based public blockchain protocol that is resilient to Byzantine faults from up to a 1/3 fraction of its participants, and achieves complete sharding of the communication, computation, and storage overhead of processing transactions without assuming any trusted setup. RapidChain employs an optimal intra-committee consensus algorithm that can achieve very high throughputs via block pipelining, a novel gossiping protocol for large blocks, and a provably-secure reconfiguration mechanism to ensure robustness. Using an efficient cross-shard transaction verification technique, our protocol avoids gossiping transactions to the entire network. Our empirical evaluations suggest that RapidChain can process (and confirm) more than 7,300 tx/sec with an expected confirmation latency of roughly 8.7 seconds in a network of 4,000 nodes with an overwhelming time-to-failure of more than 4,500 years.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243853"}, {"primary_key": "3374311", "vector": [], "sparse_vector": [], "title": "HoMonit: Monitoring Smart Home Apps from Encrypted Traffic.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Smart home is an emerging technology for intelligently connecting a large variety of smart sensors and devices to facilitate automation of home appliances, lighting, heating and cooling systems, and security and safety systems. Our research revolves around Samsung SmartThings, a smart home platform with the largest number of apps among currently available smart home platforms. The previous research has revealed several security flaws in the design of SmartThings, which allow malicious smart home apps (or SmartApps) to possess more privileges than they were designed and to eavesdrop or spoof events in the SmartThings platform. To address these problems, this paper leverages side-channel inference capabilities to design and develop a system, dubbed HoMonit, to monitor SmartApps from encrypted wireless traffic. To detect anomaly, HoMonit compares the SmartApps activities inferred from the encrypted traffic with their expected behaviors dictated in their source code or UI interfaces. To evaluate the effectiveness of HoMonit, we analyzed 181 official SmartApps and performed evaluation on 60 malicious SmartApps, which either performed over-privileged accesses to smart devices or conducted event-spoofing attacks. The evaluation results suggest that HoMonit can effectively validate the working logic of SmartApps and achieve a high accuracy in the detection of SmartApp misbehaviors.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243820"}, {"primary_key": "3374312", "vector": [], "sparse_vector": [], "title": "CALM: Consistent Adaptive Local Marginal for Marginal Release under Local Differential Privacy.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Marginal tables are the workhorse of capturing the correlations among a set of attributes. We consider the problem of constructing marginal tables given a set of user's multi-dimensional data while satisfying Local Differential Privacy (LDP), a privacy notion that protects individual user's privacy without relying on a trusted third party. Existing works on this problem perform poorly in the high-dimensional setting; even worse, some incur very expensive computational overhead. In this paper, we propose CALM, Consistent Adaptive Local Marginal, that takes advantage of the careful challenge analysis and performs consistently better than existing methods. More importantly, CALM can scale well with large data dimensions and marginal sizes. We conduct extensive experiments on several real world datasets. Experimental results demonstrate the effectiveness and efficiency of CALM over existing methods.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243742"}, {"primary_key": "3374313", "vector": [], "sparse_vector": [], "title": "Invetter: Locating Insecure Input Validations in Android Services.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Android integrates an increasing number of features into system services to manage sensitive resources, such as location, medical and social network information. To prevent untrusted apps from abusing the services, Android implements a comprehensive set of access controls to ensure proper usage of sensitive resources. Unlike explicit permission-based access controls that are discussed extensively in the past, our paper focuses on the widespread yet undocumented input validation problem. As we show in the paper, there are in fact more input validations acting as security checks than permission checks, rendering them a critical foundation for Android framework. Unfortunately, these validations are unstructured, ill-defined, and fragmented, making it challenging to analyze. To this end, we design and implement a tool, called Invetter, that combines machine learning and static analysis to locate sensitive input validations that are problematic in system services. By applying Invetter to 4 different AOSP codebases and 4 vendor-customized images, we locate 103 candidate insecure validations. Among the true positives, we are able to confirm that at least 20 of them are truly exploitable vulnerabilities by constructing various attacks such as privilege escalation and private information leakage.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243843"}, {"primary_key": "3374318", "vector": [], "sparse_vector": [], "title": "A Markov Decision Process to Determine Optimal Policies in Moving Target.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Moving Target Defense (MTD) has been introduced as a new game changer strategy in cybersecurity to strengthen defenders and conversely weaken adversaries. The successful implementation of an MTD system can be influenced by several factors including the effectiveness of the employed technique, the deployment strategy, the cost of the MTD implementation, and the impact from the enforced security policies. Several efforts have been spent on introducing various forms of MTD techniques. However, insufficient research work has been conducted on cost and policy analysis and more importantly the selection of these policies in an MTD-based setting. This poster paper proposes a Markov Decision Process (MDP) modeling-based approach to analyze security policies and further select optimal policies for moving target defense implementation and deployment. The adapted value iteration method would solve the Bellman Optimality Equation for optimal policy selection for each state of the system. The results of some simulations indicate that such modeling can be used to analyze the impact of costs of possible actions towards the optimal policies.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278489"}, {"primary_key": "3374320", "vector": [], "sparse_vector": [], "title": "PatternListener: Cracking Android Pattern Lock Using Acoustic Signals.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Chen"], "summary": "Pattern lock has been widely used for authentication to protect user privacy on mobile devices (e.g., smartphones and tablets). Given its pervasive usage, the compromise of pattern lock could lead to serious consequences. Several attacks have been constructed to crack the lock. However, these approaches require the attackers to either be physically close to the target device or be able to manipulate the network facilities (e.g., WiFi hotspots) used by the victims. Therefore, the effectiveness of the attacks is significantly impacted by the environment of mobile devices. Also, these attacks are not scalable since they cannot easily infer unlock patterns of a large number of devices. Motivated by an observation that fingertip motions on the screen of a mobile device can be captured by analyzing surrounding acoustic signals on it, we propose PatternListener, a novel acoustic attack that cracks pattern lock by analyzing imperceptible acoustic signals reflected by the fingertip. It leverages speakers and microphones of the victim's device to play imperceptible audio and record the acoustic signals reflected by the fingertip. In particular, it infers each unlock pattern by analyzing individual lines that compose the pattern and are the trajectories of the fingertip. We propose several algorithms to construct signal segments according to the captured signals for each line and infer possible candidates of each individual line according to the signal segments. Finally, we map all line candidates into grid patterns and thereby obtain the candidates of the entire unlock pattern. We implement a PatternListener prototype by using off-the-shelf smartphones and thoroughly evaluate it using 130 unique patterns. The real experimental results demonstrate that <PERSON><PERSON><PERSON><PERSON><PERSON> can successfully exploit over 90% patterns within five attempts.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243777"}, {"primary_key": "3374321", "vector": [], "sparse_vector": [], "title": "NANOPI: Extreme-Scale Actively-Secure Multi-Party Computation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Existing actively-secure MPC protocols require either linear rounds or linear space. Due to this fundamental space-round dilemma, no existing MPC protocols is able to run large-scale computations without significantly sacrificing performance. To mitigate this issue, we developed nanoPI, which is practically efficient in terms of both time and space. Our protocol is based on WRK but introduces interesting and necessary modifications to address several important programmatic and cryptographic challenges. A technique that may be of independent interest (in transforming other computation-oriented cryptographic protocols) is a staged execution model, which we formally define and realize using a combination of lightweight static and dynamic program instrumentation. Our techniques are integrated in nanoPI, an open-source tool for efficiently building and running actively-secure extreme-scale MPC applications. We demonstrate the unprecedented scalability and performance of nanoPI by building and running a suit of bench- mark applications, including an actively-secure four-party logistical regression (involving 4.7 billion ANDs and 8.9 billion XORs) which finished in less than 28 hours on four small-memory machines.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3243850"}, {"primary_key": "3374322", "vector": [], "sparse_vector": [], "title": "Game Theory Meets Network Security: A Tutorial.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The increasingly pervasive connectivity of today's information systems brings up new challenges to security. Traditional security has accomplished a long way toward protecting well-defined goals such as confidentiality, integrity, availability, and authenticity. However, with the growing sophistication of the attacks and the complexity of the system, the protection using traditional methods could be cost-prohibitive. A new perspective and a new theoretical foundation are needed to understand security from a strategic and decision-making perspective. Game theory provides a natural framework to capture the adversarial and defensive interactions between an attacker and a defender. It provides a quantitative assessment of security, prediction of security outcomes, and a mechanism design tool that can enable security-by-design and reverse the attacker's advantage. This tutorial provides an overview of diverse methodologies from game theory that includes games of incomplete information, dynamic games, mechanism design theory to offer a modern theoretic underpinning of a science of cybersecurity. The tutorial will also discuss open problems and research challenges that the CCS community can address and contribute with an objective to build a multidisciplinary bridge between cybersecurity, economics, game and decision theory.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3264421"}, {"primary_key": "3374323", "vector": [], "sparse_vector": [], "title": "Adversarial Product Review Generation with Word Replacements.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Machine learning algorithms including Deep Neural Networks (DNNs) have shown great success in many different areas. However, they are frequently susceptible to adversarial examples, which are maliciously crafted inputs to fool machine learning classifiers. On the other hand, humans cannot distinguish between non-adversarial and adversarial inputs. In this work, we focus on creating adversarial examples to change the polarity of positive and negative reviews with Amazon product review dataset. We introduce a simple heuristics algorithm to construct adversarial product reviews by replacing words with semantically and synthetically similar synonyms. We evaluate our approach against the state-of-the-art CNN-BLSTM classifier. Our preliminary results show the performance drop of the classifier against the adversarial examples. We also present the defense mechanism using adversarial training.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3243734.3278492"}, {"primary_key": "3529093", "vector": [], "sparse_vector": [], "title": "Proceedings of the 2018 ACM SIGSAC Conference on Computer and Communications Security, CCS 2018, Toronto, ON, Canada, October 15-19, 2018", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As the ACM's flagship security and privacy conference for more than two decades and the world's largest forum on computing and communications security research, CCS today is facing great challenges in meeting the demands of an expanding community and also ensuring high quality of its published research. This year's conference is unique as the last one before the conference's model change, moving from a single deadline toward a multiple deadline and more journal-like system. This year, a total of 809 submissions were received by the conference. Among them, 134 were selected to appear in the program and be included in the proceedings, with an acceptance rate of 16.6%. These papers were reviewed by a Program Committee of 159 security and privacy experts from academia, industry and government around the world. Following the convention in the past years, the review process includes three rounds. Each submission were reviewed by two PC members in the first round and those moving to the later rounds received additional reviews, depending on the results of the initial reviews. The vast majority of the accepted papers have 4 to 5 reviews. Right after the second round, authors were given the opportunities to respond to the reviews received. During the final review stage, papers were extensively discussed online, taking into account both reviews and the author responses, and additional reviews were assigned to those controversial or with low confident reviews only. Like last year, we used Toronto Paper Matching System (TPMS) to automatically assign papers and then manually adjusted the assignments when necessary. Most PC members have reviewed 16 to 20 papers. The accepted papers cover a large spectrum of security and privacy topics, ranging from cryptography and formal methods to SDN, blockchain and IoT security. This diversity demonstrates the breadth of applied security and privacy research and also reflects the technological trends in the area.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": ""}, {"primary_key": "3531609", "vector": [], "sparse_vector": [], "title": "Proceedings of the 2018 Workshop on Privacy in the Electronic Society, WPES@CCS 2018, Toronto, ON, Canada, October 15-19, 2018", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "These proceedings contain the papers selected for inclusion in the program for the seventeenth ACM Workshop on Privacy in the Electronic Society (WPES 2018), held in conjunction with the 25th ACM Conference on Computer and Communications Security (CCS 2018) on October 15, 2018 in Toronto, Ontario, Canada. In response to the workshop's call for papers, 52 submissions were made. Of these, 25 were submitted as full papers only, 13 as full or short, and 14 as short only. These papers were evaluated by a Program Committee comprised of 51 researchers whose backgrounds include a diverse array of topics related to privacy. Each paper was reviewed by at least three members of the Program Committee, with an average of 3.7 reviews per paper. Papers were evaluated for their importance, novelty, technical quality, and potential to stimulate discussion at the workshop. After a period of online discussion, the Program Committee selected 11 full papers and 8 short papers for presentation at WPES, leading to full acceptance rate of 29% and an overall acceptance rate of 37%.", "published": "2018-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": ""}]