[{"primary_key": "3083233", "vector": [], "sparse_vector": [], "title": "Learning-Guided Network Fuzzing for Testing Cyber-Physical System Defences.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The threat of attack faced by cyber-physical systems (CPSs), especially when they play a critical role in automating public infrastructure, has motivated research into a wide variety of attack defence mechanisms. Assessing their effectiveness is challenging, however, as realistic sets of attacks to test them against are not always available. In this paper, we propose smart fuzzing, an automated, machine learning guided technique for systematically finding 'test suites' of CPS network attacks, without requiring any knowledge of the system's control programs or physical processes. Our approach uses predictive machine learning models and metaheuristic search algorithms to guide the fuzzing of actuators so as to drive the CPS into different unsafe physical states. We demonstrate the efficacy of smart fuzzing by implementing it for two real-world CPS testbeds---a water purification plant and a water distribution system---finding attacks that drive them into 27 different unsafe states involving water flow, pressure, and tank levels, including six that were not covered by an established attack benchmark. Finally, we use our approach to test the effectiveness of an invariant-based defence system for the water treatment plant, finding two attacks that were not detected by its physical invariant checks, highlighting a potential weakness that could be exploited in certain conditions.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00093"}, {"primary_key": "3083234", "vector": [], "sparse_vector": [], "title": "VeriSmart 2.0: Swarm-Based Bug-Finding for Multi-threaded Programs with Lazy-CSeq.", "authors": ["<PERSON><PERSON>", "Salvatore <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Swarm-based verification methods split a verification problem into a large number of independent simpler tasks and so exploit the availability of large numbers of cores to speed up verification. Lazy-CSeq is a BMC-based bug-finding tool for C programs using POSIX threads that is based on sequentialization. Here we present the tool VeriSmart 2.0, which extends Lazy-CSeq with a swarm-based bug-finding method. The key idea of this approach is to constrain the interleaving such that context switches can only happen within selected tiles (more specifically, contiguous code segments within the individual threads). This under-approximates the program's behaviours, with the number and size of tiles as additional parameters, which allows us to vary the complexity of the tasks. Overall, this significantly improves peak memory consumption and (wall-clock) analysis time.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00124"}, {"primary_key": "3083235", "vector": [], "sparse_vector": [], "title": "Continuous Incident Triage for Large-Scale Online Service Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In recent years, online service systems have become increasingly popular. Incidents of these systems could cause significant economic loss and customer dissatisfaction. Incident triage, which is the process of assigning a new incident to the responsible team, is vitally important for quick recovery of the affected service. Our industry experience shows that in practice, incident triage is not conducted only once in the beginning, but is a continuous process, in which engineers from different teams have to discuss intensively among themselves about an incident, and continuously refine the incident-triage result until the correct assignment is reached. In particular, our empirical study on 8 real online service systems shows that the percentage of incidents that were reassigned ranges from 5.43% to 68.26% and the number of discussion items before achieving the correct assignment is up to 11.32 on average. To improve the existing incident triage process, in this paper, we propose DeepCT, a Deep learning based approach to automated Continuous incident Triage. DeepCT incorporates a novel GRU-based (Gated Recurrent Unit) model with an attention-based mask strategy and a revised loss function, which can incrementally learn knowledge from discussions and update incident-triage results. Using DeepCT, the correct incident assignment can be achieved with fewer discussions. We conducted an extensive evaluation of DeepCT on 14 large-scale online service systems in Microsoft. The results show that DeepCT is able to achieve more accurate and efficient incident triage, e.g., the average accuracy identifying the responsible team precisely is 0.641~0.729 with the number of discussion items increasing from 1 to 5. Also, DeepCT statistically significantly outperforms the state-of-the-art bug triage approach.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00042"}, {"primary_key": "3083236", "vector": [], "sparse_vector": [], "title": "History-Guided Configuration Diversification for Compiler Test-Program Generation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Wang", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Compilers, like other software systems, contain bugs, and compiler testing is the most widely-used way to assure compiler quality. A critical task of compiler testing is to generate test programs that could effectively and efficiently discover bugs. Though we can configure test generators such as <PERSON>smith to control the features of the generated programs, it is not clear what test configuration is effective. In particular, an effective test configuration needs to generate test programs that are bug-revealing, i.e., likely to trigger bugs, and diverse, i.e., able to discover different types of bugs. It is not easy to satisfy both properties. In this paper, we propose a novel test-program generation approach, called HiCOND, which utilizes historical data for configuration diversification to solve this challenge. HiCOND first infers the range for each option in a test configuration where bug-revealing test programs are more likely to be generated based on historical data. Then, it identifies a set of test configurations that can lead to diverse test programs through a search method (particle swarm optimization). Finally, based on the set of test configurations for compiler testing, HiCOND generates test programs, which are likely to be bug-revealing and diverse. We have conducted experiments on two popular compilers GCC and LLVM, and the results confirm the effectiveness of our approach. For example, HiCOND detects 75.00%, 133.33%, and 145.00% more bugs than the three existing approaches, respectively. Moreover, HiCOND has been successfully applied to actual compiler testing in a global IT company and detected 11 bugs during the practical evaluation.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00037"}, {"primary_key": "3083238", "vector": [], "sparse_vector": [], "title": "VeriAbs : Verification by Abstraction and Test Generation.", "authors": ["<PERSON>", "Asia A", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>ita Datar", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Verification of programs continues to be a challenge and no single known technique succeeds on all programs. In this paper we present VeriAbs, a reachability verifier for C programs that incorporates a portfolio of techniques implemented as four strategies, where each strategy is a set of techniques applied in a specific sequence. It selects a strategy based on the kind of loops in the program. We analysed the effectiveness of the implemented strategies on the 3831 verification tasks from the ReachSafety category of the 8th International Competition on Software Verification (SV-COMP) 2019 and found that although classic techniques - explicit state model checking and bounded model checking, succeed on a majority of the programs, a wide range of further techniques are required to analyse the rest. A screencast of the tool is available at https://youtu.be/Hzh3PPiODwk.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00121"}, {"primary_key": "3083239", "vector": [], "sparse_vector": [], "title": "mCUTE: A Model-Level Concolic Unit Testing Engine for UML State Machines.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Model Driven Engineering (MDE) techniques raise the level of abstraction at which developers construct software. However, modern cyber-physical systems are becoming more prevalent and complex and hence software models that represent the structure and behavior of such systems still tend to be large and complex. These models may have numerous if not infinite possible behaviors, with complex communications between their components. Appropriate software testing techniques to generate test cases with high coverage rate to put these systems to test at the model-level (without the need to understand the underlying code generator or refer to the generated code) are therefore important. Concolic testing, a hybrid testing technique that benefits from both concrete and symbolic execution, gains a high execution coverage and is used extensively in the industry for program testing but not for software models. In this paper, we present a novel technique and its tool mCUTE1, an open source 2 model-level concolic testing engine. We describe the implementation of our tool in the context of Papyrus-RT, an open source Model Driven Engineering (MDE) tool based on UML-RT, and report the results of validating our tool using a set of benchmark models.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00132"}, {"primary_key": "3083240", "vector": [], "sparse_vector": [], "title": "Targeted Example Generation for Compilation Errors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present TEGCER, an automated feedback tool for novice programmers. TEGCER uses supervised classification to match compilation errors in new code submissions with relevant pre-existing errors, submitted by other students before. The dense neural network used to perform this classification task is trained on 15000+ error-repair code examples. The proposed model yields a test set classification Pred@3 accuracy of 97.7% across 212 error category labels. Using this model as its base, TEGCER presents students with the closest relevant examples of solutions for their specific error on demand. A large scale (N>230) usability study shows that students who use TEGCER are able to resolve errors more than 25% faster on average than students being assisted by human tutors.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00039"}, {"primary_key": "3083241", "vector": [], "sparse_vector": [], "title": "RefBot: Intelligent Software Refactoring Bot.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The adoption of refactoring techniques for continuous integration received much less attention from the research community comparing to root-canal refactoring to fix the quality issues in the whole system. Several recent empirical studies show that developers, in practice, are applying refactoring incrementally when they are fixing bugs or adding new features. There is an urgent need for refactoring tools that can support continuous integration and some recent development processes such as DevOps that are based on rapid releases. Furthermore, several studies show that manual refactoring is expensive and existing automated refactoring tools are challenging to configure and integrate into the development pipelines with significant disruption cost. In this paper, we propose, for the first time, an intelligent software refactoring bot, called RefBot. Integrated into the version control system (e.g. GitHub), our bot continuously monitors the software repository, and it is triggered by any \"open\" or \"merge\" action on pull requests. The bot analyzes the files changed during that pull request to identify refactoring opportunities using a set of quality attributes then it will find the best sequence of refactorings to fix the quality issues if any. The bot recommends all these refactorings through an automatically generated pull-request. The developer can review the recommendations and their impacts in a detailed report and select the code changes that he wants to keep or ignore. After this review, the developer can close and approve the merge of the bot's pull request. We quantitatively and qualitatively evaluated the performance and effectiveness of RefBot by a survey conducted with experienced developers who used the bot on both open source and industry projects.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00081"}, {"primary_key": "3083242", "vector": [], "sparse_vector": [], "title": "Developer Reputation Estimator (DRE).", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Evidence shows that developer reputation is extremely important when accepting pull requests or resolving reported issues. It is particularly salient in Free/Libre Open Source Software since the developers are distributed around the world, do not work for the same organization and, in most cases, never meet face to face. The existing solutions to expose developer reputation tend to be forge specific (GitHub), focus on activity instead of impact, do not leverage social or technical networks, and do not correct often misspelled developer identities. We aim to remedy this by amalgamating data from all public Git repositories, measuring the impact of developer work, expose developer's collaborators, and correct notoriously problematic developer identity data. We leverage World of Code (WoC), a collection of an almost complete (and continuously updated) set of Git repositories by first allowing developers to select which of the 34 million(M) Git commit author IDs belong to them and then generating their profiles by treating the selected collection of IDs as that single developer. As a side-effect, these selections serve as a training set for a supervised learning algorithm that merges multiple identity strings belonging to a single individual. As we evaluate the tool and the proposed impact measure, we expect to build on these findings to develop reputation badges that could be associated with pull requests and commits so developers could easier trust and prioritize them.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00107"}, {"primary_key": "3083243", "vector": [], "sparse_vector": [], "title": "Visual Analytics for Concurrent Java Executions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Analyzing executions of concurrent software is very difficult. Even if a trace is available, such traces are very hard to read and interpret. A textual trace contains a lot of data, most of which is not relevant to the issue at hand. Past visualization attempts either do not show concurrent behavior, or result in a view that is overwhelming for the user. We provide a visual analytics tool, VA4JVM, for error traces produced by either the Java Virtual Machine, or by Java Pathfinder. Its key features are a layout that spatially associates events with threads, a zoom function, and the ability to filter event data in various ways. We show in examples how filtering and zooming in can highlight a problem without having to read lengthy textual data.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00112"}, {"primary_key": "3083244", "vector": [], "sparse_vector": [], "title": "PMExec: An Execution Engine of Partial UML-RT Models.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents PMExec, a tool that supports the execution of partial UML-RT models. To this end, the tool implements the following steps: static analysis, automatic refinement, and input-driven execution. The static analysis that respects the execution semantics of UML-RT models is used to detect problematic model elements, i.e., elements that cause problems during execution due to the partiality. Then, the models are refined automatically using model transformation techniques, which mostly add decision points where missing information can be supplied. Third, the refined models are executed, and when the execution reaches the decision points, input required to continue the execution is obtained either interactively or from a script that captures how to deal with partial elements. We have evaluated PMExec using several use-cases that show that the static analysis, refinement, and application of user input can be carried out with reasonable performance, and that the overhead of approach is manageable. https://youtu.be/BRKsselcMnc Note: Interested readers can refer to [1] for a thorough discussion and evaluation of this work.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00131"}, {"primary_key": "3083245", "vector": [], "sparse_vector": [], "title": "Towards Comprehensible Representation of Controllers using Machine Learning.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "From the point of view of a software engineer, having safe and optimal controllers for real life systems like cyber physical systems is a crucial requirement before deployment. Given the mathematical model of these systems along with their specifications, model checkers can be used to synthesize controllers for them. The given work proposes novel approaches for making controller analysis easier by using machine learning to represent the controllers synthesized by model checkers in a succinct manner, while also incorporating the domain knowledge of the system. It also proposes the implementation of a visualization tool which will be integrated into existing model checkers. A lucid controller representation along with a tool to visualize it will help the software engineer debug and monitor the system much more efficiently.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00163"}, {"primary_key": "3083246", "vector": [], "sparse_vector": [], "title": "Statistical Log Differencing.", "authors": ["Ling<PERSON> Bao", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Recent works have considered the problem of log differencing: given two or more system's execution logs, output a model of their differences. Log differencing has potential applications in software evolution, testing, and security. In this paper we present statistical log differencing, which accounts for frequencies of behaviors found in the logs. We present two algorithms, s2KDiff for differencing two logs, and snKDiff, for differencing of many logs at once, both presenting their results over a single inferred model. A unique aspect of our algorithms is their use of statistical hypothesis testing: we let the engineer control the sensitivity of the analysis by setting the target distance between probabilities and the statistical significance value, and report only (and all) the statistically significant differences. Our evaluation shows the effectiveness of our work in terms of soundness, completeness, and performance. It also demonstrates its effectiveness compared to previous work via a user-study and its potential applications via a case study using real-world logs.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00084"}, {"primary_key": "3083247", "vector": [], "sparse_vector": [], "title": "ACTGAN: Automatic Configuration Tuning for Software Systems with Generative Adversarial Networks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Complex software systems often provide a large number of parameters so that users can configure them for their specific application scenarios. However, configuration tuning requires a deep understanding of the software system, far beyond the abilities of typical system users. To address this issue, many existing approaches focus on exploring and learning good performance estimation models. The accuracy of such models often suffers when the number of available samples is small, a thorny challenge under a given tuning-time constraint. By contrast, we hypothesize that good configurations often share certain hidden structures. Therefore, instead of trying to improve the performance estimation of a given configuration, we focus on capturing the hidden structures of good configurations and utilizing such learned structure to generate potentially better configurations. We propose ACTGAN to achieve this goal. We have implemented and evaluated ACTGAN using 17 workloads with eight different software systems. Experimental results show that ACTGAN outperforms default configurations by 76.22% on average, and six state-of-the-art configuration tuning algorithms by 6.58%-64.56%. Furthermore, the ACTGAN-generated configurations are often better than those used in training and show certain features consisting with domain knowledge, both of which supports our hypothesis.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00051"}, {"primary_key": "3083249", "vector": [], "sparse_vector": [], "title": "Test Migration Between Mobile Apps with Similar Functionality.", "authors": ["Far<PERSON>z <PERSON>", "<PERSON>"], "summary": "The use of mobile apps is increasingly widespread, and much effort is put into testing these apps to make sure they behave as intended. To reduce this effort, and thus the overall cost of mobile app testing, we propose APPTESTMIGRATOR, a technique for migrating test cases between apps in the same category (e.g., banking apps). The intuition behind APPTESTMIGRATOR is that many apps share similarities in their functionality, and these similarities often result in conceptually similar user interfaces (through which that functionality is accessed). APPTESTMIGRATOR leverages these commonalities between user interfaces to migrate existing tests written for an app to another similar app. Specifically, given (1) a test case for an app (source app) and (2) a second app (target app), APPTESTMIGRATOR attempts to automatically transform the sequence of events and oracles in the test for the source app to events and oracles for the target app. We implemented APPTESTMIGRATOR for Android mobile apps and evaluated it on a set of randomly selected apps from the Google Play Store in four different categories. Our initial results are promising, support our intuition that test migration is possible, and motivate further research in this direction.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00016"}, {"primary_key": "3083250", "vector": [], "sparse_vector": [], "title": "TestCov: Robust Test-Suite Execution and Coverage Measurement.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We present TestCov, a tool for robust test-suite execution and test-coverage measurement on C programs. TestCov executes program tests in isolated containers to ensure system integrity and reliable resource control. The tool provides coverage statistics per test and for the whole test suite. TestCov uses the simple, XML -based exchange format for test-suite specifications that was established as standard by Test-Comp. TestCov has been successfully used in Test-Comp '19 to execute almost 9 million tests on 1720 different programs. The source code of TestCov is released under the open-source license Apache 2.0 and available at https://gitlab.com/sosy-lab/software/test-suite-validator. A full artifact, including a demonstration video, is available at https://doi.org/10.5281/zenodo.3418726.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00105"}, {"primary_key": "3083251", "vector": [], "sparse_vector": [], "title": "Code-First Model-Driven Engineering: On the Agile Adoption of MDE Tooling.", "authors": ["<PERSON><PERSON>"], "summary": "Domain models are the most important asset in widely accepted software development approaches, like Domain-Driven Design (DDD), yet those models are still implicitly represented in programs. Model-Driven Engineering (MDE) regards those models as representable entities that are amenable to automated analysis and processing, facilitating quality assurance while increasing productivity in software development processes. Although this connection is not new, very few approaches facilitate adoption of MDE tooling without compromising existing value, their data. Moreover, switching to MDE tooling usually involves re-engineering core parts of an application, hindering backward compatibility and, thereby, continuous integration, while requiring an up-front investment in training in specialized modeling frameworks. In those approaches that overcome the previous problem, there is no clear indication - from a quantitative point of view - of the extent to which adopting state-of-the-art MDE practices and tooling is feasible or advantageous. In this work, we advocate a code-first approach to modeling through an approach for applying MDE techniques and tools to existing object-oriented software applications that fully preserves the semantics of the original application, which need not be modified. Our approach consists both of a semi-automated method for specifying explicit view models out of existing object-oriented applications and of a conservative extension mechanism that enables the use of such view models at run time, where view model queries are resolved on demand and view model updates are propagated incrementally to the original application. This mechanism enables an iterative, flexible application of MDE tooling to software applications, where metamodels and models do not exist explicitly. An evaluation of this extension mechanism, implemented for Java applications and for view models atop the Eclipse Modeling Framework (EMF), has been conducted with an industry-targeted benchmark for decision support systems, analyzing performance and scalability of the synchronization mechanism. Backward propagation of large updates over very large views is instant.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00086"}, {"primary_key": "3083253", "vector": [], "sparse_vector": [], "title": "AutoFocus: Interpreting Attention-Based Neural Networks by Code Perturbation.", "authors": ["Nghi D<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Despite being adopted in software engineering tasks, deep neural networks are treated mostly as a black box due to the difficulty in interpreting how the networks infer the outputs from the inputs. To address this problem, we propose AutoFocus, an automated approach for rating and visualizing the importance of input elements based on their effects on the outputs of the networks. The approach is built on our hypotheses that (1) attention mechanisms incorporated into neural networks can generate discriminative scores for various input elements and (2) the discriminative scores reflect the effects of input elements on the outputs of the networks. This paper verifies the hypotheses by applying AutoFocus on the task of algorithm classification (i.e., given a program source code as input, determine the algorithm implemented by the program). AutoFocus identifies and perturbs code elements in a program systematically, and quantifies the effects of the perturbed elements on the network's classification results. Based on evaluation on more than 1000 programs for 10 different sorting algorithms, we observe that the attention scores are highly correlated to the effects of the perturbed code elements. Such a correlation provides a strong basis for the uses of attention scores to interpret the relations between code elements and the algorithm classification results of a neural network, and we believe that visualizing code elements in an input program ranked according to their attention scores can facilitate faster program comprehension with reduced code.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00014"}, {"primary_key": "3083255", "vector": [], "sparse_vector": [], "title": "Size and Accuracy in Model Inference.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Many works infer finite-state models from execution logs. Large models are more accurate but also more difficult to present and understand. Small models are easier to present and understand but are less accurate. In this work we investigate the tradeoff between model size and accuracy in the context of the classic k-Tails model inference algorithm. First, we define mk-Tails, a generalization of k-Tails from one to many parameters, which enables fine-grained control over the tradeoff. Second, we extend mk-Tails with a reduction based on past-equivalence, which effectively reduces the size of the model without decreasing its accuracy. We implemented our work and evaluated its performance and effectiveness on real-world logs as well as on models and generated logs from the literature.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00087"}, {"primary_key": "3083256", "vector": [], "sparse_vector": [], "title": "Understanding Automatically-Generated Patches Through Symbolic Invariant Differences.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Developer trust is a major barrier to the deployment of automatically-generated patches. Understanding the effect of a patch is a key element of that trust. We find that differences in sets of formal invariants characterize patch differences and that implication-based distances in invariant space characterize patch similarities. When one patch is similar to another it often contains the same changes as well as additional behavior; this pattern is well-captured by logical implication. We can measure differences using a theorem prover to verify implications between invariants implied by separate programs. Although effective, theorem provers are computationally intensive; we find that string distance is an efficient heuristic for implication-based distance measurements. We propose to use distances between patches to construct a hierarchy highlighting patch similarities. We evaluated this approach on over 300 patches and found that it correctly categorizes programs into semantically similar clusters. Clustering programs reduces human effort by reducing the number of semantically distinct patches that must be considered by over 50%, thus reducing the time required to establish trust in automatically generated repairs.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00046"}, {"primary_key": "3083257", "vector": [], "sparse_vector": [], "title": "Pangolin: An SFL-Based Toolset for Feature Localization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Pinpointing the location where a given unit of functionality-or feature-was implemented is a demanding and time-consuming task, yet prevalent in most software maintenance or evolution efforts. To that extent, we present PANGOLIN, an Eclipse plugin that helps developers identifying features among the source code. It borrows Spectrum-based Fault Localization techniques from the software diagnosis research field by framing feature localization as a diagnostic problem. PANGOLIN prompts users to label system executions based on feature involvement, and subsequently presents its spectrum-based feature localization analysis to users with the aid of a color-coded, hierarchic, and navigable visualization which was shown to be effective at conveying diagnostic information to users. Our evaluation shows that PANGOLIN accurately pinpoints feature implementations and is resilient to misclassifications by users. The tool can be downloaded at https://tqrg.github.io/pangolin/.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00119"}, {"primary_key": "3083258", "vector": [], "sparse_vector": [], "title": "The Impact of Structure on Software Merging: Semistructured Versus Structured Merge.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Merge conflicts often occur when developers concurrently change the same code artifacts. While state of practice unstructured merge tools (e.g Git merge) try to automatically resolve merge conflicts based on textual similarity, semistructured and structured merge tools try to go further by exploiting the syntactic structure and semantics of the artifacts involved. Although there is evidence that semistructured merge has significant advantages over unstructured merge, and that structured merge reports significantly fewer conflicts than unstructured merge, it is unknown how semistructured merge compares with structured merge. To help developers decide which kind of tool to use, we compare semistructured and structured merge in an empirical study by reproducing more than 40,000 merge scenarios from more than 500 projects. In particular, we assess how often the two merge strategies report different results, we identify conflicts incorrectly reported by one but not by the other (false positives), and conflicts correctly reported by one but missed by the other (false negatives). Our results show that semistructured and structured merge differ in 24% of the scenarios with conflicts. Semistructured merge reports more false positives, whereas structured merge has more false negatives. Finally, we found that adapting a semistructured merge tool to resolve a particular kind of conflict makes semistructured and structured merge even closer.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00097"}, {"primary_key": "3083259", "vector": [], "sparse_vector": [], "title": "Mutation Analysis for Coq.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Mutation analysis, which introduces artificial defects into software systems, is the basis of mutation testing, a technique widely applied to evaluate and enhance the quality of test suites. However, despite the deep analogy between tests and formal proofs, mutation analysis has seldom been considered in the context of deductive verification. We propose mutation proving, a technique for analyzing verification projects that use proof assistants. We implemented our technique for the Coq proof assistant in a tool dubbed mCoq. mCoq applies a set of mutation operators to Coq definitions of functions and datatypes, inspired by operators previously proposed for functional programming languages. mCoq then checks proofs of lemmas affected by operator application. To make our technique feasible in practice, we implemented several optimizations in mCoq such as parallel proof checking. We applied mCoq to several medium and large scale Coq projects, and recorded whether proofs passed or failed when applying different mutation operators. We then qualitatively analyzed the mutants, finding many instances of incomplete specifications. For our evaluation, we made several improvements to serialization of Coq files and even discovered a notable bug in Coq itself, all acknowledged by developers. We believe mCoq can be useful both to proof engineers for improving the quality of their verification projects and to researchers for evaluating proof engineering techniques.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00057"}, {"primary_key": "3083260", "vector": [], "sparse_vector": [], "title": "Understanding Exception-Related Bugs in Large-Scale Cloud Systems.", "authors": ["Haicheng Chen", "Wensheng Dou", "<PERSON><PERSON>", "Feng <PERSON>"], "summary": "Exception mechanism is widely used in cloud systems. This is mainly because it separates the error handling code from main business logic. However, the huge space of potential error conditions and the sophisticated logic of cloud systems present a big hurdle to the correct use of exception mechanism. As a result, mistakes in the exception use may lead to severe consequences, such as system downtime and data loss. To address this issue, the communities direly need a better understanding of the exception-related bugs, i.e., eBugs, which are caused by the incorrect use of exception mechanism, in cloud systems. In this paper, we present a comprehensive study on 210 eBugs from six widely-deployed cloud systems, including Cassandra, HBase, HDFS, Hadoop MapReduce, YARN, and ZooKeeper. For all the studied eBugs, we analyze their triggering conditions, root causes, bug impacts, and their relations. To the best of our knowledge, this is the first study on eBugs in cloud systems, and the first one that focuses on triggering conditions. We find that eBugs are severe in cloud systems: 74% of our studied eBugs affect system availability or integrity. Luckily, exposing eBugs through testing is possible: 54% of the eBugs are triggered by non-semantic conditions, such as network errors; 40% of the eBugs can be triggered by simulating the triggering conditions at simple system states. Furthermore, we find that the triggering conditions are useful for detecting eBugs. Based on such relevant findings, we build a static analysis tool, called DIET, and apply it to the latest versions of the studied systems. Our results show that DIET reports 31 bugs and bad practices, and 23 of them are confirmed by the developers as \"previously-unknown\" ones.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00040"}, {"primary_key": "3083261", "vector": [], "sparse_vector": [], "title": "An Industrial Experience Report on Performance-Aware Refactoring on a Database-Centric Web Application.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Modern web applications rely heavily on databases to query and update information. To ease the development efforts, Object Relational Mapping (ORM) frameworks provide an abstraction for developers to manage databases by writing in the same Object-Oriented programming languages. Prior studies have shown that there are various types of performance issues caused by inefficient accesses to databases via different ORM frameworks (e.g., Hibernate and ActiveRecord). However, it is not clear whether the reported performance anti-patterns (common performance issues) can be generalizable across various frameworks. In particular, there is no study focusing on detecting performance issues for applications written in PHP, which is the choice of programming languages for the majority (79%) of web applications. In this experience paper, we detail our process on conducting performance-aware refactoring of an industrial web application written in Laravel, the most popular web framework in PHP. We have derived a complete catalog of 17 performance anti-patterns based on prior research and our experimentation. We have found that some of the reported anti-patterns and refactoring techniques are framework or programming language specific, whereas others are general. The performance impact of the anti-pattern instances are highly dependent on the actual usage context (workload and database settings). When communicating the performance differences before and after refactoring, the results of the complex statistical analysis may be sometimes confusing. Instead, developers usually prefer more intuitive measures like percentage improvement. Experiments show that our refactoring techniques can reduce the response time up to 93.0% and 93.4% for the industrial and the open source application under various scenarios.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00066"}, {"primary_key": "3083262", "vector": [], "sparse_vector": [], "title": "An Experience Report of Generating Load Tests Using Log-Recovered Workloads at Varying Granularities of User Behaviour.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Designing field-representative load tests is an essential step for the quality assurance of large-scale systems. Practitioners may capture user behaviour at different levels of granularity. A coarse-grained load test may miss detailed user behaviour, leading to a non-representative load test; while an extremely fine-grained load test would simply replay user actions step by step, leading to load tests that are costly to develop, execute and maintain. Workload recovery is at core of these load tests. Prior research often captures the workload as the frequency of user actions. However, there exists much valuable information in the context and sequences of user actions. Such richer information would ensure that the load tests that leverage such workloads are more field-representative. In this experience paper, we study the use of different granularities of user behaviour, i.e., basic user actions, basic user actions with contextual information and user action sequences with contextual information, when recovering workloads for use in the load testing of large-scale systems. We propose three approaches that are based on the three granularities of user behaviour and evaluate our approaches on four subject systems, namely Apache James, OpenMRS, Google Borg, and an ultra-large-scale industrial system (SA) from Alibaba. Our results show that our approach that is based on user action sequences with contextual information outperforms the other two approaches and can generate more representative load tests with similar throughput and CPU usage to the original field workload (i.e., mostly statistically insignificant or with small/trivial effect sizes). Such representative load tests are generated only based on a small number of clusters of users, leading to a low cost of conducting/maintaining such tests. Finally, we demonstrate that our approaches can detect injected users in the original field workloads with high precision and recall. Our paper demonstrates the importance of user action sequences with contextual information in the workload recovery of large-scale systems.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00068"}, {"primary_key": "3083263", "vector": [], "sparse_vector": [], "title": "BuRRiTo: A Framework to Extract, Specify, Verify and Analyze Business Rules.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "An enterprise system operates business by providing various services that are guided by set of certain business rules (BR) and constraints. These BR are usually written using plain Natural Language in operating procedures, terms and conditions, and other documents or in source code of legacy enterprise systems. For implementing the BR in a software system, expressing them as UML use-case specifications, or preparing for Merger & Acquisition (M&A) activity, analysts manually interpret the documents or try to identify constraints from the source code, leading to potential discrepancies and ambiguities. These issues in the software system can be resolved only after testing, which is a very tedious and expensive activity. To minimize such errors and efforts, we propose BuRRiTo framework consisting of automatic extraction of BR by mining documents and source code, ability to clean them of various anomalies like inconsistency, redundancies, conflicts, etc. and able to analyze the functional gaps present and performing semantic querying and searching.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00134"}, {"primary_key": "3083264", "vector": [], "sparse_vector": [], "title": "Testing Regex Generalizability And Its Implications: A Large-Scale Many-Language Measurement Study.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The regular expression (regex) practices of software engineers affect the maintainability, correctness, and security of their software applications. Empirical research has described characteristics like the distribution of regex feature usage, the structural complexity of regexes, and worst-case regex match behaviors. But researchers have not critically examined the methodology they follow to extract regexes, and findings to date are typically generalized from regexes written in only 1-2 programming languages. This is an incomplete foundation. Generalizing existing research depends on validating two hypotheses: (1) Various regex extraction methodologies yield similar results, and (2) Regex characteristics are similar across programming languages. To test these hypotheses, we defined eight regex metrics to capture the dimensions of regex representation, string language diversity, and worst-case match complexity. We report that the two competing regex extraction methodologies yield comparable corpuses, suggesting that simpler regex extraction techniques will still yield sound corpuses. But in comparing regexes across programming languages, we found significant differences in some characteristics by programming language. Our findings have bearing on future empirical methodology, as the programming language should be considered, and generalizability will not be assured. Our measurements on a corpus of 537,806 regexes can guide data-driven designs of a new generation of regex tools and regex engines.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00048"}, {"primary_key": "3083265", "vector": [], "sparse_vector": [], "title": "CocoQa: Question Answering for Coding Conventions Over Knowledge Graphs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Jun<PERSON> Cao", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Coding convention plays an important role in guaranteeing software quality. However, coding conventions are usually informally presented and inconvenient for programmers to use. In this paper, we present CocoQa, a system that answers programmer's questions about coding conventions. CocoQa answers questions by querying a knowledge graph for coding conventions. It employs 1) a subgraph matching algorithm that parses the question into a SPARQL query, and 2) a machine comprehension algorithm that uses an end-to-end neural network to detect answers from searched paragraphs. We have implemented CocoQa, and evaluated it on a coding convention QA dataset. The results show that CocoQa can answer questions about coding conventions precisely. In particular, CocoQa can achieve a precision of 82.92% and a recall of 91.10%. Repository: https://github.com/14dtj/CocoQa/ Video: https://youtu.be/VQaXi1WydAU.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00108"}, {"primary_key": "3083266", "vector": [], "sparse_vector": [], "title": "A Quantitative Analysis Framework for Recurrent Neural Network.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recurrent neural network (RNN) has achieved great success in processing sequential inputs for applications such as automatic speech recognition, natural language processing and machine translation. However, quality and reliability issues of RNNs make them vulnerable to adversarial attacks and hinder their deployment in real-world applications. In this paper, we propose a quantitative analysis framework - DeepStellar - to pave the way for effective quality and security analysis of software systems powered by RNNs. DeepStellar is generic to handle various RNN architectures, including LSTM and GRU, scalable to work on industrial-grade RNN models, and extensible to develop customized analyzers and tools. We demonstrated that, with DeepStellar, users are able to design efficient test generation tools, and develop effective adversarial sample detectors. We tested the developed applications on three real RNN models, including speech recognition and image classification. DeepStellar outperforms existing approaches three hundred times in generating defect-triggering tests and achieves 97% accuracy in detecting adversarial attacks. A video demonstration which shows the main features of DeepStellar is available at: https://sites.google.com/view/deepstellar/tool-demo.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00102"}, {"primary_key": "3083267", "vector": [], "sparse_vector": [], "title": "Subformula Caching for Model Counting and Quantitative Program Analysis.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Quantitative program analysis is an emerging area with applications to software reliability, quantitative information flow, side-channel detection and attack synthesis. Most quantitative program analysis techniques rely on model counting constraint solvers, which are typically the bottleneck for scalability. Although the effectiveness of formula caching in expediting expensive model-counting queries has been demonstrated in prior work, our key insight is that many subformulas are shared across non-identical constraints generated during program analyses. This has not been utilized by prior formula caching approaches. In this paper we present a subformula caching framework and integrate it into a model counting constraint solver. We experimentally evaluate its effectiveness under three quantitative program analysis scenarios: 1) model counting constraints generated by symbolic execution, 2) reliability analysis using probabilistic symbolic execution, 3) adaptive attack synthesis for side-channels. Our experimental results demonstrate that our subformula caching approach significantly improves the performance of quantitative program analysis.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00050"}, {"primary_key": "3083269", "vector": [], "sparse_vector": [], "title": "InFix: Automatically Repairing Novice Program Inputs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents InFix, a technique for automatically fixing erroneous program inputs for novice programmers. Unlike comparable existing approaches for automatic debugging and maintenance tasks, InFix repairs input data rather than source code, does not require test cases, and does not require special annotations. Instead, we take advantage of patterns commonly used by novice programmers to automatically create helpful, high quality input repairs. InFix iteratively applies error-message based templates and random mutations based on insights about the debugging behavior of novices. This paper presents an implementation of InFix for Python. We evaluate on 29,995 unique scenarios with input-related errors collected from four years of data from Python Tutor, a free online programming tutoring environment. Our results generalize and scale; compared to previous work, we consider an order of magnitude more unique programs. Overall, InFix is able to repair 94.5% of deterministic input errors. We also present the results of a human study with 97 participants. Surprisingly, this simple approach produces high quality repairs; humans judged the output of InFix to be equally helpful and within 4% of the quality of human-generated repairs.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00045"}, {"primary_key": "3083270", "vector": [], "sparse_vector": [], "title": "MutAPK: Source-Codeless Mutant Generation for Android Apps.", "authors": ["<PERSON><PERSON>Velasquez", "<PERSON>", "<PERSON>"], "summary": "The amount of Android application is having a tremendous increasing trend, exerting pressure over practitioners and researchers around application quality, frequent releases, and quick fixing of bugs. This pressure leads practitioners to make usage of automated approaches based on using source-code as input. Nevertheless, third-party services are not able to use these approaches due to privacy factors. In this paper we present MutAPK, an open source mutation testing tool that enables the usage of APK as input for this task. MutAPK generates mutants without the need of having access to source code, because the mutations are done in an intermediate representation of the code (i.e., SMALI) that does not require compilation. MutAPK is publicly available at GitHub: https://bit.ly/2KYvgP9 VIDEO: https://bit.ly/2WOjiyy.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00109"}, {"primary_key": "3083271", "vector": [], "sparse_vector": [], "title": "Active Hotspot: An Issue-Oriented Model to Monitor Software Evolution and Degradation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Hongzhou Fang"], "summary": "Architecture degradation has a strong negative impact on software quality and can result in significant losses. Severe software degradation does not happen overnight. Software evolves continuously, through numerous issues, fixing bugs and adding new features, and architecture flaws emerge quietly and largely unnoticed until they grow in scope and significance when the system becomes difficult to maintain. Developers are largely unaware of these flaws or the accumulating debt as they are focused on their immediate tasks of address individual issues. As a consequence, the cumulative impacts of their activities, as they affect the architecture, go unnoticed. To detect these problems early and prevent them from accumulating into severe ones we propose to monitor software evolution by tracking the interactions among files revised to address issues. In particular, we propose and show how we can automatically detect active hotspots, to reveal architecture problems. We have studied hundreds of hotspots along the evolution timelines of 21 open source projects and showed that there exist just a few dominating active hotspots per project at any given time. Moreover, these dominating active hotspots persist over long time periods, and thus deserve special attention. Compared with state-of-the-art design and code smell detection tools we report that, using active hotspots, it is possible to detect signs of software degradation both earlier and more precisely.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00095"}, {"primary_key": "3083272", "vector": [], "sparse_vector": [], "title": "B2SFinder: Detecting Open-Source Software Reuse in COTS Software.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Chendong Yu", "<PERSON><PERSON><PERSON>", "Aihua Piao", "Jingling Xue", "<PERSON>"], "summary": "COTS software products are developed extensively on top of OSS projects, resulting in OSS reuse vulnerabilities. To detect such vulnerabilities, finding OSS reuses in COTS software has become imperative. While scalable to tens of thousands of OSS projects, existing binary-to-source matching approaches are severely imprecise in analyzing COTS software products, since they support only a limited number of code features, compute matching scores only approximately in measuring OSS reuses, and neglect the code structures in OSS projects. We introduce a novel binary-to-source matching approach, called B2SFINDER 1 , to address these limitations. First of all, B2SFINDER can reason about seven kinds of code features that are traceable in both binary and source code. In order to compute matching scores precisely, B2SFINDER employs a weighted feature matching algorithm that combines three matching methods (for dealing with different code features) with two importance-weighting methods (for computing the weight of an instance of a code feature in a given COTS software application based on its specificity and occurrence frequency). Finally, B2SFINDER identifies different types of code reuses based on matching scores and code structures of OSS projects. We have implemented B2SFINDER using an optimized data structure. We have evaluated B2SFINDER using 21991 binaries from 1000 popular COTS software products and 2189 candidate OSS projects. Our experimental results show that B2SFINDER is not only precise but also scalable. Compared with the state ofthe art, B2SFINDER has successfully found up to 2.15× as many reuse cases in 53.85 seconds per binary file on average. We also discuss how B2SFINDER can be leveraged in detecting OSS reuse vulnerabilities in practice.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00100"}, {"primary_key": "3083273", "vector": [], "sparse_vector": [], "title": "Automating App Review Response Generation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous studies showed that replying to a user review usually has a positive effect on the rating that is given by the user to the app. For example, <PERSON> et al. found that responding to a review increases the chances of a user updating their given rating by up to six times compared to not responding. To alleviate the labor burden in replying to the bulk of user reviews, developers usually adopt a template-based strategy where the templates can express appreciation for using the app or mention the company email address for users to follow up. However, reading a large number of user reviews every day is not an easy task for developers. Thus, there is a need for more automation to help developers respond to user reviews. Addressing the aforementioned need, in this work we propose a novel approach RRGen that automatically generates review responses by learning knowledge relations between reviews and their responses. RRGen explicitly incorporates review attributes, such as user rating and review length, and learns the relations between reviews and corresponding responses in a supervised way from the available training data. Experiments on 58 apps and 309,246 review-response pairs highlight that RRGen outperforms the baselines by at least 67.4% in terms of BLEU-4 (an accuracy measure that is widely used to evaluate dialogue response generation systems). Qualitative analysis also confirms the effectiveness of RRGen in generating relevant and accurate responses.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00025"}, {"primary_key": "3083274", "vector": [], "sparse_vector": [], "title": "Automated Trainability Evaluation for Smart Software Functions.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Alois C. <PERSON>ll"], "summary": "More and more software-intensive systems employ machine learning and runtime optimization to improve their functionality by providing advanced features (e. g. personal driving assistants or recommendation engines). Such systems incorporate a number of smart software functions (SSFs) which gradually learn and adapt to the users' preferences. A key property of SSFs is their ability to learn based on data resulting from the interaction with the user (implicit and explicit feedback)-which we call trainability. Newly developed and enhanced features in a SSF must be evaluated based on their effect on the trainability of the system. Despite recent approaches for continuous deployment of machine learning systems, trainability evaluation is not yet part of continuous integration and deployment (CID) pipelines. In this paper, we describe the different facets of trainability for the development of SSFs. We also present our approach for automated trainability evaluation within an automotive CID framework which proposes to use automated quality gates for the continuous evaluation of machine learning models. The results from our indicative evaluation based on real data from eight BMW cars highlight the importance of continuous and rigorous trainability evaluation in the development of SSFs.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00096"}, {"primary_key": "3083275", "vector": [], "sparse_vector": [], "title": "Toward Practical Automatic Program Repair.", "authors": ["<PERSON>"], "summary": "Automated program repair (APR) reduces the burden of debugging by directly suggesting likely fixes for the bugs. We believe scalability, applicability, and accurate patch validation are among the main challenges for building practical APR techniques that the researchers in this area are dealing with. In this paper, we describe the steps that we are taking toward addressing these challenges.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00156"}, {"primary_key": "3083276", "vector": [], "sparse_vector": [], "title": "PraPR: Practical Program Repair via Bytecode Mutation.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Automated program repair (APR) is one of the recent advances in automated software engineering aiming for reducing the burden of debugging by suggesting high-quality patches that either directly fix the bugs, or help the programmers in the course of manual debugging. We believe scalability, applicability, and accurate patch validation are the main design objectives for a practical APR technique. In this paper, we present PraPR, our implementation of a practical APR technique that operates at the level of JVM bytecode. We discuss design decisions made in the development of PraPR, and argue that the technique is a viable baseline toward attaining aforementioned objectives. Our experimental results show that: (1) PraPR can fix more bugs than state-of-the-art APR techniques and can be over 10X faster, (2) state-of-the-art APR techniques suffer from dataset overfitting, while the simplistic template-based PraPR performs more consistently on different datasets, and (3) PraPR can fix bugs for other JVM languages, such as Kotlin. PraPR is publicly available at https://github.com/prapr/prapr.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00116"}, {"primary_key": "3083278", "vector": [], "sparse_vector": [], "title": "Experience Paper: Search-Based Testing in Automated Driving Control Applications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Automated test generation and evaluation in simulation environments is a key technology for verification of automated driving (AD) applications. Search-based testing (SBT) is an approach for automated test generation that leverages optimization to efficiently generate interesting concrete tests from abstract test descriptions. In this experience paper, we report on our observations after successfully applying SBT to AD control applications in several use cases with different characteristics. Based on our experiences, we derive a number of lessons learned that we consider important for the adoption of SBT methods and tools in industrial settings. The key lesson is that SBT finds relevant errors and provides valuable feedback to the developers, but requires tool support for writing specifications.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00013"}, {"primary_key": "3083279", "vector": [], "sparse_vector": [], "title": "Efficient Test Generation Guided by Field Coverage Criteria.", "authors": ["<PERSON>", "Valeria S. Bengolea", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Field-exhaustive testing is a testing criterion suitable for object-oriented code over complex, heap-allocated, data structures. It requires test suites to contain enough test inputs to cover all feasible values for the object's fields within a certain scope (input-size bound). While previous work shows that field-exhaustive suites can be automatically generated, the generation technique required a formal specification of the inputs that can be subject to SAT-based analysis. Moreover, the restriction of producing all feasible values for inputs' fields makes test generation costly. In this paper, we deal with field coverage as testing criteria that measure the quality of a test suite in terms of coverage and mutation score, by examining to what extent the values of inputs' fields are covered. In particular, we consider field coverage in combination with test generation based on symbolic execution to produce underapproximations of field-exhaustive suites, using the Symbolic Pathfinder tool. To underapproximate these suites we use tranScoping, a technique that estimates characteristics of yet to be run analyses for large scopes, based on data obtained from analyses performed in small scopes. This provides us with a suitable condition to prematurely stop the symbolic execution. As we show, tranScoping different metrics regarding field coverage allows us to produce significantly smaller suites using a fraction of the generation time. All this while retaining the effectiveness of field exhaustive suites in terms of test suite quality.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00019"}, {"primary_key": "3083280", "vector": [], "sparse_vector": [], "title": "Empirical Evaluation of the Impact of Class Overlap on Software Defect Prediction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Li <PERSON>"], "summary": "Software defect prediction (SDP) utilizes the learning models to detect the defective modules in project, and their performance depends on the quality of training data. The previous researches mainly focus on the quality problems of class imbalance and feature redundancy. However, training data often contains some instances that belong to different class but have similar values on features, and this leads to class overlap to affect the quality of training data. Our goal is to investigate the impact of class overlap on software defect prediction. At the same time, we propose an improved K-Means clustering cleaning approach (IKMCCA) to solve both the class overlap and class imbalance problems. Specifically, we check whether K-Means clustering cleaning approach (KMCCA) or neighborhood cleaning learning (NCL) or IKMCCA is feasible to improve defect detection performance for two cases (i) within-project defect prediction (WPDP) (ii) cross-project defect prediction (CPDP). To have an objective estimate of class overlap, we carry out our investigations on 28 open source projects, and compare the performance of state-of-the-art learning models for the above-mentioned cases by using IKMCCA or KMCCA or NCL VS. without cleaning data. The experimental results make clear that learning models obtain significantly better performance in terms of balance, Recall and AUC for both WPDP and CPDP when the overlapping instances are removed. Moreover, it is better to consider both class overlap and class imbalance.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00071"}, {"primary_key": "3083281", "vector": [], "sparse_vector": [], "title": "Property Inference for Deep Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "Hayes Converse", "Corina S<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present techniques for automatically inferring formal properties of feed-forward neural networks. We observe that a significant part (if not all) of the logic of feed forward networks is captured in the activation status (on or off) of its neurons. We propose to extract patterns based on neuron decisions as preconditions that imply certain desirable output property e.g., the prediction being a certain class. We present techniques to extract input properties, encoding convex predicates on the input space that imply given output properties and layer properties, representing network properties captured in the hidden layers that imply the desired output behavior. We apply our techniques on networks for the MNIST and ACASXU applications. Our experiments highlight the use of the inferred properties in a variety of tasks, such as explaining predictions, providing robustness guarantees, simplifying proofs, and network distillation.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00079"}, {"primary_key": "3083282", "vector": [], "sparse_vector": [], "title": "CodeKernel: A Graph Kernel Based Approach to the Selection of API Usage Examples.", "authors": ["Xiaodong Gu", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Developers often want to find out how to use a certain API (e.g., FileReader.read in JDK library). API usage examples are very helpful in this regard. Over the years, many automated methods have been proposed to generate code examples by clustering and summarizing relevant code snippets extracted from a code corpus. These approaches simplify source code as method invocation sequences or feature vectors. Such simplifications only model partial aspects of the code and tend to yield inaccurate examples. We propose CodeKernel, a graph kernel based approach to the selection of API usage examples. Instead of approximating source code as method invocation sequences or feature vectors, CodeKernel represents source code as object usage graphs. Then, it clusters graphs by embedding them into a continuous space using a graph kernel. Finally, it outputs code examples by selecting a representative graph from each cluster using designed ranking metrics. Our empirical evaluation shows that CodeKernel selects more accurate code examples than the related work (MUSE and eXoaDocs). A user study involving 25 developers in a multinational company also confirms the usefulness of CodeKernel in selecting API usage examples.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00061"}, {"primary_key": "3083283", "vector": [], "sparse_vector": [], "title": "An Empirical Study Towards Characterizing Deep Learning Development and Deployment Across Different Frameworks and Platforms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep Learning (DL) has recently achieved tremendous success. A variety of DL frameworks and platforms play a key role to catalyze such progress. However, the differences in architecture designs and implementations of existing frameworks and platforms bring new challenges for DL software development and deployment. Till now, there is no study on how various mainstream frameworks and platforms influence both DL software development and deployment in practice. To fill this gap, we take the first step towards understanding how the most widely-used DL frameworks and platforms support the DL software development and deployment. We conduct a systematic study on these frameworks and platforms by using two types of DNN architectures and three popular datasets. (1) For development process, we investigate the prediction accuracy under the same runtime training configuration or same model weights/biases. We also study the adversarial robustness of trained models by leveraging the existing adversarial attack techniques. The experimental results show that the computing differences across frameworks could result in an obvious prediction accuracy decline, which should draw the attention of DL developers. (2) For deployment process, we investigate the prediction accuracy and performance (refers to time cost and memory consumption) when the trained models are migrated/quantized from PC to real mobile devices and web browsers. The DL platform study unveils that the migration and quantization still suffer from compatibility and reliability issues. Meanwhile, we find several DL software bugs by using the results as a benchmark. We further validate the results through bug confirmation from stakeholders and industrial positive feedback to highlight the implications of our study. Through our study, we summarize practical guidelines, identify challenges and pinpoint new research directions, such as understanding the characteristics of DL frameworks and platforms, avoiding compatibility and reliability issues, detecting DL software bugs, and reducing time cost and memory consumption towards developing and deploying high quality DL systems effectively.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00080"}, {"primary_key": "3083284", "vector": [], "sparse_vector": [], "title": "Tackling Build Failures in Continuous Integration.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "In popular continuous integration(CI) practice, coding is followed by building, integration and system testing, pre-release inspection, and deploying artifacts. This can reduce integration risk and speed up the development process. But large number of CI build failures may interrupt the normal software development process. So, the failures need to be analyzed and fixed quickly. Although various automated program repair techniques have great potential to resolve software failures, the existing techniques mostly focus on repairing source code. So, those techniques cannot directly help resolve software build failures. Apart from that, a special challenge to fix build failures in CI environment is that the failures are often involved with both source code and build scripts. This paper outlines promising preliminary work towards automatic build repair in CI environment that involves both source code and build script. As the first step, we conducted an empirical study on software build failures and build fix patterns. Based on the findings of the empirical study, we developed an approach that can automatically fix build errors involving build scripts. We plan to extend this repair approach considering both source code and build script. Moreover, we plan to quantify our automatic fixes by user study and comparison between fixes generated by our approach and actual fixes.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00150"}, {"primary_key": "3083285", "vector": [], "sparse_vector": [], "title": "Systematically Covering Input Structure.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Grammar-based testing uses a given grammar to produce syntactically valid inputs. To cover program features, it is necessary to also cover input features-say, all URL variants for a URL parser. Our k-path algorithm for grammar production systematically covers syntactic elements as well as their combinations. In our evaluation, we show that this results in a significantly higher code coverage than state of the art.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00027"}, {"primary_key": "3083286", "vector": [], "sparse_vector": [], "title": "Performance-Boosting Sparsification of the IFDS Algorithm with Applications to Taint Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>ofeng Li", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Hu", "<PERSON><PERSON>", "Jingling Xue"], "summary": "The IFDS algorithm can be compute-and memoryintensive for some large programs, often running for a long time (more than expected) or terminating prematurely after some time and/or memory budgets have been exhausted. In the latter case, the corresponding IFDS data-flow analyses may suffer from false negatives and/or false positives. To improve this, we introduce a sparse alternative to the traditional IFDS algorithm. Instead of propagating the data-flow facts across all the program points along the program's (interprocedural) control flow graph, we propagate every data-flow fact directly to its next possible use points along its own sparse control flow graph constructed on the fly, thus reducing significantly both the time and memory requirements incurred by the traditional IFDS algorithm. In our evaluation, we compare FLOWDROID, a taint analysis performed by using the traditional IFDS algorithm, with our sparse incarnation, SPARSEDROID, on a set of 40 Android apps selected. For the time budget (5 hours) and memory budget (220GB) allocated per app, SPARSEDROID can run every app to completion but FLOWDROID terminates prematurely for 9 apps, resulting in an average speedup of 22.0x. This implies that when used as a market-level vetting tool, SPARSEDROID can finish analyzing these 40 apps in 2.13 hours (by issuing 228 leak warnings) while FLOWDROID manages to analyze only 30 apps in the same time period (by issuing only 147 leak warnings).", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00034"}, {"primary_key": "3083287", "vector": [], "sparse_vector": [], "title": "V2: Fast Detection of Configuration Drift in Python.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Code snippets are prevalent, but are hard to reuse because they often lack an accompanying environment configuration. Most are not actively maintained, allowing for drift between the most recent possible configuration and the code snippet as the snippet becomes out-of-date over time. Recent work has identified the problem of validating and detecting out-of-date code snippets as the most important consideration for code reuse. However, determining if a snippet is correct, but simply out-of-date, is a non-trivial task. In the best case, breaking changes are well documented, allowing developers to manually determine when a code snippet contains an out-of-date API usage. In the worst case, determining if and when a breaking change was made requires an exhaustive search through previous dependency versions. We present V2, a strategy for determining if a code snippet is out-of-date by detecting discrete instances of configuration drift, where the snippet uses an API which has since undergone a breaking change. Each instance of configuration drift is classified by a failure encountered during validation and a configuration patch, consisting of dependency version changes, which fixes the underlying fault. V2 uses feedback-directed search to explore the possible configuration space for a code snippet, reducing the number of potential environment configurations that need to be validated. When run on a corpus of public Python snippets from prior research, V2 identifies 248 instances of configuration drift.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00052"}, {"primary_key": "3083288", "vector": [], "sparse_vector": [], "title": "DeepMutation++: A Mutation Testing Framework for Deep Learning Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deep neural networks (DNNs) are increasingly expanding their real-world applications across domains, e.g., image processing, speech recognition and natural language processing. However, there is still limited tool support for DNN testing in terms of test data quality and model robustness. In this paper, we introduce a mutation testing-based tool for DNNs, DeepMutation++, which facilitates the DNN quality evaluation, supporting both feed-forward neural networks (FNNs) and stateful recurrent neural networks (RNNs). It not only enables to statically analyze the robustness of a DNN model against the input as a whole, but also allows to identify the vulnerable segments of a sequential input (e.g. audio input) by runtime analysis. It is worth noting that DeepMutation++ specially features the support of RNNs mutation testing. The tool demo video can be found on the project website https://sites.google.com/view/deepmutationpp.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00126"}, {"primary_key": "3083289", "vector": [], "sparse_vector": [], "title": "Re-Factoring Based Program Repair Applied to Programming Assignments.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Automated program repair has been used to provide feedback for incorrect student programming assignments, since program repair captures the code modification needed to make a given buggy program pass a given test-suite. Existing student feedback generation techniques are limited because they either require manual effort in the form of providing an error model, or require a large number of correct student submissions to learn from, or suffer from lack of scalability and accuracy. In this work, we propose a fully automated approach for generating student program repairs in real-time. This is achieved by first re-factoring all available correct solutions to semantically equivalent solutions. Given an incorrect program, we match the program with the closest matching refactored program based on its control flow structure. Subsequently, we infer the input-output specifications of the incorrect program's basic blocks from the executions of the correct program's aligned basic blocks. Finally, these specifications are used to modify the blocks of the incorrect program via search-based synthesis. Our dataset consists of almost 1,800 real-life incorrect Python program submissions from 361 students for an introductory programming course at a large public university. Our experimental results suggest that our method is more effective and efficient than recently proposed feedback generation approaches. About 30% of the patches produced by our tool Refactory are smaller than those produced by the state-of-art tool Clara, and can be produced given fewer correct solutions (often a single correct solution) and in a shorter time. We opine that our method is applicable not only to programming assignments, and could be seen as a general-purpose program repair method that can achieve good results with just a single correct reference solution.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00044"}, {"primary_key": "3083290", "vector": [], "sparse_vector": [], "title": "Prema: A Tool for Precise Requirements Editing, Modeling and Analysis.", "authors": ["<PERSON><PERSON>", "Jinca<PERSON> Feng", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Siyuan Jiang", "Weikai Miao", "<PERSON><PERSON><PERSON>"], "summary": "We present Prema, a tool for Precise Requirement Editing, Modeling and Analysis. It can be used in various fields for describing precise requirements using formal notations and performing rigorous analysis. By parsing the requirements written in formal modeling language, Prema is able to get a model which aptly depicts the requirements. It also provides different rigorous verification and validation techniques to check whether the requirements meet users' expectation and find potential errors. We show that our tool can provide a unified environment for writing and verifying requirements without using tools that are not well inter-related. For experimental demonstration, we use the requirements of the automatic train protection (ATP) system of CASCO signal co. LTD., the largest railway signal control system manufacturer of China. The code of the tool cannot be released here because the project is commercially confidential. However, a demonstration video of the tool is available at https://youtu.be/BX0yv8pRMWs.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00128"}, {"primary_key": "3083293", "vector": [], "sparse_vector": [], "title": "Detecting Error-Handling Bugs without Error Specification Input.", "authors": ["<PERSON><PERSON> Ji<PERSON>", "<PERSON><PERSON>", "Tingting Yu", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Most software systems frequently encounter errors when interacting with their environments. When errors occur, error-handling code must execute flawlessly to facilitate system recovery. Implementing correct error handling is repetitive but non-trivial, and developers often inadvertently introduce bugs into error-handling code. Existing tools require correct error specifications to detect error-handling bugs. Manually generating error specifications is error-prone and tedious, while automatically mining error specifications is hard to achieve a satisfying accuracy. In this paper, we propose EH-Miner, a novel and practical tool that can automatically detect error-handling bugs without the need for error specifications. Given a function, EH-Miner mines its error-handling rules when the function is frequently checked by an equivalent condition, and handled by the same action. We applied EH-Miner to 117 applications across 15 software domains. EH-Miner mined error-handling rules with the precision of 91.1% and the recall of 46.9%. We reported 142 bugs to developers, and 106 bugs had been confirmed and fixed at the time of writing. We further applied EH-Miner to Linux kernel, and reported 68 bugs for kernel-4.17, of which 42 had been confirmed or fixed.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00029"}, {"primary_key": "3083294", "vector": [], "sparse_vector": [], "title": "Boosting Neural Commit Message Generation with Code Semantic Analysis.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "It has been long suggested that commit messages can greatly facilitate code comprehension. However, developers may not write good commit messages in practice. Neural machine translation (NMT) has been suggested to automatically generate commit messages. Despite the efforts in improving NMT algorithms, the quality of the generated commit messages is not yet satisfactory. This paper, instead of improving NMT algorithms, suggests that proper preprocessing of code changes into concise inputs is quite critical to train NMT. We approach it with semantic analysis of code changes. We collect a real-world dataset with 50k+ commits of popular Java projects, and verify our idea with comprehensive experiments. The results show that preprocessing inputs with code semantic analysis can improve NMT significantly. This work sheds light to how to apply existing DNNs designed by the machine learning community, e.g., NMT models, to complete software engineering tasks.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00162"}, {"primary_key": "3083295", "vector": [], "sparse_vector": [], "title": "Machine Learning Based Recommendation of Method Names: How Far are We.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "High quality method names are critical for the readability and maintainability of programs. However, constructing concise and consistent method names is often challenging, especially for inexperienced developers. To this end, advanced machine learning techniques have been recently leveraged to recommend method names automatically for given method bodies/implementation. Recent large-scale evaluations also suggest that such approaches are accurate. However, little is known about where and why such approaches work or don't work. To figure out the state of the art as well as the rationale for the success/failure, in this paper we conduct an empirical study on the state-of-the-art approach code2vec. We assess code2vec on a new dataset with more realistic settings. Our evaluation results suggest that although switching to new dataset does not significantly influence the performance, more realistic settings do significantly reduce the performance of code2vec. Further analysis on the successfully recommended method names also reveals the following findings: 1) around half (48.3%) of the accepted recommendations are made on getter/setter methods; 2) a large portion (19.2%) of the successfully recommended method names could be copied from the given bodies. To further validate its usefulness, we ask developers to manually score the difficulty in naming methods they developed. Code2vec is then applied to such manually scored methods to evaluate how often it works in need. Our evaluation results suggest that code2vec rarely works when it is really needed. Finally, to intuitively reveal the state of the art and to investigate the possibility of designing simple and straightforward alternative approaches, we propose a heuristics based approach to recommending method names. Evaluation results on large-scale dataset suggest that this simple heuristics-based approach significantly outperforms the state-of-the-art machine learning based approach, improving precision and recall by 65.25% and 22.45%, respectively. The comparison suggests that machine learning based recommendation of method names may still have a long way to go.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00062"}, {"primary_key": "3083296", "vector": [], "sparse_vector": [], "title": "Inferring Program Transformations From Singular Examples via Big Code.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Inferring program transformations from concrete program changes has many potential uses, such as applying systematic program edits, refactoring, and automated program repair. Existing work for inferring program transformations usually rely on statistical information over a potentially large set of program-change examples. However, in many practical scenarios we do not have such a large set of program-change examples. In this paper, we address the challenge of inferring a program transformation from one single example. Our core insight is that \"big code\" can provide effective guide for the generalization of a concrete change into a program transformation, i.e., code elements appearing in many files are general and should not be abstracted away. We first propose a framework for transformation inference, where programs are represented as hypergraphs to enable fine-grained generalization of transformations. We then design a transformation inference approach, GENPAT, that infers a program transformation based on code context and statistics from a big code corpus. We have evaluated GENPAT under two distinct application scenarios, systematic editing and program repair. The evaluation on systematic editing shows that GENPAT significantly outperforms a state-of-the-art approach, SYDIT, with up to 5.5x correctly transformed cases. The evaluation on program repair suggests that GENPAT has the potential to be integrated in advanced program repair tools-GENPAT successfully repaired 19 real-world bugs in the Defects4J benchmark by simply applying transformations inferred from existing patches, where 4 bugs have never been repaired by any existing technique. Overall, the evaluation results suggest that GENPAT is effective for transformation inference and can potentially be adopted for many different applications.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00033"}, {"primary_key": "3083297", "vector": [], "sparse_vector": [], "title": "Combining Spectrum-Based Fault Localization and Statistical Debugging: An Empirical Study.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Program debugging is a time-consuming task, and researchers have proposed different kinds of automatic fault localization techniques to mitigate the burden of manual debugging. Among these techniques, two popular families are spectrum-based fault localization (SBFL) and statistical debugging (SD), both localizing faults by collecting statistical information at runtime. Though the ideas are similar, the two families have been developed independently and their combinations have not been systematically explored. In this paper we perform a systematical empirical study on the combination of SBFL and SD. We first build a unified model of the two techniques, and systematically explore four types of variations, different predicates, different risk evaluation formulas, different granularities of data collection, and different methods of combining suspicious scores. Our study leads to several findings. First, most of the effectiveness of the combined approach contributed by a simple type of predicates: branch conditions. Second, the risk evaluation formulas of SBFL significantly outperform that of SD. Third, fine-grained data collection significantly outperforms coarse-grained data collection with a little extra execution overhead. Fourth, a linear combination of SBFL and SD predicates outperforms both individual approaches. According to our empirical study, we propose a new fault localization approach, PREDFL (Predicate-based Fault Localization), with the best configuration for each dimension under the unified model. Then, we explore its complementarity to existing techniques by integrating PREDFL with a state-of-the-art fault localization framework. The experimental results show that PREDFL can further improve the effectiveness of state-of-the-art fault localization techniques. More concretely, integrating PREDFL results in an up to 20.8% improvement w.r.t the faults successfully located at Top-1, which reveals that PREDFL complements existing techniques.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00054"}, {"primary_key": "3083298", "vector": [], "sparse_vector": [], "title": "Assessing the Generalizability of Code2vec Token Embeddings.", "authors": ["<PERSON>", "Tegawendé F. Bissyandé", "<PERSON>"], "summary": "Many Natural Language Processing (NLP) tasks, such as sentiment analysis or syntactic parsing, have benefited from the development of word embedding models. In particular, regardless of the training algorithms, the learned embeddings have often been shown to be generalizable to different NLP tasks. In contrast, despite recent momentum on word embeddings for source code, the literature lacks evidence of their generalizability beyond the example task they have been trained for. In this experience paper, we identify 3 potential downstream tasks, namely code comments generation, code authorship identification, and code clones detection, that source code token embedding models can be applied to. We empirically assess a recently proposed code token embedding model, namely code2vec's token embeddings. Code2vec was trained on the task of predicting method names, and while there is potential for using the vectors it learns on other tasks, it has not been explored in literature. Therefore, we fill this gap by focusing on its generalizability for the tasks we have identified. Eventually, we show that source code token embeddings cannot be readily leveraged for the downstream tasks. Our experiments even show that our attempts to use them do not result in any improvements over less sophisticated methods. We call for more research into effective and general use of code embeddings.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00011"}, {"primary_key": "3083299", "vector": [], "sparse_vector": [], "title": "Compile-Time Detection of Machine Image Sniping.", "authors": ["<PERSON>"], "summary": "Machine image sniping is a difficult-to-detect security vulnerability in cloud computing code. When programmatically initializing a machine, a developer specifies a machine image (operating system and file system). The developer should restrict the search to only those machine images which their organization controls: otherwise, an attacker can insert a similarly-named malicious image into the public database, where it might be selected instead of the image the developer intended. We present a lightweight type and effect system that detects requests to a cloud provider that are vulnerable to an image sniping attack, or proves that no vulnerable request exists in a codebase. We prototyped our type system for Java programs that initialize Amazon Web Services machines, and evaluated it on more than 500 codebases, detecting 14 vulnerable requests with only 3 false positives.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00154"}, {"primary_key": "3083300", "vector": [], "sparse_vector": [], "title": "Model Checking Embedded Control Software using OS-in-the-Loop CEGAR.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Verification of multitasking embedded software requires taking into account its underlying operating system w.r.t. its scheduling policy and handling of task priorities in order to achieve a higher degree of accuracy. However, such comprehensive verification of multitasking embedded software together with its underlying operating system is very costly and impractical. To reduce the verification cost while achieving the desired accuracy, we propose a variant of CEGAR, named OiL-CEGAR (OS-in-the-Loop Counterexample-Guided Abstraction Refinement), where a composition of a formal OS model and an abstracted application program is used for comprehensive verification and is successively refined using the counterexamples generated from the composition model. The refinement process utilizes the scheduling information in the counterexample, which acts as a mini-OS to check the executability of the counterexample trace on the concrete program. Our experiments using a prototype implementation of OiL-CEGAR show that OiL-CEGAR greatly improves the accuracy and efficiency of property checking in this domain. It automatically removed all false alarms and accomplished property checking within an average of 476 seconds over a set of multitasking programs, whereas model checking using existing approaches over the same set of programs either showed an accuracy of under 11.1% or was unable to finish the verification due to timeout.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00059"}, {"primary_key": "3083301", "vector": [], "sparse_vector": [], "title": "Automated Refactoring to Reactive Programming.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Reactive programming languages and libraries, such as ReactiveX, have been shown to significantly improve software design and have seen important industrial adoption over the last years. Asynchronous applications - which are notoriously error-prone to implement and to maintain - greatly benefit from reactive programming because they can be defined in a declarative style, which improves code clarity and extensibility. In this paper, we tackle the problem of refactoring existing software that has been designed with traditional abstractions for asynchronous programming. We propose 2Rx, a refactoring approach to automatically convert asynchronous code to reactive programming. Our evaluation on top-starred GitHub projects shows that 2Rx is effective with the most common asynchronous constructs, covering 12.7% of projects with asynchronous computations, and it can provide a refactoring for 91.7% of their occurrences.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00082"}, {"primary_key": "3083302", "vector": [], "sparse_vector": [], "title": "Automatic Generation of Graphical User Interface Prototypes from Unrestricted Natural Language Requirements.", "authors": ["<PERSON><PERSON>"], "summary": "High-fidelity GUI prototyping provides a meaningful manner for illustrating the developers' understanding of the requirements formulated by the customer and can be used for productive discussions and clarification of requirements and expectations. However, high-fidelity prototypes are time-consuming and expensive to develop. Furthermore, the interpretation of requirements expressed in informal natural language is often error-prone due to ambiguities and misunderstandings. In this dissertation project, we will develop a methodology based on Natural Language Processing (NLP) for supporting GUI prototyping by automatically translating Natural Language Requirements (NLR) into a formal Domain-Specific Language (DSL) describing the GUI and its navigational schema. The generated DSL can be further translated into corresponding target platform prototypes and directly provided to the user for inspection. Most related systems stop after generating artifacts, however, we introduce an intelligent and automatic interaction mechanism that allows users to provide natural language feedback on generated prototypes in an iterative fashion, which accordingly will be translated into respective prototype changes.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00148"}, {"primary_key": "3083306", "vector": [], "sparse_vector": [], "title": "DIRE: A Neural Approach to Decompiled Identifier Naming.", "authors": ["<PERSON>", "Pengcheng Yin", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The decompiler is one of the most common tools for examining binaries without corresponding source code. It transforms binaries into high-level code, reversing the compilation process. Decompilers can reconstruct much of the information that is lost during the compilation process (e.g., structure and type information). Unfortunately, they do not reconstruct semantically meaningful variable names, which are known to increase code understandability. We propose the Decompiled Identifier Renaming Engine (DIRE), a novel probabilistic technique for variable name recovery that uses both lexical and structural information recovered by the decompiler. We also present a technique for generating corpora suitable for training and evaluating models of decompiled code renaming, which we use to create a corpus of 164,632 unique x86-64 binaries generated from C projects mined from GitHub. Our results show that on this corpus DIRE can predict variable names identical to the names in the original source code up to 74.3% of the time.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00064"}, {"primary_key": "3083307", "vector": [], "sparse_vector": [], "title": "FPChecker: Detecting Floating-Point Exceptions in GPU Applications.", "authors": ["Ignacio <PERSON>"], "summary": "Floating-point arithmetic is widely used in applications from several fields including scientific computing, machine learning, graphics, and finance. Many of these applications are rapidly adopting the use of GPUs to speedup computations. GPUs, however, have limited support to detect floating-point exceptions, which hinders the development of reliable applications in GPU-based systems. We present FPCHECKER, the first tool to automatically detect floating-point exceptions in GPU applications. FPCHECKER uses the clang/LLVM compiler to instrument GPU kernels and to detect exceptions at runtime. Once an exception is detected, it reports to the programmer the code location of the exception as well as other useful information. The programmer can then use this report to avoid the exception, e.g., by modifying the application algorithm or changing the input. We present the design of FPCHECKER, an evaluation of the overhead of the tool, and a real-world case scenario on which the tool is used to identify a hidden exception. The slowdown of FPCHECKER is moderate and the code is publicly available as open source.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00118"}, {"primary_key": "3083308", "vector": [], "sparse_vector": [], "title": "Goal-Driven Exploration for Android Applications.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "This paper proposes a solution for automated goal-driven exploration of Android applications - a scenario in which a user, e.g., a security auditor, needs to dynamically trigger the functionality of interest in an application, e.g., to check whether user-sensitive info is only sent to recognized third-party servers. As the auditor might need to check hundreds or even thousands of apps, manually exploring each app to trigger the desired behavior is too time-consuming to be feasible. Existing automated application exploration and testing techniques are of limited help in this scenario as well, as their goal is mostly to identify faults by systematically exploring different app paths, rather than swiftly navigating to the target functionality. The goal-driven application exploration approach proposed in this paper, called GoalExplorer, automatically generates an executable test script that directly triggers the functionality of interest. The core idea behind GoalExplorer is to first statically model the application's UI screens and transitions between these screens, producing a Screen Transition Graph (STG). Then, GoalExplorer uses the STG to guide the dynamic exploration of the application to the particular target of interest: an Android activity, API call, or a program statement. The results of our empirical evaluation on 93 benchmark applications and the 95 most popular GooglePlay applications show that the STG is substantially more accurate than other Android UI models and that GoalExplorer is able to trigger a target functionality much faster than existing application exploration techniques.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00021"}, {"primary_key": "3083309", "vector": [], "sparse_vector": [], "title": "Ares: Inferring Error Specifications through Static Analysis.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Misuse of APIs happens frequently due to misunderstanding of API semantics and lack of documentation. An important category of API-related defects is the error handling defects, which may result in security and reliability flaws. These defects can be detected with the help of static program analysis, provided that error specifications are known. The error specification of an API function indicates how the function can fail. Writing error specifications manually is time-consuming and tedious. Therefore, automatic inferring the error specification from API usage code is preferred. In this paper, we present Ares, a tool for automatic inferring error specifications for C code through static analysis. We employ multiple heuristics to identify error handling blocks and infer error specifications by analyzing the corresponding condition logic. Ares is evaluated on 19 real world projects, and the results reveal that Ares outperforms the state-of-the-art tool APEx by 37% in precision. Ares can also identify more error specifications than APEx. Moreover, the specifications inferred from Ares help find dozens of API-related bugs in well-known projects such as OpenSSL, among them 10 bugs are confirmed by developers. Video: https://youtu.be/nf1QnFAmu8Q. Repository: https://github.com/lc3412/Ares.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00130"}, {"primary_key": "3083310", "vector": [], "sparse_vector": [], "title": "SGUARD: A Feature-Based Clustering Tool for Effective Spreadsheet Defect Detection.", "authors": ["Da Li", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> Ma"], "summary": "Spreadsheets are widely used but subject to various defects. In this paper, we present SGuard to effectively detect spreadsheet defects. SGuard learns spreadsheet features to cluster cells with similar computational semantics, and then refines these clusters to recognize anomalous cells as defects. SGuard well balances the trade-off between the precision (87.8%) and recall rate (71.9%) in the defect detection, and achieves an F-measure of 0.79, exceeding existing spreadsheet defect detection techniques. We introduce the SGuard implementation and its usage by a video presentation (https://youtu.be/gNPmMvQVf5Q), and provide its public download repository (https://github.com/sheetguard/sguard).", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00122"}, {"primary_key": "3083311", "vector": [], "sparse_vector": [], "title": "MuSC: A Tool for Mutation Testing of Ethereum Smart Contract.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The smart contract cannot be modified when it has been deployed on a blockchain. Therefore, it must be given thorough test before its being deployed. Mutation testing is considered as a practical test methodology to evaluate the adequacy of software testing. In this paper, we introduce MuSC, a mutation testing tool for Ethereum Smart Contract (ESC). It can generate numerous mutants at a fast speed and supports the automatic operations such as creating test nets, deploying and executing tests. Specially, MuSC implements a set of novel mutation operators w.r.t ESC programming language, Solidity. Therefore, it can expose the defects of smart contracts to a certain degree. The demonstration video of MuSC is available at https: //youtu.be/3KBKXJPVjbQ, and the source code can be downloaded at https://github.com/belikout/MuSC-Tool-Demo-repo.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00136"}, {"primary_key": "3083312", "vector": [], "sparse_vector": [], "title": "Humanoid: A Deep Learning-Based Approach to Automated Black-box Android App Testing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Automated input generators must constantly choose which UI element to interact with and how to interact with it, in order to achieve high coverage with a limited time budget. Currently, most black-box input generators adopt pseudo-random or brute-force searching strategies, which may take very long to find the correct combination of inputs that can drive the app into new and important states. We propose Humanoid, an automated black-box Android app testing tool based on deep learning. The key technique behind Humanoid is a deep neural network model that can learn how human users choose actions based on an app's GUI from human interaction traces. The learned model can then be used to guide test input generation to achieve higher coverage. Experiments on both open-source apps and market apps demonstrate that Humanoid is able to reach higher coverage, and faster as well, than the state-of-the-art test input generators. Humanoid is open-sourced at https://github.com/yzygitzh/Humanoid and a demo video can be found at https://youtu.be/PDRxDrkyORs.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00104"}, {"primary_key": "3083313", "vector": [], "sparse_vector": [], "title": "Test Transfer Across Mobile Apps Through Semantic Mapping.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "GUI-based testing has been primarily used to examine the functionality and usability of mobile apps. Despite the numerous GUI-based test input generation techniques proposed in the literature, these techniques are still limited by (1) lack of context-aware text inputs; (2) failing to generate expressive tests; and (3) absence of test oracles. To address these limitations, we propose CraftDroid, a framework that leverages information retrieval, along with static and dynamic analysis techniques, to extract the human knowledge from an existing test suite for one app and transfer the test cases and oracles to be used for testing other apps with the similar functionalities. Evaluation of CraftDroid on real-world commercial Android apps corroborates its effectiveness by achieving 73% precision and 90% recall on average for transferring both the GUI events and oracles. In addition, 75% of the attempted transfers successfully generated valid and feature-based tests for popular features among apps in the same category.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00015"}, {"primary_key": "3083314", "vector": [], "sparse_vector": [], "title": "Automatic Generation of Pull Request Descriptions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Enabled by the pull-based development model, developers can easily contribute to a project through pull requests (PRs). When creating a PR, developers can add a free-form description to describe what changes are made in this PR and/or why. Such a description is helpful for reviewers and other developers to gain a quick understanding of the PR without touching the details and may reduce the possibility of the PR being ignored or rejected. However, developers sometimes neglect to write descriptions for PRs. For example, in our collected dataset with over 333K PRs, more than 34% of the PR descriptions are empty. To alleviate this problem, we propose an approach to automatically generate PR descriptions based on the commit messages and the added source code comments in the PRs. We regard this problem as a text summarization problem and solve it using a novel sequence-to-sequence model. To cope with out-of-vocabulary words in software artifacts and bridge the gap between the training loss function of the sequence-to-sequence model and the evaluation metric ROUGE, which has been shown to correspond to human evaluation, we integrate the pointer generator and directly optimize for ROUGE using reinforcement learning and a special loss function. We build a dataset with over 41K PRs and evaluate our approach on this dataset through ROUGE and a human evaluation. Our evaluation results show that our approach outperforms two baselines by significant margins.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00026"}, {"primary_key": "3083315", "vector": [], "sparse_vector": [], "title": "FogWorkflowSim: An Automated Simulation Toolkit for Workflow Performance Evaluation in Fog Computing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Workflow underlies most process automation software, such as those for product lines, business processes, and scientific computing. However, current Cloud Computing based workflow systems cannot support real-time applications due to network latency, which limits their application in many IoT systems such as smart healthcare and smart traffic. Fog Computing extends the Cloud by providing virtualized computing resources close to the End Devices so that the response time of accessing computing resources can be reduced significantly. However, how to most effectively manage heterogeneous resources and different computing tasks in the Fog is a big challenge. In this paper, we introduce \"FogWorkflowSim\" an efficient and extensible toolkit for automatically evaluating resource and task management strategies in Fog Computing with simulated user-defined workflow applications. Specifically, FogWorkflowSim is able to: 1) automatically set up a simulated Fog Computing environment for workflow applications; 2) automatically execute user submitted workflow applications; 3) automatically evaluate and compare the performance of different computation offloading and task scheduling strategies with three basic performance metrics, including time, energy and cost. FogWorkflowSim can serve as an effective experimental platform for researchers in Fog based workflow systems as well as practitioners interested in adopting Fog Computing and workflow systems for their new software projects. (Demo video: https://youtu.be/AsMovcuSkx8).", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00115"}, {"primary_key": "3083316", "vector": [], "sparse_vector": [], "title": "Predicting Licenses for Changed Source Code.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Ge", "<PERSON>"], "summary": "Open source software licenses regulate the circumstances under which software can be redistributed, reused and modified. Ensuring license compatibility and preventing license restriction conflicts among source code during software changes are the key to protect their commercial use. However, selecting the appropriate licenses for software changes requires lots of experience and manual effort that involve examining, assimilating and comparing various licenses as well as understanding their relationships with software changes. Worse still, there is no state-of-the-art methodology to provide this capability. Motivated by this observation, we propose in this paper Automatic License Prediction (ALP), a novel learning-based method and tool for predicting licenses as software changes. An extensive evaluation of ALP on predicting licenses in 700 open source projects demonstrate its effectiveness: ALP can achieve not only a high overall prediction accuracy (92.5% in micro F1 score) but also high accuracies across all license types.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00070"}, {"primary_key": "3083317", "vector": [], "sparse_vector": [], "title": "Verifying Arithmetic in Cryptographic C Programs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cryptographic primitives are ubiquitous for modern security. The correctness of their implementations is crucial to resist malicious attacks. Typical arithmetic computation of these C programs contains large numbers of non-linear operations, hence is challenging existing automatic C verification tools. We present an automated approach to verify cryptographic C programs. Our approach successfully verifies C implementations of various arithmetic operations used in NIST P-224, P-256, P-521 and Curve25519 in OpenSSL. During verification, we expose a bug and a few anomalies that have been existing for a long time. They have been reported to and confirmed by the OpenSSL community. Our results establish the functional correctness of these C implementations for the first time.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00058"}, {"primary_key": "3083318", "vector": [], "sparse_vector": [], "title": "DaPanda: Detecting Aggressive Push Notifications in Android Apps.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Guangdong Bai", "<PERSON>", "<PERSON><PERSON>"], "summary": "Mobile push notifications have been widely used in mobile platforms to deliver all sorts of information to app users. Although it offers great convenience for both app developers and mobile users, this feature was frequently reported to serve malicious and aggressive purposes, such as delivering annoying push notification advertisement. However, to the best of our knowledge, this problem has not been studied by our research community so far. To fill the void, this paper presents the first study to detect aggressive push notifications and further characterize them in the global mobile app ecosystem on a large scale. To this end, we first provide a taxonomy of mobile push notifications and identify the aggressive ones using a crowdsourcing-based method. Then we propose sc DaPanda, a novel hybrid approach, aiming at automatically detecting aggressive push notifications in Android apps. sc DaPanda leverages a guided testing approach to systematically trigger and record push notifications. By instrumenting the Android framework, sc DaPanda further collects all notification-relevant runtime information to flag the aggressive ones. Our experimental results show that sc DaPanda is capable of detecting different types of aggressive push notifications effectively in an automated way. By applying sc DaPanda to 20,000 Android apps from different app markets, it yields over 1,000 aggressive notifications, which have been further confirmed as true positives. Our in-depth analysis further reveals that aggressive notifications are prevalent across different markets and could be manifested in all the phases in the lifecycle of push notifications. It is hence urgent for our community to take actions to detect and mitigate apps involving aggressive push notifications.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00017"}, {"primary_key": "3083319", "vector": [], "sparse_vector": [], "title": "Logzip: Extracting Hidden Structures via Iterative Clustering for Log Compression.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "System logs record detailed runtime information of software systems and are used as the main data source for many tasks around software engineering. As modern software systems are evolving into large scale and complex structures, logs have become one type of fast-growing big data in industry. In particular, such logs often need to be stored for a long time in practice (e.g., a year), in order to analyze recurrent problems or track security issues. However, archiving logs consumes a large amount of storage space and computing resources, which in turn incurs high operational cost. Data compression is essential to reduce the cost of log storage. Traditional compression tools (e.g., gzip) work well for general texts, but are not tailed for system logs. In this paper, we propose a novel and effective log compression method, namely logzip. Logzip is capable of extracting hidden structures from raw logs via fast iterative clustering and further generating coherent intermediate representations that allow for more effective compression. We evaluate logzip on five large log datasets of different system types, with a total of 63.6 GB in size. The results show that logzip can save about half of the storage space on average over traditional compression tools. Meanwhile, the design of logzip is highly parallel and only incurs negligible overhead. In addition, we share our industrial experience of applying logzip to Huawei's real products.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00085"}, {"primary_key": "3083320", "vector": [], "sparse_vector": [], "title": "Generating Tests to Analyse Dynamically-Typed Programs.", "authors": ["<PERSON>"], "summary": "The increasing popularity of dynamically-typed programming languages, such as JavaScript or Python, requires specific support methods for developers to avoid pitfalls arising from the dynamic nature of these languages. Static analyses are frequently used but the dynamic type systems limit their applicability. Dynamic analyses, in contrast, depend on the execution of the code under analysis, and thus depend on the quality of existing tests. This quality of the test suite can be improved by the use of automated test generation but automated test generation for dynamically-typed programming languages itself is hard due to the lack of type information in the programs. The limitations of each of these approaches will be overcome by iteratively combining test generation with static and dynamic analysis techniques for dynamically-typed programs.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00146"}, {"primary_key": "3083321", "vector": [], "sparse_vector": [], "title": "A Qualitative Analysis of Android Taint-Analysis Results.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In the past, researchers have developed a number of popular taint-analysis approaches, particularly in the context of Android applications. Numerous studies have shown that automated code analyses are adopted by developers only if they yield a good \"signal to noise ratio\", i.e., high precision. Many previous studies have reported analysis precision quantitatively, but this gives little insight into what can and should be done to increase precision further. To guide future research on increasing precision, we present a comprehensive study that evaluates static Android taint-analysis results on a qualitative level. To unravel the exact nature of taint flows, we have designed COVA, an analysis tool to compute partial path constraints that inform about the circumstances under which taint flows may actually occur in practice. We have conducted a qualitative study on the taint flows reported by FlowDroid in 1,022 real-world Android applications. Our results reveal several key findings: Many taint flows occur only under specific conditions, e.g., environment settings, user interaction, I/O. Taint analyses should consider the application context to discern such situations. COVA shows that few taint flows are guarded by multiple different kinds of conditions simultaneously, so tools that seek to confirm true positives dynamically can concentrate on one kind at a time, e.g., only simulating user interactions. Lastly, many false positives arise due to a too liberal source/sink configuration. Taint analyses must be more carefully configured, and their configuration could benefit from better tool assistance.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00020"}, {"primary_key": "3083322", "vector": [], "sparse_vector": [], "title": "SPrinter: A Static Checker for Finding Smart Pointer Errors in C++ Programs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Smart pointers are widely used to prevent memory errors in modern C++ code. However, improper usage of smart pointers may also lead to common memory errors, which makes the code not as safe as expected. To avoid smart pointer errors as early as possible, we present a coding style checker to detect possible bad smart pointer usages during compile time, and notify programmers about bug-prone behaviors. The evaluation indicates that the currently available state-of-the-art static code checkers can only detect 25 out of 116 manually inserted errors, while our tool can detect all these errors. And we also found 521 bugs among 8 open source projects with only 4 false positives.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00117"}, {"primary_key": "3083323", "vector": [], "sparse_vector": [], "title": "Cautious Adaptation of Defiant Components.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Systems-of-systems are formed by the composition of independently created software components. These components are designed to satisfy their individual requirements, rather than the global requirements of the systems-of-systems. We refer to components that cannot be adapted to meet both individual and global requirements as \"defiant\" components. In this paper, we propose a \"cautious\" adaptation approach which supports changing the behaviour of such defiant components under exceptional conditions to satisfy global requirements, while continuing to guarantee the satisfaction of the components' individual requirements. The approach represents both normal and exceptional conditions as scenarios; models the behaviour of exceptional conditions as wrappers implemented using an aspect-oriented technique; and deals with both single and multiple instances of defiant components with different precedence order at runtime. We evaluated an implementation of the approach using drones and boats for an organ delivery application conceived by our industrial partners, in which we assess how the proposed approach help achieve the system-of-systems' global requirements while accommodating increased complexity of hybrid aspects such as multiplicity, precedence ordering, openness and heterogeneity.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00094"}, {"primary_key": "3083324", "vector": [], "sparse_vector": [], "title": "XRaSE: Towards Virtually Tangible Software using Augmented Reality.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Software engineering has seen much progress in recent past including introduction of new methodologies, new paradigms for software teams, and from smaller monolithic applications to complex, intricate, and distributed software applications. However, the way we represent, discuss, and collaborate on software applications throughout the software development life cycle is still primarily using the source code, textual representations, or charts on 2D computer screens - the confines of which have long limited how we visualize and comprehend software systems. In this paper, we present XRaSE, a novel prototype implementation that leverages augmented reality to visualize a software application as a virtually tangible entity. This immersive approach is aimed at making activities like application comprehension, architecture analysis, knowledge communication, and analysis of a software's dynamic aspects, more intuitive, richer and collaborative.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00135"}, {"primary_key": "3083325", "vector": [], "sparse_vector": [], "title": "CONVUL: An Effective Tool for Detecting Concurrency Vulnerabilities.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Haicheng Li", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Concurrency vulnerabilities are extremely harmful and can be frequently exploited to launch severe attacks. Due to the non-determinism of multithreaded executions, it is very difficult to detect them. Recently, data race detectors and techniques based on maximal casual model have been applied to detect concurrency vulnerabilities. However, the former are ineffective and the latter report many false negatives. In this paper, we present CONVUL, an effective tool for concurrency vulnerability detection. CONVUL is based on exchangeable events, and adopts novel algorithms to detect three major kinds of concurrency vulnerabilities. In our experiments, CONVUL detected 9 of 10 known vulnerabilities, while other tools only detected at most 2 out of these 10 vulnerabilities. The 10 vulnerabilities are available at https://github.com/mryancai/ConVul.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00125"}, {"primary_key": "3083326", "vector": [], "sparse_vector": [], "title": "Regexes are Hard: Decision-Making, Difficulties, and Risks in Programming Regular Expressions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Regular expressions (regexes) are a powerful mechanism for solving string-matching problems. They are supported by all modern programming languages, and have been estimated to appear in more than a third of Python and JavaScript projects. Yet existing studies have focused mostly on one aspect of regex programming: readability. We know little about how developers perceive and program regexes, nor the difficulties that they face. In this paper, we provide the first study of the regex development cycle, with a focus on (1) how developers make decisions throughout the process, (2) what difficulties they face, and (3) how aware they are about serious risks involved in programming regexes. We took a mixed-methods approach, surveying 279 professional developers from a diversity of backgrounds (including top tech firms) for a high-level perspective, and interviewing 17 developers to learn the details about the difficulties that they face and the solutions that they prefer. In brief, regexes are hard. Not only are they hard to read, our participants said that they are hard to search for, hard to validate, and hard to document. They are also hard to master: the majority of our studied developers were unaware of critical security risks that can occur when using regexes, and those who knew of the risks did not deal with them in effective manners. Our findings provide multiple implications for future work, including semantic regex search engines for regex reuse and improved input generators for regex validation.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00047"}, {"primary_key": "3083331", "vector": [], "sparse_vector": [], "title": "Manticore: A User-Friendly Symbolic Execution Framework for Binaries and Smart Contracts.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "An effective way to maximize code coverage in software tests is through dynamic symbolic execution-a technique that uses constraint solving to systematically explore a program's state space. We introduce an open-source dynamic symbolic execution framework called Manticore for analyzing binaries and Ethereum smart contracts. Manticore's flexible architecture allows it to support both traditional and exotic execution environments, and its API allows users to customize their analysis. Here, we discuss Manticore's architecture and demonstrate the capabilities we have used to find bugs and verify the correctness of code for our commercial clients.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00133"}, {"primary_key": "3083332", "vector": [], "sparse_vector": [], "title": "RENN: Efficient Reverse Execution with Neural-Network-Assisted Alias Analysis.", "authors": ["<PERSON><PERSON><PERSON> Mu", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Reverse execution and coredump analysis have long been used to diagnose the root cause of software crashes. Each of these techniques, however, face inherent challenges, such as insufficient capability when handling memory aliases. Recent works have used hypothesis testing to address this drawback, albeit with high computational complexity, making them impractical for real world applications. To address this issue, we propose a new deep neural architecture, which could significantly improve memory alias resolution. At the high level, our approach employs a recurrent neural network (RNN) to learn the binary code pattern pertaining to memory accesses. It then infers the memory region accessed by memory references. Since memory references to different regions naturally indicate a non-alias relationship, our neural architecture can greatly reduce the burden of doing hypothesis testing to track down non-alias relation in binary code. Different from previous researches that have utilized deep learning for other binary analysis tasks, the neural network proposed in this work is fundamentally novel. Instead of simply using off-the-shelf neural networks, we designed a new recurrent neural architecture that could capture the data dependency between machine code segments. To demonstrate the utility of our deep neural architecture, we implement it as RENN, a neural network-assisted reverse execution system. We utilize this tool to analyze software crashes corresponding to 40 memory corruption vulnerabilities from the real world. Our experiments show that RENN can significantly improve the efficiency of locating the root cause for the crashes. Compared to a state-of-the-art technique, RENN has 36.25% faster execution time on average, detects an average of 21.35% more non-alias pairs, and successfully identified the root cause of 12.5% more cases.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00090"}, {"primary_key": "3083333", "vector": [], "sparse_vector": [], "title": "Verifying Determinism in Sequential Programs.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "A nondeterministic program is difficult to test and debug. Nondeterminism occurs even in sequential programs: for example, iterating over the elements of a hash table can result in diverging test results. We have created a type system that can express whether a computation is deterministic, nondeterministic, or ordernondeterministic (like a set). While state-of-the-art nondeterminism detection tools unsoundly rely on observing run-time output, our approach soundly verifies determinism at compile time. Our implementation found previously-unknown nondeterminism errors in a 24,000 line program that had been heavily vetted by its developers.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00159"}, {"primary_key": "3083334", "vector": [], "sparse_vector": [], "title": "Accurate Modeling of Performance Histories for Evolving Software Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Learning from the history of a software system's performance behavior does not only help discovering and locating performance bugs, but also identifying evolutionary performance patterns and general trends, such as when technical debt accumulates. Exhaustive regression testing is usually impractical, because rigorous performance benchmarking requires executing a realistic workload per revision, which results in large execution times. In this paper, we propose a novel active revision sampling approach, which aims at tracking and understanding a system's performance history by approximating the performance behavior of a software system across all of its revisions. In a nutshell, we iteratively sample and measure the performance of specific revisions that help us building an exact performance-evolution model, and we use Gaussian Process models to assess in which revision ranges our model is most uncertain with the goal to sample further revisions for measurement. We have conducted an empirical analysis of the evolutionary performance behavior modeled as a time series of the histories of six real-world software systems. Our evaluation demonstrates that Gaussian Process models are able to accurately estimate the performance-evolution history of real-world software systems with only few measurements and to reveal interesting behaviors and trends.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00065"}, {"primary_key": "3083336", "vector": [], "sparse_vector": [], "title": "CLCDSA: Cross Language Code Clone Detection using Syntactical Features and API Documentation.", "authors": ["<PERSON><PERSON><PERSON> Wazed Nafi", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Software clones are detrimental to software maintenance and evolution and as a result many clone detectors have been proposed. These tools target clone detection in software applications written in a single programming language. However, a software application may be written in different languages for different platforms to improve the application's platform compatibility and adoption by users of different platforms. Cross language clones (CLCs) introduce additional challenges when maintaining multi-platform applications and would likely go undetected using existing tools. In this paper, we propose CLCDSA, a cross language clone detector which can detect CLCs without extensive processing of the source code and without the need to generate an intermediate representation. The proposed CLCDSA model analyzes different syntactic features of source code across different programming languages to detect CLCs. To support large scale clone detection, the CLCDSA model uses an action filter based on cross language API call similarity to discard non-potential clones. The design methodology of CLCDSA is two-fold: (a) it detects CLCs on the fly by comparing the similarity of features, and (b) it uses a deep neural network based feature vector learning model to learn the features and detect CLCs. Early evaluation of the model observed an average precision, recall and F-measure score of 0.55, 0.86, and 0.64 respectively for the first phase and 0.61, 0.93, and 0.71 respectively for the second phase which indicates that CLCDSA outperforms all available models in detecting cross language clones.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00099"}, {"primary_key": "3083337", "vector": [], "sparse_vector": [], "title": "API Design Implications of Boilerplate Client Code.", "authors": ["<PERSON><PERSON>"], "summary": "Designing usable APIs is critical to developers' productivity and software quality but is quite difficult. In this paper, I focus on \"boilerplate\" code, sections of code that have to be included in many places with little or no alteration, which many experts in API design have said can be an indicator of API usability problems. I investigate what properties make code count as boilerplate, and present a novel approach to automatically mine boilerplate code from a large set of client code. The technique combines an existing API usage mining algorithm, with novel filters using AST comparison and graph partitioning. With boilerplate candidates identified by the technique, I discuss how this technique could help API designers in reviewing their design decisions and identifying usability issues.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00153"}, {"primary_key": "3083338", "vector": [], "sparse_vector": [], "title": "MARBLE: Mining for Boilerplate Code to Identify API Usability Problems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Designing usable APIs is critical to developers' productivity and software quality, but is quite difficult. One of the challenges is that anticipating API usability barriers and real-world usage is difficult, due to a lack of automated approaches to mine usability data at scale. In this paper, we focus on one particular grievance that developers repeatedly express in online discussions about APIs: \"boilerplate code.\" We investigate what properties make code count as boilerplate, the reasons for boilerplate, and how programmers can reduce the need for it. We then present MARBLE, a novel approach to automatically mine boilerplate code candidates from API client code repositories. MARBLE adapts existing techniques, including an API usage mining algorithm, an AST comparison algorithm, and a graph partitioning algorithm. We evaluate MARBLE with 13 Java APIs, and show that our approach successfully identifies both already-known and new API-related boilerplate code instances.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00063"}, {"primary_key": "3083339", "vector": [], "sparse_vector": [], "title": "A Study of Oracle Approximations in Testing Deep Learning Libraries.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Due to the increasing popularity of deep learning (DL) applications, testing DL libraries is becoming more and more important. Different from testing general software, for which output is often asserted definitely (e.g., an output is compared with an oracle for equality), testing deep learning libraries often requires to perform oracle approximations, i.e., the output is allowed to be within a restricted range of the oracle. However, oracle approximation practices have not been studied in prior empirical work that focuses on traditional testing practices. The prevalence, common practices, maintenance and evolution challenges of oracle approximations remain unknown in literature. In this work, we study oracle approximation assertions implemented to test four popular DL libraries. Our study shows that there exists a non-negligible portion of assertions that leverage oracle approximation in the test cases of DL libraries. Also, we identify the common sources of oracles on which oracle approximations are being performed through a comprehensive manual study. Moreover, we find that developers frequently modify code related to oracle approximations, i.e., using a different approximation API, modifying the oracle or the output from the code under test, and using a different approximation threshold. Last, we performed an in-depth study to understand the reasons behind the evolution of oracle approximation assertions. Our findings reveal important maintenance challenges that developers may face when maintaining oracle approximation practices as code evolves in DL libraries.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00078"}, {"primary_key": "3083341", "vector": [], "sparse_vector": [], "title": "An Approach for Investigating Emotion Dynamics in Software Development.", "authors": ["<PERSON>"], "summary": "Emotion awareness is critical to interpersonal communication, including that in software development. The SE community has studied emotion in software development using isolated emotion states but it has not considered the dynamic nature of emotion. To investigate the emotion dynamics, SE community needs an effective approach. In this paper, we propose such an approach which can automatically collect project teams' communication records, identify the emotions and their intensities in them, model the emotion dynamics into time series, and provide efficient data management. We demonstrate that this approach can provide end-to-end support for various emotion awareness research and practices through automated data collection, modeling, storage, analysis, and presentation using the IPython's project data on GitHub.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00158"}, {"primary_key": "3083342", "vector": [], "sparse_vector": [], "title": "Combining Program Analysis and Statistical Language Model for Code Statement Completion.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Automatic code completion helps improve developers' productivity in their programming tasks. A program contains instructions expressed via code statements, which are considered as the basic units of program execution. In this paper, we introduce AutoSC, which combines program analysis and the principle of software naturalness to fill in a partially completed statement. AutoSC benefits from the strengths of both directions, in which the completed code statement is both frequent and valid. AutoSC is first trained on a large code corpus to derive the templates of candidate statements. Then, it uses program analysis to validate and concretize the templates into syntactically and type-valid candidate statements. Finally, these candidates are ranked by using a language model trained on the lexical form of the source code in the code corpus. Our empirical evaluation on the large datasets of real-world projects shows that AutoSC achieves 38.9-41.3% top-1 accuracy and 48.2-50.1% top-5 accuracy in statement completion. It also outperforms a state-of-the-art approach from 9X-69X in top-1 accuracy.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00072"}, {"primary_key": "3083343", "vector": [], "sparse_vector": [], "title": "Feature-Interaction Aware Configuration Prioritization for Configurable Code.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Hieu Tran", "<PERSON><PERSON>"], "summary": "Unexpected interactions among features induce most bugs in a configurable software system. Exhaustively analyzing all the exponential number of possible configurations is prohibitively costly. Thus, various sampling techniques have been proposed to systematically narrow down the exponential number of legal configurations to be analyzed. Since analyzing all selected configurations can require a huge amount of effort, fault-based configuration prioritization, that helps detect faults earlier, can yield practical benefits in quality assurance. In this paper, we propose CoPro, a novel formulation of feature-interaction bugs via common program entities enabled/disabled by the features. Leveraging from that, we develop an efficient feature-interaction-aware configuration prioritization technique for a configurable system by ranking the configurations according to their total number of potential bugs. We conducted several experiments to evaluate CoPro on the ability to detect configuration-related bugs in a public benchmark. We found that CoPro outperforms the state-of-the-art configuration prioritization techniques when we add them on advanced sampling algorithms. In 78% of the cases, CoPro ranks the buggy configurations at the top 3 positions in the resulting list. Interestingly, CoPro is able to detect 17 not-yet-discovered feature-interaction bugs.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00053"}, {"primary_key": "3083344", "vector": [], "sparse_vector": [], "title": "Fine-Grain Memory Object Representation in Symbolic Execution.", "authors": ["<PERSON>"], "summary": "Dynamic Symbolic Execution (DSE) has seen rising popularity as it allows to check applications for behaviours such as error patterns automatically. One of its biggest challenges is the state space explosion problem: DSE tries to evaluate all possible execution paths of an application. For every path, it needs to represent the allocated memory and its accesses. Even though different approaches have been proposed to mitigate the state space explosion problem, DSE still needs to represent a multitude of states in parallel to analyse them. If too many states are present, they cannot fit into memory, and DSE needs to terminate them prematurely or store them on disc intermediately. With a more efficient representation of allocated memory, DSE can handle more states simultaneously, improving its performance. In this work, we introduce an enhanced, fine-grain and efficient representation of memory that mimics the allocations of tested applications. We tested Coreutils using three different search strategies with our implementation on top of the symbolic execution engine KLEE. We achieve a significant reduction of the memory consumption of states by up to 99.06% (mean DFS: 2%, BFS: 51%, Cov.: 49%), allowing to represent more states in memory more efficiently. The total execution time is reduced by up to 97.81% (mean DFS: 9%, BFS: 7%, Cov.:4%)-a speedup of 49x in comparison to baseline KLEE.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00089"}, {"primary_key": "3083345", "vector": [], "sparse_vector": [], "title": "Grading-Based Test Suite Augmentation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Enrollment in introductory programming (CS1) courses continues to surge and hundreds of CS1 students can produce thousands of submissions for a single problem, all requiring timely and accurate grading. One way that instructors can efficiently grade is to construct a custom instructor test suite that compares a student submission to a reference solution over randomly generated or hand-crafted inputs. However, such test suite is often insufficient, causing incorrect submissions to be marked as correct. To address this issue, we propose the Grasa (GRAding-based test Suite Augmentation) approach consisting of two techniques. Grasa first detects and clusters incorrect submissions by approximating their behavioral equivalence to each other. To augment the existing instructor test suite, <PERSON>rasa generates a minimal set of additional tests that help detect the incorrect submissions. We evaluate our Grasa approach on a dataset of CS1 student submissions for three programming problems. Our preliminary results show that <PERSON><PERSON><PERSON> can effectively identify incorrect student submissions and minimally augment the instructor test suite.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00030"}, {"primary_key": "3083346", "vector": [], "sparse_vector": [], "title": "Improving the Decision-Making Process of Self-Adaptive Systems by Accounting for Tactic Volatility.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "When self-adaptive systems encounter changes withintheir surrounding environments, they enacttacticsto performnecessary adaptations. For example, a self-adaptive cloud-basedsystem may have a tactic that initiates additional computingresources when response time thresholds are surpassed, or theremay be a tactic to activate a specific security measure when anintrusion is detected. In real-world environments, these tacticsfrequently experiencetactic volatilitywhich is variable behaviorduring the execution of the tactic.Unfortunately, current self-adaptive approaches do not accountfor tactic volatility in their decision-making processes, and merelyassume that tactics do not experience volatility. This limitationcreates uncertainty in the decision-making process and mayadversely impact the system's ability to effectively and efficientlyadapt. Additionally, many processes do not properly account forvolatility that may effect the system's Service Level Agreement(SLA). This can limit the system's ability to act proactively, especially when utilizing tactics that contain latency.To address the challenge of sufficiently accounting for tacticvolatility, we propose aTactic Volatility Aware(TVA) solution.Using Multiple Regression Analysis (MRA), TVA enables self-adaptive systems to accurately estimate the cost and timerequired to execute tactics. TVA also utilizesAutoregressiveIntegrated Moving Average(ARIMA) for time series forecasting, allowing the system to proactively maintain specifications.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00092"}, {"primary_key": "3083348", "vector": [], "sparse_vector": [], "title": "Debreach: Mitigating Compression Side Channels via Static Analysis and Transformation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Compression is an emerging source of exploitable side-channel leakage that threatens data security, particularly in web applications where compression is indispensable for performance reasons. Current approaches to mitigating compression side channels have drawbacks in that they either degrade compression ratio drastically or require too much effort from developers to be widely adopted. To bridge the gap, we develop Debreach, a static analysis and program transformation based approach to mitigating compression side channels. Debreach consists of two steps. First, it uses taint analysis to soundly identify flows of sensitive data in the program and uses code instrumentation to annotate data before feeding them to the compressor. Second, it enhances the compressor to exploit the freedom to not compress of standard compression protocols, thus removing the dependency between sensitive data and the size of the compressor's output. Since Debreach automatically instruments applications and does not change the compression protocols, it has the advantage of being non-disruptive and compatible with existing systems. We have evaluated <PERSON>breach on a set of web server applications written in PHP. Our experiments show that, while ensuring leakage-freedom, <PERSON>breach can achieve significantly higher compression performance than state-of-the-art approaches.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00088"}, {"primary_key": "3083349", "vector": [], "sparse_vector": [], "title": "SWAN_ASSIST: Semi-Automated Detection of Code-Specific, Security-Relevant Methods.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "To detect specific types of bugs and vulnerabilities, static analysis tools must be correctly configured with security-relevant methods (SRM), e.g., sources, sinks, sanitizers and authentication methods-usually a very labour-intensive and error-prone process. This work presents the semi-automated tool SWAN_ASSIST, which aids the configuration with an IntelliJ plugin based on active machine learning. It integrates our novel automated machine-learning approach SWAN, which identifies and classifies Java SRM. SWAN_ASSIST further integrates user feedback through iterative learning. SWAN_ASSIST aids developers by asking them to classify at each point in time exactly those methods whose classification best impact the classification result. Our experiments show that SWAN_ASSIST classifies SRM with a high precision, and requires a relatively low effort from the user. A video demo of SWAN_ASSIST can be found at https://youtu.be/fSyD3V6EQOY. The source code is available at https://github.com/secure-software-engineering/swan.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00110"}, {"primary_key": "3083350", "vector": [], "sparse_vector": [], "title": "Efficient Transaction-Based Deterministic Replay for Multi-threaded Programs.", "authors": ["<PERSON>", "Xiupei Mei", "<PERSON><PERSON>"], "summary": "Existing deterministic replay techniques propose strategies which attempt to reduce record log sizes and achieve successful replay. However, these techniques still generate large logs and achieve replay only under certain conditions. We propose a solution based on the division of the sequence of events of each thread into sequential blocks called transactions. Our insight is that there are usually few to no atomicity violations among transactions reported during a program execution. We present TPLAY, a novel deterministic replay technique which records thread access interleavings on shared memory locations at the transactional level. TPLAY also generates an artificial pair of interleavings when an atomicity violation is reported on a transaction. We present an experiment using the Splash2x extension of the PARSEC benchmark suite. Experimental results indicate that TPLAY experiences a 13-fold improvement in record log sizes and achieves a higher replay probability in comparison to existing work.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00076"}, {"primary_key": "3083351", "vector": [], "sparse_vector": [], "title": "OAUTHLINT: An Empirical Study on OAuth Bugs in Android Applications.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Mobile developers use OAuth APIs to implement Single-Sign-On services. However, the OAuth protocol was originally designed for the authorization for third-party websites not to authenticate users in third-party mobile apps. As a result, it is challenging for developers to correctly implement mobile OAuth securely. These vulnerabilities due to the misunderstanding of OAuth and inexperience of developers could lead to data leakage and account breach. In this paper, we perform an empirical study on the usage of OAuth APIs in Android applications and their security implications. In particular, we develop OAUTHLINT, that incorporates a query-driven static analysis to automatically check programs on the Google Play marketplace. OAUTHLINT takes as input an anti-protocol that encodes a vulnerable pattern extracted from the OAuth specifications and a program P. Our tool then generates a counter-example if the anti-protocol can match a trace of Ps possible executions. To evaluate the effectiveness of our approach, we perform a systematic study on 600+ popular apps which have more than 10 millions of downloads. The evaluation shows that 101 (32%) out of 316 applications that use OAuth APIs make at least one security mistake.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00036"}, {"primary_key": "3083352", "vector": [], "sparse_vector": [], "title": "User Preference Aware Multimedia Pricing Model using Game Theory and Prospect Theory for Wireless Communications.", "authors": ["<PERSON>"], "summary": "Providing user satisfaction is a major concern for on-demand multimedia service providers and Internet carriers in Wireless Communications. Traditionally, user satisfaction was measured objectively in terms of throughput and latency. Nowadays the user satisfaction is measured using subjective metrices such as Quality of Experience (QoE). Recently, Smart Media Pricing (SMP) was conceptualized to price the QoE rather than the binary data traffic in multimedia services. In this research, we have leveraged the SMP concept to chalk up a QoE-sensitive multimedia pricing framework to allot price, based on the user preference and multimedia quality achieved by the customer. We begin by defining the utility equations for the provider-carrier and the customer. Then we translate the profit maximizing interplay between the parties into a two-stage Stackelberg game. We model the user personal preference using Prelec weighting function which follows the postulates Prospect Theory (PT). An algorithm has been developed to implement the proposed pricing scheme and determine the Nash Equilibrium. Finally, the proposed smart pricing scheme was tested against the traditional pricing method and simulation results indicate a significant boost in the utility achieved by the mobile customers.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00157"}, {"primary_key": "3083353", "vector": [], "sparse_vector": [], "title": "Get Rid of Inline Assembly through Verification-Oriented Lifting.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Formal methods for software development have made great strides in the last two decades, to the point that their application in safety-critical embedded software is an undeniable success. Their extension to non-critical software is one of the notable forthcoming challenges. For example, C programmers regularly use inline assembly for low-level optimizations and system primitives. This usually results in rendering state-of-the-art formal analyzers developed for C ineffective. We thus propose TINA, the first automated, generic, verification-friendly and trustworthy lifting technique turning inline assembly into semantically equivalent C code amenable to verification, in order to take advantage of existing C analyzers. Extensive experiments on real-world code (including GMP and ffmpeg) show the feasibility and benefits of TINA.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00060"}, {"primary_key": "3083354", "vector": [], "sparse_vector": [], "title": "Inference of Properties from Requirements and Automation of Their Formal Verification.", "authors": ["<PERSON>"], "summary": "Over the past decades, various techniques for the application of formal program analysis of software for embedded systems have been proposed. However, the application of formal methods for software verification is still limited in practise. It is acknowledged that the task of formally stating requirements by specifying the formal properties is a major hindrance. The verification step itself has its shortcoming in its scalability and its limitation to predefined proof tactics in case of automated theorem proving (ATP). These constraints are reduced today by the interaction of the user with the theorem prover (TP) during the execution of the proof. However, this is difficult for non-experts. The objectives of the presented PhD project are the automated inference of declarative property specifications from example data specified by the engineer for a function under development and their automated verification on abstract model level and on code level. We propose the meta-model for Scenario Modeling Language (SML) that allows to specify example data. For the automated property generation we are motivated by Inductive Logic Programming (ILP) techniques for first-order logic in pure mathematics. We propose modifications to its algorithm that allow to process the information that is incorporated in the meta-model of SML. However, this technique is expected to produce too many uninteresting properties. To turn this weakness into strength, our approach proposes to tailor the algorithm towards selection of the right properties that facilitate the automation of the proof. Automated property generation and less user interaction with the prover will leverage formal verification as it will relieve the engineer in the specification as well as in proofing tasks.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00145"}, {"primary_key": "3083355", "vector": [], "sparse_vector": [], "title": "PeASS: A Tool for Identifying Performance Changes at Code Level.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present PeASS (Performance Analysis of Software System versions), a tool for detecting performance changes at source code level that occur between different code versions. By using PeASS, it is possible to identify performance regressions that happened in the past to fix them. PeASS measures the performance of unit tests in different source code versions. To achieve statistic rigor, measurements are repeated and analyzed using an agnostic t-test. To execute a minimal amount of tests, PeASS uses a regression test selection. We evaluate PeASS on a selection of Apache Commons projects and show that 81% of all unit test covered performance changes can be found by PeASS. A video presentation is available at https://www.youtube.com/watch?v=RORFEGSCh6Y and PeASS can be downloaded from https://github.com/DaGeRe/peass.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00123"}, {"primary_key": "3083356", "vector": [], "sparse_vector": [], "title": "Root Cause Localization for Unreproducible Builds via Causality Analysis Over System Call Tracing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Localization of the root causes for unreproducible builds during software maintenance is an important yet challenging task, primarily due to limited runtime traces from build processes and high diversity of build environments. To address these challenges, in this paper, we propose RepTrace, a framework that leverages the uniform interfaces of system call tracing for monitoring executed build commands in diverse build environments and identifies the root causes for unreproducible builds by analyzing the system call traces of the executed build commands. Specifically, from the collected system call traces, RepTrace performs causality analysis to build a dependency graph starting from an inconsistent build artifact (across two builds) via two types of dependencies: read/write dependencies among processes and parent/child process dependencies, and searches the graph to find the processes that result in the inconsistencies. To address the challenges of massive noisy dependencies and uncertain parent/child dependencies, RepTrace includes two novel techniques: (1) using differential analysis on multiple builds to reduce the search space of read/write dependencies, and (2) computing similarity of the runtime values to filter out noisy parent/child process dependencies. The evaluation results of RepTrace over a set of real-world software packages show that RepTrace effectively finds not only the root cause commands responsible for the unreproducible builds, but also the files to patch for addressing the unreproducible issues. Among its Top-10 identified commands and files, RepTrace achieves high accuracy rate of 90.00% and 90.56% in identifying the root causes, respectively.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00056"}, {"primary_key": "3083357", "vector": [], "sparse_vector": [], "title": "Discovering, Explaining and Summarizing Controversial Discussions in Community Q&amp;A Sites.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jianling Sun"], "summary": "Developers often look for solutions to programming problems in community Q&A sites like Stack Overflow. Due to the crowdsourcing nature of these Q&A sites, many user-provided answers are wrong, less optimal or out-of-date. Relying on community-curated quality indicators (e.g., accepted answer, answer vote) cannot reliably identify these answer problems. Such problematic answers are often criticized by other users. However, these critiques are not readily discoverable when reading the posts. In this paper, we consider the answers being criticized and their critique posts as controversial discussions in community Q&A sites. To help developers notice such controversial discussions and make more informed choices of appropriate solutions, we design an automatic open information extraction approach for systematically discovering and summarizing the controversies in Stack Overflow and exploiting official API documentation to assist the understanding of the discovered controversies. We apply our approach to millions of java/android-tagged Stack overflow questions and answers and discover a large scale of controversial discussions in Stack Overflow. Our manual evaluation confirms that the extracted controversy information is of high accuracy. A user study with 18 developers demonstrates the usefulness of our generated controversy summaries in helping developers avoid the controversial answers and choose more appropriate solutions to programming questions.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00024"}, {"primary_key": "3083358", "vector": [], "sparse_vector": [], "title": "SiMPOSE - Configurable N-Way Program Merging Strategies for Superimposition-Based Analysis of Variant-Rich Software.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Modern software often exists in many different, yet similar versions and/or variants, usually derived from a common code base (e.g., via clone-and-own). In the context of product-line engineering, family-based analysis has shown very promising potential for improving efficiency in applying quality-assurance techniques to variant-rich software, as compared to a variant-by-variant approach. Unfortunately, these strategies rely on a product-line representation superimposing all program variants in a syntactically well-formed, semantically sound and variant-preserving manner, which is manually hard to obtain in practice. We demonstrate the SiMPOSE methodology for automatically generating superimpositions of N given program versions and/or variants facilitating family-based analysis of variant-rich software. SiMPOSE is based on a novel N-way model-merging technique operating at the level of control-flow automata (CFA) representations of C programs. CFAs constitute a unified program abstraction utilized by many recent software-analysis tools. We illustrate different merging strategies supported by SiMPOSE, namely variant-by-variant, N-way merging, incremental 2-way merging, and partition-based N/2-way merging, and demonstrate how SiMPOSE can be used to systematically compare their impact on efficiency and effectiveness of family-based unit-test generation. The SiMPOSE tool, the demonstration of its usage as well as related artifacts and documentation can be found at http://pi.informatik.uni-siegen.de/projects/variance/simpose.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00120"}, {"primary_key": "3083360", "vector": [], "sparse_vector": [], "title": "Sip4J: Statically Inferring Access Permission Contracts for Parallelising Sequential Java Programs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> Ling"], "summary": "This paper presents Sip4J, a fully automated, scalable and effective tool to automatically generate access permission contracts for a sequential Java program. The access permission contracts, which represent the dependency of code blocks, have been frequently used to enable concurrent execution of sequential programs. Those permission contracts, unfortunately, need to be manually created by programmers, which is known to be time-consuming, laborious and error-prone. To mitigate those manual efforts, Sip4J performs inter-procedural static analysis of Java source code to automatically extract the implicit dependencies in the program and subsequently leverages them to automatically generate access permission contracts, following the Design by Contract principle. The inferred specifications are then used to identify the concurrent (immutable) methods in the program. Experimental results further show that Sip4J is useful and effective towards generating access permission contracts for sequential Java programs. The implementation of Sip4J has been published as an open-sourced project at https://github.com/Sip4J/Sip4J and a demo video of <PERSON><PERSON><PERSON><PERSON> can be found at https://youtu.be/RjMTIxlhHTg.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00111"}, {"primary_key": "3083361", "vector": [], "sparse_vector": [], "title": "RANDR: Record and Replay for Android Applications via Targeted Runtime Instrumentation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ayse K. <PERSON>", "<PERSON>"], "summary": "The ability to repeat the execution of a program is a fundamental requirement in many areas of computing from computer system evaluation to software engineering. Reproducing executions of mobile apps, in particular, has proven difficult under real-life scenarios due to multiple sources of external inputs and interactive nature of the apps. Previous works that provide record/replay functionality for mobile apps are restricted to particular input sources (e.g., touchscreen events) and present deployment challenges due to intrusive modifications to the underlying software stack. Moreover, due to their reliance on record and replay of device specific events, the recorded executions cannot be reliably reproduced across different platforms. In this paper, we present a new practical approach, RandR, for record and replay of Android applications. RandR captures and replays multiple sources of input (i.e., UI and network) without requiring source code (OS or app), administrative device privileges, or any special platform support. RandR achieves these qualities by instrumenting a select set of methods at runtime within an application's own sandbox. In addition, to enable portability of recorded executions across different platforms for replay, RandR contextualizes UI events as interactions with particular UI components (e.g., a button) as opposed to relying on platform specific features (e.g., screen coordinates). We demonstrate RandR's accurate cross-platform record and replay capabilities using over 30 real-world Android apps across a variety of platforms including emulators as well as commercial off-the-shelf mobile devices deployed in real life.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00022"}, {"primary_key": "3083362", "vector": [], "sparse_vector": [], "title": "Learning from Examples to Find Fully Qualified Names of API Elements in Code Snippets.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Developers often reuse code snippets from online forums, such as Stack Overflow, to learn API usages of software frameworks or libraries. These code snippets often contain ambiguous undeclared external references. Such external references make it difficult to learn and use those APIs correctly. In particular, reusing code snippets containing such ambiguous undeclared external references requires significant manual efforts and expertise to resolve them. Manually resolving fully qualified names (FQN) of API elements is a non-trivial task. In this paper, we propose a novel context-sensitive technique, called COSTER, to resolve FQNs of API elements in such code snippets. The proposed technique collects locally specific source code elements as well as globally related tokens as the context of FQNs, calculates likelihood scores, and builds an occurrence likelihood dictionary (OLD). Given an API element as a query, COSTER captures the context of the query API element, matches that with the FQNs of API elements stored in the OLD, and rank those matched FQNs leveraging three different scores: likelihood, context similarity, and name similarity scores. Evaluation with more than 600K code examples collected from GitHub and two different Stack Overflow datasets shows that our proposed technique improves precision by 4-6% and recall by 3-22% compared to state-of-the-art techniques. The proposed technique significantly reduces the training time compared to the StatType, a state-of-the-art technique, without sacrificing accuracy. Extensive analyses on results demonstrate the robustness of the proposed technique.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00032"}, {"primary_key": "3083363", "vector": [], "sparse_vector": [], "title": "Automatically Repairing Binary Programs Using Adapter Synthesis.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Bugs in commercial software and third-party components are an undesirable and expensive phenomenon. Such software is usually released to users only in binary form. The lack of source code renders users of such software dependent on their software vendors for repairs of bugs. Such dependence is even more harmful if the bugs introduce new vulnerabilities in the software. Automatically repairing security and functionality bugs in binary code increases software robustness without any developer effort. In this research, we propose development of a binary program repair tool that uses existing bug-free fragments of code to repair buggy code.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00149"}, {"primary_key": "3083364", "vector": [], "sparse_vector": [], "title": "A Journey Towards Providing Intelligence and Actionable Insights to Development Teams in Software Delivery.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "For delivering high-quality artifacts within the budget and on schedule, software delivery teams ideally should have a holistic and in-process view of the current health and future trajectory of the project. However, such insights need to be at the right level of granularity and need to be derived typically from a heterogeneous project environment, in a way that helps development team members with their tasks at hand. Due to client mandates, software delivery project environments employ many disparate tools and teams tend to be distributed, thus making the relevant information retrieval, insight generation, and developer intelligence augmentation process fairly complex. In this paper, we discuss our journey in this area spanning across facets like software project modelling and new development metrics, studying developer priorities, adoption of new metrics, and different approaches of developer intelligence augmentation. Finally, we present our exploration of new immersive technologies for human-centered software engineering.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00142"}, {"primary_key": "3083365", "vector": [], "sparse_vector": [], "title": "Accurate String Constraints Solution Counting with Weighted Automata.", "authors": ["<PERSON>", "<PERSON>"], "summary": "As an important extension of symbolic execution (SE), probabilistic symbolic execution (PSE) computes execution probabilities of program paths. Using this information, PSE can prioritize path exploration strategies. To calculate the probability of a path PSE relies on solution counting approaches for the path constraint. The correctness of a solution counting approach depends on the methodology used to count solutions and whether a path constraint maintains a one-to-one relation with program input values. This work focuses on the latter aspect of the solution counting correctness for string constraints. In general, maintaining a one-to-one relation is not always possible, especially in the presence of non-linear constraints. To deal with this issue, researchers that work on PSE for numerical domains either analyze programs with linear constraints, or develop novel techniques to handle solution counting of non-linear constraints. For the string domain, however, previous work on PSE mainly focuses on efficient and accurate solution counting for automata-based string models and has not investigated whether a one-to-one relationship between the strings encoded by automata and input string values is preserved. In this work we demonstrate that traditional automata-based string models fail to maintain one-to-one relations and propose to use the weighted automata model, which preserves the one-to-one relation between the path constraint it encodes and the input string values. We use this model to implement a string constraint solver and show its correctness on a set of non-trivial synthetic benchmarks. We also present an empirical evaluation of traditional and proposed automata solvers on real-world string constraints. The evaluations show that while being less efficient than traditional automata models, the weighted automata model maintains correct solution counts.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00049"}, {"primary_key": "3083366", "vector": [], "sparse_vector": [], "title": "Trusted Software Supply Chain.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Modern software delivery happens in a geographically distributed environment and resembles like a supply chain - consists of various participants, involves various phases, needs adherence to multiple regulations and needs to maintain artifacts' integrity throughout the delivery phases. This shift in software development brings along with it several challenges ranging from communication of information/knowledge, coordination and control of teams, activities adhering to goals and policies and artifacts adhering to quality, visibility, and management. With the dispersion of centralized control over software delivery to autonomous delivery organizations, the variety of processes and tools used turns transparency into opacity as autonomous teams use different software processes, tools, and metrics, leading to issues like ineffective compliance monitoring, friction prone coordination, and lack of provenance, and thereby trust. In this paper, we present a delivery governance framework based on distributed ledger technology that uses a notion of `software telemetry' to record data from disparate delivery partners and enables compliance monitoring and adherence, provenance and traceability, transparency, and thereby trust.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00141"}, {"primary_key": "3083368", "vector": [], "sparse_vector": [], "title": "SEGATE: Unveiling Semantic Inconsistencies between Code and Specification of String Inputs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Automated testing techniques are often assessed on coverage based metrics. However, despite giving good coverage, the test cases may miss the gap between functional specification and the code implementation. This gap may be subtle in nature, arising due to the absence of logical checks, either in the implementation or in the specification, resulting in inconsistencies in the input definition. The inconsistencies may be prevalent especially for structured inputs, commonly specified using string-based data types. Our study on defects reported over popular libraries reveals that such gaps may not be limited to input validation checks. We propose a test generation technique for structured string inputs where we infer inconsistencies in input definition to expose semantic gaps in the method under test and the method specification. We assess this technique using our tool SEGATE, Semantic Gap Tester. SEGATE uses static analysis and automaton modeling to infer the gap and generate test cases. On our benchmark dataset, comprising of defects reported in 15 popular open-source libraries, written in Java, SEGATE was able to generate tests to expose 80% of the defects.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00028"}, {"primary_key": "3083369", "vector": [], "sparse_vector": [], "title": "Improving Patch Quality by Enhancing Key Components of Automatic Program Repair.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "The error repair process in software systems is, historically, a resource-consuming task that relies heavily in developer manual effort. Automatic program repair approaches enable the repair of software with minimum human interaction, therefore, mitigating the burden from developers. However, a problem automatically generated patches commonly suffer is generating low-quality patches (which overfit to one program specification, thus not generalizing to an independent oracle evaluation). This work proposes a set of mechanisms to increase the quality of plausible patches including an analysis of test suite behavior and their key characteristics for automatic program repair, analyzing developer behavior to inform the mutation operator selection distribution, and a study of patch diversity as a means to create consolidated higher quality fixes.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00147"}, {"primary_key": "3083370", "vector": [], "sparse_vector": [], "title": "ReduKtor: How We Stopped Worrying About Bugs in Kotlin Compiler.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Bug localization is well-known to be a difficult problem in software engineering, and specifically in compiler development, where it is beneficial to reduce the input program to a minimal reproducing example; this technique is more commonly known as delta debugging. What additionally contributes to the problem is that every new programming language has its own unique quirks and foibles, making it near impossible to reuse existing tools and approaches with full efficiency. In this experience paper we tackle the delta debugging problem w.<PERSON><PERSON><PERSON><PERSON> Ko<PERSON>in, a relatively new programming language from JetBrains. Our approach is based on a novel combination of program slicing, hierarchical delta debugging and Kotlin-specific transformations, which are synergistic to each other. We implemented it in a prototype called ReduKtor and did extensive evaluation on both synthetic and real Kotlin programs; we also compared its performance with classic delta debugging techniques. The evaluation results support the practical usability of our approach to Kotlin delta debugging and also shows the importance of using both language-agnostic and language-specific techniques to achieve best reduction efficiency and performance.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00038"}, {"primary_key": "3083371", "vector": [], "sparse_vector": [], "title": "Test Automation and Its Limitations: A Case Study.", "authors": ["<PERSON><PERSON><PERSON> Sung", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Modern embedded systems are increasingly complex and contain multiple software layers from BSP (Board Support Packages) to OS to middleware to AI (Artificial Intelligence) algorithms like perception and voice recognition. Integrations of inter-layer and intra-layer in embedded systems provide dedicated services such as taking a picture or movie-streaming. Accordingly, it gets more complicated to find out the root cause of a system failure. This industrial proposal describes a difficulty of testing embedded systems, and presents a case study in terms of integration testing.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00139"}, {"primary_key": "3083372", "vector": [], "sparse_vector": [], "title": "Demystifying Application Performance Management Libraries for Android.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Since the performance issues of apps can influence users' experience, developers leverage application performance management (APM) tools to locate the potential performance bottleneck of their apps. Unfortunately, most developers do not understand how APMs monitor their apps during the runtime and whether these APMs have any limitations. In this paper, we demystify APMs by inspecting 25 widely-used APMs that target on Android apps. We first report how these APMs implement 8 key functions as well as their limitations. Then, we conduct a large-scale empirical study on 500,000 Android apps from Google Play to explore the usage of APMs. This study has some interesting observations about existing APMs for Android, including 1) some APMs still use deprecated permissions and approaches so that they may not always work properly; 2) some app developers use APMs to collect users' privacy information.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00069"}, {"primary_key": "3083373", "vector": [], "sparse_vector": [], "title": "Kotless: A Serverless Framework for Kotlin.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent trends in Web development demonstrate an increased interest in serverless applications, i.e. applications that utilize computational resources provided by cloud services on demand instead of requiring traditional server management. This approach enables better resource management while being scalable, reliable, and cost-effective. However, it comes with a number of organizational and technical difficulties which stem from the interaction between the application and the cloud infrastructure, for example, having to set up a recurring task of reuploading updated files. In this paper, we present Kotless - a Kotlin Serverless Framework. Kotless is a cloud-agnostic toolkit that solves these problems by interweaving the deployed application into the cloud infrastructure and automatically generating the necessary deployment code. This relieves developers from having to spend their time integrating and managing their applications instead of developing them. Kotless has proven its capabilities and has been used to develop several serverless applications already in production. Its source code is available at https://github.com/JetBrains/kotless, a tool demo can be found at https://www.youtube.com/watch?v=IMSakPNl3TY.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00114"}, {"primary_key": "3083374", "vector": [], "sparse_vector": [], "title": "How Do API Selections Affect the Runtime Performance of Data Analytics Tasks?", "authors": ["<PERSON><PERSON>", "Shan <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "As data volume and complexity grow at an unprecedented rate, the performance of data analytics programs is becoming a major concern for developers. We observed that developers sometimes use alternative data analytics APIs to improve program runtime performance while preserving functional equivalence. However, little is known on the characteristics and performance attributes of alternative data analytics APIs. In this paper, we propose a novel approach to extracting alternative implementations that invoke different data analytics APIs to solve the same tasks. A key appeal of our approach is that it exploits the comparative structures in Stack Overflow discussions to discover programming alternatives. We show that our approach is promising, as 86% of the extracted code pairs were validated as true alternative implementations. In over 20% of these pairs, the faster implementation was reported to achieve a 10x or more speedup over its slower alternative. We hope that our study offers a new perspective of API recommendation and motivates future research on optimizing data analytics programs.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00067"}, {"primary_key": "3083375", "vector": [], "sparse_vector": [], "title": "Semistructured Merge in JavaScript Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Industry widely uses unstructured merge tools that rely on textual analysis to detect and resolve conflicts between code contributions. Semistructured merge tools go further by partially exploring the syntactic structure of code artifacts, and, as a consequence, obtaining significant merge accuracy gains for Java-like languages. To understand whether semistructured merge and the observed gains generalize to other kinds of languages, we implement two semistructured merge tools for JavaScript, and compare them to an unstructured tool. We find that current semistructured merge algorithms and frameworks are not directly applicable for scripting languages like JavaScript. By adapting the algorithms, and studying 10,345 merge scenarios from 50 JavaScript projects on GitHub, we find evidence that our JavaScript tools report fewer spurious conflicts than unstructured merge, without compromising the correctness of the merging process. The gains, however, are much smaller than the ones observed for Java-like languages, suggesting that semistructured merge advantages might be limited for languages that allow both commutative and non-commutative declarations at the same syntactic level.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00098"}, {"primary_key": "3083376", "vector": [], "sparse_vector": [], "title": "PHANTA: Diversified Test Code Quality Measurement for Modern Software Development.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Test code is becoming more essential to the modern software development process. However, practitioners often pay inadequate attention to key aspects of test code quality, such as bug detectability, maintainability and speed. Existing tools also typically report a single test code quality measure, such as code coverage, rather than a diversified set of metrics. To measure and visualize quality of test code in a comprehensive fashion, we developed an integrated test code analysis tool called Phanta. In this show case, we posit that the enhancement of test code quality is key to modernizing software development, and show how Phanta's techniques measure the quality using mutation analysis, test code clone detection, and so on. Further, we present an industrial case study where Phanta was applied to analyze test code in a real Fujitsu project, and share lessons learned from the case study.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00138"}, {"primary_key": "3083377", "vector": [], "sparse_vector": [], "title": "Enabling Continuous Improvement of a Continuous Integration Process.", "authors": ["<PERSON>"], "summary": "Continuous Integration (CI) is a widely-adopted software engineering practice. Despite its undisputed benefits, like higher software quality and improved developer productivity, mastering CI is not easy. Among the several barriers when transitioning to CI, developers need to face a new type of software failures (i.e., build failures) that requires them to understand complex build logs. Even when a team has successfully introduced a CI culture, living up to its principles and improving the CI practice are also challenging. In my research, I want to provide developers with the right support for establishing CI and the proper recommendations for continuously improving their CI process.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00151"}, {"primary_key": "3083378", "vector": [], "sparse_vector": [], "title": "Batch Alias Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Many program-analysis based tools require precise points-to/alias information only for some program variables. To meet this requirement efficiently, there have been many works on demand-driven analyses that perform only the work necessary to compute the points-to or alias information on the requested variables (queries). However, these demand-driven analyses can be very expensive when applied on large systems where the number of queries can be significant. Such a blow-up in analysis time is unacceptable in cases where scalability with real-time constraints is crucial; for example, when program analysis tools are plugged into an IDE (Integrated Development Environment). In this paper, we propose schemes to improve the scalability of demand-driven analyses without compromising on precision. Our work is based on novel ideas for eliminating irrelevant and redundant data-flow paths for the given queries. We introduce the idea of batch analysis, which can answer multiple given queries in batch mode. Batch analysis suits the environments with strict time constraints, where the queries come in batch. We present a batch alias analysis framework that can be used to speed up given demand-driven alias analysis. To show the effectiveness of this framework, we use two demand-driven alias analyses (1) the existing best performing demand-driven alias analysis tool for race-detection clients and (2) an optimized version thereof that avoids irrelevant computation. Our evaluations on a simulated data-race client, and on a recent program-understanding tool, show that batch analysis leads to significant performance gains, along with minor gains in precision.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00091"}, {"primary_key": "3083379", "vector": [], "sparse_vector": [], "title": "Empirical Study of Programming to an Interface.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "A popular recommendation to programmers in object-oriented software is to \"program to an interface, not an implementation\" (PTI). Expected benefits include increased simplicity from abstraction, decreased dependency on implementations, and higher flexibility. Yet, interfaces must be immutable, excessive class hierarchies can be a form of complexity, and \"speculative generality\" is a known code smell. To advance the empirical knowledge of PTI, we conducted an empirical investigation that involves 126 Java projects on GitHub, aiming to measuring the decreased dependency benefits (in terms of cochange).", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00083"}, {"primary_key": "3083380", "vector": [], "sparse_vector": [], "title": "Multi-modal Attention Network Learning for Semantic Source Code Retrieval.", "authors": ["<PERSON>", "Jingdong Shu", "<PERSON><PERSON><PERSON>", "G<PERSON><PERSON> Xu", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Code retrieval techniques and tools have been playing a key role in facilitating software developers to retrieve existing code fragments from available open-source repositories given a user query (e.g., a short natural language text describing the functionality for retrieving a particular code snippet). Despite the existing efforts in improving the effectiveness of code retrieval, there are still two main issues hindering them from being used to accurately retrieve satisfiable code fragments from large-scale repositories when answering complicated queries. First, the existing approaches only consider shallow features of source code such as method names and code tokens, but ignoring structured features such as abstract syntax trees (ASTs) and control-flow graphs (CFGs) of source code, which contains rich and well-defined semantics of source code. Second, although the deep learning-based approach performs well on the representation of source code, it lacks the explainability, making it hard to interpret the retrieval results and almost impossible to understand which features of source code contribute more to the final results. To tackle the two aforementioned issues, this paper proposes MMAN, a novel Multi-Modal Attention Network for semantic source code retrieval. A comprehensive multi-modal representation is developed for representing unstructured and structured features of source code, with one LSTM for the sequential tokens of code, a Tree-LSTM for the AST of code and a GGNN (Gated Graph Neural Network) for the CFG of code. Furthermore, a multi-modal attention fusion layer is applied to assign weights to different parts of each modality of source code and then integrate them into a single hybrid representation. Comprehensive experiments and analysis on a large-scale real-world dataset show that our proposed model can accurately retrieve code snippets and outperforms the state-of-the-art methods.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00012"}, {"primary_key": "3083381", "vector": [], "sparse_vector": [], "title": "Emotions Extracted from Text vs. True Emotions-An Empirical Evaluation in SE Context.", "authors": ["<PERSON>"], "summary": "Emotion awareness research in SE context has been growing in recent years. Currently, researchers often rely on textual communication records to extract emotion states using natural language processing techniques. However, how well these extracted emotion states reflect people's real emotions has not been thoroughly investigated. In this paper, we report a multi-level, longitudinal empirical study with 82 individual members in 27 project teams. We collected their self-reported retrospective emotion states on a weekly basis during their year-long projects and also extracted corresponding emotions from the textual communication records. We then model and compare the dynamics of these two types of emotions using multiple statistical and time series analysis methods. Our analyses yield a rich set of findings. The most important one is that the dynamics of emotions extracted using text-based algorithms often do not well reflect the dynamics of self-reported retrospective emotions. Besides, the extracted emotions match self-reported retrospective emotions better at the team-level. Our results also suggest that individual personalities and the team's emotion display norms significantly impact the match/mismatch. Our results should warn the research community about the limitations and challenges of applying text-based emotion recognition tools in SE research.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00031"}, {"primary_key": "3083382", "vector": [], "sparse_vector": [], "title": "TsmartGP: A Tool for Finding Memory Defects with Pointer Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Jiaguang Sun"], "summary": "Precise pointer analysis is desired since it is a core technique to find memory defects. There are several dimensions of pointer analysis precision, flow sensitivity, context sensitivity, field sensitivity and path sensitivity. For static analysis tools utilizing pointer analysis, considering all dimensions is difficult because the trade-off between precision and efficiency should be balanced. This paper presents TsmartGP, a static analysis tool for finding memory defects in C programs with a precise and efficient pointer analysis. The pointer analysis algorithm is flow, context, field, and quasi path sensitive. Control flow automatons are the key structures for our analysis to be flow sensitive. Function summaries are applied to get context information and elements of aggregate structures are handled to improve precision. Path conditions are used to filter unreachable paths. For efficiency, a multi-entry mechanism is proposed. Utilizing the pointer analysis algorithm, we implement a checker in TsmartGP to find uninitialized pointer errors in 13 real-world applications. Cppcheck and Clang Static Analyzer are chosen for comparison. The experimental results show that TsmartGP can find more errors while its accuracy is also higher than Cppcheck and Clang Static Analyzer. The demo video is available at https://youtu.be/IQlshemk6OA.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00129"}, {"primary_key": "3083384", "vector": [], "sparse_vector": [], "title": "Characterizing Android App Signing Issues.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In the app releasing process, Android requires all apps to be digitally signed with a certificate before distribution. Android uses this certificate to identify the author and ensure the integrity of an app. However, a number of signature issues have been reported recently, threatening the security and privacy of Android apps. In this paper, we present the first large-scale systematic measurement study on issues related to Android app signatures. We first create a taxonomy covering four types of app signing issues (21 anti-patterns in total), including vulnerabilities, potential attacks, release bugs and compatibility issues. Then we developed an automated tool to characterize signature-related issues in over 5 million app items (3 million distinct apks) crawled from Google Play and 24 alternative Android app markets. Our empirical findings suggest that although Google has introduced apk-level signing schemes (V2 and V3) to overcome some of the known security issues, more than 93% of the apps still use only the JAR signing scheme (V1), which poses great security threats. Besides, we also revealed that 7% to 45% of the apps in the 25 studied markets have been found containing at least one signing issue, while a large number of apps have been exposed to security vulnerabilities, attacks and compatibility issues. Among them a considerable number of apps we identified are popular apps with millions of downloads. Finally, our evolution analysis suggested that most of the issues were not mitigated after a considerable amount of time across markets. The results shed light on the emergency for detecting and repairing the app signing issues.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00035"}, {"primary_key": "3083385", "vector": [], "sparse_vector": [], "title": "CoRA: Decomposing and Describing Tangled Code Changes for Reviewer.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Code review is an important mechanism for code quality assurance both in open source software and industrial software. Reviewers usually suffer from numerous, tangled and loosely related code changes that are bundled in a single commit, which makes code review very difficult. In this paper, we propose CoRA (Code Review Assistant), an automatic approach to decompose a commit into different parts and generate concise descriptions for reviewers. More specifically, CoRA can decompose a commit into independent parts (e.g., bug fixing, new feature adding, or refactoring) by code dependency analysis and tree-based similar-code detection, then identify the most important code changes in each part based on the PageRank algorithm and heuristic rules. As a result, CoRA can generate a concise description for each part of the commit. We evaluate our approach in seven open source software projects and 50 code commits. The results indicate that CoRA can improve the accuracy of decomposing code changes by 6.3% over the state-of-art practice. At the same time, CoRA can identify the important part from the fine-grained code changes with a mean average precision (MAP) of 87.7%. We also conduct a human study with eight participants to evaluate the performance and usefulness of CoRA, the user feedback indicates that CoRA can effectively help reviewers.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00101"}, {"primary_key": "3083387", "vector": [], "sparse_vector": [], "title": "MAP-Coverage: A Novel Coverage Criterion for Testing Thread-Safe Classes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Concurrent programs must be thoroughly tested, as concurrency bugs are notoriously hard to detect. Code coverage criteria can be used to quantify the richness of a test suite (e.g., whether a program has been tested sufficiently) or provide practical guidelines on test case generation (e.g., as objective functions used in program fuzzing engines). Traditional code coverage criteria are, however, designed for sequential programs and thus ineffective for concurrent programs. In this work, we introduce a novel code coverage criterion for testing thread-safe classes called MAP-coverage (short for memory-access patterns). The motivation is that concurrency bugs are often correlated with certain memory-access patterns, and thus it is desirable to comprehensively cover all memory-access patterns. Furthermore, we propose a testing method for maximizing MAP-coverage. Our method has been implemented as a self-contained toolkit, and the experimental results on 20 benchmark programs show that our toolkit outperforms existing testing methods. Lastly, we show empirically that there exists positive correlation between MAP-coverage and the effectiveness of a set of test executions.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00073"}, {"primary_key": "3083388", "vector": [], "sparse_vector": [], "title": "Retrieve and Refine: Exemplar-Based Neural Comment Generation.", "authors": ["<PERSON><PERSON>"], "summary": "Code comment generation is a crucial task in the field of automatic software development. Most previous neural comment generation systems used an encoder-decoder neural network and encoded only information from source code as input. Software reuse is common in software development. However, this feature has not been introduced to existing systems. Inspired by the traditional IR-based approaches, we propose to use the existing comments of similar source code as exemplars to guide the comment generation process. Based on an open source search engine, we first retrieve a similar code and treat its comment as an exemplar. Then we applied a seq2seq neural network to conduct an exemplar-based comment generation. We evaluate our approach on a large-scale Java corpus, and experimental results demonstrate that our model significantly outperforms the state-of-the-art methods.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00152"}, {"primary_key": "3083389", "vector": [], "sparse_vector": [], "title": "PTracer: A Linux Kernel Patch Trace Bot.", "authors": ["<PERSON>", "Jicheng Cao", "<PERSON><PERSON><PERSON>"], "summary": "We present PTracer, a Linux kernel patch trace bot based on an improved PatchNet. PTracer continuously monitors new patches in the git repository of the mainline Linux kernel, filters out unconcerned ones, classifies the rest as bug-fixing or non bug-fixing patches, and reports bug-fixing patches to the kernel experts of commercial operating systems. We use the patches in February 2019 of the mainline Linux kernel to perform the test. As a result, PTracer recommended 151 patches to CGEL kernel experts out of 5,142, and 102 of which were accepted. PTracer has been successfully applied to a commercial operating system and has the advantages of improving software quality and saving labor cost.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00140"}, {"primary_key": "3083391", "vector": [], "sparse_vector": [], "title": "Better Development of Safety Critical Systems: Chinese High Speed Railway System Development Experience Report.", "authors": ["Zhi<PERSON><PERSON> Wu", "<PERSON>", "<PERSON><PERSON>"], "summary": "Ensure the correctness of safety critical systems play a key role in the worldwide software engineering. Over the past years we have been helping CASCO Signal Ltd which is the Chinese biggest high speed railway company to develop high speed railway safety critical software. We have also contributed specific methods for developing better safety critical software, including a search-based model-driven software development approach which uses SysML diagram refinement method to construct SysML model and SAT solver to check the model. This talk aims at sharing the challenge of developing high speed railway safety critical system, what we learn from develop a safety critical software with a Chinese high speed railway company, and we use ZC subsystem as a case study to show the systematic model-driven safety critical software development method.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00143"}, {"primary_key": "3083392", "vector": [], "sparse_vector": [], "title": "MalScan: Fast Market-Wide Mobile Malware Scanning by Social-Network Centrality Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Deqing Zou", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Malware scanning of an app market is expected to be scalable and effective. However, existing approaches use either syntax-based features which can be evaded by transformation attacks or semantic-based features which are usually extracted by performing expensive program analysis. Therefor, in this paper, we propose a lightweight graph-based approach to perform Android malware detection. Instead of traditional heavyweight static analysis, we treat function call graphs of apps as social networks and perform social-network-based centrality analysis to represent the semantic features of the graphs. Our key insight is that centrality provides a succinct and fault-tolerant representation of graph semantics, especially for graphs with certain amount of inaccurate information (e.g., inaccurate call graphs). We implement a prototype system, MalScan, and evaluate it on datasets of 15,285 benign samples and 15,430 malicious samples. Experimental results show that MalScan is capable of detecting Android malware with up to 98% accuracy under one second which is more than 100 times faster than two state-of-the-art approaches, namely MaMaDroid and Drebin. We also demonstrate the feasibility of MalScan on market-wide malware scanning by performing a statistical study on over 3 million apps. Finally, in a corpus of dataset collected from Google-Play app market, MalScan is able to identify 18 zero-day malware including malware samples that can evade detection of existing tools.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00023"}, {"primary_key": "3083393", "vector": [], "sparse_vector": [], "title": "Automating CUDA Synchronization via Program Transformation.", "authors": ["<PERSON><PERSON> Wu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While CUDA has been the most popular parallel computing platform and programming model for general purpose GPU computing, CUDA synchronization undergoes significant challenges for GPU programmers due to its intricate parallel computing mechanism and coding practices. In this paper, we propose AuCS, the first general framework to automate synchronization for CUDA kernel functions. AuCS transforms the original LLVM-level CUDA program control flow graph in a semantic-preserving manner for exploring the possible barrier function locations. Accordingly, AuCS develops mechanisms to correctly place barrier functions for automating synchronization in multiple erroneous (challenging-to-be-detected) synchronization scenarios, including data race, barrier divergence, and redundant barrier functions. To evaluate the effectiveness and efficiency of AuCS, we conduct an extensive set of experiments and the results demonstrate that AuCS can automate 20 out of 24 erroneous synchronization scenarios.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00075"}, {"primary_key": "3083394", "vector": [], "sparse_vector": [], "title": "An Image-Inspired and CNN-Based Android Malware Detection Approach.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Until 2017, Android smartphones occupied approximately 87% of the smartphone market. The vast market also promotes the development of Android malware. Nowadays, the number of malware targeting Android devices found daily is more than 38,000. With the rapid progress of mobile application programming and anti-reverse-engineering techniques, it is harder to detect all kinds of malware. To address challenges in existing detection techniques, such as data obfuscation and limited code coverage, we propose a detection approach that directly learns features of malware from Dalvik bytecode based on deep learning technique (CNN). The average detection time of our model is0.22 seconds, which is much lower than other existing detection approaches. In the meantime, the overall accuracy of our model achieves over 93%.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00155"}, {"primary_key": "3083395", "vector": [], "sparse_vector": [], "title": "Coverage-Guided Fuzzing for Feedforward Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deep neural network (DNN) has been widely applied to safety-critical scenarios such as autonomous vehicle, security surveillance, and cyber-physical control systems. Yet, the incorrect behaviors of DNNs can lead to severe accidents and tremendous losses due to hidden defects. In this paper, we present DeepHunter, a general-purpose fuzzing framework for detecting defects of DNNs. DeepHunter is inspired by traditional grey-box fuzzing and aims to increase the overall test coverage by applying adaptive heuristics according to runtime feedback. Specifically, DeepHunter provides a series of seed selection strategies, metamorphic mutation strategies, and testing criteria customized to DNN testing; all these components support multiple built-in configurations which are easy to extend. We evaluated DeepHunter on two popular datasets and the results demonstrate the effectiveness of DeepHunter in achieving coverage increase and detecting real defects. A video demonstration which showcases the main features of DeepHunter can be found at https://youtu.be/s5DfLErcgrc.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00127"}, {"primary_key": "3083396", "vector": [], "sparse_vector": [], "title": "Automatic Self-Validation for Code Coverage Profilers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Hao Sun", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Code coverage as the primitive dynamic program behavior information, is widely adopted to facilitate a rich spectrum of software engineering tasks, such as testing, fuzzing, debugging, fault detection, reverse engineering, and program understanding. Thanks to the widespread applications, it is crucial to ensure the reliability of the code coverage profilers. Unfortunately, due to the lack of research attention and the existence of testing oracle problem, coverage profilers are far away from being tested sufficiently. Bugs are still regularly seen in the widely deployed profilers, like gcov and llvm-cov, along with gcc and llvm, respectively. This paper proposes Cod, an automated self-validator for effectively uncovering bugs in the coverage profilers. Starting from a test program (either from a compiler's test suite or generated randomly), Cod detects profiler bugs with zero false positive using a metamorphic relation in which the coverage statistics of that program and a mutated variant are bridged. We evaluated Cod over two of the most well-known code coverage profilers, namely gcov and llvm-cov. Within a four-month testing period, a total of 196 potential bugs (123 for gcov, 73 for llvm-cov) are found, among which 23 are confirmed by the developers.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00018"}, {"primary_key": "3083397", "vector": [], "sparse_vector": [], "title": "Empirical Study of Python Call Graph.", "authors": ["<PERSON>"], "summary": "In recent years, the extensive application of the Python language has made its analysis work more and more valuable. Many static analysis algorithms need to rely on the construction of call graphs. In this paper, we did a comparative empirical analysis of several widely used Python static call graph tools both quantitatively and qualitatively. Experiments show that the existing Python static call graph tools have a large difference in the construction effectiveness, and there is still room for improvement.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00160"}, {"primary_key": "3083398", "vector": [], "sparse_vector": [], "title": "Crowdsourced Report Generation via Bug Screenshot Understanding.", "authors": ["Shengcheng Yu"], "summary": "Quality control is a challenge of crowdsourcing, especially in software testing. As some unprofessional workers involved, low-quality yieldings may hinder crowdsourced testing from satisfying requesters' requirements. Therefore, it is in demand to assist crowdworkers to raise bug report quality. In this paper, we propose a novel auxiliary method, namely CroReG, to generate crowdsourcing bug reports by analyzing bug screenshots uploaded by crowdworkers with image understanding techniques. The preliminary experiment results show that CroReG can effectively generate bug reports containing accurate screenshot captions and providing positive guidance for crowdworkers.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00161"}, {"primary_key": "3083399", "vector": [], "sparse_vector": [], "title": "LIRAT: Layout and Image Recognition Driving Automated Mobile Testing of Cross-Platform.", "authors": ["Shengcheng Yu", "Chunrong Fang", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The fragmentation issue spreads over multiple mobile platforms such as Android, iOS, mobile web, and WeChat, which hinders test scripts from running across platforms. To reduce the cost of adapting scripts for various platforms, some existing tools apply conventional computer vision techniques to replay the same script on multiple platforms. However, because these solutions can hardly identify dynamic or similar widgets. It becomes difficult for engineers to apply them in practice. In this paper, we present an image-driven tool, namely LIRAT, to record and replay test scripts cross platforms, solving the problem of test script cross-platform replay for the first time. LIRAT records screenshots and layouts of the widgets, and leverages image understanding techniques to locate them in the replay process. Based on accurate widget localization, LIRAT supports replaying test scripts across devices and platforms. We employed LIRAT to replay 25 scripts from 5 application across 8 Android devices and 2 iOS devices. The results show that LIRAT can replay 88% scripts on Android platforms and 60% on iOS platforms. The demo can be found at: https: //github.com/YSC9848/LIRAT.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00103"}, {"primary_key": "3083400", "vector": [], "sparse_vector": [], "title": "SCMiner: Localizing System-Level Concurrency Faults from Large System Call Traces.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Tingting Yu"], "summary": "Localizing concurrency faults that occur in production is hard because, (1) detailed field data, such as user input, file content and interleaving schedule, may not be available to developers to reproduce the failure; (2) it is often impractical to assume the availability of multiple failing executions to localize the faults using existing techniques; (3) it is challenging to search for buggy locations in an application given limited runtime data; and, (4) concurrency failures at the system level often involve multiple processes or event handlers (e.g., software signals), which can not be handled by existing tools for diagnosing intra-process(thread-level) failures. To address these problems, we present SCMiner, a practical online bug diagnosis tool to help developers understand how a system-level concurrency fault happens based on the logs collected by the default system audit tools. SCMiner achieves online bug diagnosis to obviate the need for offline bug reproduction. SCMiner does not require code instrumentation on the production system or rely on the assumption of the availability of multiple failing executions. Specifically, after the system call traces are collected, SCMiner uses data mining and statistical anomaly detection techniques to identify the failure-inducing system call sequences. It then maps each abnormal sequence to specific application functions. We have conducted an empirical study on 19 real-world benchmarks. The results show that SCMiner is both effective and efficient at localizing system-level concurrency faults.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00055"}, {"primary_key": "3083401", "vector": [], "sparse_vector": [], "title": "A Machine Learning Based Approach to Identify SQL Injection Vulnerabilities.", "authors": ["<PERSON>"], "summary": "This paper presents a machine learning classifier designed to identify SQL injection vulnerabilities in PHP code. Both classical and deep learning based machine learning algorithms were used to train and evaluate classifier models using input validation and sanitization features extracted from source code files. On ten-fold cross validations a model trained using Convolutional Neural Network(CNN) achieved the highest precision (95.4%), while a model based on Multilayer Perceptron(MLP) achieved the highest recall (63.7%) and the highest f-measure (0.746).", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00164"}, {"primary_key": "3083402", "vector": [], "sparse_vector": [], "title": "Apricot: A Weight-Adaptation Approach to Fixing Deep Learning Models.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "A deep learning (DL) model is inherently imprecise. To address this problem, existing techniques retrain a DL model over a larger training dataset or with the help of fault injected models or using the insight of failing test cases in a DL model. In this paper, we present Apricot, a novel weight-adaptation approach to fixing DL models iteratively. Our key observation is that if the deep learning architecture of a DL model is trained over many different subsets of the original training dataset, the weights in the resultant reduced DL model (rDLM) can provide insights on the adjustment direction and magnitude of the weights in the original DL model to handle the test cases that the original DL model misclassifies. Apricot generates a set of such reduced DL models from the original DL model. In each iteration, for each failing test case experienced by the input DL model (iDLM), <PERSON><PERSON><PERSON> adjusts each weight of this iDLM toward the average weight of these rDLMs correctly classifying the test case and/or away from that of these rDLMs misclassifying the same test case, followed by training the weight-adjusted iDLM over the original training dataset to generate a new iDLM for the next iteration. The experiment using five state-of-the-art DL models shows that <PERSON><PERSON><PERSON> can increase the test accuracy of these models by 0.87%-1.55% with an average of 1.08%. The experiment also reveals the complementary nature of these rDLMs in Apr<PERSON>t.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00043"}, {"primary_key": "3083403", "vector": [], "sparse_vector": [], "title": "Automating Non-Blocking Synchronization In Concurrent Data Abstractions.", "authors": ["<PERSON><PERSON>", "Qing Yi", "<PERSON>"], "summary": "This paper investigates using compiler technology to automatically convert sequential C++ data abstractions, e.g., queues, stacks, maps, and trees, to concurrent lock-free implementations. By automatically tailoring a number of state-of-the-practice synchronization methods to the underlying sequential implementations of different data structures, our automatically synchronized code can attain performance competitive to that of manually-written concurrent data structures by experts and much better performance than heavier-weight support by software transactional memory (STM).", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00074"}, {"primary_key": "3083404", "vector": [], "sparse_vector": [], "title": "NeuralVis: Visualizing and Interpreting Deep Learning Models.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Qingkai Shi", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deep Neural Network(DNN) techniques have been prevalent in software engineering. They are employed to facilitate various software engineering tasks and embedded into many software applications. However, because DNNs are built upon a rich data-driven programming paradigm that employs plenty of labeled data to train a set of neurons to construct the internal system logic, analyzing and understanding their behaviors becomes a difficult task for software engineers. In this paper, we present an instance-based visualization tool for DNN, namely NeuralVis, to support software engineers in visualizing and interpreting deep learning models. NeuralVis is designed for: 1). visualizing the structure of DNN models, i.e., neurons, layers, as well as connections; 2). visualizing the data transformation process; 3). integrating existing adversarial attack algorithms for test input generation; 4). comparing intermediate layers' outputs of different inputs. To demonstrate the effectiveness of NeuralVis, we design a task-based user study involving ten participants on two classic DNN models, i.e., LeNet and VGG-12. The result shows NeuralVis can assist engineers in identifying critical features that determine the prediction results. Video: https://youtu.be/solkJri4Z44", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00113"}, {"primary_key": "3083405", "vector": [], "sparse_vector": [], "title": "Wuji: Automatic Online Combat Game Testing Using Evolutionary Deep Reinforcement Learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Chen"], "summary": "Game testing has been long recognized as a notoriously challenging task, which mainly relies on manual playing and scripting based testing in game industry. Even until recently, automated game testing still remains to be largely untouched niche. A key challenge is that game testing often requires to play the game as a sequential decision process. A bug may only be triggered until completing certain difficult intermediate tasks, which requires a certain level of intelligence. The recent success of deep reinforcement learning (DRL) sheds light on advancing automated game testing, without human competitive intelligent support. However, the existing DRLs mostly focus on winning the game rather than game testing. To bridge the gap, in this paper, we first perform an in-depth analysis of 1349 real bugs from four real-world commercial game products. Based on this, we propose four oracles to support automated game testing, and further propose Wuji, an on-the-fly game testing framework, which leverages evolutionary algorithms, DRL and multi-objective optimization to perform automatic game testing. <PERSON><PERSON> balances between winning the game and exploring the space of the game. Winning the game allows the agent to make progress in the game, while space exploration increases the possibility of discovering bugs. We conduct a large-scale evaluation on a simple game and two popular commercial games. The results demonstrate the effectiveness of <PERSON><PERSON> in exploring space and detecting bugs. Moreover, <PERSON><PERSON> found 3 previously unknown bugs, which have been confirmed by the developers, in the commercial games.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00077"}, {"primary_key": "3083406", "vector": [], "sparse_vector": [], "title": "iFeedback: Exploiting User Feedback for Real-Time Issue Detection in Large-Scale Online Service Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Lu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Large-scale online systems are complex, fast-evolving, and hardly bug-free despite the testing efforts. Backend system monitoring cannot detect many types of issues, such as UI related bugs, bugs with small impact on backend system indicators, or errors from third-party co-operating systems, etc. However, users are good informers of such issues: They will provide their feedback for any types of issues. This experience paper discusses our design of iFeedback, a tool to perform real-time issue detection based on user feedback texts. Unlike traditional approaches that analyze user feedback with computation-intensive natural language processing algorithms, iFeedback is focusing on fast issue detection, which can serve as a system life-condition monitor. In particular, iFeedback extracts word combination-based indicators from feedback texts. This allows iFeedback to perform fast system anomaly detection with sophisticated machine learning algorithms. iFeedback then further summarizes the texts with an aim to effectively present the anomaly to the developers for root cause analysis. We present our representative experiences in successfully applying iFeedback in tens of large-scale production online service systems in ten months.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00041"}, {"primary_key": "3083407", "vector": [], "sparse_vector": [], "title": "Improving Collaboration Efficiency in Fork-Based Development.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Fork-based development is a lightweight mechanism that allows developers to collaborate with or without explicit coordination. Although it is easy to use and popular, when developers each create their own fork and develop independently, their contributions are usually not easily visible to others. When the number of forks grows, it becomes very difficult to maintain an overview of what happens in individual forks, which would lead to additional problems and inefficient practices: lost contributions, redundant development, fragmented communities, and so on. Facing the problems mentioned above, we developed two complementary strategies: (1) Identifying existing best practices and suggesting evidence-based interventions for projects that are inefficient; (2) designing new interventions that could improve the awareness of a community using fork-based development, and help developers to detect redundant development to reduce unnecessary effort.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00144"}, {"primary_key": "3083408", "vector": [], "sparse_vector": [], "title": "Lancer: Your Code Tell Me What You Need.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Programming is typically a difficult and repetitive task. Programmers encounter endless problems during programming, and they often need to write similar code over and over again. To prevent programmers from reinventing wheels thus increase their productivity, we propose a context-aware code-to-code recommendation tool named Lancer. With the support of a Library-Sensitive Language Model (LSLM) and the BERT model, <PERSON><PERSON> is able to automatically analyze the intention of the incomplete code and recommend relevant and reusable code samples in real-time. A video demonstration of <PERSON><PERSON> can be found at https://youtu.be/tO9nhqZY35g. Lancer is open source and the code is available at https://github.com/sfzhou5678/Lancer.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00137"}, {"primary_key": "3083409", "vector": [], "sparse_vector": [], "title": "VisFuzz: Understanding and Intervening Fuzzing with Interactive Visualization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Chengnian Sun", "Yu <PERSON>"], "summary": "Fuzzing is widely used for vulnerability detection. One of the challenges for an efficient fuzzing is covering code guarded by constraints such as the magic number and nested conditions. Recently, academia has partially addressed the challenge via whitebox methods. However, high-level constraints such as array sorts, virtual function invocations, and tree set queries are yet to be handled. To meet this end, we present VisFuzz, an interactive tool for better understanding and intervening fuzzing process via real-time visualization. It extracts call graph and control flow graph from source code, maps each function and basic block to the line of source code and tracks real-time execution statistics with detail constraint contexts. With VisFuzz, test engineers first locate blocking constraints and then learn its semantic context, which helps to craft targeted inputs or update test drivers. Preliminary evaluations are conducted on four real-world programs in Google fuzzer-test-suite. Given additional 15 minutes to understand and intervene the state of fuzzing, the intervened fuzzing outperform the original pure AFL fuzzing, and the path coverage improvements range from 10.84% to 150.58%, equally fuzzed by for 12 hours.", "published": "2019-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2019.00106"}]