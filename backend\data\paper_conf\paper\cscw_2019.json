[{"primary_key": "2898109", "vector": [], "sparse_vector": [], "title": "Behavioral Petri Net Mining and Automated Analysis for Human-Computer Interaction Recommendations in Multi-Application Environments.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Process Mining is a famous technique which is frequently applied to Software Development Processes, while being neglected in Human-Computer Interaction (HCI) recommendation applications. Organizations usually train employees to interact with required IT systems. Often, employees, or users in general, develop their own strategies for solving repetitive tasks and processes. However, organizations find it hard to detect whether employees interact efficiently with IT systems or not. Hence, we have developed a method which detects inefficient behavior assuming that at least one optimal HCI strategy is known. This method provides recommendations to gradually adapt users' behavior towards the optimal way of interaction considering satisfaction of users. Based on users' behavior logs tracked by a Java application suitable for multi-application and multi-instance environments, we demonstrate the applicability for a specific task in a common Windows environment utilizing realistic simulated behaviors of users.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3331155"}, {"primary_key": "2897925", "vector": [], "sparse_vector": [], "title": "Who is the &quot;Human&quot; in Human-Centered Machine Learning: The Case of Predicting Mental Health from Social Media.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "\"Human-centered machine learning\" (HCML) combines human insights and domain expertise with data-driven predictions to answer societal questions. This area's inherent interdisciplinarity causes tensions in the obligations researchers have to the humans whose data they use. This paper studies how scientific papers represent human research subjects in HCML. Using mental health status prediction on social media as a case study, we conduct thematic discourse analysis on 55 papers to examine these representations. We identify five discourses that weave a complex narrative of who the human subject is in this research: Disorder/Patient, Social Media, Scientific, Data/Machine Learning, and Person. We show how these five discourses create paradoxical subject and object representations of the human, which may inadvertently risk dehumanization. We also discuss the tensions and impacts of interdisciplinary research; the risks of this work to scientific rigor, online communities, and mental health; and guidelines for stronger HCML research in this nascent area.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359249"}, {"primary_key": "2897988", "vector": [], "sparse_vector": [], "title": "A Newcomer&apos;s Guide to EICS, the Engineering Interactive Computing Systems Community.", "authors": ["<PERSON><PERSON><PERSON>", "Radu<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Welcome to EICS, the Engineering Interactive Computing Systems community, PACMHCI/EICS journal, and annual conference! In this short article, we introduce newcomers to the field and to our community with an overview of what EICS is and how it positions with respect to other venues in Human-Computer Interaction, such as CHI, UIST, and IUI, highlighting its legacy and paying homage to past scientific events from which EICS emerged. We also take this opportunity to enumerate and exemplify scientific contributions to the field of Engineering Interactive Computing Systems, which we hope to guide researchers and practitioners towards making their future PACMHCI/EICS submissions successful and impactful in the EICS community.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3300960"}, {"primary_key": "2898032", "vector": [], "sparse_vector": [], "title": "Discovering the Sweet Spot of Human-Computer Configurations: A Case Study in Information Extraction.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Interactive intelligent systems, i.e., interactive systems that employ AI technologies, are currently present in many parts of our social, public and political life. An issue reoccurring often in the development of these systems is the question regarding the level of appropriate human and computer contributions. Engineers and designers lack a way of systematically defining and delimiting possible options for designing such systems in terms of levels of automation. In this paper, we propose, apply and reflect on a method for human-computer configuration design. It supports the systematic investigation of the design space for developing an interactive intelligent system. We illustrate our method with a use case in the context of collaborative ideation. Here, we developed a tool for information extraction from idea content. A challenge was to find the right level of algorithmic support, whereby the quality of the information extraction should be as high as possible, but, at the same time, the human effort should be low. Such contradicting goals are often an issue in system development; thus, our method proposed helped us to conceptualize and explore the design space. Based on a critical reflection on our method application, we want to offer a complementary perspective to the value-centered design of interactive intelligent systems. Our overarching goal is to contribute to the design of so-called hybrid systems where humans and computers are partners.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359297"}, {"primary_key": "2897902", "vector": [], "sparse_vector": [], "title": "&apos;I&apos;m going to hell for laughing at this&apos;: <PERSON><PERSON>, <PERSON><PERSON>, and the Neutralisation of Aggression in Online Communities.", "authors": ["<PERSON><PERSON> R<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The subreddit r/RoastMe presents an intriguing case of how alternative norms can emerge in subversive online communities, allowing behaviours conventionally condemned as inappropriate to be reframed as acceptable. In this community, users post photos of themselves with the explicit expectation of being mocked or ridiculed by others. This mixed-methods, within-subjects experiment explores the influence of three factors that allow negative comments to be framed as acceptable and appropriate within RoastMe: humour, a mean (but funny) normative tone, and explicit articulation of these norms. 117 participants read, rated and reported their intended responses to humorous and non-humorous comments presented as being from RoastMe (explicitly mean but funny), ToastMe (explicitly positive), and two fictionalised communities where the normative tone was not explicitly defined (the mean but funny RateMe, and the positive DescribeMe). Results indicated clear interaction effects between community tone and norm explicitness, whereby comments from RoastMe were consistently rated and responded to most positively, and separate effects of humour on comment ratings and responses. Individual-level moral disengagement appeared central in allowing participants to excuse negative comments in humorous or permissive contexts. Consistent with benign violation theory, the explicitly negative tone of RoastMe was seen to create a shared understanding that users posting photos would expect and not be harmed by comments, allowing participants to reframe interactions as safe, acceptable and funny.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359254"}, {"primary_key": "2897906", "vector": [], "sparse_vector": [], "title": "Computing Education for Intercultural Learning: Lessons from the Nairobi Play Project.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Tapan <PERSON>", "<PERSON><PERSON>"], "summary": "This paper explores computing education as a potential site for intercultural learning and encounter in post-conflict environments. It reports on ethnographic fieldwork from the Nairobi Play Project, a constructionist educational program serving adolescents aged 14-18 in urban and rural multi-ethnic refugee communities in Kenya. While the program offers programming and game design instruction, an equal goal is to foster interaction, collaboration, dialogue and understanding across cultural backgrounds. Based on fieldwork from two project cycles involving 5 after-school classes of 12-24 students each, we describe key affordances for encounter, important resistances to be managed or overcome, and emergent complications in the execution of such programs. We argue that many important accomplishments of intercultural learning occur through moments of friction, breakdowns, and gaps -- for example, technical challenges that produce sites of shared humour; frictions between intercultural activities and computing activities; acts of disrupting order; and unstructured time that students collaboratively fill in. We also describe significant complications in such programs, including pressures to adopt norms and practices consistent with dominant or majority cultures, and instances of intercultural bonding over artefacts with xenophobic themes. We reflect on the implications of these phenomena for the design of future programs that use computing as a backbone for intercultural learning or diversity and inclusion efforts in CSCW, ICTD, and allied fields of work.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359154"}, {"primary_key": "2897914", "vector": [], "sparse_vector": [], "title": "The Coerciveness of the Primary Key: Infrastructure Problems in Human Services Work.", "authors": ["<PERSON>", "<PERSON><PERSON> M<PERSON>", "<PERSON>"], "summary": "The premise of a primary key is simple enough: every record or row in a table should have some number or string that can uniquely identify it. Primary keys are essential for linking data spread across database tables, and for looking up and retrieving data from specific records. Yet for an identifier that seems so straightforward and uncontroversial, we find myriad ways that this unassuming bit of infrastructure has an outsized influence in human services work. Through case studies of the organizational networks of two nonprofit human services organizations, we find that different stakeholders use variants of identifiers to support work practices that are far more complex and social than the linking of tables or the lookup of data. Yet we also find that the low-level technical properties of the primary key are often coercive, forcing end-users to work on the infrastructure's terms-influencing the nature and order of the work, creating new forms of work, and influencing the tenor of the relationships among stakeholders. The technical abstractions of the underlying infrastructure, then, start to become the de facto public policy. We conclude by offering design provocations for better supporting identification across a variety of contexts.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359153"}, {"primary_key": "2897921", "vector": [], "sparse_vector": [], "title": "&quot;Hello AI&quot;: Uncovering the Onboarding Needs of Medical Practitioners for Human-AI Collaborative Decision-Making.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Although rapid advances in machine learning have made it increasingly applicable to expert decision-making, the delivery of accurate algorithmic predictions alone is insufficient for effective human-AI collaboration. In this work, we investigate the key types of information medical experts desire when they are first introduced to a diagnostic AI assistant. In a qualitative lab study, we interviewed 21 pathologists before, during, and after being presented deep neural network (DNN) predictions for prostate cancer diagnosis, to learn the types of information that they desired about the AI assistant. Our findings reveal that, far beyond understanding the local, case-specific reasoning behind any model decision, clinicians desired upfront information about basic, global properties of the model, such as its known strengths and limitations, its subjective point-of-view, and its overall design objective--what it's designed to be optimized for. Participants compared these information needs to the collaborative mental models they develop of their medical colleagues when seeking a second opinion: the medical perspectives and standards that those colleagues embody, and the compatibility of those perspectives with their own diagnostic patterns. These findings broaden and enrich discussions surrounding AI transparency for collaborative decision-making, providing a richer understanding of what experts find important in their introduction to AI assistants before integrating them into routine practice.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359206"}, {"primary_key": "2897939", "vector": [], "sparse_vector": [], "title": "VidLyz: An Interactive Approach to Assist Novice Entrepreneurs in Making Persuasive Campaign Videos.", "authors": ["<PERSON><PERSON><PERSON>", "Brittany R. L. <PERSON>", "Wai-Tat Fu", "<PERSON><PERSON>"], "summary": "Videos are essential for successful crowdfunding campaigns. However, without knowledge of the underlying persuasion factors, novice entrepreneurs may find it difficult to optimize their videos for success. This paper presents VidLyz, a novel assistive tool that allows users to explore the implications of audience-engagement persuasion factors in the context of campaign videos through contrasting examples, crowd-sourced subjective ratings, and feedback on engagement factors. VidLyz promotes active thinking about the impact of audience persuasion factors in making an effective campaign video by guiding novice entrepreneurs in planning materials of their videos on their own, with consideration of the product category, the target audience, and audience-product interactions. To evaluate our system, we collected subjective ratings and feedback on persuasion factors for 140 Kickstarter campaign videos from 2100 crowd workers and presented them through our prototype VidLyz tool. A user study with 45 novice users and five previous campaign creators found that our tool was useful for understanding the implication and relative importance of the persuasion factors of the campaign videos. The interactive and active thinking elements of VidLyz promoted novice users to make coherent and persuasive pre-production plan (using storyboards) for their proposed campaign videos. A follow-up user study showed that these storyboards had a higher likelihood of getting funded by crowd workers than those with less persuasive pre-production plan. We concluded with design implications to better support novice entrepreneurs.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359145"}, {"primary_key": "2897943", "vector": [], "sparse_vector": [], "title": "Pen-and-paper Rituals in Service Interaction: Combining High-touch and High-tech in Financial Advisory Encounters.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Advisory services are ritualized encounters between an expert and an advisee. Empathetic, high-touch relationship between those two parties was identified as the key aspect of a successful advisory encounter. To facilitate the high-touch interaction, advisors established rituals which stress the unique, individual character of each client and each single encounter. Simultaneously, organizations like banks or insurances rolled out tools and technologies for use in advisory services to offer a uniform experience and consistent quality across branches and advisors. As a consequence, advisors were caught between the high-touch and high-tech aspects of an advisory service. This manuscript presents a system that accommodates for high-touch rituals and practices and combines them with high-tech collaboration. The proposed solution augments pen-and-paper practices with digital content and affords new material performances coherent with the existing rituals. The evaluation in realistic mortgage advisory services unveils the potential of mixed reality approaches for application in professional, institutional settings. The blow-by-blow analysis of the conversations reveals how an advisory service can become equally high-tech and high-touch thanks to a careful ritual-oriented system design. As a consequence, this paper presents a solution to the tension between the high-touch and high-tech tendencies in advisory services.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359326"}, {"primary_key": "2897956", "vector": [], "sparse_vector": [], "title": "&quot;Is my phone hacked?&quot; Analyzing Clinical Computer Security Interventions with Survivors of Intimate Partner Violence.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Intimate partner abusers use technology to track, monitor, harass, and otherwise harm their victims, and prior work reports that victims have few resources for obtaining help with such attacks. This paper presents a qualitative analysis of data from a field study of an approach to helping survivors of intimate partner violence (IPV) with technology abuse. In this approach, called clinical computer security, a trained technologist performs a face-to-face consultation with an IPV survivor to help them understand and navigate technology issues. Findings from consultations with 31 survivors, as well as IPV professionals working on their behalf, uncovered a range of digital security and privacy vulnerabilities exacerbated by the nuanced social context of such abuse. In this paper we explore survivor experiences with, and reactions to, the consultations, discussing (1) the ways in which survivors present their tech concerns, (2) the cooperative work required to guide survivors towards understanding probable causes of tech insecurity, (3) survivors' reactions to the consultations, particularly when security vulnerabilities or spyware are discovered, and (4) the role we play as consultants and interventionists in the complex socio-technical systems involved in mitigating IPV. We conclude by discussing some of the broad ethical and sustainability challenges raised by our work, and provide design opportunities for tech platforms to better support survivors of IPV.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359304"}, {"primary_key": "2897962", "vector": [], "sparse_vector": [], "title": "The Rise and Fall of the Note: Changing Paper Lengths in ACM CSCW, 2000-2018.", "authors": ["<PERSON><PERSON>"], "summary": "In this note, I quantitatively examine various trends in the lengths of published papers in ACM CSCW from 2000-2018, focusing on several major transitions in editorial and reviewing policy. The focus is on the rise and fall of the 4-page note, which was introduced in 2004 as a separate submission type to the 10-page double-column \"full paper\" format. From 2004-2012, 4-page notes of 2,500 to 4,500 words consistently represented about 20-35\\% of all publications. In 2013, minimum and maximum page lengths were officially removed, with no formal distinction made between full papers and notes. The note soon completely disappeared as a distinct genre, which co-occurred with a trend in steadily rising paper lengths. I discuss such findings both as they directly relate to local concerns in CSCW and in the context of longstanding theoretical discussions around genre theory and how socio-technical structures and affordances impact participation in distributed, computer-mediated organizations and user-generated content platforms. There are many possible explanations for the decline of the note and the emergence of longer and longer papers, which I identify for future work. I conclude by addressing the implications of such findings for the CSCW community, particularly given how genre norms impact what kinds of scholarship and scholars thrive in CSCW, as well as whether new top-down rules or bottom-up guidelines ought to be developed around paper lengths and different kinds of contributions.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359324"}, {"primary_key": "2897966", "vector": [], "sparse_vector": [], "title": "Human Decision Making with Machine Assistance: An Experiment on Bailing and Jailing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Much of political debate focuses on the concern that machines might take over. Yet in many domains it is much more plausible that the ultimate choice and responsibility remain with a human decision-maker, but that she is provided with machine advice. A quintessential illustration is the decision of a judge to bail or jail a defendant. In multiple jurisdictions in the US, judges have access to a machine prediction about a defendant's recidivism risk. In our study, we explore how receiving machine advice influences people's bail decisions. We run a vignette experiment with laypersons whom we test on a subsample of cases from the database of this prediction tool. In study 1, we ask them to predict whether defendants will recidivate before tried, and manipulate whether they have access to machine advice. We find that receiving machine advice has a small effect, which is biased in the direction of predicting no recidivism. In the field, human decision makers sometimes have a chance, after the fact, to learn whether the machine has given good advice. In study 2, after each trial we inform participants of ground truth. This does not make it more likely that they follow the advice, despite the fact that the machine is (on average) slightly more accurate than real judges. This also holds if initially the advice is mostly correct, or if it initially is mostly to predict (no) recidivism. Real judges know that their decisions affect defendants' lives. They may also be concerned about reelection or promotion. Hence a lot is at stake. In study 3 we emulate high stakes by giving participants a financial incentive. An incentive to find the ground truth, or to avoid false positive or false negatives, does not make participants more sensitive to machine advice. But an incentive to follow the advice is effective.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359280"}, {"primary_key": "2898007", "vector": [], "sparse_vector": [], "title": "Creating Interactive Scientific Publications using Bindings.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many scientific publications report on computational results based on code and data, but even when code and data are published, the main text is usually provided in a separate, traditional format such as PDF. Since code, data, and text are not linked on a deep level, it is difficult for readers and reviewers to understand and retrace how the authors achieved a specific result that is reported in the main text, e.g. a figure, table, or number. In addition, to make use of new the opportunities afforded by data and code availability, such as re-running analyses with changed parameters, considerable effort is required. In order to overcome this issue and to enable more interactive publications that support scientists in more deeply exploring the reported results, we present the concept, implementation, and initial evaluation of bindings. A binding describes which data subsets, code lines, and parameters produce a specific result that is reported in the main text (e.g. a figure or number). Based on a prototypical implementation of these bindings, we propose a toolkit for authors to easily create interactive figures by connecting specific UI widgets (e.g. a slider) to parameters. In addition to inspecting code and data, readers can then manipulate the parameter and see how the results change. We evaluated the approach by applying it to a set of existing articles. The results provide initial evidence that the concept is feasible and applicable to many papers with moderate effort.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3331158"}, {"primary_key": "2898023", "vector": [], "sparse_vector": [], "title": "Gestures as Intrinsic Creativity Support: Understanding the Usage and Function of Hand Gestures in Computer-Mediated Group Brainstorming.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Interpersonal communication is essential to group creativity tasks, such as group brainstorming. However, what's easily overlooked in supporting group creativity is the potential use of nonverbal behavior in communication. When people communicate verbally (e.g., discussing a topic through conversation), they may also co-produce hand movements along with verbal contents as part of the communication process. Going beyond using external interventions for creativity gain, we examine the potential to facilitate worker's in-conversation gesture use as an intrinsic mechanism for bolstering group creativity. In this paper, we aim to gain a deeper understanding of the usage and function of hand gestures in computer-mediated group brainstorming. Through a laboratory study, we verified that metaphoric gestures, or producing spatial cues with hands to convey intended concepts, can best influence self's and partner's idea generation. Also, the positive effect of metaphoric gesture is independent of media richness of communication medium (e.g., whether there's visibility or not). The results help to fill in the knowledge gap regarding how gesture use may relate to group creativity, and reveal the potential viability of shaping metaphoric gesture use as an intrinsic creativity support.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3361124"}, {"primary_key": "2898034", "vector": [], "sparse_vector": [], "title": "Tangible BioNets: Multi-Surface and Tangible Interactions for Exploring Structural Features of Biological Networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Biological networks analysis has become a systematic and large-scale phenomenon. Most biological systems are often difficult to interpret due to the complexity of relationships and structural features. Moreover, existing primarily web-based interfaces for biological networks analysis often have limitations in usability as well as in supporting high-level reasoning and collaboration. Interactive surfaces coupled with tangible interactions offer opportunities to improve the comparison and analysis of large biological networks, which can aid researchers in making hypotheses and forming insights. We present Tangible BioNets, an active tangible and multi-surface system that allows users with diverse expertise to explore and understand the structural and functional aspects of biological organisms individually or collaboratively. The system was designed through an iterative co-design process and facilitates the exploration of biological network topology, catalyzing the generation of new insights. We describe a first informal evaluation with expert users and discuss considerations for designing tangible and multi-surface systems for large biological datasets.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3331156"}, {"primary_key": "2898073", "vector": [], "sparse_vector": [], "title": "Polyphony: Programming Interfaces and Interactions with the Entity-Component-System Model.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper introduces a new Graphical User Interface (GUI) and Interaction framework based on the Entity-Component-System model (ECS). In this model, interactive elements (Entities) are characterized only by their data (Components). Behaviors are managed by continuously running processes (Systems) which select entities by the Components they possess. This model facilitates the handling of behaviors and promotes their reuse. It provides developers with a simple yet powerful composition pattern to build new interactive elements with Components. It materializes interaction devices as Entities and interaction techniques as a sequence of Systems operating on them. We present Polyphony, an experimental toolkit implementing this approach, and discuss our interpretation of the ECS model in the context of GUIs programming.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3331150"}, {"primary_key": "2898086", "vector": [], "sparse_vector": [], "title": "How Computers See Gender: An Evaluation of Gender Classification in Commercial Facial Analysis Services.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Investigations of facial analysis (FA) technologies-such as facial detection and facial recognition-have been central to discussions about Artificial Intelligence's (AI) impact on human beings. Research on automatic gender recognition, the classification of gender by FA technologies, has raised potential concerns around issues of racial and gender bias. In this study, we augment past work with empirical data by conducting a systematic analysis of how gender classification and gender labeling in computer vision services operate when faced with gender diversity. We sought to understand how gender is concretely conceptualized and encoded into commercial facial analysis and image labeling technologies available today. We then conducted a two-phase study: (1) a system analysis of ten commercial FA and image labeling services and (2) an evaluation of five services using a custom dataset of diverse genders using self-labeled Instagram images. Our analysis highlights how gender is codified into both classifiers and data standards. We found that FA services performed consistently worse on transgender individuals and were universally unable to classify non-binary genders. In contrast, image labeling often presented multiple gendered concepts. We also found that user perceptions about gender performance and identity contradict the way gender performance is encoded into the computer vision infrastructure. We discuss our findings from three perspectives of gender identity (self-identity, gender performativity, and demographic identity) and how these perspectives interact across three layers: the classification infrastructure, the third-party applications that make use of that infrastructure, and the individuals who interact with that software. We employ <PERSON><PERSON> and <PERSON>'s concepts of \"torque\" and \"residuality\" to further discuss the social implications of gender classification. We conclude by outlining opportunities for creating more inclusive classification infrastructures and datasets, as well as with implications for policy.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359246"}, {"primary_key": "2898087", "vector": [], "sparse_vector": [], "title": "SAPIENS: Towards Software Architecture to Support Peripheral Interaction in Smart Environments.", "authors": ["Ovidiu<PERSON><PERSON>", "Radu<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present SAPIENS, a software architecture designed to support engineering of interactive systems featuring peripheral interaction in the context of smart environments. SAPIENS introduces dedicated components for user and device tracking, attention detection, priority management for devices, tasks, and notifications, context-awareness inference, user interruptibility prediction, and device interchangeability that can be instantiated at will according to the needs of the application. To implement these components effectively, SAPIENS employs event-based processing by reusing the core engine of a recently introduced software architecture, Euphoria (<PERSON><PERSON><PERSON> et al., 2019), that was specifically designed for engineering interactions in smart environments with heterogeneous I/O devices, and relies entirely on web standards, protocols, and open data-interchange formats, such as JavaScript, WebSockets, HTTP, and JSON. This inheritance makes SAPIENS flexible and adaptable to support implementation of diverse application scenarios for peripheral interaction and for a wide variety of smart environments, devices, platforms, data formats, and contexts of use. We present our design criteria for SAPIENS regarding (1) event handling techniques, (2) quality, (3) contextual, and (4) attention-related properties, and describe its components and dataflows that make SAPIENS a specialized software architecture for peripheral interaction scenarios. We also demonstrate SAPIENS with a practical application, inspired and adapted from <PERSON><PERSON>'s (2013) classical example for peripheral interaction, for which we provide an online simulation tool that researchers and practitioners can readily use to consult actual JavaScript code implementing the inner logic of selected components of our architecture as well as to observe live JSON messages exchanged by the various components of SAPIENS.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3331153"}, {"primary_key": "2898097", "vector": [], "sparse_vector": [], "title": "I Say, You Say, We Say: Using Spoken Language to Model Socio-Cognitive Processes during Computer-Supported Collaborative Problem Solving.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Sidney K. D&apos;Mello"], "summary": "Collaborative problem solving (CPS) is a crucial 21st century skill; however, current technologies fall short of effectively supporting CPS processes, especially for remote, computer-enabled interactions. In order to develop next-generation computer-supported collaborative systems that enhance CPS processes and outcomes by monitoring and responding to the unfolding collaboration, we investigate automated detection of three critical CPS process ? construction of shared knowledge, negotiation/coordination, and maintaining team function ? derived from a validated CPS framework. Our data consists of 32 triads who were tasked with collaboratively solving a challenging visual computer programming task for 20 minutes using commercial videoconferencing software. We used automatic speech recognition to generate transcripts of 11,163 utterances, which trained humans coded for evidence of the above three CPS processes using a set of behavioral indicators. We aimed to automate the trained human-raters' codes in a team-independent fashion (current study) in order to provide automatic real-time or offline feedback (future work). We used Random Forest classifiers trained on the words themselves (bag of n-grams) or with word categories (e.g., emotions, thinking styles, social constructs) from the Linguistic Inquiry Word Count (LIWC) tool. Despite imperfect automatic speech recognition, the n-gram models achieved AUROC (area under the receiver operating characteristic curve) scores of .85, .77, and .77 for construction of shared knowledge, negotiation/coordination, and maintaining team function, respectively; these reflect 70%, 54%, and 54% improvements over chance. The LIWC-category models achieved similar scores of .82, .74, and .73 (64%, 48%, and 46% improvement over chance). Further, the LIWC model-derived scores predicted CPS outcomes more similar to human codes, demonstrating predictive validity. We discuss embedding our models in collaborative interfaces for assessment and dynamic intervention aimed at improving CPS outcomes.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359296"}, {"primary_key": "2898118", "vector": [], "sparse_vector": [], "title": "&quot;I Love the Feeling of Being on Stage, but I Become Greedy&quot;: Exploring the Impact of Monetary Incentives on Live Streamers&apos; Social Interactions and Streaming Content.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Wai-Tat Fu"], "summary": "Live streaming is an emergent social medium that allows remote interaction between the streamer and an audience of any size. Major live-streaming platforms in some Asian countries have a digital gift-giving feature that allows viewers to directly reward streamers during live sessions. Streamers can later exchange the digital gifts they have received for money, and this monetary incentive appears likely to influence how they interact with their viewers and generate live-streaming content. However, the precise nature and mechanisms of such impact have not previously been explored. Therefore, this qualitative study with 13 streamer participants examines how digital gifting influences streamers' motivations and the nature of both the content that they generate and their social interactions with their audiences. It reports that the digital-gifting function serves as a major incentive for active streaming, but may also disincentivize some streamers from continuing to contribute, for reasons that will be explored. Moreover, the participants devised strategies for both content generation and social interaction with the specific objective of earning gifts from viewers: practices that, in some cases, appeared to limit the quality of their live-streaming content. It was also noted that the streamers tended to have constrained social relationships with their viewers, in part because such relationships were seen as unequal or one-sided due to gift-giving behavior. The paper concludes with a discussion of design considerations for the incorporation of gift-giving features into live-streaming platforms, and additional recommendations for future research and the design of such platforms.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359194"}, {"primary_key": "2898120", "vector": [], "sparse_vector": [], "title": "How Data Scientists Use Computational Notebooks for Real-Time Collaboration.", "authors": ["April <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Effective collaboration in data science can leverage domain expertise from each team member and thus improve the quality and efficiency of the work. Computational notebooks give data scientists a convenient interactive solution for sharing and keeping track of the data exploration process through a combination of code, narrative text, visualizations, and other rich media. In this paper, we report how synchronous editing in computational notebooks changes the way data scientists work together compared to working on individual notebooks. We first conducted a formative survey with 195 data scientists to understand their past experience with collaboration in the context of data science. Next, we carried out an observational study of 24 data scientists working in pairs remotely to solve a typical data science predictive modeling problem, working on either notebooks supported by synchronous groupware or individual notebooks in a collaborative setting. The study showed that working on the synchronous notebooks improves collaboration by creating a shared context, encouraging more exploration, and reducing communication costs. However, the current synchronous editing features may lead to unbalanced participation and activity interference without strategic coordination. The synchronous notebooks may also amplify the tension between quick exploration and clear explanations. Building on these findings, we propose several design implications aimed at better supporting collaborative editing in computational notebooks, and thus improving efficiency in teamwork among data scientists.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359141"}, {"primary_key": "2898121", "vector": [], "sparse_vector": [], "title": "Human-AI Collaboration in Data Science: Exploring Data Scientists&apos; Perceptions of Automated AI.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The rapid advancement of artificial intelligence (AI) is changing our lives in many ways. One application domain is data science. New techniques in automating the creation of AI, known as AutoAI or AutoML, aim to automate the work practices of data scientists. AutoAI systems are capable of autonomously ingesting and pre-processing data, engineering new features, and creating and scoring models based on a target objectives (e.g. accuracy or run-time efficiency). Though not yet widely adopted, we are interested in understanding how AutoAI will impact the practice of data science. We conducted interviews with 20 data scientists who work at a large, multinational technology company and practice data science in various business settings. Our goal is to understand their current work practices and how these practices might change with AutoAI. Reactions were mixed: while informants expressed concerns about the trend of automating their jobs, they also strongly felt it was inevitable. Despite these concerns, they remained optimistic about their future job security due to a view that the future of data science work will be a collaboration between humans and AI systems, in which both automation and human expertise are indispensable.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359313"}, {"primary_key": "2898127", "vector": [], "sparse_vector": [], "title": "Evaluating the Promise of Human-Algorithm Collaborations in Everyday Work Practices.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Human-algorithm interaction is a growing phenomenon of interest as the use of machine learning (ML) capabilities in everyday technologies becomes more commonplace. In the workplace, such developments raise questions about how people not only make sense of algorithmic actions, but also figure out ways to collaborate with tools and systems that integrate algorithmic outputs. We draw on a field study of IT infrastructure design and report on the experiences of highly-skilled IT architects with the natural language processing (NLP) capabilities in an intelligent system under development to support their solution design work. While architects were supportive of the potential of NLP to enhance their solutioning work, they faced challenges in integrating such capabilities into their existing collaborative work practices. We discuss how these findings add nuance and complexity to discourse around the future of work.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359245"}, {"primary_key": "2898128", "vector": [], "sparse_vector": [], "title": "Home Worlds: Situating Domestic Computing in Everyday Life Through a Study of DIY Home Repair.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We draw on a qualitative study of homeowners who engage in do-it-yourself (DIY) home repair and introduce \"home worlds\" as a conceptual lens to understand how interactions with the home as a built environment (local and mechanical engagements with its material components) are situated in broad, diffuse, and constraint-rich social worlds. A \"home worlds\" perspective challenges domestic computing inquiries to adopt a broader orientation when considering a home's users and requirements; our case of home repair, in particular, provokes us to consider the home as embedded in communities and framed in relation to the precarious constraints of everyday life. We discuss how these insights open up new avenues for future research and design of collaborative domestic computing technologies. ?", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359263"}, {"primary_key": "2898140", "vector": [], "sparse_vector": [], "title": "StressMon: Scalable Detection of Perceived Stress and Depression Using Passive Sensing of Changes in Work Routines and Group Interactions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Stress and depression are a common affliction in all walks of life. When left unmanaged, stress can inhibit productivity or cause depression. Depression can occur independently of stress. There has been a sharp rise in mobile health initiatives to monitor stress and depression. However, these initiatives usually require users to install dedicated apps or multiple sensors, making such solutions hard to scale. Moreover, they emphasise sensing individual factors and overlook social interactions, which plays a significant role in influencing stress and depression while being a part of a social system. We present StressMon, a stress and depression detection system that leverages single-attribute location data, passively sensed from the WiFi infrastructure. Using the location data, it extracts a detailed set of movement, and physical group interaction pattern features without requiring explicit user actions or software installation on client devices. These features are used in two different machine learning models to detect stress and depression. To validate StressMon, we conducted three different longitudinal studies at a university with different groups of students, totalling up to 108 participants. Our evaluation demonstrated StressMon detecting severely stressed students with a 96.01% True Positive Rate (TPR), an 80.76% True Negative Rate (TNR), and a 0.97 area under the ROC curve (AUC) score (a score of 1 indicates a perfect binary classifier) using a 6-day prediction window. In addition, StressMon was able to detect depression at 91.21% TPR, 66.71% TNR, and 0.88 AUC using a 15-day window. We end by discussing how StressMon can expand CSCW research, especially in areas involving collaborative practices for mental health management.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359139"}, {"primary_key": "2898141", "vector": [], "sparse_vector": [], "title": "Dissonance Between Human and Machine Understanding.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Complex machine learning models are deployed in several critical domains including healthcare and autonomous vehicles nowadays, albeit as functional black boxes. Consequently, there has been a recent surge in interpreting decisions of such complex models in order to explain their actions to humans. Models that correspond to human interpretation of a task are more desirable in certain contexts and can help attribute liability, build trust, expose biases and in turn build better models. It is, therefore, crucial to understand how and which models conform to human understanding of tasks. In this paper, we present a large-scale crowdsourcing study that reveals and quantifies the dissonance between human and machine understanding, through the lens of an image classification task. In particular, we seek to answer the following questions: Which (well-performing) complex ML models are closer to humans in their use of features to make accurate predictions? How does task difficulty affect the feature selection capability of machines in comparison to humans? Are humans consistently better at selecting features that make image recognition more accurate? Our findings have important implications on human-machine collaboration, considering that a long term goal in the field of artificial intelligence is to make machines capable of learning and reasoning like humans.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359158"}, {"primary_key": "2897898", "vector": [], "sparse_vector": [], "title": "Life-Tags: A Smartglasses-based System for Recording and Abstracting Life with Tag Clouds.", "authors": ["<PERSON>", "Radu<PERSON><PERSON>"], "summary": "We introduce \"Life-Tags,\" a wearable, smartglasses-based system for abstracting life in the form of clouds of tags and concepts automatically extracted from snapshots of the visual reality recorded by wearable video cameras. Life-Tags summarizes users' life experiences using word clouds, highlighting the \"executive summary\" of what the visual experience felt like for the smartglasses user during some period of time, such as a specific day, week, month, or the last hour. In this paper, we focus on (i) design criteria and principles of operation for Life-Tags, such as its first-person, eye-level perspective for recording life, passive logging mode, and privacy-oriented operation, as well as on (ii) technical and engineering aspects for implementing Life-Tags, such as the block architecture diagram highlighting devices, software modules, third-party services, and dataflows. We also conduct a technical evaluation of Life-Tags and report results from a controlled experiment that generated 21,600 full HD snapshots from six indoor and outdoor scenarios, representative of everyday life activities, such as walking, eating, traveling, etc., with a total of 180 minutes of recorded life to abstract with tag clouds. Our experimental results and Life-Tags prototype inform design and engineering of future life abstracting systems based on smartglasses and wearable video cameras to ensure effective generation of rich clouds of concepts, reflective of the visual experience of the smartglasses user.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3331157"}, {"primary_key": "2897899", "vector": [], "sparse_vector": [], "title": "Hateful People or Hateful Bots?: Detection and Characterization of Bots Spreading Religious Hatred in Arabic Social Media.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Arabic Twitter space is crawling with bots that fuel political feuds, spread misinformation, and proliferate sectarian rhetoric. While efforts have long existed to analyze and detect English bots, Arabic bot detection and characterization remains largely understudied. In this work, we contribute new insights into the role of bots in spreading religious hatred on Arabic Twitter and introduce a novel regression model that can accurately identify Arabic language bots. Our assessment shows that existing tools that are highly accurate in detecting English bots don't perform as well on Arabic bots. We identify the possible reasons for this poor performance, perform a thorough analysis of linguistic, content, behavioral and network features, and report on the most informative features that distinguish Arabic bots from humans as well as the differences between Arabic and English bots. Our results mark an important step toward understanding the behavior of malicious bots on Arabic Twitter and pave the way for a more effective Arabic bot detection tools.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359163"}, {"primary_key": "2897900", "vector": [], "sparse_vector": [], "title": "Your Stance is Exposed! Analysing Possible Factors for Stance Detection on Social Media.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "To what extent user's stance towards a given topic could be inferred? Most of the studies on stance detection have focused on analysing user's posts on a given topic to predict the stance. However, the stance in social media can be inferred from a mixture of signals that might reflect user's beliefs including posts and online interactions. This paper examines various online features of users to detect their stance towards different topics. We compare multiple set of features, including on-topic content, network interactions, user's preferences, and online network connections. Our objective is to understand the online signals that can reveal the users' stance. Experimentation is applied on tweets dataset from the SemEval stance detection task, which covers five topics. Results show that stance of a user can be detected with multiple signals of user's online activity, including their posts on the topic, the network they interact with or follow, the websites they visit, and the content they like. The performance of the stance modelling using different network features are comparable with the state-of-the-art reported model that used textual content only. In addition, combining network and content features leads to the highest reported performance to date on the SemEval dataset with F-measure of 72.49%. We further present an extensive analysis to show how these different set of features can reveal stance. Our findings have distinct privacy implications, where they highlight that stance is strongly embedded in user's online social network that, in principle, individuals can be profiled from their interactions and connections even when they do not post about the topic.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359307"}, {"primary_key": "2897901", "vector": [], "sparse_vector": [], "title": "Discrimination through Optimization: How Facebook&apos;s Ad Delivery Can Lead to Biased Outcomes.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The enormous financial success of online advertising platforms is partially due to the precise targeting features they offer. Although researchers and journalists have found many ways that advertisers can target---or exclude---particular groups of users seeing their ads, comparatively little attention has been paid to the implications of the platform's ad delivery process, comprised of the platform's choices about which users see which ads. It has been hypothesized that this process can \"skew\" ad delivery in ways that the advertisers do not intend, making some users less likely than others to see particular ads based on their demographic characteristics. In this paper, we demonstrate that such skewed delivery occurs on Facebook, due to market and financial optimization effects as well as the platform's own predictions about the \"relevance\" of ads to different groups of users. We find that both the advertiser's budget and the content of the ad each significantly contribute to the skew of Facebook's ad delivery. Critically, we observe significant skew in delivery along gender and racial lines for \"real\" ads for employment and housing opportunities despite neutral targeting parameters. Our results demonstrate previously unknown mechanisms that can lead to potentially discriminatory ad delivery, even when advertisers set their targeting parameters to be highly inclusive. This underscores the need for policymakers and platforms to carefully consider the role of the ad delivery optimization run by ad platforms themselves---and not just the targeting choices of advertisers---in preventing discrimination in digital advertising.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359301"}, {"primary_key": "2897903", "vector": [], "sparse_vector": [], "title": "Individual and Collaborative Behaviors of Rideshare Drivers in Protecting their Safety.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The safety of passengers of rideshare apps has received attention from researchers, yet there is a lack of research on safety of rideshare drivers in the context of CSCW and HCI. As drivers are also an important user in the ecosystem of the ridesharing systems, we conducted interviews with drivers in the U.S. to understand how they, individually and collaboratively, address safety related issues they face conducting their job. We identified the factors that contributed to drivers' feelings of safety and the strategies they engaged in to protect themselves. We found that drivers relied on methods that were technical, social, and physical, to ensure their safety and engaged in informal collaborative and communicative activities with other drivers inside and outside of the ridesharing system. We discuss implications for future design for ridesharing apps and other location-based computer-supported collaborative systems that have potential safety hazards.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359319"}, {"primary_key": "2897904", "vector": [], "sparse_vector": [], "title": "Self-declared Throwaway Accounts on Reddit: How Platform Affordances and Shared Norms enable Parenting Disclosure and Support.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Parents can be subjected to scrutiny and judgment for their parenting choices. Much of this scrutiny is experienced online, especially around stigmatized topics such as divorce, custody, postpartum depression, and miscarriage. Prior theory suggests that parents might be able to access greater support online when anonymous, but other evidence suggests that anonymity may increase bad behavior. Drawing from ten years of Reddit parenting boards, we show that parents are more likely to discuss potentially stigmatizing topics using anonymous (\"throwaway\") accounts. We find that, on average, throwaway comments are more likely to receive a response, receive more responses that are longer, and receive responses that have higher karma scores than topically similar comments posted by non-throwaway accounts. We argue that self-identified throwaway accounts provide a crucial environment for supporting parents with stigmatizing experiences. They also provide a shared platform signal (the throwaway account) which enables other Reddit users to access shared experiences and support. We propose that a hybrid combination of identified and anonymous platforms could provide more supportive online experiences for parents and other users.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359237"}, {"primary_key": "2897905", "vector": [], "sparse_vector": [], "title": "Narrative Paths and Negotiation of Power in Birth Stories.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Birth stories have become increasingly common on the internet, but they have received little attention as a computational dataset. These unsolicited, publicly posted stories provide rich descriptions of decisions, emotions, and relationships during a common but sometimes traumatic medical experience. These personal details can be illuminating for medical practitioners, and due to their shared structures, birth stories are also an ideal testing ground for narrative analysis techniques. We present an analysis of 2,847 birth stories from an online forum and demonstrate the utility of these stories for computational work. We discover clear sentiment, topic and persona-based patterns that both model the expected narrative event sequences of birth stories and highlight diverging pathways and exceptions to narrative norms. The authors' motivation to publicly post these personal stories can be a way to regain power after a surveilled and disempowering experience, and we explore power relationships between the personas in the stories, showing that these dynamics can vary with the type of birth (e.g., medicated vs unmedicated). Finally, birth stories exist in a space that is both public and deeply personal. This liminality poses a challenge for analysis and presentation, and we discuss tradeoffs and ethical practices for this collection. WARNING: This paper includes detailed narratives of pregnancy and birth.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359190"}, {"primary_key": "2897907", "vector": [], "sparse_vector": [], "title": "Prefigurative Design as a Method for Research Justice.", "authors": ["<PERSON><PERSON>"], "summary": "While there is growing concern around justice and equity, they mean different things in different socio-political and cultural contexts. Additionally, it can be difficult to make sense of how to incorporate the more abstract concept of justice into our research practices. This paper discusses prefigurative design as a framework for more just research practices to challenge inequity, particularly in community-based collaborations. I draw from past fieldwork with activist organizations and radical organizing literature to explore opportunities for how to engage with justice as academics, identifying three main opportunities for intervention through research: social relationships, resource distribution, and counter-institutions. I offer these contributions in the spirit of generative critique, in hopes that other researchers with similar concerns will iterate on these practices to commit to more just and equitable scholarly impacts.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359302"}, {"primary_key": "2897908", "vector": [], "sparse_vector": [], "title": "Additional Labors of the Entrepreneurial Self.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Workers are increasingly expected to take on the responsibility and effort of preparing for employment in the new economy, where digital technologies play a central role in bridging access to resources, connections, and opportunity. Drawing from multi-year studies of entrepreneurs in Accra and Detroit, two cities that continue to experience high rates of inequality and persistently low incomes for the majority of their residents, this article highlights three key challenges to self-entrepreneurialization in the digital age: self-upgrading, maintaining technology, and overcoming exclusion. Locating these challenges at the intersection of (1) two powerful global discourses of entrepreneurialism and technology upgrade and (2) class frictions and racial dynamics, this paper uncovers ways in which CSCW might support entrepreneurialism in the new economy, particularly given that it is becoming a de facto space of work and mode of living.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359320"}, {"primary_key": "2897909", "vector": [], "sparse_vector": [], "title": "Interrogating Social Virtual Reality as a Communication Medium for Older Adults.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Thuong N. <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A growing body of research is examining the way that virtual reality (VR) technology might enrich the lives of older adults. However, no studies have yet examined how this technology---combining head mounted displays, motion tracking, avatars, and virtual environments---might contribute to older adult wellbeing by facilitating greater social participation (social VR). To address this gap, we conducted three workshops in which 25 older adults aged 70 to 81 explored the utility of social VR as a medium for communicating with other older adults. Participants first created embodied avatars that were controlled through natural gestures, and subsequently used these avatars in two high-fidelity social VR prototypes. Findings from the workshops provide insight into older adults' design motivations when creating embodied avatars for social VR; their acceptance of social VR as a communication tool; and their views on how social VR might play a beneficial role in their lives. Outcomes from the workshops also illustrate the critical importance our participants placed onbehavioural anthropomorphism ---the embodied avatars' ability to speak, move, and act in a human-like manner--- alongsidetranslational factors, which encapsulate issues relating to the way physical movements are mapped to the embodied avatar and the way in which errors in these mappings may invoke ageing stereotypes. Findings demonstrate the critical role that these characteristics might play in the success of future social VR applications targeting older users. We translate our findings into a set of design considerations for developing social VR systems for older adults, and we reflect on how our participants' experiences can inform future research on social virtual reality.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359251"}, {"primary_key": "2897910", "vector": [], "sparse_vector": [], "title": "Design in the Public Square: Supporting Assistive Technology Design Through Public Mixed-Ability Cooperation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "From the white cane to the smartphone, technology has been an effective tool for broadening blind and low vision participation in a sighted world. In the face of this increased participation, individuals with visual impairments remain on the periphery of most sight-first activities. In this paper, we describe a multi-month public-facing co-design engagement with an organization that supports blind and low vision outrigger paddling. Using a mixed-ability design team, we developed an inexpensive cooperative outrigger paddling system, called CoOP, that shares control between sighted and visually impaired paddlers. The results suggest that public design, a DIY (do-it-yourself) stance, and attentiveness to shared physical experiences, represent key strategies for creating assistive technologies that support shared experiences.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359257"}, {"primary_key": "2897911", "vector": [], "sparse_vector": [], "title": "All Users are (Not) Created Equal: Predictors Vary for Different Forms of Facebook Non/use.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Relatively little work has empirically examined use and non-use of social technologies as more than a dichotomous binary, despite increasing calls to do so. This paper compares three different forms of non/use that might otherwise fall under the single umbrella of Facebook \"user\": (1) those who have a current active account; (2) those who have deactivated their account; and (3) those who have considered deactivating but not actually done so. A subset of respondents (N=256) from a larger, demographically representative sample of internet users completed measures for usage and perceptions of Facebook, Facebook addiction, privacy experiences and behaviors, and demographics. Multinomial logistic regression modeling shows four specific variables as most predictive of a respondent's type: negative effects from \"addictive\" use, subjective intensity of Facebook usage, number of Facebook friends, and familiarity with or use of Facebook's privacy settings. These findings both fill gaps left by, and help resolve conflicting expectations from, prior work. Furthermore, they demonstrate how valuable insights can be gained by disaggregating \"users\" based on different forms of engagement with a given technology.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359182"}, {"primary_key": "2897912", "vector": [], "sparse_vector": [], "title": "Crowdsourcing Perceptions of Fair Predictors for Machine Learning: A Recidivism Case Study.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The increased reliance on algorithmic decision-making in socially impactful processes has intensified the calls for algorithms that are unbiased and procedurally fair. Identifying fair predictors is an essential step in the construction of equitable algorithms, but the lack of ground-truth in fair predictor selection makes this a challenging task. In our study, we recruit 90 crowdworkers to judge the inclusion of various predictors for recidivism. We divide participants across three conditions with varying group composition. Our results show that participants were able to make informed decisions on predictor selection. We find that agreement with the majority vote is higher when participants are part of a more diverse group. The presented workflow, which provides a scalable and practical approach to reach a diverse audience, allows researchers to capture participants' perceptions of fairness in private while simultaneously allowing for structured participant discussion.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359130"}, {"primary_key": "2897913", "vector": [], "sparse_vector": [], "title": "Harassment in Social Virtual Reality: Challenges for Platform Governance.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>-<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In immersive virtual reality (VR) environments, experiences of harassment can be exacerbated by features such as synchronous voice chat, heightened feelings of presence and embodiment, and avatar movements that can feel like violations of personal space (such as simulated touching or grabbing). Simultaneously, efforts to govern these developing spaces are made more complex by the distributed landscape of virtual reality applications and the dynamic nature of local community norms. To better understand this nascent social and psychological environment, we interviewed VR users (n=25) about their experiences with harassment, abuse, and discomfort in social VR. We find that users' definitions of what constitutes online harassment are subjective and highly personal, which poses significant challenges for the enforcement of platform- or application-level policies. We also find that embodiment and presence in VR spaces make harassment feel more intense, while ephemerality and non-standardized application controls make it difficult to escape or report unwanted behavior. Finally, we find that shared norms for appropriate behavior in social VR are still emergent, and that users distinguish between newcomers who unknowingly violate expectations for appropriate behavior and those users who aim to cause intentional harm. We draw from social norms theory to help explain why norm formation is particularly challenging in virtual reality environments, and we discuss the implications of our findings for the top-down governance of online communities by platforms. We conclude by recommending alternative strategies for community governance.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359202"}, {"primary_key": "2897915", "vector": [], "sparse_vector": [], "title": "MyAutsomeFamilyLife: Analyzing Parents of Children with Developmental Disabilities on YouTube.", "authors": ["<PERSON><PERSON>-<PERSON>", "<PERSON>", "<PERSON>"], "summary": "While parents of young children regularly make decisions about sharing content about their child or family online, we know less about how they create, produce, and share video-based content of children with stigmatizing experiences. Through an analysis of publicly available content on YouTube, supplemented with semi-structured interviews, we report on the ways in which parents of children with developmental disabilities produce, share, and interact with others through videos of their children's experiences. Our analysis finds that parents disclose child information on YouTube to build authenticity, connect with others, advocate for social change, and justify monetization and child involvement. We discuss tensions between parents' practices and the ethical complexities of sharing and studying parent-generated content featuring children with disabilities.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359196"}, {"primary_key": "2897916", "vector": [], "sparse_vector": [], "title": "Stories from the Front Seat: Supporting Accessible Transportation in the Sharing Economy.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Ridesharing services like Uber and Lyft play an important role in broadening access to affordable, efficient transportation for people with vision impairments by providing an alternative to public transit, friends, or family. While previous work has focused on the challenges that people with disabilities experience using public transportation and how they interact with ridesharing drivers, little research has focused on the people in the \"front seat\" and how drivers support people with vision impairments during rides. To better understand these interactions, we interviewed 18 ridesharing drivers who had experience driving passengers with a disability, specifically people with vision impairments. Our findings show that ridesharing drivers engage in different forms of labor (i.e. physical, relational) to support passengers with disabilities and are seeking ways to learn more about disability. Contrary to most previous literature on ridesharing and the sharing economy, we find that drivers do not see this labor as a burden. We discuss ethical and design considerations for the complex process of disability disclosure, wherein passengers might benefit from having drivers know in advance about their disability, but also open themselves up to possible exploitation by drivers.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359197"}, {"primary_key": "2897917", "vector": [], "sparse_vector": [], "title": "&quot;<PERSON>s as Candy Wrappers&quot;: Critical Infrastructure Supporting the Transition into Motherhood.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The transition into motherhood is a complicated and often unsupported major life disruption. To alleviate mental health issues and to support identity re-negotiation, mothers are increasingly turning to online mothers’ groups, particularly private and secret Facebook groups; these can provide a complex system of social, emotional, and practical support for new mothers. In this paper we present findings from an exploratory interview study of how new mothers create, find, use, and participate in ICTs, specifically online mothers’ groups, to combat the lack of formal support systems by developing substitute networks. Utilizing a framework of critical infrastructures, we found that these online substitute networks were created by women, for women, in an effort to fill much needed social, political, and medical gaps that fail to see ‘woman and mother’ as a whole being, rather than simply as a ‘discarded candy wrapper’. Our study contributes to the growing literature on ICT use by mothers for supporting and negotiating new identities, by illustrating how these infrastructures can be re-designed and appropriated in use, for critical utilization.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3361113"}, {"primary_key": "2897918", "vector": [], "sparse_vector": [], "title": "Orienting to Networked Grief: Situated Perspectives of Communal Mourning on Facebook.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Contemporary American experiences of death and mourning increasingly extend onto social network sites, where friends gather to memorialize the deceased. That \"everyone grieves in their own way'' may be true, but it forecloses important questions about how people evaluate these expressions, their relationship to others who are grieving, and impacts on their own experiences of grief. Drawing from mixed-methods research conducted over five years, we describe how individuals position themselves within and evaluate expressions of networked grief. We start by identifying five orientations -- reinforcing, supporting, transferring, objecting, and isolating -- that describe how individuals evaluate actions of grievers, position themselves relative to the network, and act when they encounter grief. We then describe factors and tensions that influence how individuals arrive at these orientations. Based on our findings, we argue that the design of social media can be sensitized to diverse needs by adopting a situated perspective within a dynamic post-mortem network.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359129"}, {"primary_key": "2897919", "vector": [], "sparse_vector": [], "title": "&quot;I think people are powerful&quot;: The Sociality of Individuals Managing Depression.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Millions of Americans struggle with depression, a condition characterized by feelings of sadness and motivation loss. To understand how individuals managing depression conceptualize their self-management activities, we conducted visual elicitations and semi-structured interviews with 30 participants managing depression in a large city in the U.S. Midwest. Many depression support tools are focused on the individual user and do not often incorporate social features. However, our analysis showed the key importance of sociality for self-management of depression. We describe how individuals connect with specific others to achieve expected support and how these interactions are mediated through locations and communication channels. We discuss factors influencing participants' sociality including relationship roles and expectations, mood state and communication channels, location and privacy, and culture and society. We broaden our understanding of sociality in CSCW through discussing diffuse sociality (being proximate to others but not interacting directly) as an important activity to support depression self-management.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359143"}, {"primary_key": "2897920", "vector": [], "sparse_vector": [], "title": "When Users Control the Algorithms: Values Expressed in Practices on Twitter.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent interest in ethical AI has brought a slew of values, including fairness, into conversations about technology design. Research in the area of algorithmic fairness tends to be rooted in questions of distribution that can be subject to precise formalism and technical implementation. We seek to expand this conversation to include the experiences of people subject to algorithmic classification and decision-making. By examining tweets about the \"Twitter algorithm\" we consider the wide range of concerns and desires Twitter users express. We find a concern with fairness (narrowly construed) is present, particularly in the ways users complain that the platform enacts a political bias against conservatives. However, we find another important category of concern, evident in attempts to exert control over the algorithm. Twitter users who seek control do so for a variety of reasons, many well justified. We argue for the need for better and clearer definitions of what constitutes legitimate and illegitimate control over algorithmic processes and to consider support for users who wish to enact their own collective choices.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359240"}, {"primary_key": "2897922", "vector": [], "sparse_vector": [], "title": "One Voice Fits All?: Social Implications and Research Challenges of Designing Voices for Smart Devices.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "When a smart device talks, what should its voice sound like? Voice-enabled devices are becoming a ubiquitous presence in our everyday lives. Simultaneously, speech synthesis technology is rapidly improving, making it possible to generate increasingly varied and realistic computerized voices. Despite the flexibility and richness of expression that technology now affords, today's most common voice assistants often have female-sounding, polite, and playful voices by default. In this paper, we examine the social consequences of voice design, and introduce a simple research framework for understanding how voice affects how we perceive and interact with smart devices. Based on the foundational paradigm of computers as social actors, and informed by research in human-robot interaction, this framework demonstrates how voice design depends on a complex interplay between characteristics of the user, device, and context. Through this framework, we propose a set of guiding questions to inform future research in the space of voice design for smart devices.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359325"}, {"primary_key": "2897923", "vector": [], "sparse_vector": [], "title": "PWA-EU: Extending PWA Approach for Promoting Customization based on User Preferences.", "authors": ["Giulia de Andrade Cardieri", "Luciana A<PERSON>"], "summary": "Progressive Web App (PWA) is a new approach to the development of mobile applications (apps) which was proposed by Google in 2015. It combines technology resources of both web and native apps. Meta-design is an End-User Development (EUD) approach from which end-users participate actively in a system's design process. Yet, PWAs are a recent technology and the impacts of associating EUD and PWAs has been little exploited. As the traditional PWA approach is limited regarding users acting as co-designers, we propose the PWA-EU approach, an extension of the traditional PWA architecture that includes EUD concepts. PWA-EU provides contributions in two lenses. First, the proposal was designed to be used by developers on the design/development time. Second, the app developed using PWA-EU approach will allow end-users to select design preferences, which makes them participants of the app's design. This active participation of end-users on the design is possible due to the meta-design concepts present on the PWA-EU approach. In this article, we present the PWA-EU approach and its evaluation in the perspective of developers/designers. For the evaluation, we grouped participants according to their professional background. The results also indicate that novice developers had a reasonable performance with only one hour of training.We conclude that even novice developers could achieve better performance in a real-life environment, in which they would have more time.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3331152"}, {"primary_key": "2897924", "vector": [], "sparse_vector": [], "title": "A Forensic Qualitative Analysis of Contributions to Wikipedia from Anonymity Seeking Users.", "authors": ["<PERSON><PERSON> Champion", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "By choice or by necessity, some contributors to commons-based peer production sites use privacy-protecting services to remain anonymous. As anonymity seekers, users of the Tor network have been cast both as ill-intentioned vandals and as vulnerable populations concerned with their privacy. In this study, we use a dataset drawn from a corpus of Tor edits to Wikipedia to uncover the character of Tor users' contributions. We build in-depth narrative descriptions of Tor users' actions and conduct a thematic analysis that places their editing activity into seven broad groups. We find that although their use of a privacy-protecting service marks them as unusual within Wikipedia, the character of many Tor users' contributions is in line with the expectations and norms of Wikipedia. However, our themes point to several important places where lack of trust promotes disorder, and to contributions where risks to contributors, service providers, and communities are unaligned.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359155"}, {"primary_key": "2897926", "vector": [], "sparse_vector": [], "title": "Crossmod: A Cross-Community Learning-based System to Assist Reddit Moderators.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we introduce a novel sociotechnical moderation system for Reddit called Crossmod. Through formative interviews with 11 active moderators from 10 different subreddits, we learned about the limitations of currently available automated tools, and how a new system could extend their capabilities. Developed out of these interviews, Crossmod makes its decisions based on cross-community learning---an approach that leverages a large corpus of previous moderator decisions via an ensemble of classifiers. Finally, we deployed Crossmod in a controlled environment, simulating real-time conversations from two large subreddits with over 10M subscribers each. To evaluate Crossmod's moderation recommendations, 4 moderators reviewed comments scored by Crossmod that had been drawn randomly from existing threads. Crossmod achieved an overall accuracy of 86% when detecting comments that would be removed by moderators, with high recall (over 87.5%). Additionally, moderators reported that they would have removed 95.3% of the comments flagged by Crossmod; however, 98.3% of these comments were still online at the time of this writing (i.e., not removed by the current moderation system). To the best of our knowledge, Crossmod is the first open source, AI-backed sociotechnical moderation system to be designed using participatory methods.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359276"}, {"primary_key": "2897927", "vector": [], "sparse_vector": [], "title": "Sensing (Co)operations: Articulation and Compensation in the Robotic Operating Room.", "authors": ["<PERSON>", "<PERSON>", "Malte F. <PERSON>", "<PERSON>"], "summary": "Drawing on ethnographic fieldwork in two different teaching hospitals that deployed the <PERSON> <PERSON> surgical robot, this paper traces how the introduction of robotics reconfigures the sensory environment of surgery and how surgeons and their teams recalibrate their work in response. We explore the entangled and mutually supportive nature of sensing within and between individual actors and the broader world of people and things (with emphasis on vision and touch) and illustrate how such inter-sensory dependencies are challenged and sometimes extended under the conditions of robotic surgery. We illustrate how sensory (re)articulations and compensations allow the surgeon and surgical teams to adapt to a more-than-human sensorium and conclude by advocating new forms of sensory-aware design capable of enhancing and supporting embodied sensory conditions both individually and across teams.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359327"}, {"primary_key": "2897928", "vector": [], "sparse_vector": [], "title": "Gallery D.C.: Design Search and Knowledge Discovery through Auto-created GUI Component Gallery.", "authors": ["Chunyang Chen", "<PERSON><PERSON> Feng", "<PERSON><PERSON><PERSON>", "<PERSON>", "Sheng<PERSON> Zhao", "<PERSON><PERSON><PERSON>"], "summary": "Online communities like Dribbble and GraphicBurger allow GUI designers to share their design artwork and learn from each other. These design sharing platforms are important sources for design inspiration, but our survey with GUI designers suggests additional information needs unmet by existing design sharing platforms. First, designers need to see the practical use of certain GUI designs in real applications, rather than just artworks. Second, designers want to see not only the overall designs but also the detailed design of the GUI components. Third, designers need advanced GUI design search abilities (e.g., multi-facets search) and knowledge discovery support (e.g., demographic investigation, cross-company design comparison). This paper presents Gallery D.C. http://mui-collection.herokuapp.com/, a gallery of GUI design components that harness GUI designs crawled from millions of real-world applications using reverse-engineering and computer vision techniques. Through a process of invisible crowdsourcing, Gallery D.C. supports novel ways for designers to collect, analyze, search, summarize and compare GUI designs on a massive scale. We quantitatively evaluate the quality of Gallery D.C. and demonstrate that Gallery D.C. offers additional support for design sharing and knowledge discovery beyond existing platforms.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359282"}, {"primary_key": "2897929", "vector": [], "sparse_vector": [], "title": "Issues and Experiences in Building Heterogeneous Co-Editing Systems.", "authors": ["<PERSON><PERSON><PERSON>", "Chengzheng Sun", "<PERSON><PERSON><PERSON>"], "summary": "Most past research efforts in co-editing focused on homogeneous co-editing, which allows multiple users to use the same editor to edit shared documents in the same session, and nearly all real-world co-editors, such as Google Docs, are homogeneous co-editors. In this work, we explore issues and solutions in building heterogeneous co-editing systems, which allow multiple users to use different editors to edit shared documents in the same session. To drive our exploration, we built a prototype heterogeneous co-editing system, named CoVim+CoEmacs, which allows multiple users to use full functionalities and UI features of two comprehensive and rivalry text editors, Vim and Emacs, in the same co-editing session. In this paper, we focus on technical issues in designing and implementing heterogeneous co-editors in general and CoVim+CoEmacs in particular. We have motivated this work by potential usage benefits of heterogeneous co-editing systems and used working scenarios under the CoVim+CoEmacs prototype to illustrate some novel usages and inner workings of such systems, but left systematic user studies on heterogeneous co-editing to future work. We hope the insights and experiences drawn from this work can not only contribute to advancing state-of-the-art collaborative system design and implementation, but also provide inspiration to future heterogeneous collaborative application system designers.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3361126"}, {"primary_key": "2897930", "vector": [], "sparse_vector": [], "title": "Intentional Technology Use in Early Childhood Education.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Early childhood is a critical developmental period when children's experiences have lasting impacts on long-term outcomes. Thus, an evidence-based understanding of how technology can support early childhood education (ECE) classrooms promises to be disproportionately useful to children's long-term wellbeing. We conducted an observational study at ten child-care centers, complemented by interviews with teachers and directors. Using a Uses and Gratifications (U&amp;G) perspective, we found that the gratifications teachers seek when they incorporate technology into the classroom cluster into six categories, such as encouraging technology literacy, regulating children's behavior, and supporting child autonomy. Using these themes, we contribute a set of design priorities for supporting this population. We also contribute an expansion of the U&amp;G perspective to include: 1) gratifications resisted, to account for the ways in which teachers resist gratifying uses of technology, and 2) differentiation between direct gratifications and indirect gratifications to better describe technology use in collaborative contexts.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359180"}, {"primary_key": "2897931", "vector": [], "sparse_vector": [], "title": "Co-designing for Community Oversight: Helping People Make Privacy and Security Decisions Together.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Collective feedback can support an individual's decision-making process. For instance, individuals often seek the advice of friends, family, and co-workers to help them make privacy decisions. However, current technologies often do not provide mechanisms for this type of collaborative interaction. To address this gap, we propose a novel model of Community Oversight for Privacy and Security (\"CO-oPS\"), which identifies mechanisms for users to interact with people they trust to help one another make digital privacy and security decisions. We apply our CO-oPS model in the context of mobile applications (\"apps\"). To interrogate and refine this model, we conducted participatory design sessions with 32 participants in small groups of 2-4 people who know one another, with the goal of designing a mobile app that facilitates collaborative privacy and security decision-making. We describe and reflect on the opportunities and challenges that arise from the unequal motivation and trust in seeking support and giving support within and beyond a community. Through this research, we contribute a novel framework for collaborative digital privacy and security decision-making and provide empirical evidence towards how researchers and designers might translate this framework into design-based features.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359248"}, {"primary_key": "2897932", "vector": [], "sparse_vector": [], "title": "Challenges and Design Considerations for Multimodal Asynchronous Collaboration in VR.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Dongwook Yoon"], "summary": "Studies on collaborative virtual environments (CVEs) have suggested capture and later replay of multimodal interactions (e.g., speech, body language, and scene manipulations), which we refer to as multimodal recordings, as an effective medium for time-distributed collaborators to discuss and review 3D content in an immersive, expressive, and asynchronous way. However, there exist gaps of empirical knowledge in understanding how this multimodal asynchronous VR collaboration (MAVRC) context impacts social behaviors in mediated-communication, workspace awareness in cooperative work, and user requirements for authoring and consuming multimedia recording. This study aims to address these gaps by conceptualizing MAVRC as a type of CSCW and by understanding the challenges and design considerations of MAVRC systems. To this end, we conducted an exploratory need-finding study where participants (N = 15) used an experimental MAVRC system to complete a representative spatial task in an asynchronously collaborative setting, involving both consumption and production of multimodal recordings. Qualitative analysis of interview and observation data from the study revealed unique, core design challenges of MAVRC in: (1) coordinating proxemic behaviors between asynchronous collaborators, (2) providing traceability and change awareness across different versions of 3D scenes, (3) accommodating viewpoint control to maintain workspace awareness, and (4) supporting navigation and editing of multimodal recordings. We discuss design implications, ideate on potential design solutions, and conclude the paper with a set of design recommendations for MAVRC systems.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359142"}, {"primary_key": "2897933", "vector": [], "sparse_vector": [], "title": "Efficient Elicitation Approaches to Estimate Collective Crowd Answers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> (Ray) Hong", "<PERSON><PERSON>", "<PERSON>"], "summary": "When crowdsourcing the creation of machine learning datasets, statistical distributions that capture diverse answers can represent ambiguous data better than a single best answer. Unfortunately, collecting distributions is expensive because a large number of responses need to be collected to form a stable distribution. Despite this, the efficient collection of answer distributions-that is, ways to use less human effort to collect estimates of the eventual distribution that would be formed by a large group of responses-is an under-studied topic. In this paper, we demonstrate that this type of estimation is possible and characterize different elicitation approaches to guide the development of future systems. We investigate eight elicitation approaches along two dimensions: annotation granularity and estimation perspective. Annotation granularity is varied by annotating i) a single \"best\" label, ii) all relevant labels, iii) a ranking of all relevant labels, or iv) real-valued weights for all relevant labels. Estimation perspective is varied by prompting workers to either respond with their own answer or an estimate of the answer(s) that they expect other workers would provide. Our study collected ordinal annotations on the emotional valence of facial images from 1,960 crowd workers and found that, surprisingly, the most fine-grained elicitation methods were not the most accurate, despite workers spending more time to provide answers. Instead, the most efficient approach was to ask workers to choose all relevant classes that others would have selected. This resulted in a 21.4% reduction in the human time required to reach the same performance as the baseline (i.e., selecting a single answer with their own perspective). By analyzing cases in which finer-grained annotations degraded performance, we contribute to a better understanding of the trade-offs between answer elicitation approaches. Our work makes it more tractable to use answer distributions in large-scale tasks such as ML training, and aims to spark future work on techniques that can efficiently estimate answer distributions.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359164"}, {"primary_key": "2897934", "vector": [], "sparse_vector": [], "title": "Fortunettes: Feedfor<PERSON> about the Future State of GUI Widgets.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Feedback is commonly used to explain what happened in an interface. What if questions, on the other hand, remain mostly unanswered. In this paper, we present the concept of enhanced widgets capable of visualizing their future state, which helps users to understand what will happen without committing to an action. We describe two approaches to extend GUI toolkits to support widget-level feedforward, and illustrate the usefulness of widget-level feedforward in a standardized interface to control the weather radar in commercial aircraft. In our evaluation, we found that users require less clicks to achieve tasks and are more confident about their actions when feedforward information was available. These findings suggest that widget-level feedforward is highly suitable in applications the user is unfamiliar with, or when high confidence is desirable.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3331162"}, {"primary_key": "2897935", "vector": [], "sparse_vector": [], "title": "Socio-technical Affordances for Stigmergic Coordination Implemented in MIDST, a Tool for Data-Science Teams.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> You"], "summary": "We present a conceptual framework for socio-technical affordances for stigmergic coordination, that is, coordination supported by a shared work product. Based on research on free/libre open source software development, we theorize that stigmergic coordination depends on three sets of socio-technical affordances: the visibility and combinability of the work, along with defined genres of work contributions. As a demonstration of the utility of the developed framework, we use it as the basis for the design and implementation of a system, MIDST, that supports these affordances and that we thus expect to support stigmergic coordination. We describe an initial assessment of the impact of the tool on the work of project teams of three to six data-science students that suggests that the tool was useful but also in need of further development. We conclude with plans for future research and an assessment of theory-driven system design.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359219"}, {"primary_key": "2897936", "vector": [], "sparse_vector": [], "title": "?It doesn&apos;t win you friends&quot;: Understanding Accessibility in Collaborative Writing for People with Vision Impairments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Collaborative writing tools have become ubiquitous in today's world and are used widely in many professional organizations and academic settings. Yet, we know little about how ability-diverse teams, such as those involving people with and without vision impairments, make use of collaborative writing tools. We report on interviews with 20 academics and professionals who are blind or visually impaired and perform collaborative writing with sighted colleagues. Our findings reveal that people with vision impairments perform collaborative writing activities through four interconnected processes, which include learning an ecosystem of (in)accessible tools, adapting to complexities of collaborative features, balancing the cost and benefit of accessibility, and navigating power dynamics within organizations. We discuss how our analysis contributes to theories of accessibility in collaboration and offers practical insights for future collaborative system design.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359293"}, {"primary_key": "2897937", "vector": [], "sparse_vector": [], "title": "Summarizing User-generated Textual Content: Motivation and Methods for Fairness in Algorithmic Summaries.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Kripaband<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Ghos<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "As the amount of user-generated textual content grows rapidly, text summarization algorithms are increasingly being used to provide users a quick overview of the information content. Traditionally, summarization algorithms have been evaluated only based on how well they match human-written summaries (e.g. as measured by ROUGE scores). In this work, we propose to evaluate summarization algorithms from a completely new perspective that is important when the user-generated data to be summarized comes from different socially salient user groups, e.g. men or women, Caucasians or African-Americans, or different political groups (Republicans or Democrats). In such cases, we check whether the generated summaries fairly represent these different social groups. Specifically, considering that an extractive summarization algorithm selects a subset of the textual units (e.g. microblogs) in the original data for inclusion in the summary, we investigate whether this selection is fair or not. Our experiments over real-world microblog datasets show that existing summarization algorithms often represent the socially salient user-groups very differently compared to their distributions in the original data. More importantly, some groups are frequently under-represented in the generated summaries, and hence get far less exposure than what they would have obtained in the original data. To reduce such adverse impacts, we propose novel fairness-preserving summarization algorithms which produce high-quality summaries while ensuring fairness among various groups. To our knowledge, this is the first attempt to produce fair text summarization, and is likely to open up an interesting research direction.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359274"}, {"primary_key": "2897938", "vector": [], "sparse_vector": [], "title": "Quantifying Voter Biases in Online Platforms: An Instrumental Variable Approach.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In content-based online platforms, use of aggregate user feedback (say, the sum of votes) is commonplace as the \"gold standard\" for measuring content quality. Use of vote aggregates, however, is at odds with the existing empirical literature, which suggests that voters are susceptible to different biases-reputation (e.g., of the poster), social influence (e.g., votes thus far), and position (e.g., answer position). Our goal is to quantify, in an observational setting, the degree of these biases in online platforms. Specifically, what are the causal effects of different impression signals-such as the reputation of the contributing user, aggregate vote thus far, and position of content-on a participant's vote on content? We adopt an instrumental variable (IV) framework to answer this question. We identify a set of candidate instruments, carefully analyze their validity, and then use the valid instruments to reveal the effects of the impression signals on votes. Our empirical study using log data from Stack Exchange websites shows that the bias estimates from our IV approach differ from the bias estimates from the ordinary least squares (OLS) method. In particular, OLS underestimates reputation bias (1.6-2.2x for gold badges) and position bias (up to 1.9x for the initial position) and overestimates social influence bias (1.8-2.3x for initial votes). The implications of our work include: redesigning user interface to avoid voter biases; making changes to platforms' policy to mitigate voter biases; detecting other forms of biases in online platforms.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359222"}, {"primary_key": "2897940", "vector": [], "sparse_vector": [], "title": "Whose Walkability?: Challenges in Algorithmically Measuring Subjective Experience.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The Walk Score is a patented algorithm for measuring the walkability of a given geographic area. In addition to its use in real estate, the accompanying API is used in a range of research in public health and urban development. This study explores how neighborhood residents differently understand the notion of walkability as well as the extent to which their personal definitions of neighborhood walkability are reflected in the Walk Score's underlying algorithm. We find that, while the Walk Score generally aligns with residents' priorities around walkability, significant subjective aspects that influence walking behavior are not reflected in the score, raising the need to consider implications for using algorithmic tools like the Walk Score in certain research contexts. We discuss the challenge of measuring subjective experience and how designers might begin to address it. We call for qualitative evaluations of algorithmic tools to help determine appropriate contexts of use.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359228"}, {"primary_key": "2897941", "vector": [], "sparse_vector": [], "title": "&quot;The cavalry ain&apos;t coming in to save us&quot;: Supporting Capacities and Relationships through Civic Tech.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Cities are increasingly integrating sensing and information and communication technologies to improve municipal services, civic engagement, and quality of life for residents. Although these civic technologies have the potential to affect economic, social, and environmental factors, there has been less focus on residents of lower income communities' involvement in civic technology design. Based on two public forums held in underserved communities, we describe residents' perceptions of civic technologies in their communities and challenges that limit the technologies' viability. We found that residents viewed civic technology as a tool that should strengthen existing community assets by providing an avenue to connect assets and build upon them. We describe how an asset-based approach can move us toward designing civic technology that develops stronger relationships among community-led initiatives, and between the community and local government--- rather than a data-driven approach to civic tech that focuses on transactions between residents and city services.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359225"}, {"primary_key": "2897942", "vector": [], "sparse_vector": [], "title": "Boundary Negotiation for Patient-Provider Communication via WeChat in China.", "authors": ["Xiang<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Patient-Provider Communication (PPC) is crucial to the quality and outcome of healthcare practices. With the development of Information and Communication Technologies (ICTs), ICT mediated PPC has become increasingly commonplace, and has been extensively studied. However, prior research has primarily focused on the institutional use of ICTs for PPC, with institutional support and regulation, while the personal use of ICTs for this purpose has been mostly under-explored. This paper presents a qualitative study of the use of WeChat, a general mobile social application, that has been personally appropriated for PPC in China. Sixteen patients and seven physicians, who had experience using WeChat for PPC, were recruited and interviewed to gain an understanding from both perspectives on how WeChat was utilized for the communication purposes between them. We found that the use of WeChat helped to strengthen the relationship between patients and providers, and provided a psychological reassurance that the structure of the current Chinese health system doesn't provide. Most importantly, we found that the use of WeChat was dependent on the negotiation and management of boundaries that address various concerns associated with the use of ICTs for PPC, such as workload and safety. In this paper, we will highlight the boundary negotiation practices and discuss implications based on the findings.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359259"}, {"primary_key": "2897944", "vector": [], "sparse_vector": [], "title": "Increasing Native Speakers&apos; Awareness of the Need to Slow Down in Multilingual Conversations Using a Real-Time Speech Speedometer.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Collaborating using a common language can be challenging for non-native speakers (NNS). These challenges can be reduced when native speakers (NS) adjust their speech behavior for NNS, for example by speaking more slowly. In this study, we examined whether the use of real-time speech rate feedback (a speech speedometer) would help NS monitor their speaking speed and adjust for NNS accordingly. We conducted a laboratory experiment with 20 triads of 2 NS and 1 NNS. NS in half of the groups were given the speech speedometer. We found that NS with the speech speedometer were significantly more motivated to slow down their speech but they did not actually speak more slowly, although they made other speech adjustments. Furthermore, NNS perceived the speech of NS with the speedometer less clear, and they felt less accommodated. The results highlight the need for tools that create scaffolding to help NS make speech accommodations. We conclude with some design ideas for these scaffolding tools.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359273"}, {"primary_key": "2897945", "vector": [], "sparse_vector": [], "title": "If it Rains, Ask <PERSON> to Disconnect the Nano: Maintenance &amp; Care in Havana&apos;s StreetNet.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In Cuba, where internet access is severely constrained, technology enthusiasts have built StreetNet (SNET), a community network (CN) that has grown organically, reaching tens of thousands of households across Havana. Through fieldwork conducted in 2016 and 2017, we investigate participants' strategies as they engage with a network where the material elements---cables, switches, nanos, and servers---are regularly breaking down. Drawing on maintenance and care (M&amp;C) scholarship, we present an in-depth investigation of the management and anticipation of breakdowns in SNET, foregrounding the deeply relational nature of repair work, collective efforts required for SNET's M&amp;C, and the values and motivations underpinning these practices. Our paper contributes a unique perspective on how CNs are run locally and organically, outlining considerations for how interventions along these lines might be more suitably designed. We also complicate perspectives of innovation through a discussion of cultural ideologies and tensions underpinning M&amp;C practices.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359289"}, {"primary_key": "2897946", "vector": [], "sparse_vector": [], "title": "&quot;Coming Out Okay&quot;: Community Narratives for LGBTQ Identity Recovery Work.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Online communities provide support for those who are vulnerable, such as LGBTQ people while coming out. Research shows that social support and personal narrative construction are important when recovering from personal crises and traumatic events. As an online community focused on writing fanfiction and also consisting of a large number of LGBTQ members, transformative fandom provides an opportunity to examine the relationship between support, crisis, and narrative. Through an interview study with 31 LGBTQ fanfiction authors, our findings mirror <PERSON>'s model of trauma recovery: these spaces self-organize to support recovery work through constructing \"community narratives\" that help LGBTQ people establish safety when exploring their identity and build LGBTQ support structures without publicly outing themselves before they are ready, challenge stereotypes, and support others through reshaping existing media that perpetuate inaccurate or harmful LGBTQ narratives. These online communities embody \"selective visibility\"--that is, though not specifically designed as support structures for identity work and recovery, their design allows people to selectively find and create communities of support for stigmatized issues that they might be unable to safely seek out in other spaces. Based on lessons learned, we generate insights that can inform the design of safe support spaces online.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359256"}, {"primary_key": "2897947", "vector": [], "sparse_vector": [], "title": "Sorting Out Valuation in the Charity Shop: Designing for Data-Driven Innovation through Value Translation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent work within HCI and CSCW has become attentive to the politics of data and metrics in order to highlight the implications of what counts and how. In this paper, we relate these discussions to the longstanding distinctions made between value and values. We introduce literature on 'Valuation Studies' and argue for understanding the politics of data through valuation - an ongoing social practice that transforms socially embedded values into different forms of more abstract value. This theoretical work is developed through an ethnographic study of contemporary UK charity shops, as a site focused on the labour of valuation, but embedded in both local and global values. Through this study, we consider implications for the intervention and design of 'data-driven innovation', with a particular focus on distributed ledger technologies. We argue that these technologies inevitably engage in valuation, and require careful attention to the ongoing processes by which value is translated and performed by different stakeholders.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359211"}, {"primary_key": "2897948", "vector": [], "sparse_vector": [], "title": "Editors&apos; Welcome.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "It is our pleasure to welcome you to the first PACMHCI GROUP issue. For over 25 years, the GROUP research community has supported the development of robust scholarship at the intersection of Computer Supported Cooperative Work, Human Computer Interaction, Computer Supported Collaborative Learning and Socio-Technical Studies. This volume is the latest product of our efforts to validate and integrate the strong work happening within this broadly-conceived community. We hope that the range of papers presented here reflects our intent to be international, interdisciplinary, and inclusive, both in our organization of the review process as well as within the set of accepted papers. This first PACM journal volume in the GROUP series features studies of collaboration in multiple settings, including social networks, editing systems, and mixed reality. It also showcases sociotechnical studies in domains ranging from outdoor activities to scientific projects.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3361110"}, {"primary_key": "2897949", "vector": [], "sparse_vector": [], "title": "The Social Mile - How (Psychosocial) ICT can Help to Promote Resocialization and to Overcome Prison.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "There is currently uncertainty in the research community as to how ICT can and should be designed in such a way that it can be convincingly integrated into the everyday lives of prison inmates. In this paper, we discuss a design fiction that closes this research gap. The descriptions and results of the study are purely fictitious. Excluded is the State of the Art as well as the description of the legal situation of prisons in Germany. The analysis of the fictional study data designed here thus refers to the real world in order to derive ethical guidelines and draw practical conclusions. It is our intention to use these results as a possible basis for further research. The paper presents results of an explorative study dealing with the design, development and evaluation of an AI-based Smart Mirror System, Prison AI 2.0, in a German prison. Prison AI 2.0 was developed for daily use and voluntarily tested by eight prisoners over a period of 12 months to gain insight into their individual and social impact, with an emphasis on its ability to actively support rehabilitation. Based on qualitative data, our findings suggest that intelligent AI-based devices can actually help promote such an outcome. Our results also confirm the valuable impact of (Psychosocial) ICT on the psychological, social and individual aspects of prison life, and in particular how prisoners used the Smart Mirror system to improve and maintain their cognitive, mental and physical state and to restore social interactions with the outside world. With the presentation of these results we want to initiate discussions about the use of ICT by prisoners in closed prisons in order to identify opportunities and risks.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3370270"}, {"primary_key": "2897950", "vector": [], "sparse_vector": [], "title": "Jam Today, Jam Tomorrow: Learning in Online Game Jams.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Game jams, which are game creation events in which developers design and build a game over a short period of time, have been shown to support participatory, active STEM learning. Game jams have expanded from their origins as physically co-located experiences and many are now conducted exclusively online. Though the co-located game jam has been noted as educational, little is known about how learning happens within online game jams. To better understand the ways online game jams support self-development, we interviewed fifteen online jam participants about their learning experiences during their jams. Additionally, we observed and analyzed several jams using activity theory. We found that online game jams support participants' learning through extensive feedback from others during and after the jam. Such feedback sessions were a key social and participatory learning elements for online jams, providing much-need social support. In contrast to the group-focused development in offline jams many participants in our study chose to develop games alone. Many individuals participated in online game jams regularly, treating them as a broader experience than singular events. We use these findings to discuss how we might better design for self-directed learning online and offer suggestions on how to better attune online game jams to the needs of participants.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3361121"}, {"primary_key": "2897951", "vector": [], "sparse_vector": [], "title": "&quot;I Don&apos;t Want Them to Not Know&quot;: Investigating Decisions to Disclose Transgender Identity on Dating Platforms.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Dating platform research often focuses on people's decisions about when to reveal certain aspects of themselves to others, or self-disclosure. One example is deciding what to include in one's profile and what to reveal in chat conversations or in person. Transgender people face a particularly acute challenge in self-disclosure, but we know little about how they experience it on dating platforms. Revealing trans status can result in physical or emotional harm, but is also often considered necessary for a successful relationship and for self-fulfillment. To better understand disclosure of sensitive information, we interviewed 20 transgender dating platform users in the U.S. We find that direct, proactive disclosure of trans status was motivated by desires for safety and certainty, though this could involve tension. Physical separation and one-to-many communication surface as key affordances that facilitated disclosure. These results help us better understand motivations behind disclosure decisions.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359328"}, {"primary_key": "2897952", "vector": [], "sparse_vector": [], "title": "From Nomadic Work to Nomadic Leisure Practice: A Study of Long-term Bike Touring.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Mobility has long been a central concern in research within the Computer-Supported Cooperative Work (CSCW) community, particularly when it comes to work and how being on the move calls for reorganizing work practices. We expand this line of work with a focus on nomadic leisure practices. Based on interviews with eleven participants, we present a study that illuminates how digital technologies are used to shape and structure long-distance cycling. Our main analysis centers on bike touring as a nomadic leisure practice and on how it offers a radical departure from traditional modes of structuring work and life, and thus, complicates the relationship between work and leisure. We complement this with an account of managing the uncertainties of nomadicity by focusing on participants’ experiences with arranging overnighting and network hospitality. We offer this study, firstly, as one response to the call for more diversity in the empirical cases drawn upon in theorizing nomadic work and leisure practices, but more productively, as an opportunity to reflect upon the temporal and spatial logics of digital technologies and platforms and how they frame our attitudes towards the interplay between work and leisure.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359213"}, {"primary_key": "2897953", "vector": [], "sparse_vector": [], "title": "Ethical Considerations for Research Involving (Speculative) Public Data.", "authors": ["<PERSON>"], "summary": "As the process of creating and sharing data about ourselves becomes more prevalent, researchers have access to increasingly rich data about human behavior. Framed as a fictional paper published at some point in the not-so-distant future, this design fiction draws from current inquiry and debate into the ethics of using public data for research, and speculatively extends this conversation into even more robust and more personal data that could exist when we design new technologies in the future. By looking to how the precedents of today might impact the practices of tomorrow, we can consider how we might design policies, ethical guidelines, and technologies that are forward-thinking.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3370271"}, {"primary_key": "2897954", "vector": [], "sparse_vector": [], "title": "Creativity, Copyright, and Close-Knit Communities: A Case Study of Social Norm Formation and Enforcement.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Social norms as a regulatory mechanism often carry more weight than formal law--particularly in contexts when legal rules are gray. In online creative communities that focus on remix, community members must navigate copyright complexities regarding how they are permitted to re-use existing content. This paper focuses on one such community--transformative fandom--where strong social norms regulate behavior beyond copyright law. We conducted interviews with fan creators about their \"unwritten rules\" surrounding copying and remix and identified highly consistent social norms that have been remarkably effective in policing this community. In examining how these norms have formed over time, and how they are enforced, we conclude that the effectiveness of norms in encouraging cooperative behavior is due in part to a strong sense of social identity within the community. Furthermore, our findings suggest the benefits of creating formal rules within a community that support existing norms, rather than imposing rules from external sources.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3361122"}, {"primary_key": "2897955", "vector": [], "sparse_vector": [], "title": "A Comparative Evaluation of Techniques for Sharing AR Experiences in Museums.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Museums are constantly searching for new ways to increase engagement with their exhibits, from electronic guides to modern digital technologies such as special-purpose tablets, smartphones, and virtual and augmented reality (AR). For AR exhibits in particular, promoting shared experience and group cohesion is not straightforward. In this work, we investigate scenarios in which not everyone is using a head-worn display (HWD), either because there aren't enough available or simply because someone might feel uncomfortable using it. We propose two sharing techniques for AR experiences and evaluate them in a long term in-the-wild study: Over-the-Shoulder AR, which renders a real-time virtual representation of the augmented reality content on a large secondary display; Semantic Linking, which displays contextual information about the virtual content on the same large display. We also introduce a complementary technique: Indicator Rings, which display the locations of the HWD user's objects-of-focus. We observed that participants in the Over-the-Shoulder AR and Semantic Linking conditions stayed together and exhibited more verbal exchanges than participants in a Baseline condition, which could indicate that they were more engaged. Self-reported measures indicated an increase in pair communication and increased comprehension of the virtual content for participants without the HWD. Participants without the HWD also displayed a greater understanding of the location of virtual elements with support from the Indicator Rings, and used them as a tool to guide the HWD user through the virtual content. We discuss design implications for interactive augmented reality exhibits and possible applications outside the cultural heritage scenario.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359226"}, {"primary_key": "2897957", "vector": [], "sparse_vector": [], "title": "&quot;This Place Does What It Was Built For&quot;: Designing Digital Institutions for Participatory Change.", "authors": ["<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>"], "summary": "Whether we recognize it or not, the Internet is rife with exciting and original institutional forms that are transforming social organization on and offline. Governing these Internet platforms and other digital institutions has posed a challenge for engineers and managers, many of whom have little exposure to the relevant history or theory of institutional design. The dominant guiding practices for the design of digital institutions to date in human-computer interaction, computer-supported cooperative work, and the tech industry at large have been an incentive-focused behavioral engineering paradigm encompassing atheoretical approaches such as emulation, A/B-testing, engagement maximization, and piecemeal issue-driven engineering. One institutional analysis framework that has been useful in the study of traditional institutions comes from scholars of natural resource management, particularly that community of economists, anthropologists, and environmental and political scientists focused around the work of <PERSON><PERSON>, known collectively as the \"Ostrom Workshop.\" A key finding from this community that has yet to be broadly incorporated into the design of many digital institutions is the importance of including participatory change mechanisms in what is called a \"constitutional layer\" of institutional design. The institutional rules that compose a constitutional layer facilitate stakeholder participation in the ongoing process of institutional design change. We explore to what extent consideration of constitutional layers is met or could be better met in three varied cases of digital institutions: cryptocurrencies, cannabis informatics, and amateur Minecraft server governance. Examining such highly varied cases allows us to demonstrate the broad relevance of constitutional layers in many different types of digital institutions.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359134"}, {"primary_key": "2897958", "vector": [], "sparse_vector": [], "title": "Reclaiming Stigmatized Narratives: The Networked Disclosure Landscape of #MeToo.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The social stigma looming over disclosures of sexual violence discourages many women from publicly sharing their stories, limiting their ability to seek support and obscuring the epidemic of sexual violence against women. By inviting women to share their ordinarily silenced stories, the hashtag #MeToo surfaced a network of survivors to confront this stigma. Through a mixed-methods analysis of over 1.8 million tweets posted during the first two weeks after #MeToo gained widespread popularity in 2017, we map the landscape of disclosures that emerged and disentangle the effects of network-level reciprocal disclosures, or disclosures made in reaction to seeing others disclose. We detail how survivors disclosed a diversity of sexual violence experiences in solidarity with others, composing nearly half of all authored tweets and comprising a disproportionate number of interactions within the #MeToo network. Further, we show that the more disclosures an individual potentially saw prior to disclosing, the more likely they were to share details with their disclosure. We argue that such network-level reciprocal disclosures may have reduced stigma, creating a counterpublic space safe for disclosure which, subsequently, generated more disclosures. Our work illustrates how feminist hashtag activism, like #MeToo, can unify individual and collective narratives to dismantle the stigma surrounding disclosures of sexual violence. Content warning: This article heavily discusses issues of sexual violence against women.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359198"}, {"primary_key": "2897959", "vector": [], "sparse_vector": [], "title": "Expanding Our Reflexive Toolbox: Collaborative Possibilities for Examining Socio-Technical Systems Using Duoethnography.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Addressing harm and ethical dilemmas that arise from the design of socio-technical systems requires methodologies that foster critical investigations of normative values, perspectives, and experiences. In this paper, we propose duoethnography as a feminist methodological tool for collaboratively researching interactions between users, devices, and data. Drawing on an interdisciplinary body of work and reflexive methodological traditions, we propose and describe four facets of the methodology: relationality, difference, dialogic process, and critical subjectivity. Next, we provide examples of the methodology \"in practice\" by detailing how the facets were implemented in a six-month diary study of digital health tracking. We use the study findings to illustrate how the application of these four facets facilitated a collaborative and dialogic process that enhanced self-knowledge and resulted in a multifaceted understanding of individual and shared health experiences. Finally, we conclude by discussing the methodological contributions of duoethnography for socio-technical research critically examining normative values and ethics in design. We contend that the collaborative intentionality of duoethnography offers a unique contribution and unifying methodology that views the personal as a valuable site of knowledge production, positions knowledge formation as a dialogic process, and promotes alternative ways of knowing and meaning making.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359292"}, {"primary_key": "2897960", "vector": [], "sparse_vector": [], "title": "4X: A Hybrid Approach for Scaffolding Data Collection and Interest in Low-Effort Participatory Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Participatory sensing systems in which people actively participate in the data collection process must account for both the needs of data contributors and the data collection goals. Existing approaches tend to emphasize one or the other, with opportunistic and directed approaches making opposing tradeoffs between providing convenient opportunities for contributors and collecting high-fidelity data. This paper explores a new, hybrid approach, in which collected data-even if low-fidelity initially-can provide useful information to data contributors and inspire further contributions. We realize this approach with 4X, a multi-stage data collection framework that first collects data opportunistically by requesting contributions at specific locations along users' routes and then uses collected data to direct users to locations of interest to make additional contributions that build data fidelity and coverage. To study the efficacy of 4X, we implemented 4X into LES, an application for collecting information about campus locations and events. Results from two field deployments (N = 95, N = 18) show that the 4X framework created 34% more opportunities for contributing data without increasing disruption, and yielded 49% more data by directing users to locations of interest. Our results demonstrate the value and potential of multi-stage, dynamic data collection processes that draw on multiple sources of motivation for data, and how they can be used to better meet data collection goals as data becomes available while avoiding unnecessary disruption.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359192"}, {"primary_key": "2897961", "vector": [], "sparse_vector": [], "title": "&quot;When you can do it, why can&apos;t I?&quot;: Racial and Socioeconomic Differences in Family Technology Use and Non-Use.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "There is racial diversity as well as economic inequality in the United States (U.S.). To gain a nuanced understanding of how households from different socio economic and racial backgrounds integrate technology into their lives, we conducted a diary study with 22 parents who were Asian Indian (the fastest-growing immigrant population in U.S.) and 18 who were White American (the largest racial group in U.S.) parents from the working and middle classes. The participants logged in-situ instances of using smart phones and speaker use by, with, and around children for 8 weeks, and were interviewed once every four weeks (two times in total). Our findings reveal differences and similarities in parents' attitudes and practices of using or not using these devices around and with children, in parental restrictions of children's use of technology, and children's daily use patterns. The paper concludes with a discussions of the implications of our findings and suggestions for future design improvements in smart phones and speakers.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359165"}, {"primary_key": "2897963", "vector": [], "sparse_vector": [], "title": "Causal Effects of Brevity on Style and Success in Social Media.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In online communities, where billions of people strive to propagate their messages, understanding how wording affects success is of primary importance. In this work, we are interested in one particularly salient aspect of wording: brevity. What is the causal effect of brevity on message success? What are the linguistic traits of brevity? When is brevity beneficial, and when is it not? Whereas most prior work has studied the effect of wording on style and success in observational setups, we conduct a controlled experiment, in which crowd workers shorten social media posts to prescribed target lengths and other crowd workers subsequently rate the original and shortened versions. This allows us to isolate the causal effect of brevity on the success of a message. We find that concise messages are on average more successful than the original messages up to a length reduction of 30--40%. The optimal reduction is on average between 10% and 20%. The observed effect is robust across different subpopulations of raters and is the strongest for raters who visit social media on a daily basis. Finally, we discover unique linguistic and content traits of brevity and correlate them with the measured probability of success in order to distinguish effective from ineffective shortening strategies. Overall, our findings are important for developing a better understanding of the effect of brevity on the success of messages in online social media.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359147"}, {"primary_key": "2897964", "vector": [], "sparse_vector": [], "title": "Do I Stay or Do I Go?: Motivations and Decision Making in Social Media Non-use and Reversion.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON> <PERSON><PERSON><PERSON>"], "summary": "This paper explores the ways in which users give up as well as revert back to using social media based on the analysis of qualitative data from 680 respondents to a survey (N=1072). By focusing on the motivations rather than any particular social media platform, we were able to uncover reasons for voluntary non-use and reversion when users have a choice of several social media platforms. Our findings provide an ecological view of social media non-use and reversion highlighting that 1) previous findings of social media non-use and reversion reported on a single social media platform (e.g. Facebook, Twitter) are seen across several other platforms too; 2) access to multiple social media platforms in itself readily leads to non-use of one platform in favor of the use of another for reasons such as lack of differentiation and/or richness of features, device constraints of storage/ memory space or operating system support, perceptions of fading popularity, and availability of \"a new kid on the block;\" 3) findings of non-use and reversion practices and motivations that were seemingly incongruent between previous studies of different or same social media platforms tend to coalesce when we take a wider look at the social media landscape. By adopting the theoretical lens of cognitive biases in decision-making we were able to explain why users quit but decide to revert to using again in the environment of various positive and negative experiences on a particular platform, and to reconcile the seemingly paradoxical reasons for social media non-use and reversion.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3361116"}, {"primary_key": "2897965", "vector": [], "sparse_vector": [], "title": "The Principles and Limits of Algorithm-in-the-Loop Decision Making.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The rise of machine learning has fundamentally altered decision making: rather than being made solely by people, many important decisions are now made through an \"algorithm-in-the-loop'' process where machine learning models inform people. Yet insufficient research has considered how the interactions between people and models actually influence human decisions. Society lacks both clear normative principles regarding how people should collaborate with algorithms as well as robust empirical evidence about how people do collaborate with algorithms. Given research suggesting that people struggle to interpret machine learning models and to incorporate them into their decisions---sometimes leading these models to produce unexpected outcomes---it is essential to consider how different ways of presenting models and structuring human-algorithm interactions affect the quality and type of decisions made. This paper contributes to such research in two ways. First, we posited three principles as essential to ethical and responsible algorithm-in-the-loop decision making. Second, through a controlled experimental study on Amazon Mechanical Turk, we evaluated whether people satisfy these principles when making predictions with the aid of a risk assessment. We studied human predictions in two contexts (pretrial release and financial lending) and under several conditions for risk assessment presentation and structure. Although these conditions did influence participant behaviors and in some cases improved performance, only one desideratum was consistently satisfied. Under all conditions, our study participants 1) were unable to effectively evaluate the accuracy of their own or the risk assessment's predictions, 2) did not calibrate their reliance on the risk assessment based on the risk assessment's performance, and 3) exhibited bias in their interactions with the risk assessment. These results highlight the urgent need to expand our analyses of algorithmic decision making aids beyond evaluating the models themselves to investigating the full sociotechnical contexts in which people and algorithms interact.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359152"}, {"primary_key": "2897967", "vector": [], "sparse_vector": [], "title": "Customizations and Expression Breakdowns in Ecosystems of Communication Apps.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The growing adoption of emojis, stickers and GIFs suggests a corresponding demand for rich, personalized expression in messaging apps. Some people customize apps to enable more personal forms of expression, yet we know little about how such customizations shape everyday communication. Since people increasingly communicate via multiple apps side-by-side, we are also interested in how customizing one app influences communication via other apps. We created a taxonomy of customization options based on interviews with 15 \"extreme users\" of communication apps. We found that participants tailored their apps to express their identities, organizational culture, and intimate bonds with others. They also experienced expression breakdowns: frustrations around barriers to transferring personal forms of expression across apps, which inspired inventive workarounds to maintain cross-app habits of expression, such as briefly switching apps to generate and export content for a particular conversation. We conclude with implications for personalized expression in ecosystems of communication apps.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359128"}, {"primary_key": "2897968", "vector": [], "sparse_vector": [], "title": "Of Ulti, &apos;hajano&apos;, and &quot;Matachetar otanetak datam&quot;: Exploring Local Practices of Exchanging Confidential and Sensitive Information in Urban Bangladesh.", "authors": ["S. <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Strategies of hiding information over communication media have long been an interest of CSCW and related communities. Most of the studies in this area have focused on various computational means of data protection and their vulnerabilities, and occasionally on social practices situated in the West. However, communities in the Global South often have a rich trove of vernacular arts and crafts of hiding information that might be leveraged to design novel kinds of privacy-preserving technologies. In this paper, we present findings from our three-month long original ethnographic work with various communities in Dhaka, Bangladesh that reveal a wide range of culturally embedded techniques of hiding confidential and sensitive information from their 'others'. Our analysis demonstrates the dynamic nature of these techniques, the learning process associated with them, and their deep relationship with contextual politics that these communities are embedded in. We further connect our findings to the broader interests of CSCW around otherness, ethics, and democracy, and also discuss their implications for design.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359275"}, {"primary_key": "2897969", "vector": [], "sparse_vector": [], "title": "Maintainer Perspectives on Data-driven Transport Asset Management and the Future Role of the Internet-of-Things.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper we present fieldwork findings from engagements with a cooperative group of strategic and operations transport maintainers, responsible for the maintenance of drainage infrastructure to mitigate the risk and impact of flooding on the network. Through transport maintainer perspectives from rail and highways sectors, we focus on developing an understanding of work-practices in the context of transport surface-water management, and how the recent acceleration towards 'data-driven' technologies in support of maintenance intervention decision-making (i.e. the work of coordinating the cleaning of drainage) is integrated and impacting on current work-practice. Furthermore, we document and consider how maintainers perceive the potential role of the Internet-of-Things (IoT) and highlight emerging opportunities and tensions that may arise ahead of its future design and implementation.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359214"}, {"primary_key": "2897970", "vector": [], "sparse_vector": [], "title": "Rural HCI Research: Definitions, Distinctions, Methods, and Opportunities.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "HCI researchers are increasingly conducting research in rural communities. This paper interrogates how rurality has been treated in previous HCI research conducted in developed and high-income countries. We draw from research outside of HCI to suggest how we can effectively engage with rurality in research. We present results of a scoping review of HCI literature that asks: 1) How do HCI researchers define rurality?; 2) How do the unique characteristics of rural communities enter into study findings?; 3) What methods are used in rural research?; and 4) Where has rural research been conducted? More than twice as many rural HCI articles have been conducted in low-income and/or developing countries than in high-income and/or developed countries. HCI researchers rarely define rurality, and when they do, they primarily define it using descriptive rather than sociocultural or symbolic definitions. Rural research findings have primarily addressed infrastructure and distance/geographic isolation as unique rural characteristics, while qualitative, observational, and cross-sectional methods dominate this research. There are further opportunities for HCI research to more productively advance understanding of what rurality is, and how it matters for sociotechnical systems.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359298"}, {"primary_key": "2897971", "vector": [], "sparse_vector": [], "title": "Deconstructing Community-Based Collaborative Design: Towards More Equitable Participatory Design Engagements.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Participatory Design (PD) is envisioned as an approach to democratizing innovation in the design process by shifting the power dynamics between researcher and participant. Recent scholarship in HCI and design has analyzed the ways collaborative design engagements, such as PD situated in the design workshop can amplify voices and empower underserved populations. Yet, we argue that PD as instantiated in the design workshop is very much an affluent and privileged activity that often neglects the challenges associated with envisioning equitable design solutions among underserved populations. Based on two series of community-based PD workshops with underserved populations in the U.S., we highlight key areas of tension and considerations for a more equitable PD approach: historical context of the research environment, community access, perceptions of materials and activities, and unintentional harm in collecting full accounts of personal narratives. By reflecting on these tensions as a call-to-action, we hope to deconstruct the privilege of the PD workshop within HCI and re-center the focus of design on individuals who are historically underserved.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359318"}, {"primary_key": "2897972", "vector": [], "sparse_vector": [], "title": "Joining Together Online: The Trajectory of CSCW Scholarship on Group Formation.", "authors": ["Alexa M<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> S<PERSON> Contractor"], "summary": "The field of Computer Supported Cooperative Work (CSCW) has an enduring interest in studying and designing technologies that bring people together in partnerships, teams, crowds, communities, and other collectives. As the technologies enabling group formation have evolved, so too have the guiding questions pursued by CSCW scholars. This review outlines the trajectory of scholarship on group formation with an eye towards the most pressing future questions in this area. To understand how CSCW researchers have studied technology-enabled group formation, we systematically review articles published at CSCW from 1992 to 2018. Exploring more than 2,000 potentially relevant works, we identified 35 focused on technologies and group formation. Content coding and thematic analysis revealed four periods and six themes in the study of online group formation. These themes include: group composition, self-presentation, assembly mechanisms, recruitment, organizing structures, and group culture. Quo vadis? Based on our review, we offer recommendations for the next generation of CSCW scholarship seeking to understand and enable collectives joining together online.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359250"}, {"primary_key": "2897973", "vector": [], "sparse_vector": [], "title": "Away and (Dis)connection: Reconsidering the Use of Digital Technologies in Light of Long-term Outdoor Activities.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present a study of long-term outdoor activities, based on altogether 34 interviews with 19 participants. Our goal was not only to explore these enjoyable experiences, but more broadly to examine how technology use was recontextualized ‘away’ from the everyday. Outdoor activities are commonly presented as an escape from our technology-infused world. In contrast, our interviews reveal experiences that are heavily dependent on technology, both digital and not. However, digital technology — and in particular the mobile phone — is reconfigured when taken out of its ordinary, often urban and indoor, context. We first present a diversity of ‘aways’ during outdoor activities by depicting cherished freedoms and interpersonal preferences. We then describe how participants managed connection and disconnection while away and upon coming back. To conclude, we discuss how constructions of away can support more purposeful engagements with digital technology, and how pointed (dis)connection can be useful for technology design also in non-outdoor settings.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3361111"}, {"primary_key": "2897974", "vector": [], "sparse_vector": [], "title": "GestureCards: A Hybrid Gesture Notation.", "authors": ["<PERSON>", "Volker Gruhn"], "summary": "Describing gestures in detail has various advantages for project teams: communication is simplified, interaction concepts are documented, and technical decisions are supported. Common gesture notations focus on textual or graphical elements only, but we argue that hybrid approaches have various advantages, especially because some gesture traits are easier to describe with text and others with arrows or icons. We present GestureCards, a hybrid gesture notation mixing graphical and textual elements we developed to describe multi-touch gestures. To evaluate our approach, we compared how users perceive and are affected by different notations. First, we compared GestureCards with a textual notation and observed advantages in terms of speed, correctness, and confidence. Second, we asked participants to compare GestureCards, a textual, and a graphical notation and rate them. The results indicate that the participants' perception depends on the gesture, but GestureCards received consistently good ratings. Third, we monitored several participants working with GestureCards solving practical development tasks for gesture-based applications and they felt well supported by GestureCards.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3331164"}, {"primary_key": "2897975", "vector": [], "sparse_vector": [], "title": "Head-mounted Displays, Smartphones, or Smartwatches? - Augmenting Conversations with Digital Representation of Self.", "authors": ["Ilyena <PERSON>rsk<PERSON>j-Douglas", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Technologies that augment face-to-face interactions with a digital sense of self have been used to support conversations. That work has employed one homogenous technology, either 'off-the-shelf' or with a bespoke prototype, across all participants. Beyond speculative instances, it is unclear what technology individuals themselves would choose, if any, to augment their social interactions; what influence it may exert; or how use of heterogeneous devices may affect the value of this augmentation. This is important, as the devices that we use directly affect our behaviour, influencing affordances and how we engage in social interactions. Through a study of 28 participants, we compared head-mounted display, smartphones, and smartwatches to support digital augmentation of self during face-to-face interactions within a group. We identified a preference among participants for head-mounted displays to support privacy, while smartwatches and smartphones better supported conversational events (such as grounding and repair), along with group use through screen-sharing. Accordingly, we present software and hardware design recommendations and user interface guidelines for integrating a digital form of self into face-to-face conversations.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359281"}, {"primary_key": "2897976", "vector": [], "sparse_vector": [], "title": "Learning to Airbnb by Engaging in Online Communities of Practice.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Technological advances, combined with sustained, minimalist consumerism, have raised the popularity of sharing economy platforms like Airbnb and Uber. These platforms are considered to have disrupted traditional industries and revolutionized how consumers interact with their services. The Computer-Supported Cooperative Work (CSCW) community has researched various aspects of the sharing economy; however, it is unclear how novices grow into experts in its various instantiations. In this paper, we present a qualitative investigation of Airbnb hosts, and Facebook groups in which they participate, for an enriched understanding of their learning mechanisms. Drawing on the theory of Legitimate Peripheral Participation (LPP), our findings highlight the learning mechanisms that enable novice hosts to transition from partaking in peripheral roles to becoming integrated members of their (Facebook) communities of practice. We also present recommendations for sharing economy platforms, micro-entrepreneurs, and the online communities that serve them both.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359330"}, {"primary_key": "2897977", "vector": [], "sparse_vector": [], "title": "Design for Collaborative Information-Seeking: Understanding User Challenges and Deploying Collaborative Dynamic Queries.", "authors": ["<PERSON><PERSON><PERSON> (Ray) Hong", "<PERSON><PERSON> (Mia) Suh", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Although Collaborative Information-Seeking (CIS) is becoming prevalent as people engage in shared decision-making, interface components adopted in the most commonly used information seeking tools (e.g., search, filter, select, and sort) are designed for individual use. To deepen our understanding of (1) how such single-user designs affect people's consensus building processes in CIS and (2) how to devise an alternative design to improve current practices, we conducted two 4-week diary studies and observed how groups seek out places together. Our studies focus on social event coordination as a case where CIS is necessary and important. In Study 1, we examined the major challenges people encounter when performing CIS using their preferred tools. These challenges include difficulties in capturing mutual preferences, high communication cost, and disparity of work depending on a group member's perceived role as an organizer or invitee. We discovered that improving a group's shared understanding of the target information they seek (e.g., places, products) could potentially address the challenges. In Study 2, we designed, deployed, and evaluated ComeTogether, a novel system that supports a group's social event coordination. ComeTogether adopts Collaborative Dynamic Queries (C-DQ), an interface designed to allow a group to share their preferences regarding potential destinations. Study 2 results indicate that using C-DQ increased users' awareness of other group members' preferences in performing CIS, making their coordination more transparent, more inviting, and fairer than what their current practice allows. Meanwhile, ComeTogether improved communication efficiency of groups while presenting opportunities to learn about others and to discover new places. We provide implications for design that explain considerations for adopting C-DQ and identify future research directions.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359208"}, {"primary_key": "2897978", "vector": [], "sparse_vector": [], "title": "Deconstructing Creativity: Non-Linear Processes and Fluid Roles in Contemporary Music and Dance.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The stereotype of creativity as an isolated, individual activity has given way to a more nuanced understanding of creativity as a social process. Our interview study of 23 contemporary music composers and choreographers focuses on the role that artifacts play in shaping creative collaborations with performers. We found that creators and performers form relationships where the creator acts as an author, a curator, a planner, or a researcher, and the performer acts as an interpreter, a creator, an improvisor, or an informant. Furthermore, we found that creators sculpt, layer, remix artifacts, moving fluidly across these different forms of interaction throughout the creative process. We conclude that the slippages that occur at the seams between roles and interactions drive creativity forward by opening up pathways into the future.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359305"}, {"primary_key": "2897979", "vector": [], "sparse_vector": [], "title": "Higher Education Check-Ins: Exploring the User Experience of Hybrid Location Sensing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A large body of literature is dedicated to understanding people's check-in behavior when they use location sharing services to pair their location with a venue, e.g., a restaurant, a park, etc. Check-in behavior in higher education settings, e.g., where students and instructors have academic purposes for check-ins, is under-studied. In this work, we explore how university students apply two different mechanisms, i.e., automatic and manual location-sharing services, to conduct check-ins for an academic purpose (i.e., students sharing their class attendance with their instructor). More specifically, a Bluetooth Low Energy beacon-based technology is applied to enable automatic class check-ins. We conducted two field trials with a total of 141 university students. Our findings showed that several social, technological, and psychological factors impacted the use of auto and manual check-ins. Feedback from the student participants suggested that future higher education check-in systems may need to consider the integration of check-ins for a variety of purposes.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359168"}, {"primary_key": "2897980", "vector": [], "sparse_vector": [], "title": "Understanding the Skill Provision in Gig Economy from A Network Perspective: A Case Study of Fiverr.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The recent emergence of gig economy facilitates the exchange of skilled labor by allowing workers to showcase and sell their skills to a global market. Despite the recent effort on thoroughly examining who workers in the gig economy are and what their experience in the gig economy are like, our knowledge on how exactly do workers provide their skills in gig economy, and how worker's strategies on skill provision and expansion relate to their success in gig economy is still lacking. In this paper, we conduct a case study on a prominent gig economy platform, Fiverr.com, to better understand the provision of skills on it through large-scale, data-driven analysis. In particular, we propose the concept of \"skill space\" from a network perspective to characterize the relationship between different skills by measuring how frequently workers provide different skills together. Through our analysis, we reveal interesting patterns in worker's provision of skills on Fiverr. We then show how these patterns change over time and differ across subgroups of workers with different characteristics. In addition, we find that providing a set of skills that are highly related with each other correlates with a better overall performance in gig economy, and when workers expand their skillsets, expanding to a new skill that is highly-related to the existing skills takes less time and is associated with better performance on the new skill. We conclude by discussing the implications of our findings for gig economy workers and platform in general.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359234"}, {"primary_key": "2897981", "vector": [], "sparse_vector": [], "title": "Automatically Analyzing Brainstorming Language Behavior with <PERSON><PERSON>.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Language both influences and indicates group behavior, and we need tools that let us study the content of what is communicated. While one could annotate these spoken dialogue acts by hand, this is a tedious, not scalable process. We present Meet<PERSON>, a tool for automatically detecting information sharing, shared understanding, word counts, and group activation in spoken interactions. The contribution of our work is two-fold: (1) We validated the tool by showing that the measures computed by <PERSON><PERSON> align with human-generated labels, and (2) we demonstrated the value of <PERSON><PERSON> as a research tool by quantifying aspects of group behavior using those measures and deriving novel findings from that. Our tool is valuable for researchers conducting group science, as well as those designing groupware systems.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359132"}, {"primary_key": "2897982", "vector": [], "sparse_vector": [], "title": "When Delayed in a Hurry: Interpretations of Response Delays in Time-Sensitive Instant Messaging.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "When Instant Messaging (IM) users experience delays in receiving responses from a partner, they may interpret these delays in a variety of ways (e.g., a technological problem vs. the partner is avoiding interaction), and these interpretations can have social consequences. In a laboratory study, we explored whether the presence of a typing indicator shaped people's interpretation of delays. Participants collaborated with a confederate on a time-sensitive task and retrospectively reviewed their interaction. We manipulated delay condition (none, delay, delay with typing indicator) and time pressure (low, high). Participants rated their partners as significantly lower in conversational involvement, were more frustrated, and liked their partners less during a delay whether or not they saw a typing indicator. We discuss implications for the design of new features for IM clients that help people interpret delays appropriately.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3361115"}, {"primary_key": "2897983", "vector": [], "sparse_vector": [], "title": "&quot;My cousin bought the phone for me. I never go to mobile shops.&quot;: The Role of Family in Women&apos;s Technological Inclusion in Islamic Culture.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The intersection of Islam and gender affect technological and social interactions for Muslim women in significant ways and remains an understudied domain for CSCW and related fields. Building on 73 qualitative interviews with low-income women in Punjab, Pakistan, we analyze the complexity of family relationships and the subsequent dynamics of authority around technology uptake and usage by women within non-Western contexts, and, specifically, within the Islamic world. We argue that a Pakistani woman's experience with technology depends on many factors, including gendered roles, generational differences in a family, and wider socio-cultural and religious influences against the backdrop of a culturally conservative and patriarchal society. Our paper highlights the rich family dynamics, including key life events, that transform the roles of both Muslim women and their relatives. Our work is intended to inform scholars, practitioners within development agencies and industry, and other individuals studying technology and development about household dynamics that influence Muslim women's use of technology to encourage them to consider these dynamics during design and implementation processes for technological inclusion.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359148"}, {"primary_key": "2897984", "vector": [], "sparse_vector": [], "title": "&quot;Privacy is not a concept, but a way of dealing with life&quot;: Localization of Transnational Technology Platforms and Liminal Privacy Practices in Cambodia.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Privacy scholarship has shown how norms of appropriate information flow and information regulatory processes vary according to environment, which change as the environment changes, including through the introduction of new technologies. This paper describes findings from a qualitative research study that examines practices and perceptions of privacy in Cambodia as the population rapidly moves into an online environment (specifically Facebook, the most popular Internet tool in Cambodia today). We empirically demonstrate how the concept of privacy differs across cultures and show how the Facebook platform, as it becomes popular worldwide, catalyzes change in norms of information regulation. We discuss how the localization of transnational technology platforms provides a key site in which to investigate changing cultural ideas about privacy, and to discover misalignments between different expectations for information flow. Finally, we explore ways that insufficient localization effort by transnational technology companies puts some of the most marginalized users at disproportionate information disclosure risk when using new Internet tools, and offer some pragmatic suggestions for how such companies could improve privacy tools for users who are far -geographically or culturally - from where the tools are designed.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359230"}, {"primary_key": "2897985", "vector": [], "sparse_vector": [], "title": "&quot;I think we know more than our doctors&quot;: How Primary Caregivers Manage Care Teams with Limited Disease-related Expertise.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Healthcare providers play a critical role in the management of a chronic illness by providing education about the disease, recommending treatment options, and developing care plans. However, when managing a rare disease, patients and their primary caregivers often work with healthcare systems that lack the infrastructure to diagnosis, treat, or provide education on the disease. Little research has explored care coordination practices between patients, family members, and healthcare providers under these circumstances. With the goal of identifying opportunities for technological support, we conducted qualitative interviews with the primary caregivers of children with a rare neurodegenerative disorder, ataxia-telangiectasia. We report on the responsibilities that the primary caregivers take on in response to care teams' lack of experience with the illness, and the ways in which an online health community supports this care coordination work. We also describe barriers that limited participants' use of the online health community, including the emotional consequences of participation and information overload. Based on these findings, we discuss two promising research agendas for supporting rare disease management: facilitating primary caregivers' care coordination tasks and increasing access to online community knowledge.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359261"}, {"primary_key": "2897986", "vector": [], "sparse_vector": [], "title": "Trust and Technology Repair Infrastructures in the Remote Rural Philippines: Navigating Urban-Rural Seams.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper analyzes the processes and challenges of technology repair in remote, low-income areas far from standard ICT repair infrastructure. Our sites of study are the fishing and farming villages of Dibut, Diotorin, and Dikapinisan in Aurora Province, Philippines, located in coastal coves against a mountain range. Residents are geographically isolated from urban areas, with the nearest peri-urban center of Baler a boat trip of several hours away, infeasible in some sea conditions. Unlike prior work in more connected rural areas, there are no local repair shops and device repair is uncommon, despite frequent breakage due to harsh conditions for electronics. The scarcity of local electronics repair limits technology access and leads to accumulation of e-waste. While prior work demonstrates that local electronics repair capability does arise in many rural areas around the world, we must also acknowledge that the successful emergence of this infrastructure depends on the intersection of many structural conditions and cannot be taken for granted. We present the material hardships of achieving local repair in terms of seams between heterogeneous urban and rural infrastructures, which illustrate the cove communities' marginality with respect to many forms of public infrastructure. However, intermittent and informal repair infrastructures based on trust relationships emerge to patch these seams in remote settings. We show how trust affects the way people dynamically construct repair infrastructure and why, based on their remoteness and the resulting value propositions of repair. Networks of trust between repairers, their clients, suppliers, fellow repairers, and certifying or training institutions crucially facilitate the movement of resources and expertise across the Philippines, but also reinforce the marginality of residents and repairers in the coves. Despite these structural challenges, local people are able to maintain a robust ecosystem for rural electrical line repair, from which we generalize the model of training grounds as a strategy for sustaining local communities of repair experts.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359201"}, {"primary_key": "2897987", "vector": [], "sparse_vector": [], "title": "Quantification of Gender Representation Bias in Commercial Films based on Image Analysis.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In film directing, a bias towards the representation of a particular gender can cause the audience to form a distorted stereotype of the gender role. The <PERSON><PERSON><PERSON> test has been widely used to objectively judge the existence of such bias in films. However, because its analysis is based solely on the script of a film, the <PERSON><PERSON><PERSON> test is incapable of considering the broad spectrum of bias that films can have as a visual medium. This study proposes a more comprehensive analysis system that quantifies the degree of bias in the visual representations of female and male characters in commercial films. By analyzing the image frames of a movie using the latest image analysis techniques, a total of 40 films were analyzed based on 8 quantitative indices. The result demonstrates that there exists a statistically significant difference in the visual representation of female and male characters. Specifically, female characters showed lower values in emotional diversity, spatial occupancy, and temporal occupancy compared to male characters in commercial films. Further, female characters were less likely to wear eyeglasses and also appeared more in static scenes, such as indoors.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359300"}, {"primary_key": "2897989", "vector": [], "sparse_vector": [], "title": "Into Scandinavia: When Online Fatherhood Reflects Societal Infrastructures.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Denmark is a generous welfare state which provides resources and legal means for fathers to take their parental role seriously and engage with their children. In this paper, we explore the relation between Danish fathers' interaction online and the societal, legal, and economic infrastructures in which they are situated. By focusing on how fathers living in Denmark make use of the Internet and social media sites to facilitate their role as parents, we are able to explore how online engagement is shaped by the different societal 'norms' of parenting. Our research outlines the ways in which societal infrastructures influence how fathers perceive, and subsequently make use of social media in relation to child-caring. We find that fathers discuss their experiences of legal inequities and stereotypical discrimination on social network sites like Facebook. We also study fathers' online reviews of a Danish parenting App, FAR, designed specifically to support fathers. By analyzing social media discussions around fatherhood in Denmark, we found connections to the ways in which the current political climate shapes and influences fatherhood in Denmark, as they reflect the societal infrastructures which situate fathers in contemporary Denmark. Further, we found a strong political interest for collective action to transform the societal infrastructures to support legal equality for child caretaking across genders. This strong political motivation is distinct from existing studies exploring how fatherhood is displayed on social media in other countries such as the USA. On this basis, we argue that research exploring social media use in institutions which are strongly shaped by societal norms, must explicitly consider the role which society takes in shaping such institutions, and include these aspects into the analysis. Our data show that fathers use social media sites as platforms to produce a fatherhood more in line with their lived experience of parenting, and that they advocate for collective political action to strengthen fathers' legal rights. Copyright is held by the owner/author(s). Publication rights licensed to ACM.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3361112"}, {"primary_key": "2897990", "vector": [], "sparse_vector": [], "title": "?Did You Suspect the Post Would be Removed?&quot;: Understanding User Reactions to Content Removals on Reddit.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Thousands of users post on Reddit every day, but a fifth of all posts are removed. How do users react to these removals? We conducted a survey of 907 Reddit users, asking them to reflect on their post removal a few hours after it happened. Examining the qualitative and quantitative responses from this survey, we present users' perceptions of the platform's moderation processes. We find that although roughly a fifth (18%) of the participants accepted that their post removal was appropriate, a majority of the participants did not --- over a third (37%) of the participants did not understand why their post was removed, and further, 29% of the participants expressed some level of frustration about the removal. We focus on factors that shape users' attitudes aboutfairness in moderation andposting again in the community. Our results indicate that users who read community guidelines or receive explanations for removal are more likely to perceive the removal as fair and post again in the future. We discuss implications for moderation practices and policies. Our findings suggest that the extra effort required to establish community guidelines and educate users with helpful feedback is worthwhile, leading to better user attitudes about fairness and propensity to post again.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359294"}, {"primary_key": "2897991", "vector": [], "sparse_vector": [], "title": "Does Transparency in Moderation Really Matter?: User Behavior After Content Removal Explanations on Reddit.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "When posts are removed on a social media platform, users may or may not receive an explanation. What kinds of explanations are provided? Do those explanations matter? Using a sample of 32 million Reddit posts, we characterize the removal explanations that are provided to Redditors, and link them to measures of subsequent user behaviors---including future post submissions and future post removals. Adopting a topic modeling approach, we show that removal explanations often provide information that educate users about the social norms of the community, thereby (theoretically) preparing them to become a productive member. We build regression models that show evidence of removal explanations playing a role in future user activity. Most importantly, we show that offering explanations for content moderation reduces the odds of future post removals. Additionally, explanations provided by human moderators did not have a significant advantage over explanations provided by bots for reducing future post removals. We propose design solutions that can promote the efficient use of explanation mechanisms, reflecting on how automated moderation tools can contribute to this space. Overall, our findings suggest that removal explanations may be under-utilized in moderation practices, and it is potentially worthwhile for community managers to invest time and resources into providing them.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359252"}, {"primary_key": "2897992", "vector": [], "sparse_vector": [], "title": "Moderation Challenges in Voice-based Online Communities on Discord.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Online community moderators are on the front lines of combating problems like hate speech and harassment, but new modes of interaction can introduce unexpected challenges. In this paper, we consider moderation practices and challenges in the context of real-time, voice-based communication through 25 in-depth interviews with moderators on Discord. Our findings suggest that the affordances of voice-based online communities change what it means to moderate content and interactions. Not only are there new ways to break rules that moderators of text-based communities find unfamiliar, such as disruptive noise and voice raiding, but acquiring evidence of rule-breaking behaviors is also more difficult due to the ephemerality of real-time voice. While moderators have developed new moderation strategies, these strategies are limited and often based on hearsay and first impressions, resulting in problems ranging from unsuccessful moderation to false accusations. Based on these findings, we discuss how voice communication complicates current understandings and assumptions about moderation, and outline ways that platform designers and administrators can design technology to facilitate moderation.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359157"}, {"primary_key": "2897993", "vector": [], "sparse_vector": [], "title": "Assisted Music Score Reading Using Fixed-Gaze Head Movement: Empirical Experiment and Design Implications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Eye-tracking has a very strong potential in human computer interaction (HCI) as an input modality, particularly in mobile situations. However, it lacks convenient action triggering methods. In our research, we investigate the combination of eye-tracking and fixed-gaze head movement, which allows us to trigger various commands without using our hands or changing gaze direction. In this instance, we have proposed a new algorithm for fixed-gaze head movement detection using only scene images captured by the scene camera equipped in front of the head-mounted eye-tracker, for the purpose of saving computation time. To test the performance of our fixed-gaze head movement detection algorithm and the acceptance of triggering commands by these movements when the user's hands are occupied by another task, we have designed and developed an experimental application known as EyeMusic. The EyeMusic system is a music reading system, which can play the notes of a measure in a music score that the user does not understand. By making a voluntary head movement when fixing his/her gaze on the same point of a music score, the user can obtain the desired audio feedback. The design, development and usability testing of the first prototype for this application are presented in this paper. The usability of our application is confirmed by the experimental results, as 85% of participants were able to use all the head movements we implemented in the prototype. The average success rate of this application is 70%, which is partly influenced by the performance of the eye-tracker we use. The performance of our fixed-gaze head movement detection algorithm is 85%, and there were no significant differences between the performance of each head movement.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3300962"}, {"primary_key": "2897994", "vector": [], "sparse_vector": [], "title": "Circadian Rhythms and Physiological Synchrony: Evidence of the Impact of Diversity on Small Group Creativity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Circadian rhythms determine daily sleep cycles, mood, and cognition. Depending on an individual's circadian preference, or chronotype (i.e.,\"early birds\" and \"night owls\"), the rhythms shift earlier or later in the day. Early birds experience circadian arousal peaks earlier in the morning than night owls. Prior work has shown that individuals are more effective at analytic tasks during their peak arousal times but are more creative during their off-peak times. We investigate if these findings hold true for small groups. We find that time of day and a group's majority chronotype impact performance on analytic and creative tasks. Physiological synchrony among group members positively predicts group satisfaction. Specifically, homogeneous groups perform worse on all tasks regardless of time of day, but they achieve greater physiological synchrony and feel more satisfied as a group. Based on these findings, we present and advocate for a temporal dimension of group diversity.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359162"}, {"primary_key": "2897995", "vector": [], "sparse_vector": [], "title": "Cash, Digital Payments and Accessibility: A Case Study from Metropolitan India.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Despite the growing interest in digitization and money in HCI and CSCW, the use of cash and digital payments by people with disabilities has received scant attention. We present findings from a qualitative study of people with visual impairments’ use of cash and digital payments in metropolitan India. Using ride-hailing services as an exemplar, we find that both cash and digital payments were inaccessible to participants. We use <PERSON> and <PERSON><PERSON>’s \"moneywork\" as a theoretical framework to highlight the \"added\" work necessitated by this inaccessibility; that is, the work done in addition to the interactional work necessary to complete financial transactions. We argue that this \"added\" work is instrumental in \"making\" payments accessible. We discuss how ride-hailing platforms mediated collaborations between drivers and riders in relation to payments, while still making \"moneywork\" essential. We provide recommendations to improve the accessibility of digital payments to facilitate greater economic inclusion.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359199"}, {"primary_key": "2897996", "vector": [], "sparse_vector": [], "title": "Engaging Identity, Assets, and Constraints in Designing for Resilience.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We contribute to the growing conversation on assets-based approaches to design in Computer-Supported Cooperative Work (CSCW) and Human-Computer Interaction (HCI) with a qualitative study of resilience. Our study is situated within a community health infrastructure in a rural county in southwest Kenya, where health organizations pay community health workers' salaries via digital payments, backdropped by ongoing issues with missing and delayed payments. Through the lens of intersectionality, we examine how community health workers of diverse backgrounds and contracted status respond to the mandated use of digital payment methods and long payment delays. We highlight how resilience in this context is situated in workers' intersecting socioeconomic and professional identities, which shape the assets and constraints that workers engage with in efforts to be resilient. We leverage our findings to discuss how assets-based approaches to design can be further operationalized and used to sustainably support resilience.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359315"}, {"primary_key": "2897997", "vector": [], "sparse_vector": [], "title": "Street-Level Realities of Data Practices in Homeless Services Provision.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Quantification and standardization of concepts like risk and vulnerability are increasingly being used in high-stakes, client-facing social services, also presenting the potential for data-driven tools for decision-making in this context. These trends necessitate an understanding of the role of quantitative data in the work of street-level decision-makers in social services. We present a qualitative study of existing data practices and perceptions of potential data-driven tools in housing allocation, engaging the perspective of service providers and policymakers in homeless services in a large urban county in the United States. Our findings highlight participants' concerns around centering clients' choices and ensuring integrity in a resource-constrained, high-stakes context. We also highlight differences between the perspectives of policymakers and service providers on standardization and fairness in the decision-making process. We discuss how use of and policies around data in social services need to consider the importance of the relationships that client-facing service providers have with other workers in the organization, with their work, and with clients.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359286"}, {"primary_key": "2897998", "vector": [], "sparse_vector": [], "title": "Precarious Interventions: Designing for Ecologies of Care.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we present ethnographic account of people's everyday behavioral health experiences in the city of Jackson, Michigan to explore community forms of care work through an infrastructural lens. Detailing people's interactions with clinical processes and health policies, local resources, and diverse social worlds, we highlight problematic healthcare delivery gaps, as well as the informal (and often invisible) practices people depend upon to manage their health needs given socioeconomic hardships and cultural concerns. We also discuss the city's efforts to support local behavioral health needs through the development of a community health record. Placing fieldwork findings in conversation with the goals of this ongoing civic design project, we propose the analytic sensibility of precarious intervention to unpack the significance of the infrastructural tensions and power relations at play when people seek solutions to complex sociotechnical problems. Precarious intervention calls for CSCW research that attends to 1) the collective labor necessary to create and maintain ecologies of care in the face of infrastructural brokenness; and 2) the high-stakes and varied costs of 'engagement' for different community stakeholders.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359215"}, {"primary_key": "2897999", "vector": [], "sparse_vector": [], "title": "The Dynamics of Peer-Produced Political Information During the 2016 U.S. Presidential Campaign.", "authors": ["<PERSON>"], "summary": "Wikipedia plays a crucial role for online information seeking and its editors have a remarkable capacity to rapidly revise its content in response to current events. How did the production and consumption of political information on Wikipedia mirror the dynamics of the 2016 U.S. Presidential campaign? Drawing on systems justification theory and methods for measuring the enthusiasm gap among voters, this paper quantitatively analyzes the candidates' biographical and related articles and their editors. Information production and consumption patterns match major events over the course of the campaign, but Trump-related articles show consistently higher levels of engagement than Clinton-related articles. Analysis of the editors' participation and backgrounds show analogous shifts in the composition and durability of the collaborations around each candidate. The implications for using Wikipedia to monitor political engagement are discussed.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359135"}, {"primary_key": "2898000", "vector": [], "sparse_vector": [], "title": "The Tools of Management: Adapting Historical Union Tactics to Platform-Mediated Labor.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "At the same time that workers' rights are generally declining in the United States (US), workplace computing systems gather more data about workers and their activities than ever before. The rise of large scale labor analytics raises questions about how and whether workers could use such data to advocate for their own goals. Here, we analyze the historical development of workplace technology design methods in CSCW to show how mid-20th century labor responses to scientific management can inform directions in contemporary digital labor advocacy. First, we demonstrate how specific methodological tendencies from industrial scientific management were adapted to work in CSCW, and then subsequently altered in crowd work and social computing research to more closely resemble industrial approaches. Next, we show how three tactics used by labor unions to strategically engage with industrial scientific management in the mid-20th century can inform data-driven worker advocacy in platform-mediated work. Finally, we discuss how this history shapes our understanding of worker participation and the implications of using worker data for contemporary advocacy goals.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359310"}, {"primary_key": "2898001", "vector": [], "sparse_vector": [], "title": "Technological Frames and User Innovation: Exploring Technological Change in Community Moderation Teams.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Management of technological change in organizations is one of the most enduring topics in the literature on computer-supported cooperative work. The successful navigation of technological change is both more challenging and more critical in online communities that are entirely mediated by technology than it is in traditional organizations. This paper presents an analysis of 14 in-depth interviews with moderators of subcommunities of one technological platform (Reddit) that added communities on a new technological platform (Discord). Moderation teams experienced several problems related to moderating content at scale as well as a disconnect between the affordances of Discord and their assumptions based on their experiences on Reddit. We found that moderation teams used Discord's API to create scripts and bots that augmented Discord to make the platform work more like tools on Reddit. These tools were particularly important in communities struggling with scale. Our findings suggest that increasingly widespread end user programming allow users of social computing systems to innovate and deploy solutions to unanticipated design problems by transforming new technological platforms to align with their past expectations.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359146"}, {"primary_key": "2898002", "vector": [], "sparse_vector": [], "title": "Love in Lyrics: An Exploration of Supporting Textual Manifestation of Affection in Social Messaging.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Affectionate communication, the conveyance of closeness, care, and fondness for another, plays a key role in romantic relationships. While the pervasive use of digital technology for communication limits affectionate interaction through nonverbal cues -- a major channel of expression in face-to-face settings, there have been few approaches which scaffold couples' romantic text conversations. To bridge this gap, we propose a novel interactive system Lily which gives users inspirations to enrich their romantic expressions in text messaging. It first listens to users' original input and then recommends romantic lyrics holding the closest meaning in real-time during chats with partners. After a three-day empirical study, participants who are real-life couples reported that they not only received useful cues from <PERSON> in terms of how to polish their affectionate expressions, but also learnt to enrich the conversation with topics enlightened by its recommendations. Based on our findings, we finally provide several design considerations for actual deployment of such an application.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359181"}, {"primary_key": "2898003", "vector": [], "sparse_vector": [], "title": "Gig Platforms, Tensions, Alliances and Ecosystems: An Actor-Network Perspective.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The algorithm-based management exercised by digital gig platforms contributes to information and power asymmetries that are pervasive in the gig economy. Although the design of these platforms may foster unbalanced relationships, in this paper, we outline how freelancers and clients on the gig platform Upwork can leverage a network of alliances with external digital platforms to repossess their displaced agency within the gig economy. Building on 39 interviews with Upwork freelancers and clients, we found a dynamic ecosystem of digital platforms that facilitate gig work through and around the Upwork platform. We use actor-network theory to: 1) delineate Upwork's strategy to establish a comprehensive and isolated platform within the gig economy, 2) track human and nonhuman alliances that run counter to Upwork's system design and control mechanisms, and 3) capture the existence of a larger ecosystem of external digital platforms that undergird online freelancing. This work explicates the tensions that Upwork users face, and also illustrates the multiplicity of actors that create alliances to work with, through, around, and against the platform's algorithmic management.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359314"}, {"primary_key": "2898004", "vector": [], "sparse_vector": [], "title": "&quot;Becoming Part of Something Bigger&quot;: Direct to Consumer Genetic Testing, Privacy, and Personal Disclosure.", "authors": ["<PERSON>"], "summary": "Direct to consumer genetic testing (DTCGT) services where users can identify their inherited diseases and traits, find genetic relatives, and learn more about their ethnic heritage continue to grow in popularity. At the same time, one's DNA is one of the most identifiable, immutable forms of personal information, and sharing it carries risks to one's privacy. What motivates individuals to engage in DTCGT, and what are the implications for information privacy at both the individual and societal levels? This study uses qualitative interviews with ten customers of the DTCGT service 23andMe to explore why they engaged in DTCGT, the benefits they received, their expectations of privacy, and perceptions of risk. It also introduces the use of social exchange theory as a theoretical framework for examining the social dimensions of information privacy and personal disclosure. The findings demonstrate that the participants' assumptions of anonymity, as well as their their belief that their contributions to online genetic databases aid the public good, were key motivating factors. The participants were generally unaware of the potential risks to their individual genetic privacy as well as the impact of large scale genetic testing databases on networked, collective privacy. The findings demonstrate that framework of social exchange theory aids in understanding how the form of the relationship affected the participants' decisions to disclose their personal information to 23andMe as well as their perceptions of risk in the DTCGT context.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359260"}, {"primary_key": "2898005", "vector": [], "sparse_vector": [], "title": "&quot;The Most Trustworthy Coin&quot;: How Ideological Tensions Drive Trust in Bitcoin.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Bitcoin is an innovative technological network, a new, non-governmental currency, and a worldwide group of users. In other words, Bitcoin is a complex sociotechnical system with a complex set of risks and challenges for anyone using it. We investigated how everyday users of Bitcoin develop trust in Bitcoin on one of the largest online communities devoted to Bitcoin: the Reddit.com r/bitcoin forum. Using qualitative content analysis, we examined how trust in Bitcoin develops based on contributions to this community. On r/bitcoin, trust in Bitcoin is driven by a pervasive ideology we call the \"True Bitcoiner\" ideology. This ideological viewpoint in centered on the interpretation of Bitcoin as functionally \"trustless\" and risk-free. Despite widespread evidence of emerging individual and system-level risks with using Bitcoin, participants continue to maintain this ideological perspective. This ideology consists of three primary beliefs: viewing Bitcoin's technology as more trustworthy than its people; rejecting 'corrupt' social hierarchies related to money; and the importance of accumulating or 'HODLing' quantities of Bitcoin as a strategy to create an ideal future. We conclude that this \"True Bitcoiner\" ideology is maintained despite contradictory evidence in the world because it allows participants to more easily interpret Bitcoin and make decisions by reducing perceived risk and uncertainty in the system. The role of this ideology on r/bitcoin demonstrates an expanded conceptualization of how trust is created and socially-mediated in socio-technical contexts.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359138"}, {"primary_key": "2898006", "vector": [], "sparse_vector": [], "title": "Understanding Decision-Making in Recruitment: Opportunities and Challenges for Information Technology.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Although the composition of individuals can strongly affect the success of professional collaboration, organizations often struggle with their so-called social matching decisions. For example, when recruiting new people to an organization, the decision-making is often reduced to intuitively matching individuals based on vague descriptions of projects or positions. The role of technology in recruiting is typically confined to gathering and presenting simple candidate profiles. We argue that many issues in recruitment boil down to lack of understanding the process of decision-making from social matching perspective, covering aspects like identification of relevant selection criteria and choice of the most suitable candidate. To better understand the appropriate roles of information technology (IT) in this domain, we interviewed 21 expert matchmakers, such as HR specialists and headhunters. Based on qualitative analysis of their experiences, we provide a bottom-up framework of the decision-making stages in recruitment, focusing on the pertinent challenges from the perspective of social matching. The findings indicate that, particularly, the epistemic asymmetry between the recruiter and candidates regarding the expected qualities calls for deliberation throughout the decision-making process. Matchmakers also struggle between contradictory ideals of agility and holistic decision-making. Based on the findings and relevant literature, we propose six roles that IT could play in social matching decisions in recruitment.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3361123"}, {"primary_key": "2898008", "vector": [], "sparse_vector": [], "title": "Restoration Work: Responding to Everyday Challenges of HIV Outreach.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "There has been a growing commitment across the fields of Computer-Supported Cooperative Work (CSCW) and Human-Computer Interaction for Development (HCI4D) towards investigating the design and deployment of technologies in the context of complex healthcare ecologies. We present a qualitative inquiry of one such context, as we examine the outreach practices and everyday challenges of workers at a community-based organization in Gujarat (India) that works with People Living with the Human Immuno-deficiency Virus(PLHIV). Drawing on <PERSON><PERSON>'s framework of community cultural wealth-apt for such intersectional settings-and the lens of articulation work, we describe how the workers at Vikas build and strengthen varied forms of capital to restore \"old normals,\" or what life was like for their PLHIV clients prior to diagnoses. Finally, we propose that attention to this nature of restoration work, and the workers' engagement with diverse forms of community cultural wealth, allows us to reflect on how technologies might (or might not) be designed to impact social and affective aspects of health.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359156"}, {"primary_key": "2898009", "vector": [], "sparse_vector": [], "title": "Engaging Feminist Solidarity for Comparative Research, Design, and Practice.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>-<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Research in the fields of Computer Supported Cooperative Work (CSCW) and Human-Computer Interaction (HCI) is increasingly embracing and moving across borders. While universalism in such research is widely rejected, sole focus on the \"particular'' is also commonly critiqued. <PERSON><PERSON> unpacks this tension, calling for balance via \"deliberate efforts to understand the interplay of human universals and cultural differences, and how it pertains to design.'' In this paper, one such deliberate effort, we introduce the notion of feminist solidarity---as theorized by <PERSON>---for drawing connections across borders in CSCW and HCI research, design, and practice. To enable contributions in these fields to cross cultures and geographies in productive ways, we draw attention to commonalities in the struggles and processes of resistance operating in different contexts of study. To do this, we present lessons learned from conducting three comparative studies in six contexts, which were located across various borders. The primary contribution of our analysis is to leverage a feminist solidarity-based approach towards extending conversations on comparative, transnational, and feminist CSCW and HCI research, design, and practice. Our focus remains on resource-constrained regions across the world, in both the Global North and South.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359269"}, {"primary_key": "2898010", "vector": [], "sparse_vector": [], "title": "Modeling Islamist Extremist Communications on Social Media using Contextual Dimensions: Religion, Ideology, and Hate.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Arpinar", "<PERSON><PERSON>"], "summary": "Terror attacks have been linked in part to online extremist content. Online conversations are cloaked in religious ambiguity, with deceptive intentions, often twisted from mainstream meaning to serve a malevolent ideology. Although tens of thousands of Islamist extremism supporters consume such content, they are a small fraction relative to peaceful Muslims. The efforts to contain the ever-evolving extremism on social media platforms have remained inadequate and mostly ineffective. Divergent extremist and mainstream contexts challenge machine interpretation, with a particular threat to the precision of classification algorithms. Radicalization is a subtle long-running persuasive process that occurs over time. Our context-aware computational approach to the analysis of extremist content on Twitter breaks down this persuasion process into building blocks that acknowledge inherent ambiguity and sparsity that likely challenge both manual and automated classification. Based on prior empirical and qualitative research in social sciences, particularly political science, we model this process using a combination of three contextual dimensions -- religion, ideology, and hate -- each elucidating a degree of radicalization and highlighting independent features to render them computationally accessible. We utilize domain-specific knowledge resources for each of these contextual dimensions such as Qur'an for religion, the books of extremist ideologues and preachers for political ideology and a social media hate speech corpus for hate. The significant sensitivity of the Islamist extremist ideology and its local and global security implications require reliable algorithms for modelling such communications on Twitter. Our study makes three contributions to reliable analysis: (i) Development of a computational approach rooted in the contextual dimensions of religion, ideology, and hate, which reflects strategies employed by online Islamist extremist groups, (ii) An in-depth analysis of relevant tweet datasets with respect to these dimensions to exclude likely mislabeled users, and (iii) A framework for understanding online radicalization as a process to assist counter-programming. Given the potentially significant social impact, we evaluate the performance of our algorithms to minimize mislabeling, where our context-aware approach outperforms a competitive baseline by 10.2% in precision, thereby enhancing the potential of such tools for use in human review.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359253"}, {"primary_key": "2898011", "vector": [], "sparse_vector": [], "title": "&quot;Point-of-Care Manufacturing&quot;: Maker Perspectives on Digital Fabrication in Medical Practice.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Maker culture is on the rise in healthcare with the adoption of consumer-grade fabrication technologies. However, little is known about the activities and resources involved in prototyping medical devices to improve patient care. In this paper, we refer to such activity asmedical making to report findings based on a qualitative study of stakeholder engagement in physical prototyping (making) experiences. We examine perspectives from diverse stakeholders including clinicians, engineers, administrators, and medical researchers. Through 18 semi-structured interviews with medical-makers in the US and Canada, we analyze making activity in medical settings. We find that medical makers share strategies to address risks, adopt labor roles, and acquire resources within traditional medical practice. Our findings outline how medical-makers mitigate risks for patient safety, collaborate with local and global stakeholder networks, and overcome constraints of co-location and material practices. We recommend a clinician-aided software system, partially-open repositories, and a collaborative skill-sharing social network to extend their strategies in support of medical making.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359193"}, {"primary_key": "2898012", "vector": [], "sparse_vector": [], "title": "Editors&apos; Message.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "It is our great pleasure to welcome you to this issue of the Proceedings of the ACM on Human- Computer Interaction, on the contributions of the research community Computer-Supported Cooperative Work and Social Computing (CSCW). This issue contains a carefully selected set of papers, accepted through our review process from among the 658 world-wide articles submitted by the Spring 2019 deadline. After the first round of peer review, 325 (49.4%) papers were invited to the Revise and Resubmit phase. After receiving the revised submissions, the external reviewers and the program committee reviewed all second round contributions. Finally, the program committee came together for a three-day online editorial committee meeting, held to allow for collective deliberation. Ultimately, 205 papers (31.2%) were accepted.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359125"}, {"primary_key": "2898013", "vector": [], "sparse_vector": [], "title": "Safe Enough to Share: Setting the Dementia Agenda Online.", "authors": ["<PERSON>", "<PERSON>"], "summary": "CSCW research is increasingly interested in the ways that people use technology to discuss health and disability online. In addition to studying how people share information and seek and provide emotional support, a growing area of interest is health activism. In this paper, we analyze how a project centered around sharing \"real and raw\" experiences with dementia provides a safe platform for people to share their authentic experiences. These accounts counter predominant depictions of dementia and push back on tokenistic involvement of people with this condition. In a study involving observations and interviews with members of this project, we find that people with dementia must negotiate several goals which at times compete with each other: sharing a \"real and raw\" look at dementia, changing attitudes, showcasing a polished presentation, and inhabiting a safe space. The paper concludes with a discussion of future directions for CSCW on configuring a space for dialogue on sensitive topics, health activism, and sharing online with dementia.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359187"}, {"primary_key": "2898014", "vector": [], "sparse_vector": [], "title": "Procedural Justice in Algorithmic Fairness: Leveraging Transparency and Outcome Control for Fair Algorithmic Mediation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "As algorithms increasingly take managerial and governance roles, it is ever more important to build them to be perceived as fair and adopted by people. With this goal, we propose a procedural justice framework in algorithmic decision-making drawing from procedural justice theory, which lays out elements that promote a sense of fairness among users. As a case study, we built an interface that leveraged two key elements of the framework---transparency and outcome control---and evaluated it in the context of goods division. Our interface explained the algorithm's allocative fairness properties (standards clarity) and outcomes through an input-output matrix (outcome explanation), then allowed people to interactively adjust the algorithmic allocations as a group (outcome control). The findings from our within-subjects laboratory study suggest that standards clarity alone did not increase perceived fairness; outcome explanation had mixed effects, increasing or decreasing perceived fairness and reducing algorithmic accountability; and outcome control universally improved perceived fairness by allowing people to realize the inherent limitations of decisions and redistribute the goods to better fit their contexts, and by bringing human elements into final decision-making.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359284"}, {"primary_key": "2898015", "vector": [], "sparse_vector": [], "title": "WeBuildAI: Participatory Framework for Algorithmic Governance.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Algorithms increasingly govern societal functions, impacting multiple stakeholders and social groups. How can we design these algorithms to balance varying interests in a moral, legitimate way? As one answer to this question, we present WeBuildAI, a collective participatory framework that enables people to build algorithmic policy for their communities. The key idea of the framework is to enable stakeholders to construct a computational model that represents their views and to have those models vote on their behalf to create algorithmic policy. As a case study, we applied this framework to a matching algorithm that operates an on-demand food donation transportation service in order to adjudicate equity and efficiency trade-offs. The service's stakeholders--donors, volunteers, recipient organizations, and nonprofit employees--used the framework to design the algorithm through a series of studies in which we researched their experiences. Our findings suggest that the framework successfully enabled participants to build models that they felt confident represented their own beliefs. Participatory algorithm design also improved both procedural fairness and the distributive outcomes of the algorithm, raised participants' algorithmic awareness, and helped identify inconsistencies in human decision-making in the governing organization. Our work demonstrates the feasibility, potential and challenges of community involvement in algorithm design.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359283"}, {"primary_key": "2898016", "vector": [], "sparse_vector": [], "title": "An Empirical Study of How Socio-Spatial Formations are Influenced by Interior Elements and Displays in an Office Context.", "authors": ["Bokyung Lee", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The design of a workplace can have a profound impact on the effectiveness of the workforce utilizing the space. When considering dynamic social activities in the flow of work, the constraints of the static elements of the interior reveals the adaptive behaviour of the occupants in trying to accommodate these constraints while performing their daily tasks. To better understand how workplace design shapes social interactions, we ran an empirical study in an office context over a two week period. We collected video from 24 cameras in a dozen space configurations totaling 1,920 hours of recorded activities. We utilized computer vision techniques, to produce skeletonized representations of the occupants, to assist in the annotation and data analysis process. We present our findings of socio-spatial formation patterns and the effects of furniture and interior elements on the observed behaviour of collaborators for both computer-supported work and for unmediated social interaction. Combining the observations with an interview of the occupants' reflections, we discuss dynamics of socio-spatial formations and how this knowledge can support social interactions in the domain of space design systems and interactive interiors.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359160"}, {"primary_key": "2898017", "vector": [], "sparse_vector": [], "title": "The Social Infrastructure of Co-spaces: Home, Work, and Sociable Places for Digital Nomads.", "authors": ["<PERSON><PERSON><PERSON>", "Austin L. <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The rise of co-working and co-living spaces, as well as related shared spaces such as makerspaces and hackerspaces-a group we refer to as various types of \"co-spaces\" - has helped facilitate a parallel expansion of the \"digital nomad (DN)\" lifestyle. Digital nomads, colloquially, are those individuals that leverage digital infrastructures and sociotechnical systems to live location-independent lives. In this paper, we use Oldenburg's framework of a first (home), second (work), and third (social) place as an analytical lens to investigate how digital nomads understand the affordance of these different types of spaces. We present an analysis of posts and comments on the '/r/digitalnomad' subreddit, a vibrant online community where DNs ask questions and share advice about the different types of places and amenities that are necessary to pursue their digital nomad lifestyle. We found that places are often assessed positively or negatively relative to one primary characteristic: either they provide a means for nomads to maintain a clear separation between the social and professional aspects of their lives, or they provide a means to merge these aspects together. Digital nomads that favor the first type of place tend to focus on searching for factors that they feel will promote their own work productivity, whereas DNs that favor the second type of place tend to focus on factors that they feel will allow them to balance their work and social lives. We also build on linkages between the notion of a third place and the more recent theoretical construct of social infrastructure. Ultimately, we demonstrate how DNs' interests in co-spaces provide a kind of edge-case for CSCW and HCI scholars to explore how sociotechnical systems, such as variants of co-spaces, inform one another as well as signify important details regarding new ways of living and engaging with technology.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359244"}, {"primary_key": "2898018", "vector": [], "sparse_vector": [], "title": "SMAC: A Simplified Model of Attention and Capture in Multi-Device Desk-Centric Environments.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Prior research has demonstrated that users are increasingly employing multiple devices during daily work. Currently, devices such as keyboards, cell phones, and tablets remain largely unaware of their role within a user's workflow. As a result, transitioning between devices is tedious, often to the degree that users are discouraged from taking full advantage of the devices they have within reach. This work explores the device ecologies used in desk-centric environments and complies the insights observed into SMAC, a simplified model of attention and capture that emphasizes the role of user-device proxemics, as mediated by hand placement, gaze, and relative body orientation, as well as inter-device proxemics. SMAC illustrates the potential of harnessing the rich, proxemic diversity that exists between users and their device ecologies, while also helping to organize and synthesize the growing body of literature on distributed user interfaces. An evaluation study using SMAC demonstrated that users could easily understand the tenants of user- and inter-device proxemics and found them to be valuable within their workflows.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3300961"}, {"primary_key": "2898019", "vector": [], "sparse_vector": [], "title": "Does Driving as a Form of.", "authors": ["Linfeng Li", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "About 20% of the U.S. unemployed population has been out of the labor force for more than 6 months. The rise of the gig economy has changed the landscape of nontraditional employment opportunities for these predominantly low-skilled long-term unemployed workers. This particular type of on-demand work can be used to fill unemployment gaps and offers little to no training costs and flexible hours. Therefore, we explore whether driving as a form of gig work helps to mitigate the negative effects of long-term unemployment for low-skilled job seekers with employment gaps, and how employers evaluate workers who have held non-traditional jobs. Using a correspondence audit study with 1006 job applications, we evaluated whether a set of resumes \"enhanced\" with experience driving for a real-time ridesharing service received more callbacks than baseline resumes with an employment gap. We found no evidence that driving as a form of gig work increased the callback rates of applicants. In fact, we observed that in comparison to men, the callback rates for women slightly declined. Our study suggests that driving 'gigs' might not be a substitute for traditional employment on resumes for low-skilled workers. We contribute a call to CSCW to investigate methods that help to understand why real-time ridesharing services do not substitute for traditional jobs in bridging employment gaps and solutions on how to overcome it. Finally, we reflect on our use of audit studies in the new digital era and present potential CSCW and HCI contributions using this method.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359258"}, {"primary_key": "2898020", "vector": [], "sparse_vector": [], "title": "Live Streaming as Co-Performance: Dynamics between Center and Periphery in Theatrical Engagement.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Live streaming is a highly participatory form of performance, involving various types of audience participation such as liking, commenting, and gifting. But how do streamers and audiences collaborate to deliver live streaming performances? We approach this question through an interview study with 30 spectators and eight streamers in China. Drawing from theatrical engagement research, we use the cogitative spatial concept of center-peripheral attention of the audience to analyze the complex interplay between streamers and spectators, where the former occupy the center and the latter the periphery. We then discuss the orchestration of the center and the periphery, where streamers and spectators coordinate their respective performances, as well as the interaction between the center and the periphery, where the center-periphery distinction blurs. Based on these findings, we discuss co-performance as a theatrical metaphor for understanding live streaming and audience engagement.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359166"}, {"primary_key": "2898021", "vector": [], "sparse_vector": [], "title": "Dropping the Baton?: Understanding Errors and Bottle<PERSON><PERSON> in a Crowdsourced Sensemaking Pipeline.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Crowdsourced sensemaking has shown great potential for enabling scalable analysis of complex data sets, from planning trips, to designing products, to solving crimes. Yet, most crowd sensemaking approaches still require expert intervention because of worker errors and bottlenecks that would otherwise harm the output quality. Mitigating these errors and bottlenecks would significantly reduce the burden on experts, yet little is known about the types of mistakes crowds make with sensemaking micro-tasks and how they propagate in the sensemaking loop. In this paper, we conduct a series of studies with 325 crowd workers using a crowd sensemaking pipeline to solve a fictional terrorist plot, focusing on understanding why errors and bottlenecks happen and how they propagate. We classify types of crowd errors and show how the amount and quality of input data influence worker performance. We conclude by suggesting design recommendations for integrated crowdsourcing systems and speculating how a complementary top-down path of the pipeline could refine crowd analyses.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359238"}, {"primary_key": "2898022", "vector": [], "sparse_vector": [], "title": "How Do People Change Their Technology Use in Protest?: Understanding.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Researchers and the media have become increasingly interested in protest users, or people who change (protest use) or stop (protest non-use) their use of a company's products because of the company's values and/or actions. Past work has extensively engaged with the phenomenon of technology non-use but has not focused on non-use (nor changed use) in the context of protest. With recent research highlighting the potential for protest users to exert leverage against technology companies, it is important for technology stakeholders to understand the prevalence of protest users, their motivations, and the specific tactics they currently use. In this paper, we report the results of two surveys (n = 463 and n = 398) of representative samples of American web users that examine if, how, and why people have engaged in protest use and protest non-use of the products of five major technology companies. We find that protest use and protest non-use are relatively common, with 30% of respondents in 2019 reporting they were protesting at least one major tech company. Furthermore, we identify that protest users' most common motivations were (1) concerns about business models that profit from user data and (2) privacy; and the most common tactics were (1) stopping use and (2) leveraging ad blockers. We also identify common challenges and roadblocks faced by active and potential protest users, which include (1) losing social connections and (2) the lack of alternative products. Our results highlight the growing importance of protest users in the technology ecosystem and the need for further social computing research into this phenomenon. We also provide concrete design implications for existing and future technologies to support or account for protest use and protest non-use.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359189"}, {"primary_key": "2898024", "vector": [], "sparse_vector": [], "title": "How Emotional and Contextual Annotations Involve in Sensemaking Processes of Foreign Language Social Media Posts.", "authors": ["Hajin Lim", "<PERSON>", "<PERSON>"], "summary": "The goal of this paper is to investigate how computational tools to annotate communication can support multilingual sense-making on social media. We conducted a field study of SenseTrans, a browser extension that uses sentiment analysis and named entity extraction techniques to annotate Facebook posts with emotional and contextual information. Interviews with 18 participants who used SenseTrans in their Facebook newsfeed for two weeks suggest that the annotations often supported sensemaking by providing additional information they could use to get a quick gist of the posts or to supplement their own interpretations. Participants varied in the extent to which they were motivated to evaluate the credibility of and form mental models of how the annotations were generated, which shaped how they utilized the annotations for sensemaking. Our findings demonstrate the value of designing to support cross-lingual communication and inform design implications for intelligent tools that support communication and sensemaking.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359171"}, {"primary_key": "2898025", "vector": [], "sparse_vector": [], "title": "The Effect of Expressive Biosignals on Empathy and Closeness for a Stigmatized Group Member.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We explore the potential for expressive biosignals, or displays of sensed physiological data as social cues, to influence perceptions of a member of a stigmatized group. In a between-subjects experiment (N=62) participants read a fictional interview with a drug addict in prison, and rated their empathy and closeness with the interviewee. Participants were randomly assigned to read either 1) the transcript of the interview by itself, 2) the transcript with a text description of the interviewee's heart rate, or 3) the transcript with a graph of the interviewee's heart rate. Results demonstrate that providing information about heart rate can increase empathy in terms of emotional perspective-taking. Additionally, visualizing the heart rate as a graph, as opposed to text, can increase closeness. We discuss the implications of these results and present suggestions for future directions.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359303"}, {"primary_key": "2898026", "vector": [], "sparse_vector": [], "title": "&quot;Giving a little &apos;ayyy, I feel ya&apos; to someone&apos;s personal post&quot;: Performing Support on Social Media.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Social media platforms offer people a variety of ways to interact, ranging from public broadcast posts, to comments on posts, to private messages, to paralinguistic interactions such as \"liking\" posts. In 2015, the commenting function \"replies\" was temporarily removed from Tumblr, providing a unique opportunity to study the deprivation of a standard social media feature. We administered a survey to investigate Tumblr users' perceptions and use of replies. Respondents reported that they used replies to simultaneously support others' performance and their own. Respondents compared replies to other digital interaction channels such as paralinguistic interactions, the sharing feature \"reblogs\", and \"direct messages\" (DMs), citing social considerations and norms around each. We used <PERSON><PERSON><PERSON>'s performance theory to draw insights on the perceived semi-public / semi-private space of replies, which enabled users to perform supportive actions that did not belong in their main blogging identity frontstage but that were not backstage either. We discuss the limitation of performance theory to describe a presentation to a limited but unknown audience, and we describe how replies enabled new frontstages such as the delicate ramp up to the performance of intimacy in DMs. We discuss implications for performing support and identity on social media with audiences that are perceived as limited but are unknown.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359179"}, {"primary_key": "2898027", "vector": [], "sparse_vector": [], "title": "Vicariously Experiencing it all Without Going Outside: A Study of Outdoor Livestreaming in China.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The livestreaming industry in China is gaining greater traction than its European and North American counterparts and has a profound impact on the stakeholders' online and offline lives. An emerging genre of livestreaming that has become increasingly popular in China is outdoor livestreaming. With outdoor livestreams, streamers broadcast outdoor activities, travel, or socialize with passersby in outdoor settings, often for 6 or more hours, and viewers watch such streams for hours each day. However, given that professionally produced content about travel and outdoor activities are not very popular, it is currently unknown what makes this category of livestreams so engaging and how these techniques can be applied to other content or genres. Thus, we conducted a mixed methods study consisting of a survey (N=287) and interviews (N = 20) to understand how viewers watch and engage with outdoor livestreams in China. The data revealed that outdoor livestreams encompass many categories of content, environments and passersby behaviors create challenges and uncertainty for viewers and streamers, and viewers watch livestreams for surprising lengths of time (e.g., sometimes more than 5 continuous hours). We also gained insights into how live commenting and virtual gifting encourage engagement. Lastly, we detail how the behaviors of dedicated fans and casual viewers differ and provide implications for the design of livestreaming services that support outdoor activities.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359127"}, {"primary_key": "2898028", "vector": [], "sparse_vector": [], "title": "Intersecting Imaginaries: Visions of Decentralized Autonomous Systems.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Sociotechnical imaginaries are futures that people envision might be possible and desirable. They have a real impact on how systems are designed and what values they have embedded in their design. This article examines imaginaries about autonomous systems, decentralized systems, and decentralized autonomous systems. Through a discussion of the literature on autonomous and decentralized systems and how these imaginaries play out in the blockchain community based on my qualitative research, I demonstrate how decentralized autonomous systems are related to imaginaries about the organization of and the future of work. I identify three framings of imaginaries about autonomous systems: (1) autonomous technology as physical objects, (2) as mathematical rules, and (3) as artificial mangers. I also identify two sometimes conflicting framings of imaginaries about distributed and decentralized technology: these technologies as a new form of production and as freedom from control. These imaginaries intersect in decentralized autonomous systems, and I examine what they can tell us about the design and governance of such technologies. Lastly, I suggest ways of using the concept of imaginaries in participatory design.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359312"}, {"primary_key": "2898029", "vector": [], "sparse_vector": [], "title": "Effects of Anonymity, Ephemerality, and System Routing on Cost in Social Question Asking.", "authors": ["Haiwei <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Online platforms provide new channels for people in need to seek help from friends and strangers. However, individuals often encounter psychological barriers that deter them from asking for help. For example, people might have different concerns about asking for help, including acknowledging incompetence, bothering others, and accruing social debt. These perceived social costs limit the potential benefits of help solicitations. In this study, we attempt to investigate whether anonymity (posting a question anonymously), ephemerality (allowing questions to be visible for only a short period), and system routing (having the system handle the question routing) could reduce social costs in a typical online help-seeking behavior-question asking. We built a platform to support these three features and conducted a controlled within-subjects experiment to test their effects on the social costs of posting questions. Results suggest that the presence of anonymity, ephemerality, and system routing reduce social costs. Further, we find that employing anonymity and system routing features did not lower the quality and quantity of answers to the questions in our system.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3361119"}, {"primary_key": "2898030", "vector": [], "sparse_vector": [], "title": "Part-Time Ride-Sharing: Recognizing the Context in which Drivers Ride-Share and its Impact on Platform Use.", "authors": ["Ning F. Ma", "<PERSON>"], "summary": "Ride-sharing companies have been reshaping the structure and practice of ride-hailing work. At the same time, studies have been showing mixed driver experiences on the platform while many of the drivers are working part-time. In this research, we seek to understand why drivers on this platform are working part-time, how this impacts their view of the platform, and what this means for more accurately evaluating the design of these platforms. To investigate this question, we focused on situating ride-sharing in the lives and constellation of gigs that drivers maintain. We collected 53 survey responses and conducted 10 semi-structured interviews with drivers to probe these questions. We found that the extent that drivers categorize themselves as part-time is less about the number of hours worked and more about how dependent they are on ride-sharing income. The level of this dependency seemed to heavily influence how they interacted with the platform and their attitudes towards difficulties faced. It seemed to us that in some ways that the design or functioning of the platform almost pushed users towards working part-time. We discuss the importance of taking these different types of workers and their situations into consideration when evaluating the design and usability of these platforms.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3361128"}, {"primary_key": "2898031", "vector": [], "sparse_vector": [], "title": "Makers and Quilters: Investigating Opportunities for Improving Gender-Imbalanced Maker Groups.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent efforts to diversify participation in STEM (Science, Technology, Engineering &amp; Math) activities through informal learning environments, such as hackathons and makerspaces, confirm a real desire for inclusion among potential female participants. However, understanding factors that may contribute to longer-term, sustainable diversification of such groups remains a challenge. In this paper, we present the results of a mixed-methods study of two microcosms of making: game development, and quilting. Our findings reveal parallel structures within these groups despite being highly skewed towards male or female participation, respectively. Our results shed light on attitudes, behaviours, and experiences indicating that similar desires for wider community support among other factors exist in both groups, but these needs are not satisfied in the STEM context. We conclude by discussing the implications of our findings as opportunities for rethinking how we design the environments that are meant to support design itself, considering the role of technology in these spaces, and prioritizing nurturing the development of the maker community beyond the maker space.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359131"}, {"primary_key": "2898033", "vector": [], "sparse_vector": [], "title": "Gelicit: A Cloud Platform for Distributed Gesture Elicitation Studies.", "authors": ["<PERSON>", "<PERSON>"], "summary": "A gesture elicitation study, as originally defined, consists of gathering a sample of participants in a room, instructing them to produce gestures they would use for a particular set of tasks, materialized through a representation called referent, and asking them to fill in a series of tests, questionnaires, and feedback forms. Until now, this procedure is conducted manually in a single, physical, and synchronous setup. To relax the constraints imposed by this manual procedure and to support stakeholders in defining and conducting such studies in multiple contexts of use, this paper presents Gelicit, a cloud computing platform that supports gesture elicitation studies distributed in time and space structured into six stages: (1) define a study: a designer defines a set of tasks with their referents for eliciting gestures and specifies an experimental protocol by parameterizing its settings; (2) conduct a study: any participant receiving the invitation to join the study conducts the experiment anywhere, anytime, anyhow, by eliciting gestures and filling forms; (3) classify gestures: an experimenter classifies elicited gestures according to selected criteria and a vocabulary; (4) measure gestures: an experimenter computes gesture measures, like agreement, frequency, to understand their configuration; (5) discuss gestures: a designer discusses resulting gestures with the participants to reach a consensus; (6) export gestures: the consensus set of gestures resulting from the discussion is exported to be used with a gesture recognizer. The paper discusses Gelicit advantages and limitations with respect to three main contributions: as a conceptual model for gesture management, as a method for distributed gesture elicitation based on this model, and as a cloud computing platform supporting this distributed elicitation. We illustrate <PERSON><PERSON><PERSON><PERSON> through a study for eliciting 2D gestures executing Internet of Things tasks on a smartphone.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3331148"}, {"primary_key": "2898035", "vector": [], "sparse_vector": [], "title": "How Data ScientistsWork Together With Domain Experts in Scientific Collaborations: To Find The Right Answer Or To Ask The Right Question?", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In recent years there has been an increasing trend in which data scientists and domain experts work together to tackle complex scientific questions. However, such collaborations often face challenges. In this paper, we aim to decipher this collaboration complexity through a semi-structured interview study with 22 interviewees from teams of bio-medical scientists collaborating with data scientists. In the analysis, we adopt the Olsons' four-dimensions framework proposed in Distance Matters to code interview transcripts. Our findings suggest that besides the glitches in the collaboration readiness, technology readiness, and coupling of work dimensions, the tensions that exist in the common ground building process influence the collaboration outcomes, and then persist in the actual collaboration process. In contrast to prior works' general account of building a high level of common ground, the breakdowns of content common ground together with the strengthen of process common ground in this process is more beneficial for scientific discovery. We discuss why that is and what the design suggestions are, and conclude the paper with future directions and limitations.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3361118"}, {"primary_key": "2898036", "vector": [], "sparse_vector": [], "title": "&quot;You Know What to Do&quot;: Proactive Detection of YouTube Videos Targeted by Coordinated Hate Attacks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Emiliano De Cristofaro", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Video sharing platforms like YouTube are increasingly targeted by aggression and hate attacks. Prior work has shown how these attacks often take place as a result of \"raids,\" i.e., organized efforts by ad-hoc mobs coordinating from third-party communities. Despite the increasing relevance of this phenomenon, however, online services often lack effective countermeasures to mitigate it. Unlike well-studied problems like spam and phishing, coordinated aggressive behavior both targets and is perpetrated by humans, making defense mechanisms that look for automated activity unsuitable. Therefore, the de-facto solution is to reactively rely on user reports and human moderation. In this paper, we propose an automated solution to identify YouTube videos that are likely to be targeted by coordinated harassers from fringe communities like 4chan. First, we characterize and model YouTube videos along several axes (metadata, audio transcripts, thumbnails) based on a ground truth dataset of videos that were targeted by raids. Then, we use an ensemble of classifiers to determine the likelihood that a video will be raided with very good results (AUC up to 94%). Overall, our work provides an important first step towards deploying proactive systems to detect and mitigate coordinated hate attacks on platforms like YouTube.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359309"}, {"primary_key": "2898037", "vector": [], "sparse_vector": [], "title": "Analysing and Demonstrating Tool-Supported Customizable Task Notations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "When task descriptions are precise they can be analysed to yield a variety of insights about interaction, such as the quantity of actions performed, the amount of information that must be perceived, and the cognitive workload involved. Task modelling notations and associated tools provide support for precise task description, but they generally provide a fixed set of constructs, which can limit their ability to model new and evolving application domains and technologies. This article describes challenges involved in using fixed notations for describing tasks. We use examples of recognized tasks analysis processes and their phases to show the need for customization of task notations, and through a series of illustrative examples, we demonstrate the benefits using our extensible task notation and tool (HAMSTERS-XL).", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3331154"}, {"primary_key": "2898038", "vector": [], "sparse_vector": [], "title": "Dark Patterns at Scale: Findings from a Crawl of 11K Shopping Websites.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Dark patterns are user interface design choices that benefit an online service by coercing, steering, or deceiving users into making unintended and potentially harmful decisions. We present automated techniques that enable experts to identify dark patterns on a large set of websites. Using these techniques, we study shopping websites, which often use dark patterns to influence users into making more purchases or disclosing more information than they would otherwise. Analyzing ~53K product pages from ~11K shopping websites, we discover 1,818 dark pattern instances, together representing 15 types and 7 broader categories. We examine these dark patterns for deceptive practices, and find 183 websites that engage in such practices. We also uncover 22 third-party entities that offer dark patterns as a turnkey solution. Finally, we develop a taxonomy of dark pattern characteristics that describes the underlying influence of the dark patterns and their potential harm on user decision-making. Based on our findings, we make recommendations for stakeholders including researchers and regulators to study, mitigate, and minimize the use of these patterns.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359183"}, {"primary_key": "2898039", "vector": [], "sparse_vector": [], "title": "Analyzing Wikipedia Deletion Debates with a Group Decision-Making Forecast Model.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this work we show that machine learning with natural language processing can accurately forecast the outcomes of group decision-making in online discussions. Specifically, we study Articles for Deletion, a Wikipedia forum for determining which content should be included on the site. Applying this model, we replicate several findings from prior work on the factors that predict debate outcomes; we then extend this prior work and present new avenues for study, particularly in the use of policy citation during discussion. Alongside these findings, we introduce a structured corpus and source code for analyzing over 400,000 deletion debates spanning Wikipedia's history, enabling future large-scale studies of group decision-making discourse", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359308"}, {"primary_key": "2898040", "vector": [], "sparse_vector": [], "title": "Information Materialities of Citizen Communication in the U.S. Congress.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this paper, we use a materiality lens to explore how information and communication technologies condition interaction between citizens and policymakers of the U.S. Congress. We work with ethnographic data - six months of observation in Washington D.C. and 48 interviews with staff in the House of Representatives. Customer relation management systems (CRMs) used by Congress are one of numerous technologies expected to enhance responsive communication between citizens and representatives. We find, instead, that these technologies promote the datafication of citizen information that configures and constrains how policymakers engage citizens as legitimate actors within the policy-making process. CRMs not only mediate communication between citizens and policymakers, they shape the idea of what communication between citizens and policymakers can be and how citizens are viewed in the eyes of policymakers and their staff. Thus, we extend our understanding of the ways in which material configurations of communication technologies influence not only how communication acts unfolds, but also how each partner conceives of and engages with the other. This has dramatic implications for the possibilities of digital communication channels to enhance, or uphold, the ideals of a representative democracy.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359149"}, {"primary_key": "2898041", "vector": [], "sparse_vector": [], "title": "Reliability and Inter-rater Reliability in Qualitative Research: Norms and Guidelines for CSCW and HCI Practice.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "What does reliability mean for building a grounded theory? What about when writing an auto-ethnography? When is it appropriate to use measures like inter-rater reliability (IRR)? Reliability is a familiar concept in traditional scientific practice, but how, and even whether to establish reliability in qualitative research is an oft-debated question. For researchers in highly interdisciplinary fields like computer-supported cooperative work (CSCW) and human-computer interaction (HCI), the question is particularly complex as collaborators bring diverse epistemologies and training to their research. In this article, we use two approaches to understand reliability in qualitative research. We first investigate and describe local norms in the CSCW and HCI literature, then we combine examples from these findings with guidelines from methods literature to help researchers answer questions like: \"should I calculate IRR?\" Drawing on a meta-analysis of a representative sample of CSCW and HCI papers from 2016-2018, we find that authors use a variety of approaches to communicate reliability; notably, IRR is rare, occurring in around 1/9 of qualitative papers. We reflect on current practices and propose guidelines for reporting on reliability in qualitative research using IRR as a central example of a form of agreement. The guidelines are designed to generate discussion and orient new CSCW and HCI scholars and reviewers to reliability in qualitative research.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359174"}, {"primary_key": "2898042", "vector": [], "sparse_vector": [], "title": "Designing with Gaze: Tama - a Gaze Activated Smart-Speaker.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Hideaki <PERSON>"], "summary": "Recent developments in gaze tracking present new opportunities for social computing. This paper presents a study of <PERSON><PERSON>, a gaze actuated smart speaker. <PERSON><PERSON> was designed taking advantage of research on gaze in conversation. Rather than being activated with a wake word (such as \"Ok Google\") <PERSON><PERSON> detects the gaze of a user, moving an articulated 'head' to achieve mutual gaze. We tested <PERSON><PERSON>'s use in a multi-party conversation task, with users successfully activating and receiving a response to over 371 queries (over 10 trials). When <PERSON><PERSON> worked well, there was no significant difference in length of interaction. However, interactions with <PERSON><PERSON> had a higher rate of repeated queries, causing longer interactions overall. Video analysis lets us explain the problems users had interacting with gaze. In the discussion, we describe implications for designing new gaze systems, using gaze both as input and output. We also discuss how the relationship to anthropomorphic design and taking advantage of learned skills of interaction. Finally, two paths for future work are proposed, one in the field of speech agents, and the second in using human gaze as an interaction modality more widely.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359278"}, {"primary_key": "2898043", "vector": [], "sparse_vector": [], "title": "Towards Digitization of Collaborative Savings Among Low-Income Groups.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Rotating Savings and Credit Association (ROSCA) is a mechanism of informal collaborative savings that is widely used across the globe. Despite its popularity and prevalence, it is not well-studied from HCI and CSCW perspectives. The global increase in mobile penetration has created opportunities to serve the unbanked using mobile-based Digital Financial Services (DFS) for greater financial inclusion but there have not been any DFS-based interventions around ROSCAs. In this paper, we report a qualitative study involving 80 individuals to understand the dynamics of ROSCAs and opportunities for their digitization in the Pakistani context. We also present a smartphone-based Digital ROSCA platform designed on top of a simulated mobile money system. The platform was designed to be inclusive towards low-literate users. We present qualitative findings of its evaluation with 15 users (3 individual ROSCA groups). We find that digitization has the potential to support and strengthen traditional ROSCAs by mitigating issues like record-keeping, delayed payments, collection, distribution, and safety of money. It also allows the creation of payment history for individuals that can be used to score their financial credibility.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274304"}, {"primary_key": "2898044", "vector": [], "sparse_vector": [], "title": "Collaborative Data Work Towards a Caring Democracy.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Researchers in human-centered computing have surfaced a feminist ethic of care in interaction with technologies, in data collection, and in data work. Drawing on two years of ethnographic fieldwork, we consider how democratic caring might be enacted and sustained through collaborative data work. We employ philosopher <PERSON>'s theory of caring democracy to structure our analysis of a resident-led initiative that uses data to organize and address issues of neglect and abandonment in their neighborhood. Adding to the CSCW literature on sociotechnical systems of care, we look particularly at Tronto's concept of caring democracy where caring needs and the ways in which they are met are an ongoing and inclusive process of assigning and reassigning caring responsibilities, characterized by both equality of voice and freedom from domination. This work develops grounded insight into the practice of democratic caring and how collaborative data work is relevant to this caring practice. We discuss opportunities and challenges for a data-supported caring democracy and address how caring democracy technologies are different from other modern civic technology practices. We conclude with a call to researchers to identify and enact democratic caring experiments in the small.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359144"}, {"primary_key": "2898045", "vector": [], "sparse_vector": [], "title": "Search Media and Elections: A Longitudinal Investigation of Political Search Results.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Concern about algorithmically-curated content and its impact on democracy is reaching a fever pitch worldwide. But relative to the role of social media in electoral processes, the role of search results has received less public attention. We develop a theoretical conceptualization of search results as a form of media-search media-and analyze search media in the context of political partisanship in the six months leading up to the 2018 U.S. midterm elections. Our empirical analyses use a total of over 4 million URLs, scraped daily from Google search queries for all candidates running for federal office in the United States in 2018. In our first set of analyses we characterize the nature of search media from the data collected in terms of the types of URLs present and the stability of search results over time. In our second, we annotate URLs' top-level domains with existing measures of political partisanship, examining trends by incumbency, election outcome, and other election characteristics. Among other findings, we note that partisanship trends in search media are largely similar for content about candidates from the two major political parties, whereas there are substantial differences in search media for incumbent versus challenger candidates. This work suggests that longitudinal, systematic audits of search media can reflect real-world political trends. We conclude with implications for web search designers and consumers of political content online.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359231"}, {"primary_key": "2898046", "vector": [], "sparse_vector": [], "title": "Assembling the Case: Citizens&apos; Strategies for Exercising Authority and Personal Autonomy in Social Welfare.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This study examines caseworkers' and citizens' interactions when assembling resource development applications for citizens with serious health and personal issues. As with other types of welfare schemes, the application serves as a mechanism of both support and control. From our study, we illustrate how an increased reliance on data is transforming the citizen-caseworker interaction in social welfare. We characterize this transformation as 'datafication': a phenomenon where the increased reliance on data for decision-support across contexts of data production makes it challenging for individual citizens to contest or correct data-born accounts of their situation. Our contribution is two-fold: first, we empirically characterize the citizen-caseworker interaction in the application process. Second, we discuss how citizens' private resourcing complements the formal application process and provides them with strategies to give authority to their case and exercise personal autonomy. The private resourcing practices we observed show how integrating supplementary accounts from citizens into the systems that caseworkers rely on could make citizens' experiences and social context legible. This in turn has policy and technology design implications as public services increasingly introduce data-driven modes of case management.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3361125"}, {"primary_key": "2898047", "vector": [], "sparse_vector": [], "title": "The iMPAcT Tool for Android Testing.", "authors": ["Ines Coimbra Morgado", "Ana C. R. Paiva"], "summary": "This paper presents iMPAcT tool that tests recurring common behavior on Android mobile applications. The process followed combines exploration, reverse engineering and testing to automatically test Android mobile applications. The tool explores automatically the App by firing UI events. After each event fired, the tool checks if there are UI patterns present using a reverse engineering process. If a UI pattern is present, the tool runs the corresponding testing strategy (Test Pattern). During reverse engineering the tool uses a catalog of UI Patterns which describes recurring behavior (UI Patterns) to test and the corresponding test strategies (Test Patterns). This catalog may be extended in the future as needed (e.g., to deal with new interaction trends). This paper describes the implementation details of the iMPAcT tool, the catalog of patterns used, the outputs produced by the tool and the results of experiments performed in order to evaluate the overall testing approach. These results show that the overall testing approach is capable of finding failures on existing Android mobile applications.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3300963"}, {"primary_key": "2898048", "vector": [], "sparse_vector": [], "title": "Designing for Real-Time Groupware Systems to Support Complex Scientific Data Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Scientific Workflow Management Systems (SWfMSs) have become popular for accelerating the specification, execution, visualization, and monitoring of data-intensive scientific experiments. Unfortunately, to the best of our knowledge no existing SWfMSs directly support collaboration. Data is increasing in complexity, dimensionality, and volume, and the efficient analysis of data often goes beyond the realm of an individual and requires collaboration with multiple researchers from varying domains. In this paper, we propose a groupware system architecture for data analysis that in addition to supporting collaboration, also incorporates features from SWfMSs to support modern data analysis processes. As a proof of concept for the proposed architecture we developed SciWorCS - a groupware system for scientific data analysis. We present two real-world use-cases: collaborative software repository analysis and bioinformatics data analysis. The results of the experiments evaluating the proposed system are promising. Our bioinformatics user study demonstrates that SciWorCS can leverage real-world data analysis tasks by supporting real-time collaboration among users.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3331151"}, {"primary_key": "2898049", "vector": [], "sparse_vector": [], "title": "This Thing Called Fairness: Disciplinary Confusion Realizing a Value in Technology.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Richmond Y<PERSON>"], "summary": "The explosion in the use of software in important sociotechnical systems has renewed focus on the study of the way technical constructs reflect policies, norms, and human values. This effort requires the engagement of scholars and practitioners from many disciplines. And yet, these disciplines often conceptualize the operative values very differently while referring to them using the same vocabulary. The resulting conflation of ideas confuses discussions about values in technology at disciplinary boundaries. In the service of improving this situation, this paper examines the value of shared vocabularies, analytics, and other tools that facilitate conversations about values in light of these disciplinary specific conceptualizations, the role such tools play in furthering research and practice, outlines different conceptions of \"fairness\" deployed in discussions about computer systems, and provides an analytic tool for interdisciplinary discussions and collaborations around the concept of fairness. We use a case study of risk assessments in criminal justice applications to both motivate our effort--describing how conflation of different concepts under the banner of \"fairness\" led to unproductive confusion--and illustrate the value of the fairness analytic by demonstrating how the rigorous analysis it enables can assist in identifying key areas of theoretical, political, and practical misunderstanding or disagreement, and where desired support alignment or collaboration in the absence of consensus.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359221"}, {"primary_key": "2898050", "vector": [], "sparse_vector": [], "title": "Collaboration Drives Individual Productivity.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "How does the number of collaborators affect individual productivity? Results of prior research have been conflicting, with some studies reporting an increase in individual productivity as the number of collaborators grows, while other studies showing that the free-rider effect skews the effort invested by individuals, making larger groups less productive. The difference between these schools of thought is substantial: if a super-scaling effect exists, as suggested by former studies, then as groups grow, their productivity will increase even faster than their size, super-linearly improving their efficiency. We address this question by studying two planetary-scale collaborative systems: GitHub and Wikipedia. By analyzing the activity of over 2 million users on these platforms, we discover that the interplay between group size and productivity exhibits complex, previously-unobserved dynamics: the productivity of smaller groups scales super-linearly with group size, but saturates at larger sizes. This effect is not an artifact of the heterogeneity of productivity: the relation between group size and productivity holds at the individual level. People tend to do more when collaborating with more people. We propose a generative model of individual productivity that captures the non-linearity in collaboration effort. The proposed model is able to explain and predict group work dynamics in GitHub and Wikipedia by capturing their maximally informative behavioral features, and it paves the way for a principled, data-driven science of collaboration.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359176"}, {"primary_key": "2898051", "vector": [], "sparse_vector": [], "title": "All Talk: How Increasing Interpersonal Communication on Wikis May Not Enhance Productivity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Prior research suggests that facilitating easier communication in social computing systems will increase both interpersonal interactions as well as group productivity. This study tests these claims by examining the impact of a new communication feature called \"message walls\" that allows for faster and more intuitive interpersonal communication in wikis. Using panel data from a sample of 275 wiki communities that migrated to message walls and a method inspired by regression discontinuity designs, we analyze these transitions and estimate the impact of the system's introduction. Although the adoption of message walls was associated with increased communication among all editors and newcomers, it had little effect on productivity, and was further associated with a decrease in article contributions from new editors. Our results imply that design changes that make communication easier in a social computing system may not translate to increased participation along other dimensions.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359203"}, {"primary_key": "2898052", "vector": [], "sparse_vector": [], "title": "A Review of Research on Participation in Democratic Decision-Making Presented at SIGCHI Conferences. Toward an Improved Trading Zone Between Political Science and HCI.", "authors": ["<PERSON><PERSON>"], "summary": "We present a review of 80 papers representing efforts to support participation in democratic decision-making mostly related to local or national governments. The papers were published in leading human--computer interaction (SIGCHI conferences) venues. Most of this literature represents attempts to support assembly-oriented participation, wherein decisions are made through discussion, although referendum-type participation, involving decision-making based on voting, has gained attention too. Primarily, those papers addressing agenda-setting have examined organization-led forms, in which the agenda is controlled by those issuing the call for participation. Accordingly, the authors call for more research into support for representative models and participant-driven agenda-setting. Furthermore, the literature review pinpoints areas wherein further interdisciplinary engagement may be expected to improve research quality: in political science, HCI-informed methods and new ways of using physical input in participation merit more research, while, from the HCI side, cultivating closer relationships with political science concepts such as democratic innovations and calculus of voting could encourage reconsideration of the research foci. These observations speak to the benefits of a new research agenda for human--computer interaction research, involving different forms of participation, most importantly to address lack of engagement under the representative model of participation. Furthermore, in light of these findings, the paper discusses what type of interdisciplinary research is viable in the HCI field today and how political science and HCI scholars could usefully collaborate.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359241"}, {"primary_key": "2898053", "vector": [], "sparse_vector": [], "title": "(Re)Design to Mitigate Political Polarization: Reflecting Habermas&apos; ideal communication space in the United States of America and Finland.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Social Media platforms are increasingly being used for political activities and communication, and research suggests that social media design and use is contributing to the polarization of the public sphere. This study draws on <PERSON><PERSON><PERSON>' ideals concerning deliberative democracy to explore if novel interface designs that diversify information sources through content recommendation, can decrease polarization. Through a design-probe interview approach and insights generated from 19 political and citizen experts in Finland and the United States, we found that our deliberative design can lead to depolarization, while creating additional complexity through which users question content and information. We discuss the need to move beyond naive content recommendation, and user interface level changes, in order to work towards a depolarized public sphere.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359243"}, {"primary_key": "2898054", "vector": [], "sparse_vector": [], "title": "Provider Perspectives on Integrating Sensor-Captured Patient-Generated Data in Mental Health Care.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Alyson K<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The increasing ubiquity of health sensing technology holds promise to enable patients and health care providers to make more informed decisions based on continuously-captured data. The use of sensor-captured patient-generated data (sPGD) has been gaining greater prominence in the assessment of physical health, but we have little understanding of the role that sPGD can play in mental health. To better understand the use of sPGD in mental health, we interviewed care providers in an intensive treatment program (ITP) for veterans with post-traumatic stress disorder. In this program, patients were given Fitbits for their own voluntary use. Providers identified a number of potential benefits from patients' Fitbit use, such as patient empowerment and opportunities to reinforce therapeutic progress through collaborative data review and interpretation. However, despite the promise of sensor data as offering an \"objective\" view into patients' health behavior and symptoms, the relationships between sPGD and therapeutic progress are often ambiguous. Given substantial subjectivity involved in interpreting data from commercial wearables in the context of mental health treatment, providers emphasized potential risks to their patients and were uncertain how to adjust their practice to effectively guide collaborative use of the FitBit and its sPGD. We discuss the implications of these findings for designing systems to leverage sPGD in mental health care.?", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359217"}, {"primary_key": "2898055", "vector": [], "sparse_vector": [], "title": "&apos;Is the Time Right Now?&apos;: Reconciling Sociotemporal Disorder in Distributed Team Work.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Temporal coordination endures as a central topic in computer-supported cooperative work (CSCW) because information systems still struggle to adequately support varying representations of time in the context of collaborations that areboth temporally and geographically dispersed. Moreover, the adaptive practices of these broadly dispersed groups are still not well understood. We ask:How do globally distributed teams temporally coordinate to accomplish their work? We examine an extreme case of online temporal coordination: high-tempo information curation about the urgent humanitarian crisis following the 2017 Hurricane Maria landfall in Puerto Rico. Our analysis of synchronous chat transcripts and data artifacts produced by The Standby Task Force reveals how this digital humanitarian group establishes temporal coordination through different shared understandings of time relative to the crisis, the globally distributed work, and the collaborative information technologies. We make four contributions from our analysis: First, we developed a pluritemporal analytical framework that describes different forms of socially constructed time and disambiguates their meaning in talk. Next, we present empirical evidence of how this distributed team establishes shared temporal orders to collectively orient their work in time. Then, we describe how they reconcile multiple and conflicting instances of sociotemporal \"disorder'' to enable productive work. Last, we reflect on the design implications for collaborative information systems informed by this work.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359200"}, {"primary_key": "2898056", "vector": [], "sparse_vector": [], "title": "Implications of Grassroots Sustainable Agriculture Community Values on the Design of Information Systems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Information system designers embed values into the systems they design, even if unwittingly. However, the values embedded in many information systems clash with values held by many sustainability communities. This research focuses on two grassroots sustainable agriculture communities, which are seeking to develop a food infrastructure that is under their own control, and thereby more resilient to disruptions across the globe. This paper presents a five-year ethnographic study of these two communities, maps out the values of members of these communities, and explores the implications of their values on the information systems that members use and that could be developed to support them in the future. By doing so, we hope to influence the design of future information systems to align more closely with the values of these stakeholders, and through these stakeholders to move toward a food system that supports food security and global sustainability.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359136"}, {"primary_key": "2898057", "vector": [], "sparse_vector": [], "title": "Including the Voice of Care Recipients in Community Health Feedback Loops in Rural Kenya.", "authors": ["<PERSON>", "<PERSON>", "Mercy Amulele", "<PERSON>", "<PERSON>"], "summary": "Community health programs in low-resource settings (like rural Kenya) aim to provide essential health services to vulnerable populations. However, to date, there has been limited research that explores the design of mechanisms that enable care recipients to provide feedback regarding their satisfaction with the services they receive. Such feedback has the potential to increase the motivation of community health workers (CHWs), enhance training procedures, detect fraudulent behavior, and inform key performance indicators for health programs. Our paper explores the design and deployment of a USSD-based system that allows anyone who possesses a basic mobile phone to provide feedback regarding the health services and quality of care they received from a CHW or during a hospital visit. Our system was designed through iterative fieldwork in rural Kenya that engaged with multiple stakeholder groups, including care recipients, CHWs, and high-level decision makers. After designing and testing the system, we deployed it for seven weeks in Siaya, Kenya, collecting both quantitative system usage data and qualitative data from six focus groups with 42 participants. Findings from our deployment show that 168 care recipients engaged with the system, submitting 495 reports via USSD. We discuss the broader factors impacting deployment, including the feasibility of USSD, actionability of feedback, scalability, and sustainability. Taken together, our findings suggest that USSD is a promising approach for enabling care recipients to submit feedback in a way that balances privacy, equity, and sustainability.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359173"}, {"primary_key": "2898058", "vector": [], "sparse_vector": [], "title": "Towards Successful Knowledge Integration in Online Collaboration: An Experiment on the Role of Meta-Knowledge.", "authors": ["<PERSON><PERSON>", "Nikol Rummel"], "summary": "Successful knowledge integration, that is, systematic synthesis of unshared information, is key to suc-cess, but at the same time a challenging venture for teams with distributed knowledge collaborating online. For example, teams with heterogeneous knowledge often have only vague or even wrong ideas about who knows what. This situation is further complicated if the collaboration partners do not know each other and merely communicate online. Previous research has found meta-knowledge, that is, knowledge about one's own and the partner's knowledge areas, to be a promising but not yet sufficient-ly investigated approach to promote knowledge integration. With our experimental study we aimed to address this desideratum of research on the role of meta-knowledge in net-based collaborations. We \"simulated\" a chat-based collaboration between partners with heterogeneous knowledge by assigning specific information to students collaborating in dyads on a Hidden Profile task. To arrive at the correct joint solution for this task, collaborating partners had to pool their shared, but more importantly their unshared information. We compared two conditions: In the experimental condition meta-knowledge was promoted by providing the collaboration partners with self-presentations of each other's roles, which pointed to their unique fields of knowledge, while participants in the control condition did not receive this information. Results suggest a positive impact of the meta-knowledge manipulation on two key factors of collaboration: knowledge integration and construction of a transactive memory system (TMS).", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359133"}, {"primary_key": "2898059", "vector": [], "sparse_vector": [], "title": "Understanding Users Information Needs and Collaborative Sensemaking of Microbiome Data.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Oded Nov", "<PERSON>"], "summary": "Recent years are seeing a sharp increase in the availability of personal omic (e.g. genomes, microbiomes) data to non-experts through direct-to-consumer testing kits. While the scientific understanding of human -omic information is evolving, the interpretation of the data may impact well-being of users and relevant others, and therefore poses challenges and opportunities for CSCW research. We identify the information, interaction, and sense-making needs of microbiomic data users, within the broader context of social omics - the sharing and collaborative engagement with data and interpretation. Analyzing users' discussions on Reddit's r/HumanMicrobiome, we identified seven user needs for microbiome data: reviewing an annotated report, comparing microbiome data, tracking changes, receiving personalized actionable information, curating and securing information, documenting and sharing self experiments, and enhancing the communication between patients and health-care providers. We highlight the ways in which users interact with each other to collaboratively make sense of the data. We conclude with design implications, including tools for better communication with care providers, and for symptom-centered sharing and discussion.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274470"}, {"primary_key": "2898060", "vector": [], "sparse_vector": [], "title": "Pragmatic Tool vs. Relational Hindrance: Exploring Why Some Social Media Users Avoid Privacy Features.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Social media privacy features can act as a mechanism for regulating interpersonal relationships, but why do some people not use these features? Through an interview study of 56 social media users, we found two high-level perspectives towards social media and privacy that affected attitudes towards and usage of privacy features. Some users took a pragmatic approach to using social media and felt comfortable using various privacy features as a tool to manage their social relationships (e.g., avoiding bothersome posts, not feeling compelled to interact). However, there were also users who viewed taking such privacy actions as a relational hindrance and were concerned how using certain features to meet their own needs would harm their relationships with others. Through a subsequent survey (N=320), we reveal how these two perspectives impact user behavior across four social media platforms (Facebook, Instagram, LinkedIn, Twitter). Users who viewed social media as a pragmatic tool indeed used privacy features more. On the other hand, users who focused on how privacy can serve as a relational hindrance avoided using these features and, instead, prioritized social engagement and took a more indirect approach to protecting their privacy. Furthermore, the results show how these perspectives vary by individual rather than by privacy feature. These findings demonstrate the need to consider different perspectives towards social media and privacy when trying to understand and design for user behavior.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359212"}, {"primary_key": "2898061", "vector": [], "sparse_vector": [], "title": "A Slow Algorithm Improves Users&apos; Assessments of the Algorithm&apos;s Accuracy.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "With computational algorithms making an increasing number of deeply consequential, and often problematic judgments on our behalf, there is a growing interest in slowing down technology to encourage users to reflect on judgments made by algorithms. Prior work in slow technology has established slowness as an agent of reflection and serendipity; however, it has been unclear whether this waiting time actually helps users gain useful insight or any other benefits as they make judgments using an algorithm. To this end, we conducted a series of online and in-person between-subject user studies in which we isolate the impact of an algorithm's speed on how users incorporate the algorithm's advice when making judgments in the context of simple visual recognition tasks. We find that our participants followed good quality algorithms more and bad quality algorithms somewhat less if the response time of the algorithm is slower. Furthermore, qualitative analysis of the in-person study interviews reveals that the waiting was not time wasted, but was often used to reflect on the task and the estimation process of themselves and the algorithm, and to compare and reevaluate the two processes. Based on these findings, we outline design implications of future algorithmic systems.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359204"}, {"primary_key": "2898062", "vector": [], "sparse_vector": [], "title": "Gesto: Mapping UI Events to Gestures and Voice Commands.", "authors": ["<PERSON>", "<PERSON><PERSON>on Ki", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Gesto is a system that enables task automation for Android apps using gestures and voice commands. Using Gesto, a user can record a UI action sequence for an app, choose a gesture or a voice command to activate the UI action sequence, and later trigger the UI action sequence by the corresponding gesture/voice command. Gesto enables this for existing Android apps without requiring their source code or any help from their developers. In order to make such capability possible, Gesto combines bytecode instrumentation and UI action record-and-replay. To show the applicability of Gesto, we develop four use cases using real apps downloaded from Google Play-Bing, Yelp, AVG Cleaner, and Spotify. For each of these apps, we map a gesture or a voice command to a sequence of UI actions. According to our measurement, Gesto incurs modest overhead for these apps in terms of memory usage, energy usage, and code size increase. We evaluate our instrumentation capability and overhead using 1,000 popular apps downloaded from Google Play. Our result shows that Gesto is able to instrument 94.9% of the apps without any significant overhead. In addition, since our prototype currently supports 6 main UI elements of Android, we evaluate our coverage and measure what percentage of UI element uses we can cover. Our result shows that our 6 UI elements can cover 96.4% of all statically-declared UI element uses in the 1,000 Google Play apps.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3300964"}, {"primary_key": "2898063", "vector": [], "sparse_vector": [], "title": "&quot;I feel like only half a man&quot;: Online Forums as a Resource for Finding a &quot;New Normal&quot; for Men Experiencing Fertility Issues.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Infertility can place a significant burden on couples and individuals when trying to conceive. Approximately 20-30% of all cases of infertility are due to male-related factors. Whatever the cause of difficulty in conceiving, little is known about how men find support when dealing with fertility issues, or when or how online resources are being used. This paper reports on a qualitative study of anonymous online posts (N=603) from forums related to fertility that are used by men. We analysed this data using thematic analysis to understand how men are using online forums as a resource when experiencing fertility issues. We found that online forums play a valued role in facilitating connections between men experiencing an often stigmatised condition. These forums offer men accessible and private spaces which allow for more open discussion, helping them to make sense of their situation. We discuss our findings in relation to <PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>'s model of finding a \"new normal\" and present our elaborated model of finding a \"new normal\" in the context of experiencing fertility problems.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359184"}, {"primary_key": "2898064", "vector": [], "sparse_vector": [], "title": "Exploring Indicators of Digital Self-Harm with Eating Disorder Patients: A Case Study.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Digital self-harm encompasses a variety of activities, including the use of social media to facilitate or amplify mental illness-related behaviors. To understand the extent of these behaviors and their impacts, we conducted an in-depth case study with three patients who are in recovery from an eating disorder. We collected survey data, conducted interviews reflecting back to their technology use during their active disease state, and reviewed up to 18 months of their social media data leading up to the start of their initial point of recovery. Through the triangulation of this data, we explore the role of social media and social technologies in relation to their eating disorder. By utilizing this methodology, we were able to provide a contextually rich and nuanced lens for exploring the impacts of digital self-harm on this group of patients. We found that patients acknowledged that the eating disordered content on social media had a negative impact on their health, often contributing to a worsening of the physical manifestations of their disorder. Conversely, while they actively consumed this content, our participants did not produce online content related to eating disordered activities or behaviors. Finally, we discuss the patterns within their social media data and how platform designers and operators could use these findings in the future through design considerations for future platform-based interventions.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359186"}, {"primary_key": "2898065", "vector": [], "sparse_vector": [], "title": "Cross-Cultural Differences in the Use of Online Mental Health Support Forums.", "authors": ["Sachin <PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Online mental health forums facilitate supportive relationships between peers that transcend national and cultural boundaries. While past work in medical anthropology indicates a central role of cultural identity in how individuals frame their mental well-being and distress, little research has been done to investigate the role of culture in seeking and providing mental health support on online forums. Using data from two mental health forums, we analyze cross-cultural differences in mental health expression between people from different countries. We characterize these differences along three dimensions--identity, language use, and support behavior. Through comparing usage of the platform by individuals from three Asian countries with their counterparts from primarily Western countries, we find that individuals from these less-represented countries mention their own country more often when expressing distress, use fewer clinical language terms, and are more likely to provide support to people from the same country as them, as expected from past work on mental health in these countries. Contrary to past work, however, we find that the use of clinical mental health language is not affected over time by interacting with others in an international forum. While these findings are useful for understanding the role of culture in mental health support, they also have practical design implications for online forums. We find that the three dimensions of cultural differences we analyze are correlated with receiving effective support, and make design recommendations that can improve quality of support for the people in the minority on these forums.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359169"}, {"primary_key": "2898066", "vector": [], "sparse_vector": [], "title": "My Own Private Nightlife: Understanding Youth Personal Spaces from Crowdsourced Video.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON>"], "summary": "Private nightlife environments of young people are likely characterized by their physical attributes, particular ambiance, and activities, but relatively little is known about it from social media studies. For instance, recent work has documented ambiance and physical characteristics of homes using pictures from Airbnb, but questions remain on whether this kind of curated data reliably represents everyday life situations. To describe the physical and ambiance features of homes of youth using manual annotations and machine-extracted features, we used a unique dataset of 301 crowdsourced videos of home environments recorded in-situ by young people on weekend nights. Agreement among five independent annotators was high for most studied variables. Results of the annotation task revealed various patterns of youth home spaces, such as the type of room attended (e.g., living room and bedroom), the number and gender of friends present, and the type of ongoing activities (e.g., watching TV alone; or drinking, chatting and eating in the presence of others.) Then, object and scene visual features of places, extracted via deep learning, were found to correlate with ambiances, while sound features did not. Finally, the results of a regression task for inferring ambiances from those features showed that six of the ambiance categories can be inferred with R 2 in the [0.21, 0.69] range. Our work is novel with regard to the type of data (crowdsourced videos of real homes of young people) and the analytical design (combined use of manual annotation and deep learning to identify relevant cues), and contributes to the understanding of home environments represented through digital media.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359291"}, {"primary_key": "2898067", "vector": [], "sparse_vector": [], "title": "Chats Over Spats: Exploring Social Media Use for Mentorship in Youth Development Programs.", "authors": ["<PERSON>"], "summary": "This paper presents a unique finding from a larger research project exploring social media use for social support among youth development program participants in Lafayette, IN. Through participant observation and semi-structured interviews conducted with Boys and Girls Club members ages 9-15, youth revealed varied uses of social media to sustain unexpected cross-age friendships. These friendships were perceived as integral sources of social support, and members described them to be some of the most significant in their lives at the time of their interviews. This finding raises important questions about the significant role social media may play in the maintenance of such cross-age relationships and mentorships, considering the absence of consistent face-to-face contact during the school day. Ultimately, participation in youth development programs in combination with social media use fosters these mentorships and encourages maturity and stability among these youth, which validates these unique friendships as important sources of social support. This finding offers implications for integrating digital and social media into youth development spaces, as well as for informing social and digital media designers of this particular use case.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359329"}, {"primary_key": "2898068", "vector": [], "sparse_vector": [], "title": "&quot;Am I Never Going to Be Free of All This Crap?&quot;: Upsetting Encounters with Algorithmically Curated Content About Ex-Partners.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Every day on social media, people see streams of content curated by algorithms that leverage their relationships, preferences, and identities. However, algorithms can oversimplify the complexity of people's social contexts. Consequently, algorithms can present content to people in ways that are insensitive to their circumstances. Through 19 in-depth interviews, our empirical study examines instances of contextually insensitive content through the lens of people's upsetting encounters with content about their ex-romantic partners on Facebook. We characterize the encounters our participants had with content about their exes, including where on Facebook it occurred, the types of social connections involved in the content, and participants' perceptions of why the content appeared. Based on our findings, we describe the \"social periphery\"---the complex social networks and data that enable inferred connections around otherwise explicit relationships---and discuss the design challenges that the periphery presents designers.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359172"}, {"primary_key": "2898069", "vector": [], "sparse_vector": [], "title": "&quot;Phantom Friend&quot; or &quot;Just a Box with Information&quot;: Personification and Ontological Categorization of Smart Speaker-based Voice Assistants by Older Adults.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "As voice-based conversational agents such as Amazon Alexa and Google Assistant move into our homes, researchers have studied the corresponding privacy implications, embeddedness in these complex social environments, and use by specific user groups. Yet it is unknown how users categorize these devices: are they thought of as just another object, like a toaster? As a social companion? Though past work hints to human-like attributes that are ported onto these devices, the anthropomorphization of voice assistants has not been studied in depth. Through a study deploying Amazon Echo Dot Devices in the homes of older adults, we provide a preliminary assessment of how individuals 1) perceive having social interactions with the voice agent, and 2) ontologically categorize the voice assistants. Our discussion contributes to an understanding of how well-developed theories of anthropomorphism apply to voice assistants, such as how the socioemotional context of the user (e.g., loneliness) drives increased anthropomorphism. We conclude with recommendations for designing voice assistants with the ontological category in mind, as well as implications for the design of technologies for social companionship for older adults.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359316"}, {"primary_key": "2898070", "vector": [], "sparse_vector": [], "title": "&quot;I simply watched where she was looking at&quot;: Coordination in Short-term Synchronous Cooperative Mixed Reality.", "authors": ["<PERSON>"], "summary": "Mixed reality (MR) cooperation scenarios are more and more interesting for business and research as powerful wearable devices like head mounted displays (HMD) become commercially available. A lot of work focuses on remote MR cooperation settings like remote maintenance support and co-located scenarios in which participants cooperate over a longer period time. Despite this, MR also has great potential for real-time co-located cooperation support with the need of short-term decisions and interactions. However, little is known on how this support can be provided. To bridge this gap, we conducted an experiment using a MR visual search task performed by dyads. Based on related work, visual search was chosen to represent typical challenges of short-term cooperative MR tasks. The aim of the experiment was to explore how the participants coordinate their searches and how this influences their performance in a task. We found that participants mainly used embodied and verbal cues to coordinate their searches (rather than virtual cues provided by the HMD) and that less communication worked significantly better, which is in (partial) contrast to existing findings. We discuss potential reasons for and impacts of these findings.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3361127"}, {"primary_key": "2898071", "vector": [], "sparse_vector": [], "title": "Infrastructuring Food Democracy: The Formation of a Local Food Hub in the Context of Socio-Economic Deprivation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper discusses what infrastructuring in participatory design can contribute to processes of food system democratisation. It presents almost two years of engagement with a community-based organisation in a socio-economically deprived neighbourhood in England with the aim of developing a local food hub. It documents how the collaborative work shifted from setting up an infrastructure to ongoing infrastructuring of an enabling environment to grow and sustain social innovation. While the former focused on a technological platform and a business to deliver inclusivity, customer experience, and marketing, the latter focuses on building lasting relationships on three levels: strengthening ties within the community-based organisation, offering social spaces to connect with the local community, and with other organisations by forming coalitions with mutual benefits. We contribute three design opportunities: First, in order to infrastructure the formation of publics around food initiatives, we need to design for relationship building. Second, we point to design implications for ethical aspirations, participation, and system transformation towards a food democracy. Finally, we reflect on the sustainability of infrastructuring and the role of designers to work towards independent infrastructures.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359159"}, {"primary_key": "2898072", "vector": [], "sparse_vector": [], "title": "The Signals that Potential Contributors Look for When Choosing Open-source Projects.", "authors": ["Huilian <PERSON>", "Yucen <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While open-source software has become ubiquitous, its sustainability is in question: without a constant supply of contributor effort, open-source projects are at risk. While prior work has extensively studied the motivations of open-source contributors in general, relatively little is known about how people choose which project to contribute to, beyond personal interest. This question is especially relevant in transparent social coding environments like GitHub, where visible cues on personal profile and repository pages, known as signals, are known to impact impression formation and decision making. In this paper, we report on a mixed-methods empirical study of the signals that influence the contributors' decision to join a GitHub project. We first interviewed 15 GitHub contributors about their project evaluation processes and identified the important signals they used, including the structure of the README and the amount of recent activity. Then, we proceeded quantitatively to test out the impact of each signal based on the data of 9,977 GitHub projects. We reveal that many important pieces of information lack easily observable signals, and that some signals may be both attractive and unattractive. Our findings have direct implications for open-source maintainers and the design of social coding environments, e.g., features to be added to facilitate better project searching experience.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359224"}, {"primary_key": "2898074", "vector": [], "sparse_vector": [], "title": "Making a &quot;Pro&quot;: &apos;Professionalism&apos; after Platforms in Beauty-work.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Beauty and Wellness work in India happen largely in the informal sector and are heavily gendered forms of work. The arrival of app-based on-demand platforms that aggregate beauticians and spa therapists to provide at-home services in urban India have aimed to create new efficiencies in this work, providing both the service providers and users of these with new means of transacting. This paper presents findings from an ethnographic study of an app-based platform that provides beauty and wellness services. We interviewed stakeholders including platform company employees, customers, beauty-trainers as well as platform-workers. Our results show the ways in which technology is creating new expectations around professionalism and training, which intersect with the ways workspaces and provider-receiver relationships are articulated around wellness work in urban India. We argue that this case offers an insightful example into the future of work - specifically platform work's attempts to create entrepreneurial subjects, and its tensions with the realities of gender, class and caste as they relate to care labor in India.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359277"}, {"primary_key": "2898075", "vector": [], "sparse_vector": [], "title": "Efficient, but Effective?: Volunteer Engagement in Short-term Virtual Citizen Science Projects.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Virtual citizen science (VCS) projects have proven to be a highly effective method to analyse large quantities of data for scientific research purposes. Yet if these projects are to achieve their goals, they must attract and maintain the interest of sufficient numbers of active, dedicated volunteers. Although CSCW and HCI research has typically focussed on designing platforms to support long-term engagement, in recent years a new project format has been trialled -- using short-term crowdsourcing activities lasting as little as 48 hours. In this paper, we explore two short-term projects to understand how they influence participant engagement in the task and discussion elements of VCS. We calculate descriptive statistics to characterise project participants. Additionally, using calculation of correlation coefficients and hypothesis testing, we identify factors influencing volunteer task engagement and the effect this has on project outcomes. Our findings contribute to the understanding of volunteer engagement in VCS.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359279"}, {"primary_key": "2898076", "vector": [], "sparse_vector": [], "title": "Hashtag Burnout? A Control Experiment Investigating How Political Hashtags Shape Reactions to News Content.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Both hashtag activists and news organizations assume that trending political hashtags effectively capture the nowness of social issues that people care about [20]. In fact, news organizations with growing social media presence increasingly capitalize the use of political hashtags in article headlines and social media news post - a practice aimed to generate new readership through lightweight news consumption of content by linking a particular story to a broader topic [28]. However, response to political hashtags can be complicated as demonstrated with the events surrounding #MeToo and #BlackLivesMatter. In fact, the semantic simplicity of political hashtags often belies the complexities around the question of who gets to participate [71], what intersectional identities are included or excluded from the hashtag [45], as well as how the meaning of the hashtag expands and drifts [10] depending on the context through which it is expressed. Overtime, reports show increasing backlash [70, 73, 74] and polarization [21, 52, 66, 67, 70] against key issues embodied by political hashtags. In this vein, we assume that political hashtags affect how people make sense of and engage with media content. However, we do not know how the presence of political hashtags -signaling that a news story is related to a current social issue - influences the assumptions potential readers make about the social content of an article. In this work we conducted a randomized control experiment to examine how the presence of political hashtags (particularly the most prevalently used #MeToo and #BlackLivesMatter) in social media news posts shape reactions across a general audience (n=1979). Our findings show that compared to the control group, people shown news posts with political hashtags perceive the news topic as less socially important and are less motivated to know more about social issues related to the post. People also find the news more partisan and controversial when hashtags are included. In fact, negative perception associated with political hashtags (partisan bias &amp; topic controversy) mediates people's motivation to further engage with the news content). High-intensity Facebook users and politically moderate participants perceive news with political hashtags as more partisan compared to posts excluding hashtags. There are also significant differences in discourse patterns between the hashtag and control groups around how politically moderate respondents engage with the news content in their comments.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359299"}, {"primary_key": "2898077", "vector": [], "sparse_vector": [], "title": "How I Learned What a Domain Was.", "authors": ["<PERSON>"], "summary": "This paper re-traverses the author's investigations across several years as he sought to pin-down the meaning of the in vivo category 'domain'. The paper is a methodological reflection on the grounded theory approach to concept development, with a focus on the technical terms: in vivo category, iteration on the code, and sensitizing category. It is also a substantive theoretical contribution, elaborating the concept of a domain in computing, data and information science, and how it has long served as an organizing principle for developing computational systems. Four tricks of the trade for studying the 'logic of domains' are offered as sensitizing concepts to aid future investigations.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359140"}, {"primary_key": "2898078", "vector": [], "sparse_vector": [], "title": "Capturing Movement Decomposition to Support Learning and Teaching in Contemporary Dance.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Our goal is to understand how dancers learn complex dance phrases. We ran three workshops where dancers learned dance fragments from videos. In workshop 1, we analyzed how dancers structure their learning strategies by decomposing movements. In workshop 2, we introduced MoveOn, a technology probe that lets dancers decompose video into short, repeatable clips to support their learning. This served as an effective analysis tool for identifying the changes in focus and understanding their decomposition and recomposition processes. In workshop 3, we compared the teacher's and dancers' decomposition strategies, and how dancers learn on their own compared to teacher-created decompositions. We found that they all ungroup and regroup dance fragments, but with different foci of attention, which suggests that teacher-imposed decomposition is more effective for introductory dance students, whereas personal decomposition is more suitable for expert dancers. We discuss the implications for designing technology to support analysis, learning and teaching of dance through movement decomposition.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359188"}, {"primary_key": "2898079", "vector": [], "sparse_vector": [], "title": "Exploring the Potential for Technology to Improve Cystic Fibrosis Care Provision: Patient and Professional Perspectives.", "authors": ["<PERSON><PERSON>", "Kenton O&apos;Hara"], "summary": "Health care systems increasingly promote self-management of chronic conditions outside of traditional clinical environments, often through technologies which help to support patient self-care and engagement with medical professionals. We investigate specialist care provision in cystic fibrosis (CF), a life-shortening genetic condition, to understand the experiences of those living with it and of professionals who provide such care. Our work highlights how the motivations for the use of technology in this context are often intrinsically linked to the nature of CF itself and the constraints that the condition imposes on care provision. These include the high burden associated with self-management and clinic attendance; the ever-present risk of infection and a subsequent decline in health; and patients who are often very well-informed and actively engaged in their care. In exploring enablers and barriers to technology in this context, we highlight the importance of considering its integration into the chronic care cycles, practices, and structures of CF care.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359223"}, {"primary_key": "2898080", "vector": [], "sparse_vector": [], "title": "A Conceptual Framework and Content Model for Next Generation Presentation Solutions.", "authors": ["<PERSON><PERSON><PERSON>", "Beat Signer"], "summary": "Mainstream presentation tools such as Microsoft PowerPoint were originally built to mimic physical media like photographic slides and still exhibit the same characteristics. However, the state of the art in presentation tools shows that more recent solutions start to go beyond the classic presentation paradigms. For instance, presentations are becoming increasingly non-linear, content is quickly evolving beyond simple text and images and the way we author our presentations is becoming more collaborative. Nevertheless, existing presentation content models are often based on assumptions that do not apply to the current state of presentations any more, making them incompatible for some use cases and limiting the potential of end-user presentation solutions. In order to support state-of-the-art presentation functionality, we rethink the concept of a presentation and introduce a conceptual framework for presentation content. We then present a new content model for presentation solutions based on the Resource-Selector-Link (RSL) hypermedia metamodel. We further discuss an implementation of our model and show some example use cases. We conclude by outlining how design choices in the model address currently unmet needs with regards to extensibility, content reuse, collaboration, semantics, user access management, non-linearity, and context awareness, resulting in better support for the corresponding end-user functionality in presentation tools.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3331149"}, {"primary_key": "2898081", "vector": [], "sparse_vector": [], "title": "The Language of LGBTQ+ Minority Stress Experiences on Social Media.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Man<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "LGBTQ+ (lesbian, gay, bisexual, transgender, queer) individuals are at significantly higher risk for mental health challenges than the general population. Social media and online communities provide avenues for LGBTQ+ individuals to have safe, candid, semi-anonymous discussions about their struggles and experiences. We study minority stress through the language of disclosures and self-experiences on the r/lgbt Reddit community. Drawing on <PERSON>'s minority stress theory, and adopting a combined qualitative and computational approach, we make three primary contributions, 1) a theoretically grounded codebook to identify minority stressors across three types of minority stress-prejudice events, perceived stigma, and internalized LGBTphobia, 2) a machine learning classifier to scalably identify social media posts describing minority stress experiences, that achieves an AUC of 0.80, and 3) a lexicon of linguistic markers, along with their contextualization in the minority stress theory. Our results bear implications to influence public health policy and contribute to improving knowledge relating to the mental health disparities of LGBTQ+ populations. We also discuss the potential of our approach to enable designing online tools sensitive to the needs of LGBTQ+ individuals.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3361108"}, {"primary_key": "2898082", "vector": [], "sparse_vector": [], "title": "LibRA: On LinkedIn based Role Ambiguity and Its Relationship with Wellbeing and Job Performance.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Man<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Job roles serve as a boundary between an employee and an organization, and are often considered building blocks in understanding the behavior and functioning of organizational systems. However, a lack of clarity about one's role, that is, one's work responsibilities and degree of authority, can lead to absenteeism, turnover, dissatisfaction, stress, and lower workplace performance. This paper proposes a methodology to quantitatively estimate role ambiguity via unobtrusively gathered data from LinkedIn, shared voluntarily by a cohort of information workers spanning multiple organizations. After successfully validating this LinkedIn based measure of Role Ambiguity, or LibRA against a state-of-the-art gold standard, drawing upon theories in organizational psychology, we examine the efficacy and convergent validity of LibRA in explaining established relationships of role ambiguity with wellbeing and performance measures of individuals. We find that greater LibRA is associated with depleted wellbeing, such as increased heart rate, increased arousal, decreased sleep, and higher stress. In addition, greater LibRA is associated with lower job performance such as decreased organizational citizenship behavior and decreased individual task performance. We discuss how LibRA can help fill gaps in state-of-the-art assessments of role ambiguity, and the potential of this measure in building novel technology-mediated strategies to combat role ambiguity in organizations.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359239"}, {"primary_key": "2898083", "vector": [], "sparse_vector": [], "title": "MP Remix: Relaxed WYSIWIS Immersive Interfaces for Mixed Presence Collaboration With 3D Content.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "to create an integrated space. We consider an MR configuration in which collocated collaborators work around a tabletop display, while remote collaborators wear an HMD to interact with a connected virtual environment that gives a 3D perspective, and consider the impact of varying degrees of view congruence with their collaborators. In a within-subjects study with 18 groups of 3, groups completed task scenarios involving 3D object manipulation around a physical-virtual mapped tabletop. We compare a synchronized Tabletop display baseline and two MR conditions with different levels of view congruence: Fishtank and Hover. Fishtank has a high degree of congruence as it shares a top-down perspective of the 3D objects with the tabletop collaborators. The Hover condition has less view congruence since 3D content hovers front of the remote collaborator above the table. The MR conditions yielded higher self-reported awareness and co-presence than the Tabletop condition for both collocated and remote participants. Remote collaborators significantly preferred the MR conditions for manipulating shared 3D models and communicating with their collaborators. Our findings illustrate strengths and weaknesses of both MR techniques but show that more participants preferred the less-congruent Hover condition overall. Reasons include that it facilitated interaction and viewing 3D objects.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359207"}, {"primary_key": "2898084", "vector": [], "sparse_vector": [], "title": "What&apos;s in a Review: Discrepancies Between Expert and Amateur Reviews of Video Games on Metacritic.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "As video game press (\"experts\") and casual gamers (\"amateurs\") have different motivations when writing video game reviews, discrepancies in their reviews may arise. To study such potential discrepancies, we conduct a large-scale investigation of more than 1 million reviews on the Metacritic review platform. In particular, we assess the existence and nature of discrepancies in video game appraisal by experts and amateurs, and how they manifest in ratings, over time, and in review language. Leveraging these insights, we explore the predictive power of early expert vs. amateur reviews in forecasting video game reputation in the short- and long-term. We find that amateurs, in contrast to experts, give more polarized ratings of video games, rate games surprisingly long after game release, and are positively biased towards older games. On a textual level, we observe that experts write rather complex, less readable texts than amateurs, whose reviews are more emotionally charged. While in the short-term amateur reviews are remarkably predictive of game reputation among other amateurs (achieving 91% ROC AUC in a binary classification), both expert and amateur reviews are equally well suited for long-term predictions. Overall, our work is the first large-scale comparative study of video game reviewing behavior, with practical implications for amateurs when deciding which games to play, and for game developers when planning which games to design, develop, or continuously support. More broadly, our work contributes to the discussion of wisdom of the few vs. wisdom of the crowds, as we uncover the limits of experts in capturing the views of amateurs in the particular context of video game reviews.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359242"}, {"primary_key": "2898085", "vector": [], "sparse_vector": [], "title": "Understanding Expert Disagreement in Medical Data Analysis through Structured Adjudication.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Expert disagreement is pervasive in clinical decision making and collective adjudication is a useful approach for resolving divergent assessments. Prior work shows that expert disagreement can arise due to diverse factors including expert background, the quality and presentation of data, and guideline clarity. In this work, we study how these factors predict initial discrepancies in the context of medical time series analysis, examining why certain disagreements persist after adjudication, and how adjudication impacts clinical decisions. Results from a case study with 36 experts and 4,543 adjudicated cases in a sleep stage classification task show that these factors contribute to both initial disagreement and resolvability, each in their own unique way. We provide evidence suggesting that structured adjudication can lead to significant revisions in treatment-relevant clinical parameters. Our work demonstrates how structured adjudication can support consensus and facilitate a deep understanding of expert disagreement in medical data analysis.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359178"}, {"primary_key": "2898088", "vector": [], "sparse_vector": [], "title": "Passively-sensed Behavioral Correlates of Discrimination Events in College Students.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A deep understanding of how discrimination impacts psychological health and well-being of students could allow us to better protect individuals at risk and support those who encounter discrimination. While the link between discrimination and diminished psychological and physical well-being is well established, existing research largely focuses on chronic discrimination and long-term outcomes. A better understanding of the short-term behavioral correlates of discrimination events could help us to concretely quantify such experiences, which in turn could support policy and intervention design. In this paper we specifically examine, for the first time, what behaviors change and in what ways in relation to discrimination. We use actively-reported and passively-measured markers of health and well-being in a sample of 209 first-year college students over the course of two academic quarters. We examine changes in indicators of psychological state in relation to reports of unfair treatment in terms of five categories of behaviors: physical activity, phone usage, social interaction, mobility, and sleep. We find that students who encounter unfair treatment become more physically active, interact more with their phone in the morning, make more calls in the evening, and spend more time in bed on the day of the event. Some of these patterns continue the next day. Our results further our understanding of the impact of discrimination and can inform intervention work.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359216"}, {"primary_key": "2898089", "vector": [], "sparse_vector": [], "title": "&apos;Routine Infrastructuring&apos; as &apos;Building Everyday Resilience with Technology&apos;: When Disruption Becomes Ordinary.", "authors": ["<PERSON>"], "summary": "Getting a divorce. Being diagnosed with a disease. Going through a relationship breakup. Living through a natural disaster. All of these events are often life disrupting and debilitating. While some disruptive events are short-lived, some can be a routine part of everyday life. This leads to the question of how people who experience prolonged disruption in their lives build resilience---that is, how do they manage and overcome such events? To explore this question, this paper utilizes a case study approach to explore the use, creation, and re-appropriation of technology across three prolonged disruptions-the Second Gulf War in Iraq, veteran transitions, and the coming out experiences of LGBTQ-identifying people. Using a conceptual frame that brings together routine dynamics and infrastructuring, we find that engaging in routine infrastructuring practices generated resilience in people's daily lives---a phenomenon we dub 'routine infrastructuring' as 'building everyday resilience with technology.' We then theorize properties of infrastructure and infrastructuring practice that enable resiliency, and conclude with how infrastructuring is a form of care work that is oriented towards individuals, communities, and society.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359175"}, {"primary_key": "2898090", "vector": [], "sparse_vector": [], "title": "How Trainees Use the Information from Telepointers in Remote Instruction.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Yuanyuan Feng", "<PERSON><PERSON>", "Adrian Park", "<PERSON>", "<PERSON>"], "summary": "Researchers have shown both performance drawbacks and benefits of using telepointers or similar display overlay-technologies in remote instruction; however, there is not a clear understanding of why there are these performance effects. This poses a challenge in knowing how and when to successfully use or design telepointing technologies in remote instruction. A better understanding is needed with the rise of remote workers in a wide array of industries from oil rig repair to surgery, and the proliferation of heads-up displays or telecommunications devices to support these future work practices. In this study, we explore how the information conveyed through a telepointer is taken up and acted upon by surgical trainees in a laparoscopic surgical telementoring setting. We collected audio and video data of 12 surgical trainees who performed standard laparoscopic surgical tasks on a physical model under the guidance of a surgical trainer. We investigated both action and talk to determine how the telepointer-based information was used. Our findings reveal three main challenges in using the instructional information conveyed through the telepointer including the trainees' tendency of attending to the telepointer instruction as the primary source of information. We argue that the found challenges are socio-technical in nature and require a redesign of the mentoring context as well as the technological tools.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359195"}, {"primary_key": "2898091", "vector": [], "sparse_vector": [], "title": "Balancing Tensions between Caregiving and Parenting Responsibilities in Pediatric Patient Care.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Ayse G. <PERSON>", "Sun Young Park"], "summary": "In pediatric chronic care, the treatment process affects not just the child's physical health, but his or her psychosocial and emotional development. As a result, caring for pediatric patients with a chronic illness such as cancer is becoming a daunting task for parental caregivers. They are expected to fulfill the caregiving needs of managing the child's health condition and treatment while also meeting the parenting needs of translating knowledge, communicating about the illness, and making numerous decisions on a daily basis for their sick child due to the child's young age. Drawing on 15 semi-structured interviews, we examined parental caregivers' perspectives on raising a child while also managing the child's health. We identified three tensions that participants encountered as they balanced parenting and caregiving responsibilities: (i) tension between ensuring the child's health and safety and attending to the child's social development, (ii) tension between disclosing health-related information and minimizing the psychological burden on the child, and (iii) tension between rewarding the child's cooperation in treatment and maintaining discipline. Together, these tensions reveal an ongoing process through which caregivers assess and interpret their actions and responsibilities relative to anticipated consequences across multiple time scales. These findings reveal opportunities for sociotechnical systems to account for and support this active process of iterative cycles of assessment.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359255"}, {"primary_key": "2898092", "vector": [], "sparse_vector": [], "title": "Speedrunning for Charity: How Don<PERSON> Gather Around a Live Streamed Couch.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Games Done Quick (GDQ) is both a week-long video game speedrunning marathon and a successful charity event, raising more than $1.5 million USD in each of its past five events. To understand GDQ's success as an online charity event, we conducted 18 semi-structured interviews with GDQ speedrunners, attendees, hosts, and online viewers, analyzed past donation data, and conducted 72 hours of in-person participant observations at a live GDQ event. We found that central to every GDQ event is \"the couch\" which reconstructs the environment of a living room. Viewers do not simply donate to support the charity or in response to the technical prowess and ingenuity of speedrunners, but to actively interact with and be part of the couch experience, the ideal social milieu of speedrunning. Building upon previously identified motivations of why viewers donate to online live streamers, our work contributes to understanding how collocated gatherings can reinforce and amplify the cultural and social aspects of online subcultures (even heterogeneous ones) to encourage charitable giving. This opens opportunities for design that evoke a visceral level of engagement with charity events similar to that observed at GDQ.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359150"}, {"primary_key": "2898093", "vector": [], "sparse_vector": [], "title": "Accessible Video Calling: Enabling Nonvisual Perception of Visual Conversation Cues.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Nonvisually Accessible Video Calling (NAVC) is a prototype that detects visual conversation cues in a video call and uses audio cues to convey them to a user who is blind or low-vision. NAVC uses audio cues inspired by movie soundtracks to convey Attention, Agreement, Disagreement, Happiness, Thinking, and Surprise. When designing NAVC, we partnered with people who are blind or low-vision through a user-centered design process that included need-finding interviews and design reviews. To evaluate NAVC, we conducted a user study with 16 participants. The study provided feedback on the NAVC prototype and showed that the participants could easily discern some cues, like Attention and Agreement, but had trouble distinguishing others. The accuracy of the prototype in detecting conversation cues emerged as a key concern, especially in avoiding false positives and in detecting negative emotions, which tend to be masked in social conversations. This research identified challenges and design opportunities in using AI models to enable accessible video calling.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359233"}, {"primary_key": "2898094", "vector": [], "sparse_vector": [], "title": "Normal and Easy: Account Sharing Practices in the Workplace.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Work is being digitized across all sectors, and digital account sharing has become common in the workplace. In this paper, we conduct a qualitative and quantitative study of digital account sharing practices in the workplace. Across two surveys, we examine the sharing process at work, probing what accounts people share, how and why they share those accounts, and identifying the major challenges people face in sharing accounts. Our results demonstrate that account sharing in the modern workplace serves as a norm rather than a simple workaround; centralizing collaborative activity and reducing boundary management effort are key motivations for sharing. But people still struggle with a lack of activity accountability and awareness, conflicts over simultaneous access, difficulties controlling access, and collaborative password use. Our work provides insights into the current difficulties people face in workplace collaboration with online account sharing, as a result of inappropriate designs that still assume a single-user model for accounts. We highlight opportunities for CSCW and HCI researchers and designers to better support sharing by multiple people in a more usable and secure way.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359185"}, {"primary_key": "2898095", "vector": [], "sparse_vector": [], "title": "Content Removal as a Moderation Strategy: Compliance and Other Outcomes in the ChangeMyView Community.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Moderators of online communities often employ comment deletion as a tool. We ask here whether, beyond the positive effects of shielding a community from undesirable content, does comment removal actually cause the behavior of the comment's author to improve? We examine this question in a particularly well-moderated community, the ChangeMyView subreddit. The standard analytic approach of interrupted time-series analysis unfortunately cannot answer this question of causality because it fails to distinguish the effect of having made a non-compliant comment from the effect of being subjected to moderator removal of that comment. We therefore leverage a \"delayed feedback\" approach based on the observation that some users may remain active between the time when they posted the non-compliant comment and the time when that comment is deleted. Applying this approach to such users, we reveal the causal role of comment deletion in reducing immediate noncompliance rates, although we do not find evidence of it having a causal role in inducing other behavior improvements. Our work thus empirically demonstrates both the promise and some potential limits of content removal as a positive moderation strategy, and points to future directions for identifying causal effects from observational data.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359265"}, {"primary_key": "2898096", "vector": [], "sparse_vector": [], "title": "Disinformation as Collaborative Work: Surfacing the Participatory Nature of Strategic Information Operations.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we argue that strategic information operations (e.g. disinformation, political propaganda, and other forms of online manipulation) are a critical concern for CSCW researchers, and that the CSCW community can provide vital insight into understanding how these operations function-by examining them as collaborative \"work\" within online crowds. First, we provide needed definitions and a framework for conceptualizing strategic information operations, highlighting related literatures and noting historical context. Next, we examine three case studies of online information operations using a sociotechnical lens that draws on CSCW theories and methods to account for the mutual shaping of technology, social structure, and human action. Through this lens, we contribute a more nuanced understanding of these operations (beyond \"bots\" and \"trolls\") and highlight a persistent challenge for researchers, platform designers, and policy makers-distinguishing between orchestrated, explicitly coordinated, information operations and the emergent, organic behaviors of an online crowd.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359229"}, {"primary_key": "2898098", "vector": [], "sparse_vector": [], "title": "Caring for Alzheimer&apos;s Disease Caregivers: A Qualitative Study Investigating Opportunities for Exergame Innovation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The number of informal caregivers for family members with Alzheimer's Disease (AD) is rising dramatically in the United States. AD caregivers disproportionately experience numerous health problems and are often isolated with little support. An active lifestyle can help prevent and mitigate physical and psychological health concerns amongst AD caregivers. Research has demonstrated how pervasive exergames can encourage physical activity (PA) in the general population, yet little work has explored how these tools can address the significant PA barriers that AD caregivers face. To identify opportunities for design, we conducted semi-structured interviews and participatory design sessions with 14 informal caregivers of family members with AD. Our findings characterize how becoming an AD caregiver profoundly impacts one's ability to be active, perspectives on being active, and the ways that exergames might best support this population.We discuss implications for design and howour findings challenge existing technological approaches to PA promotion.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359232"}, {"primary_key": "2898099", "vector": [], "sparse_vector": [], "title": "&quot;<PERSON>r-<PERSON>ktar Understands My Problems Better&quot;: Disentangling the Challenges to Designing Better Access to Healthcare in Rural Bangladesh.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper discusses the issues that arise while designing better wellbeing support for a low-income rural population in Bangladesh. Through a four-month long ethnographic study, we explored how people in 13villages in southwestern Bangladesh accessed healthcare. Over the course of our fieldwork, we asked the participants about the existing healthcare services available to them and how they interacted with different ICT-based wellbeing support systems. Our findings show that insufficient resources, schedules, and the distant location of government-supported healthcare facilities were major challenges for the villagers. We also found that villagers' limited knowledge and mistrust of care-providing infrastructure block them from the benefits of available ICT-based supports and resources. Drawing on our findings from the field, we discuss possible alternative design directions for improving wellbeing support for rural Bangladeshis.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359270"}, {"primary_key": "2898100", "vector": [], "sparse_vector": [], "title": "Multi-level Modeling of Social Roles in Online Micro-lending Platforms.", "authors": ["Lu Sun", "<PERSON>", "<PERSON><PERSON>"], "summary": "In many teams, members play distinct roles, from leader to disrupter to social networker. Understanding the roles that contributors enact in micro-lending platforms is both psychologically and socially important for sustaining members' motivation and coordinating their joint efforts. Knowing what roles exist in these communities or which additional ones might be needed can also help teams function more effectively. In this paper, we identify social roles in Kiva.org, a peer-to-peer micro-funding platform, by utilizing members' lending behaviors, social network behaviors and communication behaviors to model their social roles at three levels. At the individual level, this method discovered active lenders who made many loans, early-bird lenders who made loans well before deadlines, and lurkers who rarely lent. At the topical level, our method differentiated those who had broad interests and lent to many causes from those who made loans only to borrowers in certain geographic regions or industry sectors. At the team level, the method revealed eight team-oriented functional roles such as encouragers, reminders, competitors, followers, and welcomers. To demonstrate the utility of the team roles, we used regression analysis to show how the distribution of social roles within teams influences the amount of money teams lent. Implications for identifying roles and understanding their contributions to teams are discussed.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359235"}, {"primary_key": "2898101", "vector": [], "sparse_vector": [], "title": "How Presenters Perceive and React to Audience Flow Prediction In-situ: An Explorative Study of Live Online Lectures.", "authors": ["<PERSON>", "<PERSON><PERSON> Li", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The degree and quality of instructor-student interactions are crucial for students' engagement, retention, and learning outcomes. However, such interactions are limited in live online lectures, where instructors no longer have access to important cues such as raised hands or facial expressions at the time of teaching. As a result, instructors cannot fully understand students' learning progresses. This paper presents an explorative study investigating how presenters perceive and react to audience flow prediction when giving live-stream lectures, which has not been examined yet. The study was conducted with an experimental system that can predict audience's psychological states (e.g., anxiety, flow, boredom) through real-time facial expression analysis, and can provide aggregated views illustrating the flow experience of the whole group. Through evaluation with 8 online lectures (N_instructors=8, N_learners=21), we found such real-time flow prediction and visualization can provide value to presenters. This paper contributes a set of useful findings regarding their perception and reaction of such flow prediction, as well as lessons learned in the study, which can be inspirational for building future AI-powered system to assist people in delivering live online presentations.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359264"}, {"primary_key": "2898102", "vector": [], "sparse_vector": [], "title": "Birds of a Feather Clock Together: A Study of Person-Organization Fit Through Latent Activity Routines.", "authors": ["Vedant <PERSON>", "Man<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Organizations often strive to recruit and retain individuals who would be a \"good fit\" with their core values, beliefs and practices. Person-Organization (P-O congruence is known to explain employee satisfaction, commitment and absenteeism. This paper proposes a new measure of P-O fit by empirically investigating the similarity of routine within an organization. This measure of routine fit is motivated by the theory of entrainment, which refers to the synchrony of individual and community behaviors. We use unobtrusive bluetooth sensing to examine how the concurrence of latent activity patterns is related to job performance and wellbeing. Routine fit echoes traditional constructs of congruence as it is significantly related to higher task performance and lower workplace deviance. Additionally however, it is also related to greater stress and higher arousal. Prior work in organizational psychology have used single-occasion survey instruments to infer uni-dimensional models of fit. These methods are limited by subjective perceptions of employees. In contrast, we demonstrate a data-driven and multidimensional approach to study normative routines in an organization as a measure of P-O fit. We discuss the potential of our approach in designing technologies that understand the congruence of employee routines and positively impact employee functioning at the workplace.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359267"}, {"primary_key": "2898103", "vector": [], "sparse_vector": [], "title": "Learn2Earn: Using Mobile Airtime Incentives to Bolster Public Awareness Campaigns.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In rural parts of the developing world, spreading awareness about critical issues in health, governance, and other topics is challenging and costly. Traditional media such as print, radio and TV each have limitations and offer little guarantee that new information is absorbed or retained by the target population. This paper describes Learn2Earn, a system that leverages mobile payments to bolster public awareness campaigns in rural India. Users call an Interactive Voice Response (IVR) system, listen to a brief audio tutorial, and take a multiple-choice quiz to check their understanding. People who pass the quiz receive a mobile top-up (about $0.14) and have the opportunity to earn additional credits by referring others to the system. We describe a pilot deployment of Learn2Earn in rural India that spread via word-of-mouth to over 15,000 people within seven weeks. Usage was concentrated among young men, many of them students. In a mixed-methods study, we draw upon call logs, electronic surveys, qualitative interviews, and other sources of data to suggest that Learn2Earn could be an effective way to build awareness about important topics.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359151"}, {"primary_key": "2898104", "vector": [], "sparse_vector": [], "title": "Working Apart, Together: The Challenges of Co-Work.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Attracting contingent workers, teleworkers, small start-up teams and the self-employed, co-working spaces have grown from corner coffee shops and internet cafes, to business empires with thousands of members in urban locations. Yet the rise of co-working in the twenty-first century has received only limited attention in CSCW, despite co-workers' copious use of distributed collaborative tools. CSCW has more frequently addressed the challenges of working together, apart - that is, how we might support or otherwise transpose rich collocated interactions into distributed environments. This paper explores the challenges of working alone and apart, yet together, based on observational study and interviews at three large co-working spaces in New York City. Using a sociomaterial approach, we identify and explore core tensions visible in the site between participation in a distributed work team and in a distinct, collocated community, which we label the co-working paradox. This includes local configurations of digital and physical materials that shift locus of participation, that blend work and home, and manage employer visibility. We use these themes to suggest a generative return to certain core concepts in studies of distributed work - namely, common ground, workspaces, and placefulness - to further study and design for the growth of these unique environments.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359306"}, {"primary_key": "2898105", "vector": [], "sparse_vector": [], "title": "How to Communicate when Submitting Patches: An Empirical Study of the Linux Kernel.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Communication when submitting patches (CSP) is critical in software development, because ineffective communication wastes the time of developers and reviewers, and is even harmful to future product release and maintenance. In distributed software development, CSP is usually in the form of computer-mediated communication (CMC), which is a crucial topic concerned by the CSCW community. However, what to say and how to say in communication including CSP has been rarely studied. To bridge this knowledge gap and provide relevant guidance for developers to conduct CSP, in this study, we conducted an empirical study on the Linux kernel. We found four themes involving 17 expression elements that characterize what to express when submitting patches and three themes of contextual factors that determine how these elements are applied. Considering both expression elements and context, combined with an online survey, we obtained 17 practices for communication. Among them, four practices, such as \"provide sufficient reasons\" and \"provide a trade-off\" are the most important but difficult practices, for which we provide specific instructions. We also found that the \"individual factors\" plays a special role in communication, which may lead to potential problems even in accepted patches. Based on these findings, we discuss the recommendations for different practitioners, including patch submitters, reviewers, and tool designers, and the implications for open source software (OSS) communities and the CSCW researchers.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359210"}, {"primary_key": "2898106", "vector": [], "sparse_vector": [], "title": "Digital Participation in Prison - A Public Discourse Analysis of the Use of ICT by Inmates.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Digital participation has become an important issue in modern societies, typically focusing on groups prone to marginalization. From this perspective, less attention has yet been paid to imprisoned persons. Many penitentiary systems are formally characterized by the basic requirement of resocialization. Accordingly, life in prison should be largely adapted to conditions outside and to fundamental social changes, which must be considered in designing the framework for prison systems. Still, the digital opening of the prison is debated controversially, not least due to anticipated security issues. Hence, to raise awareness about challenges for digital participation of prisoners, we conducted a knowledge-sociological analysis (SKAD) of the public discourse on the use of ICT by prison inmates in Germany. We thereby seek to advance knowledge and insights from social and organizational practice arising from the use of ICT in context of total institutions and create the basis for action from a socio-informatics perspective.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3361114"}, {"primary_key": "2898107", "vector": [], "sparse_vector": [], "title": "Accountability and Empathy by Design: Encouraging Bystander Intervention to Cyberbullying on Social Media.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Natalya N<PERSON>"], "summary": "Bystander intervention can reduce the amount of cyberbullying victimization on social media, but bystanders often fail to act. Limited accountability for their behavior and a lack of empathy for the victim are frequently cited as reasons for why bystanders do not act against cyberbullying. We developed design interventions that aimed to increase accountability and empathy among bystanders. In Study 1, participants were experimentally exposed to three social media posts with different types of empathy nudges. Empathy nudges embedded into social media posts displayed the potential to motivate empathy. In Study 2, participants took part in a 3-day experiment that simulated a social media experience. Results suggested that increased social transparency on social media promoted accountability through heightened self-presentation concerns, but empathy nudges did not encourage greater bystander empathy. Both accountability and empathy predicted bystander intervention, but the types of bystander actions promoted by each mechanism differed. We consider how these results contribute to theories of bystander behavior and designing social media to promote prosocial behaviors.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359220"}, {"primary_key": "2898108", "vector": [], "sparse_vector": [], "title": "Coordinating Clinical Teams: Using Robots to Empower Nurses to Stop the Line.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Patient safety errors account for over 400,000 preventable deaths annually in US in hospitals alone, 70% of which are caused by team communication breakdowns, stemming from hierarchical structures and asymmetrical power dynamics between physicians, nurses, patients, and others. Nurses are uniquely positioned to identify and prevent these errors, but they are often penalized for speaking up, particularly when physicians are responsible. Nevertheless, empowering nurses and building strong interdisciplinary teams can lead to improved patient safety and outcomes. Thus, our group has been developing a series of intelligent systems that support teaming in safety critical settings, Robot-Centric Team Support System (RoboTSS), and recently developed a group detection and tracking system for collaborative robots. In this paper, we explore how RoboTSS can be used to empower nurses in interprofessional team settings, through a three month long, collaborative design process with nurses across five US-based hospitals. The main findings and contributions of this paper are as follows. First, we found that participants envisioned using a robotic crash cart to guide resuscitation procedures to improve efficiency and reduce errors. Second, nurses discussed how RoboTSS can generate choreography for efficient spatial reconfigurations in co-located clinical teams, which is particularly important in time-sensitive situations such as resuscitation. Third, we found that nurses want to use RoboTSS to \"stop the line,\" and disrupt power dynamics by policing unsafe physician behavior, such as avoiding safety protocols using a robotic crash cart. Fourth, nurses envisioned using our system to support real-time error identification, such as breaking the sterile field, and then communicating those errors to physicians, to relieve them of responsibility. Finally, based on our findings, we propose robot design implications that capture how nurses envision utilizing RoboTSS. We hope this work promotes further exploration in how to design technology to challenge authority in asymmetrical power relationships, particularly in healthcare, as strong teams save lives.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359323"}, {"primary_key": "2898110", "vector": [], "sparse_vector": [], "title": "MoCaDiX: Designing Cross-Device User Interfaces of an Information System based on its Class Diagram.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper presents MoCaDiX, a method for designing cross-device graphical user interfaces of an information system based on its UML class diagram, structured as a four-step process: (1) a UML class diagram of the information system is created in a model editor, (2) how the classes, attributes, methods, and relationships of this class diagram are presented across devices is then decided based on user interface patterns with their own parametrization, (3) based on these parameters, a Concrete User Interface model is generated in QuiXML, a lightweight fit-to-purpose User Interface Description Language, and (4) based on this model, HTML5 cross-device user interfaces are semi-automatically generated for four configurations: single/multipledevice single/multiple-display on a smartphone, a tablet, and a desktop. From the practitioners' viewpoint, a first experiment investigates effectiveness, efficiency, and subjective satisfaction of three intermediate and three expert designers, using MoCaDiX on a representative class diagram. From the end user's viewpoint, a second experiment compares subjective satisfaction and preference of twenty end users assessing layout strategies for interfaces generated on two devices.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3331159"}, {"primary_key": "2898111", "vector": [], "sparse_vector": [], "title": "AB4Web: An On-Line A/B Tester for Comparing User Interface Design Alternatives.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Radu<PERSON><PERSON>"], "summary": "We introduce AB4Web, a web-based engine that implements a balanced randomized version of the multivariate A/B testing, specifically designed for practitioners to readily compare end-users' preferences for user interface alternatives, such as menu layouts, widgets, controls, forms, or visual input commands. AB4Web automatically generates a balanced set of randomized pairs from a pool of user interface design alternatives, presents them to participants, collects their preferences, and reports results from the perspective of four quantitative measures: the number of presentations, the preference percentage, the latent score of preference, and the matrix of preferences. In this paper, we exemplify the AB4Web tester with a user study for which N=108 participants expressed their preferences regarding the visual design of 49 distinct graphical adaptive menus, with a total number of 5,400 preference votes. We compare the results obtained from our quantitative measures with four alternative methods: Condorcet, de Borda count starting at one and zero, and the Dowdall scoring system. We plan to release AB4Web as a public tool for practitioners to create their own A/B testing experiments.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3331160"}, {"primary_key": "2898112", "vector": [], "sparse_vector": [], "title": "How Teachers in India Reconfigure their Work Practices around a Teacher-Oriented Technology Intervention.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The proliferation of mobile devices around the world, combined with falling costs of hardware and Internet connectivity, have resulted in an increasing number of organizations that work to introduce educational technology interventions into low-income schools in the Global South. However, to date, most prior HCI research examining such interventions has focused on interventions that target students. In this paper, we expand prior literature by examining an intervention, called Meghshala, that targets teachers in low-income schools as its primary users. Through interviews and observations with 39 participants from 12 government schools in India, we show how the introduction of a teacher-focused technology intervention causes teachers to reconfigure their work practices, including lesson preparation, in-classroom teaching practices, bureaucratic work processes, and post-teaching feedback mechanisms. We use the concept of material agency to analyze our findings with respect to teacher agency and reconfiguration, and use theories of teacher knowledge to highlight the kinds of knowledge production that teachers in our research context tend to focus on (e.g., content knowledge). Finally, we offer design opportunities for future teacher-focused technology interventions.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359322"}, {"primary_key": "2898113", "vector": [], "sparse_vector": [], "title": "GroundTruth: Augmenting Expert Image Geolocation with Crowdsourcing and Shared Representations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Rifat Sabbir <PERSON>", "<PERSON>"], "summary": "Expert investigators bring advanced skills and deep experience to analyze visual evidence, but they face limits on their time and attention. In contrast, crowds of novices can be highly scalable and parallelizable, but lack expertise. In this paper, we introduce the concept of shared representations for crowd--augmented expert work, focusing on the complex sensemaking task of image geolocation performed by professional journalists and human rights investigators. We built GroundTruth, an online system that uses three shared representations-a diagram, grid, and heatmap-to allow experts to work with crowds in real time to geolocate images. Our mixed-methods evaluation with 11 experts and 567 crowd workers found that GroundTruth helped experts geolocate images, and revealed challenges and success strategies for expert-crowd interaction. We also discuss designing shared representations for visual search, sensemaking, and beyond.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359209"}, {"primary_key": "2898114", "vector": [], "sparse_vector": [], "title": "Complex, Contemporary, and Unconventional: Characterizing the Tweets of the #NativeVote Movement and Native American Candidates through the 2018 U.S. Midterm Elections.", "authors": ["Morgan Vigil-<PERSON>", "<PERSON><PERSON><PERSON> Deschine <PERSON>", "<PERSON><PERSON>"], "summary": "In the 2018 U.S. midterm elections, a record number of Native American candidates ran for office at all levels of government. To better understand how these 104 candidates intersected with Indigenous political issues and movements to increase Native American voter turnout, we study 723,269 tweets about or by these candidates and 15,476 tweets associated with the #NativeVote movement between October 6, 2018 and February 5, 2019. We use a mixed methods approach to identify issues that emerge in the Native Candidates data set, including issues of representation and protean usage of the \"Make America Great Again\" hashtag #maga. When examining the feeds of selected candidates, we find that there can be a disconnect between the issues that candidates align themselves with on social media and the issues that they are associated with by others. We also find evidence of Indigenous issues spanning a vast political spectrum and being coupled with other issues in different ways by different candidates and audiences. Finally, we examine the intersection between Native American candidates and the\\#NativeVote movement to discover emergent issue networks, including networks around voter suppression and Indigenous political action. Critically, we discuss how our interdisciplinary Indigenous feminist approach to social media analysis illuminates issues of marginalized communities in both a systematic and inductive manner that allows us to discover new patterns and issues with limited a priori knowledge about a complex system.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359205"}, {"primary_key": "2898115", "vector": [], "sparse_vector": [], "title": "Altruism and Wellbeing as Care Work in a Craft-based Maker Culture.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "This paper aims to examine motivations of participants for being involved in a crafts-based, women-only maker organization. Over a period of three months, we observed and interviewed ten women aged 42 - 78 to explore how they experienced being involved in knitting, sewing, crocheting, card making and other similar craft forms. Using the ethos of care as a lens to interpret our data, we found that our participants perceived making as an 'outlet' for them to perform activities that support their physical and mental wellbeing. Moreover, they perceived making as a tool to support others in need and help the larger community rather than a self-serving achievement. We also observed how the use of technology was inherent into their craft making activities that supported not only the making processes but also sharing these with others. Our findings highlight the 'care work' that goes into making in such a social setting that generally goes unnoticed when the focus is purely on instrumental and objective aspects of making. We believe that these findings will inform CSCW researchers to think about the not-so-visible qualities of making that elevate care (for self and for others), and to support these via the design of innovative technologies.?", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3361120"}, {"primary_key": "2898116", "vector": [], "sparse_vector": [], "title": "Making at the Margins: Making in an Under-resourced e-Waste Recycling Center.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "HCI and CSCW literature has extensively studied a wide variety of maker cultures. In this paper, we focus on understanding what making is like for people and communities who do not have access to advanced technological infrastructures. We report on six-month-long ethnographic fieldwork at a non-profit, resource-constrained, e-waste recycling centre that engages members from a low socioeconomic status (SES) community in making activities. Our findings show that making in such a setting is shaped by local economic and social factors in a resource-constrained environment and highlight how this community engages in a wide range of making activities. In describing these making activities, we emphasize how making was conducted to purposely enable ongoing and future making by others; promoted the wellbeing and skill development of centre members; and was socially-engaged to address concerns in the local community. We conclude by discussing how such type of making contributes a new understanding of maker culture, one that is appreciative of resource-constraints, integrates different sources of value, and is embedded in local place.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359290"}, {"primary_key": "2898117", "vector": [], "sparse_vector": [], "title": "When DiDi Is Not Really A Choice in Small Chinese Cities, Taxi Drivers Build Their Own.", "authors": ["<PERSON>"], "summary": "\"Sharing economy\" platforms have become increasingly popular, providing people with cost-effective choices and service providers a chance to make a profit. Among these platforms, ride-sharing services such as Uber reshape people's daily mobility experiences. However, these highly-functional platforms do not always provide effective mobility solutions due to social, economic, and technical restrictions. In low resource and rural areas, drivers in these areas often have to build their own ride-sharing systems to satisfy their needs. We present a qualitative study employing both participant observation and semi-structured interview techniques to document the barriers for DiDi, a dominant Chinese ride-sharing platform, to solve the mobility problem between small cities and nearby larger hubs. We further describe a simple, non-algorithmic ride-sharing solution developed by taxi drivers in a small county-level Chinese city (Chiping, Shandong Province). This solution provides low cost, highly-effective mobility services from the small city to the province's capital city (Jinan). Users' perceptions and concerns of such a system are also discussed. Our work contributes to the CSCW literature on effective ride-sharing innovations built by people in low resource and rural areas, as well as to a broader understanding of computing beyond major cities' urban centers.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359268"}, {"primary_key": "2898119", "vector": [], "sparse_vector": [], "title": "Culturally-Embedded Visual Literacy: A Study of Impression Management via Emoticon, Emoji, Sticker, and Meme on Social Media in China.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Social media is often analyzed as a \"front stage\" where social media users perform their identities and manage impressions through various forms of user-generated content: text, picture, video, as well as visual expression such as emoticon, emoji, sticker, and meme. Previous scholarship has suggested various functions of visual expression in supporting computer-mediated communication. In this study, we move beyond the utility perspective to ask what constitutes the literacy to utilize visual expression for impression management. Towards this goal, we conducted an interview study with 30 social media users in China. We found that visual literacy intersects with its cultural context. Our interviewees used text and visual expressions in sophisticated, skillful ways and tailored for different audiences in order to construct desired images. They also interacted with audience memory in order to perform their uniqueness and creativity. Lastly, we discuss culturally-embedded visual literacy and provide implications for design.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359170"}, {"primary_key": "2898122", "vector": [], "sparse_vector": [], "title": "Finding Language Classes: Designing a Digital Language Wizard with Refugees and Migrants.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "For migrants and refugees, settling in a host country often proves hugely problematic. The ability to communicate marks a significant step in the process of resettlement. The current high number of migrants coming to Europe has therefore meant that the need to improve and increase the availability of language provision has become more pressing. Nevertheless, due to the complexity of financial and legal regulations and of policy restrictions, access to and information about available courses are in practice hard to find, and non-funded volunteer initiatives are scarcely visible at all. To address this problem, a newly developed language-course wizard provides orientation for refugees and migrants. Findings indicate that its visual representation is apt to span a broad range of cultural as well as educational backgrounds. Our work demonstrates the need for a detailed approach to understanding the challenges that refugees and other migrants face in practice. Failing this, services frequently do not reach those they are intended for.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359218"}, {"primary_key": "2898123", "vector": [], "sparse_vector": [], "title": "Did It Have To End This Way?: Understanding The Consistency of Team Fracture.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Was a problematic team always doomed to frustration, or could it have ended another way? In this paper, we study the consistency of team fracture: a loss of team viability so severe that the team no longer wants to work together. Understanding whether team fracture is driven by the membership of the team, or by how their collaboration unfolded, motivates the design of interventions that either identify compatible teammates or ensure effective early interactions. We introduce an online experiment that reconvenes the same team without members realizing that they have worked together before, enabling us to temporarily erase previous team dynamics. Participants in our study completed a series of tasks across multiple teams, including one reconvened team, and privately blacklisted any teams that they would not want to work with again. We identify fractured teams as those blacklisted by half the members. We find that reconvened teams are strikingly polarized by task in the consistency of their fracture outcomes. On a creative task, teams might as well have been a completely different set of people: the same teams changed their fracture outcomes at a random chance rate. On a cognitive conflict and on an intellective task, the team instead replayed the same dynamics without realizing it, rarely changing their fracture outcomes. These results indicate that, for some tasks, team fracture can be strongly influenced by interactions in the first moments of a team's collaboration, and that interventions targeting these initial moments may be critical to scaffolding long-lasting teams.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359311"}, {"primary_key": "2898124", "vector": [], "sparse_vector": [], "title": "Measuring the Effects of Gender on Online Social Conformity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Social conformity occurs when an individual changes their behaviour in line with the majority's expectations. Although social conformity has been investigated in small group settings, the effect of gender - of both the individual and the majority/minority - is not well understood in online settings. Here we systematically investigate the impact of groups' gender composition on social conformity in online settings. We use an online quiz in which participants submit their answers and confidence scores, both prior to and following the presentation of peer answers that are dynamically fabricated. Our results show an overall conformity rate of 39%, and a significant effect of gender that manifests in a number of ways: gender composition of the majority, the perceived nature of the question, participant gender, visual cues of the system, and final answer correctness. We conclude with a discussion on the implications of our findings in designing online group settings, accounting for the effects of gender on conformity.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359247"}, {"primary_key": "2898125", "vector": [], "sparse_vector": [], "title": "The Perpetual Work Life of Crowdworkers: How Tooling Practices Increase Fragmentation in Crowdwork.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Crowdworkers regularly support their work with scripts, extensions, and software to enhance their productivity. Despite their evident significance, little is understood regarding how these tools affect crowdworkers' quality of life and work. In this study, we report findings from an interview study (N=21) aimed at exploring the tooling practices used by full-time crowdworkers on Amazon Mechanical Turk. Our interview data suggests that the tooling utilized by crowdworkers (1) strongly contributes to the fragmentation of microwork by enabling task switching and multitasking behavior; (2) promotes the fragmentation of crowdworkers' work-life boundaries by relying on tooling that encourages a 'work-anywhere' attitude; and (3) aids the fragmentation of social ties within worker communities through limited tooling access. Our findings have implications for building systems that unify crowdworkers' work practice in support of their productivity and well-being.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359126"}, {"primary_key": "2898126", "vector": [], "sparse_vector": [], "title": "Understanding Family Collaboration Around Lightweight Modification of Everyday Objects in the Home.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The internet-of-things (IoT) carries substantial costs by urging households to replace their possessions with new, internet connected versions of everyday objects. Beyond financial, these costs include waste, work to arrange and orchestrate objects to suit households, and that of acquiring new skills. Upcycling domestic objects could offer households greater discretion and control over these costs by supporting the ability to tailor IoT to the home. To understand how households might do this, we conducted a home study with 10 diverse American households over 7 days to surface the approaches families are likely to use when tailoring IoT to their existing possessions. We asked family members to enact their process using endowed sticker props---IoT Stickers---to modify objects in their home. We develop a framework of how families make light weight modifications of domestic possessions, summarize trends of their object modifications, and describe the burdens such a system could impose.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359287"}, {"primary_key": "2898129", "vector": [], "sparse_vector": [], "title": "The Work of Bilingual Parent-Education Liaisons: Assembling Information Patchworks for Immigrant Parents.", "authors": ["<PERSON><PERSON>-<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we examine the information work that bilingual parent-education liaisons perform to create connections towards assisting immigrant parents in the United States. As part of formal and informal educational institutions, liaisons operate between different social worlds---within and beyond the domain of education---to maximize immigrant parents' engagement in their children's academic lives. Drawing upon ethnographic fieldwork with low-income Latino immigrants and their liaisons, we explore how liaisons leverage technology, content, and people to align these worlds; bringing them closer to mutual understanding. We borrow inspiration from the analytical lens of seams suggested by <PERSON><PERSON><PERSON> to explore how liaisons manage the \"seams'' between worlds. This analysis provides a rich and unique perspective of the work that successfully engages immigrant parents with their children's education, as well as the tensions that challenge that engagement. This understanding of the work that liaisons do with and across seams provides insights to inform design directions for technology that could facilitate and amplify their efforts. Finally, we highlight how exploring the seams that liaisons work with can inform the field of Computer-Supported Collaborative Work (CSCW) on the role of technology in mediation.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359288"}, {"primary_key": "2898130", "vector": [], "sparse_vector": [], "title": "Agent, Gatekeeper, Drug Dealer: How Content Creators Craft Algorithmic Personas.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Online content creators have to manage their relations with opaque, proprietary algorithms that platforms employ to rank, filter, and recommend content. How do content creators make sense of these algorithms and what does that teach us about the roles that algorithms play in the social world? We take the case of YouTube because of its widespread use and the spaces for collective sense-making and mutual aid that content creators (YouTubers) have built within the last decade. We engaged with YouTubers in one-on-one interviews, performed content analysis on YouTube videos that discuss the algorithm, and conducted a wiki survey on YouTuber online groups. This triangulation of methodologies afforded us a rich understanding of content creators' understandings, priorities, and wishes as they relate to the algorithm. We found that YouTubers assign human characteristics to the algorithm to explain its behavior; what we have termed algorithmic personas. We identify three main algorithmic personas on YouTube: Agent, Gatekeeper, and Drug Dealer. We propose algorithmic personas as a conceptual framework that describes the new roles that algorithmic systems take on in the social world. As we face new challenges around the ethics and politics of algorithmic platforms such as YouTube, algorithmic personas describe roles that are familiar and can help develop our understanding of algorithmic power relations and accountability mechanisms.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359321"}, {"primary_key": "2898131", "vector": [], "sparse_vector": [], "title": "Estimating Attention Flow in Online Video Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Online videos have shown tremendous increase in Internet traffic. Most video hosting sites implement recommender systems, which connect the videos into a directed network and conceptually act as a source of pathways for users to navigate. At present, little is known about how human attention is allocated over such large-scale networks, and about the impacts of the recommender systems. In this paper, we first construct the Vevo network -- a YouTube video network with 60,740 music videos interconnected by the recommendation links, and we collect their associated viewing dynamics. This results in a total of 310 million views every day over a period of 9 weeks. Next, we present large-scale measurements that connect the structure of the recommendation network and the video attention dynamics. We use the bow-tie structure to characterize the Vevo network and we find that its core component (23.1% of the videos), which occupies most of the attention (82.6% of the views), is made out of videos that are mainly recommended among themselves. This is indicative of the links between video recommendation and the inequality of attention allocation. Finally, we address the task of estimating the attention flow in the video recommendation network. We propose a model that accounts for the network effects for predicting video popularity, and we show it consistently outperforms the baselines. This model also identifies a group of artists gaining attention because of the recommendation network. Altogether, our observations and our models provide a new set of tools to better understand the impacts of recommender systems on collective social attention.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359285"}, {"primary_key": "2898132", "vector": [], "sparse_vector": [], "title": "Should We Use an Abstract Comic Form to Persuade?: Experiments with Online Charitable Donation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper examines the use of the abstract comic form for persuading online charitable donations. Persuading individuals to contribute to charitable causes online is hard and responses to the appeals are typically low; charitable donations share the structure of public goods dilemmas where the rewards are distant and non-exclusive. In this paper, we examine if comics in abstract form are more persuasive than in the plain text form. Drawing on a rich literature on comics, we synthesized a three-panel abstract comic to create our appeal. We conducted a between-subject study with 307 participants from Amazon Mechanical Turk on the use of abstract comic form to appeal for charitable donations. As part of our experimental procedure, we sought to persuade individuals to contribute to a real charity focused on Autism research with monetary costs. We compared the average amount of donation to the charity under three conditions: the plain text message, an abstract comic that includes the plain text, and an abstract comic that additionally includes the social proof. We use Bayesian modeling to analyze the results, motivated by model transparency and its use in small-sized studies. Our experiments reveal that the message in abstract comic form elicited significantly more donations than text form (medium to large effect size=0.59). Incorporating social proof in the abstract comic message did not show a significant effect. Our studies have design implications: non-profits and governmental agencies interested in alleviating public goods dilemmas that share a similar structure to our experiment (single-shot task, distant, non-exclusive reward) ought to consider including messages in the abstract comic form as part of their online fund-raising campaign.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359177"}, {"primary_key": "2898133", "vector": [], "sparse_vector": [], "title": "&quot;I Think You&apos;ll Like It&quot;: Modelling the Online Purchase Behavior in Social E-commerce.", "authors": ["<PERSON><PERSON>", "Zhenyu Han", "Jinghua Piao", "<PERSON>"], "summary": "Understanding the roles of social factors in online purchase behavior has been a long standing research problem. The recently emerging social e-commerce platforms leverage the stimulated word-of-mouth effect to promote the sales of items, which offers a peek into the complex interplay between the social influence and online purchasing behavior. In this paper, we investigate this problem on a full-scale purchase behavior dataset from one of the leading social e-commerce platforms, Beidian. Specifically, we conduct a comparison study between the social e-commerce and conventional e-commerce that are both integrated in Beidian to examine how social factors affect user's purchase behavior. We reveal that social e-commerce leads to a 3.09~10.37 times higher purchase conversion rate compared with the conventional settings, which indicates users make purchase with significantly fewer item explorations. Then, we propose and validate four primary mechanisms that contribute to the efficient purchase conversion: better matching, social enrichment, social proof and price sensitivity. Moreover, we identify several behavioral indicators that are able to measure the effect of these mechanisms, based on which we design an accurate predictive model (AUC=0.7738) for user's purchase decision. These results combine to shed light on how to understand and model the purchase behavior in social e-commerce.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359167"}, {"primary_key": "2898134", "vector": [], "sparse_vector": [], "title": "LEAP: Scaffolding Collaborative Learning of Community Health Workers in India.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Despite a crucial role in providing public health services, Community Health Workers (CHWs) remain disadvantaged in receiving effective skill-building opportunities. Due to the lack of health experts and appropriate infrastructure, it becomes challenging to provide training on a regular basis. Our aim is to investigate opportunities for designing technology-supported collaborative learning to compensate for the limited availability of instructors. We designed a mobile learning-based peer-led educational intervention, and conducted an eight week long between-group study with 120 CHWs across four districts of Delhi, India. We found that CHWs were able to participate and use the system on their own leading to significant knowledge gains and increased desire to learn. With little guidance, CHWs exhibited benefits of collaborative learning in terms of positive interdependence on each other and use of interpersonal skills. The informal peer learning environment encouraged CHWs to have discourses on deeper societal aspects e.g. their role in society.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359271"}, {"primary_key": "2898135", "vector": [], "sparse_vector": [], "title": "Feedpal: Understanding Opportunities for Chatbots in Breastfeeding Education of Women in India.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Use of chatbots in different spheres of life is continuously increasing since a couple of years. We attempt to understand the potential of chatbots for breastfeeding education by conducting an Wizard-of-Oz experiment with 22 participants. Our participants included breastfeeding mothers and community health workers from the slum areas of Delhi, India. We prototyped our chatbot as an interactive question-answering application and analyzed users' interaction patterns, perceptions, and contexts of use. The chatbot use cases emerged primarily as the first line of support. The participants, especially the mothers, were enthusiastic with the opportunity to ask questions and get reliable answers. We also observed the influencing role of female relative, e.g. mothers-in-law, in breastfeeding practices. Our analysis of user information-seeking suggests that a majority of questions (88%) are of nature that can be answered by a chatbot application. We further observe that the queries are embedded deeply into myths and existing belief systems. Therefore requiring the designers to focus on subtle aspects for providing information such as positive reinforcement and contextual sensitivity. Further, we discuss, different societal and ethical issues associated with Chatbot usage for a public health topic such as breastfeeding education.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359272"}, {"primary_key": "2898136", "vector": [], "sparse_vector": [], "title": "When Knowledge Network is Social Network: Understanding Collaborative Knowledge Transfer in Workplace.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In workplaces, knowledge has the property of being individually kept and collectively shared. It is distributed and embedded among members but needs to be transferred and pooled through knowledge networks for successful collaboration. In order to understand and embody such networks, we employed a mixture of field observations, social network analysis, and in-depth interviews to examine the practices of knowledge transfer in a local financial institute (N=32). Results from the analyses show that there is a discrepancy between inbound and outbound knowledge transfer, and also suggest that expertise or social relation cannot solely account for members' decision on whom to ask questions and share knowledge. We also found that scaffolding workers to connect to the right person with metaknowledge of the knowledge network is important. Our findings point to research and design opportunities to support knowledge transfer at the collaborative level within a knowledge community.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359266"}, {"primary_key": "2898137", "vector": [], "sparse_vector": [], "title": "Privacy Perceptions and Designs of Bystanders in Smart Homes.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "As the Internet of Things (IoT) devices make their ways into people's homes, traditional dwellings are turning into smart homes. While prior empirical studies have examined people's privacy concerns of smart homes and their desired ways of mitigating these concerns, the focus was primarily on the end users or device owners. Our research investigated the privacy perceptions and design ideas of smart home bystanders, i.e., people who are not the owners nor the primary users of smart home devices but can potentially be involved in the device usage, such as other family members or guests. We conducted focus groups and co-design activities with eighteen participants. We identified three impacting factors of bystanders' privacy perceptions (e.g., perceived norms) and a number of design factors to mitigate their privacy concerns (e.g., asking for device control). We highlighted bystanders' needs for privacy and controls, as well as the tension of privacy expectations between the owners/users and the bystanders in smart homes. We discussed how future designs can better support and balance the privacy needs of different stakeholders in smart homes.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359161"}, {"primary_key": "2898138", "vector": [], "sparse_vector": [], "title": "Context- and Data-driven Satisfaction Analysis of User Interface Adaptations Based on Instant User Feedback.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Modern User Interfaces (UIs) are increasingly expected to be plastic, in the sense that they retain a constant level of usability, even when subjected to context (platform, user, and environment) changes at runtime. Adaptive UIs have been promoted as a solution for context variability due to their ability to automatically adapt to the context-of-use at runtime. However, evaluating end-user satisfaction of adaptive UIs is a challenging task, because the UI and the context-of-use are both constantly changing. Thus, an acceptance analysis of UI adaptation features should consider the context-of-use when adaptations are triggered. Classical usability evaluation methods like usability tests mostly focus on a posteriori analysis techniques and do not fully exploit the potential of collecting implicit and explicit user feedback at runtime. To address this challenge, we present an on-the-fly usability testing solution that combines continuous context monitoring together with collection of instant user feedback to assess end-user satisfaction of UI adaptation features. The solution was applied to a mobile Android mail application, which served as basis for a usability study with 23 participants. A data-driven end-user satisfaction analysis based on the collected context information and user feedback was conducted. The main results show that most of the triggered UI adaptation features were positively rated.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3331161"}, {"primary_key": "2898139", "vector": [], "sparse_vector": [], "title": "Evaluating Multimodal Feedback for Assembly Tasks in a Virtual Environment.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Operating power tools over extended periods of time can pose significant risks to humans, due to the strong forces and vibrations they impart to the limbs. Telemanipulation systems can be employed to minimize these risks, but may impede effective task performance due to the reduced sensory cues they typically convey. To address this shortcoming, we explore the benefits of augmenting these cues with the addition of audition, vibration, and force feedback, and evaluate them on users' performance in a VR mechanical assembly task employing a simulated impact wrench. Our research focuses on the utility of vibrotactile feedback, rendered as a simplified and attenuated version of the vibrations experienced while operating an actual impact wrench. We investigate whether such feedback can serve to enhance the operator's awareness of the state of the tool, as well as a proxy for the forces experienced during collisions and coupling, while operating the tool an actual impact wrench. Results from our user study comparing feedback modalities confirm that the introduction of vibrotactile, in addition to auditory feedback can significantly improve user performance as assessed by completion time. However, the addition of force feedback to these two modalities did not further improve performance.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3331163"}, {"primary_key": "2898142", "vector": [], "sparse_vector": [], "title": "Intergroup Contact in the Wild: Characterizing Language Differences between Intergroup and Single-group Members in NBA-related Discussion Forums.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Qin Lv"], "summary": "Intergroup contact has long been considered as an effective strategy to reduce prejudice between groups. However, recent studies suggest that exposure to opposing groups in online platforms can exacerbate polarization. To further understand the behavior of individuals who actively engage in intergroup contact in practice, we provide a large-scale observational study of intragroup behavioral differences between members with and without intergroup contact. We leverage the existing structure of NBA-related discussion forums on Reddit to study the context of professional sports. We identify fans of each NBA team as members of a group and trace whether they have intergroup contact. Our results show that members with intergroup contact use more negative and abusive language in their affiliated group than those without such contact, after controlling for activity levels. We further quantify different levels of intergroup contact and show that there may exist nonlinear mechanisms regarding how intergroup contact relates to intragroup behavior. Our findings provide complementary evidence to experimental studies in a novel context and also shed light on possible reasons for the different outcomes in prior studies.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359295"}, {"primary_key": "2898143", "vector": [], "sparse_vector": [], "title": "The Roles Bots Play in Wikipedia.", "authors": ["<PERSON><PERSON> (Nico) <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Bots are playing an increasingly important role in the creation of knowledge in Wikipedia. In many cases, editors and bots form tightly knit teams. Humans develop bots, argue for their approval, and maintain them, performing tasks such as monitoring activity, merging similar bots, splitting complex bots, and turning off malfunctioning bots. Yet this is not the entire picture. Bots are designed to perform certain functions and can acquire new functionality over time. They play particular roles in the editing process. Understanding these roles is an important step towards understanding the ecosystem, and designing better bots and interfaces between bots and humans. This is important for understanding Wikipedia along with other kinds of work in which autonomous machines affect tasks performed by humans. In this study, we use unsupervised learning to build a nine category taxonomy of bots based on their functions in English Wikipedia. We then build a multi-class classifier to classify 1,601 bots based on labeled data. We discuss different bot activities, including their edit frequency, their working spaces, and their software evolution. We use a model to investigate how bots playing certain roles will have differential effects on human editors. In particular, we build on previous research on newcomers by studying the relationship between the roles bots play, the interactions they have with newcomers, and the ensuing survival rate of the newcomers.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359317"}, {"primary_key": "2898144", "vector": [], "sparse_vector": [], "title": "Managing Stress: The Needs of Autistic Adults in Video Calling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Video calling (VC) aims to create multi-modal, collaborative environments that are \"just like being there.\" However, we found that autistic individuals, who exhibit atypical social and cognitive processing, may not share this goal. We interviewed autistic adults about their perceptions of VC compared to other computer- mediated communications (CMC) and face-to-face interactions. We developed a neurodiversity-sensitive model of CMC that describes how stressors such as sensory sensitivities, cognitive load, and anxiety, contribute to their preferences for CMC channels. We learned that they apply significant effort to construct coping strategies to support their sensory, cognitive, and social needs. These strategies include moderating their sensory inputs, creating mental models of conversation partners, and attempting to mask their autism by adopting neurotypical behaviors. Without effective strategies, interviewees experience more stress, have less capacity to interpret verbal and non-verbal cues, and feel less empowered to participate. Our findings reveal critical needs for autistic users. We suggest design opportunities to support their ability to comfortably use VC, and in doing so, point the way towards making VC more comfortable for all.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359236"}, {"primary_key": "2898145", "vector": [], "sparse_vector": [], "title": "Designing a Social Matching System to Connect Academic Researchers with Local Community Collaborators.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "This paper reports on efforts to design a social matching system that instigates collaborative research across multiple fields of practice, in this instance: researchers from academia and organizations in their local geographic community. A qualitative study is presented about university researchers and the design of their profile pages for the system. Findings show that university researchers prefer profile page designs that enable them to demonstrate a willingness to adapt to non-academic partners, such as by de-emphasizing esoteric markers of expertise like scholarly publications and clarifying their resources and goals. Some also wish to circumvent potential bias by omitting information about their name, physical appearance, and academic department. However, these desired omissions raise questions about how to design for sufficient distinction between profile pages and the presentation of a unique professional identity. Implications are discussed for the design of social marching systems for collaboration.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3361117"}, {"primary_key": "2898146", "vector": [], "sparse_vector": [], "title": "Paying Crowd Workers for Collaborative Work.", "authors": ["Greg d&apo<PERSON>;<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Collaborative crowdsourcing tasks allow crowd workers to solve problems that they could not handle alone, but worker motivation in these tasks is not well understood. In this paper, we study how to motivate groups of workers by paying them equitably. To this end, we characterize existing collaborative tasks based on the types of information available to crowd workers. Then, we apply concepts from equity theory to show how fair payments relate to worker motivation, and we propose two theoretically grounded classes of fair payments. Finally, we run two experiments using an audio transcription task on Amazon Mechanical Turk to understand how workers perceive these payments. Our results show that workers recognize fair and unfair payment divisions, but are biased toward payments that reward them more. Additionally, our data suggests that fair payments could lead to a small increase in worker effort. These results inform the design of future collaborative crowdsourcing tasks.", "published": "2019-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3359227"}]