[{"primary_key": "3641564", "vector": [], "sparse_vector": [], "title": "Running with Technology: Evaluating the Impact of Interacting with Wearable Devices on Running Movement.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The use of wearable devices during running has become commonplace. Although there is ongoing research on interaction techniques for use while running, the effects of the resulting interactions on the natural movement patterns have received little attention so far. While previous studies on pedestrians reported increased task load and reduced walking speed while interacting, running movement further restricts interaction and requires minimizing interferences, e.g. to avoid injuries and maximize comfort. In this paper, we aim to shed light on how interacting with wearable devices affects running movement. We present results from a motion-tracking study (N=12) evaluating changes in movement and task load when users interact with a smartphone, a smartwatch, or a pair of smartglasses while running. In our study, smartwatches required less effort than smartglasses when using swipe input, resulted in less interference with the running movement and were preferred overall. From our results, we infer a number of guidelines regarding interaction design targeting runners.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130966"}, {"primary_key": "3641435", "vector": [], "sparse_vector": [], "title": "Camera Based Two Factor Authentication Through Mobile and Wearable Devices.", "authors": ["Mozhgan Azimpourkivi", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce <PERSON><PERSON><PERSON>, a novel, camera based two factor authentication solution for mobile and wearable devices. A quick and familiar user action of snapping a photo is sufficient for <PERSON><PERSON><PERSON> to simultaneously perform a graphical password authentication and a physical token based authentication, yet it does not require any expensive, uncommon hardware. <PERSON><PERSON><PERSON> establishes trust based on both the knowledge and possession of an arbitrary physical object readily accessible to the user, called trinket. Users choose their trinkets similar to setting a password, and authenticate by presenting the same trinket to the camera. The fact that the object is the trinket, is secret to the user. <PERSON><PERSON><PERSON> extracts robust, novel features from trinket images, and leverages a supervised learning classifier to effectively address inconsistencies between images of the same trinket captured in different circumstances. <PERSON><PERSON><PERSON> achieved a false accept rate below 0.09% in a brute force attack with 14.3 million authentication attempts, generated with 40,000 trinket images that we captured and collected from public datasets. We identify master images, that match multiple trinkets, and study techniques to reduce their impact. In a user study with 42 participants over 8 days in 3 sessions we found that <PERSON><PERSON><PERSON> outperforms text based passwords on memorability, speed, and user preference. Furthermore, <PERSON><PERSON><PERSON> was easily discoverable by new users and accurate under field use. Users were able to remember their trinkets 2 and 7 days after registering them, without any practice between the 3 test dates.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3131904"}, {"primary_key": "3641475", "vector": [], "sparse_vector": [], "title": "RFID Light Bulb: Enabling Ubiquitous Deployment of Interactive RFID Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Radio-Frequency Identification (RFID) technology has the potential to provide inexpensive, wireless, battery-free connectivity and interactivity for objects that are traditionally not instrumented. However, these systems have not seen widespread deployment outside warehouses and supply chains, owing to the complexity of installing bulky RFID readers, antennas, and their supporting power and network infrastructure. In this work, we leverage advances in semiconductor optics, RF antenna design and system integration to create a hybrid RFID reader and smart LED lamp, in the form factor of a standard light bulb. This makes deploying RFID readers literally as easy as screwing in a light bulb. We explore the home-scale RFID interactions enabled by these smart bulbs, including infrastructure monitoring, localization and guided navigation, and standalone lighting effects.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3090077"}, {"primary_key": "3641534", "vector": [], "sparse_vector": [], "title": "Understanding the Role of Places and Activities on Mobile Phone Interaction and Usage Patterns.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "User interaction patterns with mobile apps and notifications are generally complex due to the many factors involved. However a deep understanding of what influences them can lead to more acceptable applications that are able to deliver information at the right time. In this paper, we present for the first time an in-depth analysis of interaction behavior with notifications in relation to the location and activity of users. We conducted an in-situ study for a period of two weeks to collect more than 36,000 notifications, 17,000 instances of application usage, 77,000 location samples, and 487 days of daily activity entries from 26 students at a UK university. Our results show that users’ attention towards new notifications and willingness to accept them are strongly linked to the location they are in and in minor part to their current activity. We consider both users’ receptivity and attentiveness, and we show that different response behaviors are associated to different locations. These findings are fundamental from a design perspective since they allow us to understand how certain types of places are linked to specific types of interaction behavior. This information can be used as a basis for the development of novel intelligent mobile applications and services.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3131901"}, {"primary_key": "3641535", "vector": [], "sparse_vector": [], "title": "MyTraces: Investigating Correlation and Causation between Users&apos; Emotional States and Mobile Phone Interaction.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Most of the existing work concerning the analysis of emotional states and mobile phone interaction has been based on correlation analysis. In this paper, for the first time, we carry out a causality study to investigate the causal links between users’ emotional states and their interaction with mobile phones, which could provide valuable information to practitioners and researchers. The analysis is based on a dataset collected in-the-wild. We recorded 5,118 mood reports from 28 users over a period of 20 days. Our results show that users’ emotions have a causal impact on different aspects of mobile phone interaction. On the other hand, we can observe a causal impact of the use of specific applications, reflecting the external users’ context, such as socializing and traveling, on happiness and stress level. This study has profound implications for the design of interactive mobile systems since it identifies the dimensions that have causal effects on users’ interaction with mobile phones and vice versa. These findings might lead to the design of more effective computing systems and services that rely on the analysis of the emotional state of users, for example for marketing and digital health applications.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130948"}, {"primary_key": "3641538", "vector": [], "sparse_vector": [], "title": "Detecting Emerging Activity-Based Working Traits through Wearable Technology.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Sarfraz Nawaz"], "summary": "A recent trend in corporate real-estate is Activity-Based Working (ABW). The ABW concept removes designated desks but offers different work settings designed to support typical work activities. In this context there is still a need for objective data to understand the implications of these design decisions. We aim to contribute by using automated data collection to study how ABW’s principles impact office usage and dynamics. To this aim we analyse team dynamics and employees’ tie strength in relation to space usage and organisational hierarchy using data collected with wearable devices in a company adopting ABW principles. Our findings show that the office fosters interactions across team boundaries and among the lower levels of the hierarchy suggesting a strong lateral communication. Employees also tend to have low space exploration on a daily basis which is instead more prevalent during an average week and strong social clusters seem to be resisting the ABW principles of space dynamics. With the availability of two additional data sets about social encounters in traditional offices we highlight traits emerging from the application of ABW’s principles. In particular, we observe how the absence of designated desks might be responsible for more rapid dynamics inside the office. In more general terms, this work opens the door to new and scalable technology-based methodologies to study dynamic office usage and social interactions.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130951"}, {"primary_key": "3641544", "vector": [], "sparse_vector": [], "title": "Conquering the City: Understanding perceptions of Mobility and Human Territoriality in Location-based Mobile Games.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "With the increasing popularity of mobile video games, game designers and developers are starting to integrate geolocation into video games. Popular location-based games such as Ingress or Pokémon Go have millions of users, yet little is known about how the use of such games influences the nature of a user’s interaction with other users and their physical surroundings. To investigate how location-based games are integrated into a player’s daily life, how they influence a player’s mobility through the city, their perception of places and the role of human territoriality in this context, we have developed a location-based mobile multiplayer game called CityConqueror. In this paper, we present CityConqueror and the results of a study, which has focused on participants playing the game over a period of two weeks. The findings show that location-based games can be designed to give the player the illusion of playing in the context of the “real” world rather than a virtual or hybrid game reality. Our findings also suggest that location-based games can have a strong influence on a player’s mobility and perception of urban space and that human territoriality can be expressed through location-based games. Based on our findings we propose a series of design implications for the design of mobile location-based games.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130955"}, {"primary_key": "3641560", "vector": [], "sparse_vector": [], "title": "Sensing Cold-Induced Situational Impairments in Mobile Interaction Using Battery Temperature.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous work has highlighted the detrimental effect of cold ambience on fine-motor skills during interaction with mobile devices. In this work, we develop a method to infer changes in finger temperature of smartphone users without the need for specialised hardware. Specifically, we demonstrate that smartphone battery temperature is a reliable gauge for determining changes to finger temperature. In addition, we show that the behaviour of smartphone battery temperature in cold settings is consistent across different smartphone models and battery configurations. Our method can be used to determine cold-induced situational impairments, and trigger interface adaptations during mobile interaction.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130963"}, {"primary_key": "3641566", "vector": [], "sparse_vector": [], "title": "VRShop: A Mobile Interactive Virtual Reality Shopping Environment Combining the Benefits of On- and Offline Shopping.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this work, we explored the main characteristics of on- and offline shops with regard to customer shopping behavior and frequency. Thus, we designed and implemented an immersive virtual reality (VR) online shopping environment. We tried to maintain the benefits of online shops, like search functionality and availability, while simultaneously focusing on shopping experience and immersion. By touching the third dimension, VR provides a more advanced form of visualization, which can increase the customer’s satisfaction and thus shopping experience. We further introduced the Virtual Reality Shopping Experience (VRSE) model based on customer satisfaction, task performance and user preference. A case study of a first VR shop prototype was conducted and evaluated with respect to the VRSE model. The results showed that the usability and user experience of our system is above average overall. In summary, searching for a product in a WebVR online shop using speech input in combination with VR output proved to be the best regarding user performance (speed, error rate) and preference (usability, user experience, immersion, motion sickness).", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130967"}, {"primary_key": "3641589", "vector": [], "sparse_vector": [], "title": "From Intermittent to Ubiquitous: Enhancing Mobile Access to Online Social Networks with Opportunistic Optimization.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Accessing online social networks in situations with intermittent Internet connectivity is a challenge. We have designed a context-aware mobile system to enable efficient offline access to online social media by prefetching, caching and disseminating content opportunistically when signal availability is detected. This system can measure, crowdsense and predict network characteristics, and then use these predictions of mobile network signal to schedule cellular access or device-to-device (D2D) communication. We propose several opportunistic optimization schemes to enhance controlled crowdsensing, resource constrained mobile prefetch, and D2D transmissions impacted by individual selfishness. Realistic tests and large-scale trace analysis show our system can achieve a significant improvement over existing approaches in situations where users experience intermittent cellular service or disrupted network connection.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130979"}, {"primary_key": "3641432", "vector": [], "sparse_vector": [], "title": "Drone Near Me: Exploring Touch-Based Human-Drone Interaction.", "authors": ["Parastoo Abtahi", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Personal drones are becoming more mainstream and are used for a variety of tasks, such as delivery and photography. The exposed blades in conventional drones raise serious safety concerns. To address this, commercial drones have been moving towards a safe-to-touch design or have increased safety by adding propeller guards. The affordances of safe-to-touch drones enable new types of touch-based human-drone interaction. Various applications have been explored, such as augmented sports and haptic feedback in virtual reality; however, it is unclear if individuals feel comfortable using direct touch and manipulation when interacting with safe-to-touch drones. A previous elicitation study showed how users naturally interact with drones. We replicated this study with an unsafe and a safe-to-touch drone, to find out if participants will instinctively use touch as a means of interacting with the safe-to-touch drone. We found that 58% of the participants used touch, and across all tasks 39% of interactions were touch-based. The proposed touch interactions were in agreement for 67% of the tasks, and users reported that interacting with the safe-to-touch drone was significantly less mentally demanding than the unsafe drone.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130899"}, {"primary_key": "3641438", "vector": [], "sparse_vector": [], "title": "Every Byte Counts: Selective Prefetching for Mobile Applications.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Quick responses to user actions are instrumental to the success of mobile applications. To ensure such responsiveness, applications often prefetch data objects before the user requests them. This way, applications can avoid the need to retrieve data through slow network connections during user interactions. However, prefetches may also harm. They increase launch delays and might cause substantial amounts of data to be downloaded through energy-hungry, cellular connections. In this paper, we propose EBC, a novel algorithm to schedule application prefetches and overcome their drawbacks. EBC computes application usage probabilities and traffic volume estimates to determine when and for which applications prefetches should be triggered. Thereby, it applies different strategies depending on whether a cellular or Wi-Fi connection is available. We evaluate the performance of EBC on two publicly available, large-scale data sets: LiveLab and Device Analyzer. Our results show that EBC can lower launch delays and ensure freshness of application content. At the same time, it reduces the amount of data downloaded through cellular connections. On the Device Analyzer data set, for instance, EBC achieves a 10% reduction in cellular traffic and a 36% better average freshness with respect to its closest competitor.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3090052"}, {"primary_key": "3641440", "vector": [], "sparse_vector": [], "title": "EarBit: Using Wearable Sensors to Detect Eating Episodes in Unconstrained Environments.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Temiloluwa Prioleau", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Chronic and widespread diseases such as obesity, diabetes, and hypercholesterolemia require patients to monitor their food intake, and food journaling is currently the most common method for doing so. However, food journaling is subject to self-bias and recall errors, and is poorly adhered to by patients. In this paper, we propose an alternative by introducing EarBit, a wearable system that detects eating moments. We evaluate the performance of inertial, optical, and acoustic sensing modalities and focus on inertial sensing, by virtue of its recognition and usability performance. Using data collected in a simulated home setting with minimum restrictions on participants' behavior, we build our models and evaluate them with an unconstrained outside-the-lab study. For both studies, we obtained video footage as ground truth for participants activities. Using leave-one-user-out validation, EarBit recognized all the eating episodes in the semi-controlled lab study, and achieved an accuracy of 90.1% and an F1-score of 90.9% in detecting chewing instances. In the unconstrained, outside-the-lab evaluation, EarBit obtained an accuracy of 93% and an F1-score of 80.1% in detecting chewing instances. It also accurately recognized all but one recorded eating episodes. These episodes ranged from a 2 minute snack to a 30 minute meal.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130902"}, {"primary_key": "3641441", "vector": [], "sparse_vector": [], "title": "Gamification of Mobile Experience Sampling Improves Data Quality and Quantity.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Experience Sampling Method is used to capture high-quality in situ data from study participants. This method has become popular in studies involving smartphones, where it is often adapted to motivate participation through the use of gamification techniques. However, no work to date has evaluated whether gamification actually affects the quality and quantity of data collected through Experience Sampling. Our study systematically investigates the effect of gamification on the quantity and quality of experience sampling responses on smartphones. In a field study, we combine event contingent and interval contingent triggers to ask participants to describe their location. Subsequently, participants rate the quality of these entries by playing a game with a purpose. Our results indicate that participants using the gamified version of our ESM software provided significantly higher quality responses, slightly increased their response rate, and provided significantly more data on their own accord. Our findings suggest that gamifying experience sampling can improve data collection and quality in mobile settings.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130972"}, {"primary_key": "3641442", "vector": [], "sparse_vector": [], "title": "Bites&apos;n&apos;Bits: Inferring Eating Behavior from Contextual Mobile Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>-<PERSON>"], "summary": "We collect and analyze mobile data about everyday eating occasions to study eating behavior in relation to its context (time, location, social context, related activities and physical activity). Our contributions are three-fold. First, we deployed a data collection campaign with 122 Swiss university students, resulting in 1208 days of food data, 3414 meal occasions, 1034 snacking occasions, 5097 photos, and 998 days of physical activity. Second, we analyzed the collected data and report findings associated to the compliance, snacks vs. meals patterns, physical activity, and contextual differences between snacks and meals. Third, we addressed a novel ubicomp task, namely the classification of eating occasions (meals vs. snacks) in everyday life. We show that a machine learning method using time of day, time since last intake, and location is able to discriminate eating occasions with 84% accuracy, which significantly outperforms a baseline method based only on time.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161161"}, {"primary_key": "3641445", "vector": [], "sparse_vector": [], "title": "A Comparative Evaluation of Spatial Targeting Behaviour Patterns for Finger and <PERSON><PERSON><PERSON> on Mobile Touchscreen Devices.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Models of 2D targeting error patterns have been applied as a valuable computational tool for analysing finger touch behaviour on mobile devices, improving touch accuracy and inferring context. However, their use in stylus input is yet unexplored. This paper presents the first empirical study and analyses of such models for tapping with a stylus. In a user study (N = 28), we collected targeting data on a smartphone, both for stationary use (sitting) and walking. We compare targeting patterns between index finger input and three stylus variations -- two stylus widths and nib types as well as the addition of a hover cursor. Our analyses reveal that stylus targeting patterns are user-specific, and that offset models improve stylus tapping accuracy, but less so than for finger touch. Input method has a stronger influence on targeting patterns than mobility, and stylus width is more influential than the hover cursor. Stylus models improve finger accuracy as well, but not vice versa. The extent of the stylus accuracy advantage compared to the finger depends on screen location and mobility. We also discuss patterns related to mobility and gliding of the stylus on the screen. We conclude with implications for target sizes and offset model applications.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161160"}, {"primary_key": "3641448", "vector": [], "sparse_vector": [], "title": "SeismoWatch: Wearable Cuffless Blood Pressure Monitoring Using Pulse Transit Time.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The current norm for measuring blood pressure (BP) at home is using an automated BP cuff based on oscillometry. Despite providing a viable and familiar method of tracking BP at home, oscillometric devices can be both cumbersome and inaccurate with the inconvenience of the hardware typically limiting measurements to once or twice per day. To address these limitations, a wrist-watch BP monitor was developed to measure BP through a simple maneuver: holding the watch against the sternum to detect micro-vibrations of the chest wall associated with the heartbeat. As a pulse wave propagates from the heart to the wrist, an accelerometer and optical sensor on the watch measure the travel time -- pulse transit time (PTT) -- to estimate BP. In this paper, we conducted a study to test the accuracy and repeatability of our device. After calibration, the diastolic pressure estimations reached a root-mean-square error of 2.9 mmHg. The watch-based system significantly outperformed (p&lt;0.05) conventional pulse arrival time (PAT) based wearable blood pressure estimations -- the most commonly used method for wearable BP sensing in the existing literature and commercial devices. Our device can be a convenient means for wearable BP monitoring outside of clinical settings in both health-conscious and hypertensive populations.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130905"}, {"primary_key": "3641455", "vector": [], "sparse_vector": [], "title": "Detecting Gaze Towards Eyes in Natural Social Interactions and Its Use in Child Assessment.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Eye contact is a crucial element of non-verbal communication that signifies interest, attention, and participation in social interactions. As a result, measures of eye contact arise in a variety of applications such as the assessment of the social communication skills of children at risk for developmental disorders such as autism, or the analysis of turn-taking and social roles during group meetings. However, the automated measurement of visual attention during naturalistic social interactions is challenging due to the difficulty of estimating a subject’s looking direction from video. This paper proposes a novel approach to eye contact detection during adult-child social interactions in which the adult wears a point-of-view camera which captures an egocentric view of the child’s behavior. By analyzing the child’s face regions and inferring their head pose we can accurately identify the onset and duration of the child’s looks to their social partner’s eyes. We introduce the Pose-Implicit CNN, a novel deep learning architecture that predicts eye contact while implicitly estimating the head pose. We present a fully automated system for eye contact detection that solves the sub-problems of end-to-end feature learning and pose estimation using deep neural networks. To train our models, we use a dataset comprising 22 hours of 156 play session videos from over 100 children, half of whom are diagnosed with Autism Spectrum Disorder. We report an overall precision of 0.76, recall of 0.80, and an area under the precision-recall curve of 0.79, all of which are significant improvements over existing methods.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3131902"}, {"primary_key": "3641460", "vector": [], "sparse_vector": [], "title": "DualBlink: A Wearable Device to Continuously Detect, Track, and Actuate Blinking For Alleviating Dry Eyes and Computer Vision Syndrome.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Increased visual attention, such as during computer use leads to less blinking, which can cause dry eyes—the leading cause of computer vision syndrome. As people spend more time looking at screens on mobile and desktop devices, computer vision syndrome is becoming epidemic in today's population, leading to blurry vision, fatigue, and a reduced quality of life. One way to alleviate dry eyes is increased blinking. In this paper, we present a series of glasses-mounted devices that track the wearer's blink rate and, upon absent blinks, trigger blinks through actuation: light flashes, physical taps, and small puffs of air near the eye. We conducted a user study to evaluate the effectiveness of our devices and found that air puff and physical tap actuations result in a 36% increase in participants’ average blink rate. Air puff thereby struck the best compromise between effective blink actuations and low distraction ratings from participants. In a follow-up study, we found that high intensity, short puffs near the eye were most effective in triggering blinks while receiving only low-rated distraction and invasiveness ratings from participants. We conclude this paper with two miniaturized and self-contained DualBlink prototypes, one integrated into the frame of a pair of glasses and the other one as a clip-on for existing glasses. We believe that DualBlink can serve as an always-available and viable option to treat computer vision syndrome in the future.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3053330"}, {"primary_key": "3641462", "vector": [], "sparse_vector": [], "title": "Building Cognition-Aware Systems: A Mobile Toolkit for Extracting Time-of-Day Fluctuations of Cognitive Performance.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "People’s alertness fluctuates across the day: at some times we are highly focused while at others we feel unable to concentrate. So far, extracting fluctuation patterns has been time and cost-intensive. Using an in-the-wild approach with 12 participants, we evaluated three cognitive tasks regarding their adequacy as a mobile and economical assessment tool of diurnal changes in mental performance. Participants completed the five-minute test battery on their smartphones multiple times a day for a period of 1-2 weeks. Our results show that people’s circadian rhythm can be obtained under unregulated non-laboratory conditions. Along with this validation study, we release our test battery as an open source library for future work towards cognition-aware systems as well as a tool for psychological and medical research. We discuss ways of integrating the toolkit and possibilities for implicitly measuring performance variations in common applications. The ability to detect systematic patterns in alertness levels will allow cognition-aware systems to provide in-situ assistance in accordance with users’ current cognitive capabilities and limitations.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3132025"}, {"primary_key": "3641463", "vector": [], "sparse_vector": [], "title": "PocketThumb: a Wearable Dual-Sided Touch Interface for Cursor-based Control of Smart-Eyewear.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present PocketThumb, a wearable touch interface for smart-eyewear that is embedded into the fabrics of the front trouser pocket. The interface is reachable from outside and inside of the pocket to allow for a combined dual-sided touch input. The user can control an absolute cursor with their thumb sliding along the fabric from the inside, while at the same time tapping or swiping with fingers from the outside to perform joint gestures. This allows for resting the hand in a comfortable and quickly accessible position, while performing interaction with a high expressiveness that is feasible in mobile scenarios. In a cursor-based target selection study, we found that our introduced dual-sided touch interaction is significantly faster in comparison to common single-sided absolute as well as relative touch interaction (~19%, resp. ~23% faster). The effect is largest in the mobile conditions standing and walking (up to ~31% faster).", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3090055"}, {"primary_key": "3641467", "vector": [], "sparse_vector": [], "title": "Low-resource Multi-task Audio Sensing for Mobile and Embedded Devices via Shared Deep Neural Network Representations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Continuous audio analysis from embedded and mobile devices is an increasingly important application domain. More and more, appliances like the Amazon Echo, along with smartphones and watches, and even research prototypes seek to perform multiple discriminative tasks simultaneously from ambient audio; for example, monitoring background sound classes (e.g., music or conversation), recognizing certain keywords (‘<PERSON> <PERSON><PERSON>' or ‘<PERSON><PERSON>'), or identifying the user and her emotion from speech. The use of deep learning algorithms typically provides state-of-the-art model performances for such general audio tasks. However, the large computational demands of deep learning models are at odds with the limited processing, energy and memory resources of mobile, embedded and IoT devices. In this paper, we propose and evaluate a novel deep learning modeling and optimization framework that specifically targets this category of embedded audio sensing tasks. Although the supported tasks are simpler than the task of speech recognition, this framework aims at maintaining accuracies in predictions while minimizing the overall processor resource footprint. The proposed model is grounded in multi-task learning principles to train shared deep layers and exploits, as input layer, only statistical summaries of audio filter banks to further lower computations. We find that for embedded audio sensing tasks our framework is able to maintain similar accuracies, which are observed in comparable deep architectures that use single-task learning and typically more complex input layers. Most importantly, on an average, this approach provides almost a 2.1× reduction in runtime, energy, and memory for four separate audio sensing tasks, assuming a variety of task combinations.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3131895"}, {"primary_key": "3641472", "vector": [], "sparse_vector": [], "title": "Automated Ski Velocity and Jump Length Determination in Ski Jumping Based on Unobtrusive and Wearable Sensors.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>rn M<PERSON>"], "summary": "Although ski jumping is a widely investigated sport, competitions and training sessions are rarely supported by state-of-the-art technology. Supporting technologies could focus on a continuous velocity determination and visualization for competitions as well as on an analysis of the velocity development and the jump length for training sessions. In the literature, there are several approaches for jump analysis. However, the majority of these approaches aim for a biomechanical analysis instead of a support system for frequent use. They do not fulfill the requirements of unobtrusiveness and usability that are necessary for a long-term application in competitions and training. In this paper, we propose an algorithm for ski velocity calculation and jump length determination based on the processing of unobtrusively obtained ski jumping data. Our algorithm is evaluated with data from eleven athletes in two different acquisitions. The results show an error of the velocity measurement at take-off of (which equals -3.0 % ± 4.7 % in reference to the estimated average take-off velocity) compared to a light barrier system. The error of the jump length compared to a video-based system is 0.8 m ± 2.9 m (which equals 0.9 % ± 3.4 % of the average jump length of the training jumps in this work). Although our proposed system does not outperform existing camera-based methods of jump length measurements at competitions, it provides an affordable and unobtrusive support for competitions and has the potential to simplify analyses in standard training.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130918"}, {"primary_key": "3641474", "vector": [], "sparse_vector": [], "title": "Ensembles of Deep LSTM Learners for Activity Recognition using Wearables.", "authors": ["Yu <PERSON>", "<PERSON>"], "summary": "Recently, deep learning (DL) methods have been introduced very successfully into human activity recognition (HAR) scenarios in ubiquitous and wearable computing. Especially the prospect of overcoming the need for manual feature design combined with superior classification capabilities render deep neural networks very attractive for real-life HAR applications. Even though DL-based approaches now outperform the state-of-the-art in a number of recognition tasks, still substantial challenges remain. Most prominently, issues with real-life datasets, typically including imbalanced datasets and problematic data quality, still limit the effectiveness of activity recognition using wearables. In this paper we tackle such challenges through Ensembles of deep Long Short Term Memory (LSTM) networks. LSTM networks currently represent the state-of-the-art with superior classification performance on relevant HAR benchmark datasets. We have developed modified training procedures for LSTM networks and combine sets of diverse LSTM learners into classifier collectives. We demonstrate that Ensembles of deep LSTM learners outperform individual LSTM networks and thus push the state-of-the-art in human activity recognition using wearables. Through an extensive experimental evaluation on three standard benchmarks (Opportunity, PAMAP2, Skoda) we demonstrate the excellent recognition capabilities of our approach and its potential for real-life applications of human activity recognition.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3090076"}, {"primary_key": "3641481", "vector": [], "sparse_vector": [], "title": "Auth &apos;n&apos; Scan: Opportunistic Photoplethysmography in Mobile Fingerprint Authentication.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent commodity smartphones have biometric sensing capabilities, allowing their daily use for authentication and identification. This increasing use of biometric systems motivates us to design an opportunistic way to sense user's additional physiological or behavioral data. We define this concurrent physiological or behavioral data sensing during biometric authentication or identification as dual-purpose biometrics. As an instance of dual-purpose biometrics, we develop photoplethysmography (PPG) sensing during mobile fingerprint authentication, called Auth ‘n’ Scan. Our system opportunistically extracts cardiovascular information, such as a heart rate and its variability, while users perform phone unlock of a smartphone. To achieve this sensing, our Auth ‘n’ Scan system attaches four PPG units around a fingerprint sensor. The system also performs noise removal and signal selection to accurately estimate cardiovascular information. This paper presents the hardware implementation and signal processing algorithm of our Auth ‘n’ Scan prototype. We also report our system evaluations with 10 participants, showing that, despite a little low precision (a standard deviation of 3--7), estimation of heart rates with high accuracy (under a mean error of 1) is possible from PPG data of five seconds and longer if their baseline information is given. We discuss the feasibility of opportunistic PPG sensing in mobile fingerprint authentication.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161189"}, {"primary_key": "3641484", "vector": [], "sparse_vector": [], "title": "A Large-Scale, Long-Term Analysis of Mobile Device Usage Characteristics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Today, mobile devices like smartphones and tablets have become an indispensable part of people's lives, posing many new questions e.g., in terms of interaction methods, but also security. In this paper, we conduct a large scale, long term analysis of mobile device usage characteristics like session length, interaction frequency, and daily usage in locked and unlocked state with respect to location context and diurnal pattern. Based on detailed logs from 29,279 mobile phones and tablets representing a total of 5,811 years of usage time, we identify and analyze 52.2 million usage sessions with some participants providing data for more than four years. Our results show that context has a highly significant effect on both frequency and extent of mobile device usage, with mobile phones being used twice as much at home compared to in the office. Interestingly, devices are unlocked for only 46 % of the interactions. We found that with an average of 60 interactions per day, smartphones are used almost thrice as often as tablet devices (23), while usage sessions on tablets are three times longer, hence are used almost for an equal amount of time throughout the day. We conclude that usage session characteristics differ considerably between tablets and smartphones. These results inform future approaches to mobile interaction as well as security.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3090078"}, {"primary_key": "3641485", "vector": [], "sparse_vector": [], "title": "Glabella: Continuously Sensing Blood Pressure Behavior using an Unobtrusive Wearable Device.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We propose Glabella, a wearable device that continuously and unobtrusively monitors heart rates at three sites on the wearer’s head. Our glasses prototype incorporates optical sensors, processing, storage, and communication components, all integrated into the frame to passively collect physiological data about the user without the need for any interaction. Glabella continuously records the stream of reflected light intensities from blood flow as well as inertial measurements of the user’s head. From the temporal differences in pulse events across the sensors, our prototype derives the wearer’s pulse transit time on a beat-to-beat basis. Numerous efforts have found a significant correlation between a person’s pulse transit time and their systolic blood pressure. In this paper, we leverage this insight to continuously observe pulse transit time as a proxy for the behavior of systolic blood pressure levels—at a substantially higher level of convenience and higher rate than traditional blood pressure monitors, such as cuff-based oscillometric devices. This enables our prototype to model the beat-to-beat fluctuations in the user’s blood pressure over the course of the day and record its short-term responses to events, such as postural changes, exercise, eating and drinking, resting, medication intake, location changes, or time of day. During our in-the-wild evaluation, four participants wore a custom-fit Glabella prototype device over the course of five days throughout their daytime job and regular activities. Participants additionally measured their radial blood pressure three times an hour using a commercial oscillometric cuff. Our analysis shows a high correlation between the pulse transit times computed on our devices with participants’ heart rates (mean r = 0.92, SE = 0.03, angular artery) and systolic blood pressure values measured using the oscillometric cuffs (mean r = 0.79, SE = 0.15, angular-superficial temporal artery, considering participants’ self-administered cuff-based measurements as ground truth). Our results indicate that Glabella has the potential to serve as a socially-acceptable capture device, requiring no user input or behavior changes during regular activities, and whose continuous measurements may prove informative to physicians as well as users’ self-tracking activities.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3132024"}, {"primary_key": "3641492", "vector": [], "sparse_vector": [], "title": "Portable+: A Ubiquitous And Smart Way Towards Comfortable Energy Savings.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "An air conditioner (AC) consumes a significant proportion of the total household power consumption. Primarily used in developing countries, decentralised AC has an inbuilt thermostat to cool the room to a temperature, manually set by the users. However, residents are incapable of specifying their goal through these thermostats - maximise their comfort or save AC energy. State-of-the-art portable thermostats emulate AC remotes and assist occupants in remotely changing the thermostat temperature, through their smartphones. We propose extending such thermostats to portable+ by adding a Comfort-Energy Trade-off (CET) knob, realised through an optimisation framework to allow users to balance their comfort and the savings without worrying about the right set temperature. Analysis based on real data, collected from a controlled experiment (across two rooms for two weeks) and an in-situ deployment (across five rooms for three months), indicates that portable+ thermostats can reduce residents’ discomfort by 23% (CET selection for maximal comfort) and save 26% energy when CET is set for maximising savings.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3090079"}, {"primary_key": "3641495", "vector": [], "sparse_vector": [], "title": "Towards Wearable Everyday Body-Frame Tracking using Passive RFIDs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We introduce RF-Wear, an accurate and wearable solution to track movements of a user's body using passive RFIDs embedded in their clothing. RF-Wear processes wireless signals reflected off these tags to a compact single-antenna RFID reader in the user's pocket. In doing so, RF-Wear enables a first-of-its-kind body-frame tracking mechanism that is lightweight and convenient for day-to-day use, without relying on external infrastructure. At the heart of RF-Wear is a novel primitive that computes angles between different parts of the user's body using the RFID tags attached to them. RF-Wear achieves this by treating groups of RFID tags as an array of antennas whose orientation can be computed accurately relative to the handheld reader. By computing the orientation of individual body parts, we demonstrate how RF-Wear reconstructs the real-time posture of the user's entire body frame. Our solution overcomes multiple challenges owing to the interactions of wireless signals with the body, the 3-D nature of human joints and the flexibility of fabric on which RFIDs are placed. We implement and evaluate a prototype of RF-Wear on commercial RFID readers and tags and demonstrate its performance in body-frame tracking. Our results reveal a mean error of 8--12° in tracking angles at joints that rotate along one degree-of-freedom, and 21°- azimuth, 8°- elevation for joints supporting two degrees-of-freedom.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161199"}, {"primary_key": "3641498", "vector": [], "sparse_vector": [], "title": "EyePACT: Eye-Based Parallax Correction on Touch-Enabled Interactive Displays.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The parallax effect describes the displacement between the perceived and detected touch locations on a touch-enabled surface. Parallax is a key usability challenge for interactive displays, particularly for those that require thick layers of glass between the screen and the touch surface to protect them from vandalism. To address this challenge, we present EyePACT, a method that compensates for input error caused by parallax on public displays. Our method uses a display-mounted depth camera to detect the user's 3D eye position in front of the display and the detected touch location to predict the perceived touch location on the surface. We evaluate our method in two user studies in terms of parallax correction performance as well as multi-user support. Our evaluations demonstrate that EyePACT (1) significantly improves accuracy even with varying gap distances between the touch surface and the display, (2) adapts to different levels of parallax by resulting in significantly larger corrections with larger gap distances, and (3) maintains a significantly large distance between two users' fingers when interacting with the same object. These findings are promising for the development of future parallax-free interactive displays.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161168"}, {"primary_key": "3641500", "vector": [], "sparse_vector": [], "title": "Technology Supported Behavior Restriction for Mitigating Self-Interruptions in Multi-device Environments.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Cho", "<PERSON><PERSON><PERSON>"], "summary": "The interruptions people experience may be initiated from digital devices but also from oneself, an action which is termed “self-interruption.” Prior work mostly focused on understanding work-related self-interruptions and designing tools for mitigating them in work contexts. However, self-interruption to off-tasks (e.g., viewing social networking sites, and playing mobile games) has received little attention in the HCI community thus far. We conducted a formative study about self-interruptions to off-tasks and coping strategies in multi-device working environments. Off-task usage was considered a serious roadblock to productivity, and yet, the habitual usage and negative triggers made it challenging to manage off-task usage. To mitigate these concerns, we developed “PomodoLock,” a self-interruption management tool that allows users voluntarily to set a timer for a fixed period, during which it selectively blocks interruption sources across multiple devices. To understand the effect of restricting access to self-interruptive sources such as applications and websites, we conducted a three-week field trial (n=40) where participants were asked to identify disrupting apps and sites to be blocked, but the multi-device blocking feature was only provided to the experimental group. Our study results showed the perceived coercion and the stress of the experimental group were lower despite its behavioral restriction with multi-device blocking. Qualitative study results from interviews and surveys confirm that multi-device blocking significantly reduced participants’ mental effort for managing self-interruptions, thereby leading to a reduction in the overall stress level. The findings suggest that when the coerciveness of behavioral restriction is appropriately controlled, coercive design can positively assist users in achieving their goals.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130932"}, {"primary_key": "3641501", "vector": [], "sparse_vector": [], "title": "UbiSwarm: Ubiquitous Robotic Interfaces and Investigation of Abstract Motion as a Display.", "authors": ["<PERSON>", "<PERSON>"], "summary": "As robots increasingly enter our everyday life, we envision a future in which robots are ubiquitous and interact with both ourselves and our environments. This paper introduces the concept of ubiquitous robotic interfaces (URIs), multi-robot interfaces capable of mobility, manipulation, sensing, display and interaction. URIs interact directly with the user and indirectly through surrounding objects. A key aspect of URIs is their ability to display information to users either by collectively forming shapes or through their movements. In this paper, we focus on the use of URIs to display information in ubiquitous settings. We first investigate the use of abstract motion as a display for URIs by studying human perception of abstract multi-robot motion. With ten small robots, we produced 42 videos of bio-inspired abstract motion by varying three parameters (7 x 2 x 3): bio-inspired behavior, speed and smoothness. In a crowdsourced between-subjects study, 1067 subjects were recruited to watch the videos and describe their perception through Likert scales and free text. Study results suggest that different bio-inspired behaviors elicit significantly different responses in arousal, dominance, hedonic and pragmatic qualities, animacy, urgency and willingness to attend. On the other hand, speed significantly affects valence, arousal, hedonic quality, urgency and animacy while smoothness affects hedonic quality, animacy, attractivity and likeability. We discuss how these results inform URI designers to formulate appropriate motion for different interaction scenarios and use these results to derive our own example applications using our URI platform, UbiSwarm.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130931"}, {"primary_key": "3641502", "vector": [], "sparse_vector": [], "title": "Let&apos;s FOCUS: Mitigating Mobile Phone Use in College Classrooms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Minsam Ko", "<PERSON><PERSON><PERSON>"], "summary": "With the increasingly frequent appearance of mobile phones in college classrooms, there have been growing concerns regarding their negative aspects including distractive off-task multitasking. In this work, we design and evaluate Let’s FOCUS, a software-based intervention service that assists college students in self-regulating their mobile phone use in classrooms. Our preliminary survey study (with 47 professors and 283 students) reveals that it is critical to encourage voluntary participation by framing intervention as a learning tool and to raise awareness regarding appropriate mobile phone usage by establishing social norms in colleges. Let’s FOCUS introduces a virtual limiting space for each class (or a virtual classroom) where the students can explicitly restrict their mobile phone use voluntarily. Furthermore, it promotes students’ willing participation by leveraging social facilitation and context-aware reminders associated with virtual classrooms. We conducted a campus-wide campaign for approximately six weeks to evaluate the feasibility of the proposed approach. The results confirm that 379 students used the app to limit 9,335 hours of mobile phone usage over 233 classrooms. Let’s FOCUS was used in diverse learning contexts and for different purposes and its social learning and context-awareness features significantly motivated prolonged participation. We present the design considerations of software-based intervention.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130928"}, {"primary_key": "3641508", "vector": [], "sparse_vector": [], "title": "Exploring Context-Aware User Interfaces for Smartphone-Smartwatch Cross-Device Interaction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this study, we explore context-aware cross-device interactions between a smartphone and smartwatch. We present 24 contexts, and then examine and prioritize suitable user interfaces (UIs) for each. In addition, we present example applications, including a map, notification management system, multitasking application, music player, and video chat application, each of which has its own context-aware UIs. To support these context-aware UIs, we investigate the performance of our context recognizer in which recognition is based on machine-learning using the accelerometers in a smartphone and smartwatch. We conduct seven different evaluations using four machine-learning algorithms: J48 decision tree, sequential minimal optimization (SMO)-based support vector machine (SVM), random forest, and multilayer perceptron. With each algorithm, we conduct a long-interval experiment to examine the level of accuracy at which each context is recognized using data previously collected for training. The results show that SMO-based SVM is suitable for recognizing the 24 contexts considered in this study.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130934"}, {"primary_key": "3641510", "vector": [], "sparse_vector": [], "title": "hEYEbrid: A hybrid approach for mobile calibration-free gaze estimation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce hEYEbrid, a calibration-free method for spontaneous and long-term eye gaze tracking, with competitive gaze estimation. It is based on a hybrid concept that combines infrared eye images with corneal imaging. For this, two eye cameras are mounted on a glasses frame. In this way, the pupil can be tracked quickly with high precision. This information is translated into the corneal image, which is used to create a connection to the environment, acting like a scene camera. In a user study with 20 participants, we evaluated our approach against an extended version of the system, called 3C-hEYEbrid, and a state-of-the-art head-mounted Pupil Labs eye tracker. We show that hEYEbrid provides accurate gaze estimation in unconstrained environments and is robust against calibration drift (e.g. caused by taking off and putting on the device). In addition, we present a mobile and wearable implementation of hEYEbrid and 3C-hEYEbrid, that is also usable with a monocular Pupil Labs eye tracker. It connects the head-mounted device to a mobile phone, enabling gaze estimation in real time. hEYEbrid represents a significant step towards pervasive gaze-based interfaces.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161166"}, {"primary_key": "3641511", "vector": [], "sparse_vector": [], "title": "Flower-Pop: Facilitating Casual Group Conversations With Multiple Mobile Devices.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Yea-<PERSON><PERSON> Row", "<PERSON><PERSON>ung Son", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We explore the potential use of mobile devices as a collaborative sensing system that can proactively mediate casual group conversations. In this study, we aim to investigate (i) the impacts of a mobile system's passive and active conversation facilitation and (ii) the ways in which sociocultural aspects that affect casual group conversation should be considered in the design of proactive mobile systems. Toward this goal, we developed Flower-Pop, a mobile system that monitors group conversations and visualizes interaction patterns using metaphorical expressions based on blossoms. This system provides passive facilitation as well as active facilitation modes such as proactive conversation visualization and photo sharing. The active modes can encourage inactive participants to share photos and select random people to speak. Focusing on Korea, our field study showed that Flower-<PERSON>'s mediation created smooth topic/speaker transitions and encouraged less-active speakers to better engage in group conversation. We also found that the sociocultural aspects of casual group conversation, such as the location's characteristics, social relations, and the group's interests, affected participants' use of the Flower-Pop system. Based on our findings, we discuss methods for designing mobile systems for conversation facilitation and outline how opportune sociocultural factors could be identified based on mobile devices.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161170"}, {"primary_key": "3641512", "vector": [], "sparse_vector": [], "title": "PrivacyStreams: Enabling Transparency in Personal Data Processing for Mobile Apps.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Smartphone app developers often access and use privacy-sensitive data to create apps with rich and meaningful interactions. However, it can be challenging for auditors and end-users to know what granularity of data is being used and how, thereby hindering assessment of potential risks. Furthermore, developers lack easy ways of offering transparency to users regarding how personal data is processed, even if their intentions are to make their apps more privacy friendly. To address these challenges, we introduce PrivacyStreams, a functional programming model for accessing and processing personal data as a stream. PrivacyStreams is designed to make it easy for developers to make use of personal data while simultaneously making it easier to analyze how that personal data is processed and what granularity of data is actually used. We present the design and implementation of PrivacyStreams, as well as several user studies and experiments to demonstrate its usability, utility, and support for privacy.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130941"}, {"primary_key": "3641515", "vector": [], "sparse_vector": [], "title": "Mining User Reviews for Mobile App Comparisons.", "authors": ["<PERSON><PERSON><PERSON>", "Baoxiong Ji<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "As the number of mobile apps keeps increasing, users often need to compare many apps, in order to choose one that best fits their needs. Fortunately, as there are so many users sharing an app market, it is likely that some other users with the same preferences have already made the comparisons and shared their opinions. For example, a user may state that an app is better in power consumption than another app in a review, then the review would help other users who care about battery life while choosing apps. This paper presents a method to identify comparative reviews for mobile apps from an app market, which can be used to provide fine-grained app comparisons based on different topics. According to experiments on 5 million reviews from Google Play and manual assessments on 900 reviews, our method is able to identify opinions accurately and provide meaningful comparisons between apps, which could in turn help users find desired apps based on their preferences.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130935"}, {"primary_key": "3641521", "vector": [], "sparse_vector": [], "title": "Supporting Social Interactions with an Expressive Heart Rate Sharing Application.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The present work explores the social dynamics of expressive biosignals: leveraging wearable technologies to introduce sensed physiological data as a means of clarifying the emotional or psychological processes underlying our subjective experiences. We developed an Android application that linked to a wearable heart rate sensor and allowed for the direct sharing and real-time broadcasting of users’ heart rate via text messaging. We deployed this application in a two-week field study to investigate the contextual triggers, perceptions, and consequences of users’ sharing behaviors. The study (N=13) utilized a combination of Experience Sampling Methodology (ESM) and qualitative interviews to discover the situations in which users were more or less likely to share their heart rate with contacts, and the subsequent interactions that occurred after sharing. The results revealed that participants used heart rate sharing as a means to express emotions and provide daily updates, as well as simply a novel and playful form of communication. They reported a variety of communicative consequences of their sharing as well as specific logistical and psychological barriers to sharing. The implications of these results for the design of expressive biosignal sharing systems for supporting positive social interactions are discussed.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130943"}, {"primary_key": "3641526", "vector": [], "sparse_vector": [], "title": "Inferring Correlation between User Mobility and App Usage in Massive Coarse-grained Data Traces.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Qing Cao"], "summary": "With the rapid growth in smartphone usage, it has been more and more important to understand the patterns of mobile data consumption by users. In this paper, we present an empirical study of the correlation between user mobility and app usage patterns. In particular, we focus on users' moving speed as the key mobility metric, and try to answer the following question: are there any notable relations between moving speed and the app usage patterns? Our study is based on a real-world, large-scale dataset of 2G phone network data request records. A critical challenge was that the raw data records are rather coarse-grained. More specifically, unlike GPS traces, the exact locations of users were not readily available. We inferred users' approximate locations according to their interactions with nearby cell towers, whose locations were known. We proposed a novel method to filter out noises and perform reliable speed estimation. We verify our methodology with out of sample data and show its improvement in speed estimation accuracy. We then examined several aspects of mobile data usage patterns, including the data volume, the access frequency, and the app categories, to reveal the correlation between these patterns and users' moving speed. Experimental results based on our large-scale real-world datasets revealed that users under different mobility categories not only have different smartphone usage motivations but also have different ways of using their smartphones.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161171"}, {"primary_key": "3641528", "vector": [], "sparse_vector": [], "title": "TestAWARE: A Laboratory-Oriented Testing Tool for Mobile Context-Aware Applications.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Although mobile context instrumentation frameworks have simplified the development of mobile context-aware applications, it remains challenging to test such applications. In this paper, we present TestAWARE that enables developers to systematically test context-aware applications in laboratory settings. To achieve this, TestAWARE is able to download, replay and emulate contextual data on either physical devices or emulators. To support both white -box and black-box testing, TestAWARE has been implemented as a novel structure with a mobile client and code library. In blackbox testing scenarios, developers can manage data replay through the mobile client, without writing testing scripts or modifying the source code of the targeted application. In white-box testing scenarios, developers can manage data replay and test functional/non-functional properties of the targeted application by writing testing scripts using the code library. We evaluated TestAWARE by quantifying its maximal data replay speed, and by conducting a user study with 13 developers. We show that TestAWARE can overcome data synchronisation challenges, and found that PC-based emulators can replay data significantly faster than physical smartphones and tablets. The user study highlights the usefulness of TestAWARE in the systematic testing of mobile context-aware applications in laboratory settings.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130945"}, {"primary_key": "3641529", "vector": [], "sparse_vector": [], "title": "SCAN: Multi-Hop Calibration for Mobile Sensor Arrays.", "authors": ["Balz Maag", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Urban air pollution monitoring with mobile, portable, low-cost sensors has attracted increasing research interest for their wide spatial coverage and affordable expenses to the general public. However, low-cost air quality sensors not only drift over time but also suffer from cross-sensitivities and dependency on meteorological effects. Therefore calibration of measurements from low-cost sensors is indispensable to guarantee data accuracy and consistency to be fit for quantitative studies on air pollution. In this work we propose sensor array network calibration (SCAN), a multi-hop calibration technique for dependent low-cost sensors. SCAN is applicable to sets of co-located, heterogeneous sensors, known as sensor arrays, to compensate for cross-sensitivities and dependencies on meteorological influences. SCAN minimizes error accumulation over multiple hops of sensor arrays, which is unattainable with existing multi-hop calibration techniques. We formulate SCAN as a novel constrained least-squares regression and provide a closed-form expression of its regression parameters. We theoretically prove that SCAN is free from regression dilution even in presence of measurement noise. In-depth simulations demonstrate that SCAN outperforms various calibration techniques. Evaluations on two real-world low-cost air pollution sensor datasets comprising 66 million samples collected over three years show that SCAN yields 16% to 60% lower error than state-of-the-art calibration techniques.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3090084"}, {"primary_key": "3641537", "vector": [], "sparse_vector": [], "title": "HeartSense: Ubiquitous Accurate Multi-Modal Fusion-based Heart Rate Estimation Using Smartphones.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Heart rate is one of the most important vital signals for personal health tracking. A number of smartphone-based heart rate estimation systems have been proposed over the years. However, they either depend on special hardware sensors or suffer from the high noise due to the weakness of the heart signals, affecting their accuracy in many practical scenarios. Inspired by medical studies about the heart motion mechanics, we propose the HeartSense heart rate estimation system. Specifically, we show that the gyroscope sensor is the most sensitive sensor for measuring the heart rate. To further counter noise and handle different practical scenarios, we introduce a novel quality metric that allows us to fuse the different gyroscope axes in a probabilistic framework to achieve a robust and accurate estimate. We have implemented and evaluated our system on different Android phones. Results using 836 experiments on different subjects in practical scenarios with a side-by-side comparison with other systems show that HeartSense can achieve 1.03 bpm median absolute error for heart rate estimation. This is better than the state-of-the-art by more than 147% in median error, highlighting HeartSense promise as a ubiquitous system for medical and personal well-being applications.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3132028"}, {"primary_key": "3641542", "vector": [], "sparse_vector": [], "title": "FallDeFi: Ubiquitous Fall Detection using Commodity Wi-Fi Devices.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Falling or tripping among elderly people living on their own is recognized as a major public health worry that can even lead to death. Fall detection systems that alert caregivers, family members or neighbours can potentially save lives. In the past decade, an extensive amount of research has been carried out to develop fall detection systems based on a range of different detection approaches, i.e, wearable and non-wearable sensing and detection technologies. In this paper, we consider an emerging non-wearable fall detection approach based on WiFi Channel State Information (CSI). Previous CSI based fall detection solutions have considered only time domain approaches. Here, we take an altogether different direction, time-frequency analysis as used in radar fall detection. We use the conventional Short-Time Fourier Transform (STFT) to extract time-frequency features and a sequential forward selection algorithm to single out features that are resilient to environment changes while maintaining a higher fall detection rate. When our system is pre-trained, it has a 93% accuracy and compared to RTFall and CARM, this is a 12% and 15% improvement respectively. When the environment changes, our system still has an average accuracy close to 80% which is more than a 20% to 30% and 5% to 15% improvement respectively.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161183"}, {"primary_key": "3641545", "vector": [], "sparse_vector": [], "title": "Beyond Interruptibility: Predicting Opportune Moments to Engage Mobile Phone Users.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Many of today's mobile products and services engage their users proactively via push notifications. However, such notifications are not always delivered at the right moment, therefore not meeting products' and users' expectations. To address this challenge, we aim at developing an intelligent mobile system that automatically infers moments in which users are open to engage with suggested content. To inform the development of such a system, we carried out a field study with 337 mobile phone users. For 4 weeks, participants ran a study application on their primary phones. They were tasked to frequently report their current mood via a notification-administered experience-sampling questionnaire. In this study, however, we analyze whether they voluntarily engaged with content that we offered at the bottom of that questionnaire. In addition, the study app logged a wide range of data related to their phone use. Based on 120 Million phone-use events and 78,930 questionnaire notifications, we build a machine-learning model that before delivering a notification predicts whether a participant will click on the notification and subsequently engage with the offered content. When compared to a naïve baseline, which emulates current non-intelligent engagement strategies, our model achieves 66.6% higher success rate in its predictions. If the model also considers the user's past behavior, predictions improve 5-fold over the baseline. Based on these findings, we discuss the implications for building an intelligent service that identifies opportune moments for proactive user engagement, while, at the same time, reduces the number of undesirable interruptions.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130956"}, {"primary_key": "3641558", "vector": [], "sparse_vector": [], "title": "Sensei: Sensing Educational Interaction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present <PERSON><PERSON>, the first system designed to understand social interaction and learning in an early-childhood classroom using a distributed sensor network. Our unobtrusive sensors measure proximity between each node in a dynamic range-based mesh network. The sensors can be worn in the shoes, attached to selected landmarks in the classroom, and placed on Montessori materials. This data, accessible to teachers in a web dashboard, enables teachers to derive deeper insights from their classrooms. <PERSON><PERSON> is currently deployed in three Montessori schools and we have evaluated the effectiveness of the system with teachers. Our user studies have shown that the system enhances teachers' capabilities and helps discover insights that would have otherwise been lost. From our evaluation interviews, we have established three major use cases of the system. <PERSON><PERSON> augments teachers' manual observations, helps them plan individualized curriculum for each student, and identifies their needs for more interaction with some children. Further, the anonymized data can be used in large-scale research in early childhood development.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161172"}, {"primary_key": "3641570", "vector": [], "sparse_vector": [], "title": "LoRa Backscatter: Enabling The Vision of Ubiquitous Connectivity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The vision of embedding connectivity into billions of everyday objects runs into the reality of existing communication technologies -- there is no existing wireless technology that can provide reliable and long-range communication at tens of microwatts of power as well as cost less than a dime. While backscatter is low-power and low-cost, it is known to be limited to short ranges. This paper overturns this conventional wisdom about backscatter and presents the first wide-area backscatter system. Our design can successfully backscatter from any location between an RF source and receiver, separated by 475 m, while being compatible with commodity LoRa hardware. Further, when our backscatter device is co-located with the RF source, the receiver can be as far as 2.8 km away. We deploy our system in a 4,800 ft2 (446 m2) house spread across three floors, a 13,024 ft2 (1210 m2) office area covering 41 rooms, as well as a one-acre (4046 m2) vegetable farm and show that we can achieve reliable coverage, using only a single RF source and receiver. We also build a contact lens prototype as well as a flexible epidermal patch device attached to the human skin. We show that these devices can reliably backscatter data across a 3,328 ft2 (309 m2) room. Finally, we present a design sketch of a LoRa backscatter IC that shows that it costs less than a dime at scale and consumes only 9.25 μW of power, which is more than 1000x lower power than LoRa radio chipsets.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130970"}, {"primary_key": "3641574", "vector": [], "sparse_vector": [], "title": "InvisibleEye: Mobile Eye Tracking Using Multiple Low-Resolution Cameras and Learning-Based Gaze Estimation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Analysis of everyday human gaze behaviour has significant potential for ubiquitous computing, as evidenced by a large body of work in gaze-based human-computer interaction, attentive user interfaces, and eye-based user modelling. However, current mobile eye trackers are still obtrusive, which not only makes them uncomfortable to wear and socially unacceptable in daily life, but also prevents them from being widely adopted in the social and behavioural sciences. To address these challenges we present InvisibleEye, a novel approach for mobile eye tracking that uses millimetre-size RGB cameras that can be fully embedded into normal glasses frames. To compensate for the cameras’ low image resolution of only a few pixels, our approach uses multiple cameras to capture different views of the eye, as well as learning-based gaze estimation to directly regress from eye images to gaze directions. We prototypically implement our system and characterise its performance on three large-scale, increasingly realistic, and thus challenging datasets: 1) eye images synthesised using a recent computer graphics eye region model, 2) real eye images recorded of 17 participants under controlled lighting, and 3) eye images recorded of four participants over the course of four recording sessions in a mobile setting. We show that InvisibleEye achieves a top person-specific gaze estimation accuracy of 1.79° using four cameras with a resolution of only 5 × 5 pixels. Our evaluations not only demonstrate the feasibility of this novel approach but, more importantly, underline its significant potential for finally realising the vision of invisible mobile eye tracking and pervasive attentive user interfaces.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130971"}, {"primary_key": "3641578", "vector": [], "sparse_vector": [], "title": "Exploring the Communication of Progress in Home-based Falls Rehabilitation using Exergame Technologies.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Little is known on how to effectively represent rehabilitation progress, over a period of time, using exercise game (exergame) technologies. Progress in falls rehabilitation, which consists of improved performance in balance and muscle strength, is essential to assuring seniors of a reduced risk of falling. In this paper, we build on our previous research into exergames for falls, and we investigate how an exergame system can be used to communicate long-term progress to seniors. Using a multiphase user-centered requirements gathering process, we first investigated stakeholder perspectives regarding progress in self-managed rehabilitation. Following this we describe the home-based evaluation of our prototype exergame system, which highlights rehabilitation progress, with seniors, over a period of 2 months. Progress, in our system is communicated using charts of exercise performance and frequency, as well as medals awarded for achieving longer-term rehabilitation milestones. We report on seniors' opinions and preferences regarding the potential of our exergame system to communicate this rehabilitation progress in a meaningful way. Finally we discuss implications for design, based on our studies, to inform the development of more effective exergame systems for long-term unassisted rehabilitation in the home.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161195"}, {"primary_key": "3641580", "vector": [], "sparse_vector": [], "title": "Enabling Interactive Infrastructure with Body Channel Communication.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Body channel communication (BCC) uses the human body to carry signals, and therefore provides communication and localization that are directly tied to human presence and actions. Previous BCC systems were expensive, could operate only in a laboratory, or only focused on special use cases. We present here an end-to-end BCC system that is designed for ambient intelligence. We introduce the BCC infrastructure that consists of portable devices (e.g., a simple sphere), mobile devices (e.g., a smartwatch-like wristband), and stationary devices (e.g., floor/wall tiles). We also describe the core technology that is used in each of these units. The TouchCom hardware-software platform is a simple transceiver with software-centered processing. The focus on software (even the implementation of the physical layer is based on software) allows the adaptivity that is necessary to operate a BCC-based system in practice. The paper describes the design and a prototype implementation of the TouchCom-based interactive infrastructure and provides evidence that this BCC infrastructure works for different persons and different setups. The system provides moderate bandwidth (about 3.5 kb/s) that is suitable for several usage scenarios like games, localization, and identification. The implemented demonstrations illustrate the benefits these applications gain when touching an object is tied to communication.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161180"}, {"primary_key": "3641582", "vector": [], "sparse_vector": [], "title": "Predicting Symptom Trajectories of Schizophrenia using Mobile Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON> <PERSON><PERSON> <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Continuously monitoring schizophrenia patients’ psychiatric symptoms is crucial for in-time intervention and treatment adjustment. The Brief Psychiatric Rating Scale (BPRS) is a survey administered by clinicians to evaluate symptom severity in schizophrenia. The CrossCheck symptom prediction system is capable of tracking schizophrenia symptoms based on BPRS using passive sensing from mobile phones. We present results from an ongoing randomized control trial, where passive sensing data, self-reports, and clinician administered 7-item BPRS surveys are collected from 36 outpatients with schizophrenia recently discharged from hospital over a period ranging from 2-12 months. We show that our system can predict a symptom scale score based on a 7-item BPRS within ±1.45 error on average using automatically tracked behavioral features from phones (e.g., mobility, conversation, activity, smartphone usage, the ambient acoustic environment) and user supplied self-reports. Importantly, we show our system is also capable of predicting an individual BPRS score within ±1.59 error purely based on passive sensing from phones without any self-reported information from outpatients. Finally, we discuss how well our predictive system reflects symptoms experienced by patients by reviewing a number of case studies.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130976"}, {"primary_key": "3641583", "vector": [], "sparse_vector": [], "title": "XRec: Behavior-Based User Recognition Across Mobile Devices.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "As smartphones and tablets become increasingly prevalent, more customers have multiple devices. The multi-user, multi-device interactions inspire many problems worthy of investigation, among which recognizing users across devices has significant implications on recommendation, advertising and user experience. Unlike the binary classification problem in user identification on a single device, cross-device user recognition is essentially a set partition problem. The app back-end aims to divide user activities on devices hosting the app into groups each associated with one user. In this paper, we present XRec which leverages user behavioral patterns, namely when, where and how a user uses the app, to achieve the recognition. To address the user-device partition problem, we propose a classification-plus-refinement algorithm. To validate our approach, we conduct a field study with an Android app. We instrument the app to collect usage data from real users. We provide proof-of-concept experimental results to demonstrate how XRec can provide added value to mobile apps, with the ability to correctly match a user across multiple devices with 70% recall and 90% precision.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130975"}, {"primary_key": "3641585", "vector": [], "sparse_vector": [], "title": "PupilNet, Measuring Task Evoked Pupillary Response using Commodity RGB Tablet Cameras: Comparison to Mobile, Infrared Gaze Trackers for Inferring Cognitive Load.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Pupillary diameter monitoring has been proven successful at objectively measuring cognitive load that might otherwise be unobservable. This paper compares three different algorithms for measuring cognitive load using commodity cameras. We compare the performance of modified starburst algorithm (from previous work) and propose two new algorithms: 2 Level Snakuscules and a convolutional neural network which we call PupilNet. In a user study with eleven participants, our comparisons show PupilNet outperforms other algorithms in measuring pupil dilation, is robust to various lighting conditions, and robust to different eye colors. We show that the difference between PupilNet and a gold standard head-mounted gaze tracker varies only from -2.6% to 2.8%. Finally, we also show that PupilNet gives similar conclusions about cognitive load during a longer duration typing task.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161164"}, {"primary_key": "3641586", "vector": [], "sparse_vector": [], "title": "SuperpowerGlass: A Wearable Aid for the At-Home Therapy of Children with Autism.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Titas De", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We have developed a system for automatic facial expression recognition running on Google Glass, delivering real-time social cues to children with Autism Spectrum Disorder (ASD). The system includes multiple mechanisms to engage children and their parents, who administer this technology within the home. We completed an at-home design trial with 14 families that used the learning aid over a 3-month period. We found that children with ASD generally respond well to wearing the system at home and opt for the most expressive feedback choice. We further evaluated app usage, facial engagement, and model accuracy. We found that the device can act as a powerful training aid when used periodically in the home, that interactive video content from wearable therapy sessions should be augmented with sufficient context about the content to produce long-term engagement, and that the design of wearable systems for children with ASD should be heavily dependent on the functioning level of the child. We contribute general design implications for developing wearable aids used by children with ASD and other behavioral disorders as well as their parents during at-home parent-administered therapy sessions.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130977"}, {"primary_key": "3641587", "vector": [], "sparse_vector": [], "title": "DeformWear: Deformation Input on Tiny Wearable Devices.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Due to their small surfaces, wearable devices make existing techniques for touch input very challenging. This paper proposes deformation input on a tiny and soft surface as an input modality for wearable computing devices. We introduce DeformWear, tiny wearable devices that leverage single-point deformation input on various body locations. Despite the small input surface, DeformWear enables expressive and precise input using high-resolution pressure, shear, and pinch deformations. We present a first set of interaction techniques for tiny deformation-sensitive wearable devices. They enable fluid interaction in a large input space by combining multiple dimensions of deformation. We demonstrate their use in seven application examples, showing DeformWear as a standalone input device and as a companion device for smartwatches, head-mounted displays, or headphones. Results from a user study demonstrate that these tiny devices allow for precise and expressive interactions on many body locations, in standing and walking conditions.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3090093"}, {"primary_key": "3641597", "vector": [], "sparse_vector": [], "title": "RDeepSense: Reliable Deep Mobile Computing Models with Uncertainty Estimations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Recent advances in deep learning have led various applications to unprecedented achievements, which could potentially bring higher intelligence to a broad spectrum of mobile and ubiquitous applications. Although existing studies have demonstrated the effectiveness and feasibility of running deep neural network inference operations on mobile and embedded devices, they overlooked the reliability of mobile computing models. Reliability measurements such as predictive uncertainty estimations are key factors for improving the decision accuracy and user experience. In this work, we propose RDeepSense, the first deep learning model that provides well-calibrated uncertainty estimations for resource-constrained mobile and embedded devices. RDeepSense enables the predictive uncertainty by adopting a tunable proper scoring rule as the training criterion and dropout as the implicit Bayesian approximation, which theoretically proves its correctness. To reduce the computational complexity, RDeepSense employs efficient dropout and predictive distribution estimation instead of the model ensemble or sampling-based method for inference operations. We evaluate RDeepSense with four mobile sensing applications using Intel Edison devices. Results show that RDeepSense can reduce around 90% of the energy consumption while producing superior uncertainty estimations and preserving at least the same model accuracy compared with other state-of-the-art methods.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161181"}, {"primary_key": "3641599", "vector": [], "sparse_vector": [], "title": "Understanding Group Event Scheduling via the OutWithFriendz Mobile Application.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Qin Lv", "<PERSON><PERSON><PERSON>"], "summary": "The wide adoption of smartphones and mobile applications has brought significant changes to not only how individuals behave in the real world, but also how groups of users interact with each other when organizing group events. Understanding how users make event decisions as a group and identifying the contributing factors can offer important insights for social group studies and more effective system and application design for group event scheduling. In this work, we have designed a new mobile application called OutWithFriendz, which enables users of our mobile app to organize group events, invite friends, suggest and vote on event time and venue. We have deployed OutWithFriendz at both Apple App Store and Google Play, and conducted a large-scale user study spanning over 500 users and 300 group events. Our analysis has revealed several important observations regarding group event planning process including the importance of user mobility, individual preferences, host preferences, and group voting process.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161200"}, {"primary_key": "3641604", "vector": [], "sparse_vector": [], "title": "TouchPower: Interaction-based Power Transfer for Power-as-needed Devices.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The trend toward ubiquitous deployment of electronic devices demands novel low maintenance power schemes to decrease the burden of maintaining such a large number of devices. In this paper, we propose Interaction-based Power Transfer (IPT): a novel power scheme for power-as-needed devices (i.e., devices that only require power during interaction). IPT allows for the removal of built-in batteries on these devices, and to instead be powered up through direct contact interaction with the user (e.g. gripping a mouse, holding a pen). We prove the concept and show the potential of IPT through our TouchPower prototype. TouchPower transfers on-body power to off-body power-as-needed devices through contact between electrodes on a glove worn by the user and those on the target device during the interaction process. We design TouchPower to automatically detect the contact topology at runtime to supply power accordingly, and place electrodes on the glove so that TouchPower is compatible with various interactions with different objects. We also show the methodology of placing electrodes on the device-end, and evaluate it on a mouse and a remote controller. Results show that during interaction, TouchPower is able to provide stable power supply to these devices with only a small sacrifice in regards to interaction naturalness. At last we demonstrate six applications of TouchPower, and discuss the limitations and potential of TouchPower and IPT systems.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130986"}, {"primary_key": "3641430", "vector": [], "sparse_vector": [], "title": "Cognitive Heat: Exploring the Usage of Thermal Imaging to Unobtrusively Estimate Cognitive Load.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Current digital systems are largely blind to users’ cognitive states. Systems that adapt to users’ states show great potential for augmenting cognition and for creating novel user experiences. However, most approaches for sensing cognitive states, and cognitive load specifically, involve obtrusive technologies, such as physiological sensors attached to users’ bodies. This paper present an unobtrusive indicator of the users’ cognitive load based on thermal imaging that is applicable in real-world. We use a commercial thermal camera to monitor a person’s forehead and nose temperature changes to estimate their cognitive load. To assess the effect of different levels of cognitive load on facial temperature we conducted a user study with 12 participants. The study showed that different levels of the Stroop test and the complexity of reading texts affect facial temperature patterns, thereby giving a measure of cognitive load. To validate the feasibility for real-time assessments of cognitive load, we conducted a second study with 24 participants, we analyzed the temporal latency of temperature changes. Our system detected temperature changes with an average latency of 0.7 seconds after users were exposed to a stimulus, outperforming latency in related work that used other thermal imaging techniques. We provide empirical evidence showing how to unobtrusively detect changes in cognitive load in real-time. Our exploration of exposing users to different content types gives rise to thermal-based activity tracking, which facilitates new applications in the field of cognition-aware computing.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130898"}, {"primary_key": "3641431", "vector": [], "sparse_vector": [], "title": "Editorial.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "No abstract available.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3075960"}, {"primary_key": "3641433", "vector": [], "sparse_vector": [], "title": "Using <PERSON><PERSON> Stimuli to Enhance Photo-Sharing in Social Media.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Limited work has been undertaken to show how the emotive ability of thermal stimuli can be used for interaction purposes. One potential application area is using thermal stimuli to influence emotions in images shared online such as social media platforms. This paper presents a two-part study, which examines how the documented emotive property of thermal stimuli can be applied to enhance social media images. Participants in part-one supplied images from their personal collection or social media profiles, and were asked to augment each image with thermal stimuli based on the emotions they wanted to enhance or reduce. Part-one participants were interviewed to understand the effects they wanted augmented images to have. In part-two, these augmented images were perceived by a different set of participants in a simulated social media interface. Results showed strong agreement between the emotions augmented images were designed to evoke and the emotions they actually evoked as perceived by part-two participants. Participants in part-one selected thermal stimuli augmentation intended to modulate valence and arousal in images as a way of enhancing the realism of the images augmented. Part-two results indicate this was achieved as participants perceived thermal stimuli augmentation reduced valence in negative images and modulated valence and arousal in positive images.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3090050"}, {"primary_key": "3641434", "vector": [], "sparse_vector": [], "title": "Wordometer Systems for Everyday Life.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present in this paper a detailed comparison of different algorithms and devices to determine the number of words read in everyday life. We call our system the “Wordometer”. We used three kinds of eye tracking systems in our experiment: mobile video-oculography (MVoG); stationary video-oculography (SVoG); and electro-oculography (EoG). By analyzing the movement of the eyes we were able to estimate the number of words that a user read. Recently, inexpensive eye trackers have appeared on the market. Thus, we undertook a large-scale experiment that compared three devices that can be used for daily reading on a screen: the Tobii Eye X SVoG; the JINS MEME EoG; and the Pupil MVoG. We found that the accuracy of the everyday life devices and professional devices was similar when used with the Wordometer. We analyzed the robustness of the systems for special reading behaviors: rereading and skipping. With the MVoG, SVoG and EoG systems, we obtained estimation errors respectively, 7.2%, 13.0%, and 10.6% in our main experiment. In all our experiments, we obtained 300 recordings by 14 participants, which amounted to 109,097 read words.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161601"}, {"primary_key": "3641436", "vector": [], "sparse_vector": [], "title": "Detecting Drinking Episodes in Young Adults Using Smartphone-based Sensors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Alcohol use in young adults is common, with high rates of morbidity and mortality largely due to periodic, heavy drinking episodes (HDEs). Behavioral interventions delivered through electronic communication modalities (e.g., text messaging) can reduce the frequency of HDEs in young adults, but effects are small. One way to amplify these effects is to deliver support materials proximal to drinking occasions, but this requires knowledge of when they will occur. Mobile phones have built-in sensors that can potentially be useful in monitoring behavioral patterns associated with the initiation of drinking occasions. The objective of our work is to explore the detection of daily-life behavioral markers using mobile phone sensors and their utility in identifying drinking occasions. We utilized data from 30 young adults aged 21-28 with past hazardous drinking and collected mobile phone sensor data and daily Experience Sampling Method (ESM) of drinking for 28 consecutive days. We built a machine learning-based model that is 96.6% accurate at identifying non-drinking, drinking and heavy drinking episodes. We highlight the most important features for detecting drinking episodes and identify the amount of historical data needed for accurate detection. Our results suggest that mobile phone sensors can be used for automated, continuous monitoring of at-risk populations to detect drinking episodes and support the delivery of timely interventions.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3090051"}, {"primary_key": "3641437", "vector": [], "sparse_vector": [], "title": "Warming Up to Cold Start Personalization.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Smart agents face abandonment if they are unable to provide value to the users from the very first interaction. Existing smart agents take time to learn about new users before they can offer them personalized services. We present a method for learning personalization information about users quickly and without placing unnecessary hardship on them. Our method enables smart agents to pick which questions to ask the user when they first interact to maximize the agent's overall knowledge about the user. We demonstrate our method on two publically available US census datasets containing 172 user variables from 1,799,394 training and 1,618,489 testing users. The questions selected using our method improve the agent's accuracy when inferring information about future users, including information that they did not ask about. Our work enables smart agents that assist the user with personalized services soon after they start interacting.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161175"}, {"primary_key": "3641439", "vector": [], "sparse_vector": [], "title": "Exploring How Drivers Perceive Spatial Earcons in Automated Vehicles.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Automated vehicles seek to relieve the human driver from primary driving tasks, but this substantially diminishes the connection between driver and vehicle compared to manual operation. At present, automated vehicles lack any form of continual, appropriate feedback to re-establish this connection and offer a feeling of control. We suggest that auditory feedback can be used to support the driver in this context. A preliminary field study that explored how drivers respond to existing auditory feedback in manual vehicles was first undertaken. We then designed a set of abstract, synthesised sounds presented spatially around the driver, known as Spatial Earcons, that represented different primary driving sounds e.g. acceleration. To evaluate their effectiveness, we undertook a driving simulator study in an outdoor setting using a real vehicle. Spatial Earcons performed as well as Existing Vehicle Sounds during automated and manual driving scenarios. Subjective responses suggested Spatial Earcons produced an engaging driving experience. This paper argues that entirely new synthesised primary driving sounds, such as Spatial Earcons, can be designed for automated vehicles to replace Existing Vehicle Sounds. This creates new possibilities for presenting primary driving information in automated vehicles using auditory feedback, in order to re-establish a connection between driver and vehicle.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130901"}, {"primary_key": "3641443", "vector": [], "sparse_vector": [], "title": "How to Remember What to Remember: Exploring Possibilities for Digital Reminder Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON>"], "summary": "Digital reminder systems typically use time and place as triggers to remind people to perform activities. In this paper, we investigate how digital reminder systems could better support the process of remembering in a wider range of situations. We report findings from a survey and one-week diary study, which reveal that people want to remember to perform a broad spectrum of activities in the future, many of which cannot be supported by simple time- and location-based reminders. In addition to these examples of prospective memory, or ‘remembering intentions’ [53], we also find that people want support in ‘retrieving’ [53] information and details, especially those encountered through social interactions or intended for use in conversations with others. Drawing on our analysis of what people want to remember and how they try to support this, we draw implications for the design of intelligent reminder systems such as digital assistants (e.g. Microsoft’s Cortana) and smart speaker systems (e.g. Amazon Echo), and highlight the possibilities afforded by drawing on conversation and giving material form to digital reminders.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130903"}, {"primary_key": "3641444", "vector": [], "sparse_vector": [], "title": "Participatory Sensing or Participatory Nonsense?: Mitigating the Effect of Human Error on Data Quality in Citizen Science.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Citizen Science with mobile and wearable technology holds the possibility of unprecedented observation systems. Experts and policy makers are torn between enthusiasm and scepticism regarding the value of the resulting data, as their decision making traditionally relies on high-quality instrumentation and trained personnel measuring in a standardized way. In this paper, we (1) present an empirical behavior taxonomy of errors exhibited in non-expert smartphone-based sensing, based on four small exploratory studies, and discuss measures to mitigate their effects. We then present a large summative study (N=535) that compares instructions and technical measures to address these errors, both from the perspective of improvements to error frequency and perceived usability. Our results show that (2) technical measures without explanation notably reduce the perceived usability and (3) technical measures and instructions nicely complement each other: Their combination achieves a significant reduction in observed error rates while not affecting the user experience negatively.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3131900"}, {"primary_key": "3641446", "vector": [], "sparse_vector": [], "title": "InformationSense: Trade-offs for the Design and the Implementation of a Large Highly Deformable Cloth Display.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Deformable displays can provide two major benefits compared to rigid displays: Objects of different shapes and deformabilities, situated in our physical environment, can be equipped with deformable displays, and users can benefit from their pre-existing knowledge about the interaction with physical objects when interacting with deformable displays. In this article we present InformationSense, a large, highly deformable cloth display. The article contributes to two research areas in the context of deformable displays: It presents an approach for the tracking of large, highly deformable surfaces, and it presents one of the first UX analyses of cloth displays that will help with the design of future interaction techniques for this kind of display. The comparison of InformationSense with a rigid display interface unveiled the trade-off that while users are able to interact with InformationSense more naturally and significantly preferred InformationSense in terms of joy of use, they preferred the rigid display interfaces in terms of efficiency. This suggests that deformable displays are already suitable if high hedonic qualities are important but need to be enhanced with additional digital power if high pragmatic qualities are required.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3090053"}, {"primary_key": "3641447", "vector": [], "sparse_vector": [], "title": "Predicting the Suitability of Service Animals Using Instrumented Dog Toys.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Working dogs1 are significantly beneficial to society; however, a substantial number of dogs are released from time consuming and expensive training programs because of unsuitability in behavior. Early prediction of successful service dog placement could save time, resources, and funding. Our research focus is to explore whether aspects of canine temperament can be detected from interactions with sensors, and to develop classifiers that correlate sensor data to predict the success (or failure) of assistance dogs in advanced training. In a 2-year longitudinal study, our team tested a cohort of dogs entering advanced training in the Canine Companions for Independence (CCI) Program with 2 instrumented dog toys: a silicone ball and a silicone tug sensor. We then create a logistic model tree classifier to predict service dog success using only 5 features derived from dog-toy interactions. During randomized 10-fold cross validation where 4 of the 40 dogs were kept in an independent test set for each fold, our classifier predicts the dogs' outcomes with 87.5% average accuracy. We assess the reliability of our model by performing the testing routine 10 times over 1.5 years for a single suitable working dog, which predicts that the dog would pass each time. We calculate the resource benefit of identifying dogs who will fail early in their training, and the value for a cohort of 40 dogs using our toys and our methods for prediction is over $70,000. With CCI's 6 training centers, annual savings could be upwards of $5 million per year.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161184"}, {"primary_key": "3641449", "vector": [], "sparse_vector": [], "title": "RF-Copybook: A Millimeter Level Calligraphy Copybook based on commodity RFID.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Xiao<PERSON> Chen", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As one of the best ways to learn and appreciate the Chinese culture, Chinese calligraphy is widely practiced and learned all over the world. Traditional calligraphy learners spend a great amount of time imitating the image templates of reputed calligraphers. In this paper, we propose an RF-based Chinese calligraphy template, named RF-Copybook, to precisely monitor the writing process of the learner and provide detail instructions to improve the learner's imitating behavior. With two RFID tags attached on the brush pen and three antennas equipped at the commercial RFID reader side, RF-Copybook tracks the pen's 3-dimensional movements precisely. The key intuition behind RF-Copybook's idea is that: (i) when there is only direct path signal between the tag and the antenna, the phase measured at the reader changes linearly with the distance, (ii) the reader offers very fine-grained phase readings, thus a millimeter level accuracy of antenna-tag distance can be obtained, (iii) by combing multiple antenna-tag distances, we can quantify the writing process with stroke based feature models. Extensive experiments show that RF-Copybook is robust against the environmental noise and achieves high accuracies across different environments in the estimation of the brush pen's elevation angle, nib's moving speed and position.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161191"}, {"primary_key": "3641450", "vector": [], "sparse_vector": [], "title": "Rapid: A Multimodal and Device-free Approach Using Noise Estimation for Robust Person Identification.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON> Gu"], "summary": "Device-free human sensing is a key technology to support many applications such as indoor navigation and activity recognition. By exploiting WiFi signals reflected by human body, there have been many WiFi-based device-free human sensing applications. Among these applications, person identification is a fundamental technology to enable user-specific services. In this paper, we present Rapid, a system that can perform robust person identification in a device-free and low-cost manner, using fine-grained channel information (i.e., CSI) of WiFi and acoustic information from footstep sound. In order to achieve high accuracy in real-life scenarios with both system and environment noise, we perform noise estimation and include two different confidence values to quantify the impact of noise to both CSI and acoustic measurements. Based on an accurate gait analysis, we then adaptively fuse CSI and acoustic measurements to achieve robust person identification. We implement low-cost Rapid nodes and evaluate our system using experiments at multiple locations with a total of 1800 gait instances from 20 volunteers, and the results show that <PERSON> identifies a subject with an average accuracy of 92% to 82% from a group of 2 to 6 subjects, respectively.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130906"}, {"primary_key": "3641451", "vector": [], "sparse_vector": [], "title": "SnapLink: Fast and Accurate Vision-Based Appliance Control in Large Commercial Buildings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Xi<PERSON>", "<PERSON>", "<PERSON>"], "summary": "As the number and heterogeneity of appliances in smart buildings increases, identifying and controlling them becomes challenging. Existing methods face various challenges when deployed in large commercial buildings. For example, voice command assistants require users to memorize many control commands. Attaching Bluetooth dongles or QR codes to appliances introduces considerable deployment overhead. In comparison, identifying an appliance by simply pointing a smartphone camera at it and controlling the appliance using a graphical overlay interface is more intuitive. We introduce SnapLink, a responsive and accurate vision-based system for mobile appliance identification and interaction using image localization. Compared to the image retrieval approaches used in previous vision-based appliance control systems, SnapLink exploits 3D models to improve identification accuracy and reduce deployment overhead via quick video captures and a simplified labeling process. We also introduce a feature sub-sampling mechanism to achieve low latency at the scale of a commercial building. To evaluate SnapLink, we collected training videos from 39 rooms to represent the scale of a modern commercial building. It achieves a 94% successful appliance identification rate among 1526 test images of 179 appliances within 120 ms average server processing time. Furthermore, we show that SnapLink is robust to viewing angle and distance differences, illumination changes, as well as daily changes in the environment. We believe the SnapLink use case is not limited to appliance control: it has the potential to enable various new smart building applications.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161173"}, {"primary_key": "3641452", "vector": [], "sparse_vector": [], "title": "RADAR: Road Obstacle Identification for Disaster Response Leveraging Cross-Domain Urban Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhang", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON>", "Gang Pan", "<PERSON>"], "summary": "Typhoons and hurricanes cause extensive damage to coast cities annually, demanding urban authorities to take effective actions in disaster response to reduce losses. One of the first priority in disaster response is to identify and clear road obstacles, such as fallen trees and ponding water, and restore road transportation in a timely manner for supply and rescue. Traditionally, identifying road obstacles is done by manual investigation and reporting, which is labor intensive and time consuming, hindering the timely restoration of transportation. In this work, we propose RADAR, a low-cost and real-time approach to identify road obstacles leveraging large-scale vehicle trajectory data and heterogeneous road environment sensing data. First, based on the observation that road obstacles may cause abnormal slow motion behaviors of vehicles in the surrounding road segments, we propose a cluster direct robust matrix factorization (CDRMF) approach to detect road obstacles by identifying the collective anomalies of slow motion behaviors from vehicle trajectory data. Then, we classify the detected road obstacles leveraging the correlated spatial and temporal features extracted from various road environment data, including satellite images and meteorological records. To address the challenges of heterogeneous features and sparse labels, we propose a semi-supervised approach combining co-training and active learning (CORAL). Real experiments on Xiamen City show that our approach accurately detects and classifies the road obstacles during the 2016 typhoon season with precision and recall both above 90%, and outperforms the state-of-the-art baselines.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161159"}, {"primary_key": "3641453", "vector": [], "sparse_vector": [], "title": "Mago: Mode of Transport Inference Using the Hall-Effect Magnetic Sensor and Accelerometer.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we introduce Mago, a novel system that can infer a person's mode of transport (MOT) using the Hall-effect magnetic sensor and accelerometer present in most smart devices. When a vehicle is moving, the motions of its mechanical components such as the wheels, transmission and the differential distort the earth's magnetic field. The magnetic field is distorted corresponding to the vehicle structure (e.g., bike chain or car transmission system), which manifests itself as a strong signal for sensing a person's transportation modality. We utilize this magnetic signal combined with the accelerometer and design a robust algorithm for the MOT detection. In particular, our system extracts frame-based features from the sensor data and can run in nearly real-time with only a few seconds of delay. We evaluated Ma<PERSON> using over 70 hours of daily commute data from 7 participants and the leave-one-out analysis of our cross-user, cross-device model reports an average accuracy of 94.4% among seven classes (stationary, bus, bike, car, train, light rail and scooter). Besides MOT, our system is able to reliably differentiate the phone's in-car position at an average accuracy of 92.9%. We believe Mago could potentially benefit many contextually-aware applications that require MOT detection such as a digital personal assistant or a life coaching application.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3090054"}, {"primary_key": "3641454", "vector": [], "sparse_vector": [], "title": "Does this App Really Need My Location?: Context-Aware Privacy Management for Smartphones.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The enormous popularity of smartphones, their rich sensing capabilities, and the data they have about their users have lead to millions of apps being developed and used. However, these capabilities have also led to numerous privacy concerns. Platform manufacturers, as well as researchers, have proposed numerous ways of mitigating these concerns, primarily by providing fine-grained visibility and privacy controls to the user on a per-app basis. In this paper, we show that this per-app permission approach is suboptimal for many apps, primarily because most data accesses occur due to a small set of popular third-party libraries which are common across multiple apps. To address this problem, we present the design and implementation of ProtectMyPrivacy (PmP) for Android, which can detect critical contextual information at runtime when privacy-sensitive data accesses occur. In particular, PmP infers the purpose of the data access, i.e. whether the data access is by a third-party library or by the app itself for its functionality. Based on crowdsourced data, we show that there are in fact a set of 30 libraries which are responsible for more than half of private data accesses. Controlling sensitive data accessed by these libraries can therefore be an effective mechanism for managing their privacy. We deployed our PmP app to 1,321 real users, showing that the number of privacy decisions that users have to make are significantly reduced. In addition, we show that our users are better protected against data leakage when using our new library-based blocking mechanism as compared to the traditional app-level permission mechanisms.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3132029"}, {"primary_key": "3641456", "vector": [], "sparse_vector": [], "title": "Devices and Data and Agents, Oh My: How Smart Home Abstractions Prime End-User Mental Models.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the advent of DIY smart homes and the Internet of Things comes the emergence of user interfaces for domestic human-building interaction. However, the design trade-offs between the different representations of a smart home’s capabilities are still not well-understood. In this work, we examine how four different smart home abstractions affect end users’ mental models of a hypothetical system. We develop four questionnaires, each of which describes the same hypothetical smart home using a different abstraction, and then we collect responses depicting desired smart home applications from over 1,500 Mechanical Turk workers. We find that the choice of abstraction strongly primes end users’ responses. In particular, the purely device-oriented abstraction results in the most limited scenarios, suggesting that if we want users to associate smart home technologies with valuable high-level applications we should shift the UI paradigm for the Internet of Things from device-oriented control to other abstractions that inspire a greater diversity of interactions.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3132031"}, {"primary_key": "3641457", "vector": [], "sparse_vector": [], "title": "Remote Control by Body Movement in Synchrony with Orbiting Widgets: an Evaluation of TraceMatch.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this work we consider how users can use body movement for remote control with minimal effort and maximum flexibility. TraceMatch is a novel technique where the interface displays available controls as circular widgets with orbiting targets, and where users can trigger a control by mimicking the displayed motion. The technique uses computer vision to detect circular motion as a uniform type of input, but is highly appropriable as users can produce matching motion with any part of their body. We present three studies that investigate input performance with different parts of the body, user preferences, and spontaneous choice of movements for input in realistic application scenarios. The results show that users can provide effective input with their head, hands and while holding objects, that multiple controls can be effectively distinguished by the difference in presented phase and direction of movement, and that users choose and switch modes of input seamlessly.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130910"}, {"primary_key": "3641458", "vector": [], "sparse_vector": [], "title": "Money Drives: Can Monetary Incentives based on Real-Time Monitoring Improve Driving Behavior?", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper examines the effectiveness of monetary incentives based on real-time monitoring as means to improve driving behavior of company car drivers. We conducted a 5-months 60-drivers field study with one of the largest public transportation companies in Israel. Driving behavior was measured continuously using In-Vehicle Data Recorders (IVDR) that were pre-installed in the vehicles, enabling naturalistic, objective and concise measurements. The driving behavior measurements were then used to examine two different monetary incentive schemes: (1) a simple individual incentive scheme where each driver was rewarded based on his own improvement in driving behavior, and (2) a peer-reward scheme where each driver was rewarded based on the improvement of his peers. Drivers were also provided with daily feedback about their improvement and the reward they gained using text messages and a dedicated smartphone app. We find that the two incentive schemes presented an average improvement of 25% in driving behavior, whereas the control group (that did not use any monetary incentive) presented no improvement at all. Surprisingly and in contrast to the reported superiority of the peer-reward scheme in previous studies, we find the individual scheme to perform better in our setting (31% vs. 15% improvement). Finally, we find that the monetary incentive schemes were able to reduce fuel consumption significantly, suggesting that such incentives can serve as a sustainable mechanism for improving driving behavior in real-world applications.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161417"}, {"primary_key": "3641459", "vector": [], "sparse_vector": [], "title": "Lessons Learned from Two Cohorts of Personal Informatics Self-Experiments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Self-experiments allow people to investigate their own individual outcomes from behavior change, often with the aid of personal tracking devices. The challenge is to design scientifically valid self-experiments that can reach conclusive results. In this paper, we aim to understand how novices run self-experiments when they are provided with a structured lesson in experimental design. We conducted a study on self-experimentation with two cohorts of students, where a total of 34 students performed a self-experiment of their choice. In the first cohort, students were given only two restrictions: a specific number of variables to track and a set duration for the study. The findings from this cohort helped us generate concrete guidelines for running a self-experiment, and use them as the format for the next cohort. A second cohort of students used these guidelines to conduct their own self-experiments in a more structured manner. Based on the findings from both cohorts, we propose a set of guidelines for running successful self-experiments that address the pitfalls encountered by students in the study, such as inadequate study design and analysis methods. We also discuss broader implications for future self-experimenters and designers of tools for self-experimentation.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130911"}, {"primary_key": "3641461", "vector": [], "sparse_vector": [], "title": "Mitigating Bystander Privacy Concerns in Egocentric Activity Recognition with Deep Learning and Intentional Image Degradation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>az"], "summary": "Recent advances in wearable camera technology and computer vision algorithms have greatly enhanced the automatic capture and recognition of human activities in real-world settings. While the appeal and utility of wearable camera devices for human-behavior understanding is indisputable, privacy concerns have limited the broader adoption of this method. To mitigate this problem, we propose a deep learning-based approach that recognizes everyday activities in egocentric photos that have been intentionally degraded in quality to preserve the privacy of bystanders. An evaluation on 2 annotated datasets collected in the field with a combined total of 84,078 egocentric photos showed activity recognition performance with accuracy between 79% and 88% across 17 and 21 activity classes when the images were subjected to blurring (mean filter k=20). To confirm that image degradation does indeed raise the perception of bystander privacy, we conducted a crowd sourced validation study with 640 participants; it showed a statistically significant positive relationship between the amount of image degradation and participants' willingness to be captured by wearable cameras. This work contributes to the field of privacy-sensitive activity recognition with egocentric photos by highlighting the trade-off between perceived bystander privacy protection and activity recognition performance.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161190"}, {"primary_key": "3641464", "vector": [], "sparse_vector": [], "title": "If It&apos;s Convenient: Leveraging Context in Peer-to-Peer Variable Service Transaction Recommendations.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Peer-to-Peer Variable Service Transaction (P2P-VST) systems enable people to offer and receive help with a wide range of task types. However, such services are hampered by the difficulty of finding relevant and convenient opportunities for transactions in a timely fashion. Many transaction opportunities are missed as a consequence of members not being aware of offers and/or requests from people nearby or en route that match their needs and/or abilities. In this paper, we explore the impact of context-awareness on P2P-VSTs to address this problem. Using mobile technology and an in situ study, we evaluate how recommending service requests targeted at a person’s context impacts their willingness to enter a transaction. Our results show that, even when people have not actively volunteered for a service, they are significantly more likely to accept a transaction opportunity if it is convenient for them in terms of time and location. These findings demonstrate how context-aware technology holds the promise of increasing the efficiency and activity level in P2P-VST systems.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130913"}, {"primary_key": "3641465", "vector": [], "sparse_vector": [], "title": "Augmenting Audits: Exploring the Role of Sensor Toolkits in Sustainable Buildings Management.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Audits are commonly carried out by facilities managers (FMs) to quantify the sustainability and performance of the buildings they manage, informing improvements to infrastructure for resource and cost savings, and assessing compliance with standards and legislation. The scope for what can be audited is limited by available infrastructure. In this article, we investigate the utility of a flexible sensor toolkit to enhance existing energy auditing practices. We present findings from a qualitative study with FM and student auditor participants from 3 organisations. Our study covers how these toolkits were used and integrated into auditing practices within these organisations, and the opportunities and issues for resource management that arose as a result. We conclude with design implications for toolkits to support sensor-augmented audits, make recommendations towards a deployment protocol for sensor toolkits used in this context, and develop broader considerations for how future standards and policies might be adapted to leverage this potential.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3090075"}, {"primary_key": "3641466", "vector": [], "sparse_vector": [], "title": "Modus Operandi of Crowd Workers: The Invisible Role of Microtask Work Environments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The ubiquity of the Internet and the widespread proliferation of electronic devices has resulted in flourishing microtask crowdsourcing marketplaces, such as Amazon MTurk. An aspect that has remained largely invisible in microtask crowdsourcing is that of work environments; defined as the hardware and software affordances at the disposal of crowd workers which are used to complete microtasks on crowdsourcing platforms. In this paper, we reveal the significant role of work environments in the shaping of crowd work. First, through a pilot study surveying the good and bad experiences workers had with UI elements in crowd work, we revealed the typical issues workers face. Based on these findings, we then deployed over 100 distinct microtasks on CrowdFlower, addressing workers in India and USA in two identical batches. These tasks emulate the good and bad UI element designs that characterize crowdsourcing microtasks. We recorded hardware specifics such as CPU speed and device type, apart from software specifics including the browsers used to complete tasks, operating systems on the device, and other properties that define the work environments of crowd workers. Our findings indicate that crowd workers are embedded in a variety of work environments which influence the quality of work produced. To confirm and validate our data-driven findings we then carried out semi-structured interviews with a sample of Indian and American crowd workers from this platform. Depending on the design of UI elements in microtasks, we found that some work environments support crowd workers more than others. Based on our overall findings resulting from all the three studies, we introduce ModOp, a tool that helps to design crowdsourcing microtasks that are suitable for diverse crowd work environments. We empirically show that the use of ModOp results in reducing the cognitive load of workers, thereby improving their user experience without affecting the accuracy or task completion time.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130914"}, {"primary_key": "3641468", "vector": [], "sparse_vector": [], "title": "CrowdPickUp: Crowdsourcing Task Pickup in the Wild.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We develop and evaluate a new ubiquitous crowdsourcing platform called CrowdPickUp, that combines the advantages of mobile and situated crowdsourcing to overcome their respective limitations. In a 19-day long field study with 70 participants, we evaluate the quality of work that CrowdPickUp produces. In particular, we measure quality in terms of worker performance in a variety of tasks (requiring local knowledge, location-based, general) while using a number of different quality control mechanisms, and also capture workers’ perceptions of the platform. Our findings show that workers of CrowdPickUp contributed data of comparable quality to previously presented crowdsourcing deployments while at the same time allowing for a wide breadth of tasks to be deployed. Finally, we offer insights towards the continued exploration of this research agenda.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130916"}, {"primary_key": "3641469", "vector": [], "sparse_vector": [], "title": "Intelligent Interruption Management using Electro Dermal Activity based Physiological Sensor for Collaborative Sensemaking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Sensemaking tasks are difficult to accomplish with limited time and attentional resources because analysts are faced with a constant stream of new information. While this information is often important, the timing of the interruptions may detract from analyst's work. In an ideal world, there would be no interruptions. But that is not the case in real world sensemaking tasks. So, in this study, we explore the value of timing interruptions based on an analyst's state of arousal as detected by Electrodermal activity derived form galvanic skin response (EDA). In a laboratory study, we compared performance when interruptions were timed to occur during increasing arousal, decreasing arousal, at random intervals or not at all. Analysts performed significantly better when interruptions occurred during periods of increasing arousal than when they were random. Further, analysts rated process component of team experience significantly higher also during periods of increasing arousal than when they were random. Self-reported workload was not impacted by interruptions timing. We discuss how system designs could leverage inexpensive off-the-shelf wrist sensors to improve interruption timing.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130917"}, {"primary_key": "3641470", "vector": [], "sparse_vector": [], "title": "Privacy-preserving Image Processing with Binocular Thermal Cameras.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Today, cameras and digital image processing are transforming industries and the human environment with rich, informative sensing. However, image processing is not utilized nearly as much in homes where concerns about image privacy dominate. In a preliminary study with 200 participants, we found 21% would reject a camera based system even if the system was designed to not report images as they could still be collected if the camera system was hacked. In this paper, we demonstrate a hardware-based approach for privacy-preserving image processing: the ability to automatically extract information from imaging sensors without the risk of compromising image privacy, even if the system is hacked. The basic idea is to limit both the memory available on board the camera and the data rate of camera communication to prevent a full image from ever being extracted. As a proof of concept, we prototype a system, called Lethe, that tracks and identifies individuals by height with a thermal camera as they move from room to room. Our results show that <PERSON><PERSON> can detect the presence of individuals with 96.9% accuracy and determine their direction of travel with 99.7% accuracy. Additionally, <PERSON><PERSON> can identify individuals 96.0% of the time with a 5cm (~2in) or greater difference in walking height and 92.9% with a 2.5cm (~1in) or greater difference. Finally, <PERSON><PERSON> performs this processing with only 33 bytes of memory (or 0.69% of the full thermal image).", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161198"}, {"primary_key": "3641471", "vector": [], "sparse_vector": [], "title": "ObjectSkin: Augmenting Everyday Objects with Hydroprinted Touch Sensors and Displays.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Augmenting everyday objects with interactive input and output surfaces is a long-standing topic in ubiquitous computing and HCI research. Existing approaches, however, fail to leverage the objects' full potential, particularly in highly curved organic geometries and in diverse visuo-haptic surface properties. We contribute ObjectSkin, a fabrication technique for adding conformal interactive surfaces to rigid and flexible everyday objects. It enables multi-touch sensing and display output that seamlessly integrates with highly curved and irregular geometries. The approach is based on a novel water-transfer process for interactive surfaces. It leverages off-the-shelf hobbyist equipment to fabricate thin, conformal, and translucent electronic circuits that preserve the surface characteristics of everyday objects. It offers two methods, for rapid low-fidelity and versatile high-fidelity prototyping, and is applicable to a wide variety of materials. Results from a series of technical experiments provide insights into the supported object geometries, compatible object materials, and robustness. Seven example cases demonstrate how ObjectSkin makes it possible to leverage geometries, surface properties, and unconventional objects for prototyping novel interactions for ubiquitous computing.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161165"}, {"primary_key": "3641473", "vector": [], "sparse_vector": [], "title": "SugarMate: Non-intrusive Blood Glucose Monitoring with Smartphones.", "authors": ["Weixi Gu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Inferring abnormal glucose events such as hyperglycemia and hypoglycemia is crucial for the health of both diabetic patients and non-diabetic people. However, regular blood glucose monitoring can be invasive and inconvenient in everyday life. We present SugarMate, a first smartphone-based blood glucose inference system as a temporary alternative to continuous blood glucose monitors (CGM) when they are uncomfortable or inconvenient to wear. In addition to the records of food, drug and insulin intake, it leverages smartphone sensors to measure physical activities and sleep quality automatically. Provided with the imbalanced and often limited measurements, a challenge of SugarMate is the inference of blood glucose levels at a fine-grained time resolution. We propose Md3RNN, an efficient learning paradigm to make full use of the available blood glucose information. Specifically, the newly designed grouped input layers, together with the adoption of a deep RNN model, offer an opportunity to build blood glucose models for the general public based on limited personal measurements from single-user and grouped-users perspectives. Evaluations on 112 users demonstrate that Md3RNN yields an average accuracy of 82.14%, significantly outperforming previous learning methods those are either shallow, generically structured, or oblivious to grouped behaviors. Also, a user study with the 112 participants shows that SugarMate is acceptable for practical usage.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130919"}, {"primary_key": "3641476", "vector": [], "sparse_vector": [], "title": "Modelling Passengers&apos; Reaction to Dynamic Prices in Ride-on-demand Services: A Search for the Best Fare.", "authors": ["Su<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In emerging ride-on-demand (RoD) services such as Uber and Didi (in China), dynamic prices play an important role in regulating supply and demand, trying to improve the service quality for both drivers and passengers. In this paper, we take a new perspective to study RoD services besides the supply or demand, and focus on passengers' reaction to dynamic prices. Passengers' reaction can be regarded as a process of searching for the best price before getting on a car, and the searching process reflects passengers' demand elasticity -- “how eager they are requesting a ride”. We collect data of passengers' reaction from a real RoD service provider in China, and analyze the patterns of passengers' reaction. The analysis results show that both the dynamic prices and passengers' demand elasticity influence their reaction. We then adopt and extend a previous model for sequential search from a known distribution to understand passengers' reaction, and use our data to obtain the search costs under various circumstances, which could be interpreted as passengers' demand elasticity. Insights on the search cost and other relevant quantities are discussed. Our expectation is that the result of the study should be helpful not only for service providers in designing dynamic pricing algorithms, but also for passengers and policy makers in understanding the effects and implications of dynamic pricing.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161194"}, {"primary_key": "3641477", "vector": [], "sparse_vector": [], "title": "CityTransfer: Transferring Inter- and Intra-City Knowledge for Chain Store Site Recommendation based on Multi-Source Urban Data.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Chain businesses have been dominating the market in many parts of the world. It is important to identify the optimal locations for a new chain store. Recently, numerous studies have been done on chain store location recommendation. These studies typically learn a model based on the features of existing chain stores in the city and then predict what other sites are suitable for running a new one. However, these models do not work when a chain enterprise wants to open business in a new city where there is not enough data about this chain store. To solve the cold-start problem, we propose CityTransfer, which transfers chain store knowledge from semantically-relevant domains (e.g., other cities with rich knowledge, similar chain enterprises in the target city) for chain store placement recommendation in a new city. In particular, CityTransfer is a two-fold knowledge transfer framework based on collaborative filtering, which consists of the transfer rating prediction model, the inter-city knowledge association method and the intra-city semantic extraction method. Experiments using data of chain hotels from four different cities crawled from Ctrip (a popular travel reservation website in China) and the urban characters extracted from several other data sources validate the effectiveness of our approach on store site recommendation.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161411"}, {"primary_key": "3641478", "vector": [], "sparse_vector": [], "title": "CrowdStory: Fine-Grained Event Storyline Generation by Fusion of Multi-Modal Crowdsourced Data.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Event summarization based on crowdsourced microblog data is a promising research area, and several researchers have recently focused on this field. However, these previous works fail to characterize the fine-grained evolution of an event and the rich correlations among posts. The semantic associations among the multi-modal data in posts are also not investigated as a means to enhance the summarization performance. To address these issues, this study presents CrowdStory, which aims to characterize an event as a fine-grained, evolutionary, and correlation-rich storyline. A crowd-powered event model and a generic event storyline generation framework are first proposed, based on which a multi-clue--based approach to fine-grained event summarization is presented. The implicit human intelligence (HI) extracted from visual contents and community interactions is then used to identify inter-clue associations. Finally, a cross-media mining approach to selective visual story presentation is proposed. The experiment results indicate that, compared with the state-of-the-art methods, CrowdStory enables fine-grained event summarization (e.g., dynamic evolution) and correctly identifies up to 60% strong correlations (e.g., causality) of clues. The cross-media approach shows diversity and relevancy in visual data selection.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130920"}, {"primary_key": "3641479", "vector": [], "sparse_vector": [], "title": "QuickTalk: An Association-Free Communication Method for IoT Devices in Proximity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "IoT devices are in general considered to be straightforward to use. However, we find that there are a number of situations where the usability becomes poor. The situations include but not limited to the followings: 1) when initializing an IoT device, 2) when trying to control an IoT device which is initialized by another person, and 3) when trying to control an IoT device out of many of the same type. We tackle these situations by proposing a new association-free communication method, QuickTalk. QuickTalk lets a user device such as a smartphone pinpoint and activate an IoT device with the help of an IR transmitter and communicate with the pinpointed IoT device through the broadcast channel of WiFi without a conventional association process. This nature, QuickTalk allows a user device to immediately give a command to a specific IoT device in proximity even when the IoT device is uninitialized, unassociated with the control interface of the user, or associated but visually indistinguishable from others of the same kind. Our experiments of QuickTalk implemented on Raspberry Pi 2 devices show that QuickTalk does its job quickly and intuitively. The end-to-end delay of QuickTalk for transmitting an IoT command is on average about 0.74 seconds, and is upper bounded by 2.5 seconds. We further confirm that even when an IoT device has ongoing data sessions with other devices, which disturb the broadcast channel, QuickTalk can still reliably communicate with the IoT device at the cost of minor throughput degradation.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130921"}, {"primary_key": "3641480", "vector": [], "sparse_vector": [], "title": "MindfulWatch: A Smartwatch-Based System For Real-Time Respiration Monitoring During Meditation.", "authors": ["<PERSON><PERSON>", "Chongguang Bi", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With a wealth of scientifically proven health benefits, meditation was enjoyed by about 18 million people in the U.S. alone, as of 2012. Yet, there remains a stunning lack of convenient tools for promoting long-term and effective meditation practice. In this paper, we present MindfulWatch, a practical smartwatch-based sensing system that monitors respiration in real-time during meditation -- offering essential biosignals that can potentially be used to empower various future applications such as tracking changes in breathing pattern, offering real-time guidance, and providing an accurate bio-marker for meditation research. To this end, MindfulWatch is designed to be convenient for everyday use with no training required. Operating solely on a smartwatch, MindfulWatch can immediately reach the growing population of smartwatch users, making it ideal for longitudinal data collection for meditation studies. Specifically, it utilizes motion sensors to sense the subtle “micro” wrist rotation (0.01 rad/s) induced by respiration. To accurately capture breathing, we developed a novel self-adaptive model that tracks changes in both breathing pattern and meditation posture over time. MindfulWatch was evaluated based on data from 36 real-world meditation sessions (8.7 hours, 11 subjects). The results suggest that MindfulWatch offers reliable real-time respiratory timing measurement (70% errors under 0.5 seconds).", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130922"}, {"primary_key": "3641482", "vector": [], "sparse_vector": [], "title": "FootStriker: An EMS-based Foot Strike Assistant for Running.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In running, knee-related injuries are very common. The main cause are high impact forces when striking the ground with the heel first. Mid- or forefoot running is generally known to reduce impact loads and to be a more efficient running style. In this paper, we introduce a wearable running assistant, consisting of an electrical muscle stimulation (EMS) device and an insole with force sensing resistors. It detects heel striking and actuates the calf muscles during the flight phase to control the foot angle before landing. We conducted a user study, in which we compared the classical coaching approach using slow motion video analysis as a terminal feedback to our proposed real-time EMS feedback. The results show that EMS actuation significantly outperforms traditional coaching, i.e., a decreased average heel striking rate, when using the system. As an implication, EMS feedback can generally be beneficial for the motor learning of complex, repetitive movements.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3053332"}, {"primary_key": "3641483", "vector": [], "sparse_vector": [], "title": "Effects of Lateral Eye Displacement on Comfort While Reading from a Video Display Terminal.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Some small field-of-view (FOV) head worn displays (HWD), like Epson's Moverio BT-300, are mounted directly in the user's line of sight. In contrast, Google Glass is mounted “out of the way” and above the line of sight. Other displays like the Vuzix M100 or Optinvent ORA-1 allow the user to adjust the display position, and some users have expressed a desire for the display to be laterally displaced toward the ear, out of the main line of sight. How far toward the ear can a small FOV display be mounted and still be used comfortably? Using a 30-minute reading task and an emulated display with the FOV of a typical smart phone (9.2°x 16.3°), we study a user's perceived comfort level while reading at four horizontally displaced positions. We ask participants to rate their comfort every five minutes using a 5-point Likert scale knob (5 being most comfortable), for a total of seven measurements. Scores are summed over the seven measurements to form a summed comfort score. We find that 0° (Md = 34.0; p«0.001), 10° (Md = 33.5; p«c0.001), and 20° (Md = 33.5; p«c0.001) are more comfortable than 30° (Md = 29.5) and that 0° (p&lt;0.01) and 10° (p&lt;0.01) are more comfortable than 20°. Reading performance and workload measures were numerically similar across all conditions. Given the main results of the experiment, post-hoc analysis on other measurements such as preference and asthenopia, and participant comments, we suggest that small FOV displays should be mounted at lateral displacement angles of 20° and less for sustained use.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161177"}, {"primary_key": "3641486", "vector": [], "sparse_vector": [], "title": "Zero-Effort In-Home Sleep and Insomnia Monitoring using Radio Signals.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Insomnia is the most prevalent sleep disorder in the US. In-home insomnia monitoring is important for both diagnosis and treatment. Existing solutions, however, require the user to either maintain a sleep diary or wear a sensor while sleeping. Both can be quite cumbersome. This paper introduces EZ-Sleep, a new approach for monitoring insomnia and sleep. EZ-Sleep has three properties. First, it is zero effort, i.e., it neither requires the user to wear a sensor nor to record any data. It monitors the user remotely by analyzing the radio signals that bounce off her body. Second, it delivers new features unavailable with other devices such as automatically detecting where the user sleeps and her exact bed schedule, while simultaneously monitoring multiple users in different beds. Third, it is highly accurate. Its average error in measuring sleep latency and total sleep time is 4.9 min and 10.3 min, respectively.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130924"}, {"primary_key": "3641487", "vector": [], "sparse_vector": [], "title": "iTour: Making Tourist Maps GPS-Enabled.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Uyn-Dinh Trân", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Although tourist maps are useful resources for people to visit scenic areas, they are also commonly distorted and omit details according to the purposes and functions of a map. In this paper, we present iTour, a semi-automatic system that turns tourist maps into digital maps. By involving users in matching the road network of a tourist map and the paired standard map, our system computes road network correspondence between the two maps. By doing so, users can navigate on such GPS-enabled tourist maps using mobile devices. This transformation creates the possibility of augmenting a large number of tourist maps with digital map features. To evaluate the performance of matching road networks, we compared the presented semi-automatic interface to a manual interface. The results showed that the semi-automatic interface saved participants significant effort in generating correspondence and was perceived to require significantly less time by the participants. In addition, we conducted a field study of the iTour in comparison to using a tourist map and Google Maps together. Our results showed that iTour helped participants find their way during travel. The participants provided positive feedback on the combination of tourist maps and GPS location because of its highlights of important landmarks, showing users' locations relative to those landmarks, and saving the effort of switching tourist maps and Google Maps.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161167"}, {"primary_key": "3641488", "vector": [], "sparse_vector": [], "title": "3DLoc: 3D Features for Accurate Indoor Positioning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A variety of indoor applications require both accurate location and orientation, such as indoor navigation and augmented reality. This paper presents 3DLoc, with which you can find your location and orientation by pointing your smartphone camera at 3D features e.g., doors and entrances. Different from the previous image-based localization of matching features via SIFT or SURF, 3DLoc takes advantage of rules for 3D features, including the ratio between height and width, the orientation and the distribution on the 2D floor map. The features around users are regarded as a unique 3D signature for the location. Based on prior researches on vanishing points and indoor geometric reasoning, we propose an algorithm to extract the signature from captured images and robustly decode the signature to accurate location and orientation. In terms of efficiency and user-friendliness, a series of optimizations are adopted through fusion of smartphone sensors and vision. We conduct experiments on different floors of a typical office building via the prototype built on Huawei P7 and iPhone 5S. Ninety percent of errors for location and orientation are within 25cm and two de4rees, respectively. With a 2D floor map provided, KB (-KiloByte-) level storage is required for the additional 3D information.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161409"}, {"primary_key": "3641489", "vector": [], "sparse_vector": [], "title": "CTS: A Cellular-based Trajectory Tracking System with GPS-level Accuracy.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "GPS has been widely used for locating mobile devices on the road map. Due to its high power consumption and poor signal penetration, GPS is unfortunately unsuitable to be used for continuously tracking low-power devices. Compared with GPS-based positioning, cellular-infrastructure-based positioning consumes much less energy, and works in any place covered by the cellular networks. However, the challenges of cellular positioning come from the relatively low accuracy and sampling rate. In this paper, we propose a novel cellular-based trajectory tracking system, namely CTS. It achieves GPS-level accuracy by combining trilateration-based cellular positioning, stationary state detection, and Hidden-Markov-Model-based path recovery. In particular, CTS utilizes basic characteristics of cellular sectors to produce more credible inferences for device locations. To evaluate the performance of CTS, we collaborated with a mobile operator and deployed the system the city of Urumchi, Xinjiang Province of China. We collected the location data of 489,032 anonymous mobile subscribers from cellular networks during 24 hours, and retrieved 201 corresponding GPS trajectories. Our experimental results show that CTS achieves GPS-level accuracy in 95.7% of cases, which significantly outperforms the state-of-the-art solutions.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161185"}, {"primary_key": "3641490", "vector": [], "sparse_vector": [], "title": "Early Destination Prediction with Spatio-temporal User Behavior Patterns.", "authors": ["<PERSON><PERSON>", "Kota Tsubouchi", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Predicting user behavior makes it possible to provide personalized services. Destination prediction (e.g. predicting a future location) can be applied to various practical applications. An example of destination prediction is personalized GIS services, which are expected to provide alternate routes to enable users to avoid congested roads. However, the destination prediction problem requires critical trade-offs between timing and accuracy. In this paper, we focus on early destination prediction as the central issue, as early recognition in destination prediction has not been fully explored. As an alternative to the traditional two basic approaches with trajectory tracking that narrow down the candidates with respect to the trip progress, and Next Place Prediction (NPP) that infers the future location of a user from user habits, we propose a new probabilistic model based on both conventional models. The advantage of our model is that it drastically narrows down the destination candidates efficiently at the early stage of a trip, owing to the staying information derived from the NPP approach. In other words, our approach achieves high prediction accuracy by considering both approaches at the same time. To implement our model, we employ SubSynE for state-of-the-art prediction based on trajectory tracking as well as a multi-class logistic regression based on user contexts. Despite the simplicity of our model, the proposed method provides improved performance compared to conventional approaches based on the experimental results using the GPS logs of 1,646 actual users from the commercial services.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161197"}, {"primary_key": "3641491", "vector": [], "sparse_vector": [], "title": "Charging a Smartphone Across a Room Using Lasers.", "authors": ["<PERSON><PERSON><PERSON>", "Elyas <PERSON>ati", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We demonstrate a novel laser-based wireless power delivery system that can charge mobile devices such as smartphones across a room. The key challenges in achieving this are multi-fold: delivering greater than a watt of power across the room, minimizing the exposure of the resulting high-power lasers to human tissue, and finally ensuring that the design meets the form-factor requirements of a smartphone and requires minimal instrumentation to the environment. This paper presents a novel, and to the best of our knowledge, the first design, implementation and evaluation of an end-to-end power delivery system that satisfies all the above requirements. Our results show that we can deliver more than 2 W at ranges of 4.3 m and 12.2 m for a smartphone (25 cm2) and table-top form factor (100 cm2) receiver respectively. Further, extensive characterization of our safety system shows that we can turn off our laser source much before a human moving at a maximum speed of 44 m/s can even enter the high-power laser beam area.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161163"}, {"primary_key": "3641493", "vector": [], "sparse_vector": [], "title": "Smartwatch Wearing Behavior Analysis: A Longitudinal Study.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Smartwaches are the representative wearable or body-worn devices that provide convenient and easy information access. There is a growing body of research work on enabling novel interaction techniques and understanding user experiences of smartwatches. However, there is still lack of user experience research on wearing behaviors of smartwatches, which is critical for wearable device and service design. In this work, we investigate how college students wear smartwatches and what factors affect wearing behaviors by analyzing a longitudinal activity dataset collected from 50 smartwatch users for 203 days. Our results show that there are several temporal usage patterns and distinct groups of usage patterns. The factors affecting wearing behaviors are contextual, nuanced, and multifaceted. Our findings provide diverse design implications for improving wearability of smartwatches and leveraging smartwatches for behavioral changes.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3131892"}, {"primary_key": "3641494", "vector": [], "sparse_vector": [], "title": "SafeDrive: Detecting Distracted Driving Behaviors Using Wrist-Worn Devices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chongguang Bi", "<PERSON><PERSON><PERSON>"], "summary": "Distracted driving causes a large number of fatalities every year and is now becoming an important issue in the traffic safety study. In this paper, we present SafeDrive, a driving safety system that leverages wearable wrist sensing techniques to detect and analyze driver distracted behaviors. Existing wrist-worn sensing approaches, however, do not address challenges under real driving environments, such as less distinguishable gesture patterns due to in-vehicle physical constraints, various gesture hallmarks produced by different drivers and significant noise introduced by various driving conditions. In response, SafeDrive adopts a semi-supervised machine learning model for in-vehicle distracting activity detection. To improve the detection accuracy, we provide online updated classifiers by collecting real-time gesture data, while at the same time utilize smartphone sensing to generate soft hints filtering out anomalies and non-distracted hand movements. In the evaluation, we conduct extensive real-road experiments involving 20 participants (10 males and 10 females) and 5 vehicles (a sedan, a minivan and three SUVs). Our approach can achieve an average classification accuracy of over 90% with a error rate of a few percent, which demonstrate that SafeDrive is robust to real driving environments, and has great potential to help drivers shape safe driving habits.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161179"}, {"primary_key": "3641496", "vector": [], "sparse_vector": [], "title": "Beautiful...but at What Cost?: An Examination of Externalities in Geographic Vehicle Routing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Millions of people use platforms such as Google Maps to search for routes to their desired destinations. Recently, researchers and mapping platforms have shown growing interest in optimizing routes for criteria other than travel time, e.g. simplicity, safety, and beauty. However, despite the ubiquity of algorithmic routing and its potential to define how millions of people move around the world, very little is known about the externalities that arise when adopting these new optimization criteria, e.g. potential redistribution of traffic to certain neighborhoods and increased route complexity (with its associated risks). In this paper, we undertake the first controlled examination of these externalities, doing so across multiple mapping platforms, alternative optimizations, and cities. We find, for example, that scenic routing (i.e. “beauty”-optimized routing) would remove vehicles from highways, greatly increase traffic around parks, and, in certain cases, do the same for high-income areas. Our results also highlight that the interaction between routing criteria and urban structure is complex and effects vary from city to city, an important consideration for the growing literature on alternative routing strategies. Finally, to address the lack of open implementations of alternative routing algorithms and controlled routing evaluation frameworks, we are releasing our alternative routing and evaluation platform with this paper.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3090080"}, {"primary_key": "3641497", "vector": [], "sparse_vector": [], "title": "Forma Track: Tracking People based on Body Shape.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Knowledge of a person’s whereabouts in the home is key to context-aware applications, but many people do not want to carry or wear a tag or mobile device in the home. Therefore, many tracking systems are now using so-called weak biometrics such as height, weight, and width. In this paper, we propose to use body shape as a weak biometric, differentiating people based on features such as head size, shoulder size, or torso size. The basic idea is to scan the body with a radar sensor and to compute the reflection profile: the amount of energy that reflects back from each part of the body. Many people have different body shapes even if they have the same height, weight, or width, which makes body shape a stronger biometric. We built a proof-of-concept system called FormaTrack to test this approach, and evaluate using eight participants of varying height and weight. We collected over 2800 observations while capturing a wide range of factors such as clothing, hats, shoes, and backpacks. Results show that FormaTrack can achieve a precision, recall, direction and identity accuracy (over all possible groups of 2 people) of 100%, 99.86%, 99.7% and 95.3% respectively. Results indicate that FormaTrack can achieve over 99% tracking accuracy with 2 people in a home with 5 or more rooms.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130926"}, {"primary_key": "3641499", "vector": [], "sparse_vector": [], "title": "Activity Recognition for Quality Assessment of Batting Shots in Cricket using a Hierarchical Representation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Quality assessment in cricket is a complex task that is performed by understanding the combination of individual activities a player is able to perform and by assessing how well these activities are performed. We present a framework for inexpensive and accessible, automated recognition of cricketing shots. By means of body-worn inertial measurement units, movements of batsmen are recorded, which are then analysed using a parallelised, hierarchical recognition system that automatically classifies relevant categories of shots as required for assessing batting quality. Our system then generates meaningful visualisations of key performance parameters, including feet positions, attack/defence, and distribution of shots around the ground. These visualisations are the basis for objective skill assessment thereby focusing on specific personal improvement points as identified through our system. We evaluated our framework through a deployment study where 6 players engaged in batting exercises. Based on the recorded movement data we could automatically identify 20 classes of unique batting shot components with an average F1-score greater than 88%. This analysis is the basis for our detailed analysis of our study participants’ skills. Our system has the potential to rival expensive vision-based systems but at a fraction of the cost.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130927"}, {"primary_key": "3641503", "vector": [], "sparse_vector": [], "title": "OmniTrack: A Flexible Self-Tracking Approach Leveraging Semi-Automated Tracking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Jinwook Seo"], "summary": "We now see an increasing number of self-tracking apps and wearable devices. Despite the vast number of available tools, however, it is still challenging for self-trackers to find apps that suit their unique tracking needs, preferences, and commitments. Furthermore, people are bounded by the tracking tools’ initial design because it is difficult to modify, extend, or mash up existing tools. In this paper, we present OmniTrack, a mobile self-tracking system, which enables self-trackers to construct their own trackers and customize tracking items to meet their individual tracking needs. To inform the OmniTrack design, we first conducted semi-structured interviews (N = 12) and analyzed existing mobile tracking apps (N = 62). We then designed and developed OmniTrack as an Android mobile app, leveraging a semi-automated tracking approach that combines manual and automated tracking methods. We evaluated OmniTrack through a usability study (N = 10) and improved its interfaces based on the feedback. Finally, we conducted a 3-week deployment study (N = 21) to assess if people can capitalize on OmniTrack’s flexible and customizable design to meet their tracking needs. From the study, we showed how participants used OmniTrack to create, revise, and appropriate trackers—ranging from a simple mood tracker to a sophisticated daily activity tracker. We discuss how OmniTrack positively influences and supports self-trackers’ tracking practices over time, and how to further improve OmniTrack by providing more appropriate visualizations and sharable templates, incorporating external contexts, and supporting researchers’ unique data collection needs.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130930"}, {"primary_key": "3641504", "vector": [], "sparse_vector": [], "title": "TrailSense: A Crowdsensing System for Detecting Risky Mountain Trail Segments with Walking Pattern Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Trail surface information is critical in preventing from the mountain accidents such as falls and slips. In this paper, we propose a new mobile crowdsensing system that automatically infers whether trail segments are risky to climb by using sensor data collected from multiple hikers’ smartphones. We extract cyclic gait-based features from walking motion data to train machine learning models, and multiple hikers’ results are then aggregated for robust classification. We evaluate our system with two real-world datasets. First, we collected data from 14 climbers for a mountain trail which includes 13 risky segments. The average accuracy of individuals is approximately 80%, but after clustering the results, our system can accurately identify all the risky segments. We then collected an additional dataset from five climbers in two different mountain trails, which have 10 risky segments in total. Our results show that the model trained in one trail can be used to accurately identify all the risky segments in the other trail, which documents the generalizability of our system.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3131893"}, {"primary_key": "3641505", "vector": [], "sparse_vector": [], "title": "Automatic Calibration of High Density Electric Muscle Stimulation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Electric muscle stimulation (EMS) can enable mobile force feedback, support pedestrian navigation, or confer object affordances. To date, however, EMS is limited by two interlinked problems. (1) EMS is low resolution -- achieving only coarse movements and constraining opportunities for exploration. (2) EMS requires time consuming, expert calibration -- confining these interaction techniques to the lab. EMS arrays have been shown to increase stimulation resolution, but as calibration complexity increases exponentially as more electrodes are used, we require heuristics or automated procedures for successful calibration. We explore the feasibility of using electromyography (EMG) to auto-calibrate high density EMS arrays. We determine regions of muscle activity during human-performed gestures, to inform stimulation patterns for EMS-performed gestures. We report on a study which shows that auto-calibration of a 60-electrode array is feasible: achieving 52% accuracy across six gestures, with 82% accuracy across our best three gestures. By highlighting the electrode-array calibration problem, and presenting a first exploration of a potential solution, this work lays the foundations for high resolution, wearable and, perhaps one day, ubiquitous EMS beyond the lab.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130933"}, {"primary_key": "3641506", "vector": [], "sparse_vector": [], "title": "An LSTM Based System for Prediction of Human Activities with Durations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Sanket V. Mehta", "<PERSON><PERSON>"], "summary": "Human activity prediction is an interesting problem with a wide variety of applications like intelligent virtual assistants, contextual marketing, etc. One formulation of this problem is jointly predicting human activities (viz. eating, commuting, etc.) with associated durations. Herein a deep learning system is proposed for this problem. Given a sequence of past activities and durations, the system estimates the probabilities for future activities and their durations. Two distinct Long-Short Term Memory (LSTM) networks are developed that cater to different assumptions about the data and achieve different modeling complexities and prediction accuracies. The networks are trained and tested with two real-world datasets, one being publicly available while the other collected from a field experiment. Modeling on the segment level public dataset mitigates the cold-start problem. Experiments indicate that compared to traditional approaches based on sequence mining or hidden Markov modeling, LSTM networks perform significantly better. The ability of LSTM networks to detect long term correlations in activity data is also demonstrated. The trained models are each less than 500KB in size and can be deployed to run in real-time on a mobile device without any dependencies on the cloud. This can help applications like mobile personal assistants by providing predictive context.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161201"}, {"primary_key": "3641507", "vector": [], "sparse_vector": [], "title": "Urban Impulses: Evoked Responses From Local Event Stimuli.", "authors": ["<PERSON>"], "summary": "In modeling human behavior, we expect people to make noticeable reactions to the events they witness. For people at a scheduled event like a concert or sports game, we can measure reactions by looking at geotagged social media posts. We work from a database of known events from a commercial ticket broker and a database of geotagged tweets to show how we can derive impulse response functions of tweet counts as event responses. Tweet counts typically rise in anticipation of the event and gradually fall after the event starts. We draw an analogy between evoked responses in functional magnetic resonance imaging (fMRI) from mental stimuli and social media responses from local event stimuli. Our analysis of event and tweet data shows that our derived impulse responses are statistically significant and that we can use the functions to accurately predict reactions to some event types. We give examples of impulse response functions derived from repeated events at different venues.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161193"}, {"primary_key": "3641509", "vector": [], "sparse_vector": [], "title": "Towards Calm Displays: Matching Ambient Illumination in Bedrooms.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a system for making emissive computer displays (LCDs) look like they are reflective, i.e. not emitting light but instead reflecting ambient light, an effect that we call a “calm display”. We achieve this effect by using a light sensor and a one-time calibration process to drive an algorithm which controls the display's backlight intensity and gamma correction functionality to continually match the brightness and chromaticity of the ambient light. We present an experimental evaluation of our system, showing quantitatively that the color and brightness output by our system is perceptually close to that of a piece of paper under similar lighting conditions. We argue that calm displays can more easily fade into the background, and further that they are more suitable for environments such as bedrooms where glowing displays are often out-of-place. We validate these claims and more generally explore users’ perception of calm displays, through a field study of an LCD display deployed in participants’ bedrooms.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3090081"}, {"primary_key": "3641513", "vector": [], "sparse_vector": [], "title": "Gazture: Design and Implementation of a Gaze based Gesture Control System on Tablets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present Gazture, a light-weight gaze based real-time gesture control system on commercial tablets. Unlike existing approaches that require dedicated hardware (e.g., high resolution camera), high computation overhead (powerful CPU) or specific user behavior (keeping head steady), Gazture provides gesture recognition based on easy-to-control user gaze input with a small overhead. To achieve this goal, Gazture incorporates a two-layer structure: The first layer focuses on real-time gaze estimation with acceptable tracking accuracy while incurring a small overhead. The second layer implements a robust gesture recognition algorithm while compensating gaze estimation error. To address user posture change while using mobile device, we design a online transfer function based method to convert current eye features into corresponding eye features in reference posture, which then facilitates efficient gaze position estimation. We implement Gazture on Lenovo Tab3 8 Plus tablet with Android 6.0.1, and evaluate its performance in different scenarios. The evaluation results show that Gazture can achieve a high accuracy in gesture recognition while incurring a low overhead.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130939"}, {"primary_key": "3641514", "vector": [], "sparse_vector": [], "title": "Auto++: Detecting Cars Using Embedded Microphones in Real-Time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this work, we propose a system that detects approaching cars for smartphone users. In addition to detecting the presence of a vehicle, it can also estimate the vehicle’s driving direction, as well as count the number of cars around the user. We achieve these goals by processing the acoustic signal captured by microphones embedded in the user’s mobile phone. The largest challenge we faced involved addressing the fact that vehicular noise is predominantly due to tire-road friction, and therefore lacked strong (frequency) formant or temporal structure. Additionally, outdoor environments have complex acoustic noise characteristics, which are made worse when the signal is captured by non-professional grade microphones embedded in smartphones. We address these challenges by monitoring a new feature: maximal frequency component that crosses a threshold. We extract this feature with a blurred edge detector. Through detailed experiments, we found our system to be robust across different vehicles and environmental conditions, and thereby support unsupervised car detection and counting. We evaluated our system using audio tracks recorded from seven different models of cars, including SUVs, medium-sized sedans, compact cars, and electric cars. We also tested our system with the user walking in various outdoor environments including parking lots, campus roads, residential areas, and shopping centers. Our results show that we can accurately and robustly detect cars with low CPU and memory requirements.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130938"}, {"primary_key": "3641516", "vector": [], "sparse_vector": [], "title": "Reconstructing Hand Poses Using Visible Light.", "authors": ["T<PERSON>xing Li", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Free-hand gestural input is essential for emerging user interactions. We present <PERSON><PERSON>, a table lamp reconstructing a 3D hand skeleton in real time, requiring neither cameras nor on-body sensing devices. <PERSON><PERSON> consists of an LED panel in a lampshade and a few low-cost photodiodes embedded in the lamp base. To reconstruct a hand skeleton, <PERSON><PERSON> combines 2D binary blockage maps from vantage points of different photodiodes, which describe whether a hand blocks light rays from individual LEDs to all photodiodes. Empowering a table lamp with sensing capability, <PERSON><PERSON> can be seamlessly integrated into the existing environment. Relying on such low-level cues, <PERSON><PERSON> entails lightweight computation and is inherently privacy-preserving. We build and evaluate an <PERSON><PERSON> prototype. Results show that <PERSON><PERSON>’s algorithm reconstructs a hand pose within 7.2 ms on average, with 10.2° mean angular deviation and 2.5-mm mean translation deviation in comparison to Leap Motion. We also conduct user studies to examine the privacy issues of Leap Motion and solicit feedback on <PERSON><PERSON>’s privacy protection. We conclude by demonstrating various interaction applications <PERSON><PERSON> enables.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130937"}, {"primary_key": "3641517", "vector": [], "sparse_vector": [], "title": "IndoTrack: Device-Free Indoor Human Tracking with Commodity Wi-Fi.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Zhang", "Qin Lv", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Hong Mei"], "summary": "Indoor human tracking is fundamental to many real-world applications such as security surveillance, behavioral analysis, and elderly care. Previous solutions usually require dedicated device being carried by the human target, which is inconvenient or even infeasible in scenarios such as elderly care and break-ins. However, compared with device-based tracking, device-free tracking is particularly challenging because the much weaker reflection signals are employed for tracking. The problem becomes even more difficult with commodity Wi-Fi devices, which have limited number of antennas, small bandwidth size, and severe hardware noise. In this work, we propose IndoTrack, a device-free indoor human tracking system that utilizes only commodity Wi-Fi devices. IndoTrack is composed of two innovative methods: (1) Doppler-MUSIC is able to extract accurate Doppler velocity information from noisy Wi-Fi Channel State Information (CSI) samples; and (2) Doppler-AoA is able to determine the absolute trajectory of the target by jointly estimating target velocity and location via probabilistic co-modeling of spatial-temporal Doppler and AoA information. Extensive experiments demonstrate that IndoTrack can achieve a 35cm median error in human trajectory estimation, outperforming the state-of-the-art systems and provide accurate location and velocity information for indoor human mobility and behavioral analysis.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130940"}, {"primary_key": "3641518", "vector": [], "sparse_vector": [], "title": "Progress Estimation and Phase Detection for Sequential Processes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Process modeling and understanding are fundamental for advanced human-computer interfaces and automation systems. Most recent research has focused on activity recognition, but little has been done on sensor-based detection of process progress. We introduce a real-time, sensor-based system for modeling, recognizing and estimating the progress of a work process. We implemented a multimodal deep learning structure to extract the relevant spatio-temporal features from multiple sensory inputs and used a novel deep regression structure for overall completeness estimation. Using process completeness estimation with a Gaussian mixture model, our system can predict the phase for sequential processes. The performance speed, calculated using completeness estimation, allows online estimation of the remaining time. To train our system, we introduced a novel rectified hyperbolic tangent (rtanh) activation function and conditional loss. Our system was tested on data obtained from the medical process (trauma resuscitation) and sports events (Olympic swimming competition). Our system outperformed the existing trauma-resuscitation phase detectors with a phase detection accuracy of over 86%, an F1-score of 0.67, a completeness estimation error of under 12.6%, and a remaining-time estimation error of less than 7.5 minutes. For the Olympic swimming dataset, our system achieved an accuracy of 88%, an F1-score of 0.58, a completeness estimation error of 6.3% and a remaining-time estimation error of 2.9 minutes.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130936"}, {"primary_key": "3641519", "vector": [], "sparse_vector": [], "title": "SHOW: Smart Handwriting on Watches.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Smart watch is becoming a new gateway through which people stay connected and track everyday activities, and text-entry on it is becoming a frequent need. With the two de facto solutions: tap-on-screen and voice input, text-entry on the watch remains a tedious task because 1. Tap-on-screen is error prone due to the small screen; 2. Voice input is strongly constrained by the surroundings and suffers from privacy leak. In this paper, we propose SHOW, which enables the user to input as they handwrite on horizontal surfaces, and the only requirement is to use the elbow as the support point. SHOW captures the gyroscope and accelerometer traces and deduces the user's handwriting thereafter. SHOW differs from previous work of gesture recognition in that: 1. it employs a novel rotation injection technique to substantially reduce the effort of data collection; 2. it does not require whole-arm posture, hence is better suited to space-limited places (e.g. vehicles). Our experiments show that SHOW can effectively generate 60 traces from one real handwriting trace and achieve high accuracy at 99.9% when recognizing the 62 different characters written by 10 volunteers. Furthermore, having more screen space after removing the virtual keyboard, SH<PERSON> can display 4x candidate words for autocompletion. Aided by the tolerance of character ambiguity and accurate character recognition, SHOW achieves over 70% lower mis-recognition-rate, 43% lower no-response-rate in both daily and general purposed text-entry scenarios, and 33.3% higher word suggestion coverage than the tap-on-screen method using a virtual QWERTY keyboard.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161412"}, {"primary_key": "3641520", "vector": [], "sparse_vector": [], "title": "Guessing Attacks on User-Generated Gesture Passwords.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Touchscreens, the dominant input type for mobile phones, require unique authentication solutions. Gesture passwords have been proposed as an alternative ubiquitous authentication technique. Prior security analysis has relied on inconsistent measurements such as mutual information or shoulder surfing attacks.We present the first approach for measuring the security of gestures with guessing attacks that model real-world attacker behavior. Our major contributions are: 1) a comprehensive analysis of the weak subspace for gesture passwords, 2) a method for enumerating the size of the full theoretical gesture password space, 3) a design of a novel guessing attack against user-chosen gestures using a dictionary, and 4) a brute-force attack used for benchmarking the performance of the guessing attack. Our dictionary attack, tested on newly collected user data, achieves a cracking rate of 47.71% after two weeks of computation using 109 guesses. This is a difference of 35.78 percentage points compared to the 11.93% cracking rate of the brute-force attack. In conclusion, users are not taking full advantage of the large theoretical password space and instead choose their gesture passwords from weak subspaces. We urge for further work on addressing this challenge.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3053331"}, {"primary_key": "3641522", "vector": [], "sparse_vector": [], "title": "Your Search Path Tells Others Where to Park: Towards Fine-Grained Parking Availability Crowdsourcing Using Parking Decision Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A main challenge faced by the state-of-the-art parking sensing systems is to infer the state of the spots not covered by participants’ parking/unparking events (called background availability) when the system penetration rate is limited. In this paper, we tackle this problem by exploring an empirical phenomenon that ignoring a spot along a driver’s parking search trajectory is likely due to the unavailability. However, complications caused by drivers’ preferences, e.g. ignoring the spots too far from the driver’s destination, have to be addressed based on human parking decisions. We build a model based on a dataset of more than 55,000 real parking decisions to predict the probability that a driver would take a spot, assuming the spot is available. Then, we present a crowdsourcing system, called ParkScan, which leverages the learned parking decision model in collaboration with the hidden Markov model to estimate background parking spot availability. We evaluated ParkScan with real-world data from both off-street scenarios (i.e., two public parking lots) and an on-street parking scenario (i.e., 35 urban blocks in Seattle). Both of the experiments showed that with a 5% penetration rate, ParkScan reduces over 12.9% of availability estimation errors for all the spots during parking peak hours, compared to the baseline using only the historical data. Also, even with a single participant driver, ParkScan cuts off at least 15% of the estimation errors for the spots along the driver’s parking search trajectory.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130942"}, {"primary_key": "3641523", "vector": [], "sparse_vector": [], "title": "UbiEar: Bringing Location-independent Sound Awareness to the Hard-of-hearing People with Smartphones.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Junz<PERSON> Du", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Non-speech sound-awareness is important to improve the quality of life for the deaf and hard-of-hearing (DHH) people. DHH people, especially the young, are not always satisfied with their hearing aids. According to the interviews with 60 young hard-of-hearing students, a ubiquitous sound-awareness tool for emergency and social events that works in diverse environments is desired. In this paper, we design UbiEar, a smartphone-based acoustic event sensing and notification system. Core techniques in UbiEar are a light-weight deep convolution neural network to enable location-independent acoustic event recognition on commodity smartphons, and a set of mechanisms for prompt and energy-efficient acoustic sensing. We conducted both controlled experiments and user studies with 86 DHH students and showed that UbiEar can assist the young DHH students in awareness of important acoustic events in their daily life.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3090082"}, {"primary_key": "3641524", "vector": [], "sparse_vector": [], "title": "PRADO: Predicting App Adoption by Learning the Correlation between Developer-Controllable Properties and User Behaviors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "To survive and stand out from the fierce market competition nowadays, it is critical for app developers to know (desirably ahead of time) whether, how well, and why their apps would be adopted by users. Ideally, the adoption of an app could be predicted by factors that can be controlled by app developers in the development process, and factors that app developers are able to take actions on and improve according to the predictions. To this end, this paper proposes PRADO, an approach to measuring various aspects of user adoption, including app download and installation, uninstallation, and user ratings. PRADO employs advanced machine learning algorithms to predict user adoption based on how these metrics correlate to a comprehensive taxonomy of 108 developer-controllable features of the app. To evaluate PRADO, we use 9,824 free apps along with their behavioral data from 12.57 million Android users, demonstrating that user adoption of a new app can be accurately predicted. We also derive insights on which factors are statistically significant to user adoption, and suggest what kinds of actions can be possibly performed by developers in practice.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130944"}, {"primary_key": "3641525", "vector": [], "sparse_vector": [], "title": "Snoopy: Sniffing Your Smartwatch Passwords via Deep Sequence Learning.", "authors": ["<PERSON>", "<PERSON>", "Hongkai Wen", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Demand for smartwatches has taken off in recent years with new models which can run independently from smartphones and provide more useful features, becoming first-class mobile platforms. One can access online banking or even make payments on a smartwatch without a paired phone. This makes smartwatches more attractive and vulnerable to malicious attacks, which to date have been largely overlooked. In this paper, we demonstrate Snoopy, a password extraction and inference system which is able to accurately infer passwords entered on Android/Apple watches within 20 attempts, just by eavesdropping on motion sensors. <PERSON>noopy uses a uniform framework to extract the segments of motion data when passwords are entered, and uses novel deep neural networks to infer the actual passwords. We evaluate the proposed Snoopy system in the real-world with data from 362 participants and show that our system offers a ~ 3-fold improvement in the accuracy of inferring passwords compared to the state-of-the-art, without consuming excessive energy or computational resources. We also show that Snoopy is very resilient to user and device heterogeneity: it can be trained on crowd-sourced motion data (e.g. via Amazon Mechanical Turk), and then used to attack passwords from a new user, even if they are wearing a different model. This paper shows that, in the wrong hands, Snoopy can potentially cause serious leaks of sensitive information. By raising awareness, we invite the community and manufacturers to revisit the risks of continuous motion sensing on smart wearable devices.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161196"}, {"primary_key": "3641527", "vector": [], "sparse_vector": [], "title": "BlindType: Eyes-Free Text Entry on Handheld Touchpad by Leveraging Thumb&apos;s Muscle Memory.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sheng<PERSON> Zhao"], "summary": "Eyes-free input is desirable for ubiquitous computing, since interacting with mobile and wearable devices often competes for visual attention with other devices and tasks. In this paper, we explore eyes-free typing on a touchpad using one thumb, wherein a user taps on an imaginary QWERTY keyboard while receiving text feedback on a separate screen. Our hypothesis is that users can transfer their typing ability obtained from visible keyboards to eyes-free use. We propose two statistical decoding algorithms to infer users’ eyes-free input: the absolute algorithm and the relative algorithm. The absolute algorithm infers user input based on the absolute position of touch endpoints, while the relative algorithm infers based on the vectors between successive touch endpoints. Evaluation results showed users could achieve satisfying performance with both algorithms. Text entry rate was 17-23 WPM (words per minute) depending on the algorithm used. In comparison, a baseline cursor-based text entry method yielded only 7.66 WPM. In conclusion, our research demonstrates for the first time the feasibility of thumb-based eyes-free typing, which provides a new possibility for text entry on ubiquitous computing platforms such as smart TVs and HMDs.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3090083"}, {"primary_key": "3641530", "vector": [], "sparse_vector": [], "title": "BiliScreen: Smartphone-Based Scleral Jaundice Monitoring for Liver and Pancreatic Disorders.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Shwetak N. Patel"], "summary": "Pancreatic cancer has one of the worst survival rates amongst all forms of cancer because its symptoms manifest later into the progression of the disease. One of those symptoms is jaundice, the yellow discoloration of the skin and sclera due to the buildup of bilirubin in the blood. Jaundice is only recognizable to the naked eye in severe stages, but a ubiquitous test using computer vision and machine learning can detect milder forms of jaundice. We propose BiliScreen, a smartphone app that captures pictures of the eye and produces an estimate of a person's bilirubin level, even at levels normally undetectable by the human eye. We test two low-cost accessories that reduce the effects of external lighting: (1) a 3D-printed box that controls the eyes' exposure to light and (2) paper glasses with colored squares for calibration. In a 70-person clinical study, we found that BiliScreen with the box achieves a Pearson correlation coefficient of 0.89 and a mean error of -0.09 ± 2.76 mg/dl in predicting a person's bilirubin level. As a screening tool, BiliScreen identifies cases of concern with a sensitivity of 89.7% and a specificity of 96.8% with the box accessory.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3090085"}, {"primary_key": "3641531", "vector": [], "sparse_vector": [], "title": "PupilScreen: Using Smartphones to Assess Traumatic Brain Injury.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Shwetak N. Patel"], "summary": "Before a person suffering from a traumatic brain injury reaches a medical facility, measuring their pupillary light reflex (PLR) is one of the few quantitative measures a clinician can use to predict their outcome. We propose PupilScreen, a smartphone app and accompanying 3D-printed box that combines the repeatability, accuracy, and precision of a clinical device with the ubiquity and convenience of the penlight test that clinicians regularly use in emergency situations. The PupilScreen app stimulates the patient's eyes using the smartphone's flash and records the response using the camera. The PupilScreen box, akin to a head-mounted virtual reality display, controls the eyes' exposure to light. The recorded video is processed using convolutional neural networks that track the pupil diameter over time, allowing for the derivation of clinically relevant measures. We tested two different network architectures and found that a fully convolutional neural network was able to track pupil diameter with a median error of 0.30 mm. We also conducted a pilot clinical evaluation with six patients who had suffered a TBI and found that clinicians were almost perfect when separating unhealthy pupillary light reflexes from healthy ones using PupilScreen alone.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3131896"}, {"primary_key": "3641532", "vector": [], "sparse_vector": [], "title": "Moving Beyond Market Research: Demystifying Smartphone User Behavior in India.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Large-scale mobile data studies can reveal valuable insights into user behavior, which in turn can assist system designers to create better user experiences. After a careful review of existing mobile data literature, we found that there have been no large-scale studies to understand smartphone usage behavior in India -- the second-largest and fastest growing smartphone market in the world. With the goal of understanding various facets of smartphone usage in India, we conducted a mixed-method longitudinal data collection study through an Android app released on Google Play. Our app was installed by 215 users, and logged 11.9 million data points from them over a period of 8 months. We analyzed this rich dataset along the lines of four broad facets of smartphone behavior -- how users use different apps, interact with notihcations, react to different contexts, and charge their smartphones -- to paint a holistic picture of smartphone usage behavior of Indian users. This quantitative analysis was complemented by a survey with 55 users and semi-structured interviews with 26 users to deeply understand their smartphone usage behavior. While our first-of-its-kind study uncovered many interesting facts about Indian smartphone users, we also found striking differences in usage behavior compared to past studies in other geographical contexts. We observed that Indian users spend significant time with their smartphones after midnight, continuously check notifications without attending to them and are extremely conscious about their smartphones’ battery. Perhaps the most dramatic finding is the nature of mobile consumerism of Indian users as shown by our results. Taken together, these and the rest of our findings demonstrate the unique characteristics that are shaping the smartphone usage behavior of Indian users.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130947"}, {"primary_key": "3641533", "vector": [], "sparse_vector": [], "title": "On Active Passengering: Supporting In-Car Experiences.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We describe the development of an interactive car window system designed to support passengers in engaging with the external environment during a journey. Through advances in embedded digital technologies, cars increasingly have a potential to become interactive spaces, in which passengers will find it possible to interact with the external environment through in-car interfaces. However the utility and benefit of such interactive systems for passengers has not been well studied. There is a need therefore, to study the design and use of these technologies, as they are emerging. We thus investigated how digital technology might support passengers' interactions with the external environment. Through a focus group (n=6) and interviews (n=5) we investigated passengers' attitudes towards, and practices during, ordinary car journeys. From this scoping study we formulated five design considerations for designing/implementing a prototype interactive car-window system. This system was then evaluated through an in-lab user study (n=8). Qualitative thematic analysis of interviews during the user study suggested a variety of orientations towards ‘passengering’, the act of being a passenger, on a journey. Herein we critically examine the role of our interactive technology in supporting desired experiences of ‘active passengering’.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161176"}, {"primary_key": "3641536", "vector": [], "sparse_vector": [], "title": "Recognizing Eating from Body-Worn Sensors: Combining Free-living and Laboratory Data.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Automated dietary monitoring solutions that can find when, what, and how much individuals consume are needed for many applications such as providing feedback to individuals with chronic disease. Advances in body-worn sensors have led to systems with high accuracy for finding meals and even which foods are consumed in each bite. However, most tests are done in controlled lab settings with restricted meal choices, little background noise, and subjects focused on eating. For these systems to be adopted by users it is critical that they work well in realistic situations and be able to handle confounding factors such as background noise, shared meals, and multi-tasking. Work in realistic environments usually has lower accuracy, but has challenges in determining ground truth. Most critically, there has been a significant gap between lab and free-living environments. This is compounded by data usually being collected for different individuals in each setting, making it difficult to determine how the accuracy gap can be closed. We present a multi-modality study on eating recognition, using body-worn motion (head, wrists) and audio (earbud microphone) sensors for 12 participants (6 from the lab study, 6 new to test generalizability). In contrast to the lab, where audio alone has the highest accuracy, we find now that a combination of sensing modalities (audio, motion) is needed; yet sensor placement (head vs. wrist) is not critical. We further find that lab data does generalize to other participants, but while personal free-living data improves accuracy, more data from others can actually lead to worse performance.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3131894"}, {"primary_key": "3641539", "vector": [], "sparse_vector": [], "title": "CovertBand: Activity Information Leakage using Music.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper contributes a novel method for low-cost, covert physical sensing and, by doing so, surfaces new privacy threats. We demonstrate how a smartphone and portable speaker playing music with embedded, inaudible signals can track multiple individuals’ locations and activities both within a room and through barriers in 2D space. We achieve this by transforming a smartphone into an active sonar system that emits a combination of a sonar pulse and music and listens to the reflections off of humans in the environment. Our implementation, CovertBand, monitors minute changes to these reflections to track multiple people concurrently and to recognize different types of motion, leaking information about where people are in addition to what they may be doing. We evaluated CovertBand by running experiments in five homes in the Seattle area, showing that we can localize both single and multiple individuals through barriers. These tests show CovertBand can track walking subjects with a mean tracking error of 18 cm and subjects moving at a fixed position with an accuracy of 8 cm at up to 6 m in line-of-sight and 3 m through barriers. We test a variety of rhythmic motions such as pumping arms, jumping, and supine pelvic tilts in through-wall scenarios and show that they produce discernibly different spectrograms from walking in the acoustic reflections. In tests with 33 subjects, we also show that even in ideal scenarios, listeners were unlikely to detect a CovertBand attack.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3131897"}, {"primary_key": "3641540", "vector": [], "sparse_vector": [], "title": "Can Less be More?: Contrasting Limited, Unlimited, and Automatic Picture Capture for Augmenting Memory Recall.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Today's abundance1 of cheap digital storage in the form of tiny memory cards put literally no bounds on the number of images one can capture with one's digital camera or smartphone during an event. However, prior work has shown that taking many pictures may actually make us remember less of a particular event. Does automated picture taking (lifelogging) help avoid this, yet still offer to capture meaningful pictures? In this work, we investigate the effect of capture modality (i.e., limited, unlimited, automatic, and no capture) on people's ability to recall a past event – with and without the support of the pictures captured through these modalities. Our results from a field experiment with 83 participants show that capturing fewer pictures does not necessarily lead to the capture of more relevant pictures. However, when controlling for number of pictures taken, our results show that having a limited number of pictures to capture may lead to pictures with increased memory value. At the same time, automated capture failed to produce pictures that would help remember the past experience better.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3090086"}, {"primary_key": "3641541", "vector": [], "sparse_vector": [], "title": "Detecting State Changes of Indoor Everyday Objects using Wi-Fi Channel State Information.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Detecting the events of indoor everyday objects such as door or window open/close events has been actively studied to implement such applications as intrusion detection, adaptive HVAC control, and monitoring an independently living elderly person. This study proposes a method for detecting the events and states of indoor everyday objects such as doors and windows without using distributed sensors attached to the objects. In this study, we achieve practical and unobtrusive event detection using a commodity Wi-Fi access point and a computer equipped with a commodity Wi-Fi module. Specifically, we detect the events using Wi-Fi channel state information (CSI), which describes how a signal propagates from a transmitter to a receiver, and is affected by such events. To handle CSI data that consists of the mixed effects of multiple indoor objects in an environment of interest, we employ independent component analysis to separate the events caused by the objects. The decomposed data are then fed into our event classifier based on convolutional and recurrent neural networks to automatically extract features from CSI data, as it is difficult to intuitively design features to be extracted from the CSI data. Moreover, we correct the neural network estimates by incorporating knowledge about the state transitions of an object using hidden Markov models. For example, because the “open” event of a door occurs only when the door is in a “closed” state. We correct impossible state transitions estimated by the neural network based on this knowledge.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3131898"}, {"primary_key": "3641543", "vector": [], "sparse_vector": [], "title": "FootprintID: Indoor Pedestrian Identification through Ambient Structural Vibration Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present FootprintID, an indoor pedestrian identification system that utilizes footstep-induced structural vibration to infer pedestrian identities for enabling various smart building applications. Previous studies have explored other sensing methods, including vision-, RF-, mobile-, and acoustic-based methods. They often require specific sensing conditions, including line-of-sight, high sensor density, and carrying wearable devices. Vibration-based methods, on the other hand, provide easy-to-install sparse sensing and utilize gait to distinguish different individuals. However, the challenge for these methods is that the signals are sensitive to the gait variations caused by different walking speeds and the floor variations caused by structural heterogeneity. We present FootprintID, a vibration-based approach that achieves robust pedestrian identification. The system uses vibration sensors to detect footstep-induced vibrations. It then selects vibration signals and classifiers to accommodate sensing variations, taking step location and frequency into account. We utilize the physical insight on how individual step signal changes with walking speeds and introduce an iterative transductive learning algorithm (ITSVM) to achieve robust classification with limited labeled training data. When trained only on the average walking speed and tested on different walking speeds, FootprintID achieves up to 96% accuracy and a 3X improvement in extreme speeds compared to the Support Vector Machine. Furthermore, it achieves up to 90% accuracy (1.5X improvement) in uncontrolled experiments.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130954"}, {"primary_key": "3641546", "vector": [], "sparse_vector": [], "title": "Improving Input Accuracy on Smartphones for Persons who are Affected by Tremor using Motion Sensors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Witali Hepting", "<PERSON>", "<PERSON>"], "summary": "Having a hand tremor often complicates interactions with touchscreens on mobile devices. Due to the uncontrollable oscillations of both hands, hitting targets can be hard, and interaction can be slow. Correcting input needs additional time and mental effort. We propose a method for automatically correcting such inputs based on motion data, gathered both with the devices' sensors and a small wearable sensor on the finger used for tapping. The development was informed by interviews with persons with tremor. Two empirical studies showed that our method, involving both smartphone and finger motion sensors without changing the user interface, allows users with tremor to select objects with up to 40 % fewer misses.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161169"}, {"primary_key": "3641547", "vector": [], "sparse_vector": [], "title": "Microinteraction Ecological Momentary Assessment Response Rates: Effect of Microinteractions or the Smartwatch?", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Mobile-based ecological-momentary-assessment (EMA) is an in-situ measurement methodology where an electronic device prompts a person to answer questions of research interest. EMA has a key limitation: interruption burden. Microinteraction-EMA(µEMA) may reduce burden without sacrificing high temporal density of measurement. In µEMA, all EMA prompts can be answered with ‘at a glance' microinteractions. In a prior 4-week pilot study comparing standard EMA delivered on a phone (phone-EMA) vs. µEMA delivered on a smartwatch (watch-µEMA), watch-µEMA demonstrated higher response rates and lower perceived burden than phone-EMA, even when the watch-µEMA interruption rate was 8 times more than phone-EMA. A new 4-week dataset was gathered on smartwatch-based EMA (i.e., watch-EMA with 6 back-to-back, multiple-choice questions on a watch) to compare whether the high response rates of watch-µEMA previously observed were a result of using microinteractions, or due to the novelty and accessibility of the smartwatch. No statistically significant differences in compliance, completion, and first-prompt response rates were observed between phone-EMA and watch-EMA. However, watch-µEMA response rates were significantly higher than watch-EMA. This pilot suggests that (1) the high compliance and low burden previously observed in watch-µEMA is likely due to the microinteraction question technique, not simply the use of the watch versus the phone, and that (2) compliance with traditional EMA (with long surveys) may not improve simply by moving survey delivery from the phone to a smartwatch.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130957"}, {"primary_key": "3641548", "vector": [], "sparse_vector": [], "title": "Logging you, Logging me: A Replicable Study of Privacy and Sharing Behaviour in Groups of Visual Lifeloggers.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Low cost digital cameras in smartphones and wearable devices make it easy for people to automatically capture and share images as a visual lifelog. Having been inspired by a US campus based study that explored individual privacy behaviours of visual lifeloggers, we conducted a similar study on a UK campus, however we also focussed on the privacy behaviours of groups of lifeloggers. We argue for the importance of replicability and therefore we built a publicly available toolkit, which includes camera design, study guidelines and source code. Our results show some similar sharing behaviour to the US based study: people tried to preserve the privacy of strangers, but we found fewer bystander reactions despite using a more obvious camera. In contrast, we did not find a reluctance to share images of screens but we did find that images of vices were shared less. Regarding privacy behaviours in groups of lifeloggers, we found that people were more willing to share images of people they were interacting with than of strangers, that lifelogging in groups could change what defines a private space, and that lifelogging groups establish different rules to manage privacy for those inside and outside the group.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3090087"}, {"primary_key": "3641549", "vector": [], "sparse_vector": [], "title": "Multimodal Deep Learning for Activity and Context Recognition.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Wearables and mobile devices see the world through the lens of half a dozen low-power sensors, such as, barometers, accelerometers, microphones and proximity detectors. But differences between sensors ranging from sampling rates, discrete and continuous data or even the data type itself make principled approaches to integrating these streams challenging. How, for example, is barometric pressure best combined with an audio sample to infer if a user is in a car, plane or bike? Critically for applications, how successfully sensor devices are able to maximize the information contained across these multi-modal sensor streams often dictates the fidelity at which they can track user behaviors and context changes. This paper studies the benefits of adopting deep learning algorithms for interpreting user activity and context as captured by multi-sensor systems. Specifically, we focus on four variations of deep neural networks that are based either on fully-connected Deep Neural Networks (DNNs) or Convolutional Neural Networks (CNNs). Two of these architectures follow conventional deep models by performing feature representation learning from a concatenation of sensor types. This classic approach is contrasted with a promising deep model variant characterized by modality-specific partitions of the architecture to maximize intra-modality learning. Our exploration represents the first time these architectures have been evaluated for multimodal deep learning under wearable data -- and for convolutional layers within this architecture, it represents a novel architecture entirely. Experiments show these generic multimodal neural network models compete well with a rich variety of conventional hand-designed shallow methods (including feature extraction and classifier construction) and task-specific modeling pipelines, across a wide-range of sensor types and inference tasks (four different datasets). Although the training and inference overhead of these multimodal deep approaches is in some cases appreciable, we also demonstrate the feasibility of on-device mobile and wearable execution is not a barrier to adoption. This study is carefully constructed to focus on multimodal aspects of wearable data modeling for deep learning by providing a wide range of empirical observations, which we expect to have considerable value in the community. We summarize our observations into a series of practitioner rules-of-thumb and lessons learned that can guide the usage of multimodal deep learning for activity and context detection.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161174"}, {"primary_key": "3641550", "vector": [], "sparse_vector": [], "title": "Eyes-Free Art: Exploring Proxemic Audio Interfaces For Blind and Low Vision Art Engagement.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Engagement in the arts1 is an important component of participation in cultural activities, but remains a largely unaddressed challenge for people with sensory disabilities. Visual arts are generally inaccessible to people with visual impairments due to their inherently visual nature. To address this, we present Eyes-Free Art, a design probe to explore the use of proxemic audio for interactive sonic experiences with 2D art work. The proxemic audio interface allows a user to move closer and further away from a painting to experience background music, a novel sonification, sound effects, and a detailed verbal description. We conducted a lab study by creating interpretations of five paintings with 13 people with visual impairments and found that participants enjoyed interacting with the artwork. We then created a live installation with a visually impaired artist to iterate on this concept to account for multiple users and paintings. We learned that a proxemic audio interface allows for people to feel immersed in the artwork. Proxemic audio interfaces are similar to visual because they increase in detail with closer proximity, but are different because they need a descriptive verbal overview to give context. We present future research directions in the space of proxemic audio interactions.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130958"}, {"primary_key": "3641551", "vector": [], "sparse_vector": [], "title": "Label Propagation: An Unsupervised Similarity Based Method for Integrating New Sensors in Activity Recognition Systems.", "authors": ["Vitor F. Rey", "<PERSON>"], "summary": "Current activity recognition systems mostly work with static, pre-trained sensor configuration. As a consequence they are not able to leverage new sensors appearing in their environment (e.g. the user buying a new wearable devices). In this work we present a method inspired by semi-supervised graph methods that can add new sensors to an existing system in an unsupervised manner. We have evaluated our method in two well known activity recognition datasets and found that it can take advantage of the information provided by new unknown sensor sources, improving the recognition performance in most cases.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130959"}, {"primary_key": "3641552", "vector": [], "sparse_vector": [], "title": "SynchroWatch: One-Handed Synchronous Smartwatch Gestures Using Correlation and Magnetic Sensing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>tein", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "SynchroWatch is a one-handed interaction technique for smartwatches that uses rhythmic correlation between a user's thumb movement and on-screen blinking controls. Our technique uses magnetic sensing to track the synchronous extension and reposition of the thumb, augmented with a passive magnetic ring. The system measures the relative changes in the magnetic field induced by the required thumb movement and uses a time-shifted correlation approach with a reference waveform for detection of synchrony. We evaluated the technique during three distraction tasks with varying degrees of hand and finger movement: active walking, browsing on a computer, and relaxing while watching online videos. Our initial offline results suggest that intentional synchronous gestures can be distinguished from other movement. A second evaluation using a live implementation of the system running on a smartwatch suggests that this technique is viable for gestures used to respond to notifications or issue commands. Finally, we present three demonstration applications that highlight the technique running in real-time on the smartwatch.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161162"}, {"primary_key": "3641553", "vector": [], "sparse_vector": [], "title": "Comparing Speech and Keyboard Text Entry for Short Messages in Two Languages on Touchscreen Phones.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "With the ubiquity of mobile touchscreen devices like smartphones, two widely used text entry methods have emerged: small touch-based keyboards and speech recognition. Although speech recognition has been available on desktop computers for years, it has continued to improve at a rapid pace, and it is currently unknown how today's modern speech recognizers compare to state-of-the-art mobile touch keyboards, which also have improved considerably since their inception. To discover both methods' \"upper-bound performance,\" we evaluated them in English and Mandarin Chinese on an Apple iPhone 6 Plus in a laboratory setting. Our experiment was carried out using Baidu's Deep Speech 2, a deep learning-based speech recognition system, and the built-in Qwerty (English) or Pinyin (Mandarin) Apple iOS keyboards. We found that with speech recognition, the English input rate was 2.93 times faster (153 vs. 52 WPM), and the Mandarin Chinese input rate was 2.87 times faster (123 vs. 43 WPM) than the keyboard for short message transcription under laboratory conditions for both methods. Furthermore, although speech made fewer errors during entry (5.30% vs. 11.22% corrected error rate), it left slightly more errors in the final transcribed text (1.30% vs. 0.79% uncorrected error rate). Our results show that comparatively, under ideal conditions for both methods, upper-bound speech recognition performance has greatly improved compared to prior systems, and might see greater uptake in the future, although further study is required to quantify performance in non-laboratory settings for both methods.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161187"}, {"primary_key": "3641554", "vector": [], "sparse_vector": [], "title": "Inferring Mood Instability on Social Media by Leveraging Ecological Momentary Assessments.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Active and passive sensing technologies are providing powerful mechanisms to track, model, and understand a range of health behaviors and well-being states. Despite yielding rich, dense and high fidelity data, current sensing technologies often require highly engineered study designs and persistent participant compliance, making them difficult to scale to large populations and to data acquisition tasks spanning extended time periods. This paper situates social media as a new passive, unobtrusive sensing technology. We propose a semi-supervised machine learning framework to combine small samples of data gathered through active sensing, with large-scale social media data to infer mood instability (MI) in individuals. Starting from a theoretically-grounded measure of MI obtained from mobile ecological momentary assessments (EMAs), we show that our model is able to infer MI in a large population of Twitter users with 96% accuracy and F-1 score. Additionally, we show that, our model predicts self-identifying Twitter users with bipolar and borderline personality disorder to exhibit twice the likelihood of high MI, compared to that in a suitable control. We discuss the implications and the potential for integrating complementary sensing capabilities to address complex research challenges in precision medicine.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130960"}, {"primary_key": "3641555", "vector": [], "sparse_vector": [], "title": "Distant Emotion Recognition.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Distant emotion recognition (DER) extends the application of speech emotion recognition to the very challenging situation that is determined by variable speaker to microphone distances. The performance of conventional emotion recognition systems degrades dramatically as soon as the microphone is moved away from the mouth of the speaker. This is due to a broad variety of effects such as background noise, feature distortion with distance, overlapping speech from other speakers, and reverberation. This paper presents a novel solution for DER, addressing the key challenges by identification and deletion of features from consideration which are significantly distorted by distance, creating a novel, called Emo2vec, feature modeling and overlapping speech filtering technique, and the use of an LSTM classifier to capture the temporal dynamics of speech states found in emotions. A comprehensive evaluation is conducted on two acted datasets (with artificially generated distance effect) as well as on a new emotional dataset of spontaneous family discussions with audio recorded from multiple microphones placed in different distances. Our solution achieves an average 91.6%, 90.1% and 89.5% accuracy for emotion happy, angry and sad, respectively, across various distances which is more than a 16% increase on average in accuracy compared to the best baseline method.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130961"}, {"primary_key": "3641556", "vector": [], "sparse_vector": [], "title": "CoCo: Collaboration Coach for Understanding Team Dynamics during Video Conferencing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present and discuss a fully-automated collaboration system, CoCo, that allows multiple participants to video chat and receive feedback through custom video conferencing software. After a conferencing session, a virtual feedback assistant provides insights on the conversation to participants. CoCo automatically pulls audial and visual data during conversations and analyzes the extracted streams for affective features, including smiles, engagement, attention, as well as speech overlap and turn-taking. We validated CoCo with 39 participants split into 10 groups. Participants played two back-to-back team-building games, Lost at Sea and Survival on the Moon, with the system providing feedback between the two. With feedback, we found a statistically significant change in balanced participation---that is, everyone spoke for an equal amount of time. There was also statistically significant improvement in participants' self-evaluations of conversational skills awareness, including how often they let others speak, as well as of teammates' conversational skills. The entire framework is available at https://github.com/ROC-HCI/CollaborationCoach_PostFeedback.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161186"}, {"primary_key": "3641557", "vector": [], "sparse_vector": [], "title": "Inferring Person-to-person Proximity Using WiFi Signals.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Today's societies are enveloped in an ever-growing telecommunication infrastructure. This infrastructure offers important opportunities for sensing and recording a multitude of human behaviors. Human mobility patterns are a prominent example of such a behavior which has been studied based on cell phone towers, Bluetooth beacons, and WiFi networks as proxies for location. While mobility is an important aspect of human behavior, it is also crucial to study physical interactions among individuals. Sensing proximity that enables social interactions on a large scale is a technical challenge and many commonly used approaches—including RFID badges or Bluetooth scanning—offer only limited scalability. Here we show that it is possible, in a scalable and robust way, to accurately infer person-to-person physical proximity from the lists of WiFi access points measured by smartphones carried by the two individuals. Based on a longitudinal dataset of approximately 800 participants with ground-truth Bluetooth proximity collected over a year, we show that our model performs better than the current state-of-the-art. Our results demonstrate the value of WiFi signals as a tool for social sensing and show how collections of WiFi data pose a potential threat to privacy.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3090089"}, {"primary_key": "3641559", "vector": [], "sparse_vector": [], "title": "MORP: Data-Driven Multi-Objective Route Planning and Optimization for Electric Vehicles.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The Wireless Power Transfer (WPT) system that enables in-motion charging (or wireless charging) for Electric Vehicles (EVs) has been introduced to resolve battery-related issues (such as long charging time, high cost, and short driving range) and increase the wide-acceptance of EVs. In this paper, we study the WPT system with the objectives of minimizing energy consumption, travel time, charging monetary cost on the way, and range anxiety for online EVs. Specifically, we propose the Multi-Objective Route Planner system (MORP) to guide EVs for the multi-objective routing. MORP incorporates two components: traffic state prediction and optimal route determination. For the traffic state prediction, we conducted analysis on a traffic dataset and observed spatial-temporal features of traffic patterns. Accordingly, we introduce the horizontal space-time Autoregressive Integrated Moving Average (ARIMA) model to predict vehicle counts (i.e., traffic volume) for locations with available historical traffic data. And, we use the spatial-temporal ordinary kriging method to predict vehicle counts for locations without historical traffic data. Based on vehicle counts, we use the non-parametric kernel regression method to predict velocity of road sections, which is used to predict travel time and then, energy consumption of a route of an EV with the help of the proposed energy consumption model. We also estimate charging monetary cost and EV related range anxiety based on unit energy cost, predicted travel time and energy consumption, and current onboard energy. We design four different cost functions (travel time, energy consumption, charging monetary cost, and range anxiety) of routing and formulate a multi-objective routing optimization problem. We use the predicted parameters as inputs of the optimization problem and find the optimal route using the adaptive epsilon constraint method. We evaluate our proposed MORP system in four different aspects (including traffic prediction, velocity prediction, energy consumption prediction, and EV routing). From the experimental studies, we find the effectiveness of the proposed MORP system in different aspects of the online EV routing system.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161408"}, {"primary_key": "3641561", "vector": [], "sparse_vector": [], "title": "OpenKeychain: An Architecture for Cryptography with Smart Cards and NFC Rings on Android.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "While many Android apps provide end-to-end encryption, the cryptographic keys are still stored on the device itself and can thus be stolen by exploiting vulnerabilities. External cryptographic hardware solves this issue, but is currently only used for two-factor authentication and not for communication encryption. In this paper, we design, implement, and evaluate an architecture for NFC-based cryptography on Android. Our high-level API provides cryptographic operations without requiring knowledge of public-key cryptography. By developing OpenKeychain, we were able to roll out this architecture for more than 100,000 users. It provides encryption for emails, messaging, and a password manager. We provide a threat model, NFC performance measurements, and discuss their impact on our architecture design. As an alternative form factor to smart cards, we created the prototype of an NFC signet ring. To evaluate the UI components and form factors, a lab study with 40 participants at a large company has been conducted. We measured the time required by the participants to set up the system and reply to encrypted emails. These measurements and a subsequent interview indicate that our NFC-based solutions are more user friendly in comparison to traditional password-protected keys.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130964"}, {"primary_key": "3641562", "vector": [], "sparse_vector": [], "title": "Passive Haptic Training to Improve Speed and Performance on a Keypad.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Learning text entry systems is challenging, yet necessary. Many layouts and keyboards exist, but they rely on laborious learning techniques. Passive haptic learning (PHL) has already demonstrated some benefit for learning the Braille text entry system. Could this computing-enabled technique be used to improve desktop keyboard typing skills? It is unknown whether passive haptic training can improve speed on a motor task (as opposed to initial learning). We use a randomized numeric keypad to examine users’ typing performance with or without passive haptic training. When users were prevented from looking at the keyboard, the PHL group demonstrated consistent accuracy (-0.011 KSPC) while those in the control group greatly increased their error (+1.26 KSPC on average). This result is consistent with the finding that PHL users looked significantly less at the keyboard. In a second, longer study, users exposed to PHL were found to significantly improve their typing speed (mean increase of 11 WPM) versus control (mean increase of 2.2 WPM).", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3132026"}, {"primary_key": "3641563", "vector": [], "sparse_vector": [], "title": "Automated Dyadic Data Recorder (ADDR) Framework and Analysis of Facial Cues in Deceptive Communication.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON> (<PERSON><PERSON><PERSON>) <PERSON><PERSON>"], "summary": "We developed an online framework that can automatically pair two crowd-sourced participants, prompt them to follow a research protocol, and record their audio and video on a remote server. The framework comprises two web applications: an Automatic Quality Gatekeeper for ensuring only high quality crowd-sourced participants are recruited for the study, and a Session Controller which directs participants to play a research protocol, such as an interrogation game. This framework was used to run a research study for analyzing facial expressions during honest and deceptive communication using a novel interrogation protocol. The protocol gathers two sets of nonverbal facial cues in participants: features expressed during questions relating to the interrogation topic and features expressed during control questions. The framework and protocol were used to gather 151 dyadic conversations (1.3 million video frames). Interrogators who were lied to expressed the smile-related lip corner puller cue more often than interrogators who were being told the truth, suggesting that facial cues from interrogators may be useful in evaluating the honesty of witnesses in some contexts. Overall, these results demonstrate that this framework is capable of gathering high quality data which can identify statistically significant results in a communication study.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161178"}, {"primary_key": "3641565", "vector": [], "sparse_vector": [], "title": "iLid: Low-power Sensing of Fatigue and Drowsiness Measures on a Computational Eyeglass.", "authors": ["Soha Rostaminia", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The ability to monitor eye closures and blink patterns has long been known to enable accurate assessment of fatigue and drowsiness in individuals. Many measures of the eye are known to be correlated with fatigue including coarse-grained measures like the rate of blinks as well as fine-grained measures like the duration of blinks and the extent of eye closures. Despite a plethora of research validating these measures, we lack wearable devices that can continually and reliably monitor them in the natural environment. In this work, we present a low-power system, iLid, that can continually sense fine-grained measures such as blink duration and Percentage of Eye Closures (PERCLOS) at high frame rates of 100fps. We present a complete solution including design of the sensing, signal processing, and machine learning pipeline; implementation on a prototype computational eyeglass platform; and extensive evaluation under many conditions including illumination changes, eyeglass shifts, and mobility. Our results are very encouraging, showing that we can detect blinks, blink duration, eyelid location, and fatigue-related metrics such as PERCLOS with less than a few percent error.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3090088"}, {"primary_key": "3641567", "vector": [], "sparse_vector": [], "title": "TouchCam: Realtime Recognition of Location-Specific On-Body Gestures to Support Users with Visual Impairments.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "On-body interaction, which employs the user's own body as an interactive surface, offers several advantages over existing touchscreen devices: always-available control, an expanded input space, and additional proprioceptive and tactile cues that support non-visual use. While past work has explored a variety of approaches such as wearable depth cameras, bio-acoustics, and infrared reflectance (IR) sensors, these systems do not instrument the gesturing finger, do not easily support multiple body locations, and have not been evaluated with visually impaired users (our target). In this paper, we introduce TouchCam, a finger wearable to support location-specific, on-body interaction. TouchCam combines data from infrared sensors, inertial measurement units, and a small camera to classify body locations and gestures using supervised learning. We empirically evaluate TouchCam's performance through a series of offline experiments followed by a realtime interactive user study with 12 blind and visually impaired participants. In our offline experiments, we achieve high accuracy (&gt;96%) at recognizing coarse-grained touch locations (e.g., palm, fingers) and location-specific gestures (e.g., tap on wrist, left swipe on thigh). The follow-up user study validated our real-time system and helped reveal tradeoffs between various on-body interface designs (e.g., accuracy, convenience, social acceptability). Our findings also highlight challenges to robust input sensing for visually impaired users and suggest directions for the design of future on-body interaction systems.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161416"}, {"primary_key": "3641568", "vector": [], "sparse_vector": [], "title": "Robust Indoor Localization across Smartphone Models with Ellipsoid Features from Multiple RSSIs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Localization for mobile devices has become important as the basis technology for various ubiquitous computing applications. While GPS is leveraged as the de-facto standard technology in outdoor localization, its accuracy is poor indoors. For twenty years, researchers have tried to investigate indoor localization technology using fingerprinting from received signal strength indicators (RSSIs). With the widespread use of smartphones in the last decade, device dependency (e.g. antenna characteristics) must be considered to avoid performance degradation, while most of the recent localization approaches assume that all the smartphone models have the same device characteristics. In this paper, we propose a novel feature representation based on multiple RSSIs for compensating performance degradation against smartphone models changes. In contrast to the previous feature representation based on a single RSSI, our new feature representation, which we call Ellipsoid features, employs tuples of pair of RSSIs to eliminate device dependence in the path loss model for wave propagation. In contrast to recent advances in machine learning methods such as domain adaptation, multi-task learning, and semi-supervised learning, our approach requires no additional dataset nor retraining for the new target models. This simplicity would promote ubiquity of indoor localization in the era of smartphones. Moreover, our feature representation works well compared to the state-of-the-arts in feature representations based on multiple RSSIs even when only a small number of access points (APs) are available. Experimental result using smartphone devices including Android Nexus5, Nexus5X, Nexus6P, and Xperia X Performance shows that our approach achieves superior performance over the state-of-the-art indoor localization models as well as robust performance against device changes.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130968"}, {"primary_key": "3641569", "vector": [], "sparse_vector": [], "title": "SleepMonitor: Monitoring Respiratory Rate and Body Position During Sleep Using Smartwatch.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Tang", "<PERSON><PERSON>"], "summary": "Respiratory rate and body position are two major physiological parameters in sleep study, and monitoring them during sleep can provide helpful information for health care. In this paper, we present SleepMonitor, a smartwatch based system which leverages the built-in accelerometer to monitor the respiratory rate and body position. To calculate respiratory rate, we design a filter to extract the weak respiratory signal from the noisy accelerometer data collected on the wrist, and use frequency analysis to estimate the respiratory rate from the data along each axis. Further, we design a multi-axis fusion approach which can adaptively adjust the estimates from the three axes and then significantly improve the estimation accuracy. To detect the body position, we apply machine learning techniques based on the features extracted from the accelerometer data. We have implemented our system on Android Wear based smartwatches and evaluated its performance in real experiments. The results show that our system can monitor respiratory rate and body position during sleep with high accuracy under various conditions.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130969"}, {"primary_key": "3641571", "vector": [], "sparse_vector": [], "title": "Battery-Free Cellphone.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present the first battery-free cellphone design that consumes only a few micro-watts of power. Our design can sense speech, actuate the earphones, and switch between uplink and downlink communications, all in real time. Our system optimizes transmission and reception of speech while simultaneously harvesting power which enables the battery-free cellphone to operate continuously. The battery-free device prototype is built using commercial-off-the-shelf components on a printed circuit board. It can operate on power that is harvested from RF signals transmitted by a basestation 31 feet (9.4 m) away. Further, using power harvested from ambient light with tiny photodiodes, we show that our device can communicate with a basestation that is 50 feet (15.2 m) away. Finally, we perform the first Skype call using a battery-free phone over a cellular network, via our custom bridged basestation. This we believe is a major leap in the capability of battery-free devices and a step towards a fully functional battery-free cellphone.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3090090"}, {"primary_key": "3641572", "vector": [], "sparse_vector": [], "title": "Harnessing Long Term Physical Activity Data - How Long-term Trackers Use Data and How an Adherence-based Interface Supports New Insights.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Increasingly, people are amassing long term physical activity data which could play an important role for reflection. However, it is not clear if and how existing trackers use their long term data and incomplete data is a potential challenge. We introduced the notion of adherence to design iStuckWithIt, a custom calendar display that integrates and embeds daily adherence (days with data and days without), hourly adherence (hours of wear each day) and goal adherence (days people achieved their activity goals). Our study of 21 long term FitBit users (average: 23 months, 17 over 1 year) began with an interview about their use and knowledge of long term physical activity data followed by a think-aloud use of iStuckWithIt and a post-interview. Our participants gained new insights about their wearing patterns and they could then use this to overcome problems of missing data, to gain insights about their physical activity and goal achievement. This work makes two main contributions: new understanding of the ways that long term trackers have used and understand their data; the design and evaluation of iStuckWithIt demonstrating that people can gain new insights through designs that embed daily, hourly adherence data with goal adherence.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3090091"}, {"primary_key": "3641573", "vector": [], "sparse_vector": [], "title": "Character Actor: Design and Evaluation of Expressive Robot Car Seat Motion.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "How might an actuated car seat become an expressive robot? To explore the possibilities of this novel interaction, we conducted a full design exploration from prototyping to validation, drawing on methods for embodied physical interaction design. First, we applied physical and digital puppeteering techniques to explore how a car seat can display emotional affect through movement with limited degrees of freedom in a semi-structured design workshop. Second, prototyped emotions were formalized with the <PERSON>an Effort framework and translated into computer animations. Third, we tested if lay users understood the expressions communicated by the animations in an online validation study on Amazon Mechanical Turk. Participants generally agreed with our interpretation of six prototyped expressive states for the robot car seat (Neutral, Aggressive, Confident, Cool, Excited, and Quirky), and reported quantitative and qualitative reactions to each including perceived safety, which varied across conditions. Participants reported more implied cognition for higher valence expressions, and also were more likely to agree with our design intent. This specific case of physical interaction design and evaluation serves as a vignette for how to design and validate novel physical expressions in non-anthropomorphic robot interfaces.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161407"}, {"primary_key": "3641575", "vector": [], "sparse_vector": [], "title": "RoboCOP: A Robotic Coach for Oral Presentations.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Rehearsing in front of a live audience is invaluable when preparing for important presentations. However, not all presenters take the opportunity to engage in such rehearsal, due to time constraints, availability of listeners who can provide constructive feedback, or public speaking anxiety. We present RoboCOP, an automated anthropomorphic robot head that acts as a coach to provide spoken feedback during presentation rehearsals at both the individual slide and overall presentation level. The robot offers conversational coaching on three key aspects of presentations: speech quality, content coverage, and audience orientation. The design of the feedback strategies was informed by findings from an exploratory study with academic professionals who were experienced in mentoring students on their presentations. In a within-subjects study comparing RoboCOP to visual feedback and spoken feedback without a robot, the robotic coach was shown to lead to significant improvement in the overall experience of presenters. Results of a second within-subjects evaluation study comparing RoboCOP with existing rehearsal practices show that our system creates a natural, interactive, and motivating rehearsal environment that leads to improved presentation quality.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3090092"}, {"primary_key": "3641576", "vector": [], "sparse_vector": [], "title": "Watching inside the Screen: Digital Activity Monitoring for Task Recognition and Proactive Information Retrieval.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We investigate to what extent it is possible to infer a user’s work tasks by digital activity monitoring and use the task models for proactive information retrieval. Ten participants volunteered for the study, in which their computer screen was monitored and related logs were recorded for 14 days. Corresponding diary entries were collected to provide ground truth to the task detection method. We report two experiments using this data. The unsupervised task detection experiment was conducted to detect tasks using unsupervised topic modeling. The results show an average task detection accuracy of more than 70% by using rich screen monitoring data. The single-trial task detection and retrieval experiment utilized unseen user inputs in order to detect related work tasks and retrieve task-relevant information on-line. We report an average task detection accuracy of 95%, and the corresponding model-based document retrieval with Normalized Discounted Cumulative Gain of 98%. We discuss and provide insights regarding the types of digital tasks occurring in the data, the accuracy of task detection on different task types, and the role of using different data input such as application names, extracted keywords, and bag-of-words representations in the task detection process. We also discuss the implications of our results for ubiquitous user modeling and privacy.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130974"}, {"primary_key": "3641577", "vector": [], "sparse_vector": [], "title": "Luciola: A Millimeter-Scale Light-Emitting Particle Moving in Mid-Air Based On Acoustic Levitation and Wireless Powering.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we present an approach to realize the levitation of a small object with an embedded electronic circuit. Luciola is a light-emitting particle with a diameter of 3.5mm and a weight of 16.2mg moving in mid-air in a 10.4cm x 10.4cm x 5.4cm space through acoustic levitation using two 40-kHz 17 x 17 ultrasonic transducer arrays placed face-to-face at a distance of 20cm and wirelessly powered by 12.3-MHz resonant inductive coupling. The novelty of this paper is the acoustically levitated electronic object by the combined application of ultrasonic levitation and wireless powering to the levitated electronic object. A new shape of the levitated object and a new placement of the receiver coil to simultaneously realize acoustic levitation and wireless powering are proposed, achieving a stable wireless powering to a rotating levitated object at the bottom of an acoustic potential. To enable the levitation of a particle, a custom IC chip is essential in reducing the size and weight of the particle. In the design of the custom IC chip, a new voltage detector circuit enabling an accurate voltage detection and a correct output during the start-up is proposed to achieve an intermittent lighting of the LED to increase the maximum distance between the transmitter and the receiver coil. Luciola is applied to a self-luminous pixel in a mid-air display and drawings of characters in mid-air are demonstrated.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161182"}, {"primary_key": "3641579", "vector": [], "sparse_vector": [], "title": "Context Recognition In-the-Wild: Unified Model for Multi-Modal Sensors and Multi-Label Classification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "Automatic recognition of behavioral context (location, activities, body-posture etc.) can serve health monitoring, aging care, and many other domains. Recognizing context in-the-wild is challenging because of great variability in behavioral patterns, and it requires a complex mapping from sensor features to predicted labels. Data collected in-the-wild may be unbalanced and incomplete, with cases of missing labels or missing sensors. We propose using the multiple layer perceptron (MLP) as a multi-task model for context recognition. Based on features from multi-modal sensors, the model simultaneously predicts many diverse context labels. We analyze the advantages of the model's hidden layers, which are shared among all sensors and all labels, and provide insight to the behavioral patterns that these hidden layers may capture. We demonstrate how recognition of new labels can be improved when utilizing a model that was trained for an initial set of labels, and show how to train the model to withstand missing sensors. We evaluate context recognition on the previously published ExtraSensory Dataset, which was collected in-the-wild. Compared to previously suggested models, the MLP improves recognition, even with fewer parameters than a linear model. The ability to train a good model using data that has incomplete, unbalanced labeling and missing sensors encourages further research with uncontrolled, in-the-wild behavior.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161192"}, {"primary_key": "3641581", "vector": [], "sparse_vector": [], "title": "FiDO: A Community-based Web Browsing Agent and CDN for Challenged Network Environments.", "authors": ["Morgan Vigil-<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Homes located on tribal lands, particularly in rural areas of the United States, continue to lack access to broadband Internet and cellular connectivity [19]. Inspired by previous observations of community content similarity in tribal networks, we propose FiDO, a community-based Web browsing and content delivery system that takes advantage of user mobility, opportunistic connectivity, and collaborative filtering to provide relevant Web content to members of disconnected households via opportunistic contact with cellular base stations during a daily commute. We evaluate FiDO using trace-driven simulations with network usage data collected from a tribal-operated ISP that serves the Coeur d’Alene Indian Reservation in Western Idaho. By collecting data about household Web preferences and applying a collaborative filtering technique based on the Web usage patterns of the surrounding reservation community, we are able to opportunistically browse the Web on behalf of members of disconnected households, providing an average of 69.4 Web pages (all content from a specific URL, e.g., “http://gis.cdatribe-nsn.gov/LandBuyBack/”) crawled from 73% of their top 10 most visited Web domains (e.g., “cdatribe-nsn.gov” or “cnn.com/”) per day. Moreover, this content is able to be fetched and pushed to users even when the opportunistic data rate is limited to an average of only 0.99 Mbps (σ = 0.24 Mbps) and the daily opportunistic connection time is an average of 45.9 minutes (σ = 2.3 minutes). Additionally, we demonstrate a hybrid “search and browse” approach that allocates a percentage of opportunistic resources to the download of user-specified content. By dedicating only 10% of opportunistic windows of connectivity to the download of social media content, 51% of households were able to receive all of their daily expected social media content in addition to an average of 55.3 Web pages browsed on their behalf from an average of 4 different Web domains. Critically, we demonstrate the feasibility of a collaborative and community-based Web browsing model that extends access to Web content across the last mile(s) using existing infrastructure and rural patterns of mobility.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3132030"}, {"primary_key": "3641584", "vector": [], "sparse_vector": [], "title": "C-FMCW Based Contactless Respiration Detection Using Acoustic Signal.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhang", "Yuan<PERSON> Zheng", "<PERSON> Gu", "<PERSON><PERSON><PERSON>", "Bernadette <PERSON>"], "summary": "Recent advances in ubiquitous sensing technologies have exploited various approaches to monitoring vital signs. One of the vital signs is human respiration which typically requires reliable monitoring with low error rate in practice. Previous works in respiration monitoring however either incur high cost or suffer from poor error rate. In this paper, we propose a Correlation based Frequency Modulated Continuous Wave method (C-FMCW) which is able to achieve high ranging resolution. Based on C-FMCW, we present the design and implementation of an audio-based highly-accurate system for human respiration monitoring, leveraging on commodity speaker and microphone widely available in home environments. The basic idea behind the audio-based method is that when a user is close to a pair of speaker and microphone, body movement during respiration causes periodic audio signal changes, which can be extracted to obtain the respiration rate. However, several technical challenges exist when applying C-FMCW to detect respiration with commodity acoustic devices. First, the sampling frequency offset between speakers and microphones if not being corrected properly would cause high ranging errors. Second, the uncertain starting time difference between the speaker and microphone varies over time. Moreover, due to multipath effect, weak periodic components due to respiration can easily be overwhelmed by strong static components in practice. To address those challenges, we 1) propose an algorithm to compensate dynamically acoustic signal and counteract the offset between speaker and microphone; 2) co-locate speaker and microphone and use the received signal without reflection (self-interference) as a reference to eliminate the starting time difference; and 3) leverage the periodicity of respiration to extract weak periodic components with autocorrelation. Extensive experimental results show that our system detects respiration in real environments with the median error lower than 0.35 breaths/min, outperforming the state-of-the-arts.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161188"}, {"primary_key": "3641588", "vector": [], "sparse_vector": [], "title": "DigiTouch: Reconfigurable Thumb-to-Finger Input and Text Entry on Head-mounted Displays.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Shwetak N. Patel", "<PERSON><PERSON>"], "summary": "Input is a significant problem for wearable systems, particularly for head mounted virtual and augmented reality displays. Existing input techniques either lack expressive power or may not be socially acceptable. As an alternative, thumb-to-finger touches present a promising input mechanism that is subtle yet capable of complex interactions. We present DigiTouch, a reconfigurable glove-based input device that enables thumb-to-finger touch interaction by sensing continuous touch position and pressure. Our novel sensing technique improves the reliability of continuous touch tracking and estimating pressure on resistive fabric interfaces. We demonstrate DigiTouch’s utility by enabling a set of easily reachable and reconfigurable widgets such as buttons and sliders. Since <PERSON>gi<PERSON>ou<PERSON> senses continuous touch position, widget layouts can be customized according to user preferences and application needs. As an example of a real-world application of this reconfigurable input device, we examine a split-QWERTY keyboard layout mapped to the user’s fingers. We evaluate DigiTouch for text entry using a multi-session study. With our continuous sensing method, users reliably learned to type and achieved a mean typing speed of 16.0 words per minute at the end of ten 20-minute sessions, an improvement over similar wearable touch systems.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130978"}, {"primary_key": "3641590", "vector": [], "sparse_vector": [], "title": "Cost-Sensitive Semi-Supervised Personalized Semantic Place Label Recognition Using Multi-Context Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Mingqi Lv", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Personalized semantic place label recognition is a promising research issue in ubiquitous computing. However, there are some problems existed in the current methods: 1) They use trajectory data to learn recognition model and ignore other context data that could reflect human behaviors over places. 2) They tend to maximize accuracy and ignore the imbalanced costs caused by the similarity between semantic place labels. 3) They use supervised learning, which cannot achieve a good performance when the number of labeled places is limited. In this paper, we exploit multi-context data to construct effective features and propose a method called CEMENT (Cost-sensitive sEmi-supervised personalized seMantic place labEl recogNiTion). On one hand, CEMENT is cost-sensitive, and it calculates misclassification costs according to the semantic similarity between place labels. On the other hand, CEMENT utilizes ensemble semi-supervised learning to leverage the unlabeled data to improve the performance. Experimental results show that the proposed method has superiority over state-of-the-art methods.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3131903"}, {"primary_key": "3641591", "vector": [], "sparse_vector": [], "title": "CLSTERS: A General System for Reducing Errors of Trajectories Under Challenging Localization Situations.", "authors": ["<PERSON><PERSON>", "Weiwei Sun", "<PERSON><PERSON> Zheng", "<PERSON>", "<PERSON>"], "summary": "Trajectory data generated by outdoor activities have great potential for location based services. However, depending on the localization technique used, certain trajectory data could contain large errors. For example, the error of trajectories generated by cellular-based localization techniques is around 100m which is ten times larger than that of GPS-based trajectories. Hence, enhancing the utility of those large-error trajectories becomes a challenge. In this paper we show how to improve the quality of trajectory data having large errors. Some existing works reduce the error through hardware which requires information such as the time of arrival (TOA), received signal strength indication (RSSI), the position of cell towers, etc. Moreover, different positioning techniques will result in different hardware-based solutions and different data formats, which limit the generalizablity. Other works study a related but different problem, i.e., map matching, with the aid of road network information, to reduce the uncertainty and the noise of trajectory data. However, most of these approaches are designed for the GPS-sampled data, and hence they might not be able to achieve a similar performance when applied directly to trajectories with large errors. Motivated by this, we propose a general error reduction system namely CLSTERS for trajectories with large scale of errors. Our system is hardware independent and only requires the coordinates and the time stamp of each sample point which makes it general and ubiquitous. We present results from experiments using three real-world datasets in three different cities generated by two different localization techniques and the results show that our approach outperforms existing solutions.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130981"}, {"primary_key": "3641592", "vector": [], "sparse_vector": [], "title": "Gain Without Pain: Accurate WiFi-based Localization using Fingerprint Spatial Gradient.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Among numerous indoor localization systems proposed during the past decades, WiFi fingerprint-based localization has been one of the most attractive solutions, which is known to be free of extra infrastructure and specialized hardware. However, current WiFi fingerprinting suffers from a pivotal problem of RSS fluctuations caused by unpredictable environmental dynamics. The RSS variations lead to severe spatial ambiguity and temporal instability in RSS fingerprinting, both impairing the location accuracy. To overcome such drawbacks, we propose fingerprint spatial gradient (FSG), a more stable and distinctive form than RSS fingerprints, which exploits the spatial relationships among the RSS fingerprints of multiple neighbouring locations. As a spatially relative form, FSG is more resistant to RSS uncertainties. Based on the concept of FSG, we design novel algorithms to construct FSG on top of a general RSS fingerprint database and then propose effective FSG matching methods for location estimation. Unlike previous works, the resulting system, named ViVi, yields performance gain without the pains of introducing extra information or additional service restrictions or assuming impractical RSS models. Extensive experiments in different buildings demonstrate that ViVi achieves great performance, outperforming the best among four comparative start-of-the-art approaches by 29% in mean accuracy and 19% in 95th percentile accuracy and outweighing the worst one by 39% and 24% respectively. We envision FSG as a promising supplement and alternative to existing RSS fingerprinting for future WiFi localization.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3090094"}, {"primary_key": "3641593", "vector": [], "sparse_vector": [], "title": "WiStep: Device-free Step Counting with WiFi Signals.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Hong Li", "<PERSON><PERSON><PERSON>"], "summary": "Inspired by the emerging WiFi-based applications, in this paper, we leverage ubiquitous WiFi signals and propose a device-free step counting system, called WiStep. Based on the multipath propagation model, when a person is walking, her torso and limbs move at different speeds, which modulates wireless signals to the propagation paths with different lengths and thus introduces different frequency components into the received Channel State Information (CSI). To count walking steps, we first utilize time-frequency analysis techniques to segment and recognize the walking movement, and then dynamically select the sensitive subcarriers with largest amplitude variances from multiple CSI streams. Wavelet decomposition is applied to extract the detail coefficients corresponding to the frequencies induced by feet or legs, and compress the data so as to improve computing speed. Short-time energy of the coefficients is then calculated as the metric for step counting. Finally, we combine the results derived from the selected subcarriers to produce a reliable step count estimation. In contrast to counting steps based on the torso frequency analysis, WiStep can count the steps of in-place walking even when the person's torso speed is null. We implement WiStep on commodity WiFi devices in two different indoor scenarios, and various influence factors are taken into consideration when evaluating the performance of WiStep. The experimental results demonstrate that WiStep can realize overall step counting accuracies of 90.2% and 87.59% respectively in these two scenarios, and it is resilient to the change of scenarios.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161415"}, {"primary_key": "3641594", "vector": [], "sparse_vector": [], "title": "CityFlowFragility: Measuring the Fragility of People Flow in Cities to Disasters using GPS Data Collected from Smartphones.", "authors": ["<PERSON><PERSON><PERSON>", "Kota Tsubouchi", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Economic loss caused by natural disasters is increasing in many cities around the world. There is an increasing demand for a method that effectively measures the fragility of people flow to appropriately plan the future investment into infrastructure. Conventional methods measure the fragility of urban systems using infrastructure data such as the road and railway networks. However, these methods are costly to perform, cannot directly measure the disruption on human activities caused by disasters, nor can they be applied for individual disasters. Here, we propose a novel method that quantifies the fragility of cities through detecting the delay in commuting activities using GPS data collected from smartphones. Because commuting activities are daily routines for many people, commuting flow has little day-to-day fluctuation, which makes it an appropriate metric for detecting anomalies and disruption in urban systems. This method can be utilized in any city in the world regardless of differences in network structures or population distribution, as long as people commute on a daily basis. We validate our method in various cities for snowfall and typhoons using real datasets in Japan, and show that intuitive results can be obtained. Our method's reliability is clarified by comparing the results with conventional metrics. We also present useful analyses and applications of CityFlowFragility for urban planning and disaster management.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130982"}, {"primary_key": "3641595", "vector": [], "sparse_vector": [], "title": "EM-Comm: Touch-based Communication via Modulated Electromagnetic Emissions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Touch-based communication offers a direct and intuitive way for users to initiate and control data transfer when using tangible and ubiquitous interfaces. However, this requires that each interactive device be instrumented with a dedicated radio transmitter, which limits many applications. While not all devices have radio hardware, all devices do emit small amounts of electromagnetic noise in the form of EMI. We argue that if properly modulated these electromagnetic emissions can be used as an untapped communication channel capable of transmitting arbitrary data. To enable this, a spread spectrum frequency shift keying modulation scheme has been developed to encode data onto the device’s EMI. When the device is touched by a user, the data encoded EMI signal travels through their body and into our custom wrist band, consisting of a single op-amp and MCU. Our results show that we are able to turn electronic primitives such as LEDs, buttons, I/O lines, LCD screens, motors and power supplies into radio transmitters capable of touch communication. Effective data rates range from 5.8Kbps to 22 bit per image depending on the primitive used. To demonstrate this new communication technique, we develop several interactive experiences where users can retrieve complex information such as the function of buttons on a device, directions embedded into a LCD screen, and simplified device pairing. Ultimately, EM-Comm enables nearly any electronic device to be turned into a touch-based radio transmitter with only a software upgrade.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130984"}, {"primary_key": "3641596", "vector": [], "sparse_vector": [], "title": "Predicting Commercial Activeness over Urban Big Data.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Yi Sun", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This study aims at revealing how commercial hotness of urban commercial districts (UCDs) is shaped by social contexts of surrounding areas so as to render predictive business planning. We define social contexts for a given region as the number of visitors, the region functions, the population and buying power of local residents, the average price of services, and the rating scores of customers, which are computed from heterogeneous data including taxi GPS trajectories, point of interests, geographical data, and user-generated comments. Then, we apply sparse representation to discover the impactor factor of each variable of the social contexts in terms of predicting commercial activeness of UCDs under a linear predictive model. The experiments show that a linear correlation between social contexts and commercial activeness exists for Beijing and Shanghai based on an average prediction accuracy of 77.69% but the impact factors of social contexts vary from city to city, where the key factors are rich life services, diversity of restaurants, good shopping experience, large number of local residents with relatively high purchasing power, and convenient transportation. This study reveals the underlying mechanism of urban business ecosystems, and promise social context-aware business planning over heterogeneous urban big data.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130983"}, {"primary_key": "3641598", "vector": [], "sparse_vector": [], "title": "Smartphone App Usage Prediction Using Points of Interest.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper we present the first population-level, city-scale analysis of application usage on smartphones. Using deep packet inspection at the network operator level, we obtained a geo-tagged dataset with more than 6 million unique devices that launched more than 10,000 unique applications across the city of Shanghai over one week. We develop a technique that leverages transfer learning to predict which applications are most popular and estimate the whole usage distribution based on the Point of Interest (POI) information of that particular location. We demonstrate that our technique has an 83.0% hitrate in successfully identifying the top five popular applications, and a 0.15 RMSE when estimating usage with just 10% sampled sparse data. It outperforms by about 25.7% over the existing state-of-the-art approaches. Our findings pave the way for predicting which apps are relevant to a user given their current location, and which applications are popular where. The implications of our findings are broad: it enables a range of systems to benefit from such timely predictions, including operating systems, network operators, appstores, advertisers, and service providers.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161413"}, {"primary_key": "3641600", "vector": [], "sparse_vector": [], "title": "Continuous Authentication Using Eye Movement Response of Implicit Visual Stimuli.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Smart head-worn or head-mounted devices, including smart glasses and Virtual Reality (VR) headsets, are gaining popularity. Online shopping and in-app purchase from such headsets are presenting new e-commerce opportunities to the app developers. For convenience, users of these headsets may store account login, bank account and credit card details in order to perform quick in-app purchases. If the device is unattended, then an attacker, which can include insiders, can make use of the stored account and banking details to perform their own in-app purchases at the expense of the legitimate owner. To better protect the legitimate users of VR headsets (or head mounted displays in general) from such threats, in this paper, we propose to use eye movement to continuously authenticate the current wearer of the VR headset. We built a prototype device which allows us to apply visual stimuli to the wearer and to video the eye movements of the wearer at the same time. We use implicit visual stimuli (the contents of existing apps) which evoke eye movements from the headset wearer but without distracting them from their normal activities. This is so that we can continuously authenticate the wearer without them being aware of the authentication running in the background. We evaluated our proposed system experimentally with 30 subjects. Our results showed that the achievable authentication accuracy for implicit visual stimuli is comparable to that of using explicit visual stimuli. We also tested the time stability of our proposed method by collecting eye movement data on two different days that are two weeks apart. Our authentication method achieved an Equal Error Rate of 6.9% (resp. 9.7%) if data collected from the same day (resp. two weeks apart) were used for testing. In addition, we considered active impersonation attacks where attackers trying to imitate legitimate users' eye movements. We found that for a simple (resp. complex) eye tracking scene, a successful attack could be realised after on average 5.67 (13.50) attempts and our proposed authentication algorithm gave a false acceptance rate of 14.17% (3.61%). These results show that active impersonating attacks can be prevented using complex scenes and an appropriate limit on the number of authentication attempts. Lastly, we carried out a survey to study the user acceptability to our proposed implicit stimuli. We found that on a 5-point Likert scale, at least 60% of the respondents either agreed or strongly agreed that our proposed implicit stimuli were non-intrusive.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161410"}, {"primary_key": "3641601", "vector": [], "sparse_vector": [], "title": "MoodExplorer: Towards Compound Emotion Detection via Smartphone Sensing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Social psychology and neuroscience had confirmed that emotion state exerts a significant effect on human communication, perception, social behavior and decision making. With the wide availability of smartphones equipped with microphone, accelerometer, GPS, and other source of sensors, it is worthwhile to explore the possibility of automatic emotion detection via smartphone sensing. Particularly, we focus on a novel research problem that tries to detect the compound emotion (a set of multiple dimensional basic emotions) of smartphone users. We observe that users' self-reported emotional states have high correlation with their smartphone usage patterns and sensing data. Based on the observations, we exploit a feature extraction and selection algorithm to find the most significant features. We further adopt a factor graph model to tackle the correlations between features and emotion labels, and propose a machine learning algorithm for compound emotion detection based on the smartphone sensing data. The proposed mechanism is implemented as an APP called MoodExplorer in Android platform. Extensive experiments conducted on the smartphone data collected from 30 university students show that MoodExplorer can recognize users' compound emotions with 76.0% exact match on average.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3161414"}, {"primary_key": "3641602", "vector": [], "sparse_vector": [], "title": "FingerSound: Recognizing unistroke thumb gestures using a ring.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce FingerSound, an input technology to recognize unistroke thumb gestures, which are easy to learn and can be performed through eyes-free interaction. The gestures are performed using a thumb-mounted ring comprising a contact microphone and a gyroscope sensor. A K-Nearest-Neighbor(KNN) model with a distance function of Dynamic Time Warping (DTW) is built to recognize up to 42 common unistroke gestures. A user study, where the real-time classification results were given, shows an accuracy of 92%-98% by a machine learning model built with only 3 training samples per gesture. Based on the user study results, we further discuss the opportunities, challenges and practical limitations of FingerSound when deploying it to real-world applications in the future.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3130985"}, {"primary_key": "3641603", "vector": [], "sparse_vector": [], "title": "SoundTrak: Continuous 3D Tracking of a Finger Using Active Acoustics.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Kent Lyons", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The small size of wearable devices limits the efficiency and scope of possible user interactions, as inputs are typically constrained to two dimensions: the touchscreen surface. We present SoundTrak, an active acoustic sensing technique that enables a user to interact with wearable devices in the surrounding 3D space by continuously tracking the finger position with high resolution. The user wears a ring with an embedded miniature speaker sending an acoustic signal at a specific frequency (e.g., 11 kHz), which is captured by an array of miniature, inexpensive microphones on the target wearable device. A novel algorithm is designed to localize the finger’s position in 3D space by extracting phase information from the received acoustic signals. We evaluated SoundTrak in a volume of space (20cm × 16cm × 11cm) around a smartwatch, and show an average accuracy of 1.3 cm. We report on results from a Fitts’ Law experiment with 10 participants as the evaluation of the real-time prototype. We also present a set of applications which are supported by this 3D input technique, and show the practical challenges that need to be addressed before widespread use.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3090095"}, {"primary_key": "3641605", "vector": [], "sparse_vector": [], "title": "Mediated Atmospheres: A Multimodal Mediated Work Environment.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Atmosphere - the sensorial qualities of a space, shaped by the composition of light, sound, objects, people, etc. - has remarkable influence on our experiences and behavior. Manipulating it has been shown to be powerful, affecting cognitive performance, mood and even physiology, our work envisions and implements a smart office prototype, capable of digitally transforming its atmosphere - creating what we call Mediated Atmospheres (MA) - using computationally controlled lighting, video projection and sound. Additionally, we equipped this space with a modular real-time data collection infrastructure, integrating a set of biosignal sensors. Through a user study (N=29) we demonstrate MA's effects on occupants’ ability to focus and to recover from a stressful situation. Our evaluation is based on subjective measurements of perception, as well as objective measurements, extracted from recordings of heart rate variability and facial features. We compare multiple signal processing approaches for quantifying changes in occupant physiological state. Our findings show that MA significantly (p&lt;0.05) affect occupants’ perception as well as physiological response, which encouragingly correlate with occupants’ perception. Our findings is a first step towards personalized control of the ambient atmosphere to support wellbeing and productivity.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3090096"}, {"primary_key": "3641606", "vector": [], "sparse_vector": [], "title": "Semi-Automated 8 Collaborative Online Training Module for Improving Communication Skills.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> (<PERSON><PERSON><PERSON>) <PERSON><PERSON>"], "summary": "This paper presents a description and evaluation of the ROC Speak system, a platform that allows ubiquitous access to communication skills training. ROC Speak (available at rocspeak.com) enables anyone to go to a website, record a video, and receive feedback on smile intensity, body movement, volume modulation, filler word usage, unique word usage, word cloud of the spoken words, in addition to overall assessment and subjective comments by peers. Peer comments are automatically ranked and sorted for usefulness and sentiment (i.e., positive vs. negative). We evaluated the system with a diverse group of 56 online participants for a 10-day period. Participants submitted responses to career oriented prompts every other day. The participants were randomly split into two groups: 1) treatment - full feedback from the ROC Speak system; 2) control – written feedback from online peers. When judged by peers (p &lt; .001) and independent raters (p &lt; .05), participants from the treatment group demonstrated statistically significant improvement in overall speaking skills rating while the control group did not. Furthermore, in terms of speaking attributes, treatment group showed an improvement in friendliness (p &lt; .001), vocal variety (p &lt; .05) and articulation (p &lt; .01).", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3090097"}, {"primary_key": "3641607", "vector": [], "sparse_vector": [], "title": "VibeBin: A Vibration-Based Waste Bin Level Detection System.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents the design and implementation of VibeBin, a low-cost, non-intrusive and easy-to-install waste bin level detection system. Recent popularity of Internet-of-Things (IoT) sensors has brought us unprecedented opportunities to enable a variety of new services for monitoring and controlling smart buildings. Indoor waste management is crucial to a healthy environment in smart buildings. Measuring the waste bin fill-level helps building operators schedule garbage collection more responsively and optimize the quantity and location of waste bins. Existing systems focus on directly and intrusively measuring the physical quantities of the garbage (weight, height, volume, etc.) or its appearance (image), and therefore require careful installation, laborious calibration or labeling, and can be costly. Our system indirectly measures fill-level by sensing the changes in motor-induced vibration characteristics on the outside surface of waste bins. VibeBin exploits the physical nature of vibration resonance of the waste bin and the garbage within, and learns the vibration features of different fill-levels through a few garbage collection (emptying) cycles in a completely unsupervised manner. VibeBin identifies vibration features of different fill-levels by clustering historical vibration samples based on a custom distance metric which measures the dissimilarity between two samples. We deploy our system on eight waste bins of different types and sizes, and show that under normal usage and real waste, it can deliver accurate level measurements after just 3 garbage collection cycles. The average F-score (harmonic mean of precision and recall) of measuring empty, half, and full levels achieves 0.912. A two-week deployment also shows that the false positive and false negative events are satisfactorily rare.", "published": "2017-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3132027"}]