[{"primary_key": "1961656", "vector": [], "sparse_vector": [], "title": "11 Years with Wearables: Quantitative Analysis of Social Media, Academia, News Agencies, and Lead User Community from 2009-2020 on Wearable Technologies.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The role of wearable technology in our daily lives is rapidly growing and many users are cumulatively becoming dependent on it. To provide insight into the future of wearable technologies and various community attitudes towards them, we implemented an in-depth quantitative investigation of opinions from academic texts (DBLP and PubMed), social media (Twitter), news media (Google News and Bing News), and entrepreneurship communities (Kickstarter and Indiegogo) over a 10-year period. Our results indicate that unlike academia, the news media, entrepreneurship communities, and social media all hold overall positive attitudes towards wearable technologies. Secondly, there are diverse perspectives towards various wearable products across different platforms. Specifically, \"XR\" technologies received the most attention, while \"Exoskeleton\" ignited the most heated debates. Thirdly, we discovered that the lifetime of a hyped wearable technology lasts approximately three years. Furthermore, the news media and entrepreneurship community's attitudes towards wearable technologies did not have a strong impact on public opinion. Finally, among all types of wearable technologies, \"fashion design\" and \"healthcare\" products were the most enlightening for the market.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448096"}, {"primary_key": "1961696", "vector": [], "sparse_vector": [], "title": "A Survey and Taxonomy of Electronics Toolkits for Interactive and Ubiquitous Device Prototyping.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Over the past two decades, many toolkits for prototyping interactive and ubiquitous electronic devices have been developed. Although their technical specifications are often easy to look up, they vary greatly in terms of design, features and target audience, resulting in very real strengths and weaknesses depending on the intended application. These less technical characteristics are often reported inconsistently, if at all. In this paper we provide a comprehensive survey of interactive and ubiquitous device prototyping toolkits, systematically analysing their characteristics within the framework of a new taxonomy that we present. In addition to the specific characteristics we cover, we introduce a way to evaluate toolkits more holistically, covering user needs such as 'ease of construction' and 'ease of moving from prototype to product' rather than features. We also present results from an online survey which offers new insights on how the surveyed users prioritize these characteristics during prototyping, and what techniques they use to move beyond prototyping. We hope our analysis will be valuable for others in the community who need to build and potentially scale out prototypes as part of their research. We end by identifying gaps that have not yet been addressed by existing offerings and discuss opportunities for future research into electronics prototyping toolkits.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463523"}, {"primary_key": "1961749", "vector": [], "sparse_vector": [], "title": "Multimodal Joint Head Orientation Estimation in Interacting Groups via Proxemics and Interaction Dynamics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Human head orientation estimation has been of interest because head orientation serves as a cue to directed social attention. Most existing approaches rely on visual and high-fidelity sensor inputs and deep learning strategies that do not consider the social context of unstructured and crowded mingling scenarios. We show that alternative inputs, like speaking status, body location, orientation, and acceleration contribute towards head orientation estimation. These are especially useful in crowded and in-the-wild settings where visual features are either uninformative due to occlusions or prohibitive to acquire due to physical space limitations and concerns of ecological validity. We argue that head orientation estimation in such social settings needs to account for the physically evolving interaction space formed by all the individuals in the group. To this end, we propose an LSTM-based head orientation estimation method that combines the hidden representations of the group members. Our framework jointly predicts head orientations of all group members and is applicable to groups of different sizes. We explain the contribution of different modalities to model performance in head orientation estimation. The proposed model outperforms baseline methods that do not explicitly consider the group context, and generalizes to an unseen dataset from a different social event.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448122"}, {"primary_key": "1961605", "vector": [], "sparse_vector": [], "title": "Adaptive Computation Offloading for Mobile Augmented Reality.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Augmented reality (AR) underpins many emerging mobile applications, but it increasingly requires more computation power for better machine understanding and user experience. While computation offloading promises a solution for high-quality and interactive mobile AR, existing methods work best for high-definition videos but cannot meet the real-time requirement for emerging 4K videos due to the long uploading latency. We introduce ACTOR, a novel computation-offloading framework for 4K mobile AR. To reduce the uploading latency, ACTOR dynamically and judiciously downscales the mobile video feed to be sent to the remote server. On the server-side, it leverages image super-resolution technology to scale back the received video so that high-quality object detection, tracking and rendering can be performed on the full 4K resolution. ACTOR employs machine learning to predict which of the downscaling resolutions and super-resolution configurations should be used, by taking into account the video content, server processing delay, and user expected latency. We evaluate ACTOR by applying it to over 2,000 4K video clips across two typical WiFi network settings. Extensive experimental results show that ACTOR consistently and significantly outperforms competitive methods for simultaneously meeting the latency and user-perceived video quality requirements.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494958"}, {"primary_key": "1961607", "vector": [], "sparse_vector": [], "title": "Attend and Discriminate: Beyond the State-of-the-Art for Human Activity Recognition Using Wearable Sensors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Qinfeng Shi", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Wearables are fundamental to improving our understanding of human activities, especially for an increasing number of healthcare applications from rehabilitation to fine-grained gait analysis. Although our collective know-how to solve Human Activity Recognition (HAR) problems with wearables has progressed immensely with end-to-end deep learning paradigms, several fundamental opportunities remain overlooked. We rigorously explore these new opportunities to learn enriched and highly discriminating activity representations. We propose: i) learning to exploit the latent relationships between multi-channel sensor modalities and specific activities; ii) investigating the effectiveness of data-agnostic augmentation for multi-modal sensor data streams to regularize deep HAR models; and iii) incorporating a classification loss criterion to encourage minimal intra-class representation differences whilst maximising inter-class differences to achieve more discriminative features. Our contributions achieves new state-of-the-art performance on four diverse activity recognition problem benchmarks with large margins---with up to 6% relative margin improvement. We extensively validate the contributions from our design concepts through extensive experiments, including activity misalignment measures, ablation studies and insights shared through both quantitative and qualitative studies. The code base and trained network parameters are open-sourced on GitHub https://github.com/AdelaideAuto-IDLab/Attend-And-Discriminate to support further research.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448083"}, {"primary_key": "1961608", "vector": [], "sparse_vector": [], "title": "Ok <PERSON>, What Am I Doing?: Acoustic Activity Recognition Bounded by Conversational Assistant Interactions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>az"], "summary": "Conversational assistants in the form of stand-alone devices such as Amazon Echo and Google Home have become popular and embraced by millions of people. By serving as a natural interface to services ranging from home automation to media players, conversational assistants help people perform many tasks with ease, such as setting timers, playing music and managing to-do lists. While these systems offer useful capabilities, they are largely passive and unaware of the human behavioral context in which they are used. In this work, we explore how off-the-shelf conversational assistants can be enhanced with acoustic-based human activity recognition by leveraging the short interval after a voice command is given to the device. Since always-on audio recording can pose privacy concerns, our method is unique in that it does not require capturing and analyzing any audio other than the speech-based interactions between people and their conversational assistants. In particular, we leverage background environmental sounds present in these short duration voice-based interactions to recognize activities of daily living. We conducted a study with 14 participants in 3 different locations in their own homes. We showed that our method can recognize 19 different activities of daily living with average precision of 84.85% and average recall of 85.67% in a leave-one-participant-out performance evaluation with 30-second audio clips bound by the voice interactions.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448090"}, {"primary_key": "1961609", "vector": [], "sparse_vector": [], "title": "Identifying Mobile Sensing Indicators of Stress-Resilience.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Resident physicians (residents) experiencing prolonged workplace stress are at risk of developing mental health symptoms. Creating novel, unobtrusive measures of resilience would provide an accessible approach to evaluate symptom susceptibility without the perceived stigma of formal mental health assessments. In this work, we created a system to find indicators of resilience using passive wearable sensors and smartphone-delivered ecological momentary assessment (EMA). This system identified indicators of resilience during a medical internship, the high stress first-year of a residency program. We then created density estimation approaches to predict these indicators before mental health changes occurred, and validated whether the predicted indicators were also associated with resilience. Our system identified resilience indicators associated with physical activity (step count), sleeping behavior, reduced heart rate, increased mood, and reduced mood variability. Density estimation models were able to replicate a subset of the associations between sleeping behavior, heart rate, and resilience. To the best of our knowledge, this work provides the first methodology to identify and predict indicators of resilience using passive sensing and EMA. Researchers studying resident mental health can apply this approach to design resilience-building interventions and prevent mental health symptom development.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463528"}, {"primary_key": "1961617", "vector": [], "sparse_vector": [], "title": "Every Byte Matters: Traffic Analysis of Bluetooth Wearable Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Wearable devices such as smartwatches, fitness trackers, and blood-pressure monitors process, store, and communicate sensitive and personal information related to the health, life-style, habits and interests of the wearer. This data is typically synchronized with a companion app running on a smartphone over a Bluetooth (Classic or Low Energy) connection. In this work, we investigate what can be inferred from the metadata (such as the packet timings and sizes) of encrypted Bluetooth communications between a wearable device and its connected smartphone. We show that a passive eavesdropper can use traffic-analysis attacks to accurately recognize (a) communicating devices, even without having access to the MAC address, (b) human actions (e.g., monitoring heart rate, exercising) performed on wearable devices ranging from fitness trackers to smartwatches, (c) the mere opening of specific applications on a Wear OS smartwatch (e.g., the opening of a medical app, which can immediately reveal a condition of the wearer), (d) fine-grained actions (e.g., recording an insulin injection) within a specific application that helps diabetic users to monitor their condition, and (e) the profile and habits of the wearer by continuously monitoring her traffic over an extended period. We run traffic-analysis attacks by collecting a dataset of Bluetooth communications concerning a diverse set of wearable devices, by designing features based on packet sizes and timings, and by using machine learning to classify the encrypted traffic to actions performed by the wearer. Then, we explore standard defense strategies against traffic-analysis attacks such as padding, delaying packets, or injecting dummy traffic. We show that these defenses do not provide sufficient protection against our attacks and introduce significant costs. Overall, our research highlights the need to rethink how applications exchange sensitive information over Bluetooth, to minimize unnecessary data exchanges, and to research and design new defenses against traffic-analysis tailored to the wearable setting.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463512"}, {"primary_key": "1961619", "vector": [], "sparse_vector": [], "title": "GlucoMine: A Case for Improving the Use of Wearable Device Data in Diabetes Management.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Temiloluwa Prioleau"], "summary": "The growing popularity of wearable devices for continuous sensing has made personal health data increasingly available, yet methods for data interpretation are still a work in progress. This paper investigates potential under-utilization of wearable device data in diabetes management and develops an analytic approach - GlucoMine - to uncover individualized patterns in extended periods of such data to support and improve care. In addition, we conduct a user study with clinicians to assess and compare conventional tools used for reviewing wearable device data in diabetes management with the proposed solution. Using 3-6 months of continuous glucose monitor (CGM) data from 54 patients with type 1 diabetes, we found that: 1) the recommended practice of reviewing only short periods (e.g., the most recent 2-weeks) of CGM data based on correlation analysis is not sufficient for finding hidden patterns of poor management; 2) majority of subjects (96% in this study) had clinically-recognized episodes of recurrent adverse glycemic events observable from analysis of extended periods of their CGM data; 3) majority of clinicians (89% in this study) believe there is benefit to be gained in having an algorithm for extracting patterns of adverse glycemic events from longer periods of wearable device data. Findings from our user study also provides insights, including strengths and weakness of various data presentation tools, to guide development of better solutions that improve the use of wearable device data for patient care.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478109"}, {"primary_key": "1961620", "vector": [], "sparse_vector": [], "title": "Modelling Memory for Individual Re-identification in Decentralised Mobile Contact Tracing Applications.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In 2020 the coronavirus outbreak changed the lives of people worldwide. After an initial time period in which it was unclear how to battle the virus, social distancing has been recognised globally as an effective method to mitigate the disease spread. This called for technological tools such as Mobile Contact Tracing Applications (MCTA), which are used to digitally trace contacts among people, and in case a positive case is found, people with the application installed which had been in contact will be notified. De-centralised MCTA may suffer from a novel kind of privacy attack, based on the memory of the human beings, which upon notification of the application can identify who is the positive individual responsible for the notification. Our results show that it is indeed possible to identify positive people among the group of contacts of a human being, and this is even easier when the sociability of the positive individual is low. In practice, our simulation results show that identification can be made with an accuracy of more than 90% depending on the scenario. We also provide three mitigation strategies which can be implemented in de-centralised MCTA and analyse which of the three are more effective in limiting this novel kind of attack.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448088"}, {"primary_key": "1961628", "vector": [], "sparse_vector": [], "title": "NeckFace: Continuously Tracking Full Facial Expressions on Neck-mounted Wearables.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Hyunchul Lim", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Facial expressions are highly informative for computers to understand and interpret a person's mental and physical activities. However, continuously tracking facial expressions, especially when the user is in motion, is challenging. This paper presents NeckFace, a wearable sensing technology that can continuously track the full facial expressions using a neck-piece embedded with infrared (IR) cameras. A customized deep learning pipeline called NeckNet based on Resnet34 is developed to learn the captured infrared (IR) images of the chin and face and output 52 parameters representing the facial expressions. We demonstrated NeckFace on two common neck-mounted form factors: a necklace and a neckband (e.g., neck-mounted headphones), which was evaluated in a user study with 13 participants. The study results showed that NeckFace worked well when the participants were sitting, walking, or after remounting the device. We discuss the challenges and opportunities of using NeckFace in real-world applications.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463511"}, {"primary_key": "1961636", "vector": [], "sparse_vector": [], "title": "Auditable Augmented/Mixed/Virtual Reality: The Practicalities of Mobile System Transparency.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Virtual, Augmented and Mixed Reality (XR) technologies are becoming increasingly pervasive. However, the contextual nature of XR, and its tight coupling of the digital and physical environments, brings real propensity for loss and harm. This means that auditability---the ability to inspect how a system operates---will be crucial for dealing with incidents as they occur, by providing the information enabling rectification, repair and recourse. However, supporting audit in XR brings considerations, as the process of capturing audit data itself has implications and challenges, both for the application (e.g., overheads) and more broadly. This paper explores the practicalities of auditing XR systems, characterises the tensions between audit and other considerations, and argues the need for flexible tools enabling the management of such. In doing so, we introduce Droiditor, a configurable open-source Android toolkit that enables the runtime capture of audit-relevant data from mobile applications. We use Droiditor as a means to indicate some potential implications of audit data capture, demonstrate how greater configurability can assist in managing audit-related concerns, and discuss the potential considerations that result. Given the societal demands for more transparent and accountable systems, our broader aim is to draw attention to auditability, highlighting tangible ways forward and areas for future work.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3495001"}, {"primary_key": "1961637", "vector": [], "sparse_vector": [], "title": "SwiVR-Car-Seat: Exploring Vehicle Motion Effects on Interaction Quality in Virtual Reality Automated Driving Using a Motorized Swivel Seat.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Autonomous vehicles provide new input modalities to improve interaction with in-vehicle information systems. However, due to the road and driving conditions, the user input can be perturbed, resulting in reduced interaction quality. One challenge is assessing the vehicle motion effects on the interaction without an expensive high-fidelity simulator or a real vehicle. This work presents SwiVR-Car-Seat, a low-cost swivel seat to simulate vehicle motion using rotation. In an exploratory user study (N=18), participants sat in a virtual autonomous vehicle and performed interaction tasks using the input modalities touch, gesture, gaze, or speech. Results show that the simulation increased the perceived realism of vehicle motion in virtual reality and the feeling of presence. Task performance was not influenced uniformly across modalities; gesture and gaze were negatively affected while there was little impact on touch and speech. The findings can advise automotive user interface design to mitigate the adverse effects of vehicle motion on the interaction.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494968"}, {"primary_key": "1961649", "vector": [], "sparse_vector": [], "title": "Urban Map Inference by Pervasive Vehicular Sensing Systems with Complementary Mobility.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Accurate and up-to-date digital road maps are the foundation of many mobile applications, such as navigation and autonomous driving. A manually-created map suffers from the high cost for creation and maintenance due to constant road network updating. Recently, the ubiquity of GPS devices in vehicular systems has led to an unprecedented amount of vehicle sensing data for map inference. Unfortunately, accurate map inference based on vehicle GPS is challenging for two reasons. First, it is challenging to infer complete road structures due to the sensing deviation, sparse coverage, and low sampling rate of GPS of a fleet of vehicles with similar mobility patterns, e.g., taxis. Second, a road map requires various road properties such as road categories, which is challenging to be inferred by just GPS locations of vehicles. In this paper, we design a map inference system called coMap by considering multiple fleets of vehicles with Complementary Mobility Features. coMap has two key components: a graph-based map sketching component, a learning-based map painting component. We implement coMap with the data from four type-aware vehicular sensing systems in one city, which consists of 18 thousand taxis, 10 thousand private vehicles, 6 thousand trucks, and 14 thousand buses. We conduct a comprehensive evaluation of coMap with two state-of-the-art baselines along with ground truth based on OpenStreetMap and a commercial map provider, i.e., Baidu Maps. The results show that (i) for the map sketching, our work improves the performance by 15.9%; (ii) for the map painting, our work achieves 74.58% of average accuracy on road category classification.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448076"}, {"primary_key": "1961650", "vector": [], "sparse_vector": [], "title": "CellSense: Human Mobility Recovery via Cellular Network Data Enhancement.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Data from the cellular network have been proved as one of the most promising way to understand large-scale human mobility for various ubiquitous computing applications due to the high penetration of cellphones and low collection cost. Existing mobility models driven by cellular network data suffer from sparse spatial-temporal observations because user locations are recorded with cellphone activities, e.g., calls, text, or internet access. In this paper, we design a human mobility recovery system called CellSense to take the sparse cellular billing data (CBR) as input and outputs dense continuous records to recover the sensing gap when using cellular networks as sensing systems to sense the human mobility. There is limited work on this kind of recovery systems at large scale because even though it is straightforward to design a recovery system based on regression models, it is very challenging to evaluate these models at large scale due to the lack of the ground truth data. In this paper, we explore a new opportunity based on the upgrade of cellular infrastructures to obtain cellular network signaling data as the ground truth data, which log the interaction between cellphones and cellular towers at signal levels (e.g., attaching, detaching, paging) even without billable activities. Based on the signaling data, we design a system CellSense for human mobility recovery by integrating collective mobility patterns with individual mobility modeling, which achieves the 35.3% improvement over the state-of-the-art models. The key application of our recovery model is to take regular sparse CBR data that a researcher already has, and to recover the missing data due to sensing gaps of CBR data to produce a dense cellular data for them to train a machine learning model for their use cases, e.g., next location prediction.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478087"}, {"primary_key": "1961663", "vector": [], "sparse_vector": [], "title": "OpiTrack: A Wearable-based Clinical Opioid Use Tracker with Temporal Convolutional Attention Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Brittany <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Opioid use disorder is a medical condition with major social and economic consequences. While ubiquitous physiological sensing technologies have been widely adopted and extensively used to monitor day-to-day activities and deliver targeted interventions to improve human health, the use of these technologies to detect drug use in natural environments has been largely underexplored. The long-term goal of our work is to develop a mobile technology system that can identify high-risk opioid-related events (i.e., development of tolerance in the setting of prescription opioid use, return-to-use events in the setting of opioid use disorder) and deploy just-in-time interventions to mitigate the risk of overdose morbidity and mortality. In the current paper, we take an initial step by asking a crucial question: Can opioid use be detected using physiological signals obtained from a wrist-mounted sensor? Thirty-six individuals who were admitted to the hospital for an acute painful condition and received opioid analgesics as part of their clinical care were enrolled. Subjects wore a noninvasive wrist sensor during this time (1-14 days) that continuously measured physiological signals (heart rate, skin temperature, accelerometry, electrodermal activity, and interbeat interval). We collected a total of 2070 hours (≈ 86 days) of physiological data and observed a total of 339 opioid administrations. Our results are encouraging and show that using a Channel-Temporal Attention TCN (CTA-TCN) model, we can detect an opioid administration in a time-window with an F1-score of 0.80, a specificity of 0.77, sensitivity of 0.80, and an AUC of 0.77. We also predict the exact moment of administration in this time-window with a normalized mean absolute error of 8.6% and R2 coefficient of 0.85.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478107"}, {"primary_key": "1961665", "vector": [], "sparse_vector": [], "title": "MSLife: Digital Behavioral Phenotyping of Multiple Sclerosis Symptoms in the Wild Using Wearables and Graph-Based Statistical Analysis.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Huining Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Treatment for multiple sclerosis (MS) focuses on managing its symptoms (e.g., depression, fatigue, poor sleep quality), varying with specific symptoms experienced. Thus, for optimal treatment, there arises the need to track these symptoms. Towards this goal, there is great interest in finding their relevant phenotypes. Prior research suggests links between activities of daily living (ADLs) and MS symptoms; therefore, we hypothesize that the behavioral phenotype (revealed through ADLs) is closely related to MS symptoms. Traditional approaches to finding behavioral phenotypes which rely on human observation or controlled clinical settings are burdensome and cannot account for all genuine ADLs. Here, we present MSLife, an end-to-end, burden-free approach to digital behavioral phenotyping of MS symptoms in the wild using wearables and graph-based statistical analysis. MSLife is built upon (1) low-cost, unobtrusive wearables (i.e., smartwatches) that can track and quantify ADLs among MS patients in the wild; (2) graph-based statistical analysis that can model the relationships between quantified ADLs (i.e., digital behavioral phenotype) and MS symptoms. We design, implement, and deploy MSLife with 30 MS patients across a one-week home-based IRB-approved clinical pilot study. We use the GENEActiv smartwatch to monitor ADLs and clinical behavioral instruments to collect MS symptoms. Then we develop a graph-based statistical analysis framework to model phenotyping relationships between ADLs and MS symptoms, incorporating confounding demographic factors. We discover 102 significant phenotyping relationships (e.g., later rise times are related to increased levels of depression, history of caffeine consumption is associated with lower fatigue levels, higher relative levels of moderate physical activity are linked with decreased sleep quality). We validate their healthcare implications, using them to track MS symptoms in retrospective analysis. To our best knowledge, this is one of the first practices to digital behavioral phenotyping of MS symptoms in the wild.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494970"}, {"primary_key": "1961666", "vector": [], "sparse_vector": [], "title": "Unravelling Spatial Privacy Risks of Mobile Mixed Reality Data.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previously, 3D data---particularly, spatial data---have primarily been utilized in the field of geo-spatial analyses, or robot navigation (e.g. self-automated cars) as 3D representations of geographical or terrain data (usually extracted from lidar). Now, with the increasing user adoption of augmented, mixed, and virtual reality (AR/MR/VR; we collectively refer to as MR) technology on user mobile devices, spatial data has become more ubiquitous. However, this ubiquity also opens up a new threat vector for adversaries: aside from the traditional forms of mobile media such as images and video, spatial data poses additional and, potentially, latent risks to users of AR/MR/VR. Thus, in this work, we analyse MR spatial data using various spatial complexity metrics---including a cosine similarity-based, and a Euclidean distance-based metric---as heuristic or empirical measures that can signify the inference risk a captured space has. To demonstrate the risk, we utilise 3D shape recognition and classification algorithms for spatial inference attacks over various 3D spatial data captured using mobile MR platforms: i.e. Microsoft HoloLens, and Android with Google ARCore. Our experimental evaluation and investigation shows that the cosine similarity-based metric is a good spatial complexity measure of captured 3D spatial maps and can be utilised as an indicator of spatial inference risk.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448103"}, {"primary_key": "1961674", "vector": [], "sparse_vector": [], "title": "iMon: Appearance-based Gaze Tracking System on Mobile Devices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "JeongGil Ko"], "summary": "Gaze tracking is a key building block used in many mobile applications including entertainment, personal productivity, accessibility, medical diagnosis, and visual attention monitoring. In this paper, we present iMon, an appearance-based gaze tracking system that is both designed for use on mobile phones and has significantly greater accuracy compared to prior state-of-the-art solutions. iMon achieves this by comprehensively considering the gaze estimation pipeline and then overcoming three different sources of errors. First, instead of assuming that the user's gaze is fixed to a single 2D coordinate, we construct each gaze label using a probabilistic 2D heatmap gaze representation input to overcome errors caused by microsaccade eye motions that cause the exact gaze point to be uncertain. Second, we design an image enhancement model to refine visual details and remove motion blur effects of input eye images. Finally, we apply a calibration scheme to correct for differences between the perceived and actual gaze points caused by individual Kappa angle differences. With all these improvements, iMon achieves a person-independent per-frame tracking error of 1.49 cm (on smartphones) and 1.94 cm (on tablets) when tested with the GazeCapture dataset and 2.01 cm with the TabletGaze dataset. This outperforms the previous state-of-the-art solutions by ~22% to 28%. By averaging multiple per-frame estimations that belong to the same fixation point and applying personal calibration, the tracking error is further reduced to 1.11 cm (smartphones) and 1.59 cm (tablets). Finally, we built implementations that run on an iPhone 12 Pro and show that our mobile implementation of iMon can run at up to 60 frames per second - thus making gaze-based control of applications possible.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494999"}, {"primary_key": "1961678", "vector": [], "sparse_vector": [], "title": "SwingNet: Ubiquitous Fine-Grained Swing Tracking Framework via Stochastic Neural Architecture Search and Adversarial Learning.", "authors": ["<PERSON>", "Ji<PERSON><PERSON>", "<PERSON>"], "summary": "Sports analytics in the wild (i.e., ubiquitously) is a thriving industry. Swing tracking is a key feature in sports analytics. Therefore, a centimeter-level tracking resolution solution is required. Recent research has explored deep neural networks for sensor fusion to produce consistent swing-tracking performance. This is achieved by combining the advantages of two sensor modalities (IMUs and depth sensors) for golf swing tracking. Here, the IMUs are not affected by occlusion and can support high sampling rates. Meanwhile, depth sensors produce significantly more accurate motion measurements than those produced by IMUs. Nevertheless, this method can be further improved in terms of accuracy and lacking information for different domains (e.g., subjects, sports, and devices). Unfortunately, designing a deep neural network with good performance is time consuming and labor intensive, which is challenging when a network model is deployed to be used in new settings. To this end, we propose a network based on Neural Architecture Search (NAS), called SwingNet, which is a regression-based automatic generated deep neural network via stochastic neural network search. The proposed network aims to learn the swing tracking feature for better prediction automatically. Furthermore, SwingNet features a domain discriminator by using unsupervised learning and adversarial learning to ensure that it can be adaptive to unobserved domains. We implemented SwingNet prototypes with a smart wristband (IMU) and smartphone (depth sensor), which are ubiquitously available. They enable accurate sports analytics (e.g., coaching, tracking, analysis and assessment) in the wild. Our comprehensive experiment shows that SwingNet achieves less than 10 cm errors of swing tracking with a subject-independent model covering multiple sports (e.g., golf and tennis) and depth sensor hardware, which outperforms state-of-the-art approaches.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478082"}, {"primary_key": "1961684", "vector": [], "sparse_vector": [], "title": "Be Consistent, Work the Program, Be Present Every Day: Exploring Technologies for Self-Tracking in Early Recovery.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recovery from substance abuse disorders (SUDs) is a lifelong process of change. Self-tracking technologies have been proposed by the recovery community as a beneficial design space to support people adopting positive lifestyles and behaviors in their recovery. To explore the potential of this design space, we designed and deployed a technology probe consisting of a mobile app, wearable visualization, and ambient display to enable people to track and reflect on the activities they adopted in their recovery process. With this probe we conducted a four-week exploratory field study with 17 adults in early recovery to investigate 1) what activities people in recovery desire to track, 2) how people perceive self-tracking tools in relation to their recovery process, and 3) what digital resources self-tracking tools can provide to aid the recovery process. Our findings illustrate the array of activities that people track in their recovery, along with usage scenarios, preferences and design tensions that arose. We discuss implications for holistic self-tracking technologies and opportunities for future work in behavior change support for this context.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494955"}, {"primary_key": "1961691", "vector": [], "sparse_vector": [], "title": "When Do Drivers Interact with In-Vehicle Well-being Interventions?: An Exploratory Analysis of a Longitudinal Study on Public Roads.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent developments of novel in-vehicle interventions show the potential to transform the otherwise routine and mundane task of commuting into opportunities to improve the drivers' health and well-being. Prior research has explored the effectiveness of various in-vehicle interventions and has identified moments in which drivers could be interruptible to interventions. All the previous studies, however, were conducted in either simulated or constrained real-world driving scenarios on a pre-determined route. In this paper, we take a step forward and evaluate when drivers interact with in-vehicle interventions in unconstrained free-living conditions. To this end, we conducted a two-month longitudinal study with 10 participants, in which each participant was provided with a study car for their daily driving needs. We delivered two in-vehicle interventions - each aimed at improving affective well-being - and simultaneously recorded the participants' driving behavior. In our analysis, we found that several pre-trip characteristics (like trip length, traffic flow, and vehicle occupancy) and the pre-trip affective state of the participants had significant associations with whether the participants started an intervention or canceled a started intervention. Next, we found that several in-the-moment driving characteristics (like current road type, past average speed, and future brake behavior) showed significant associations with drivers' responsiveness to the intervention. Further, we identified several driving behaviors that \"negated\" the effectiveness of interventions and highlight the potential of using such \"negative\" driving characteristics to better inform intervention delivery. Finally, we compared trips with and without intervention and found that both interventions employed in our study did not have a negative effect on driving behavior. Based on our analyses, we provide solid recommendations on how to deliver interventions to maximize responsiveness and effectiveness and minimize the burden on the drivers.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448116"}, {"primary_key": "1961694", "vector": [], "sparse_vector": [], "title": "Designing Kitchen Technologies for Ageing in Place: A Video Study of Older Adults&apos; Cooking at Home.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Assistive technologies can significantly increase older adults' independent living if these technologies are designed to meet their needs and abilities. This study investigates conditions and present possibilities for assistive technology to provide physical and cognitive support to older adults in a specific domestic task, which is cooking a meal at home. The empirical material consists of six video recordings of adults aged 65 and over preparing a meal in their kitchen. The study unpacks the complexity of kitchen tasks, from the physical interactions involved to the temporal and spatial alignment of objects and goals in the kitchen. We focus on a) Physical manipulation, such as chopping, opening packages, and moving objects around the kitchen, b) Organisation and coordination, including switching, synchronising and monitoring cooking tasks, and c) Reorchestration and reorganisation in the form of inserting additional tasks, and rearranging tools and ingredients when adjustments need to be made in the cooking process. The study outlines design principles for operational and organisational interventions to support cooking a meal for independent living. The study concludes with discussing design implications for conversational user interfaces in the kitchen, and the significance of assistive kitchen technologies for ageing in place.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463516"}, {"primary_key": "1961704", "vector": [], "sparse_vector": [], "title": "DualRing: Enabling Subtle and Expressive Hand Interaction with Dual IMU Rings.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present DualRing, a novel ring-form input device that can capture the state and movement of the user's hand and fingers. With two IMU rings attached to the user's thumb and index finger, DualRing can sense not only the absolute hand gesture relative to the ground but also the relative pose and movement among hand segments. To enable natural thumb-to-finger interaction, we develop a high-frequency AC circuit for on-body contact detection. Based on the sensing information of DualRing, we outline the interaction space and divide it into three sub-spaces: within-hand interaction, hand-to-surface interaction, and hand-to-object interaction. By analyzing the accuracy and performance of our system, we demonstrate the informational advantage of DualRing in sensing comprehensive hand gestures compared with single-ring-based solutions. Through the user study, we discovered the interaction space enabled by DualRing is favored by users for its usability, efficiency, and novelty.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478114"}, {"primary_key": "1961708", "vector": [], "sparse_vector": [], "title": "DistFL: Distribution-aware Federated Learning for Mobile Scenarios.", "authors": ["<PERSON><PERSON>", "Yifeng Cai", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Federated learning (FL) has emerged as an effective solution to decentralized and privacy-preserving machine learning for mobile clients. While traditional FL has demonstrated its superiority, it ignores the non-iid (independently identically distributed) situation, which widely exists in mobile scenarios. Failing to handle non-iid situations could cause problems such as performance decreasing and possible attacks. Previous studies focus on the \"symptoms\" directly, as they try to improve the accuracy or detect possible attacks by adding extra steps to conventional FL models. However, previous techniques overlook the root causes for the \"symptoms\": blindly aggregating models with the non-iid distributions. In this paper, we try to fundamentally address the issue by decomposing the overall non-iid situation into several iid clusters and conducting aggregation in each cluster. Specifically, we propose DistFL, a novel framework to achieve automated and accurate Distribution-aware Federated Learning in a cost-efficient way. DistFL achieves clustering via extracting and comparing the distribution knowledge from the uploaded models. With this framework, we are able to generate multiple personalized models with distinctive distributions and assign them to the corresponding clients. Extensive experiments on mobile scenarios with popular model architectures have demonstrated the effectiveness of DistFL.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494966"}, {"primary_key": "1961709", "vector": [], "sparse_vector": [], "title": "AdaSpring: Context-adaptive and Runtime-evolutionary Deep Model Compression for Mobile Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Junz<PERSON> Du"], "summary": "There are many deep learning (e.g., DNN) powered mobile and wearable applications today continuously and unobtrusively sensing the ambient surroundings to enhance all aspects of human lives. To enable robust and private mobile sensing, DNN tends to be deployed locally on the resource-constrained mobile devices via model compression. The current practice either hand-crafted DNN compression techniques, i.e., for optimizing DNN-relative performance (e.g., parameter size), or on-demand DNN compression methods, i.e., for optimizing hardware-dependent metrics (e.g., latency), cannot be locally online because they require offline retraining to ensure accuracy. Also, none of them have correlated their efforts with runtime adaptive compression to consider the dynamic nature of the deployment context of mobile applications. To address those challenges, we present AdaSpring, a context-adaptive and self-evolutionary DNN compression framework. It enables the runtime adaptive DNN compression locally online. Specifically, it presents the ensemble training of a retraining-free and self-evolutionary network to integrate multiple alternative DNN compression configurations (i.e., compressed architectures and weights). It then introduces the runtime search strategy to quickly search for the most suitable compression configurations and evolve the corresponding weights. With evaluation on five tasks across three platforms and a real-world case study, experiment outcomes show that AdaSpring obtains up to 3.1x latency reduction, 4.2 x energy efficiency improvement in DNNs, compared to hand-crafted compression techniques, while only incurring <= 6.2ms runtime-evolution latency.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448125"}, {"primary_key": "1961712", "vector": [], "sparse_vector": [], "title": "SplitSR: An End-to-End Approach to Super-Resolution on Mobile Devices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Shwetak N. Patel"], "summary": "Super-resolution (SR) is a coveted image processing technique for mobile apps ranging from the basic camera apps to mobile health. Existing SR algorithms rely on deep learning models with significant memory requirements, so they have yet to be deployed on mobile devices and instead operate in the cloud to achieve feasible inference time. This shortcoming prevents existing SR methods from being used in applications that require near real-time latency. In this work, we demonstrate state-of-the-art latency and accuracy for on-device super-resolution using a novel hybrid architecture called SplitSR and a novel lightweight residual block called SplitSRBlock. The SplitSRBlock supports channel-splitting, allowing the residual blocks to retain spatial information while reducing the computation in the channel dimension. SplitSR has a hybrid design consisting of standard convolutional blocks and lightweight residual blocks, allowing people to tune SplitSR for their computational budget. We evaluate our system on a low-end ARM CPU, demonstrating both higher accuracy and up to 5× faster inference than previous approaches. We then deploy our model onto a smartphone in an app called ZoomSR to demonstrate the first-ever instance of on-device, deep learning-based SR. We conducted a user study with 15 participants to have them assess the perceived quality of images that were post-processed by SplitSR. Relative to bilinear interpolation --- the existing standard for on-device SR --- participants showed a statistically significant preference when looking at both images (Z=-9.270, p&lt;0.01) and text (Z=-6.486, p&lt;0.01).", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448104"}, {"primary_key": "1961713", "vector": [], "sparse_vector": [], "title": "WR-Hand: Wearable Armband Can Track User&apos;s Hand.", "authors": ["<PERSON>", "<PERSON><PERSON> Lin", "Zhenjiang Li"], "summary": "This paper presents WR-Hand, a wearable-based system tracking 3D hand pose of 14 hand skeleton points over time using Electromyography (EMG) and gyroscope sensor data from commercial armband. This system provides a significant leap in wearable sensing and enables new application potentials in medical care, human-computer interaction, etc. A challenge is the armband EMG sensors inevitably collect mixed EMG signals from multiple forearm muscles because of the fixed sensor positions on the device, while prior bio-medical models for hand pose tracking are built on isolated EMG signal inputs from isolated forearm spots for different muscles. In this paper, we leverage the recent success of neural networks to enhance the existing bio-medical model using the armband's EMG data and visualize our design to understand why our solution is effective. Moreover, we propose solutions to place the constructed hand pose reliably in a global coordinate system, and address two practical issues by providing a general plug-and-play version for new users without training and compensating for the position difference in how users wear their armbands. We implement a prototype using different commercial armbands, which is lightweight to execute on user's phone in real-time. Extensive evaluation shows the efficacy of the WR-Hand design.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478112"}, {"primary_key": "1961717", "vector": [], "sparse_vector": [], "title": "Mover: Generalizability Verification of Human Mobility Models via Heterogeneous Use Cases.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Human mobility models typically produce mobility data to capture human mobility patterns individually or collectively based on real-world observations or assumptions, which are essential for many use cases in research and practice, e.g., mobile networking, autonomous driving, urban planning, and epidemic control. However, most existing mobility models suffer from practical issues like unknown accuracy and uncertain parameters in new use cases because they are normally designed and verified based on a particular use case (e.g., mobile phones, taxis, or mobile payments). This causes significant challenges for researchers when they try to select a representative human mobility model with appropriate parameters for new use cases. In this paper, we introduce a MObility VERification framework called MOVER to systematically measure the performance of a set of representative mobility models including both theoretical and empirical models based on a diverse set of use cases with various measures. Based on a taxonomy built upon spatial granularity and temporal continuity, we selected four representative mobility use cases (e.g., the vehicle tracking system, the camera-based system, the mobile payment system, and the cellular network system) to verify the generalizability of the state-of-the-art human mobility models. MOVER methodically characterizes the accuracy of five different mobility models in these four use cases based on a comprehensive set of mobility measures and provide two key lessons learned: (i) For the collective level measures, the finer spatial granularity of the user cases, the better generalization of the theoretical models; (ii) For the individual-level measures, the lower periodic temporal continuity of the user cases, the theoretical models typically generalize better than the empirical models. The verification results can help the research community to select appropriate mobility models and parameters in different use cases.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494997"}, {"primary_key": "1961719", "vector": [], "sparse_vector": [], "title": "CrowdAct: Achieving High-Quality Crowdsourced Datasets in Mobile Activity Recognition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this study, we propose novel gamified active learning and inaccuracy detection for crowdsourced data labeling for an activity recognition system using mobile sensing (CrowdAct). First, we exploit active learning to address the lack of accurate information. Second, we present the integration of gamification into active learning to overcome the lack of motivation and sustained engagement. Finally, we introduce an inaccuracy detection algorithm to minimize inaccurate data. To demonstrate the capability and feasibility of the proposed model in realistic settings, we developed and deployed the CrowdAct system to a crowdsourcing platform. For our experimental setup, we recruited 120 diverse workers. Additionally, we gathered 6,549 activity labels from 19 activity classes by using smartphone sensors and user engagement information. We empirically evaluated the quality of CrowdAct by comparing it with a baseline using techniques such as machine learning and descriptive and inferential statistics. Our results indicate that CrowdAct was effective in improving activity accuracy recognition, increasing worker engagement, and reducing inaccurate data in crowdsourced data labeling. Based on our findings, we highlight critical and promising future research directions regarding the design of efficient activity data collection with crowdsourcing.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3432222"}, {"primary_key": "1961725", "vector": [], "sparse_vector": [], "title": "ShaZam: Charge-Free Wearable Devices via Intra-Body Power Transfer from Everyday Objects.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>on-<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this work, we investigate a wireless power transfer technology that can unobtrusively charge wearable devices while users interact with everyday objects, such as an office desk, laptop, or car. We design and develop our solution, ShaZam, that exploits the human body as a medium to transfer Radio Frequency (RF) energy-carrier signals from minimally-instrumented daily objects to wearable devices. We focus on establishing the technical groundwork of the proposed technology by incorporating the capacitive coupling mechanism, in which the forward signal path is established through the human body, and the return path is established via capacitive coupling to the surrounding environment. To showcase the feasibility of our technology, we investigate three different use scenarios---i.e., interacting with a keyboard on a desk, a laptop, and the steering wheel of a car---to transfer power to a wrist-worn device. Using data obtained from ten healthy individuals within a setting where uncontrolled electromagnetic interference was relatively low, we demonstrate that we can transfer approximately 0.5 mW - 1 mW of DC power to the wrist-worn device. We also investigate several critical environmental and design parameters that could affect the power transfer and offer design guidelines that optimize performance. Our initial results suggest the potential for a new design paradigm towards completely charge-free wearable devices.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463505"}, {"primary_key": "1961730", "vector": [], "sparse_vector": [], "title": "CHIMERA: Supporting Wearables Development across Multidisciplinary Perspectives.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Wearable technologies draw on a range of disciplines, including fashion, textiles, HCI, and engineering. Due to differences in methodology, wearables researchers can experience gaps or breakdowns in values, goals, and vocabulary when collaborating. This situation makes wearables development challenging, even more so when technologies are in the early stages of development and their technological and cultural potential is not fully understood. We propose a common ground to enhance the accessibility of wearables-related resources. The objective is to raise awareness and create a convergent space for researchers and developers to both access and share information across domains. We present CHIMERA, an online search interface that allows users to explore wearable technologies beyond their discipline. CHIMERA is powered by a Wearables Taxonomy and a database of research, tutorials, aesthetic approaches, concepts, and patents. To validate CHIMERA, we used a design task with multidisciplinary designers, an open-ended usability study with experts, and a usability survey with students of a wearables design class. Our findings suggest that CHIMERA assists users with different mindsets and skillsets to engage with information, expand and share knowledge when developing wearables. It forges common ground across divergent disciplines, encourages creativity, and affords the formation of inclusive, multidisciplinary perspectives in wearables development.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494974"}, {"primary_key": "1961731", "vector": [], "sparse_vector": [], "title": "FabHandWear: An End-to-End Pipeline from Design to Fabrication of Customized Functional Hand Wearables.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Current hand wearables have limited customizability, they are loose-fit to an individual's hand and lack comfort. The main barrier in customizing hand wearables is the geometric complexity and size variation in hands. Moreover, there are different functions that the users can be looking for; some may only want to detect hand's motion or orientation; others may be interested in tracking their vital signs. Current wearables usually fit multiple functions and are designed for a universal user with none or limited customization. There are no specialized tools that facilitate the creation of customized hand wearables for varying hand sizes and provide different functionalities. We envision an emerging generation of customizable hand wearables that supports hand differences and promotes hand exploration with additional functionality. We introduce FabHandWear, a novel system that allows end-to-end design and fabrication of customized functional self-contained hand wearables. FabHandWear is designed to work with off-the-shelf electronics, with the ability to connect them automatically and generate a printable pattern for fabrication. We validate our system by using illustrative applications, a durability test, and an empirical user evaluation. Overall, FabHandWear offers the freedom to create customized, functional, and manufacturable hand wearables.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463518"}, {"primary_key": "1961732", "vector": [], "sparse_vector": [], "title": "Enabling Real-time Sign Language Translation on Mobile Platforms with On-board Depth Cameras.", "authors": ["Hyeonjung Park", "<PERSON><PERSON>", "JeongGil Ko"], "summary": "In this work we present SUGO, a depth video-based system for translating sign language to text using a smartphone's front camera. While exploiting depth-only videos offer benefits such as being less privacy-invasive compared to using RGB videos, it introduces new challenges which include dealing with low video resolutions and the sensors' sensitiveness towards user motion. We overcome these challenges by diversifying our sign language video dataset to be robust to various usage scenarios via data augmentation and design a set of schemes to emphasize human gestures from the input images for effective sign detection. The inference engine of SUGO is based on a 3-dimensional convolutional neural network (3DCNN) to classify a sequence of video frames as a pre-trained word. Furthermore, the overall operations are designed to be light-weight so that sign language translation takes place in real-time using only the resources available on a smartphone, with no help from cloud servers nor external sensing components. Specifically, to train and test SUGO, we collect sign language data from 20 individuals for 50 Korean Sign Language words, summing up to a dataset of ~5,000 sign gestures and collect additional in-the-wild data to evaluate the performance of SUGO in real-world usage scenarios with different lighting conditions and daily activities. Comprehensively, our extensive evaluations show that SUGO can properly classify sign words with an accuracy of up to 91% and also suggest that the system is suitable (in terms of resource usage, latency, and environmental robustness) to enable a fully mobile solution for sign language translation.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463498"}, {"primary_key": "1961733", "vector": [], "sparse_vector": [], "title": "The Design and Evaluation of a Mobile System for Rapid Diagnostic Test Interpretation.", "authors": ["Chunjong Park", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Victoria Lyon", "<PERSON>", "<PERSON>", "<PERSON>", "Shwetak N. Patel"], "summary": "Rapid diagnostic tests (RDTs) provide point-of-care medical screening without the need for expensive laboratory equipment. RDTs are theoretically straightforward to use, yet their analog colorimetric output leaves room for diagnostic uncertainty and error. Furthermore, RDT results within a community are kept isolated unless they are aggregated by healthcare workers, limiting the potential that RDTs can have in supporting public health efforts. In light of these issues, we present a system called RDTScan for detecting and interpreting lateral flow RDTs with a smartphone. RDTScan provides real-time guidance for clear RDT image capture and automatic interpretation for accurate diagnostic decisions. RDTScan is structured to be quickly configurable to new RDT designs by requiring only a template image and some metadata about how the RDT is supposed to be read, making it easier to extend than a data-driven approach. Through a controlled lab study, we demonstrate that RDTScan's limit-of-detection can match, and even exceed, the performance of expert readers who are interpreting the physical RDTs themselves. We then present two field evaluations of smartphone apps built on the RDTScan system: (1) at-home influenza testing in Australia and (2) malaria testing by community healthcare workers in Kenya. RDTScan achieved 97.5% and 96.3% accuracy compared to RDT interpretation by experts in the Australia Flu Study and the Kenya Malaria Study, respectively.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448106"}, {"primary_key": "1961735", "vector": [], "sparse_vector": [], "title": "SquiggleMilli: Approximating SAR Imaging on Mobile Millimeter-Wave Devices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Sanjib Sur", "<PERSON><PERSON>"], "summary": "This paper proposes SquiggleMilli, a system that approximates traditional Synthetic Aperture Radar (SAR) imaging on mobile millimeter-wave (mmWave) devices. The system is capable of imaging through obstructions, such as clothing, and under low visibility conditions. Unlike traditional SAR that relies on mechanical controllers or rigid bodies, SquiggleMilli is based on the hand-held, fluidic motion of the mmWave device. It enables mmWave imaging in hand-held settings by re-thinking existing motion compensation, compressed sensing, and voxel segmentation. Since mmWave imaging suffers from poor resolution due to specularity and weak reflectivity, the reconstructed shapes could be imperceptible by machines and humans. To this end, <PERSON><PERSON>ggleMilli designs a machine learning model to recover the high spatial frequencies in the object to reconstruct an accurate 2D shape and predict its 3D features and category. We have customized SquiggleMilli for security applications, but the model is adaptable to other applications with limited training samples. We implement SquiggleMilli on off-the-shelf components and demonstrate its performance improvement over the traditional SAR qualitatively and quantitatively.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478113"}, {"primary_key": "1961744", "vector": [], "sparse_vector": [], "title": "HERMAS: A Human Mobility Embedding Framework with Large-scale Cellular Signaling Data.", "authors": ["<PERSON><PERSON>", "Dongz<PERSON> Jiang", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Efficient representations for spatio-temporal cellular Signaling Data (SD) are essential for many human mobility applications. Traditional representation methods are mainly designed for GPS data with high spatio-temporal continuity, and thus will suffer from poor embedding performance due to the unique Ping Pong Effect in SD. To address this issue, we explore the opportunity offered by a large number of human mobility traces and mine the inherent neighboring tower connection patterns. More specifically, we design HERMAS, a novel representation learning framework for large-scale cellular SD with three steps: (1) extract rich context information in each trajectory, adding neighboring tower information as extra knowledge in each mobility observation; (2) design a sequence encoding model to aggregate the embedding of each observation; (3) obtain the embedding for a trajectory. We evaluate the performance of HERMAS based on two human mobility applications, i.e. trajectory similarity measurement and user profiling. We conduct evaluations based on a 30-day SD dataset with 130,612 users and 2,369,267 moving trajectories. Experimental results show that (1) for the trajectory similarity measurement application, HERMAS improves the Hitting Rate (HR@10) from 15.2% to 39.2%; (2) for the user profiling application, HERMAS improves the F1-score for around 9%. More importantly, HERMAS significantly improves the computation efficiency by over 30x.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478108"}, {"primary_key": "1961759", "vector": [], "sparse_vector": [], "title": "CSMC: Cellular Signal Map Construction via Mobile Crowdsensing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The rise concern about mobile communication performance has driven the growing demand for the construction of mobile network signal maps which are widely utilized in network monitoring, spectrum management, and indoor/outdoor localization. Existing studies such as time-consuming and labor-intensive site surveys are difficult to maintain an update-to-date finegrained signal map within a large area. The mobile crowdsensing (MCS) paradigm is a promising approach for building signal maps because collecting large-scale MCS data is low-cost and with little extra-efforts. However, the dynamic environment and the mobility of the crowd cause spatio-temporal uncertainty and sparsity of MCS. In this work, we leverage MCS as an opportunity to conduct the city-wide mobile network signal map construction. We propose a fine-grained city-wide Cellular Signal Map Construction (CSMC) framework to address two challenges including (i) the problem of missing and unreliable MCS data; (ii) spatio-temporal uncertainty of signal propagation. In particular, CSMC captures spatio-temporal characteristics of signals from both inter- and intra- cellular base stations and conducts missing signal recovery with Bayesian tensor decomposition to build large-area fine-grained signal maps. Furthermore, CSMC develops a context-aware multi-view fusion network to make full use of external information and enhance signal map construction accuracy. To evaluate the performance of CSMC, we conduct extensive experiments and ablation studies on a large-scale dataset with over 200GB MCS signal records collected from Shanghai. Experimental results demonstrate that our model outperforms state-of-the-art baselines in the accuracy of signal estimation and user localization.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494959"}, {"primary_key": "1961762", "vector": [], "sparse_vector": [], "title": "EarDynamic: An Ear Canal Deformation Based Continuous User Authentication Using In-Ear Wearables.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Biometric-based authentication is gaining increasing attention for wearables and mobile applications. Meanwhile, the growing adoption of sensors in wearables also provides opportunities to capture novel wearable biometrics. In this work, we propose EarDynamic, an ear canal deformation based user authentication using in-ear wearables. EarDynamic provides continuous and passive user authentication and is transparent to users. It leverages ear canal deformation that combines the unique static geometry and dynamic motions of the ear canal when the user is speaking for authentication. It utilizes an acoustic sensing approach to capture the ear canal deformation with the built-in microphone and speaker of the in-ear wearable. Specifically, it first emits well-designed inaudible beep signals and records the reflected signals from the ear canal. It then analyzes the reflected signals and extracts fine-grained acoustic features that correspond to the ear canal deformation for user authentication. Our extensive experimental evaluation shows that EarDynamic can achieve a recall of 97.38% and an F1 score of 96.84%. Results also show that our system works well under different noisy environments with various daily activities.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448098"}, {"primary_key": "1961763", "vector": [], "sparse_vector": [], "title": "Spatio-Temporal Urban Knowledge Graph Enabled Mobility Prediction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "With the rapid development of the mobile communication technology, mobile trajectories of humans are massively collected by Internet service providers (ISPs) and application service providers (ASPs). On the other hand, the rising paradigm of knowledge graph (KG) provides us a promising solution to extract structured \"knowledge\" from massive trajectory data. In this paper, we focus on modeling users' spatio-temporal mobility patterns based on knowledge graph techniques, and predicting users' future movement based on the \"knowledge\" extracted from multiple sources in a cohesive manner. Specifically, we propose a new type of knowledge graph, i.e., spatio-temporal urban knowledge graph (STKG), where mobility trajectories, category information of venues, and temporal information are jointly modeled by the facts with different relation types in STKG. The mobility prediction problem is converted to the knowledge graph completion problem in STKG. Further, a complex embedding model with elaborately designed scoring functions is proposed to measure the plausibility of facts in STKG to solve the knowledge graph completion problem, which considers temporal dynamics of the mobility patterns and utilizes PoI categories as the auxiliary information and background knowledge. Extensive evaluations confirm the high accuracy of our model in predicting users' mobility, i.e., improving the accuracy by 5.04% compared with the state-of-the-art algorithms. In addition, PoI categories as the background knowledge and auxiliary information are confirmed to be helpful by improving the performance by 3.85% in terms of accuracy. Additionally, experiments show that our proposed method is time-efficient by reducing the computational time by over 43.12% compared with existing methods.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494993"}, {"primary_key": "1961775", "vector": [], "sparse_vector": [], "title": "Computing Touch-Point Ambiguity on Mobile Touchscreens for Modeling Target Selection Times.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Finger-Fitts law (<PERSON>itts law) is a model to predict touch-pointing times, modified from <PERSON><PERSON>' law. It considers the absolute touch-point precision, or a finger tremor factor σa, to decrease the admissible target area and thus increase the task difficulty. Among choices such as running an independent task or performing parameter optimization, there is no consensus on the best methodology to measure σa. This inconsistency could be detrimental to HCI studies such as pointing technique evaluations and user group comparisons. By integrating the results of our 1D and 2D touch-pointing experiments and reanalyses of previous studies' data, we examined the advantages and disadvantages of each approach to compute σa. We found that the parameter optimization method is a suboptimal choice for predicting the performance.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494976"}, {"primary_key": "1961776", "vector": [], "sparse_vector": [], "title": "Spatio-Temporal Graph Attention Embedding for Joint Crowd Flow and Transition Predictions: A Wi-Fi-based Mobility Case Study.", "authors": ["<PERSON>", "Suining He", "<PERSON>", "<PERSON><PERSON>"], "summary": "Crowd mobility prediction, in particular, forecasting flows at and transitions across different locations, is essential for crowd analytics and management in spacious environments featured with large gathering. We propose GAEFT, a novel crowd mobility analytics system based on the multi-task graph attention neural network to forecast crowd flows (inflows/outflows) and transitions. Specifically, we leverage the collective and sanitized campus Wi-Fi association data provided by our university information technology service and conduct a relatable case study. Our comprehensive data analysis reveals the important challenges of sparsity and skewness, as well as the complex spatio-temporal variations within the crowd mobility data. Therefore, we design a novel spatio-temporal clustering method to group Wi-Fi access points (APs) with similar transition features, and obtain more regular mobility features for model inputs. We then propose an attention-based graph embedding design to capture the correlations among the crowd flows and transitions, and jointly predict the AP-level flows as well as transitions across buildings and clusters through a multi-task formulation. Extensive experimental studies using more than 28 million association records collected during 2020-2021 academic year validate the excellent accuracy of GAEFT in forecasting dynamic and complex crowd mobility.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3495003"}, {"primary_key": "1961782", "vector": [], "sparse_vector": [], "title": "Ubi-SleepNet: Advanced Multimodal Fusion Techniques for Three-stage Sleep Classification Using Ubiquitous Sensing.", "authors": ["<PERSON>", "Yu <PERSON>", "<PERSON>", "<PERSON>"], "summary": "Sleep is a fundamental physiological process that is essential for sustaining a healthy body and mind. The gold standard for clinical sleep monitoring is polysomnography(PSG), based on which sleep can be categorized into five stages, including wake/rapid eye movement sleep (REM sleep)/Non-REM sleep 1 (N1)/Non-REM sleep 2 (N2)/Non-REM sleep 3 (N3). However, PSG is expensive, burdensome and not suitable for daily use. For long-term sleep monitoring, ubiquitous sensing may be a solution. Most recently, cardiac and movement sensing has become popular in classifying three-stage sleep, since both modalities can be easily acquired from research-grade or consumer-grade devices (e.g., Apple Watch). However, how best to fuse the data for greatest accuracy remains an open question. In this work, we comprehensively studied deep learning (DL)-based advanced fusion techniques consisting of three fusion strategies alongside three fusion methods for three-stage sleep classification based on two publicly available datasets. Experimental results demonstrate important evidences that three-stage sleep can be reliably classified by fusing cardiac/movement sensing modalities, which may potentially become a practical tool to conduct large-scale sleep stage assessment studies or long-term self-tracking on sleep. To accelerate the progression of sleep research in the ubiquitous/wearable computing community, we made this project open source, and the code can be found at: https://github.com/bzhai/Ubi-SleepNet.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494961"}, {"primary_key": "1961786", "vector": [], "sparse_vector": [], "title": "SoundLip: Enabling Word and Sentence-level Lip Interaction for Smart Devices.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As a natural and convenient interaction modality, voice input has now become indispensable to smart devices (e.g. mobile phones and smart appliances). However, voice input is strongly constrained by surroundings and may raise privacy leakage in public areas. In this paper, we present SoundLip, an end-to-end interaction system enabling users to interact with smart devices via silent voice input. The key insight is to use inaudible acoustic signals to capture the lip movements of users when they issue commands. Previous works have considered lip reading as a naive classification task and thus can only recognize individual words. In contrast, our proposed system enables lip reading at both word and sentence levels, which are more suitable for daily-life use. We exploit the built-in speakers and microphones of smart devices to emit acoustic signals and listen to their reflections, respectively. In order to better abstract representations from multi-frequency and multi-modality acoustic signals, we elaborate a hierarchical convolutional neural network (HCNN) to serve as the front-end as well as recognize individual word commands. Then, for the sentence-level recognition, we exploit a multi-task encoder-decoder network to get around temporal segmentation and output sentences in an end-to-end way. We evaluate SoundLip on 20 individual words and 70 sentences from 12 participants. Our system achieves an accuracy of 91.2% at word-level and a word error rate of 7.1% at sentence-level in both user-independent and environment-independent settings. Given its innovative solution and promising performance, we believe that SoundLip has made a significant contribution to the advancement of silent voice input technology.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448087"}, {"primary_key": "1961788", "vector": [], "sparse_vector": [], "title": "Sensing to Hear: Speech Enhancement for Mobile Devices Using Acoustic Signals.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Voice interactions and voice messages on mobile phones are rapidly growing in popularity. However, the user experience of these services is still worse than desired in noisy environments, especially in multi-talker scenarios, where the phone can only provide low-quality voice recordings. Speech enhancement using only audio as the input remains a grand challenge in these scenarios. In this paper, we handle this with the help of the emerging acoustic sensing technology. The key insight is that the inaudible acoustic signals emitted by speakers of phones can capture the subtle lip movements when people speak. Instead of enabling lip reading for the classification of limited voice commands, we further unlock the potential of acoustic sensing and leverage the captured lip information to improve the voice recording quality. We propose WaveVoice, a joint audio-sensory deep learning method for end-to-end speech enhancement on mobile phones. The model of WaveVoice is structured as an encoder-decoder network, in which audio and acoustic sensing data are processed through two individual CNN branches, respectively, and then fused into a joint network to generate enhanced speech. In addition, to improve the performance on new users, a self-supervised learning methodology is developed to adapt the model to extract speaker-specific features. We construct a dataset to train and evaluate WaveVoice. We also perform online tests under various noisy conditions to show the applicability of our system in real-world scenarios. Experimental results show that WaveVoice can effectively reconstruct the target clean speech from the noisy audio signals, and yield notably superior performance compared with the audio-only encoder-decoder model and the state-of-the-art speech enhancement methods. Given its promising performance, we believe that WaveVoice has made a substantial contribution to the advancement of mobile voice input.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478093"}, {"primary_key": "1961789", "vector": [], "sparse_vector": [], "title": "Passive Health Monitoring Using Large Scale Mobility Data.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Tong Li", "<PERSON><PERSON><PERSON>", "Pan Hui", "<PERSON>"], "summary": "In this paper, we investigate the feasibility of using mobility patterns and demographic data to predict hospital visits. We collect mobility traces from two thousand users for around two months. We extract 16 mobility features from these passively collected mobility traces and train an XGBoost model to predict users' hospital visits. We demonstrate that the designed mobility features can significantly improve prediction accuracy (p &lt; 0.01, AUC = 0.79). We further analyze how these mobility features affect the prediction results and measure their importance by using S<PERSON><PERSON>y additive explanation values. We discover that users with less mobility activity, less visit diversity, and few sports facilities, bountiful entertainment around their visited locations are more likely to visit hospitals. Moreover, we conduct predictions on the populations with different demographic features, which achieves meaningful and insightful results, i.e. maintaining a high mobility activity is crucial for older people's health, while fast food store more substantially affects younger people's health; visit patterns can indicate females' health, while the neighborhood environment is more indicative of males, etc. These results shed light on how to use and understand large scale mobility data in health monitoring and other health-related applications in practice.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448078"}, {"primary_key": "1961790", "vector": [], "sparse_vector": [], "title": "Quantifying the Causal Effect of Individual Mobility on Health Status in Urban Space.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "How does individual mobility in the urban environment impact their health status? Previous works have explored the correlation between human mobility behaviour and individual health, yet the study on the underlying causal effect is woefully inadequate. However, the correlation analysis can sometimes be bewildering because of the confounding effects. For example, older people visit park more often but have worse health status than younger people. The common associations with age will lead to a counter-intuitive negative correlation between park visits and health status. Obtaining causal effects from confounded observations remains a challenge. In this paper, we construct a causal framework based on propensity score matching on multi-level treatment to eliminate the bias brought by confounding effects and estimate the total treatment effects of mobility behaviours on health status. We demonstrate that the matching procedure approximates a de-confounded randomized experiment where confounding variables are balanced substantially. The analysis on the directions of estimated causal effects reveals that fewer neighbouring tobacco shops and frequent visits to sports facilities are related with higher risk in health status, which differs from their correlation directions. Physical mobility behaviours and environment features have more significant estimated effects on health status than contextual mobility behaviours. Moreover, we embed our causal analysis framework in health prediction models to filter out features with superficial correlation but insignificant effects that might lead to over-fitting. This strategy achieves better model robustness with more features filtered out than L1-regularization. Our findings shed light on individual healthy lifestyle and mobility-related health policymaking.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494990"}, {"primary_key": "1961604", "vector": [], "sparse_vector": [], "title": "RFaceID: Towards RFID-based Facial Recognition.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xingyu Feng", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Face recognition (FR) has been widely used in many areas nowadays. However, the existing mainstream vision-based facial recognition has limitations such as vulnerability to spoofing attacks, sensitivity to lighting conditions, and high risk of privacy leakage, etc. To address these problems, in this paper we take a sparkly different approach and propose RFaceID, a novel RFID-based face recognition system. RFaceID only needs the users to shake their faces in front of the RFID tag matrix for a few seconds to get their faces recognized. Through theoretical analysis and experiment validations, the feasibility of the RFID-based face recognition is studied. Multiple data processing and data augmentation techniques are proposed to minimize the negative impact of environmental noises and user dynamics. A deep neural network (DNN) model is designed to characterize both the spatial and temporal feature of face shaking events. We implement the system and extensive evaluation results show that RFaceID achieves a high face recognition accuracy at 93.1% for 100 users, which shows the potential of RFaceID for future facial recognition applications.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494985"}, {"primary_key": "1961606", "vector": [], "sparse_vector": [], "title": "SonicFace: Tracking Facial Expressions Using a Commodity Microphone Array.", "authors": ["<PERSON>", "Yincheng Jin", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Li", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Accurate recognition of facial expressions and emotional gestures is promising to understand the audience's feedback and engagement on the entertainment content. Existing methods are primarily based on various cameras or wearable sensors, which either raise privacy concerns or demand extra devices. To this aim, we propose a novel ubiquitous sensing system based on the commodity microphone array --- SonicFace, which provides an accessible, unobtrusive, contact-free, and privacy-preserving solution to monitor the user's emotional expressions continuously without playing hearable sound. SonicFace utilizes a pair of speaker and microphone array to recognize various fine-grained facial expressions and emotional hand gestures by emitted ultrasound and received echoes. Based on a set of experimental evaluations, the accuracy of recognizing 6 common facial expressions and 4 emotional gestures can reach around 80%. Besides, the extensive system evaluations with distinct configurations and an extended real-life case study have demonstrated the robustness and generalizability of the proposed SonicFace system.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494988"}, {"primary_key": "1961610", "vector": [], "sparse_vector": [], "title": "CoolMoves: User Motion Accentuation in Virtual Reality.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Current Virtual Reality (VR) systems are bereft of stylization and embellishment of the user's motion - concepts that have been well explored in animations for games and movies. We present CooIMoves, a system for expressive and accentuated full-body motion synthesis of a user's virtual avatar in real-time, from the limited input cues afforded by current consumer-grade VR systems, specifically headset and hand positions. We make use of existing motion capture databases as a template motion repository to draw from. We match similar spatio-temporal motions present in the database and then interpolate between them using a weighted distance metric. Joint prediction probability is then used to temporally smooth the synthesized motion, using human motion dynamics as a prior. This allows our system to work well even with very sparse motion databases (e.g., with only 3-5 motions per action). We validate our system with four experiments: a technical evaluation of our quantitative pose reconstruction and three additional user studies to evaluate the motion quality, embodiment and agency.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463499"}, {"primary_key": "1961611", "vector": [], "sparse_vector": [], "title": "mTeeth: Identifying Brushing Teeth Surfaces Using Wrist-Worn Inertial Sensors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Ensuring that all the teeth surfaces are adequately covered during daily brushing can reduce the risk of several oral diseases. In this paper, we propose the mTeeth model to detect teeth surfaces being brushed with a manual toothbrush in the natural free-living environment using wrist-worn inertial sensors. To unambiguously label sensor data corresponding to different surfaces and capture all transitions that last only milliseconds, we present a lightweight method to detect the micro-event of brushing strokes that cleanly demarcates transitions among brushing surfaces. Using features extracted from brushing strokes, we propose a Bayesian Ensemble method that leverages the natural hierarchy among teeth surfaces and patterns of transition among them. For training and testing, we enrich a publicly-available wrist-worn inertial sensor dataset collected from the natural environment with time-synchronized precise labels of brushing surface timings and moments of transition. We annotate 10,230 instances of brushing on different surfaces from 114 episodes and evaluate the impact of wide between-person and within-person between-episode variability on machine learning model's performance for brushing surface detection.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463494"}, {"primary_key": "1961612", "vector": [], "sparse_vector": [], "title": "Orbuculum - Predicting When Users Intend to Leave Large Public Displays.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a system, predicting the point in time when users of a public display are about to leave. The ability to react to users' intention to leave is valuable for researchers and practitioners alike: users can be presented additional content with the goal to maximize interaction times; they can be offered a discount coupon for redemption in a nearby store hence enabling new business models; or feedback can be collected from users right after they have finished interaction without interrupting their task. Our research consists of multiple steps: (1) We identified features that hint at users' intention to leave from observations and video logs. (2) We implemented a system capable of detecting such features from Microsoft Kinect's skeleton data and subsequently make a prediction. (3) We trained and deployed a prediction system with a Quiz game which reacts when users are about to leave (N=249), achieving an accuracy of 78%. The majority of users indeed reacted to the presented intervention.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448075"}, {"primary_key": "1961613", "vector": [], "sparse_vector": [], "title": "ViscoCam: Smartphone-based Drink Viscosity Control Assistant for Dysphagia Patients.", "authors": ["Kecheng An", "<PERSON><PERSON>", "<PERSON>"], "summary": "Dysphagia patients need to carefully control their intake liquid's viscosity to reduce choking and aspiration risks. However, accurate liquid viscosity measurement requires expensive rheometers still unavailable in daily life. Though the existing approximate testing methods are low-cost, they are not convenient for everyday use as they require either tedious procedures or dedicated apparatus. This paper presents ViscoCam, the first liquid viscosity classification system for dysphagia patients or carers, which only requires a smartphone. It is easy to operate, widely deployable, and robust for daily use. ViscoCam classifies visually indistinguishable liquid of various viscosity levels by exploiting the fact that the sloshing motion of viscous liquid decays faster than thin liquid. To perform a measurement, the user shakes a cup of liquid and their smartphone to induce the liquid sloshing motion. Then, ViscoCam senses the cup's motion using the smartphone's built-in accelerometer or microphone and infers liquid viscosity from the fluid surface motion captured by flashlight camera. To combat changes in camera position, lighting conditions, and liquid sloshing motion, a 3D convolutional neural network is trained to extract reliable motion features for classification. We evaluate ViscoCam's performance in classifying three levels in the IDDSI standard, which is the most up-to-date and internationally adopted one for dysphagia patients. Results show that ViscoCam achieves an overall accuracy of 96.52% in controlled cases. It is robust to unseen liquid heights or container sizes, and &gt;81% accuracy is maintained under extreme testing cases.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448109"}, {"primary_key": "1961614", "vector": [], "sparse_vector": [], "title": "REHASH: A Flexible, Developer Focused, Heuristic Adaptation Platform for Intermittently Powered Computing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Battery-free sensing devices harvest energy from their surrounding environment to perform sensing, computation, and communication. This enables previously impossible applications in the Internet-of-Things. A core challenge for these devices is maintaining usefulness despite erratic, random or irregular energy availability; which causes inconsistent execution, loss of service and power failures. Adapting execution (degrading or upgrading) seems promising as a way to stave off power failures, meet deadlines, or increase throughput. However, because of constrained resources and limited local information, it is a challenge to decide when would be the best time to adapt, and how exactly to adapt execution. In this paper, we systematically explore the fundamental mechanisms of energy-aware adaptation, and propose heuristic adaptation as a method for modulating the performance of tasks to enable higher sensor coverage, completion rates, or throughput, depending on the application. We build a task based adaptive runtime system for intermittently powered sensors embodying this concept. We complement this runtime with a user facing simulator that enables programmers to conceptualize the tradeoffs they make when choosing what tasks to adapt, and how, relative to real world energy harvesting environment traces. While we target battery-free, intermittently powered sensors, we see general application to all energy harvesting devices. We explore heuristic adaptation with varied energy harvesting modalities and diverse applications: machine learning, activity recognition, and greenhouse monitoring, and find that the adaptive version of our ML app performs up to 46% more classifications with only a 5% drop in accuracy; the activity recognition app captures 76% more classifications with only nominal down-sampling; and find that heuristic adaptation leads to higher throughput versus non-adaptive in all cases.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478077"}, {"primary_key": "1961615", "vector": [], "sparse_vector": [], "title": "RFTemp: Monitoring Microwave Oven Leakage to Estimate Food Temperature.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Microwave ovens have been widely used in recent years to heat food quickly and efficiently. Users estimate the time to heat the food by prior knowledge or by trial and error process. However, this often results in the food being over-heated or under-heated, destroying the nutrients. In this paper, we present RFTemp, a system that can monitor microwave oven leakage to estimate the temperature of the food that is being heated and thus estimate the accurate time when the food has reached the targeted temperature. To design such a system, we propose an innovative microwave leakage sensing procedure and a novel water-equivalent food model to estimate food temperature. To evaluate the real-world performance of RFTemp we build a prototype using software defined radios and conducted experiments on various food items using household microwave ovens. We show that RFTemp can estimate the temperature of the food with a mean error of 5°C, 2x improvement over contactless infrared thermometer and sensors.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494967"}, {"primary_key": "1961616", "vector": [], "sparse_vector": [], "title": "Who Am I?: A Design Probe Exploring Real-Time Transparency about Online and Offline User Profiling Underlying Targeted Ads.", "authors": ["Natã M<PERSON>", "<PERSON>", "<PERSON><PERSON> U<PERSON>", "<PERSON>"], "summary": "To enable targeted ads, companies profile Internet users, automatically inferring potential interests and demographics. While current profiling centers on users' web browsing data, smartphones and other devices with rich sensing capabilities portend profiling techniques that draw on methods from ubiquitous computing. Unfortunately, even existing profiling and ad-targeting practices remain opaque to users, engendering distrust, resignation, and privacy concerns. We hypothesized that making profiling visible at the time and place it occurs might help users better understand and engage with automatically constructed profiles. To this end, we built a technology probe that surfaces the incremental construction of user profiles from both web browsing and activities in the physical world. The probe explores transparency and control of profile construction in real time. We conducted a two-week field deployment of this probe with 25 participants. We found that increasing the visibility of profiling helped participants anticipate how certain actions can trigger specific ads. Participants' desired engagement with their profile differed in part based on their overall attitudes toward ads. Furthermore, participants expected algorithms would automatically determine when an inference was inaccurate, no longer relevant, or off-limits. Current techniques typically do not do this. Overall, our findings suggest that leveraging opportunistic moments within pervasive computing to engage users with their own inferred profiles can create more trustworthy and positive experiences with targeted ads.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478122"}, {"primary_key": "1961618", "vector": [], "sparse_vector": [], "title": "Smartphone-Based Tapping Frequency as a Surrogate for Perceived Fatigue: An in-the-Wild Feasibility Study in Multiple Sclerosis Patients.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Fatigue is a common symptom in various diseases, including multiple sclerosis (MS). The current standard method to assess fatigue is through questionnaires, which has several shortcomings; questionnaires are subjective, prone to recall bias, and potentially confounded by other symptoms like stress and depression. Thus, there is an unmet medical need to develop objective and reliable methods to evaluate fatigue. Our study seeks to develop an objective and ubiquitous monitoring tool for assessing fatigue. Leveraging a smartphone-based rapid tapping task, we conducted a two-week in-the-wild study with 35 MS patients. We explore the association between tapping derived metrics and perceived fatigue assessed with two standard clinical scales: fatigue severity scale (FSS) and fatigue scale for motor and cognitive function (FSMC). Our novel smartphone-based fatigue metric, mean tapping frequency, objectively ranks perceived fatigue with a mean AUCROC = .76, CI = [.71, .81] according to the FSMC, and a mean AUCROC = .81, CI = [.76, .86] according to the FSS. These results demonstrate that our approach is feasible and valid in uncontrolled environments. In this work, we provide a promising tool for objective fatigue monitoring to be used in clinical trials and routine medical care.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478098"}, {"primary_key": "1961621", "vector": [], "sparse_vector": [], "title": "IMU2Doppler: Cross-Modal Domain Adaptation for Doppler-based Activity Recognition Using IMU Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The proliferation of sensors powered by state-of-the-art machine learning techniques can now infer context, recognize activities and enable interactions. A key component required to build these automated sensing systems is labeled training data. However, the cost of collecting and labeling new data impedes our ability to deploy new sensors to recognize human activities. We tackle this challenge using domain adaptation i.e., using existing labeled data in a different domain to aid the training of a machine learning model for a new sensor. In this paper, we use off-the-shelf smartwatch IMU datasets to train an activity recognition system for mmWave radar sensor with minimally labeled data. We demonstrate that despite the lack of extensive datasets for mmWave radar, we are able to use our domain adaptation approach to build an activity recognition system that classifies between 10 activities with an accuracy of 70% with only 15 seconds of labeled doppler data. We also present results for a range of available labeled data (10 - 30 seconds) and show that our approach outperforms the baseline in every single scenario. We take our approach a step further and show that multiple IMU datasets can be combined together to act as a single source for our domain adaptation approach. Lastly, we discuss the limitations of our work and how it can impact future research directions.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494994"}, {"primary_key": "1961622", "vector": [], "sparse_vector": [], "title": "Handwriting-Assistant: Reconstructing Continuous Strokes with Millimeter-level Accuracy via Attachable Inertial Sensors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Pen-based handwriting has become one of the major human-computer interaction methods. Traditional approaches either require writing on the specific supporting device like the touch screen, or limit the way of using the pen to pure rotation or translation. In this paper, we propose Handwriting-Assistant, to capture the free handwriting of ordinary pens on regular planes with mm-level accuracy. By attaching the inertial measurement unit (IMU) to the pen tail, we can infer the handwriting on the notebook, blackboard or other planes. Particularly, we build a generalized writing model to correlate the rotation and translation of IMU with the tip displacement comprehensively, thereby we can infer the tip trace accurately. Further, to display the effective handwriting during the continuous writing process, we leverage the principal component analysis (PCA) based method to detect the candidate writing plane, and then exploit the distance variation of each segment relative to the plane to distinguish on-plane strokes. Moreover, our solution can apply to other rigid bodies, enabling smart devices embedded with IMUs to act as handwriting tools. Experiment results show that our approach can capture the handwriting with high accuracy, e.g., the average tracking error is 1.84mm for letters with the size of about 2cmx1cm, and the average character recognition rate of recovered single letters achieves 98.2% accuracy of the ground-truth recorded by touch screen.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494956"}, {"primary_key": "1961623", "vector": [], "sparse_vector": [], "title": "Outliers in Smartphone Sensor Data Reveal Outliers in Daily Happiness.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Enabling smartphones to understand our emotional well-being provides the potential to create personalised applications and highly responsive interfaces. However, this is by no means a trivial task - subjectivity in reporting emotions impacts the reliability of ground-truth information whereas smartphones, unlike specialised wearables, have limited sensing capabilities. In this paper, we propose a new approach that advances emotional state prediction by extracting outlier-based features and by mitigating the subjectivity in capturing ground-truth information. We utilised this approach in a distinctive and challenging use case - happiness detection - and we demonstrated prediction performance improvements of up to 13% in AUC and 27% in F-score compared to the traditional modelling approaches. The results indicate that extreme values (i.e. outliers) of sensor readings mirror extreme values in the reported happiness levels. Furthermore, we showed that this approach is more robust in replicating the prediction model in completely new experimental settings.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448095"}, {"primary_key": "1961624", "vector": [], "sparse_vector": [], "title": "We Hear Your PACE: Passive Acoustic Localization of Multiple Walking Persons.", "authors": ["<PERSON>", "<PERSON>nglin Pu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Indoor localization is crucial to enable context-aware applications, but existing solutions mostly require a user to carry a device, so as to actively sense location-discriminating signals. However, many applications do not prefer user involvement due to, e.g., the cumbersome of carrying a device. Therefore, solutions that track user locations passively can be desirable, yet lack of active user involvement has made passive indoor localization very challenging even for a single person. To this end, we propose Passive Acoustic loCalization of multiple walking pErsons (PACE) as a solution for small-scale indoor scenarios: it passively locates users by pinpointing the positions of their footsteps. In particular, PACE leverages both structure-borne and air-borne footstep impact sounds (FIS); it uses structure-borne FIS for range estimations exploiting their acoustic dispersion nature, and it employs air-borne FIS for Angle-of-Arrival (AoA) estimations and person identifications. To combat the low-SNR nature of FIS, PACE innovatively employs domain adversarial adaptation and spectral weighting to ranging/identification and AoA estimations, respectively. We implement a PACE prototype and extensively evaluate its performance in representative environments. The results demonstrate a promising sub-meter localization accuracy with a median error of 30 cm.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463510"}, {"primary_key": "1961625", "vector": [], "sparse_vector": [], "title": "Investigating the Effect of Sensory Concurrency on Learning Haptic Spatiotemporal Signals.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A new generation of multimodal interfaces and interactions is emerging. Drawing on the principles of Sensory Substitution and Augmentation Devices (SSADs), these new interfaces offer the potential for rich, immersive human-computer interactions, but are difficult to design well, and take time to master, creating significant barriers towards wider adoption. Following a review of the literature surrounding existing SSADs, their metrics for success and their growing influence on interface design in Human Computer Interaction, we present a medium term (4-day) study comparing the effectiveness of various combinations of visual and haptic feedback (sensory concurrencies) in preparing users to perform a virtual maze navigation task using haptic feedback alone. Participants navigated 12 mazes in each of 3 separate sessions under a specific combination of visual and haptic feedback, before performing the same task using the haptic feedback alone. Visual sensory deprivation was shown to be inferior to visual &amp; haptic concurrency in enabling haptic signal comprehension, while a new hybridized condition combining reduced visual feedback with the haptic signal was shown to be superior. Potential explanations for the effectiveness of the hybrid mechanism are explored, and the scope and implications of its generalization to new sensory interfaces is presented.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448102"}, {"primary_key": "1961626", "vector": [], "sparse_vector": [], "title": "MemX: An Attention-Aware Smart Eyewear System for Personalized Moment Auto-capture.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Mingzhi Dong", "<PERSON><PERSON> Wang", "<PERSON><PERSON>", "Qin Lv", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This work presents MemX: a biologically-inspired attention-aware eyewear system developed with the goal of pursuing the long-awaited vision of a personalized visual Memex. MemX captures human visual attention on the fly, analyzes the salient visual content, and records moments of personal interest in the form of compact video snippets. Accurate attentive scene detection and analysis on resource-constrained platforms is challenging because these tasks are computation and energy intensive. We propose a new temporal visual attention network that unifies human visual attention tracking and salient visual content analysis. Attention tracking focuses computation-intensive video analysis on salient regions, while video analysis makes human attention detection and tracking more accurate. Using the YouTube-VIS dataset and 30 participants, we experimentally show that MemX significantly improves the attention tracking accuracy over the eye-tracking-alone method, while maintaining high system energy efficiency. We have also conducted 11 in-field pilot studies across a range of daily usage scenarios, which demonstrate the feasibility and potential benefits of MemX.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463509"}, {"primary_key": "1961627", "vector": [], "sparse_vector": [], "title": "ViFin: <PERSON><PERSON>ss Passive Vibration to Continuous Micro Finger Writing with a Commodity Smartwatch.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shwetak N. Patel", "<PERSON>"], "summary": "Wearable devices, such as smartwatches and head-mounted devices (HMD), demand new input devices for a natural, subtle, and easy-to-use way to input commands and text. In this paper, we propose and investigate ViFin, a new technique for input commands and text entry, which harness finger movement induced vibration to track continuous micro finger-level writing with a commodity smartwatch. Inspired by the recurrent neural aligner and transfer learning, ViFin recognizes continuous finger writing, works across different users, and achieves an accuracy of 90% and 91% for recognizing numbers and letters, respectively. We quantify our approach's accuracy through real-time system experiments in different arm positions, writing speeds, and smartwatch position displacements. Finally, a real-time writing system and two user studies on real-world tasks are implemented and assessed.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448119"}, {"primary_key": "1961629", "vector": [], "sparse_vector": [], "title": "SenseCollect: We Need Efficient Ways to Collect On-body Sensor-based Human Activity Data!", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "On-body sensor-based human activity recognition (HAR) lags behind other fields because it lacks large-scale, labeled datasets; this shortfall impedes progress in developing robust and generalized predictive models. To facilitate researchers in collecting more extensive datasets quickly and efficiently we developed SenseCollect. We did a survey and interviewed student researchers in this area to identify what barriers are making it difficult to collect on-body sensor-based HAR data from human subjects. Every interviewee identified data collection as the hardest part of their research, stating it was laborious, consuming and error-prone. To improve HAR data resources we need to address that barrier, but we need a better understanding of the complicating factors to overcome it. To that end we conducted a series of control variable experiments that tested several protocols to ascertain their impact on data collection. SenseCollect studied 240+ human subjects in total and presented the findings to develop a data collection guideline. We also implemented a system to collect data, created the two largest on-body sensor-based human activity datasets, and made them publicly available.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478119"}, {"primary_key": "1961630", "vector": [], "sparse_vector": [], "title": "UVLens: Urban Village Boundary Identification and Population Estimation Leveraging Open Government Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhang", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Urban villages refer to the residential areas lagging behind the rapid urbanization process in many developing countries. These areas are usually with overcrowded buildings, high population density, and low living standards, bringing potential risks of public safety and hindering the urban development. Therefore, it is crucial for urban authorities to identify the boundaries of urban villages and estimate their resident and floating populations so as to better renovate and manage these areas. Traditional approaches, such as field surveys and demographic census, are time consuming and labor intensive, lacking a comprehensive understanding of urban villages. Against this background, we propose a two-phase framework for urban village boundary identification and population estimation. Specifically, based on heterogeneous open government data, the proposed framework can not only accurately identify the boundaries of urban villages from large-scale satellite imagery by fusing road networks guided patches with bike-sharing drop-off patterns, but also accurately estimate the resident and floating populations of urban villages with a proposed multi-view neural network model. We evaluate our method leveraging real-world datasets collected from Xiamen Island. Results show that our framework can accurately identify the urban village boundaries with an IoU of 0.827, and estimate the resident population and floating population with R2 of 0.92 and 0.94 respectively, outperforming the baseline methods. We also deploy our system on the Xiamen Open Government Data Platform to provide services to both urban authorities and citizens.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463495"}, {"primary_key": "1961631", "vector": [], "sparse_vector": [], "title": "ChestLive: Fortifying Voice-based Authentication with Chest Motion Biometric on Smart Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Voice-based authentication is prevalent on smart devices to verify the legitimacy of users, but is vulnerable to replay attacks. In this paper, we propose to leverage the distinctive chest motions during speaking to establish a secure multi-factor authentication system, named ChestLive. Compared with other biometric-based authentication systems, ChestLive does not require users to remember any complicated information (e.g., hand gestures, doodles) and the working distance is much longer (30cm). We use acoustic sensing to monitor chest motions with a built-in speaker and microphone on smartphones. To obtain fine-grained chest motion signals during speaking for reliable user authentication, we derive Channel Energy (CE) of acoustic signals to capture the chest movement, and then remove the static and non-static interference from the aggregated CE signals. Representative features are extracted from the correlation between voice signal and corresponding chest motion signal. Unlike learning-based image or speech recognition models with millions of available training samples, our system needs to deal with a limited number of samples from legitimate users during enrollment. To address this problem, we resort to meta-learning, which initializes a general model with good generalization property that can be quickly fine-tuned to identify a new user. We implement ChestLive as an application and evaluate its performance in the wild with 61 volunteers using their smartphones. Experiment results show that ChestLive achieves an authentication accuracy of 98.31% and less than 2% of false accept rate against replay attacks and impersonation attacks. We also validate that ChestLive is robust to various factors, including training set size, distance, angle, posture, phone models, and environment noises.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494962"}, {"primary_key": "1961632", "vector": [], "sparse_vector": [], "title": "ApneaDetector: Detecting Sleep Apnea with Smartwatches.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Tang", "<PERSON>-<PERSON>", "<PERSON><PERSON>"], "summary": "Sleep apnea is a sleep disorder in which breathing is briefly and repeatedly interrupted. Polysomnography (PSG) is the standard clinical test for diagnosing sleep apnea. However, it is expensive and time-consuming which requires hospital visits, specialized wearable sensors, professional installations, and long waiting lists. To address this problem, we design a smartwatch-based system called ApneaDetector, which exploits the built-in sensors in smartwatches to detect sleep apnea. Through a clinical study, we identify features of sleep apnea captured by smartwatch, which can be leveraged by machine learning techniques for sleep apnea detection. However, there are many technical challenges such as how to extract various special patterns from the noisy and multi-axis sensing data. To address these challenges, we propose signal denoising and data calibration techniques to process the noisy data while preserving the peaks and troughs which reflect the possible apnea events. We identify the characteristics of sleep apnea such as signal spikes which can be captured by smartwatch, and propose methods to extract proper features to train machine learning models for apnea detection. Through extensive experimental evaluations, we demonstrate that our system can detect apnea events with high precision (0.9674), recall (0.9625), and F1-score (0.9649).", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463514"}, {"primary_key": "1961633", "vector": [], "sparse_vector": [], "title": "Constructing Floor Plan through Smoke Using Ultra Wideband Radar.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON> Gu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "Floor plan construction has been one of the key techniques in many important applications such as indoor navigation, location-based services, and emergency rescue. Existing floor plan construction methods require expensive dedicated hardware (e.g., Lidar or depth camera), and may not work in low-visibility environments (e.g., smoke, fog or dust). In this paper, we develop a low-cost Ultra Wideband (UWB)-based system (named UWBMap) that is mounted on a mobile robot platform to construct floor plan through smoke. UWBMap leverages on low-cost and off-the-shelf UWB radar, and it is able to construct an indoor map with an accuracy comparable to Lidar (i.e., the state-of-the-art). The underpinning technique is to take advantage of the mobility of radar to form virtual antennas and gather spatial information of a target. UWBMap also eliminates both robot motion noise and environmental noise to enhance weak reflection from small objects for the robust construction process. In addition, we overcome the limited view of single radar by combining multi-view from multiple radars. Extensive experiments in different indoor environments show that UWBMap achieves a map construction with a median error of 11 cm and a 90-percentile error of 26 cm, and it operates effectively in indoor scenarios with glass wall and dense smoke.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494977"}, {"primary_key": "1961634", "vector": [], "sparse_vector": [], "title": "Duco: Autonomous Large-Scale Direct-Circuit-Writing (DCW) on Vertical Everyday Surfaces Using A Scalable Hanging Plotter.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Li", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Youngwook Do", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Oh"], "summary": "Human environments are filled with large open spaces that are separated by structures like walls, facades, glass windows, etc. Most often, these structures are largely passive offering little to no interactivity. In this paper, we present Duco, a large-scale electronics fabrication robot that enables room-scale &amp; building-scale circuitry to add interactivity to vertical everyday surfaces. Duco negates the need for any human intervention by leveraging a hanging robotic system that automatically sketches multi-layered circuity to enable novel large-scale interfaces. The key idea behind Duco is that it achieves single-layer or multi-layer circuit fabrication on 2D surfaces as well as 2D cutouts that can be assembled into 3D objects by loading various functional inks (e.g., conductive, dielectric, or cleaning) to the wall-hanging drawing robot, as well as employing an optional laser cutting head as a cutting tool. Our technical evaluation shows that Duco's mechanical system works reliably on various surface materials with a wide range of roughness and surface morphologies. The system achieves superior mechanical tolerances (0.1mm XY axis resolution and 1mm smallest feature size). We demonstrate our system with five application examples, including an interactive piano, an IoT coffee maker controller, an FM energy-harvester printed on a large glass window, a human-scale touch sensor and a 3D interactive lamp.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478118"}, {"primary_key": "1961635", "vector": [], "sparse_vector": [], "title": "Captivates: A Smart Eyeglass Platform for Across-Context Physiological Measurement.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present Captivates, an open-source smartglasses system designed for long-term, in-the-wild psychophysiological monitoring at scale. Captivates integrate many underutilized physiological sensors in a streamlined package, including temple and nose temperature measurement, blink detection, head motion tracking, activity classification, 3D localization, and head pose estimation. Captivates were designed with an emphasis on: (1) manufacturing and scalability, so we can easily support large scale user studies for ourselves and offer the platform as a generalized tool for ambulatory psychophysiology research; (2) robustness and battery life, so long-term studies result in trustworthy data individual's entire day in natural environments without supervision or recharge; and (3) aesthetics and comfort, so people can wear them in their normal daily contexts without self-consciousness or changes in behavior. Captivates are intended to enable large scale data collection without altering user behavior. We validate that our sensors capture useful data robustly for a small set of beta testers. We also show that our additional effort on aesthetics was imperative to meet our goals; namely, earlier versions of our prototype make people uncomfortable to interact naturally in public, and our additional design and miniaturization effort has made a significant impact in preserving natural behavior. There is tremendous promise in translating psychophysiological laboratory techniques into real-world insight. Captivates serve as an open-source bridge to this end. Paired with an accurate underlying model, Captivates will be able to quantify the long-term psychological impact of our design decisions and provide real-time feedback for technologists interested in actuating a cognitively adaptive, user-aligned future.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478079"}, {"primary_key": "1961638", "vector": [], "sparse_vector": [], "title": "How Should Automated Vehicles Communicate Critical Situations?: A Comparative Analysis of Visualization Concepts.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Passengers of automated vehicles will likely engage in non-driving related activities like reading and, therefore, be disengaged from the driving task. However, especially in critical situations such as unexpected pedestrian crossings, it can be assumed that passengers request information about the vehicle's intention and an explanation. Some concepts were proposed for such communication from the automated vehicle to the passenger. However, results are not comparable due to varying information content and scenarios. We present a comparative study in Virtual Reality (N=20) of four visualization concepts and a baseline with Augmented Reality, a Head-Up Display, or Lightbands. We found that all concepts were rated reasonable and necessary and increased trust, perceived safety, perceived intelligence, and acceptance compared to no visualization. However, when visualizations were compared, there were hardly any significant differences between them.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478111"}, {"primary_key": "1961639", "vector": [], "sparse_vector": [], "title": "FaceBit: Smart Face Masks Platform.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Aaron<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Nabil <PERSON>", "<PERSON>"], "summary": "The COVID-19 pandemic has dramatically increased the use of face masks across the world. Aside from physical distancing, they are among the most effective protection for healthcare workers and the general population. Face masks are passive devices, however, and cannot alert the user in case of improper fit or mask degradation. Additionally, face masks are optimally positioned to give unique insight into some personal health metrics. Recognizing this limitation and opportunity, we present FaceBit: an open-source research platform for smart face mask applications. FaceBit's design was informed by needfinding studies with a cohort of health professionals. Small and easily secured into any face mask, FaceBit is accompanied by a mobile application that provides a user interface and facilitates research. It monitors heart rate without skin contact via ballistocardiography, respiration rate via temperature changes, and mask-fit and wear time from pressure signals, all on-device with an energy-efficient runtime system. FaceBit can harvest energy from breathing, motion, or sunlight to supplement its tiny primary cell battery that alone delivers a battery lifetime of 11 days or more. FaceBit empowers the mobile computing community to jumpstart research in smart face mask sensing and inference, and provides a sustainable, convenient form factor for health management, applicable to COVID-19 frontline workers and beyond.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494991"}, {"primary_key": "1961640", "vector": [], "sparse_vector": [], "title": "A City-Wide Crowdsourcing Delivery System with Reinforcement Learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Mingming Lu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The revolution of online shopping in recent years demands corresponding evolution in delivery services in urban areas. To cater to this trend, delivery by the crowd has become an alternative to the traditional delivery services thanks to the advances in ubiquitous computing. Notably, some studies use public transportation for crowdsourcing delivery, given its low-cost delivery network with millions of passengers as potential couriers. However, multiple practical impact factors are not considered in existing public-transport-based crowdsourcing delivery studies due to a lack of data and limited ubiquitous computing infrastructures in the past. In this work, we design a crowdsourcing delivery system based on public transport, considering the practical factors of time constraints, multi-hop delivery, and profits. To incorporate the impact factors, we build a reinforcement learning model to learn the optimal order dispatching strategies from massive passenger data and package data. The order dispatching problem is formulated as a sequential decision making problem for the packages routing, i.e., select the next station for the package. A delivery time estimation module is designed to accelerate the training process and provide statistical delivery time guarantee. Three months of real-world public transportation data and one month of package delivery data from an on-demand delivery platform in Shenzhen are used in the evaluation. Compared with existing crowdsourcing delivery algorithms and widely used baselines, we achieve a 40% increase in profit rates and a 29% increase in delivery rates. Comparison with other reinforcement learning algorithms shows that we can improve the profit rate and the delivery rate by 9% and 8% by using time estimation in action filtering. We share the data used in the project to the community for other researchers to validate our results and conduct further research.1 [1].", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478117"}, {"primary_key": "1961641", "vector": [], "sparse_vector": [], "title": "SmartLOC: Indoor Localization with Smartphone Anchors for On-Demand Delivery.", "authors": ["<PERSON>", "Dongz<PERSON> Jiang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "On-demand delivery is a rapidly developing business worldwide, where meals and groceries are delivered door to door from merchants to customers by the couriers. Couriers' real-time localization plays a key role in on-demand delivery for all parties like the platform's order dispatching, merchants' order preparing, couriers' navigation, and customers' shopping experience. Although GPS has well solved outdoor localization, indoor localization is still challenging due to the lack of large-coverage, low-cost anchors. Given the high penetration of smartphones in merchants and frequent rendezvous between merchants and couriers, we employ merchants' smartphones as indoor anchors for a new sensing opportunity. In this paper, we design, implement and evaluate SmartLOC, a map-free localization system that employs merchants' smartphones as anchors to obtain couriers' real-time locations. Specifically, we design a rendezvous detection module based on Bluetooth Low Energy (BLE), build indoor shop graphs for each mall, and adopt graph embedding to extract indoor shops' topology. To guarantee anchors' accuracy and privacy, we build a mutual localization module to iteratively infer merchants' state (in-shop or not) and couriers' locations with transformer models. We implement SmartLOC in a large on-demand delivery platform and deploy the system in 566 malls in Shanghai, China. We evaluate SmartLOC in two multi-floor malls in Shanghai and show that it can improve the accuracy of couriers' travel time estimation by 24%, 43%, 70%, and 76% compared with a straightforward graph solution, GPS, Wi-Fi, and TransLoc.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494972"}, {"primary_key": "1961642", "vector": [], "sparse_vector": [], "title": "Leakage or Identification: Behavior-irrelevant User Identification Leveraging Leakage Current on Laptops.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Yang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The convenience of laptops brings with it the risk of information leakage, and conventional security systems based on the password or the explicit biometric do little to alleviate this problem. Biometric identification based on anatomical features provides far stronger security; however, a lack of suitable sensors on laptops limits the applicability of this technology. In this paper, we developed a behavior-irrelevant user identification system applicable to laptops with a metal casing. The proposed scheme, referred to as LeakPrint, is based on leakage current, wherein the system uses an earphone to capture current leaking through the body and then transmits the corresponding signal to a server for identification. The user identification is achieved via denoising, dimension reduction, and feature extraction. Compared to other biometric identification methods, the proposed system is less dependent on external hardware and more robust to environmental noise. The experiments in real-world environments demonstrated that LeakPrint can verify user identity with high accuracy (93.6%), while providing effective defense against replay attacks (96.5%) and mimicry attacks (90.9%).", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494984"}, {"primary_key": "1961643", "vector": [], "sparse_vector": [], "title": "RF-ray: Joint RF and Linguistics Domain Learning for Object Recognition.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Songjiang Hou", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents a non-invasive design, namely RF-ray, to recognize the shape and material of an object simultaneously. RF-ray puts the object approximate to an RFID tag array, and explores the propagation effect as well as coupling effect between RFIDs and the object for sensing. In contrast to prior proposals, RF-ray is capable to recognize unseen objects, including unseen shape-material pairs and unseen materials within a certain container. To make it real, RF-ray introduces a sensing capability enhancement module and leverages a two-branch neural network for shape profiling and material identification respectively. Furthermore, we incorporate a Zero-Shot Learning based embedding module that incorporates the well-learned linguistic features to generalize RF-ray to recognize unseen materials. We build a prototype of RF-ray using commodity RFID devices. Comprehensive real-world experiments demonstrate our system can achieve high object recognition performance.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478115"}, {"primary_key": "1961644", "vector": [], "sparse_vector": [], "title": "Smart Webcam Cover: Exploring the Design of an Intelligent Webcam Cover to Improve Usability and Trust.", "authors": ["Youngwook Do", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Laptop webcams can be covertly activated by malware and law enforcement agencies. Consequently, 59% percent of Americans manually cover their webcams to avoid being surveilled. However, manual covers are prone to human error---through a survey with 200 users, we found that 61.5% occasionally forget to re-attach their cover after using their webcam. To address this problem, we developed Smart Webcam Cover (SWC): a thin film that covers the webcam (PDLC-overlay) by default until a user manually uncovers the webcam, and automatically covers the webcam when not in use. Through a two-phased design iteration process, we evaluated SWC with 20 webcam cover users through a remote study with a video prototype of SWC, compared to manual operation, and discussed factors that influence users' trust in the effectiveness of SWC and their perceptions of its utility.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494983"}, {"primary_key": "1961645", "vector": [], "sparse_vector": [], "title": "Investigating Retention in Passive Haptic Learning of Piano Songs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Passive haptic learning (PHL) is a phenomenon where one is able to acquire new motor skills through repeated haptic stimuli applied to the body without paying active attention to learning. In the following work, we investigated the retention of passively learned material compared to actively learned material. For the purposes of answering this question, we invited 20 individuals to learn a 10-note sequence actively and a 10-note sequence passively on a piano. The subjects were then tested 3 days later on their remembered material. There was no significant difference between passive and active learning when the subjects played the note sequences from memory. After being cued (auditory and visually) the participants were able to recall the passively learned note sequence significantly better.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463513"}, {"primary_key": "1961646", "vector": [], "sparse_vector": [], "title": "Full-Dimension Relative Positioning for RFID-Enabled Self-Checkout Services.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Zhenhua Li", "<PERSON><PERSON>"], "summary": "Self-checkout services in today's retail stores are well received as they set free the labor force of cashiers and shorten conventional checkout lines. However, existing self-checkout options either require customers to scan items one by one, which is troublesome and inefficient, or rely on deployments of massive sensors and cameras together with complex tracking algorithms. On the other hand, RFID-based item-level tagging in retail offers an extraordinary opportunity to enhance current checkout experiences. In this work, we propose Taggo, a lightweight and efficient self-checkout schema utilizing well-deployed RFIDs. Taggo attaches a few anchor tags on the four upper edges of each shopping cart, so as to figure out which cart each item belongs to, through relative positioning among the tagged items and anchor tags without knowing their absolute positions. Specifically, a full-dimension ordering technique is devised to accurately determine the order of tags in each dimension, as well as to address the negative impacts from imperfect measurements in indoor surroundings. Besides, we design a holistic classifying solution based on probabilistic modeling to map each item to the correct cart that carries it. We have implemented Taggo with commercial RFID devices and evaluated it extensively in our lab environment. On average, Taggo achieves 90% ordering accuracy in real-time, eventually producing 95% classifying accuracy.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448094"}, {"primary_key": "1961647", "vector": [], "sparse_vector": [], "title": "Is Someone Listening?: Audio-Related Privacy Perceptions and Design Recommendations from Guardians, Pragmatists, and Cynics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Smart devices with the capability to record audio can create a trade-off for users between convenience and privacy. To understand how users experience this trade-off, we report on data from 35 interview, focus group, and design workshop participants. Participants' perspectives on smart-device audio privacy clustered into the pragmatist, guardian, and cynic perspectives that have previously been shown to characterize privacy concerns in other domains. These user groups differed along four axes in their audio-related behaviors (for example, guardians alone say they often move away from a microphone when discussing a sensitive topic). Participants surfaced three usage phases that require design consideration with respect to audio privacy: 1) adoption, 2) in-the-moment recording, and 3) downstream use of audio data. We report common design solutions that participants created for each phase (such as indicators showing when an app is recording audio and annotations making clear when an advertisement was selected based on past audio recording).", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478091"}, {"primary_key": "1961648", "vector": [], "sparse_vector": [], "title": "ISACS: In-Store Autonomous Checkout System for Retail.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "90% of retail sales occur in physical stores. In these physical stores 40% of shoppers leave the store based on the wait time. Autonomous stores can remove customer waiting time by providing a receipt without the need for scanning the items. Prior approaches use computer vision only, combine computer vision with weight sensors, or combine computer vision with sensors and human product recognition. These approaches, in general, suffer from low accuracy, up to hour long delays for receipt generation, or do not scale to store level deployments due to computation requirements and real-world multiple shopper scenarios. We present ISACS, which combines a physical store model (e.g. customers, shelves, and item interactions), multi-human 3D pose estimation, and live inventory monitoring to provide an accurate matching of multiple people to multiple products. ISACS utilizes only shelf weight sensors and does not require visual inventory monitoring which drastically reduces the computational requirements and thus is scalable to a store-level deployment. In addition, ISACS generates an instant receipt by not requiring human intervention during receipt generation. To fully evaluate the ISACS, we deployed and evaluated our approach in an operating convenience store covering 800 square feet with 1653 distinct products, and more than 20,000 items. Over the course of 13 months of operation, ISACS achieved a receipt daily accuracy of up to 96.4%. Which translates to a 3.5x reduction in error compared to self-checkout stations.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478086"}, {"primary_key": "1961651", "vector": [], "sparse_vector": [], "title": "S3: Side-Channel Attack on <PERSON><PERSON><PERSON> Pencil through Sensors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Yang", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "With smart devices being an essential part of our everyday lives, unsupervised access to the mobile sensors' data can result in a multitude of side-channel attacks. In this paper, we study potential data leaks from Apple Pencil (2nd generation) supported by the Apple iPad Pro, the latest stylus pen which attaches to the iPad body magnetically for charging. We observe that the Pencil's body affects the magnetic readings sensed by the iPad's magnetometer when a user is using the Pencil. Therefore, we ask: Can we infer what a user is writing on the iPad screen with the Apple Pencil, given access to only the iPad's motion sensors' data? To answer this question, we present Side-channel attack on Stylus pencil through Sensors (S3), a system that identifies what a user is writing from motion sensor readings. We first use the sharp fluctuations in the motion sensors' data to determine when a user is writing on the iPad. We then introduce a high-dimensional particle filter to track the location and orientation of the Pencil during usage. Lastly, to guide particles, we build the Pencil's magnetic map serving as a bridge between the measured magnetic data and the Pencil location and orientation. We evaluate S3 with 10 subjects and demonstrate that we correctly identify 93.9%, 96%, 97.9%, and 93.33% of the letters, numbers, shapes, and words by only having access to the motion sensors' data.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448085"}, {"primary_key": "1961652", "vector": [], "sparse_vector": [], "title": "RF-Identity: Non-Intrusive Person Identification Based on Commodity RFID Devices.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Person identification plays a critical role in a large range of applications. Recently, RF based person identification becomes a hot research topic due to the contact-free nature of RF sensing that is particularly appealing in current COVID-19 pandemic. However, existing systems still have multiple limitations: i) heavily rely on the gait patterns of users for identification; ii) require a large amount of data to train the model and also extensive retraining for new users and iii) require a large frequency bandwidth which is not available on most commodity RF devices for static person identification. This paper proposes RF-Identity, an RFID-based identification system to address the above limitations and the contribution is threefold. First, by integrating walking pattern features with unique body shape features (e.g., height), RF-Identity achieves a high accuracy in person identification. Second, RF-Identity develops a data augmentation scheme to expand the size of the training data set, thus reducing the human effort in data collection. Third, RF-Identity utilizes the tag diversity in spatial domain to identify static users without a need of large frequency bandwidth. Extensive experiments show an identification accuracy of 94.2% and 95.9% for 50 dynamic and static users, respectively.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448101"}, {"primary_key": "1961653", "vector": [], "sparse_vector": [], "title": "User Expectations and Mental Models for Communicating Emotions through Compressive &amp; Warm Affective Garment Actuation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Wearable haptic garments for communicating emotions have great potential in various applications, including supporting social interactions, improving immersive experiences in entertainment, or simply as a research tool. Shape-memory alloys (SMAs) are an emerging and interesting actuation scheme for affective haptic garments since they provide coupled warmth and compressive sensations in a single actuation---potentially acting as a proxy for human touch. However, SMAs are underutilized in current research and there are many unknowns regarding their design/use. The goal of this work is to map the design space for SMA-based garment-mediated emotional communication through warm, compressive actuation (termed 'warm touch'). Two online surveys were deployed to gather user expectations in using varying 'warm touch' parameters (body location, intensity, pattern) to communicate 7 distinct emotions. Further, we also investigated mental models used by participants during the haptic strategy selection process. The findings show 5 major categories of mental models, including representation of body sensations, replication of typical social touch strategies, metaphorical representation of emotions, symbolic representation of physical actions, and mimicry of objects or tasks; the frequency of use of each of these mental frameworks in relation to the selected 'warm touch' parameters in the communication of emotions are presented. These gathered insights can inform more intuitive and consistent haptic garment design approaches for emotional communication.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448097"}, {"primary_key": "1961654", "vector": [], "sparse_vector": [], "title": "Inverse Foraging: Inferring Users&apos; Interest in Pervasive Displays.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Users' engagement with pervasive displays has been extensively studied, however, determining how their content is interesting remains an open problem. Tracking of body postures and gaze has been explored as an indication of attention; still, existing works have not been able to estimate the interest of passers-by from readily available data, such as the display viewing time. This article presents a simple yet accurate method of estimating users' interest in multiple content items shown at the same time on displays. The proposed approach builds on the information foraging theory, which assumes that users optimally decide on the content they consume. Through inverse foraging, the parameters of a foraging model are fitted to the values of viewing times observed in practice, to yield estimates of user interest. Different foraging models are evaluated by using synthetic data and with a controlled user study. The results demonstrate that inverse foraging accurately estimates interest, achieving an R2 above 70% in comparison to self-reported interest. As a consequence, the proposed solution allows to dynamically adapt the content shown on pervasive displays, based on viewing data that can be easily obtained in field deployments.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478103"}, {"primary_key": "1961655", "vector": [], "sparse_vector": [], "title": "SmartKC: Smartphone-based Corneal Topographer for Keratoconus Detection.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Keratoconus is a severe eye disease affecting the cornea (the clear, dome-shaped outer surface of the eye), causing it to become thin and develop a conical bulge. The diagnosis of keratoconus requires sophisticated ophthalmic devices which are non-portable and very expensive. This makes early detection of keratoconus inaccessible to large populations in low-and middle-income countries, making it a leading cause for partial/complete blindness among such populations. We propose SmartKC, a low-cost, smartphone-based keratoconus diagnosis system comprising of a 3D-printed placido's disc attachment, an LED light strip, and an intelligent smartphone app to capture the reflection of the placido rings on the cornea. An image processing pipeline analyzes the corneal image and uses the smartphone's camera parameters, the placido rings' 3D location, the pixel location of the reflected placido rings and the setup's working distance to construct the corneal surface, via the Arc-Step method and Zernike polynomials based surface fitting. In a clinical study with 101 distinct eyes, we found that SmartKC achieves a sensitivity of 87.8% and a specificity of 80.4%. Moreover, the quantitative curvature estimates (sim-K) strongly correlate with a gold-standard medical device (Pearson correlation coefficient = 0.77). Our results indicate that SmartKC has the potential to be used as a keratoconus screening tool under real-world medical settings.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494982"}, {"primary_key": "1961657", "vector": [], "sparse_vector": [], "title": "Voice In Ear: Spoofing-Resistant and Passphrase-Independent Body Sound Authentication.", "authors": ["<PERSON>", "Yincheng Jin", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Li", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "With the rapid growth of wearable computing and increasing demand for mobile authentication scenarios, voiceprint-based authentication has become one of the prevalent technologies and has already presented tremendous potentials to the public. However, it is vulnerable to voice spoofing attacks (e.g., replay attacks and synthetic voice attacks). To address this threat, we propose a new biometric authentication approach, named EarPrint, which aims to extend voiceprint and build a hidden and secure user authentication scheme on earphones. EarPrint builds on the speaking-induced body sound transmission from the throat to the ear canal, i.e., different users will have different body sound conduction patterns on both sides of ears. As the first exploratory study, extensive experiments on 23 subjects show the EarPrint is robust against ambient noises and body motions. EarPrint achieves an Equal Error Rate (EER) of 3.64% with 75 seconds enrollment data. We also evaluate the resilience of EarPrint against replay attacks. A major contribution of EarPrint is that it leverages two-level uniqueness, including the body sound conduction from the throat to the ear canal and the body asymmetry between the left and the right ears, taking advantage of earphones' paring form-factor. Compared with other mobile and wearable biometric modalities, EarPrint is a low-cost, accurate, and secure authentication solution for earphone users.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448113"}, {"primary_key": "1961658", "vector": [], "sparse_vector": [], "title": "Towards Position-Independent Sensing for Gesture Recognition with Wi-Fi.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "Past decades have witnessed the extension of the Wi-Fi signals as a useful tool sensing human activities. One common assumption behind it is that there is a one-to-one mapping between human activities and Wi-Fi received signal patterns. However, this assumption does not hold when the user conducts activities in different locations and orientations. Actually, the received signal patterns of the same activity would become inconsistent when the relative location and orientation of the user with respect to transceivers change, leading to unstable sensing performance. This problem is known as the position-dependent problem, hindering the actual deployment of Wi-Fi-based sensing applications. In this paper, to tackle this fundamental problem, we develop a new position-independent sensing strategy and use gesture recognition as an application example to demonstrate its effectiveness. The key idea is to shift our observation from the traditional transceiver view to the hand-oriented view, and extract features that are irrespective of position-specific factors. Following the strategy, we design a position-independent feature, denoted as Motion Navigation Primitive(MNP). MNP captures the pattern of moving direction changes of the hand, which shares consistent patterns when the user performs the same gesture with different position-specific factors. By analyzing the pattern of MNP, we convert gestures into sequences of strokes (e.g, line, arc and corner) which makes them easy to be recognized. We build a prototype WiFi gesture recognition system, i.e., WiGesture to validate the effectiveness of the proposed strategy. Experiments show that our system can outperform the start-of-arts significantly in different settings. Given its novelty and superiority, we believe the proposed method symbolizes a major step towards gesture recognition and would inspire other solutions to position-independent activity recognition in the future.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463504"}, {"primary_key": "1961659", "vector": [], "sparse_vector": [], "title": "My(o) Armband Leaks Passwords: An EMG and IMU Based Keylogging Side-Channel Attack.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Wearables that constantly collect various sensor data of their users increase the chances for inferences of unintentional and sensitive information such as passwords typed on a physical keyboard. We take a thorough look at the potential of using electromyographic (EMG) data, a sensor modality which is new to the market but has lately gained attention in the context of wearables for augmented reality (AR), for a keylogging side-channel attack. Our approach is based on neural networks for a between-subject attack in a realistic scenario using the Myo Armband to collect the sensor data. In our approach, the EMG data has proven to be the most prominent source of information compared to the accelerometer and gyroscope, increasing the keystroke detection performance. For our end-to-end approach on raw data, we report a mean balanced accuracy of about 76 % for the keystroke detection and a mean top-3 key accuracy of about 32 % on 52 classes for the key identification on passwords of varying strengths. We have created an extensive dataset including more than 310 000 keystrokes recorded from 37 volunteers, which is available as open access along with the source code used to create the given results.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494986"}, {"primary_key": "1961660", "vector": [], "sparse_vector": [], "title": "Robust Inertial Motion Tracking through Deep Sensor Fusion across Smart Earbuds and Smartphone.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "IMU based inertial tracking plays an indispensable role in many mobility centric tasks, such as robotic control, indoor navigation and virtual reality gaming. Despite its mature application in rigid machine mobility (e.g., robot and aircraft), tracking human users via mobile devices remains a fundamental challenge due to the intractable gait/posture patterns. Recent data-driven models have tackled sensor drifting, one key issue that plagues inertial tracking. However, these systems still assume the devices are held or attached to the user body with a relatively fixed posture. In practice, natural body activities may rotate/translate the device which may be mistaken as whole body movement. Such motion artifacts remain as the dominating factor that fails existing inertial tracing systems in practical uncontrolled settings. Inspired by the observation that human heads induces far less intensive movement relative to the body during walking, compared to other parts, we propose a novel multi-stage sensor fusion pipeline called DeepIT, which realizes inertial tracking by synthesizing the IMU measurements from a smartphone and an associated earbud. DeepIT introduces a data-driven reliability aware attention model, which assesses the reliability of each IMU and opportunistically synthesizes their data to mitigate the impacts of motion noise. Furthermore, DeepIT uses a reliability aware magnetometer compensation scheme to combat the angular drifting problem caused by unrestricted motion artifacts. We validate DeepIT on the first large-scale inertial navigation dataset involving both smartphone and earbud IMUs. The evaluation results show that DeepIT achieves multiple folds of accuracy improvement on the challenging uncontrolled natural walking scenarios, compared with state-of-the-art closed-form and data-driven models.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463517"}, {"primary_key": "1961661", "vector": [], "sparse_vector": [], "title": "RF Vital Sign Sensing under Free Body Movement.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Radio frequency (RF) sensors such as radar are instrumental for continuous, contactless sensing of vital signs, especially heart rate (HR) and respiration rate (RR). However, decades of related research mainly focused on static subjects, because the motion artifacts from other body parts may easily overwhelm the weak reflections from vital signs. This paper marks a first step in enabling RF vital sign sensing under ambulant daily living conditions. Our solution is inspired by existing physiological research that revealed the correlation between vital signs and body movement. Specifically, we propose to combine direct RF sensing for static instances and indirect vital sign prediction based on movement power estimation. We design customized machine learning models to capture the sophisticated correlation between RF signal pattern, movement power, and vital signs. We further design an instant calibration and adaptive training scheme to enable cross-subjects generalization, without any explicit data labeling from unknown subjects. We prototype and evaluate the framework using a commodity radar sensor. Under a variety of moving conditions, our solution demonstrates an average estimation error of 5.57 bpm for HR and 3.32 bpm for RR across multiple subjects, which largely outperforms state-of-the-art systems.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478090"}, {"primary_key": "1961662", "vector": [], "sparse_vector": [], "title": "Toward User-Driven Sound Recognizer Personalization with People Who Are d/Deaf or Hard of Hearing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Automated sound recognition tools can be a useful complement to d/Deaf and hard of hearing (DHH) people's typical communication and environmental awareness strategies. Pre-trained sound recognition models, however, may not meet the diverse needs of individual DHH users. While approaches from human-centered machine learning can enable non-expert users to build their own automated systems, end-user ML solutions that augment human sensory abilities present a unique challenge for users who have sensory disabilities: how can a DHH user, who has difficulty hearing a sound themselves, effectively record samples to train an ML system to recognize that sound? To better understand how DHH users can drive personalization of their own assistive sound recognition tools, we conducted a three-part study with 14 DHH participants: (1) an initial interview and demo of a personalizable sound recognizer, (2) a week-long field study of in situ recording, and (3) a follow-up interview and ideation session. Our results highlight a positive subjective experience when recording and interpreting training data in situ, but we uncover several key pitfalls unique to DHH users---such as inhibited judgement of representative samples due to limited audiological experience. We share implications of these results for the design of recording interfaces and human-the-the-loop systems that can support DHH users to build sound recognizers for their personal needs.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463501"}, {"primary_key": "1961664", "vector": [], "sparse_vector": [], "title": "PREFER: Point-of-interest REcommendation with efficiency and privacy-preservation via Federated Edge leaRning.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Point-of-Interest (POI) recommendation is significant in location-based social networks to help users discover new locations of interest. Previous studies on such recommendation mainly adopted a centralized learning framework where check-in data were uploaded, trained and predicted centrally in the cloud. However, such a framework suffers from privacy risks caused by check-in data exposure and fails to meet real-time recommendation needs when the data volume is huge and communication is blocked in crowded places. In this paper, we propose PREFER, an edge-accelerated federated learning framework for POI recommendation. It decouples the recommendation into two parts. Firstly, to protect privacy, users train local recommendation models and share multi-dimensional user-independent parameters instead of check-in data. Secondly, to improve recommendation efficiency, we aggregate these distributed parameters on edge servers in proximity to users (such as base stations) instead of remote cloud servers. We implement the PREFER prototype and evaluate its performance using two real-world datasets and two POI recommendation models. Extensive experiments demonstrate that PREFER strengthens privacy protection and improves efficiency with little sacrifice to recommendation quality compared to centralized learning. It achieves the best quality and efficiency and is more compatible with increasingly sophisticated POI recommendation models compared to other state-of-the-art privacy-preserving baselines.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448099"}, {"primary_key": "1961667", "vector": [], "sparse_vector": [], "title": "WiStress: Contactless Stress Monitoring Using Wireless Signals.", "authors": ["Unsoo Ha", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Stress plays a critical role in our lives, impacting our productivity and our long-term physiological and psychological well-being. This has motivated the development of stress monitoring solutions to better understand stress, its impact on productivity and teamwork, and help users adapt their habits toward more sustainable stress levels. However, today's stress monitoring solutions remain obtrusive, requiring active user participation (e.g., self-reporting), interfering with people's daily activities, and often adding more burden to users looking to reduce their stress. In this paper, we introduce WiStress, the first system that can passively monitor a user's stress levels by relying on wireless signals. WiStress does not require users to actively provide input or to wear any devices on their bodies. It operates by transmitting ultra-low-power wireless signals and measuring their reflections off the user's body. WiStress introduces two key innovations. First, it presents the first machine learning network that can accurately and robustly extract heartbeat intervals (IBI's) from wireless reflections without constraints on a user's daily activities. Second, it introduces a stress classification framework that combines the extracted heartbeats with other wirelessly captured stress-related features in order to infer a subject's stress level. We built a prototype of WiStress and tested it on 22 different subjects across different environments in both stress-induced and free-living conditions. Our results demonstrate that <PERSON>iStress has high accuracy (84%-95%) in inferring a person's stress level in a fully-automated way, paving the way for ubiquitous sensing systems that can monitor stress and provide feedback to improve productivity, health, and well-being.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478121"}, {"primary_key": "1961668", "vector": [], "sparse_vector": [], "title": "Who Will Survive and Revive Undergoing the Epidemic: Analyses about POI Visit Behavior in Wuhan via Check-in Records.", "authors": ["Zhenyu Han", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Pan Hui", "<PERSON>"], "summary": "A rapid-spreading epidemic of COVID-19 hit China at the end of 2019, resulting in unignorable social and economic damage in the epicenter, Wuhan. POIs capture the microscopic behavior of citizens, providing valuable information to understand city reactions toward the epidemic. Leveraging large-scale check-in records, we analyze the POI visit trends over the epidemic period and normal times. We demonstrate that COVID-19 greatly influences the society, where most POIs demonstrate more than 60% of visit drops during the city lockdown period. Among them, Tourist Attractions received greatest impact with a 78.8% drop. Entertainment, Food, Medical and Shopping are sensible to the disease before lockdown, and we identify these \"early birds\" to investigate the public reaction in the early stage of the epidemic. We further analyze the revival trends, generating four different revival patterns that correlated with the necessity of POI functions. Finally, we analyze the perseverance during the COVID-19, finding no large-scale closures compared with the tremendous visit drop. The strong resilience in Wuhan supports the rapid recovery of society. These findings are important for researchers, industries, and governments to understand the city respondence under severe epidemic, proposing better regulations to respond, control, and prevent public emergencies.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463525"}, {"primary_key": "1961669", "vector": [], "sparse_vector": [], "title": "Contrastive Predictive Coding for Human Activity Recognition.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Feature extraction is crucial for human activity recognition (HAR) using body-worn movement sensors. Recently, learned representations have been used successfully, offering promising alternatives to manually engineered features. Our work focuses on effective use of small amounts of labeled data and the opportunistic exploitation of unlabeled data that are straightforward to collect in mobile and ubiquitous computing scenarios. We hypothesize and demonstrate that explicitly considering the temporality of sensor data at representation level plays an important role for effective HAR in challenging scenarios. We introduce the Contrastive Predictive Coding (CPC) framework to human activity recognition, which captures the temporal structure of sensor data streams. Through a range of experimental evaluations on real-life recognition tasks, we demonstrate its effectiveness for improved HAR. CPC-based pre-training is self-supervised, and the resulting learned representations can be integrated into standard activity chains. It leads to significantly improved recognition performance when only small amounts of labeled training data are available, thereby demonstrating the practical value of our approach. Through a series of experiments, we also develop guidelines to help practitioners adapt and modify the framework towards other mobile and ubiquitous computing scenarios.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463506"}, {"primary_key": "1961670", "vector": [], "sparse_vector": [], "title": "ModElec: A Design Tool for Prototyping Physical Computing Devices Using Conductive 3D Printing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> <PERSON>", "<PERSON>", "Rafael T<PERSON>"], "summary": "Integrating electronics with highly custom 3D designs for the physical fabrication of interactive prototypes is traditionally cumbersome and requires numerous iterations of manual assembly and debugging. With the new capabilities of 3D printers, combining electronic design and 3D modeling workflows can lower the barrier for achieving interactive functionality or iterating on the overall design. We present ModElec---an interactive design tool that enables the coordinated expression of electronic and physical design intent by allowing designers to integrate 3D-printable circuits with 3D forms. With ModElec, the user can arrange electronic parts in a 3D body, modify the model design with embedded circuits updated, and preview the auto-generated 3D traces that can be directly printed with a multi-material-based 3D printer. We demonstrate the potential of ModElec with four example applications, from a set of game controls to reconfigurable devices. Further, the tool was reported as easy to use through a preliminary evaluation with eight designers.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3495000"}, {"primary_key": "1961671", "vector": [], "sparse_vector": [], "title": "To See or Not to See: Exploring Inattentional Blindness for the Design of Unobtrusive Interfaces in Shared Public Places.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "People visit public places with different intentions and motivations. While some explore it carefully, others may just want to pass or are otherwise engaged. We investigate how to exploit the inattentional blindness (IB) of indirect users in the design of public interfaces to apply to such diverse needs. Beginning with a structured literature study in the ACM Digital Library on IB, we analyzed 135 publications to derive design strategies that benefit from IB or avoid IB. Using these findings, we selected three existing interfaces for information presentation on a large public square and created two additional interfaces ourselves. We then compared users' perceptions through a self-reported photography study (N = 40). Participants followed one of four scripted profiles to imitate different user intentions, two for direct and two for indirect users. We hypothesized that direct users would recognize the interfaces, while indirect users would experience IB and ignore them. Our results show that direct users reported up to 68% of our interfaces, whereas indirect users noticed only 16%. Thus, IB can be exploited to hide interfaces from indirect users while keeping them noticeable to direct users.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448123"}, {"primary_key": "1961672", "vector": [], "sparse_vector": [], "title": "Unobtrusive Pedestrian Identification by Leveraging Footstep Sounds with Replay Resistance.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The ability to identify pedestrians unobtrusively is essential for smart buildings to provide customized environments, energy saving, health monitoring and security-enhanced services. In this paper, we present an unobtrusive pedestrian identification system by passively listening to people's walking sounds. The proposed acoustic system can be easily integrated with the widely deployed voice assistant devices while providing the context awareness ability. This work focuses on two major tasks. Firstly, we address the challenge of recognizing footstep sounds in complex indoor scenarios by exploiting deep learning and the advanced stereo recording technology that is available on most voice assistant devices. We develop a Convolutional Neural Network-based algorithm and the footstep sound-oriented signal processing schemes to identify users by their footstep sounds accurately. Secondly, we design a \"live\" footstep detection approach to defend against replay attacks. By deriving the novel inter-footstep and intra-footstep characteristics, we distinguish live footstep sounds from the machine speaker's replay sounds based on their spatial variances. The system is evaluated under normal scenarios, traditional replay attacks and the advanced replays, which are designed to forge footstep sounds both acoustically and spatially. Extensive experiments show that our system identifies people with up to 94.9% accuracy in one footstep and shields 100% traditional replay attacks and up to 99% advanced replay attacks.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494963"}, {"primary_key": "1961673", "vector": [], "sparse_vector": [], "title": "Multi-Head Spatio-Temporal Attention Mechanism for Urban Anomaly Event Prediction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Suining He"], "summary": "Timely forecasting the urban anomaly events in advance is of great importance to the city management and planning. However, anomaly event prediction is highly challenging due to the sparseness of data, geographic heterogeneity (e.g., complex spatial correlation, skewed spatial distribution of anomaly events and crowd flows), and the dynamic temporal dependencies. In this study, we propose M-STAP, a novel Multi-head Spatio-Temporal Attention Prediction approach to address the problem of multi-region urban anomaly event prediction. Specifically, M-STAP considers the problem from three main aspects: (1) extracting the spatial characteristics of the anomaly events in different regions, and the spatial correlations between anomaly events and crowd flows; (2) modeling the impacts of crowd flow dynamic of the most relevant regions in each time step on the anomaly events; and (3) employing attention mechanism to analyze the varying impacts of the historical anomaly events on the predicted data. We have conducted extensive experimental studies on the crowd flows and anomaly events data of New York City, Melbourne and Chicago. Our proposed model shows higher accuracy (41.91% improvement on average) in predicting multi-region anomaly events compared with the state-of-the-arts.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478099"}, {"primary_key": "1961675", "vector": [], "sparse_vector": [], "title": "BreathTrack: Detecting Regular Breathing Phases from Unannotated Acoustic Data Captured by a Smartphone.", "authors": ["Bashima Islam", "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Breathing biomarkers, such as breathing rate, fractional inspiratory time, and inhalation-exhalation ratio, are vital for monitoring the user's health and well-being. Accurate estimation of such biomarkers requires breathing phase detection, i.e., inhalation and exhalation. However, traditional breathing phase monitoring relies on uncomfortable equipment, e.g., chestbands. Smartphone acoustic sensors have shown promising results for passive breathing monitoring during sleep or guided breathing. However, detecting breathing phases using acoustic data can be challenging for various reasons. One of the major obstacles is the complexity of annotating breathing sounds due to inaudible parts in regular breathing and background noises. This paper assesses the potential of using smartphone acoustic sensors for passive unguided breathing phase monitoring in a natural environment. We address the annotation challenges by developing a novel variant of the teacher-student training method for transferring knowledge from an inertial sensor to an acoustic sensor, eliminating the need for manual breathing sound annotation by fusing signal processing with deep learning techniques. We train and evaluate our model on the breathing data collected from 131 subjects, including healthy individuals and respiratory patients. Experimental results show that our model can detect breathing phases with 77.33% accuracy using acoustic sensors. We further present an example use-case of breathing phase-detection by first estimating the biomarkers from the estimated breathing phases and then using these biomarkers for pulmonary patient detection. Using the detected breathing phases, we can estimate fractional inspiratory time with 92.08% accuracy, the inhalation-exhalation ratio with 86.76% accuracy, and the breathing rate with 91.74% accuracy. Moreover, we can distinguish respiratory patients from healthy individuals with up to 76% accuracy. This paper is the first to show the feasibility of detecting regular breathing phases towards passively monitoring respiratory health and well-being using acoustic data captured by a smartphone.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478123"}, {"primary_key": "1961676", "vector": [], "sparse_vector": [], "title": "Self-supervised Learning for Reading Activity Classification.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Reading analysis can relay information about user's confidence and habits and can be used to construct useful feedback. A lack of labeled data inhibits the effective application of fully-supervised Deep Learning (DL) for automatic reading analysis. We propose a Self-supervised Learning (SSL) method for reading analysis. Previously, SSL has been effective in physical human activity recognition (HAR) tasks, but it has not been applied to cognitive HAR tasks like reading. We first evaluate the proposed method on a four-class classification task on reading detection using electrooculography datasets, followed by an evaluation of a two-class classification task of confidence estimation on multiple-choice questions using eye-tracking datasets. Fully-supervised DL and support vector machines (SVMs) are used as comparisons for the proposed SSL method. The results show that the proposed SSL method is superior to the fully-supervised DL and SVM for both tasks, especially when training data is scarce. This result indicates the proposed method is the superior choice for reading analysis tasks. These results are important for informing the design of automatic reading analysis platforms.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478088"}, {"primary_key": "1961677", "vector": [], "sparse_vector": [], "title": "Janus: Dual-radio Accurate and Energy-Efficient Proximity Detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Proximity detection is at the core of several mobile and ubiquitous computing applications. These include reactive use cases, e.g., alerting individuals of hazards or interaction opportunities, and others concerned only with logging proximity data, e.g., for offline analysis and modeling. Common approaches rely on Bluetooth Low Energy (BLE) or ultra-wideband (UWB) radios. Nevertheless, these strike opposite tradeoffs between the accuracy of distance estimates quantifying proximity and the energy efficiency affecting system lifetime, effectively forcing a choice between the two and ultimately constraining applicability. <PERSON><PERSON> reconciles these dimensions in a dual-radio protocol enabling accurate and energy-efficient proximity detection, where the energy-savvy BLE is exploited to discover devices and coordinate their distance measurements, acquired via the energy-hungry UWB. A model supports domain experts in configuring <PERSON><PERSON> for their use cases with predictable performance. The latency, reliability, and accuracy of <PERSON><PERSON> are evaluated experimentally, including realistic scenarios endowed with the mm-level ground truth provided by a motion capture system. Energy measurements show that <PERSON><PERSON> achieves weeks to months of autonomous operation, depending on the use case configuration. Finally, several large-scale campaigns exemplify its practical usefulness in real-world contexts.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494978"}, {"primary_key": "1961679", "vector": [], "sparse_vector": [], "title": "Douleur: Creating Pain Sensation with Chemical Stimulant to Enhance User Experience in Virtual Reality.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Fan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The imitation of pain sensation in Virtual Reality is considered valuable for safety education and training but has been seldom studied. This paper presents Douleur, a wearable haptic device that renders intensity-adjustable pain sensations with chemical stimulants. Different from mechanical, thermal, or electric stimulation, chemical-induced pain is more close to burning sensations and long-lasting. Douleur consists of a microfluidic platform that precisely emits capsaicin onto the skin and a microneedling component to help the stimulant penetrate the epidermis layer to activate the trigeminal nerve efficiently. Moreover, it embeds a Peltier module to apply the heating or cooling stimulus to the affected area to adjust the level of pain on the skin. To better understand how people would react to the chemical stimulant, we conducted a first study to quantify the enhancement of the sensation by changing the capsaicin concentration, skin temperature, and time and to determine suitable capsaicin concentration levels. In the second study, we demonstrated that <PERSON>uleur could render a variety of pain sensations in corresponding virtual reality applications. In sum, <PERSON><PERSON>ur is the first wearable prototype that leverages a combination of capsaicin and Peltier to induce rich pain sensations and opens up a wide range of applications for safety education and more.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463527"}, {"primary_key": "1961680", "vector": [], "sparse_vector": [], "title": "ALWAES: an Automatic Outdoor Location-Aware Correction System for Online Delivery Platforms.", "authors": ["Dongz<PERSON> Jiang", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "For an online delivery platform, accurate physical locations of merchants are essential for delivery scheduling. It is challenging to maintain tens of thousands of merchant locations accurately because of potential errors introduced by merchants for profits (e.g., potential fraud). In practice, a platform periodically sends a dedicated crew to survey limited locations due to high workforce costs, leaving many potential location errors. In this paper, we design and implement ALWAES, a system that automatically identifies and corrects location errors based on fundamental tradeoffs of five measurement strategies from manual, physical, and virtual data collection infrastructures for online delivery platforms. ALWAES explores delivery data already collected by platform infrastructures to measure the travel time of couriers between merchants and verify all merchants' locations by cross-validation automatically. We explore tradeoffs between performance and cost of different measurement approaches. By comparing with the manually-collected ground truth, the experimental results show that ALWAES outperforms three other baselines by 32.2%, 41.8%, and 47.2%, respectively. More importantly, ALWAES saves 3,846 hours of the delivery time of 35,005 orders in a month and finds new erroneous locations that initially were not in the ground truth but are verified by our field study later, accounting for 3% of all merchants with erroneous locations.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478081"}, {"primary_key": "1961681", "vector": [], "sparse_vector": [], "title": "DriverSonar: Fine-Grained Dangerous Driving Detection Using Active Sonar.", "authors": ["Hongbo Jiang", "Jingyang Hu", "Dai<PERSON> Liu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Dangerous driving due to drowsiness and distraction is the main cause of traffic accidents, resulting in casualties and economic loss. There is an urgent need to address this problem by accurately detecting dangerous driving behaviors and generating real-time alerts. Inspired by the observation that dangerous driving actions induce unique acoustic features that respond to the signal of an acoustic source, we present the DriverSonar system in this paper. The proposed system detects dangerous driving actions and generates real-time alarms using off-the-shelf smartphones. Compared with the state-of-the-arts, the DriverSonar system does not require dedicated sensors but just uses the built-in speaker and microphone in a smartphone. Specifically, DriverSonar is able to recognize head/hand motions such as nodding, yawning, and abrupt adjustment of the steering wheel. We design, implement and evaluate DriverSonar with extensive experiments. We conduct both simulator-based and and real driving-based experiments (IRB-approved) with 30 volunteers for a period over 12 months. Experiment results show that the proposed system can detect drowsy and distraction related dangerous driving actions at an precision up to 93.2% and a low false acceptance rate of 3.6%.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478084"}, {"primary_key": "1961682", "vector": [], "sparse_vector": [], "title": "SonicASL: An Acoustic-based Sign Language Gesture Recognizer Using Earphones.", "authors": ["Yincheng Jin", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose SonicASL, a real-time gesture recognition system that can recognize sign language gestures on the fly, leveraging front-facing microphones and speakers added to commodity earphones worn by someone facing the person making the gestures. In a user study (N=8), we evaluate the recognition performance of various sign language gestures at both the word and sentence levels. Given 42 frequently used individual words and 30 meaningful sentences, SonicASL can achieve an accuracy of 93.8% and 90.6% for word-level and sentence-level recognition, respectively. The proposed system is tested in two real-world scenarios: indoor (apartment, office, and corridor) and outdoor (sidewalk) environments with pedestrians walking nearby. The results show that our system can provide users with an effective gesture recognition tool with high reliability against environmental factors such as ambient noises and nearby pedestrians.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463519"}, {"primary_key": "1961683", "vector": [], "sparse_vector": [], "title": "CycleGuard: A Smartphone-based Assistive Tool for Cyclist Safety Using Acoustic Ranging.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Every year 41,000 cyclists die in road traffic-related incidents worldwide [47]. One of the most startling and infuriating conflicts that cyclists experience is the so-called \"right hook\". It refers to a vehicle striking a cyclist heading in the same direction by turning right into the cyclist. To prevent such a crash, this work presents CycleGuard, an acoustic-based collision detection system using smartphones. It is composed of a cheap commercial off-the-shelf (COTS) portable speaker that emits imperceptible high-frequency acoustic signals and a smartphone for reflected signal reception and analysis. Since received acoustic signals bear rich information of their reflecting objects, CycleGuard applies advanced acoustic ranging techniques to extract those information for traffic analysis. Cyclists are alerted if any pending right hook crashes are detected. Real-time alerts ensure that cyclists have sufficient time to react, apply brakes, and eventually avoid the hazard. To validate the efficacy of CycleGuard, we implement a proof-of-concept prototype and carry out extensive in-field experiments under a broad spectrum of settings. Results show that CycleGuard achieves up to 95% accuracy in preventing right hook crashes and is robust to various scenarios. It is also energy-friendly to run on battery-powered smartphones.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494992"}, {"primary_key": "1961685", "vector": [], "sparse_vector": [], "title": "Determinants of Longitudinal Adherence in Smartphone-Based Self-Tracking for Chronic Health Conditions: Evidence from Axial Spondyloarthritis.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The use of interactive mobile and wearable technologies for understanding and managing health conditions is a growing area of interest for patients, health professionals and researchers. Self-tracking technologies such as smartphone apps and wearable devices for measuring symptoms and behaviours generate a wealth of patient-centric data with the potential to support clinical decision making. However, the utility of self-tracking technologies for providing insight into patients' conditions is impacted by poor adherence with data logging. This paper explores factors associated with adherence in smartphone-based tracking, drawing on two studies of patients living with axial spondyloarthritis (axSpA), a chronic rheumatological condition. In Study 1, 184 axSpA patients used the uMotif health tracking smartphone app for a period of up to 593 days. In Study 2, 108 axSpA patients completed a survey about their experience of using self-tracking technologies. We identify six significant correlates of self-tracking adherence, providing insight into the determinants of tracking behaviour. Specifically, our data provides evidence that adherence correlates with the age of the user, the types of tracking devices that are being used (smartphone OS and physical activity tracker), preferences for types of data to record, the timing of interactions with a self-tracking app, and the reported symptom severity of the user. We discuss how these factors may have implications for those designing, deploying or using mobile and wearable tracking technologies to support monitoring and management of chronic diseases.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448093"}, {"primary_key": "1961686", "vector": [], "sparse_vector": [], "title": "LAX-Score: Quantifying Team Performance in Lacrosse and Exploring IMU Features towards Performance Enhancement.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Liu", "<PERSON>"], "summary": "For the past several decades, machine learning has played an important role in sports science with regard to player performance and result prediction. However, it is still challenging to quantify team-level game performance because there is no strong ground truth. Thus, a team cannot receive feedback in a standardized way. The aim of this study was twofold. First, we designed a metric called LAX-Score to quantify a collegiate lacrosse team's athletic performance. Next, we explored the relationship between our proposed metric and practice sensing features for performance enhancement. To derive the metric, we utilized feature selection and weighted regression. Then, the proposed metric was statistically validated on over 700 games from the last three seasons of NCAA Division I women's lacrosse. We also explored our biometric sensing dataset obtained from a collegiate team's athletes over the course of a season. We then identified the practice features that are most correlated with high-performance games. Our results indicate that LAX-Score provides insight into athletic performance beyond wins and losses. Moreover, though COVID-19 has stalled implementation, the collegiate team studied applied our feature outcomes to their practices, and the initial results look promising with regard to better performance.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478076"}, {"primary_key": "1961687", "vector": [], "sparse_vector": [], "title": "FaceSense: Sensing Face Touch with an Ear-worn System.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Face touch is an unconscious human habit. Frequent touching of sensitive/mucosal facial zones (eyes, nose, and mouth) increases health risks by passing pathogens into the body and spreading diseases. Furthermore, accurate monitoring of face touch is critical for behavioral intervention. Existing monitoring systems only capture objects approaching the face, rather than detecting actual touches. As such, these systems are prone to false positives upon hand or object movement in proximity to one's face (e.g., picking up a phone). We present FaceSense, an ear-worn system capable of identifying actual touches and differentiating them between sensitive/mucosal areas from other facial areas. Following a multimodal approach, FaceSense integrates low-resolution thermal images and physiological signals. Thermal sensors sense the thermal infrared signal emitted by an approaching hand, while physiological sensors monitor impedance changes caused by skin deformation during a touch. Processed thermal and physiological signals are fed into a deep learning model (TouchNet) to detect touches and identify the facial zone of the touch. We fabricated prototypes using off-the-shelf hardware and conducted experiments with 14 participants while they perform various daily activities (e.g., drinking, talking). Results show a macro-F1-score of 83.4% for touch detection with leave-one-user-out cross-validation and a macro-F1-score of 90.1% for touch zone identification with a personalized model.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478129"}, {"primary_key": "1961688", "vector": [], "sparse_vector": [], "title": "EDEN: Enforcing Location Privacy through Re-identification Risk Assessment: A Federated Learning Approach.", "authors": ["<PERSON><PERSON> Khalfoun", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Crowd sensing applications have demonstrated their usefulness in many real-life scenarios (e.g., air quality monitoring, traffic and noise monitoring). Preserving the privacy of crowd sensing app users is becoming increasingly important as the collected geo-located data may reveal sensitive information about these users (e.g., home, work places, political, religious, sexual preferences). In this context, a large variety of Location Privacy Protection Mechanisms (LPPMs) have been proposed. However, each LPPM comes with a given set of configuration parameters. The value of these parameters impacts not only the privacy level but also the utility of the resulting data. Choosing the right LPPM and the right configuration for reaching a satisfactory privacy vs. utility tradeoff is generally a difficult problem mobile app developers have to face. Solving this problem is commonly done by relying on a trusted proxy server to which raw geo-located traces are sent and privacy vs. utility assessment is performed enabling the selection of the best LPPM for each trace. In this paper we present EDEN, the first solution that selects automatically the best LPPM and its corresponding configuration without sending raw geo-located traces outside the user's device. We reach this objective by relying on a federated learning approach. The evaluation of EDEN on five real-world mobility datasets shows that EDEN outperforms state-of-the-art LPPMs reaching a better privacy vs. utility tradeoff.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463502"}, {"primary_key": "1961689", "vector": [], "sparse_vector": [], "title": "Ray Tracing-based Light Energy Prediction for Indoor Batteryless Sensors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Light energy harvesting is a valuable technique for batteryless sensors located indoors. A key challenge is finding the right locations to deploy sensors to provide sufficient harvesting capability. A trial-and-error approach or energy prediction method is used as the solution, but existing schemes are either time-consuming or employing a naïve prediction mechanism primarily developed for outdoor environments. In this paper, we propose a light energy prediction technique, called Solacle, which accounts for various factors in indoor light harvesting to provide accuracy at any given location. Exploiting the ray tracing technique, Solacle estimates the illuminance and the luminous efficacy of light sources to predict the harvesting capability, by considering the spatiotemporal characteristics of the surrounding environment. To this end, we defined the optical properties of a space, and devised an optimization approach, specifically a gradient-free-based scheme, to acquire adequate values for the combination of optical properties. We implemented the system and evaluated its efficacy in controlled and real environments. The experiment results show that the proposed approach delivers a significant improvement over previous work in light energy prediction of indoor space.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448086"}, {"primary_key": "1961690", "vector": [], "sparse_vector": [], "title": "Beneficial Neglect: Instant Message Notification Handling Behaviors and Academic Performance.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Smartphone notifications inform users of events to prompt timely engagement. However, there are growing concerns about messaging behaviors in schools because frequent in-class checking and responding may hinder students' learning. This study analyzed a large-scale smartphone usage dataset collected from 81 first-year college students for 14 weeks. We quantified notification arrival and checking patterns in classrooms and analyzed how notification handling behaviors were correlated with students' smartphone usage and academic performance. Our results showed that receiving messages and frequent checking occur during class. Frequent checking behaviors were positively related to longer app usage, while willful neglect of incoming messages was positively related to overall academic performance. Our work demonstrates that problematic behaviors exist related to instant messaging in learning contexts and recommends further study to devise effective intervention mechanisms.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448089"}, {"primary_key": "1961692", "vector": [], "sparse_vector": [], "title": "DronePrint: Acoustic Signatures for Open-set Drone Detection and Identification with Online Data.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Suranga Seneviratne", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the ubiquitous availability of drones, they are adopted benignly in multiple applications such as cinematography, surveying, and legal goods delivery. Nonetheless, they are also being used for reconnaissance, invading personal or secure spaces, harming targeted individuals, smuggling drugs and contraband, or creating public disturbances. These malicious or improper use of drones can pose significant privacy and security threats in both civilian and military settings. Therefore, it is vital to identify drones in different environments to assist the decisions on whether or not to contain unknown drones. While there are several methods proposed for detecting the presence of a drone, they have limitations when it comes to low visibility, limited access, or hostile environments. In this paper, we propose DronePrint that uses drone acoustic signatures to detect the presence of a drone and identify the make and the model of the drone. We address the shortage of drone acoustic data by relying on audio components of online videos. In drone detection, we achieved 96% accuracy in a closed-set scenario, and 86% accuracy in a more challenging open-set scenario. Our proposed method of cascaded drone identification, where a drone is identified for its 'make' followed by the 'model' of the drone achieves 90% overall accuracy. In this work, we cover 13 commonly used commercial and consumer drone models, which is to the best of understanding is the most comprehensive such study to date. Finally, we demonstrate the robustness of DronePrint to drone hardware modifications, Doppler effect, varying SNR conditions, and in realistic open-set acoustic scenes.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448115"}, {"primary_key": "1961693", "vector": [], "sparse_vector": [], "title": "SkinKit: Construction Kit for On-Skin Interface Prototyping.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The emergence of on-skin interfaces has created an opportunity for seamless, always-available on-body interactions. However, developing a new fabrication process for on-skin interfaces can be time-consuming, challenging to incorporate new features, and not available for quick form-factor preview through prototyping. We introduce SkinKit, the first construction toolkit for on-skin interfaces, which enables fast, low-fidelity prototyping with a slim form factor directly applicable to the skin. SkinKit comprises modules consisting of skin-conformable base substrates and reusable Flexible Printed Circuits Board (FPCB) blocks. They are easy to attach and remove under tangible plug-and-play construction but still offer robust conductive connections in a slim form. Further, SkinKit aims to lower the barrier to entry in building on-skin interfaces without demanding technical expertise. It leverages a variety of preprogrammed modules connected in unique sequences to achieve various function customizations. We describe our iterative design and development process of SkinKit, comparing materials, connection mechanisms, and modules reflecting on its capability. We report results from single- and multi- session workshops with 34 maker participants spanning STEM and design backgrounds. Our findings reveal how diverse maker populations engage in on-skin interface design, what types of applications they choose to build, and what challenges they faced.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494989"}, {"primary_key": "1961695", "vector": [], "sparse_vector": [], "title": "Approaching the Real-World: Supporting Activity Recognition Training with Virtual IMU Data.", "authors": ["HyeokHyen <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recently, IMUTube introduced a paradigm change for bootstrapping human activity recognition (HAR) systems for wearables. The key idea is to utilize videos of activities to support training activity recognizers based on inertial measurement units (IMUs). This system retrieves video from public repositories and subsequently generates virtual IMU data from this. The ultimate vision for such a system is to make large amounts of weakly labeled videos accessible for model training in HAR and, as such, to overcome one of the most pressing issues in the field: the lack of significant amounts of labeled sample data. In this paper we present the first in-detail exploration of IMUTube in a realistic assessment scenario: the analysis of free-weight gym exercises. We make significant progress towards a flexible, fully-functional IMUTube system by extending it such that it can handle a range of artifacts that are common in unrestricted online videos, including various forms of video noise, non-human poses, body part occlusions, and extreme camera and human motion. By overcoming these real-world challenges, we are able to generate high-quality virtual IMU data, which allows us to employ IMUTube for practical analysis tasks. We show that HAR systems trained by incorporating virtual sensor data generated by IMUTube significantly outperform baseline models trained only with real IMU data. In doing so we demonstrate the practical utility of IMUTube and the progress made towards the final vision of the new bootstrapping paradigm.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478096"}, {"primary_key": "1961697", "vector": [], "sparse_vector": [], "title": "AmbientBreath: Unobtrusive Just-in-time Breathing Intervention Using Multi-sensory Stimulation and its Evaluation in a Car Simulator.", "authors": ["<PERSON><PERSON>", "Neska El Haouij", "<PERSON><PERSON>"], "summary": "To promote calm breathing inside a car, we designed a just-in-time breathing intervention stimulated by multi-sensory feedback and evaluated its efficacy in a driving simulator. Efficacy was measured via reduction in breathing rate as well as by user acceptance and driving safety measures. Drivers were first exposed to demonstrations of three kinds of ambient feedback designed to stimulate a goal breathing rate: (1) auditory (rhythmic background noise), (2) synchronized modulation of wind (dashboard fans modulating air pointed toward the driver) together with auditory, or (3) synchronized visual (ambient lights) together with auditory. After choosing one preference from these three, each driver engaged in a challenging driving task in a car simulator, where the ambient stimulation was triggered when their breathing exceeded a goal rate adapted to their personal baseline. Two user studies were conducted in a car simulator involving respectively 23 and 31 participants. The studies include both manual and autonomous driving scenarios to evaluate drivers' engagement in the intervention under different cognitive loads. The most frequently selected stimulation was the combined auditory and wind modalities. Measures of changes in breathing rate show that the participants were able to successfully engage in the breathing intervention; however, several factors from the driving context appear to have an impact on when the intervention is or is not effective.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463493"}, {"primary_key": "1961698", "vector": [], "sparse_vector": [], "title": "Understanding and Supporting Self-Tracking App Selection.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "People often face barriers to selecting self-tracking tools that support their goals and needs, resulting in tools not meeting their expectations and ultimately abandonment. We therefore examine how people approach selecting self-tracking apps and investigate how technology can better support the process. Drawing on past literature on how people select and perceive the features of commercial and research tracking tools, we surface seven attributes people consider during selection, and design a low-fidelity prototype of an app store that highlights these attributes. We then conduct semi-structured interviews with 18 participants to further investigate what people consider during selection, how people select self-tracking apps, and how surfacing tracking-related attributes could better support selection. We find that people often prioritize features related to self-tracking during selection, such as approaches to collecting and reflecting on data, and trial apps to determine whether they would suit their needs. Our results also show potential for technology surfacing how apps support tracking to reduce barriers to selection. We discuss future opportunities for improving self-tracking app selection, such as ways to enhance existing self-tracking app distribution platforms to enable people to filter and search apps by desirable features.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494980"}, {"primary_key": "1961699", "vector": [], "sparse_vector": [], "title": "CrossGR: Accurate and Low-cost Cross-target Gesture Recognition Using Wi-Fi.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Xiao<PERSON> Chen", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper focuses on a fundamental question in Wi-Fi-based gesture recognition: \"Can we use the knowledge learned from some users to perform gesture recognition for others?\". This problem is also known as cross-target recognition. It arises in many practical deployments of Wi-Fi-based gesture recognition where it is prohibitively expensive to collect training data from every single user. We present CrossGR, a low-cost cross-target gesture recognition system. As a departure from existing approaches, CrossGR does not require prior knowledge (such as who is currently performing a gesture) of the target user. Instead, CrossGR employs a deep neural network to extract user-agnostic but gesture-related Wi-Fi signal characteristics to perform gesture recognition. To provide sufficient training data to build an effective deep learning model, CrossGR employs a generative adversarial network to automatically generate many synthetic training data from a small set of real-world examples collected from a small number of users. Such a strategy allows CrossGR to minimize the user involvement and the associated cost in collecting training examples for building an accurate gesture recognition system. We evaluate CrossGR by applying it to perform gesture recognition across 10 users and 15 gestures. Experimental results show that CrossGR achieves an accuracy of over 82.6% (up to 99.75%). We demonstrate that CrossGR delivers comparable recognition accuracy, but uses an order of magnitude less training samples collected from the end-users when compared to state-of-the-art recognition systems.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448100"}, {"primary_key": "1961700", "vector": [], "sparse_vector": [], "title": "DAFI: WiFi-based Device-free Indoor Localization via Domain Adaptation.", "authors": ["Hang Li", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "WiFi-based Device-free Passive (DfP) indoor localization systems liberate their users from carrying dedicated sensors or smartphones, and thus provide a non-intrusive and pleasant experience. Although existing fingerprint-based systems achieve sub-meter-level localization accuracy by training location classifiers/regressors on WiFi signal fingerprints, they are usually vulnerable to small variations in an environment. A daily change, e.g., displacement of a chair, may cause a big inconsistency between the recorded fingerprints and the real-time signals, leading to significant localization errors. In this paper, we introduce a Domain Adaptation WiFi (DAFI) localization approach to address the problem. DAFI formulates this fingerprint inconsistency issue as a domain adaptation problem, where the original environment is the source domain and the changed environment is the target domain. Directly applying existing domain adaptation methods to our specific problem is challenging, since it is generally hard to distinguish the variations in the different WiFi domains (i.e., signal changes caused by different environmental variations). DAFI embraces the following techniques to tackle this challenge. 1) DAFI aligns both marginal and conditional distributions of features in different domains. 2) Inside the target domain, DAFI squeezes the marginal distribution of every class to be more concentrated at its center. 3) Between two domains, DAFI conducts fine-grained alignment by forcing every target-domain class to better align with its source-domain counterpart. By doing these, DAFI outperforms the state of the art by up to 14.2% in real-world experiments.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494954"}, {"primary_key": "1961701", "vector": [], "sparse_vector": [], "title": "Honeysuckle: Annotation-Guided Code Generation of In-App Privacy Notices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In-app privacy notices can help smartphone users make informed privacy decisions. However, they are rarely used in real-world apps, since developers often lack the knowledge, time, and resources to design and implement them well. We present Honeysuckle, a programming tool that helps Android developers build in-app privacy notices using an annotation-based code generation approach facilitated by an IDE plugin, a build system plugin, and a library. We conducted a within-subjects study with 12 Android developers to evaluate Honeysuckle. Each participant was asked to implement privacy notices for two popular open-source apps using the Honeysuckle library as a baseline as well as the annotation-based approach. Our results show that the annotation-based approach helps developers accomplish the task faster with significantly lower cognitive load. Developers preferred the annotation-based approach over the library approach because it was much easier to learn and use and allowed developers to achieve various types of privacy notices using a unified code format, which can enhance code readability and benefit team collaboration.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478097"}, {"primary_key": "1961702", "vector": [], "sparse_vector": [], "title": "EchoSpot: Spotting Your Locations via Acoustic Sensing.", "authors": ["<PERSON><PERSON>", "Jiadong Lou", "<PERSON>", "<PERSON>"], "summary": "Indoor localization has played a significant role in facilitating a collection of emerging applications in the past decade. This paper presents a novel indoor localization solution via inaudible acoustic sensing, called EchoSpot, which relies on only one speaker and one microphone that are readily available on audio devices at households. We program the speaker to periodically send FMCW chirps at 18kHz-23kHz and leverage the co-located microphone to capture the reflected signals from the body and the wall for analysis. By applying the normalized cross-correlation on the transmitted and received signals, we can estimate and profile their time-of-flights (ToFs). We then eliminate the interference from device imperfection and environmental static objects, able to identify the ToFs corresponding to the direct reflection from human body. In addition, a new solution to estimate the ToF from wall reflection is designed, assisting us in spotting a human location in the two-dimensional space. We implement EchoSpot on three different types of speakers, e.g., Amazon Echo, Edifier R1280DB, and Logitech z200, and deploy them in real home environments for evaluation. Experimental results exhibit that EchoSpot achieves the mean localization errors of 4.1cm, 9.2cm, 13.1cm, 17.9cm, 22.2cm, respectively, at 1m, 2m, 3m, 4m, and 5m, comparable to results from the state-of-the-arts while maintaining favorable advantages.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478095"}, {"primary_key": "1961703", "vector": [], "sparse_vector": [], "title": "Fall Detection via Inaudible Acoustic Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The fall detection system is of critical importance in protecting elders through promptly discovering fall accidents to provide immediate medical assistance, potentially saving elders' lives. This paper aims to develop a novel and lightweight fall detection system by relying solely on a home audio device via inaudible acoustic sensing, to recognize fall occurrences for wide home deployment. In particular, we program the audio device to let its speaker emit 20kHz continuous wave, while utilizing a microphone to record reflected signals for capturing the Doppler shift caused by the fall. Considering interferences from different factors, we first develop a set of solutions for their removal to get clean spectrograms and then apply the power burst curve to locate the time points at which human motions happen. A set of effective features is then extracted from the spectrograms for representing the fall patterns, distinguishable from normal activities. We further apply the Singular Value Decomposition (SVD) and K-mean algorithms to reduce the data feature dimensions and to cluster the data, respectively, before input them to a Hidden Markov Model for training and classification. In the end, our system is implemented and deployed in various environments for evaluation. The experimental results demonstrate that our system can achieve superior performance for detecting fall accidents and is robust to environment changes, i.e., transferable to other environments after training in one environment.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478094"}, {"primary_key": "1961705", "vector": [], "sparse_vector": [], "title": "FG-LiquID: A Contact-less Fine-grained Liquid Identifier by Pushing the Limits of Millimeter-wave Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Huadong Ma"], "summary": "Contact-less liquid identification via wireless sensing has diverse potential applications in our daily life, such as identifying alcohol content in liquids, distinguishing spoiled and fresh milk, and even detecting water contamination. Recent works have verified the feasibility of utilizing mmWave radar to perform coarse-grained material identification, e.g., discriminating liquid and carpet. However, they do not fully exploit the sensing limits of mmWave in terms of fine-grained material classification. In this paper, we propose FG-LiquID, an accurate and robust system for fine-grained liquid identification. To achieve the desired fine granularity, FG-LiquID first focuses on the small but informative region of the mmWave spectrum, so as to extract the most discriminative features of liquids. Then we design a novel neural network, which uncovers and leverages the hidden signal patterns across multiple antennas on mmWave sensors. In this way, FG-LiquID learns to calibrate signals and finally eliminate the adverse effect of location interference caused by minor displacement/rotation of the liquid container, which ensures robust identification towards daily usage scenarios. Extensive experimental results using a custom-build prototype demonstrate that FG-LiquID can accurately distinguish 30 different liquids with an average accuracy of 97%, under 5 different scenarios. More importantly, it can discriminate quite similar liquids, such as liquors with the difference of only 1% alcohol concentration by volume.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478075"}, {"primary_key": "1961706", "vector": [], "sparse_vector": [], "title": "Towards Finding the Optimum Position in the Visual Field for a Head Worn Display Used for Task Guidance with Non-registered Graphics.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Where should a HWD be placed in a user's visual field? We present two studies that compare comfort, preference, task efficiency and accuracy for various HWD positions. The first study offsets a 9.2° horizontal field-of-view (FOV) display temporally (toward the ear) from 0° to 30° in 10° steps. 30° proves too uncomfortable while 10° is the most preferred position for a simple button-pushing game, corroborating results from previous single-task reading experiments. The second experiment uses a Magic Leap One to compare 10° x 10° FOV interfaces centered at line-of-sight, temporally offset 15° (center-right), inferiorly offset 15° (bottom-center), and offset in both directions (bottom-right) for an order picking task. The bottom-right position proved worst in terms of accuracy and several subjective metrics when compared to the line-of-sight position.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448091"}, {"primary_key": "1961707", "vector": [], "sparse_vector": [], "title": "DropMonitor: Millimeter-level Sensing for RFID-based Infusion Drip Rate Monitoring.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As an important indicator of the infusion monitoring for clinical treatment, the drip rate is expected to be monitored in an accurate and real-time manner. However, state-of-the-art drip rate monitoring schemes either suffer from high maintenance or incur high hardware cost. In this paper, we propose DropMonitor, an RFID-based approach to perform the mm-level sensing for infusion drip rate monitoring. By attaching a pair of batteryless RFID tags on the drip chamber, we can estimate the drip rate by capturing the RF-signals reflected from the vibrating liquid surface caused by the falling droplets. Particularly, we use the sensing tag to perceive the liquid surface vibration in the drip chamber and further derive the drip rate for infusion monitoring. Moreover, to sufficiently mitigate the multi-path interference from the surrounding human activities, we use the reference tag to perceive the multi-path signals from the indoor environment. By computing the difference of RF-signals from tag pairs, we cancel the multi-path interference and extract the drip-rate-related signals. We have implemented a prototype system and evaluated its performance in real applications. The experiment results show that DropMonitor can accurately estimate the infusion drip rate, and the average relative error of drip rate estimation is below 1% for conventional cases. In this way, considering the essential sampling rates of each tag, DropMonitor is able to monitor the drip rate for over a dozen of infusion bottles/bags in parallel with one COTS RFID system.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463496"}, {"primary_key": "1961710", "vector": [], "sparse_vector": [], "title": "Wi-Fruit: See Through Fruits with Smart Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Linghe Kong", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "People usually assess fruit qualities from external features such as color, shape, size, and texture. However, it is quite common that we select fruits with perfect appearances but rotten inside, especially for fruits with thick pericarps. Thus the accurate measurement is desirable to evaluate the internal conditions of fruits. As two key features of fruit internal qualities, existing methods on measuring fruit moisture and soluble solid contents (SSC) are either destructive or costly, limiting their adoption in daily life. In this paper, we propose Wi-Fruit, a non-destructive and low-cost fruit moisture and SSC measurement system leveraging Wi-Fi channel state information (CSI). First, to cope with the fruit structure dependency challenge, we propose a double-quotient model to pre-process CSI on adjacent antennas. Second, to address the fruit size and type dependency challenges, a lightweight artificial neural network (ANN) model with visual information fusion is proposed for fruit moisture and SSC estimations. Extensive evaluations are conducted on 6 types of fruits with both thick (i.e., watermelon and grapefruit) and thin pericarps (i.e., dragon fruit, apple, pear, and orange) over a month in either an empty laboratory room or a library with massive books. Results demonstrate that Wi-Fruit achieves an acceptable estimation accuracy (RMSE=0.319). It is independent of various fruit structures, sizes, and types, while also robust to time and environmental changes. The fruit internal sensing capabilities of Wi-Fruit can help fruit saving and safety in both pre-harvest and post-harvest applications.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494971"}, {"primary_key": "1961711", "vector": [], "sparse_vector": [], "title": "The Empathetic Car: Exploring Emotion Inference via Driver Behaviour and Traffic Context.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "An empathetic car that is capable of reading the driver's emotions has been envisioned by many car manufacturers. Emotion inference enables in-vehicle applications to improve driver comfort, well-being, and safety. Available emotion inference approaches use physiological, facial, and speech-related data to infer emotions during driving trips. However, existing solutions have two major limitations: Relying on sensors that are not built into the vehicle restricts emotion inference to those people leveraging corresponding devices (e.g., smartwatches). Relying on modalities such as facial expressions and speech raises privacy concerns. By contrast, researchers in mobile health have been able to infer affective states (e.g., emotions) based on behavioral and contextual patterns decoded in available sensor streams, e.g., obtained by smartphones. We transfer this rationale to an in-vehicle setting by analyzing the feasibility of inferring driver emotions by passively interpreting the data streams of the control area network (CAN-bus) and the traffic context (inferred from the front-view camera). Therefore, our approach does not rely on particularly privacy-sensitive data streams such as the driver facial video or driver speech, but is built based on existing CAN-bus data and traffic information, which is available in current high-end or future vehicles. To assess our approach, we conducted a four-month field study on public roads covering a variety of uncontrolled daily driving activities. Hence, our results were generated beyond the confines of a laboratory environment. Ultimately, our proposed approach can accurately recognise drivers' emotions and achieve comparable performance as the medical-grade physiological sensor-based state-of-the-art baseline method.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478078"}, {"primary_key": "1961714", "vector": [], "sparse_vector": [], "title": "BlinkListener: &quot;Listen&quot; to Your Eye Blink Using Your Smartphone.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Eye blink detection plays a key role in many real-life applications such as Human-Computer Interaction (HCI), drowsy driving prevention and eye disease detection. Although traditional camera-based techniques are promising, multiple issues hinder their wide adoption including the privacy concern, strict lighting condition and line-of-sight (LoS) requirements. On the other hand, wireless sensing without a need for dedicated sensors gains a tremendous amount of attention in recent years. Among the wireless signals utilized for sensing, acoustic signals show a unique potential for fine-grained sensing owing to their low propagation speed in the air. Another trend favoring acoustic sensing is the wide availability of speakers and microphones in commodity devices. Promising progress has been achieved in fine-grained human motion sensing such as breathing using acoustic signals. However, it is still very challenging to employ acoustic signals for eye blink detection due to the unique characteristics of eye blink (i.e., subtle, sparse and aperiodic) and severe interference (i.e., from the human target himself and surrounding objects). We find that even the very subtle involuntary head movement induced by breathing can severely interfere with eye blink detection. In this work, for the first time, we propose a system called BlinkListener to sense the subtle eye blink motion using acoustic signals in a contact-free manner. We first quantitatively model the relationship between signal variation and the subtle movements caused by eye blink and interference. Then, we propose a novel method that exploits the \"harmful\" interference to maximize the subtle signal variation induced by eye blinks. We implement BlinkListener on both a research-purpose platform (Bela) and a commodity smartphone (iPhone 5c). Experiment results show that BlinkListener can achieve robust performance with a median detection accuracy of 95%. Our system can achieve high accuracies when the smartphone is held in hand, the target wears glasses/sunglasses and in the presence of strong interference with people moving around.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463521"}, {"primary_key": "1961715", "vector": [], "sparse_vector": [], "title": "WiPhone: Smartphone-based Respiration Monitoring Using Ambient Reflected WiFi Signals.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON> Gu", "<PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "Recent years have witnessed a trend of monitoring human respiration using Channel State Information (CSI) retrieved from commodity WiFi devices. Existing approaches essentially leverage signal propagation in a Line-of-Sight (LoS) setting to achieve good performance. However, in real-life environments, LoS can be easily blocked by furniture, home appliances and walls. This paper presents a novel smartphone-based system named WiPhone, aiming to robustly monitor human respiration in NLoS settings. Since a smartphone is usually carried around by one subject, leveraging directly-reflected CSI signals in LoS becomes infeasible. WiPhone exploits ambient reflected CSI signals in a Non-Line-of-Sight (NLoS) setting to quantify the relationship between CSI signals reflected from the environment and a subject's chest displacement. In this way, WiPhone successfully turns ambient reflected signals which have been previously considered \"destructive\" into beneficial sensing capability. CSI signals obtained from smartphone are usually very noisy and may scatter over different sub-carriers. We propose a density-based preprocessing method to extract useful CSI amplitude patterns for effective respiration monitoring. We conduct extensive experiments with 8 subjects in a real home environment. WiPhone achieves a respiration rate error of 0.31 bpm (breaths per minute) on average in a range of NLoS settings.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448092"}, {"primary_key": "1961716", "vector": [], "sparse_vector": [], "title": "VeriMask: Facilitating Decontamination of N95 Masks in the COVID-19 Pandemic: Challenges, Lessons Learned, and Safeguarding the Future.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The US CDC has recognized moist-heat as one of the most effective and accessible methods of decontaminating N95 masks for reuse in response to the persistent N95 mask shortages caused by the COVID-19 pandemic. However, it is challenging to reliably deploy this technique in healthcare settings due to a lack of smart technologies capable of ensuring proper decontamination conditions of hundreds of masks simultaneously. To tackle these challenges, we developed an open-source wireless sensor platform---VeriMask1 ---that facilitates per-mask verification of the moist-heat decontamination process. VeriMask is capable of monitoring hundreds of masks simultaneously in commercially available heating systems and provides a novel throughput-maximization functionality to help operators optimize the decontamination settings. We evaluate VeriMask in laboratory and real-scenario clinical settings and find that it effectively detects decontamination failures and operator errors in multiple settings and increases the mask decontamination throughput. Our easy-to-use, low-power, low-cost, scalable platform integrates with existing hospital protocols and equipment, and can be broadly deployed in under-resourced facilities to protect front-line healthcare workers by lowering their risk of infection from reused N95 masks. We also memorialize the design challenges, guidelines, and lessons learned from developing and deploying VeriMask during the COVID-19 Pandemic. Our hope is that by reflecting and reporting on this design experience, technologists and front-line health workers will be better prepared to collaborate for future pandemics, regarding mask decontamination, but also other forms of crisis tech.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478105"}, {"primary_key": "1961718", "vector": [], "sparse_vector": [], "title": "Unsupervised Human Activity Representation Learning with Multi-task Deep Clustering.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Human activity recognition (HAR) based on sensing data from wearable and mobile devices has become an active research area in ubiquitous computing, and it envisions a wide range of application scenarios in mobile social networking, environmental context sensing, health and well-being monitoring, etc. However, activity recognition based on manually annotated sensing data is manpower-expensive, time-consuming, and privacy-sensitive, which prevents HAR systems from being really deployed in scale. In this paper, we address the problem of unsupervised human activity recognition, which infers activities from unlabeled datasets without the need of domain knowledge. We propose an end-to-end multi-task deep clustering framework to solve the problem. Taking the unlabeled multi-dimensional sensing signals as input, we firstly apply a CNN-BiLSTM autoencoder to form a compressed latent feature representation. Then we apply a K-means clustering algorithm based on the extracted features to partition the dataset into different groups, which produces pseudo labels for the instances. We further train a deep neural network (DNN) with the latent features and pseudo labels for activity recognition. The tasks of feature representation, clustering, and classification are integrated into a uniform multi-task learning framework and optimized jointly to achieve unsupervised activity classification. We conduct extensive experiments based on three public datasets. It is shown that the proposed approach outperforms shallow unsupervised learning approaches, and it performs close to the state-of-the-art supervised approaches by fine-tuning with a small number of labeled data. The proposed approach significantly reduces the cost of human-based data annotation and narrows down the gap between unsupervised and supervised human activity recognition.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448074"}, {"primary_key": "1961720", "vector": [], "sparse_vector": [], "title": "DANA: Dimension-Adaptive Neural Architecture for Multivariate Sensor Data.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Motion sensors embedded in wearable and mobile devices allow for dynamic selection of sensor streams and sampling rates, enabling several applications, such as power management and data-sharing control. While deep neural networks (DNNs) achieve competitive accuracy in sensor data classification, DNNs generally process incoming data from a fixed set of sensors with a fixed sampling rate, and changes in the dimensions of their inputs cause considerable accuracy loss, unnecessary computations, or failure in operation. We introduce a dimension-adaptive pooling (DAP) layer that makes DNNs flexible and more robust to changes in sensor availability and in sampling rate. DAP operates on convolutional filter maps of variable dimensions and produces an input of fixed dimensions suitable for feedforward and recurrent layers. We also propose a dimension-adaptive training (DAT) procedure for enabling DNNs that use DAP to better generalize over the set of feasible data dimensions at inference time. DAT comprises the random selection of dimensions during the forward passes and optimization with accumulated gradients of several backward passes. Combining DAP and DAT, we show how to transform non-adaptive DNNs into a Dimension-Adaptive Neural Architecture (DANA), while keeping the same number of parameters. Compared to existing approaches, our solution provides better classification accuracy over the range of possible data dimensions at inference time and does not require up-sampling or imputation, thus reducing unnecessary computations. Experiments on seven datasets (four benchmark real-world datasets for human activity recognition and three synthetic datasets) show that DANA prevents significant losses in classification accuracy of the state-of-the-art DNNs and, compared to baselines, it better captures correlated patterns in sensor data under dynamic sensor availability and varying sampling rates.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478074"}, {"primary_key": "1961721", "vector": [], "sparse_vector": [], "title": "Examining the Social Context of Alcohol Drinking in Young Adults with Smartphone Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>-<PERSON>"], "summary": "According to prior work, the type of relationship between the person consuming alcohol and others in the surrounding (friends, family, spouse, etc.), and the number of those people (alone, with one person, with a group, etc.) are related to many aspects of alcohol consumption, such as the drinking amount, location, motives, and mood. Even though the social context is recognized as an important aspect that influences the drinking behavior of young adults in alcohol research, relatively little work has been conducted in smartphone sensing research on this topic. In this study, we analyze the weekend nightlife drinking behavior of 241 young adults in Switzerland, using a dataset consisting of self-reports and passive smartphone sensing data over a period of three months. Using multiple statistical analyses, we show that features from modalities such as accelerometer, location, application usage, bluetooth, and proximity could be informative about different social contexts of drinking. We define and evaluate seven social context inference tasks using smartphone sensing data, obtaining accuracies of the range 75%-86% in four two-class and three three-class inferences. Further, we discuss the possibility of identifying the sex composition of a group of friends using smartphone sensor data with accuracies over 70%. The results are encouraging towards (a) supporting future interventions on alcohol consumption that incorporate users' social context more meaningfully, and (b) reducing the need for user self-reports when creating drink logs.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478126"}, {"primary_key": "1961722", "vector": [], "sparse_vector": [], "title": "One More Bite?: Inferring Food Consumption Level of College Students Using Smartphone Sensing and Self-Reports.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Viridiana del Carmen Robledo-Valero", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>-<PERSON>"], "summary": "While the characterization of food consumption level has been extensively studied in nutrition and psychology research, advancements in passive smartphone sensing have not been fully utilized to complement mobile food diaries in characterizing food consumption levels. In this study, a new dataset regarding the holistic food consumption behavior of 84 college students in Mexico was collected using a mobile application combining passive smartphone sensing and self-reports. We show that factors such as sociability and activity types and levels have an association to food consumption levels. Finally, we define and assess a novel ubicomp task, by using machine learning techniques to infer self-perceived food consumption level (eating as usual, overeating, undereating) with an accuracy of 87.81% in a 3-class classification task by using passive smartphone sensing and self-report data. Furthermore, we show that an accuracy of 83.49% can be achieved for the same classification task by using only smartphone sensing data and time of eating, which is an encouraging step towards building context-aware mobile food diaries and making food diary based apps less tedious for users.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448120"}, {"primary_key": "1961723", "vector": [], "sparse_vector": [], "title": "A CNN-based Human Activity Recognition System Combining a Laser Feedback Interferometry Eye Movement Sensor and an IMU for Context-aware Smart Glasses.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Enkelejda Kasneci"], "summary": "Smart glasses are considered the next breakthrough in wearables. As the successor of smart watches and smart ear wear, they promise to extend reality by immersive embedding of content in the user's field of view. While advancements in display technology seems to fulfill this promises, interaction concepts are derived from established wearable concepts like touch interaction or voice interaction, preventing full immersion as they require the user to frequently interact with the glasses. To minimize interactions, we propose to add context-awareness to smart glasses through human activity recognition (HAR) by combining head- and eye movement features to recognize a wide range of activities. To measure eye movements in unobtrusive way, we propose laser feedback interferometry (LFI) sensors. These tiny low power sensors are highly robust to ambient light. We combine LFI sensors and an IMU to collect eye and head movement features from 15 participants performing 7 cognitive and physical activities, leading to a unique data set. To recognize activities we propose a 1D-CNN model and apply transfer learning to personalize the classification, leading to an outstanding macro-F1 score of 88.15 % which outperforms state of the art methods. Finally, we discuss the applicability of the proposed system in a smart glasses setup.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494998"}, {"primary_key": "1961724", "vector": [], "sparse_vector": [], "title": "Detecting Receptivity for mHealth Interventions in the Natural Environment.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Just-In-Time Adaptive Intervention (JITAI) is an emerging technique with great potential to support health behavior by providing the right type and amount of support at the right time. A crucial aspect of JITAIs is properly timing the delivery of interventions, to ensure that a user is receptive and ready to process and use the support provided. Some prior works have explored the association of context and some user-specific traits on receptivity, and have built post-study machine-learning models to detect receptivity. For effective intervention delivery, however, a JITAI system needs to make in-the-moment decisions about a user's receptivity. To this end, we conducted a study in which we deployed machine-learning models to detect receptivity in the natural environment, i.e., in free-living conditions. We leveraged prior work regarding receptivity to JITAIs and deployed a chatbot-based digital coach - Ally - that provided physical-activity interventions and motivated participants to achieve their step goals. We extended the original Ally app to include two types of machine-learning model that used contextual information about a person to predict when a person is receptive: a static model that was built before the study started and remained constant for all participants and an adaptive model that continuously learned the receptivity of individual participants and updated itself as the study progressed. For comparison, we included a control model that sent intervention messages at random times. The app randomly selected a delivery model for each intervention message. We observed that the machine-learning models led up to a 40% improvement in receptivity as compared to the control model. Further, we evaluated the temporal dynamics of the different models and observed that receptivity to messages from the adaptive model increased over the course of the study.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463492"}, {"primary_key": "1961726", "vector": [], "sparse_vector": [], "title": "An Interview Method for Engaging Personal Data.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Whether investigating research questions or designing systems, many researchers and designers need to engage users with their personal data. However, it is difficult to successfully design user-facing tools for interacting with personal data without first understanding what users want to do with their data. Techniques for raw data exploration, sketching, or physicalization can avoid the perils of tool development, but prevent direct analytical access to users' rich personal data. We present a new method that directly tackles this challenge: the data engagement interview. This interview method incorporates an analyst to provide real-time personal data analysis, granting interview participants the opportunity to directly engage with their data, and interviewers to observe and ask questions throughout this engagement. We describe the method's development through a case study with asthmatic participants, share insights and guidance from our experience, and report a broad set of insights from these interviews.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494964"}, {"primary_key": "1961727", "vector": [], "sparse_vector": [], "title": "Reducing Muscle Activity when Playing Tremolo by Using Electrical Muscle Stimulation to Learn Efficient Motor Skills.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "When beginners play the piano, the activity of the forearm muscles tends to be greater than that of experts because beginners move their fingers with more force than necessary. Reducing forearm muscle activity is important for pianists to prevent fatigue and injury. However, it is difficult for beginners to learn how to do so by themselves. We propose using electrical muscle stimulation (EMS) to teach beginners how to reduce this muscle activity while playing a tremolo: a rapid alternation between two notes. Since experts use wrist rotation efficiently when playing tremolos, we propose an EMS-based support system that applies EMS not to muscles that are relevant to moving the fingers but to the supinator and pronator teres muscles, which are involved in wrist rotation. We conducted a user study with 16 beginners to investigate how the forearm muscle activity on the extensor pollicis longus and digitorum muscles changed when using our EMS-based support system. We divided the participants into two groups: an experimental group who practiced by themselves with EMS and a control group who practiced by themselves without EMS and then practiced with instruction. When practicing by themselves, practicing with EMS was more effective than that without EMS; the activity levels of the extensor pollicis longus and digitorum muscles were significantly lower with EMS, and the participants felt less fatigue when playing tremolos. By comparing the improvement in reducing muscle activity between practicing with EMS and practicing with instruction, there was no significant difference. The results suggest that our EMS-based support system can reduce target muscle activity by applying EMS to other muscles to teach beginners how to move limbs efficiently.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478110"}, {"primary_key": "1961728", "vector": [], "sparse_vector": [], "title": "Pantomime: Mid-Air Gesture Recognition with Sparse Millimeter-Wave Radar Point Clouds.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce Pantomime, a novel mid-air gesture recognition system exploiting spatio-temporal properties of millimeter-wave radio frequency (RF) signals. Pantomime is positioned in a unique region of the RF landscape: mid-resolution mid-range high-frequency sensing, which makes it ideal for motion gesture interaction. We configure a commercial frequency-modulated continuous-wave radar device to promote spatial information over the temporal resolution by means of sparse 3D point clouds and contribute a deep learning architecture that directly consumes the point cloud, enabling real-time performance with low computational demands. Pantomime achieves 95% accuracy and 99% AUC in a challenging set of 21 gestures articulated by 41 participants in two indoor environments, outperforming four state-of-the-art 3D point cloud recognizers. We further analyze the effect of the environment in 5 different indoor environments, the effect of articulation speed, angle, and the distance of the person up to 5m. We have publicly made available the collected mmWave gesture dataset consisting of nearly 22,000 gesture instances along with our radar sensor configuration, trained models, and source code for reproducibility. We conclude that pantomime is resilient to various input conditions and that it may enable novel applications in industrial, vehicular, and smart home scenarios.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448110"}, {"primary_key": "1961729", "vector": [], "sparse_vector": [], "title": "Efficient Schedule of Energy-Constrained UAV Using Crowdsourced Buses in Last-Mile Parcel Delivery.", "authors": ["<PERSON>", "Shining <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Stimulated by the dramatical service demand in the logistics industry, logistics trucks employed in last-mile parcel delivery bring critical public concerns, such as heavy cost burden, traffic congestion and air pollution. Unmanned Aerial Vehicles (UAVs) are a promising alternative tool in last-mile delivery, which is however limited by insufficient flight range and load capacity. This paper presents an innovative energy-limited logistics UAV schedule approach using crowdsourced buses. Specifically, when one UAV delivers a parcel, it first lands on a crowdsourced social bus to parcel destination, gets recharged by the wireless recharger deployed on the bus, and then flies from the bus to the parcel destination. This novel approach not only increases the delivery range and load capacity of battery-limited UAVs, but is also much more cost-effective and environment-friendly than traditional methods. New challenges therefore emerge as the buses with spatiotemporal mobility become the bottleneck during delivery. By landing on buses, an Energy-Neutral Flight Principle and a delivery scheduling algorithm are proposed for the UAVs. Using the Energy-Neutral Flight Principle, each UAV can plan a flying path without depleting energy given buses with uncertain velocities. Besides, the delivery scheduling algorithm optimizes the delivery time and number of delivered parcels given warehouse location, logistics UAVs, parcel locations and buses. Comprehensive evaluations using a large-scale bus dataset demonstrate the superiority of the innovative logistics UAV schedule approach.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448079"}, {"primary_key": "1961734", "vector": [], "sparse_vector": [], "title": "LSVP: Towards Effective On-the-go Video Learning Using Optical Head-Mounted Displays.", "authors": ["<PERSON><PERSON>", "Sheng<PERSON> Zhao"], "summary": "The ubiquity of mobile phones allows video content to be watched on the go. However, users' current on-the-go video learning experience on phones is encumbered by issues of toggling and managing attention between the video and surroundings, as informed by our initial qualitative study. To alleviate this, we explore how combining the emergent smart glasses (Optical Head-Mounted Display or OHMD) platform with a redesigned video presentation style can better distribute users' attention between learning and walking tasks. We evaluated three presentation techniques: highlighting, sequentiality, and data persistence to find that combining sequentiality and data persistence is highly effective, yielding a 56% higher immediate recall score compared to a static video presentation. We also compared the OHMD against smartphones to delineate the advantages of either platform for on-the-go video learning in the context of everyday mobility tasks. We found that OHMDs improved users' 7-day delayed recall scores by 17% while still allowing 5.6% faster walking speed, especially during complex mobility tasks. Based on the findings, we introduce Layered Serial Visual Presentation (LSVP) style, which incorporates sequentiality, strict data persistence, and transparent background, among other properties, for future OHMD-based on-the-go video learning.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448118"}, {"primary_key": "1961736", "vector": [], "sparse_vector": [], "title": "Winect: 3D Human Pose Tracking for Free-form Activity Using Commodity WiFi.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "WiFi human sensing has become increasingly attractive in enabling emerging human-computer interaction applications. The corresponding technique has gradually evolved from the classification of multiple activity types to more fine-grained tracking of 3D human poses. However, existing WiFi-based 3D human pose tracking is limited to a set of predefined activities. In this work, we present Winect, a 3D human pose tracking system for free-form activity using commodity WiFi devices. Our system tracks free-form activity by estimating a 3D skeleton pose that consists of a set of joints of the human body. In particular, we combine signal separation and joint movement modeling to achieve free-form activity tracking. Our system first identifies the moving limbs by leveraging the two-dimensional angle of arrival of the signals reflected off the human body and separates the entangled signals for each limb. Then, it tracks each limb and constructs a 3D skeleton of the body by modeling the inherent relationship between the movements of the limb and the corresponding joints. Our evaluation results show that Winect is environment-independent and achieves centimeter-level accuracy for free-form activity tracking under various challenging environments including the none-line-of-sight (NLoS) scenarios.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494973"}, {"primary_key": "1961737", "vector": [], "sparse_vector": [], "title": "How Low Can You Go?: Performance Trade-offs in Low-Resolution Thermal Sensors for Occupancy Detection: A Systematic Evaluation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We contribute by systematically analysing the performance trade-offs, costs (privacy loss and deployment cost) and limits of low-resolution thermal array sensors for occupancy detection. First, to assess performance limits, we manipulate the frame rate and resolution of images to establish the lowest possible values where reliable occupancy information can be captured. We also assess the effect of different viewing angles on the performance. We analyse performance using two datasets, an open-source dataset of thermal array sensor measurements (TIDOS) and a proprietary dataset that is used to validate the generality of the findings and to study the effect of different viewing angles. Our results show that even cameras with a 4 × 2 resolution - significantly lower than what has been used in previous research - can support reliable detection, as long as the frame rate is at least 4 frames per second. The lowest tested resolution, 2 × 2, can also offer reliable detection rates but requires higher frame rates (at least 16 frames per second) and careful adjustment of the camera viewing angle. We also show that the performance is sensitive to the viewing angle of the sensor, suggesting that the camera's field-of-view needs to be carefully adjusted to maximize the performance of low-resolution cameras. Second, in terms of costs, using a camera with only 4 × 2 resolution reveals very few insights about the occupants' identity or behaviour, and thus helps to preserve their privacy. Besides privacy, lowering the resolution and frame rate decreases manufacturing and operating costs and helps to make the solution easier to adopt. Based on our results, we derive guidelines on how to choose sensor resolution in real-world deployments by carrying out a small-scale trade-off analysis that considers two representative buildings as potential deployment areas and compares the cost, privacy and accuracy trade-offs of different resolutions.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478104"}, {"primary_key": "1961738", "vector": [], "sparse_vector": [], "title": "A Scalable Solution for Signaling Face Touches to Reduce the Spread of Surface-based Pathogens.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Zipporah Cohen", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Hand-to-Face transmission has been estimated to be a minority, yet non-negligible, vector of COVID-19 transmission and a major vector for multiple other pathogens. At the same time, as it cannot be effectively addressed with mainstream protection measures, such as wearing masks or tracing contacts, it remains largely untackled. To help address this issue, we have developed Saving Face - an app that alerts users when they are about to touch their faces, by analyzing the distortion patterns in the ultrasound signal emitted by their earphones. The system only relies on pre-existing hardware (a smartphone with generic earphones), which allows it to be rapidly scalable to billions of smartphone users worldwide. This paper describes the design, implementation and evaluation of the system, as well as the results of a user study testing the solution's accuracy, robustness, and user experience during various day-to-day activities (93.7% Sensitivity and 91.5% Precision, N=10). While this paper focuses on the system's application to detecting hand-to-face gestures, the technique can also be applicable to other types of gestures and gesture-based applications.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448121"}, {"primary_key": "1961739", "vector": [], "sparse_vector": [], "title": "Person-Centered Predictions of Psychological Constructs with Social Media Contextualized by Multimodal Sensing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Vedant <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Personalized predictions have shown promises in various disciplines but they are fundamentally constrained in their ability to generalize across individuals. These models are often trained on limited datasets which do not represent the fluidity of human functioning. In contrast, generalized models capture normative behaviors between individuals but lack precision in predicting individual outcomes. This paper aims to balance the tradeoff between one-for-each and one-for-all models by clustering individuals on mutable behaviors and conducting cluster-specific predictions of psychological constructs in a multimodal sensing dataset of 754 individuals. Specifically, we situate our modeling on social media that has exhibited capability in inferring psychosocial attributes. We hypothesize that complementing social media data with offline sensor data can help to personalize and improve predictions. We cluster individuals on physical behaviors captured via Bluetooth, wearables, and smartphone sensors. We build contextualized models predicting psychological constructs trained on each cluster's social media data and compare their performance against generalized models trained on all individuals' data. The comparison reveals no difference in predicting affect and a decline in predicting cognitive ability, but an improvement in predicting personality, anxiety, and sleep quality. We construe that our approach improves predicting psychological constructs sharing theoretical associations with physical behavior. We also find how social media language associates with offline behavioral contextualization. Our work bears implications in understanding the nuanced strengths and weaknesses of personalized predictions, and how the effectiveness may vary by multiple factors. This work reveals the importance of taking a critical stance on evaluating the effectiveness before investing efforts in personalization.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448117"}, {"primary_key": "1961740", "vector": [], "sparse_vector": [], "title": "Overthere: A Simple and Intuitive Object Registration Method for an Absolute Mid-air Pointing Interface.", "authors": ["Hyunggoog Seo", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "An absolute mid-air pointing technique requires a preprocess called registration that makes the system remember the 3D positions and types of objects in advance. Previous studies have simply assumed that the information is already available because it requires a cumbersome process performed by an expert in a carefully calibrated environment. We introduce Overthere, which allows the user to intuitively register the objects in a smart environment by pointing to each target object a few times. To ensure accurate and coherent pointing gestures made by the user regardless of individual differences between them, we performed a user study and identified a desirable gesture motion for this purpose. In addition, we provide the user with various feedback to help them understand the current registration progress and adhere to required conditions, which will lead to accurate registration results. The user studies show that Overthere is sufficiently intuitive to be used by ordinary people.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478128"}, {"primary_key": "1961741", "vector": [], "sparse_vector": [], "title": "IriTrack: Face Presentation Attack Detection Using Iris Tracking.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With a growing adoption of face authentication systems in various application scenarios, face Presentation Attack Detection (PAD) has become of great importance to withstand artefacts. Existing methods of face PAD generally focus on designing intelligent classifiers or customized hardware to differentiate between the image or video samples of a real legitimate user and the imitated ones. Although effective, they can be resource-consuming and suffer from performance degradation due to environmental changes. In this paper, we propose IriTrack, which is a simple and efficient PAD system that takes iris movement as a significant evidence to identify face artefacts. More concretely, users are required to move their eyes along with a randomly generated poly-line, where the resulting trajectories of their irises are used as an evidence for PAD i.e., a presentation attack will be identified if the deviation of one's actual iris trajectory from the given poly-line exceeds a threshold. The threshold is carefully selected to balance the latency and accuracy of PAD. We have implemented a prototype and conducted extensive experiments to evaluate the performance of the proposed system. The results show that IriTrack can defend against artefacts with moderate time and memory overheads.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463515"}, {"primary_key": "1961742", "vector": [], "sparse_vector": [], "title": "Fine-Grained and Context-Aware Behavioral Biometrics for Pattern Lock on Smartphones.", "authors": ["Dai Shi", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Pattern lock has been widely used in smartphones as a simple and effective authentication mechanism, which however is shown to be vulnerable to various attacks. In this paper, we design a novel authentication system for more secure pattern unlocking on smartphones. The basic idea is to utilize various behavior information of the user during pattern unlocking as additional authentication fingerprints, so that even if the pattern password is leaked to an attacker, the system remains safe and protected. To accommodate a variety of user contexts by our system, a context-aware module is proposed to distinguish any of such contexts (e.g., body postures when drawing the pattern) and use it to guide the authentication. Moreover, we design a polyline weighted strategy with overlapping based on the consistency of pattern lock, which analyzes the behavior information of the user during the unlock process in a fine-grained manner and takes an overall consideration the results of different polylines. Based on 14,850 samples collected from 77 participants, we have extensively evaluated the proposed system. The results demonstrate that it outperforms state-of-the-art implicit authentication based pattern lock approaches, and that each key module in our system is effective.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448080"}, {"primary_key": "1961743", "vector": [], "sparse_vector": [], "title": "TagFi: Locating Ultra-Low Power WiFi Tags Using Unmodified WiFi Infrastructure.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Tag localization is crucial for many context-aware and automation applications in smart homes, retail stores, or warehouses. While custom localization technologies (e.g RFID) have the potential to support low-cost battery-free tag tracking, the cost and complexity of commissioning a space with beacons or readers has stifled adoption. In this paper, we explore how WiFi backscatter localization can be realized using the existing WiFi infrastructure already deployed for data applications. We present a new approach that leverages existing WiFi infrastructure to enable extremely low-power and accurate tag localization relative to a single scanning device. First, we adopt an ultra-low power tag design in which the tag blindly modulates ongoing WiFi packets using On-Off Keying (OOK). Then, we utilize the underlying physical properties of multipath propagation to detect the passive wireless reflection from the tag in the presence of rich multipath propagations. Finally, we localize the tag from a single receiver by forming a triangle between the tag reflection and the LoS path between the two WiFi transceivers. We implement TagFi using a customized backscatter tag and off-the-shelf WiFi chipsets. Our empirical results in a cluttered office building demonstrate that TagFi achieves a median localization accuracy of 0.2m up to 8 meters range.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448082"}, {"primary_key": "1961745", "vector": [], "sparse_vector": [], "title": "LumNet: Learning to Estimate Vertical Visual Field Luminance for Adaptive Lighting Control.", "authors": ["Prince <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "High-quality lighting positively influences visual performance in humans. The experienced visual performance can be measured using desktop luminance and hence several lighting control systems have been developed for its quantification. However, the measurement devices that are used to monitor the desktop luminance in existing lighting control systems are obtrusive to the users. As an alternative, ceiling-based luminance projection sensors are being used recently as these are unobtrusive and can capture the direct task area of a user. The positioning of these devices on the ceiling requires to estimate the desktop luminance in the user's vertical visual field, solely using ceiling-based measurements, to better predict the experienced visual performance of the user. For this purpose, we present LUMNET, an approach for estimating desktop luminance with deep models through utilizing supervised and self-supervised learning. Our model learns visual representations from ceiling-based images, which are collected in indoor spaces within the physical vicinity of the user to predict average desktop luminance as experienced in a real-life setting. We also propose a self-supervised contrastive method for pre-training LUMNET with unlabeled data and we demonstrate that the learned features are transferable onto a small labeled dataset which minimizes the requirement of costly data annotations. Likewise, we perform experiments on domain-specific datasets and show that our approach significantly improves over the baseline results from prior methods in estimating luminance, particularly in the low-data regime. LUMNET is an important step towards learning-based technique for luminance estimation and can be used for adaptive lighting control directly on-device thanks to its minimal computational footprint with an added benefit of preserving user's privacy.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463500"}, {"primary_key": "1961746", "vector": [], "sparse_vector": [], "title": "Towards Device Independent Eavesdropping on Telephone Conversations with Built-in Accelerometer.", "authors": ["Weigao Su", "Dai<PERSON> Liu", "Tai<PERSON> Zhang", "Hongbo Jiang"], "summary": "Motion sensors in modern smartphones have been exploited for audio eavesdropping in loudspeaker mode due to their sensitivity to vibrations. In this paper, we further move one step forward to explore the feasibility of using built-in accelerometer to eavesdrop on the telephone conversation of caller/callee who takes the phone against cheek-ear and design our attack Vibphone. The inspiration behind Vibphone is that the speech-induced vibrations (SIV) can be transmitted through the physical contact of phone-cheek to accelerometer with the traces of voice content. To this end, Vibphone faces three main challenges: i) Accurately detecting SIV signals from miscellaneous disturbance; ii) Combating the impact of device diversity to work with a variety of attack scenarios; and iii) Enhancing feature-agnostic recognition model to generalize to newly issued devices and reduce training overhead. To address these challenges, we first conduct an in-depth investigation on SIV features to figure out the root cause of device diversity impacts and identify a set of critical features that are highly relevant to the voice content retained in SIV signals and independent of specific devices. On top of these pivotal observations, we propose a combo method that is the integration of extracted critical features and deep neural network to recognize speech information from the spectrogram representation of acceleration signals. We implement the attack using commodity smartphones and the results show it is highly effective. Our work brings to light a fundamental design vulnerability in the vast majority of currently deployed smartphones, which may put people's speech privacy at risk during phone calls. We also propose a practical and effective defense solution. We validate that it is feasible to prevent audio eavesdropping by using random variation of sampling rate.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494969"}, {"primary_key": "1961747", "vector": [], "sparse_vector": [], "title": "VREED: Virtual Reality Emotion Recognition Dataset Using Eye Tracking &amp; Physiological Measures.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "Jittrapol Intarasirisawat", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The paper introduces a multimodal affective dataset named VREED (VR Eyes: Emotions Dataset) in which emotions were triggered using immersive 360° Video-Based Virtual Environments (360-VEs) delivered via Virtual Reality (VR) headset. Behavioural (eye tracking) and physiological signals (Electrocardiogram (ECG) and Galvanic Skin Response (GSR)) were captured, together with self-reported responses, from healthy participants (n=34) experiencing 360-VEs (n=12, 1--3 min each) selected through focus groups and a pilot trial. Statistical analysis confirmed the validity of the selected 360-VEs in eliciting the desired emotions. Preliminary machine learning analysis was carried out, demonstrating state-of-the-art performance reported in affective computing literature using non-immersive modalities. VREED is among the first multimodal VR datasets in emotion recognition using behavioural and physiological signals. VREED is made publicly available on Kaggle1. We hope that this contribution encourages other researchers to utilise VREED further to understand emotional responses in VR and ultimately enhance VR experiences design in applications where emotional elicitation plays a key role, i.e. healthcare, gaming, education, etc.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3495002"}, {"primary_key": "1961748", "vector": [], "sparse_vector": [], "title": "Twin Meander Coil: Sensitive Readout of Battery-free On-body Wireless Sensors Using Body-scale Meander Coils.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Energy-efficient and unconstrained wearable sensing platforms are essential for ubiquitous healthcare and activity monitoring applications. This paper presents Twin Meander Coil for wirelessly connecting battery-free on-body sensors to a textile-based reader knitted into clothing. This connection is based on passive inductive telemetry (PIT), wherein an external reader coil collects data from passive sensor coils via the magnetic field. In contrast to standard active sensing techniques, PIT does not require the reader to power up the sensors. Thus, the reader can be fabricated using a lossy conductive thread and industrial knitting machines. Furthermore, the sensors can superimpose information such as ID, touch, rotation, and pressure on its frequency response. However, conventional PIT technology needs a strong coupling between the reader and the sensor, requiring the reader to be small to the same extent as the sensors' size. Thus, applying this technology to body-scale sensing systems is challenging. To enable body-scale readout, Twin Meander Coil enhances the sensitivity of PIT technology by dividing the body-scale meander-shaped reader coils into two parts and integrating them so that they support the readout of each other. To demonstrate its feasibility, we built a prototype with a knitting machine, evaluated its sensing ability, and demonstrated several applications.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494996"}, {"primary_key": "1961750", "vector": [], "sparse_vector": [], "title": "SelfHAR: Improving Human Activity Recognition through Self-training with Unlabeled Data.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Machine learning and deep learning have shown great promise in mobile sensing applications, including Human Activity Recognition. However, the performance of such models in real-world settings largely depends on the availability of large datasets that captures diverse behaviors. Recently, studies in computer vision and natural language processing have shown that leveraging massive amounts of unlabeled data enables performance on par with state-of-the-art supervised models. In this work, we present SelfHAR, a semi-supervised model that effectively learns to leverage unlabeled mobile sensing datasets to complement small labeled datasets. Our approach combines teacher-student self-training, which distills the knowledge of unlabeled and labeled datasets while allowing for data augmentation, and multi-task self-supervision, which learns robust signal-level representations by predicting distorted versions of the input. We evaluated SelfHAR on various HAR datasets and showed state-of-the-art performance over supervised and previous semi-supervised approaches, with up to 12% increase in F1 score using the same number of model parameters at inference. Furthermore, SelfHAR is data-efficient, reaching similar performance using up to 10 times less labeled data compared to supervised approaches. Our work not only achieves state-of-the-art performance in a diverse set of HAR datasets, but also sheds light on how pre-training tasks may affect downstream performance.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448112"}, {"primary_key": "1961751", "vector": [], "sparse_vector": [], "title": "Zero-Shot Learning for IMU-Based Activity Recognition Using Video Embeddings.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The Activity Recognition Chain generally precludes the challenging scenario of recognizing new activities that were unseen during training, despite this scenario being a practical and common one as users perform diverse activities at test time. A few prior works have adopted zero-shot learning methods for IMU-based activity recognition, which work by relating seen and unseen classes through an auxiliary semantic space. However, these methods usually rely heavily on a hand-crafted attribute space which is costly to define, or a learnt semantic space based on word embedding, which lacks motion-related information crucial for distinguishing IMU features. Instead, we propose a strategy to exploit videos of human activities to construct an informative semantic space. With our approach, knowledge from state-of-the-art video action recognition models is encoded into video embeddings to relate seen and unseen activity classes. Experiments on three public datasets find that our approach outperforms other learnt semantic spaces, with an additional desirable feature of scalability, as recognition performance is seen to scale with the amount of data used. More generally, our results indicate that exploiting information from the video domain for IMU-based tasks is a promising direction, with tangible returns in a zero-shot learning scenario.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494995"}, {"primary_key": "1961752", "vector": [], "sparse_vector": [], "title": "WiFiTrace: Network-based Contact Tracing for Infectious Diseases Using Passive WiFi Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Contact tracing is a well-established and effective approach for the containment of the spread of infectious diseases. While Bluetooth-based contact tracing method using phones has become popular recently, these approaches suffer from the need for a critical mass adoption to be effective. In this paper, we present WiFiTrace, a network-centric approach for contact tracing that relies on passive WiFi sensing with no client-side involvement. Our approach exploits WiFi network logs gathered by enterprise networks for performance and security monitoring, and utilizes them for reconstructing device trajectories for contact tracing. Our approach is specifically designed to enhance the efficacy of traditional methods, rather than to supplant them with new technology. We designed an efficient graph algorithm to scale our approach to large networks with tens of thousands of users. The graph-based approach outperforms an indexed PostgresSQL in memory by at least 4.5X without any index update overheads or blocking. We have implemented a full prototype of our system and deployed it on two large university campuses. We validated our approach and demonstrate its efficacy using case studies and detailed experiments using real-world WiFi datasets.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448084"}, {"primary_key": "1961753", "vector": [], "sparse_vector": [], "title": "Are Those Steps Worth Your Privacy?: Fitness-Tracker Users&apos; Perceptions of Privacy and Utility.", "authors": ["Lev <PERSON>ely<PERSON>ko", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Fitness trackers are increasingly popular. The data they collect provides substantial benefits to their users, but it also creates privacy risks. In this work, we investigate how fitness-tracker users perceive the utility of the features they provide and the associated privacy-inference risks. We conduct a longitudinal study composed of a four-month period of fitness-tracker use (N = 227), followed by an online survey (N = 227) and interviews (N = 19). We assess the users' knowledge of concrete privacy threats that fitness-tracker users are exposed to (as demonstrated by previous work), possible privacy-preserving actions users can take, and perceptions of utility of the features provided by the fitness trackers. We study the potential for data minimization and the users' mental models of how the fitness tracking ecosystem works. Our findings show that the participants are aware that some types of information might be inferred from the data collected by the fitness trackers. For instance, the participants correctly guessed that sexual activity could be inferred from heart-rate data. However, the participants did not realize that also the non-physiological information could be inferred from the data. Our findings demonstrate a high potential for data minimization, either by processing data locally or by decreasing the temporal granularity of the data sent to the service provider. Furthermore, we identify the participants' lack of understanding and common misconceptions about how the Fitbit ecosystem works.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494960"}, {"primary_key": "1961754", "vector": [], "sparse_vector": [], "title": "ExpressEar: Sensing Fine-Grained Facial Expressions with Earables.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Continuous and unobtrusive monitoring of facial expressions holds tremendous potential to enable compelling applications in a multitude of domains ranging from healthcare and education to interactive systems. Traditional, vision-based facial expression recognition (FER) methods, however, are vulnerable to external factors like occlusion and lighting, while also raising privacy concerns coupled with the impractical requirement of positioning the camera in front of the user at all times. To bridge this gap, we propose ExpressEar, a novel FER system that repurposes commercial earables augmented with inertial sensors to capture fine-grained facial muscle movements. Following the Facial Action Coding System (FACS), which encodes every possible expression in terms of constituent facial movements called Action Units (AUs), ExpressEar identifies facial expressions at the atomic level. We conducted a user study (N=12) to evaluate the performance of our approach and found that ExpressEar can detect and distinguish between 32 Facial AUs (including 2 variants of asymmetric AUs), with an average accuracy of 89.9% for any given user. We further quantify the performance across different mobile scenarios in presence of additional face-related activities. Our results demonstrate ExpressEar's applicability in the real world and open up research opportunities to advance its practical adoption.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478085"}, {"primary_key": "1961755", "vector": [], "sparse_vector": [], "title": "Exploration of Person-Independent BCIs for Internal and External Attention-Detection in Augmented Reality.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Adding attention-awareness to an Augmented Reality setting by using a Brain-Computer Interface promises many interesting new applications and improved usability. The possibly complicated setup and relatively long training period of EEG-based BCIs however, reduce this positive effect immensely. In this study, we aim at finding solutions for person-independent, training-free BCI integration into AR to classify internally and externally directed attention. We assessed several different classifier settings on a dataset of 14 participants consisting of simultaneously recorded EEG and eye tracking data. For this, we compared the classification accuracies of a linear algorithm, a non-linear algorithm, and a neural net that were trained on a specifically generated feature set, as well as a shallow neural net for raw EEG data. With a real-time system in mind, we also tested different window lengths of the data aiming at the best payoff between short window length and high classification accuracy. Our results showed that the shallow neural net based on 4-second raw EEG data windows was best suited for real-time person-independent classification. The accuracy for the binary classification of internal and external attention periods reached up to 88% accuracy with a model that was trained on a set of selected participants. On average, the person-independent classification rate reached 60%. Overall, the high individual differences could be seen in the results. In the future, further datasets are necessary to compare these results before optimizing a real-time person-independent attention classifier for AR.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463507"}, {"primary_key": "1961756", "vector": [], "sparse_vector": [], "title": "Ride Substitution Using Electric Bike Sharing: Feasibility, Cost, and Carbon Analysis.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While ride-sharing has emerged as a popular form of transportation in urban areas due to its on-demand convenience, it has become a major contributor to carbon emissions, with recent studies suggesting it is 47% more carbon-intensive than personal car trips. In this paper, we examine the feasibility, costs, and carbon benefits of using electric bike-sharing---a low carbon form of ride-sharing---as a potential substitute for shorter ride-sharing trips, with the overall goal of greening the ride-sharing ecosystem. Using public datasets from New York City, our analysis shows that nearly half of the taxi and rideshare trips in New York are shorts trips of less than 3.5km, and that biking is actually faster than using a car for ultra-short trips of 2km or less. We analyze the cost and carbon benefits of different levels of ride substitution under various scenarios. We find that the additional bikes required to satisfy increased demand from ride substitution increases sub-linearly and results in 6.6% carbon emission reduction for 10% taxi ride substitution. Moreover, this reduction can be achieved through a hybrid mix that requires only a quarter of the bikes to be electric bikes, which reduces system costs. We also find that expanding bike-share systems to new areas that lack bike-share coverage requires additional investments due to the need for new bike stations and bike capacity to satisfy demand but also provides substantial carbon emission reductions. Finally, frequent station repositioning can reduce the number of bikes needed in the system by up to a third for a minimal increase in carbon emissions of 2% from the trucks required to perform repositioning, providing an interesting tradeoff between capital costs and carbon emissions.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448081"}, {"primary_key": "1961757", "vector": [], "sparse_vector": [], "title": "Thru-the-wall Eavesdropping on Loudspeakers via RFID by Capturing Sub-mm Level Vibration.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The unprecedented success of speech recognition methods has stimulated the wide usage of intelligent audio systems, which provides new attack opportunities for stealing the user privacy through eavesdropping on the loudspeakers. Effective eavesdropping methods employ a high-speed camera, relying on LOS to measure object vibrations, or utilize WiFi MIMO antenna array, requiring to eavesdrop in quiet environments. In this paper, we explore the possibility of eavesdropping on the loudspeaker based on COTS RFID tags, which are prevalently deployed in many corners of our daily lives. We propose Tag-Bug that focuses on the human voice with complex frequency bands and performs the thru-the-wall eavesdropping on the loudspeaker by capturing sub-mm level vibration. Tag-Bug extracts sound characteristics through two means: (1) Vibration effect, where a tag directly vibrates caused by sounds; (2) Reflection effect, where a tag does not vibrate but senses the reflection signals from nearby vibrating objects. To amplify the influence of vibration signals, we design a new signal feature referred as Modulated Signal Difference (MSD) to reconstruct the sound from RF-signals. To improve the quality of the reconstructed sound for human voice recognition, we apply a Conditional Generative Adversarial Network (CGAN) to recover the full-frequency band from the partial-frequency band of the reconstructed sound. Extensive experiments on the USRP platform show that Tag-Bug can successfully capture the monotone sound when the loudness is larger than 60dB. Tag-Bug can efficiently recognize the numbers of human voice with 95.3%, 85.3% and 87.5% precision in the free-space eavesdropping, thru-the-brick-wall eavesdropping and thru-the-insulating-glass eavesdropping, respectively. Tag-Bug can also accurately recognize the letters with 87% precision in the free-space eavesdropping.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494975"}, {"primary_key": "1961758", "vector": [], "sparse_vector": [], "title": "Demystifying the Vetting Process of Voice-controlled Skills on Markets.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Smart speakers, such as Google Home and Amazon Echo, have become popular. They execute user voice commands via their built-in functionalities together with various third-party voice-controlled applications, called skills. Malicious skills have brought significant threats to users in terms of security and privacy. As a countermeasure, only skills passing the strict vetting process can be released onto markets. However, malicious skills have been reported to exist on markets, indicating that the vetting process can be bypassed. This paper aims to demystify the vetting process of skills on main markets to discover weaknesses and protect markets better. To probe the vetting process, we carefully design numerous skills, perform the Turing test, a test for machine intelligence, to determine whether humans or machines perform vetting, and leverage natural language processing techniques to analyze their behaviors. Based on our comprehensive experiments, we gain a good understanding of the vetting process (e.g., machine or human testers and skill exploration strategies) and discover some weaknesses. In this paper, we design three types of attacks to verify our results and prove an attacker can embed sensitive behaviors in skills and bypass the strict vetting process. Accordingly, we also propose countermeasures to these attacks and weaknesses.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478101"}, {"primary_key": "1961760", "vector": [], "sparse_vector": [], "title": "Context-aware Adaptive Surgery: A Fast and Effective Framework for Adaptative Model Partition.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deep Neural Networks (DNNs) have made massive progress in many fields and deploying DNNs on end devices has become an emerging trend to make intelligence closer to users. However, it is challenging to deploy large-scale and computation-intensive DNNs on resource-constrained end devices due to their small size and lightweight. To this end, model partition, which aims to partition DNNs into multiple parts to realize the collaborative computing of multiple devices, has received extensive research attention. To find the optimal partition, most existing approaches need to run from scratch under given resource constraints. However, they ignore that resources of devices (e.g., storage, battery power), and performance requirements (e.g., inference latency), are often continuously changing, making the optimal partition solution change constantly during processing. Therefore, it is very important to reduce the tuning latency of model partition to realize the real-time adaption under the changing processing context. To address these problems, we propose the Context-aware Adaptive Surgery (CAS) framework to actively perceive the changing processing context, and adaptively find the appropriate partition solution in real-time. Specifically, we construct the partition state graph to comprehensively model different partition solutions of DNNs by import context resources. Then \"the neighbor effect\" is proposed, which provides the heuristic rule for the search process. When the processing context changes, CAS adopts the runtime search algorithm, Graph-based Adaptive DNN Surgery (GADS), to quickly find the appropriate partition that satisfies resource constraints under the guidance of the neighbor effect. The experimental results show that CAS realizes adaptively rapid tuning of the model partition solutions in 10ms scale even for large DNNs (2.25x to 221.7x search time improvement than the state-of-the-art researches), and the total inference latency still keeps the same level with baselines.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478073"}, {"primary_key": "1961761", "vector": [], "sparse_vector": [], "title": "Leveraging Activity Recognition to Enable Protective Behavior Detection in Continuous Data.", "authors": ["Chongyang Wang", "Yuan Gao", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Protective behavior exhibited by people with chronic pain (CP) during physical activities is very informative to understanding their physical and emotional states. Existing automatic protective behavior detection (PBD) methods rely on pre-segmentation of activities predefined by users. However, in real life, people perform activities casually. Therefore, where those activities present difficulties for people with CP, technology-enabled support should be delivered continuously and automatically adapted to activity type and occurrence of protective behavior. Hence, to facilitate ubiquitous CP management, it becomes critical to enable accurate PBD over continuous data. In this paper, we propose to integrate human activity recognition (HAR) with PBD via a novel hierarchical HAR-PBD architecture comprising graph-convolution and long short-term memory (GC-LSTM) networks, and alleviate class imbalances using a class-balanced focal categorical cross-entropy (CFCC) loss. Through in-depth evaluation of the approach using a CP patients' dataset, we show that the leveraging of HAR, GC-LSTM networks, and CFCC loss leads to clear increase in PBD performance against the baseline (macro F1 score of 0.81 vs. 0.66 and precision-recall area-under-the-curve (PR-AUC) of 0.60 vs. 0.44). We conclude by discussing possible use cases of the hierarchical architecture in CP management and beyond. We also discuss current limitations and ways forward.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463508"}, {"primary_key": "1961764", "vector": [], "sparse_vector": [], "title": "Audio Keyword Reconstruction from On-Device Motion Sensor Signals via Neural Frequency Unfolding.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we present a novel deep neural network architecture that reconstructs the high-frequency audio of selected spoken human words from low-sampling-rate signals of (ego-)motion sensors, such as accelerometer and gyroscope data, recorded on everyday mobile devices. As the sampling rate of such motion sensors is much lower than the Nyquist rate of ordinary human voice (around 6kHz+), these motion sensor recordings suffer from a significant frequency aliasing effect. In order to recover the original high-frequency audio signal, our neural network introduces a novel layer, called the alias unfolding layer, specialized in expanding the bandwidth of an aliased signal by reversing the frequency folding process in the time-frequency domain. While perfect unfolding is known to be unrealizable, we leverage the sparsity of the original signal to arrive at a sufficiently accurate statistical approximation. Comprehensive experiments show that our neural network significantly outperforms the state of the art in audio reconstruction from motion sensor data, effectively reconstructing a pre-trained set of spoken keywords from low-frequency motion sensor signals (with a sampling rate of 100-400 Hz). The approach demonstrates the potential risk of information leakage from motion sensors in smart mobile devices.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478102"}, {"primary_key": "1961765", "vector": [], "sparse_vector": [], "title": "Watching Your Phone&apos;s Back: Gesture Recognition by Sensing Acoustical Structure-borne Propagation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "Gesture recognition on the back surface of mobile phone, not limited to the touch screen, is an enabling Human-Computer Interaction (HCI) mechanism which enriches the user interaction experiences. However, there are two main limitations in the existing Back-of-Device (BoD) gesture recognition systems. They can only handle coarse-grained gesture recognition such as tap detection and cannot avoid the air-borne propagation suffering from the interference in the air. In this paper, we propose StruGesture, a fine-grained gesture recognition system using the back of mobile phones with ultrasonic signals. The key technique is to use the structure-borne sounds (i.e., sound propagation via structure of the device) to recognize sliding gestures on the back of mobile phones. StruGesture can fully extract the structure-borne component from the hybrid Channel Impulse Response (CIR) based on Peak Selection Algorithm. We develop a deep adversarial learning architecture to learn the gesture-specific representation for robust and effective recognition. Extensive experiments are designed to evaluate the robustness over nine deployment scenarios. The results show that StruGesture outperforms the competitive state-of-the-art classifiers by achieving an average recognition accuracy of 99.5% over 10 gestures.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463522"}, {"primary_key": "1961766", "vector": [], "sparse_vector": [], "title": "Understanding User Perceptions of Proactive Smart Speakers.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Voice assistants, such as Amazon's Alexa and Google Home, increasingly find their way into consumer homes. Their functionality, however, is currently limited to being passive answer machines rather than proactively engaging users in conversations. Speakers' proactivity would open up a range of important application scenarios, including health services, such as checking in on patient states and triggering medication reminders. It remains unclear how passive speakers should implement proactivity. To better understand user perceptions, we ran a 3-week field study with 13 participants where we modified the off-the-shelf Google Home to become proactive. During the study, our speaker proactively triggered conversations that were essentially Experience Sampling probes allowing us to identify when to engage users. Applying machine-learning, we are able to predict user responsiveness with a 71.6% accuracy and find predictive features. We also identify self-reported factors, such as boredom and mood, that are significantly correlated with users' perceived availability. Our prototype and findings inform the design of proactive speakers that verbally engage users at opportune moments and contribute to the design of proactive application scenarios and voice-based experience sampling studies.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494965"}, {"primary_key": "1961767", "vector": [], "sparse_vector": [], "title": "Objective Measures of Cognitive Load Using Deep Multi-Modal Learning: A Use-Case in Aviation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The capability of measuring human performance objectively is hard to overstate, especially in the context of the instructor and student relationship within the process of learning. In this work, we investigate the automated classification of cognitive load leveraging the aviation domain as a surrogate for complex task workload induction. We use a mixed virtual and physical flight environment, given a suite of biometric sensors utilizing the HTC Vive Pro Eye and the E4 Empatica. We create and evaluate multiple models. And we have taken advantage of advancements in deep learning such as generative learning, multi-modal learning, multi-task learning, and x-vector architectures to classify multiple tasks across 40 subjects inclusive of three subject types --- pilots, operators, and novices. Our cognitive load model can automate the evaluation of cognitive load agnostic to subject, subject type, and flight maneuver (task) with an accuracy of over 80%. Further, this approach is validated with real-flight data from five test pilots collected over two test and evaluation flights on a C-17 aircraft.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448111"}, {"primary_key": "1961768", "vector": [], "sparse_vector": [], "title": "Learning When Agents Can Talk to Drivers Using the INAGT Dataset and Multisensor Fusion.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper examines sensor fusion techniques for modeling opportunities for proactive speech-based in-car interfaces. We leverage the Is Now a Good Time (INAGT) dataset, which consists of automotive, physiological, and visual data collected from drivers who self-annotated responses to the question \"Is now a good time?,\" indicating the opportunity to receive non-driving information during a 50-minute drive. We augment this original driver-annotated data with third-party annotations of perceived safety, in order to explore potential driver overconfidence. We show that fusing automotive, physiological, and visual data allows us to predict driver labels of availability, achieving an 0.874 F1-score by extracting statistically relevant features and training with our proposed deep neural network, PazNet. Using the same data and network, we achieve an 0.891 F1-score for predicting third-party labeled safe moments. We train these models to avoid false positives---determinations that it is a good time to interrupt when it is not---since false positives may cause driver distraction or service deactivation by the driver. Our analyses show that conservative models still leave many moments for interaction and show that most inopportune moments are short. This work lays a foundation for using sensor fusion models to predict when proactive speech systems should engage with drivers.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478125"}, {"primary_key": "1961769", "vector": [], "sparse_vector": [], "title": "Pushing the Limits of Long Range Wireless Sensing with LoRa.", "authors": ["<PERSON><PERSON>", "Yuqing Yin", "<PERSON><PERSON>"], "summary": "Wireless sensing is an exciting new research area which enables a large variety of applications ranging from coarse-grained daily activity recognition to fine-grained vital sign monitoring. While promising in many aspects, one critical issue is the limited sensing range because weak reflection signals are used for sensing. Recently, LoRa signals are exploited for wireless sensing, moving a big step towards long-range sensing. Although promising, there is still a huge room for improvement. In this work, we qualitatively characterize the relationship between target movements and target-induced signal variations, and propose signal processing methods to enlarge the induced signal variation to achieve a longer sensing range. Experiment results show that the proposed system (1) pushes the contact-free sensing range of human walking from the state-of-the-art 50 m to 120 m; (2) achieves a sensing range of 75 m for fine-grained respiration sensing; and (3) demonstrates human respiration sensing even through seven concrete walls.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478080"}, {"primary_key": "1961770", "vector": [], "sparse_vector": [], "title": "Acoustic-based Upper Facial Action Recognition for Smart Eyewear.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Smart eyewear (e.g., AR glasses) is considered to be the next big breakthrough for wearable devices. The interaction of state-of-the-art smart eyewear mostly relies on the touchpad which is obtrusive and not user-friendly. In this work, we propose a novel acoustic-based upper facial action (UFA) recognition system that serves as a hands-free interaction mechanism for smart eyewear. The proposed system is a glass-mounted acoustic sensing system with several pairs of commercial speakers and microphones to sense UFAs. There are two main challenges in designing the system. The first challenge is that the system is in a severe multipath environment and the received signal could have large attenuation due to the frequency-selective fading which will degrade the system's performance. To overcome this challenge, we design an Orthogonal Frequency Division Multiplexing (OFDM)-based channel state information (CSI) estimation scheme that is able to measure the phase changes caused by a facial action while mitigating the frequency-selective fading. The second challenge is that because the skin deformation caused by a facial action is tiny, the received signal has very small variations. Thus, it is hard to derive useful information directly from the received signal. To resolve this challenge, we apply a time-frequency analysis to derive the time-frequency domain signal from the CSI. We show that the derived time-frequency domain signal contains distinct patterns for different UFAs. Furthermore, we design a Convolutional Neural Network (CNN) to extract high-level features from the time-frequency patterns and classify the features into six UFAs, namely, cheek-raiser, brow-raiser, brow-lower, wink, blink and neutral. We evaluate the performance of our system through experiments on data collected from 26 subjects. The experimental result shows that our system can recognize the six UFAs with an average F1-score of 0.92.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448105"}, {"primary_key": "1961771", "vector": [], "sparse_vector": [], "title": "Leveraging Collaborative-Filtering for Personalized Behavior Modeling: A Case Study of Depression Detection among College Students.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Daniella K. <PERSON>ba", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The prevalence of mobile phones and wearable devices enables the passive capturing and modeling of human behavior at an unprecedented resolution and scale. Past research has demonstrated the capability of mobile sensing to model aspects of physical health, mental health, education, and work performance, etc. However, most of the algorithms and models proposed in previous work follow a one-size-fits-all (i.e., population modeling) approach that looks for common behaviors amongst all users, disregarding the fact that individuals can behave very differently, resulting in reduced model performance. Further, black-box models are often used that do not allow for interpretability and human behavior understanding. We present a new method to address the problems of personalized behavior classification and interpretability, and apply it to depression detection among college students. Inspired by the idea of collaborative-filtering, our method is a type of memory-based learning algorithm. It leverages the relevance of mobile-sensed behavior features among individuals to calculate personalized relevance weights, which are used to impute missing data and select features according to a specific modeling goal (e.g., whether the student has depressive symptoms) in different time epochs, i.e., times of the day and days of the week. It then compiles features from epochs using majority voting to obtain the final prediction. We apply our algorithm on a depression detection dataset collected from first-year college students with low data-missing rates and show that our method outperforms the state-of-the-art machine learning model by 5.1% in accuracy and 5.5% in F1 score. We further verify the pipeline-level generalizability of our approach by achieving similar results on a second dataset, with an average improvement of 3.4% across performance metrics. Beyond achieving better classification performance, our novel approach is further able to generate personalized interpretations of the models for each individual. These interpretations are supported by existing depression-related literature and can potentially inspire automated and personalized depression intervention design in the future.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448107"}, {"primary_key": "1961772", "vector": [], "sparse_vector": [], "title": "CardiacWave: A mmWave-based Scheme of Non-Contact and High-Definition Heart Activity Computing.", "authors": ["<PERSON><PERSON>", "Huining Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xingyu Chen", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Using wireless signals to monitor human vital signs, especially heartbeat information, has been intensively studied in the past decade. This non-contact sensing modality can drive various applications from cardiac health, sleep, and emotion management. Under the circumstance of the COVID-19 pandemic, non-contact heart monitoring receives increasingly market demands. However, existing wireless heart monitoring schemes can only detect limited heart activities, such as heart rate, fiducial points, and Seismocardiography (SCG)-like information. In this paper, we present CardiacWave to enable a non-contact high-definition heart monitoring. CardiacWave can provide a full spectrum of Electrocardiogram (ECG)-like heart activities, including the details of P-wave, T-wave, and QRS complex. Specifically, CardiacWave is built upon the Cardiac-mmWave scattering effect (CaSE), which is a variable frequency response of the cardiac electromagnetic field under the mmWave interrogation. The CardiacWave design consists of a noise-resistant sensing scheme to interrogate CaSE and a cardiac activity profiling module for extracting cardiac electrical activities from the interrogation response. Our experiments show that the CardiacWave-induced ECG measures have a high positive correlation with the heart activity ground truth (i.e., measurements from a medical-grade instrument). The timing difference of P-waves, T-waves, and QRS complex is 0.67%, 0.71%, and 0.49%, respectively, and a mean cardiac event difference is within a delay of 5.3 milliseconds. These results indicate that CaridacWave offers high-fidelity and integral heart clinical characteristics. Furthermore, we evaluate the CardiacWave system with participants under various conditions, including heart and breath rates, ages, and heart habits (e.g., tobacco use).", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478127"}, {"primary_key": "1961773", "vector": [], "sparse_vector": [], "title": "Listen2Cough: Leveraging End-to-End Deep Learning Cough Detection Model to Enhance Lung Health Assessment Using Passively Sensed Audio.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The prevalence of ubiquitous computing enables new opportunities for lung health monitoring and assessment. In the past few years, there have been extensive studies on cough detection using passively sensed audio signals. However, the generalizability of a cough detection model when applied to external datasets, especially in real-world implementation, is questionable and not explored adequately. Beyond detecting coughs, researchers have looked into how cough sounds can be used in assessing lung health. However, due to the challenges in collecting both cough sounds and lung health condition ground truth, previous studies have been hindered by the limited datasets. In this paper, we propose Listen2Cough to address these gaps. We first build an end-to-end deep learning architecture using public cough sound datasets to detect coughs within raw audio recordings. We employ a pre-trained MobileNet and integrate a number of augmentation techniques to improve the generalizability of our model. Without additional fine-tuning, our model is able to achieve an F1 score of 0.948 when tested against a new clean dataset, and 0.884 on another in-the-wild noisy dataset, leading to an advantage of 5.8% and 8.4% on average over the best baseline model, respectively. Then, to mitigate the issue of limited lung health data, we propose to transform the cough detection task to lung health assessment tasks so that the rich cough data can be leveraged. Our hypothesis is that these tasks extract and utilize similar effective representation from cough sounds. We embed the cough detection model into a multi-instance learning framework with the attention mechanism and further tune the model for lung health assessment tasks. Our final model achieves an F1-score of 0.912 on healthy v.s. unhealthy, 0.870 on obstructive v.s. non-obstructive, and 0.813 on COPD v.s. asthma classification, outperforming the baseline by 10.7%, 6.3%, and 3.7%, respectively. Moreover, the weight value in the attention layer can be used to identify important coughs highly correlated with lung health, which can potentially provide interpretability for expert diagnosis in the future.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448124"}, {"primary_key": "1961774", "vector": [], "sparse_vector": [], "title": "Embracing Collisions to Increase Fidelity of Sensing Systems with COTS Tags.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "RFID techniques have been extensively used in sensing systems due to their low cost. However, limited by the structural simplicity, collision is one key issue which is inevitable in RFID systems, thus limiting the accuracy and scalability of such sensing systems. Existing anti-collision techniques try to enable parallel decoding without sensing based applications in mind, which can not operate on COTS RFID systems. To address the issue, we propose COFFEE, which enables parallel channel estimation of COTS passive tags by harnessing the collision. We revisit the physical layer design of current standard. By exploiting the characteristics of low sampling rate and channel diversity of RFID tags, we separate the collided data and extract the channels of the collided tags. We also propose a tag identification algorithm which explores history channel information and identify the tags without decoding. COFFEE is compatible with current COTS RFID standards which can be applied to all RFID-based sensing systems without any modification on tag side. To evaluate the real world performance of our system, we build a prototype and conduct extensive experiments. The experimental results show that we can achieve up to 7.33x median time resolution gain for the best case and 3.42x median gain on average.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463497"}, {"primary_key": "1961777", "vector": [], "sparse_vector": [], "title": "LightGuide: Directing Visually Impaired People along a Path Using Light Cues.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Xu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Precise and reliable directional feedback is crucial for electronic traveling aids that guide visually impaired people along safe paths. A large proportion of visually impaired people can determine light position using their light perception. This work presents LightGuide, a directional feedback solution that indicates a safe direction of travel via the position of a light within the user's visual field. We prototyped LightGuide using an LED strip attached to the brim of a cap, and conducted three user studies to explore the effectiveness of LightGuide compared to HapticBag, a state-of-the-art baseline solution that indicates directions through on-shoulder vibrations. Results showed that, with LightGuide, participants turned to target directions in place more quickly and smoothly, and navigated along basic and complex paths more efficiently, smoothly, and accurately than HapticBag. Users' subjective feedback implied that LightGuide was easy to learn and intuitive to use. The potential limitations of using LightGuide in real environments are subsequently discussed.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463524"}, {"primary_key": "1961778", "vector": [], "sparse_vector": [], "title": "Model-based Head Orientation Estimation for Smart Devices.", "authors": ["<PERSON><PERSON>", "Yuan<PERSON> Zheng"], "summary": "Voice interaction is friendly and convenient for users. Smart devices such as Amazon Echo allow users to interact with them by voice commands and become increasingly popular in our daily life. In recent years, research works focus on using the microphone array built in smart devices to localize the user's position, which adds additional context information to voice commands. In contrast, few works explore the user's head orientation, which also contains useful context information. For example, when a user says, \"turn on the light\", the head orientation could infer which light the user is referring to. Existing model-based works require a large number of microphone arrays to form an array network, while machine learning-based approaches need laborious data collection and training workload. The high deployment/usage cost of these methods is unfriendly to users. In this paper, we propose HOE, a model-based system that enables Head Orientation Estimation for smart devices with only two microphone arrays, which requires a lower training overhead than previous approaches. HOE first estimates the user's head orientation candidates by measuring the voice energy radiation pattern. Then, the voice frequency radiation pattern is leveraged to obtain the final result. Real-world experiments are conducted, and the results show that HOE can achieve a median estimation error of 23 degrees. To the best of our knowledge, HOE is the first model-based attempt to estimate the head orientation by only two microphone arrays without the arduous data training overhead.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478089"}, {"primary_key": "1961779", "vector": [], "sparse_vector": [], "title": "Context-Aware Compilation of DNN Training Pipelines across Edge and Cloud.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Empowered by machine learning, edge devices including smartphones, wearable, and IoT devices have become growingly intelligent, raising conflicts with the limited resource. On-device model personalization is particularly hard as training models on edge devices is highly resource-intensive. In this work, we propose a novel training pipeline across the edge and the cloud, by taking advantage of the powerful cloud while keeping data local at the edge. Highlights of the design incorporate the parallel execution enabled by our feature replay, reduced communication cost by our error-feedback feature compression, as well as the context-aware deployment decision engine. Working as an integrated system, the proposed pipeline training framework not only significantly speeds up training, but also incurs little accuracy loss or additional memory/energy overhead. We test our system in a variety of settings including WiFi, 5G, household IoT, and on different training tasks such as image/text classification, image generation, to demonstrate its advantage over the state-of-the-art. Experimental results show that our system not only adapts well to, but also draws on the varying contexts, delivering a practical and efficient solution to edge-cloud model training.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494981"}, {"primary_key": "1961780", "vector": [], "sparse_vector": [], "title": "KATN: Key Activity Detection via Inexact Supervised Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Leveraging sensor data of mobile devices and wearables, activity detection is a critical task in various intelligent systems. Most recent work train deep models to improve the accuracy of recognizing specific human activities, which, however, rely on specially collected and accurately labeled sensor data. It is labor-intensive and time-consuming to collect and label large-scale sensor data that cover various people, mobile devices, and environments. In production scenarios, on the one hand, the lack of accurately labeled sensor data poses significant challenges to the detection of key activities; on the other hand, massive continuously generated sensor data attached with inexact information is severely underutilized. For example, in an on-demand food delivery system, detecting the key activity that the rider gets off his/her motorcycle to hand food over to the customer is essential for predicting the exact delivery time. Nevertheless, the system has only the raw sensor data and the clicking \"finish delivery\" events, which are highly relevant to the key activity but very inexact, since different riders may click \"finish delivery\" at any time in the last-mile delivery. Without exact labels of key activities, in this work, we propose a system, named KATN, to detect the exact regions of key activities based on inexact supervised learning. We design a novel siamese key activity attention network (SAN) to learn both discriminative and detailed sequential features of the key activity under the supervision of inexact labels. By interpreting the behaviors of SAN, an exact time estimation method is devised. We also provide a personal adaptation mechanism to cope with diverse habits of users. Extensive experiments on both public datasets and data from a real-world food delivery system testify the significant advantages of our design. Furthermore, based on KATN, we propose a novel user-friendly annotation mechanism to facilitate the annotation of large-scale sensor data for a wide range of applications.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494957"}, {"primary_key": "1961781", "vector": [], "sparse_vector": [], "title": "Exploring Multiple Antennas for Long-range WiFi Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Zhang"], "summary": "Despite extensive research effort on contactless WiFi sensing over the past few years, there are still significant barriers hindering its wide application. One key issue is the limited sensing range due to the intrinsic nature of employing the weak target-reflected signal for sensing and therefore the sensing range is much smaller than the communication range. In this work, we address this challenging issue, moving WiFi sensing one step closer to real-world adoption. The key idea is to effectively utilize the multiple antennas widely available on commodity WiFi access points to simultaneously strengthen the target-reflected signal and reduce the noise. Although traditional beamforming schemes can help increase the signal strength, they are designed for communication and can not be directly applied to benefit sensing. To effectively increase the WiFi sensing range using multiple antennas, we first propose a new metric that quantifies the signal sensing capability. We then propose novel signal processing methods, which lay the theoretical foundation to support beamforming-based long-range WiFi sensing. To validate the proposed idea, we develop two sensing applications: fine-grained human respiration monitoring and coarse-grained human walking tracking. Extensive experiments show that: (i) the human respiration sensing range is significantly increased from the state-of-the-art 6-8 m to 11 m;1 and (ii) human walking can be accurately tracked even when the target is 18 m away from the WiFi transceivers, outperforming the sensing range of the state-of-the-art by 50%.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494979"}, {"primary_key": "1961783", "vector": [], "sparse_vector": [], "title": "SpeeChin: A Smart Necklace for Silent Speech Recognition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Chen", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hyunchul Lim", "<PERSON>"], "summary": "This paper presents SpeeChin, a smart necklace that can recognize 54 English and 44 Chinese silent speech commands. A customized infrared (IR) imaging system is mounted on a necklace to capture images of the neck and face from under the chin. These images are first pre-processed and then deep learned by an end-to-end deep convolutional-recurrent-neural-network (CRNN) model to infer different silent speech commands. A user study with 20 participants (10 participants for each language) showed that SpeeChin could recognize 54 English and 44 Chinese silent speech commands with average cross-session accuracies of 90.5% and 91.6%, respectively. To further investigate the potential of <PERSON><PERSON><PERSON><PERSON><PERSON> in recognizing other silent speech commands, we conducted another study with 10 participants distinguishing between 72 one-syllable nonwords. Based on the results from the user studies, we further discuss the challenges and opportunities of deploying SpeeChin in real-world applications.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3494987"}, {"primary_key": "1961784", "vector": [], "sparse_vector": [], "title": "Unlocking the Beamforming Potential of LoRa for Long-range Multi-target Respiration Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "Despite extensive research effort in contact-free sensing using RF signals in the last few years, there still exist significant barriers preventing their wide adoptions. One key issue is the inability to sense multiple targets due to the intrinsic nature of relying on reflection signals for sensing: the reflections from multiple targets get mixed at the receiver and it is extremely difficult to separate these signals to sense each individual. This problem becomes even more severe in long-range LoRa sensing because the sensing range is much larger compared to WiFi and acoustic based sensing. In this work, we address the challenging multi-target sensing issue, moving LoRa sensing one big step towards practical adoption. The key idea is to effectively utilize multiple antennas at the LoRa gateway to enable spatial beamforming to support multi-target sensing. While traditional beamforming methods adopted in WiFi and Radar systems rely on accurate channel information or transmitter-receiver synchronization, these requirements can not be satisfied in LoRa systems: the transmitter and receiver are not synchronized and no channel state information can be obtained from the cheap LoRa nodes. Another interesting observation is that while beamforming helps to increase signal strength, the phase/amplitude information which is critical for sensing can get corrupted during the beamforming process, eventually compromising the sensing capability. In this paper, we propose novel signal processing methods to address the issues above to enable long-range multi-target reparation sensing with LoRa. Extensive experiments show that our system can monitor the respiration rates of five human targets simultaneously at an average accuracy of 98.1%.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463526"}, {"primary_key": "1961785", "vector": [], "sparse_vector": [], "title": "A Feature Adaptive Learning Method for High-Density sEMG-Based Gesture Recognition.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Surface electromyography (sEMG) array based gesture recognition, which is widely-used, could provide natural surfaces for human-computer interaction. Currently, most existing gesture recognition methods with sEMG array only work with the fixed and pre-defined electrodes configuration. However, changes in the number of electrodes (i.e., increment or decrement) is common in real scenarios due to the variability of physiological electrodes. In this paper, we study this challenging problem and propose a random forest based ensemble learning method, namely feature incremental and decremental ensemble learning (FIDE). FIDE is able to support continuous changes in the number of electrodes by dynamically maintaining the matrix sketches of every sEMG electrode and spatial structure of sEMG array. To evaluate the performance of FIDE, we conduct extensive experiments on three benchmark datasets, including NinaPro, CSL-hdemg, and CapgMyo. Experimental results demonstrate that FIDE outperforms other state-of-the-art methods and has the potential to adapt to the evolution of electrodes in the changing environments. Moreover, based on FIDE, we implement a multi clients/server collaboration system, namely McS, to support feature adaption in real-world environment. By collecting sEMG using two clients (smartphone and personal computer) and adaptively recognizing gestures in the cloud server, FIDE significantly improves the gesture recognition accuracy in electrode increment and decrement circumstances.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3448114"}, {"primary_key": "1961787", "vector": [], "sparse_vector": [], "title": "Write, Attend and Spell: Streaming End-to-end Free-style Handwriting Recognition Using Smartwatches.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Text entry on a smartwatch is challenging due to its small form factor. Handwriting recognition using the built-in sensors of the watch (motion sensors, microphones, etc.) provides an efficient and natural solution to deal with this issue. However, prior works mainly focus on individual letter recognition rather than word recognition. Therefore, they need users to pause between adjacent letters for segmentation, which is counter-intuitive and significantly decreases the input speed. In this paper, we present 'Write, Attend and Spell' (WriteAS), a word-level text-entry system which enables free-style handwriting recognition using the motion signals of the smartwatch. First, we design a multimodal convolutional neural network (CNN) to abstract motion features across modalities. After that, a stacked dilated convolutional network with an encoder-decoder network is applied to get around letter segmentation and output words in an end-to-end way. More importantly, we leverage a multi-task sequence learning method to enable handwriting recognition in a streaming way. We construct the first sequence-to-sequence handwriting dataset using smartwatch. WriteAS can yield 9.3% character error rate (CER) on 250 words for new users and 3.8% CER for words unseen in the training set. In addition, WriteAS can handle various writing conditions very well. Given the promising performance, we envision that WriteAS can be a fast and accurate input tool for smartwatch.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478100"}, {"primary_key": "1961791", "vector": [], "sparse_vector": [], "title": "Towards Early Detection and Burden Estimation of Atrial Fibrillation in an Ambulatory Free-living Environment.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Early detection and accurate burden estimation of atrial fibrillation (AFib) can provide the foundation for effective physician treatment. New approaches to accomplish this have attracted tremendous attention in recent years. In this paper, we develop a novel passive smartwatch-based system to detect AFib episodes and estimate the AFib burden in an ambulatory free-living environment without user engagement. Our system leverages a built-in PPG sensor to collect heart rhythm without user engagement. Then, a data preprocessor module includes time-frequency (TF) analysis to augment features in both the time and frequency domain. Finally, a lightweight multi-view convolutional neural network consisting of 19 layers achieves the AFib detection. To validate our system, we carry out a research study that enrolls 53 participants across three months, where we collect and annotate more than 27,622 hours of data. Our system achieves an average of 91.6% accuracy, 93.0% specificity, and 90.8% sensitivity without dropping any data. Moreover, our system takes 0.51 million parameters and costs 5.18 ms per inference. These results reveal that our proposed system can provide a clinical assessment of AFib in daily living.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3463503"}, {"primary_key": "1961792", "vector": [], "sparse_vector": [], "title": "ULoc: Low-Power, Scalable and cm-Accurate UWB-Tag Localization and Tracking for Indoor Applications.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A myriad of IoT applications, ranging from tracking assets in hospitals, logistics, and construction industries to indoor tracking in large indoor spaces, demand centimeter-accurate localization that is robust to blockages from hands, furniture, or other occlusions in the environment. With this need, in the recent past, Ultra Wide Band (UWB) based localization and tracking has become popular. Its popularity is driven by its proposed high bandwidth and protocol specifically designed for localization of specialized \"tags\". This high bandwidth of UWB provides a fine resolution of the time-of-travel of the signal that can be translated to the location of the tag with centimeter-grade accuracy in a controlled environment. Unfortunately, we find that high latency and high-power consumption of these time-of-travel methods are the major culprits which prevent such a system from deploying multiple tags in the environment. Thus, we developed ULoc, a scalable, low-power, and cm-accurate UWB localization and tracking system. In ULoc, we custom build a multi-antenna UWB anchor that enables azimuth and polar angle of arrival (henceforth shortened to '3D-AoA') measurements, with just the reception of a single packet from the tag. By combining multiple UWB anchors, ULoc can localize the tag in 3D space. The single-packet location estimation reduces the latency of the entire system by at least 3×, as compared with state of art multi-packet UWB localization protocols, making UWB based localization scalable. ULoc's design also reduces the power consumption per location estimate at the tag by 9×, as compared to state-of-art time-of-travel algorithms. We further develop a novel 3D-AoA based 3D localization that shows a stationary localization accuracy of 3.6 cm which is 1.8× better than the state-of-the-art two-way ranging (TWR) systems. We further developed a temporal tracking system that achieves a tracking accuracy of 10 cm in mobile conditions which is 4.3× better than the state-of-the-art TWR systems.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478124"}, {"primary_key": "1961793", "vector": [], "sparse_vector": [], "title": "Anti-Spoofing Voice Commands: A Generic Wireless Assisted Design.", "authors": ["<PERSON><PERSON>", "Zhenjiang Li", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents an anti-spoofing design to verify whether a voice command is spoken by one live legal user, which supplements existing speech recognition systems and could enable new application potentials when many crucial voice commands need a higher-standard verification in applications. In the literature, verifying the liveness and legality of the command's speaker has been studied separately. However, to accept a voice command from a live legal user, prior solutions cannot be combined directly due to two reasons. First, previous methods have introduced various sensing channels for the liveness detection, while the safety of a sensing channel itself cannot be guaranteed. Second, a direct combination is also vulnerable when an attacker plays a recorded voice command from the legal user and mimics this user to speak the command simultaneously. In this paper, we introduce an anti-spoofing sensing channel to fulfill the design. More importantly, our design provides a generic interface to form the sensing channel, which is compatible to a variety of widely-used signals, including RFID, Wi-Fi and acoustic signals. This offers a flexibility to balance the system cost and verification requirement. We develop a prototype system with three versions by using these sensing signals. We conduct extensive experiments in six different real-world environments under a variety of settings to examine the effectiveness of our design.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478116"}, {"primary_key": "1961794", "vector": [], "sparse_vector": [], "title": "MetaTP: Traffic Prediction with Unevenly-Distributed Road Sensing Data via Fast Adaptation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Lu <PERSON>"], "summary": "With the popularity of smartphones, large-scale road sensing data is being collected to perform traffic prediction, which is an important task in modern society. Due to the nature of the roving sensors on smartphones, the collected traffic data which is in the form of multivariate time series, is often temporally sparse and unevenly distributed across regions. Moreover, different regions can have different traffic patterns, which makes it challenging to adapt models learned from regions with sufficient training data to target regions. Given that many regions may have very sparse data, it is also impossible to build individual models for each region separately. In this paper, we propose a meta-learning based framework named MetaTP to overcome these challenges. MetaTP has two key parts, i.e., basic traffic prediction network (base model) and meta-knowledge transfer. In base model, a two-layer interpolation network is employed to map original time series onto uniformly-spaced reference time points, so that temporal prediction can be effectively performed in the reference space. The meta-learning framework is employed to transfer knowledge from source regions with a large amount of data to target regions with a few data examples via fast adaptation, in order to improve model generalizability on target regions. Moreover, we use two memory networks to capture the global patterns of spatial and temporal information across regions. We evaluate the proposed framework on two real-world datasets, and experimental results show the effectiveness of the proposed framework.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478083"}, {"primary_key": "1961795", "vector": [], "sparse_vector": [], "title": "The Crowd Wisdom for Location Privacy of Crowdsensing Photos: Spear or Shield?", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The incorporation of the mobile crowd in visual sensing provides a significant opportunity to explore and understand uncharted physical places. We investigate the gains and losses of the involvement of the crowd wisdom on users' location privacy in photo crowdsensing. For the negative effects, we design a novel crowdsensing photo location inference model, regardless of the robust location protection techniques, by jointly exploiting the visual representation, correlation, and geo-annotation capabilities extracted from the crowd. Compared with existing retrieval-based and model-based location inference techniques, our proposal poses more pernicious threats to location privacy by considering the no-reference-photos situations of crowdsensing. We conduct extensive analyses on the model with four photo datasets and crowdsourcing surveys for geo-annotation. The results indicate that being in a crowd of photos will, unfortunately, increase one's risk to be geo-identified, and highlights that the model can yield a considerable high inference accuracy (48%~70%) and serious privacy exposure (over 80% of users get privacy disclosed) with a small portion of geo-annotated samples. In view of the threats, we further propose an adaptive grouping-based signing model that hides a user's track with the camouflage of a crowd of users. Wherein, ring signature is tailored for crowdsensing to provide indistinguishable while valid identities for every user's submission. We theoretically analyze its adjustable privacy protection capability and develop a prototype to evaluate the effectiveness and performance.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478106"}, {"primary_key": "1961796", "vector": [], "sparse_vector": [], "title": "SyncUp: Vision-based Practice Support for Synchronized Dancing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The beauty of synchronized dancing lies in the synchronization of body movements among multiple dancers. While dancers utilize camera recordings for their practice, standard video interfaces do not efficiently support their activities of identifying segments where they are not well synchronized. This thus fails to close a tight loop of an iterative practice process (i.e., capturing a practice, reviewing the video, and practicing again). We present SyncUp, a system that provides multiple interactive visualizations to support the practice of synchronized dancing and liberate users from manual inspection of recorded practice videos. By analyzing videos uploaded by users, SyncUp quantifies two aspects of synchronization in dancing: pose similarity among multiple dancers and temporal alignment of their movements. The system then highlights which body parts and which portions of the dance routine require further practice to achieve better synchronization. The results of our system evaluations show that our pose similarity estimation and temporal alignment predictions were correlated well with human ratings. Participants in our qualitative user evaluation expressed the benefits and its potential use of SyncUp, confirming that it would enable quick iterative practice.", "published": "2021-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3478120"}]