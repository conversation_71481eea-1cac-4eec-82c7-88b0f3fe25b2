[{"primary_key": "2972777", "vector": [], "sparse_vector": [], "title": "PAC Statistical Model Checking for Markov Decision Processes and Stochastic Games.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Statistical model checking (SMC) is a technique for analysis of probabilistic systems that may be (partially) unknown. We present an SMC algorithm for (unbounded) reachability yielding probably approximately correct (PAC) guarantees on the results. We consider both the setting (i) with no knowledge of the transition function (with the only quantity required a bound on the minimum transition probability) and (ii) with knowledge of the topology of the underlying graph. On the one hand, it is the first algorithm for stochastic games. On the other hand, it is the first practical algorithm even for Markov decision processes. Compared to previous approaches where PAC guarantees require running times longer than the age of universe even for systems with a handful of states, our algorithm often yields reasonably precise results within minutes, not requiring the knowledge of mixing time.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_29"}, {"primary_key": "2972778", "vector": [], "sparse_vector": [], "title": "Run-Time Optimization for Learned Controllers Through Quantitative Games.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A controller is a device that interacts with a plant. At each time point, it reads the plant's state and issues commands with the goal that the plant operates optimally. Constructing optimal controllers is a fundamental and challenging problem. Machine learning techniques have recently been successfully applied to train controllers, yet they have limitations. Learned controllers are monolithic and hard to reason about. In particular, it is difficult to add features without retraining, to guarantee any level of performance, and to achieve acceptable performance when encountering untrained scenarios. These limitations can be addressed by deploying quantitative run-time shields that serve as a proxy for the controller. At each time point, the shield reads the command issued by the controller and may choose to alter it before passing it on to the plant. We show how optimal shields that interfere as little as possible while guaranteeing a desired level of controller performance, can be generated systematically and automatically using reactive synthesis. First, we abstract the plant by building a stochastic model. Second, we consider the learned controller to be a black box. Third, we measure controller performance and shield interference by two quantitative run-time measures that are formally defined using weighted automata. Then, the problem of constructing a shield that guarantees maximal performance with minimal interference is the problem of finding an optimal strategy in a stochastic 2-player game \"controller versus shield\" played on the abstract state space of the plant with a quantitative objective obtained from combining the performance and interference measures. We illustrate the effectiveness of our approach by automatically constructing lightweight shields for learned traffic-light controllers in various road networks. The shields we generate avoid liveness bugs, improve controller performance in untrained and changing traffic situations, and add features to learned controllers, such as giving priority to emergency vehicles .", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_36"}, {"primary_key": "2972779", "vector": [], "sparse_vector": [], "title": "Reachability Analysis for AWS-Based Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>gt<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Cloud services provide the ability to provision virtual networked infrastructure on demand over the Internet. The rapid growth of these virtually provisioned cloud networks has increased the demand for automated reasoning tools capable of identifying misconfigurations or security vulnerabilities. This type of automation gives customers the assurance they need to deploy sensitive workloads. It can also reduce the cost and time-to-market for regulated customers looking to establish compliance certification for cloud-based applications. In this industrial case-study, we describe a new network reachability reasoning tool, calledTiros, that uses off-the-shelf automated theorem proving tools to fill this need.Tirosis the foundation of a recently introduced network security analysis feature in theAmazon Inspectorservice now available to millions of customers building applications in the cloud.Tirosis also used within Amazon Web Services (AWS) to automate the checking of compliance certification and adherence to security invariants for many AWS services that build on existing AWS networking features.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_14"}, {"primary_key": "2972780", "vector": [], "sparse_vector": [], "title": "Safety and Co-safety Comparator Automata for Discounted-Sum Inclusion.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Discounted-sum inclusion (DS-inclusion, in short) formalizes the goal of comparing quantitative dimensions of systems such as cost, resource consumption, and the like, when the mode of aggregation for the quantitative dimension is discounted-sum aggregation. Discounted-sum comparator automata, or DS-comparators in short, are Büchi automata that read two infinite sequences of weights synchronously and relate their discounted-sum. Recent empirical investigations have shown that while DS-comparators enable competitive algorithms for DS-inclusion, they still suffer from the scalability bottleneck of Büchi operations. Motivated by the connections between discounted-sum and Büchi automata, this paper undertakes an investigation of language-theoretic properties of DS-comparators in order to mitigate the challenges of Büchi DS-comparators to achieve improved scalability of DS-inclusion. Our investigation uncovers that DS-comparators possess safety and co-safety language-theoretic properties. As a result, they enable reductions based on subset construction-based methods as opposed to higher complexity Büchi complementation, yielding tighter worst-case complexity and improved empirical scalability for DS-inclusion.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_4"}, {"primary_key": "2972781", "vector": [], "sparse_vector": [], "title": "Icing: Supporting Fast-Math Style Optimizations in a Verified Compiler.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Verified compilers like CompCert and CakeML offer increasingly sophisticated optimizations. However, their deterministic source semantics and strict IEEE 754 compliance prevent the verification of “fast-math” style floating-point optimizations. Developers often selectively use these optimizations in mainstream compilers like GCC and LLVM to improve the performance of computations over noisy inputs or for heuristics by allowing the compiler to perform intuitive but IEEE 754-unsound rewrites. We designed, formalized, implemented, and verified a compiler for Icing, a new language which supports selectively applying fast-math style optimizations in a verified compiler. Icing’s semantics provides the first formalization of fast-math in a verified compiler. We show how the Icing compiler can be connected to the existing verified CakeML compiler and verify the end-to-end translation by a sequence of refinement proofs from Icing to the translated CakeML. We evaluated Icing by incorporating several of GCC’s fast-math rewrites. While Icing targets CakeML’s source language, the techniques we developed are general and could also be incorporated in lower-level intermediate representations.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_10"}, {"primary_key": "2972782", "vector": [], "sparse_vector": [], "title": "Checking Robustness Against Snapshot Isolation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Constantin <PERSON>"], "summary": "Transactional access to databases is an important abstraction allowing programmers to consider blocks of actions (transactions) as executing in isolation. The strongest consistency model isserializability, which ensures the atomicity abstraction of transactions executing over a sequentially consistent memory. Since ensuring serializability carries a significant penalty on availability, modern databases provide weaker consistency models, one of the most prominent beingsnapshot isolation. In general, the correctness of a program relying on serializable transactions may be broken when using weaker models. However, certain programs may also be insensitive to consistency relaxations, i.e., all their properties holding under serializability are preserved even when they are executed over a weak consistent database and without additional synchronization. In this paper, we address the issue of verifying if a given program isrobust against snapshot isolation, i.e., all its behaviors are serializable even if it is executed over a database ensuring snapshot isolation. We show that this verification problem is polynomial time reducible to a state reachability problem in transactional programs over a sequentially consistent shared memory. This reduction opens the door to the reuse of the classic verification technology for reasoning about weakly-consistent programs. In particular, we show that it can be used to derive a proof technique based on <PERSON><PERSON>’s reduction theory that allows to prove programs robust.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_17"}, {"primary_key": "2972783", "vector": [], "sparse_vector": [], "title": "Verification of Threshold-Based Distributed Algorithms by Decomposition to Decidable Logics.", "authors": ["<PERSON>n <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Verification of fault-tolerant distributed protocols is an immensely difficult task. Often, in these protocols,thresholdson set cardinalities are used both in the process code and in its correctness proof, e.g., a process can perform an action only if it has received an acknowledgment from at least half of its peers. Verification of threshold-based protocols is extremely challenging as it involves two kinds of reasoning: first-order reasoning about the unbounded state of the protocol, together with reasoning about sets and cardinalities. In this work, we develop a new methodology for decomposing the verification task of such protocols intotwodecidable logics: EPR and BAPA. Our key insight is that such protocols use thresholds in a restricted way as a means to obtain certain properties of “intersection” between sets. We define a language for expressing such properties, and present two translations: to EPR and BAPA. The EPR translation allows verifying the protocol while assuming these properties, and the BAPA translation allows verifying the correctness of the properties. We further develop an algorithm for automatically generating the properties needed for verifying a given protocol, facilitating fully automated deductive verification. Using this technique we have verified several challenging protocols, including Byzantine one-step consensus, hybrid reliable broadcast and fast Byzantine Paxos.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_15"}, {"primary_key": "2972784", "vector": [], "sparse_vector": [], "title": "On the Complexity of Checking Consistency for Replicated Data Types.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Constantin <PERSON>"], "summary": "Recent distributed systems have introduced variations of familiar abstract data types (ADTs) like counters, registers, flags, and sets, that provide high availability and partition tolerance. These conflict-free replicated data types (CRDTs) utilize mechanisms to resolve the effects of concurrent updates to replicated data. Naturally these objects weaken their consistency guarantees to achieve availability and partition-tolerance, and various notions of weak consistency capture those guarantees. In this work we study the tractability of CRDT-consistency checking. To capture guarantees precisely, and facilitate symbolic reasoning, we propose novel logical characterizations. By developing novel reductions from propositional satisfiability problems, and novel consistency-checking algorithms, we discover both positive and negative results. In particular, we show intractability for replicated flags, sets, counters, and registers, yet tractability for replicated growable arrays. Furthermore, we demonstrate that tractability can be redeemed for registers when each value is written at most once, for counters when the number of replicas is fixed, and for sets and flags when the number of replicas and variables is fixed.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_19"}, {"primary_key": "2972785", "vector": [], "sparse_vector": [], "title": "Invertibility Conditions for Floating-Point Formulas.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Automated reasoning procedures are essential for a number of applications that involve bit-exact floating-point computations. This paper presents conditions that characterize when a variable in a floating-point constraint has a solution, which we call invertibility conditions. We describe a novel workflow that combines human interaction and a syntax-guided synthesis (SyGuS) solver that was used for discovering these conditions. We verify our conditions for several floating-point formats. One implication of this result is that a fragment of floating-point arithmetic admits compact quantifier elimination. We implement our invertibility conditions in a prototype extension of our solver CVC4, showing their usefulness for solving quantified constraints over floating-points.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_8"}, {"primary_key": "2972786", "vector": [], "sparse_vector": [], "title": "Robust Controller Synthesis in Timed Büchi Automata: A Symbolic Approach.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Ocan Sankur"], "summary": "We solve in a purely symbolic way the robust controller synthesis problem in timed automata with Büchi acceptance conditions. The goal of the controller is to play according to an accepting lasso of the automaton, while resisting to timing perturbations chosen by a competing environment. The problem was previously shown to be PSPACE-complete using regions-based techniques, but we provide a first tool solving the problem using zones only, thus more resilient to state-space explosion problem. The key ingredient is the introduction of branching constraint graphs allowing to decide in polynomial time whether a given lasso is robust, and even compute the largest admissible perturbation if it is. We also make an original use of constraint graphs in this context in order to test the inclusion of timed reachability relations, crucial for the termination criterion of our algorithm. Our techniques are illustrated using a case study on the regulation of a train network.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_33"}, {"primary_key": "2972787", "vector": [], "sparse_vector": [], "title": "Semi-quantitative Abstraction and Analysis of Chemical Reaction Networks.", "authors": ["Milan Ceska", "<PERSON>"], "summary": "Analysis of large continuous-time stochastic systems is a computationally intensive task. In this work we focus on population models arising from chemical reaction networks (CRNs), which play a fundamental role in analysis and design of biochemical systems. Many relevant CRNs are particularly challenging for existing techniques due to complex dynamics including stochasticity, stiffness or multimodal population distributions. We propose a novel approach allowing not only to predict, but also to explain both the transient and steady-state behaviour. It focuses on qualitative description of the behaviour and aims at quantitative precision only in orders of magnitude. First we build a compact understandable model, which we then crudely analyse. As demonstrated on complex CRNs from literature, our approach reproduces the known results, but in contrast to the state-of-the-art methods, it runs with virtually no computational cost and thus offers unprecedented scalability.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_28"}, {"primary_key": "2972788", "vector": [], "sparse_vector": [], "title": "Extending nuXmv with Timed Transition Systems and Timed Temporal Properties.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "nuXmv is a well-known symbolic model checker, which implements various state-of-the-art algorithms for the analysis of finite- and infinite-state transition systems and temporal logics. In this paper, we present a new version that supports timed systems and logics over continuous super-dense semantics. The system specification was extended with clocks to constrain the timed evolution. The support for temporal properties has been expanded to include $$\\textsc {MTL}_{0,\\infty }$$ formulas with parametric intervals. The analysis is performed via a reduction to verification problems in the discrete-time case. The internal representation of traces has been extended to go beyond the lasso-shaped form, to take into account the possible divergence of clocks. We evaluated the new features by comparing nuXmv with other verification tools for timed automata and $$\\textsc {MTL}_{0,\\infty }$$ , considering different benchmarks from the literature. The results show that nuXmv is competitive with and in many cases performs better than state-of-the-art tools, especially on validity problems for $$\\textsc {MTL}_{0,\\infty }$$ .", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_21"}, {"primary_key": "2972789", "vector": [], "sparse_vector": [], "title": "Verifying Hyperliveness.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "HyperLTL is an extension of linear-time temporal logic for the specification of hyperproperties, i.e., temporal properties that relate multiple computation traces. HyperLTL can express information flow policies as well as properties like symmetry in mutual exclusion algorithms or Hamming distances in error-resistant transmission protocols. Previous work on HyperLTL model checking has focussed on the alternation-free fragment of HyperLTL, where verification reduces to checking a standard trace property over an appropriate self-composition of the system. The alternation-free fragment does, however, not cover general hyperliveness properties. Universal formulas, for example, cannot express the secrecy requirement that for every possible value of a secret variable there exists a computation where the value is different while the observations made by the external observer are the same. In this paper, we study the more difficult case of hyperliveness properties expressed as HyperLTL formulas with quantifier alternation. We reduce existential quantification to strategic choice and show that synthesis algorithms can be used to eliminate the existential quantifiers automatically. We furthermore show that this approach can be extended to reactive system synthesis, i.e., to automatically construct a reactive system that is guaranteed to satisfy a given HyperLTL formula.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_7"}, {"primary_key": "2972790", "vector": [], "sparse_vector": [], "title": "Symbolic Register Automata.", "authors": ["Loris D&apos;Antoni", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Symbolic Finite Automata and Register Automata are two orthogonal extensions of finite automata motivated by real-world problems where data may have unbounded domains. These automata address a demand for a model over large or infinite alphabets, respectively. Both automata models have interesting applications and have been successful in their own right. In this paper, we introduce Symbolic Register Automata, a new model that combines features from both symbolic and register automata, with a view on applications that were previously out of reach. We study their properties and provide algorithms for emptiness, inclusion and equivalence checking, together with experimental results.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_1"}, {"primary_key": "2972791", "vector": [], "sparse_vector": [], "title": "Communication-Closed Asynchronous Protocols.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The verification of asynchronous fault-tolerant distributed systems is challenging due to unboundedly many interleavings and network failures (e.g., processes crash or message loss). We propose a method that reduces the verification of asynchronous fault-tolerant protocols to the verification of round-based synchronous ones. Synchronous protocols are easier to verify due to fewer interleavings, bounded message buffers etc. We implemented our reduction method and applied it to several state machine replication and consensus algorithms. The resulting synchronous protocols are verified using existing deductive verification methods.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_20"}, {"primary_key": "2972792", "vector": [], "sparse_vector": [], "title": "Sound Approximation of Programs with Elementary Functions.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Elementary function calls are a common feature in numerical programs. While their implementations in mathematical libraries are highly optimized, function evaluation is nonetheless very expensive compared to plain arithmetic. Full accuracy is, however, not always needed. Unlike arithmetic, where the performance difference between for example single and double precision floating-point arithmetic is relatively small, elementary function calls provide a much richer tradeoff space between accuracy and efficiency. Navigating this space is challenging, as guaranteeing the accuracy and choosing correct parameters for good performance of approximations is highly nontrivial. We present a fully automated approach and a tool which approximates elementary function calls inside small programs while guaranteeing overall user given error bounds. Our tool leverages existing techniques for roundoff error computation and approximation of individual elementary function calls and provides an automated methodology for the exploration of parameter space. Our experiments show that significant efficiency improvements are possible in exchange for reduced, but guaranteed, accuracy.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_11"}, {"primary_key": "2972793", "vector": [], "sparse_vector": [], "title": "When Human Intuition Fails: Using Formal Methods to Find an Error in the &quot;Proof&quot; of a Multi-agent Protocol.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Designing protocols for multi-agent interaction that achieve the desired behavior is a challenging and error-prone process. The standard practice is to manually develop proofs of protocol correctness that rely on human intuition and require significant effort to develop. Even then, proofs can have mistakes that may go unnoticed after peer review, modeling and simulation, and testing. The use of formal methods can reduce the potential for such errors. In this paper, we discuss our experience applying model checking to a previously published multi-agent protocol for unmanned air vehicles. The original publication provides a compelling proof of correctness, along with extensive simulation results to support it. However, analysis through model checking found an error in one of the proof's main lemmas. In this paper, we start by providing an overview of the protocol and its original \"proof\" of correctness, which represents the standard practice in multi-agent protocol design. We then describe how we modeled the protocol for a three-vehicle system in a model checker, the counterexample it returned, and the insight this counterexample provided. We also discuss benefits, limitations, and lessons learned from this exercise, as well as what future efforts would be needed to fully verify the protocol for an arbitrary number of vehicles.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_20"}, {"primary_key": "2972795", "vector": [], "sparse_vector": [], "title": "Synthesizing Approximate Implementations for Unrealizable Specifications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The unrealizability of a specification is often due to the assumption that the behavior of the environment is unrestricted. In this paper, we present algorithms for synthesis in bounded environments, where the environment can only generate input sequences that are ultimately periodic words (lassos) with finite representations of bounded size. We provide automata-theoretic and symbolic approaches for solving this synthesis problem, and also study the synthesis of approximative implementations from unrealizable specifications. Such implementations may violate the specification in general, but are guaranteed to satisfy the specification on at least a specified portion of the bounded-size lassos. We evaluate the algorithms on different arbiter specifications.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_13"}, {"primary_key": "2972796", "vector": [], "sparse_vector": [], "title": "VerifAI: A Toolkit for the Formal Design and Analysis of Artificial Intelligence-Based Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present VerifAI, a software toolkit for the formal design and analysis of systems that include artificial intelligence (AI) and machine learning (ML) components. VerifAI particularly addresses challenges with applying formal methods to ML components such as perception systems based on deep neural networks, as well as systems containing them, and to model and analyze system behavior in the presence of environment uncertainty. We describe the initial version of VerifAI, which centers on simulation-based verification and synthesis, guided by formal models and specifications. We give examples of several use cases, including temporal-logic falsification, model-based systematic fuzz testing, parameter synthesis, counterexample analysis, and data set augmentation.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_25"}, {"primary_key": "2972797", "vector": [], "sparse_vector": [], "title": "Efficient Synthesis with Probabilistic Constraints.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Loris D&apos;Antoni"], "summary": "We consider the problem of synthesizing a program given a probabilistic specification of its desired behavior. Specifically, we study the recent paradigm of distribution-guided inductive synthesis (digits), which iteratively calls a synthesizer on finite sample sets from a given distribution. We make theoretical and algorithmic contributions: (i) We prove the surprising result that digits only requires a polynomial number of synthesizer calls in the size of the sample set, despite its ostensibly exponential behavior. (ii) We present a property-directed version of digits that further reduces the number of synthesizer calls, drastically improving synthesis performance on a range of benchmarks.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_15"}, {"primary_key": "2972798", "vector": [], "sparse_vector": [], "title": "Security-Aware Synthesis Using Delayed-Action Games.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Stochastic multiplayer games (SMGs) have gained attention in the field of strategy synthesis for multi-agent reactive systems. However, standard SMGs are limited to modeling systems where all agents have full knowledge of the state of the game. In this paper, we introduce delayed-action games (DAGs) formalism that simulates hidden-information games (HIGs) as SMGs, where hidden information is captured by delaying a player’s actions. The elimination of private variables enables the usage of SMG off-the-shelf model checkers to implement HIGs. Furthermore, we demonstrate how a DAG can be decomposed into subgames that can be independently explored, utilizing parallel computation to reduce the model checking time, while alleviating the state space explosion problem that SMGs are notorious for. In addition, we propose a DAG-based framework for strategy synthesis and analysis. Finally, we demonstrate applicability of the DAG-based synthesis framework on a case study of a human-on-the-loop unmanned-aerial vehicle system under stealthy attacks, where the proposed framework is used to formally model, analyze and synthesize security-aware strategies for the system.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_10"}, {"primary_key": "2972799", "vector": [], "sparse_vector": [], "title": "Violat: Generating Tests of Observational Refinement for Concurrent Objects.", "authors": ["<PERSON>", "Constantin <PERSON>"], "summary": "High-performance multithreaded software often relies on optimized implementations of common abstract data types (ADTs) like counters, key-value stores, and queues, i.e., concurrent objects. By using fine-grained and non-blocking mechanisms for efficient inter-thread synchronization, these implementations are vulnerable to violations of ADT-consistency which are difficult to detect: bugs can depend on specific combinations of method invocations and argument values, as well as rarely-occurring thread interleavings. Even given a bug-triggering interleaving, detection generally requires unintuitive test assertions to capture inconsistent combinations of invocation return values. In this work we describe the Violat tool for generating tests that witness violations to atomicity, or weaker consistency properties. Violat generates self-contained and efficient programs that test observational refinement, i.e., substitutability of a given ADT with a given implementation. Our approach is both sound and complete in the limit: for every consistency violation there is a failed execution of some test program, and every failed test signals an actual consistency violation. In practice we compromise soundness for efficiency via random exploration of test programs, yielding probabilistic soundness instead. <PERSON><PERSON>'s tests reliably expose ADT-consistency violations using off-the-shelf approaches to concurrent test validation, including stress testing and explicit-state model checking.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_30"}, {"primary_key": "2972800", "vector": [], "sparse_vector": [], "title": "SecCSL: Security Concurrent Separation Logic.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present SecCSL, a concurrent separation logic for proving expressive, data-dependent information flow security properties of low-level programs. SecCSL is considerably more expressive, while being simpler, than recent compositional information flow logics that cannot reason about pointers, arrays etc. To capture security concerns, SecCSL adopts a relational semantics for its assertions. At the same time it inherits the structure of traditional concurrent separation logics; thus SecCSL reasoning can be automated via symbolic execution. We demonstrate this by implementing SecC, an automatic verifier for a subset of the C programming language, which we apply to a range of benchmarks.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_13"}, {"primary_key": "2972801", "vector": [], "sparse_vector": [], "title": "Automated Hypersafety Verification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose an automated verification technique for hypersafety properties, which express sets of valid interrelations between multiple finite runs of a program. The key observation is that constructing a proof for a small representative set of the runs of the product program (i.e. the product of the several copies of the program by itself), called a reduction, is sufficient to formally prove the hypersafety property about the program. We propose an algorithm based on a counterexample-guided refinement loop that simultaneously searches for a reduction and a proof of the correctness for the reduction. We demonstrate that our tool Weaver is very effective in verifying a diverse array of hypersafety properties for a diverse class of input programs.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_11"}, {"primary_key": "2972802", "vector": [], "sparse_vector": [], "title": "StreamLAB: Stream-based Monitoring of Cyber-Physical Systems.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With ever increasing autonomy of cyber-physical systems, monitoring becomes an integral part for ensuring the safety of the system at runtime. $$\\text {StreamLAB} $$ is a monitoring framework with high degree of expressibility and strong correctness guarantees. Specifications are written in $$\\text {RTLola} $$ , a stream-based specification language with formal semantics. $$\\text {StreamLAB} $$ provides an extensive analysis of the specification, including the computation of memory consumption and run-time guarantees. We demonstrate the applicability of $$\\text {StreamLAB} $$ on typical monitoring tasks for cyber-physical systems, such as sensor validation and system health checks.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_24"}, {"primary_key": "2972803", "vector": [], "sparse_vector": [], "title": "Quantified Invariants via Syntax-Guided Synthesis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Programs with arrays are ubiquitous. Automated reasoning about arrays necessitates discovering properties about ranges of elements at certain program points. Such properties are formally specified by universally quantified formulas, which are difficult to find, and difficult to prove inductive. In this paper, we propose an algorithm based on an enumerative search that discovers quantified invariants in stages. First, by exploiting the program syntax, it identifies ranges of elements accessed in each loop. Second, it identifies potentially useful facts about individual elements and generalizes them to hypotheses about entire ranges. Finally, by applying recent advances of SMT solving, the algorithm filters out wrong hypotheses. The combination of properties is often enough to prove that the program meets a safety specification. The algorithm has been implemented in a solver for Constrained Horn Clauses, Freq-Horn, and extended to deal with multiple (possibly nested) loops. We show that FreqHorn advances state-of-the-art on a wide range of public array-handling programs.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_14"}, {"primary_key": "2972804", "vector": [], "sparse_vector": [], "title": "Inferring Inductive Invariants from Phase Structures.", "authors": ["<PERSON><PERSON> <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Infinite-state systems such as distributed protocols are challenging to verify using interactive theorem provers or automatic verification tools. Of these techniques, deductive verification is highly expressive but requires the user to annotate the system with inductive invariants. To relieve the user from this labor-intensive and challenging task, invariant inference aims to find inductive invariants automatically. Unfortunately, when applied to infinite-state systems such as distributed protocols, existing inference techniques often diverge, which limits their applicability. This paper proposes user-guided invariant inference based on phase invariants, which capture the different logical phases of the protocol. Users conveys their intuition by specifying a phase structure, an automaton with edges labeled by program transitions; the tool automatically infers assertions that hold in the automaton's states, resulting in a full safety proof. The additional structure from phases guides the inference procedure towards finding an invariant. Our results show that user guidance by phase structures facilitates successful inference beyond the state of the art. We find that phase structures are pleasantly well matched to the intuitive reasoning routinely used by domain experts to understand why distributed protocols are correct, so that providing a phase structure reuses this existing intuition.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_23"}, {"primary_key": "2972805", "vector": [], "sparse_vector": [], "title": "Taming Delays in Dynamical Systems - Unbounded Verification of Delay Differential Equations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Delayed coupling between state variables occurs regularly in technical dynamical systems, especially embedded control. As it consequently is omnipresent in safety-critical domains, there is an increasing interest in the safety verification of systems modelled by Delay Differential Equations (DDEs). In this paper, we leverage qualitative guarantees for the existence of an exponentially decreasing estimation on the solutions to DDEs as established in classical stability theory, and present a quantitative method for constructing such delay-dependent estimations, thereby facilitating a reduction of the verification problem over an unbounded temporal horizon to a bounded one. Our technique builds on the linearization technique of nonlinear dynamics and spectral analysis of the linearized counterparts. We show experimentally on a set of representative benchmarks from the literature that our technique indeed extends the scope of bounded verification techniques to unbounded verification tasks. Moreover, our technique is easy to implement and can be combined with any automatic tool dedicated to bounded verification of DDEs.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_37"}, {"primary_key": "2972806", "vector": [], "sparse_vector": [], "title": "Temporal Stream Logic: Synthesis Beyond the Bools.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Ruzica Piskac", "<PERSON>"], "summary": "Reactive systems that operate in environments with complex data, such as mobile apps or embedded controllers with many sensors, are difficult to synthesize. Synthesis tools usually fail for such systems because the state space resulting from the discretization of the data is too large. We introduce TSL, a new temporal logic that separates control and data. We provide a CEGAR-based synthesis approach for the construction of implementations that are guaranteed to satisfy a TSL specification for all possible instantiations of the data processing functions. TSL provides an attractive trade-off for synthesis. On the one hand, synthesis from TSL, unlike synthesis from standard temporal logics, is undecidable in general. On the other hand, however, synthesis from TSL is scalable, because it is independent of the complexity of the handled data. Among other benchmarks, we have successfully synthesized a music player Android app and a controller for an autonomous vehicle in the Open Race Car Simulator (TORCS).", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_35"}, {"primary_key": "2972807", "vector": [], "sparse_vector": [], "title": "Termination of Triangular Integer Loops is Decidable.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the problem whether termination of affine integer loops is decidable. Since <PERSON><PERSON><PERSON> conjectured decidability in 2004 [15], only special cases have been solved [3, 4, 14]. We complement this work by proving decidability for the case that the update matrix is triangular.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_24"}, {"primary_key": "2972808", "vector": [], "sparse_vector": [], "title": "Numerically-Robust Inductive Proof Rules for Continuous Dynamical Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Jyotirmoy V<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>-<PERSON>", "<PERSON><PERSON>", "Soonho Kong"], "summary": "We formulate numerically-robust inductive proof rules for unbounded stability and safety properties of continuous dynamical systems. These induction rules robustify standard notions of Lyapunov functions and barrier certificates so that they can tolerate small numerical errors. In this way, numerically-driven decision procedures can establish a sound and relative-complete proof system for unbounded properties of very general nonlinear systems. We demonstrate the effectiveness of the proposed rules for rigorously verifying unbounded properties of various nonlinear systems, including a challenging powertrain control model.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_9"}, {"primary_key": "2972809", "vector": [], "sparse_vector": [], "title": "Fast Algorithms for Handling Diagonal Constraints in Timed Automata.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A popular method for solving reachability in timed automata proceeds by enumerating reachable sets of valuations represented as zones. A naïve enumeration of zones does not terminate. Various termination mechanisms have been studied over the years. Coming up with efficient termination mechanisms has been remarkably more challenging when the automaton has diagonal constraints in guards. In this paper, we propose a new termination mechanism for timed automata with diagonal constraints based on a new simulation relation between zones. Experiments with an implementation of this simulation show significant gains over existing methods.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_3"}, {"primary_key": "2972810", "vector": [], "sparse_vector": [], "title": "BMC for Weak Memory Models: Relation Analysis for Compact SMT Encodings.", "authors": ["<PERSON>", "Hernán <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present <PERSON><PERSON><PERSON>, a bounded model checker (BMC) for concurrent programs under weak memory models. Its distinguishing feature is that the memory model is not implemented inside the tool but taken as part of the input. Dartagnan reads CAT, the standard language for memory models, which allows to define x86/TSO, ARMv7, ARMv8, Power, C/C++, and Linux kernel concurrency primitives. BMC with memory models as inputs is challenging. One has to encode into SMT not only the program but also its semantics as defined by the memory model. What makes Dartagnan scale is its relation analysis, a novel static analysis that significantly reduces the size of the encoding. Dartagnan matches or even exceeds the performance of the model-specific verification tools Nidhugg and CBMC, as well as the performance of Herd, a CAT-compatible litmus testing tool. Compared to the unoptimized encoding, the speed-up is often more than two orders of magnitude.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_19"}, {"primary_key": "2972812", "vector": [], "sparse_vector": [], "title": "Efficient Verification of Network Fault Tolerance via Counterexample-Guided Refinement.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We show how to verify that large data center networks satisfy key properties such as all-pairs reachability under a bounded number of faults. To scale the analysis, we develop algorithms that identify network symmetries and compute small abstract networks from large concrete ones. Using counter-example guided abstraction refinement, we successively refine the computed abstractions until the given property may be verified. The soundness of our approach relies on a novel notion of network approximation: routing paths in the concrete network are not precisely simulated by those in the abstract network but are guaranteed to be \"at least as good.\" We implement our algorithms in a tool called Origami and use them to verify reachability under faults for standard data center topologies. We find that Origami computes abstract networks with 1–3 orders of magnitude fewer edges, which makes it possible to verify large networks that are out of reach of existing techniques.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_18"}, {"primary_key": "2972813", "vector": [], "sparse_vector": [], "title": "Integrating Formal Schedulability Analysis into a Verified OS Kernel.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Formal verification of real-time systems is attractive because these systems often perform critical operations. Unlike non real-time systems, latency and response time guarantees are of critical importance in this setting, as much as functional correctness. Nevertheless, formal verification of real-time OSes usually stops the scheduling analysis at the policy level: they only prove that the scheduler (or its abstract model) satisfies some scheduling policy. In this paper, we go further and connect together Prosa, a verified schedulability analyzer, and RT-CertiKOS, a verified single-core sequential real-time OS kernel. Thus, we get a more general and extensible schedulability analysis proof for RT-CertiKOS, as well a concrete implementation validating Prosa models. It also showcases that it is realistic to connect two completely independent formal developments in a proof assistant.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_28"}, {"primary_key": "2972815", "vector": [], "sparse_vector": [], "title": "Probabilistic Bisimulation for Parameterized Systems - (with Applications to Verifying Anonymous Protocols).", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Probabilistic bisimulation is a fundamental notion of process equivalence for probabilistic systems. It has important applications, including the formalisation of the anonymity property of several communication protocols. While there is a large body of work on verifying probabilistic bisimulation for finite systems, the problem is in general undecidable for parameterized systems, i.e., for infinite families of finite systems with an arbitrary number n of processes. In this paper we provide a general framework for reasoning about probabilistic bisimulation for parameterized systems. Our approach is in the spirit of software verification, wherein we encode proof rules for probabilistic bisimulation and use a decidable first-order theory to specify systems and candidate bisimulation relations, which can then be checked automatically against the proof rules. We work in the framework of regular model checking, and specify an infinite-state system as a regular relation described by a first-order formula over a universal automatic structure, i.e., a logical theory over the string domain. For probabilistic systems, we show how probability values (as well as the required operations) can be encoded naturally in the logic. Our main result is that one can specify the verification condition of whether a given regular binary relation is a probabilistic bisimulation as a regular relation. Since the first-order theory of the universal automatic structure is decidable, we obtain an effective method for verifying probabilistic bisimulation for infinite-state systems, given a regular relation as a candidate proof. As a case study, we show that our framework is sufficiently expressive for proving the anonymity property of the parameterized dining cryptographers protocol and the parameterized grades protocol. Both of these protocols hitherto could not be verified by existing automatic methods. Moreover, with the help of standard automata learning algorithms, we show that the candidate relations can be synthesized fully automatically, making the verification fully automated.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_27"}, {"primary_key": "2972816", "vector": [], "sparse_vector": [], "title": "Proving Unrealizability for Syntax-Guided Synthesis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Loris D&apos;Antoni", "<PERSON>s"], "summary": "We consider the problem of automatically establishing that a given syntax-guided-synthesis (SyGuS) problem is unrealizable (i.e., has no solution). Existing techniques have quite limited ability to establish unrealizability for general SyGuS instances in which the grammar describing the search space contains infinitely many programs. By encoding the synthesis problem's grammar G as a nondeterministic program $$P_G$$ , we reduce the unrealizability problem to a reachability problem such that, if a standard program-analysis tool can establish that a certain assertion in $$P_G$$ always holds, then the synthesis problem is unrealizable. Our method can be used to augment existing SyGuS tools so that they can establish that a successfully synthesized program q is optimal with respect to some syntactic cost—e.g., q has the fewest possible if-then-else operators. Using known techniques, grammar G can be transformed to generate the set of all programs with lower costs than q—e.g., fewer conditional expressions. Our algorithm can then be applied to show that the resulting synthesis problem is unrealizable. We implemented the proposed technique in a tool called nope. nope can prove unrealizability for 59/132 variants of existing linear-integer-arithmetic SyGuS benchmarks, whereas all existing SyGuS solvers lack the ability to prove that these benchmarks are unrealizable, and time out on them.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_18"}, {"primary_key": "2972818", "vector": [], "sparse_vector": [], "title": "Alternating Automata Modulo First Order Theories.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce first-order alternating automata, a generalization of boolean alternating automata, in which transition rules are described by multisorted first-order formulae, with states and internal variables given by uninterpreted predicate terms. The model is closed under union, intersection and complement, and its emptiness problem is undecidable, even for the simplest data theory of equality. To cope with the undecidability problem, we develop an abstraction refinement semi-algorithm based on lazy annotation of the symbolic execution paths with interpolants, obtained by applying (i) quantifier elimination with witness term generation and (ii) Lyndon interpolation in the quantifier-free theory of the data domain, with uninterpreted predicate symbols. This provides a method for checking inclusion of timed and finite-memory register automata, and emptiness of quantified predicate automata, previously used in the verification of parameterized concurrent programs, composed of replicated threads, with shared memory.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_3"}, {"primary_key": "2972819", "vector": [], "sparse_vector": [], "title": "Local and Compositional Reasoning for Optimized Reactive Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We develop a compositional, algebraic theory of skipping refinement, as well as local proof methods to effectively analyze the correctness of optimized reactive systems. A verification methodology based on refinement involves showing that any infinite behavior of an optimized low-level implementation is a behavior of the high-level abstract specification. Skipping refinement is a recently introduced notion to reason about the correctness of optimized implementations that run faster than their specifications, i.e., a step in the implementation can skip multiple steps of the specification. For the class of systems that exhibit bounded skipping, existing proof methods have been shown to be amenable to mechanized verification using theorem provers and model-checkers. However, reasoning about the correctness of reactive systems that exhibit unbounded skipping using these proof methods requires reachability analysis, significantly increasing the verification effort. In this paper, we develop two new sound and complete proof methods for skipping refinement. Even in presence of unbounded skipping, these proof methods require only local reasoning and, therefore, are amenable to mechanized verification. We also show that skipping refinement is compositional, so it can be used in a stepwise refinement methodology. Finally, we illustrate the utility of the theory of skipping refinement by proving the correctness of an optimized event processing system.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_32"}, {"primary_key": "2972821", "vector": [], "sparse_vector": [], "title": "Q3B: An Efficient BDD-based SMT Solver for Quantified Bit-Vectors.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We present the first stable release of our tool Q3B for deciding satisfiability of quantified bit-vector formulas. Unlike other state-of-the-art solvers for this problem, Q3B is based on translation of a formula to a bdd that represents models of the formula. The tool also employs advanced formula simplifications and approximations by effective bit-width reduction and by abstraction of bit-vector operations. The paper focuses on the architecture and implementation aspects of the tool, and provides a brief experimental comparison with its competitors.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_4"}, {"primary_key": "2972822", "vector": [], "sparse_vector": [], "title": "Automated Synthesis of Secure Platform Mappings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "System development often involves decisions about how a high-level design is to be implemented using primitives from a low-level platform. Certain decisions, however, may introduce undesirable behavior into the resulting implementation, possibly leading to a violation of a desired property that has already been established at the design level. In this paper, we introduce the problem of synthesizing a property-preserving platform mapping: synthesize a set of implementation decisions ensuring that a desired property is preserved from a high-level design into a low-level platform implementation. We formalize this synthesis problem and propose a technique for generating a mapping based on symbolic constraint search. We describe our prototype implementation, and two real-world case studies demonstrating the applicability of our technique to the synthesis of secure mappings for the popular web authorization protocols OAuth 1.0 and 2.0.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_12"}, {"primary_key": "2972823", "vector": [], "sparse_vector": [], "title": "The Marabou Framework for Verification and Analysis of Deep Neural Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Aleksandar <PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Deep neural networks are revolutionizing the way complex systems are designed. Consequently, there is a pressing need for tools and techniques for network analysis and certification. To help in addressing that need, we present Marabou, a framework for verifying deep neural networks. Marabou is an SMT-based tool that can answer queries about a network's properties by transforming these queries into constraint satisfaction problems. It can accommodate networks with different activation functions and topologies, and it performs high-level reasoning on the network that can curtail the search space and improve performance. It also supports parallel execution to further enhance scalability. Marabou accepts multiple input formats, including protocol buffer files generated by the popular TensorFlow framework for neural networks. We describe the system architecture and main components, evaluate the technique and discuss ongoing work.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_26"}, {"primary_key": "2972824", "vector": [], "sparse_vector": [], "title": "Flexible Computational Pipelines for Robust Abstraction-Based Control Synthesis.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Successfully synthesizing controllers for complex dynamical systems and specifications often requires leveraging domain knowledge as well as making difficult computational or mathematical tradeoffs. This paper presents a flexible and extensible framework for constructing robust control synthesis algorithms and applies this to the traditional abstraction-based control synthesis pipeline. It is grounded in the theory of relational interfaces and provides a principled methodology to seamlessly combine different techniques (such as dynamic precision grids, refining abstractions while synthesizing, or decomposed control predecessors) or create custom procedures to exploit an application's intrinsic structural properties. A Dubins vehicle is used as a motivating example to showcase memory and runtime improvements.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_34"}, {"primary_key": "2972825", "vector": [], "sparse_vector": [], "title": "Clock Bound Repair for Timed Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present algorithms and techniques for the repair of timed system models, given as networks of timed automata (NTA). The repair is based on an analysis of timed diagnostic traces (TDTs) that are computed by real-time model checking tools, such as UPPAAL, when they detect the violation of a timed safety property. We present an encoding of TDTs in linear real arithmetic and use the MaxSMT capabilities of the SMT solver Z3 to compute possible repairs to clock bound values that minimize the necessary changes to the automaton. We then present an admissibility criterion, called functional equivalence, that assesses whether a proposed repair is admissible in the overall context of the NTA. We have implemented a proof-of-concept tool called TarTar for the repair and admissibility analysis. To illustrate the method, we have considered a number of case studies taken from the literature and automatically injected changes to clock bounds to generate faulty mutations. Our technique is able to compute a feasible repair for $$91\\%$$ of the faults detected by UPPAAL in the generated mutants.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_5"}, {"primary_key": "2972826", "vector": [], "sparse_vector": [], "title": "Interpolating Strong Induction.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The principle of strong induction, also known as k-induction is one of the first techniques for unbounded SAT-based Model Checking (SMC). While elegant and simple to apply, properties as such are rarely k-inductive and when they can be strengthened, there is no effective strategy to guess the depth of induction. It has been mostly displaced by techniques that compute inductive strengthenings based on interpolation and property directed reachability (Pdr). In this paper, we present kAvy, an SMC algorithm that effectively uses k-induction to guide interpolation and Pdr-style inductive generalization. Unlike pure k-induction, kAvy uses Pdr-style generalization to compute and strengthen an inductive trace. Unlike pure Pdr, kAvy uses relative k-induction to construct an inductive invariant. The depth of induction is adjusted dynamically by minimizing a proof of unsatisfiability. We have implemented kAvy within the Avy Model Checker and evaluated it on HWMCC instances. Our results show that kAvy is more effective than both Avy and Pdr, and that using k-induction leads to faster running time and solving more instances. Further, on a class of benchmarks, called shift, kAvy is orders of magnitude faster than Avy, Pdr and k-induction.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_21"}, {"primary_key": "2972827", "vector": [], "sparse_vector": [], "title": "Verifying Asynchronous Interactions via Communicating Session Automata.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper proposes a sound procedure to verify properties of communicating session automata (csa), i.e., communicating automata that include multiparty session types. We introduce a new asynchronous compatibility property for csa, called k-multiparty compatibility (k-mc), which is a strict superset of the synchronous multiparty compatibility used in theories and tools based on session types. It is decomposed into two bounded properties: (i) a condition called k-safety which guarantees that, within the bound, all sent messages can be received and each automaton can make a move; and (ii) a condition called k-exhaustivity which guarantees that all k-reachable send actions can be fired within the bound. We show that k-exhaustivity implies existential boundedness, and soundly and completely characterises systems where each automaton behaves equivalently under bounds greater than or equal to k. We show that checking k-mc is pspace-complete, and demonstrate its scalability empirically over large systems (using partial order reduction).", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_6"}, {"primary_key": "2972828", "vector": [], "sparse_vector": [], "title": "Cerberus-BMC: A Principled Reference Semantics and Exploration Tool for Concurrent and Sequential C.", "authors": ["<PERSON>", "Victor <PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON>ara<PERSON><PERSON>", "<PERSON>"], "summary": "C remains central to our infrastructure, making verification of C code an essential and much-researched topic, but the semantics of C is remarkably complex, and important aspects of it are still unsettled, leaving programmers and verification tool builders on shaky ground. This paper describes a tool, Cerberus-BMC, that for the first time provides a principled reference semantics that simultaneously supports (1) a choice of concurrency memory model (including substantial fragments of the C11, RC11, and Linux kernel memory models), (2) a modern memory object model, and (3) a well-validated thread-local semantics for a large fragment of the language. The tool should be useful for C programmers, compiler writers, verification tool builders, and members of the C/C++ standards committees.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_22"}, {"primary_key": "2972830", "vector": [], "sparse_vector": [], "title": "AliveInLean: A Verified LLVM Peephole Optimization Verifier.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Ensuring that compiler optimizations are correct is important for the reliability of the entire software ecosystem, since all software is compiled. Alive [12] is a tool for verifying LLVM's peephole optimizations. Since Alive was released, it has helped compiler developers proactively find dozens of bugs in LLVM, avoiding potentially hazardous miscompilations. Despite having verified many LLVM optimizations so far, Alive is itself not verified, which has led to at least once declaring an optimization correct when it was not. We introduce AliveInLean, a formally verified peephole optimization verifier for LLVM. As the name suggests, AliveInLean is a reengineered version of Alive developed in the Lean theorem prover [14]. Assuming that the proof obligations are correctly discharged by an SMT solver, AliveInLean gives the same level of correctness guarantees as state-of-the-art formal frameworks such as CompCert [11], Peek [15], and Vellvm [26], while inheriting the advantages of Alive (significantly more automation and easy adoption by compiler developers).", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_25"}, {"primary_key": "2972831", "vector": [], "sparse_vector": [], "title": "Satisfiability Checking for Mission-Time LTL.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Mission-time LTL (MLTL) is a bounded variant of MTL over naturals designed to generically specify requirements for mission-based system operation common to aircraft, spacecraft, vehicles, and robots. Despite the utility of MLTL as a specification logic, major gaps remain in analyzing MLTL, e.g., for specification debugging or model checking, centering on the absence of any complete MLTL satisfiability checker. We prove that the MLTL satisfiability checking problem is NEXPTIME-complete and that satisfiability checking , the variant of MLTL where all intervals start at 0, is PSPACE-complete. We introduce translations for MLTL-to-LTL, , MLTL-to-SMV, and MLTL-to-SMT, creating four options for MLTL satisfiability checking. Our extensive experimental evaluation shows that the MLTL-to-SMT transition with the Z3 SMT solver offers the most scalable performance.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_1"}, {"primary_key": "2972832", "vector": [], "sparse_vector": [], "title": "Verifying Asynchronous Event-Driven Programs Using Partial Abstract Transformers.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We address the problem of analyzing asynchronous event-driven programs, in which concurrent agents communicate via unbounded message queues. The safety verification problem for such programs is undecidable. We present in this paper a technique that combines queue-bounded exploration with a convergence test: if the sequence of certain abstractions of the reachable states, for increasing queue bounds k, converges, we can prove any property of the program that is preserved by the abstraction. If the abstract state space is finite, convergence is guaranteed; the challenge is to catch the point $$k_{\\max }$$ where it happens. We further demonstrate how simple invariants formulated over the concrete domain can be used to eliminate spurious abstract states, which otherwise prevent the sequence from converging. We have implemented our technique for the P programming language for event-driven programs. We show experimentally that the sequence of abstractions often converges fully automatically, in hard cases with minimal designer support in the form of sequentially provable invariants, and that this happens for a value of $$k_{\\max }$$ small enough to allow the method to succeed in practice.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_22"}, {"primary_key": "2972833", "vector": [], "sparse_vector": [], "title": "Formal Verification of Quantum Algorithms Using Quantum Hoare Logic.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We formalize the theory of quantum Hoare logic (QHL) [TOPLAS 33(6),19], an extension of Hoare logic for reasoning about quantum programs. In particular, we formalize the syntax and semantics of quantum programs in Isabelle/HOL, write down the rules of quantum Hoare logic, and verify the soundness and completeness of the deduction system for partial correctness of quantum programs. As preliminary work, we formalize some necessary mathematical background in linear algebra, and define tensor products of vectors and matrices on quantum variables. As an application, we verify the correctness of <PERSON><PERSON>'s search algorithm. To our best knowledge, this is the first time a Hoare logic for quantum programs is formalized in an interactive theorem prover, and used to verify the correctness of a nontrivial quantum algorithm.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_12"}, {"primary_key": "2972834", "vector": [], "sparse_vector": [], "title": "Automated Parameterized Verification of CRDTs.", "authors": ["Kartik Nagar", "<PERSON><PERSON>"], "summary": "Maintaining multiple replicas of data is crucial to achieving scalability, availability and low latency in distributed applications.Conflict-free Replicated Data Types(CRDTs) are important building blocks in this domain because they are designed to operate correctly under the myriad behaviors possible in a weakly-consistent distributed setting. Because of the possibility of concurrent updates to the same object at different replicas, and the absence of any ordering guarantees on these updates,convergenceis an important correctness criterion for CRDTs. This property asserts that two replicas which receive the same set of updates (in any order) must nonetheless converge to the same state. One way to prove that operations on a CRDT converge is to show that they commute since commutative actions by definition behave the same regardless of the order in which they execute. In this paper, we present a framework for automatically verifying convergence of CRDTs under different weak-consistency policies. Surprisingly, depending upon the consistency policy supported by the underlying system, we show that not all operations of a CRDT need to commute to achieve convergence. We develop a proof rule parameterized by a consistency specification based on the concepts ofcommutativity modulo consistency policyandnon-interference to commutativity. We describe the design and implementation of a verification engine equipped with this rule and show how it can be used to provide the first automated convergence proofs for a number of challenging CRDTs, including sets, lists, and graphs.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_26"}, {"primary_key": "2972835", "vector": [], "sparse_vector": [], "title": "STAMINA: STochastic Approximate Model-Checker for INfinite-State Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Stochastic model checking is a technique for analyzing systems that possess probabilistic characteristics. However, its scalability is limited as probabilistic models of real-world applications typically have very large or infinite state space. This paper presents a new infinite state CTMC model checker, STAMINA, with improved scalability. It uses a novel state space approximation method to reduce large and possibly infinite state CTMC models to finite state representations that are amenable to existing stochastic model checkers. It is integrated with a new property-guided state expansion approach that improves the analysis accuracy. Demonstration of the tool on several benchmark examples shows promising results in terms of analysis efficiency and accuracy compared with a state-of-the-art CTMC model checker that deploys a similar approximation method.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_31"}, {"primary_key": "2972836", "vector": [], "sparse_vector": [], "title": "Overfitting in Synthesis: Theory and Practice.", "authors": ["Saswat Padhi", "<PERSON>", "<PERSON><PERSON><PERSON> V<PERSON>", "<PERSON><PERSON>"], "summary": "In syntax-guided synthesis (SyGuS), a synthesizer's goal is to automatically generate a program belonging to a grammar of possible implementations that meets a logical specification. We investigate a common limitation across state-of-the-art SyGuS tools that perform counterexample-guided inductive synthesis (CEGIS). We empirically observe that as the expressiveness of the provided grammar increases, the performance of these tools degrades significantly. We claim that this degradation is not only due to a larger search space, but also due to overfitting. We formally define this phenomenon and prove no-free-lunch theorems for SyGuS, which reveal a fundamental tradeoff between synthesizer performance and grammar expressiveness. A standard approach to mitigate overfitting in machine learning is to run multiple learners with varying expressiveness in parallel. We demonstrate that this insight can immediately benefit existing SyGuS tools. We also propose a novel single-threaded technique called hybrid enumeration that interleaves different grammars and outperforms the winner of the 2018 SyGuS competition (Inv track), solving more problems and achieving a $$5\\times $$ mean speedup.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_17"}, {"primary_key": "2972837", "vector": [], "sparse_vector": [], "title": "Incremental Determinization for Quantifier Elimination and Functional Synthesis.", "authors": ["<PERSON>"], "summary": "Quantifier elimination and its cousin functional synthesis are fundamental problems in automated reasoning that could be used in many applications of formal methods. But, effective algorithms are still elusive. In this paper, we suggest a simple modification to a QBF algorithm to adapt it for quantifier elimination and functional synthesis. We demonstrate that the approach significantly outperforms previous algorithms for functional synthesis.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_6"}, {"primary_key": "2972838", "vector": [], "sparse_vector": [], "title": "cvc4sy: Smart and Fast Term Enumeration for Syntax-Guided Synthesis.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present cvc4sy, a syntax-guided synthesis (SyGuS) solver based on three bounded term enumeration strategies. The first encodes term enumeration as an extension of the quantifier-free theory of algebraic datatypes. The second is based on a highly optimized brute-force algorithm. The third combines elements of the others. Our implementation of the strategies within the satisfiability modulo theories (SMT) solver cvc4 and a heuristic to choose between them leads to significant improvements over state-of-the-art SyGuS solvers.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_5"}, {"primary_key": "2972839", "vector": [], "sparse_vector": [], "title": "High-Level Abstractions for Simplifying Extended String Constraints in SMT.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Satisfiability Modulo Theories (SMT) solvers with support for the theory of strings have recently emerged as powerful tools for reasoning about string-manipulating programs. However, due to the complex semantics of extended string functions, it is challenging to develop scalable solvers for the string constraints produced by program analysis tools. We identify several classes of simplification techniques that are critical for the efficient processing of string constraints in SMT solvers. These techniques can reduce the size and complexity of input constraints by reasoning about arithmetic entailment, multisets, and string containment relationships over input terms. We provide experimental evidence that implementing them results in significant improvements over the performance of state-of-the-art SMT solvers for extended string constraints.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_2"}, {"primary_key": "2972840", "vector": [], "sparse_vector": [], "title": "Abstraction Refinement Algorithms for Timed Automata.", "authors": ["<PERSON>", "Ocan Sankur", "<PERSON>"], "summary": "We present abstraction-refinement algorithms for model checking safety properties of timed automata. The abstraction domain we consider abstracts away zones by restricting the set of clock constraints that can be used to define them, while the refinement procedure computes the set of constraints that must be taken into consideration in the abstraction so as to exclude a given spurious counterexample. We implement this idea in two ways: an enumerative algorithm where a lazy abstraction approach is adopted, meaning that possibly different abstract domains are assigned to each exploration node; and a symbolic algorithm where the abstract transition system is encoded with Boolean formulas.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_2"}, {"primary_key": "2972842", "vector": [], "sparse_vector": [], "title": "Property Directed Self Composition.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We address the problem of verifying k-safety properties: properties that refer to k interacting executions of a program. A prominent way to verify k-safety properties is by self composition. In this approach, the problem of checking k-safety over the original program is reduced to checking an \"ordinary\" safety property over a program that executes k copies of the original program in some order. The way in which the copies are composed determines how complicated it is to verify the composed program. We view this composition as provided by a semantic self composition function that maps each state of the composed program to the copies that make a move. Since the \"quality\" of a self composition function is measured by the ability to verify the safety of the composed program, we formulate the problem of inferring a self composition function together with the inductive invariant needed to verify safety of the composed program, where both are restricted to a given language. We develop a property-directed inference algorithm that, given a set of predicates, infers composition-invariant pairs expressed by Boolean combinations of the given predicates, or determines that no such pair exists. We implemented our algorithm and demonstrate that it is able to find self compositions that are beyond reach of existing tools.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_9"}, {"primary_key": "2972843", "vector": [], "sparse_vector": [], "title": "What&apos;s Wrong with On-the-Fly Partial Order Reduction.", "authors": ["<PERSON>"], "summary": "Partial order reduction and on-the-fly model checking are well-known approaches for improving model checking performance. The two optimizations interact in subtle ways, so care must be taken when using them in combination. A standard algorithm combining the two optimizations, published over twenty years ago, has been widely studied and deployed in popular model checking tools. Yet the algorithm is incorrect. Counterexamples were discovered using the Alloy analyzer. A fix for a restricted class of property automata is proposed.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_27"}, {"primary_key": "2972844", "vector": [], "sparse_vector": [], "title": "Loop Summarization with Rational Vector Addition Systems.", "authors": ["<PERSON>", "<PERSON>"], "summary": "This paper presents a technique for computing numerical loop summaries. The method synthesizes a rational vector addition system with resets ( $$\\mathbb {Q}$$ -VASR) that simulates the action of an input loop, and then uses the reachability relation of that $$\\mathbb {Q}$$ -VASR to over-approximate the behavior of the loop. The key technical problem solved in this paper is to automatically synthesize a $$\\mathbb {Q}$$ -VASR that is a best abstraction of a given loop in the sense that (1) it simulates the loop and (2) it is simulated by any other $$\\mathbb {Q}$$ -VASR that simulates the loop. Since our loop summarization scheme is based on computing the exact reachability relation of a best abstraction of a loop, we can make theoretical guarantees about its behavior. Moreover, we show experimentally that the technique is precise and performant in practice.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_7"}, {"primary_key": "2972846", "vector": [], "sparse_vector": [], "title": "Membership-Based Synthesis of Linear Hybrid Automata.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Luka Zeleznik"], "summary": "We present two algorithmic approaches for synthesizing linear hybrid automata from experimental data. Unlike previous approaches, our algorithms work without a template and generate an automaton with nondeterministic guards and invariants, and with an arbitrary number and topology of modes. They thus construct a succinct model from the data and provide formal guarantees. In particular, (1) the generated automaton can reproduce the data up to a specified tolerance and (2) the automaton is tight, given the first guarantee. Our first approach encodes the synthesis problem as a logical formula in the theory of linear arithmetic, which can then be solved by an smt solver. This approach minimizes the number of modes in the resulting model but is only feasible for limited data sets. To address scalability, we propose a second approach that does not enforce to find a minimal model. The algorithm constructs an initial automaton and then iteratively extends the automaton based on processing new data. Therefore the algorithm is well-suited for online and synthesis-in-the-loop applications. The core of the algorithm is a membership query that checks whether, within the specified tolerance, a given data set can result from the execution of a given automaton. We solve this membership problem for linear hybrid automata by repeated reachability computations. We demonstrate the effectiveness of the algorithm on synthetic data sets and on cardiac-cell measurements.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_16"}, {"primary_key": "2972847", "vector": [], "sparse_vector": [], "title": "Quantitative Mitigation of Timing Side Channels.", "authors": ["<PERSON><PERSON><PERSON>", "Pa<PERSON><PERSON> Cerný", "<PERSON><PERSON><PERSON>"], "summary": "Timing side channels pose a significant threat to the security and privacy of software applications. We propose an approach for mitigating this problem by decreasing the strength of the side channels as measured by entropy-based objectives, such as min-guess entropy. Our goal is to minimize the information leaks while guaranteeing a user-specified maximal acceptable performance overhead. We dub the decision version of this problem Shannon mitigation, and consider two variants, deterministic and stochastic. First, we show that the deterministic variant is NP-hard. However, we give a polynomial algorithm that finds an optimal solution from a restricted set. Second, for the stochastic variant, we develop an approach that uses optimization techniques specific to the entropy-based objective used. For instance, for min-guess entropy, we used mixed integer-linear programming. We apply the algorithm to a threat model where the attacker gets to make functional observations, that is, where she observes the running time of the program for the same secret value combined with different public input values. Existing mitigation approaches do not give confidentiality or performance guarantees for this threat model. We evaluate our tool Schmit on a number of micro-benchmarks and real-world applications with different entropy-based objectives. In contrast to the existing mitigation approaches, we show that in the functional-observation threat model, <PERSON>hm<PERSON> is scalable and able to maximize confidentiality under the performance overhead bound.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_8"}, {"primary_key": "2972848", "vector": [], "sparse_vector": [], "title": "Symbolic Monitoring Against Specifications Parametric in Time and Data.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Monitoring consists in deciding whether a log meets a given specification. In this work, we propose an automata-based formalism to monitor logs in the form of actions associated with time stamps and arbitrarily data values over infinite domains. Our formalism uses both timing parameters and data parameters, and is able to output answers symbolic in these parameters and in the log segments where the property is satisfied or violated. We implemented our approach in an ad-hoc prototype SyMon, and experiments show that its high expressive power still allows for efficient online monitoring.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_30"}, {"primary_key": "2972850", "vector": [], "sparse_vector": [], "title": "Gradual Consistency Checking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Constantin <PERSON>", "<PERSON>"], "summary": "We address the problem of checking that computations of a shared memory implementation (with write and read operations) adheres to some given consistency model. It is known that checking conformance to Sequential Consistency (SC) for a given computation is NP-hard, and the same holds for checking Total Store Order (TSO) conformance. This poses a serious issue for the design of scalable verification or testing techniques for these important memory models. In this paper, we tackle this issue by providing an approach that avoids hitting systematically the worst-case complexity. The idea is to consider, as an intermediary step, the problem of checking weaker criteria that are as strong as possible while they are still checkable in polynomial time (in the size of the computation). The criteria we consider are new variations of causal consistency suitably defined for our purpose. The advantage of our approach is that in many cases (1) it can catch violations of SC/TSO early using these weaker criteria that are efficiently checkable, and (2) when a computation is causally consistent (according to our newly defined criteria), the work done for establishing this fact simplifies significantly the work required for checking SC/TSO conformance. We have implemented our algorithms and carried out several experiments on realistic cache-coherence protocols showing the efficiency of our approach.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_16"}, {"primary_key": "2972851", "vector": [], "sparse_vector": [], "title": "Multi-armed Bandits for Boolean Connectives in Hybrid System Falsification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Hybrid system falsification is an actively studied topic, as a scalable quality assurance methodology for real-world cyber-physical systems. In falsification, one employs stochastic hill-climbing optimization to quickly find a counterexample input to a black-box system model. Quantitative robust semantics is the technical key that enables use of such optimization. In this paper, we tackle the so-called scale problem regarding Boolean connectives that is widely recognized in the community: quantities of different scales (such as speed [km/h] vs. rpm, or worse, rph) can mask each other's contribution to robustness. Our solution consists of integration of the multi-armed bandit algorithms in hill climbing-guided falsification frameworks, with a technical novelty of a new reward notion that we call hill-climbing gain. Our experiments show our approach's robustness under the change of scales, and that it outperforms a state-of-the-art falsification tool.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25540-4_23"}, {"primary_key": "2972852", "vector": [], "sparse_vector": [], "title": "Rely-Guarantee Reasoning About Concurrent Memory Management in Zephyr RTOS.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Formal verification of concurrent operating systems (OSs) is challenging, and in particular the verification of the dynamic memory management due to its complex data structures and allocation algorithm. Up to our knowledge, this paper presents the first formal specification and mechanized proof of a concurrent buddy memory allocation for a real-world OS. We develop a fine-grained formal specification of the buddy memory management in Zephyr RTOS. To ease validation of the specification and the source code, the provided specification closely follows the C code. Then, we use the rely-guarantee technique to conduct the compositional verification of functional correctness and invariant preservation. During the formal verification, we found three bugs in the C code of Zephyr.", "published": "2019-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-030-25543-5_29"}]