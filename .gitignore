# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
.pnp
.pnp.js
.log
/logs
logs/
tmp/
*.exe


# testing
coverage
# backend/test.ipynb

semantic_search_log.txt
*.key

backend/data/aaa_crawler_code/output
backend/data/aaa_crawler_code_new/test_output
backend/data/aaa_crawler_code_new/sigkdd_papers
backend/pdf
backend/md
backend/utils/keys/

backend/data/acl-anthology-master
backend/data/continue
backend/data/database
backend/data/paper_acl
backend/data/paper_arxiv
backend/data/paper_deal
backend/data/paper_cv_old
backend/data/paper_jn_old
backend/data/paper_conf
backend/data/paper_journal
backend/data/papers
backend/data/tmp
backend/data/old
backend/service/logs
siliconflow_balance.json


public/data/papers

# next.js
.next/
out/
build
__pycache__/

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env.local
.env.development.local
.env.test.local
.env.production.local

# turbo
.turbo

.env
.vercel
.vscode
.env*.local
backend/test_tags.ipynb
