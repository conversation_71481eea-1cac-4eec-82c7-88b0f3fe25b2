import { Search } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { BetterTooltip } from "@/components/ui/tooltip"
import { Badge } from "@/components/ui/badge"
import { ChevronDown } from "lucide-react"
import { conferenceOptions, journalOptions } from './conference-info'

interface ConferenceOption {
  value: string;
  label: string;
}

interface JournalOption {
  value: string;
  label: string;
}

interface SearchBarProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  isSearching: boolean;
  searchState: string;
  isTitleTranslation: boolean;
  setIsTitleTranslation: (value: boolean) => void;
  handleSearch: () => void;
  sourceType: 'arxiv' | 'conference' | 'journal';
  setSourceType: (type: 'arxiv' | 'conference' | 'journal') => void;
  selectedYears: string[];
  yearOptions: string[];
  handleYearChange: (year: string) => void;
  isYearsOpen: boolean;
  setIsYearsOpen: (open: boolean) => void;
  selectedConferences: string[];
  handleConferenceChange: (conference: string) => void;
  isConferencesOpen: boolean;
  setIsConferencesOpen: (open: boolean) => void;
  selectedJournals: string[];
  handleJournalChange: (journal: string) => void;
  isJournalsOpen: boolean;
  setIsJournalsOpen: (open: boolean) => void;
  isMoreOptionsOpen: boolean;
  setIsMoreOptionsOpen: (open: boolean) => void;
  yearSelectorRef: React.RefObject<HTMLDivElement>;
  conferencesSelectorRef: React.RefObject<HTMLDivElement>;
  journalsSelectorRef: React.RefObject<HTMLDivElement>;
  isSourceSelectorOpen?: boolean;
  setIsSourceSelectorOpen?: (open: boolean) => void;
  sourceSelectorRef?: React.RefObject<HTMLDivElement>;
}

export default function SearchBar({
  searchQuery,
  setSearchQuery,
  isSearching,
  searchState,
  isTitleTranslation,
  setIsTitleTranslation,
  handleSearch,
  sourceType,
  setSourceType,
  selectedYears,
  yearOptions,
  handleYearChange,
  isYearsOpen,
  setIsYearsOpen,
  selectedConferences,
  handleConferenceChange,
  isConferencesOpen,
  setIsConferencesOpen,
  selectedJournals,
  handleJournalChange,
  isJournalsOpen,
  setIsJournalsOpen,
  isMoreOptionsOpen,
  setIsMoreOptionsOpen,
  yearSelectorRef,
  conferencesSelectorRef,
  journalsSelectorRef,
  isSourceSelectorOpen,
  setIsSourceSelectorOpen,
  sourceSelectorRef
}: SearchBarProps) {
  return (
    <div className="space-y-4 w-full max-w-md">
      <div className="flex flex-col gap-4 items-center">
        {/* 输入框 */}
        <div className="relative w-full">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input 
            value={searchQuery}
            type="text" 
            autoComplete="off"
            inputMode="text"
            className="pl-10"
            placeholder="随意描述主题，尽可能详细（支持中文）" 
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                if (!isSearching && searchQuery.trim()) {
                  handleSearch();
                }
              }
            }}
          />
        </div>

        {/* 上排：论文来源、翻译、搜索按钮 */}
        <div className="flex w-full gap-2">
          {/* 论文来源选择 */}
          <div className="relative flex-[1.5]" ref={sourceSelectorRef}>
            <Button
              type="button"
              variant="outline"
              className="w-full justify-between h-10 hover:bg-gray-100 dark:hover:bg-gray-800"
              onClick={() => {
                if (setIsSourceSelectorOpen) {
                  setIsSourceSelectorOpen(!isSourceSelectorOpen);
                }
              }}
            >
              {sourceType === 'arxiv' ? 'arXiv预印本' : sourceType === 'conference' ? '已发表会议' : '已发表期刊'}
              <ChevronDown className={`ml-2 h-4 w-4 transition-transform ${isSourceSelectorOpen ? 'transform rotate-180' : ''}`} />
            </Button>
            
            {isSourceSelectorOpen && (
              <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-900 border dark:border-gray-700 rounded-md shadow-lg">
                <div className="p-1">
                  <div
                    className={`flex items-center px-2 py-1.5 text-sm rounded-sm cursor-pointer ${sourceType === 'arxiv' ? 'bg-blue-50 dark:bg-blue-900' : 'hover:bg-gray-100 dark:hover:bg-gray-800'}`}
                    onClick={() => {
                      setSourceType('arxiv');
                      if (setIsSourceSelectorOpen) {
                        setIsSourceSelectorOpen(false);
                      }
                    }}
                  >
                    arXiv预印本
                  </div>
                  <div
                    className={`flex items-center px-2 py-1.5 text-sm rounded-sm cursor-pointer ${sourceType === 'conference' ? 'bg-blue-50 dark:bg-blue-900' : 'hover:bg-gray-100 dark:hover:bg-gray-800'}`}
                    onClick={() => {
                      setSourceType('conference');
                      if (setIsSourceSelectorOpen) {
                        setIsSourceSelectorOpen(false);
                      }
                    }}
                  >
                    已发表会议
                  </div>
                  <div
                    className={`flex items-center px-2 py-1.5 text-sm rounded-sm cursor-pointer ${sourceType === 'journal' ? 'bg-blue-50 dark:bg-blue-900' : 'hover:bg-gray-100 dark:hover:bg-gray-800'}`}
                    onClick={() => {
                      setSourceType('journal');
                      if (setIsSourceSelectorOpen) {
                        setIsSourceSelectorOpen(false);
                      }
                    }}
                  >
                    已发表期刊
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* 标题翻译开关 */}
          <BetterTooltip 
            content={
              <div className="max-w-[260px] text-center text-sm">
                {isTitleTranslation ? '关闭标题翻译' : '开启标题翻译'}
              </div>
            }
            align='center'
            side="bottom"
          >
            <Button
              className={`flex-[1] ${isTitleTranslation ? 'bg-gray-300 hover:bg-gray-250' : 'bg-gray-100 hover:bg-gray-250'} text-gray-700`}
              variant="ghost"
              onClick={() => setIsTitleTranslation(!isTitleTranslation)}
            >
              <span className="font-bold text-md">
                <span className="hidden sm:inline">标题翻译</span>
                <span className="sm:hidden">翻译</span>
              </span>
            </Button>
          </BetterTooltip>

          {/* 搜索按钮 */}
          <Button
            className='flex-[1] bg-gray-100 hover:bg-gray-300 text-gray-700'
            variant="ghost"
            onClick={() => handleSearch()}
            disabled={isSearching}
          >
            <span className="font-bold text-md">
              {isSearching ? searchState : '搜索'}
            </span>
          </Button>
        </div>

        {/* 更多选项按钮 */}
        <div className="w-full flex justify-start">
          <Button
            type="button"
            variant="ghost"
            className="text-xs text-gray-500 hover:text-gray-700 px-2 py-1 h-auto"
            onClick={() => setIsMoreOptionsOpen(!isMoreOptionsOpen)}
          >
            <span>更多选项</span>
            <ChevronDown className={`ml-1 h-3 w-3 transition-transform ${isMoreOptionsOpen ? 'transform rotate-180' : ''}`} />
          </Button>
        </div>

        {/* 更多选项框 */}
        {isMoreOptionsOpen && (
          <div className="w-full p-3 border rounded-lg bg-white dark:bg-gray-900 shadow-sm">
            <div className="flex gap-4">
              {/* 年份多选下拉框 */}
              <div className="relative flex-1" ref={yearSelectorRef}>
                <Button
                  type="button"
                  variant="outline"
                  className="w-full justify-between"
                  onClick={() => {
                    setIsYearsOpen(!isYearsOpen);
                  }}
                >
                  {selectedYears.length === 0 ? (
                    <>
                      <span className="hidden sm:inline">年份（默认 10-25）</span>
                      <span className="sm:hidden">年份</span>
                    </>
                  ) : (
                    `${selectedYears.length} 个年份`
                  )}
                  <ChevronDown className={`ml-2 h-4 w-4 transition-transform ${isYearsOpen ? 'transform rotate-180' : ''}`} />
                </Button>

                {/* 已选年份标签 */}
                {selectedYears.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-2">
                    {selectedYears.map(year => (
                      <Badge
                        key={year}
                        variant="secondary"
                        className="cursor-pointer"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleYearChange(year);
                        }}
                      >
                        {year} ✕
                      </Badge>
                    ))}
                  </div>
                )}

                {/* 年份选项 */}
                {isYearsOpen && (
                  <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-900 border dark:border-gray-700 rounded-md shadow-lg max-h-60 overflow-auto">
                    <div className="p-1">
                      {yearOptions.map(year => (
                        <div
                          key={year}
                          className={`
                            flex items-center px-2 py-1.5 text-sm rounded-sm cursor-pointer
                            ${selectedYears.includes(year) 
                              ? 'bg-blue-50 dark:bg-blue-900' 
                              : 'hover:bg-gray-100 dark:hover:bg-gray-800'}
                          `}
                          onClick={() => handleYearChange(year)}
                        >
                          <input
                            type="checkbox"
                            checked={selectedYears.includes(year)}
                            readOnly
                            className="mr-2"
                          />
                          {year}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* 会议多选按钮 - 仅在选择已发表会议时显示 */}
              {sourceType === 'conference' && (
                <div className="relative flex-1" ref={conferencesSelectorRef}>
                  <Button
                    type="button"
                    variant="outline"
                    className="w-full justify-between"
                    onClick={() => {
                      setIsConferencesOpen(!isConferencesOpen);
                    }}
                  >
                    {selectedConferences.length === 0 ? (
                      <span className="text-sm">选择会议（默认全选）</span>
                    ) : (
                      <span className="text-sm">{selectedConferences.length} 个会议</span>
                    )}
                    <ChevronDown className={`ml-2 h-4 w-4 transition-transform ${isConferencesOpen ? 'transform rotate-180' : ''}`} />
                  </Button>

                  {/* 已选会议标签 */}
                  {selectedConferences.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      <Badge
                        variant="secondary"
                        className="cursor-pointer bg-red-100 dark:bg-red-900 hover:bg-red-200 dark:hover:bg-red-800 text-red-700 dark:text-red-200"
                        onClick={(e) => {
                          e.stopPropagation();
                          selectedConferences.forEach(conf => handleConferenceChange(conf));
                        }}
                      >
                        清空选择
                      </Badge>
                      {selectedConferences.map(conf => {
                        const confLabel = conferenceOptions.find((option: ConferenceOption) => option.value === conf)?.label;
                        return (
                          <Badge
                            key={conf}
                            variant="secondary"
                            className="cursor-pointer"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleConferenceChange(conf);
                            }}
                          >
                            {confLabel} ✕
                          </Badge>
                        );
                      })}
                    </div>
                  )}

                  {/* 会议选项 */}
                  {isConferencesOpen && (
                    <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-900 border dark:border-gray-700 rounded-md shadow-lg max-h-60 overflow-auto">
                      <div className="p-1">
                        {conferenceOptions.map((conference: ConferenceOption) => (
                          <div
                            key={conference.value}
                            className={`
                              flex items-center px-2 py-1.5 text-sm rounded-sm cursor-pointer
                              ${selectedConferences.includes(conference.value) 
                                ? 'bg-blue-50 dark:bg-blue-900' 
                                : 'hover:bg-gray-100 dark:hover:bg-gray-800'}
                            `}
                            onClick={() => handleConferenceChange(conference.value)}
                          >
                            <input
                              type="checkbox"
                              checked={selectedConferences.includes(conference.value)}
                              readOnly
                              className="mr-2"
                            />
                            {conference.label}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* 期刊多选按钮 - 仅在选择已发表期刊时显示 */}
              {sourceType === 'journal' && (
                <div className="relative flex-1" ref={journalsSelectorRef}>
                  <Button
                    type="button"
                    variant="outline"
                    className="w-full justify-between"
                    onClick={() => {
                      setIsJournalsOpen(!isJournalsOpen);
                    }}
                  >
                    {selectedJournals.length === 0 ? (
                      <span className="text-sm">选择期刊（默认全选）</span>
                    ) : (
                      <span className="text-sm">{selectedJournals.length} 个期刊</span>
                    )}
                    <ChevronDown className={`ml-2 h-4 w-4 transition-transform ${isJournalsOpen ? 'transform rotate-180' : ''}`} />
                  </Button>

                  {/* 已选期刊标签 */}
                  {selectedJournals.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      <Badge
                        variant="secondary"
                        className="cursor-pointer bg-red-100 dark:bg-red-900 hover:bg-red-200 dark:hover:bg-red-800 text-red-700 dark:text-red-200"
                        onClick={(e) => {
                          e.stopPropagation();
                          selectedJournals.forEach(journal => handleJournalChange(journal));
                        }}
                      >
                        清空选择
                      </Badge>
                      {selectedJournals.map(journal => {
                        const journalLabel = journalOptions.find((option: JournalOption) => option.value === journal)?.label;
                        return (
                          <Badge
                            key={journal}
                            variant="secondary"
                            className="cursor-pointer"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleJournalChange(journal);
                            }}
                          >
                            {journalLabel} ✕
                          </Badge>
                        );
                      })}
                    </div>
                  )}

                  {/* 期刊选项 */}
                  {isJournalsOpen && (
                    <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-900 border dark:border-gray-700 rounded-md shadow-lg max-h-60 overflow-auto">
                      <div className="p-1">
                        {journalOptions.map((journal: JournalOption) => (
                          <div
                            key={journal.value}
                            className={`
                              flex items-center px-2 py-1.5 text-sm rounded-sm cursor-pointer
                              ${selectedJournals.includes(journal.value) 
                                ? 'bg-blue-50 dark:bg-blue-900' 
                                : 'hover:bg-gray-100 dark:hover:bg-gray-800'}
                            `}
                            onClick={() => handleJournalChange(journal.value)}
                          >
                            <input
                              type="checkbox"
                              checked={selectedJournals.includes(journal.value)}
                              readOnly
                              className="mr-2"
                            />
                            {journal.label}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}