import { useRef, useEffect, useState } from "react";
import { LatexEditor } from "@/components/latex/LatexEditor";
import { LatexPreview } from "@/components/latex/LatexPreview";
import { LatexToolbar } from "@/components/latex/LatexToolbar";
import { X } from 'lucide-react';
import { Button } from "@/components/ui/button";

interface FormulaEditorModalProps {
  isOpen: boolean;
  onClose: () => void;
  latex: string;
  setLatex: (val: string) => void;
}

export default function FormulaEditorModal({
  isOpen,
  onClose,
  latex,
  setLatex,
}: FormulaEditorModalProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth < 640);
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // 图片上传逻辑
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    const reader = new FileReader();
    reader.onload = async () => {
      const base64 = (reader.result as string).split(",")[1];
      const res = await fetch("/api/sc-api-chat", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ imageBase64: base64 })
      });
      const data = await res.json();
      if (data.latex) {
        setLatex(latex + data.latex);
      } else {
        alert("识别失败");
      }
    };
    reader.readAsDataURL(file);
  };

  if (!isOpen) return null;

  if (isMobile) {
    // 直接渲染为全屏界面
    return (
      <div className="fixed inset-0 z-50 bg-white dark:bg-zinc-900 flex flex-col p-4" style={{height: '100vh', width: '100vw'}}>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold">LaTeX 公式编辑器</h2>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="rounded-full"
            aria-label="关闭"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>
        <input
          type="file"
          accept="image/*"
          ref={fileInputRef}
          style={{ display: "none" }}
          onChange={handleImageUpload}
        />
        <LatexToolbar onInsert={formula => setLatex(latex + formula)} />
        <div className="flex flex-col gap-4 mt-4 flex-1 overflow-auto">
          <div className="min-h-[80px]">
            <LatexEditor value={latex} onChange={setLatex} />
          </div>
          <div className="min-h-[80px] bg-gray-50 dark:bg-zinc-800 rounded p-2 flex-1">
            <LatexPreview formula={latex} />
          </div>
        </div>
      </div>
    );
  }
  // 大屏幕弹窗模式
  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/40"
      onClick={onClose}
    >
      <div
        className="bg-white dark:bg-zinc-900 p-6 w-full max-w-4xl mx-2 sm:mx-4 overflow-auto rounded-lg shadow-lg relative"
        style={{
          height: '100%',
          maxHeight: '90vh',
        }}
        onClick={e => e.stopPropagation()}
      >
        <Button
          variant="ghost"
          size="icon"
          onClick={onClose}
          className="absolute top-4 right-4 rounded-full z-10"
          aria-label="关闭"
        >
          <X className="h-5 w-5" />
        </Button>
        <h2 className="text-xl font-bold mb-4">LaTeX 公式编辑器</h2>
        <input
          type="file"
          accept="image/*"
          ref={fileInputRef}
          style={{ display: "none" }}
          onChange={handleImageUpload}
        />
        <LatexToolbar onInsert={formula => setLatex(latex + formula)} />
        <div className="flex flex-col gap-4 mt-4">
          <div className="flex-1 min-h-[80px]">
            <LatexEditor value={latex} onChange={setLatex} />
          </div>
          <div className="flex-1 min-h-[80px] bg-gray-50 dark:bg-zinc-800 rounded p-2">
            <LatexPreview formula={latex} />
          </div>
        </div>
      </div>
    </div>
  );
} 