[{"primary_key": "4066260", "vector": [], "sparse_vector": [], "title": "Reliable Third-Party Library Detection in Android and its Security Applications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Third-party libraries on Android have been shown to be security and privacy hazards by adding security vulnerabilities to their host apps or by misusing inherited access rights. Correctly attributing improper app behavior either to app or library developer code or isolating library code from their host apps would be highly desirable to mitigate these problems, but is impeded by the absence of a third-party library detection that is effective and reliable in spite of obfuscated code. This paper proposes a library detection technique that is resilient against common code obfuscations and that is capable of pinpointing the exact library version used in apps. Libraries are detected with profiles from a comprehensive library database that we generated from the original library SDKs. We apply our technique to the top apps on Google Play and their complete histories to conduct a longitudinal study of library usage and evolution in apps. Our results particularly show that app developers only slowly adapt new library versions, exposing their end-users to large windows of vulnerability. For instance, we discovered that two long-known security vulnerabilities in popular libs are still present in the current top apps. Moreover, we find that misuse of cryptographic APIs in advertising libs, which increases the host apps' attack surface, affects 296 top apps with a cumulative install base of 3.7bn devices according to Play. To the best of our knowledge, our work is first to quantify the security impact of third-party libs on the Android ecosystem.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978333"}, {"primary_key": "4066262", "vector": [], "sparse_vector": [], "title": "Membership Privacy in MicroRNA-based Studies.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The continuous decrease in cost of molecular profiling tests is revolutionizing medical research and practice, but it also raises new privacy concerns. One of the first attacks against privacy of biological data, proposed by <PERSON> et al. in 2008, showed that, by knowing parts of the genome of a given individual and summary statistics of a genome-based study, it is possible to detect if this individual participated in the study. Since then, a lot of work has been carried out to further study the theoretical limits and to counter the genome-based membership inference attack. However, genomic data are by no means the only or the most influential biological data threatening personal privacy. For instance, whereas the genome informs us about the risk of developing some diseases in the future, epigenetic biomarkers, such as microRNAs, are directly and deterministically affected by our health condition including most common severe diseases. In this paper, we show that the membership inference attack also threatens the privacy of individuals contributing their microRNA expressions to scientific studies. Our results on real and public microRNA expression data demonstrate that disease-specific datasets are especially prone to membership detection, offering a true-positive rate of up to 77% at a false-negative rate of less than 1%. We present two attacks: one relying on the L_1 distance and the other based on the likelihood-ratio test. We show that the likelihood-ratio test provides the highest adversarial success and we derive a theoretical limit on this success. In order to mitigate the membership inference, we propose and evaluate both a differentially private mechanism and a hiding mechanism. We also consider two types of adversarial prior knowledge for the differentially private mechanism and show that, for relatively large datasets, this mechanism can protect the privacy of participants in miRNA-based studies against strong adversaries without degrading the data utility too much. Based on our findings and given the current number of miRNAs, we recommend to only release summary statistics of datasets containing at least a couple of hundred individuals.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978355"}, {"primary_key": "4066263", "vector": [], "sparse_vector": [], "title": "POSTER: The ART of App Compartmentalization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "On Android, advertising libraries are commonly integrated with their host apps. Since the host and advertising components share the application's sandbox, advertisement code inherits all permissions and can access host resources with no further approval needed. Motivated by the privacy risks of advertisement libraries as already shown in the literature, this poster introduces an Android Runtime (ART) based app compartmentalization mechanism to achieve separation between trusted app code and untrusted library code without system modification and application rewriting. With our approach, advertising libraries will be isolated from the host app and the original app will be partitioned into two sub-apps that run independently, with the host app's resources and permissions being protected by Android's app sandboxing mechanism. ARTist [1], a compiler-based Android app instrumentation framework, is utilized here to recreate the communication channels between host and advertisement library. The result is a robust toolchain on device which provides a clean separation of developer-written app code and third-party advertisement code, allowing for finer-grained access control policies and information flow control without OS customization and application rebuilding.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989056"}, {"primary_key": "4066264", "vector": [], "sparse_vector": [], "title": "Computational Soundness for Dalvik Bytecode.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Automatically analyzing information flow within Android applications that rely on cryptographic operations with their computational security guarantees imposes formidable challenges that existing approaches for understanding an app's behavior struggle to meet. These approaches do not distinguish cryptographic and non-cryptographic operations, and hence do not account for cryptographic protections: f(m) is considered sensitive for a sensitive message m irrespective of potential secrecy properties offered by a cryptographic operation f. These approaches consequently provide a safe approximation of the app's behavior, but they mistakenly classify a large fraction of apps as potentially insecure and consequently yield overly pessimistic results. In this paper, we show how cryptographic operations can be faithfully included into existing approaches for automated app analysis. To this end, we first show how cryptographic operations can be expressed as symbolic abstractions within the comprehensive Dalvik bytecode language. These abstractions are accessible to automated analysis, and they can be conveniently added to existing app analysis tools using minor changes in their semantics. Second, we show that our abstractions are faithful by providing the first computational soundness result for Dalvik bytecode, i.e., the absence of attacks against our symbolically abstracted program entails the absence of any attacks against a suitable cryptographic program realization. We cast our computational soundness result in the CoSP framework, which makes the result modular and composable.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978418"}, {"primary_key": "4066265", "vector": [], "sparse_vector": [], "title": "A Protocol for Privately Reporting Ad Impressions at Scale.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a protocol to enable privacy preserving advertising reporting at scale. Unlike previous systems, our work scales to millions of users and tens of thousands of distinct ads. Our approach builds on the homomorphic encryption approach proposed by Ad<PERSON><PERSON>, but uses new cryptographic proof techniques to efficiently report billions of ad impressions a day using an additively homomorphic voting schemes. Most importantly, our protocol scales without imposing high loads on trusted third parties. Finally, we investigate a cost effective method to privately deliver ads with computational private information retrieval.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978407"}, {"primary_key": "4066266", "vector": [], "sparse_vector": [], "title": "POSTER: Locally Virtualized Environment for Mitigating Ransomware Threat.", "authors": ["<PERSON><PERSON>", "Sutapa <PERSON>", "<PERSON><PERSON>"], "summary": "Ransomware is one of the rising malwares in the crimeware family. It encrypts the user files and demands extortion money. From the perspective of an enterprise it is very crucial to detect and stop a ransomware attack. A well studied technique is to monitor file system behavior for suspicious activity. In this work we will show the gap in the existing state of art and describe a dynamic system which learns new behavior while under attack.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989051"}, {"primary_key": "4066267", "vector": [], "sparse_vector": [], "title": "MPC-Friendly Symmetric Key Primitives.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We discuss the design of symmetric primitives, in particular Pseudo-Random Functions (PRFs) which are suitable for use in a secret-sharing based MPC system. We consider three different PRFs: the Naor-Reingold PRF, a PRF based on the Legendre symbol, and a specialized block cipher design called MiMC. We present protocols for implementing these PRFs within a secret-sharing based MPC system, and discuss possible applications. We then compare the performance of our protocols. Depending on the application, different PRFs may offer different optimizations and advantages over the classic AES benchmark. Thus, we cannot conclude that there is one optimal PRF to be used in all situations.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978332"}, {"primary_key": "4066269", "vector": [], "sparse_vector": [], "title": "MTD 2016: Third ACM Workshop on Moving Target Defense.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The 2016 MTD (Moving Target Defense) workshop seeks to bring together researchers from academia, government, and industry to report on the latest research efforts on moving-target defense, and to have productive discussion and constructive debate on this topic. It is a single day workshop co-located with ACM CCS (Conference on Computer and Communications Security) 2016.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2990483"}, {"primary_key": "4066271", "vector": [], "sparse_vector": [], "title": "Deep Learning with Differential Privacy.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Machine learning techniques based on neural networks are achieving remarkable results in a wide variety of domains. Often, the training of models requires large, representative datasets, which may be crowdsourced and contain sensitive information. The models should not expose private information in these datasets. Addressing this goal, we develop new algorithmic techniques for learning and a refined analysis of privacy costs within the framework of differential privacy. Our implementation and experiments demonstrate that we can train deep neural networks with non-convex objectives, under a modest privacy budget, and at a manageable cost in software complexity, training efficiency, and model quality.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978318"}, {"primary_key": "4066273", "vector": [], "sparse_vector": [], "title": "C-FLAT: Control-Flow Attestation for Embedded Systems Software.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>. <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Remote attestation is a crucial security service particularly relevant to increasingly popular IoT (and other embedded) devices. It allows a trusted party (verifier) to learn the state of a remote, and potentially malware-infected, device (prover). Most existing approaches are static in nature and only check whether benign software is initially loaded on the prover. However, they are vulnerable to runtime attacks that hijack the application's control or data flow, e.g., via return-oriented programming or data-oriented exploits. As a concrete step towards more comprehensive runtime remote attestation, we present the design and implementation of Control-FLow ATtestation (C-FLAT) that enables remote attestation of an application's control-flow path, without requiring the source code. We describe a full prototype implementation of C-FLAT on Raspberry Pi using its ARM TrustZone hardware security extensions. We evaluate C-FLAT's performance using a real-world embedded (cyber-physical) application, and demonstrate its efficacy against control-flow hijacking attacks.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978358"}, {"primary_key": "4066281", "vector": [], "sparse_vector": [], "title": "POSTER: Privacy Enhanced Secure Location Verification.", "authors": ["<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose a privacy enhanced location verification system that uses in-region location verification to verify if a location claim is from within an area specified by a policy. The novelty of our work is the use of distance bounding protocols to construct a pseudo-rectangle (P-rectangle) that optimizes coverage of the policy area, and uses it to verify the claim with respect to the P-rectangle, thereby minimizing error. We propose a privacy enhancement for the system that ensures that the prover's location cannot be inferred by an adversary who monitors protocol messages. We discuss our results and propose directions for future research.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989066"}, {"primary_key": "4066283", "vector": [], "sparse_vector": [], "title": "A Surfeit of SSH Cipher Suites.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This work presents a systematic analysis of symmetric encryption modes for SSH that are in use on the Internet, providing deployment statistics, new attacks, and security proofs for widely used modes. We report deployment statistics based on two Internet-wide scans of SSH servers conducted in late 2015 and early 2016. Dropbear and OpenSSH implementations dominate in our scans. From our first scan, we found 130,980 OpenSSH servers that are still vulnerable to the CBC-mode-specific attack of <PERSON><PERSON> et al. (IEEE S&P 2009), while we found a further 20,000 OpenSSH servers that are vulnerable to a new attack on CBC-mode that bypasses the counter-measures introduced in OpenSSH 5.2 to defeat the attack of <PERSON><PERSON> et al. At the same time, 886,449 Dropbear servers in our first scan are vulnerable to a variant of the original CBC-mode attack. On the positive side, we provide formal security analyses for other popular SSH encryption modes, namely ChaCha20-Poly1305, generic Encrypt-then-MAC, and AES-GCM. Our proofs hold for detailed pseudo-code descriptions of these algorithms as implemented in OpenSSH. Our proofs use a corrected and extended version of the \"fragmented decryption\" security model that was specifically developed for the SSH setting by <PERSON><PERSON><PERSON> et al. (Eurocrypt 2012). These proofs provide strong confidentiality and integrity guarantees for these alternatives to CBC-mode encryption in SSH. However, we also show that these alternatives do not meet additional, desirable notions of security (boundary-hiding under passive and active attacks, and denial-of-service resistance) that were formalised by <PERSON><PERSON><PERSON> et al.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978364"}, {"primary_key": "4066284", "vector": [], "sparse_vector": [], "title": "Chainsaw: Chained Automated Workflow-based Exploit Generation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "We tackle the problem of automated exploit generation for web applications. In this regard, we present an approach that significantly improves the state-of-art in web injection vulnerability identification and exploit generation. Our approach for exploit generation tackles various challenges associated with typical web application characteristics: their multi-module nature, interposed user input, and multi-tier architectures using a database backend. Our approach develops precise models of application workflows, database schemas, and native functions to achieve high quality exploit generation. We implemented our approach in a tool called Chainsaw. Chainsaw was used to analyze 9 open source applications and generated over 199 first- and second-order injection exploits combined, significantly outperforming several related approaches.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978380"}, {"primary_key": "4066286", "vector": [], "sparse_vector": [], "title": "SANA: Secure and Scalable Aggregate Network Attestation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Large numbers of smart connected devices, also named as the Internet of Things (IoT), are permeating our environments (homes, factories, cars, and also our body - with wearable devices) to collect data and act on the insight derived. Ensuring software integrity (including OS, apps, and configurations) on such smart devices is then essential to guarantee both privacy and safety. A key mechanism to protect the software integrity of these devices is remote attestation: A process that allows a remote verifier to validate the integrity of the software of a device. This process usually makes use of a signed hash value of the actual device's software, generated by dedicated hardware. While individual device attestation is a well-established technique, to date integrity verification of a very large number of devices remains an open problem, due to scalability issues. In this paper, we present SANA, the first secure and scalable protocol for efficient attestation of large sets of devices that works under realistic assumptions. SANA relies on a novel signature scheme to allow anyone to publicly verify a collective attestation in constant time and space, for virtually an unlimited number of devices. We substantially improve existing swarm attestation schemes by supporting a realistic trust model where: (1) only the targeted devices are required to implement attestation; (2) compromising any device does not harm others; and (3) all aggregators can be untrusted. We implemented SANA and demonstrated its efficiency on tiny sensor devices. Furthermore, we simulated SANA at large scale, to assess its scalability. Our results show that SANA can provide efficient attestation of networks of 1,000,000 devices, in only 2.5 seconds.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978335"}, {"primary_key": "4066290", "vector": [], "sparse_vector": [], "title": "CCSW&apos;16: 8th ACM Cloud Computing Security Workshop.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Cloud computing is a dominant trend in computing for the foreseeable future; e.g., major cloud operators are now estimated to house over a million machines each and to host substantial (and growing) fractions of our IT and web infrastructure. CCSW is a forum for bringing together researchers and practitioners to discuss the implications of this trend to the security of cloud operators, tenants, and the larger Internet community. CCSW welcomes submissions on new threats, countermeasures, and opportunities brought about by the move to cloud computing, with a preference for unconventional approaches, as well as measurement studies and case studies that shed light on the security implications of clouds.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2990480"}, {"primary_key": "4066292", "vector": [], "sparse_vector": [], "title": "DEMO: High-Throughput Secure Three-Party Computation of Kerberos Ticket Generation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Secure multi-party computation (SMPC) is a cryptographic tool that enables a set of parties to jointly compute any function of their inputs while keeping the privacy of inputs. The paper \"High Throughput Semi-Honest Secure Three-Party Computation with an Honest Majority\" in this ACM CCS 2016 [4] presents a new protocol which its implementation carried out over 1,300,000 AESs per second and was able to support 35,000 login queries of Kerberos authentication per second. This poster/demo presents the design of the implementation and demonstrates the Kerberos authentication over here. The design will show how this high-throughput three-party computation can be done using simple servers. The demonstration proves that secure multiparty computation of Kerberos authentications in large organizations is now practical.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989035"}, {"primary_key": "4066293", "vector": [], "sparse_vector": [], "title": "High-Throughput Semi-Honest Secure Three-Party Computation with an Honest Majority.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we describe a new information-theoretic protocol (and a computationally-secure variant) for secure three-party computation with an honest majority. The protocol has very minimal computation and communication; for Boolean circuits, each party sends only a single bit for every AND gate (and nothing is sent for XOR gates). Our protocol is (simulation-based) secure in the presence of semi-honest adversaries, and achieves privacy in the client/server model in the presence of malicious adversaries. On a cluster of three 20-core servers with a 10Gbps connection, the implementation of our protocol carries out over 1.3 million AES computations per second, which involves processing over 7 billion gates per second. In addition, we developed a Kerberos extension that replaces the ticket-granting-ticket encryption on the Key Distribution Center (KDC) in MIT-Kerberos with our protocol, using keys/ passwords that are shared between the servers. This enables the use of Kerberos while protecting passwords. Our implementation is able to support a login storm of over 35,000 logins per second, which suffices even for very large organizations. Our work demonstrates that high-throughput secure computation is possible on standard hardware.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978331"}, {"primary_key": "4066294", "vector": [], "sparse_vector": [], "title": "SFADiff: Automated Evasion Attacks and Fingerprinting Using Black-box Differential Automata Learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Finding differences between programs with similar functionality is an important security problem as such differences can be used for fingerprinting or creating evasion attacks against security software like Web Application Firewalls (WAFs) which are designed to detect malicious inputs to web applications. In this paper, we present SFADIFF, a black-box differential testing framework based on Symbolic Finite Automata (SFA) learning. SFADIFF can automatically find differences between a set of programs with comparable functionality. Unlike existing differential testing techniques, instead of searching for each difference individually, SFADIFF infers SFA models of the target programs using black-box queries and systematically enumerates the differences between the inferred SFA models. All differences between the inferred models are checked against the corresponding programs. Any difference between the models, that does not result in a difference between the corresponding programs, is used as a counterexample for further refinement of the inferred models. SFADIFF's model-based approach, unlike existing differential testing tools, also support fully automated root cause analysis in a domain-independent manner.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978383"}, {"primary_key": "4066296", "vector": [], "sparse_vector": [], "title": "POSTER: Phishing Website Detection with a Multiphase Framework to Find Visual Similarity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Most phishing pages try to convince users that they are legitimate sites by imitating visual signals like logos from the websites they are targeting. Visual similarity detection methods look for these imitations between the screen-shots of the suspect pages and an image database of the most targeted websites. Existing approaches, however, are either too slow for real-time use or not robust to manipulation. In this work, we design a multi-phase framework for visual similarity detection. The first phase of the framework should rule out the bulk of websites quickly, but without introducing false negatives and with resistance to attacker manipulations. Later phases can use more heavyweight operations to decide whether or not to warn the user about possible phishing. In this abstract, we focus on the first phase. In experiments, our proposed method rules out more than half of the test cases with zero false negatives with less than 5 ms of processing time per page.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989050"}, {"primary_key": "4066298", "vector": [], "sparse_vector": [], "title": "Privacy and Security in the Genomic Era.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the help of rapidly developing technology, DNA sequencing is becoming less expensive. As a consequence, the research in genomics has gained speed in paving the way to personalized (genomic) medicine, and geneticists need large collections of human genomes to further increase this speed. Furthermore, individuals are using their genomes to learn about their (genetic) predispositions to diseases, their ancestries, and even their (genetic) compatibilities with potential partners. This trend has also caused the launch of health-related websites and online social networks (OSNs), in which individuals share their genomic data (e.g., OpenSNP or 23andMe). On the other hand, genomic data carries much sensitive information about its owner. By analyzing the DNA of an individual, it is now possible to learn about his disease predispositions (e.g., for Alzheimer's or Parkinson's), ancestries, and physical attributes. The threat to genomic privacy is magnified by the fact that a person's genome is correlated to his family members' genomes, thus leading to interdependent privacy risks. This short tutorial will help computer scientists better understand the privacy and security challenges in today's genomic era. We will first highlight the significance of genomic data and the threats for genomic privacy. Then, we will present the high level descriptions of the proposed solutions to protect the privacy of genomic data and we will discuss future research directions. No prerequisite knowledge on biology or genomics is required for the attendees of this proposal. We only require the attendees to have a slight background on cryptography and statistics.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2976751"}, {"primary_key": "4066301", "vector": [], "sparse_vector": [], "title": "Mix&amp;Slice: Efficient Access Revocation in the Cloud.", "authors": ["<PERSON>", "Sabrina De Capitani di Vimercati", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present an approach to enforce access revocation on resources stored at external cloud providers. The approach relies on a resource transformation that provides strong mutual inter-dependency in its encrypted representation. To revoke access on a resource, it is then sufficient to update a small portion of it, with the guarantee that the resource as a whole (and any portion of it) will become unintelligible to those from whom access is revoked. The extensive experimental evaluation on a variety of configurations confirmed the effectiveness and efficiency of our solution, which showed excellent performance and compatibility with several implementation strategies.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978377"}, {"primary_key": "4066304", "vector": [], "sparse_vector": [], "title": "Garbling Gadgets for Boolean and Arithmetic Circuits.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present simple, practical, and powerful new techniques for garbled circuits. These techniques result in significant concrete and asymptotic improvements over the state of the art, for several natural kinds of computations. For arithmetic circuits over the integers, our construction results in garbled circuits with free addition, weighted threshold gates with cost independent of fan-in, and exponentiation by a fixed exponent with cost independent of the exponent. For boolean circuits, our construction gives an exponential improvement over the state of the art for threshold gates (including AND/OR gates) of high fan-in.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978410"}, {"primary_key": "4066307", "vector": [], "sparse_vector": [], "title": "Strong Non-Interference and Type-Directed Higher-Order Masking.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Differential power analysis (DPA) is a side-channel attack in which an adversary retrieves cryptographic material by measuring and analyzing the power consumption of the device on which the cryptographic algorithm under attack executes. An effective countermeasure against DPA is to mask secrets by probabilistically encoding them over a set of shares, and to run masked algorithms that compute on these encodings. Masked algorithms are often expected to provide, at least, a certain level of probing security. Leveraging the deep connections between probabilistic information flow and probing security, we develop a precise, scalable, and fully automated methodology to verify the probing security of masked algorithms, and generate them from unprotected descriptions of the algorithm. Our methodology relies on several contributions of independent interest, including a stronger notion of probing security that supports compositional reasoning, and a type system for enforcing an expressive class of probing policies. Finally, we validate our methodology on examples that go significantly beyond the state-of-the-art.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978427"}, {"primary_key": "4066308", "vector": [], "sparse_vector": [], "title": "Differentially Private Bayesian Programming.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present PrivInfer, an expressive framework for writing and verifying differentially private Bayesian machine learning algorithms. Programs in PrivInfer are written in a rich functional probabilistic programming language with constructs for performing Bayesian inference. Then, differential privacy of programs is established using a relational refinement type system, in which refinements on probability types are indexed by a metric on distributions. Our framework leverages recent developments in Bayesian inference, probabilistic programming languages, and in relational refinement types. We demonstrate the expressiveness of PrivInfer by verifying privacy for several examples of private Bayesian inference.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978371"}, {"primary_key": "4066309", "vector": [], "sparse_vector": [], "title": "Advanced Probabilistic Couplings for Differential Privacy.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Differential privacy is a promising formal approach to data privacy, which provides a quantitative bound on the privacy cost of an algorithm that operates on sensitive information. Several tools have been developed for the formal verification of differentially private algorithms, including program logics and type systems. However, these tools do not capture fundamental techniques that have emerged in recent years, and cannot be used for reasoning about cutting-edge differentially private algorithms. Existing techniques fail to handle three broad classes of algorithms: 1) algorithms where privacy depends on accuracy guarantees, 2) algorithms that are analyzed with the advanced composition theorem, which shows slower growth in the privacy cost, 3) algorithms that interactively accept adaptive inputs.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978391"}, {"primary_key": "4066311", "vector": [], "sparse_vector": [], "title": "Message-Recovery Attacks on Feistel-Based Format Preserving Encryption.", "authors": ["<PERSON><PERSON>", "Viet Tung Hoang", "<PERSON>"], "summary": "We give attacks on Feistel-based format-preserving encryption (FPE) schemes that succeed in message recovery (not merely distinguishing scheme outputs from random) when the message space is small. For $4$-bit messages, the attacks fully recover the target message using $2^{21}$ examples for the FF3 NIST standard and $2^{25}$ examples for the FF1 NIST standard. The examples include only three messages per tweak, which is what makes the attacks non-trivial even though the total number of examples exceeds the size of the domain. The attacks are rigorously analyzed in a new definitional framework of message-recovery security. The attacks are easily put out of reach by increasing the number of Feistel rounds in the standards.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978390"}, {"primary_key": "4066312", "vector": [], "sparse_vector": [], "title": "Optimizing Semi-Honest Secure Multiparty Computation for the Internet.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the setting of secure multiparty computation, a set of parties with private inputs wish to compute some function of their inputs without revealing anything but their output. Over the last decade, the efficiency of secure two-party computation has advanced in leaps and bounds, with speedups of some orders of magnitude, making it fast enough to be of use in practice. In contrast, progress on the case of multiparty computation (with more than two parties) has been much slower, with very little work being done. Currently, the only implemented efficient multiparty protocol has many rounds of communication (linear in the depth of the circuit being computed) and thus is not suited for Internet-like settings where latency is not very low. In this paper, we construct highly efficient constant-round protocols for the setting of multiparty computation for semi-honest adversaries. Our protocols work by constructing a multiparty garbled circuit, as proposed in BMR (<PERSON> et al., STOC 1990). Our first protocol uses oblivious transfer and constitutes the first concretely-efficient constant-round multiparty protocol for the case of no honest majority. Our second protocol uses BGW, and is significantly more efficient than the FairplayMP protocol (<PERSON><PERSON> et al., CCS 2008) that also uses BGW.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978347"}, {"primary_key": "4066317", "vector": [], "sparse_vector": [], "title": "On the Practical (In-)Security of 64-bit Block Ciphers: Collision Attacks on HTTP over TLS and OpenVPN.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "While modern block ciphers, such as AES, have a block size of at least 128 bits, there are many 64-bit block ciphers, such as 3DES and Blowfish, that are still widely supported in Internet security protocols such as TLS, SSH, and IPsec. When used in CBC mode, these ciphers are known to be susceptible to collision attacks when they are used to encrypt around 232 blocks of data (the so-called birthday bound). This threat has traditionally been dismissed as impractical since it requires some prior knowledge of the plaintext and even then, it only leaks a few secret bits per gigabyte. Indeed, practical collision attacks have never been demonstrated against any mainstream security protocol, leading to the continued use of 64-bit ciphers on the Internet.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978423"}, {"primary_key": "4066319", "vector": [], "sparse_vector": [], "title": "Statistical Deobfuscation of Android Applications.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This work presents a new approach for deobfuscating Android APKs based on probabilistic learning of large code bases (termed \"Big Code\"). The key idea is to learn a probabilistic model over thousands of non-obfuscated Android applications and to use this probabilistic model to deobfuscate new, unseen Android APKs. The concrete focus of the paper is on reversing layout obfuscation, a popular transformation which renames key program elements such as classes, packages, and methods, thus making it difficult to understand what the program does. Concretely, the paper: (i) phrases the layout deobfuscation problem of Android APKs as structured prediction in a probabilistic graphical model, (ii) instantiates this model with a rich set of features and constraints that capture the Android setting, ensuring both semantic equivalence and high prediction accuracy, and (iii) shows how to leverage powerful inference and learning algorithms to achieve overall precision and scalability of the probabilistic predictions.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978422"}, {"primary_key": "4066321", "vector": [], "sparse_vector": [], "title": "Theory of Implementation Security Workshop (TIs 2016).", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The Internet of Things (IoT) enables a network of communication between people-to-people, people-to-things and things-to-things. The security of these communications against all possible attacks is a significant part of todays security and privacy. Due to the design nature of IoT systems, IoT devices are easily accessible by attackers which increases the importance of their security against physical attacks. This workshop is dedicated to research on the design of cryptographic algorithms and implementations secure against physical attacks.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2990488"}, {"primary_key": "4066323", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON><PERSON>: Perfectly Imitated <PERSON>oy Routing through Traffic Replacement.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "As the capabilities of censors increase and their ability to perform more powerful deep-packet inspection techniques grows, more powerful systems are needed in turn to disguise user traffic and allow users under a censor's influence to access blocked content on the Internet. Decoy routing is a censorship resistance technique that hides traffic under the guise of a HTTPS connection to a benign, uncensored overt site. However, existing techniques far from perfectly mimic a typical access of content on the overt server. Artificial latency introduced by the system, as well as differences in packet sizes and timings betray their use to a censor capable of performing basic packet and latency analysis. While many of the more recent decoy routing systems focus on deployability concerns, they do so at the cost of security, adding vulnerabilities to both passive and active attacks. We propose Slitheen, a decoy routing system capable of perfectly mimicking the traffic patterns of overt sites. Our system is secure against previously undefended passive attacks, as well as known active attacks. Further, we show how recent innovations in traffic-shaping technology for ISPs mitigate previous deployability challenges.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978312"}, {"primary_key": "4066325", "vector": [], "sparse_vector": [], "title": "Coverage-based Greybox Fuzzing as Markov Chain.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Coverage-based Greybox Fuzzing (CGF) is a random testing approach that requires no program analysis. A new test is generated by slightly mutating a seed input. If the test exercises a new and interesting path, it is added to the set of seeds; otherwise, it is discarded. We observe that most tests exercise the same few \"high-frequency\" paths and develop strategies to explore significantly more paths with the same number of tests by gravitating towards low-frequency paths. We explain the challenges and opportunities of CGF using a Markov chain model which specifies the probability that fuzzing the seed that exercises path i generates an input that exercises path j. Each state (i.e., seed) has an energy that specifies the number of inputs to be generated from that seed. We show that CGF is considerably more efficient if energy is inversely proportional to the density of the stationary distribution and increases monotonically every time that seed is chosen. Energy is controlled with a power schedule.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978428"}, {"primary_key": "4066326", "vector": [], "sparse_vector": [], "title": "Frodo: Take off the Ring! Practical, Quantum-Secure Key Exchange from LWE.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Léo Du<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Douglas <PERSON>"], "summary": "Lattice-based cryptography offers some of the most attractive primitives believed to be resistant to quantum computers. Following increasing interest from both companies and government agencies in building quantum computers, a number of works have proposed instantiations of practical post-quantum key exchange protocols based on hard problems in ideal lattices, mainly based on the Ring Learning With Errors (R-LWE) problem. While ideal lattices facilitate major efficiency and storage benefits over their non-ideal counterparts, the additional ring structure that enables these advantages also raises concerns about the assumed difficulty of the underlying problems. Thus, a question of significant interest to cryptographers, and especially to those currently placing bets on primitives that will withstand quantum adversaries, is how much of an advantage the additional ring structure actually gives in practice. Despite conventional wisdom that generic lattices might be too slow and unwieldy, we demonstrate that LWE-based key exchange is quite practical: our constant time implementation requires around 1.3ms computation time for each party; compared to the recent NewHope R-LWE scheme, communication sizes increase by a factor of 4.7x, but remain under 12 KiB in each direction. Our protocol is competitive when used for serving web pages over TLS; when partnered with ECDSA signatures, latencies increase by less than a factor of 1.6x, and (even under heavy load) server throughput only decreases by factors of 1.5x and 1.2x when serving typical 1 KiB and 100 KiB pages, respectively. To achieve these practical results, our protocol takes advantage of several innovations. These include techniques to optimize communication bandwidth, dynamic generation of public parameters (which also offers additional security against backdoors), carefully chosen error distributions, and tight security parameters.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978425"}, {"primary_key": "4066327", "vector": [], "sparse_vector": [], "title": "∑oφoς: Forward Secure Searchable Encryption.", "authors": ["<PERSON>"], "summary": "Searchable Symmetric Encryption aims at making possible searching over an encrypted database stored on an untrusted server while keeping privacy of both the queries and the data, by allowing some small controlled leakage to the server. Recent work shows that dynamic schemes -- in which the data is efficiently updatable -- leaking some information on updated keywords are subject to devastating adaptative attacks breaking the privacy of the queries. The only way to thwart this attack is to design forward private schemes whose update procedure does not leak if a newly inserted element matches previous search queries.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978303"}, {"primary_key": "4066328", "vector": [], "sparse_vector": [], "title": "Function Secret Sharing: Improvements and Extensions.", "authors": ["<PERSON><PERSON>", "<PERSON>v <PERSON>", "<PERSON><PERSON>"], "summary": "Function Secret Sharing (FSS), introduced by <PERSON> et al. (Eurocrypt 2015), provides a way for additively secret-sharing a function from a given function family F. More concretely, an m-party FSS scheme splits a function f : {0, 1}n -> G, for some abelian group G, into functions f1,...,fm, described by keys k1,...,km, such that f = f1 + ... + fm and every strict subset of the keys hides f. A Distributed Point Function (DPF) is a special case where F is the family of point functions, namely functions f_{a,b} that evaluate to b on the input a and to 0 on all other inputs. FSS schemes are useful for applications that involve privately reading from or writing to distributed databases while minimizing the amount of communication. These include different flavors of private information retrieval (PIR), as well as a recent application of DPF for large-scale anonymous messaging.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978429"}, {"primary_key": "4066333", "vector": [], "sparse_vector": [], "title": "Content Security Problems?: Evaluating the Effectiveness of Content Security Policy in the Wild.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Content Security Policy (CSP) is an emerging W3C standard introduced to mitigate the impact of content injection vulnerabilities on websites. We perform a systematic, large-scale analysis of four key aspects that impact on the effectiveness of CSP: browser support, website adoption, correct configuration and constant maintenance. While browser support is largely satisfactory, with the exception of few notable issues, our analysis unveils several shortcomings relative to the other three aspects. CSP appears to have a rather limited deployment as yet and, more crucially, existing policies exhibit a number of weaknesses and misconfiguration errors. Moreover, content security policies are not regularly updated to ban insecure practices and remove unintended security violations. We argue that many of these problems can be fixed by better exploiting the monitoring facilities of CSP, while other issues deserve additional research, being more rooted into the CSP design.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978338"}, {"primary_key": "4066334", "vector": [], "sparse_vector": [], "title": "Measurement and Analysis of Private Key Sharing in the HTTPS Ecosystem.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The semantics of online authentication in the web are rather straightforward: if <PERSON> has a certificate binding <PERSON>'s name to a public key, and if a remote entity can prove knowledge of <PERSON>'s private key, then (barring key compromise) that remote entity must be <PERSON>. However, in reality, many websites' and the majority of the most popular ones-are hosted at least in part by third parties such as Content Delivery Networks (CDNs) or web hosting providers. Put simply: administrators of websites who deal with (extremely) sensitive user data are giving their private keys to third parties. Importantly, this sharing of keys is undetectable by most users, and widely unknown even among researchers. In this paper, we perform a large-scale measurement study of key sharing in today's web. We analyze the prevalence with which websites trust third-party hosting providers with their secret keys, as well as the impact that this trust has on responsible key management practices, such as revocation. Our results reveal that key sharing is extremely common, with a small handful of hosting providers having keys from the majority of the most popular websites. We also find that hosting providers often manage their customers' keys, and that they tend to react more slowly yet more thoroughly to compromised or potentially compromised keys.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978301"}, {"primary_key": "4066335", "vector": [], "sparse_vector": [], "title": "On the Instability of Bitcoin Without the Block Reward.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Bitcoin provides two incentives for miners: block rewards and transaction fees. The former accounts for the vast majority of miner revenues at the beginning of the system, but it is expected to transition to the latter as the block rewards dwindle. There has been an implicit belief that whether miners are paid by block rewards or transaction fees does not affect the security of the block chain.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978408"}, {"primary_key": "4066337", "vector": [], "sparse_vector": [], "title": "POSTER: (Semi)-Supervised Machine Learning Approaches for Network Security in High-Dimensional Network Data.", "authors": ["<PERSON>", "Alessandro D&apos;Alconzo", "<PERSON>", "Pierdomenico Fiadino", "<PERSON><PERSON><PERSON>"], "summary": "Network security represents a keystone to ISPs, who need to cope with an increasing number of network attacks that put the network's integrity at risk. The high-dimensionality of network data provided by current network monitoring systems opens the door to the massive application of machine learning approaches to improve the detection and classification of network attacks. In this paper we devise a novel attacks detection and classification technique based on semi-supervised Machine Learning (ML) algorithms to automatically detect and diagnose network attacks with minimal training, and compare its performance to that achieved by other well-known supervised learning detectors. The proposed solution is evaluated using real network measurements coming from the WIDE backbone network, using the well-known MAWILab dataset for attacks labeling.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989069"}, {"primary_key": "4066338", "vector": [], "sparse_vector": [], "title": "Second Workshop on Cyber-Physical Systems Security and PrivaCy (CPS-SPC&apos;16).", "authors": ["Alvaro <PERSON>", "Rakesh B. Bobba"], "summary": "The Second International Workshop on Cyber-Physical Systems Security and PrivaCy (CPS-SPC'16) is being held in conjunction with the 23rd ACM CCS Conference. This second edition follows a successful workshop held with ACM CCS in 2015. The workshop was motivated by several observations. First, cyber-physical systems represent the new frontier for cyber risk. The attack surface imposed by the convergence of computing, communications and physical control represents unique challenges for security researchers and practitioners. Second, majority of the published literature addressing the security and privacy of CPS reflect a field still in its infancy. As such, the overall principles, models, and theories for securing CPS have not yet emerged. Third, the organizers of this workshop strongly felt that a premiere forum associated with a premiere conference was needed for rapidly publishing diverse, multidisciplinary in-progress work on the security and privacy of CPS and galvanizing the research community. The set of accepted papers reflect this vision. We have organized an exciting program for this workshop and look forward to active participation in this and future workshops.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2990481"}, {"primary_key": "4066339", "vector": [], "sparse_vector": [], "title": "POSTER: WiPING: Wi-Fi signal-based PIN Guessing attack.", "authors": ["<PERSON><PERSON><PERSON>", "Jaewoo Park", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper presents a new type of online password guessing attack called \"WiPING\" (Wi-Fi signal-based PIN Guessing attack) to guess a victim's PIN (Personal Identification Number) within a small number of unlock attempts. WiPING uses wireless signal patterns identified from observing sequential finger movements involved in typing a PIN to unlock a mobile device. A list of possible PIN candidates is generated from the wireless signal patterns, and is used to improve performance of PIN guessing attacks. We implemented a proof-of-concept attack to demonstrate the feasibility of WiPING. Our results showed that WiPING could be practically effective: while pure guessing attacks failed to guess all 20 PINs, WiPING successfully guessed two PINs.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989067"}, {"primary_key": "4066340", "vector": [], "sparse_vector": [], "title": "BeleniosRF: A Non-interactive Receipt-Free Electronic Voting Scheme.", "authors": ["<PERSON><PERSON><PERSON>", "Véronique Cortier", "<PERSON>", "<PERSON>"], "summary": "We propose a new voting scheme, BeleniosRF, that offers both receipt-freeness and end-to-end verifiability. It is receipt-free in a strong sense, meaning that even dishonest voters cannot prove how they voted. We provide a game-based definition of receipt-freeness for voting protocols with non-interactive ballot casting, which we name strong receipt-freeness (sRF). To our knowledge, sRF is the first game-based definition of receipt-freeness in the literature, and it has the merit of being particularly concise and simple. Built upon the Helios protocol, BeleniosRF inherits its simplicity and does not require any anti-coercion strategy from the voters. We implement BeleniosRF and show its feasibility on a number of platforms, including desktop computers and smartphones.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978337"}, {"primary_key": "4066341", "vector": [], "sparse_vector": [], "title": "POSTER: DataLair: A Storage Block Device with Plausible Deniability.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Sensitive information is present on our phones, disks, watches and computers. Its protection is essential. Plausible deniability of stored data allows individuals to deny that their device contains a piece of sensitive information. This constitutes a key tool in the fight against oppressive governments and censorship.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989061"}, {"primary_key": "4066342", "vector": [], "sparse_vector": [], "title": "POSTER: ConcurORAM: High-Throughput Parallel Multi-Client ORAM.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Oblivious RAM (ORAM) mechanisms have improved rapidly in recent years as increasing amounts of data are outsourced. Although several tree-based ORAMs such as PathORAM [8] and RingORAM [6] have achieved near-optimal bandwidth for single client scenarios, their low overall throughput due to high latency of access -- as clients need to wait for or know about and coordinate with each other, lest privacy is lost -- reduces their applicability for multi-client scenarios.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989062"}, {"primary_key": "4066343", "vector": [], "sparse_vector": [], "title": "Transparency Overlays and Applications.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this paper, we initiate a formal study of transparency, which in recent years has become an increasingly critical requirement for the systems in which people place trust. We present the abstract concept of a transparency overlay, which can be used in conjunction with any system to give it provable transparency guarantees, and then apply the overlay to two settings: Certificate Transparency and Bitcoin. In the latter setting, we show that the usage of our transparency overlay eliminates the need to engage in mining and allows users to store a single small value rather than the entire blockchain. Our transparency overlay is generically constructed from a signature scheme and a new primitive we call a dynamic list commitment, which in practice can be instantiated using a collision-resistant hash function.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978404"}, {"primary_key": "4066345", "vector": [], "sparse_vector": [], "title": "A Systematic Analysis of the Juniper Dual EC Incident.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In December 2015, Juniper Networks announced multiple security vulnerabilities stemming from unauthorized code in ScreenOS, the operating system for their NetScreen VPN routers. The more sophisticated of these vulnerabilities was a passive VPN decryption capability, enabled by a change to one of the elliptic curve points used by the Dual EC pseudorandom number generator. In this paper, we describe the results of a full independent analysis of the ScreenOS randomness and VPN key establishment protocol subsystems, which we carried out in response to this incident. While Dual EC is known to be insecure against an attacker who can choose the elliptic curve parameters, Juniper had claimed in 2013 that ScreenOS included countermeasures against this type of attack. We find that, contrary to Juniper's public statements, the ScreenOS VPN implementation has been vulnerable since 2008 to passive exploitation by an attacker who selects the Dual EC curve point. This vulnerability arises due to apparent flaws in Juniper's countermeasures as well as a cluster of changes that were all introduced concurrently with the inclusion of Dual EC in a single 2008 release. We demonstrate the vulnerability on a real NetScreen device by modifying the firmware to install our own parameters, and we show that it is possible to passively decrypt an individual VPN session in isolation without observing any other network traffic. We investigate the possibility of passively fingerprinting ScreenOS implementations in the wild. This incident is an important example of how guidelines for random number generation, engineering, and validation can fail in practice.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978395"}, {"primary_key": "4066346", "vector": [], "sparse_vector": [], "title": "Host of Troubles: Multiple Host Ambiguities in HTTP Implementations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The Host header is a security-critical component in an HTTP request, as it is used as the basis for enforcing security and caching policies. While the current specification is generally clear on how host-related protocol fields should be parsed and interpreted, we find that the implementations are problematic. We tested a variety of widely deployed HTTP implementations and discover a wide range of non-compliant and inconsistent host processing behaviours. The particular problem is that when facing a carefully crafted HTTP request with ambiguous host fields (e.g., with multiple Host headers), two different HTTP implementations often accept and understand it differently when operating on the same request in sequence. We show a number of techniques to induce inconsistent interpretations of host between HTTP implementations and how the inconsistency leads to severe attacks such as HTTP cache poisoning and security policy bypass. The prevalence of the problem highlights the potential negative impact of gaps between the specifications and implementations of Internet protocols.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978394"}, {"primary_key": "4066347", "vector": [], "sparse_vector": [], "title": "POSTER: KXRay: Introspecting the Kernel for Rootkit Timing Footprints.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Kernel rootkits often hide associated malicious processes by altering reported task struct information to upper layers and applications such as ps and top. Virtualized settings offer a unique opportunity to mitigate this behavior using dynamic virtual machine introspection (VMI). For known kernels, VMI can be deployed to search for kernel objects and identify them by using unique data structure \"signatures\".", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989053"}, {"primary_key": "4066350", "vector": [], "sparse_vector": [], "title": "Error Handling of In-vehicle Networks Makes Them Vulnerable.", "authors": ["Kyong<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Contemporary vehicles are getting equipped with an increasing number of Electronic Control Units (ECUs) and wireless connectivities. Although these have enhanced vehicle safety and efficiency, they are accompanied with new vulnerabilities. In this paper, we unveil a new important vulnerability applicable to several in-vehicle networks including Control Area Network (CAN), the de facto standard in-vehicle network protocol. Specifically, we propose a new type of Denial-of-Service (DoS), called the bus-off attack, which exploits the error-handling scheme of in-vehicle networks to disconnect or shut down good/uncompromised ECUs. This is an important attack that must be thwarted, since the attack, once an ECU is compromised, is easy to be mounted on safety-critical ECUs while its prevention is very difficult. In addition to the discovery of this new vulnerability, we analyze its feasibility using actual in-vehicle network traffic, and demonstrate the attack on a CAN bus prototype as well as on two real vehicles. Based on our analysis and experimental results, we also propose and evaluate a mechanism to detect and prevent the bus-off attack.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978302"}, {"primary_key": "4066359", "vector": [], "sparse_vector": [], "title": "Λολ: Functional Lattice Cryptography.", "authors": ["<PERSON>", "<PERSON>"], "summary": "This work describes the design, implementation, and evaluation of Λολ, a general-purpose software framework for lattice-based cryptography. The Λολ framework has several novel properties that distinguish it from prior implementations of lattice cryptosystems, including the following.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978402"}, {"primary_key": "4066360", "vector": [], "sparse_vector": [], "title": "Differential Privacy as a Mutual Information Constraint.", "authors": ["<PERSON>", "Lanqing Yu"], "summary": "Differential privacy is a precise mathematical constraint meant to ensure privacy of individual pieces of information in a database even while queries are being answered about the aggregate. Intuitively, one must come to terms with what differential privacy does and does not guarantee. For example, the definition prevents a strong adversary who knows all but one entry in the database from further inferring about the last one. This strong adversary assumption can be overlooked, resulting in misinterpretation of the privacy guarantee of differential privacy. Herein we give an equivalent definition of privacy using mutual information that makes plain some of the subtleties of differential privacy. The mutual-information differential privacy is in fact sandwiched between $\\epsilon$-differential privacy and $(\\epsilon,\\delta)$-differential privacy in terms of its strength. In contrast to previous works using unconditional mutual information, differential privacy is fundamentally related to conditional mutual information, accompanied by a maximization over the database distribution. The conceptual advantage of using mutual information, aside from yielding a simpler and more intuitive definition of differential privacy, is that its properties are well understood. Several properties of differential privacy are easily verified for the mutual information alternative, such as composition theorems.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978308"}, {"primary_key": "4066364", "vector": [], "sparse_vector": [], "title": "SandScout: Automatic Detection of Flaws in iOS Sandbox Profiles.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Recent literature on iOS security has focused on the malicious potential of third-party applications, demonstrating how developers can bypass application vetting and code-level protections. In addition to these protections, iOS uses a generic sandbox profile called \"container\" to confine malicious or exploited third-party applications. In this paper, we present the first systematic analysis of the iOS container sandbox profile. We propose the SandScout framework to extract, decompile, formally model, and analyze iOS sandbox profiles as logic-based programs. We use our Prolog-based queries to evaluate file-based security properties of the container sandbox profile for iOS 9.0.2 and discover seven classes of exploitable vulnerabilities. These attacks affect non-jailbroken devices running later versions of iOS. We are working with Apple to resolve these attacks, and we expect that SandScout will play a significant role in the development of sandbox profiles for future versions of iOS.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978336"}, {"primary_key": "4066366", "vector": [], "sparse_vector": [], "title": "Alternative Implementations of Secure Real Numbers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper extends the choice available for secure real number implementations with two new contributions. We will consider the numbers represented in form a-φ b where φ is the golden ratio, and in form (-1)s.2e where e is a fixed-point number. We develop basic arithmetic operations together with some frequently used elementary functions. All the operations are implemented and benchmarked on SHAREMIND secure multi-party computation framework. It turns out that the new proposals provide viable alternatives to standard floating- and fixed-point implementations from the performance/error viewpoint in various settings. However, the optimal choice still depends on the exact requirements of the numerical algorithm to be implemented.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978348"}, {"primary_key": "4066367", "vector": [], "sparse_vector": [], "title": "Secure Stable Matching at Scale.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "When a group of individuals and organizations wish to compute a stable matching---for example, when medical students are matched to medical residency programs---they often outsource the computation to a trusted arbiter in order to preserve the privacy of participants' preferences. Secure multi-party computation offers the possibility of private matching processes that do not rely on any common trusted third party. However, stable matching algorithms have previously been considered infeasible for execution in a secure multi-party context on non-trivial inputs because they are computationally intensive and involve complex data-dependent memory access patterns.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978373"}, {"primary_key": "4066369", "vector": [], "sparse_vector": [], "title": "Practical Detection of Entropy Loss in Pseudo-Random Number Generators.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Pseudo-random number generators (PRNGs) are a critical infrastructure for cryptography and security of many computer applications. At the same time, PRNGs are surprisingly difficult to design, implement, and debug. This paper presents the first static analysis technique specifically for quality assurance of cryptographic PRNG implementations.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978369"}, {"primary_key": "4066370", "vector": [], "sparse_vector": [], "title": "What <PERSON><PERSON> is Revealed by Order-Revealing Encryption?", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The security of order-revealing encryption (ORE) has been unclear since its invention. Dataset characteristics for which ORE is especially insecure have been identified, such as small message spaces and low-entropy distributions. On the other hand, properties like one-wayness on uniformly-distributed datasets have been proved for ORE constructions.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978379"}, {"primary_key": "4066372", "vector": [], "sparse_vector": [], "title": "Private Circuits III: Hardware Trojan-Resilience via Testing Amplification.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Security against hardware trojans is currently becoming an essential ingredient to ensure trust in information systems. A variety of solutions have been introduced to reach this goal, ranging from reactive (i.e., detection-based) to preventive (i.e., trying to make the insertion of a trojan more difficult for the adversary). In this paper, we show how testing (which is a typical detection tool) can be used to state concrete security guarantees for preventive approaches to trojan-resilience. For this purpose, we build on and formalize two important previous works which introduced ``input scrambling\" and ``split manufacturing\" as countermeasures to hardware trojans. Using these ingredients, we present a generic compiler that can transform any circuit into a trojan-resilient one, for which we can state quantitative security guarantees on the number of correct executions of the circuit thanks to a new tool denoted as ``testing amplification\". Compared to previous works, our threat model covers an extended range of hardware trojans while we stick with the goal of minimizing the number of honest elements in our transformed circuits. Since transformed circuits essentially correspond to redundant multiparty computations of the target functionality, they also allow reasonably efficient implementations, which can be further optimized if specialized to certain cryptographic primitives and security goals.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978419"}, {"primary_key": "4066375", "vector": [], "sparse_vector": [], "title": "Online Tracking: A 1-million-site Measurement and Analysis.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present the largest and most detailed measurement of online tracking conducted to date, based on a crawl of the top 1 million websites. We make 15 types of measurements on each site, including stateful (cookie-based) and stateless (fingerprinting-based) tracking, the effect of browser privacy tools, and the exchange of tracking data between different sites (\"cookie syncing\"). Our findings include multiple sophisticated fingerprinting techniques never before measured in the wild. This measurement is made possible by our open-source web privacy measurement tool, OpenWPM, which uses an automated version of a full-fledged consumer browser. It supports parallelism for speed and scale, automatic recovery from failures of the underlying browser, and comprehensive browser instrumentation. We demonstrate our platform's strength in enabling researchers to rapidly detect, quantify, and characterize emerging online tracking behaviors.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978313"}, {"primary_key": "4066377", "vector": [], "sparse_vector": [], "title": "Covert Channels through Random Number Generator: Mechanisms, Capacity Estimation and Mitigations.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Covert channels present serious security threat because they allow secret communication between two malicious processes even if the system inhibits direct communication. We describe, implement and quantify a new covert channel through shared hardware random number generation (RNG) module that is available on modern processors. We demonstrate that a reliable, high-capacity and low-error covert channel can be created through the RNG module that works across CPU cores and across virtual machines. We quantify the capacity of the RNG channel under different settings and show that transmission rates in the range of 7-200 kbit/s can be achieved depending on a particular system used for transmission, assumptions, and the load level. Finally, we describe challenges in mitigating the RNG channel, and propose several mitigation approaches both in software and hardware.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978374"}, {"primary_key": "4066379", "vector": [], "sparse_vector": [], "title": "Sixth International Workshop on Trustworthy Embedded Devices (TrustED 2016).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The Internet of Things (IoT) is expected to become a global information and communication infrastructure for cyber physical systems and to bring numerous value-added services for modern society. However, the integration of heterogeneous devices and service models into a cohesive system significantly increases the complexity of design and deployment and introduces the new challenges for the security of systems and processed as well as the privacy of the collected data. The Workshop on Trustworthy Embedded Devices (TrustED) addresses all aspects of security and privacy related to embedded systems and the IoT. TrustED 2016 is a continuation of previous workshops in this series, which were held in conjunction with ESORICS 2011, IEEE Security & Privacy 2012, ACM CCS 2013, ACM CCS 2014, and ACM CCS 2015 (see http://www.trusted-workshop.de for details). The goal of this workshop is to bring together experts from academia and research institutes, industry, and government in the field of security and privacy in cyber physical systems to discuss and investigate the problems, challenges, and recent scientific and technological developments.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2990489"}, {"primary_key": "4066380", "vector": [], "sparse_vector": [], "title": "Attacking OpenSSL Implementation of ECDSA with a Few Signatures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Cheng"], "summary": "In this work, we give a lattice attack on the ECDSA implementation in the latest version of OpenSSL, which implement the scalar multiplication by windowed Non-Adjacent Form method. We propose a totally different but more efficient method of extracting and utilizing information from the side-channel results, remarkably improving the previous attacks. First, we develop a new efficient method, which can extract almost all information from the side-channel results, obtaining 105.8 bits of information per signature on average for 256-bit ECDSA. Then in order to make the utmost of our extracted information, we translate the problem of recovering secret key to the Extended Hidden Number Problem, which can be solved by lattice reduction algorithms. Finally, we introduce the methods of elimination, merging, most significant digit recovering and enumeration to improve the attack. Our attack is mounted to the {series secp256k1} curve, and the result shows that only 4 signatures would be enough to recover the secret key if the Flush+Reload attack is implemented perfectly without any error,which is much better than the best known result needing at least 13 signatures.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978400"}, {"primary_key": "4066381", "vector": [], "sparse_vector": [], "title": "POSTER: Accuracy vs. Time Cost: Detecting Android Malware through Pareto Ensemble Pruning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Lihua Xu", "<PERSON><PERSON><PERSON>"], "summary": "This paper proposes Begonia, a malware detection system through Pareto ensemble pruning. We convert the malware detection problem into the bi-objective Pareto optimization, aiming to trade off the classification accuracy and the size of classifiers as two objectives. We automatically generate several groups of base classifiers using SVM and generate solutions through bi-objective Pareto optimization. We then select the ensembles with highest accuracy of each group to form the final solutions, among which we hit the optimal solution where the combined loss function is minimal considering the trade-off between accuracy and time cost. We expect users to provide different trade-off levels to their different requirements to select the best solution. Experimental results show that Begonia can achieve higher accuracy with relatively lower overhead compared to the ensemble containing all the classifiers and can make a good trade-off to different requirements.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989055"}, {"primary_key": "4066385", "vector": [], "sparse_vector": [], "title": "Scalable Graph-based Bug Search for Firmware Images.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Chengcheng Xu", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Because of rampant security breaches in IoT devices, searching vulnerabilities in massive IoT ecosystems is more crucial than ever. Recent studies have demonstrated that control-flow graph (CFG) based bug search techniques can be effective and accurate in IoT devices across different architectures. However, these CFG-based bug search approaches are far from being scalable to handle an enormous amount of IoT devices in the wild, due to their expensive graph matching overhead. Inspired by rich experience in image and video search, we propose a new bug search scheme which addresses the scalability challenge in existing cross-platform bug search techniques and further improves search accuracy. Unlike existing techniques that directly conduct searches based upon raw features (CFGs) from the binary code, we convert the CFGs into high-level numeric feature vectors. Compared with the CFG feature, high-level numeric feature vectors are more robust to code variation across different architectures, and can easily achieve realtime search by using state-of-the-art hashing techniques. We have implemented a bug search engine, Genius, and compared it with state-of-art bug search approaches. Experimental results show that Genius outperforms baseline approaches for various query loads in terms of speed and accuracy. We also evaluated Genius on a real-world dataset of 33,045 devices which was collected from public sources and our system. The experiment showed that Genius can finish a search within 1 second on average when performed over 8,126 firmware images of 420,558,702 functions. By only looking at the top 50 candidates in the search result, we found 38 potentially vulnerable firmware images across 5 vendors, and confirmed 23 of them by our manual analysis. We also found that it took only 0.1 seconds on average to finish searching for all 154 vulnerabilities in two latest commercial firmware images from D-LINK. 103 of them are potentially vulnerable in these images, and 16 of them were confirmed.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978370"}, {"primary_key": "4066386", "vector": [], "sparse_vector": [], "title": "On the Provable Security of (EC)DSA Signatures.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Among the signature schemes most widely deployed in practice are the DSA (Digital Signature Algorithm) and its elliptic curves variant ECDSA. They are represented in many international standards, including IEEE P1363, ANSI X9.62, and FIPS 186-4. Their popularity stands in stark contrast to the absence of rigorous security analyses: Previous works either study modified versions of (EC)DSA or provide a security analysis of unmodified ECDSA in the generic group model. Unfortunately, works following the latter approach assume abstractions of non-algebraic functions over generic groups for which it remains unclear how they translate to the security of ECDSA in practice. For instance, it has been pointed out that prior results in the generic group model actually establish strong unforgeability of ECDSA, a property that the scheme de facto does not possess. As, further, no formal results are known for DSA, understanding the security of both schemes remains an open problem. In this work we propose GenericDSA, a signature framework that subsumes both DSA and ECDSA in unmodified form. It carefully models the \"modulo q\" conversion function of (EC)DSA as a composition of three independent functions. The two outer functions mimic algebraic properties in the function's domain and range, the inner one is modeled as a bijective random oracle. We rigorously prove results on the security of GenericDSA that indicate that forging signatures in (EC)DSA is as hard as solving discrete logarithms. Importantly, our proofs do not assume generic group behavior.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978413"}, {"primary_key": "4066387", "vector": [], "sparse_vector": [], "title": "A Comprehensive Formal Security Analysis of OAuth 2.0.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The OAuth 2.0 protocol is one of the most widely deployed authorization/single sign-on (SSO) protocols and also serves as the foundation for the new SSO standard OpenID Connect. Despite the popularity of OAuth, so far analysis efforts were mostly targeted at finding bugs in specific implementations and were based on formal models which abstract from many web features or did not provide a formal treatment at all. In this paper, we carry out the first extensive formal analysis of the OAuth 2.0 standard in an expressive web model. Our analysis aims at establishing strong authorization, authentication, and session integrity guarantees, for which we provide formal definitions. In our formal analysis, all four OAuth grant types (authorization code grant, implicit grant, resource owner password credentials grant, and the client credentials grant) are covered. They may even run simultaneously in the same and different relying parties and identity providers, where malicious relying parties, identity providers, and browsers are considered as well. Our modeling and analysis of the OAuth 2.0 standard assumes that security recommendations and best practices are followed in order to avoid obvious and known attacks.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978385"}, {"primary_key": "4066388", "vector": [], "sparse_vector": [], "title": "Hash First, Argue Later: Adaptive Verifiable Computations on Outsourced Data.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Proof systems for verifiable computation (VC) have the potential to make cloud outsourcing more trustworthy. Recent schemes enable a verifier with limited resources to delegate large computations and verify their outcome based on succinct arguments: verification complexity is linear in the size of the inputs and outputs (not the size of the computation). However, cloud computing also often involves large amounts of data, which may exceed the local storage and I/O capabilities of the verifier, and thus limit the use of VC. In this paper, we investigate multi-relation hash & prove schemes for verifiable computations that operate on succinct data hashes. Hence, the verifier delegates both storage and computation to an untrusted worker. She uploads data and keeps hashes; exchanges hashes with other parties; verifies arguments that consume and produce hashes; and selectively downloads the actual data she needs to access.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978368"}, {"primary_key": "4066393", "vector": [], "sparse_vector": [], "title": "9th International Workshop on Artificial Intelligence and Security: AISec 2016.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Artificial Intelligence (AI) and Machine Learning (ML) provide a set of useful analytic and decision-making techniques that are being leveraged by an ever-growing community of practitioners, including many whose applications have security-sensitive elements. However, while security researchers often utilize such techniques to address problems and AI/ML researchers develop techniques for Big Data analytics applications, neither community devotes much attention to the other. Within security research, AI/ML components are usually regarded as black-box solvers. Conversely, the learning community seldom considers the security/privacy implications entailed in the application of their algorithms when they are designing them. While these two communities generally focus on different directions, where these two fields do meet, interesting problems appear. Researchers working in this intersection have raised many novel questions for both communities and created a new branch of research known as secure learning. The AISec workshop has become the primary venue for this unique fusion of research. In recent years, there has been an increase of activity within the AISec/secure learning community. There are several reasons for this surge. Firstly, machine learning, data mining, and other artificial intelligence technologies play a key role in extracting knowledge, situational awareness, and security intelligence from Big Data. Secondly, companies like Google, Facebook, Amazon, and Splunk are increasingly exploring and deploying learning technologies to address Big Data problems for their customers.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2990479"}, {"primary_key": "4066396", "vector": [], "sparse_vector": [], "title": "&quot;Make Sure DSA Signing Exponentiations Really are Constant-Time&quot;.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "TLS and SSH are two of the most commonly used protocols for securing Internet traffic. Many of the implementations of these protocols rely on the cryptographic primitives provided in the OpenSSL library. In this work we disclose a vulnerability in OpenSSL, affecting all versions and forks (e.g. LibreSSL and BoringSSL) since roughly October 2005, which renders the implementation of the DSA signature scheme vulnerable to cache-based side-channel attacks. Exploiting the software defect, we demonstrate the first published cache-based key-recovery attack on these protocols: 260 SSH-2 handshakes to extract a 1024/160-bit DSA host key from an OpenSSH server, and 580 TLS 1.2 handshakes to extract a 2048/256-bit DSA key from an stunnel server.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978420"}, {"primary_key": "4066398", "vector": [], "sparse_vector": [], "title": "ECDSA Key Extraction from Mobile Devices via Nonintrusive Physical Side Channels.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We show that elliptic-curve cryptography implementations on mobile devices are vulnerable to electromagnetic and power side-channel attacks. We demonstrate full extraction of ECDSA secret signing keys from OpenSSL and CoreBitcoin running on iOS devices, and partial key leakage from OpenSSL running on Android and from iOS's CommonCrypto. These non-intrusive attacks use a simple magnetic probe placed in proximity to the device, or a power probe on the phone's USB cable. They use a bandwidth of merely a few hundred kHz, and can be performed cheaply using an audio card and an improvised magnetic probe.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978353"}, {"primary_key": "4066399", "vector": [], "sparse_vector": [], "title": "On the Security and Performance of Proof of Work Blockchains.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Proof of Work (PoW) powered blockchains currently account for more than 90% of the total market capitalization of existing digital cryptocurrencies. Although the security provisions of Bitcoin have been thoroughly analysed, the security guarantees of variant (forked) PoW blockchains (which were instantiated with different parameters) have not received much attention in the literature. This opens the question whether existing security analysis of Bitcoin's PoW applies to other implementations which have been instantiated with different consensus and/or network parameters.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978341"}, {"primary_key": "4066403", "vector": [], "sparse_vector": [], "title": "On the Security of Cracking-Resistant Password Vaults.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Password vaults are used to store login credentials, usually encrypted by a master password, relieving the user from memorizing a large number of complex passwords. To manage accounts on multiple devices, vaults are often stored at an online service, which substantially increases the risk of leaking the (encrypted) vault. To protect the master password against guessing attacks, previous work has introduced cracking-resistant password vaults based on Honey Encryption. If decryption is attempted with a wrong master password, they output plausible-looking decoy vaults, thus seemingly disabling offline guessing attacks. In this work, we propose attacks against cracking-resistant password vaults that are able to distinguish between real and decoy vaults with high accuracy and thus circumvent the offered protection. These attacks are based on differences in the generated distribution of passwords, which are measured using Kullback-Leibler divergence. Our attack is able to rank the correct vault into the 1.3% most likely vaults (on median), compared to 37.8% of the best-reported attack in previous work. (Note that smaller ranks are better, and 50% is achievable by random guessing.) We demonstrate that this attack is, to a certain extent, a fundamental problem with all static Natural Language Encoders (NLE), where the distribution of decoy vaults is fixed. We propose the notion of adaptive NLEs and demonstrate that they substantially limit the effectiveness of such attacks. We give one example of an adaptive NLE based on Markov models and show that the attack is only able to rank the decoy vaults with a median rank of 35.1%.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978416"}, {"primary_key": "4066409", "vector": [], "sparse_vector": [], "title": "Breaking Web Applications Built On Top of Encrypted Data.", "authors": ["<PERSON>", "<PERSON>", "<PERSON> Naveed", "<PERSON>", "<PERSON><PERSON>"], "summary": "We develop a systematic approach for analyzing client-server applications that aim to hide sensitive user data from untrusted servers. We then apply it to Mylar, a framework that uses multi-key searchable encryption (MKSE) to build Web applications on top of encrypted data.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978351"}, {"primary_key": "4066410", "vector": [], "sparse_vector": [], "title": "Prefetch Side-Channel Attacks: Bypassing SMAP and Kernel ASLR.", "authors": ["<PERSON>", "Clé<PERSON><PERSON> Maurice", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Modern operating systems use hardware support to protect against control-flow hijacking attacks such as code-injection attacks. Typically, write access to executable pages is prevented and kernel mode execution is restricted to kernel code pages only. However, current CPUs provide no protection against code-reuse attacks like ROP. ASLR is used to prevent these attacks by making all addresses unpredictable for an attacker. Hence, the kernel security relies fundamentally on preventing access to address information. We introduce Prefetch Side-Channel Attacks, a new class of generic attacks exploiting major weaknesses in prefetch instructions. This allows unprivileged attackers to obtain address information and thus compromise the entire system by defeating SMAP, SMEP, and kernel ASLR. Prefetch can fetch inaccessible privileged memory into various caches on Intel x86. It also leaks the translation-level for virtual addresses on both Intel x86 and ARMv8-A. We build three attacks exploiting these properties. Our first attack retrieves an exact image of the full paging hierarchy of a process, defeating both user space and kernel space ASLR. Our second attack resolves virtual to physical addresses to bypass SMAP on 64-bit Linux systems, enabling ret2dir attacks. We demonstrate this from unprivileged user programs on Linux and inside Amazon EC2 virtual machines. Finally, we demonstrate how to defeat kernel ASLR on Windows 10, enabling ROP attacks on kernel and driver binary code. We propose a new form of strong kernel isolation to protect commodity systems incuring an overhead of only 0.06-5.09%.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978356"}, {"primary_key": "4066415", "vector": [], "sparse_vector": [], "title": "POSTER: Towards Privacy-Preserving Biometric Identification in Cloud Computing.", "authors": ["<PERSON><PERSON>", "Junbeom Hur"], "summary": "<PERSON> et al. recently proposed a privacy-preserving biometric identification scheme. However, the security assumption of the scheme does not capture practical aspects of real world attacks. In this paper, we consider a practical attack model which results in the leakage of biometric data in <PERSON> et al.'s scheme. We first show the feasibility of our attack model and demonstrate how an attacker is able to recover the biometric data. Then, we propose a new biometric identification scheme that is secure against the attack model.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989048"}, {"primary_key": "4066418", "vector": [], "sparse_vector": [], "title": "TypeSan: Practical Type Confusion Detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The low-level C++ programming language is ubiquitously used for its modularity and performance. Typecasting is a fundamental concept in C++ (and object-oriented programming in general) to convert a pointer from one object type into another. However, downcasting (converting a base class pointer to a derived class pointer) has critical security implications due to potentially different object memory layouts. Due to missing type safety in C++, a downcasted pointer can violate a programmer's intended pointer semantics, allowing an attacker to corrupt the underlying memory in a type-unsafe fashion. This vulnerability class is receiving increasing attention and is known as type confusion (or bad-casting). Several existing approaches detect different forms of type confusion, but these solutions are severely limited due to both high run-time performance overhead and low detection coverage.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978405"}, {"primary_key": "4066420", "vector": [], "sparse_vector": [], "title": "PhishEye: Live Monitoring of Sandboxed Phishing Kits.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Phishing is a form of online identity theft that deceives unaware users into disclosing their confidential information. While significant effort has been devoted to the mitigation of phishing attacks, much less is known about the entire life-cycle of these attacks in the wild, which constitutes, however, a main step toward devising comprehensive anti-phishing techniques. In this paper, we present a novel approach to sandbox live phishing kits that completely protects the privacy of victims. By using this technique, we perform a comprehensive real-world assessment of phishing attacks, their mechanisms, and the behavior of the criminals, their victims, and the security community involved in the process -- based on data collected over a period of five months.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978330"}, {"primary_key": "4066422", "vector": [], "sparse_vector": [], "title": "PREDATOR: Proactive Recognition and Elimination of Domain Abuse at Time-Of-Registration.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Miscreants register thousands of new domains every day to launch Internet-scale attacks, such as spam, phishing, and drive-by downloads. Quickly and accurately determining a domain's reputation (association with malicious activity) provides a powerful tool for mitigating threats and protecting users. Yet, existing domain reputation systems work by observing domain use (e.g., lookup patterns, content hosted) often too late to prevent miscreants from reaping benefits of the attacks that they launch. As a complement to these systems, we explore the extent to which features evident at domain registration indicate a domain's subsequent use for malicious activity. We develop PREDATOR, an approach that uses only time-of-registration features to establish domain reputation. We base its design on the intuition that miscreants need to obtain many domains to ensure profitability and attack agility, leading to abnormal registration behaviors (e.g., burst registrations, textually similar names). We evaluate PREDATOR using registration logs of second-level .com and .net domains over five months. PREDATOR achieves a 70% detection rate with a false positive rate of 0.35%, thus making it an effective and early first line of defense against the misuse of DNS domains. It predicts malicious domains when they are registered, which is typically days or weeks earlier than existing DNS blacklists.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978317"}, {"primary_key": "4066426", "vector": [], "sparse_vector": [], "title": "Cybersecurity, Nuclear Security, <PERSON>, and Illogical Logic.", "authors": ["<PERSON>"], "summary": "My work that is being recognized by the 2015 ACM A. M<PERSON> Turing Award is in cybersecurity, while my primary interest for the last thirty-five years is concerned with reducing the risk that nuclear deterrence will fail and destroy civilization. This Turing Lecture draws connections between those seemingly disparate areas as well as <PERSON>'s elegant proof that the computable real numbers, while denumerable, are not effectively denumerable.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2976757"}, {"primary_key": "4066431", "vector": [], "sparse_vector": [], "title": "Leave Your Phone at the Door: Side Channels that Reveal Factory Floor Secrets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "From pencils to commercial aircraft, every man-made object must be designed and manufactured. When it is cheaper or easier to steal a design or a manufacturing process specification than to invent one's own, the incentive for theft is present. As more and more manufacturing data comes online, incidents of such theft are increasing. In this paper, we present a side-channel attack on manufacturing equipment that reveals both the form of a product and its manufacturing process, i.e., exactly how it is made. In the attack, a human deliberately or accidentally places an attack-enabled phone close to the equipment or makes or receives a phone call on any phone nearby. The phone executing the attack records audio and, optionally, magnetometer data. We present a method of reconstructing the product's form and manufacturing process from the captured data, based on machine learning, signal processing, and human assistance. We demonstrate the attack on a 3D printer and a CNC mill, each with its own acoustic signature, and discuss the commonalities in the sensor data captured for these two different machines. We compare the quality of the data captured with a variety of smartphone models. Capturing data from the 3D printer, we reproduce the form and process information of objects previously unknown to the reconstructors. On average, our accuracy is within 1 mm in reconstructing the length of a line segment in a fabricated object's shape and within 1 degree in determining an angle in a fabricated object's shape. We conclude with recommendations for defending against these attacks.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978323"}, {"primary_key": "4066432", "vector": [], "sparse_vector": [], "title": "An In-Depth Study of More Than Ten Years of Java Exploitation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "When created, the Java platform was among the first runtimes designed with security in mind. Yet, numerous Java versions were shown to contain far-reaching vulnerabilities, permitting denial-of-service attacks or even worse allowing intruders to bypass the runtime's sandbox mechanisms, opening the host system up to many kinds of further attacks.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978361"}, {"primary_key": "4066433", "vector": [], "sparse_vector": [], "title": "Enforcing Least Privilege Memory Views for Multithreaded Applications.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Failing to properly isolate components in the same address space has resulted in a substantial amount of vulnerabilities. Enforcing the least privilege principle for memory accesses can selectively isolate software components to restrict attack surface and prevent unintended cross-component memory corruption. However, the boundaries and interactions between software components are hard to reason about and existing approaches have failed to stop attackers from exploiting vulnerabilities caused by poor isolation. We present the secure memory views (SMV) model: a practical and efficient model for secure and selective memory isolation in monolithic multithreaded applications. SMV is a third generation privilege separation technique that offers explicit access control of memory and allows concurrent threads within the same process to partially share or fully isolate their memory space in a controlled and parallel manner following application requirements. An evaluation of our prototype in the Linux kernel (TCB < 1,800 LOC) shows negligible runtime performance overhead in real-world applications including Cherokee web server (< 0.69%), Apache httpd web server (< 0.93%), and Mozilla Firefox web browser (< 1.89%) with at most 12 LOC changes.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978327"}, {"primary_key": "4066434", "vector": [], "sparse_vector": [], "title": "MERS: Statistical Test Generation for Side-Channel Analysis based Trojan Detection.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Hardware Trojan detection has emerged as a critical challenge to ensure security and trustworthiness of integrated circuits. A vast majority of research efforts in this area has utilized side-channel analysis for Trojan detection. Functional test generation for logic testing is a promising alternative but it may not be helpful if a Trojan cannot be fully activated or the Trojan effect cannot be propagated to the observable outputs. Side-channel analysis, on the other hand, can achieve significantly higher detection coverage for Trojans of all types/sizes, since it does not require activation/propagation of an unknown Trojan. However, they have often limited effectiveness due to poor detection sensitivity under large process variations and small Trojan footprint in side-channel signature. In this paper, we address this critical problem through a novel side-channel-aware test generation approach, based on a concept of Multiple Excitation of Rare Switching (MERS), that can significantly increase Trojan detection sensitivity. The paper makes several important contributions: i) it presents in detail the statistical test generation method, which can generate high-quality testset for creating high relative activity in arbitrary Trojan instances; ii) it analyzes the effectiveness of generated testset in terms of Trojan coverage; and iii) it describes two judicious reordering methods can further tune the testset and greatly improve the side channel sensitivity. Simulation results demonstrate that the tests generated by MERS can significantly increase the Trojans sensitivity, thereby making Trojan detection effective using side-channel analysis.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978396"}, {"primary_key": "4066441", "vector": [], "sparse_vector": [], "title": "POSTER: A Behavioural Authentication System for Mobile Users.", "authors": ["<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Active behavioural-based authentication systems are challenge-response based implicit authentication systems that authenticate users using the behavioural features of the users when responding to challenges that are sent from the server. They provide a flexible (no extra hardware) and secure second factor for authentication systems, with applications including protection against identity theft and password compromise of web applications. We propose a novel active behavioural authentication system for mobile devices, called DAC (Draw A Circle), where a challenge specifies a set of constraints on a circle and the response is a user drawn circle that satisfies the constraints. We carefully select a set of features that capture behavioural traits of the user which is used to construct a profile for them, then design a matching algorithm that allows users to be authenticated with approximately 95% accuracy. We discuss our implementation, and present our experimental results that show, (i) the accuracy of authentication system and (ii) non-delegateability of profile, guaranteeing that the user cannot pass their credentials to others.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989065"}, {"primary_key": "4066445", "vector": [], "sparse_vector": [], "title": "Breaking Kernel Address Space Layout Randomization with Intel TSX.", "authors": ["<PERSON><PERSON><PERSON> Jang", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Kernel hardening has been an important topic since many applications and security mechanisms often consider the kernel as part of their Trusted Computing Base (TCB). Among various hardening techniques, Kernel Address Space Layout Randomization (KASLR) is the most effective and widely adopted defense mechanism that can practically mitigate various memory corruption vulnerabilities, such as buffer overflow and use-after-free. In principle, KASLR is secure as long as no memory leak vulnerability exists and high entropy is ensured.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978321"}, {"primary_key": "4066446", "vector": [], "sparse_vector": [], "title": "Safely Measuring Tor.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Tor is a popular network for anonymous communication. The usage and operation of Tor is not well-understood, however, because its privacy goals make common measurement approaches ineffective or risky. We present PrivCount, a system for measuring the Tor network designed with user privacy as a primary goal. PrivCount securely aggregates measurements across Tor relays and over time to produce differentially private outputs. PrivCount improves on prior approaches by enabling flexible exploration of many diverse kinds of Tor measurements while maintaining accuracy and privacy for each. We use PrivCount to perform a measurement study of Tor of sufficient breadth and depth to inform accurate models of Tor users and traffic. Our results indicate that Tor has 710,000 users connected but only 550,000 active at a given time, that Web traffic now constitutes 91% of data bytes on Tor, and that the strictness of relays' connection policies significantly affects the type of application data they forward.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978310"}, {"primary_key": "4066449", "vector": [], "sparse_vector": [], "title": "&quot;The Web/Local&quot; Boundary Is Fuzzy: A Security Study of Chrome&apos;s Process-based Sandboxing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Hong Hu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Process-based isolation, suggested by several research prototypes, is a cornerstone of modern browser security architectures. Google Chrome is the first commercial browser that adopts this architecture. Unlike several research prototypes, Chrome's process-based design does not isolate different web origins, but primarily promises to protect \"the local system\" from \"the web\". However, as billions of users now use web-based cloud services (e.g., Dropbox and Google Drive), which are integrated into the local system, the premise that browsers can effectively isolate the web from the local system has become questionable. In this paper, we argue that, if the process-based isolation disregards the same-origin policy as one of its goals, then its promise of maintaining the \"web/local system (local)\" separation is doubtful. Specifically, we show that existing memory vulnerabilities in Chrome's renderer can be used as a stepping-stone to drop executables/scripts in the local file system, install unwanted applications and misuse system sensors. These attacks are purely data-oriented and do not alter any control flow or import foreign code. Thus, such attacks bypass binary-level protection mechanisms, including ASLR and in-memory partitioning. Finally, we discuss various full defenses and present a possible way to mitigate the attacks presented.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978414"}, {"primary_key": "4066451", "vector": [], "sparse_vector": [], "title": "DPSense: Differentially Private Crowdsourced Spectrum Sensing.", "authors": ["<PERSON><PERSON><PERSON> Jin", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Dynamic spectrum access (DSA) has great potential to address worldwide spectrum shortage by enhancing spectrum efficiency. It allows unlicensed secondary users to access the underutilized licensed spectrum when the licensed primary users are not transmitting. As a key enabler for DSA systems, crowdsourced spectrum sensing (CSS) allows a spectrum sensing provider (SSP) to outsource the sensing of spectrum occupancy to distributed mobile users. In this paper, we propose DPSense, a novel framework that allows the SSP to select mobile users for executing spatiotemporal spectrum-sensing tasks without violating the location privacy of mobile users. Detailed evaluations on real location traces confirm that DPSense can provide differential location privacy to mobile users while ensuring that the SSP can accomplish spectrum-sensing tasks with overwhelming probability and also the minimal cost.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978426"}, {"primary_key": "4066454", "vector": [], "sparse_vector": [], "title": "Condensed Cryptographic Currencies Crash Course (C5).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "\"Bitcoin is a rare case where practice seems to be ahead of theory.\" <PERSON> et al. [3] This tutorial aims to further close the gap between IT security research and the area of cryptographic currencies and block chains. We will describe and refer to Bitcoin as an example throughout the tutorial, as it is the most prominent representative of such a system. It also is a good reference to discuss the underlying block chain mechanics which are the foundation of various altcoins and other derived systems. In this tutorial, the topic of cryptographic currencies is solely addressed from a technical IT security point-of-view. Therefore we do not cover any legal, sociological, financial or economical aspects.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2976754"}, {"primary_key": "4066455", "vector": [], "sparse_vector": [], "title": "The Ring of Gyges: Investigating the Future of Criminal Smart Contracts.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Thanks to their anonymity (pseudonymity) and elimination of trusted intermediaries, cryptocurrencies such as Bitcoin have created or stimulated growth in many businesses and communities. Unfortunately, some of these are criminal, e.g., money laundering, illicit marketplaces, and ransomware. Next-generation cryptocurrencies such as Ethereum will include rich scripting languages in support of smart contracts, programs that autonomously intermediate transactions. In this paper, we explore the risk of smart contracts fueling new criminal ecosystems. Specifically, we show how what we call criminal smart contracts (CSCs) can facilitate leakage of confidential information, theft of cryptographic keys, and various real-world crimes (murder, arson, terrorism).", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978362"}, {"primary_key": "4066461", "vector": [], "sparse_vector": [], "title": "Adversarial Data Mining: Big Data Meets Cyber Security.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As more and more cyber security incident data ranging from systems logs to vulnerability scan results are collected, manually analyzing these collected data to detect important cyber security events become impossible. Hence, data mining techniques are becoming an essential tool for real-world cyber security applications. For example, a report from Gartner [gartner12] claims that \"Information security is becoming a big data analytics problem, where massive amounts of data will be correlated, analyzed and mined for meaningful patterns\". Of course, data mining/analytics is a means to an end where the ultimate goal is to provide cyber security analysts with prioritized actionable insights derived from big data. This raises the question, can we directly apply existing techniques to cyber security applications? One of the most important differences between data mining for cyber security and many other data mining applications is the existence of malicious adversaries that continuously adapt their behavior to hide their actions and to make the data mining models ineffective. Unfortunately, traditional data mining techniques are insufficient to handle such adversarial problems directly. The adversaries adapt to the data miner's reactions, and data mining algorithms constructed based on a training dataset degrades quickly. To address these concerns, over the last couple of years new and novel data mining techniques which is more resilient to such adversarial behavior are being developed in machine learning and data mining community. We believe that lessons learned as a part of this research direction would be beneficial for cyber security researchers who are increasingly applying machine learning and data mining techniques in practice.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2976753"}, {"primary_key": "4066462", "vector": [], "sparse_vector": [], "title": "POSTER: A Keyless Efficient Algorithm for Data Protection by Means of Fragmentation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Although symmetric ciphers may provide strong computational security, a key leakage makes the encrypted data vulnerable. In a distributed storage environment, reinforcement of data protection consists of dispersing data over multiple servers in a way that no information can be obtained from data fragments until a defined threshold of them has been collected. A secure fragmentation is usually enabled by secret sharing, information dispersal algorithms or data shredding. However, these solutions suffer from various limitations, like additional storage requirement or performance burden. This poster presents a novel flexible keyless fragmentation scheme, balancing memory use and performance with security. It could be applied in many different contexts, such as dispersal of outsourced data over one or multiple clouds or in resource-restrained environments like sensor networks. The scheme has been implemented in JAVA and Matlab. Preliminary analysis shows good performance and data protection.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989043"}, {"primary_key": "4066463", "vector": [], "sparse_vector": [], "title": "On the Security and Scalability of Bitcoin&apos;s Blockchain.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "The blockchain emerges as an innovative tool which proves to be useful in a number of application scenarios. A number of large industrial players, such as IBM, Microsoft, Intel, and NEC, are currently investing in exploiting the blockchain in order to enrich their portfolio of products. A number of researchers and practitioners speculate that the blockchain technology can change the way we see a number of online applications today. Although it is still early to tell for sure, it is expected that the blockchain will stimulate considerable changes to a large number of products and will positively impact the digital experience of many individuals around the globe. In this tutorial, we overview, detail, and analyze the security provisions of Bitcoin and its underlying blockchain-effectively capturing recently reported attacks and threats in the system. Our contributions go beyond the mere analysis of reported vulnerabilities of Bitcoin; namely, we describe and evaluate a number of countermeasures to deter threats on the system-some of which have already been incorporated in the system. Recall that Bitcoin has been forked multiple times in order to fine-tune the consensus (i.e., the block generation time and the hash function), and the network parameters (e.g., the size of blocks). As such, the results reported in this tutorial are not only restricted to Bitcoin, but equally apply to a number of \"altcoins\" which are basically clones/forks of the Bitcoin source code. Given the increasing number of alternative blockchain proposals, this tutorial extracts the basic security lessons learnt from the Bitcoin system with the aim to foster better designs and analysis of next-generation secure blockchain currencies and technologies.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2976756"}, {"primary_key": "4066464", "vector": [], "sparse_vector": [], "title": "Introduction to Credit Networks: Security, Privacy, and Applications.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Credit networks model transitive IOweYou (IOU) credit between their users. With their flexible-yet-scalable design and robustness against intrusion, we are observing a rapid increase in their popularity as a backbone of real-world permission-less payment settlement networks (e.g., Ripple and Stellar) as well as several other weak-identity systems requiring Sybil-tolerant communication. In payment scenarios, due to their unique capability to unite emerging crypto-currencies and user-defined currencies with the traditional fiat currency and banking systems, several existing and new payment enterprises are entering in this space. Nevertheless, this enthusiasm in the market significantly exceeds our understanding of security, privacy, and reliability of these inherently distributed systems. Currently employed ad hoc strategies to fix apparent flaws have made those systems vulnerable to bigger problems once they become lucrative targets for malicious players. In this tutorial, we first define the concept of IOU credit networks, and describe some of the important credit network applications. We then describe and analyze recent and ongoing projects to improve the credit-network security, privacy and reliability. We end our discussion with interesting open problems and systems challenges in the field. This introductory tutorial is accessible to the standard CCS audience with graduate-level security knowledge.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2976755"}, {"primary_key": "4066466", "vector": [], "sparse_vector": [], "title": "Generic Attacks on Secure Outsourced Databases.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>;<PERSON>"], "summary": "Recently, various protocols have been proposed for securely outsourcing database storage to a third party server, ranging from systems with \"full-fledged\" security based on strong cryptographic primitives such as fully homomorphic encryption or oblivious RAM, to more practical implementations based on searchable symmetric encryption or even on deterministic and order-preserving encryption. On the flip side, various attacks have emerged that show that for some of these protocols confidentiality of the data can be compromised, usually given certain auxiliary information. We take a step back and identify a need for a formal understanding of the inherent efficiency/privacy trade-off in outsourced database systems, independent of the details of the system. We propose abstract models that capture secure outsourced storage systems in sufficient generality, and identify two basic sources of leakage, namely access pattern and ommunication volume. We use our models to distinguish certain classes of outsourced database systems that have been proposed, and deduce that all of them exhibit at least one of these leakage sources.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978386"}, {"primary_key": "4066467", "vector": [], "sparse_vector": [], "title": "MASCOT: Faster Malicious Arithmetic Secure Computation with Oblivious Transfer.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We consider the task of secure multi-party computation of arithmetic circuits over a finite field. Unlike Boolean circuits, arithmetic circuits allow natural computations on integers to be expressed easily and efficiently. In the strongest setting of malicious security with a dishonest majority --- where any number of parties may deviate arbitrarily from the protocol --- most existing protocols require expensive public-key cryptography for each multiplication in the preprocessing stage of the protocol, which leads to a high total cost. We present a new protocol that overcomes this limitation by using oblivious transfer to perform secure multiplications in general finite fields with reduced communication and computation. Our protocol is based on an arithmetic view of oblivious transfer, with careful consistency checks and other techniques to obtain malicious security at a cost of less than 6 times that of semi-honest security. We describe a highly optimized implementation together with experimental results for up to five parties. By making extensive use of parallelism and SSE instructions, we improve upon previous runtimes for MPC over arithmetic circuits by more than 200 times.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978357"}, {"primary_key": "4066468", "vector": [], "sparse_vector": [], "title": "WISCS&apos;16: The 3rd ACM Workshop on Information Sharing and Collaborative Security.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The objective of the 3rd ACM Workshop on Information Sharing and Collaborative Security is to advance the scientific foundations for sharing security-related data. Improving information sharing remains an important theme in the computer security community. A number of new sharing communities have been formed. Also, so called \"threat intelligence\" originating from open, commercial or governmental sources has by now become an important, commonly used tool for detecting and mitigating attacks in organizations. Security vendors are offering novel technologies for sharing, managing and consuming such data. The OASIS Technical Committee for Cyber Threat Intelligence (CTI) is creating a standard for structured sharing of information. This is the largest TC within OASIS attesting to the broad interest in the topic. As progress in real-life deployment of information sharing makes clear, the creation, analysis, sharing, and effective use of security data continues to raise intriguing technical problems. Addressing these problems will be critical for the ultimate success of sharing efforts and will benefit greatly from the diverse knowledge and techniques the scientific community brings. The 3rd ACM Workshop on Information Sharing and Collaborative Security (WISCS'16) brings together experts and practitioners from academia, industry, and government to present innovative research, case studies, and legal and policy issues. WISCS'16 is held in Vienna, Austria on October 24, 2016 in conjunction with the 23rd ACM Conference on Computer and Communications Security (ACM CCS 2016).", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2990490"}, {"primary_key": "4066471", "vector": [], "sparse_vector": [], "title": "Practical Non-Malleable Codes from l-more Extractable Hash Functions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this work, we significantly improve the efficiency of non-malleable codes in the split state model, by constructing a code with codeword length (roughly), where |s| is the length of the message, and k is the security parameter. This is a substantial improvement over previous constructions, both asymptotically and concretely.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978352"}, {"primary_key": "4066477", "vector": [], "sparse_vector": [], "title": "Attribute-based Key Exchange with General Policies.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Attribute-based methods provide authorization to parties based on whether their set of attributes (e.g., age, organization, etc.) fulfills a policy. In attribute-based encryption (ABE), authorized parties can decrypt, and in attribute-based credentials (ABCs), authorized parties can authenticate themselves. In this paper, we combine elements of ABE and ABCs together with garbled circuits to construct attribute-based key exchange (ABKE). Our focus is on an interactive solution involving a client that holds a certificate (issued by an authority) vouching for that client's attributes and a server that holds a policy computable on such a set of attributes. The goal is for the server to establish a shared key with the client but only if the client's certified attributes satisfy the policy. Our solution enjoys strong privacy guarantees for both the client and the server, including attribute privacy and unlinkability of client sessions.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978359"}, {"primary_key": "4066478", "vector": [], "sparse_vector": [], "title": "Efficient Batched Oblivious PRF with Applications to Private Set Intersection.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We describe a lightweight protocol for oblivious evaluation of a pseudorandom function (OPRF) in the presence of semihonest adversaries. In an OPRF protocol a receiver has an input r; the sender gets output s and the receiver gets output F(s; r), where F is a pseudorandom function and s is a random seed. Our protocol uses a novel adaptation of 1-out-of-2 OT-extension protocols, and is particularly efficient when used to generate a large batch of OPRF instances. The cost to realize m OPRF instances is roughly the cost to realize 3:5m instances of standard 1-out-of-2 OTs (using state-of-the-art OT extension). We explore in detail our protocol's application to semihonest secure private set intersection (PSI). The fastest state-of- the-art PSI protocol (<PERSON><PERSON> et al., Usenix 2015) is based on efficient OT extension. We observe that our OPRF can be used to remove their PSI protocol's dependence on the bit-length of the parties' items. We implemented both PSI protocol variants and found ours to be 3.1{3.6 faster than <PERSON><PERSON> et al. for PSI of 128-bit strings and sufficiently large sets. Concretely, ours requires only 3.8 seconds to securely compute the intersection of 220-size sets, regardless of the bitlength of the items. For very large sets, our protocol is only 4:3 slower than the insecure naive hashing approach for PSI.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978381"}, {"primary_key": "4066480", "vector": [], "sparse_vector": [], "title": "Protecting Insecure Communications with Topology-aware Network Tunnels.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Unencrypted and unauthenticated protocols present security and privacy risks to end-to-end communications. At the same time we observe that only 30% of popular web servers offer HTTPS. Even when services support it, implementation vulnerabilities threaten their security. In this paper we propose an architecture called Topology-aware Network Tunnels (TNT) which minimizes insecure network paths to Internet services without their participation. TNT is not a substitute for TLS. We determine that popular web destinations are collocated in a small set of networks with 10 autonomous systems hosting 66% of traffic. At the same time cloud providers own these networks or are very close to them. Therefore clients can strategically establish secure tunnels to these providers and route their traffic through them. As a result adversaries not able to compromise the web service or its hosting provider are presented with encrypted and authenticated traffic instead of today's plain text. The strategic placement of network tunnels, gathering of network intelligence and routing decisions of the TNT architecture are not found in VPN services, network proxies or Tor. Existing overlay routing systems such as RON and one-hop source routing cannot substitute TNT. We implement our proposal as a routing software suite and evaluate it extensively using diverse cloud and ISP networks. We eliminate plain-text traffic to the Internet for 20% of web servers, reduce it to 1 network hop for an additional 20% and minimize it for the rest. We preserve the original network latency and page load time. TNT is practical and can be deployed by clients today.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978305"}, {"primary_key": "4066482", "vector": [], "sparse_vector": [], "title": "POSTER: Weighing in eHealth Security.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "eHealth devices such as smart scales and wearable fitness trackers are a key part of many health technology solutions. However, these eHealth devices can be vulnerable to privacy and security related attacks. In this poster, we propose a security analysis framework for eHealth devices, called mH-PriSe, that will yield useful information for security analysts, vendors, health care providers, and consumers. We demonstrate our framework by analysing scales from 6 vendors. Our results show that while vendors strive to address security and privacy issues correctly, challenges remain in many cases. Only 5 out of 8 solutions can be recommended with some caveats whereas the remaining 3 solutions expose severe vulnerabilities.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989044"}, {"primary_key": "4066483", "vector": [], "sparse_vector": [], "title": "A Unilateral-to-Mutual Authentication Compiler for Key Exchange (with Applications to Client Authentication in TLS 1.3).", "authors": ["<PERSON>"], "summary": "We study the question of how to build \"compilers\" that transform a unilaterally authenticated (UA) key-exchange protocol into a mutually-authenticated (MA) one. We present a simple and efficient compiler and characterize the UA protocols that the compiler upgrades to the MA model, showing this to include a large and important class of UA protocols. The question, while natural, has not been studied widely. Our work is motivated in part by the ongoing work on the design of TLS 1.3, specifically the design of the client authentication mechanisms including the challenging case of post-handshake authentication. Our approach supports the analysis of these mechanisms in a general and modular way, in particular aided by the notion of \"functional security\" that we introduce as a generalization of key exchange models and which may be of independent interest.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978325"}, {"primary_key": "4066484", "vector": [], "sparse_vector": [], "title": "Identifying the Scan and Attack Infrastructures Behind Amplification DDoS Attacks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Amplification DDoS attacks have gained popularity and become a serious threat to Internet participants. However, little is known about where these attacks originate, and revealing the attack sources is a non-trivial problem due to the spoofed nature of the traffic.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978293"}, {"primary_key": "4066486", "vector": [], "sparse_vector": [], "title": "Amortizing Secure Computation with Penalties.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Motivated by the impossibility of achieving fairness in secure computation [<PERSON><PERSON><PERSON>, STOC 1986], recent works study a model of fairness in which an adversarial party that aborts on receiving output is forced to pay a mutually predefined monetary penalty to every other party that did not receive the output. These works show how to design protocols for secure computation with penalties that guarantees that either fairness is guaranteed or that each honest party obtains a monetary penalty from the adversary. Protocols for this task are typically designed in an hybrid model where parties have access to a \"claim-or-refund\" transaction functionality denote FCR*.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978424"}, {"primary_key": "4066487", "vector": [], "sparse_vector": [], "title": "Improvements to Secure Computation with Penalties.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Motivated by the impossibility of achieving fairness in secure computation [<PERSON><PERSON><PERSON>, STOC 1986], recent works study a model of fairness in which an adversarial party that aborts on receiving output is forced to pay a mutually predefined monetary penalty to every other party that did not receive the output. These works show how to design protocols for secure computation with penalties that tolerate an arbitrary number of corruptions.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978421"}, {"primary_key": "4066488", "vector": [], "sparse_vector": [], "title": "POSTER: Towards Highly Interactive Honeypots for Industrial Control Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Honeypots are a common tool to set intrusion alarms and to study attacks against computer systems. In order to be convincing, honeypots attempt to resemble actual systems that are in active use. Recently, researchers have begun to develop honeypots for programmable logic controllers (PLCs). The tools of which we are aware have limited functionality compared to genuine devices. Particularly, they do not support running actual PLC programs.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989063"}, {"primary_key": "4066491", "vector": [], "sparse_vector": [], "title": "5Gen: A Framework for Prototyping Applications Using Multilinear Maps and Matrix Branching Programs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Secure multilinear maps (mmaps) have been shown to have remarkable applications in cryptography, such as multi-input functional encryption (MIFE) and program obfuscation. To date, there has been little evaluation of the performance of these applications. In this paper we initiate a systematic study of mmap-based constructions. We build a general framework, called 5Gen, to experiment with these applications. At the top layer we develop a compiler that takes in a high-level program and produces an optimized matrix branching program needed for the applications we consider. Next, we optimize and experiment with several MIFE and obfuscation constructions and evaluate their performance. The 5Gen framework is modular and can easily accommodate new mmap constructions as well as new MIFE and obfuscation constructions, as well as being an open-source tool that can be used by other research groups to experiment with a variety of mmap-based constructions.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978314"}, {"primary_key": "4066492", "vector": [], "sparse_vector": [], "title": "Order-Revealing Encryption: New Constructions, Applications, and Lower Bounds.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In the last few years, there has been significant interest in developing methods to search over encrypted data. In the case of range queries, a simple solution is to encrypt the contents of the database using an order-preserving encryption (OPE) scheme (i.e., an encryption scheme that supports comparisons over encrypted values). However, <PERSON><PERSON><PERSON> et al. (CCS 2015) recently showed that OPE-encrypted databases are extremely vulnerable to \"inference attacks.\"", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978376"}, {"primary_key": "4066493", "vector": [], "sparse_vector": [], "title": "iLock: Immediate and Automatic Locking of Mobile Devices against Data Theft.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Jingchao Sun", "<PERSON><PERSON><PERSON> Jin", "<PERSON><PERSON><PERSON>"], "summary": "Mobile device losses and thefts are skyrocketing. The sensitive data hosted on a lost/stolen device are fully exposed to the adversary. Although password-based authentication mechanisms are available on mobile devices, many users reportedly do not use them, and a device may be lost/stolen while in the unlocked mode. This paper presents the design and evaluation of iLock, a secure and usable defense against data theft on a lost/stolen mobile device. iLock automatically, quickly, and accurately recognizes the user's physical separation from his/her device by detecting and analyzing the changes in wireless signals. Once significant physical separation is detected, the device is immediately locked to prevent data theft. iLock relies on acoustic signals and requires at least one speaker and one microphone that are available on most COTS (commodity-off-the-shelf) mobile devices. Extensive experiments on Samsung Galaxy S5 show that iLock can lock the device with negligible false positives and negatives.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978294"}, {"primary_key": "4066495", "vector": [], "sparse_vector": [], "title": "When CSI Meets Public WiFi: Inferring Your Mobile Phone Password via WiFi Signals.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this study, we present WindTalker, a novel and practical keystroke inference framework that allows an attacker to infer the sensitive keystrokes on a mobile device through WiFi-based side-channel information. <PERSON><PERSON><PERSON><PERSON> is motivated from the observation that keystrokes on mobile devices will lead to different hand coverage and the finger motions, which will introduce a unique interference to the multi-path signals and can be reflected by the channel state information (CSI). The adversary can exploit the strong correlation between the CSI fluctuation and the keystrokes to infer the user's number input. WindTalker presents a novel approach to collect the target's CSI data by deploying a public WiFi hotspot. Compared with the previous keystroke inference approach, WindTalker neither deploys external devices close to the target device nor compromises the target device. Instead, it utilizes the public WiFi to collect user's CSI data, which is easy-to-deploy and difficult-to-detect. In addition, it jointly analyzes the traffic and the CSI to launch the keystroke inference only for the sensitive period where password entering occurs. WindTalker can be launched without the requirement of visually seeing the smart phone user's input process, backside motion, or installing any malware on the tablet. We implemented Windtalker on several mobile phones and performed a detailed case study to evaluate the practicality of the password inference towards Alipay, the largest mobile payment platform in the world. The evaluation results show that the attacker can recover the key with a high successful rate.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978397"}, {"primary_key": "4066501", "vector": [], "sparse_vector": [], "title": "Lurking <PERSON>ce in the Cloud: Understanding and Detecting Cloud Repository as a Malicious Service.", "authors": ["<PERSON><PERSON><PERSON>", "Sumayah <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The popularity of cloud hosting services also brings in new security challenges: it has been reported that these services are increasingly utilized by miscreants for their malicious online activities. Mitigating this emerging threat, posed by such \"bad repositories\" (simply Bar), is challenging due to the different hosting strategy to traditional hosting service, the lack of direct observations of the repositories by those outside the cloud, the reluctance of the cloud provider to scan its customers' repositories without their consent, and the unique evasion strategies employed by the adversary. In this paper, we took the first step toward understanding and detecting this emerging threat. Using a small set of \"seeds\" (i.e., confirmed Bars), we identified a set of collective features from the websites they serve (e.g., attempts to hide Bars), which uniquely characterize the Bars. These features were utilized to build a scanner that detected over 600 Bars on leading cloud platforms like Amazon, Google, and 150K sites, including popular ones like groupon.com, using them. Highlights of our study include the pivotal roles played by these repositories on malicious infrastructures and other important discoveries include how the adversary exploited legitimate cloud repositories and why the adversary uses Bars in the first place that has never been reported. These findings bring such malicious services to the spotlight and contribute to a better understanding and ultimately eliminating this new threat.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978349"}, {"primary_key": "4066502", "vector": [], "sparse_vector": [], "title": "Acing the IOC Game: Toward Automatic Discovery and Analysis of Open-Source Cyber Threat Intelligence.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "To adapt to the rapidly evolving landscape of cyber threats, security professionals are actively exchanging Indicators of Compromise (IOC) (e.g., malware signatures, botnet IPs) through public sources (e.g. blogs, forums, tweets, etc.). Such information, often presented in articles, posts, white papers etc., can be converted into a machine-readable OpenIOC format for automatic analysis and quick deployment to various security mechanisms like an intrusion detection system. With hundreds of thousands of sources in the wild, the IOC data are produced at a high volume and velocity today, which becomes increasingly hard to manage by humans. Efforts to automatically gather such information from unstructured text, however, is impeded by the limitations of today's Natural Language Processing (NLP) techniques, which cannot meet the high standard (in terms of accuracy and coverage) expected from the IOCs that could serve as direct input to a defense system. In this paper, we present iACE, an innovation solution for fully automated IOC extraction. Our approach is based upon the observation that the IOCs in technical articles are often described in a predictable way: being connected to a set of context terms (e.g., \"download\") through stable grammatical relations. Leveraging this observation, iACE is designed to automatically locate a putative IOC token (e.g., a zip file) and its context (e.g., \"malware\", \"download\") within the sentences in a technical article, and further analyze their relations through a novel application of graph mining techniques. Once the grammatical connection between the tokens is found to be in line with the way that the IOC is commonly presented, these tokens are extracted to generate an OpenIOC item that describes not only the indicator (e.g., a malicious zip file) but also its context (e.g., download from an external source). Running on 71,000 articles collected from 45 leading technical blogs, this new approach demonstrates a remarkable performance: it generated 900K OpenIOC items with a precision of 95% and a coverage over 90%, which is way beyond what the state-of-the-art NLP technique and industry IOC tool can achieve, at a speed of thousands of articles per hour. Further, by correlating the IOCs mined from the articles published over a 13-year span, our study sheds new light on the links across hundreds of seemingly unrelated attack instances, particularly their shared infrastructure resources, as well as the impacts of such open-source threat intelligence on security protection and evolution of attack strategies.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978315"}, {"primary_key": "4066508", "vector": [], "sparse_vector": [], "title": "All Your DNS Records Point to Us: Understanding the Security Threats of Dangling DNS Records.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Haining <PERSON>"], "summary": "In a dangling DNS record (Dare), the resources pointed to by the DNS record are invalid, but the record itself has not yet been purged from DNS. In this paper, we shed light on a largely overlooked threat in DNS posed by dangling DNS records. Our work reveals that Dare can be easily manipulated by adversaries for domain hijacking. In particular, we identify three attack vectors that an adversary can harness to exploit Dares. In a large-scale measurement study, we uncover 467 exploitable Dares in 277 Alexa top 10,000 domains and 52 edu zones, showing that Dare is a real, prevalent threat. By exploiting these Dares, an adversary can take full control of the (sub)domains and can even have them signed with a Certificate Authority (CA). It is evident that the underlying cause of exploitable Dares is the lack of authenticity checking for the resources to which that DNS record points. We then propose three defense mechanisms to effectively mitigate Dares with little human effort.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978387"}, {"primary_key": "4066509", "vector": [], "sparse_vector": [], "title": "MiddlePolice: Toward Enforcing Destination-Defined Policies in the Middle of the Internet.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Volumetric attacks, which overwhelm the bandwidth of a destination, are amongst the most common DDoS attacks today. One practical approach to addressing these attacks is to redirect all destination traffic (e.g., via DNS or BGP) to a third-party, DDoS-protection-as-a-service provider (e.g., CloudFlare) that is well provisioned and equipped with filtering mechanisms to remove attack traffic before passing the remaining benign traffic to the destination. An alternative approach is based on the concept of network capabilities, whereby source sending rates are determined by receiver consent, in the form of capabilities enforced by the network. While both third-party scrubbing services and network capabilities can be effective at reducing unwanted traffic at an overwhelmed destination, DDoS-protection-as-a-service solutions outsource all of the scheduling decisions (e.g., fairness, priority and attack identification) to the provider, while capability-based solutions require extensive modifications to existing infrastructure to operate. In this paper we introduce MiddlePolice, which seeks to marry the deployability of DDoS-protection-as-a-service solutions with the destination-based control of network capability systems. We show that by allowing feedback from the destination to the provider, MiddlePolice can effectively enforce destination-chosen policies, while requiring no deployment from unrelated parties.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978306"}, {"primary_key": "4066510", "vector": [], "sparse_vector": [], "title": "SmartWalk: Enhancing Social Network Security via Adaptive Random Walks.", "authors": ["<PERSON><PERSON>", "Shouling Ji", "<PERSON><PERSON><PERSON>"], "summary": "Random walks form a critical foundation in many social network based security systems and applications. Currently, the design of such social security mechanisms is limited to the classical paradigm of using fixed-length random walks for all nodes on a social graph. However, the fixed-length walk paradigm induces a poor trade-off between security and other desirable properties. In this paper, we propose SmartWalk, a security enhancing system which incorporates adaptive random walks in social network security applications. We utilize a set of supervised machine learning techniques to predict the necessary random walk length based on the structural characteristics of a social graph. Using experiments on multiple real world topologies, we show that the desired walk length starting from a specific node can be well predicted given the local features of the node, and limited knowledge for a small set of training nodes. We describe node-adaptive and path-adaptive random walk usage models, where the walk length adaptively changes based on the starting node and the intermediate nodes on the path, respectively. We experimentally demonstrate the applicability of adaptive random walks on a number of social network based security and privacy systems, including Sybil defenses, anonymous communication and link privacy preserving systems, and show up to two orders of magnitude improvement in performance.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978319"}, {"primary_key": "4066512", "vector": [], "sparse_vector": [], "title": "On Code Execution Tracking via Power Side-Channel.", "authors": ["<PERSON><PERSON> Liu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Xu", "<PERSON><PERSON>"], "summary": "With the proliferation of Internet of Things, there is a growing interest in embedded system attacks, e.g., key extraction attacks and firmware modification attacks. Code execution tracking, as the first step to locate vulnerable instruction pieces for key extraction attacks and to conduct control-flow integrity checking against firmware modification attacks, is therefore of great value. Because embedded systems, especially legacy embedded systems, have limited resources and may not support software or hardware update, it is important to design low-cost code execution tracking methods that require as little system modification as possible. In this work, we propose a non-intrusive code execution tracking solution via power-side channel, wherein we represent the code execution and its power consumption with a revised hidden Markov model and recover the most likely executed instruction sequence with a revised Viterbi algorithm. By observing the power consumption of the microcontroller unit during execution, we are able to recover the program execution flow with a high accuracy and detect abnormal code execution behavior even when only a single instruction is modified.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978299"}, {"primary_key": "4066515", "vector": [], "sparse_vector": [], "title": "Sixth Annual ACM CCS Workshop on Security and Privacy in Smartphones and Mobile Devices (SPSM 2016).", "authors": ["<PERSON>", "<PERSON>"], "summary": "Mobile security and privacy issues are receiving significant attention from the research community. The SPSM workshop was created to provide a venue for researchers and practitioners interested in such issues to get together and exchange ideas. Following the success of the previous editions, we present the sixth edition of the workshop. It brings together the expertise of an international program committee, comprising of 22 mobile security experts from the academia and the industry. The workshop received 31 submissions (regular and short papers combined) from a diverge set of authors located in 19 countries.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2990487"}, {"primary_key": "4066516", "vector": [], "sparse_vector": [], "title": "UniSan: Proactive Kernel Memory Initialization to Eliminate Data Leakages.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Operating system kernel is the de facto trusted computing base for most computer systems. To secure the OS kernel, many security mechanisms, e.g., kASLR and StackGuard, have been increasingly deployed to defend against attacks (e.g., code reuse attack). However, the effectiveness of these protections has been proven to be inadequate-there are many information leak vulnerabilities in the kernel to leak the randomized pointer or canary, thus bypassing kASLR and StackGuard. Other sensitive data in the kernel, such as cryptographic keys and file caches, can also be leaked. According to our study, most kernel information leaks are caused by uninitialized data reads. Unfortunately, existing techniques like memory safety enforcements and dynamic access tracking tools are not adequate or efficient enough to mitigate this threat.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978366"}, {"primary_key": "4066518", "vector": [], "sparse_vector": [], "title": "Making Smart Contracts Smarter.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>as <PERSON>"], "summary": "Cryptocurrencies record transactions in a decentralized data structure called a blockchain. Two of the most popular cryptocurrencies, Bitcoin and Ethereum, support the feature to encode rules or scripts for processing transactions. This feature has evolved to give practical shape to the ideas of smart contracts, or full-fledged programs that are run on blockchains. Recently, Ethereum's smart contract system has seen steady adoption, supporting tens of thousands of contracts, holding millions dollars worth of virtual coins.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978309"}, {"primary_key": "4066519", "vector": [], "sparse_vector": [], "title": "A Secure Sharding Protocol For Open Blockchains.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cryptocurrencies, such as Bitcoin and 250 similar alt-coins, embody at their core a blockchain protocol --- a mechanism for a distributed network of computational nodes to periodically agree on a set of new transactions. Designing a secure blockchain protocol relies on an open challenge in security, that of designing a highly-scalable agreement protocol open to manipulation by byzantine or arbitrarily malicious nodes. Bitcoin's blockchain agreement protocol exhibits security, but does not scale: it processes 3--7 transactions per second at present, irrespective of the available computation capacity at hand.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978389"}, {"primary_key": "4066522", "vector": [], "sparse_vector": [], "title": "POSTER: Re-Thinking Risks and Rewards for Trusted Third Parties.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Commercial trusted third parties (TTPs) may increase their bottom line by watering down their validation procedures because they assume no liability for lapses of judgement. Consumers bear the risk of misplaced trust. Reputation loss is a weak deterrent for TTPs because consumers do not choose them - web shops and browser vendors do. At the same time, consumers are the source of income of these parties. Hence, risks and rewards are not well-aligned. Towards a better alignment, we explore the brokering of connection insurances and transaction insurances, where consumers get to choose their insurer. We lay out the principal idea how such a brokerage might work at a technical level with minimal interference with existing protocols and mechanisms, we analyze the security requirements and we propose techniques to meet these requirements.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989060"}, {"primary_key": "4066525", "vector": [], "sparse_vector": [], "title": "Stemming Downlink Leakage from Training Sequences in Multi-User MIMO Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Multi-User MIMO has attracted much attention due to its significant advantage of increasing the utilization ratio of wireless channels. Recently a serious eavesdropping attack, which exploits the CSI feedback of the FDD system, is discovered in MU-MIMO networks. In this paper, we firstly show a similar eavesdropping attack for the TDD system is also possible by proposing a novel, feasible attack approach. Following it, a malicious user can eavesdrop on other users' downloads by transforming training sequences. To prevent this attack, we propose a secure CSI estimation scheme for instantaneous CSI. Furthermore, we extend this scheme to achieve adaptive security when CSI is relatively statistical. We have implemented our scheme for both uplink and downlink of MU-MIMO and performed a series of experiments. Results show that our secure CSI estimation scheme is highly effective in preventing downlink leakage against malicious users.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978412"}, {"primary_key": "4066528", "vector": [], "sparse_vector": [], "title": "POSTER: Insights of Antivirus Relationships when Detecting Android Malware: A Data Analytics Approach.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This work performs a deep analysis on the behaviour of Anti-Virus (AV) engines regarding Android malware detection. A large dataset, with more than 80K apk files tagged as Malware by one or many AV engines is used in the analysis. With the help of association rule learning, we show interesting patterns and dependencies between different AV engines.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989038"}, {"primary_key": "4066529", "vector": [], "sparse_vector": [], "title": "POSTER: I Don&apos;t Want That Content! On the Risks of Exploiting Bitcoin&apos;s Blockchain as a Content Store.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Bitcoin has revolutionized digital currencies and its underlying blockchain has been successfully applied to other domains. To be verifiable by every participating peer, the blockchain maintains every transaction in a persistent, distributed, and tamper-proof log that every participant needs to replicate locally. While this constitutes the central innovation of blockchain technology and is thus a desired property, it can also be abused in ways that are harmful to the overall system. We show for Bitcoin that blockchains potentially provide multiple ways to store (malicious and illegal) content that, once stored, cannot be removed and is replicated by every participating user. We study the evolution of content storage in Bitcoin's blockchain, classify the stored content, and highlight implications of allowing the storage of arbitrary data in globally replicated blockchains.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989059"}, {"primary_key": "4066530", "vector": [], "sparse_vector": [], "title": "POSTER: Towards Collaboratively Supporting Decision Makers in Choosing Suitable Authentication Schemes.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In spite of the the issues associated with them, text passwords are the predominant means of user authentication today. To foster the adoption of alternative authentication schemes, <PERSON><PERSON> et al. (2014) proposed the ACCESS (Authentication ChoiCE Support System) framework. In prior work, we presented the first implementation of this abstract framework as a decision support system. In this work, we report on the current progress of expanding our prototype implementation into a collaborative authentication research platform. In addition to a decision support system, this platform also includes an interface to systematically access all the information in the knowledge base and collaborative features to facilitate the process of keeping the data for the decision support system current.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989045"}, {"primary_key": "4066536", "vector": [], "sparse_vector": [], "title": "DEMO: OffPAD - Offline Personal Authenticating Device with Applications in Hospitals and e-Banking.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Identity and authentication solutions often lack usability and scalability, or do not provide high enough authentication assurance. The concept of Lucidman (Local User-Centric Identity Management) is an approach to providing scalable, secure and user friendly identity and authentication functionalities. In this context we demonstrate the use of an OffPAD (Offline Personal Authentication Device) as a trusted device to support different forms of authentication. The Lucidman/OffPAD approach consists of locating the identity management and authentication functionalities on the user side instead of on the server side or in the cloud. This demo aims to show how OffPAD strengthens authentication assurance, improves usability, minimizes trust requirements, and has the advantage that trusted online interaction can be achieved even on malware infected client platforms. The trusted device OffPAD has been designed as a phone cover, therefore not requiring the user to carry an extra gadget. We focus on six demonstrators, three useful in e-banking and three in the hospital domain where nurses, doctors, or patients are authenticated and access is granted in various situations base on the OffPAD. A video with the same title is available online at www.offpad.org.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989033"}, {"primary_key": "4066537", "vector": [], "sparse_vector": [], "title": "The <PERSON> Badger of BFT Protocols.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The surprising success of cryptocurrencies has led to a surge of interest in deploying large scale, highly robust, Byzantine fault tolerant (BFT) protocols for mission-critical applications, such as financial transactions. Although the conventional wisdom is to build atop a (weakly) synchronous protocol such as PBFT (or a variation thereof), such protocols rely critically on network timing assumptions, and only guarantee liveness when the network behaves as expected. We argue these protocols are ill-suited for this deployment scenario. We present an alternative, HoneyBadgerBFT, the first practical asynchronous BFT protocol, which guarantees liveness without making any timing assumptions. We base our solution on a novel atomic broadcast protocol that achieves optimal asymptotic efficiency. We present an implementation and experimental results to show our system can achieve throughput of tens of thousands of transactions per second, and scales to over a hundred nodes on a wide area network. We even conduct BFT experiments over Tor, without needing to tune any parameters. Unlike the alternatives, HoneyBadgerBFT simply does not care about the underlying network.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978399"}, {"primary_key": "4066539", "vector": [], "sparse_vector": [], "title": "POSTER: Fingerprinting Tor Hidden Services.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The website fingerprinting attack aims to infer the content of encrypted and anonymized connections by analyzing patterns from the communication such as packet sizes, their order, and direction. Although recent study has shown that no existing fingerprinting method scales in Tor when applied in realistic settings, this does not consider the case of Tor hidden services. In this work, we propose a two-phase fingerprinting approach applied in the scope of Tor hidden services and explore its scalability. We show that the success of the only previously proposed fingerprinting attack against hidden services strongly depends on the Tor version used; i.e., it may be applicable to less than 1.5% of connections to hidden services due to its requirement for control of the first anonymization node. In contrast, in our method, the attacker needs merely to be somewhere on the link between the client and the first anonymization node and the attack can be mounted for any connection to a hidden service.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989054"}, {"primary_key": "4066541", "vector": [], "sparse_vector": [], "title": "SafeConfig&apos;16: Testing and Evaluation for Active and Resilient Cyber Systems.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The premise of this year's SafeConfig Workshop is existing tools and methods for security assessments are necessary but insufficient for scientifically rigorous testing and evaluation of resilient and active cyber systems. The objective for this workshop is the exploration and discussion of scientifically sound testing regimen(s) that will continuously and dynamically probe, attack, and \"test\" the various resilient and active technologies. This adaptation and change in focus necessitates at the very least modification, and potentially, wholesale new developments to ensure that resilient- and agile-aware security testing is available to the research community. All testing, validation and experimentation must also be repeatable, reproducible, subject to scientific scrutiny, measurable and meaningful to both researchers and practitioners.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2990485"}, {"primary_key": "4066544", "vector": [], "sparse_vector": [], "title": "PLAS&apos;16: ACM SIGPLAN 11th Workshop on Programming Languages and Analysis for Security.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "No abstract available.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2990484"}, {"primary_key": "4066547", "vector": [], "sparse_vector": [], "title": "GAME OF DECOYS: Optimal Decoy Routing Through Game Theory.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Decoy routing is a promising new approach for censorship circumvention that relies on traffic re-direction by volunteer autonomous systems. Decoy routing is subject to a fundamental censorship attack, called routing around decoy (RAD), in which the censors re-route their clients' Internet traffic in order to evade decoy routing autonomous systems. Recently, there has been a heated debate in the community on the real-world feasibility of decoy routing in the presence of the RAD attack. Unfortunately, previous studies rely their analysis on heuristic-based mechanisms for decoy placement strategies as well as ad hoc strategies for the implementation of the RAD attack by the censors. In this paper, we perform the first systematic analysis of decoy routing in the presence of the RAD attack. We use game theory to model the interactions between decoy router deployers and the censors in various settings. Our game-theoretic analysis finds the optimal decoy placement strategies---as opposed to heuristic-based placements---in the presence of RAD censors who take their optimal censorship actions---as opposed to some ad hoc implementation of RAD. That is, we investigate the best decoy placement given the best RAD censorship.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978367"}, {"primary_key": "4066555", "vector": [], "sparse_vector": [], "title": "CSPAutoGen: Black-box Enforcement of Content Security Policy upon Real-world Websites.", "authors": ["Xiang Pan", "<PERSON><PERSON> Cao", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Content security policy (CSP) which has been standardized by W3C and adopted by all major commercial browsers-is one of the most promising approaches for defending against cross-site scripting (XSS) attacks. Although client-side adoption of CSP is successful, server-side adoption is far behind the client side: according to a large-scale survey, less than 0.002% of Alexa Top 1M websites enabled CSP. To facilitate the adoption of CSP, we propose CSPAutoGen to enable CSP in real-time, without server modifications, and being compatible with real-world websites. Specifically, CSPAutoGen trains so-called templates for each domain, generates CSPs based on the templates, rewrites incoming webpages on the fly to apply those generated CSPs, and then serves those rewritten webpages to client browsers. CSPAutoGen is designed to automatically enforce the most secure and strict version of CSP without enabling \"unsafe-inline\" and \"unsafe-eval\", i.e., CSPAutoGen can handle all the inline and dynamic scripts.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978384"}, {"primary_key": "4066556", "vector": [], "sparse_vector": [], "title": "Security on Wheels: Security and Privacy for Vehicular Communication Systems.", "authors": ["<PERSON><PERSON>"], "summary": "There is already a significant body of work on security and privacy for vehicular communication systems and the conditions for deploying the technology are maturing. This tutorial provides a crystalized and easily accessible view of the state of the art.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2976752"}, {"primary_key": "4066557", "vector": [], "sparse_vector": [], "title": "PIPSEA: A Practical IPsec Gateway on Embedded APUs.", "authors": ["Jung-<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Accelerated Processing Unit (APU) is a heterogeneous multicore processor that contains general-purpose CPU cores and a GPU in a single chip. It also supports Heterogeneous System Architecture (HSA) that provides coherent physically-shared memory between the CPU and the GPU. In this paper, we present the design and implementation of a high-performance IPsec gateway using a low-cost commodity embedded APU. The HSA supported by the APUs eliminates the data copy overhead between the CPU and the GPU, which is unavoidable in the previous discrete GPU approaches. The gateway is implemented in OpenCL to exploit the GPU and uses zero-copy packet I/O APIs in DPDK. The IPsec gateway handles the real-world network traffic where each packet has a different workload. The proposed packet scheduling algorithm significantly improves GPU utilization for such traffic. It works not only for APUs but also for discrete GPUs. With three CPU cores and one GPU in the APU, the IPsec gateway achieves a throughput of 10.36 Gbps with an average latency of 2.79 ms to perform AES-CBC+HMAC-SHA1 for incoming packets of 1024 bytes.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978329"}, {"primary_key": "4066562", "vector": [], "sparse_vector": [], "title": "The Shadow Nemesis: Inference Attacks on Efficiently Deployable, Efficiently Searchable Encryption.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Encrypting Internet communications has been the subject of renewed focus in recent years. In order to add end-to-end encryption to legacy applications without losing the convenience of full-text search, ShadowCrypt and Mimesis Aegis use a new cryptographic technique called \"efficiently deployable efficiently searchable encryption\" (EDESE) that allows a standard full-text search system to perform searches on encrypted data. Compared to other recent techniques for searching on encrypted data, EDESE schemes leak a great deal of statistical information about the encrypted messages and the keywords they contain. Until now, the practical impact of this leakage has been difficult to quantify.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978401"}, {"primary_key": "4066563", "vector": [], "sparse_vector": [], "title": "Heavy Hitter Estimation over Set-Valued Data with Local Differential Privacy.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In local differential privacy (LDP), each user perturbs her data locally before sending the noisy data to a data collector. The latter then analyzes the data to obtain useful statistics. Unlike the setting of centralized differential privacy, in LDP the data collector never gains access to the exact values of sensitive data, which protects not only the privacy of data contributors but also the collector itself against the risk of potential data leakage. Existing LDP solutions in the literature are mostly limited to the case that each user possesses a tuple of numeric or categorical values, and the data collector computes basic statistics such as counts or mean values. To the best of our knowledge, no existing work tackles more complex data mining tasks such as heavy hitter discovery over set-valued data. In this paper, we present a systematic study of heavy hitter mining under LDP. We first review existing solutions, extend them to the heavy hitter estimation, and explain why their effectiveness is limited. We then propose LDPMiner, a two-phase mechanism for obtaining accurate heavy hitters with LDP. The main idea is to first gather a candidate set of heavy hitters using a portion of the privacy budget, and focus the remaining budget on refining the candidate set in a second phase, which is much more efficient budget-wise than obtaining the heavy hitters directly from the whole dataset. We provide both in-depth theoretical analysis and extensive experiments to compare LDPMiner against adaptations of previous solutions. The results show that LDPMiner significantly improves over existing methods. More importantly, LDPMiner successfully identifies the majority true heavy hitters in practical settings.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978409"}, {"primary_key": "4066566", "vector": [], "sparse_vector": [], "title": "POSTER: Security Enhanced Administrative Role Based Access Control Models.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>"], "summary": "Administrative rights are more powerful permissions and checking accountability of execution of admin rights is an important security measure. Most of the administrative RBAC models distribute rights to multiple administrators. Though such decentralized security management has difficulties in checking admin accountability, it is more efficient compared to centralized approach, particularly in large organizations. We introduced administrative obligations in ARBAC as a way to improve the accountability of admin users in the decentralized systems. The proposed approach would reduce the potential of security risk and improve accountability of security administrators. As the cloud and mobile applications are becoming integral part of business information systems, ensuring the accountability of admins play a vital role in system security. Obligations are well studied feature in the security literature and adding them into security administration would open up many possibilities for future developments in this direction.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989068"}, {"primary_key": "4066570", "vector": [], "sparse_vector": [], "title": "How I Learned to be Secure: a Census-Representative Survey of Security Advice Sources and Behavior.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Few users have a single, authoritative, source from whom they can request digital-security advice. Rather, digital-security skills are often learned haphazardly, as users filter through an overwhelming quantity of security advice. By understanding the factors that contribute to users' advice sources, beliefs, and security behaviors, we can help to pare down the quantity and improve the quality of advice provided to users, streamlining the process of learning key behaviors. This paper rigorously investigates how users' security beliefs, knowledge, and demographics correlate with their sources of security advice, and how all these factors influence security behaviors. Using a carefully pre-tested, U.S.-census-representative survey of 526 users, we present an overview of the prevalence of respondents' advice sources, reasons for accepting and rejecting advice from those sources, and the impact of these sources and demographic factors on security behavior. We find evidence of a \"digital divide\" in security: the advice sources of users with higher skill levels and socioeconomic status differ from those with fewer resources. This digital security divide may add to the vulnerability of already disadvantaged users. Additionally, we confirm and extend results from prior small-sample studies about why users accept certain digital-security advice (e.g., because they trust the source rather than the content) and reject other advice (e.g., because it is inconvenient and because it contains too much marketing material). We conclude with recommendations for combating the digital divide and improving the efficacy of digital-security advice.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978307"}, {"primary_key": "4066572", "vector": [], "sparse_vector": [], "title": "AUDACIOUS: User-Driven Access Control with Unmodified Operating Systems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "User-driven access control improves the coarse-grained access control of current operating systems (particularly in the mobile space) that provide only all-or-nothing access to a resource such as the camera or the current location. By granting appropriate permissions only in response to explicit user actions (for example, pressing a camera button), user-driven access control better aligns application actions with user expectations. Prior work on user-driven access control has relied in essential ways on operating system (OS) modifications to provide applications with uncompromisable access control gadgets, distinguished user interface (UI) elements that can grant access permissions. This work presents a design, implementation, and evaluation of user-driven access control that works with no OS modifications, thus making deployability and incremental adoption of the model more feasible. We develop (1) a user-level trusted library for access control gadgets, (2) static analyses to prevent malicious creation of UI events, illegal flows of sensitive information, and circumvention of our library, and (3) dynamic analyses to ensure users are not tricked into granting permissions. In addition to providing the original user-driven access control guarantees, we use static information flow to limit where results derived from sensitive sources may flow in an application.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978344"}, {"primary_key": "4066574", "vector": [], "sparse_vector": [], "title": "POPE: Partial Order Preserving Encoding.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recently there has been much interest in performing search queries over encrypted data to enable functionality while protecting sensitive data. One particularly efficient mechanism for executing such queries is order-preserving encryption/encoding (OPE) which results in ciphertexts that preserve the relative order of the underlying plaintexts thus allowing range and comparison queries to be performed directly on ciphertexts. In this paper, we propose an alternative approach to range queries over encrypted data that is optimized to support insert-heavy workloads as are common in \"big data\" applications while still maintaining search functionality and achieving stronger security. Specifically, we propose a new primitive called partial order preserving encoding (POPE) that achieves ideal OPE security with frequency hiding and also leaves a sizable fraction of the data pairwise incomparable. Using only O(1) persistent and $O(n^\\epsilon)$ non-persistent client storage for $0<\\epsilon<1$, our POPE scheme provides extremely fast batch insertion consisting of a single round, and efficient search with O(1) amortized cost for up to $O(n^{1-\\epsilon})$ search queries. This improved security and performance makes our scheme better suited for today's insert-heavy databases.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978345"}, {"primary_key": "4066575", "vector": [], "sparse_vector": [], "title": "Build It, Break It, Fix It: Contesting Secure Development.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Typical security contests focus on breaking or mitigating the impact of buggy systems. We present the Build-it, Break-it, Fix-it (BIBIFI) contest, which aims to assess the ability to securely build software, not just break it. In BIBIFI, teams build specified software with the goal of maximizing correctness, performance, and security. The latter is tested when teams attempt to break other teams' submissions. Winners are chosen from among the best builders and the best breakers. BIBIFI was designed to be open-ended-teams can use any language, tool, process, etc. that they like. As such, contest outcomes shed light on factors that correlate with successfully building secure software and breaking insecure software. During 2015, we ran three contests involving a total of 116 teams and two different programming problems. Quantitative analysis from these contests found that the most efficient build-it submissions used C/C++, but submissions coded in other statically-typed languages were less likely to have a security flaw; build-it teams with diverse programming-language knowledge also produced more secure code. Shorter programs correlated with better scores. Break-it teams that were also successful build-it teams were significantly better at finding security bugs.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978382"}, {"primary_key": "4066576", "vector": [], "sparse_vector": [], "title": "POSTER: Identifying Dynamic Data Structures in Malware.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "As the complexity of malware grows, so does the necessity of employing program structuring mechanisms during development. While control flow structuring is often obfuscated, the dynamic data structures employed by the program are typically untouched. We report on work in progress that exploits this weakness to identify dynamic data structures present in malware samples for the purposes of aiding reverse engineering and constructing malware signatures, which may be employed for malware classification. Using a prototype implementation, which combines the type recovery tool Howard and the identification tool Data Structure Investigator (DSI), we analyze data structures in Carberp and AgoBot malware. Identifying their data structures illustrates a challenging problem. To tackle this, we propose a new type recovery for binaries based on machine learning, which uses <PERSON>'s types to guide the search and DSI's memory abstraction for hypothesis evaluation.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989041"}, {"primary_key": "4066578", "vector": [], "sparse_vector": [], "title": "POSTER: Towards Exposing Internet of Things: A Roadmap.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Considering the exponential increase of Internet of Things (IoT) devices there is also unforeseen vulnerabilities associated with these IoT devices. One of the major problems in the IoT is the security testing and analysis due to the heterogeneous nature of deployments. Currently, there is no mechanism that performs security testing for IoT devices in different contexts. In addition, there is a missing framework to be able to adapt and tune accordingly with various security testing perspectives. In this paper, we propose an innovative security testbed targeted at IoT devices and also briefly introduce Adaptable and Tunable Framework (ATF) for testing IoT devices.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989046"}, {"primary_key": "4066579", "vector": [], "sparse_vector": [], "title": "Over-The-Top Bypass: Study of a Recent Tel<PERSON><PERSON><PERSON>.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper, we study the Over-The-Top (OTT) bypass fraud, a recent form of interconnect telecom fraud. In OTT bypass, a normal phone call is diverted over IP to a voice chat application on a smartphone, instead of being terminated over the normal telecom infrastructure. This rerouting (or hijack) is performed by an international transit operator in coordination with the OTT service provider, but without explicit authorization from the caller, callee and their operators. By doing so, they collect a large share of the call charge and induce a significant loss of revenue to the bypassed operators. Moreover, this practice degrades the quality of service without providing any benefits for the users.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978334"}, {"primary_key": "4066585", "vector": [], "sparse_vector": [], "title": "Efficient Cryptographic Password Hardening Services from Partially Oblivious Commitments.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Password authentication still constitutes the most widespread authentication concept on the Internet today, but the human incapability to memorize safe passwords has left this concept vulnerable to various attacks ever since. Affected enterprises such as Facebook now strive to mitigate such attacks by involving external cryptographic services that harden passwords. <PERSON><PERSON><PERSON> et al.~provided the first comprehensive formal treatment of such a service, and proposed the Pythia PRF-Service as a cryptographically secure solution (Usenix Security'15). Pythia relies on a novel cryptographic primitive called partially oblivious pseudorandom functions and its security is proven under a strong new interactive assumption in the random oracle model.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978375"}, {"primary_key": "4066590", "vector": [], "sparse_vector": [], "title": "The Misuse of Android Unix Domain Sockets and Security Implications.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this work, we conduct the first systematic study in understanding the security properties of the usage of Unix domain sockets by both Android apps and system daemons as an IPC (Inter-process Communication) mechanism, especially for cross-layer communications between the Java and native layers. We propose a tool called SInspector to expose potential security vulnerabilities in using Unix domain sockets through the process of identifying socket addresses, detecting authentication checks, and performing data flow analysis. Our in-depth analysis revealed some serious vulnerabilities in popular apps and system daemons, such as root privilege escalation and arbitrary file access. Based on our findings, we propose countermeasures and improved practices for utilizing Unix domain sockets on Android.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978297"}, {"primary_key": "4066593", "vector": [], "sparse_vector": [], "title": "Accessorize to a Crime: Real and Stealthy Attacks on State-of-the-Art Face Recognition.", "authors": ["<PERSON><PERSON><PERSON>", "Sruti Bhagavatula", "<PERSON><PERSON>", "<PERSON>"], "summary": "Machine learning is enabling a myriad innovations, including new algorithms for cancer diagnosis and self-driving cars. The broad use of machine learning makes it important to understand the extent to which machine-learning algorithms are subject to attack, particularly when used in applications where physical security or safety is at risk.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978392"}, {"primary_key": "4066595", "vector": [], "sparse_vector": [], "title": "Safe Serializable Secure Scheduling: Transactions and the Trade-Off Between Security and Consistency.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Modern applications often operate on data in multiple administrative domains. In this federated setting, participants may not fully trust each other. These distributed applications use transactions as a core mechanism for ensuring reliability and consistency with persistent data. However, the coordination mechanisms needed for transactions can both leak confidential information and allow unauthorized influence. By implementing a simple attack, we show these side channels can be exploited. However, our focus is on preventing such attacks. We explore secure scheduling of atomic, serializable transactions in a federated setting. While we prove that no protocol can guarantee security and liveness in all settings, we establish conditions for sets of transactions that can safely complete under secure scheduling. Based on these conditions, we introduce \\ti{staged commit}, a secure scheduling protocol for federated transactions. This protocol avoids insecure information channels by dividing transactions into distinct stages. We implement a compiler that statically checks code to ensure it meets our conditions, and a system that schedules these transactions using the staged commit protocol. Experiments on this implementation demonstrate that realistic federated transactions can be scheduled securely, atomically, and efficiently.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978415"}, {"primary_key": "4066596", "vector": [], "sparse_vector": [], "title": "EpicRec: Towards Practical Differentially Private Framework for Personalized Recommendation.", "authors": ["<PERSON><PERSON>", "Hongxia Jin"], "summary": "Recommender systems typically require users' history data to provide a list of recommendations and such recommendations usually reside on the cloud/server. However, the release of such private data to the cloud has been shown to put users at risk. It is highly desirable to provide users high-quality personalized services while respecting their privacy. In this paper, we develop the first Enhanced Privacy-built-In Client for Personalized Recommendation (EpicRec) system that performs the data perturbation on the client side to protect users' privacy. Our system needs no assumption of trusted server and no change on the recommendation algorithms on the server side; and needs minimum user interaction in their preferred manner, which makes our solution fit very well into real world practical use.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978316"}, {"primary_key": "4066598", "vector": [], "sparse_vector": [], "title": "The Sounds of the Phones: Dangers of Zero-Effort Second Factor Login based on Ambient Audio.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Reducing user burden underlying traditional two-factor authentication constitutes an important research effort. An interesting representative approach, Sound-Proof, leverages ambient sounds to detect the proximity between the second factor device (phone) and the login terminal (browser). Sound-Proof was shown to be secure against remote attackers and highly usable, and is now under early deployment phases. In this paper, we identify a weakness of the Sound-Proof system, namely, the remote attacker does not have to predict the ambient sounds near the phone as assumed in the Sound-Proof paper, but rather can deliberately make-or wait for-the phone to produce predictable or previously known sounds (e.g., ringer, notification or alarm sounds). Exploiting this weakness, we build Sound-Danger, a full attack system that can successfully compromise the security of Sound-Proof. The attack involves buzzing the victim user's phone, or waiting for the phone to buzz, and feeding the corresponding sounds at the browser to login on behalf of the user. The attack works precisely under Sound-Proof's threat model.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978328"}, {"primary_key": "4066599", "vector": [], "sparse_vector": [], "title": "Program Anomaly Detection: Methodology and Practices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Yao"], "summary": "This tutorial will present an overview of program anomaly detection, which analyzes normal program behaviors and discovers aberrant executions caused by attacks, misconfigurations, program bugs, and unusual usage patterns. It was first introduced as an analogy between intrusion detection for programs and the immune mechanism in biology. Advanced models have been developed in the last decade and comprehensive techniques have been adopted such as hidden Markov model and machine learning. We will introduce the audience to the problem of program attacks and the anomaly detection approach against threats. We will give a general definition for program anomaly detection and derive model abstractions from the definition. The audience will be walked through the development of program anomaly detection methods from early-age n-gram approaches to complicated pushdown automata and probabilistic models. Some lab tools will be provided to help understand primitive detection models. This procedure will help the audience understand the objectives and challenges in designing program anomaly detection models. We will discuss the attacks that subvert anomaly detection mechanisms. The field map of program anomaly detection will be presented. We will also briefly discuss the applications of program anomaly detection in Internet of Things security. We expect the audience to get an idea of unsolved challenges in the field and develop a sense of future program anomaly detection directions after attending the tutorial.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2976750"}, {"primary_key": "4066602", "vector": [], "sparse_vector": [], "title": "Using Reflexive Eye Movements for Fast Challenge-Response Authentication.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Eye tracking devices have recently become increasingly popular as an interface between people and consumer-grade electronic devices. Due to the fact that human eyes are fast, responsive, and carry information unique to an individual, analyzing person's gaze is particularly attractive for effortless biometric authentication. Unfortunately, previous proposals for gaze-based authentication systems either suffer from high error rates, or require long authentication times.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978311"}, {"primary_key": "4066604", "vector": [], "sparse_vector": [], "title": "Systematic Fuzzing and Testing of TLS Libraries.", "authors": ["<PERSON><PERSON>"], "summary": "We present TLS-Attacker, an open source framework for evaluating the security of TLS libraries. TLS-Attacker allows security engineers to create custom TLS message flows and arbitrarily modify message contents using a simple interface in order to test the behavior of their libraries. Based on TLS-Attacker, we present a two-stage fuzzing approach to evaluate TLS server behavior. Our approach automatically searches for cryptographic failures and boundary violation vulnerabilities. It allowed us to find unusual padding oracle vulnerabilities and overflows/overreads in widely used TLS libraries, including OpenSSL, Botan, and MatrixSSL.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978411"}, {"primary_key": "4066605", "vector": [], "sparse_vector": [], "title": "My Smartphone Knows What You Print: Exploring Smartphone-based Side-channel Attacks Against 3D Printers.", "authors": ["<PERSON>", "<PERSON>", "Zhongjie Ba", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Additive manufacturing, also known as 3D printing, has been increasingly applied to fabricate highly intellectual property (IP) sensitive products. However, the related IP protection issues in 3D printers are still largely underexplored. On the other hand, smartphones are equipped with rich onboard sensors and have been applied to pervasive mobile surveillance in many applications. These facts raise one critical question: is it possible that smartphones access the side-channel signals of 3D printer and then hack the IP information? To answer this, we perform an end-to-end study on exploring smartphone-based side-channel attacks against 3D printers. Specifically, we formulate the problem of the IP side-channel attack in 3D printing. Then, we investigate the possible acoustic and magnetic side-channel attacks using the smartphone built-in sensors. Moreover, we explore a magnetic-enhanced side-channel attack model to accurately deduce the vital directional operations of 3D printer. Experimental results show that by exploiting the side-channel signals collected by smartphones, we can successfully reconstruct the physical prints and their G-code with Mean Tendency Error of 5.87% on regular designs and 9.67% on complex designs, respectively. Our study demonstrates this new and practical smartphone-based side channel attack on compromising IP information during 3D printing.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978300"}, {"primary_key": "4066609", "vector": [], "sparse_vector": [], "title": "POSTER: VUDEC: A Framework for Vulnerability Management in Decentralized Communication Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Vulnerability management, often used as a generic term for any organizational and technical security controls in the context of identifying, assessing, and mitigating security-relevant software and network weaknesses, has specific challenges in decentralized communication networks such as research and education networks operated by higher education institutions. While many large organizations perform professional vulnerability management and related activities, especially risk management, which are supported by commercial and open source software products, universities and other academic environments still often struggle with ad-hoc and scope-limited approaches due to often unclear responsibilities and a lack of suitable tool support. This poster presents VUDEC, an integrated vulnerability management framework tailored for the requirements of decentrally operated networks; besides organizational aspects of the vulnerability management process, its implementation supports, among other functionality, a highly distributed vulnerability scan architecture and full multi-tenancy capability.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989049"}, {"primary_key": "4066610", "vector": [], "sparse_vector": [], "title": "POSTER: Mapping the Landscape of Large-Scale Vulnerability Notifications.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The Internet is an ever-growing ecosystem with diverse software and hardware applications deployed in numerous countries around the globe. This heterogenous structure, however, is reduced to a homogenous means of addressing servers, i.e., their IP address. Due to this, analyzing different Internet services for vulnerabilities at scale is easy, leading to many researcher focusing on large-scale detection of many types of flaws. On the other hand, the persons responsible for the administration of said services are as heterogenous as the Internet architecture itself: be it in spoken languages or knowledge of technical details of the services. The notification of vulnerable services has long been treated as a side note in research. Recently, the community has focussed more not only the detection of flaws, but also on the notification of affected parties. These works, however, only analyze a small segment of the problem space. Hence, in this paper, we investigate the issues encountered by the previous works and provide a number of future directions for research, ultimately aiming to allow for an easier means of notifying affected parties about vulnerabilities at scale.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989057"}, {"primary_key": "4066611", "vector": [], "sparse_vector": [], "title": "POSTER: DroidShield: Protecting User Applications from Normal World Access.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Smartphones are becoming the main data sharing and storage devices in both our personal and professional lives, as companies now allow employees to share the same device for both purposes, provided the company's confidential information can be protected. However, as history has shown, systems relying on security policies or rules to protect user data are not airtight. Any flaw in the constructed rules or in the code of privileged applications can lead to complete compromise. In addition, we can not rely only on TrustZone[6] world separation to isolate confidential data from unauthorized access, because in addition to severe limitations in terms of both communication and memory space, there is a very low limit on the number of applications that can be installed in the secure world before we can start questioning its security, especially when considering code originating from multiple sources. Thus, the solutions currently available for TrustZone devices are not perfect and the data confidentiality can not be guaranteed. We propose an alternative approach, which involves providing the majority of secure world application advantages to a set of normal world applications, with almost none of the drawbacks by relying only on the TrustZone world separation and the TZ-RKP[2] kernel protection scheme.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989052"}, {"primary_key": "4066612", "vector": [], "sparse_vector": [], "title": "POSTER: Toward Automating the Generation of Malware Analysis Reports Using the Sandbox Logs.", "authors": ["Bo <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In recent years, the number of new examples of malware has continued to increase. To create effective countermeasures, security specialists often must manually inspect vast sandbox logs produced by the dynamic analysis method. Conversely, antivirus vendors usually publish malware analysis reports on their website. Because malware analysis reports and sandbox logs do not have direct connections, when analyzing sandbox logs, security specialists can not benefit from the information described in such expert reports. To address this issue, we developed a system called ReGenerator that automates the generation of reports related to sandbox logs by making use of existing reports published by antivirus vendors. Our system combines several techniques, including the Jaccard similarity, Natural Language Processing (NLP), and Generation (NLG), to produce concise human-readable reports describing malicious behavior for security specialists.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989064"}, {"primary_key": "4066614", "vector": [], "sparse_vector": [], "title": "TaintART: A Practical Multi-level Information-Flow Tracking System for Android RunTime.", "authors": ["Mingshen Sun", "<PERSON>", "<PERSON>"], "summary": "Mobile operating systems like Android failed to provide sufficient protection on personal data, and privacy leakage becomes a major concern. To understand the security risks and privacy leakage, analysts have to carry out data-flow analysis. In 2014, Android upgraded with a fundamentally new design known as Android RunTime (ART) environment in Android 5.0. ART adopts ahead-of-time compilation strategy and replaces previous virtual-machine-based Dalvik. Unfortunately, many data-flow analysis systems like TaintDroid were designed for the legacy Dalvik environment. This makes data-flow analysis of new apps and malware infeasible. We design a multi-level information-flow tracking system for the new Android system called TaintART. TaintART employs a multi-level taint analysis technique to minimize the taint tag storage. Therefore, taint tags can be stored in processor registers to provide efficient taint propagation operations. We also customize the ART compiler to maximize performance gains of the ahead-of-time compilation optimizations. Based on the general design of TaintART, we also implement a multi-level privacy enforcement to prevent sensitive data leakage. We demonstrate that TaintART only incurs less than 15% overheads on a CPU-bound microbenchmark and negligible overhead on built-in or third-party applications. Compared to legacy Dalvik environment in Android 4.4, TaintART achieves about 99.7% faster performance for Java runtime benchmark.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978343"}, {"primary_key": "4066622", "vector": [], "sparse_vector": [], "title": "DEMO: Starving Permission-Hungry Android Apps Using SecuRank.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We demonstrate SecuRank, a tool that can be employed by Android smartphone users to replace their currently installed apps with functionally-similar ones that require less sensitive access to their device. SecuRank works by using text mining on the app store description of apps to perform groupings by functionality. Once groups of functionally-similar apps are found, SecuRank uses contextual permission usage within groups to identify those apps that are less permission-hungry. Our demonstration will showcase both the Android app version of SecuRank and the web-based version. Participants will see the effectiveness of SecuRank as a tool for finding and replacing apps with less permission-hungry alternatives.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989032"}, {"primary_key": "4066626", "vector": [], "sparse_vector": [], "title": "ProvUSB: Block-level Provenance-Based Data Protection for USB Storage Devices.", "authors": ["<PERSON> (Jing) <PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Defenders of enterprise networks have a critical need to quickly identify the root causes of malware and data leakage. Increasingly, USB storage devices are the media of choice for data exfiltration, malware propagation, and even cyber-warfare. We observe that a critical aspect of explaining and preventing such attacks is understanding the provenance of data (i.e., the lineage of data from its creation to current state) on USB devices as a means of ensuring their safe usage. Unfortunately, provenance tracking is not offered by even sophisticated modern devices. This work presents ProvUSB, an architecture for fine-grained provenance collection and tracking on smart USB devices. ProvUSB maintains data provenance by recording reads and writes at the block layer and reliably identifying hosts editing those blocks through attestation over the USB channel. Our evaluation finds that ProvUSB imposes a one-time 850 ms overhead during USB enumeration, but approaches nearly-bare-metal runtime performance (90% of throughput) on larger files during normal execution, and less than 0.1% storage overhead for provenance in real-world workloads. ProvUSB thus provides essential new techniques in the defense of computer systems and USB storage devices.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978398"}, {"primary_key": "4066630", "vector": [], "sparse_vector": [], "title": "New Security Threats Caused by IMS-based SMS Service in 4G LTE Networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "SMS (Short Messaging Service) is a text messaging service for mobile users to exchange short text messages. It is also widely used to provide SMS-powered services (e.g., mobile banking). With the rapid deployment of all-IP 4G mobile networks, the underlying technology of SMS evolves from the legacy circuit-switched network to the IMS (IP Multimedia Subsystem) system over packet-switched network. In this work, we study the insecurity of the IMS-based SMS. We uncover its security vulnerabilities and exploit them to devise four SMS attacks: silent SMS abuse, SMS spoofing, SMS client DoS, and SMS spamming. We further discover that those SMS threats can propagate towards SMS-powered services, thereby leading to three malicious attacks: social network account hijacking, unauthorized donation, and unauthorized subscription. Our analysis reveals that the problems stem from the loose security regulations among mobile phones, carrier networks, and SMS-powered services. We finally propose remedies to the identified security issues.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978393"}, {"primary_key": "4066631", "vector": [], "sparse_vector": [], "title": "Draco: A System for Uniform and Fine-grained Access Control for Web Code on Android.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In-app embedded browsers are commonly used by app developers to display web content without having to redirect the user to heavy-weight web browsers. Just like the conventional web browsers, embedded browsers can allow the execution of web code. In addition, they provide mechanisms (viz., JavaScript bridges) to give web code access to internal app code that might implement critical functionalities and expose device resources. This is intrinsically dangerous since there is currently no means for app developers to perform origin-based access control on the JavaScript bridges, and any web code running in an embedded browser is free to use all the exposed app and device resources. Previous work that addresses this problem provided access control solutions that work only for apps that are built using hybrid frameworks. Additionally, these solutions focused on protecting only the parts of JavaScript bridges that expose permissions-protected resources. In this work, our goal is to provide a generic solution that works for all apps that utilize embedded web browsers and protects all channels that give access to internal app and device resources. Towards realizing this goal, we built Draco, a uniform and fine-grained access control framework for web code running on Android embedded browsers (viz., WebView). Draco provides a declarative policy language that allows developers to define policies to specify the desired access characteristics of web origins in a fine-grained fashion, and a runtime system that dynamically enforces the policies. In contrast with previous work, we do not assume any modifications to the Android operating system, and implement <PERSON><PERSON> in the Chromium Android System WebView app to enable seamless deployment. Our evaluation of the the <PERSON><PERSON> runtime system shows that Draco incurs negligible overhead, which is in the order of microseconds.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978322"}, {"primary_key": "4066632", "vector": [], "sparse_vector": [], "title": "POSTER: Improved Markov Strength Meters for Passwords.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Markov-based strength meters provide more accurate estimate of password strength as opposed to rule-based strength meters. However, we observed that these meters assign very high scores to slightly altered weak passwords. It is important to score modified variants of weak passwords more conservatively as the existing password cracking tools generate such guesses much more quickly. In this paper, we propose a simple greedy algorithm to detect small alterations and improve the scoring mechanism of Markov strength meters.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989058"}, {"primary_key": "4066633", "vector": [], "sparse_vector": [], "title": "DEMO: Easy Deployment of a Secure Internet Architecture for the 21st Century: How hard can it be to build a secure Internet?", "authors": ["Ercan Ucan", "<PERSON>", "<PERSON>"], "summary": "We propose a demonstration of SCION, a future Internet Architecture designed for the 21st century. We demonstrate SCION's various rich features (including DDoS defense, native multipath communication, high-speed anonymous routing) and its ease of deployment.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989036"}, {"primary_key": "4066635", "vector": [], "sparse_vector": [], "title": "Limiting the Impact of Stealthy Attacks on Industrial Control Systems.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Alvaro <PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "While attacks on information systems have for most practical purposes binary outcomes (information was manipulated/eavesdropped, or not), attacks manipulating the sensor or control signals of Industrial Control Systems (ICS) can be tuned by the attacker to cause a continuous spectrum in damages. Attackers that want to remain undetected can attempt to hide their manipulation of the system by following closely the expected behavior of the system, while injecting just enough false information at each time step to achieve their goals. In this work, we study if attack-detection can limit the impact of such stealthy attacks. We start with a comprehensive review of related work on attack detection schemes in the security and control systems community. We then show that many of those works use detection schemes that are not limiting the impact of stealthy attacks. We propose a new metric to measure the impact of stealthy attacks and how they relate to our selection on an upper bound on false alarms. We finally show that the impact of such attacks can be mitigated in several cases by the proper combination and configuration of detection schemes. We demonstrate the effectiveness of our algorithms through simulations and experiments using real ICS testbeds and real ICS systems.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978388"}, {"primary_key": "4066636", "vector": [], "sparse_vector": [], "title": "POSTER: Static ROP Chain Detection Based on Hidden Markov Model Considering ROP Chain Integrity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Return-oriented programming (ROP) has been crucial for attackers to evade the security mechanisms of operating systems. It is currently used in malicious documents that exploit viewer applications and cause malware infection. For inspecting a large number of commonly handled documents, high-performance and flexible-detection methods are required. However, current solutions are either time-consuming or less precise. In this paper, we propose a novel method for statically detecting ROP chains in malicious documents. Our method generates a hidden Markov model (HMM) of ROP chains as well as one of benign documents by learning known malicious and benign documents and libraries used for ROP gadgets. Detection is performed by calculating the likelihood ratio between malicious and benign HMMs. In addition, we reduce the number of false positives by ROP chain integrity checking, which confirms whether ROP gadgets link properly if they are executed. Experimental results showed that our method can detect ROP-based malicious documents with no false negatives and few false positives at high throughput.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989040"}, {"primary_key": "4066644", "vector": [], "sparse_vector": [], "title": "Drammer: Deterministic Rowhammer Attacks on Mobile Platforms.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Clé<PERSON><PERSON> Maurice", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent work shows that the Rowhammer hardware bug can be used to craft powerful attacks and completely subvert a system. However, existing efforts either describe probabilistic (and thus unreliable) attacks or rely on special (and often unavailable) memory management features to place victim objects in vulnerable physical memory locations. Moreover, prior work only targets x86 and researchers have openly wondered whether Rowhammer attacks on other architectures, such as ARM, are even possible. We show that deterministic Rowhammer attacks are feasible on commodity mobile platforms and that they cannot be mitigated by current defenses. Rather than assuming special memory management features, our attack, DRAMMER, solely relies on the predictable memory reuse patterns of standard physical memory allocators. We implement DRAMMER on Android/ARM, demonstrating the practicability of our attack, but also discuss a generalization of our approach to other Linux-based platforms. Furthermore, we show that traditional x86-based Rowhammer exploitation techniques no longer work on mobile platforms and address the resulting challenges towards practical mobile Rowhammer attacks.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978406"}, {"primary_key": "4066647", "vector": [], "sparse_vector": [], "title": "15th Workshop on Privacy in the Electronic Society (WPES 2016).", "authors": ["Sabrina De Capitani di Vimercati"], "summary": "The advancements in the Information and Communication Technologies (ICTs) have introduced new computing paradigms (e.g., cloud computing, pervasive and ubiquitous computing, ambient intelligence and aware-computing) where the techniques for processing, storing, communicating, sharing, and disseminating information have radically changed. These novel computing paradigms bring enormous benefits: the availability of a universal access to data; the reduction in power, storage, hardware, and software costs; and the availability of elastic storage and computation services. While these advantages are appealing, as a side effect there is a tremendous risk of exposure of confidential or sensitive information to privacy breaches. WPES is a yearly forum, this year at its 15th edition, aiming at discussing the open privacy challenges, emerging directions, and original novel approaches for guaranteeing privacy in today's global interconnected society.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2990491"}, {"primary_key": "4066649", "vector": [], "sparse_vector": [], "title": "DEMO: Integrating MPC in Big Data Workflows.", "authors": ["<PERSON><PERSON>", "Mal<PERSON>", "<PERSON>", "Mayank Varia", "<PERSON><PERSON>"], "summary": "Secure multi-party computation (MPC) allows multiple parties to perform a joint computation without disclosing their private inputs. Many real-world joint computation use cases, however, involve data analyses on very large data sets, and are implemented by software engineers who lack MPC knowledge. Moreover, the collaborating parties -- e.g., several companies -- often deploy different data analytics stacks internally. These restrictions hamper the real-world usability of MPC. To address these challenges, we combine existing MPC frameworks with data-parallel analytics frameworks by extending the Musketeer big data workflow manager [4]. Musketeer automatically generates code for both the sensitive parts of a workflow, which are executed in MPC, and the remainder of the computation, which runs on scalable, widely-deployed analytics systems. In a prototype use case, we compute the Herfindahl-Hirschman Index (HHI), an index of market concentration used in antitrust regulation, on an aggregate 156GB of taxi trip data over five transportation companies. Our implementation computes the HHI in about 20 minutes using a combination of Hadoop and VIFF [1], while even \"mixed mode\" MPC with VIFF alone would have taken many hours. Finally, we discuss future research questions that we seek to address using our approach.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989034"}, {"primary_key": "4066652", "vector": [], "sparse_vector": [], "title": "On the Security and Usability of Segment-based Visual Cryptographic Authentication Protocols.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Visual cryptography has been applied to design human computable authentication protocols. In such a protocol, the user and the server share a secret key in the form of an image printed on a transparent medium, which the user superimposes on server-generated image challenges, and visually decodes a response code from the image. An example of such protocols is PassWindow, an award-winning commercial product. We study the security and usability of segment-based visual cryptographic authentication protocols (SVAPs), which include PassWindow as a special case. In SVAP, the images consist of segments and are thus structured. Our overall findings are negative. We introduce two attacks that together are able to break all SVAPs we considered in the paper. Furthermore, our attacks exploit fundamental weaknesses of SVAPs that appear difficult to fix. We have also evaluated the usability of different SVAPs, and found that the protocol that offers the best security has the poorest usability.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978417"}, {"primary_key": "4066656", "vector": [], "sparse_vector": [], "title": "POSTER: RIA: an Audition-based Method to Protect the Runtime Integrity of MapReduce Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Public cloud vendors have been offering varies big data computing services. However, runtime integrity is one of the major concerns that hinders the adoption of those services. In this paper, we focus on MapReduce, a popular big data computing framework, propose the runtime integrity audition (RIA), a solution to verify the runtime integrity of MapReduce applications. Based on the idea of RIA, we developed a prototype system, called MR Auditor, and tested its applicability and the performance with multiple Hadoop applications. Our experimental results showed that MR Auditor is an efficient tool to detect runtime integrity violation and incurs a moderate performance overhead.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989042"}, {"primary_key": "4066659", "vector": [], "sparse_vector": [], "title": "Call Me Back!: Attacks on System Server and System Apps in Android through Synchronous Callback.", "authors": ["<PERSON>", "<PERSON><PERSON> Zhang", "<PERSON><PERSON>"], "summary": "Android is the most commonly used mobile device operation system. The core of Android, the System Server (SS), is a multi-threaded process that provides most of the system services. Based on a new understanding of the security risks introduced by the callback mechanism in system services, we have discovered a general type of design flaw. A vulnerability detection tool has been designed and implemented based on static taint analysis. We applied the tool on all the 80 system services in the SS of Android 5.1.0. With its help, we have discovered six previously unknown vulnerabilities, which are further confirmed on Android 2.3.7-6.0.1. According to our analysis, about 97.3% of the entire 1.4 billion real-world Android devices are vulnerable. Our proof-of-concept attack proves that the vulnerabilities can enable a malicious app to freeze critical system functionalities or soft-reboot the system immediately. It is a neat type of denial-of-service at-tack. We also proved that the attacks can be conducted at mission critical moments to achieve meaningful goals, such as anti anti-virus, anti process-killer, hindering app updates or system patching. After being informed, Google confirmed our findings promptly. Several suggestions on how to use callbacks safely are also proposed to Google.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978342"}, {"primary_key": "4066660", "vector": [], "sparse_vector": [], "title": "Targeted Online Password Guessing: An Underestimated Threat.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While trawling online/offline password guessing has been intensively studied, only a few studies have examined targeted online guessing, where an attacker guesses a specific victim's password for a service, by exploiting the victim's personal information such as one sister password leaked from her another account and some personally identifiable information (PII). A key challenge for targeted online guessing is to choose the most effective password candidates, while the number of guess attempts allowed by a server's lockout or throttling mechanisms is typically very small. We propose TarGuess, a framework that systematically characterizes typical targeted guessing scenarios with seven sound mathematical models, each of which is based on varied kinds of data available to an attacker. These models allow us to design novel and efficient guessing algorithms. Extensive experiments on 10 large real-world password datasets show the effectiveness of TarGuess. Particularly, TarGuess I~IV capture the four most representative scenarios and within 100 guesses: (1) TarGuess-I outperforms its foremost counterpart by 142% against security-savvy users and by 46% against normal users; (2) TarGuess-II outperforms its foremost counterpart by 169% on security-savvy users and by 72% against normal users; and (3) Both TarGuess-III and IV gain success rates over 73% against normal users and over 32% against security-savvy users. TarGuess-III and IV, for the first time, address the issue of cross-site online guessing when given the victim's one sister password and some PII.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978339"}, {"primary_key": "4066661", "vector": [], "sparse_vector": [], "title": "CSP Is Dead, Long Live CSP! On the Insecurity of Whitelists and the Future of Content Security Policy.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Content Security Policy is a web platform mechanism designed to mitigate cross-site scripting (XSS), the top security vulnerability in modern web applications. In this paper, we take a closer look at the practical benefits of adopting CSP and identify significant flaws in real-world deployments that result in bypasses in 94.72% of all distinct policies. We base our Internet-wide analysis on a search engine corpus of approximately 100 billion pages from over 1 billion hostnames; the result covers CSP deployments on 1,680,867 hosts with 26,011 unique CSP policies -- the most comprehensive study to date. We introduce the security-relevant aspects of the CSP specification and provide an in-depth analysis of its threat model, focusing on XSS protections. We identify three common classes of CSP bypasses and explain how they subvert the security of a policy. We then turn to a quantitative analysis of policies deployed on the Internet in order to understand their security benefits. We observe that 14 out of the 15 domains most commonly whitelisted for loading scripts contain unsafe endpoints; as a consequence, 75.81% of distinct policies use script whitelists that allow attackers to bypass CSP. In total, we find that 94.68% of policies that attempt to limit script execution are ineffective, and that 99.34% of hosts with CSP use policies that offer no benefit against XSS. Finally, we propose the \"strict-dynamic\" keyword, an addition to the specification that facilitates the creation of policies based on cryptographic nonces, without relying on domain whitelists. We discuss our experience deploying such a nonce-based policy in a complex application and provide guidance to web authors for improving their policies.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978363"}, {"primary_key": "4066662", "vector": [], "sparse_vector": [], "title": "POSTER: An Educational Network Protocol for Covert Channel Analysis Using Patterns.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON> Mazurczyk"], "summary": "The utilization of information hiding is on the rise among cybercriminals, e.g. to cloak the communication of malicious software as well as by ordinary users for privacy-enhancing purposes. A recent trend is to use network traffic in form of covert channels to convey secrets. In result, security expert training is incomplete if these aspects are not covered. This paper fills this gap by providing a method for teaching covert channel analysis of network protocols. We define a sample protocol called Covert Channel Educational Analysis Protocol (CCEAP) that can be used in didactic environments. Compared to previous works we lower the barrier for understanding network covert channels by eliminating the requirement for students to understand several network protocols in advance and by focusing on so-called hiding patterns.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989037"}, {"primary_key": "4066664", "vector": [], "sparse_vector": [], "title": "MEMS Gyroscopes as Physical Unclonable Functions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A key requirement for most security solutions is to provide secure cryptographic key storage in a way that will easily scale in the age of the Internet of Things. In this paper, we focus on providing such a solution based on Physical Unclonable Functions (PUFs). To this end, we focus on microelectromechanical systems (MEMS)-based gyroscopes and show via wafer-level measurements and simulations, that it is feasible to use the physical and electrical properties of these sensors for cryptographic key generation. After identifying the most promising features, we propose a novel quantization scheme to extract bit strings from the MEMS analog measurements. We provide upper and lower bounds for the minimum entropy of the derived bit strings and fully analyze the intra- and inter-class distributions across the operation range of the MEMS device. We complement these measurements via Monte-Carlo simulations based on the distributions of the parameters measured on actual devices. We also propose and evaluate a complete cryptographic key generation chain based on fuzzy extractors. We derive a full entropy 128-bit key using the obtained min-entropy estimates, requiring 1219 bits of helper data with an (authentication) failure probability of 4 . 10-7. In addition, we propose a dedicated MEMS-PUF design, which is superior to our measured sensor, in terms of chip area, quality and quantity of key seed features.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978295"}, {"primary_key": "4066666", "vector": [], "sparse_vector": [], "title": "Twice the Bits, Twice the Trouble: Vulnerabilities Induced by Migrating to 64-Bit Platforms.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Subtle flaws in integer computations are a prime source for exploitable vulnerabilities in system code. Unfortunately, even code shown to be secure on one platform can be vulnerable on another, making the migration of code a notable security challenge. In this paper, we provide the first study on how code that works as expected on 32-bit platforms can become vulnerable on 64-bit platforms. To this end, we systematically review the effects of data model changes between platforms. We find that the larger width of integer types and the increased amount of addressable memory introduce previously non-existent vulnerabilities that often lie dormant in program code. We empirically evaluate the prevalence of these flaws on the source code of Debian stable (\"Jessie\") and 200 popular open-source projects hosted on GitHub. Moreover, we discuss 64-bit migration vulnerabilities that have been discovered as part of our study, including vulnerabilities in Chromium, the Boost C++ Libraries, libarchive, the Linux Kernel, and zlib.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978403"}, {"primary_key": "4066670", "vector": [], "sparse_vector": [], "title": "2nd International Workshop on Software Protection: SPRO 2016.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Software Protection techniques aim to defend the confidentiality and integrity of software applications that are exposed to an adversary that shares the execution host and access privileges of the application. This scenario is often denoted as protection against MATE (Man-At-The-End) attacks. This is an area of growing importance. For industry, in many cases the deployment of such techniques is crucial to ensure business continuity. Following the first SPRO workshop co-located with ICSE 2015 in Florence, Italy, this second edition aims to establish a tradition where academics and industrial experts in software protection can meet to confront the challenges in designing stronger protections and in developing better support to deploy those protections and to make them compatible with industrial software development life cycle requirements.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2990486"}, {"primary_key": "4066671", "vector": [], "sparse_vector": [], "title": "Instant and Robust Authentication and Key Agreement among Mobile Devices.", "authors": ["<PERSON>", "<PERSON>", "Jinsong Han", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Device-to-device communication is important to emerging mobile applications such as Internet of Things and mobile social networks. Authentication and key agreement among multiple legitimate devices is the important first step to build a secure communication channel. Existing solutions put the devices into physical proximity and use the common radio environment as a proof of identities and the common secret to agree on a same key. However they experience very slow secret bit generation rate and high errors, requiring several minutes to build a 256-bit key. In this work, we design and implement an authentication and key agreement protocol for mobile devices, called The Dancing Signals (TDS), being extremely fast and error-free. TDS uses channel state information (CSI) as the common secret among legitimate devices. It guarantees that only devices in a close physical proximity can agree on a key and any device outside a certain distance gets nothing about the key. Compared with existing solutions, TDS is very fast and robust, supports group key agreement, and can effectively defend against predictable channel attacks. We implement TDS using commodity off-the-shelf 802.11n devices and evaluate its performance via extensive experiments. Results show that TDS only takes a couple of seconds to make devices agree on a 256-bit secret key with high entropy.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978298"}, {"primary_key": "4066676", "vector": [], "sparse_vector": [], "title": "CREDAL: Towards Locating a Memory Corruption Vulnerability with Your Core Dump.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON> Mu", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "After a program has crashed and terminated abnormally, it typically leaves behind a snapshot of its crashing state in the form of a core dump. While a core dump carries a large amount of information, which has long been used for software debugging, it barely serves as informative debugging aids in locating software faults, particularly memory corruption vulnerabilities. A memory corruption vulnerability is a special type of software faults that an attacker can exploit to manipulate the content at a certain memory. As such, a core dump may contain a certain amount of corrupted data, which increases the difficulty in identifying useful debugging information (e.g. , a crash point and stack traces). Without a proper mechanism to deal with this problem, a core dump can be practically useless for software failure diagnosis. In this work, we develop CREDAL, an automatic tool that employs the source code of a crashing program to enhance core dump analysis and turns a core dump to an informative aid in tracking down memory corruption vulnerabilities. Specifically, CREDAL systematically analyzes a core dump potentially corrupted and identifies the crash point and stack frames. For a core dump carrying corrupted data, it goes beyond the crash point and stack trace. In particular, CREDAL further pinpoints the variables holding corrupted data using the source code of the crashing program along with the stack frames. To assist software developers (or security analysts) in tracking down a memory corruption vulnerability, CREDAL also performs analysis and highlights the code fragments corresponding to data corruption.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978340"}, {"primary_key": "4066677", "vector": [], "sparse_vector": [], "title": "High Fidelity Data Reduction for Big Data Security Dependency Analyses.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Kangkook Jee", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Xu", "Haining <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Intrusive multi-step attacks, such as Advanced Persistent Threat (APT) attacks, have plagued enterprises with significant financial losses and are the top reason for enterprises to increase their security budgets. Since these attacks are sophisticated and stealthy, they can remain undetected for years if individual steps are buried in background \"noise.\" Thus, enterprises are seeking solutions to \"connect the suspicious dots\" across multiple activities. This requires ubiquitous system auditing for long periods of time, which in turn causes overwhelmingly large amount of system audit events. Given a limited system budget, how to efficiently handle ever-increasing system audit logs is a great challenge. This paper proposes a new approach that exploits the dependency among system events to reduce the number of log entries while still supporting high-quality forensic analysis. In particular, we first propose an aggregation algorithm that preserves the dependency of events during data reduction to ensure the high quality of forensic analysis. Then we propose an aggressive reduction algorithm and exploit domain knowledge for further data reduction. To validate the efficacy of our proposed approach, we conduct a comprehensive evaluation on real-world auditing systems using log traces of more than one month. Our evaluation results demonstrate that our approach can significantly reduce the size of system logs and improve the efficiency of forensic analysis without losing accuracy.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978378"}, {"primary_key": "4066681", "vector": [], "sparse_vector": [], "title": "An Empirical Study of Mnemonic Sentence-based Password Generation Strategies.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Mnemonic strategy has been recommended to help users generate secure and memorable passwords. We evaluated the security of $6$ mnemonic strategy variants in a series of online studies involving $5,484$ participants. In addition to applying the standard method of using guess numbers or similar metrics to compare the generated passwords, we also measured the frequencies of the most commonly chosen sentences as well as the resulting passwords. While metrics similar to guess numbers suggested that all variants provided highly secure passwords, statistical metrics told a different story. In particular, differences in the exact instructions had a tremendous impact on the security level of the resulting passwords. We examined the mental workload and memorability of 2 mnemonic strategy variants in another online study with $752$ participants. Although perceived workloads for the mnemonic strategy variants were higher than that for the control group where no strategy is required, no significant reduction in password recall after $1$ week was obtained.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978346"}, {"primary_key": "4066684", "vector": [], "sparse_vector": [], "title": "POSTER: Attack on Non-Linear Physical Unclonable Function.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Physical Unclonable Function (PUF) is a promising hardware security primitive with broad application prospect. However, the strong PUF with numerous Challenge and Response Pairs (CRPs), e.g. the arbiter PUF, is vulnerable to modeling attacks. There are two major kinds of countermeasures. One is restricting CRP access interface, such as controlled PUF and XOR arbiter PUF, which unfortunately has been broken with the help of side-channels. The other is using non-linear electronic characteristics to produce CRPs, such as the current mirror PUF and the voltage transfer PUF. They are only proved to be resistant to SVM based attack, while no more analysis is further explored so far. In this paper, we propose an attack method based on compound heuristic algorithms of evolution strategy, simulated annealing, and ant colony to efficiently attack these two non-linear PUFs. This paper reveals that current mirror and voltage transfer are still not able to help strong PUF resist attacks. Our experimental results show that the average CRP prediction accuracy is as high as 99%.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989039"}, {"primary_key": "4066686", "vector": [], "sparse_vector": [], "title": "MIST 2016: 8th International Workshop on Managing Insider Security Threats.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, a brief introduction is presented on the 8th International Workshop on Managing Insider Security Threats (MIST 2016). MIST 2016 takes place in conjunction with the 23rd ACM Conference on Computer and Communications Security (ACM CCS 2016). Its main goal is to provide a forum for sharing the most recent challenges and advanced technologies in managing insider security threats.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2990482"}, {"primary_key": "4066687", "vector": [], "sparse_vector": [], "title": "POSTER: Efficient Cross-User Chunk-Level Client-Side Data Deduplication with Symmetrically Encrypted Two-Party Interactions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Data deduplication has been widely used in cloud storage to reduce the amount of storage space and save bandwidth. Unfortunately, as an increasing number of sensitive data are stored remotely, the encryption, the simplest way for data privacy, is not compatible with data deduplication. Here, we propose an encrypted deduplication scheme, XDedup, based on <PERSON><PERSON><PERSON> puzzle. To the best of our knowledge, XDedup is the first brute-force resilient encrypted deduplication with only symmetrically cryptographic two-party interactions. XDedup also achieves perfect deduplication.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2989047"}, {"primary_key": "4066689", "vector": [], "sparse_vector": [], "title": "Town Crier: An Authenticated Data Feed for Smart Contracts.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Smart contracts are programs that execute autonomously on blockchains. Their key envisioned uses (e.g. financial instruments) require them to consume data from outside the blockchain (e.g. stock quotes). Trustworthy data feeds that support a broad range of data requests will thus be critical to smart contract ecosystems.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978326"}, {"primary_key": "4066693", "vector": [], "sparse_vector": [], "title": "Android ION Hazard: the Curse of Customizable Memory Management System.", "authors": ["<PERSON>", "<PERSON><PERSON> She", "<PERSON><PERSON><PERSON>"], "summary": "ION is a unified memory management interface for Android that is widely used on virtually all ARM based Android devices. ION attempts to achieve several ambitious goals that have not been simultaneously achieved before (not even on Linux). Different from managing regular memory in the system, ION is designed to share and manage memory with special constraints, e.g., physically contiguous memory. Despite the great flexibility and performance benefits offered, such a critical subsystem, as we discover, unfortunately has flawed security assumptions and designs. In this paper, we systematically analyze ION related vulnerabilities from conceptual root causes to detailed implementation decisions. Since ION is often customized heavily for different Android devices, the specific vulnerabilities often manifest themselves differently. By conducting a range of runtime testing as well as static analysis, we are able to uncover a large number of serious vulnerabilities on the latest Android devices (e.g., Nexus 6P running Android 6.0 and 7.0 preview) such as denial-of-service and dumping memory from the system and arbitrary applications (e.g., email content, passwords). Finally, we offer suggestions on how to redesign the ION subsystem to eliminate these flaws. We believe that the lessons learned can help guide the future design of similar memory management subsystems.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978320"}, {"primary_key": "4066694", "vector": [], "sparse_vector": [], "title": "VoiceLive: A Phoneme Localization based Liveness Detection for Voice Authentication on Smartphones.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Voice authentication is drawing increasing attention and becomes an attractive alternative to passwords for mobile authentication. Recent advances in mobile technology further accelerate the adoption of voice biometrics in an array of diverse mobile applications. However, recent studies show that voice authentication is vulnerable to replay attacks, where an adversary can spoof a voice authentication system using a pre-recorded voice sample collected from the victim. In this paper, we propose VoiceLive, a practical liveness detection system for voice authentication on smartphones. VoiceLive detects a live user by leveraging the user's unique vocal system and the stereo recording of smartphones. In particular, with the phone closely placed to a user's mouth, it captures time-difference-of-arrival (TDoA) changes in a sequence of phoneme sounds to the two microphones of the phone, and uses such unique TDoA dynamic which doesn't exist under replay attacks for liveness detection. VoiceLive is practical as it doesn't require additional hardware but two-channel stereo recording that is supported by virtually all smartphones. Our experimental evaluation with 12 participants and different types of phones shows that VoiceLive achieves over 99% detection accuracy at around 1% Equal Error Rate (EER). Results also show that VoiceLive is robust to different phone placements and is compatible to different sampling rates and phone models.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978296"}, {"primary_key": "4066695", "vector": [], "sparse_vector": [], "title": "Return-Oriented Flush-Reload Side Channels on ARM and Their Implications for Android Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cache side-channel attacks have been extensively studied on x86 architectures, but much less so on ARM processors. The technical challenges to conduct side-channel attacks on ARM, presumably, stem from the poorly documented ARM cache implementations, such as cache coherence protocols and cache flush operations, and also the lack of understanding of how different cache implementations will affect side-channel attacks. This paper presents a systematic exploration of vectors for flush-reload attacks on ARM processors. flush-reload attacks are among the most well-known cache side-channel attacks on x86. It has been shown in previous work that they are capable of exfiltrating sensitive information with high fidelity. We demonstrate in this work a novel construction of flush-reload side channels on last-level caches of ARM processors, which, particularly, exploits return-oriented programming techniques to reload instructions. We also demonstrate several attacks on Android OS (e.g., detecting hardware events and tracing software execution paths) to highlight the implications of such attacks for Android devices.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978360"}, {"primary_key": "4066696", "vector": [], "sparse_vector": [], "title": "Practical Anonymous Password Authentication and TLS with Anonymous Client Authentication.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Anonymous authentication allows one to authenticate herself without revealing her identity, and becomes an important technique for constructing privacy-preserving Internet connections. Anonymous password authentication is highly desirable as it enables a client to authenticate herself by a human-memorable password while preserving her privacy. In this paper, we introduce a novel approach for designing anonymous password-authenticated key exchange (APAKE) protocols using algebraic message authentication codes (MACs), where an algebraic MAC wrapped by a password is used by a client for anonymous authentication, and a server issues algebraic MACs to clients and acts as the verifier of login protocols. Our APAKE construction is secure provided that the algebraic MAC is strongly existentially unforgeable under random message and chosen verification queries attack (suf-rmva), weak pseudorandom and tag-randomization simulatable, and has simulation-sound extractable non-interactive zero-knowledge proofs (SE-NIZKs). To design practical APAKE protocols, we instantiate an algebraic MAC based on the q-SDH assumption which satisfies all the required properties, and construct credential presentation algorithms for the MAC which have optimal efficiency for a randomize-then-prove paradigm. Based on the algebraic MAC, we instantiate a highly practical APAKE protocol and denote it by APAKE, which is much more efficient than the mechanisms specified by ISO/IEC 20009-4. An efficient revocation mechanism for APAKE is also proposed.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978354"}, {"primary_key": "4066700", "vector": [], "sparse_vector": [], "title": "Identity-Concealed Authenticated Encryption and Key Exchange.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Identity concealment and zero-round trip time (0-RTT) connection are two of current research focuses in the design and analysis of secure transport protocols, like TLS1.3 and Google's QUIC, in the client-server setting. In this work, we introduce a new primitive for identity-concealed authenticated encryption in the public-key setting, referred to as higncryption, which can be viewed as a novel monolithic integration of public-key encryption, digital signature, and identity concealment. We then present the security definitional framework for higncryption, and a conceptually simple (yet carefully designed) protocol construction. As a new primitive, higncryption can have many applications. In this work, we focus on its applications to 0-RTT authentication, showing higncryption is well suitable to and compatible with QUIC and OPTLS, and on its applications to identity-concealed authenticated key exchange (CAKE) and unilateral CAKE (UCAKE). Of independent interest is a new concise security definitional framework for CAKE and UCAKE proposed in this work, which unifies the traditional BR and (post-ID) frameworks, enjoys composability, and ensures very strong security guarantee. Along the way, we make a systematically comparative study with related protocols and mechanisms including <PERSON>'s signcryption, one-pass HMQV, QUIC, TLS1.3 and OPTLS, most of which are widely standardized or in use.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978350"}, {"primary_key": "4066701", "vector": [], "sparse_vector": [], "title": "Hypnoguard: Protecting Secrets across Sleep-wake Cycles.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Attackers can get physical control of a computer in sleep (S3/suspend-to-RAM), if it is lost, stolen, or the owner is being coerced. High-value memory-resident secrets, including disk encryption keys, and private signature/encryption keys for PGP, may be extracted (e.g., via cold-boot or DMA attacks), by physically accessing such a computer. Our goal is to alleviate threats of extracting secrets from a computer in sleep, without relying on an Internet-facing service. We propose Hypnoguard to protect all memory-resident OS/user data across S3 suspensions, by first performing an in-place full memory encryption before entering sleep, and then restoring the plaintext content at wakeup-time through an environment-bound, password-based authentication process. The memory encryption key is effectively \"sealed\" in a Trusted Platform Module (TPM) chip with the measurement of the execution environment supported by CPU's trusted execution mode (e.g., Intel TXT, AMD-V/SVM). Password guessing within Hypnoguard may cause the memory content to be permanently inaccessible, while guessing without Hypnoguard is equivalent to brute-forcing a high-entropy key (due to TPM protection). We achieved full memory encryption/decryption in less than a second on a mainstream computer (Intel i7-4771 CPU with 8GB RAM, taking advantage of multi-core processing and AES-NI), an apparently acceptable delay for sleep-wake transitions. To the best of our knowledge, Hypnoguard provides the first wakeup-time secure environment for authentication and key unlocking, without requiring per-application changes.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978372"}, {"primary_key": "4066703", "vector": [], "sparse_vector": [], "title": "A Software Approach to Defeating Side Channels in Last-Level Caches.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a software approach to mitigate access-driven side-channel attacks that leverage last-level caches (LLCs) shared across cores to leak information between security domains (e.g., tenants in a cloud). Our approach dynamically manages physical memory pages shared between security domains to disable sharing of LLC lines, thus preventing \"Flush-Reload\" side channels via LLCs. It also manages cacheability of memory pages to thwart cross-tenant \"Prime-Probe\" attacks in LLCs. We have implemented our approach as a memory management subsystem called CacheBar within the Linux kernel to intervene on such side channels across container boundaries, as containers are a common method for enforcing tenant isolation in Platform-as-a-Service (PaaS) clouds. Through formal verification, principled analysis, and empirical evaluation, we show that CacheBar achieves strong security with small performance overheads for PaaS workloads.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978324"}, {"primary_key": "4066705", "vector": [], "sparse_vector": [], "title": "FeatureSmith: Automatically Engineering Features for Malware Detection by Mining the Security Literature.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Malware detection increasingly relies on machine learning techniques, which utilize multiple features to separate the malware from the benign apps. The effectiveness of these techniques primarily depends on the manual feature engineering process, based on human knowledge and intuition. However, given the adversaries' efforts to evade detection and the growing volume of publications on malware behaviors, the feature engineering process likely draws from a fraction of the relevant knowledge. We propose an end-to-end approach for automatic feature engineering. We describe techniques for mining documents written in natural language (e.g. scientific papers) and for representing and querying the knowledge about malware in a way that mirrors the human feature engineering process. Specifically, we first identify abstract behaviors that are associated with malware, and then we map these behaviors to concrete features that can be tested experimentally. We implement these ideas in a system called FeatureSmith, which generates a feature set for detecting Android malware. We train a classifier using these features on a large data set of benign and malicious apps. This classifier achieves a 92.5% true positive rate with only 1% false positives, which is comparable to the performance of a state-of-the-art Android malware detector that relies on manually engineered features. In addition, FeatureSmith is able to suggest informative features that are absent from the manually engineered set and to link the features generated to abstract concepts that describe malware behaviors.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978304"}, {"primary_key": "4066706", "vector": [], "sparse_vector": [], "title": "Practical Censorship Evasion Leveraging Content Delivery Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "CDNBrowsing is a promising approach recently proposed for censorship circumvention. CDNBrowsing relies on the fact that blocking content hosted on public CDNs can potentially cause the censors collateral damage due to disrupting benign content publishers. In this work, we identify various low-cost attacks against CDNBrowsing, demonstrating that the design of practically unobservable CDNBrowsing systems is significantly more challenging than what thought previously. We particularly devise unique website fingerprinting attacks against CDNBrowsing traffic, and discover various forms of information leakage in HTTPS that can be used to block the previously proposed CDNBrowsing system. Motivated by the attacks, we design and implement a new CDNBrowsing system called CDNReaper, which defeats the discovered attacks. By design, a CDNBrowsing system can browse only particular types of webpages due to its proxy-less design. We perform a comprehensive measurement to classify popular Internet websites based on their browsability by CDNBrowsing systems. To further increase the reach of CDNBrowsing, we devise several mechanisms that enable CDNBrowsing systems to browse a larger extent of Internet webpages, particularly partial-CDN webpages.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2976749.2978365"}, {"primary_key": "4209195", "vector": [], "sparse_vector": [], "title": "Proceedings of the 2016 ACM SIGSAC Conference on Computer and Communications Security, Vienna, Austria, October 24-28, 2016", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "It is our pleasure to present the proceedings of the 23rd ACM Conference on Computer and Communications Security (CCS 2016), held in Vienna, Austria, on October 24-28, 2016. All papers in the proceedings were subject to a rigorous process of peer review. We received 831 fully reviewed submissions, the largest number of submissions received to date by a computer security conference. A Program Committee comprising 141 experts from 20 countries, helped by 360 external reviewers, evaluated these submissions, employing the customary double-blind review procedure. The review process had a 16.5% acceptance rate, resulting in 137 papers being accepted to the program, and very broad coverage of the entire security area. The review process was organized in three phases. In the first review round, at least two preliminary reviews were written for each paper. Most papers went on to a second round, during which at least one additional review was solicited. At this point, the authors were given an opportunity to respond to the comments received (in a rebuttal phase). Finally, in the third round, the program committee actively and comprehensively discussed the papers, and, if necessary, requested additional reviews. Within the program committee, a \"rebuttal committee\" subgroup helped to spur discussion, to ensure that author responses were considered carefully, and to reflect the post-review discussion in the feedback to authors. New this year, we relied heavily on the TPMS system for assigning submissions to reviews, and we thank <PERSON> for writing this system and for all his help with using it.", "published": "2016-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": ""}]