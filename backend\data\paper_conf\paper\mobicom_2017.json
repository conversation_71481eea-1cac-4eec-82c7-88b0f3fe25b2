[{"primary_key": "3841015", "vector": [], "sparse_vector": [], "title": "Poster: Resource Allocation with Conflict Resolution for Vehicular Sidelink Broadcast Communications.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper we present a graph-based resource allocation scheme for sidelink broadcast V2V communications. Harnessing available information on geographical position of vehicles and spectrum resources utilization, eNodeBs are capable of allotting the same set of sidelink resources to different vehicles distributed among several communications clusters. Within a communications cluster, it is crucial to prevent time-domain allocation conflicts since vehicles cannot transmit and receive simultaneously, i.e., they must transmit in orthogonal time resources. In this research, we present a solution based on a bipartite graph, where vehicles and spectrum resources are represented by vertices whereas the edges represent the achievable rate in each resource based on the SINR that each vehicle perceives. The aforementioned time orthogonality constraint can be approached by aggregating conflicting vertices into macro-vertices which, in addition, reduces the search complexity. We show mathematically and through simulations that the proposed approach yields an optimal solution. In addition, we provide simulations showing that the proposed method outperforms other competing approaches, specially in scenarios with high vehicular density.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3131260"}, {"primary_key": "3841018", "vector": [], "sparse_vector": [], "title": "Poster: Link Line Crossing Speed Estimation with Narrowband Signal Strength.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Peter <PERSON>", "<PERSON>"], "summary": "We present results from a system which uses received signal strength (RSS) measurements to estimate the speed at which a person is walking when they cross the link line. While many RSS-based device-free localization systems can detect a line crossing, this system estimates additionally the speed of crossing, which can provide significant additional information to a tracking system. Further, unlike device-free RF sensors which occupy tens of MHz of bandwidth, this system uses a channel of about 10 kHz. Experiments with a person walking from 0.3 to 1.8 m/s show the system can measure walking speed within 0.05 m/s RMS error.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3131253"}, {"primary_key": "3841021", "vector": [], "sparse_vector": [], "title": "Experience: An Open Platform for Experimentation with Commercial Mobile Broadband Networks.", "authors": ["Özgü Alay", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Open experimentation with operational Mobile Broadband (MBB) networks in the wild is currently a fundamental requirement of the research community in its endeavor to address the need of innovative solutions for mobile communications. Even more, there is a strong need for objective data about stability and performance of MBB (e.g., 3G/4G) networks, and for tools that rigorously and scientifically assess their status. In this paper, we introduce the MONROE measurement platform: an open access and flexible hardware-based platform for measurements and custom experimentation on operational MBB networks. The MONROE platform enables accurate, realistic and meaningful assessment of the performance and reliability of 11 MBB networks in Europe. We report on our experience designing, implementing and testing the solution we propose for the platform. We detail the challenges we overcame while building and testing the MONROE testbed and argue our design and implementation choices accordingly. We describe and exemplify the capabilities of the platform and the wide variety of experiments that external users already perform using the system.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117812"}, {"primary_key": "3841026", "vector": [], "sparse_vector": [], "title": "Making Roads Safer by Making Drivers Better.", "authors": ["<PERSON>"], "summary": "The world's roads see over 50 million injuries and 1.25 million fatalities every year; road accidents are the leading cause of death among people between the ages of 15 to 30. This talk will describe how mobile sensing (especially using smartphones), signal processing, machine learning, and behavioral science can improve road safety by making people better drivers. I'll discuss several challenges in achieving this goal, as well as learnings from successful deployments in multiple countries. Interesting problems include inferring vehicular dynamics from noisy sensor data; accurate drive detection; detecting and discouraging distracted driving; designing good incentives for safe-driving; and the design of new sensing platforms to augment smartphone sensors.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117847"}, {"primary_key": "3841027", "vector": [], "sparse_vector": [], "title": "Poster: Broadcast LTE Data Reveals Application Type.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The rapid growth in mobile connectivity is enabling phones to support a wide range of societally-important applications. In this work, we show that broad classes of popular mobile applications have distinct radio resource allocation signatures. Using this insight, we design a mobile application classifier, and demonstrate that (1) an application can infer its own type solely from its resource allocation patterns, and (2) anyone can accurately infer the type of application being served by each session on a particular cell tower. We present our findings by showing the breakdown of applications being served by an LTE base station belonging to a Tier 1 US provider in downtown Palo Alto. Our work encourages an open discussion about LTE standards, and whether they might need to be enhanced to mask features that can be exploited to infer application type from signals broadcast over the air.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3131254"}, {"primary_key": "3841028", "vector": [], "sparse_vector": [], "title": "Poster: Toward a Better Monitoring of Air Pollution using Mobile Wireless Sensor Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Mobile wireless sensor networks (MWSN) are widely used for monitoring physical phenomena such as air pollution where the aim is usually to generate accurate pollution maps in real time. The generation of pollution maps can be performed using either sensor measurements or physical models which simulate the phenomenon of pollution dispersion. The combination of these two information sources, known as data assimilation, makes it possible to better monitor air pollution by correcting the simulations of physical models while relying on sensor measurements. The quality of data assimilation mainly depends on the number of measurements and their locations. A careful deployment of nodes is therefore necessary in order to get better pollution maps. In this ongoing work, we tackle the placement problem of pollution sensors and design a mixed integer programming model allowing to maximize the assimilation quality while ensuring the connectivity of the network. We perform some simulations on a dataset of the Lyon city, France in order to show the effectiveness of our model regarding the quality of pollution coverage.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3131249"}, {"primary_key": "3841032", "vector": [], "sparse_vector": [], "title": "BlueMountain: An Architecture for Customized Data Management on Mobile Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>on Ki", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we design a pluggable data management solution for modern mobile platforms (e.g., Android). Our goal is to allow data management mechanisms and policies to be implemented independently of core app logic. Our design allows a user to install data management solutions as apps, install multiple such solutions on a single device, and choose a suitable solution each for one or more apps. It allows app developers to focus their effort on app logic and helps the developers of data management solutions to achieve wider deployability. It also gives increased control of data management to end users and allows them to use different solutions for different apps. We present a prototype implementation of our design called BlueMountain, and implement several data management solutions for file and database management to demonstrate the utility and ease of using our design. We perform detailed microbenchmarks as well as end-to-end measurements for files and databases to demonstrate the performance overhead incurred by our implementation.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117822"}, {"primary_key": "3841033", "vector": [], "sparse_vector": [], "title": "BiPass: Enabling End-to-End Full Duplex.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON>"], "summary": "Full duplex techniques can potentially double the channel capacity and achieve lower delays by empowering two radios to simultaneously transmit in thesame frequency band.However, full duplex is only available between two adjacent nodes within the communication range. In this paper, we present BiPass to break this limitation.With the help of full duplex capable relays, weenable simultaneous bidirectional in-band cut-through transmissions between two far apart nodes, so they can do full duplex communications as if they were within each other's transmission range.To design such a system, we analyze interference patterns and propose a loop-back interference cancellation strategy. We identify the power amplification problem at relay nodes and develop an algorithm to solve it. We also develop a routing algorithm, an opportunistic forwarding scheme, and a real-time feedback strategy to leverage this system in ad-hoc networks.To evaluate the real world performance of BiPass, we build a prototype and conduct experiments using software defined radios. We show that BiPass can achieve 1.6x median throughput gain over state-of-the-art one-way cut-through systems, and 4.09x gain over the decode-and-forward scheme. Our simulations further reveal that even when the data traffic is not bidirectional, BiPass has 1.36x throughput gain and 47\\% delay reduction overone-way cut-through systemsin large networks.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117826"}, {"primary_key": "3841034", "vector": [], "sparse_vector": [], "title": "Poster: DeepTFP: Mobile Time Series Data Analytics based Traffic Flow Prediction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Ren", "<PERSON><PERSON>", "<PERSON>"], "summary": "Traffic flow prediction is an important research issue to avoid traffic congestion in transportation systems. Traffic congestion avoiding can be achieved by knowing traffic flow and then conducting transportation planning. Achieving traffic flow prediction is challenging as the prediction is affected by many complex factors such as inter-region traffic, vehicles' relations, and sudden events. However, as the mobile data of vehicles has been widely collected by sensor-embedded devices in transportation systems, it is possible to predict the traffic flow by analysing mobile data. This study proposes a deep learning based prediction algorithm, DeepTFP, to collectively predict the traffic flow on each and every traffic road of a city. This algorithm uses three deep residual neural networks to model temporal closeness, period, and trend properties of traffic flow. Each residual neural network consists of a branch of residual convolutional units. DeepTFP aggregates the outputs of the three residual neural networks to optimize the parameters of a time series prediction model. Contrast experiments on mobile time series data from the transportation system of England demonstrate that the proposed DeepTFP outperforms the Long Short-Term Memory (LSTM) architecture based method in prediction accuracy.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3131269"}, {"primary_key": "3841035", "vector": [], "sparse_vector": [], "title": "Poster: A New Scalable, Programmable and Evolvable Mobile Control Plane Platform.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose a new scalable, programmable and evolvable mobile control plane platform running on realtime stream frameworks for future mobile networks. We build our prototype and show its feasibility by using Cellular Internet of Things (CIoT) as an example use case in a realistic mobile networking testbed.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3131247"}, {"primary_key": "3841036", "vector": [], "sparse_vector": [], "title": "Poster: Conservative Modulation and Coding for Low-latency Robust Transmission of Scalable ECG over LTE MTC.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This study introduces a novel conservative modulation and coding scheme to minimize and stabilize the delay incurred during the process of electrocardiogram (ECG) transmission over a wireless medium, while maintaining the desired level of the ECG pattern quality required for improving the chance of its interpretation. A machine-type communication system is adopted for the delivery of ECG data to benefit from its inherent reliability, pervasiveness, security, and performance of 4G long-term evolution technologies with reduced cost and enhanced coverage. Extensive evaluations indicate that the proposed system provides a sufficient level of service for medical-grade instantaneous ECG monitoring even under significantly deteriorated channel conditions.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3131267"}, {"primary_key": "3841037", "vector": [], "sparse_vector": [], "title": "RF-Echo: A Non-Line-of-Sight Indoor Localization System Using a Low-Power Active RF Reflector ASIC Tag.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Long-range low-power localization is a key technology that enables a host of new applications of wireless sensor nodes. We present RF-Echo, a new low-power RF localization solution that achieves decimeter accuracy in long range indoor non-line-of-sight (NLOS) scenarios. RF-Echo introduces a custom-designed active RF reflector ASIC (application specific integrated circuit) fabricated in a 180nm CMOS process which echoes a frequency-shifted orthogonal frequency division multiplexing (OFDM) signal originally generated from an anchor. The proposed technique is based on time-of-flight (ToF) estimation in the frequency domain that effectively eliminates inter-carrier and inter-symbol interference in multipath-rich indoor NLOS channels. RF-Echo uses a relatively narrow bandwidth of $\\leq$80 MHz which does not require an expensive very high sampling rate analog-to-digital converter (ADC). Unlike ultra-wideband (UWB) systems, the active reflection scheme is designed to operate at a relatively low carrier frequency that can penetrate building walls and other blocking objects for challenging NLOS scenarios. Since the bandwidth at lower frequencies (2.4 GHz and sub-1 GHz) is severely limited, we propose novel signal processing algorithms as well as machine learning techniques to significantly enhance the localization resolution given the bandwidth constraint of the proposed system. The newly fabricated tag IC consumes 62.8 mW active power. The software defined radio (SDR) based anchor prototype is rapidly deployable without the need for accurate synchronization among anchors and tags. Field trials conducted in a university building confirm up to 85 m operation with decimeter accuracy for robust 2D localization.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117840"}, {"primary_key": "3841038", "vector": [], "sparse_vector": [], "title": "Advertising-based Measurement: A Platform of 7 Billion Mobile Devices.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The most important step in an empirical computer scientist's research is gathering sufficient real-world data to validate a system. Unfortunately, it is also one of the most time-consuming and expensive tasks: placing measurement tools in remote networks or end-clients requires one to marshal resources from different administrative domains, devices, populations, and countries. Often such efforts culminate in a trace that is deficient in multiple ways: a small set of test subjects, a short time frame, missing ground truth for device IDs, networking environments lacking in diversity and geographic spread, or highly biased sampling. We present a method of addressing these challenges by leveraging the most open and globally accessible test and measurement platform: digital advertising. Digital advertising instantly provides a window into 7 billion devices spanning every county for an extremely low cost. We propose Advertising as a Platform (AaaP), an ad-based system to perform massive-scale mobile measurement studies. In contrast with measurements made by large media companies who own platforms, ad networks, and apps, we concentrate on the opportunities and challenges for researchers that are end-users of advertising systems. We evaluate a prototype system, discuss ethical guidelines, and demonstrate its use in four scenarios: IP2Geo databases, bandwidth measurement, energy management, and the identifiability of mobile users. We show the efficacy and ease-of-use of AaaP, and illuminate key challenges and the great promise of using AaaP to study a wide variety of mobile phenomena.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117844"}, {"primary_key": "3841039", "vector": [], "sparse_vector": [], "title": "FSONet: A Wireless Backhaul for Multi-Gigabit Picocells Using Steerable Free Space Optics.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Expected increase in cellular demand has pushed recent interest in picocell networks which have reduced cell sizes (100-200m or less). For ease of deployment of such networks, a wireless backhaul network is highly desired. Since RF-based technologies are unlikely to provide the desired multi-gigabit data rates, we motivate and explore use of free space optics (FSO) for picocell backhaul. In particular, we present a novel network architecture based on steerable links and sufficiently many robust short-range links, to help circumvent the key challenge of outdoor effects in reliable operation of outdoor FSO links. Our architecture is motivated by the fact that, due to the high density of picocells, many short-range links will occur naturally in a picocell backhaul. Moreover, use of steerable FSO links facilitates networks with sufficient redundancy while using only a small number of interfaces per node. We address the key problems that arise in the context of such a backhaul architecture, viz., an FSO link design with desired characteristics, and related network design and management problems. We develop and evaluate a robust 100m FSO link prototype, and simulate the proposed architecture in many metro US cities while show its viability via evaluation of key performance metrics.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3129239"}, {"primary_key": "3841040", "vector": [], "sparse_vector": [], "title": "Demo: The Sound of Silence: End-to-End Sign Language Recognition Using SmartWatch.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Sign Language is a natural and fully-fledged communication method for deaf and hearing-impaired people. In this demo, we propose the first SmartWatch-based American sign language (ASL) recognition system, which is more comfortable, portable and user-friendly and offers accessibility anytime, anywhere. This system is based on the intuitive idea that each sign has its specific motion pattern which can be transformed into unique gyroscope and accelerometer signals and then analyzed and learned by using Long-Short term memory recurrent neural network (LSTM-RNN) trained with connectionist temporal classification (CTC). In this way, signs and context information can be correctly recognized based on an off-the-shelf device (eg. SmartWatch, Smartphone). The experiments show that, in the Known user split task, our system reaches an average word error rate of 7.29% to recognize 73 sentences formed by 103 ASL signs and achieves detection ratio up to 93.7% for a single sign. The result also shows our system has a good adaptation, even including new users, it can achieve an average word error rate of 21.6% at the sentence level and reach an average detection ratio of 79.4%. Moreover, our system performs real time ASL translation, outputting the speech within 1.69 seconds for a sentence of 12 signs in average.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3119853"}, {"primary_key": "3841043", "vector": [], "sparse_vector": [], "title": "Adding the Next Nine: An Investigation of Mobile Broadband Networks Availability.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The near ubiquitous availability and success of mobile broadband networks has motivated verticals that range from public safety communication to intelligent transportation systems and beyond to consider choosing them as the communication mean of choice. Several of these verticals, however, expect high availability of multiple nines. This paper leverages end-to-end measurements to investigate the potential of current mobile broadband networks to support these expectations. We conduct a large-scale measurement study of network availability in four networks in Norway. This study is based on three years of measurements from hundreds of stationary measurement nodes and several months of measurements from four mobile nodes. We find that the mobile network centralized architecture and infrastructure sharing between operators are responsible for a non-trivial fraction of network failures. Most episodes of degraded availability, however, are uncorrelated. We also find that using two networks simultaneously can result in more than five nines of availability for stationary nodes and three nines of availability for mobile nodes. Our findings point to potential avenues for enhancing the availability of future mobile networks.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117842"}, {"primary_key": "3841045", "vector": [], "sparse_vector": [], "title": "Continuous Authentication for Voice Assistants.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Voice has become an increasingly popular User Interaction (UI) channel, mainly contributing to the current trend of wearables, smart vehicles, and home automation systems. Voice assistants such as <PERSON><PERSON>, <PERSON><PERSON>, and Google Now, have become our everyday fixtures, especially when/where touch interfaces are inconvenient or even dangerous to use, such as driving or exercising. The open nature of the voice channel makes voice assistants difficult to secure, and hence exposed to various threats as demonstrated by security researchers. To defend against these threats, we present VAuth, the first system that provides continuous authentication for voice assistants. <PERSON>uth is designed to fit in widely-adopted wearable devices, such as eyeglasses, earphones/buds and necklaces, where it collects the body-surface vibrations of the user and matches it with the speech signal received by the voice assistant's microphone. <PERSON><PERSON> guarantees the voice assistant to execute only the commands that originate from the voice of the owner. We have evaluated <PERSON><PERSON> with 18 users and 30 voice commands and find it to achieve 97% detection accuracy and less than 0.1% false positive rate, regardless of <PERSON><PERSON>'s position on the body and the user's language, accent or mobility. <PERSON><PERSON> successfully thwarts various practical attacks, such as replay attacks, mangled voice attacks, or impersonation attacks. It also incurs low energy and latency overheads and is compatible with most voice assistants.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117823"}, {"primary_key": "3841049", "vector": [], "sparse_vector": [], "title": "Orion: RAN Slicing for a Flexible and Cost-Effective Multi-Service Mobile Network Architecture.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Emerging 5G mobile networks are envisioned to become multi-service environments, enabling the dynamic deployment of services with a diverse set of performance requirements, accommodating the needs of mobile network operators, verticals and over-the-top (OTT) service providers. Virtualizing the mobile network in a flexible way is of paramount importance for a cost-effective realization of this vision. While virtualization has been extensively studied in the case of the mobile core, virtualizing the radio access network (RAN) is still at its infancy. In this paper, we present Orion, a novel RAN slicing system that enables the dynamic on-the-fly virtualization of base stations, the flexible customization of slices to meet their respective service needs and which can be used in an end-to-end network slicing setting. Orion guarantees the functional and performance isolation of slices, while allowing for the efficient use of RAN resources among them. We present a concrete prototype implementation of Orion for LTE, with experimental results, considering alternative RAN slicing approaches, indicating its efficiency and highlighting its isolation capabilities. We also present an extension to Orion for accommodating the needs of OTT providers.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117831"}, {"primary_key": "3841050", "vector": [], "sparse_vector": [], "title": "Demo: Orion: A Radio Access Network Slicing System.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Emerging 5G mobile networks are envisioned to support the dynamic deployment of services with diverse performance requirements, accommodating the needs of mobile network operators and verticals. Virtualizing the mobile network components in a flexible and cost-effective way is therefore of paramount importance. In this work, we highlight the capabilities of Orion, a novel RAN slicing architecture that enables the dynamic virtualization of base stations and flexible customization of slices to meet their respective service needs. Our demonstration of Orion's capabilities is based on a prototype implementation employing a modified version of the OpenAirInterface software LTE platform. Using this prototype, we demonstrate the functional and performance isolation, and the efficient sharing of radio hardware and spectrum that can be achieved among Orion RAN slices. Moreover, we show how Orion can be used in an end-to-end network slicing setting and demonstrate the effects of the slices' configuration and placement of virtual functions in the overall quality of the deployed services.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3119860"}, {"primary_key": "3841051", "vector": [], "sparse_vector": [], "title": "Demo: FlexRAN: A Software-Defined RAN Platform.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Although SDN is considered as one of the key technologies behind the impending 5G evolution of mobile networks, the opportunity of reaping its benefits is largely still untapped on the Radio Access Network (RAN) side due to the lack of a software-defined RAN (SD-RAN) platform. In this work we demonstrate the capabilities of FlexRAN, an open-source SD-RAN platform developed to fill this void. FlexRAN separates the RAN control and data planes with a custom-tailored southbound API. Besides it features a hierarchical control plane architecture that enables programmability, flexible and dynamic control function placement (allowing different degrees of coordination within and among base stations) and real-time control. Virtualized control functions and control delegation are two key features in FlexRAN that makes these capabilities possible. This demo illustrates the capabilities and the performance of FlexRAN based on a prototype implementation, while its applicability is highlighted through a Mobile Edge Computing use case, where it acts as an enabler of a video bitrate adaptation application based on the radio conditions at the network edge.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3119852"}, {"primary_key": "3841054", "vector": [], "sparse_vector": [], "title": "Demo: Sensor Fusion Localization and Navigation for Visually Impaired People.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Federica Inderst", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present an innovative smartphone-centric tracking system for indoor and outdoor environments, based on the joint utilization of dead-reckoning and computer vision (CV) techniques. The system is explicitly designed for visually impaired people (although it could be easily generalized to other users) and it is built under the assumption that special reference signals, such as painted lines, colored tapes or tactile pavings are deployed in the environment for guiding visually impaired users along pre-defined paths. Thanks to highly optimized software, we are able to execute the CV and sensor-fusion algorithms in run-time on low power hardware such as a normal smartphone, precisely tracking the users movements.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3119858"}, {"primary_key": "3841056", "vector": [], "sparse_vector": [], "title": "Poster: DRIZY: Collaborative Driver Assistance Over Wireless Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Driver assistance systems, that rely on vehicular sensors such as cameras, LIDAR and other on-board diagnostic sensors, have progressed rapidly in recent years to increase road safety. Road conditions in developing countries like India are chaotic where roads are not well maintained and thus vehicular sensors alone do not suffice in detecting impending collisions. In this paper, we investigate a collaborative driver assistance system \"DRIZY: DRIve eaSY\" for such scenarios where inference is drawn from on-board camera feed to alert drivers of obstacles ahead and the cloud uses GPS sensor data uploaded by all vehicles to alert drivers of vehicles in potential collision trajectory. Thus, we combine computer vision and vehicle-to-cloud communication to create comprehensive situational awareness. We prototype our system to consider two types of collisions: vehicle-to-vehicle collisions based on uploading GPS sensor data of vehicles to cloud and vehicle-to-pedestrian collisions based on detecting pedestrians from vehicle's dashboard camera feed. Sensor data processing in each vehicle occurs on smartphone for GPS values which are then uploaded to cloud and on raspberry pi3 for video feeds to make a cost-effective solution. Experiments over both 4G and wireless networks in India show that collaborative driver assistance is feasible in low traffic density within acceptable driver reaction time of <5 sec, but can be limited by the time to process compute-intensive video feeds in real-time. We investigate novel ways to optimize the processing to find an acceptable trade-off.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3131255"}, {"primary_key": "3841062", "vector": [], "sparse_vector": [], "title": "Navigating the Chasm between Curiosity- and Impact-Driven Research.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Being in academia provides a unique opportunity to explore one's curiosity and build cool systems that advance human understanding of engineering and science. System researchers however also have a responsibility to build systems that can impact practice in the near-term and perhaps, even create a brand new industry. In my talk, I will focus on my efforts so far navigating this chasm between curiosity and impact driven research. Specifically, I will share two research themes I have been working on as an assistant professor at UW CSE, where what initially began as curiosity-driven projects were transformed by the urge to make immediate practical impact as well as be unique. In particular, I will first talk about our journey going from a science-driven project on ambient backscatter (Sigcomm'13) to building wireless backscatter systems that work reliably and address a key pain-point in the industry. Next, I will talk about how we shifted gears from working on wireless gesture recognition (WiSee, Mobicom'13) to addressing a medical need of millions of people in the United States that go undiagnosed from sleep apnea (ApneaApp, Mobisys'15) and our experience licensing our technology to a multi-national medical corporation. Finally, I will share my thoughts on how our research community can help us better navigate this chasm.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3129240"}, {"primary_key": "3841064", "vector": [], "sparse_vector": [], "title": "TinyLink: A Holistic System for Rapid Development of IoT Applications.", "authors": ["Gaoyang Guan", "<PERSON>", "<PERSON>", "Kaibo Fu", "<PERSON><PERSON><PERSON>"], "summary": "Rapid development is essential for IoT (Internet of Things) application developers to obtain first-mover advantages and reduce the development cost. In this paper, we present TinyLink, a holistic system for rapid development of IoT applications. The key idea of TinyLink is to use a top-down approach for designing both the hardware and the software of IoT applications. Developers write the application code in a C-like language to specify the key logic of their applications, without dealing with the details of the specific hardware components. Taking the application code as input, TinyLink automatically generates the hardware configuration as well as the binary program executable on the target hardware platform. TinyLink provides unified APIs for applications to interact with the underlying hardware components. We implement TinyLink and evaluate its performance using real-world IoT applications. Results show that: (1) TinyLink achieves rapid development of IoT applications, reducing 52.58% of lines of code in average compared with traditional approaches; (2) TinyLink searches a much larger design space and thus can generate a superior solution for the hardware configuration, compared with the state-of-the-art approach; (3) TinyLink incurs acceptable overhead in terms of execution time and program memory.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117825"}, {"primary_key": "3841065", "vector": [], "sparse_vector": [], "title": "Demo: A Cell-level Traffic Generator for LoRa Networks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this demo we present and validate a LoRa cell traffic generator, able to emulate the behavior of thousands of low-rate sensor nodes deployed in the same cell, by using a single Software Defined Radio (SDR) platform. Differently from traditional generators, whose goal is creating packet flows which emulate specific applications and protocols, our focus is generating a combined radio signal, as seen by a gateway, given by the super-position of the signals transmitted by multiple sensors simultaneously active on the same channel. We argue that such a generator can be of interest for testing different network planning solutions for LoRa networks.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3119862"}, {"primary_key": "3841066", "vector": [], "sparse_vector": [], "title": "Accelerating Multipath Transport Through Balanced Subflow Completion.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Simultaneously using multiple network paths (e.g., WiFi and cellular) is an attractive feature on mobile devices. A key component in a multipath system such as MPTCP is the scheduler, which determines how to distribute the traffic over multiple paths. In this paper, we propose DEMS, a new multipath scheduler aiming at reducing the data chunk download time. DEMS consists of three key design decisions: (1) being aware of the chunk boundary and strategically decoupling the paths for chunk delivery, (2) ensuring simultaneous subflow completion at the receiver side, and (3) allowing a path to trade a small amount of redundant data for performance. We have implemented DEMS on smartphones and evaluated it over both emulated and real cellular/WiFi networks. DEMS is robust to diverse network conditions and brings significant performance boost compared to the default MPTCP scheduler (e.g., median download time reduction of 33%--48% for fetching files and median loading time reduction of 6%--43% for fetching web pages), and even more benefits compared to other state-of-the-art schedulers.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117829"}, {"primary_key": "3841067", "vector": [], "sparse_vector": [], "title": "Demo: DEMS: DEcoupled Multipath Scheduler for Accelerating Multipath Transport.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present the demonstration of DEMS, a new multipath scheduler aiming at reducing the data chunk download time. DEMS consists of three key design decisions: (1) being aware of the chunk boundary and strategically decoupling the paths for chunk delivery, (2) ensuring simultaneous subflow completion at the receiver side, and (3) allowing a path to trade a small amount of redundant data for performance. We integrate the DEMS components into a holistic system and implement it on commodity mobile devices, where unmodified mobile applications can use DEMS to transmit data over multipath. We demonstrate the simple configuration of using DEMS over multipath, visualization of multipath scheduling, download time reduction of data chunks with DEMS over both emulated and real cellular/WiFi networks compared to default MinRTT scheduler, and application QoE improvement on mobile phones from DEMS.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3119869"}, {"primary_key": "3841068", "vector": [], "sparse_vector": [], "title": "Poster: Emotion-Aware Smart Tips for Healthy and Happy Sleep.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "People spend up to one-third of lives asleep, and healthy sleep habits can make a big difference in their quality of life. But in modern society, many people have unhealthy sleep diaries and suffer from various sleep disorders, which may result in irregular mood fluctuations or even mental health problems such as anxiety and depression. We propose the Emotion-Aware Smart Tips (EAST), a novel approach that could help to inform users about their irregular emotional states with smart tips to improve their sleep qualities. EAST aims at helping users keep healthy sleep schedules and emotional states by providing smart tips through a novel model that combines multivariate regression, random forest, and neural network to quantify the relations between sleep patterns and emotional states. Prototype implementation and initial experiments of EAST in mobile phones have demonstrated its desired functionality and practicality for real-world deployment.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3131256"}, {"primary_key": "3841069", "vector": [], "sparse_vector": [], "title": "Poster: IoTURVA: Securing Device-to-Device Communications for IoT.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Sasu Tarkoma"], "summary": "In this poster we present IoTurva, a platform for securing Device-to-Device (D2D) communication in IoT. Our solution takes a blackbox approach to secure IoT edge-networks. We combine user and device-centric context-information together with network data to classify network communication as normal or malicious. We have designed a dual-layer traffic classification scheme based on fuzzy logic, where the classification model is trained remotely. The remotely trained model is then used by the edge gateway to classify the network traffic. We have implemented a proof-of-concept prototype and evaluate its performance in a real world environment. Theevaluation shows that IoTurva causes very small overhead while it works with minimal hardware, and that our model training and classification approach can improve system efficiency and privacy.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3131262"}, {"primary_key": "3841076", "vector": [], "sparse_vector": [], "title": "Demo: Atlas Thing Architecture: Enabling Mobile Apps as Things in the IoT.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Venkata Gutta"], "summary": "We make the case for mobile apps as crucial and influential things in the Internet of Things (IoT) and then present our Atlas Thing Architecture that provides the explicit support necessary for their inclusion. We present the World Cup demo scenario which involves media appliances and mobile app things, and which shows how we implemented it as an IoT application based on our architecture.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3119856"}, {"primary_key": "3841077", "vector": [], "sparse_vector": [], "title": "Poster: A Portfolio Theory Approach to Edge Traffic Engineering via Bayesian Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "One of the main goals of mobile edge computing is to support new generation latency-sensitive networked applications. To manage such demanding applications, a fine-grained control of end-to-end paths is imperative. End-to-end delay estimation and forecast techniques were essential traffic engineering tools even before the mobile edge computing paradigm pushed the cloud closer to the end user. In this paper, we model the path selection problem for edge traffic engineering using a risk minimization technique inspired by portfolio theory in economics, and we use machine learning to estimate the risk of a path. In particular, using real latency time series measurements, collected with and without the GENI testbed, we compare four short-horizon latency estimation techniques, commonly used by the finance community to estimate prices of volatile financial instruments. Our initial results suggest that a Bayesian Network approach may lead to good latency estimation performance and open a few research questions that we are currently exploring.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3131250"}, {"primary_key": "3841079", "vector": [], "sparse_vector": [], "title": "Poster: Interacting Data-Intensive Services Mining and Placement in Mobile Edge Clouds.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the rapid growth of cloud computing and mobile computing, it is commonplace for users to request cloud services from mobile devices. Mobile edge clouds (MECs) allow the users to access the cloud services seamlessly. Although cloudlets provide a promising technique to reduce the service access latency, how to place the data-intensive service in MECs to reduce the communication overhead between different services is yet to be addressed. To attack this challenge, this paper proposes an approach for mining interacting services and placing the services on the cloudlets by optimizing the communication overhead. In this approach, a frequent itemsets mining algorithm is proposed to obtain the fine-grained frequent 2-itemsets by analyzing the cookies. This algorithm determines the minimum support threshold automatically, based on which FP-tree with FP-matrix is constructed to avoid traversing the FP-tree during the process of frequent 2-itemsets discovery, then a searching algorithm is presented to mine the discriminative frequent 2-itemsets with interestingness measure. Furthermore, the communication overhead is optimized with the capacity constraints of cloudlets. Finally, we validate the efficacy of our approach by real-world data based simulations. The results show our approach can reduce the communication overhead for service placement in MECs.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3131263"}, {"primary_key": "3841080", "vector": [], "sparse_vector": [], "title": "Demo: LL-MEC A SDN-based MEC Platform.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Software-defined Networking (SDN) is seen as a promising solution that allows for a more distributed, flexible, and scalable network. Multi-access Edge Computing (MEC), initiated as an Industry Specification Group (ISG) within ETSI, is also emerging as a low-latency and high-throughput cloud environment at the edge of network. The noticeable success that aforementioned technologies made attracts massive research interests and the interplay between them on programmable network requires an open source platform to evaluate. In this work, we present a low-latency MEC platform (LL-MEC) providing the required flexibility and programmability to meet the expected performance gain following SDN and MEC principles. We also demonstrate an use case of real-time content caching application using LL-MEC platform and OpenAirInterface LTE implementation on commodity hardware.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3119857"}, {"primary_key": "3841083", "vector": [], "sparse_vector": [], "title": "RAVEN: Perception-aware Optimization of Power Consumption for Mobile Games.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Changyoung Koh", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "High-end mobile GPUs are now becoming an integral part of mobile devices. However, a mobile GPU constitutes a major portion of power consumption on the devices, and mobile games top as the most popular class of graphics applications. This paper presents the design and implementation of RAVEN, a novel, on-the-fly frame rate scaling system for mobile gaming applications. RAVEN utilizes human visual perception of graphics change to opportunistically achieve power saving without degrading user experiences. The system develops a light-weight frame comparison technique to measure and predict perception-aware frame similarity. It also builds a low resolution virtual display which clones the device screen for performing similarity measurement at a low-power cost. It is able to work on an existing commercial smartphone and support applications from app stores without any modifications. It has been implemented on Nexus 5X, and its performance has been measured with 13 games. The system effectively reduces the overall power consumption of mobile devices while maintaining satisfactory user experiences. The power consumption is reduced by 21.78% on aver-age and up to 34.74%.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117841"}, {"primary_key": "3841084", "vector": [], "sparse_vector": [], "title": "Automating Diagnosis of Cellular Radio Access Network Problems.", "authors": ["<PERSON>", "<PERSON>", "Ion <PERSON>"], "summary": "In an increasingly mobile connected world, our user experience of mobile applications more and more depends on the performance of cellular radio access networks (RAN). To achieve high quality of experience for the user, it is imperative that operators identify and diagnose performance problems quickly. In this paper, we describe our experience in understanding the challenges in automating the diagnosis of RAN performance problems. Working with a major cellular network operator on a part of their RAN that services more than 2 million users, we demonstrate that fine-grained modeling and analysis could be the key towards this goal. We describe our methodology in analyzing RAN problems, and highlight a few of our findings, some previously unknown. We also discuss lessons from our attempt at building automated diagnosis solutions.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117813"}, {"primary_key": "3841085", "vector": [], "sparse_vector": [], "title": "Demo: BlueBee: 10, 000x Faster Cross-Technology Communication from Bluetooth to ZigBee.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cross-Technology Communication is an emerging research direction providing a promising solution to the coexistence problem of heterogeneous wireless technologies in the ISM bands. However, existing works use only the coarse-grained packet-level information for cross-technology modulation, suffering from a low throughput (e.g., 10bps). Our approach, called BlueBee, aims at achieving much higher CTC throughput thus extends CTC applications. We pro- poses a new direction by emulating legitimate ZigBee frames using a Bluetooth Low Energy (BLE) radio. Uniquely, BlueBee achieves dual-standard compliance (i.e., BLE and ZigBee) and transparency by selecting only the payload of Bluetooth frames, requiring neither hardware nor firmware changes at the BLE senders and ZigBee receivers. Our implementation on commodity device testbeds shows that BlueBee can achieve a more than 99% accuracy and a through- put 10,000x faster than the state-of-the-art CTC reported so far. In addition, we show a demo of using BlueBee on a smartphone to control several smart light bulbs a ached with ZigBee radio.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3119855"}, {"primary_key": "3841086", "vector": [], "sparse_vector": [], "title": "FlipTracer: Practical Parallel Decoding for Backscatter Communication.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xiao<PERSON> Chen"], "summary": "With parallel decoding for backscatter communication, tags are allowed to transmit concurrently and more efficiently. Existing parallel decoding mechanisms, however, assume that signals of the tags are highly stable, and hence may not perform optimally in the naturally dynamic backscatter systems. This paper introduces FlipTracer, a practical system that achieves highly reliable parallel decoding even in hostile channel conditions. FlipTracer is designed with a key insight: although the collided signal is time-varying and irregular, transitions between signals' combined states follow highly stable probabilities, which offers important clues for identifying the collided signals, and provides us with an opportunity to decode the collided signals without relying on stable signals. Motivated by this observation, we propose a graphical model, called one-flip-graph (OFG), to capture the transition pattern of collided signals, and design a reliable approach to construct the OFG in a manner robust to the diversity in backscatter systems. Then FlipTracer can resolve the collided signals by tracking the OFG. We have implemented FlipTracer and evaluated its performance with extensive experiments across a wide variety of scenarios. Our experimental results have shown that FlipTracer achieves a maximum aggregated throughput that approaches 2 Mbps, which is 6x higher than the state-of-the-art.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117828"}, {"primary_key": "3841094", "vector": [], "sparse_vector": [], "title": "Simultaneous Power-Based Localization of Transmitters for Crowdsourced Spectrum Monitoring.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The current mechanisms for locating spectrum offenders are time consuming, human-intensive, and expensive. In this paper, we propose a novel approach to locate spectrum offenders using crowdsourcing. In such a participatory sensing system, privacy and bandwidth concerns preclude distributed sensing devices from reporting raw signal samples to a central agency; instead, devices would be limited to measurements of received power. However, this limitation enables a smart attacker to evade localization by simultaneously transmitting from multiple infected devices. Existing localization methods are insufficient or incapable of locating multiple sources when the powers from each source cannot be separated at the receivers. In this paper, we first propose a simple and efficient method that simultaneously locates multiple transmitters using the received power measurements from the selected devices. Second, we build sampling approaches to select sensing devices required for localization. Next, we enhance our sampling to also take into account incentives for participation in crowdsourcing. We experimentally evaluate our localization framework under a variety of settings and find that we are able to localize multiple sources transmitting simultaneously with reasonably high accuracy in a timely manner.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117845"}, {"primary_key": "3841098", "vector": [], "sparse_vector": [], "title": "Poster: Connecting Simulation and Real World: IEEE 802.11p in the Loop.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present LAN Radio, an Open Source prototyping system to couple simulation and real world for the wireless channel of IEEE 802.11p based communication. Our main focus is the use in a vehicular networking environment, where field testing and real world experimentation is becoming more relevant these days. Large Field Operational Tests (FOTs) are expensive and difficult to handle in terms of reproducibility and controllability -- particularly for application development and integration tests for automotive Electronic Control Units (ECUs). To support such experiments, we developed an approach to integrate a real system to test into a large scale simulation scenario without the need to change the physical and access layer parts of the communication stack of this system. We closely followed the Hardware In The Loop (HIL) simulation concept but also integrated a wireless communication channel into the picture. LAN Radio is building upon the well established and Open Source development platform OpenWRT to provide optimal extensibility.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3131265"}, {"primary_key": "3841100", "vector": [], "sparse_vector": [], "title": "Demo: Position Tracking for Virtual Reality Using Commodity WiFi.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Today, experiencing virtual reality (VR) is a cumbersome experience which either requires dedicated infrastructure like infrared cameras to track the headset and hand-motion controllers (e.g. Oculus Rift, HTC Vive), or provides only 3-DoF (Degrees of Freedom) tracking which severely limits the user experience (e.g. Samsung Gear VR). To truly enable VR everywhere, we need position tracking to be available as a ubiquitous service. This paper demonstrates WiCapture, a novel approach which leverages commodity WiFi infrastructure, which is ubiquitous today, for tracking purposes. We prototyped WiCapture using off-the-shelf WiFi radios and demonstrated that it achieves an accuracy of 0.88 cm compared to sophisticated infrared-based tracking systems like the Oculus Rift, while providing much higher range, resistance to occlusion, ubiquity and ease of deployment.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3119865"}, {"primary_key": "3841106", "vector": [], "sparse_vector": [], "title": "Furion: Engineering High-Quality Immersive Virtual Reality on Today&apos;s Mobile Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>wei Dai"], "summary": "In this paper, we perform a systematic design study of the \"elephant in the room\" facing the VR industry -- is it feasible to enable high-quality VR apps on untethered mobile devices such as smartphones? Our quantitative, performance-driven design study makes two contributions. First, we show that the QoE achievable for high-quality VR applications on today's mobile hardware and wireless networks via local rendering or offloading is about 10X away from the acceptable QoE, yet waiting for future mobile hardware or next-generation wireless networks (e.g. 5G) is unlikely to help, because of power limitation and the higher CPU utilization needed for processing packets under higher data rate. Second, we present Furion, a VR framework that enables high-quality, immersive mobile VR on today's mobile devices and wireless networks. Furion exploits a key insight about the VR workload that foreground interactions and background environment have contrasting predictability and rendering workload, and employs a split renderer architecture running on both the phone and the server. Supplemented with video compression, use of panoramic frames, and parallel decoding on multiple cores on the phone, we demonstrate Furion can support high-quality VR apps on today's smartphones over WiFi, with under 14ms latency and 60 FPS (the phone display refresh rate).", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117815"}, {"primary_key": "3841107", "vector": [], "sparse_vector": [], "title": "Demo: Ultra-Low Power Gaze Tracking for Virtual Reality.", "authors": ["T<PERSON>xing Li", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "demonstration Public Access Share on Demo: Ultra-Low Power Gaze Tracking for Virtual Reality Authors: <AUTHORS>", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3119866"}, {"primary_key": "3841110", "vector": [], "sparse_vector": [], "title": "WEBee: Physical-Layer Cross-Technology Communication via Emulation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent advances in Cross-Technology Communication (CTC) have improved efficient coexistence and cooperation among heterogeneous wireless devices (e.g., WiFi, ZigBee, and Bluetooth) operating in the same ISM band. However, until now the effectiveness of existing CTCs, which rely on packet-level modulation, is limited due to their low throughput (e.g., tens of bps). Our work, named WEBee, opens a promising direction for high-throughput CTC via physical-level emulation. WEBee uses a high-speed wireless radio (e.g., WiFi OFDM) to emulate the desired signals of a low-speed radio (e.g., ZigBee). Our unique emulation technique manipulates only the payload of WiFi packets, requiring neither hardware nor firmware changes in commodity technologies -- a feature allowing zero-cost fast deployment on existing WiFi infrastructure. We designed and implemented WEBee with commodity devices (Atheros AR2425 WiFi card and MicaZ CC2420) and the USRP-N210 platform (for PHY layer evaluation). Our comprehensive evaluation reveals that WEBee can achieve a more than 99% reliable parallel CTC between WiFi and ZigBee with 126 Kbps in noisy environments, a throughput about 16,000x faster than current state-of-the-art CTCs.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117816"}, {"primary_key": "3841111", "vector": [], "sparse_vector": [], "title": "Demo: Towards Flexible and Scalable Indoor Navigation.", "authors": ["<PERSON><PERSON>", "Yuanchao Shu", "<PERSON><PERSON><PERSON><PERSON> F<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Bootstrapping efforts and scalability issues hinder large-scale deployment of indoor navigation systems. We present FollowUs, an easily-deployable (bootstrap-free) and scalable indoor navigation system. In addition to robust navigation through real-time trace-following, FollowUs integrates cloud services to process and combine traces at large scale. It can also leverage optional floor plans to further enhance navigation performance. We designed and implemented FollowUs, including a mobile app and cloud services on Azure, and validate its real-world usability.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3119854"}, {"primary_key": "3841113", "vector": [], "sparse_vector": [], "title": "Demo: WEBee: Physical-Layer Cross-Technology Communication via Emulation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The applicability of existing Cross-Technology Communication (CTC) methods, which rely on packet-level modulation, is severely limited due to their very low throughput, e.g., tens of bps. Our work, named as WEBee, opens a promising direction for high throughput CTC via physical-level emulation. Specifically, WEBee synthesizes the time-domain signals by choosing appropriate frequency-domain components fed into the subcarriers of WiFi OFDM. WE-Bee can emulate the desired physical-layer ZigBee signals by manipulating only the data bits in WiFi packet payload, requiring neither hardware nor firmware changes in commodity technologies. Moreover, WEBee enables the parallel CTC, where one WiFi frame emulates two ZigBee frames simultaneously. To evaluate the performance, we implemented WEBee on commodity devices (the Atheros AR2425 WiFi card, BCM 4330 WiFi card and CC2420, CC2530 ZigBee devices). Our comprehensive evaluation reveals that WEBee can achieve the CTC between WiFi and ZigBee with a reliable throughput of 126Kbps in noisy environment, 16,000x faster than current state-of-the-art CTC methods.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3119859"}, {"primary_key": "3841114", "vector": [], "sparse_vector": [], "title": "A Control-Plane Perspective on Reducing Data Access Latency in LTE Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Control-plane operations are indispensable to providing data access to mobile devices in the 4G LTE networks. They provision necessary control states at the device and network nodes to enable data access. However, the current design may suffer from long data access latency even under good radio conditions. The fundamental problem is that, data-plane packet delivery cannot start or resume until all control-plane procedures are completed, and these control procedures run sequentially by design. We show both are more than necessary under popular use cases. We design DPCM, which reduces data access latency through parallel processing approaches and exploiting device-side state replica. We implement DPCM and validate its effectiveness with extensive evaluations.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117838"}, {"primary_key": "3841115", "vector": [], "sparse_vector": [], "title": "Cardiac Scan: A Non-contact and Continuous Heart-based User Authentication System.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Li", "<PERSON><PERSON>"], "summary": "Continuous authentication is of great importance to maintain the security level of a system throughout the login session. The goal of this work is to investigate a trustworthy, continuous, and non-contact user authentication approach based on a heart-related biometric that works in a daily-life environment. To this end, we present a novel, continuous authentication system, namely Cardiac Scan, based on geometric and non-volitional features of the cardiac motion. Cardiac motion is an automatic heart deformation caused by self-excitement of the cardiac muscle, which is unique to each user and is difficult (if not impossible) to counterfeit. Cardiac Scan features intrinsic liveness detection, unobtrusiveness, cost-effectiveness, and high usability. We prototype a remote, high-resolution cardiac motion sensing system based on the smart DC-coupled continuous-wave radar. Fiducial-based invariant identity descriptors of cardiac motion are extracted after the radar signal demodulation. We conduct a pilot study with 78 subjects to evaluate Cardiac Scan in accuracy, authentication time, permanence, evaluation in complex conditions, and vulnerability. Specifically, Cardiac Scan achieves 98.61% balanced accuracy (BAC) and 4.42% equal error rate (EER) in a real-world setup. We demonstrate that Cardiac Scan is a robust and usable continuous authentication system.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117839"}, {"primary_key": "3841119", "vector": [], "sparse_vector": [], "title": "Poster: FooDNet: Optimized On Demand Take-out Food Delivery using Spatial Crowdsourcing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhang", "<PERSON>"], "summary": "This paper builds a Food Delivery Network (FooDNet) that investigates the usage of urban taxis to support on demand take-out food delivery by leveraging spatial crowdsourcing. Unlike existing service sharing systems (e.g., ridesharing), the delivery of food in FooDNet is more time-sensitive and the optimization problem is more complex regarding high-efficiency, huge-number of delivery needs. In particular, we study the food delivery problem in association with the Opportunistic Online Takeout Ordering & Delivery service (O-OTOD). Specifically, the food is delivered incidentally by taxis when carrying passengers in the O-OTOD problem, and the optimization goal is to minimize the number of selected taxis to maintain a relative high incentive to the participated drivers. The two-stage method is proposed to solve the problem, consisting of the construction algorithm and the Large Neighborhood Search (LNS) algorithm. Preliminary experiments based on real-world taxi trajectory datasets verify that our proposed algorithms are effective and efficient.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3131268"}, {"primary_key": "3841121", "vector": [], "sparse_vector": [], "title": "Minding the Billions: Ultra-wideband Localization for Deployed RFID Tags.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "State-of-the-art RFID localization systems fall under two categories. The first category operates with off-the-shelf narrowband RFID tags but makes restrictive assumptions on the environment or the tag's movement patterns. The second category does not make such restrictive assumptions; however, it requires designing new ultra-wideband hardware for RFIDs and uses the large bandwidth to directly compute a tag's 3D location. Hence, while the first category is restrictive, the second one requires replacing the billions of RFIDs already produced and deployed annually. This paper presents RFind, a new technology that brings the benefits of ultra-wideband localization to the billions of RFIDs in today's world. RFind does not require changing today's passive narrowband RFID tags. Instead, it leverages their underlying physical properties to emulate a very large bandwidth and uses it for localization. Our empirical results demonstrate that RFind can emulate over 220MHz of bandwidth on tags designed with a communication bandwidth of only tens to hundreds of kHz, while remaining compliant with FCC regulations. This, combined with a new super-resolution algorithm over this bandwidth, enables RFind to perform 3D localization with sub-centimeter accuracy in each of the x/y/z dimensions, without making any restrictive assumptions on the tag's motion or the environment.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117833"}, {"primary_key": "3841129", "vector": [], "sparse_vector": [], "title": "Spurring Mobile Systems Research Into The Next Decade.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Wireless and mobile systems are becoming an ubiquitous part of everyday life. Some of the most common systems have become commoditized, enabling higher-layer application-driven research while at the same time inhibiting lower-layer research. On the other hand, new frontiers of wireless communications are opening up in various spectrum bands hitherto not used for wireless data communications. This talk will cover the future of wireless systems-driven research and contributions to science & technology from the vantage point of the National Science Foundation.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3130260"}, {"primary_key": "3841132", "vector": [], "sparse_vector": [], "title": "MagneComm: Magnetometer-based Near-Field Communication.", "authors": ["<PERSON><PERSON> Pan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Xiaoyu Ji"], "summary": "Near-field communication (NFC) plays a crucial role in the operation of mobile devices to enhance applications such as payment, social networks, private communication, gaming, and etc. Despite of the convenience, existing NFC standards like ISO-13157 require additional hardware (e.g., loop antenna and dedicated chip) and thereby hindering their wide-scale applications. In this work, we seek to propose a novel near-field communication protocol, MagneComm, which utilizes Magnetic Induction (MI) signals emitted from CPUs and captured by magnetometers on mobile devices for communication. Since CPUs and magnetometers are readily available components in mobile devices, MagneComm eliminates the requirement for special hardware and complements existing near-field communication protocols by providing additional bandwidth. We systematically analyze the characteristics of magnetic signals of CPUs and facilitate MagneComm with one-way communication, full-duplex communication, and multi-transmitter schemes in accordance with the hardware availability on devices. We prototype MagneComm on both laptops and smartphones. Extensive evaluation results show that MagneComm achieves up to 110bps within 10cm.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117824"}, {"primary_key": "3841137", "vector": [], "sparse_vector": [], "title": "RIO: A Pervasive RFID-based Touch Gesture Interface.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we design and develop RIO, a novel battery-free touch sensing user interface (UI) primitive for future IoT and smart spaces. RIO enables UIs to be constructed using off-the-shelf RFID readers and tags, and provides a unique approach to designing smart IoT spaces. With RIO, any surface can be turned into a touch-aware surface by simply attaching RFID tags to them. RIO also supports custom-designed RFID tags, and thus allows specially customized UIs to be easily deployed into a real-world environment. RIO is built using the technique of impedance tracking: when a human finger touches the surface of an RFID tag, the impedance of the antenna changes. This change manifests as a change in the phase of the RFID backscattered signal, and is used by RIO to track fine-grained touch movement over both off-the shelf and custom built tags. We study this impedance behavior in-depth and show how RIO is a reliable UI primitive that is robust even within a multi-tag environment. We leverage this primitive to build a prototype of RIO that can continuously locate a finger during a swipe movement to within 3 mm of its actual position. We also show how custom-design RFID tags can be built and used with RIO, and provide two example applications that demonstrate its real-world use.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117818"}, {"primary_key": "3841138", "vector": [], "sparse_vector": [], "title": "Demo: FROG: Optimizing Power Consumption of Mobile Games Using Perception-Aware Frame Rate Scaling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Changyoung Koh", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A mobile GPU constitutes the majority of power consumption on a mobile device and mobile games top as the most popular class of graphics applications. In this demo, we present FROG, a novel frame rate optimization system for mobile gaming applications. FROG makes use of human visual perception to graphics and regulates application's frame rendering process on-the-fly for maximizing power saving without degrading the user experience. The system works on an existing commercial smartphone and support the legacy gaming applications from app stores without requiring any changes from applications", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3119868"}, {"primary_key": "3841142", "vector": [], "sparse_vector": [], "title": "Poster: RQL: REST Query Language for Converting Firebase to a Mobile Cloud Computing Platform.", "authors": ["Saqib Rasool", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "MCC (Mobile Cloud Computing) performs computation offloading from mobile edge devices to centralized cloud services for achieving the reduction in resource consumption of mobile devices. MBaaS is considered as a new service model within the range of MCC and firebase by Google has been accepted as the most popular MBaaS. However, firebase has not made the efforts for performing computation offloading, as it is expected from an MCC platform. We have contributed in two folds by 1) identifying the limitations of firebase as an MCC platform and by 2) proposing RQL (REST Query Language) as a wrapper over firebase to improve its services for achieving computational offloading. RQL is used for requesting data in the form of JSON instead of using URLs for making REST requests. We have also explained different case studies to evaluate the effectiveness of RQL for converting firebase to an MCC platform.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3131244"}, {"primary_key": "3841144", "vector": [], "sparse_vector": [], "title": "Poster: EasyDefense: Towards Easy and Effective Protection Against Malware for Smartphones.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As the dominant mobile operating system in the markets of smartphones, Android platform is increasingly targeted by attackers. Besides, attackers often produce novel malware to bypass the conventional detection approaches, which are largely reliant on expert analysis to design the discriminative features manually. Therefore, more effective and easy-to-use approaches for detection of Android malware are in demand. In this paper, we design and implement EasyDefense, a lightweight defense system that is integrated with Android OS for easy and effective detection of Android malware utilizing machine learning methods and the ensemble of them. Besides universal static features such as permissions and API calls, EasyDefense also employs the N-gram features of operation codes (opcodes). These N-gram features are extracted and learnt automatically from raw data of applications. Experimental results on 204,650 applications show that users can easily and effectively protect the privacy and security on their smartphones through this system.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3131261"}, {"primary_key": "3841148", "vector": [], "sparse_vector": [], "title": "Poster: X60: A Programmable Testbed for Wideband 60 GHz WLANs with Phased Arrays.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce X60, the first SDR-based testbed for 60 GHz WLANs, featuring fully programmable MAC/PHY/Network layers, multi-Gbps rates, and a user-configurable 12-element phased antenna array. These features provide us with an unprecedented opportunity to revisit the most important aspects of 60 GHz signal propagation and obtain new insights on performance expected from practical 60 GHz systems. X60's unique capabilities make it an ideal platform for experimentation and prototyping across layers.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3131251"}, {"primary_key": "3841150", "vector": [], "sparse_vector": [], "title": "Poster: Can MPTCP Improve Performance for Dual-Band 60 GHz/5 GHz Clients?", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This work conducts one of the first experimental studies of Multipath TCP (MPTCP) in dual-band 60 GHz/5 GHz WLANs using off-the-shelf hardware. We consider both uncoupled and different coupled congestion control algorithms, compare their performance and their potential to improve throughput over single path TCP, and uncover their limitations. In contrast to a recent study that reports reduced throughput with MPTCP compared to single path TCP over 60 GHz, our results show that significant performance improvements are possible, especially in the case of uncoupled congestion control. On the other hand, performance gains with coupled congestion control are lower as these algorithms often fail to fully utilize the capacity of both paths simultaneously. We also observe a pathological case that can lead to significantly reduced throughput with MPTCP regardless of the congestion control algorithm.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3131248"}, {"primary_key": "3841159", "vector": [], "sparse_vector": [], "title": "Poster: A VLC Solution for Smart Parking.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the rapid growth of vehicle ownership, parking has become an issue, especially in metropolitan areas -- the extra time for check-ins, check-outs and finding available parking spaces not only causes frustration and potential road rage on the driver side, but also increases the traffic congestion, gasoline waste and air pollution in consequence. In order to address these problems, the concept of \"smart parking\" is put forward. To make a parking lot \"smart\", we argue that three basic features, namely Vehicle Identification, Parking Space Detection and Indoor Localization are are critical and should be supported by the infrastructure. Herein, we present LightPark, a Visible Light Communication (VLC) solution to realize the vision of \"smart parking\". Building on top of the visible light backscatter communication primitive, LightPark is able to leverage the lighting infrastructure to perform scalable visible light communication and networking with the batter-free tag devices instrumented on the vehicles and parking spaces to manage the critical information such as identification and real-time location of vehicles, and status of parking spaces in a centralized and low-cost manner.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3131245"}, {"primary_key": "3841160", "vector": [], "sparse_vector": [], "title": "Demo: ArgosV3: An Efficient Many-Antenna Platform.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present the third generation of Argos platforms, ArgosV3, intended for real-world applications and research. Developed from scratch specifically for many-antenna MU-MIMO, ArgosV3 is highly efficient in space, power, computation, and cost. While this new platform is highly configurable, featuring FPGA SoCs and frequency agile transceivers capable of operation from 50 MHz to 3.8 GHz, it is also highly compact and power efficient, enabling a complete 160 radio base station in less than 2 cubic feet. ArgosV3 is currently being deployed in a campus-wide multi-cell many-antenna network. For our demonstration we will show a single ArgosV3 base station serving multiple clients using a realtime LTE stack.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3119863"}, {"primary_key": "3841161", "vector": [], "sparse_vector": [], "title": "NutShell: Scalable Whittled Proxy Execution for Low-Latency Web over Cellular Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "Despite much recent progress, Web page latencies over cellular networks remain much higher than those over wired networks. Proxies that execute Web page JavaScript (JS) and push objects needed by the client can reduce latency. However, a key concern is the scalability of the proxy which must execute JS for many concurrent users. In this paper, we propose to scale the proxies, focusing on a design where the proxy's execution is solely to push the needed objects and the client completely executes the page as normal. Such redundant execution is a simple, yet effective approach to cutting network latencies, which dominate page load delays in cellular settings. We develop whittling, a technique to identify and execute in the proxy only the JS code necessary to identify and push the objects required for the client page load, while skipping other code. Whittling is closely related to program slicing, but with the important distinction that it is acceptable to approximate the program slice in the proxy given the client's complete execution. Experiments with top Alexa Web pages show NutShell can sustain, on average, 27\\% more user requests per second than a proxy performing fully redundant execution, while preserving, and sometimes enhancing, the latency benefits.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117827"}, {"primary_key": "3841163", "vector": [], "sparse_vector": [], "title": "Poster: Battery-free Visible Light Sensing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present our efforts to design the first Visible Light Sensing (VLS) system that consumes only tens of μ Ws of power to sense and communicate. Our system requires no modification to the existing light infrastructure and uses unmodulated ambient light as sensing medium. We achieve this by designing a sensing mechanism that uses solar cells to achieve sub-μWs of power consumption. Further, we devise an ultra-low power backscatter based transmission mechanism we call Scatterlight that transmits digital readings without incurring the processing and computation overhead of existing sensors. Based on these principles we build a preliminary prototype. Our initial results demonstrate its ability to sense and communicate three hand gestures at 20 μWs of power.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3131252"}, {"primary_key": "3841164", "vector": [], "sparse_vector": [], "title": "Poster: Improving Multipath Resolution with MIMO Smoothing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Super-resolution subspace methods are popular in estimating multipath parameters such as angle of arrival and time of flight. However, they require decorrelation techniques to resolve coherent multipath components. The conventional decorrelation techniques reduce the effective aperture of the MIMO array, thus reducing the resolution and number of resolved paths. In this paper, we introduce MIMO smoothing as a new technique to bring decorrelation effect by leveraging the spacial and frequential diversity in MIMO transmitters and receivers. Via extensive experiments on WiFi links, we show that MIMO smoothing can increase the accuracy of multipath resolution.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3131243"}, {"primary_key": "3841168", "vector": [], "sparse_vector": [], "title": "WiFi-Assisted 60 GHz Wireless Networks.", "authors": ["Sanjib Sur", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Despite years of innovative research and development, gigabit-speed 60 GHz wireless networks are still not mainstream. The main concern for network operators and vendors is the unfavorable propagation characteristics due to short wavelength and high directionality, which renders the 60 GHz links highly vulnerable to blockage and mobility. However, the advent of multi-band chipsets opens the possibility of leveraging the more robust WiFi technology to assist 60 GHz in order to provide seamless, Gbps connectivity. In this paper, we design and implement MUST, an IEEE 802.11-compliant system that provides seamless, high-speed connectivity over multi-band 60 GHz and WiFi devices. MUST has two key design components: (1) a WiFi-assisted 60 GHz link adaptation algorithm, which can instantaneously predict the best beam and PHY rate setting, with zero probing overhead; and (2) a proactive blockage detection and switching algorithm which can re-direct ongoing user traffic to the robust interface within sub-10 ms latency. Our experiments with off-the-shelf 802.11 hardware show that MUST can achieve 25-60% throughput gain over state-of-the-art solutions, while bringing almost 2 orders of magnitude cross-band switching latency improvement.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117817"}, {"primary_key": "3841169", "vector": [], "sparse_vector": [], "title": "Demo: WiFi-Assisted 60 GHz Wireless Networks.", "authors": ["Sanjib Sur", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Despite years of innovative research and development, multi-Gbps 60 GHz wireless networks are still not mainstream. The unfavorable propagation characteristics due to short wavelength and high directionality, makes the 60 GHz links highly vulnerable to blockage and mobility. However, the advent of multi-band chipsets opens the possibility of leveraging the more robust WiFi technology to assist 60 GHz in order to provide seamless, Gbps connectivity. In this demonstration, we will present MUST, an 802.11-compliant real-time system that provides seamless, high-speed connectivity over multi-band 60 GHz and WiFi devices. MUST has two key design components: (1) a WiFi-assisted 60 GHz link adaptation algorithm, which can instantaneously predict the best beam and PHY rate setting, with zero probing overhead at 60 GHz; and (2) a proactive blockage detection and switching algorithm which can re-direct ongoing user traffic to the robust interface within sub-10 ms latency. We have implemented MUST on off-the-shelf devices where our experiments show high throughput gain and almost 2 orders of magnitude cross-band switching latency improvement over state-of-the-art solutions.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3119870"}, {"primary_key": "3841176", "vector": [], "sparse_vector": [], "title": "Demo: Dynamic Adaptations of WiFi Channel Widths Without TX/RX Coordination.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Most modern standards for wireless communications support physical layer adaptations, in terms of dynamic selection of channel central frequency, transmission power, modulation format, etc., in order to increase link robustness under time-varying propagation and interference conditions. In this demo, we demonstrate that another powerful solution for extending physical layer flexibility in OFDM-based technologies is the dynamic adaptation of the channel width. Although some standards already define the possibility of utilizing multiple channel widths (e.g. 20MHz, 10MHz, 5MHz for IEEE 802.11a standards), such an utilization is limited to a static configuration of a value defined during the network set-up. Conversely, we demonstrate that channel width adaptations can be performed in real-time during network operation, even on a per-packet basis. To this purpose, we propose an innovative and efficient receiver design, which allows the transmitter to take decisions about the channel width without explicitly informing the receiver.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3119861"}, {"primary_key": "3841182", "vector": [], "sparse_vector": [], "title": "Poster: Combating Multipaths to Enable RFID Sensing in Practical Environments.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Jinsong Han", "<PERSON><PERSON><PERSON>"], "summary": "There have been increasing interests in exploring the sensing capabilities of RFID beyond its basic identification task, to reveal more information of tagged objects and the physical world. Phase is a crucial physical feature for many RFID sensing applications, such as tag localization. Most existing methods rely on a low multipath environment for accurate phase measurement. Unfortunately, practical environments are highly likely to be multipath-revalent, especially for indoors. This paper presents the first work to extract \"clean\" phase measurement of RFID tags in multipath-prevalent environments. We propose CPEX (Clean Phase EXtraction) based on theoretical modeling, which is also validated via experimental results of our prototype implementation. We studied the performance of CPEX on tag localization. CPEX can achieve median errors of 4.3-6.4cm (in different setups), which is the most accurate 3D tag localization result in multipath-prevalent environments.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3131258"}, {"primary_key": "3841183", "vector": [], "sparse_vector": [], "title": "Poster: EasyApp: A Widget-based Cross-platform Mobile Development Environment for End-users.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The rapid development of mobile internet attracts end-users to creating mobile applications. The traditional development process cannot meet their needs. In this paper, we present a cross-platform mobile development environment, EasyApp. It provides a highly-integrated, UI-friendly and easily-operating environment. The architecture of this environment is based on OSGi framework. Users could create mobile applications with draggable widgets and package applications for multiple platforms. Native APIs could be invoked with native API plugins.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3131242"}, {"primary_key": "3841185", "vector": [], "sparse_vector": [], "title": "TagScan: Simultaneous Target Imaging and Material Identification with Commodity RFID Devices.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Xiao<PERSON> Chen", "Hongbo Jiang", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Target imaging and material identification play an important role in many real-life applications. This paper introduces TagScan, a system that can identify the material type and image the horizontal cut of a target simultaneously with cheap commercial off the-shelf (COTS) RFID devices. The key intuition is that different materials and target sizes cause different amounts of phase and RSS (Received Signal Strength) changes when radio frequency (RF) signal penetrates through the target. Multiple challenges need to be addressed before we can turn the idea into a functional system including (i) indoor environments exhibit rich multipath which breaks the linear relationship between the phase change and the propagation distance inside a target; (ii) without knowing either material type or target size, trying to obtain these two information simultaneously is challenging; and (iii) stitching pieces of the propagation distances inside a target for an image estimate is non-trivial. We propose solutions to all the challenges and evaluate the system's performance in three different environments. TagScan is able to achieve higher than 94% material identification accuracies for 10 liquids and differentiate even very similar objects such as Coke and Pepsi. TagScan can accurately estimate the horizontal cut images of more than one target behind a wall.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117830"}, {"primary_key": "3841187", "vector": [], "sparse_vector": [], "title": "Pose Information Assisted 60 GHz Networks: Towards Seamless Coverage and Mobility Support.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "60 GHz millimeter-wave networking has emerged as the next frontier technology to provide multi-Gbps wireless connectivity. However, the intrinsic directionality and limited field-of-view of 60 GHz antennas make the links extremely sensitive to user mobility and orientation change. Hence, seamless coverage, even at room level, becomes challenging. In this paper, we propose Pia, a robust 60 GHz network architecture that can provide seamless coverage and mobility support at multi-Gbps bitrate. Pia comprises multiple cooperating access points (APs). It leverages the pose information on mobile clients to proactively select the AP and manage multi-link spatial reuse. These decisions require a model of the pose/location of the APs and ambient reflectors. We address these challenges through a set of AP-pose sensing and compressive angle estimation algorithms that fuse the pose measurement with link quality measurement on the client. We have implemented Pia using commodity 60 GHz platforms. Our experiments show that Pia reduces the occurrence of link outage by 6.3x and improves the spatial sharing capacity by 76%, compared to conventional schemes that only use in-band information for adaptation.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117832"}, {"primary_key": "3841188", "vector": [], "sparse_vector": [], "title": "Poster: RECO: A Reconfigurable Core Network for Future 5G Communication Systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "It is envisioned in the future that not only smartphones will connect to cellular networks, but all kinds of different wearable devices, sensors, vehicles, home appliances, VR headsets, robots, will also connect to cellular networks. Because the characteristics of these devices differ largely, people argue that future 5G systems should be designed to elastically accommodate to the different users. In this paper, we propose a reconfigurable core network called RECO that demonstrates how to implement customized virtual core network entities efficiently to suit for users with different characteristics. We then build a testbed to verify our proposed RECO architecture. Finally, we will open source our RECO in the near future so that the research community can take benefit out of it.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3131257"}, {"primary_key": "3841189", "vector": [], "sparse_vector": [], "title": "Poster: Smart RF Table Enables IoT on a Desk.", "authors": ["<PERSON><PERSON>", "Caihua Li", "<PERSON><PERSON><PERSON>"], "summary": "IoT has long been a hot topic, and many researchers have made great efforts especially in wireless charging and communication between smart furniture, recognization and localization. However, people always divide charging, communicating and sensing apart. In our paper, we put forward a simple but practical method to make use of NFC communication and resonance-type WPT to build up a system which can be used to locate, identify, charge devices while providing a platform for objects to communicate with each other. Our system acts as an intelligent controller, and it links all devices registered to it together to form a scalable network based on common furniture in daily life.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3131246"}, {"primary_key": "3841190", "vector": [], "sparse_vector": [], "title": "The Tick Programmable Low-Latency SDR System.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Tick is a new SDR system that provides programmability and ensures low latency at both PHY and MAC. It supports modular design and element-based programming, similar to the Click router framework [23]. It uses an accelerator-rich architecture, where an embedded processor executes control flows and handles various MAC events. User-defined accelerators offload those tasks, which are either computation-intensive or communication-heavy, or require fine-grained timing control, from the processor, and accelerate them in hardware. Tick applies a number of hardware and software co-design techniques to ensure low latency, including multi-clock-domain pipelining, field-based processing pipeline, separation of data and control flows, etc. We have implemented Tick and validated its effectiveness through extensive evaluations as well as two prototypes of 802.11ac SISO/MIMO and 802.11a/g full-duplex.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117834"}, {"primary_key": "3841192", "vector": [], "sparse_vector": [], "title": "Poster: Enabling Secure Location Authentication in Drone.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> Liye", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the popularity of commodity drones in a wide variety of applications, significant security issues have been raised. One of the major problems is that a legal drone may be illegally hijacked by GPS spoofing attacks. Though some GPS anti-spoofing techniques have been proposed, no effective technique has been implemented in commodity drones yet due to practical limitations. Motivated by the ubiquitous WiFi signals around us, we propose WiDrone, a WiFi fingerprint location cross-check based anti-hijacking system on commodity drones in this poster. WiDrone still relies on GPS for navigation but it will authenticate the destination by comparing current WiFi fingerprint (CWF) with the destination WiFi fingerprint (DWF) when the drone receives a landing order. Furthermore, we propose a WiFi fingerprint authentication algorithm to decide whether CWF matches DWF. We have designed and implemented the prototype of WiDrone on DJI Matrice 100 to ascertain the practicability of proposed system.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3131259"}, {"primary_key": "3841193", "vector": [], "sparse_vector": [], "title": "Demo: UIWear: Easily Adapting User Interfaces for Wearable Devices.", "authors": ["<PERSON><PERSON>", "Qingqing Cao", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Wearable devices, such as smart watches, offer exciting new opportunities for users to interact with their applications. The current state of the art for wearable devices is for a developer to write a custom {\\em companion app}, which is a variant of the smartphone app, tailored to the wearable form factor. A developer puts a non-trivial amount of effort to write these companion apps and the programming model does not scale to an increasing diversity of form factors. In this demo, we show a working prototype of our system UIWear that allows a developer to easily extend a smartphone application to other wearable interfaces. Our system, UIWear, extracts the application GUI as a UI tree, which preserves the semantics of the GUI. The developer (or the user) only writes a {\\em metaprogram} to encode the GUI design for the wearable device; no effort is needed beyond the design phase. UIWear executes the metaprogram by performing all the underlying tasks to virtualize the application GUI, adapt it, and recreate it on the wearable. A metaprogram can create the same functionality as existing companion apps with an order-of-magnitude less programming effort.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3124769"}, {"primary_key": "3841194", "vector": [], "sparse_vector": [], "title": "UIWear: Easily Adapting User Interfaces for Wearable Devices.", "authors": ["<PERSON><PERSON>", "Qingqing Cao", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Wearable devices such as smartwatches offer exciting new opportunities for users to interact with their applications. However, the current wearable programming model requires the developer to write a custom companion app for each wearable form factor; the companion app extends the smartphone display onto the wearable, relays user interactions from the wearable to the phone, and updates the wearable display as needed. The development effort required to write a companion app is significant and will not scale to an increasing diversity of form factors. This paper argues for a different programming model for wearable devices. The developer writes an application for the smartphone, but only specifies a UI design for the wearable. Our UIWear system abstracts a logical model of the smartphone GUI, re-tailors the GUI for the wearable device based on the specified UI design, and compiles it into a companion app that we call the UICompanion app. We implemented UIWear on Android smartphones, AndroidWear smartwatches, and Sony SmartEyeGlasses. We evaluate 20 developer-written companion apps from the AndroidWear category on Google Play against the UIWear-created UICompanion apps. The lines-of-code required for the developer to specify the UI design in UIWear is an order-of-magnitude smaller compared to the companion app lines-of-code. Further, in most cases, the UICompanion app performed comparably or better than the corresponding companion app both in terms of qualitative metrics, including latency and energy, and quantitative metrics, including look-and-feel.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117819"}, {"primary_key": "3841195", "vector": [], "sparse_vector": [], "title": "PassiveVLC: Enabling Practical Visible Light Backscatter Communication for Battery-free IoT Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper investigates the feasibility of practical backscatter communication using visible light for battery-free IoT applications. Based on the idea of modulating the light retroreflection with a commercial LCD shutter, we effectively synthesize these off-the-shelf optical components into a sub- mW low power visible light passive transmitter along with a retroreflecting uplink design dedicated for power constrained mobile/IoT devices. On top of that, we design, implement and evaluate PassiveVLC, a novel visible light backscatter communication system. PassiveVLC system enables a battery-free tag device to perform passive communication with the illuminating LEDs over the same light carrier and thus offers several favorable features including battery-free, sniff-proof, and biologically friendly for human-centric use cases. Experimental results from our prototyped system show that PassiveVLC is flexible with tag orientation, robust to ambient lighting conditions, and can achieve up to 1 kbps uplink speed. Link budget analysis and two proof-of-concept applications are developed to demonstrate PassiveVLC's efficacy and practicality.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117843"}, {"primary_key": "3841197", "vector": [], "sparse_vector": [], "title": "NICScatter: Backscatter as a Covert Channel in Mobile Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Today's mobile devices contain sensitive data, which raises concerns about data security. This paper discusses a covert channel threat on existing mobile systems. Through it, malware can wirelessly leak information without making network connections or emitting signals, such as sound, EMR, vibration, etc., that we can feel or are aware of. The covert channel is built on a communication method that we call NICScatter. NICScatter transmitter malware forces mobile devices, such as mobile phones, tablets or laptops, to reflect surrounding RF signals to covertly convey information. The operation is achieved by controlling the impedance of a device's wireless network interface card (NIC). Importantly, the operation requires no special privileges on current mobile OSs, which allows the malware to stealthily pass sensitive data to an attacker's nearby mobile device, which can then decode the signal and thus effectively gather the guarded data. Our experiments with different mobile devices show that the covert channel can achieve 1.6 bps and transmit as far as 2 meters. In a through-the-wall scenario, it can transmit up to 70 cm.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117814"}, {"primary_key": "3841198", "vector": [], "sparse_vector": [], "title": "Analog On-Tag Hashing: Towards Selective Reading as Hash Primitives in Gen2 RFID Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Lin", "<PERSON><PERSON>", "Z<PERSON><PERSON> An"], "summary": "Deployment of billions of Commercial Off-The-Shelf (COTS) RFID tags has drawn much of the attention of the research community because of the performance gaps of current systems. In particular, hash-enabled protocol (HEP) is one of the most thoroughly studied topics in the past decade. HEPs are designed for a wide spectrum of notable applications (e.g., missing detection) without need to collect all tags. HEPs assume that each tag contains a hash function, such that a tag can select a random but predicable time slot to reply with a one-bit presence signal that shows its existence. However, the hash function has never been implemented in COTS tags in reality, which makes HEPs a 10-year untouchable mirage. This work designs and implements a group of analog on-tag hash primitives (called Tash) for COTS Gen2-compatible RFID systems, which moves prior HEPs forward from theory to practice. In particular, we design three types of hash primitives, namely, tash function, tash table function and tash operator. All of these hash primitives are implemented through selective reading, which is a fundamental and mandatory functionality specified in Gen2 protocol, without any hardware modification and fabrication. We further apply our hash primitives in two typical HEP applications (i.e., cardinality estimation and missing detection) to show the feasibility and effectiveness of Tash. Results from our prototype, which is composed of one ImpinJ reader and 3,000 Alien tags, demonstrate that the new design lowers 60% of the communication overhead in the air. The tash operator can additionally introduce an overhead drop of 29.7%.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117835"}, {"primary_key": "3841199", "vector": [], "sparse_vector": [], "title": "ReflexCode: Coding with Superposed Reflection Light for LED-Camera Communication.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "As a popular approach to implementing Visible Light Communication (VLC) on commercial-off-the-shelf devices, LED-Camera VLC has attracted substantial attention recently. While such systems initially used reflected light as the communication media, direct light becomes the dominant media for the purpose of combating interference. Nonetheless, the data rate achievable by direct light LED-Camera VLC systems has hit its bottleneck: the dimension of the transmitters. In order to further improve the performance, we revisit the reflected light approach and we innovate in converting the potentially destructive interferences into collaborative transmissions. Essentially, our ReflexCode system codes information by superposing light emissions from multiple transmitters. It combines traditional amplitude demodulation with slope detection to \"decode\" the grayscale modulated signal, and it tunes decoding thresholds dynamically depending on the spatial symbol distribution. In addition, ReflexCode re-engineers the balanced codes to avoid flicker from individual transmitters. We implement ReflexCode as two prototypes and demonstrate that it can achieve a throughput up to 3.2kb/s at a distance of 3m.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117836"}, {"primary_key": "3841200", "vector": [], "sparse_vector": [], "title": "Demo: Coding with Superposed Reflection Light for LED-Camera Communication.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "As a popular approach to implementing Visible Light Communication (VLC) on commercial-off-the-shelf devices, LED-Camera VLC has attracted substantial attention recently. While such systems initially used reflected light as the communication media, direct light becomes the dominant media for the purpose of combating interference. Nonetheless, the data rate achievable by direct light LED-Camera VLC systems has hit its bottleneck: the dimension of the transmitters. In this demo, we revisit the reflected light approach and propose a novel modulation mechanism, ReflexCode, which converts the potentially destructive interferences into collaborative transmissions. Essentially, our ReflexCode system codes information by superposing light emissions from multiple transmitters. It combines traditional amplitude demodulation with slope detection to \"decode\" the grayscale modulated signal, and it tunes decoding thresholds dynamically depending on the spatial symbol distribution. In addition, ReflexCode re-engineers the balanced codes to avoid flicker from individual transmitters. We implement ReflexCode as a prototype and demonstrate that it can achieve a promising throughput.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3124770"}, {"primary_key": "3841202", "vector": [], "sparse_vector": [], "title": "Demo: Stuffing Wi-Fi Beacons for Fun and Profit.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Ubiquitous Wi-Fi access points (APs) constantly send out beacon and probe response frames to inform potential stations (STAs) about their existence. Beacon and probe responses can be extended by adding additional information (so-called beacon stuffing) making it possible to deliver this information to mobile devices (e.g. smart-phones equipped with 802.11 interfaces) without the need for association with the local infrastructure. This feature can be used to support location-based information services (LBS) related for e.g. advertising local opportunities, temporary obstacles and traffic disturbances or emergency notifications. In this paper we introduce a demonstrator for Location-based Wi-Fi Services (LoWS) based on beacon stuffing. The LoWS system can be easily installed within an existent 802.11 infrastructure while the receiver application can be installed on nearly all up-to-date commercial off-the-shelf smartphones, e.g. based on Android or iOS.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3119851"}, {"primary_key": "3841203", "vector": [], "sparse_vector": [], "title": "Pulsar: Towards Ubiquitous Visible Light Localization.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The past decade's research in visible light positioning (VLP) has led to technologies with high location precision. However, existing VLP systems either require specialized LEDs which hinder large-scale deployment, or need cameras which preclude continuous localization because of high power consumption and short coverage. In this paper, we propose Pulsar, which uses a compact photodiode sensor, readily fit into a mobile device, to discriminate existing ceiling lights---either fluorescent lights or conventional LEDs---based on their intrinsic optical emission features. To overcome the photodiode's lack of spatial resolution, we design a novel sparse photogrammetry mechanism, which resolves the light source's angle-of-arrival, and triangulates the device's 3D location and even orientation. To facilitate ubiquitous deployment, we further develop a light registration mechanism that automatically registers the ceiling lights' locations as landmarks on a building's floor map. Our experiments demonstrate that Pulsar can reliably achieve decimeter precision in both controlled environment and large-scale buildings.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117821"}, {"primary_key": "3841205", "vector": [], "sparse_vector": [], "title": "Poster: An Efficient Control Framework for Supporting the Future SDN/NFV-enabled Satellite Network.", "authors": ["<PERSON><PERSON><PERSON>", "Baokang Zhao", "Wanrong Yu", "Chunqing Wu"], "summary": "Control framework design is critical to future SDN/NFV-enabled satellite network. However, the current multi-layer constellation based solutions have drawbacks in terms of availability, latency, openness, etc. The key contribution in this work is the study of an efficient control framework, which logically consists of two parts: entity part and overlay part. The entity part is a novel heterogeneous single-layer LEO satellite network, while the overlay part implements a virtualized overlay network. We have implemented a lightweight prototype of our framework and compared it with a GEO satellite based solution (i.e. OpenSAN). We demonstrate proof-of-concept that our framework performs better than OpenSAN with respect to control latency and avoids potential system bottleneck.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3131264"}, {"primary_key": "3841206", "vector": [], "sparse_vector": [], "title": "Stateful Inter-Packet Signal Processing for Wireless Networking.", "authors": ["Shang<PERSON> Zhao", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Traditional signal processing design (e.g., frequency offset and channel estimation) at a receiver treats each packet arrival as an independent process to facilitate decoding and interpreting packet data. In this paper, we enhance the performance of this process in the wireless network domain. We propose STAteful inter-Packet signaL procEssing (STAPLE), a framework of stateful signal processing residing between the physical and link layers. STAPLE transforms the signal processing procedure into a lightweight stateful process that caches in a small-sized memory table physical and link layer header fields as packet state information. The similarity of such information among packets serves as prior knowledge to further enhance the reliability of signal processing and thus improve the wireless network performance. We implement STAPLE on USRP X300-series devices with adapted configurations for 802.11a/b/g/n/ac and 802.15.4. The STAPLE prototype is of low processing complexity and does not change any wireless standard specification. Comprehensive experimental results show that the benefit from STAPLE is universal in various wireless networks.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117846"}, {"primary_key": "3841207", "vector": [], "sparse_vector": [], "title": "Demo: Acoustic Sensing Based Indoor Floor Plan Construction Using Smartphones.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This demo presents BatMapper, an acoustics sensing technology for fast, fine-grained and low cost floor plan construction. BatMapper operates by emitting sound signal and capturing its reflections by two microphones on smartphones. We develop robust probabilistic echo-object association and outlier removal algorithms to identify the correspondence between distances and objects, thus the geometry of corridors. We compensate minute hand sway movements to identify small surface recessions, thus detecting doors automatically. Additionally, we leverage structure cues in indoor environments for user trace calibration. The demo will enable any person to hold the smartphone and walk along a corridor to map the corridor shape and detect doors in real-time.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3119864"}, {"primary_key": "3841208", "vector": [], "sparse_vector": [], "title": "Automating Visual Privacy Protection Using a Smart LED.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The ubiquity of mobile camera devices has been triggering an outcry of privacy concerns, whereas privacy protection still relies on the cooperation of the photographer or camera hardware, which can hardly be guaranteed in practice. In this paper, we introduce LiShield, which automatically protects a physical scene against photographing, by illuminating it with smart LEDs flickering in specialized waveforms. We use a model-driven approach to optimize the waveform, so as to ensure protection against the (uncontrollable) cameras and potential image-processing based attacks. We have also designed mechanisms to unblock authorized cameras and enable graceful degradation under strong ambient light interference. Our prototype implementation and experiments show that LiShield can effectively destroy unauthorized capturing while maintaining robustness against potential attacks.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3117820"}, {"primary_key": "3841209", "vector": [], "sparse_vector": [], "title": "Demo: LiShield: Privacy Protection of Physical Environment Against Photographing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The ubiquity of mobile camera devices has been triggering an outcry of privacy concerns, whereas privacy protection still relies on the compliance of the photographer or camera hardware, which can hardly be guaranteed in practice. In this demo, we introduce LiShield, which automatically protects a physical scene against photographing, by illuminating it with smart LEDs flickering in a specialized waveform. We use a model-driven approach to optimize the waveform, so as to ensure protection against the (uncontrollable) cameras and potential image-processing based attacks. We have also designed mechanisms to unblock authorized cameras and enable graceful degradation under strong ambient light interference. This demo will show our prototype implementation, with simple on-site experiments that demonstrate how LiShield effectively destroys unauthorized photo capturing.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3119867"}, {"primary_key": "3841212", "vector": [], "sparse_vector": [], "title": "Poster: WiFi-based Device-Free Human Activity Recognition via Automatic Representation Learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Weixi Gu", "Li<PERSON> Xie", "<PERSON><PERSON>"], "summary": "Existing human activity recognition approaches require either the deployment of extra infrastructure or the cooperation of occupants to carry dedicated devices, which are expensive, intrusive and inconvenient for pervasive implementation. In this paper, we propose SmartSense, a device-free human activity recognition system based on a novel machine learning algorithm with existing commercial off-the-shelf (COTS) WiFi routers. By exploiting the prevalence of existing WiFi infrastructure in buildings, we developed a novel OpenWrt based firmware for COTS WiFi routers to collect the CSI measurements from regular data frames. To identify different human activities, an automatic kernel representation learning method, namely auto-HSRL, is established to selection informative Hilbert space patterns from time, frequency, wavelet, and shape domains. A new information fusion tool based on multi-view kernel learning is proposed to combine the representations extracted from diverse perspectives and build up a robust and comprehensive activity classifier. Extensive experiments were conducted in an office and the experimental results demonstrate that SmartSense outperforms existing methods and achieves a 98% activity recognition accuracy.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811.3131266"}, {"primary_key": "3884556", "vector": [], "sparse_vector": [], "title": "Proceedings of the 23rd Annual International Conference on Mobile Computing and Networking, MobiCom 2017, Snowbird, UT, USA, October 16 - 20, 2017", "authors": ["<PERSON><PERSON> van der Merwe", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Welcome to ACM MobiCom '17, the 23nd Annual International Conference on Mobile Computing and Networking. MobiCom is the premier forum for cutting-edge research in mobile systems and wireless networks. This year's technical program features 35 outstanding papers, covering a wide variety of topics: Wireless access and backhaulCellular systemsLight communicationNear field communicationLocalizationRFIDWireless privacy and securityIoT and wearablesData managementVirtual realityMobile applications and Web We again had a heavy PC (30 members) and a light PC (30 members). The two-tiered PC balanced the goals of broader inclusivity and running an effective TPC meeting. The TPC was made up of experts from diverse research areas, and TPC selection also took into account gender, seniority, institution, country, expertise, and academic or industrial background. The TPC included researchers from 10 countries: France, India, Italy, Singapore, South Korea, Sweden, Switzerland, Taiwan, U.K., and the U.S. Twenty percent of the TPC members were female. The TPC also had broad industry participation, with members from Cisco, Google, IBM, Microsoft, and NEC. The paper review process was double-blind. It was carried out in three phases: First, each paper was reviewed by at least three TPC members, and the top 107 papers were advanced to the second phase. Review scores, in addition to reviewer confidence and normalization with respect to other papers by the same reviewer, were considered in the selection process. In the second phase, each paper was reviewed by at least two more reviewers, followed by an online discussion; that produced 68 papers for the TPC meeting on June 1 and 2 in Seattle, which was the final phase. These 68 papers were discussed at length at the meeting. Eventually, 35 papers were conditionally accepted into the program. The heavy PC participated in all three phases; the light TPC participated only in the first phase. Each of the 35 papers was assigned a shepherd from the heavy PC. Shepherds' identities were provided to authors, unlike in the previous year. Reviewers remained anonymous. Shepherds coordinated with reviewers to address the review comments and they approved the final versions for publication. The result of this process is a strong and diverse technical program consisting of 35 high-quality papers.", "published": "2017-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3117811"}]