import { createOpenAI, OpenAIProviderSettings } from '@ai-sdk/openai';
import { experimental_wrapLanguageModel as wrapLanguageModel } from 'ai';

import { customMiddleware } from './custom-middleware';

export const customModel = (apiIdentifier: string) => {

  // 创建 API Key 映射
  const apiKeyMap = {
    'gemini': [process.env.OPENAI_API_KEY_DF, process.env.OPENAI_BASE_URL],
    'gpt': [process.env.OPENAI_API_KEY_DF, process.env.OPENAI_BASE_URL],
    'o1': [process.env.OPENAI_API_KEY_DF, process.env.OPENAI_BASE_URL],
    'claude': [process.env.OPENAI_API_KEY_DF, process.env.OPENAI_BASE_URL],
    'glm': [process.env.OPENAI_API_KEY_GLM, process.env.OPENAI_BASE_URL_GLM],
    'qwen': [process.env.OPENAI_API_KEY_SC, process.env.OPENAI_BASE_URL_SC],
    'qwq': [process.env.OPENAI_API_KEY_SC, process.env.OPENAI_BASE_URL_SC],
    'deepseek': [process.env.OPENAI_API_KEY_SC, process.env.OPENAI_BASE_URL_SC],
  };

  // 根据 apiIdentifier 选择对应的 API Key
  let base_url = process.env.OPENAI_BASE_URL;
  let selectedApiKey = process.env.OPENAI_API_KEY_AZ; // 默认使用 AZ key
  for (const [key, value] of Object.entries(apiKeyMap)) {
    if (apiIdentifier.toLowerCase().includes(key)) {
      selectedApiKey = value[0]!; 
      base_url = value[1]!;
      break;
    }
  }

  // 创建自定义配置
  const settings: OpenAIProviderSettings = {
    baseURL: base_url,
    apiKey: selectedApiKey,
    compatibility: 'compatible', 
  };

  // 使用配置创建新的 provider
  const customOpenAI = createOpenAI(settings);

  // 使用自定义 provider 创建模型
  // const model = customOpenAI(apiIdentifier);
  const model = customOpenAI(apiIdentifier);

  return wrapLanguageModel({
    model,
    middleware: customMiddleware,
  });
};
