[{"primary_key": "4096669", "vector": [], "sparse_vector": [], "title": "Popular Conjectures as a Barrier for Dynamic Planar Graph Algorithms.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The dynamic shortest paths problem on planar graphs asks us to preprocess a planar graph G such that we may support insertions and deletions of edges in G as well as distance queries between any two nodes u, v subject to the constraint that the graph remains planar at all times. This problem has been extensively studied in both the theory and experimental communities over the past decades. The best known algorithm performs queries and updates in Õ(n2/3) time, based on ideas of a seminal paper by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON> [FOCS'01]. A (1+ε)-approximation algorithm of <PERSON> et al. [STOC'12] performs updates and queries in Õ(√n) time. An algorithm with a more practical O(polylog(n)) runtime would be a major breakthrough. However, such runtimes are only known for a (1+ε)-approximation in a model where only restricted weight updates are allowed due to <PERSON> et al. [SODA'16], or for easier problems like connectivity. In this paper, we follow a recent and very active line of work on showing lower bounds for polynomial time problems based on popular conjectures, obtaining the first such results for natural problems in planar graphs. Such results were previously out of reach due to the highly non-planar nature of known reductions and the impossibility of \"planarizing gadgets\". We introduce a new framework which is inspired by techniques from the literatures on distance labelling schemes and on parameterized complexity. Using our framework, we show that no algorithm for dynamic shortest paths or maximum weight bipartite matching in planar graphs can support both updates and queries in amortized O(n1/2-ε) time, for any ε>0, unless the classical all-pairs-shortest-paths problem can be solved in truly subcubic time, which is widely believed to be impossible. We extend these results to obtain strong lower bounds for other related problems as well as for possible trade-offs between query and update time. Interestingly, our lower bounds hold even in very restrictive models where only weight updates are allowed.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.58"}, {"primary_key": "4096670", "vector": [], "sparse_vector": [], "title": "On Fully Dynamic Graph Sparsifiers.", "authors": ["<PERSON><PERSON> Abraham", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We initiate the study of fast dynamic algorithms for graph sparsification problems and obtain fully dynamic algorithms, allowing both edge insertions and edge deletions, that take polylogarithmic time after each update in the graph. Our three main results are as follows. First, we give a fully dynamic algorithm for maintaining a (1 ± ϵ)-spectral sparsifier with amortized update time poly(log n, ϵ -1 ). Second, we give a fully dynamic algorithm for maintaining a (1 ± ϵ)-cut sparsifier with worst-case update time poly(log n, ϵ -1 ). Both sparsifiers have size n · poly(log n, ϵ -1 ). Third, we apply our dynamic sparsifier algorithm to obtain a fully dynamic algorithm for maintaining a (1 - ϵ)-approximation to the value of the maximum flow in an unweighted, undirected, bipartite graph with amortized update time poly(log n, ϵ -1 ).", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.44"}, {"primary_key": "4096671", "vector": [], "sparse_vector": [], "title": "Polynomial Representations of Threshold Functions and Algorithmic Applications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We design new polynomials for representing threshold functions in three different regimes: probabilistic polynomials of low degree, which need far less randomness than previous constructions, polynomial threshold functions (PTFs) with \"nice\" threshold behavior and degree almost as low as the probabilistic polynomials, and a new notion of probabilistic PTFs where we combine the above techniques to achieve even lower degree with similar \"nice\" threshold behavior. Utilizing these polynomial constructions, we design faster algorithms for a variety of problems: · Offline Hamming Nearest (and Furthest) Neighbors: Given n red and n blue points in d-dimensional Hamming space for d = c log n, we can find an (exact) nearest (or furthest) blue neighbor for every red point in randomized time n 2-1 /O(√clog 2/3 c) or deterministic time n 2-1/O(c log2 c) . These improve on a randomized n 2-1/O(c log2 c) bound by <PERSON><PERSON> and <PERSON> (FOCS'15), and also lead to faster MAX-SAT algorithms for sparse CNFs. · Offline Approximate Nearest (and Furthest) Neighbors: Given n red and n blue points in d-dimensional ℓ 1 or Euclidean space, we can find a (1+ε)-approximate nearest (or furthest) blue neighbor for each red point in randomized time near dn+n 2-Ω(ε1/3/log(1/ε)) . This improves on an algorithm by Valiant (FOCS'12) with randomized time near dn+n 2-Ω(√ε) , which in turn improves previous methods based on locality-sensitive hashing. · SAT Algorithms and Lower Bounds for Circuits With Linear Threshold Functions: We give a satisfiability algorithm for AC 0 [m] o LTF LTF circuits with a subquadratic number of LTF gates on the bottom layer, and a subexponential number of gates on the other layers, that runs in deterministic 2 n-n ε time. This strictly generalizes a SAT algorithm for ACC 0 oLTF circuits of subexponential size by Williams (STOC'14) and also implies new circuit lower bounds for threshold circuits, improving a recent gate lower bound of Kane and Williams (STOC'16). We also give a randomized 2 n-n ε -time SAT algorithm for subexponential-size MAJ o AC 0 oLTF o AC 0 oLTF circuits, where the top MAJ gate and middle LTF gates have O(n 6/5-δ ) fan-in.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.57"}, {"primary_key": "4096672", "vector": [], "sparse_vector": [], "title": "Separations in Communication Complexity Using Cheat Sheets and Information Complexity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While exponential separations are known between quantum and randomized communication complexity for partial functions (<PERSON><PERSON>, <PERSON> 1999), the best known separation between these measures for a total function is quadratic, witnessed by the disjointness function. We give the first super-quadratic separation between quantum and randomized communication complexity for a total function, giving an example exhibiting a power 2.5 gap. We further present a 1.5 power separation between exact quantum and randomized communication complexity, improving on the previous ≅ 1.15 separation by <PERSON><PERSON><PERSON><PERSON> (STOC 2013). Finally, we present a nearly optimal quadratic separation between randomized communication complexity and the logarithm of the partition number, improving upon the previous best power 1.5 separation due to <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Our results are the communication analogues of separations in query complexity proved using the recent cheat sheet framework of <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> (STOC 2016). Our main technical results are randomized communication and information complexity lower bounds for a family of functions, called lookup functions, that generalize and port the cheat sheet framework to communication complexity.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.66"}, {"primary_key": "4096673", "vector": [], "sparse_vector": [], "title": "Online Algorithms for Covering and Packing Problems with Convex Objectives.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "T.<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present online algorithms for covering and packing problems with (non-linear) convex objectives. The convex covering problem is defined as: min xϵ R + n f(x) s.t. Ax ≥ 1, where f:R + n → R + is a monotone convex function, and A is an m×n matrix with non-negative entries. In the online version, a new row of the constraint matrix, representing a new covering constraint, is revealed in each step and the algorithm is required to maintain a feasible and monotonically non-decreasing assignment x over time. We also consider a convex packing problem defined as: max yϵR+ m Σ j=1 m yj - g(A T y), where g:R + n →R + is a monotone convex function. In the online version, each variable yj arrives online and the algorithm must decide the value of yj on its arrival. This represents the Fenchel dual of the convex covering program, when g is the convex conjugate of f. We use a primal-dual approach to give online algorithms for these generic problems, and use them to simplify, unify, and improve upon previous results for several applications.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.24"}, {"primary_key": "4096674", "vector": [], "sparse_vector": [], "title": "A Discrete and Bounded Envy-Free Cake Cutting Protocol for Any Number of Agents.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We consider the well-studied cake cutting problem in which the goal is to find an envy-free allocation based on queries from n agents. The problem has received attention in computer science, mathematics, and economics. It has been a major open problem whether there exists a discrete and bounded envy-free protocol. We resolve the problem by proposing a discrete and bounded envy-free protocol for any number of agents. The maximum number of queries required by the protocol is nnnnnn. Even if we do not run our protocol to completion, it can find in at most n n+1 queries an envy-free partial allocation of the cake in which each agent gets at least 1/n of the value of the whole cake.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.52"}, {"primary_key": "4096675", "vector": [], "sparse_vector": [], "title": "Which Regular Expression Patterns Are Hard to Match?", "authors": ["Arturs Backurs", "<PERSON><PERSON><PERSON>"], "summary": "Regular expressions constitute a fundamental notion in formal language theory and are frequently used in computer science to define search patterns. In particular, regular expression matching and membership testing are widely used computational primitives, employed in many programming languages and text processing utilities. A classic algorithm for these problems constructs and simulates a non-deterministic finite automaton corresponding to the expression, resulting in an O(m n) running time (where m is the length of the pattern and n is the length of the text). This running time can be improved slightly (by a polylogarithmic factor), but no significantly faster solutions are known. At the same time, much faster algorithms exist for various special cases of regular expressions, including dictionary matching, wildcard matching, subset matching, word break problem etc. In this paper, we show that the complexity of regular expression matching can be characterized based on its depth (when interpreted as a formula). Our results hold for expressions involving concatenation, OR, Kleene star and Kleene plus. For regular expressions of depth two (involving any combination of the above operators), we show the following dichotomy: matching and membership testing can be solved in near-linear time, except for \"concatenations of stars\", which cannot be solved in strongly sub-quadratic time assuming the Strong Exponential Time Hypothesis (SETH). For regular expressions of depth three the picture is more complex. Nevertheless, we show that all problems can either be solved in strongly sub-quadratic time, or cannot be solved in strongly sub-quadratic time assuming SETH. An intriguing special case of membership testing involves regular expressions of the form \"a star of an OR of concatenations\", e.g., [a|ab|bc]*. This corresponds to the so-called word break problem, for which a dynamic programming algorithm with a runtime of (roughly) O(n √m) is known. We show that the latter bound is not tight and improve the runtime to O(n m 0.44... ).", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.56"}, {"primary_key": "4096676", "vector": [], "sparse_vector": [], "title": "An Algorithm for Komlós Conjecture Matching Banaszczyk&apos;s Bound.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Garg"], "summary": "We consider the problem of finding a low discrepancy coloring for sparse set systems where each element lies in at most t sets. We give an efficient algorithm that finds a coloring with discrepancy O((t log n) 1/2 ), matching the best known non-constructive bound for the problem due to <PERSON><PERSON><PERSON><PERSON><PERSON>. The previous algorithms only achieved an O(t 1/2 log n) bound. Our result also extends to the more general Ko<PERSON>lós setting and gives an algorithmic O(log 1/2 n) bound.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.89"}, {"primary_key": "4096677", "vector": [], "sparse_vector": [], "title": "A Nearly Tight Sum-of-Squares Lower Bound for the Planted Clique Problem.", "authors": ["<PERSON>az <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We prove that with high probability over the choice of a random graph G from the Erdös-<PERSON> distribution G(n,1/2), the n O(d) -time degree d Sum-of-Squares semidefinite programming relaxation for the clique problem will give a value of at least n 1/2-c(d/log n)1/2 for some constant c > 0. This yields a nearly tight n 1/2-o(1) bound on the value of this program for any degree d = o(log n). Moreover we introduce a new framework that we call pseudo-calibration to construct Sum-of-Squares lower bounds. This framework is inspired by taking a computational analogue of Bayesian probability theory. It yields a general recipe for constructing good pseudo-distributions (i.e., dual certificates for the Sum-of-Squares semidefinite program), and sheds further light on the ways in which this hierarchy differs from others.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.53"}, {"primary_key": "4096678", "vector": [], "sparse_vector": [], "title": "A New Framework for Distributed Submodular Maximization.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A wide variety of problems in machine learning, including exemplar clustering, document summarization, and sensor placement, can be cast as constrained submodular maximization problems. A lot of recent effort has been devoted to developing distributed algorithms for these problems. However, these results suffer from high number of rounds, suboptimal approximation ratios, or both. We develop a framework for bringing existing algorithms in the sequential setting to the distributed setting, achieving near optimal approximation ratios for many settings in only a constant number of MapReduce rounds. Our techniques also give a fast sequential algorithm for non-monotone maximization subject to a matroid constraint.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.74"}, {"primary_key": "4096679", "vector": [], "sparse_vector": [], "title": "Edit Distance: Sketching, Streaming, and Document Exchange.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We show that in the document exchange problem, where <PERSON> holds x ϵ {0, 1} n and <PERSON> holds y ϵ {0, 1} n , <PERSON> can send <PERSON> a message of size O(K(log 2 K + log n)) bits such that <PERSON> can recover x using the message and his input y if the edit distance between x and y is no more than K, and output \"error\" otherwise. Both the encoding and decoding can be done in time Õ(n + poly(K)). This result significantly improves on the previous communication bounds under polynomial encoding/decoding time. We also show that in the referee model, where <PERSON> and <PERSON> hold x and y respectively, they can compute sketches of x and y of sizes poly(K log n) bits (the encoding), and send to the referee, who can then compute the edit distance between x and y together with all the edit operations if the edit distance is no more than K, and output \"error\" otherwise (the decoding). To the best of our knowledge, this is the first result for sketching edit distance using poly(K log n) bits. Moreover, the encoding phase of our sketching algorithm can be performed by scanning the input string in one pass. Thus our sketching algorithm also implies the first streaming algorithm for computing edit distance and all the edits exactly using poly(K log n) bits of space.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.15"}, {"primary_key": "4096680", "vector": [], "sparse_vector": [], "title": "Truly Sub-cubic Algorithms for Language Edit Distance and RNA-Folding via Fast Bounded-Difference Min-Plus Product.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Virginia Vassilevska Williams"], "summary": "It is a major open problem whether the (min,+)-product of two n by n matrices has a truly sub-cubic time algorithm, as it is equivalent to the famous All-Pairs-Shortest-Paths problem (APSP) in n-vertex graphs. There are some restrictions of the (min,+)-product to special types of matrices that admit truly sub-cubic algorithms, each giving rise to a special case of APSP that can be solved faster. In this paper we consider a new, different and powerful restriction in which one matrix can be arbitrary, as long as the other matrix has \"bounded differences\" in either its columns or rows, i.e. any two consecutive entries differ by only a small amount. We obtain the first truly sub-cubic algorithm for this Bounded Differences (min,+)-product (answering an open problem of <PERSON> and <PERSON><PERSON>). Our new algorithm, combined with a strengthening of an approach of <PERSON><PERSON> for solving context-free grammar parsing with matrix multiplication, yields the first truly sub-cubic algorithms for the following problems: Language Edit Distance (a major problem in the parsing community), RNA-folding (a major problem in bioinformatics) and Optimum Stack Generation (answering an open problem of Tarjan).", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.48"}, {"primary_key": "4096681", "vector": [], "sparse_vector": [], "title": "Zero-Knowledge Proof Systems for QMA.", "authors": ["<PERSON>", "Zhengfeng Ji", "<PERSON>", "<PERSON>"], "summary": "Prior work has established that all problems in NP admit classical zero-knowledge proof systems, and under reasonable hardness assumptions for quantum computations, these proof systems can be made secure against quantum attacks. We prove a result representing a further quantum generalization of this fact, which is that every problem in the complexity class QMA has a quantum zero-knowledge proof system. More specifically, assuming the existence of an unconditionally binding and quantum computationally concealing commitment scheme, we prove that every problem in the complexity class QMA has a quantum interactive proof system that is zero-knowledge with respect to efficient quantum computations. Our QMA proof system is sound against arbitrary quantum provers, but only requires an honest prover to perform polynomial-time quantum computations, provided that it holds a quantum witness for a given instance of the QMA problem under consideration.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.13"}, {"primary_key": "4096682", "vector": [], "sparse_vector": [], "title": "No Occurrence Obstructions in Geometric Complexity Theory.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The permanent versus determinant conjecture is a major problem in complexity theory that is equivalent to the separation of the complexity classes VP ws and VNP. <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [SIAM J Comput 2001] suggested 8to study a strengthened version of this conjecture over the complex numbers that amounts to separating the orbit closures of the determinant and padded permanent polynomials. In that paper it was also proposed to separate these orbit closures by exhibiting occurrence obstructions, which are irreducible representations of GLn2(C), which occur in one coordinate ring of the orbit closure, but not in the other. We prove that this approach is impossible. However, we do not rule out the approach to the permanent versus determinant problem via multiplicity obstructions as proposed by in [32].", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.49"}, {"primary_key": "4096683", "vector": [], "sparse_vector": [], "title": "Strong Fooling Sets for Multi-player Communication with Applications to Deterministic Estimation of Stream Statistics.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We develop a paradigm for studying multi-player deterministic communication, based on a novel combinatorial concept that we call a strong fooling set. Our paradigm leads to optimal lower bounds on the per-player communication required for solving multi-player EQUALITY problems in a private-message setting. This in turn gives a very strong - O(1) versus Ω(n) - separation between private-message and one-way blackboard communication complexities. Applying our communication complexity results, we show that for deterministic data streaming algorithms, even loose estimations of some basic statistics of an input stream require large amounts of space. For instance, approximating the frequency moment F k within a factor α requires Ω(n/α 1/(1-k) ) space for k > 1 and roughly Ω(n/α k/(k-1) ) space for k > 1. In particular, approximation within any constant factor α, however large, requires linear space, with the trivial exception of k = 1. This is in sharp contrast to the situation for randomized streaming algorithms, which can approximate F k to within (1±ε) factors using Õ(1) space for k ≤ 2 and o(n) space for all finite k and all constant ε > 0. Previous linear-space lower bounds for deterministic estimation were limited to small factors α, such as α 0 or F 2 . We also provide certain space/approximation tradeoffs in a deterministic setting for the problems of estimating the empirical entropy of a stream as well as the size of the maximum matching and the edge connectivity of a streamed graph.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.14"}, {"primary_key": "4096684", "vector": [], "sparse_vector": [], "title": "A PTAS for the Steiner Forest Problem in Doubling Metrics.", "authors": ["T.<PERSON><PERSON><PERSON>", "Shuguang Hu", "Shaofeng H.-<PERSON><PERSON> Jiang"], "summary": "We achieve a (randomized) polynomial-time approximation scheme (PTAS) for the Steiner Forest Problem in doubling metrics. Before our work, a PTAS is given only for the Euclidean plane in [FOCS 2008: <PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON>]. Our PTAS also shares similarities with the dynamic programming for sparse instances used in [STOC 2012: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>] and [SODA 2016: <PERSON> and <PERSON>]. However, extending previous approaches requires overcoming several non-trivial hurdles, and we make the following technical contributions. (1) We prove a technical lemma showing that Steiner points have to be \"near\" the terminals in an optimal Steiner tree. This enables us to define a heuristic to estimate the local behavior of the optimal solution, even though the Steiner points are unknown in advance. This lemma also generalizes previous results in the Euclidean plane, and may be of independent interest for related problems involving Steiner points. (2) We develop a novel algorithmic technique known as \"adaptive cells\" to overcome the difficulty of keeping track of multiple components in a solution. Our idea is based on but significantly different from the previously proposed \"uniform cells\" in the FOCS 2008 paper, whose techniques cannot be readily applied to doubling metrics.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.91"}, {"primary_key": "4096685", "vector": [], "sparse_vector": [], "title": "An Exponential Separation between Randomized and Deterministic Complexity in the LOCAL Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Over the past 30 years numerous algorithms have been designed for symmetry breaking problems in the LOCAL model, such as maximal matching, MIS, vertex coloring, and edge coloring. For most problems the best randomized algorithm is at least exponentially faster than the best deterministic algorithm. We prove that these exponential gaps are necessary and establish numerous connections between the deterministic and randomized complexities in the LOCAL model. Each of our results has a very compelling take-away message: 1) Building on the recent randomized lower bounds of <PERSON> et al. [1], we prove that the randomized complexity of Δ-coloring a tree with maximum degree Δ is O(log Δ log n + log*n), for any Δ > = 55, whereas its deterministic complexity is Ω(log Δ n) for any Δ > = 3. This also establishes a large separation between the deterministic complexity of Δ-coloring and (Δ+1)-coloring trees. 2) We prove that any deterministic algorithm for a natural class of problems that runs in O(1) + o(log Δ n) rounds can be transformed to run in O(log*n - log*Δ + 1) rounds. If the transformed algorithm violates a lower bound (even allowing randomization), then one can conclude that the problem requires Ω(log Δ n) time deterministically. This gives an alternate proof that deterministically Δ-coloring a tree with small Δ takes Ω(log Δ n) rounds. 3) We prove that the randomized complexity of any natural problem on instances of size n is at least its deterministic complexity on instances of size √log n. This shows that a deterministic Ω(log Δ n) lower bound for any problem (Δ-coloring a tree, for example) implies a randomized Ω(log Δ log n) lower bound. It also illustrates that the graph shattering technique employed in recent randomized symmetry breaking algorithms is absolutely essential to the LOCAL model. For example, it is provably impossible to improve the 2O(√log log n) term in the complexities of the best MIS and (Δ+1)-coloring algorithms without also improving the 2O(√log n)-round Panconesi-Srinivasan algorithm.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.72"}, {"primary_key": "4096686", "vector": [], "sparse_vector": [], "title": "Explicit Non-malleable Extractors, Multi-source Extractors, and Almost Optimal Privacy Amplification Protocols.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We make progress in the following three problems: 1. Constructing optimal seeded non-malleable extractors, 2. Constructing optimal privacy amplification protocols with an active adversary, for any possible security parameter, 3. Constructing extractors for independent weak random sources, when the min-entropy is extremely small (i.e., near logarithmic). For the first two problems, the best known non-malleable extractors by <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, and by <PERSON> all require seed length and min-entropy with quadratic loss in parameters. As a result, the best known explicit privacy amplification protocols with an active adversary, which achieve two rounds of communication and optimal entropy loss was sub-optimal in the min-entropy of the source. In this paper we give an explicit non-malleable extractor that works for nearly optimal seed length and min-entropy, and yields a two-round privacy amplification protocol with optimal entropy loss for almost all ranges of the security parameter. For the third problem, we improve upon a very recent result by <PERSON> and <PERSON> and give an explicit extractor that uses an absolute constant number of sources, each with almost logarithmic min-entropy. The key ingredient in all our constructions is a generalized, and much more efficient version of the independence preserving merger introduced by <PERSON>, which we call non-malleable independence preserving merger. Our construction of the merger also simplifies that of <PERSON> and <PERSON>, and may be of independent interest.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.25"}, {"primary_key": "4096687", "vector": [], "sparse_vector": [], "title": "Decremental Single-Source Reachability and Strongly Connected Components in Õ(m√n) Total Update Time.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Giuseppe F<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present randomized algorithms with a total update time of Õ(m √n) for the problems of decremental single source reachability and decremental strongly connected components on directed graphs. This improves recent breakthrough results of <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON> [STOC 14, ICALP 15]. In addition, our algorithms are arguably simpler.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.42"}, {"primary_key": "4096688", "vector": [], "sparse_vector": [], "title": "Fourier-Sparse Interpolation without a Frequency Gap.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider the problem of estimating a Fourier-sparse signal from noisy samples, where the sampling is done over some interval [0, T] and the frequencies can be \"off-grid\". Previous methods for this problem required the gap between frequencies to be above 1/T, the threshold required to robustly identify individual frequencies. We show the frequency gap is not necessary to estimate the signal as a whole: for arbitrary k-Fourier-sparse signals under l2 bounded noise, we show how to estimate the signal with a constant factor growth of the noise and sample complexity polynomial in k and logarithmic in the bandwidth and signal-to-noise ratio. As a special case, we get an algorithm to interpolate degree d polynomials from noisy measurements, using O(d) samples and increasing the noise by a constant factor in l2.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.84"}, {"primary_key": "4096689", "vector": [], "sparse_vector": [], "title": "The Constant Inapproximability of the Parameterized Dominating Set Problem.", "authors": ["<PERSON><PERSON><PERSON>", "Bing<PERSON> Lin"], "summary": "We prove that there is no fpt-algorithm that can approximate the dominating set problem with any constant ratio, unless FPT = W[1]. Our hardness reduction is built on the second author's recent W[1]-hardness proof of the biclique problem [25]. This yields, among other things, a proof without the PCP machinery that the classical dominating set problem has no polynomial time constant approximation under the exponential time hypothesis.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.61"}, {"primary_key": "4096690", "vector": [], "sparse_vector": [], "title": "Depth-Reduction for Composites.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We obtain a new depth-reduction construction, which implies a super-exponential improvement in the depth lower bound separating NEXP from non-uniform ACC. In particular, we show that every circuit with AND, OR, NOT, and MOD m gates, m ε Z + , of polynomial size and depth d can be reduced to a depth-2, SYM-AND, circuit of size 2 (log n) O(d) . This is an exponential size improvement over the traditional Yao-Beigel-Tarui, which has size blowup 2 (log n)2 O(d) . Therefore, depth-reduction for composite m matches the size of the Allender-Hertrampf construction for primes from 1989. One immediate implication of depth reduction is an improvement of the depth from o(loglog n) to o(log n/loglog n), in <PERSON>' program for ACC circuit lower bounds against NEXP. This is just short of O(log n/loglog n) and thus pushes <PERSON>'s program to the NC 1 barrier, since NC 1 is contained in ACC of depth O(log n/loglog n). A second, but non-immediate, implication regards the strengthening of the ACC lower bound in the Chattopadhyay-Santhanam interactive compression setting.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.20"}, {"primary_key": "4096691", "vector": [], "sparse_vector": [], "title": "Testing Assignments to Constraint Satisfaction Problems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "For a finite relational structure A, let CSP(A) denote the CSP instances whose constraint relations are taken from A. The resulting family of problems CSP(A) has been considered heavily in a variety of computational contexts. In this article, we consider this family from the perspective of property testing: given an instance of a CSP and query access to an assignment, one wants to decide whether the assignment satisfies the instance, or is far from so doing. While previous work on this scenario studied concrete templates or restricted classes of structures, this article presents comprehensive classification theorems. Our first contribution is a dichotomy theorem completely characterizing the structures A such that CSP(A) is constant-query testable: (i) If A has a majority polymorphism and a <PERSON><PERSON><PERSON> polymorphism, then CSP(A) is constant-query testable with one-sided error. (ii) Else, testing CSP(A) requires a super-constant number of queries. Let ∃CSP(A) denote the extension of CSP(A) to instances which may include existentially quantified variables. Our second contribution is to classify all structures A in terms of the number of queries needed to test assignments to instances of ∃CSP(A), with one-sided error. More specifically, we show the following trichotomy (i) If A has a majority polymorphism and a <PERSON><PERSON><PERSON> polymorphism, then ∃CSP(A) is constant-query testable with one-sided error. (ii) Else, if A has a (k + 1)-ary near-unanimity polymorphism for some k ≥ 2, and no Maltsev polymorphism then ∃CSP(A) is not constant-query testable (even with two-sided error) but is sublinear-query testable with one-sided error. (iii) Else, testing ∃CSP(A) with one-sided error requires a linear number of queries.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.63"}, {"primary_key": "4096692", "vector": [], "sparse_vector": [], "title": "Informational Substitutes.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We propose definitions of substitutes and complements for pieces of information (\"signals\") in the context of a decision or optimization problem, with game-theoretic and algorithmic applications. In a game-theoretic context, substitutes capture diminishing marginal value of information to a rational decision maker. There, we address the main open problem in a fundamental strategic-information-revelation setting, prediction markets. We show that substitutes characterize \"best-possible\" equilibria with immediate information aggregation, while complements characterize \"worst-possible\", delayed aggregation. Game-theoretic applications also include settings such as crowdsourcing contests and question-and-answer forums. In an algorithmic context, where substitutes capture diminishing marginal improvement of information to an optimization problem, substitutes imply efficient approximation algorithms for a very general class of (adaptive) information acquisition problems. In tandem with these broad applications, we examine the structure and design of informational substitutes and complements. They have equivalent, intuitive definitions from disparate perspectives: submodularity, geometry, and information theory. We also consider the design of scoring rules or optimization problems so as to encourage substitutability or complementarity, with positive and negative results. Taken as a whole, the results give some evidence that, in parallel with substitutable items, informational substitutes play a natural conceptual and formal role in game theory and algorithms.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.33"}, {"primary_key": "4096693", "vector": [], "sparse_vector": [], "title": "On Approximating Maximum Independent Set of Rectangles.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We study the Maximum Independent Set of Rectangles (MISR) problem: given a set of n axis-parallel rectangles, find a largest-cardinality subset of the rectangles, such that no two of them overlap. MISR is a basic geometric optimization problem with many applications, that has been studied extensively. Until recently, the best approximation algorithm for it achieved an O(log log n)-approximation factor. In a recent breakthrough, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> provided a quasi-polynomial time approximation scheme: a (1-ε)-approximation algorithm with running time n O(poly(log n)/ε) . Despite this result, obtaining a PTAS or even a polynomial-time constant-factor approximation remains a challenging open problem. In this paper we make progress towards this goal by providing an algorithm for MISR that achieves a (1 - ε)-approximation in time n O(poly(log logn/ε)) . We introduce several new technical ideas, that we hope will lead to further progress on this and related problems.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.92"}, {"primary_key": "4096694", "vector": [], "sparse_vector": [], "title": "Local Search Yields Approximation Schemes for k-Means and k-Median in Euclidean and Minor-Free Metrics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We give the first polynomial-time approximation schemes (PTASs) for the following problems: (1) uniform facility location in edge-weighted planar graphs, (2) k-median and k-means in edge-weighted planar graphs, (3) k-means in Euclidean space of bounded dimension. Our first and second results extend to minor-closed families of graphs. All our results extend to cost functions that are the pth power of the shortest-path distance. The algorithm is local search where the local neighborhood of a solution S consists of all solutions obtained from S by removing and adding 1/ε O(1) centers.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.46"}, {"primary_key": "4096695", "vector": [], "sparse_vector": [], "title": "Making the Most of Advice: New Correlation Breakers and Their Applications.", "authors": ["<PERSON>"], "summary": "A typical obstacle one faces when constructing pseudorandom objects is undesired correlations between random variables. Identifying this obstacle and constructing certain types of \"correlation breakers\" was central for recent exciting advances in the construction of multi-source and nonmalleable extractors. One instantiation of correlation breakers is correlation breakers with advice. These are algorithms that break the correlation a \"bad\" random variable Y ' has with a \"good\" random variable Y using an \"advice\" - a fixed string α that is associated with Y which is guaranteed to be distinct from the corresponding string α' associated with Y '. Prior to this work, explicit constructions of correlation breakers with advice require the entropy of the involved random variables to depend linearly on the advice length. In this work, building on independence-preserving mergers, a pseudorandom primitive that was recently introduced by <PERSON> and <PERSON>, we devise a new construction of correlation breakers with advice that has optimal, logarithmic, dependence on the advice length. This enables us to obtain the following results. . We construct an extractor for 5 independent n-bit sources with min-entropy (log n) 1+o(1) . This result puts us tantalizingly close to the goal of constructing extractors for 2 sources with min-entropy O(log n), which would have exciting implications to Ramsey theory. . We construct non-malleable extractors with error guarantee ε for n-bit sources, with seed length d = O(log n)+ (log(1/ε)) 1+o(1) for any min-entropy k = Ω(d). Prior to this work, all constructions require either very high minentropy or otherwise have seed length ω(log n) for any ε. Further, our extractor has near-optimal output length. Prior constructions that achieve comparable output length work only for very high min-entropy k ≈ n/2. . By instantiating the Dodis-Wichs framework with our non-malleable extractor, we obtain near-optimal privacy amplification protocols against active adversaries, improving upon all (incomparable) known protocols.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.28"}, {"primary_key": "4096696", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON> Graphs in Polynomial Time.", "authors": ["<PERSON>"], "summary": "Recent work by <PERSON>, <PERSON> and <PERSON><PERSON><PERSON><PERSON> proves the existence of bipartite Ramanujan (multi) graphs of all degrees and all sizes. However, that paper did not provide a polynomial time algorithm to actually compute such graphs. Here, we provide a polynomial time algorithm to compute certain expected characteristic polynomials related to this construction. This leads to a deterministic polynomial time algorithm to compute bipartite Ramanujan (multi) graphs of all degrees and all sizes.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.37"}, {"primary_key": "4096697", "vector": [], "sparse_vector": [], "title": "Faster Algorithms for Computing the Stationary Distribution, Simulating Random Walks, and More.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we provide faster algorithms for computing variousfundamental quantities associated with random walks on a directedgraph, including the stationary distribution, personalized PageRankvectors, hitting times, and escape probabilities. In particular, ona directed graph with n vertices and m edges, we show how tocompute each quantity in time Õ(m3/4n + mn2/3), wherethe Õ notation suppresses polylog factors in n, the desired accuracy, and the appropriate condition number (i.e. themixing time or restart probability). Our result improves upon the previous fastest running times for these problems, previous results either invoke a general purpose linearsystem solver on a n × n matrix with m non-zero entries, or depend polynomially on the desired error or natural condition numberassociated with the problem (i.e. the mixing time or restart probability). For sparse graphs, we obtain a running time of Õ(n7/4), breaking the O(n2) barrier of the best running time one couldhope to achieve using fast matrix multiplication. We achieve our result by providing a similar running time improvementfor solving directed Laplacian systems, a natural directedor asymmetric analog of the well studied symmetric or undirected Laplaciansystems. We show how to solve such systems in time Õ(m3/4n + mn2/3), and efficiently reduce a broad range of problems to solving Õ(1) directed Laplacian systems on Eulerian graphs. We hope these resultsand our analysis open the door for further study into directedspectral graph theory.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.69"}, {"primary_key": "4096698", "vector": [], "sparse_vector": [], "title": "Extractors for Near Logarithmic Min-Entropy.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The main contribution of this work is an explicit construction of extractors for near logarithmic min-entropy. For any δ > 0 we construct an extractor for O(1/δ) n-bit sources with min-entropy (logn) 1+δ . This is most interesting when δ is set to a small constant, though the result also yields an extractor for O(log logn) sources with logarithmic min-entropy. Prior to this work, the best explicit extractor in terms of supporting least-possible min-entropy, due to Li (FOCS'15), requires min-entropy (logn) 2+δ from its O(1/δ) sources. Further, all current techniques for constructing multi-source extractors \"break\" below min-entropy (log n) 2 . In fact, existing techniques do not provide even a disperser for o(log n) sources each with min-entropy (log n) 1.99 . Apart from being a natural problem, supporting logarithmic min-entropy has applications to combinatorics. A two-source disperser, let alone an extractor, for min-entropy O(log n) induces a (log, nO(1))-Ramsey graph on n vertices. Thus, constructing such dispersers would be a significant step towards constructively matching <PERSON><PERSON><PERSON><PERSON>' proof for the existence of (2log n)-Ramsey graphs on n vertices. Our construction does not rely on the sophisticated primitives that were key to the substantial recent progress on multi-source extractors, such as non-malleable extractors, correlation breakers, the lightest-bin condenser, or extractors for non-oblivious bit-fixing sources, although some of these primitives can be combined with our construction so to improve the output length and the error guarantee. Instead, at the heart of our construction is a new primitive called an independence-preserving merger. The construction of the latter builds on the alternating extraction technique.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.27"}, {"primary_key": "4096699", "vector": [], "sparse_vector": [], "title": "Simulated Quaotum Annealing Can Be Exponentially Faster Than Classical Simulated Annealing.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Simulated Quantum Annealing (SQA) is a Markov Chain Monte-Carlo algorithm that samples the equilibrium thermal state of a Quantum Annealing (QA) Hamiltonian. In addition to simulating quantum systems, SQA has also been proposed as another physics-inspired classical algorithm for combinatorial optimization, alongside classical simulated annealing. However, in many cases it remains an open challenge to determine the performance of both QA and SQA. One piece of evidence for the strength of QA over classical simulated annealing comes from an example by <PERSON><PERSON>, <PERSON> and <PERSON>mann . There a bit-symmetric cost function with a thin, high energy barrier was designed to show an exponential seperation between classical simulated annealing, for which thermal fluctuations take exponential time to climb the barrier, and quantum annealing which passes through the barrier and reaches the global minimum in poly time, arguably by taking advantage of quantum tunneling. In this work we apply a comparison method to rigorously show that the Markov chain underlying SQA efficiently samples the target distribution and finds the global minimum of this spike cost function in polynomial time. Our work provides evidence for the growing consensus that SQA inherits at least some of the advantages of tunneling in QA, and so QA is unlikely to achieve exponential speedups over classical computing solely by the use of quantum tunneling. Since we analyze only a particular model this evidence is not decisive. However, techniques applied here---including warm starts from the adiabatic path and the use of the quantum ground state probability distribution to understand the stationary distribution of SQA---may be valuable for future studies of the performance of SQA on cost functions for which QA is efficient.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.81"}, {"primary_key": "4096700", "vector": [], "sparse_vector": [], "title": "Towards Strong Reverse <PERSON>kowski-Type Inequalities for Lattices.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We present a natural reverse Minkowski-type inequality for lattices, which gives upper bounds on the number of lattice points in a Euclidean ball in terms of sublattice determinants, and conjecture its optimal form. The conjecture exhibits a surprising wealth of connections to various areas in mathematics and computer science, including a conjecture motivated by integer programming by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (Annals of Math. 1988), a question from additive combinatorics asked by <PERSON>, a question on Brownian motions asked by <PERSON><PERSON><PERSON> (Colloq. Math. 2010), a theorem by <PERSON><PERSON> and <PERSON> from convex geometry (Ann. Probab. 1987), worst-case to average-case reductions in lattice-based cryptography, and more. We present these connections, provide evidence for the conjecture, and discuss possible approaches towards a proof. Our main technical contribution is in proving that our conjecture implies the l2 case of the <PERSON><PERSON><PERSON> and <PERSON> conjecture. The proof relies on a novel convex relaxation for the covering radius, and a rounding procedure based on \"uncrossing\" lattice subspaces.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.55"}, {"primary_key": "4096701", "vector": [], "sparse_vector": [], "title": "Learning in Auctions: Regret is Hard, Envy is Easy.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "An extensive body of recent work studies the welfare guarantees of simple and prevalent combinatorial auction formats, such as selling m items via simultaneous second price auctions (SiSPAs) [1], [2], [3]. These guarantees hold even when the auctions are repeatedly executed and the players use no-regret learning algorithms to choose their actions. Unfortunately, off-the-shelf no-regret learning algorithms for these auctions are computationally inefficient as the number of actions available to the players becomes exponential. We show that this obstacle is inevitable: there are no polynomial-time no-regret learning algorithms for SiSPAs, unless RP ⊇ NP, even when the bidders are unit-demand. Our lower bound raises the question of how good outcomes polynomially-bounded bidders may discover in such auctions. To answer this question, we propose a novel concept of learning in auctions, termed \"no-envy learning.\" This notion is founded upon Walrasian equilibrium, and we show that it is both efficiently implementable and results in approximately optimal welfare, even when the bidders have valuations from the broad class of fractionally subadditive (XOS) valuations (assuming demand oracle access to the valuations) or coverage valuations (even without demand oracles). No-envy learning outcomes are a relaxation of no-regret learning outcomes, which maintain their approximate welfare optimality while endowing them with computational tractability. Our positive and negative results extend to several auction formats that have been studied in the literature via the smoothness paradigm. Our positive results for XOS valuations are enabled by a novel Follow-The-Perturbed-Leader algorithm for settings where the number of experts and states of nature are both infinite, and the payoff function of the learner is non-linear. We show that this algorithm has applications outside of auction settings, establishing significant gains in a recent application of no-regret learning in security games. Our efficient learning result for coverage valuations is based on a novel use of convex rounding schemes and a reduction to online convex optimization.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.31"}, {"primary_key": "4096702", "vector": [], "sparse_vector": [], "title": "Noisy Population Recovery in Polynomial Time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the noisy population recovery problem of <PERSON><PERSON> et al. [6], the goal is to learn an unknown distribution f on binary strings of length n from noisy samples. A noisy sample with parameter μ ∈ [0,1] is generated by selecting a sample from f, and independently flipping each coordinate of the sample with probability (1-μ)/2. We assume an upper bound k on the size of the support of the distribution, and the goal is to estimate the probability of any string to within some given error ε. It is known that the algorithmic complexity and sample complexity of this problem are polynomially related to each other. We describe an algorithm that for each μ > 0, provides the desired estimate of the distribution in time bounded by a polynomial in k, n and 1/ε improving upon the previous best result of poly(k log log k , n, 1/ε) due to <PERSON><PERSON> and <PERSON> [9]. Our proof combines ideas from [9] with a noise attenuated version of Möbius inversion. The latter crucially uses the robust local inverse construction of <PERSON><PERSON><PERSON> and <PERSON><PERSON> [11].", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.77"}, {"primary_key": "4096703", "vector": [], "sparse_vector": [], "title": "A New Approach for Testing Properties of Discrete Distributions.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We study problems in distribution property testing: Given sample access to one or more unknown discrete distributions, we want to determine whether they have some global property or are epsilon-far from having the property in L1 distance (equivalently, total variation distance, or \"statistical distance\").In this work, we give a novel general approach for distribution testing. We describe two techniques: our first technique gives sample-optimal testers, while our second technique gives matching sample lower bounds. As a consequence, we resolve the sample complexity of a wide variety of testing problems. Our upper bounds are obtained via a modular reduction-based approach. Our approach yields optimal testers for numerous problemsby using a standard L2-identity tester as a black-box. Using this recipe, we obtain simple estimators for a wide range of problems, encompassing many problems previously studied in the TCS literature, namely: (1) identity testing to a fixed distribution, (2) closeness testing between two unknown distributions (with equal/unequal sample sizes), (3) independence testing (in any number of dimensions), (4) closeness testing for collections of distributions, and(5) testing histograms. For all of these problems, our testers are sample-optimal, up to constant factors. With the exception of (1), ours are the first sample-optimal testers for the corresponding problems. Moreover, our estimators are significantly simpler to state and analyze compared to previous results. As an important application of our reduction-based technique, we obtain the first adaptive algorithm for testing equivalence betweentwo unknown distributions. The sample complexity of our algorithm depends on the structure of the unknown distributions - as opposed to merely their domain size -and is significantly better compared to the worst-case optimal L1-tester in many natural instances. Moreover, our technique naturally generalizes to other metrics beyond the L1-distance. As an illustration of its flexibility, we use it to obtain the first near-optimal equivalence testerunder the Hellinger distance. Our lower bounds are obtained via a direct information-theoretic approach: Given a candidate hard instance, our proof proceeds by boundingthe mutual information between appropriate random variables. While this is a classical method in information theory, prior to our work, it had not been used in this context. Previous lower bounds relied either on the birthday paradox, oron moment-matching and were thus restricted to symmetric properties. Our lower bound approach does not suffer from any such restrictions and gives tight sample lower bounds for the aforementioned problems.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.78"}, {"primary_key": "4096704", "vector": [], "sparse_vector": [], "title": "Robust Estimators in High Dimensions without the Computational Intractability.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study high-dimensional distribution learning in an agnostic setting where an adversary is allowed to arbitrarily corrupt an epsilon fraction of the samples. Such questions have a rich history spanning statistics, machine learning and theoretical computer science. Even in the most basic settings, the only known approaches are either computationally inefficient or lose dimension dependent factors in their error guarantees. This raises the following question: Is high-dimensional agnostic distribution learning even possible, algorithmically? In this work, we obtain the first computationally efficient algorithms for agnostically learning several fundamental classes of high-dimensional distributions: (1) a single Gaussian, (2) a product distribution on the hypercube, (3) mixtures of two product distributions (under a natural balancedness condition), and (4) mixtures of k Gaussians with identical spherical covariances. All our algorithms achieve error that is independent of the dimension, and in many cases depends nearly-linearly on the fraction of adversarially corrupted samples. Moreover, we develop a general recipe for detecting and correcting corruptions in high-dimensions, that may be applicable to many other problems.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.85"}, {"primary_key": "4096705", "vector": [], "sparse_vector": [], "title": "Computational Efficiency Requires Simple Taxation.", "authors": ["<PERSON><PERSON>"], "summary": "We characterize the communication complexity of truthful mechanisms. Our departure point is the well known taxation principle. The taxation principle asserts that every truthful mechanism can be interpreted as follows: every player is presented with a menu that consists of a price for each bundle (the prices depend only on the valuations of the other players). Each player is allocated a bundle that maximizes his profit according to this menu. We define the taxation complexity of a truthful mechanism to be the logarithm of the maximum number of menus that may be presented to a player. Our main finding is that in general the taxation complexity essentially equals the communication complexity. The proof consists of two main steps. First, we prove that for rich enough domains the taxation complexity is at most the communication complexity. We then show that the taxation complexity is much smaller than the communication complexity only in \"pathological\" cases and provide a formal description of these extreme cases. Next, we study mechanisms that access the valuations via value queries only. In this setting we establish that the menu complexity - a notion that was already studied in several different contexts - characterizes the number of value queries that the mechanism makes in exactly the same way that the taxation complexity characterizes the communication complexity. Our approach yields several applications, including strengthening the solution concept with low communication overhead, fast computation of prices, and hardness of approximation by computationally efficient truthful mechanisms.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.30"}, {"primary_key": "4096706", "vector": [], "sparse_vector": [], "title": "Convergence of MCMC and Loopy BP in the Tree Uniqueness Region for the Hard-Core Model.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the hard-core (gas) model defined on independent sets of an input graph where the independent sets are weighted by a parameter (aka fugacity) λ > 0. For constant ∆, previous work of <PERSON><PERSON> (2006) established an FPTAS for the partition function for graphs of maximum degree ∆ when λ λ c (∆).The running time of <PERSON><PERSON>'s algorithm is exponential in log ∆.Here we present an FPRAS for the partition function whose running time is O * (n 2 ).We analyze the simple single-site Markov chain known as the Glauber dynamics for sampling from the associated Gibbs distribution.We prove there exists a constant ∆ 0 such that for all graphs with maximum degree ∆ ≥ ∆ 0 and girth ≥ 7 (i.e., no cycles of length ≤ 6), the mixing time of the Glauber dynamics is O(n log n) when λ < λ c (∆).Our work complements that of <PERSON><PERSON> which applies for small constant ∆ whereas our work applies for all ∆ at least a sufficiently large constant ∆ 0 (this includes ∆ depending on n = |V |).Our proof utilizes loopy BP (belief propagation) which is a widely-used algorithm for inference in graphical models.A novel aspect of our work is using the principal eigenvector for the BP operator to design a distance function which contracts in expectation for pairs of states that behave like the BP fixed point.We also prove that the Glauber dynamics behaves locally like loopy BP.As a byproduct we obtain that the Glauber dynamics converges, after a short burn-in period, close to the BP fixed point, and this implies that the fixed point of loopy BP is a close approximation to the Gibbs distribution.Using these connections we establish that loopy BP quickly converges to the Gibbs distribution when the girth ≥ 6 and λ < λ c (∆).", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.80"}, {"primary_key": "4096707", "vector": [], "sparse_vector": [], "title": "Hopsets with Constant Hopbound, and Applications to Approximate Shortest Paths.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "A (β, ∈)-hopset for a weighted undirected n-vertex graph G = (V, E) is a set of edges, whose addition to the graph guarantees that every pair of vertices has a path between them that contains at most β edges, whose length is within 1 + ∈ of the shortest path. In her seminal paper, <PERSON> [8, <PERSON><PERSON><PERSON> 2000] introduced the notion of hopsets in the context of parallel computation of approximate shortest paths, and since then it has found numerous applications in various other settings, such as dynamic graph algorithms, distributed computing, and the streaming model. <PERSON> [8] devised efficient algorithms for constructing hopsets with polylogarithmic in n number of hops. Her constructions remain the state-of-the-art since the publication of her paper in STOC'94, i.e., for more than two decades. In this paper we exhibit the first construction of sparse hopsets with a constant number of hops. We also find efficient algorithms for hopsets in various computational settings, improving the best known constructions. Generally, our hopsets strictly outperform the hopsets of [8], both in terms of their parameters, and in terms of the resources required to construct them. We demonstrate the applicability of our results for the fundamental problem of computing approximate shortest paths from s sources. Our results improve the running time for this problem in the parallel, distributed and streaming models, for a vast range of s.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.22"}, {"primary_key": "4096708", "vector": [], "sparse_vector": [], "title": "Constrained Submodular Maximization: Beyond 1/e.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this work, we present a new algorithm for maximizing a non-monotone submodular function subject to a general constraint. Our algorithm finds an approximate fractional solution for maximizing the multilinear extension of the function over a down-closed polytope. The approximation guarantee is 0.372 and it is the first improvement over the 1/e approximation achieved by the unified Continuous Greedy algorithm [<PERSON><PERSON><PERSON> et al., FOCS 2011].", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.34"}, {"primary_key": "4096709", "vector": [], "sparse_vector": [], "title": "A Better-Than-3n Lower Bound for the Circuit Complexity of an Explicit Function.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider Boolean circuits over the full binary basis. We prove a (3+1/86)n-o(n) lower bound on the size of such a circuit for an explicitly defined predicate, namely an affine disperser for sublinear dimension. This improves the 3n-o(n) bound of <PERSON><PERSON> (1984).The proof is based on the gate elimination technique extended with the following three ideas. We generalize the computational model by allowing circuits to contain cycles, this in turn allows us to perform affine substitutions. We use a carefully chosen circuit complexity measure to track the progress of the gate elimination process. Finally, we use quadratic substitutions that may be viewed as delayed affine substitutions.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.19"}, {"primary_key": "4096710", "vector": [], "sparse_vector": [], "title": "Subexponential Parameterized Algorithms for Planar and Apex-Minor-Free Graphs via Low Treewidth Pattern Covering.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We prove the following theorem. Given a planar graph G and an integer k, it is possible in polynomial time to randomly sample a subset A of vertices of G with the following properties: 1) A induces a subgraph of G of treewidth O(√(k log k)), and 2) for every connected subgraph H of G on at most k vertices, the probability that A covers the whole vertex set of H is at least (2 O(√k log2 k) · n O(1))-1 , where n is the number of vertices of G. Together with standard dynamic programming techniques for graphs of bounded treewidth, this result gives a versatile technique for obtaining (randomized) subexponential parameterized algorithms for problems on planar graphs, usually with running time bound 2 O (√(k log 2 k))n O(1) . The technique can be applied to problems expressible as searching for a small, connected pattern with a prescribed property in a large host graph, examples of such problems include DIRECTED k-Path, WEIGHTED k-Path, VERTEX COVER LOCAL SEARCH, and SUBGRAPH ISOMORPHISM, among others. Up to this point, it was open whether these problems can be solved in subexponential parameterized time on planar graphs, because they are not amenable to the classic technique of bidimensionality. Furthermore, all our results hold in fact on any class of graphs that exclude a fixed apex graph as a minor, in particular on graphs embeddable in any fixed surface.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.62"}, {"primary_key": "4096711", "vector": [], "sparse_vector": [], "title": "Local Conflict Coloring.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Locally finding a solution to symmetry-breaking tasks such as vertex-coloring, edge-coloring, maximal matching, maximal independent set, etc., is a long-standing challenge in distributed network computing. More recently, it has also become a challenge in the framework of centralized local computation. We introduce conflict coloring as a general symmetry-breaking task that includes all the aforementioned tasks as specific instantiations --- conflict coloring includes all locally checkable labeling tasks from [<PERSON><PERSON>\\&amp;<PERSON>, STOC 1993]. Conflict coloring is characterized by two parameters $l$ and $d$, where the former measures the amount of freedom given to the nodes for selecting their colors, and the latter measures the number of constraints which colors of adjacent nodes are subject to.We show that, in the standard LOCAL model for distributed network computing, if $l/d \\textgreater{} Δ$, then conflict coloring can be solved in $\\tilde O(\\sqrtΔ)+\\log^*n$ rounds in $n$-node graphs with maximum degree $Δ$, where $\\tilde O$ ignores the polylog factors in $Δ$. The dependency in~$n$ is optimal, as a consequence of the $Ω(\\log^*n)$ lower bound by [Linial, SIAM J. Comp. 1992] for $(Δ+1)$-coloring. An important special case of our result is a significant improvement over the best known algorithm for distributed $(Δ+1)$-coloring due to [<PERSON><PERSON><PERSON><PERSON>, PODC 2015], which required $\\tilde O(Δ^{3/4})+\\log^*n$ rounds. Improvements for other variants of coloring, including $(Δ+1)$-list-coloring, $(2Δ-1)$-edge-coloring, $T$-coloring, etc., also follow from our general result on conflict coloring. Likewise, in the framework of centralized local computation algorithms (LCAs), our general result yields an LCA which requires a smaller number of probes than the previously best known algorithm for vertex-coloring, and works for a wide range of coloring problems.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.73"}, {"primary_key": "4096712", "vector": [], "sparse_vector": [], "title": "Local Search Yields a PTAS for k-Means in Doubling Metrics.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The most well known and ubiquitous clustering problem encountered in nearly every branch of science is undoubtedly k-MEANS: given a set of data points and a parameter k, select k centres and partition the data points into k clusters around these centres so that the sum of squares of distances of the points to their cluster centre is minimized. Typically these data points lie in Euclidean space R d for some d ≥ 2. k-MEANS and the first algorithms for it were introduced in the 1950's. Over the last six decades, hundreds of papers have studied this problem and different algorithms have been proposed for it. The most commonly used algorithm in practice is known as Lloyd-Forgy, which is also referred to as \"the\" k-MEANS algorithm, and various extensions of it often work very well in practice. However, they may produce solutions whose cost is arbitrarily large compared to the optimum solution. <PERSON><PERSON><PERSON> et al. [2004] analyzed a very simple local search heuristic to get a polynomial-time algorithm with approximation ratio 9 + ε for any fixed ε > 0 for k-Umeans in Euclidean space. Finding an algorithm with a better worst-case approximation guarantee has remained one of the biggest open questions in this area, in particular whether one can get a true PTAS for fixed dimension Euclidean space. We settle this problem by showing that a simple local search algorithm provides a PTAS for k-MEANS for R d for any fixed d. More precisely, for any error parameter ε > 0, the local search algorithm that considers swaps of up to ρ = d O(d) · ε -O(d/ε) centres at a time will produce a solution using exactly k centres whose cost is at most a (1+ε)-factor greater than the optimum solution. Our analysis extends very easily to the more general settings where we want to minimize the sum of q'th powers of the distances between data points and their cluster centres (instead of sum of squares of distances as in k-MEANS) for any fixed q ≥ 1 and where the metric may not be Euclidean but still has fixed doubling dimension.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.47"}, {"primary_key": "4096713", "vector": [], "sparse_vector": [], "title": "NP-Hardness of <PERSON><PERSON><PERSON>ing and the Prouhet-Tarry-Escott Problem.", "authors": ["Venkata Gandikota", "<PERSON><PERSON>", "<PERSON>"], "summary": "Establishing the complexity of Bounded Distance Decoding for Reed-Solomon codes is a fundamental open problem in coding theory, explicitly asked by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (IEEE Trans. Inf. Theory, 2005). The problem is motivated by the large current gap between the regime when it is NP-hard, and the regime when it is efficiently solvable (i.e., the Johnson radius). We show the first NP-hardness results for asymptotically smaller decoding radii than the maximum likelihood decoding radius of <PERSON><PERSON><PERSON> and V<PERSON><PERSON>. Specifically, for Reed-Solomon codes of length N and dimension K = O(N), we show that it is NP-hard to decode more than N-K-O/log N log log N) errors. Moreover, we show that the problem is NP-hard under quasipolynomial-time reductions for an error amount > N-K-c log N (with c > 0 an absolute constant). An alternative natural reformulation of the Bounded Distance Decoding problem for Reed-Solomon codes is as a Polynomial Reconstruction problem. In this view, our results show that it is NP-hard to decide whether there exists a degree K polynomial passing through K + O(log N / log log N) points from a given set of points (a1, b1), (a2, b2) ..., (aN, bN). Furthermore, it is NP-hard under quasipolynomial-time reductions to decide whether there is a degree K polynomial passing through K + c log N many points (with c > 0 an absolute constant). These results follow from the NP-hardness of a generalization of the classical Subset Sum problem to higher moments, called Moments Subset Sum, which has been a known open problem, and which may be of independent interest. We further reveal a strong connection with the well-studied Prouhet-Tarry-Escott problem in Number Theory, which turns out to capture a main barrier in extending our techniques. We believe the Prouhet-Tarry-Escott problem deserves further study in the theoretical computer science community.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.86"}, {"primary_key": "4096714", "vector": [], "sparse_vector": [], "title": "A Deterministic Polynomial Time Algorithm for Non-commutative Rational Identity Testing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Symbolic matrices in non-commuting variables, andthe related structural and algorithmic questions, have a remarkablenumber of diverse origins and motivations. They ariseindependently in (commutative) invariant theory and representationtheory, linear algebra, optimization, linear system theory,quantum information theory, and naturally in non-commutativealgebra.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.95"}, {"primary_key": "4096715", "vector": [], "sparse_vector": [], "title": "Decidability of Non-interactive Simulation of Joint Distributions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Madhu <PERSON>"], "summary": "We present decidability results for a sub-class of \"non-interactive\" simulation problems, a well-studied class of problems in information theory. A non-interactive simulation problem is specified by two distributions P(x, y) and Q(u, v): The goal is to determine if two players, <PERSON> and <PERSON>, that observe sequences Xn and Yn respectively where {(Xi, Yi)}ni = 1 are drawn i.i.d. from P(x, y) can generate pairs U and V respectively (without communicating with each other) with a joint distribution that is arbitrarily close in total variation to Q(u, v). Even when P and Q are extremely simple: e.g., P is uniform on the triples (0, 0), (0,1), (1,0) and Q is a \"doubly symmetric binary source\", i.e., U and V are uniform ± 1 variables with correlation say 0.49, it is open if P can simulate Q. In this work, we show that whenever P is a distribution on a finite domain and Q is a 2 × 2 distribution, then the non-interactive simulation problem is decidable: specifically, given δ > 0 the algorithm runs in time bounded by some function of P and δ and either gives a non-interactive simulation protocol that is δ-close to Q or asserts that no protocol gets O(δ)-close to Q. The main challenge to such a result is determining explicit (computable) convergence bounds on the number n of samples that need to be drawn from P(x, y) to get δ-close to Q. We invoke contemporary results from the analysis of Boolean functions such as the invariance principle and a regularity lemma to obtain such explicit bounds.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.65"}, {"primary_key": "4096716", "vector": [], "sparse_vector": [], "title": "Extension Complexity of Independent Set Polytopes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We exhibit an n-node graph whose independent set polytope requires extended formulations of size exponential in Ω(n/log n). Previously, no explicit examples of n-dimensional 0/1-polytopes were known with extension complexity larger than exponential in Θ(√n). Our construction is inspired by a relatively little-known connection between extended formulations and (monotone) circuit depth.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.67"}, {"primary_key": "4096717", "vector": [], "sparse_vector": [], "title": "The Multiparty Communication Complexity of Interleaved Group Products.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Party A i of k parties A 1 ,...,A k receives on its forehead a t-tuple (a i1 ,...,a it ) of elements from the group G = SL(2, q). The parties are promised that the interleaved product a 11 ...a k1 a 12 ...a k2 ...a 1t ...a kt is equal either to the identity e or to some other fixed element g ∈ G. Their goal is to determine which of e and g the interleaved product is equal to, using the least amount of communication. We show that for all fixed k and all sufficiently large t the communication is Ω(t log |G|), which is tight. As an application, we establish the security of the leakage-resilient circuits studied by <PERSON> and <PERSON> (STOC 2013) in the \"only computation leaks\" model. Our main technical contribution is of independent interest. We show that if X is a probability distribution on G m such that any two coordinates are uniform in G 2 , then a pointwise product of s independent copies of X is nearly uniform in G m , where s depends on m only.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.39"}, {"primary_key": "4096718", "vector": [], "sparse_vector": [], "title": "Bounded-Communication Leakage Resilience via Parity-Resilient Circuits.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We consider the problem of distributing a computation between two parties, such that any bounded-communication leakage function applied to the local views of the two parties reveals essentially nothing about the input. This problem can be motivated by the goal of outsourcing computations on sensitive data to two servers in the cloud, where both servers can be simultaneously corrupted by viruses that have a limited communication bandwidth. We present a simple and efficient reduction of the above problem to that of constructing parity-resilient circuits, namely circuits that map an encoded input to an encoded output so that the parity of any subset of the wires is essentially independent of the input. We then construct parity-resilient circuits from circuits that are resilient to local leakage, which can in turn be obtained from protocols for secure multiparty computation. Our main reduction builds on a novel generalization of the ε-biased masking lemma that applies to interactive protocols. Applying the above, we obtain two-party protocols with resilience to bounded-communication leakage either in the information-theoretic setting, relying on random oblivious transfer correlations, or in the computational setting, relying on non-committing encryption which can be based on a variety of standard cryptographic assumptions.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.10"}, {"primary_key": "4096719", "vector": [], "sparse_vector": [], "title": "Breaking the Three Round Barrier for Non-malleable Commitments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We construct two-message non-malleable commitments with respect to opening in the standard model, assuming only one-to-one one-way functions. Our protocol consists of two unidirectional messages by the committer (with no message from the receiver), and is secure against all polynomial-time adversaries in the standard synchronous setting. Pass (TCC 2013) proved that any commitment scheme with non-malleability with respect to commitment, using only 2 rounds of communication, cannot be proved secure via a black-box reduction to any \"standard\" intractability assumption. We extend this by showing a similar impossibility result for commitments with non-malleability with respect to opening, another standard notion of non-malleability for commitments, for any 2-message challenge-response protocol, as well. However, somewhat surprisingly, we show that this barrier breaks down in the setting of two unidirectional messages by the committer (with no message from the receiver), for non-malleability with respect to opening. ° Our protocol makes only black-box use of any non-interactive statistically binding commitment scheme. Such a scheme can be based on any one-to-one one-way function. ° Our techniques depart significantly from the commit-challenge-response structure followed by nearly all prior works on non-malleable protocols in the standard model. Our methods are combinatorial in nature. ° Our protocol resolves the round complexity of commitments with non-malleability with respect to opening via natural (non-embedding) black-box security reductions. We show that completely non-interactive non-malleable commitments w.r.t. opening cannot be proved secure via most natural black-box reductions. This result extends to also rule out bi-directional two-message non-malleable commitments w.r.t. opening in the synchronous or asynchronous setting. ° Our protocol, together with our impossibility result, also resolves the round complexity of block-wise non-malleable codes (Chandran et al) w.r.t. natural black-box reductions.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.12"}, {"primary_key": "4096720", "vector": [], "sparse_vector": [], "title": "Amplification and Derandomization without Slowdown.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We present techniques for decreasing the error probability of randomized algorithms and for converting randomized algorithms to deterministic (nonuniform) algorithms. Unlike most existing techniques that involve repetition of the randomized algorithm and hence a slowdown, our techniques produce algorithms with a similar run-time to the original randomized algorithms. The amplification technique is related to a certain stochastic multi-armed bandit problem. The derandomization technique - which is the main contribution of this work - points to an intriguing connection between derandomization and sketching/sparsification. We demonstrate the techniques by showing algorithms for approximating free games (constraint satisfaction problems on dense bipartite graphs).", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.87"}, {"primary_key": "4096721", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON> and Polynomial Curve <PERSON>.", "authors": ["<PERSON>en<PERSON><PERSON>wami", "<PERSON>"], "summary": "We consider the robust curve fitting problem, for both algebraic and Fourier (trigonometric) polynomials, in the presence of outliers. In particular, we study the model of <PERSON><PERSON><PERSON> and <PERSON><PERSON> (STOC 2002), who were motivated by applications in computer vision. In their model, the input data consists of ordered pairs (x i , y i ) ε [-1, 1] × [-1, 1], i = 1, 2,..., N, and there is an unknown degree-d polynomial p such that for all but ρ fraction of the i, we have |p(x i ) - y i |≤ δ. Unlike Arora-Khot, we also study the trigonometric setting, where the input is from T × [-1, 1], where T is the unit circle. In both scenarios, the i corresponding to errors are chosen randomly, and for such i the errors in the yi can be arbitrary. The goal is to output a degree-d polynomial q such that ||p - q|| ∞ is small (for example, O(δ)). <PERSON><PERSON><PERSON> and <PERSON><PERSON> could achieve a polynomial-time algorithm only for ρ = 0. <PERSON><PERSON><PERSON> et al. observed that a simple median-based algorithm can correct errors if the desired accuracy δ is large enough. (Larger δ makes the output guarantee easier to achieve, which seems to typically outweigh the weaker input promise.) We dramatically expand the range of parameters for which recovery of q is possible in polynomial time. Specifically, we show that there are polynomial-time algorithms in both settings that recover q up to l∞ error O(δ.99) provided 1) ρ ≤/c1log d and δ ≥ 1/(log d)c, or 2) ρ ≤ c1/log log d/log2 d and δ ≥ 1/dc. Here c is any constant and c1 is a small enough constant depending on c. The number of points that suffices is N = Õ(d) in the trigonometric setting for random x i or arbitrary x i that are roughly equally spaced, or in the algebraic setting when the x i are chosen according to the Chebyshev distribution, and N = Õ(d2) in the algebraic setting with random (or roughly equally spaced) x i .", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.75"}, {"primary_key": "4096722", "vector": [], "sparse_vector": [], "title": "An Average-Case Depth Hierarchy Theorem for Higher Depth.", "authors": ["<PERSON>"], "summary": "We extend the recent hierarchy results of <PERSON><PERSON>, <PERSON> and <PERSON> [1] to address circuits of almost logarithmic depth. Our proof uses the same basic approach as [1] but a number of small differences enables us to obtain a stronger result by a significantly shorter proof.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.18"}, {"primary_key": "4096723", "vector": [], "sparse_vector": [], "title": "Structure of Protocols for XOR Functions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Let f be a boolean function on n variables. Its associated XOR function is the two-party function F(x, y) = f(x xor y). We show that, up to polynomial factors, the deterministic communication complexity of F is equal to the parity decision tree complexity of f. This relies on a novel technique of entropy reduction for protocols, combined with existing techniques in Fourier analysis and additive combinatorics.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.38"}, {"primary_key": "4096724", "vector": [], "sparse_vector": [], "title": "Universal Simulation of Directed Systems in the Abstract Tile Assembly Model Requires Undirectedness.", "authors": ["<PERSON>", "<PERSON>", "Trent <PERSON>"], "summary": "As a mathematical model of tile-based self-assembling systems, Winfree's abstract Tile Assembly Model (aTAM) has proven to be a remarkable platform for studying and understanding the behaviors and powers of self-assembling systems. Furthermore, as it is capable of Turing universal computation, the aTAM allows algorithmic self-assembly, in which the components can be designed so that the rules governing their behaviors force them to inherently execute prescribed algorithms as they combine. This power has yielded a wide variety of theoretical results in the aTAM utilizing algorithmic self-assembly to design systems capable of performing complex computations and forming extremely intricate structures. Adding to the completeness of the model, in FOCS 2012 the aTAM was shown to also be intrinsically universal, which means that there exists one single tile set such that for any arbitrary input aTAM system, that tile set can be configured into a \"seed\" structure which will then cause self-assembly using that tile set to simulate the input system, capturing its full dynamics modulo only a scale factor. However, the \"universal simulator\" of that result makes use of nondeterminism in terms of the tiles placed in several key locations when different assembly sequences are followed. This nondeterminism remains even when the simulator is simulating a system which is directed, meaning that it has exactly one unique terminal assembly and for any given location, no matter which assembly sequence is followed, the same tile type is always placed there. The question which then arose was whether or not that nondeterminism is fundamentally required, and if any universal simulator must in fact utilize more nondeterminism than directed systems when simulating them. In this paper, we answer that question in the affirmative: the class of directed systems in the aTAM is not intrinsically universal, meaning there is no universal simulator for directed systems which itself is always directed. This result provides a powerful insight into the role of nondeterminism in self-assembly, which is itself a fundamentally nondeterministic process occurring via unguided local interactions. Furthermore, to achieve this result we leverage powerful results of computational complexity hierarchies, including tight bounds on both best and worst-case complexities of decidable languages, to tailor design systems with precisely controllable space resources available to computations embedded within them. We also develop novel techniques for designing systems containing subsystems with disjoint, mutually exclusive computational powers. The main result will be important in the development of future simulation systems, and the supporting design techniques and lemmas will provide powerful tools for the development of future aTAM systems as well as proofs of their computational abilities.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.90"}, {"primary_key": "4096725", "vector": [], "sparse_vector": [], "title": "Rectangular Kronecker Coefficients and Plethysms in Geometric Complexity Theory.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The geometric complexity theory program is an approach to separate algebraic complexity classes, more precisely to show the superpolynomial growth of the determinantal complexity dc(per m ) of the permanent polynomial. <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> showed that the vanishing behaviour of rectangular Kronecker coefficients could in principle be used to show some lower bounds on dc(per m ) and they conjectured that superpolynomial lower bounds on dc(per m ) could be shown in this way. In this paper we disprove this conjecture by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, i.e., we prove that the vanishing of rectangular Kronecker coefficients cannot be used to prove superpolynomial lower bounds on dc(per m ).", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.50"}, {"primary_key": "4096726", "vector": [], "sparse_vector": [], "title": "Better Unrelated Machine Scheduling for Weighted Completion Time via Random Offsets from Non-uniform Distributions.", "authors": ["Sungjin Im", "<PERSON>"], "summary": "In this paper we consider the classic scheduling problem of minimizing total weighted completion time on unrelated machines when jobs have release times, i.e, R|r ij | Σ j w j C j using the three-field notation. For this problem, a 2-approximation is known based on a novel convex programming (<PERSON><PERSON> 2001 by <PERSON><PERSON><PERSON><PERSON>). It has been a long standing open problem if one can improve upon this 2-approximation (Open Problem 8 in J. of Sched. 1999 by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>). We answer this question in the affirmative by giving a 1.8786-approximation. We achieve this via a surprisingly simple linear programming, but a novel rounding algorithm and analysis. A key ingredient of our algorithm is the use of random offsets sampled from non-uniform distributions. We also consider the preemptive version of the problem, i.e, R|r ij , pmtn|Σ j w j C j . We again use the idea of sampling offsets from non-uniform distributions to give the first better than 2-approximation for this problem. This improvement also requires use of a configuration LP with variables for each job's complete schedules along with more careful analysis. For both non-preemptive and preemptive versions, we break the approximation barrier of 2 for the first time.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.23"}, {"primary_key": "4096727", "vector": [], "sparse_vector": [], "title": "How to Determine if a Random Graph with a Fixed Degree Sequence Has a Giant Component.", "authors": ["<PERSON>", "Guillem Perarnau", "<PERSON><PERSON>", "<PERSON>"], "summary": "The traditional <PERSON><PERSON><PERSON><PERSON> model of a random network is of little use in modelling the type of complex networks which modern researchers study. In this graph, every pair of vertices is equally likely to be connected by an edge. However, 21st century networks are of diverse nature and usually exhibit inhomogeneity among their nodes. This motivates the study, for a fixed degree sequence D=(d1, ..., dn), of a uniformly chosen simple graph G(D) on {1, ..., n} where the vertex i has degree di. In this paper, we study the existence of a giant component in G(D). A heuristic argument suggests that a giant component in G(D) will exist provided that the sum of the squares of the degrees is larger than twice the sum of the degrees. In 1995, <PERSON><PERSON><PERSON> and <PERSON> essentially proved this to be the case when the degree sequence D under consideration satisfies certain technical conditions [Random Structures & Algorithms, 6:161-180]. This work has attracted considerable attention, has been extended to degree sequences under weaker conditions and has been applied to random models of a wide range of complex networks such as the World Wide Web or biological systems operating at a sub-molecular level. Nevertheless, the technical conditions on D restrict the applicability of the result to sequences where the vertices of high degree play no important role. This is a major problem since it is observed in many real-world networks, such as scale-free networks, that vertices of high degree (the so-called hubs) are present and play a crucial role. In this paper we characterize when a uniformly random graph with a fixed degree sequence has a giant component. Our main result holds for every degree sequence of length n provided that a minor technical condition is satisfied. The typical structure of G(D) when D does not satisfy this condition is relatively simple and easy to understand. Our result gives a unified criterion that implies all the known results on the existence of a giant component in G(D), including both the generalizations of the Molloy-Reed result and results on more restrictive models. Moreover, it turns out that the heuristic argument used in all the previous works on the topic, does not extend to general degree sequences.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.79"}, {"primary_key": "4096728", "vector": [], "sparse_vector": [], "title": "A Fast and Simple Unbiased Estimator for Network (Un)reliability.", "authors": ["<PERSON>"], "summary": "The following procedure yields an unbiased estimator for the disconnection probability of an n-vertex graph with minimum cut c if every edge fails independently with probability p: (i) contract every edge independently with probability 1- n -2/c , then (ii) recursively compute the disconnection probability of the resulting tiny graph if each edge fails with probability n 2/c p. We give a short, simple, self-contained proof that this estimator can be computed in linear time and has relative variance O(n 2 ). Combining these two facts with a standard sparsification argument yields an O(n 3 log n)-time algorithm for estimating the (un)reliability of a network. We also show how the technique can be used to create unbiased samples of disconnected networks.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.96"}, {"primary_key": "4096729", "vector": [], "sparse_vector": [], "title": "Optimal Quantile Approximation in Streams.", "authors": ["<PERSON><PERSON><PERSON> <PERSON>", "<PERSON>", "Edo Liberty"], "summary": "This paper resolves one of the longest standing basic problems in the streaming computational model. Namely, optimal construction of quantile sketches. An ε approximate quantile sketch receives a stream of items x1, ⋯ ,xn and allows one to approximate the rank of any query item up to additive error ε n with probability at least 1-δ.The rank of a query x is the number of stream items such that x i ≤ x. The minimal sketch size required for this task is trivially at least 1/ε.<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> obtain a O((1/ε)log(1/ε)) space sketch for a fixed δ.Without restrictions on the nature of the stream or the ratio between ε and n, no better upper or lower bounds were known to date. This paper obtains an O((1/ε)log log (1/δ)) space sketch and a matching lower bound. This resolves the open problem and proves a qualitative gap between randomized and deterministic quantile sketching for which an Ω((1/ε)log(1/ε)) lower bound is known. One of our contributions is a novel representation and modification of the widely used merge-and-reduce construction. This modification allows for an analysis which is both tight and extremely simple. The same technique was reported, in private communications, to be useful for improving other sketching objectives and geometric coreset constructions.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.17"}, {"primary_key": "4096730", "vector": [], "sparse_vector": [], "title": "Linear Hashing Is Awesome.", "authors": ["<PERSON>"], "summary": "The most classic textbook hash function, e.g. taught in CLRS [MIT Press'09], is h(x) = ((ax + b) mod p) mod m, (◊) where x, a, b ε {0, 1, …, p –} and a, b are chosen uniformly at random. It is known that (◊) is 2-independent and almost uniform provided p is a prime and p » m. This implies that when using (◊) to build a hash table with chaining that contains n ≤ m keys, the expected query time is O(1) and the expected length of the longest chain is O(√n). This result holds for any 2-independent hash function. No hash function can improve on the expected query time, but the upper bound on the expected length of the longest chain is not known to be tight for (◊). Partially addressing this problem, <PERSON><PERSON> et al. [STOC'97] proved the existence of a class of linear hash functions such that the expected length of the longest chain is (√n) and leave as an open problem to decide which nontrivial properties (◊) has. We make the first progress on this fundamental problem, by showing that the expected length of the longest chain is at most n1/3o(1) which means that the performance of (◊) is similar to that of a 3-independent hash function for which we can prove an upper bound of O(n1/3). As a lemma we show that within a fixed set of integers there are few pairs such that the height of the ratio of the pairs are small. Given two non-zero coprime integers n, m ε Z with the height of n/m is max t{|n|, |m|}, and the height is a way of measuring how complex a fraction is. This is proved using a mixture of techniques from additive combinatorics and number theory, and we believe that the result might be of independent interest. For a natural variation of (◊), we show that it is possible to apply second order moment bounds even when a hash value is fixed. As a consequence: For min-wise hashing it was known that any key from a set of n keys has the smallest hash value with probability O (1√n). We improve this to n–1+o(1). For linear probing it was known that the worst case expected query time is O (√n). We improve this to no(1).", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.45"}, {"primary_key": "4096731", "vector": [], "sparse_vector": [], "title": "Commutativity in the Algorithmic Lovász Local Lemma.", "authors": ["<PERSON>"], "summary": "We consider the recent formulation of the Algorithmic Lovász Local Lemma [1], [2] for finding objects that avoid \"bad features\", or \"flaws\". It extends the Moser-Tardos resampling algorithm [3] to more general discrete spaces. At each step the method picks a flaw present in the current state and \"resamples\" it using a \"resampling oracle\" provided by the user. However, it is less flexible than the Moser-Tardos method since [1], [2] require a specific flaw selection rule, whereas [3] allows an arbitrary rule (and thus can potentially be implemented more efficiently). We formulate a new \"commutativity\" condition, and prove that it is sufficient for an arbitrary rule to work. It also enables an efficient parallelization under an additional assumption. We then show that existing resampling oracles for perfect matchings and permutations do satisfy this condition. Finally, we generalize the precondition in [2] (in the case of symmetric potential causality graphs). This unifies special cases that previously were treated separately.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.88"}, {"primary_key": "4096732", "vector": [], "sparse_vector": [], "title": "Approximate Gaussian Elimination for Laplacians - Fast, Sparse, and Simple.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show how to perform sparse approximate Gaussian elimination for Laplacian matrices. We present a simple, nearly linear time algorithm that approximates a Laplacian by the product of a sparse lower triangular matrix with its transpose. This gives the first nearly linear time solver for Laplacian systems that is based purely on random sampling, and does not use any graph theoretic constructions such as low-stretch trees, sparsifiers, or expanders. Our algorithm performs a subsampled Cho<PERSON>ky factorization, which we analyze using matrix martingales. As part of the analysis, we give a proof of a concentration inequality for matrix martingales where the differences are sums of conditionally independent variables.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.68"}, {"primary_key": "4096733", "vector": [], "sparse_vector": [], "title": "Agnostic Estimation of Mean and Covariance.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Santosh S<PERSON>"], "summary": "We consider the problem of estimating the mean and covariance of a distribution from i.i.d. samples in the presence of a fraction of malicious noise. This is in contrast to much recent work where the noise itself is assumed to be from a distribution of known type. The agnostic problem includes many interesting special cases, e.g., learning the parameters of a single Gaussian (or finding the best-fit Gaussian) when a fraction of data is adversarially corrupted, agnostically learning mixtures, agnostic ICA, etc. We present polynomial-time algorithms to estimate the mean and covariance with error guarantees in terms of information-theoretic lower bounds. As a corollary, we also obtain an agnostic algorithm for Singular Value Decomposition.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.76"}, {"primary_key": "4096734", "vector": [], "sparse_vector": [], "title": "Heavy Hitters via Cluster-Preserving Clustering.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the turnstile lp heavy hitters problem with parameter ε, one must maintain a high-dimensional vector xεRn subject to updates of the form update (i,Δ) causing the change xi≤ ← xi + Δ, where iε[n], ΔεR. Upon receiving a query, the goal is to report every \"heavy hitter\" iε[n] with |xi| ≥ε|x|p as part of a list L⊆[n] of size O(1/εp), i.e. proportional to the maximum possible number of heavy hitters. For any pε(0,2] the COUNTSKETCH of [CCFC04] solves lp heavy hitters using O(ε-plog n) words of space with O(log n) update time, O(nlog n) query time to output L, and whose output after any query is correct with high probability (whp) 1 - 1/poly(n) [JST11, Section 4.4]. This space bound is optimal even in the strict turnstile model [JST11] in which it is promised that xi ≥ 0 for all iε[n] at all points in the stream, but unfortunately the query time is very slow. To remedy this, the work [CM05] proposed the \"dyadic trick\" for the COUNTMIN sketch for p = 1 in the strict turnstile model, which to maintain whp correctness achieves suboptimal space O(ε-1log2 n), worse update time O(log2 n), but much better query time O(ε-1poly(log n)). An extension to all pε(0,2] appears in [KNPW11, Theorem 1], and can be obtained from [Pag13]. We show that this tradeoff between space and update time versus query time is unnecessary. We provide a new algorithm, EXPANDERSKETCH, which in the most general turnstile model achieves optimal O(ε-plog n) space, O(log n) update time, and fast O(ε-ppoly(log n)) query time, providing correctness whp. In fact, a simpler version of our algorithm for p = 1 in the strict turnstile model answers queries even faster than the \"dyadic trick\" by roughly a log n factor, dominating it in all regards. Our main innovation is an efficient reduction from the heavy hitters to a clustering problem in which each heavy hitter is encoded as some form of noisy spectral cluster in a much bigger graph, and the goal is to identify every cluster. Since every heavy hitter must be found, correctness requires that every cluster be found. We thus need a \"cluster-preserving clustering\" algorithm, that partitions the graph into clusters with the promise of not destroying any original cluster. To do this we first apply standard spectral graph partitioning, and then we use some novel combinatorial techniques to modify the cuts obtained so as to make sure that the original clusters are sufficiently preserved. Our cluster-preserving clustering may be of broader interest much beyond heavy hitters.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.16"}, {"primary_key": "4096735", "vector": [], "sparse_vector": [], "title": "Optimizing Star-Convex Functions.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Star-convexity is a significant relaxation of the notion of convexity, that allows for functions that do not have (sub)gradients at most points, and may even be discontinuous everywhere except at the global optimum. We introduce a polynomial time algorithm for optimizing the class of star-convex functions, under no Lipschitz or other smoothness assumptions whatsoever, and no restrictions except exponential boundedness on a region about the origin, and Lebesgue measurability. The algorithm's performance is polynomial in the requested number of digits of accuracy and the dimension of the search domain. This contrasts with the previous best known algorithm of Nesterov and Polyak which has exponential dependence on the number of digits of accuracy, but only n! dependence on the dimension n (where ! is the matrix multiplication exponent), and which further requires <PERSON><PERSON><PERSON><PERSON> second differentiability of the function [1].", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.71"}, {"primary_key": "4096736", "vector": [], "sparse_vector": [], "title": "Improved Two-Source Extractors, and Affine Extractors for Polylogarithmic Entropy.", "authors": ["<PERSON><PERSON>"], "summary": "In a recent breakthrough [1], <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> gave an explicit two-source extractor for min-entropy k ≥ log C n for some large enough constant C, where n is the length of the source. However, their extractor only outputs one bit. In this paper, we improve the output of the two-source extractor to k Ω(1) , while the error remains n -Ω(1) and the extractor remains strong in the second source. In the non-strong case, the output can be increased to k. Our improvement is obtained by giving a better extractor for (q, t, γ) non-oblivious bit-fixing sources, which can output t Ω(1) bits instead of one bit as in [1]. We also give the first explicit construction of deterministic extractors for affine sources over F 2 , with entropy k ≥ log C n for some large enough constant C, where n is the length of the source. Previously the best known results are by <PERSON><PERSON><PERSON> [2], <PERSON><PERSON><PERSON><PERSON> [3] and <PERSON> [4], which require the affine source to have entropy at least Ω(n/√log log n). Our extractor outputs k Ω(1) bits with error n -Ω(1) . This is done by reducing an affine source to a non-oblivious bit-fixing source, where we adapt the alternating extraction based approach in previous work on independent source extractors [5] to the affine setting. Our affine extractors also imply improved extractors for circuit sources studied in [6]. We further extend our results to the case of zero-error dispersers, and give two applications in data structures that rely crucially on the fact that our two-source or affine extractors have large output size.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.26"}, {"primary_key": "4096737", "vector": [], "sparse_vector": [], "title": "Indistinguishability Obfuscation from DDH-Like Assumptions on Constant-Degree Graded Encodings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "All constructions of general purpose indistinguishability obfuscation (IO) rely on either meta-assumptions that encapsulate an exponential family of assumptions (e.g., <PERSON>, <PERSON> and <PERSON>, CRYPTO 2014 and Lin, EUROCRYPT 2016), or polynomial families of assumptions on graded encoding schemes with a high polynomial degree/multilinearity (e.g., <PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON>, FOCS 2014). We present a new construction of IO, with a security reduction based on two assumptions: (a) a DDH-like assumption - called the sSXDH assumption - on constant degree graded encodings, and (b) the existence of polynomial-stretch pseudorandom generators (PRG) in NC0. Our assumption on graded encodings is simple, has constant size, and does not require handling composite-order rings. This narrows the gap between the mathematical objects that exist (bilinear maps, from elliptic curve groups) and ones that suffice to construct general purpose indistinguishability obfuscation.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.11"}, {"primary_key": "4096738", "vector": [], "sparse_vector": [], "title": "Accelerated Newton Iteration for Roots of Black Box Polynomials.", "authors": ["<PERSON>", "Santosh S<PERSON>"], "summary": "We study the problem of computing the largest root of a real rooted polynomial p(x) to within error 'z' given only black box access to it, i.e., for any x, the algorithm can query an oracle for the value of p(x), but the algorithm is not allowed access to the coefficients of p(x). A folklore result for this problem is that the largest root of a polynomial can be computed in O(n log (1/z)) polynomial queries using the Newton iteration. We give a simple algorithm that queries the oracle at only O(log n log(1/z)) points, where n is the degree of the polynomial. Our algorithm is based on a novel approach for accelerating the Newton method by using higher derivatives.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.83"}, {"primary_key": "4096739", "vector": [], "sparse_vector": [], "title": "Polynomial-Time Tensor Decompositions with Sum-of-Squares.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We give new algorithms based on the sum-of-squares method for tensor decomposition. Our results improve the best known running times from quasi-polynomial to polynomial for several problems, including decomposing random overcomplete 3-tensors and learning overcomplete dictionaries with constant relative sparsity. We also give the first robust analysis for decomposing overcomplete 4-tensors in the smoothed analysis model. A key ingredient of our analysis is to establish small spectral gaps in moment matrices derived from solutions to sum-of-squares relaxations. To enable this analysis we augment sum-of-squaresrelaxations with spectral analogs of maximum entropy constraints.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.54"}, {"primary_key": "4096740", "vector": [], "sparse_vector": [], "title": "Computing Maximum Flow with Augmenting Electrical Flows.", "authors": ["Aleksander Madry"], "summary": "We present an Õ (m 7/10 U 1/7)-time algorithm for the maximum s-t flow problem (and the minimum s-t cut problem) in directed graphs with m arcs and largest integer capacity U. This matches the running time of the Õ (mU)10/7)- time algorithm of <PERSON><PERSON> [30] in the unit-capacity case, and improves over it, as well as over the Õ (m√n log U)-time algorithm of <PERSON> and <PERSON> [25], whenever U is moderately large and the graph is sufficiently sparse. By well-known reductions, this also implies similar running time improvements for the maximum-cardinality bipartite b-matching problem. One of the advantages of our algorithm is that it is significantly simpler than the ones presented in [30] and [25]. In particular, these algorithms employ a sophisticated interior-point method framework, while our algorithm is cast directly in the classic augmenting path setting that almost all the combinatorial maximum flow algorithms use. At a high level, the presented algorithm takes a primal dual approach in which each iteration uses electrical flows computations both to find an augmenting s-t flow in the current residual graph and to update the dual solution. We show that by maintain certain careful coupling of these primal and dual solutions we are always guaranteed to make significant progress.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.70"}, {"primary_key": "4096741", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON> Prize Lecture: Complexity of Communication in Markets.", "authors": ["<PERSON><PERSON>"], "summary": "Summary form only given. A classical point of view in Economic Theory is that prices in markets serve as a communication mechanism between the participants (buyers and sellers) in the market. I will analyze the communication complexity (in the standard sense used in Theoretical Computer Science) required for obtaining efficiency and equilibrium in several scenarios of markets of indivisible goods.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.97"}, {"primary_key": "4096742", "vector": [], "sparse_vector": [], "title": "Lipschitz Extensions for Node-Private Graph Statistics and the Generalized Exponential Mechanism.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Lipschitz extensions were proposed as a tool for designing differentially private algorithms for approximating graph statistics. However, efficiently computable Lipschitz extensions were known only for 1-dimensional functions (that is, functions that output a single real value). We study efficiently computable Lipschitz extensions for multi-dimensional (that is, vector-valued) functions on graphs. We show that, unlike for 1-dimensional functions, Lipschitz extensions of higher-dimensional functions on graphs do not always exist, even with a non-unit stretch. We design Lipschitz extensions with small stretch for the sorted degree list and degree distribution of a graph, viewed as functions from the space of graphs equipped with the node distance into real space equipped with l1. Our extensions are from the space of bounded-degree graphs to the space of arbitrary graphs. The extensions use convex programming and are efficiently computable. We also develop a new tool for employing Lipschitz extensions in differentially private algorithms that operate with no prior knowledge of the graph (and, in particular, no knowledge of the degree bound). Specifically, we generalize the exponential mechanism, a widely used tool in data privacy. The exponential mechanism is given a collection of score functions that map datasets to real values. It returns the name of the function with nearly minimum value on the dataset. Our generalized exponential mechanism provides better accuracy than the standard exponential mechanism when the sensitivity of an optimal score function is much smaller than the maximum sensitivity over all score functions. We use our Lipschitz extensions and the generalized exponential mechanism to design a node differentially private algorithm for approximating the degree distribution of a sensitive graph. Our algorithm is much more accurate than those from previous work. In particular, our algorithm is accurate on all graphs whose degree distributions decay at least as fast as those of \"scale-free\" graphs. Using our methodology, we also obtain more accurate node-private algorithms for 1-dimensional statistics.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.60"}, {"primary_key": "4096743", "vector": [], "sparse_vector": [], "title": "Fast Learning Requires Good Memory: A Time-Space Lower Bound for Parity Learning.", "authors": ["<PERSON><PERSON>"], "summary": "We prove that any algorithm for learning parities requires either a memory of quadratic size or an exponential number of samples. This proves a recent conjecture of <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON> [15] and shows that for some learning problems a large storage space is crucial. More formally, in the problem of parity learning, an unknown string x ϵ {0,1} n was chosen uniformly at random. A learner tries to learn x from a stream of samples (a 1 , b 1 ), (a 2 , b 2 )..., where each at is uniformly distributed over {0,1} n and bt is the inner product of a t and x, modulo 2. We show that any algorithm for parity learning, that uses less than n 2 /25 bits of memory, requires an exponential number of samples. Previously, there was no non-trivial lower bound on the number of samples needed, for any learning problem, even if the allowed memory size is O(n) (where n is the space needed to store one sample). We also give an application of our result in the field of bounded-storage cryptography. We show an encryption scheme that requires a private key of length n, as well as time complexity of n per encryption/decryption of each bit, and is provenly and unconditionally secure as long as the attacker uses less than n 2 /25 memory bits and the scheme is used at most an exponential number of times. Previous works on bounded-storage cryptography assumed that the memory size used by the attacker is at most linear in the time needed for encryption/decryption.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.36"}, {"primary_key": "4096744", "vector": [], "sparse_vector": [], "title": "The Hilbert Function, Algebraic Extractors, and Recursive Fourier Sampling.", "authors": ["<PERSON>"], "summary": "In this paper, we apply tools from algebraic geometry to prove new results concerning extractors for algebraic sets, the recursive Fourier sampling problem, and VC dimension. We present a new construction of an extractor which works for algebraic sets defined by polynomials over GF(2) of substantially higher degree than the current state-of-the-art construction. We also exactly determine the GF(2)-polynomial degree of the recursive Fourier sampling problem and use this to provide new partial results towards a circuit lower bound for this problem. Finally, we answer a question concerning VC dimension, interpolation degree and the Hilbert function.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.29"}, {"primary_key": "4096745", "vector": [], "sparse_vector": [], "title": "How Limited Interaction Hinders Real Communication (and What It Means for Proof and Circuit Complexity).", "authors": ["Susanna F. de Rezende", "<PERSON>", "<PERSON>"], "summary": "We obtain the first true size-space trade-offs for the cutting planes proof system, where the upper bounds hold for size and total space for derivations with constantsize coefficients, and the lower bounds apply to length and formula space (i.e., number of inequalities in memory) even for derivations with exponentially large coefficients. These are also the first trade-offs to hold uniformly for resolution, polynomial calculus and cutting planes, thus capturing the main methods of reasoning used in current state-of-the-art SAT solvers. We prove our results by a reduction to communication lower bounds in a round-efficient version of the real communication model of [<PERSON><PERSON><PERSON> '98], drawing on and extending techniques in [<PERSON><PERSON> and <PERSON> '99] and [G̈öos et al. '15]. The communication lower bounds are in turn established by a reduction to trade-offs between cost and number of rounds in the game of [<PERSON><PERSON><PERSON> and <PERSON><PERSON> '85] played on directed acyclic graphs. As a by-product of the techniques developed to show these proof complexity trade-off results, we also obtain an exponential separation between monotone-AC i-1 and monotone-AC i , improving exponentially over the superpolynomial separation in [<PERSON><PERSON> and <PERSON> '99]. That is, we give an explicit Boolean function that can be computed by monotone Boolean circuits of depth log i n and polynomial size, but for which circuits of depth O(log i-1 n) require exponential size.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.40"}, {"primary_key": "4096746", "vector": [], "sparse_vector": [], "title": "Exponential Lower Bounds for Monotone Span Programs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Monotone span programs are a linear-algebraic model of computation which were introduced by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> in 1993 [1]. They are known to be equivalent to linear secret sharing schemes, and have various applications in complexity theory and cryptography. Lower bounds for monotone span programs have been difficult to obtain because they use non-monotone operations to compute monotone functions, in fact, the best known lower bounds are quasipolynomial for a function in (nonmonotone) P [2]. A fundamental open problem is to prove exponential lower bounds on monotone span program size for any explicit function. We resolve this open problem by giving exponential lower bounds on monotone span program size for a function in monotone P. This also implies the first exponential lower bounds for linear secret sharing schemes. Our result is obtained by proving exponential lower bounds using <PERSON><PERSON><PERSON><PERSON>'s rank method [3], a measure that is strong enough to prove lower bounds for many monotone models. As corollaries we obtain new proofs of exponential lower bounds for monotone formula size, monotone switching network size, and the first lower bounds for monotone comparator circuit size for a function in monotone P. We also obtain new polynomial degree lower bounds for Nullstellensatz refutations using an interpolation theorem of <PERSON><PERSON><PERSON> and <PERSON><PERSON> [4]. Finally, we obtain quasipolynomial lower bounds on the rank measure for the st-connectivity function, implying tight bounds for st-connectivity in all of the computational models mentioned above.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.51"}, {"primary_key": "4096747", "vector": [], "sparse_vector": [], "title": "Max-Information, Differential Privacy, and Post-selection Hypothesis Testing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we initiate a principled study of how the generalization properties of approximate differential privacy can be used to perform adaptive hypothesis testing, while giving statistically valid p-value corrections. We do this by observing that the guarantees of algorithms with bounded approximate max-information are sufficient to correct the p-values of adaptively chosen hypotheses, and then by proving that algorithms that satisfy (∈,δ)-differential privacy have bounded approximate max information when their inputs are drawn from a product distribution. This substantially extends the known connection between differential privacy and max-information, which previously was only known to hold for (pure) (∈,0)-differential privacy. It also extends our understanding of max-information as a partially unifying measure controlling the generalization properties of adaptive data analyses. We also show a lower bound, proving that (despite the strong composition properties of max-information), when data is drawn from a product distribution, (∈,δ)-differentially private algorithms can come first in a composition with other algorithms satisfying max-information bounds, but not necessarily second if the composition is required to itself satisfy a nontrivial max-information bound. This, in particular, implies that the connection between (∈,δ)-differential privacy and max-information holds only for inputs drawn from product distributions, unlike the connection between (∈,0)-differential privacy and max-information.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.59"}, {"primary_key": "4096748", "vector": [], "sparse_vector": [], "title": "On the Communication Complexity of Approximate Fixed Points.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the two-party communication complexity of finding an approximate <PERSON><PERSON>wer fixed point of a composition of two Lipschitz functions g o f: [0,1] n → [0,1] n , where <PERSON> holds f and <PERSON> holds g. We prove an exponential (in n) lower bound on the deterministic communication complexity of this problem. Our technical approach is to adapt the <PERSON><PERSON>-<PERSON> simulation theorem (FOCS 1999) into geometric settings, thereby \"smoothly lifting\" the deterministic query lower bound for finding an approximate fixed point (<PERSON><PERSON>, <PERSON> and <PERSON>, Complexity 1989) from the oracle model to the two-party model. Our results also suggest an approach to the well-known open problem of proving strong lower bounds on the communication complexity of computing approximate Nash equilibria. Specifically, we show that a slightly \"smoother\" version of our fixed-point computation lower bound (by an absolute constant factor) would imply that: The deterministic two-party communication complexity of finding an ∈ = Ω(1/log 2 N)-approximate Nash equilibrium in an N × N bimatrix game (where each player knows only his own payoff matrix) is at least N γ for some constant γ > 0. (In contrast, the nondeterministic communication complexity of this problem is only O(log 6 N)). ; The deterministic (Number-In-Hand) multiparty communication complexity of finding an ∈ = Ω(1)-Nash equilibrium in a k-player constant-action game is at least 2 Ω(k/log k) (while the nondeterministic communication complexity is only O(k)).", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.32"}, {"primary_key": "4096749", "vector": [], "sparse_vector": [], "title": "Settling the Complexity of Computing Approximate Two-Player Nash Equilibria.", "authors": ["<PERSON>via<PERSON>"], "summary": "We prove that there exists a constant ε > 0 such that, assuming the Exponential Time Hypothesis for PPAD, computing an ε-approximate Nash equilibrium in a two-player (n × n) game requires quasi-polynomial time, nlog1-o(1) n. This matches (up to the o(1) term) the algorithm of <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> [54]. Our proof relies on a variety of techniques from the study of probabilistically checkable proofs (PCP), this is the first time that such ideas are used for a reduction between problems inside PPAD. En route, we also prove new hardness results for computing Nash equilibria in games with many players. In particular, we show that computing an ε-approximate Nash equilibrium in a game with n players requires 2Ω(n) oracle queries to the payoff tensors. This resolves an open problem posed by <PERSON> and <PERSON><PERSON> [43], <PERSON><PERSON><PERSON><PERSON> [13], and <PERSON> et al. [28]. In fact, our results for n-player games are stronger: they hold with respect to the (ε,δ)-WeakNash relaxation recently introduced by <PERSON><PERSON><PERSON><PERSON> et al. [15].", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.35"}, {"primary_key": "4096750", "vector": [], "sparse_vector": [], "title": "The Salesman&apos;s Improved Paths: A 3/2+1/34 Approximation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We give a new, strongly polynomial algorithm and improved analysis of the metric s-t path TSP. It finds a tour of cost less than 1.53 times the optimum of the subtour elimination LP, while known examples show that 1.5 is a lower bound for the integrality gap. A key new idea is the deletion of some edges of <PERSON><PERSON><PERSON>' trees, and we show that the arising \"reconnection\" problems can be solved for a minor extra cost. On the one hand our algorithm and analysis extend previous tools, at the same time simplifying the framework. On the other hand new tools are introduced, such as a flow problem used for analyzing the reconnection cost, and the use of a set of more and more restrictive minimum cost spanning trees, each of which can still be found by the greedy algorithm. The latter leads to a simple Christofides-like algorithm completely avoiding the computation of a convex combination of spanning trees. Furthermore, the 3/2 target-bound is easily reached in some relevant new cases.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.21"}, {"primary_key": "4096751", "vector": [], "sparse_vector": [], "title": "Compressing Interactive Communication under Product Distributions.", "authors": ["<PERSON>"], "summary": "We study the problem of compressing interactive communication to its information content I, defined as the amount of information that the participants learn about each other's inputs. We focus on the case when the participants' inputs are distributed independently and show how to compress the communication to O(I log 2 I) bits, with no dependence on the original communication cost. This result improves quadratically on previous work by <PERSON><PERSON> (STOC 2016) and essentially matches the well-known lower bound Ω(I).", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.64"}, {"primary_key": "4096752", "vector": [], "sparse_vector": [], "title": "The Number of Solutions for Random Regular NAE-SAT.", "authors": ["<PERSON>", "Nike Sun", "<PERSON><PERSON><PERSON>"], "summary": "Recent work has made substantial progress in understanding the transitions of random constraint satisfaction problems (CSPs). In particular, for several of these models, the exact satisfiability threshold has been rigorously determined, confirming predictions from the statistical physics literature. Here we revisit one of these models, random regular NAE-SAT: knowing the satisfiability threshold, it is natural to study, in the satisfiable regime, the number of solutions in a typical instance. We prove here that these solutions have a well-defined free energy (limiting exponential growth rate), with explicit value matching the one-step replica symmetry breaking prediction. The proof develops new techniques for analyzing a certain \"survey propagation model\" associated to this problem. We believe that these methods may be applicable in a wide class of related problems.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.82"}, {"primary_key": "4096753", "vector": [], "sparse_vector": [], "title": "Fully Dynamic Maximal Matching in Constant Update Time.", "authors": ["<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON> and <PERSON> [FOCS'11] showed that fully dynamic maximal matching can be maintained in general graphs with logarithmic amortized update time. More specifically, starting from an empty graph on n fixed vertices, they devised a randomized algorithm for maintaining maximal matching over any sequence of t edge insertions and deletions with a total runtime of O(t log n) in expectation and O(t log n + n log 2 n) with high probability. Whether or not this runtime bound can be improved towards O(t) has remained an important open problem. Despite significant research efforts, this question has resisted numerous attempts at resolution even for basic graph families such as forests. In this paper, we resolve the question in the affirmative, by presenting a randomized algorithm for maintaining maximal matching in general graphs with constant amortized update time. The optimal runtime bound O(t) of our algorithm holds both in expectation and with high probability. As an immediate corollary, we can maintain 2-approximate vertex cover with constant amortized update time. This result is essentially the best one can hope for (under the unique games conjecture) in the context of dynamic approximate vertex cover, culminating a long line of research. Our algorithm builds on <PERSON><PERSON><PERSON> et al.'s algorithm, but is inherently different and arguably simpler. As an implication of our simplified approach, the space usage of our algorithm is linear in the (dynamic) graph size, while the space usage of <PERSON><PERSON><PERSON> et al.'s algorithm is always at least Ω(n log n). Finally, we present applications to approximate weighted matchings and to distributed networks.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.43"}, {"primary_key": "4096754", "vector": [], "sparse_vector": [], "title": "Amortized Dynamic Cell-Probe Lower Bounds from Four-Party Communication.", "authors": ["<PERSON><PERSON><PERSON>", "Huacheng Yu"], "summary": "This paper develops a new technique for proving amortized, randomized cell-probe lower bounds on dynamic data structure problems. We introduce a new randomized nondeterministic four-party communication model that enables \"accelerated\", error-preserving simulations of dynamic data structures. We use this technique to prove an Ω(n(log n/log log n)2) cell-probe lower bound for the dynamic 2D weighted orthogonal range counting problem (2D-ORC) with n/poly log n updates and n queries, that holds even for data structures with exp(-Ω̃(n)) success probability. This result not only proves the highest amortized lower bound to date, but is also tight in the strongest possible sense, as a matching upper bound can be obtained by a deterministic data structure with worst-case operational time. This is the first demonstration of a \"sharp threshold\" phenomenon for dynamic data structures. Our broader motivation is that cell-probe lower bounds for exponentially small success facilitate reductions from dynamic to static data structures. As a proof-of-concept, we show that a slightly strengthened version of our lower bound would imply an Ω((log n/log log n)2) lower bound for the static 3D-ORC problem with O(n logO(1) n) space. Such result would give a near quadratic improvement over the highest known static cell-probe lower bound, and break the long standing Ω(log n) barrier for static data structures.", "published": "2016-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2016.41"}]