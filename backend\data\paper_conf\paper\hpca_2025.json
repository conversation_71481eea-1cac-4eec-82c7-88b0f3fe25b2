[{"primary_key": "165311", "vector": [], "sparse_vector": [], "title": "BitMoD: Bit-serial Mixture-of-Datatype LLM Acceleration.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Large language models (LLMs) have demonstrated remarkable performance across various machine learning tasks. Yet the substantial memory footprint of LLMs significantly hinders their deployment. In this paper, we improve the accessibility of LLMs through BitMoD1, an algorithm-hardware co-design solution that enables efficient LLM acceleration at low weight precision. On the algorithm side, BitMoD introduces fine-grained data type adaptation that uses a different numerical data type to quantize a group of (e.g., 128) weights. Through the careful design of these new data types, BitMoD is able to quantize LLM weights to very low precision (e.g., 4 bits and 3 bits) while maintaining high accuracy. On the hardware side, BitMoD employs a bitserial processing element to easily support multiple numerical precisions and data types; our hardware design includes two key innovations: First, it employs a unified representation to process different weight data types, thus reducing the hardware cost. Second, it adopts a bit-serial dequantization unit to rescale the per-group partial sum with minimal hardware overhead. Our evaluation on six representative LLMs demonstrates that BitMoD significantly outperforms state-of-the-art LLM quantization and acceleration methods. For discriminative tasks, BitMoD can quantize LLM weights to 4 -bit with < 0.5% accuracy loss on average. For generative tasks, BitMoD is able to quantize LLM weights to 3-bit while achieving better perplexity than prior LLM quantization scheme. Combining the superior model performance with an efficient accelerator design, BitMoD achieves an average of $1.69 \\times$ and $1.48 \\times$ speedups compared to prior LLM accelerators ANT and OliVe, respectively.1Code is available at: https://github.com/yc2367/BitMoD-HPCA-25", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00084"}, {"primary_key": "165312", "vector": [], "sparse_vector": [], "title": "Push Multicast: A Speculative and Coherent Interconnect for Mitigating Manycore CPU Communication Bottleneck.", "authors": ["<PERSON><PERSON><PERSON>", "Yanhua Chen", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "As CPUs scale up to many cores, the bandwidth of the network-on-chip (NoC) and cache can soon become the performance bottleneck. In modern processors, the cache hierarchy plays a reactive role to supply data upon request. In parallel programs, shared data accesses from different cores at different times can consume large cache and NoC bandwidth for the same data. These same-data accesses inherently have redundancy and lead to inefficient cache and NoC bandwidth utilization. In this work, we propose Push Multicast, a speculative and coherent interconnect. We transform the last-level cache into a proactive agent to push data to other sharers upon replying to the demand requester. Pushing enables effective multicasting to reduce LLC and NoC bandwidth consumption. A coherent innetwork filter is proposed to prune the outstanding requests in the routers along the way of the pushed data delivery. Moreover, a dynamic mechanism is designed to pause and resume pushing adaptively. Compared with a system with an L1 Bingo data prefetcher and an L2 Stride prefetcher, Push Multicast achieves an average of $\\mathbf{3 3 \\%}$ NoC bandwidth saving, a geomean of $1.02 \\times$ and a maximum of $1.56 \\times$ speedup in a 16 -core system. In a 64-core system, it further achieves an average of $\\mathbf{4 3 \\%}$ NoC bandwidth saving, along with a geomean of $1.11 \\times$ and a maximum of $2.08 \\times$ speedup.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00115"}, {"primary_key": "165313", "vector": [], "sparse_vector": [], "title": "LEGO: Spatial Accelerator Generation and Optimization for Tensor Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Modern tensor applications, especially foundation models and generative AI applications require multiple input modalities (both vision and language), which increases the demand for flexible accelerator architecture. Existing frameworks suffer from the trade-off between design flexibility and productivity of RTL generation: either limited to very few hand-written templates or cannot automatically generate the RTL. To address this challenge, we propose the LEGO framework, which targets tensor applications and automatically generates spatial architecture design and outputs synthesizable RTL code without handwritten RTL design templates. Leveraging the affine-transformation-based architecture representation, LEGO front end finds interconnections between function units, synthesizes the memory system, and fuses different spatial dataflow designs based on data reuse analysis. LEGO back end then translates the hardware in a primitive-level graph to perform lower-level optimizations, and applies a set of linear-programming algorithms to optimally insert pipeline registers and reduce the overhead of unused logic when switching spatial dataflows. Our evaluation demonstrates that LEGO can achieve $3.2 \\times$ speedup and $2.4 \\times$ energy efficiency compared to previous work Gemmini, and can generate one architecture for diverse modern foundation models in generative AI applications.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00101"}, {"primary_key": "165314", "vector": [], "sparse_vector": [], "title": "VQ-LLM: High-performance Code Generation for Vector Quantization Augmented LLM Inference.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Vector quantization (VQ), which treats a vector as a compression unit, gains increasing research interests for its potential to accelerate large language models (LLMs). Compared to conventional element-wise quantization methods, VQ algorithms can compress weight and KV cache tensors in LLMs with a greater ratio while maintaining the high model accuracy. However, translating a VQ algorithm’s memory reduction into the actual latency improvement is challenging. We profile and analyze the current approach of integrating VQ into computation kernels and show that its major inefficiency lies in the poor access efficiency of codebooks in VQ algorithms and uncoordinated computation dataflow. Meanwhile, the diversity of VQ algorithms (e.g., different vector sizes and entry counts) and LLMs, computation kernels (e.g matrix-matrix/vector multiplication and attention computation) makes it impractical to manually craft efficient kernel implementations for each specific case. In this work, we design and implement VQ-LLM, an efficient fused VQ kernel generation framework. We first introduce a software abstraction called codebook cache to optimize codebook access efficiency and support the integration of VQ with various computations. The codebook cache adaptively stores different entries across the GPU’s memory hierarchy, including off-chip global memory, on-chip shared memory, and registers. Centered around the codebook cache, we design an efficient computation engine that optimizes memory traffic during computations involving codebooks. This compute engine adopts the codebook-centric dataflow and fusion optimizations. Additionally, we provide adaptive heuristics to tailor parameter selection in our optimizations to diverse VQ configurations. Our optimizations achieve the latency reduction of $\\mathbf{6 4. 3 6 \\%}$ to $\\mathbf{9 9. 1 \\%}$ compared to existing open-source implementations. A final comparison with state-of-the-art element-wise quantization methods like AWQ and QoQ shows that our VQ-LLM is practically viable, achieving latencies close or even better latencies to those at equivalent bit-widths, potentially offering greater accuracy.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00112"}, {"primary_key": "165315", "vector": [], "sparse_vector": [], "title": "Efficient Memory Side-Channel Protection for Embedding Generation in Machine Learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Modern machine learning (ML) models need to process both continuous and categorical/discrete feature values, e.g., deep learning recommendation models (DLRMs) rely on users’ categorical features to make recommendations, and large language models (LLMs) take discrete words/tokens as input. ML models process such discrete features by converting them to numerical vectors called embeddings. Unfortunately, embedding table lookups are vulnerable to side-channel attacks, as table indices leak input feature values. Due to the size of the embedding tables, using conventional oblivious computing techniques such as ORAM to protect memory access patterns to the tables incur significant overhead. In this paper, we propose to use a different technique, Deep Hash Embedding (DHE), to secure embedding table accesses, even though it is not commonly used today due to its compute-intensive nature. We investigate three embedding generation methods with side-channel protection: linear scan of the embedding table, embedding table protected by ORAM, and DHE. Our experiments on DLRMs and LLMs show that DHE or a hybrid scheme combining DHE and linear scan can significantly improve both performance and memory footprint compared to the conventional ORAM protection. For DLRM on Criteo datasets, our hybrid scheme improves performance by about $4 \\times$ for large embedding tables, and up to $3.08 \\times$ end-to-end over the optimized ORAM baseline without any loss in accuracy, while reducing the model memory footprint by up to $1116 \\times$. For a GPT-2 LLM, using DHE speeds up the prompt prefill by up to $1.32 \\times$ and decoding by up to $1.07 \\times$ over ORAM, depending on the batch size, with comparable output quality.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00041"}, {"primary_key": "165316", "vector": [], "sparse_vector": [], "title": "Ariadne: A Hotness-Aware and Size-Adaptive Compressed Swap Technique for Fast Application Relaunch and Reduced CPU Usage on Mobile Devices.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Riwei Pan", "Hai<PERSON> Mao", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Qingcai Jiang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "As the memory demands of individual mobile applications continue to grow and the number of concurrently running applications increases, available memory on mobile devices is becoming increasingly scarce. When memory pressure is high, current mobile systems use a RAM-based compressed swap scheme (called ZRAM) to compress unused execution-related data (called anonymous data in Linux) in main memory. This approach avoids swapping data to secondary storage (NAND flash memory) or terminating applications, thereby achieving shorter application relaunch latency.In this paper, we observe that the state-of-the-art ZRAM scheme prolongs relaunch latency and wastes CPU time because it does not differentiate between hot and cold data or leverage different compression chunk sizes and data locality. We make three new observations. First, anonymous data has different levels of hotness. Hot data, used during application relaunch, is usually similar between consecutive relaunches. Second, when compressing the same amount of anonymous data, small-size compression is very fast, while large-size compression achieves a better compression ratio. Third, there is locality in data access during application relaunch.Based on these observations, we propose a hotness-aware and size-adaptive compressed swap scheme, Ariadne, for mobile devices to mitigate relaunch latency and reduce CPU usage. Ariadne incorporates three key techniques. First, a low-overhead hotness-aware data organization scheme aims to quickly identify the hotness of anonymous data without significant overhead. Second, a size-adaptive compression scheme uses different compression chunk sizes based on the data’s hotness level to ensure fast decompression of hot and warm data. Third, a proactive decompression scheme predicts the next set of data to be used and decompresses it in advance, reducing the impact of data swapping back into main memory during application relaunch.We implement and evaluate Ariadne on a commercial smartphone, Google Pixel 7 with the latest Android 14. Our experimental evaluation results show that, on average, Ariadne reduces application relaunch latency by 50% and decreases the CPU usage of compression and decompression procedures by 15% compared to the state-of-the-art compressed swap scheme for mobile devices.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00118"}, {"primary_key": "165317", "vector": [], "sparse_vector": [], "title": "Anaheim: Architecture and Algorithms for Processing Fully Homomorphic Encryption in Memory.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Hyesung Ji", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Fully homomorphic encryption (FHE) is a promising solution for privacy-preserving cloud computing as it enables computations on sensitive data without any risk of data leakage. Despite the significant attention FHE has received, substantial computation and memory demands make it hardly practical for real-world applications. We propose a readily available and practical hardware solution to tackle this problem by using GPUs. GPUs have adequate computational and memory resources to handle complex operations in FHE, including number-theoretic transform (NTT), on which most prior work has deeply focused. However, through detailed analyses, we discover that the performance bottleneck on GPUs is primarily due to simpler element-wise operations, which are limited by off-chip memory (DRAM) bandwidth. Motivated by these observations, we develop Anaheim, a processing-in-memory (PIM) architecture for FHE. We develop optimized FHE execution flows and an end-toend software framework for using PIM with GPUs. Also, we design a versatile PIM unit that handles various modular integer arithmetic PIM instructions, along with an efficient data mapping and associated PIM execution algorithms that minimize data access overhead by leveraging the internal structure of DRAM. Our concerted efforts substantially enhance the performance and energy efficiency of various FHE workloads on GPUs.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00089"}, {"primary_key": "165318", "vector": [], "sparse_vector": [], "title": "QuCLEAR: Clifford Extraction and Absorption for Quantum Circuit Optimization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Quantum computing carries significant potential for addressing practical problems. However, currently available quantum devices suffer from noisy quantum gates, which degrade the fidelity of executed quantum circuits. Therefore, quantum circuit optimization is crucial for obtaining useful results. In this paper, we present QuCLEAR, a compilation framework designed to optimize quantum circuits. QuCLEAR significantly reduces both the two-qubit gate count and the circuit depth through two novel optimization steps. First, we introduce the concept of Clifford Extraction, which extracts Clifford subcircuits to the end of the circuit while optimizing the gates. Second, since Clifford circuits are classically simulatable, we propose Clifford Absorption, which efficiently processes the extracted Clifford subcircuits classically. We demonstrate our framework on quantum simulation circuits, which have wideranging applications in quantum chemistry simulation, manybody physics, and combinatorial optimization problems. Nearterm algorithms such as VQE and QAOA also fall within this category. Experimental results across various benchmarks show that QuCLEAR achieves up to a 77.7% reduction in CNOT gate count and up to an 84.1% reduction in entangling depth compared with state-of-the-art methods.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00023"}, {"primary_key": "165319", "vector": [], "sparse_vector": [], "title": "DPUaudit: DPU-assisted Pull-based Architecture for Near-Zero Cost System Auditing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "System auditing frameworks are crucial for modern data center security, as they record system events to detect intrusions. However, existing software-based auditing frameworks are limited by their high runtime overhead. To address the limitations of software-based frameworks, researchers had proposed a hardware-based auditing framework that offloads log processing to isolated hardware. However, despite using powerful specialized hardware, this approach still suffers from high runtime overhead, which contradicts their efficiency goal. We have identified that the high overhead is due to the pushbased architecture, which involves operating a log sender on the monitored host. Consequently, the existing approach requires heavy software protection mechanisms to secure the log sender, resulting in high runtime overhead.In this paper, we propose a new DPU-assisted pull-based architecture called DPUaudit for hardware-based auditing, which achieves near-zero runtime overhead. Instead of using a log sender, DPUaudit utilizes DPU to actively pull system events from the monitored host. This eliminates the need for heavy mechanisms to handle and safeguard the log sender, achieving highly efficient system auditing. Experimental results show that, on average, DPUaudit only slows down applications on the monitored host by 2.1% for six mainstream data center applications under different workloads, which is at least one order of magnitude smaller than existing approaches, while still ensuring the integrity of audit logs.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00014"}, {"primary_key": "165320", "vector": [], "sparse_vector": [], "title": "SpecMPK: Efficient In-Process Isolation with Speculative and Secure Permission Update Instruction.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhou", "<PERSON>", "<PERSON><PERSON>"], "summary": "In today’s digital landscape, software applications are susceptible to various threats arising from vulnerabilities in unsafe programming languages ($\\mathbf{C}, \\mathrm{C}++$) and speculative out-of-order cores in high-performance computers. Researchers recommended enhancements in both software and hardware for protection against such attacks. In-process isolation is a promising way to mitigate memoryrelated attacks. It compartmentalizes critical data and pointers in a separate memory region and enforces access control to this memory region. Any operation to such memory locations may require a permission adjustment before and after the operation, depending on the required access control. Memory Protection Keys, a recent architecture support, has been adopted by multiple processor vendors to allow access control changes in the user space, leading to lower performance overhead than the conventional system calls (e.g., mprotect). Still, this technology incurs significant performance overhead since the permission update instruction is serialized. Our research demonstrates significant performance improvement by allowing speculative permission updates. However, speculative execution of the permission update instruction may upgrade access permission transiently, leading to potential speculative execution attacks. To prevent such attacks, we propose Speculative Memory Protection Keys (SpecMPK), a lightweight microarchitecture enhancement to examine permission change and block transiently upgraded memory instructions until they become non-squashable. SpecMPK significantly improves performance compared to a serialized domain switch instruction. This work shows an average $\\mathbf{1 2. 2 1 \\%}$ performance improvement for selected SPEC workloads requiring frequent domain switches for various memory safety schemes using memory protection keys.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00039"}, {"primary_key": "165321", "vector": [], "sparse_vector": [], "title": "No Rush in Executing Atomic Instructions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Hardware atomic instructions are the building blocks of the synchronization algorithms. Historically, to guarantee atomicity and consistency, they were implemented using memory fences, committing older memory instructions, and draining the store buffer before initiating the execution of atomics. Unfortunately, the use of such memory fences entails huge performance penalties as it implies execution serialization, thus impeding instruction- and memory-level parallelism. The situation, however, seems to have changed recently. Through experiments on $x 86$ machines, we discovered that current $x 86$ processors manage to comply with the x86-TSO requirements while avoiding the performance overhead introduced by fences (fence-free or unfenced implementation). This paves the way to new potential optimizations to atomic instruction execution. In particular, our simulation experiments modeling unfenced atomics reveal that executing atomic instructions as soon as their operands are ready does not always lead to optimal performance. In fact, this increases the time that other threads should wait to obtain the cacheline. In contended scenarios, delaying the execution of the atomic instruction to minimize the time the cacheline is locked provides superior performance. Based on this observation, we present Rush or Wait (RoW), a hardware mechanism to decide when to execute an atomic instruction. The mechanism is based on a contention predictor that estimates if an atomic will access a contended cacheline. Non-contended atomics execute once their operands are ready. Contended atomics, on the contrary, wait to become the oldest memory instruction and to drain the store buffer to execute, minimizing the contention on the accessed cacheline. Our experimental evaluation shows that RoW reduces execution time on average by 9.2% (and up to 43%) compared to a baseline that executes atomics as soon as the operands are ready, and yet it requires a small area overhead (64 bytes).", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00120"}, {"primary_key": "165322", "vector": [], "sparse_vector": [], "title": "Efficient Caching with A Tag-enhanced DRAM.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>-Power"], "summary": "As SRAM-based caches are hitting a scaling wall, manufacturers are integrating DRAM-based caches into system designs to continue increasing cache sizes. While DRAM caches can improve the performance of memory systems, existing DRAM cache designs suffer from high miss penalties, wasted data movement, and interference between misses and demands. In this paper, we propose TDRAM, a novel DRAM microarchitecture tailored for caching. TDRAM enhances existing DRAM, such as HBM3, by adding small, low-latency mats to store tags and metadata on the same die as the data mats. These mats enable tag and data access in lockstep, in-DRAM tag comparison, and conditional data response based on the comparison result (reducing wasted data transfers), akin to SRAM cache mechanisms. TDRAM further optimizes hit and miss latencies through opportunistic early tag probing. Moreover, TDRAM introduces a flush buffer to store conflicting dirty data on write misses, eliminating data bus turnaround delays on write demands. We evaluate TDRAM in a full-system simulation using a set of HPC workloads with large memory footprints, showing that TDRAM, on average, provides $2.65 \\times$ faster tag checks, $1.23 \\times$ speedup, and 21% less energy consumption compared to state-of-the-art commercial and research designs.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00062"}, {"primary_key": "165323", "vector": [], "sparse_vector": [], "title": "Architecting Space Microdatacenters: A System-level Approach.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Server-based computing in space has been recently proposed due to potential benefits in terms of capability, latency, security, sustainability, and cost. Despite this, there has been no work asking the question: how should we architect systems for server-based computing in space when considering overall cost. This paper presents a Total Cost of Ownership (TCO)-based approach to architecture of server-based computing systems for space (Space Microdatacenters - SμDC) for processing data produced by low Earth orbit (LEO)-based Earth observation (EO) satellites. We show that power of compute is the primary factor in determining SμDC TCO, though the dependence is sublinear. Second, the impact of compute mass, monetary cost, and communication on TCO is relatively insignificant. Third, architectures with the highest $\\frac{\\text { FLOPs }}{{\\mathrm {W}}}$ provide much higher performance per TCO ${\\$}$ even if they have poor $\\frac{\\mathrm{FLOPs}}{\\$}$. We leverage these insights to advocate extreme heterogeneity designs for SμDCs. These designs reduce SμDC TCO by 116× in spite of poor $\\frac{\\mathrm{FLOPs}}{\\$}$ characteristics. We also show that (a) collaborative compute constellations — constellations in which EO satellites are also equipped with compute hardware — further improve SμDC TCO by 1.31 to 1.74×, (b) a distributed architecture reduces TCO by 10% over a monolithic architecture, and (c) low monetary cost of compute can be leveraged to provide near zero cost compute overprovisioning which improves an SμDC’s availability significantly and supports graceful degradation. Overall, this is the first paper on cost-aware architecture and optimization of a SμDC.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00099"}, {"primary_key": "165324", "vector": [], "sparse_vector": [], "title": "ChameleonEC: Exploiting Tunability of Erasure Coding for Low-Interference Repair.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>ng Shen", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Erasure coding provides fault tolerance in a storage-efficient manner, yet it introduces a high repair penalty. We uncover via trace-driven experiments that the substantial repair traffic in erasure coding is prone to entangling with the foreground traffic, thereby slowing down repair progress and downgrading service quality. We present ChameleonEC, a general mechanism that can assist a variety of erasure codes in realizing low-interference repair. ChameleonEC comprises the following design techniques: (i) repair task assignment, which decomposes a repair plan into multiple repair tasks and makes them coexist harmoniously with the foreground traffic, so as to saturate unoccupied bandwidth and avoid bandwidth contentions; (ii) repair path establishment, which orchestrates elastic transmission routings over the dispatched repair tasks to instruct the repair; and (iii) straggler-aware re-scheduling, which timely re-tunes task transmissions and repair plans to bypass unexpected stragglers emerging in repair. We conduct extensive experiments on Amazon EC2, showing that ChameleonEC can accelerate the repair by 4.9–498.2% for various erasure codes under different real-world traces. ChameleonEC can also speed up the repair process by 25.4–73.5% under the storage-bottlenecked scenarios.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00013"}, {"primary_key": "165325", "vector": [], "sparse_vector": [], "title": "SoMa: Identifying, Exploring, and Understanding the DRAM Communication Scheduling Space for DNN Accelerators.", "authors": ["Jingwei Cai", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Kaisheng Ma"], "summary": "Modern Deep Neural Network (DNN) accelerators are equipped with increasingly larger on-chip buffers to provide more opportunities to alleviate the increasingly severe DRAM bandwidth pressure. However, most existing research on buffer utilization still primarily focuses on single-layer dataflow scheduling optimization. As buffers grow large enough to accommodate most single-layer weights in most networks, the impact of single-layer dataflow optimization on DRAM communication diminishes significantly. Therefore, developing new paradigms that fuse multiple layers to fully leverage the increasingly abundant onchip buffer resources to reduce DRAM accesses has become particularly important, yet remains an open challenge.To address this challenge, we first identify the optimization opportunities in DRAM communication scheduling by analyzing the drawbacks of existing works on the layer fusion paradigm and recognizing the vast optimization potential in scheduling the timing of data prefetching from and storing to DRAM. To fully exploit these optimization opportunities, we develop a Tensor-centric Notation and its corresponding parsing method to represent different DRAM communication scheduling schemes and depict the overall space of DRAM communication scheduling. Then, to thoroughly and efficiently explore the space of DRAM communication scheduling for diverse accelerators and workloads, we develop an end-to-end scheduling framework, SoMa, which has already been developed into a compiler for our commercial accelerator product. Compared with the state-of-the-art (SOTA) Cocco framework, SoMa achieves, on average, a 2.11× performance improvement and a 37.3% reduction in energy cost simultaneously. Then, we leverage SoMa to study optimizations for LLM, perform design space exploration (DSE), and analyze the DRAM communication scheduling space through a practical example, yielding some interesting insights. Moreover, SoMa has been open-sourced at https://github.com/SET-Scheduling-Project/SoMa-HPCA2025.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00048"}, {"primary_key": "165326", "vector": [], "sparse_vector": [], "title": "Chronus: Understanding and Securing the Cutting-Edge Industry Solutions to DRAM Read Disturbance.", "authors": ["Oguzhan Canpolat", "<PERSON><PERSON>", "Geraldo F. Oliveira", "Ataberk Olgun", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Read disturbance in modern DRAM is an important robustness (security, safety, and reliability) problem, where repeatedly accessing (hammering) a row of DRAM cells (DRAM row) induces bitflips in other physically nearby DRAM rows. Shrinking technology node size exacerbates DRAM read disturbance over generations. To help mitigate read disturbance, the latest DDR5 specifications (as of April 2024) introduced a new RowHammer mitigation framework, called Per Row Activation Counting (PRAC). PRAC 1) enables the DRAM chip to accurately track row activations by allocating an activation counter per row and 2) provides the DRAM chip with the necessary time window to perform RowHammer-preventive refreshes by introducing a new back-off signal. Unfortunately, no prior work rigorously studies PRAC’s security guarantees and overheads. In this paper, we 1) present the first rigorous security, performance, energy, and cost analyses of PRAC and 2) propose Chronus, a new mechanism that addresses PRAC’s two major weaknesses. Our analysis shows that PRAC’s system performance overhead on benign applications is non-negligible for modern DRAM chips and prohibitively large for future DRAM chips that are more vulnerable to read disturbance. We identify two weaknesses of PRAC that cause these overheads. First, PRAC increases critical DRAM access latency parameters due to the additional time required to increment activation counters. Second, PRAC performs a constant number of preventive refreshes at a time, making it vulnerable to an adversarial access pattern, known as the wave attack, and consequently requiring it to be configured for significantly smaller activation thresholds. To address PRAC’s two weaknesses, we propose a new on-DRAM-die RowHammer mitigation mechanism, Chronus. Chronus 1) updates row activation counters concurrently while serving accesses by separating counters from the data and 2) prevents the wave attack by dynamically controlling the number of preventive refreshes performed. Our performance analysis shows that Chronus’s system performance overhead is near-zero for modern DRAM chips and very low for future DRAM chips. Chronus outperforms three variants of PRAC and three other state-of-the-art read disturbance solutions. We discuss Chronus’s and PRAC’s implications for future systems and foreshadow future research directions. To aid future research, we open-source our Chronus implementation at https://github.com/CMU-SAFARI/Chronus.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00071"}, {"primary_key": "165327", "vector": [], "sparse_vector": [], "title": "Veritas - Demystifying Silent Data Corruptions: μArch-Level Modeling and Fleet Data of Modern x86 CPUs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Dixit", "<PERSON><PERSON>"], "summary": "Hyperscalers have reported unexpectedly high numbers of defective CPU chips, with a defect rate of 1 in a 1000, leading to Silent Data Corruptions (SDCs) in their computing fleets. However, there is no public data on the rate of SDC incidents (corrupted program executions) in large fleets, nor nor any detailed information on which CPU units, microarchitectures, or workloads are more likely to generate SDCs due to silicon defects. While CPU array structures have been studied for fault effects, arithmetic units like integer and floating-point units have not been thoroughly analyzed as potential root causes of SDCs. This paper addresses this critical gap by accurately modeling hardware faults in the arithmetic units of modern x86 CPUs and measuring the probability and rates of SDCs. Using a full-system gem5-based fault injector, the paper examines SDC trends across five recent $x 86$ microarchitectures, various arithmetic units, and instruction classes. By integrating real-world defect rates from large-scale datacenter experiments with early-stage modeling and simulation, the paper provides critical insights into SDC incident rates across different systems. This information is essential for guiding hardware-based or software-based fault protection methods and is the paper’s primary contribution to minimizing the impact of silent data corruptions in computing.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00012"}, {"primary_key": "165328", "vector": [], "sparse_vector": [], "title": "Mithril: A Scalable System for Deep GNN Training.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Communication is a key bottleneck for distributed graph neural network (GNN) training. Existing GNN training systems fail to scale to deep GNNs because of the tremendous amount of inter-GPU communication. This paper proposes Mithril, a new approach that significantly scales the distributed full-graph deep GNN training. Being the first to use layer-level model parallelism for GNN training, <PERSON><PERSON><PERSON> partitions GNN layers among GPUs, each device performs the computation for a disjoint subset of consecutive GNN layers on the whole graph. Compared to graph parallelism with each GPU handling a graph partition, <PERSON><PERSON><PERSON> reduces the communication volume by a factor of the number of GNN layers to scale to deep models. <PERSON><PERSON><PERSON> overcomes the unique challenges for pipelined layer-level model parallelism on the whole graph by partitioning it into dependent chunks, breaking the dependencies with embedding speculation, and applying specific training techniques to ensure convergence. We also propose a hybrid approach by combining <PERSON><PERSON><PERSON> with graph parallelism to handle large graphs, achieve better computer resource utilization and ensure model convergence. We build a general GNN training system supporting all three parallelism settings. Extensive experiments show that <PERSON><PERSON><PERSON> reduces the perepoch communication volume by up to $22.89 \\times$ (on average $6.78 \\times$). It achieves a maximum training time speedup of $2.34 \\times$ (on average $1.49 \\times$) on a GPU cluster with a high-performance InfiniBand network. On another cluster with a commodity Ethernet, <PERSON><PERSON><PERSON> outperforms the baseline by up to $10.21 \\times$ (on average $7.16 \\times$). Mithril also achieves a comparable level of model accuracy and convergence speed compared to graph parallelism.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00082"}, {"primary_key": "165329", "vector": [], "sparse_vector": [], "title": "NVMePass: A Lightweight, High-performance and Scalable NVMe Virtualization Architecture with I/O Queues Passthrough.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Kanghua Fang", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Most data-intensive applications currently run on NVMe storage, and virtualization is essential in cloud computing. Existing NVMe virtualization technologies include software-based and hardware-assisted. Virtio suffers from severe performance degradation, and polling-based solutions consume too many valuable CPU resources. Hardware-assisted solutions provide high performance and no CPU usage but have the challenges of developing dedicated hardware.In this paper, we propose NVMePass, a novel software-hardware co-design NVMe passthrough virtualization architecture designed to achieve high performance and no CPU overhead while maintaining high scalability. The key ideas of NVMePass are NVMe I/O queues passthrough for VMs and a mechanism to ensure security. The NVMePass supports DMA and interrupts remapping for VMs without hypervisor involvement, eliminating virtualization overhead and providing near-native performance. Isolation is achieved by I/O queues and logical block address resources exclusively allocated to VMs. We propose NVMe Resource Domain (NRD) and implement it in the NVMe controller to intercept illegal I/O requests. Thus, isolation and security are fully achieved. Results from our experiments show that NVMePass can provide comparable performance to VFIO, with an IOPS of $\\mathbf{1 0 0. 1 \\% - 1 0 0. 5 \\%}$ of VFIO. Furthermore, compared to SPDK-Vhost, NVMePass achieves $\\mathbf{4 0. 0 \\%}$ lower latency when running 150 VMs, and NVMePass has an improvement of $\\mathbf{6 8. 0 \\%}$ OPS performance in a real-world application when running 100 VMs.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00105"}, {"primary_key": "165330", "vector": [], "sparse_vector": [], "title": "AsyncDIMM: Achieving Asynchronous Execution in DIMM-Based Near-Memory Processing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Naifeng Jing"], "summary": "DIMM-based near-memory processing (NMP) architectures address the “memory wall” problem by incorporating near-memory accelerators (NMAs) into main memory devices for high memory bandwidth and low energy consumption. However, critical challenges prevent efficient asynchronous execution between host and NMAs in DIMM-NMP architectures. Memory controllers (MCs) distributed at the host side and the NMA side issue memory accesses independently without synchronization on memory states, which may lead to memory bus contention and DRAM errors. Therefore, most existing DIMM-NMP designs adopt synchronous execution to prevent concurrent memory accesses. However, this intervention wastes either the host or the NMA computation capability. In this work, we propose AsyncDIMM, a novel DIMM-NMP design with efficient asynchronous execution based on existing memory buses. It enables single access mode (host or NMA), concurrent access mode, and a seamless switch between them. First, we propose the offload-schedule-return mechanism with explicit and implicit synchronization to ensure memory access correctness for all memory modes. Second, to further improve bandwidth utilization and decrease access latency, we introduce optimized timing constraints for offloading, a locality-aware switch-recovery method for scheduling, and adaptive batch with timing-division multiplexing notification for returning. Finally, we present a detailed design with limited hardware modifications to conventional host and NMA MCs, which is extensively validated on the FPGA. Comprehensive experiments demonstrate that AsyncDIMM outperforms four NMP baselines by $1.19 \\times-1.92 \\times$, enabling efficient asynchronous execution with up to $2.25 \\times$ bandwidth utilization uplift and 47% access latency reduction.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00047"}, {"primary_key": "165331", "vector": [], "sparse_vector": [], "title": "Grad: Intelligent Microservice Scaling by Harnessing Resource Fungibility.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Microservice applications are commonly deployed alongside other services to enhance resource utilization. However, this practice also leads to notable resource contention. While existing studies primarily focus on scaling critical microservices responsible for performance degradation to mitigate violations of SLAs regarding end-to-end latency in highly interfered environments, they often overlook the potential advantages of scaling non-critical microservices for optimized resource efficiency. In this paper, we introduce Grad, an intelligent microservice scaling framework by harnessing resource fungibility between critical and non-critical microservices. Addressing the challenges posed by the dynamic nature of resource fungibility during scaling, Grad incorporates three key components. First, Grad employs a modular learning approach to profile individual microservice latency in relation to environmental conditions. Utilizing gradient extracts from this profile, Grad designs a scalable optimization module to dynamically select the optimal set of microservices for scaling. To rapidly mitigate SLA violations, Grad also deploys an accurate end-to-end latency predictor, serving as an simulator to obtain real-time feedback. We evaluate Grad in our cluster using real microservice benchmarks and production traces, demonstrating its ability to reduce resource usage by $\\mathbf{4 9. 1 \\%}$ and lower the probability of SLA violations by $3.7 \\times$ when compared to state-of-the-art solutions.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00044"}, {"primary_key": "165332", "vector": [], "sparse_vector": [], "title": "Gaze into the Pattern: Characterizing Spatial Patterns with Internal Temporal Correlations for Hardware Prefetching.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Hardware prefetching is one of the most widely-used techniques for hiding long data access latency. To address the challenges faced by hardware prefetching, architects have proposed to detect and exploit the spatial locality at the granularity of spatial region. When a new region is activated, they try to find similar previously accessed regions for footprint prediction based on system-level environmental features such as the trigger instruction or data address. However, we find that such context-based prediction cannot capture the essential characteristics of access patterns, leading to limited flexibility, practicality and suboptimal prefetching performance. In this paper, inspired by the temporal property of memory accessing, we note that the temporal correlation exhibited within the spatial footprint is a key feature of spatial patterns. To this end, we propose <PERSON><PERSON><PERSON>, a simple and efficient hardware spatial prefetcher that skillfully utilizes footprint-internal temporal correlations to efficiently characterize spatial patterns. Meanwhile, we observe a unique unresolved challenge in utilizing spatial footprints generated by spatial streaming, which exhibit extremely high access density. Therefore, we further enhance Gaze with a dedicated two-stage approach that mitigates the over-prefetching problem commonly encountered in conventional schemes. Our comprehensive and diverse set of experiments show that Gaze can effectively enhance the performance across a wider range of scenarios. Specifically, Gaze improves performance by $\\mathbf{5. 7 \\%}$ and 5.4% at single-core, 11.4% and $\\mathbf{8. 8 \\%}$ at eight-core, compared to most recent low-cost solutions PMP and vBerti.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00024"}, {"primary_key": "165333", "vector": [], "sparse_vector": [], "title": "eDKM: An Efficient and Accurate Train-Time Weight Clustering for Large Language Models.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Carlo C. del Mundo", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Since Large Language Models or LLMs have demonstrated high-quality performance on many complex language tasks, there is a great interest in bringing these LLMs to mobile devices for faster responses and better privacy protection. However, the size of LLMs (i.e., billions of parameters) requires highly effective compression to fit into storage-limited devices. Among many compression techniques, weight-clustering, a form of non-linear quantization, is one of the leading candidates for LLM compression, and supported by modern smartphones. Yet, its training overhead is prohibitively significant for LLM fine-tuning. Especially, Differentiable KMeans Clustering, or DKM, has shown the state-of-the-art trade-off between compression ratio and accuracy regression, but its large memory complexity makes it nearly impossible to apply to train-time LLM compression. In this letter, we propose a memory-efficient DKM implementation, eDKM powered by novel techniques to reduce the memory footprint of DKM by orders of magnitudes. For a given tensor to be saved on CPU for the backward pass of DKM, we compressed the tensor by applying uniquification and sharding after checking if there is no duplicated tensor previously copied to CPU. Our experimental results demonstrate that eDKM can fine-tune and compress a pretrained LLaMA 7B model from 12.6 GB to $2.5 \\mathrm{~GB}(3 \\mathrm{~b} /$ weight) with the Alpaca dataset by reducing the train-time memory footprint of a decoder layer by $130 \\times$, while delivering good accuracy on broader LLM benchmarks (i.e., $77.7 \\%$ for PIQA, $66.1 \\%$ for Winograde, and so on).", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00133"}, {"primary_key": "165334", "vector": [], "sparse_vector": [], "title": "Bit-slice Architecture for DNN Acceleration with Slice-level Sparsity Enhancement and Exploitation.", "authors": ["<PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Deep Neural Networks (DNNs) demand significant computational resources, prompting the emergence of bit-slice architectures designed to efficiently accelerate DNNs by leveraging high bit-precision reconfigurability and fine-grained sparsity through slice-level computation. However, fully utilizing slice-level sparsity remains challenging, leading conventional bit-slice architectures to exploit either input or weight sparsity at a coarse-grained level. In this paper, we introduce a Bit-slice Architecture for DNN Acceleration (BADA) that simultaneously leverages both input and weight sparsity at coarse- and fine-grained levels. BADA features a novel architecture that skips computations for bit-slice chunks containing zero values and also skips any set of operands where either the input or weight bit-slice is zero. The design comprises a front-end unit responsible for generating bit-slices and selectively gathering only the non-zero slices, and a back-end unit equipped with a signed multiply-and-accumulate (MAC) unit to process these collected non-zero bit-slices. Additionally, we present two algorithmic optimizations to further enhance the efficiency and performance of BADA. First, we propose a novel bit-slice representation that supports 8-bit data without incurring additional hardware overhead, whereas conventional bit-slice representations are limited to 6-bit or 7-bit data under similar constraints. Second, we introduce a method to narrow the weight distribution during the training process, thereby increasing the proportion of zero-valued higher-order bit-slices and further enhancing slice-level weight sparsity. Experimental results demonstrate that BADA achieves a 2.67× increase in throughput, a 1.52× improvement in area efficiency, and a 2.15× enhancement in energy efficiency compared to LUTein, the state-of-the-art bit-slice architecture.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00067"}, {"primary_key": "165335", "vector": [], "sparse_vector": [], "title": "Enhancing Large-Scale AI Training Efficiency: The C4 Solution for Real-Time Anomaly Detection and Communication Optimization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Peng<PERSON> Zhang", "<PERSON><PERSON>", "<PERSON><PERSON> Zhu", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Gang Lu", "Yu <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>yu <PERSON>", "Man <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Binzhang Fu"], "summary": "The emergence of Large Language Models (LLMs) has necessitated the adoption of distributed training techniques, involving the deployment of thousands of GPUs to train a single model. Unfortunately, the efficiency of large-scale distributed training systems is often suboptimal due to the increased likelihood of hardware errors in high-end GPU products and the heightened risk of network traffic collisions. Specifically, GPUs involved in the same job require periodic synchronization to exchange necessary data, such as gradients, parameters, or activations. As a result, any local hardware failure can disrupt training tasks, and the inability to swiftly identify faulty components leads to a significant waste of GPU resources. Moreover, prolonged communication due to traffic collisions can substantially increase GPU waiting times. To address these challenges, we propose a communication-driven solution, namely the C 4. The key insights of C 4 are twofold. First, the load in distributed training exhibits homogeneous characteristics and is divided into iterations through periodic synchronization, therefore hardware anomalies would incur certain syndrome in collective communication. By leveraging this feature, $\\mathbf{C} 4$ can rapidly identify the faulty components, swiftly isolate the anomaly, and restart the task, thereby avoiding resource wastage caused by delays in anomaly detection. Second, the predictable communication model of collective communication, involving a limited number of long-lived flows, allows C 4 to efficiently execute traffic planning, substantially reducing bandwidth competition among these flows. The $\\mathbf{C 4}$ has been extensively deployed across real-world production systems in a hyperscale cloud provider, yielding a significant improvement in system efficiency, from 30% to $\\mathbf{4 5 \\%}$. This enhancement is attributed to a $\\mathbf{3 0 \\%}$ reduction in error-induced overhead and a 15% reduction in communication costs.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00095"}, {"primary_key": "165336", "vector": [], "sparse_vector": [], "title": "R.I.P. Geomean Speedup Use Equal-Work (Or Equal-Time) Harmonic Mean Speedup Instead.", "authors": ["<PERSON><PERSON>"], "summary": "How to accurately summarize average performance is challenging. While geometric mean speedup is prevalently used, it is meaningless. Instead, this paper argues for harmonic mean speedup which accurately summarizes how much faster a workload executes on a target system relative to a baseline. We propose the equal-work and equal-time harmonic mean speedup metrics to explicitly expose the different assumptions they make, and we further suggest that equal-work speedup is most relevant to computer architecture research. The paper demonstrates that which average speedup is used matters in practice as inappropriate averages may lead to incorrect conclusions.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00132"}, {"primary_key": "165337", "vector": [], "sparse_vector": [], "title": "CORDOBA: Carbon-Efficient Optimization Framework for Computing Systems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Okay Zed", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Gage Hills", "<PERSON><PERSON><PERSON>"], "summary": "The world’s push toward an environmentally sustainable society is highly dependent on the semiconductor industry. Despite existing carbon modeling efforts to quantify carbon footprint of computing systems, optimizing carbon footprint in large design spaces-while also considering trade-offs in power, performance, and area-is especially challenging. To address this need, we present CORDOBA, a carbon-aware optimization framework that optimizes carbon efficiency. We quantify carbon efficiency using the total Carbon Delay Product metric (tCDP): the product of total carbon and application execution time. We justify why tCDP is an effective metric for quantifying carbon efficiency. We use CORDOBA to explore the large design space for carbonefficient specialized hardware, and identify distinct carbonefficient optimal designs across operational use (eliminating up to $\\mathbf{9 8 \\%}$ of the design space) despite uncertainty in carbon footprint parameters. We quantify opportunities to improve tCDP for real system case studies: (a) optimizing hardware provisioning from 8 to 4 cores in real system CPUs improves tCDP by $1.25 \\times$; and (b) leveraging advanced three-dimensional (3D) integration techniques (3D stacking of separately-fabricated logic and memory chips) improves tCDP by $6.9 \\times$ versus conventional systems.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00098"}, {"primary_key": "165338", "vector": [], "sparse_vector": [], "title": "WarpDrive: GPU-Based Fully Homomorphic Encryption Acceleration Leveraging Tensor and CUDA Cores.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Shengy<PERSON> Fan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Liang Kong", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The application of Fully Homomorphic Encryption (FHE) is rapidly gaining traction as a means to maintain data confidentiality while performing computations on encrypted data. Given the accessibility and computational power, GPUs hold promise for significantly accelerating FHE operations. However, existing GPU-based acceleration solutions face several formidable challenges, notably the extensive occurrence of pipeline stalls induced by memory access and suboptimal harnessing of GPU hardware. This paper presents WarpDrive, a comprehensive framework for GPU-based FHE acceleration. Through sophisticated computation decomposition and fine-grained memory access design, WarpDrive significantly reduces the number of instructions by $\\mathbf{7 3 \\%}$ and pipeline stalls by $\\mathbf{8 6 \\%}$ compared to the state-of-the-art solution. Additionally, WarpDrive features a framework that supports the concurrent utilization of CUDA Cores and Tensor Cores within the NTT operation, for the first time, achieving performance that surpasses that of any single type of processing unit. Furthermore, we fully exploit the intra-ciphertext parallelism to elevate both computation and memory utilization, achieving up to $2.12 \\times$ improvements without the need for ciphertext batching. Experimental results demonstrate that our optimizations highly enhance the performance of homomorphic operations. On an NVIDIA A100 GPU, WarpDrive achieves a throughput of 1218 KOPS for NTT and 305 KOPS for homomorphic multiplication, outperforming the state-of-the-art GPU solution (TensorFHE) by factors of $13.4 \\times$ and $3.5 \\times$, respectively. For the specific FHE workload, even under a much smaller batch size, our approach achieves $2.8 \\times$ the performance of TensorFHE.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00091"}, {"primary_key": "165339", "vector": [], "sparse_vector": [], "title": "Rethinking Dead Block Prediction for Intermittent Computing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Existing dead block predictors have proven to be effective in reducing cache leakage power of conventional systems. However, prior work is significantly less effective in energy harvesting systems in that it does not take into account their unique characteristic, i.e., frequent power failure during program execution. Even if some cache blocks are predicted to be live, they may not be used due to their loss upon power failure. In response, this paper introduces EDBP, an extension to existing dead block predictors, to enhance their performance in various energy harvesting environments. EDBP can identify and deactivate those cache blocks that are not reused before upcoming power failure-though they are considered live by the existing predictor-thereby lowering cache leakage and preserving more energy for forward execution progress. Experimental results show that for 20 applications from Mediabench and MiBench, EDBP alone reduces total energy consumption by 6.5% and improves performance by 6.9% compared to the baseline with no dead block predictor. When combined with a conventional dead block predictor (Cache Decay), EDBP achieves 9.8% reduction in total energy consumption—approaching the theoretical minimum—and 11.9% performance improvement.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00061"}, {"primary_key": "165340", "vector": [], "sparse_vector": [], "title": "Anda: Unlocking Efficient LLM Inference with a Variable-Length Grouped Activation Data Format.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The widely-used, weight-only quantized large language models (LLMs), which leverage low-bit integer (INT) weights and retain floating-point (FP) activations, reduce storage requirements while maintaining accuracy. However, this shifts the energy and latency bottlenecks towards the FP activations that are associated with costly memory accesses and computations. Existing LLM accelerators focus primarily on computation optimizations, overlooking the potential of jointly optimizing FP computations and data movement, particularly for the dominant FP-INT GeMM operations in LLM inference. To address these challenges, we investigate the sensitivity of activation precision across various LLM modules and its impact on overall model accuracy. Based on our findings, we first propose the Anda data type: an adaptive data format with group-shared exponent bits and dynamic mantissa bit allocation. Secondly, we develop an iterative post-training adaptive precision search algorithm that optimizes the bit-width for different LLM modules to balance model accuracy, energy efficiency, and inference speed. Lastly, a suite of hardware optimization techniques is proposed to maximally exploit the benefits of the Anda format. These include a bit-plane-based data organization scheme, Anda-enhanced processing units with bit-serial computation, and a runtime bit-plane Anda compressor to simultaneously optimize storage, computation, and memory footprints. Our evaluations on FP-INT GeMM operations show that Anda achieves a $2.4 \\times$ speedup, $4.0 \\times$ area efficiency, and $3.1 \\times$ energy efficiency improvement on average for popular LLMs including OPT, LLaMA, and LLaMA-2 series over the GPU-like FP-FP baseline. Anda demonstrates strong adaptability across various application scenarios, accuracy requirements, and system performance, enabling efficient LLM inference across a wide range of deployment scenarios.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00110"}, {"primary_key": "165341", "vector": [], "sparse_vector": [], "title": "NOVA: A Novel Vertex Management Architecture for Scalable Graph Processing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Austin York", "S. <PERSON><PERSON>", "<PERSON>-Power", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose a scalable graph processing hardware accelerator called NOVA that is based on a novel vertex management architecture that decouples the execution of reduction and propagation operations in the popular vertex-centric graph processing paradigm. This allows us to store the working set in off-chip memory and utilize the available on-chip memory as a buffer to hide the latency of DRAM accesses instead of a traditional cache. This overcomes one of the key drawbacks of almost all the prior works which require temporal partitioning of graphs to scale to large graphs. We develop a cycle-accurate model of the architecture in gem 5 and demonstrate that NOVA exhibits near-perfect weak and strong scaling while scaling to large graphs by spatially tiling multiple nodes. In addition, our simulations show that NOVA is $2.35 \\times$ better than a state-of-the-art graph accelerator (PolyGraph) while using a fraction of the on-chip memory on a synthetic graph with 134M vertices and over 2.14B edges.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00072"}, {"primary_key": "165342", "vector": [], "sparse_vector": [], "title": "PROCA: Programmable Probabilistic Processing Unit Architecture with Accept/Reject Prediction &amp; Multicore Pipelining for Causal Inference.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Daijing Shi", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Causal inference is an important field in data science and cognitive artificial intelligence. It requires the construction of complex probabilistic models to describe the causal relationships between random variables. Probabilistic models rely on probabilistic programming as a flexible framework. However, the computing speed of probabilistic programming is often hindered by the extensive use of Markov chain Monte Carlo (MCMC) algorithms, even though they are powerful in Bayesian inference. To accelerate MCMC, this work presents PROCA, a programmable MCMC-based probabilistic processing unit architecture. PROCA exploits processing-in-memory function units to generate new samples of Markov chains. PROCA is programmable to execute the computation for arbitrary forms of posterior distribution formulas that software probabilistic programming frameworks support. We develop a novel accept/reject prediction methodology to accelerate the sequential MCMC computation, thereby introducing efficient multi-core pipelining methods. We implement and validate the PROCA architecture with commercial process development kits. The implementation is evaluated based on 9 representative benchmarks, covering PyMC official tutorial probabilistic problems, single-variable probabilistic problems, and real-world causal inference problems. Our comprehensive experiments demonstrate that PROCA achieves a speedup of 172~4871 $\\times$ compared to Intel Xeon Gold CPU, $42 \\sim 1058 \\times$ compared to NVIDIA A100 GPU, and $1.765 \\times$ over state-of-the-art MCMC accelerators, respectively. PROCA achieves comparable statistical robustness to the software probabilistic programming frameworks. Compared with state-of-the-art MCMC domain-specific accelerators, our design boosts the energy efficiency by $9.47 \\times$.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00063"}, {"primary_key": "165343", "vector": [], "sparse_vector": [], "title": "The Importance of Generalizability in Machine Learning for Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Using machine learning (ML) to tackle computer systems tasks is gaining popularity. One of the shortcomings of such ML-based approaches is the inability of models to generalize to out-ofdistribution data i.e., data whose distribution is different than the training dataset. We showcase that this issue exists in cloud environments by analyzing various ML models used to improve resource balance in Google’s fleet. We discuss the trade-offs associated with different techniques used to detect out-of-distribution data. Finally, we propose and demonstrate the efficacy of using Bayesian models to detect the model’s confidence in its output when used to improve cloud server resource balance.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00131"}, {"primary_key": "165344", "vector": [], "sparse_vector": [], "title": "ER-DCIM: Error-Resilient Digital CIM Architecture with Run-Time MAC-Cell Error Correction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Digital computing-in-memory (CIM) is an emerging solution to break through the limitations of memory wall by integrating digital logic into SRAM, which is able to achieve high area and energy efficiency with no accuracy loss. Digital CIM’s SRAM cells are still prone to errors like conventional SRAM due to noise and variation, especially under low-voltage operation for high energy efficiency. The SRAM cell errors cause multiplyaccumulation (MAC) result errors, which may seriously damage the neural network inference accuracy. However, traditional SRAM’s error correcting code (ECC) that corrects errors in one read-out row is incompatible with digital CIM, which reads out multiple rows simultaneously for computation. Detecting and correcting computational MAC errors and SRAM cell errors (i.e., MAC-cell errors) in digital CIM remain largely unexplored.To address digital CIM’s unique MAC-cell error resilience needs, we propose ER-DCIM, an error-resilient digital CIM with run-time MAC-cell error correction to guarantee computation correctness. The proposed residue code-based MAC error correction mechanism is the first to correct additive errors in the MAC result in real time during DCIM computation. Then, we propose a progressive cell error correction mechanism to correct underlying cell error in a timely manner, avoiding performance loss due to stalling computation. Further, we design a mode switcher to repurpose redundant error-resilient logic reserved for low-voltage mode to improve performance in high-voltage mode. Experimental results show that the proposed techniques enable digital CIM to maintain high throughput and energy efficiency without accuracy loss in both low-voltage and high-voltage modes.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00046"}, {"primary_key": "165345", "vector": [], "sparse_vector": [], "title": "GSArch: Breaking Memory Barriers in 3D Gaussian Splatting Training via Architectural Support.", "authors": ["<PERSON><PERSON><PERSON>", "Gang Li", "<PERSON><PERSON>", "Li <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "3D Gaussian Splatting (3DGS) introduces a novel methodology for representing scenes with anisotropic 3D Gaussian primitives, achieving exceptional quality and rendering speed in neural scene representation (NSR). However, the insufficient training speed of 3DGS limits its applicability in tasks that require online learning to perceive dynamic environments, such as autonomous driving and embodied intelligence. Although recent work, GSCore, has introduced a specialized accelerator for the rendering process of 3DGS, it overlooks the time-consuming backward propagation during 3DGS training.In this paper, we propose GSArch, a hardware architecture designed to overcome memory barriers and boost the efficiency of 3DGS training. Through a thorough characterization of 3DGS training, we identify three root causes of inefficiency: redundant data loading from off-chip memory, time-consuming atomic write operations, and severe bank conflicts during on-chip buffer reading. To address these challenges, GSArch introduces three architectural innovations. First, acknowledging that Gaussians vary in shape and often span multiple pixels, with larger Gaussians causing more repetitive data loading, GSArch employs hybrid memory management. This approach categorizes Gaussians into ‘hot’ and ‘cold’ ones, storing hot Gaussians in a fast but small on-chip buffer to reduce redundant loading while minimizing hardware costs. Second, GSArch leverages the informativeness variability of Gaussians’ gradients to filter out low-contribution gradients, significantly reducing atomic operations. Lastly, a rearrangement unit is designed to pack conflicting memory read requests into non-conflicting bundles. Our evaluation results demonstrate that GSArch achieves up to $6.49 \\times$ and $15.42 \\times$ speedups compared to Nvidia A100 and Jetson AGX Xavier, respectively, with substantially lower energy consumption and negligible image quality loss.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00037"}, {"primary_key": "165346", "vector": [], "sparse_vector": [], "title": "EXION: Exploiting Inter-and Intra-Iteration Output Sparsity for Diffusion Models.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Yune", "Hangyeol Lee", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Over the past few years, diffusion models have emerged as novel solutions in AI industries, offering the capability to generate diverse, multi-modal outputs such as images, videos, and motions from input prompts (i.e., text). Despite their impressive capabilities, diffusion models face significant challenges in computing, including excessive latency and energy consumption due to the numerous iterations in model architecture. Although prior works specialized in transformer acceleration can be applied to diffusion models, given that transformers are key components, the problem of the iterative nature of diffusion models still needs to be addressed.In this paper, we present EXION, the first software-hardware co-designed diffusion accelerator that solves the computation challenges of excessive iterations by exploiting the unique inter-and intra-iteration output sparsity in diffusion models. To this end, we propose two software-level optimizations in EXION. First, we introduce the FFN-Reuse algorithm that identifies and skips redundant computations in FFN layers across different iterations (i.e., inter-iteration sparsity). Second, we use a modified eager prediction method that employs two-step leading-one detection to accurately predict the attention score in diffusion models, skipping unnecessary computations within an iteration (i.e., intra-iteration sparsity). We also introduce a novel data compaction mechanism named ConMerge, which can enhance hardware utilization by condensing and merging large and sparse matrices into small and compact forms. Finally, EXION has a dedicated hardware architecture that supports the above sparsity-inducing algorithms, translating high output sparsity into improved energy efficiency and performance. To verify the feasibility of the EXION accelerator, we first demonstrate that it has no impact on accuracy in various types of multi-modal diffusion models, including text-to-motion, -audio, -image, and -video. We then instantiate EXION in both server-and edge-level settings and compare its performance against GPUs with similar specifications. Our evaluation shows that EXION achieves dramatic improvements in performance and energy efficiency by 3.2-379.3 × and 45.1-3067.6 × compared to a server GPU and by 42.6-1090.9 × and 196.9-4668.2 × compared to an edge GPU.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00034"}, {"primary_key": "165347", "vector": [], "sparse_vector": [], "title": "M-ANT: Efficient Low-bit Group Quantization for LLMs via Mathematically Adaptive Numerical Type.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Renyang Guan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Large language models (LLMs) are one of the most important killer computer applications. The recent algorithmic advancement proposes a fine-grained group-wise quantization for LLMs, which treats a small set (e.g., 64) of values in a tensor as a compression unit. It effectively preserves the model accuracy without retraining, and has become the standard approach to efficiently deploy LLMs. On the other hand, there are works that propose various adaptive data types to better adapt to different distributions and further reduce the required bit length for LLMs. In this work, our detailed analysis unveils a key finding that while different tensors exhibit similar distributions, small groups can have markedly different distributions. As such, the group-level diversity requires a new level of adaptivity for which existing adaptive data types fail to provide.In this paper, we propose MANT, a mathematically adaptive numeric type, featuring a more flexible encoding paradigm with a wider range of data distribution and more efficient decoding-computation fusion mechanism to address these challenges. Based on MANT, we develop a supporting framework to assign the appropriate data type for each group adaptively. Meanwhile, the dynamically generated Key-Value (KV) caches in LLMs introduce further complexity for real-time quantization. To tackle this, we propose an efficient real-time quantization mechanism. Besides, we implement a specific processing element (PE) to efficiently support MANT and incorporate a real-time quantization unit. By integrating these components into a systolic array, MANT unifies the group-wise weight and KV cache quantization and addresses the associated challenges. Our evaluation shows achieving, on average, 2.99 × (up to 4.46 ×) speedup and 2.81 × (up to 4.10 ×) energy reduction to the state-of-the-art LLM accelerator.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00086"}, {"primary_key": "165348", "vector": [], "sparse_vector": [], "title": "EFFACT: A Highly Efficient Full-Stack FHE Acceleration Platform.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Xiangyu Kong", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Fully Homomorphic Encryption (FHE) is a set of powerful cryptographic schemes that allows computation to be performed directly on encrypted data with an unlimited depth. Despite FHE’s promising in privacy-preserving computing, yet in most FHE schemes, ciphertext generally blows up thousands of times compared to the original message, and the massive amount of data load from off-chip memory for bootstrapping and privacy-preserving machine learning applications (such as HELR, ResNet-20), both degrade the performance of FHE-based computation. Several hardware designs have been proposed to address this issue, however, most of them require enormous resources and power. An acceleration platform with easy programmability, high efficiency, and low overhead is a prerequisite for practical application. This paper proposes EFFACT, a highly efficient full-stack FHE acceleration platform with a compiler that provides comprehensive optimizations and vector-friendly hardware. We start by examining the computational overhead across different real-world benchmarks to highlight the potential benefits of reallocating computing resources for efficiency enhancement. Then we make a design space exploration to find an optimal SRAM size with high utilization and low cost. On the other hand, EFFACT features a novel optimization named streaming memory access which is proposed to enable high throughput with limited SRAMs. Regarding the software-side optimization, we also propose a circuit-level function unit reuse scheme, to substantially reduce the computing resources without performance degradation. Moreover, we design novel NTT and automorphism units that are suitable for a cost-sensitive and highly efficient architecture, leading to low area. For generality, EFFACT is also equipped with an ISA and a compiler backend that can support several FHE schemes like CKKS, BGV, and BFV. We provide both FPGA and ASIC versions of EFFACT. On account of our full stack design, FPGA-EFFACT outperforms the SOTA FPGA accelerators in gmean by $1.22 \\times$. Meanwhile, ASIC-EFFACT shows increased improvements in terms of the performance per chip area and the performance per Watt compared with the SOTA ASIC works.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00088"}, {"primary_key": "165349", "vector": [], "sparse_vector": [], "title": "Efficient Optimization with Encoded Ising Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Many promising computing substrates, including quantum computers, oscillator-based computers, and p computers solve constrained combinatorial optimization problems by minimizing energy functions called Ising models. Because Ising solvers explore an unconstrained search space, Ising models for many popular optimization problems must include penalty terms to raise the energy of infeasible solutions that would appear optimal otherwise. We observe that for some problems, Ising solvers spend the majority of computation time exploring this invalid state and often never find a feasible solution. We introduce the encoded Ising model (E-I model), an extension to the Ising model that uses a digital encoding circuit to vastly reduce the proportion of time a solver spends exploring invalid states. We present Fuse, a software framework that enables the description of such functions and automatically lowers them to a p-computer. Our formulation reduces the number of iterations to a solution by a factor of $7.2-52000 \\mathrm{x}$ and achieves up to $\\mathbf{1 0 0. 0 \\%}$ higher estimated success probability over baseline formulations.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00018"}, {"primary_key": "165350", "vector": [], "sparse_vector": [], "title": "SparseWeaver: Converting Sparse Operations as Dense Operations on GPUs for Graph Workloads.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Thanks to their scalable parallel processing capability, GPUs are promising computing resources for graph processing, in which identical operations are applied to a large number of edges and vertices. However, the sparsity and skewness of real-world graphs cause imbalanced workloads across GPU threads within the same warp, thus impeding efficient processing on the GPU. To mitigate this workload imbalance problem, existing works propose workload balancing hardware and software schemes. However, these solutions often suffer from additional memory overhead or increased computations and communication overheads during inter-warp and intra-warp synchronization. This work proposes a new hardware-software collaborative graph processing framework, SparseWeaver, that converts sparse operations in graph processing into dense operations using graph topology and makes the workloads balanced across GPU threads. Based on the analysis of common patterns in software schemes, we propose Weaver, a new lightweight GPU functional unit microarchitecture that fully leverages the benefits of the GPU architecture and exploits memory access locality. We prototype SparseWeaver on the open-source RISC-V Vortex GPU and demonstrate 2.36 times faster execution time compared to state-of-the-art schemes while incurring a low area overhead of 0.045% from increased dedicated logic registers.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00108"}, {"primary_key": "165351", "vector": [], "sparse_vector": [], "title": "Warped-Compaction: Maximizing GPU Register File Bandwidth Utilization via Operand Compaction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The GPU has been successfully used for diverse emerging compute-intensive applications, including imaging, computer vision, and more recently, deep learning, to name a few. To offer high performance for such applications, it is provisioned with massive Register Files (RFs) to exploit high Thread-Level Parallelism (TLP). As RFs are designed to store thousands of contexts and provide very high bandwidth for uninterrupted supply of operands to hundreds of compute units for high TLP, they have become one of the most power-hungry components in the GPU. Meanwhile, faced with the end of Dennard scaling, it is more important than ever to innovate the microarchitecture of (power-constrained) GPUs to continuously improve the performance of future compute-intensive applications.In this work, we propose a GPU microarchitecture, WarpedCompaction, designed to utilize given RFs more efficiently, instead of relying on larger-capacity and/or higher-bandwidth RFs to further improve GPU performance. Specifically, first, we reverse-engineer the latest GPU’s RF organization through microbenchmarking. This uncovers that each sub-core within a streaming multiprocessor contains only two dual-ported RF banks, and accesses to these banks are arbitrated solely based on register IDs. We also reveal that, despite the modest configurations, RF banks are largely underutilized, staying inactive for 33.5% of the time. Second, we observe that previously proposed RF optimization techniques, data forwarding and dead register elimination, cannot address this underutilization problem. This is mainly due to the (R1) insufficient RF access requests from a limited number of Operand Collector Units (OCUs) and (R2) inefficient operand distribution by the conventional RF bank arbitration. Third, we present two architectural solutions to tackle the observed inefficiency: (S1) OCU sharing and (S2) skewed arbitrator. Building on enhanced OCU early allocation for partially ready instructions and operand forwarding to OCUs, OCU sharing allows two different warp instructions to share a single OCU, and skewed arbitrator evenly distributes register accesses across RF banks, maximizing the utilization of given RF bandwidth. The synergistic integration of these techniques, forming Warped-Compaction, results in $\\mathbf{2 6. 1 \\%}$ higher performance and $\\mathbf{2 1. 7 \\%}$ better energy efficiency of RF and OCU compared to baseline high-end GPU.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00106"}, {"primary_key": "165352", "vector": [], "sparse_vector": [], "title": "EIGEN: Enabling Efficient 3DIC Interconnect with Heterogeneous Dual-Layer Network-on-Active-Interposer.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Chiplet-based 3DICs have emerged as modular solutions for large-scale, high-performance computing systems. However, unlike monolithic Network-on-Chips (NoCs), 3DICs using active interposers encounter difficulties in managing heterogeneous and high-load network traffic patterns. The emerging field of Networks-on-Active-Interposer (NOAI), independently designed from top dies’ interconnects, is desired to address these challenges by supporting flexible topologies, low-latency memory access, and reduced traffic congestion. To satisfy the requirements, we propose a heterogeneous dual-layer interconnect architecture, EIGEN, for chiplet-interposer systems, along with a reinforcement learning (RL)-based routing framework, which can provide efficient and flexible communication for chiplet-based 3DICs. EIGEN features an application-aware switch-programable interconnection layer (AspLayer) and a dynamic packet-routing interconnection layer (DynLayer) on the interposer, respectively. To optimize inter-chiplet data communication, we also develop an RL-based path routing framework tailored to this dual-layer architecture. The RL framework’s state space includes topology, network, and memory metrics, while the RL reward is set as the product of average packet latency and link utilization. The effectiveness of EIGEN is demonstrated through different applications where CPU, GPU, and AI accelerator chiplets are integrated via NOAI. Simulation results show that the proposed EIGEN achieves up to $\\mathbf{6 7. 1 6 \\%}$ latency reduction, $\\mathbf{5 3. 8 9 \\%}$ hops reduction and $\\mathbf{1 1. 2 1 \\%}$ runtime reduction compared to the state-of-the-art (SOTA) chiplets interconnect architectures. Furthermore, sensitivity analysis of network scaling shows that the EIGEN architecture and framework exhibit strong scalability, with latency reduction of $\\mathbf{2 0. 9 6 \\%}$ to $\\mathbf{5 2. 7 7 \\%}$ and hop reduction of $\\mathbf{1 9. 7 2 \\%}$ to $\\mathbf{4 3. 4 1 \\%}$ as the system scales from $4 \\times 4$ to $32 \\times 32$ chiplets.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00117"}, {"primary_key": "165353", "vector": [], "sparse_vector": [], "title": "Zebra: Efficient Redundant Array of Zoned Namespace SSDs Enabled by Zone Random Write Area (ZRWA).", "authors": ["Tianyang Jiang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Zoned Namespace (ZNS) SSDs have emerged as a promising solution for eliminating device-level garbage collection and achieving predictable performance through the zone abstraction, which supports append-only writes. However, integrating ZNS SSDs into RAID configurations presents challenges, particularly concerning partial parity updates (PPUs) due to the no-overwrite property of ZNS SSDs. Existing solutions introduce dedicated metadata zones to manage PPUs, but these zones become performance bottlenecks, especially under high PPU workloads.In this paper, we introduce Zebra, a novel architecture for ZNS RAID that leverages the Zone Random Write Area (ZRWA) feature of modern ZNS SSDs. Our key insight is that the SSD’s write buffer (or ZRWA) in front of each zone is ideal for PPUs that exhibit a repeated sequential overwrite I/O pattern. By ensuring that the parity chunk fits within the ZRWA window, Zebra enables efficient PPUs within the ZRWA. Combined with a set of techniques, Zebra ensures both high performance and reliability. We evaluate Zebra on both large-zone and small-zone ZNS SSDs, two typical ZNS models. Our experiments with micro and application benchmarks show that Zebra improves the throughput by 1.1×-4.2× compared to RAIZN, a state-of-the-art ZNS RAID system.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00052"}, {"primary_key": "165354", "vector": [], "sparse_vector": [], "title": "throttLL&apos;eM: Predictive GPU Throttling for Energy Efficient LLM Inference Serving.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As Large Language Models (LLMs) gain traction, their reliance on power-hungry GPUs places ever-increasing energy demands, raising environmental and monetary concerns. Inference dominates LLM workloads, presenting a critical challenge for providers: minimizing energy costs under Service-Level Objectives (SLOs) that ensure optimal user experience. In this paper, we present throttLL’eM, a framework that reduces energy consumption while meeting SLOs through the use of instance and GPU frequency scaling. throttLL’eM features mechanisms that project future Key-Value (KV) cache usage and batch size. Leveraging a Machine-Learning (ML) model that receives these projections as inputs, throttLL’eM manages performance at the iteration level to satisfy SLOs with reduced frequencies and instance sizes. We show that the proposed ML model achieves $R^{2}$ scores greater than 0.97 and miss-predicts performance by less than 1 iteration per second on average. Experimental results on LLM inference traces show that throttLL’eM achieves up to $\\mathbf{4 3. 8 \\%}$ lower energy consumption and an energy efficiency improvement of at least $1.71 \\times$ under SLOs, when compared to NVIDIA’s Triton server. throttLL’eM is publicly available at https://github.com/WilliamBlas<PERSON>wicz/throttLL-eM.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00103"}, {"primary_key": "165355", "vector": [], "sparse_vector": [], "title": "Panacea: Novel DNN Accelerator using Accuracy-Preserving Asymmetric Quantization and Energy-Saving Bit-Slice Sparsity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>o Yoo", "Seungwoo Hong", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Low bit-precisions and their bit-slice sparsity have recently been studied to accelerate general matrix-multiplications (GEMM) during large-scale deep neural network (DNN) inferences. While the conventional symmetric quantization facilitates low-resolution processing with bit-slice sparsity for both weight and activation, its accuracy loss caused by the activation’s asymmetric distributions cannot be acceptable, especially for largescale DNNs. In efforts to mitigate this accuracy loss, recent studies have actively utilized asymmetric quantization for activations without requiring additional operations. However, the cuttingedge asymmetric quantization produces numerous nonzero slices that cannot be compressed and skipped by recent bit-slice GEMM accelerators, naturally consuming more processing energy to handle the quantized DNN models.To simultaneously achieve high accuracy and hardware efficiency for large-scale DNN inferences, this paper proposes an Asymmetrically-Quantized bit-Slice GEMM (AQS-GEMM) for the first time. In contrast to the previous bit-slice computing, which only skips operations of zero slices, the AQS-GEMM compresses frequent nonzero slices, generated by asymmetric quantization, and skips their operations. To increase the slicelevel sparsity of activations, we also introduce two algorithm-hardware co-optimization methods: a zero-point manipulation and a distribution-based bit-slicing. To support the proposed AQS-GEMM and optimizations at the hardware-level, we newly introduce a DNN accelerator, Panacea, which efficiently handles sparse/dense workloads of the tiled AQS-GEMM to increase data reuse and utilization. Panacea supports a specialized dataflow and run-length encoding to maximize data reuse and minimize external memory accesses, significantly improving its hardware efficiency. Numerous benchmark evaluations show that Panacea outperforms existing DNN accelerators, e.g., $1.97 \\times$ and $3.26 \\times$ higher energy efficiency, and $1.88 \\times$ and $2.41 \\times$ higher throughput than the recent bit-slice accelerator Sibia and the SIMD design, respectively, on OPT-2.7B, while providing better algorithm performance with asymmetric quantization.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00059"}, {"primary_key": "165356", "vector": [], "sparse_vector": [], "title": "Multi-Dimensional Vector ISA Extension for Mobile In-Cache Computing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Yu<PERSON> Gu", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In-cache computing technology transforms existing caches into long-vector compute units and offers low-cost alternatives to building expensive vector engines for mobile CPUs. Unfortunately, existing long-vector Instruction Set Architecture (ISA) extensions, such as RISC-V Vector Extension (RVV) and Arm Scalable Vector Extension (SVE), provide only onedimensional strided and random memory accesses. While this is sufficient for typical vector engines, it fails to effectively utilize the large Single Instruction, Multiple Data (SIMD) widths of incache vector engines. This is because mobile data-parallel kernels expose limited parallelism across a single dimension. Based on our analysis of mobile vector kernels, we introduce a long-vector Multi-dimensional Vector ISA Extension (MVE) for mobile in-cache computing. MVE achieves high SIMD resource utilization and enables flexible programming by abstracting cache geometry and data layout. The proposed ISA features multi-dimensional strided and random memory accesses and efficient dimension-level masked execution to encode parallelism across multiple dimensions. Using a wide range of data-parallel mobile workloads, we demonstrate that MVE offers significant performance and energy reduction benefits of $2.9 \\times$ and $8.8 \\times$, on average, compared to the SIMD units of a commercial mobile processor, at an area overhead of 3.6%.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00045"}, {"primary_key": "165357", "vector": [], "sparse_vector": [], "title": "BrokenSleep: Remote Power Timing Attack Exploiting Processor Idle States.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Gyeongseo Park", "Seungky<PERSON> Lee", "<PERSON><PERSON><PERSON>"], "summary": "Power and energy consumption emerge as critical aspects in computing systems, spanning from mobile devices to data-center servers. Modern processors typically support idle states (i.e., C-states), which deactivate specific hardware components, in addition to offering multiple voltage and frequency states (i.e., P-states). While C-states can significantly reduce static power when processor cores are idle, a notable security vulnerability arises due to differences in wake-up latency among various C-states when the processor cores become active again. This paper proposes a security vulnerability arising from processor idle state management, called BrokenSleep, which exploits the aforementioned wake-up latency differences to create covert and side-channel between computing nodes connected via an external network. This study presents the first remote timing attack based on power management, overcoming the limitations of previous research that required the co-location of attacker and victim applications on the same local machine. This advancement significantly extends the range of existing remote timing attacks by integrating power-related factors. Regardless of the computing system types, our experiments demonstrate that an attacker can transfer data to remote machines without direct network access and deduce the keystroke timing. This vulnerability is not confined to a single processor architecture; it affects processors designed by both Intel and ARM, indicating a widespread potential risk across different hardware platforms.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00040"}, {"primary_key": "165358", "vector": [], "sparse_vector": [], "title": "Ditto: Accelerating Diffusion Model via Temporal Value Similarity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Mincheol Park", "Won Woo Ro"], "summary": "Diffusion models achieve superior performance in image generation tasks. However, it incurs significant computation overheads due to its iterative structure. To address these overheads, we analyze this iterative structure and observe that adjacent time steps in diffusion models exhibit high value similarity, leading to narrower differences between consecutive time steps. We adapt these characteristics to a quantized diffusion model and reveal that the majority of these differences can be represented with reduced bit-width, and even zero. Based on our observations, we propose the Ditto algorithm, a difference processing algorithm that leverages temporal similarity with quantization to enhance the efficiency of diffusion models. By exploiting the narrower differences and the distributive property of layer operations, it performs full bit-width operations for the initial time step and processes subsequent steps with temporal differences. In addition, Ditto execution flow optimization is designed to mitigate the memory overhead of temporal difference processing, further boosting the efficiency of the Ditto algorithm. We also design the Ditto hardware, a specialized hardware accelerator, fully exploiting the dynamic characteristics of the proposed algorithm. As a result, the Ditto hardware achieves up to $1.5 \\times$ speedup and 17.74% energy saving compared to other accelerators.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00035"}, {"primary_key": "165359", "vector": [], "sparse_vector": [], "title": "LSQCA: Resource-Efficient Load/Store Architecture for Limited-Scale Fault-Tolerant Quantum Computing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Current fault-tolerant quantum computer (FTQC) architectures utilize several encoding techniques to enable reliable logical operations with restricted qubit connectivity. However, such logical operations demand additional memory overhead to ensure fault tolerance. Since the main obstacle to practical quantum computing is the limited qubit count, our primary mission is to design floorplans that can reduce memory overhead without compromising computational capability. Despite extensive efforts to explore FTQC architectures, even the current state-of-the-art floorplan strategy devotes 50% of memory space to this overhead, not to data storage, to guarantee unit-time random access to all logical qubits. In this paper, we propose an FTQC architecture based on a novel floorplan strategy, Load/Store Quantum Computer Architecture (LSQCA), which can achieve almost 100% memory density. The idea behind our architecture is to separate the whole memory regions into small computational space called Computational Registers (CR) and space-efficient memory space called Scan-Access Memory (SAM). We define an instruction set for these abstract structures and provide concrete designs named point-SAM and line-SAM architectures. With this design, we can improve the memory density by allowing variable-latency memory access while concealing the latency with other bottlenecks. We also propose optimization techniques to exploit properties of quantum programs observed in our static analysis, such as access locality in memory reference timestamps. Our numerical results indicate that LSQCA successfully leverages this idea. In a resource-restricted situation, a specific benchmark shows that we can achieve approximately 90% memory density with 5% increase in the execution time compared to a conventional floorplan, which achieves at most 50% memory density for unit-time random access. Our design is defined as an abstract form, making this principle ubiquitous and applicable to a wide range of quantum devices, qubit-connectivity configurations, and error-correcting codes.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00033"}, {"primary_key": "165360", "vector": [], "sparse_vector": [], "title": "Revisiting Reliability in Large-Scale Machine Learning Research Clusters.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Reliability is a fundamental challenge in operating large-scale machine learning (ML) infrastructures, particularly as the scale of ML models and training clusters continues to grow. Despite decades of research on infrastructure failures, the impact of job failures across different scales remains unclear. This paper presents a view of managing two large, multi-tenant ML clusters, providing quantitative analysis, operational experience, and our own perspective in understanding and addressing reliability concerns at scale. Our analysis reveals that while large jobs are most vulnerable to failures, smaller jobs make up the majority of jobs in the clusters and should be incorporated into optimization objectives. We identify key workload properties, compare them across clusters, and demonstrate essential reliability requirements for pushing the boundaries of ML training at scale.We hereby introduce a taxonomy of failures and key reliability metrics, analyze 11 months of data from two state-of-the-art ML environments with 4 million jobs and over 150 million A100 GPU hours. Building on our data, we fit a failure model to project Mean Time to Failure for various GPU scales. We further propose a method to estimate a related metric, Effective Training Time Ratio, as a function of job parameters, and we use this model to gauge the efficacy of potential software mitigations at scale. Our work provides valuable insights and future research directions for improving the reliability of AI supercomputer clusters, emphasizing the need for flexible, workload-agnostic, and reliability-aware infrastructure, system software, and algorithms.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00096"}, {"primary_key": "165361", "vector": [], "sparse_vector": [], "title": "PAISE: PIM-Accelerated Inference Scheduling Engine for Transformer-based LLM.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>on Baek", "Jimyoung Son", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Transformer-based Large Language Models (LLMs) demand significant computational and memory resources due to the autoregressive token generation in decoder blocks. In particular, the attention layer in LLM models has low arithmetic intensity but high memory traffic, thus requiring frequent updates to the KV matrices with each decoder iteration. As a result, LLM inference becomes memory bound, leading to increased latency. To address this, we introduce PAISE, a framework leveraging Processing-In-Memory (PIM) technology to offload memory-intensive tasks. PAISE employs GPU-PIM heterogeneous computing resources to optimize inference operations in transformer-based LLMs. The framework comprises (i) a scheduling algorithm that decides which operations to offload to PIM based on model configuration and PIM hardware specifications and (ii) an enhanced PIM kernel that performs transaction-wise interleave-batched GEMM (General Matrix Multiplication) operations, maximizing data throughput via data layout adjustments. We implemented PAISE on the GPT-2 and Llama2-7B models using an AMD MI100 GPU with HBM-PIM devices. Our evaluations show that offloading the attention layer to PIM reduces execution time by up to 48.3% compared to GPU-only inference, demonstrating PAISE’s significant potential to enhance the efficiency of LLM inference, which could lead to faster and more efficient AI applications.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00126"}, {"primary_key": "165362", "vector": [], "sparse_vector": [], "title": "Let-Me-In: (Still) Employing In-pointer Bounds Metadata for Fine-grained GPU Memory Safety.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Na", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The importance of ensuring the robustness of GPU systems has grown significantly, especially as GPUs have become vital in critical decision-making systems such as autonomous driving and medical diagnostics. However, GPU programming languages, primarily based on $\\mathrm{C} / \\mathrm{C}++$, inherit memory vulnerabilities that threaten the robustness of GPU applications. The heterogeneous GPU memory hierarchy makes it more difficult to find effective universal solutions. While several studies have proposed advanced GPU memory safety mechanisms, they still grapple with significant challenges, including substantial metadata storage and access overhead, elevated hardware implementation costs, and limited security coverage, particularly regarding fine-grained memory safety. We address this issue with Let-Me-In (LMI), a fine-grained memory safety mechanism specifically designed for GPUs. LMI features an efficient hardware bounds-checking mechanism that ensures negligible impact on performance and hardware costs, even in scenarios where thousands of concurrent threads perform memory operations across buffers in heap and local memory. This is achieved by aligning memory allocation to powers of two and performing static analysis to identify and mark pointer arithmetic instructions. This approach also enables storing metadata inside the unused upper bits of pointers, which are shrinking due to the expansion of the virtual memory address space. The unique characteristics of GPU programs make this approach feasible, unlike in CPU programs, where the inherent complexity of programs poses challenges. Our evaluation shows that LMI incurs only negligible hardware and performance overhead, making it a practical and efficient solution for enhancing GPU memory safety.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00122"}, {"primary_key": "165363", "vector": [], "sparse_vector": [], "title": "VR-Pipe: Streamlining Hardware Graphics Pipeline for Volume Rendering.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Junyong Park", "Jaewoong Sim"], "summary": "Graphics rendering that builds on machine learning and radiance fields is gaining significant attention due to its outstanding quality and speed in generating photorealistic images from novel viewpoints. However, prior work has primarily focused on evaluating its performance through software-based rendering on programmable shader cores, leaving its performance when exploiting fixed-function graphics units largely unexplored. In this paper, we investigate the performance implications of performing radiance field rendering on the hardware graphics pipeline. In doing so, we implement the state-of-the-art radiance field method, 3D Gaussian splatting, using graphics APIs and evaluate it across synthetic and real-world scenes on today’s graphics hardware. Based on our analysis, we present VR-Pipe, which seamlessly integrates two innovations into graphics hardware to streamline the hardware pipeline for volume rendering, such as radiance field methods. First, we introduce native hardware support for early termination by repurposing existing special-purpose hardware in modern GPUs. Second, we propose multi-granular tile binning with quad merging, which opportunistically blends fragments in shader cores before passing them to fixed-function blending units. Our evaluation shows that VR-Pipe greatly improves rendering performance, achieving up to a $2.78 \\times$ speedup over the conventional graphics pipeline with negligible hardware overhead.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00027"}, {"primary_key": "165364", "vector": [], "sparse_vector": [], "title": "Marching Page Walks: Batching and Concurrent Page Table Walks for Enhancing GPU Throughput.", "authors": ["<PERSON><PERSON>", "Gun Ko", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Won Woo Ro"], "summary": "Virtual memory, with the support of address translation hardware, is a key technique in expanding programmability and memory management in GPUs. However, the nature of the GPU execution model heavily pressures its translation hardware, particularly due to a discrepancy in the behavior of page table walkers and thousands of concurrently running threads. In GPU workloads, multiple threads simultaneously access a number of pages necessitating a substantial number of translations whereas each walker handles only a single walk request at a time. Such a limitation significantly increases the queueing latency of walk requests, which we observe as a major bottleneck for servicing page table walks in GPUs. To tackle this challenge, we investigate a design of page walkers that facilitates multiple walk requests to be handled together in batches. Then, we make the following observations: 1) allowing a page walker to issue beyond a single memory request significantly improves the throughput of walkers, and 2) GPU applications tend to concurrently access pages in wide address ranges. By leveraging the above implications, we propose Marching Page Walks (MPW) that effectively mitigate the contention in GPU page table walkers. MPW scans pending walk requests to identify ones that can be grouped together. Then, MPW batches these requests and concurrently handles them by issuing multiple memory instructions. Experiments show that MPW reduces the queueing latency of page walks by 86.7% and improves GPU performance by 55.6% over the baseline design.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00123"}, {"primary_key": "165365", "vector": [], "sparse_vector": [], "title": "Uni-Render: A Unified Accelerator for Real-Time Rendering Across Diverse Neural Renderers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent advancements in neural rendering technologies and their supporting devices have paved the way for immersive 3D experiences, significantly transforming human interaction with intelligent devices across diverse applications. However, achieving the desired real-time rendering speeds for immersive interactions is still hindered by (1) the lack of a universal algorithmic solution for different application scenarios and (2) the dedication of existing devices or accelerators to merely specific rendering pipelines. To overcome this challenge, we have developed a unified neural rendering accelerator that caters to a wide array of typical neural rendering pipelines, enabling real-time and on-device rendering across different applications while maintaining both efficiency and compatibility. Our accelerator design is based on the insight that, although neural rendering pipelines vary and their algorithm designs are continually evolving, they typically share common operators, predominantly executing similar workloads. Building on this insight, we propose a reconfigurable hardware architecture that can dynamically adjust dataflow to align with specific rendering metric requirements for diverse applications, effectively supporting both typical and the latest hybrid rendering pipelines. Benchmarking experiments and ablation studies on both synthetic and real-world scenes demonstrate the effectiveness of the proposed accelerator. It achieves real-time rendering speeds (> 30 FPS) and up to $119 \\times$ speedups over state-of-the-art neural rendering hardware across varied rendering pipelines, while adhering to power consumption constraints of around 5 W, typical for edge devices. Consequently, the proposed unified accelerator stands out as the first solution capable of achieving real-time neural rendering across varied representative pipelines on edge devices, potentially paving the way for the next generation of neural graphics applications.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00029"}, {"primary_key": "165366", "vector": [], "sparse_vector": [], "title": "LUT-DLA: Lookup Table as Efficient Extreme Low-Bit Deep Learning Accelerator.", "authors": ["<PERSON><PERSON>", "Shengyu Ye", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The emergence of neural network capabilities invariably leads to a significant surge in computational demands due to expanding model sizes and increased computational complexity. To reduce model size and lower inference costs, recent research has focused on simplifying models and designing hardware accelerators using low-bit quantization. However, due to numerical representation limits, scalar quantization cannot reduce bit width lower than 1-bit, diminishing its benefits. To break through these limitations, we introduce LUT-DLA, a Look-Up Table (LUT) Deep Learning Accelerator Framework that utilizes vector quantization to convert neural network models into LUTs, achieving extreme low-bit quantization. The LUT-DLA framework facilitates efficient and cost-effective hardware accelerator designs and supports the LUTBoost algorithm, which helps to transform various DNN models into LUT-based models via multistage training, drastically cutting both computational and hardware overhead. Additionally, through co-design space exploration, LUT-DLA assesses the impact of various model and hardware parameters to fine-tune hardware configurations for different application scenarios, optimizing performance and efficiency. Our comprehensive experiments show that LUT-DLA achieves improvements in power efficiency and area efficiency with gains of 1.4~7.0× and 1.5~146.1×, respectively, while maintaining only a modest accuracy drop. For CNNs, accuracy decreases by 0.1%~3.1% using the L2 distance similarity, 0.1%~3.4% with the L1 distance similarity, and 0.1%~3.8% when employing the Chebyshev distance similarity. For transformer-based models, the accuracy drop ranges from 1.4% to 3.0%.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00057"}, {"primary_key": "165367", "vector": [], "sparse_vector": [], "title": "Adyna: Accelerating Dynamic Neural Networks with Adaptive Scheduling.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xintong Li", "<PERSON><PERSON>"], "summary": "Dynamic architecture neural networks (DynNNs) are an emerging type of deep learning models that can leverage different processing difficulties of data samples to dynamically reduce computation demands at runtime. However, current GPUs and specialized accelerators lack the necessary architecture and dataflow support to achieve the promised theoretical efficiency improvements due to the high dynamism in DynNN execution. We propose Adyna, a novel hardware-software co-design solution to efficiently support DynNN inference. Adyna uses a unified representation to capture most existing DynNNs to enable a general design. It features a dynamism-aware, multi-kernel selection paradigm, in which the dataflow scheduler makes resource allocation decisions according to the distribution of dynamic size values, and the hardware architecture keeps multiple precompiled kernels and selects the best matching one to process each specific data sample according to its dynamic size. Adyna further uses an effective kernel sampling algorithm to carefully choose the set of kernels to load onto the hardware. Evaluated on various DynNN models, Adyna can outperform state-of-the-art multi-tile and multi-tenant accelerators by $1.70 \\times$ and $1.57 \\times$ on average, and up to $2.32 \\times$ and $2.01 \\times$.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00049"}, {"primary_key": "165368", "vector": [], "sparse_vector": [], "title": "Integrating Prefetcher Selection with Dynamic Request Allocation Improves Prefetching Efficiency.", "authors": ["<PERSON><PERSON><PERSON> Li", "<PERSON><PERSON>", "Yongqing Ren", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Hardware prefetching plays a critical role in hiding the off-chip DRAM latency. The complexity of applications results in a wide variety of memory access patterns, prompting the development of numerous cache-prefetching algorithms. Consequently, commercial processors often employ a hybrid of these algorithms to enhance the overall prefetching performance. Nonetheless, since these prefetchers share hardware resources, conflicts arising from competing prefetching requests can negate the benefits of hardware prefetching. Under such circumstances, several prefetcher selection algorithms have been proposed to mitigate conflicts between prefetchers. However, these prior solutions suffer from two limitations. First, the input demand request allocation is inaccurate. Second, the prefetcher selection criteria are coarse-grained. In this paper, we address both limitations by introducing an efficient and widely applicable prefetcher selection algorithm—Alecto 1, which tailors the demand requests for each prefetcher. Every demand request is first sent to Alecto to identify suitable prefetchers before being routed to prefetchers for training and prefetching. Our analysis shows that <PERSON><PERSON> is adept at not only harmonizing prefetching accuracy, coverage, and timeliness but also significantly enhancing the utilization of the prefetcher table, which is vital for temporal prefetching. <PERSON><PERSON> outperforms the state-of-the-art RL-based prefetcher selection algorithm—Bandit by $2.76 \\%$ in single-core, and $\\mathbf{7. 5 6 \\%}$ in eight-core. For memory-intensive benchmarks, <PERSON><PERSON> outperforms Bandit by $\\mathbf{5. 2 5 \\%}$. <PERSON><PERSON> consistently delivers state-of-the-art performance in scheduling various types of cache prefetchers. In addition to the performance improvement, Alecto can reduce the energy consumption associated with accessing the prefetchers’ table by $48 \\%$ ($7 \\%$ energy reduction on the entire memory hierarchy), while only adding less than 1 KB of storage overhead.1The name Alecto stands for the combination of selection and allocation.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00026"}, {"primary_key": "165369", "vector": [], "sparse_vector": [], "title": "TidalMesh: Topology-Driven AllReduce Collective Communication for Mesh Topology.", "authors": ["Dongkyun Lim", "<PERSON>"], "summary": "In deep learning workloads, collective communication across multiple nodes is a critical component in determining overall performance. AllReduce (as well as ReduceScatter and AllGather) is a commonly used collective communication for not only training but also inference. The performance of AllReduce depends on the algorithm utilized (or the “logical” topology) as well as the physical topology of the system that interconnects the nodes together. There has been many work on improving AllReduce performance but prior work have often been topologyaware approach where existing AllReduce algorithms were optimized for a given physical topology. In this work, we propose a topology-driven approach where the topology characteristics are exploited to propose a novel AllReduce collective communication algorithm; thus, the logical topology of the algorithm maps well to the physical topology. In particular, as 2D mesh topology is widely used in various scale-out systems, we propose TidalMesh AllReduce algorithm - a novel approach that exploits the inherent characteristics of the physical 2D mesh topology by pushing flows between the endpoint nodes, similar to a tidal wave, to achieve near-optimal performance for AllReduce. We propose how Sparse TidalMesh AllReduce minimizes bandwidth overhead of TidalMesh with no loss in performance. In addition, we demonstrate how collective communication unrolling can be exploited to enable “software pipelining” of collective communication while exploiting the unique opportunity of superimposing different phases of AllReduce. As a result, TidalMesh results in up to $\\mathbf{2 4 \\%}$ improvement in AllReduce performance across various deep learning models on a 64 -node $8 \\times 8$ 2D mesh, compared to the state-of-the-art while maintaining the simplicity of a logical ring algorithm.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00114"}, {"primary_key": "165370", "vector": [], "sparse_vector": [], "title": "Reuse-Aware Compilation for Zoned Quantum Architectures Based on Neutral Atoms.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Quantum computing architectures based on neutral atoms offer large scales and high-fidelity operations. They can be heterogeneous, with different zones for storage, entangling operations, and readout. Zoned architectures improve computation fidelity by shielding idling qubits in storage from side-effect noise, unlike monolithic architectures where all operations occur in a single zone. However, supporting these flexible architectures with efficient compilation remains challenging. In this paper, we propose ZAC, a scalable compiler for zoned architectures. ZAC minimizes data movement overhead between zones with qubit reuse, i.e., keeping them in the entanglement zone if an immediate entangling operation is pending. Other innovations include novel data placement and instruction scheduling strategies in ZAC, a flexible specification of zoned architectures, and an intermediate representation for zoned architectures, ZAIR. Our evaluation shows that zoned architectures equipped with ZAC achieve a 22x improvement in fidelity compared to monolithic architectures. Moreover, ZAC is shown to have a 10% fidelity gap on average compared to the ideal solution. This significant performance enhancement enables more efficient and reliable quantum circuit execution, enabling advancements in quantum algorithms and applications. ZAC is open source at https://github.com/UCLAVAST/ZAC", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00021"}, {"primary_key": "165371", "vector": [], "sparse_vector": [], "title": "CROSS: Compiler-Driven Optimization of Sparse DNNs Using Sparse/Dense Computation Kernels.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Li <PERSON>"], "summary": "As deep learning models continue to grow larger and more complex, exploiting sparsity is becoming one of the most critical areas for enhancing efficiency and scalability. Several methods for leveraging sparsity have been proposed to more effectively balance the trade-off between compression ratio and accuracy. While these methods offer algorithmic advantages, they also introduce significant hardware overhead due to index-based encoding and decoding. In this paper, we propose CROSS, an end-to-end compilation optimization technique to achieve sparse DNN acceleration using GPU computation kernels. The key insight behind CROSS is to exploit parameter distribution locality and reconcile the “sparse” DNN computation with the high-performance “dense” computation kernels. Specifically, we perform an in-depth analysis of sparse operations in mainstream DNN computing frameworks. We then decompose the sparse workload into multiple components to create highly efficient, specialized operators with different sparsity levels. Additionally, we introduce a novel sparse graph translation technique to facilitate computation kernel processing of the sparse workload. The resulting CROSS framework can accommodate various sparsity patterns and optimization techniques, delivering an average $2.03 \\times$ speedup on inference latency compared to seven state-of-the-art solutions with smaller memory footprints across various models and datasets.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00076"}, {"primary_key": "165372", "vector": [], "sparse_vector": [], "title": "HATT: Hamiltonian Adaptive Ternary Tree for Optimizing Fermion-to-Qubit Mapping.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Yunong Shi"], "summary": "This paper introduces the Hamiltonian-Adaptive Ternary Tree (HATT) framework to compile optimized Fermion-to-qubit mapping for specific Fermionic Hamiltonians. In the simulation of Fermionic quantum systems, efficient Fermion-toqubit mapping plays a critical role in transforming the Fermionic system into a qubit system. HATT utilizes ternary tree mapping and a bottom-up construction procedure to generate Hamiltonian aware Fermion-to-qubit mapping to reduce the Pauli weight of the qubit Hamiltonian, resulting in lower quantum simulation circuit overhead. Additionally, our optimizations retain the important vacuum state preservation property in our Fermion-toqubit mapping and reduce the complexity of our algorithm from $O\\left(N^{4}\\right)$ to $O\\left(N^{3}\\right)$. Evaluations on various Fermionic systems demonstrate $5 \\sim 25 \\%$ reduction in Pauli weight, gate count, and circuit depth, alongside excellent scalability to larger systems. Experiments on the Ionq device also show the advantages of HATT in noise resistance in quantum simulations.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00022"}, {"primary_key": "165373", "vector": [], "sparse_vector": [], "title": "TB-STC: Transposable Block-wise N: M Structured Sparse Tensor Core.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The computational and memory demands of Deep Learning (DL) models, from convolutional neural networks to Large Language Models (LLMs), are experiencing a notable surge. The sparsification (e.g., weight pruning and sparse attention) represents a significant approach to reducing latency and energy consumption. However, it is non-trivial to identify a good trade-off between model accuracy and hardware efficiency. Existing work has sought to mitigate the hardware complexity overhead through structured sparsity, yet the resulting accuracy loss remains considerable (e.g., more than 6% accuracy drop with 50% structured sparsity on OPT-6.7B and Llama2-7B).To address the above challenges, this paper proposes Transposable Block-wise Structured Sparsity (TBS). Our key insight is that the weight matrices of the forward and backward pass are transposed to each other during DL training. Exploiting this transposition property facilitates obtaining a structured sparsity pattern that is closer to the unstructured sparsity. In contrast, existing studies explore only one-dimensional structured sparsity. In light of these observations, we propose the transposable block-wise structured sparsity pattern with an efficient end-to-end sparse training method. This method improves accuracy by up to 2.58% over other structured sparsity studies under the same sparsity degree. At the micro-architecture level, we propose TB-STC, a Transposable Block-wise N:M Sparse Tensor Core to efficiently and flexibly facilitate the TBS pattern. TB-STC introduces an adaptive codec architecture for on-the-fly storage format conversion with a higher bandwidth utilization (1.47 ×), and implements an I/O-aware configurable architecture for sparsity-aware scheduling with a better computational utilization (1.57×). Compared with existing work, TB-STC improves the Energy-Delay Product (EDP) by an average of 3.82 × and offers an enhanced accuracy-EDP Pareto frontier across various sparse DL models.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00075"}, {"primary_key": "165374", "vector": [], "sparse_vector": [], "title": "Make LLM Inference Affordable to Everyone: Augmenting GPU Memory with NDP-DIMM.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Ren", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The billion-scale Large Language Models (LLMs) necessitate deployment on expensive server-grade GPUs with large-storage HBMs and abundant computation capability. As LLM-assisted services become popular, achieving cost-effective LLM inference on budget-friendly hardware becomes the current trend. This has sparked extensive research into relocating LLM parameters from expensive GPUs to external host memory. However, the restricted bandwidth between the host and GPU memory limits the inference performance of existing solutions. This work introduces Hermes, a budget-friendly system that leverages the near-data processing units (NDP) within commodity DRAM DIMMs to enhance the performance of a single consumer-grade GPU, achieving efficient LLM inference. We recognize that the inherent activation sparsity in LLMs naturally divides weight parameters into two categories, termed “hot” and “cold” neurons, respectively. Hot neurons, which consist of only approximately 20% of all weight parameters, account for 80% of the total computational load. In contrast, cold neurons make up the other 80% of parameters but are responsible for just 20% of the computational workload. Leveraging this observation, we propose a heterogeneous computing strategy: mapping hot neurons to a single computation-efficient GPU without large-capacity HBMs, while offloading cold neurons to NDP-DIMMs, which offer large memory size but limited computation capabilities. In addition, the dynamic nature of activation sparsity necessitates a real-time partition of hot and cold neurons and adaptive remapping of cold neurons across multiple NDP-DIMM modules. To tackle these issues, we introduce a lightweight predictor that ensures optimal real-time neuron partition and adjustment between GPU and NDP-DIMMs. Furthermore, we utilize a window-based online scheduling mechanism to maintain load balance among multiple NDP-DIMM modules. In summary, Hermes facilitates the deployment of LLaMA2-70B on consumer-grade hardware at a rate of 13.75 tokens/s and realizes an average 75.24 × speedup over the state-of-the-art offloading-based inference system on popular LLMs.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00129"}, {"primary_key": "165375", "vector": [], "sparse_vector": [], "title": "Mascot: Predicting Memory Dependencies and Opportunities for Speculative Memory Bypassing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Memory-dependence prediction (MDP) increases instruction-level parallelism (ILP) by allowing load instructions to be issued even when addresses in the store queue are unknown. The predictor determines whether a load will alias with a prior store, delaying issue when a dependence is predicted. Speculative memory bypassing (SMB) further enhances ILP by short-circuiting a predicted dependence to forward the value written by a store to a load that is predicted to depend on it, without their addresses necessarily being known. This breaks data dependencies on the load and store addresses, allowing loads to obtain their values much earlier than they normally would. To obtain benefits, dependencies must be predicted with high accuracy. Furthermore, the benefits are skewed, with false negatives being more costly for performance than false positives for MDP, since the former requires squashing when the misprediction is identified, whereas the latter only delays the issue of independent loads. For SMB, on the other hand, false positives are very costly, as they require squashing, whereas false negatives have little impact in the presence of an accurate memory dependence predictor. Due to these differing requirements, the designs of predictors for these mechanisms have diverged. In this paper, we propose Mascot, a novel predictor capable of performing both MDP and SMB. MASCOT is inspired by the TAGE predictor, widely used in branch prediction. Although TAGE has proven effective as a universal predictor structure, we demonstrate how prior TAGE-based MDP or SMB predictors suffer from inaccuracy due to not learning patterns of non-dependence. By learning the context for dependencies as well as non-dependencies, Mascot achieves sufficiently low false negatives and false positives to perform MDP and SMB, while at the same time uses less space than existing designs that only perform MDP or SMB. Our simulation results show that for SPEC CPU 2017, MAscot used for MDP alone yields an IPC gain of ${0. 4 \\%}$ over the previous state-of-the-art predictor, on average, at the same size. When used for both MDP and SMB, it yields an increase in IPC of 1.9% on average, with peak gains of 26%. A compacted version of Mascot, Mascot-opt, achieves similar numbers within 0.1% while using just 10.1 KiB of space.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00016"}, {"primary_key": "165376", "vector": [], "sparse_vector": [], "title": "Cooperative Warp Execution in Tensor Core for RISC-V GPGPU.", "authors": ["Abubak<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The rise of Deep Neural Networks (DNNs) has amplified the demand for efficient computation, with General Matrix Multiply (GEMM) operations at their core. While ASICs are efficient but inflexible, GPUs, especially NVIDIA GPUs, equipped with tensor cores, provide a flexible yet high-performance solution for GEMM-based workloads. Previous research and optimizations have largely centered on NVIDIA’s architecture and programming model, which, while effective, can obscure the rationale behind certain design decisions and limit flexibility for further improvements in tensor core designs. In this paper, we present the design and integration of a tensor core into the open-source RISC-V Vortex GPGPU platform, along with a suite of intrinsics designed for GEMM kernel generation. The analysis conducted on the integration elucidates the connections between GPU system architectural parameters and tensor core configuration. We find that the tensor core is severely under-utilized in many cases and that increased compute capacity does not always imply better performance. Hence, we propose a novel technique, cooperative warp execution in tensor core, which leverages hardware-supported warp cooperation within the tensor core to reduce memory requirements for GEMM operations and boost performance over the baseline tensor core implementation by up to 3x.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00107"}, {"primary_key": "165377", "vector": [], "sparse_vector": [], "title": "Variable Read Disturbance: An Experimental Analysis of Temporal Variation in DRAM Read Disturbance.", "authors": ["Ataberk Olgun", "<PERSON><PERSON>", "<PERSON>", "Oguzhan Canpolat", "<PERSON><PERSON><PERSON>", "Geraldo F. Oliveira", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Modern DRAM chips are subject to read disturbance errors. These errors manifest as security-critical bitflips in a victim DRAM row that is physically nearby a repeatedly activated (opened) aggressor row (Row<PERSON><PERSON><PERSON>) or an aggressor row that is kept open for a long time (RowPress). State-of-the-art read disturbance mitigations rely on accurate and exhaustive characterization of the read disturbance threshold ($R D T$) (e.g., the number of aggressor row activations needed to induce the first RowHammer or RowPress bitflip) of every DRAM row (of which there are millions or billions in a modern system) to prevent read disturbance bitflips securely and with low overhead. We experimentally demonstrate for the first time that the RDT of a DRAM row significantly and unpredictably changes over time. We call this new phenomenon variable read disturbance (VRD). Our extensive experiments using 160 DDR4 chips and 4 HBM2 chips from three major manufacturers yield three key observations. First, it is very unlikely that relatively few RDT measurements can accurately identify the RDT of a DRAM row. The minimum RDT of a DRAM row appears after tens of thousands of measurements (e.g., up to 94,467), and the minimum RDT of a DRAM row is $3.5 \\times$ smaller than the maximum RDT observed for that row. Second, the probability of accurately identifying a row’s RDT with a relatively small number of measurements reduces with increasing chip density or smaller technology node size. Third, data pattern, the amount of time an aggressor row is kept open, and temperature can affect the probability of accurately identifying a DRAM row’s RDT. Our empirical results have implications for the security guarantees of read disturbance mitigation techniques: if the RDT of a DRAM row is not identified accurately, these techniques can easily become insecure. We discuss and evaluate using a guardband for RDT and error-correcting codes for mitigating read disturbance bitflips in the presence of RDTs that change unpredictably over time. We conclude that $a\\gt 10 \\%$ guardband for the minimum observed RDT combined with SECDED or Chipkill-like SSC error-correcting codes could prevent read disturbance bitflips at the cost of large read disturbance mitigation performance overheads (e.g., 45% performance loss for an RDT guardband of $50 \\%$). We hope and believe future work on efficient online profiling mechanisms and configurable read disturbance mitigation techniques could remedy the challenges imposed on today’s read disturbance mitigations by the variable read disturbance phenomenon.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00069"}, {"primary_key": "165378", "vector": [], "sparse_vector": [], "title": "ARTEMIS: Agile Discovery of Efficient Real-Time Systems-on-Chips in the Heterogeneous Era.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Alper Buyuktosunoglu", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Heterogeneous systems-on-chips (SoCs) are pivotal for real-time applications like autonomous driving, as they blend the versatility of CPUs with the efficiency of accelerator IPs. However, evolving application demands necessitate domain-specific SoCs to meet real-time deadlines within strict power and area constraints. While prior research focused on microarchitectural optimizations, overlooking broader system-level considerations can lead to suboptimal design decisions. Thus, there is a need to elevate the abstraction level of design space exploration (DSE) to the SoC level. However, SoC-level DSE is challenging due to the vast design space, encompassing microarchitectural parameters and dynamic task-to-hardware mapping choices based on runtime characteristics and real-time constraints. This paper proposes a systematic and agile methodology, called ARTEMIS, for efficient DSE of real-time, domain-specific SoCs that are constrained by task deadlines, power, and area. The core concept involves integrating a dynamic SoC scheduler to reduce the design space by eliminating the mapping dimension. Enhanced scheduling policies, incorporating techniques like task procrastination and memory-traffic/energy awareness, expedite navigation through the pruned design space. Additionally, DSE heuristics are optimized with real-time deadline and power/areaaware ranking mechanisms. ARTEMIS is evaluated on autonomous vehicle (AV) and augmented/virtual reality (AR/VR) applications, and additionally validated on an FPGA. Compared to the state-of-the-art, DSE using ARTEMIS converges 5.1$12.8 \\times$ faster, while yielding SoCs that meet $100 \\%$ real-time deadlines with $1.2-3 \\times$ better throughput at iso-area or up to $2.4 \\times$ lower area for at iso-input-rate. ARTEMIS thus enables DSE of large designs with tractable simulation resources, without compromising on the power-performance-area metrics of the explored SoC design.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00100"}, {"primary_key": "165379", "vector": [], "sparse_vector": [], "title": "InstAttention: In-Storage Attention Offloading for Cost-Effective Long-Context LLM Inference.", "authors": ["Xiurui Pan", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yizhou Shan", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The widespread of Large Language Models (LLMs) marks a significant milestone in generative AI. Nevertheless, the increasing context length and batch size in offline LLM inference escalate the memory requirement of the key-value (KV) cache, which imposes a huge burden on the GPU VRAM, especially for resource-constrained scenarios (e.g., edge computing). Several cost-effective solutions leverage host memory or SSDs to reduce storage costs for offline inference scenarios and improve the throughput. Nevertheless, they suffer from significant performance penalties imposed by intensive KV cache accesses due to limited PCIe bandwidth. To address these issues, we propose InstAttention, a novel LLM inference system that offloads the most performance-critical computation (i.e., attention in decoding phase) and data (i.e., KV cache) parts to Computational Storage Drives (CSDs), which minimize the enormous KV transfer overheads. InstAttention designs a dedicated flashaware in-storage attention engine with KV cache management mechanisms to exploit the high internal bandwidths of CSDs instead of being limited by the PCIe bandwidth. The optimized P2P transmission between GPU and CSDs further reduces data migration overheads. Experimental results demonstrate that for a 13B model using an NVIDIA A6000 GPU, InstAttention improves throughput for long-sequence inference by up to $11.1 \\times$, compared to existing SSD-based solutions such as FlexGen.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00113"}, {"primary_key": "165380", "vector": [], "sparse_vector": [], "title": "EDA: Energy-Efficient Inter-Layer Model Compilation for Edge DNN Inference Acceleration.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Tsung Tai Yeh"], "summary": "Modern handheld devices often employ neural processing units (NPUs) to accelerate deep neural network (DNN) inference applications. Unlike the AI accelerator of a data center, the NPU of an edge device has strict price and energy budgets. In an NPU, DRAM memory consumes much energy due to frequent data movement across on-chip and off-chip memory. The inter-layer operator scheduling has been shown to reduce off-chip memory transactions by reusing DNN operator outputs on the on-chip memory space. However, it often deeply traverses operators and substantially increases off-chip memory data traffic when the on-chip memory of an NPU decreases in size. Consequently, this work creates a DNN model compilation framework called EDA that transparently improves the energy efficiency of an NPU by adjusting the operator traversal depth of the inter-layer operator scheduling in a stacked DNN model. First, EDA transforms a DNN model into a tensor-splitting model. Second, EDA breaks the tensor-splitting model graph into multiple subgraphs. Third, the EDA inter-layer cost model quickly determines the depth of each subgraph. Fourth, EDA properly manages the on-chip shared memory space of an NPU to avoid overusing memory space. Finally, EDA devises the operator grouping method to improve the MAC unit and on-chip memory space utilization. EDA improves the geometric means of $2.08 \\times$ and $2.39 \\times$, respectively, in energy efficiency and performance over the NPU with designated memory buffers.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00050"}, {"primary_key": "165381", "vector": [], "sparse_vector": [], "title": "FHENDI: A Near-DRAM Accelerator for Compiler-Generated Fully Homomorphic Encryption Applications.", "authors": ["Yongmo Park", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Alper Buyuktosunoglu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Wei <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Fully homomorphic encryption (FHE) is a powerful cryptographic technique that enables computation on encrypted data without needing to decrypt it. It has broad applications in scenarios where sensitive data needs to be processed in the cloud or in other untrusted environments. FHE applications are both compute- and memory-intensive, owing to expensive operations on large data. While prior works address the challenges of efficient compute using dedicated hardware, expensive memory transfers still remain a major limiting factor. In this work, we propose a hierarchical near-DRAM processing (NDP) solution for FHE applications, called FHENDI, that harnesses the massive DRAM bank bandwidth. We observe various data access patterns in FHE that reveal distinct levels of parallelism: element-wise, limb-wise, coefficient-wise, and ciphertext-wise. FHENDI exploits these levels of parallelism to map FHE operations and data onto different hierarchies of our design, while addressing three major challenges with NDP for FHE: (i) the lack of bank-to-bank communication support, (ii) limited die-to-die bandwidth, and (iii) large memory access latencies. We resolve the first problem through a novel, conflict-free mapping algorithm built atop localized permutation networks that enables efficient element-wise and butterfly operations in FHE. The second problem is addressed by pipelining the execution of parallel bootstrap operations observed in compiled FHE workloads. Finally, we hide the memory access latency behind computation latency by exploiting a dual-banking scheme and subarray-level parallelism (SLP) of the DRAM banks. We evaluate FHENDI using representative workloads in the domains of privacy-preserving machine learning inference on CNNs and Transformers, database range query, and sorting, that are obtained using a compiler framework called HElayers. We compare FHENDI with a server-class CPU and GPU running the state-of-the-art HEaaN library, and an FHE accelerator ASIC, and report mean speedups of $2145.8 \\times, 118.29 \\times$, and $2.45 \\times$, respectively.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00087"}, {"primary_key": "165382", "vector": [], "sparse_vector": [], "title": "FIGLUT: An Energy-Efficient Accelerator Design for FP-INT GEMM Using Look-Up Tables.", "authors": ["Gunho Park", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Baeseong Park", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Weight-only quantization has emerged as a promising solution to the deployment challenges of large language models (LLMs). However, it necessitates FP-INT operations, which make implementation on general-purpose hardware like GPUs difficult. In this paper, we propose FIGLUT, an efficient look-up table (LUT)-based GEMM accelerator architecture. Instead of performing traditional arithmetic operations, FIGLUT retrieves precomputed values from an LUT based on weight patterns, significantly reducing the computational complexity. We also introduce a novel LUT design that addresses the limitations of conventional memory architectures. To further improve LUT-based operations, we propose a half-size LUT combined with a dedicated decoding and multiplexing unit. FIGLUT efficiently supports different bit precisions and quantization methods using a single fixed hardware configuration. For the same 3-bit weight precision, FIGLUT demonstrates 59% higher TOPS/W and 20% lower perplexity than state-of-the-art accelerator design. When targeting the same perplexity, FIGLUT achieves $98 \\%$ higher TOPS/W by performing 2.4-bit operations.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00085"}, {"primary_key": "165383", "vector": [], "sparse_vector": [], "title": "AutoRFM: Scaling Low-Cost in-DRAM Trackers to Ultra-Low Rowhammer Thresholds.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "In-DRAM Rowhammer mitigation has the potential to solve the Rowhammer problem without relying on other parts of the system. In-DRAM mitigation requires space (to identify the aggressor rows) and time (to perform the victim refresh). To reduce the storage overheads of tracking, recent works have developed secure low-cost in-DRAM trackers that can probabilistically identify aggressor rows. To obtain the time required for mitigation, these trackers rely on the Refresh Management (RFM) command introduced in DDR5. As RFM stalls the bank for a latency of 200ns-400ns, frequent use of RFM can cause significant slowdowns. For example, scaling the recent MINT tracker to a threshold of 100 incurs 33% slowdown. The goal of this paper is to enable low-cost trackers to tolerate ultralow thresholds (sub-100) while incurring negligible slowdown. This paper proposes AutoRFM, a transparent RFM mechanism that can provide mitigation time to the DRAM chips without stalling the bank. The key insight in AutoRFM is to leverage the subarray structure (e.g. each bank contains 256 subarrays) and perform mitigation on only one of the subarrays. Operations to all subarrays that are not under mitigation are serviced without any interruption. If activation occurs to the subarray under mitigation, the DRAM chip sends an ALERT signal informing the Memory Controller to retry after a predefined time. As AutoRFM works best if consecutive requests to the same bank do not get mapped to the same subarray, we use Randomized Memory Mapping to break the spatial correlation between memory accesses. Furthermore, we also develop a Fractal Mitigation Algorithm that can tolerate transitive attacks (such as Half-Double) without requiring recursive mitigations to the same subarray. Our design ensures that a declined request does not have to wait more than 200 ns before retrying, thus limiting the slowdown and avoiding any potential for denial of service. Our evaluations, with SPEC, GAP, and stream workloads, show that AutoRFM enables low-cost trackers to tolerate a threshold of as low as 74 while incurring an average slowdown of only 3.1%.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00078"}, {"primary_key": "165384", "vector": [], "sparse_vector": [], "title": "SPARK: Sparsity Aware, Low Area, Energy-Efficient, Near-memory Architecture for Accelerating Linear Programming Problems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Integer Linear Programming (ILP) is an important mathematical approach for solving time-sensitive real-life optimization problems, including network routing, map routing, traffic scheduling, etc. However, the algorithms for solving ILPs are typically sparse and branch-intensive, and not CPU/GPU friendly. In the paper “What could a million cores do to solve Integer programs”, <PERSON> et al. [40] presented data illustrating that Integer Linear Programming (ILP) applications take tens of hours of execution time even on the largest parallel computers. Long execution time is a problem because many real-life applications need a decision in seconds or minutes. The widely used ILP solvers, like Gurobi (optimized for CPUs), perform software-based optimizations to handle the inherent sparsity in ILPs but still do not meet decision threshold because of the limited throughput of CPUs. GPUs are suited for large-sized dot-product compute, however, GPU-based ILP solvers also do not meet decision thresholds as (i) GPU is not sparsity friendly and (ii) GPU incurs thread divergence for branching, resulting in under-utilization of streaming engines and periodic host-GPU interaction. We propose SPARK, a sparsity-aware, reuse-aware, energy-efficient, reconfigurable, near-cache ILP architecture that (i) re-configures the existing L1 cache present in CPUs to perform near-cache acceleration with easy integration into the baseline CPU pipeline with minimal area overhead ($\\sim 1.4 \\%$ of a CPU), (ii) performs near-cache sparsity detection and sparsity-aware compute, reducing the number of insignificant computations, and data movement energy overheads, (iii) leverages the computational patterns present in algorithms used for solving ILP to realize a reuse-aware architecture, and (iv) is applicable to solving sparse and dense ILPs and LPs (Linear Programs). We observe $15 x / 20 x$, and $152 x / 740 x$ performance/energy improvement over AMD’s Zen3 CPU, and Nvidia’s Tesla v100 GPU for sparse reallife ILPs in Mixed Integer Programming library (MIPLIB 2017). For sparse LPs (non-integer), SPARK achieves 7-17x/103-250x performance/energy improvement over CPU/ GPU indicating SPARK’s broad applicability.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00019"}, {"primary_key": "165385", "vector": [], "sparse_vector": [], "title": "Architecting Value Prediction around In-Order Execution.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In the search for performance, in-order execution cannot expect to prevail as older long latency instructions prevent younger ones from issuing. Although stall-on-use processors allow independent instructions to issue in the shadow of a cache miss, the compiler cannot always find enough independent work to keep pipeline resources busy. In this paper, we study how both value prediction based on address prediction and direct value prediction can be built into an in-order pipeline to unlock significant performance. We further show that the in-order execution property provides advantages in that the pipeline may speculate aggressively without suffering from any recovery penalty. Finally, we combine this data speculation infrastructure with a reworked cache hierarchy that relies on a fast first level cache that can be written speculatively. We show that such an in-order pipeline can reach a performance level that is comparable to an equally - although moderately - wide out-of-order processor, without requiring support for partial out-of-order execution such as out-of-order memory hazard handling or full-fledged register renaming. Overall, we increase the performance of a 32 -entry scoreboard, 4-issue in-order processor based on a scaled up Open Hardware Group CVA6 by 38.4% (geomean), achieving $\\mathbf{8 6. 7 \\%}$ and $\\mathbf{4 6. 3 \\%}$ of the gains brought by comparable out-of-order processors featuring 32/16-entry and 64/32-entry Reorder Buffer and scheduler, respectively.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00017"}, {"primary_key": "165386", "vector": [], "sparse_vector": [], "title": "Machine Learning-Guided Memory Optimization for DLRM Inference on Tiered Memory.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Deep learning recommendation models (DLRMs) are widely used in industry, and their memory capacity requirements reach the terabyte scale. Tiered memory architectures provide a cost-effective solution but introduce challenges in embedding-vector placement due to complex embedding-access patterns. We propose RecMG, a machine learning (ML)-guided system for vector caching and prefetching on tiered memory. RecMG accurately predicts accesses to embedding vectors with long reuse distances or few reuses. The design of RecMG focuses on making ML feasible in the context of DLRM inference by addressing unique challenges in data labeling and navigating the search space for embedding-vector placement. By employing separate ML models for caching and prefetching, plus a novel differentiable loss function, RecMG narrows the prefetching search space and minimizes on-demand fetches. Compared to state-of-the-art temporal, spatial, and ML-based prefetchers, RecMG reduces on-demand fetches by $2.2 \\times, 2.8 \\times$, and $1.5 \\times$, respectively. In industrial-scale DLRM inference scenarios, RecMG effectively reduces end-to-end DLRM inference time by up to 43%.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00121"}, {"primary_key": "165387", "vector": [], "sparse_vector": [], "title": "HILP: Accounting for Workload-Level Parallelism in System-on-Chip Design Space Exploration.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "High-performance System-on-Chip (SoC) architectures are becoming increasingly complex and heterogeneous, and the days when a single application could utilize all of an SoC’s hardware resources are all but over. The SoC’s workload, i.e., the set of independent applications that the SoC typically executes, therefore has a significant impact on its efficiency. Accounting for Workload-Level Parallelism (WLP) in early-stage design space exploration is thus critical as later-stage analysis steps must focus on favorable design points to yield optimal results. Unfortunately, state-of-the-art MultiAmdahl and Gables fall short because they only model the extremes of minimal and maximal WLP. We hence propose HILP, the first early-stage design space exploration approach for heterogeneous SoCs that fully accounts for WLP. Our key observation is that scheduling a workload of independent multi-phase applications on a heterogeneous SoC is an instance of the classic job-shop scheduling optimization problem and thus can be solved using integer linear programming. HILP therefore uses a high-performance integer linear programming solver to find a near-optimal schedule that minimizes the overall execution time of the workload, i.e., it schedules the dependent phases of all applications in the workload on the cores and accelerators of the target SoC to maximize performance while respecting power consumption and memory bandwidth constraints. We validate HILP by demonstrating that it captures the performance effects of <PERSON><PERSON><PERSON>’s law, the memory wall, and dark silicon, and then use it to explore the impact of WLP across a large SoC design space, yielding multiple insights. The key takeaway is that modeling WLP is necessary to ensure that more detailed, later-stage design tasks focus on the most favorable parts of the vast design space of heterogeneous SoCs.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00097"}, {"primary_key": "165388", "vector": [], "sparse_vector": [], "title": "FACIL: Flexible DRAM Address Mapping for SoC-PIM Cooperative On-device LLM Inference.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yeonhong Park", "<PERSON><PERSON>"], "summary": "The rise of on-device inference of large language models (LLMs) is rapidly escalating the demand for memory-intensive operations on edge devices. While DRAMbased processing-in-memory (PIM) is a promising solution for overcoming the memory wall, edge devices require PIM to function both as a compute unit and a memory device due to their limited memory capacity. Such PIM-enabled memory complicates the partition and placement of a tensor into DRAM banks in a PIM-operable manner. Notably, we highlight that LLM weights need to be accessible by both PIM and system-on-chip (SoC) processors, as the same weights are used for both SoC-favorable GEMM and PIM-favorable GEMV operations. This necessitates different memory mappings for PIM and SoC processors, leading to potential re-layout costs when switching between the two. To address this challenge, we propose FACIL, a flexible DRAM address mapping solution that efficiently places tensors in DRAM for PIM operations while allowing SoC processors to access the same data using contiguous virtual addresses. FACIL consists of (i) a memory controller that assigns different DRAM address mapping to the page offset bits of each huge page and (ii) a user-level library that determines the appropriate DRAM address mapping. We demonstrate that enabling re-layout-free access of both PIM and SoC processor benefits LLM inference on various on-device LLM tasks, including short conversation and code autocompletion, reducing the time-to-first-token by $2.37 \\times$ and $2.63 \\times$, respectively, over the SoC-PIM baseline.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00127"}, {"primary_key": "165389", "vector": [], "sparse_vector": [], "title": "Delinquent Loop Pre-execution Using Predicated Helper Threads.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Branch pre-execution targets delinquent branches that are not predictable by conventional branch predictors. Helper threads attempt to resolve branches ahead of the main thread. Pre-executed branch outcomes are communicated to the main thread’s fetch unit via a global branch queue or local branch queues (one per branch PC). Two key challenges are discussed in this paper. 1) Handling a delinquent branch b2 that is control-dependent on another delinquent branch b1. Prior works that include both branches resort to branch prediction of b1 in the helper thread to determine whether or not to pre-execute b2. But b1 is hard-to-predict and the misprediction bottleneck merely shifts from the main thread to the helper thread. 2) Handling a store instruction that both influences a delinquent branch and is control-dependent on it. We propose predicated helper threads (<PERSON>) to address these challenges. <PERSON> constructs a helper thread for each inner loop containing delinquent branches. All delinquent branches, even control-dependent ones (b2), are unconditionally pre-executed in each loop iteration. Per-branch queues are managed in lockstep based on loop iterations, allowing the helper thread to deposit outcomes for both b1 and b2 each iteration and the main thread to consume or ignore b2 outcomes in the correct sequence dictated by b1. The helper thread also retains influential stores for dynamic disambiguation and store-load forwarding. Any such store that is control-dependent on a delinquent branch is predicated on the branch’s outcome, which is necessary because the helper thread no longer has control-flow (except for the loop branch). <PERSON> also features dual decoupled helper threads for outer-inner loop pairs, for effective branch pre-execution when the inner loop has a short and unpredictable trip count.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00015"}, {"primary_key": "165390", "vector": [], "sparse_vector": [], "title": "Piccolo: Large-Scale Graph Processing with Fine-Grained in-Memory Scatter-Gather.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Graph processing requires irregular, fine-grained random access patterns incompatible with contemporary off-chip memory architecture, leading to inefficient data access. This inefficiency makes graph processing an extremely memory-bound application. Because of this, existing graph processing accelerators typically employ a graph tiling-based or processing-in-memory (PIM) approach to relieve the memory bottleneck. In the tiling-based approach, a graph is split into chunks that fit within the on-chip cache to maximize data reuse. In the PIM approach, arithmetic units are placed within memory to perform operations such as reduction or atomic addition. However, both approaches have several limitations, especially when implemented on current memory standards (i.e., DDR). Because the access granularity provided by DDR is much larger than that of the graph vertex property data, much of the bandwidth and cache capacity are wasted. PIM is meant to alleviate such issues, but it is difficult to use in conjunction with the tiling-based approach, resulting in a significant disadvantage. Furthermore, placing arithmetic units inside a memory chip is expensive, thereby supporting multiple types of operation is thought to be impractical. To address the above limitations, we present <PERSON><PERSON><PERSON>, an end-to-end efficient graph processing accelerator with fine-grained in-memory random scatter-gather. Instead of placing expensive arithmetic units in off-chip memory, <PERSON><PERSON><PERSON> focuses on reducing the off-chip traffic with non-arithmetic function-in-memory of random scatter-gather. To fully benefit from in-memory scatter-gather, <PERSON><PERSON><PERSON> redesigns the cache and miss-handling architecture (MHA) of the accelerator such that it can enjoy both the advantage of tiling and in-memory operations. <PERSON><PERSON><PERSON> achieves a maximum speedup of 3.28 × and a geometric mean speedup of 1.62 ×, along with up to 59.7% reduction in energy consumption across various and extensive benchmarks.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00055"}, {"primary_key": "165391", "vector": [], "sparse_vector": [], "title": "PIMnet: A Domain-Specific Network for Efficient Collective Communication in Scalable PIM.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Xiangyu Wu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Processing-in-memory (PIM), where compute is moved closer to memory or data, has been explored to accelerate emerging workloads. Different PIM-based systems have been announced, each offering a unique microarchitectural organization of their compute units, ranging from fixed functional units to programmable general-purpose compute cores near memory. However, one fundamental limitation of PIM is that each compute unit can only access its local memory; access to “remote” memory must occur through the host CPU - potentially limiting application performance scalability. In this work, we first characterize the scalability of real PIM architectures using the UPMEM PIM system. We analyze how the overhead of communicating through the host (instead of providing direct communication between the PIM compute units) can become a bottleneck for collective communications that are commonly used in many workloads. To overcome this inter-PIM bank communication, we propose PIMnet - a PIM interconnection network for PIM banks that provides direct connectivity between compute units and removes the overhead of communicating through the host. PIMnet exploits bandwidth parallelism where communication across the different PIM bank/chips can occur in parallel to maximize communication performance. PIMnet also matches the DRAM packaging hierarchy with a multi-tier network architecture. Unlike traditional interconnection networks, PIMnet is a PIMcontrolled network where communication is managed by the PIM logic, optimizing collective communications and minimizing the hardware overhead of PIMnet. Our evaluation of PIMnet shows that it provides up to $85 \\times$ speedup on collective communications and achieves a $11.8 \\times$ improvement on real applications compared to the baseline PIM.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00116"}, {"primary_key": "165392", "vector": [], "sparse_vector": [], "title": "Concord: Rethinking Distributed Coherence for Software Caches in Serverless Environments.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Costly accesses to global storage substantially limit the performance of serverless functions. To mitigate this overhead, data can be cached in the memory of the nodes where functions are executed. Existing caching schemes either (1) restrict a data item to be cached in a single node, causing frequent remote reads or (2) allow a data item to be cached in multiple nodes concurrently, adding substantial overhead to maintain cache coherence. Unfortunately, current approaches are suboptimal for the access patterns present in serverless workloads, which are characterized by frequent reads to small data items, strong temporal locality, and a small number of nodes that concurrently execute functions of the same application. Driven by these insights, we propose Concord, a distributed software caching system tailored to serverless environments. Concord allows multiple copies of the same data item to be cached in different nodes concurrently, allowing each cache to satisfy local reads. To maintain coherence across software caches, <PERSON> proposes a directory-based distributed coherence protocol. The protocol is inspired by hardware cache coherence, and is enhanced to minimize coherence traffic, reduce contention points, and be robust to node failures and frequent coherence domain changes. Further, with the Concord coherence protocol, we unlock two new capabilities in serverless environments: transactional storage accesses and transparent data-aware function placement. Compared to state-of-the-art severless caching schemes, Concord running on a 16-node cluster speeds-up execution by 2.4 × and improves throughput by 1.7 ×, while using only 6.2MB of otherwise idle application memory (i.e., 4.8% of the total application memory).", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00043"}, {"primary_key": "165393", "vector": [], "sparse_vector": [], "title": "DynamoLLM: Designing LLM Inference Clusters for Performance and Energy Efficiency.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The rapid evolution and widespread adoption of generative large language models (LLMs) have made them a pivotal workload in various applications. Today, LLM inference clusters receive a large number of queries with strict Service Level Objectives (SLOs). To achieve the desired performance, these models execute on power-hungry GPUs, causing inference clusters to consume large amounts of energy and, consequently, result in substantial carbon emissions. Fortunately, we find that there is an opportunity to improve energy efficiency by exploiting the heterogeneity in inference compute properties and the fluctuations in inference workloads. However, the diversity and dynamicity of these environments create a large search space, where different system configurations (e.g., number of instances, model parallelism, and GPU frequency) translate into different energy-performance trade-offs. To address these challenges, we propose DynamoLLM, the first energy-management framework for LLM inference environments. DynamoLLM automatically and dynamically reconfigures the inference cluster to optimize for energy of LLM serving under the services’ performance SLOs. We show that at a service level, on average, DynamoLLM conserves 52% of the energy and 38% of the operational carbon emissions, and reduces the cost to the customer by 61%, while meeting the latency SLOs.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00102"}, {"primary_key": "165394", "vector": [], "sparse_vector": [], "title": "Lincoln: Real-Time 50~100B LLM Inference on Consumer Devices with LPDDR-Interfaced, Compute-Enabled Flash Memory.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the widespread use of large language models (LLMs), and with the privacy and cost concerns on cloud-based services, vendors are now pushing LLM inference to consumer devices. However, current attempts only enable real-time inference of low-quality small-sized LLMs. Large-sized LLMs have to load most of their weights from Flash storage for every execution iteration, which dominates the execution time of both the prefill and the generation phase. This performance bottleneck is attributed to both the low internal Flash memory bandwidth and the low transmission bandwidth between Flash and the Neural Processing Unit (NPU). To tackle these two challenges, we present Lincoln, a device-architecture co-design solution with LPDDR-interfaced, Compute-Enabled Flash Memory. On the device level, we boost the Flash internal bandwidth by improving upon existing array shrinking methods, to enable lower read latency and more parallel Flash planes within each Flash die. We specifically leverage 3D hybrid bonding, which is already adopted in consumer Flash products, to maintain high area efficiency and low density loss. On the architecture level, to leverage such increased internal bandwidth for resolving the transmission bottleneck, we propose two solutions for the two distinct phases of LLMs. For the compute-intensive prefill phase, we let Flash devices use the existing high-speed LPDDR interface (originally for DRAM), which offers much higher transmission bandwidth to the NPU than the conventional Flash interface, while maintaining good cost and area efficiency. For the memory-intensive generation phase, we rely on hybrid-bonding-based near-Flash computing to fully utilize the internal Flash bandwidth, and further equip with speculative decoding to eventually reach the real-time latency goal. Our evaluation shows that Lincoln enables real-time inference, with up to $13.23 \\times$ and $254.1 \\times$ speedups for LLM prefill and generation phases over conventional SSD-based systems.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00128"}, {"primary_key": "165395", "vector": [], "sparse_vector": [], "title": "IRIS: Unleashing ISP-Software Cooperation to Optimize the Machine Vision Pipeline.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Contemporary Continuous Vision (CV) systems, crucial for robotics, Autonomous Driving (AD), and Extended Reality (XR), have challenging requirements in terms of performance and energy consumption. A standard CV System-on-a-Chip (SoC) pipeline includes an image capture frontend and a backend executing vision algorithms. The frontend typically samples the entire scene at a uniform high resolution (e.g., 4K). However, since not all image regions require this level of detail, this approach over-samples other less detailed areas that could be processed more efficiently at lower resolutions (e.g., 1080p). To address this inefficiency, we introduce IRIS (Image Region ISP-Software Cooperation). Our approach empowers the CV frontend to dynamically adjust the spatial resolution independently for each image region based on its content and backend application requirements. We augment the frontend’s Image Signal Processor (ISP) to adaptively downsample image regions using a Quadtree-like spatial division scheme guided by a low-overhead saliency scorer. The saliency scorer repurposes two valuable byproducts of the ISP-region edge density and perceived motion (optical flow)—that current ISPs already use to enhance image quality but discard afterward. IRIS saves memory bandwidth for image transmission and enables applications to process each image region at the most effective resolution, reducing latency and energy consumption. We validate our approach with both a state-of-the-art CV localization application and a standard Vision Transformer for image classification. IRIS significantly reduces tail latency, average latency, and energy consumption of modern mobile CV systems.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00028"}, {"primary_key": "165396", "vector": [], "sparse_vector": [], "title": "MLPerf Power: Benchmarking the Energy Efficiency of Machine Learning Systems from μWatts to MWatts for Sustainable AI.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Ritika Borkar", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Hiwot Kassa", "<PERSON>", "<PERSON><PERSON>", "Yuechao Pan", "<PERSON><PERSON>", "<PERSON>", "Tom <PERSON>. John", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Rapid adoption of machine learning (ML) technologies has led to a surge in power consumption across diverse systems, from tiny IoT devices to massive datacenter clusters. Benchmarking the energy efficiency of these systems is crucial for optimization, but presents novel challenges due to the variety of hardware platforms, workload characteristics, and system-level interactions. This paper introduces MLPerf® Power, a comprehensive benchmarking methodology with capabilities to evaluate the energy efficiency of ML systems at power levels ranging from microwatts to megawatts. Developed by a consortium of industry professionals from more than 20 organizations, coupled with insights from academia, MLPerf Power establishes rules and best practices to ensure comparability across diverse architectures. We use representative workloads from the MLPerf benchmark suite to collect $\\mathbf{1, 8 4 1}$ reproducible measurements from 60 systems across the entire range of ML deployment scales. Our analysis reveals trade-offs between performance, complexity, and energy efficiency across this wide range of systems, providing actionable insights for designing optimized ML solutions from the smallest edge devices to the largest cloud infrastructures. This work emphasizes the importance of energy efficiency as a key metric in the evaluation and comparison of the ML system, laying the foundation for future research in this critical area. We discuss the implications for developing sustainable AI solutions and standardizing energy efficiency benchmarking for ML systems.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00092"}, {"primary_key": "165397", "vector": [], "sparse_vector": [], "title": "Understanding RowHammer Under Reduced Refresh Latency: Experimental Analysis of Real DRAM Chips and Implications on Future Solutions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Ataberk Olgun", "Oguzhan Canpolat", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Read disturbance in modern DRAM chips is a widespread weakness that is used for breaking memory isolation, one of the fundamental building blocks of system security and privacy. RowHammer is a prime example of read disturbance in DRAM where repeatedly accessing (hammering) a row of DRAM cells (DRAM row) induces bitflips in physically nearby DRAM rows (victim rows). Unfortunately, shrinking technology node size exacerbates RowHammer and as such, significantly fewer accesses can induce bitflips in newer DRAM chip generations. To ensure robust DRAM operation, state-of-the-art mitigation mechanisms restore the charge in potential victim rows (i.e., they perform preventive refresh or charge restoration). With newer DRAM chip generations, these mechanisms perform preventive refresh more aggressively and cause larger performance, energy, or area overheads. Therefore, it is essential to develop a better understanding and in-depth insights into the preventive refresh to secure real DRAM chips at low cost. In this paper, our goal is to mitigate RowHammer at low cost by understanding the preventive refresh latency and the impact of reduced refresh latency on RowHammer. To this end, we present the first rigorous experimental study on the interactions between refresh latency and RowHammer characteristics in real DRAM chips. Our experimental characterization using 388 real DDR4 DRAM chips from three major manufacturers demonstrates that a preventive refresh latency can be significantly reduced (by 64%) at the expense of requiring slightly more (by 0.54%) preventive refreshes. To investigate the impact of reduced preventive refresh latency on system performance and energy efficiency, we reduce the preventive refresh latency and adjust the aggressiveness of existing RowHammer solutions by developing a new mechanism, Partial Charge Restoration for Aggressive Mitigation (PaCRAM). Our results show that by reducing the preventive refresh latency, PaCRAM reduces the performance and energy overheads induced by five state-of-the-art RowHammer mitigation mechanisms with small additional area overhead. Thus, PaCRAM introduces a novel perspective into addressing RowHammer vulnerability at low cost by leveraging our experimental observations. To aid future research, we open-source our PaCRAM implementation at https://github.com/CMU-SAFARI/PaCRAM.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00070"}, {"primary_key": "165398", "vector": [], "sparse_vector": [], "title": "To Cross, or Not to Cross Pages for Prefetching?", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Despite processor vendors reporting that cache prefetchers operating with virtual addresses are permitted to cross page boundaries, academia is focused on optimizing cache prefetching for patterns within page boundaries. This work reveals that page-cross prefetching at the first-level data cache (L1D) is seldom beneficial across different execution phases and workloads while showing that state-of-the-art L1D prefetchers are not very accurate at prefetching across page boundaries. In response, we propose $M O K A$, a holistic framework for designing Page-Cross Filters, i.e., microarchitectural schemes that ensure effective and accurate prefetching across page boundaries. MOKA combines (i) hashed perceptron predictors that use prefetcher-independent program features, (ii) predictors that adapt decisions based on the system state (e.g., TLB pressure), and (iii) a scheme to dynamically optimize predictions across different execution phases and workload types. We use the MOKA framework to prototype a Page-Cross Filter, named DRIPPER, for three relevant L1D prefetchers (<PERSON><PERSON> [60], <PERSON><PERSON> [61], BOP [57]). We show that DRIPPER accurately enables pagecross prefetching only when it is beneficial for performance. For instance, <PERSON><PERSON> [60] (state-of-the-art prefetcher) combined with DRIPPER improves single-core geomean performance over <PERSON><PERSON> that always permits page-cross prefetches and <PERSON><PERSON> that always discards page-cross prefetches by $\\mathbf{1 . 7 \\%}(\\mathbf{1 . 2 \\%})$ and $\\mathbf{2 . 5 \\%}(\\mathbf{2 . 1 \\%})$ across 218 seen (178 unseen) workloads, respectively. Across 300 8 -core mixes, the corresponding geomean speedups are $2.0 \\%$ and $3.3 \\%$. Finally, we show that DRIPPER provides consistent benefits when both 4KB pages and 2MB large pages are used.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00025"}, {"primary_key": "165399", "vector": [], "sparse_vector": [], "title": "Interleaved Logical Qubits in Atom Arrays.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Neutral atom arrays have seen exciting progress as a platform for quantum computation. However, as we move towards the regime of fault-tolerance, the large-scale impact of fundamental features in these systems is not well-studied. In this work we point out that the use of movement in neutral atom arrays may set an unavoidable constraint on the speed of computation, erasing potential quantum advantage. As one solution, we propose a movement-free QEC architecture based on groups of interleaved surface codes. Our architecture enables fast, high-fidelity transversal CNOTs on surface codes in the same group. We also introduce interleaved lattice surgery to create high-capacity routing channels between groups. We validate our architecture through detailed numerical simulations of the underlying circuits and we evaluate its scalability through compilation of key benchmark applications. In regimes of high parallelism, we find our architecture leads to a $\\sim 3 \\times$ reduction in compute time. Our architecture leverages experimentally demonstrated dualspecies atom arrays which exhibit asymmetric interaction strengths that scale with $1 / r^{3}$ for interspecies interactions and with $1 / r^{6}$ for standard, intraspecies interactions. We examine how such scalings enable interleaving with high fidelity and propose how error rates required for QEC could be achieved. We also evaluate the tolerance of our architecture to two-qubit gate fidelities. We find the advantage of interleaving admits sizable tolerances of $\\sim 1 \\times$ to $3 \\times$ increase in error rates. We conclude the benefits of our proposed interleaved architecture grants strong motivation for future experimental efforts targeting longer range dual-species gates.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00030"}, {"primary_key": "165400", "vector": [], "sparse_vector": [], "title": "Enterprise Class Modular Cache Hierarchy.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Alper Buyuktosunoglu"], "summary": "The IBM Z® platform is optimized for processing vast amounts of data and transactions with low latency in a highly virtualized and secured environment. The platform and its microprocessor chip are designed to deliver consistent system performance, throughput, and response latencies with sustained processor utilization of over $\\mathbf{9 0 \\%}$ under all workload conditions. The IBM Telum® and IBM Telum® ${ }^{\\circledR}$ II designs introduced a novel modular and scalable cache hierarchy to the industry that is extendable to future platform generations, the adoption of emerging technologies, and new architecture enhancements, all of which are required to meet the continuously evolving needs of the mission critical workloads that run on the platform. This paper demonstrates the flow of processor fetch events and how the adaptive horizontal cache persistence algorithms work in this novel design. It explores the performance and system effects of cache size changes in this architecture. Finally, the paper explores how the resulting application of solutions leveraging these effects enhances the robustness of the next generation IBM Z caching solution embedded in the IBM Telum® II Processor, which improves mission critical workload performance while simultaneously enabling the generative AI capability.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00093"}, {"primary_key": "165401", "vector": [], "sparse_vector": [], "title": "CogSys: Efficient and Scalable Neurosymbolic Cognition System via Algorithm-Hardware Co-Design.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Neurosymbolic AI is an emerging compositional paradigm that fuses neural learning with symbolic reasoning to enhance the transparency, interpretability, and trustworthiness of AI. It also exhibits higher data efficiency making it promising for edge deployments. Despite the algorithmic promises and demonstrations, unfortunately executing neurosymbolic workloads on current hardware (CPU/GPU/TPU) is challenging due to higher memory intensity, greater compute heterogeneity and access pattern irregularity, leading to severe hardware underutilization. This work proposes CogSys, a characterization and co-design framework dedicated to neurosymbolic AI system acceleration, aiming to win both reasoning efficiency and scalability. On the algorithm side, CogSys proposes an efficient factorization technique to alleviate compute and memory overhead. On the hardware side, CogSys proposes a scalable neurosymbolic architecture with reconfigurable neuro/symbolic processing elements ($n s P E$) and bubble streaming (BS) dataflow with spatial-temporal (ST) mapping for highly parallel and efficient neurosymbolic computation. On the system side, CogSys features an adaptive workload-aware scheduler (adSCH) to orchestrate heterogeneous kernels and enhance resource utilization. Evaluated across cognitive workloads, CogSys enables reconfigurable support for neural and symbolic kernels and exhibits $\\gt75 \\times$ speedup over TPU-like systolic array with only $\\lt5 \\%$ area overhead, as benchmarked under the TSMC 28nm technology node. CogSys achieves $4 \\times$ $96 \\times$ speedup compared to desktop and edge GPUs. For the first time, CogSys enables real-time abduction reasoning towards human fluid intelligence, requiring only 0.3 s per reasoning task with $4 \\mathrm{~mm}^{2}$ area and 1.48 W power consumption.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00064"}, {"primary_key": "165402", "vector": [], "sparse_vector": [], "title": "Reviving In-Storage Hardware Compression on ZNS SSDs through Host-SSD Collaboration.", "authors": ["<PERSON><PERSON><PERSON>", "Tao Lu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Zoned Namespace (ZNS) is an emerging SSD interface with great potential for performance and cost in largescale cloud SSD deployments. Enabling in-storage hardware compression on ZNS SSDs is promising for further enhancing the cost-effectiveness of ZNS-based storage infrastructures. However, based on our investigation, existing solutions based on the host-transparent methodology are sub-optimal on ZNS SSDs due to two intrinsic challenges: (1) locating compressed chunks and (2) harvesting space savings from compression.In this paper, we for the first time revisit the compression storage system architecture on the emerging ZNS SSD and propose to decouple compression execution with indexing. We propose CCZNS (CC: collaborative compression), an advanced ZNS interface that revives in-storage hardware compression on ZNS SSDs through a novel host-SSD collaborative approach. We present how CCZNS can benefit host software by performing a case study on RocksDB and ZenFS. Extensive experiments demonstrate that the CCZNS-based storage system significantly outperforms existing system solutions.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00053"}, {"primary_key": "165403", "vector": [], "sparse_vector": [], "title": "LAD: Efficient Accelerator for Generative Inference of LLM with Locality Aware Decoding.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Large Language Models (LLMs) have emerged as the cornerstone of content generation applications due to their ability to capture relations between newly generated token and the full preceding context. However, this ability stems from the attention mechanism for decoding that retains the entire generation history as key value cache (KV cache). As the generated sequence lengthens, the KV cache expands, causing a substantial memory access bottleneck. In advanced LLM generation systems running on GPUs, the attention mechanism for decoding accounts for more than 50% of the total inference time when the KV cache length reaches 4096. To address this issue, this paper introduces LAD (Locality Aware Decoding), an LLM generation accelerator with algorithm-hardware enhancements that significantly decrease KV cache access, resulting in considerable speedups and energy savings. A key insight underlying LAD is that when the attention score for a specific position remains fixed over the next several decoding steps, it is unnecessary to repeatedly retrieve the associated key and value at each step to reproduce the computation. Our analysis reveals that numerous positions exhibit notable numerical locality in attention scores through multiple decoding steps. Leveraging these insights, we have designed an innovative attention decoding computation method that decreases the frequency of accessing the key and value for positions demonstrating good locality, all while maintaining decoding accuracy. Extensive experiments show that LAD generates sequences with an average ROUGE-1 similarity of 97% compared to those generated by the original model. When the length of KV cache exceeds 2048, the high configuration of LAD accelerator achieves on average (geomean) $10.7 \\times$ speedup and $52.4 \\times$ energy efficiency for the attention mechanism compared to the A100 GPU. For end-to-end model inference, it also achieves on average $2.3 \\times$ speedup and $13.4 \\times$ energy efficiency.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00111"}, {"primary_key": "165404", "vector": [], "sparse_vector": [], "title": "OASIS: Object-Aware Page Management for Multi-GPU Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The ever-growing need for high-performance computing has driven the popularity of employing multi-GPU systems. Modern multi-GPU systems employ unified virtual memory (UVM) to manage page placement and migration. However, the page management in UVM is application object agnostic. In this paper, we characterize the page access behaviors in relation to the application objects, and reveal that the beneficial page management policy varies according to (i) the different data objects within the same application, and (ii) the different execution phases of the same object. This motivates the need for dynamic and proactive page management in multi-GPU systems. To this end, we propose OASIS, which dynamically identifies object patterns during the execution and proactively determines the appropriate page management policies for these objects at runtime. Experimental results show that OASIS improves the performance over uniformly adopting on-touch migration, access counter-based migration, and duplication by an average of $\\mathbf{6 4 \\%}$, $\\mathbf{3 5 \\%}$, and $\\mathbf{4 2 \\%}$, respectively. Moreover, OASIS achieves a $\\mathbf{1 2 \\%}$ performance improvement over the state-of-the-art technique (i.e., GRIT) while having significantly lower design complexity.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00124"}, {"primary_key": "165405", "vector": [], "sparse_vector": [], "title": "Prosperity: Accelerating Spiking Neural Networks via Product Sparsity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> (<PERSON>) <PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Spiking Neural Networks (SNNs) are highly efficient due to their spike-based activation, which inherently produces bit-sparse computation patterns. Existing hardware implementations of SNNs leverage this sparsity pattern to avoid wasteful zero-value computations, yet this approach fails to fully capitalize on the potential efficiency of SNNs. This study introduces a novel sparsity paradigm called Product Sparsity, which leverages combinatorial similarities within matrix multiplication operations to reuse the inner product result and reduce redundant computations. Product Sparsity significantly enhances sparsity in SNNs without compromising the original computation results compared to traditional bit sparsity methods. For instance, in the SpikeBERT SNN model, Product Sparsity achieves a density of only 1.23% and reduces computation by $11 \\times$, compared to bit sparsity, which has a density of 13.19%. To efficiently implement Product Sparsity, we propose Prosperity, an architecture that addresses the challenges of identifying and eliminating redundant computations in real-time. Compared to prior SNN accelerator PTB and the A100 GPU, Prosperity achieves an average speedup of $7.4 \\times$ and $1.8 \\times$, respectively, along with energy efficiency improvements of $8.0 \\times$ and $193 \\times$, respectively. The code for Prosperity is available at https://github.com/dubcyfor3/Prosperity.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00066"}, {"primary_key": "165406", "vector": [], "sparse_vector": [], "title": "QPRAC: Towards Secure and Practical PRAC-based Rowhammer Mitigation using Priority Queues.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "JEDEC has introduced the Per Row Activation Counting (PRAC) framework for DDR5 and future DRAMs to enable precise counting of DRAM row activations. PRAC enables a holistic mitigation of Rowhammer attacks even at ultra-low Rowhammer thresholds. PRAC uses an Alert Back-Off (ABO) protocol to request the memory controller to issue Rowhammer mitigation requests. However, recent PRAC implementations are either insecure or impractical. For example, Panopticon, the inspiration for PRAC, is rendered insecure if implemented per JEDEC’s PRAC specification. On the other hand, the recent UPRAC proposal is impractical since it needs oracular knowledge of the ‘top- N ‘ activated DRAM rows that require mitigation.This paper provides the first secure, scalable, and practical RowHammer solution using the PRAC framework. The crux of our proposal is the design of a priority-based service queue (PSQ) for mitigations that prioritizes pending mitigations based on activation counts to avoid the security risks of prior solutions. This provides principled security using the reactive ABO protocol. Furthermore, we co-design our PSQ, with opportunistic mitigation on Refresh Management (RFM) operations and proactive mitigation during refresh (REF), to limit the performance impact of ABO-based mitigations. QPRAC provides secure and practical RowHammer mitigation that scales to Rowhammer thresholds as low as 71 while incurring a $0.8 \\%$ slowdown for benign workloads, which further reduces to $0 \\%$ with proactive mitigations.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00080"}, {"primary_key": "165407", "vector": [], "sparse_vector": [], "title": "DAPPER: A Performance-Attack-Resilient Tracker for RowHammer Defense.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "RowHammer vulnerabilities pose a significant threat to modern DRAM-based systems, where rapid activation of DRAM rows can induce bit-flips in neighboring rows. To mitigate this, state-of-the-art host-side RowHammer mitigations typically rely on shared counters or tracking structures. While these optimizations benefit benign applications, they are vulnerable to Performance Attacks (Perf-Attacks), where adversaries exploit shared structures to reduce DRAM bandwidth for co-running benign applications by increasing DRAM accesses for RowHammer counters or triggering repetitive refreshes required for the early reset of structures, significantly degrading performance. In this paper, we propose secure hashing mechanisms to thwart adversarial attempts to capture the mapping of shared structures. We propose DAPPER, a novel low-cost tracker resilient to Perf-Attacks even at ultra-low RowHammer thresholds. We first present a secure hashing template in the form of DAPPER-S. We then develop Dapper-H, an enhanced version of DapperS, incorporating double-hashing, novel reset strategies, and mitigative refresh techniques. Our security analysis demonstrates the effectiveness of DAPPER-H against both RowHammer and Perf-Attacks. Experiments with 57 workloads from SPEC2006, SPEC2017, TPC, Hadoop, MediaBench, and YCSB show that, even at an ultra-low RowHammer threshold of 500, DapperH incurs only a 0.9% slowdown in the presence of Perf-Attacks while using only 96 KB of SRAM per 32GB of DRAM memory.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00079"}, {"primary_key": "165408", "vector": [], "sparse_vector": [], "title": "A Hardware-Software Design Framework for SpMV Acceleration with Flexible Access Pattern Portfolio.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON>"], "summary": "Sparse matrix-vector multiplications (SpMV) are notoriously challenging to accelerate due to their highly irregular data access pattern. Although a fully customized static accelerator design may be adequate for small problems that can fit entirely within an on-chip memory buffer, practical SpMV problems are large and have dynamic matrix structures that cannot easily be optimized at compile time. To address this need for trade-off between flexibility and performance, we present SPASM, a hardware-software framework that accelerates SpMV computation using a customizable portfolio of data access patterns as templates and a reconfigurable hardware to support their run-time execution. SPASM extracts local data access patterns of the input matrices and derives a set of template patterns to encode these inputs. Subsequently, a novel hardware computing structure is proposed to support vectorized computation and flexible switching between different template patterns for each tile computation. Furthermore, SPASM leverages the global compositions of input matrices to derive hardware configuration and workload schedules that improve load balancing among the parallel processing units. Importantly, although SPASM can optimize the pattern portfolio for a particular set of expected input matrices, the generated hardware can flexibly be used to accelerate SpMV of different input patterns albeit with reduced performance. Experimental results show that SPASM can achieve an average $2.81 \\times$ speedup compared to the state-of-the-art SpMV accelerator while keeping a relatively low customization cost.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00068"}, {"primary_key": "165409", "vector": [], "sparse_vector": [], "title": "Exploring the Performance Improvement of Tensor Processing Engines through Transformation in the Bit-weight Dimension of MACs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>ng <PERSON>", "<PERSON><PERSON> Tao", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Xi Jin"], "summary": "General matrix-matrix multiplication (GEMM), serving as a cornerstone of AI computations, has positioned tensor processing engines (TPEs) as increasingly critical components within existing GPUs and domain-specific architectures (DSA). Our analysis identifies that the prevailing architectures primarily focus on dataflow or operand reuse strategies, when considering the combination of matrix multiplication with multiply-accumulator (MAC) itself, it provides greater optimization space for the design of TPEs. This work introduces a novel perspective on matrix multiplication from a hardware standpoint, focusing on the bit-weight dimension of MACs. Through this lens, we propose a finer-grained TPE notation, using matrix triple loops as an example, introducing new methods and ideas for designing and optimizing PE microarchitecture. Based on the new notation and transformations, we propose four optimization techniques that achieve varying degrees of improvement in timing, area, and power consumption. We implement our design in RTL using the SMIC-28nm process. Applying our methods to four classic TPE architectures (include systolic array [20], 3D-Cube [27], multiplier-adder tree [48], and 2D-Matrix [30]), we achieved area efficiency improvements of $1.27 \\times, 1.28 \\times, 1.56 \\times$, and $1.44 \\times$, and $1.04 \\times, 1.56 \\times, 1.49 \\times$, and $1.20 \\times$ for energy efficiency respectively. When applied to a bit-slice architecture, we achieved a $12.10 \\times$ improvement in energy efficiency and $2.85 \\times$ in area efficiency compared to Laconic [38]. Our Verilog HDL code, along with timing, area, and power reports for circuit synthesis in URL: https://github.com/wqzustc/High-Performance-Tensor-Processing-Engines.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00058"}, {"primary_key": "165410", "vector": [], "sparse_vector": [], "title": "HSMU-SpGEMM: Achieving High Shared Memory Utilization for Parallel Sparse General Matrix-Matrix Multiplication on Modern GPUs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Sparse general matrix-matrix multiplication (SpGEMM) is a core primitive for numerous scientific applications. Traditional hash-based approaches fail to strike a balance between reducing hash collisions and efficiently utilizing fast shared memory, which significantly undermines the performance of executing SpGEMM on GPUs. To address this issue, this paper introduces a novel accumulator design that achieves high shared memory utilization on modern GPUs. For the proposed high shared memory utilization algorithm, i.e., HSMU-SpGEMM1, we further optimize different symbolic stages. Our evaluations with four state-of-the-art hash-based SpGEMM libraries (Nsparse, spECK, OpSparse, and NVIDIA’s cuSPARSE) on three NVIDIA GPUs (Ampere, Ada Lovelace, Turing) demonstrate significant performance benefits from HSMU-SpGEMM.1HSMU-SpGEMM is available at https://github.com/wuminqaq/HSMUSpGEMM", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00109"}, {"primary_key": "165411", "vector": [], "sparse_vector": [], "title": "BOSS: Blocking algorithm for optimizing shuttling scheduling in Ion Trap.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Wang", "<PERSON><PERSON>"], "summary": "Ion traps stand at the forefront of quantum hardware technology, presenting unparalleled benefits for quantum computing, such as high-fidelity gates, extensive connectivity, and prolonged coherence times. In this context, we explore the critical role of shuttling operations within these systems, especially their influence on the fidelity loss and elongated execution times. To address these challenges, we have developed BOSS, an efficient blocking algorithm tailored to enhance shuttling efficiency. This optimization not only bolsters the shuttling process but also elevates the overall efficacy of ion trap devices. We experimented on multiple applications using two qubit gates up to 4000+ and qubits ranging from 64 to 78. Our method significantly reduces the number of shuttles on most applications, with a maximum reduction of $96.1 \\%$. Additionally, our investigation includes simulations of realistic experimental parameters that incorporate sympathetic cooling, offering a higher fidelity and a refined estimate of execution times that align more closely with practical scenarios.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00032"}, {"primary_key": "165412", "vector": [], "sparse_vector": [], "title": "Choco-Q: Commute Hamiltonian-based QAOA for Constrained Binary Optimization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Constrained binary optimization aims to find an optimal assignment to minimize or maximize the objective meanwhile satisfying the constraints, which is a representative NP problem in various domains, including transportation, scheduling, and economy. Quantum approximate optimization algorithms (QAOA) provide a promising methodology for solving this problem by exploiting the parallelism of quantum entanglement. However, existing QAOA approaches based on penalty-term or Hamiltonian simulation fail to thoroughly encode the constraints, leading to extremely low success rate and long searching latency.This paper proposes Choco-Q, a formal and universal framework for constrained binary optimization problems, which comprehensively covers all constraints and exhibits high deployability for current quantum devices. The main innovation of Choco-Q is to embed the commute Hamiltonian as the driver Hamiltonian, resulting in a much more general encoding formulation that can deal with arbitrary linear constraints. Leveraging the arithmetic features of commute Hamiltonian, we propose three optimization techniques to squeeze the overall circuit complexity, including Hamiltonian serialization, equivalent decomposition, and variable elimination. The serialization mechanism transforms the original Hamiltonian into smaller ones. Our decomposition methods only take linear time complexity, achieving end-to-end acceleration. Experiments demonstrate that Choco-Q shows more than 235× algorithmic improvement in successfully finding the optimal solution, and achieves 4.69 × end-to-end acceleration, compared to prior QAOA designs.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00031"}, {"primary_key": "165413", "vector": [], "sparse_vector": [], "title": "UniNDP: A Unified Compilation and Simulation Tool for Near DRAM Processing Architectures.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> He", "<PERSON><PERSON>", "Guangyu Sun", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Near DRAM Processing (NDP) architectures have emerged to be a promising solution for commercializing in-memory computing and addressing the “memory wall” problem, especially for the memory-intensive machine learning (ML) workloads. In NDP architectures, the Processing Units (PUs) are distributed next to different memory units to exploit the high internal bandwidth. Therefore, in order to fully utilize the bandwidth advantage of NDP architectures for ML applications, meticulous evaluations and optimizations of data placement in DRAM and workload scheduling among different PUs are required. However, existing simulation and compilation tools face two insuperable obstacles to achieving these targets. On the one hand, tools for traditional von Neumann architectures only focus on the data access behaviors between the host and DRAM and treat DRAM as a whole part, which cannot support NDP architectures with multiple independent processing and memory units working simultaneously. On the other hand, existing NDP simulators and compilers are designed for specific DRAM technology and NDP architecture, lacking compatibility for various NDP architectures. In order to overcome these challenges and optimize data mapping and workload scheduling for different NDP architectures, we propose UniNDP, a unified NDP compilation and simulation tool for ML applications. Firstly, we propose a unified tree-based NDP hardware abstraction and the corresponding instruction set, enabling the support for various NDP architectures based on different DRAM technologies. Secondly, we design a cycle-accurate and instruction-driven NDP simulator to evaluate hardware performance by accurately tracking the working status of memory elements and PUs. The accurate simulation can provide effective guidance for compilation. Thirdly, we design an NDP compiler that optimizes data partition, mapping, and workload scheduling in different DRAM hierarchies. Furthermore, to enhance the compilation efficiency, we propose a hardware status-guided search space pruning strategy and a fast performance predictor using DRAM timing parameters. Extensive experimental results show that, compared to existing mapping and compilation methods, UniNDP can achieve 1.05-3.43 $\\times$ speedup across multiple NDP architectures and different ML workloads. Furthermore, based on the results of UniNDP, we provide insights for the future NDP architecture design and deployment in ML applications.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00054"}, {"primary_key": "165414", "vector": [], "sparse_vector": [], "title": "I-DGNN: A Graph Dissimilarity-based Framework for Designing Scalable and Efficient DGNN Accelerators.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Dynamic Graph Neural Networks (DGNNs) have recently been used in numerous application domains, comprehending the intricate dynamics of time-evolving graph data. Despite their theoretical advancements, effectively implementing scalable DGNNs continues to be a formidable challenge due to the constantly evolving graph data and heterogeneous computation kernels. Recent efforts attempted to either exploit the graph data reuse to reduce memory access or eliminate the redundant computations between consecutive graph snapshots to scale the DGNN acceleration. These efforts are still falling short. In prior work, each graph snapshot, regardless of its size and connectivity, passes through the entire DGNN computation pipeline from layer to layer. Consequently, substantial intermediate data is generated throughout the DGNN computation, which leads to excessive offchip memory access. To address this crucial challenge, we argue that the computations between evolving graph snapshots should be decoupled from the DGNN execution pipeline. In this paper, we propose I-DGNN, a theoretical, architectural, and algorithmic framework with the aim of designing scalable and efficient accelerators for DGNN execution with improved performance and energy efficiency. On the theory side, the key idea is to identify essential computations between consecutive graph snapshots and encapsulate them as a separate kernel independent from the DGNN model. Specifically, the proposed one-pass DGNN computing model extracts the process of graph update as a chained matrix multiplication between evolving graphs through rigorous mathematical derivations. Consequently, consecutive snapshots utilize a onepass computation kernel instead of passing through the entire DGNN execution pipeline, thereby eliminating the costly data movement of intermediate results across DGNN layers. On the architecture side, we propose a unified accelerator architecture that can be dynamically configured to support the computation characteristics of the proposed I-DGNN computing model with improved data and pipeline parallelism. On the algorithm side, we propose a new dataflow and mapping tailored for I-DGNN to further improve the data locality of inter-kernel data across the DGNN pipeline. Simulation results show that the proposed accelerator achieves $\\mathbf{6 5. 9 \\%}, \\mathbf{7 1. 1 \\%}$, and $\\mathbf{5 8. 8 \\%}$ reductions in execution time and $88.4 \\%, 87.0 \\%$, and $\\mathbf{8 5. 9 \\%}$ improvements in energy efficiency on average across multiple DGNN datasets compared to state-of-the-art-accelerators [1]–[3].", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00081"}, {"primary_key": "165415", "vector": [], "sparse_vector": [], "title": "GoPIM: GCN-Oriented Pipeline Optimization for PIM Accelerators.", "authors": ["<PERSON><PERSON>", "Shuibing <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Xuechen <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Graph convolutional networks (GCNs) are popular for a variety of graph learning tasks. ReRAM-based processing-in-memory (PIM) accelerators are promising to expedite GCN training owing to their in-situ computing capability. However, existing accelerators can be severely underutilized even with pipelines, due to the oversight of the skewed execution times of various GCN stages and the ignorance of skewed degrees of graph vertices. In this work, we propose GOPIM, a GCN-oriented pipeline optimization for PIM accelerators to expedite GCN training. First, GOPIM proposes an ML-based scheme that allocates crossbar resources to the most needed stages to streamline the overall pipeline. Second, GOPIM utilizes a selective vertex updating technique that evenly distributes vertices on crossbars by interleaved mapping. These techniques collectively reduce the overall execution time without losing much accuracy. We also provide a practical architecture design for GOPIM. Our experimental results show that, GoPIM achieves up to 191 × speedup and 16.1 × energy saving, compared to the state-of-the-art work.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00056"}, {"primary_key": "165416", "vector": [], "sparse_vector": [], "title": "Hydra: Scale-out FHE Accelerator Architecture for Secure Deep Learning on FPGA.", "authors": ["<PERSON><PERSON>", "Xicheng Xu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Hang Lu", "<PERSON><PERSON>"], "summary": "Deep learning, including Convolutional Neural Network (CNN) and Large Language Model (LLM), under Fully Homomorphic Encryption (FHE) is very computationally intensive because of the burdensome computations like ciphertext convolution and matrix multiplication, non-linear layers, and bootstrapping. Existing FHE accelerators focus on the high throughput computational units, stacking parallelized clusters to maximize ciphertext inference performance. Nevertheless, this design philosophy cannot leverage the substantial parallelism at the application level and is not scalable for further performance enhancement by simply adding additional compute nodes to cope with the ever-increasing model sizes in the future. In this paper, we propose the high-performance FHE acceleration architecture in a “scale-out” manner for secure deep learning, termed as Hydra. It supports the multi-server scaling and arbitrary computational nodes theoretically, each handling a portion of the deep learning model governed by the central scheduling mechanism on the host server. Hydra exhibits excellent scalability and delivers outstanding performance across a range of compute resource sizes. We highlight the following results: (1) up to $74 \\times$ and $160 \\times$ speedup over the SOTA single card accelerator Poseidon and FAB; (2) outperforms 8-card FAB-2 by $12 \\times$ to $21 \\times$ for FHE-based CNNs and LLMs; (3) outperforms SOTA ASIC accelerators, CraterLake and SHARP, by $8.1 \\times$ and $2.5 \\times$ for LLM OPT-6.7B, and achieves comparable or superior energy efficiency under the same chip technology.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00090"}, {"primary_key": "165417", "vector": [], "sparse_vector": [], "title": "Buffalo: Enabling Large-Scale GNN Training via Memory-Efficient Bucketization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Graph Neural Networks (GNNs) have demonstrated outstanding results in many graph-based deep-learning tasks. However, training GNNs on a large graph can be difficult due to memory capacity limitations. To address this problem, we can divide the graph into multiple partitions. However, this strategy faces a memory explosion problem. This problem stems from a long tail in the degree distribution of graph nodes. This strategy also suffers from time-consuming graph partitioning, difficulty in estimating the memory consumption of each partition, and time-consuming data preparation (e.g., block generation). To address the above problems, we introduce Buffalo, a GNN training system. Buffalo enables flexible mapping between the nodes and partitions to address the memory explosion problem, and enables fast graph partitioning based on node bucketing. Buffalo also introduces lightweight analytical modeling for memory estimation, and reduces block generation time by leveraging graph sampling. Evaluating large-scale real-world datasets (including billionscale datasets), we show that Buffalo effectively addresses the memory capacity limitation, enabling scalable GNN training and outperforming prior works in the compute-vs-memory efficiency Pareto frontier. With a limited memory budget, Buffalo achieves an end-to-end reduction of training time by 70.9% on average, compared to state-of-the-art (DG<PERSON> [73], <PERSON>y<PERSON> [12], and <PERSON> [93]).", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00083"}, {"primary_key": "165418", "vector": [], "sparse_vector": [], "title": "LegoZK: A Dynamically Reconfigurable Accelerator for Zero-Knowledge Proof.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Zero-knowledge proof (ZKP) allows a prover to convince a verifier of the truth of a statement without revealing any secret information. This property is utilized in numerous privacy-preserving applications. However, the huge overhead of proof generation impedes the widespread adoption of ZKP. As a result, many ZKP accelerators have been developed to speed up proof generation. However, existing accelerators are designed at the granularity of core operators and exhibit low hardware resource utilization and limited adaptability. In this paper, we identify the commonality of all computation stages in proof generation at the level of basic finite field arithmetic operations. Based on this insight, we propose LegoZK, a dynamically reconfigurable hardware accelerator for ZKP. LegoZK employs finite field arithmetic units (FAUs) as its fundamental components and integrates these FAUs with a hierarchical on-chip network (NoC). By dynamically configuring the FAUs and the NoC, LegoZK can effectively accelerate the entire proof generation process, achieving higher overall performance. Additionally, for the most time-consuming MSM, this paper proposes a fast, fully pipelined bucket reduction algorithm based on lookup tables, which significantly reduces the latency of MSM. Experimental results demonstrate that LegoZK achieves on average speedup of $31.96 \\times$ and $11.30 \\times$ in proof generation compared to the state-of-the-art ZKP ASIC accelerator PipeZK and the GPU accelerator GZKP, respectively. And compared to PipeZK, LegoZK achieves $\\mathbf{5 0. 1 \\%}$ area reduction and $\\mathbf{3 7. 7 \\%}$ power consumption reduction.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00020"}, {"primary_key": "165419", "vector": [], "sparse_vector": [], "title": "Gaussian Blending Unit: An Edge GPU Plug-in for Real-Time Gaussian-Based Rendering in AR/VR.", "authors": ["<PERSON><PERSON><PERSON>", "Yonggan Fu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The rapidly advancing field of Augmented and Virtual Reality (AR/VR) demands real-time, photorealistic rendering on resource-constrained platforms. 3D Gaussian Splatting, delivering state-of-the-art (SOTA) performance in rendering efficiency and quality, has emerged as a promising solution across a broad spectrum of AR/VR applications. However, despite its effectiveness on high-end GPUs, it struggles on edge systems like the Jetson Orin NX Edge GPU, achieving only 7-17 FPS—well below the over 60 FPS standard required for truly immersive AR/VR experiences. Addressing this challenge, we perform a comprehensive analysis of Gaussian-based AR/VR applications and identify the Gaussian Blending Stage, which intensively calculates each Gaussian’s contribution at every pixel, as the primary bottleneck. In response, we propose a Gaussian Blending Unit (GBU), an edge GPU plug-in module for real-time rendering in AR/VR applications. Notably, our GBU can be seamlessly integrated into conventional edge GPUs and collaboratively supports a wide range of AR/VR applications. Specifically, GBU incorporates an intra-row sequential shading (IRSS) dataflow that shades each row of pixels sequentially from left to right, utilizing a two-step coordinate transformation. This transformation enables (1) the sharing of intermediate values between adjacent pixels, reducing pixel-wise computation costs by up to $5.5 \\times$, and (2) the early identification and skipping of Gaussians that minimally contribute to the pixels, reducing per-pixel computation by up to $\\mathbf{9 3 \\%}$. When directly deployed on a GPU, the proposed dataflow achieved a non-trivial $1.72 \\times$ speedup on real-world static scenes, though still falls short of real-time rendering performance. Recognizing the limited compute utilization in the GPU-based implementation, GBU enhances rendering speed with a dedicated rendering engine that balances the workload across rows by aggregating computations from multiple Gaussians. Additionally, GBU integrates a Gaussian Reuse Cache, reducing off-chip memory accesses by 44.9% and resulting in a $1.14 \\times$ speedup in rendering. Experiments across representative AR/VR applications demonstrate that our GBU provides a unified solution for on-device real-time rendering while maintaining SOTA rendering quality.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00036"}, {"primary_key": "165420", "vector": [], "sparse_vector": [], "title": "Palermo: Improving the Performance of Oblivious Memory using Protocol-Hardware Co-Design.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Oblivious RAM (ORAM) hides the memory access patterns, enhancing data privacy by preventing attackers from discovering sensitive information based on the sequence of memory accesses. The performance of ORAM is often limited by its inherent trade-off between security and efficiency, as concealing memory access patterns imposes significant computational and memory overhead. While prior works focus on improving the ORAM performance by prefetching and eliminating ORAM requests, we find that their performance is very sensitive to workload locality behavior and incurs additional management overhead caused by the ORAM stash pressure. This paper presents Palermo: a protocol-hardware co-design to improve ORAM performance. The key observation in Palermo is that classical ORAM protocols enforce restrictive dependencies between memory operations that result in low memory bandwidth utilization. Palermo introduces a new protocol that overlaps large portions of memory operations, within a single and between multiple ORAM requests, without breaking correctness and security guarantees. Subsequently, we propose an ORAM controller architecture that executes the proposed protocol to service ORAM requests. The hardware is responsible for concurrently issuing memory requests as well as imposing the necessary dependencies to ensure a consistent view of the ORAM tree across requests. Using a rich workload mix, we demonstrate that Palermo outperforms the RingORAM baseline by 2.9 ×, on average, incurring a negligible area overhead of 5.78mm2 (less than 2% in 12th generation Intel CPU after technology scaling) and 2.14W without sacrificing security. We further show that Palermo also outperforms the state-of-the-art works PageORAM, PrORAM, and IR-ORAM.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00038"}, {"primary_key": "165421", "vector": [], "sparse_vector": [], "title": "Predicting DRAM-Caused Risky VMs in Large-Scale Clouds.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Xuhua Ma", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Zheng", "Huite Yi"], "summary": "DRAM failures are one of the leading failures in large-scale clouds. Previous studies focus on predicting DRAM uncorrectable errors (UEs) and mitigating the impact of DRAM failures through node-level workload migration and proactive dual in-line memory module (DIMM) replacement. For cloud systems, node migration means migrating all virtual machines (VMs) to other nodes. Such a coarse-grained migration consumes considerable resources. Inspired by the observation that DRAM errors tend to cluster in space, this paper proposes Pegasus, the first VM-level solution to mitigate DRAM failures. The key idea of Pegasus is to use the VM as the basic unit for predicting DRAM failures instead of the whole node. Specifically, we introduce a new concept of DRAM-caused risky VMs, which causes node unavailability when accessed. We design a novel Error-VM mapping framework deployed on a large-scale cloud. Statistical results confirm that DRAM errors are concentrated in the address space managed by a small number of VMs. By combining ECC and spatio-temporal features, our predictor achieves decent performance. Pegasus has been deployed online on over $\\mathbf{3 0 0, 0 0 0}$ nodes in the production cloud. The comparative study shows that the prediction performance of Pegasus is comparable to node-level solutions. Meanwhile, our approach offers over $\\mathbf{7 0 \\%}$ lower costs and avoids more than $\\mathbf{1 0 \\%}$ of VM crashes compared to node-level mitigation.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00094"}, {"primary_key": "165422", "vector": [], "sparse_vector": [], "title": "NeuVSA: A Unified and Efficient Accelerator for Neural Vector Search.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Neural Vector Search (NVS) has exhibited superior search quality over traditional key-based strategies for information retrieval tasks. An effective NVS architecture requires high recall, low latency, and high throughput to enhance user experience and cost-efficiency. However, implementing NVS on existing neural network accelerators and vector search accelerators is sub-optimal due to the separation between the embedding stage and vector search stage at both algorithm and architecture levels. Fortunately, we unveil that Product Quantization (PQ) opens up an opportunity to break separation. However, existing PQ algorithms and accelerators still focus on either the embedding stage or the vector search stage, rather than both simultaneously. Simply combining existing solutions still follows the beaten track of separation and suffers from insufficient parallelization, frequent data access conflicts, and the absence of scheduling, thus failing to reach optimal recall, latency, and throughput. To this end, we propose a unified and efficient NVS accelerator dubbed NeuVSA based on algorithm and architecture co-design philosophy. Specifically, on the algorithm level, we propose a learned PQ-based unified NVS algorithm that consolidates two separate stages into the same computing and memory access paradigm. It integrates an end-to-end joint training strategy to learn the optimal codebook and index for enhanced recall and reduced PQ complexity, thus achieving smoother acceleration. On the architecture level, we customize a homogeneous NVS accelerator based on the unified NVS algorithm. Each sub-accelerator is optimized to exploit all parallelism exposed by unified NVS, incorporating a structured index assignment strategy and an elastic on-chip buffer to alleviate buffer conflicts for reduced latency. All sub-accelerators are coordinated using a hardware-aware scheduling strategy for boosted throughput. Experimental results show that the joint training strategy improves recall by 4.6% over the separated strategy and accuracy by 43.5% over LUT-NN. NeuVSA achieves $2.82 \\times$ to $416.17 \\times$ lower latency over CPU, GPU, DFX+ANNA, and PQA+ANNA, and up to $49.60 \\times$ and $10.57 \\times$ higher average throughput over CPU and GPU, respectively. NeuVSA also reduces chip area by 65.2% over PQA+ANNA.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00065"}, {"primary_key": "165423", "vector": [], "sparse_vector": [], "title": "Cambricon-DG: An Accelerator for Redundant-Free Dynamic Graph Neural Networks Based on Nonlinear Isolation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Xinka<PERSON> Song", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Zidong Du", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Dynamic Graph Neural Networks (DGNNs) have demonstrated significant potential in handling temporal graph-structured data. Typical DGNNs combine GNNs to process the structural information of graph and RNNs to capture the dynamic temporal information of graphs. However, current GNN accelerators process DGNNs by treating graph snapshots as static entities, leading to substantial redundant computations and memory accesses. While some DGNN accelerators have attempted to mitigate this redundancy, they face the challenge of maintaining accuracy for incremental processing due to the nonlinear activation function in GNNs. To address these issues, we propose a novel nonlinear isolation mechanism for incremental processing of DGNNs, which eliminates all redundant operations without accuracy loss. Additionally, we introduce a vertex-wise computation scheme for RNNs to minimize redundancy and reduce off-chip memory access. To validate the effectiveness of the mechanism, we implemented software optimizations on GPUs, achieving an $8.53 \\times$ performance improvement over full-graph computation schemes. To address the inefficiency of graph topology operations on GPUs, we propose the Cambricon-DG accelerator, which features a dedicated hardware pipeline, an efficient topology management engine for graph traversal and topology sorting, and a hybrid computation engine that supports both GNN and RNN computations, thus improving hardware utilization. We evaluate Cambricon-DG on seven real-world dynamic graph datasets using five representative DGNN models. The results show that Cambricon-DG achieves an average speedup of $49.73 \\times, 12.15 \\times$, and $7.03 \\times$, and an average energy saving of $41.31 \\times, 9.47 \\times$, and $5.23 \\times$ over the state-of-the-art static/dynamic GNN accelerators I-GCN, RACE, and DeltaGNN, respectively.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00074"}, {"primary_key": "165424", "vector": [], "sparse_vector": [], "title": "AccelES: Accelerating Top-K SpMV for Embedding Similarity via Low-bit Pruning.", "authors": ["<PERSON><PERSON><PERSON>", "Xuanhua Shi", "<PERSON><PERSON>", "Chencheng Ye", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In the realm of recommendation systems, achieving real-time performance in embedding similarity tasks is often hindered by the limitations of traditional Top-K sparse matrix-vector multiplication (SpMV) methods, which suffer from high latency due to inefficient memory access patterns. This paper identifies these critical gaps and introduces AccelES, a novel approach that significantly enhances the efficiency of Top-K SpMV. Our method employs a two-stage calculation scheme: the first stage utilizes a compact, low-bit dataset to quickly identify the most relevant entries, while the second stage performs full-precision calculations solely on this pruned subset, thereby minimizing computational overhead. Furthermore, AccelES incorporates innovative matrix representations, Ultra-CSR and Random-CSR, which optimize memory bandwidth utilization. Experimental results demonstrate that AccelES accelerates performance, surpassing state-of-the-art FPGA, GPU, and CPU solutions by factors of 3.4×, 2.5×, and 153.3×, respectively, under controlled conditions. These advancements not only enhance processing speed but also significantly improve real-time performance in recommendation systems, establishing AccelES as a pivotal contribution to the field of Top-K sparse matrix-vector multiplication.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00077"}, {"primary_key": "165425", "vector": [], "sparse_vector": [], "title": "RpcNIC: Enabling Efficient Datacenter RPC Offloading on PCIe-attached SmartNICs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Chen", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The emerging microservice/serverless-based cloud programming paradigm and the rising networking speeds leave the RPC stack as the predominant data center tax. Domain-specific hardware acceleration holds the potential to disentangle the overhead and save host CPU cycles. However, state-of-the-art RPC accelerators integrate RPC logic into the CPU or use specialized low-latency interconnects, hardly adopted in commodity servers. To this end, we design and implement RpcNIC, a software-hardware co-designed SmartNIC that enables efficient RPC layer offloading and reconfigurable RPC kernel offloading. RpcNIC connects to the server through the most widely used PCIe interconnect. To grapple with the ramifications of PCIe-induced challenges, RpcNIC introduces three techniques: (a) a target-aware deserializer that effectively batches cross-PCIe writes on the SmartNIC’s SRAM using compacted hardware data structures; (b) a memory-affinity CPU-SmartNIC collaborative serializer, which trades additional host memory copies for slow cross PCIe-transfers; (c) an automatic field update technique that transparently codifies the schema based on dynamic reconfigure RPC kernels to minimize superfluous PCIe traversals. We prototype RpcNIC using the Xilinx U280 FPGA card. On HyperProtoBench, RpcNIC achieves an average of 2.3 × lower RPC layer processing time than a comparable RPC accelerator baseline and demonstrates 2.6 × achievable throughput improvement in the end-to-end cloud workload.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00104"}, {"primary_key": "165426", "vector": [], "sparse_vector": [], "title": "Gemina: A Coordinated and High-Performance Memory Deduplication Engine.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Chunfeng Du", "<PERSON>"], "summary": "Memory deduplication is widely used to effectively reduce the memory footprint in operating systems, while huge pages are employed to enhance access performance by increasing TLB hit rates. Unfortunately, duplicate huge pages are very rare in memory, leading to most huge pages being split into base pages during deduplication, which degrades access performance by up to $\\mathbf{4 3 \\%}$. Although asynchronous huge page promotion attempts to elevate multiple base pages to huge pages to maximize performance, deduplication disrupts the uniformity of page attributes, limiting the effectiveness of such promotions. To address this problem, we designed Gemina, a new memory deduplication scheme that coordinates huge page management. Gemina employs a fine-grained detector to distinguish pages, redundancy and hotness, a distributor to process pages conservatively and avoid frequent switches, and an adapter to organize memory for fast conversion. This approach balances the trade-off between performance and space efficiency in memory management. Experimental evaluations show that, compared to the naive KSM-based deduplication scheme, Gemina can achieve $96.1 \\%$ of the memory savings of KSM while also improving memory access performance by $\\mathbf{4 3. 1 \\%}$ in random access.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00119"}, {"primary_key": "165427", "vector": [], "sparse_vector": [], "title": "SkyByte: Architecting an Efficient Memory-Semantic CXL-based SSD with OS and Hardware Co-design.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shaobo Li", "<PERSON><PERSON>"], "summary": "The CXL-based solid-state drive (CXL-SSD) provides a promising approach towards scaling the main memory capacity at low cost. However, the CXL-based SSD faces performance challenges due to the long flash access latency and unpredictable events such as garbage collection in the SSD device, stalling the host processor and wasting compute cycles. Although the CXL interface enables the byte-granular data access to the SSD, accessing flash chips is still at page granularity due to physical limitations. The mismatch of access granularity causes significant unnecessary I/O traffic to flash chips, worsening the suboptimal end-to-end data access performance. In this paper, we present SkyByte, an efficient CXL-based SSD that employs a holistic approach to address the aforementioned challenges by co-designing the host operating system (OS) and SSD controller. To alleviate the long memory stall when accessing the CXL-SSD, SkyByte revisits the OS context switch mechanism and enables opportunistic context switches upon the detection of long access delays. To accommodate byte-granular data accesses, SkyByte architects the internal DRAM of the SSD controller into a cacheline-level write $\\log$ and a page-level data cache, and enables data coalescing upon log cleaning to reduce the I/O traffic to flash chips. SkyByte also employs optimization techniques that include adaptive page migration for exploring the performance benefits of fast host memory by promoting hot pages in CXL-SSD to the host. We implement SkyByte with a CXL-SSD simulator and evaluate its efficiency with various data-intensive applications. Our experiments show that SkyByte outperforms current CXL-based SSD by $6.11 \\times$, and reduces the I/O traffic to flash chips by $\\mathbf{2 3. 0 8} \\times$ on average. SkyByte also reaches $\\mathbf{7 5 \\%}$ of the performance of the ideal case that assumes unlimited DRAM capacity in the host, which offers an attractive cost-effective solution.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00051"}, {"primary_key": "165428", "vector": [], "sparse_vector": [], "title": "MeHyper: Accelerating Hypergraph Neural Networks by Exploring Implicit Dataflows.", "authors": ["<PERSON><PERSON>", "Pengcheng Yao", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shaobo Ma", "<PERSON>", "<PERSON><PERSON> Liu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Jingling Xue"], "summary": "Hypergraph Neural Networks (HGNNs) are increasingly utilized to analyze complex inter-entity relationships. Traditional HGNN systems, based on a hyperedge-centric dataflow model, independently process aggregation tasks for hyperedges and vertices, leading to significant computational redundancy. This redundancy arises from recalculating shared information across different tasks. For the first time, we identify and harness implicit dataflows (i.e., dependencies) within HGNNs, introducing the microedge concept to effectively capture and reuse intricate shared information among aggregation tasks, thereby minimizing redundant computations. We have developed a new microedge-centric dataflow model that processes shared information as fine-grained microedge aggregation tasks. This dataflow model is supported by the Read-Process-Activate-Generate execution model, which aims to optimize parallelism among these tasks. Furthermore, our newly developed MeHyper, a microedge-centric HGNN accelerator, incorporates a decoupled pipeline for improved computational parallelism and a hierarchical feature management strategy to reduce off-chip memory accesses for large volumes of intermediate feature vectors generated. Our evaluation demonstrates that MeHyper substantially outperforms the leading CPUbased system PyG-CPU and the GPU-based system HyperGef, delivering performance improvements of $1,032.23 \\times$ and $10.51 \\times$, and energy efficiencies of $1,169.03 \\times$ and $9.96 \\times$, respectively.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00073"}, {"primary_key": "165429", "vector": [], "sparse_vector": [], "title": "NearFetch: Saving Inter-Module Bandwidth in Many-Chip-Module GPUs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Huadong Dai"], "summary": "As Graphics Processing Units (GPUs) face increasing computing demands that surpass single-module capabilities due to transistor scaling and lithography constraints, the necessity for expanding the module count within GPUs grows. This escalation faces a significant challenge: the total inter-module bandwidth in many-chip-module GPUs is limited by manufacturing constraints in organic substrates or silicon interposers. Unlike Central Processing Units (CPUs), which are latency-sensitive, GPUs leverage their high thread-level parallelism to effectively hide memory access latency through simultaneous multithreading. This attribute makes GPUs inherently sensitive to bandwidth constraints, making the efficient exploitation of available inter-module bandwidth important. In this paper, we identify that fetching data from faraway memory in many-chip-module GPUs can easily cause bandwidth contention which degrades the real achieved data bandwidth per GPU module compared to fetching data from nearby memory. To further analyze this problem, we introduce the Inter-Module Bandwidth per Access (IBPA) metric for quantifying bandwidth usage and finding the network hop count directly impacts the IBPA and network contention. Next, we propose NearFetch, a routing-based solution to reduce IBPA. NearFetch works due to the fact that GPU modules along the routing path are typically much closer to the source GPU module while these GPU modules can supply $29.1 \\%$ of data for high-sharing applications. NearFetch consists of two primary components: a data forwarding scheme, enabling data forwarding when the data resides in a remote GPU module, and a topology-aware Miss Status Handling Register (MSHR) coalescing scheme, responsible for recording the memory address information for future use in case of a data miss. By leveraging the data locality among various GPU modules, NearFetch substantially minimizes inter-module bandwidth usage, eliminating the need to fetch data from distant memory partitions. Our evaluation of NearFetch within the context of many-chip-module GPUs, across applications exhibiting diverse degrees of data locality, reveals that it reduces IBPA by $4 2. 6 \\%$ and enhances performance by an average of $52.2 \\%$ (with up to $9 8. 1 \\%$ improvement) for high-sharing workloads.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00125"}, {"primary_key": "165430", "vector": [], "sparse_vector": [], "title": "Criticality-Aware Instruction-Centric Bandwidth Partitioning for Data Center Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "To reduce operational costs, modern data centers co-locate high-priority latency-critical (LC) tasks and low-priority best-effort (BE) tasks on the same physical node to increase resource utilization. However, such co-location leads to contention for memory bandwidth, resulting in priority inversion, where BE tasks severely slow down LC tasks. This priority inversion often leads to violations of the quality of service (QoS) requirements for LC tasks, defeating the purpose of co-location. Prior approaches to this issue either fail to enforce the QoS requirements for LC tasks or underutilize memory bandwidth.We present Pivot, a novel bandwidth partitioning system that overcomes the limitations of prior approaches based on two key insights. First, memory accesses from LC tasks must be prioritized across all the components on the memory path rather than a single component, as done in prior work. Second, only the scheduling of a selective portion of performance-critical loads (i.e., those causing a long stall on the re-order buffer), instead of all memory accesses from LC tasks, should be prioritized. To leverage these insights, Pivot overcomes the key challenge of accurately identifying performance-critical loads while incurring minimal runtime overhead by proposing a two-phase profiling technique. Our extensive evaluation shows that Pivot improves effective machine utilization by up to $\\mathbf{3 4. 5 \\%}$ while increasing the throughput of the BE applications by up to $2.76 \\times$ compared to state-of-the-art approaches.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00042"}, {"primary_key": "165431", "vector": [], "sparse_vector": [], "title": "From Optimal to Practical: Efficient Micro-op Cache Replacement Policies for Data Center Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Optimizing the CPU frontend has become crucial for modern processors with intricate instruction decoding logic, especially for efficiently running planet-scale data center applications. Micro-operation (micro-op) cache is a key unit to help improve the energy efficiency of the CPU frontend. Unfortunately, we find that data center applications suffer from frequent micro-op cache misses due to the lack of an effective micro-op cache replacement policy. Developing micro-op cache-specific replacement policies is challenging, as there currently does not exist an optimal theoretical solution akin to <PERSON><PERSON>’s algorithm for conventional caches. As a result, it is unknown by how much replacement policies can be improved and how to get there. To address these challenges, we introduce FLACK, a new near-optimal offline policy that considers the key features of the micro-op cache, such as variable and disproportional costs of micro-op cache misses and partial hits. We show that FLACK substantially outperforms <PERSON><PERSON>’s algorithm, thus establishing a new baseline for micro-op cache replacement policies. We then design FURBYS, a practical policy that mimics FLACK via profile-guided methods. FURBYS has three key components to perform cache replacement decisions: (1) it uses profiles of the whole-execution hit/miss behavior, (2) it detects locally (transiently) hot data, and (3) it selectively ignores data with profiled low hit rates. We evaluate FLACK and FURBYS using 11 data center applications and find that FLACK demonstrates an average bound of 30.21% miss reduction, achieving 4.46% greater miss reduction than <PERSON><PERSON>’s algorithm. Our practical policy, FURBYS, provides 14.34% average miss reduction compared to LRU, which is $1.84 \\times$ greater than the current state-of-the-art replacement policy, contributing to 3.10% of performance-perwatt improvement for the CPU core. On average, in terms of miss reduction and IPC gain, FURBYS is equivalent to LRU policy on $1.5 \\times$ micro-op cache sizes (up to $2 \\times$), demonstrating the effectiveness of the proposed replacement policy.", "published": "2025-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA61900.2025.00060"}]