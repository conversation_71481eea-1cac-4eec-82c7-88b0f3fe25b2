[{"primary_key": "2186706", "vector": [], "sparse_vector": [], "title": "Beyond Value Perturbation: Local Differential Privacy in the Temporal Setting.", "authors": ["Qingqing Ye", "Haibo Hu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Meng", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Time series has numerous application scenarios. However, since many time series data are personal data, releasing them directly could cause privacy infringement. All existing techniques to publish privacy-preserving time series perturb the values while retaining the original temporal order. However, in many value-critical scenarios such as health and financial time series, the values must not be perturbed whereas the temporal order can be perturbed to protect privacy. As such, we propose \"local differential privacy in the temporal setting\" (TLDP) as the privacy notion for time series data. After quantifying the utility of a temporal perturbation mechanism in terms of the costs of a missing, repeated, empty, or delayed value, we propose three mechanisms for TLDP. Through both analytical and empirical studies, we show the last one, Threshold mechanism, is the most effective under most privacy budget settings, whereas the other two baseline mechanisms fill a niche by supporting very small or large privacy budgets.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488899"}, {"primary_key": "2186707", "vector": [], "sparse_vector": [], "title": "Learning for Learning: Predictive Online Control of Federated Learning with Edge Provisioning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Operating federated learning optimally over distributed cloud-edge networks is a non-trivial task, which requires to manage data transference from user devices to edges, resource provisioning at edges, and federated learning between edges and the cloud. We formulate a non-linear mixed integer program, minimizing the long-term cumulative cost of such a federated learning system while guaranteeing the desired convergence of the machine learning models being trained. We then design a set of novel polynomial-time online algorithms to make adaptive decisions by solving continuous solutions and converting them to integers to control the system on the fly, based only on the predicted inputs about the dynamic and uncertain cloud-edge environments via online learning. We rigorously prove the competitive ratio, capturing the multiplicative gap between our approach using predicted inputs and the offline optimum using actual inputs. Extensive evaluations with real-world training datasets and system parameters confirm the empirical superiority of our approach over multiple state-of-the-art algorithms.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488733"}, {"primary_key": "2186708", "vector": [], "sparse_vector": [], "title": "Maximizing the Benefit of RDMA at End Hosts.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Tiancheng Jin"], "summary": "RDMA is increasingly deployed in data center to meet the demands of ultra-low latency, high throughput and low CPU overhead. However, it is not easy to migrate existing applications from the TCP/IP stack to the RDMA. The developers usually need to carefully select communication primitives and manually tune the parameters for each single-purpose system. After operating the high-speed RDMA network, we identify multiple hidden costs which may cause degraded and/or unpredictable performance of RDMA-based applications. We demonstrate these hidden costs including the combination of complicated parameter settings, scalability of Reliable Connections, two-sided memory management and page alignment, resource contention among diverse traffics, etc. Furthermore, to address these problems, we introduce Nem, a suite that allows developers to maximize the benefit of RDMA by i) eliminating the resource contention at NIC cache through asynchronous resource sharing; ii) introducing hybrid page management based on messages sizes; iii) isolating flows of different traffic classes based hardware features. We implement the prototype of Nem and verify its effectiveness by rebuilding the RPC message service, which demonstrates the high throughput for large messages, low latency for small messages without compromising the low CPU utilization and good scalability performance for a large number of active connections.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488875"}, {"primary_key": "2186709", "vector": [], "sparse_vector": [], "title": "Low Cost Sparse Network Monitoring Based on Block Matrix Completion.", "authors": ["<PERSON><PERSON>", "Jiazheng T<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Zhang", "<PERSON><PERSON><PERSON>"], "summary": "Due to high network measurement cost, network-wide monitoring faces many challenges. For a network consisting of n nodes, the cost of one time network-wide monitoring will be O(n 2 ). To reduce the monitoring cost, inspired by recent progress of matrix completion, a novel sparse network monitoring scheme is proposed to obtain network-wide monitoring data by sampling a few paths while inferring monitoring data of others. However, current sparse network monitoring schemes suffer from the problems of high measurement cost, high computation complexity in sampling scheduling, and long time to recover the un-sampled data. We propose a novel block matrix completion that can guarantee the quality of the un-sampled data inference by selecting as few as m = O(nr ln(r)) samples for a rank r N × T matrix with n = max{N,T}, which largely reduces the sampling complexity as compared to the existing algorithm for matrix completion. Based on block matrix completion, we further propose a light weight sampling scheduling algorithm to select measurement samples and a light weight data inference algorithm to quickly and accurately recover the un-sampled data. Extensive experiments on three real network monitoring data sets verify our theoretical claims and demonstrate the effectiveness of the proposed algorithms.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488908"}, {"primary_key": "2186710", "vector": [], "sparse_vector": [], "title": "Layer Aware Microservice Placement and Request Scheduling at the Edge.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Container-based microservice has emerged as a promising technique in promoting edge computing elasticity. At the runtime, microservices, encapsulated in form of container images, need to be frequently downloaded from remote registries to local edge servers, which may incur significant overhead in terms of excessive download traffic and large local storage. Given the limited resources at the edge, it is of critical importance to minimize such overhead in order to enhance microservice offerings. A distinctive feature in container-based microservice, which has not been exploited, is that microservice images are in layered structure and common layers can be shared by co-located microservices. In this paper, we study a layer aware micro-service placement and request scheduling at the edge. Intuitively, throughput and number of hosted microservices can be significantly increased by layer sharing between co-located images. We formulate this into an optimization problem with approximate submodularity, and prove this to be NP-hard. We design an iterative greedy algorithm with guaranteed approximation ratio. Extensive experiments validate the efficiency of our method, and the results demonstrate that the number of placed microservices can be increased by 27.61% and the microservice throughput can be improved by 73.13%, respectively, in comparison with the state-of-the-art microservice placement strategy.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488779"}, {"primary_key": "2186712", "vector": [], "sparse_vector": [], "title": "Lifesaving with RescueChain: Energy-Efficient and Partition-Tolerant Blockchain Based Secure Information Sharing for UAV-Aided Disaster Rescue.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Ruid<PERSON> Li", "<PERSON>"], "summary": "Unmanned aerial vehicles (UAVs) have brought numerous potentials to establish flexible and reliable emergency networks in disaster areas when terrestrial communication infrastructures go down. Nevertheless, potential security threats may occur on UAVs during data transmissions due to the untrustful environment and open-access UAV networking. Moreover, UAVs typically have limited battery and computation capacity, making them unaffordable to execute heavy security provisioning operations when carrying out complicated rescue tasks. In this paper, we develop RescueChain, a secure and efficient information sharing scheme for UAV-aided disaster rescue. Specifically, we first implement a lightweight blockchain-based framework to safeguard data sharing under disasters and immutably trace misbehaving entities. A reputation-based consensus protocol is devised to adapt the weakly connected environment with improved consensus efficiency and promoted UAVs' honest behaviors. Furthermore, we introduce a novel vehicular fog computing based off-chain mechanism by leveraging ground vehicles as moving fog nodes to offload UAVs' heavy data processing and storage tasks. To optimally stimulate vehicles to share their idle computing resources, we also design a two-layer reinforcement learning based incentive algorithm for UAVs and ground vehicles in the highly dynamic networks. Simulation results show that RescueChain can effectively accelerate consensus process, enhance user payoffs, and reduce delivery latency, compared with representative existing approaches.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488719"}, {"primary_key": "2186713", "vector": [], "sparse_vector": [], "title": "Self-Adaptive Sampling for Network Traffic Measurement.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Per-flow traffic measurement in the high-speed network plays an important role in many practical applications. Due to the limited on-chip memory and the mismatch between off-chip memory speed and line rate, sampling-based methods select and forward a part of flow traffic to off-chip memory, complementing sketch-based solutions in estimation accuracy and online query support. However, most current work uses the same sampling probability for all flows, overlooking that the sampling rates different flows require to meet the same accuracy constraint are different. It leads to a waste in storage and communication resources. In this paper, we present self-adaptive sampling, a framework to sample each flow with a probability adapted to flow size/spread. Then we propose two algorithms, SAS-LC and SAS-LOG, which are geared towards per-flow spread estimation and per-flow size estimation by using different compression functions. Experimental results based on real Internet traces show that, when compared to NDS in per-flow spread estimation, SAS-LC can save around 10% on-chip space and reduce up to 40% communication cost for large flows. Moreover, SAS-LOG can save 40% on-chip space and reduce up to 96% communication cost for large flows than NDS in per-flow size estimation.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488425"}, {"primary_key": "2186716", "vector": [], "sparse_vector": [], "title": "Device Sampling for Heterogeneous Federated Learning: Theory, Algorithms, and Implementation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The conventional federated learning (FedL) architecture distributes machine learning (ML) across worker devices by having them train local models that are periodically aggregated by a server. FedL ignores two important characteristics of contemporary wireless networks, however: (i) the network may contain heterogeneous communication/computation resources, while (ii) there may be significant overlaps in devices' local data distributions. In this work, we develop a novel optimization methodology that jointly accounts for these factors via intelligent device sampling complemented by device-to-device (D2D) offloading. Our optimization aims to select the best combination of sampled nodes and data offloading configuration to maximize FedL training accuracy subject to realistic constraints on the network topology and device capabilities. Theoretical analysis of the D2D offloading subproblem leads to new FedL convergence bounds and an efficient sequential convex optimizer. Using this result, we develop a sampling methodology based on graph convolutional networks (GCNs) which learns the relationship between network attributes, sampled nodes, and resulting offloading that maximizes FedL accuracy. Through evaluation on real-world datasets and network measurements from our IoT testbed, we find that our methodology while sampling less than 5% of all devices outperforms conventional FedL substantially both in terms of trained model accuracy and required resource utilization.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488906"}, {"primary_key": "2186718", "vector": [], "sparse_vector": [], "title": "A Deep-Learning-based Link Adaptation Design for eMBB/URLLC Multiplexing in 5G NR.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "URLLC is an important use case in 5G NR that targets at 1-ms level delay-sensitive applications. For fast transmission of URLLC traffic, a promising mechanism is to multiplex URLLC traffic into a channel occupied by eMBB service through preemptive puncturing. Although preemptive puncturing can offer transmission resource to URLLC on demand, it will adversely affect throughput and link reliability performance of eMBB service. To mitigate such an adverse impact, a possible approach is to employ link adaptation (LA) through MCS selection for eMBB users. In this paper, we study the problem of maximizing eMBB throughput through MCS selection while ensuring link reliability requirement for eMBB users. We present DELUXE - the first successful design and implementation based on deep learning to address this problem. DELUXE involves a novel mapping method to compress high-dimensional eMBB transmission information into a low-dimensional representation with minimal information loss, a learning method to learn and predict the block-error rate (BLER) under each MCS, and a fast calibration method to compensate errors in BLER predictions. For proof of concept, we implement DELUXE through a link-level 5G NR simulator. Extensive experimental results show that DELUXE can successfully maintain the desired link reliability for eMBB while striving for spectral efficiency. In addition, our implementation can meet the real-time requirement (<; 125 μs) in 5G NR.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488790"}, {"primary_key": "2186719", "vector": [], "sparse_vector": [], "title": "INCdeep: Intelligent Network Coding with Deep Reinforcement Learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we address the problem of building adaptive network coding coefficients under dynamic network conditions (e.g., varying link quality and changing number of relays). In existing linear network coding solutions including deterministic network coding and random linear network coding, coding coefficients are set by a heuristic or randomly chosen from a Galois field with equal probability, which can not adapt to dynamic network conditions with good decoding performance. We propose INCdeep, an adaptive Intelligent Network Coding with Deep Reinforcement Learning. Specifically, we formulate a coding coefficients selection problem where network variations can be automatically and continuously expressed as the state transitions of a Markov decision process (MDP). The key advantage is that INCdeep is able to learn and dynamically adjust the coding coefficients for the source node and each relay node according to ongoing network conditions, instead of randomly. The results show that INCdeep has generalization ability that adapts well in dynamic scenarios where link quality is changing fast, and it converges fast in the training process. Compared with the benchmark coding algorithms, INCdeep shows superior performance, including higher decoding probability and lower coding overhead through simulations and experiments.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488770"}, {"primary_key": "2186720", "vector": [], "sparse_vector": [], "title": "DC2: Delay-aware Compression Control for Distributed Machine Learning.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Distributed training performs data-parallel training of DNN models which is a necessity for increasingly complex models and large datasets. Recent works are identifying major communication bottlenecks in distributed training. These works seek possible opportunities to speed-up the training in systems supporting distributed ML workloads. As communication reduction, compression techniques are proposed to speed up this communication phase. However, compression comes at the cost of reduced model accuracy, especially when compression is applied arbitrarily. Instead, we advocate a more controlled use of compression and propose DC2, a delay-aware compression control mechanism. DC2 couples compression control and network delays in applying compression adaptively. DC2 not only compensates for network variations but can also strike a better trade-off between training speed and accuracy. DC2 is implemented as a drop-in module to the communication library used by the ML toolkit and can operate in a variety of network settings. We empirically evaluate DC2 in network environments exhibiting low and high delay variations. Our evaluation of different popular CNN models and datasets shows that DC2 improves training speed-ups of up to 41× and 5.3 × over baselines with no-compression and uniform compression, respectively.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488810"}, {"primary_key": "2186721", "vector": [], "sparse_vector": [], "title": "Fresh Caching for Dynamic Content.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Atilla E<PERSON>il<PERSON>z", "<PERSON>"], "summary": "We introduce a framework and provably-efficient schemes for `fresh' caching at the (front-end) local cache of content that is subject to `dynamic' updates at the (back-end) database. We start by formulating the hard-cache-constrained problem for this setting, which quickly becomes intractable due to the limited cache. To bypass this challenge, we first propose a flexible time-based-eviction model to derive the average system cost function that measures the system's cost due to the service of aging content in addition to the regular cache miss cost. Next, we solve the cache-unconstrained case, which reveals how the refresh dynamics and popularity of content affect the optimal caching. Then, we extend our approach to a soft-cache-constrained version, where we can guarantee that the cache use is limited with arbitrarily high probability. The corresponding solution reveals the interesting insight that `whether to cache an item or not in the local cache?' depends primarily on its popularity level, whereas `how long the cached item should be held in the cache before eviction?' depends primarily on its refresh rate. Moreover, we investigate the cost-cache saving tradeoffs and prove that substantial cache gains can be obtained while also asymptotically achieving the minimum cost as the database size grows.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488731"}, {"primary_key": "2186722", "vector": [], "sparse_vector": [], "title": "802.11ad in Smartphones: Energy Efficiency, Spatial Reuse, and Impact on Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present an extensive experimental evaluation of the performance and power consumption of the 60 GHz IEEE 802.11ad technology on commercial smartphones. We also compare 802.11ad against its main competitors in the 5 GHz band - 802.11ac and, for first time, 802.11ax, on mobile devices. Our performance comparison focuses on two aspects that have not been extensively studied before: (i) dense multi-client and multi-AP topologies and (ii) popular mobile applications under realistic mobility patterns. Our power consumption study covers both non-communicating and communicating modes. We also present the first study of the power saving mode in 802.11ad-enabled smartphones and its impact on performance. Our results show that 802.11ad is better able to address the needs of emerging bandwidth-intensive applications in smartphones than its 5 GHz counterparts. At the same time, we identify several key research directions towards realizing its full potential.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488763"}, {"primary_key": "2186723", "vector": [], "sparse_vector": [], "title": "Monitoring Cloud Service Unreachability at Scale.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Mehta", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Venkata N. <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the problem of network unreachability in a global-scale cloud-hosted service that caters to hundreds of millions of users. Even when the service itself is up, the \"last mile\" between where users are, and the cloud is often the weak link that could render the service unreachable. We present NetDetector, a tool for detecting network-unreachability based on measurements from a client-based HTTP-ping service. NetDetector employs two models. The first, GA (Gaussian Alerts) models temporally averaged raw success rate of the HTTP-pings as a Gaussian distribution and flags significant dips below the mean as unreachability episodes. The second, more sophisticated approach (BB, or Beta-Binomial) models the health of network connectivity as the probability of an access request succeeding, estimates health from noisy samples, and alerts based on dips in health below a client-network-specific SLO (service-level objective) derived from data. These algorithms are enhanced by a drill-down technique that identifies a more precise scope of the unreachability event. We present promising results from GA, which has been in deployment, and the experimental BB detector over a 4-month period. For instance, GA flags 49 country-level unreachability incidents, of which 42 were labelled true positives based on investigation by on-call engineers (OCEs).", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488778"}, {"primary_key": "2186726", "vector": [], "sparse_vector": [], "title": "PALMAR: Towards Adaptive Multi-inhabitant Activity Recognition in Point-Cloud Technology.", "authors": ["<PERSON>", "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "With the advancement of deep neural networks and computer vision-based Human Activity Recognition, employment of Point-Cloud Data technologies (LiDAR, mmWave) has seen a lot interests due to its privacy preserving nature. Given the high promise of accurate PCD technologies, we develop, PALMAR, a multiple-inhabitant activity recognition system by employing efficient signal processing and novel machine learning techniques to track individual person towards developing an adaptive multi-inhabitant tracking and HAR system. More specifically, we propose (i) a voxelized feature representation-based real-time PCD fine-tuning method, (ii) efficient clustering (DBSCAN and BIRCH), Adaptive Order Hidden Markov Model based multi-person tracking and crossover ambiguity reduction techniques and (iii) novel adaptive deep learning-based domain adaptation technique to improve the accuracy of HAR in presence of data scarcity and diversity (device, location and population diversity). We experimentally evaluate our framework and systems using (i) a real-time PCD collected by three devices (3D LiDAR and 79 GHz mmWave) from 6 participants, (ii) one publicly available 3D LiDAR activity data (28 participants) and (iii) an embedded hardware prototype system which provided promising HAR performances in multi-inhabitants (96%) scenario with a 63% improvement of multi-person tracking than state-of-art framework without losing significant system performances in the edge computing device.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488789"}, {"primary_key": "2186727", "vector": [], "sparse_vector": [], "title": "Modeling the Cost of Flexibility in Communication Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Communication networks are evolving towards a more adaptive and reconfigurable nature due to the evergrowing demands they face. A framework for measuring network flexibility has been proposed recently, but the cost of rendering communication networks more flexible has not yet been mathematically modeled. As new technologies such as software-defined networking (SDN), network function virtualization (NFV), or network virtualization (NV) emerge to provide network flexibility, a way to estimate and compare the cost of different implementation options is needed. In this paper, we present a comprehensive model of the cost of a flexible network that takes into account its transient and stationary phases. This allows network researchers and operators to not only qualitatively argue about their new flexible network solutions, but also to analyze their cost for the first time in a quantitative way.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488900"}, {"primary_key": "2186733", "vector": [], "sparse_vector": [], "title": "Turbocharging Deep Backscatter Through Constructive Power Surges with a Single RF Source.", "authors": ["Z<PERSON><PERSON> An", "<PERSON><PERSON><PERSON><PERSON> Lin", "Qingrui Pan", "<PERSON><PERSON>"], "summary": "Backscatter networks are becoming a promising solution for embedded sensing. In these networks, backscatter sensors are deeply implanted inside objects or living beings and form a deep backscatter network (DBN). The fundamental challenges in DBNs are the significant attenuation of the wireless signal caused by environmental materials (e.g., water and bodily tissues) and the miniature antennas of the implantable backscatter sensors, which prevent existing backscatter networks from powering sensors beyond superficial depths. This study presents RiCharge, a turbocharging solution that enables powering up and communicating with DBNs through a single augmented RF source, which allows existing backscatter sensors to serve DBNs at zero startup cost. The key contribution of RiCharge is the turbocharging algorithm that utilizes RF surges to induce constructive power surges at deep backscatter sensors in accordance with the FCC regulations, for overcoming the turn-on voltage barrier. RiCharge is implemented in commodity devices, and the evaluation result reveals that RiCharge can use only a single RF source to power up backscatter sensors at 60 m distance in the air (i.e., 10x longer than a commercial off-the-shelf reader) and 50 cm-depth under water (i.e., 2x deeper than the previous record).", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488871"}, {"primary_key": "2186737", "vector": [], "sparse_vector": [], "title": "Failure Localization through Progressive Network Tomography.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Boolean Network Tomography (BNT) allows to localize network failures by means of end-to-end monitoring paths. Nevertheless, it falls short of providing efficient failure identification in real scenarios, due to the large combinatorial size of the solution space, especially when multiple failures occur concurrently. We aim at maximizing the identification capabilities of a bounded number of monitoring probes. To tackle this problem we propose a progressive approach to failure localization based on stochastic optimization, whose solution is the optimal sequence of monitoring paths to probe. We address the complexity of the problem by proposing a greedy strategy in two variants: one considers exact calculation of posterior probabilities of node failures given the observation, whereas the other approximates these values through a novel failure centrality metric. We discuss the approximation of the proposed approaches. Then, by means of numerical experiments conducted on real network topologies, we demonstrate the practical applicability of our approach. The performance evaluation evidences the superiority of our algorithms with respect to state of the art solutions based on classic Boolean Network Tomography as well as approaches based on sequential group testing.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488893"}, {"primary_key": "2186739", "vector": [], "sparse_vector": [], "title": "Bayesian Online Learning for Energy-Aware Resource Orchestration in Virtualized RANs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Radio Access Network Virtualization (vRAN) will spearhead the quest towards supple radio stacks that adapt to heterogeneous infrastructure: from energy-constrained platforms deploying cells-on-wheels (e.g., drones) or battery-powered cells to green edge clouds. We perform an in-depth experimental analysis of the energy consumption of virtualized Base Stations (vBSs) and render two conclusions: (i) characterizing performance and power consumption is intricate as it depends on human behavior such as network load or user mobility; and (ii) there are many control policies and some of them have non-linear and monotonic relations with power and throughput. Driven by our experimental insights, we argue that machine learning holds the key for vBS control. We formulate two problems and two algorithms: (i) BP-vRAN, which uses Bayesian online learning to balance performance and energy consumption, and (ii) SBP-vRAN, which augments our Bayesian optimization approach with safe controls that maximize performance while respecting hard power constraints. We show that our approaches are data-efficient and have provably performance, which is paramount for carrier-grade vRANs. We demonstrate the convergence and flexibility of our approach and assess its performance using an experimental prototype.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488845"}, {"primary_key": "2186741", "vector": [], "sparse_vector": [], "title": "Experimental UAV Data Traffic Modeling and Network Performance Analysis.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Network support for Unmanned Aerial Vehicles (UAVs) is raising an interest among researchers due to the strong potential applications. However, current knowledge on UAV data traffic is mainly based on conceptual studies and does not provide an in-depth insight on the data traffic properties. To close this gap, we present a measurement-based study analyzing in detail the Control and Non-payload Communication (CNPC) traffic produced by three different UAVs when communicating with their remote controller over 802.11 protocol. We analyze the traffic in terms of data rate, inter-packet interval and packet length distributions, and identify their main influencing factors. The data traffic appears neither deterministic nor periodic but bursty, with a tendency towards Poisson traffic. We further create an understanding on how the traffic of the investigated UAVs are internally generated and propose a model to analytically capture their traffic processes, which provides an explanation for the observed behavior. We implemented a publicly available UAV traffic generator \"AVIATOR\" based on the proposed traffic model and verified the model by comparing the simulated traces with the experimental results.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488878"}, {"primary_key": "2186749", "vector": [], "sparse_vector": [], "title": "BlendVLC: A Cell-free VLC Network Architecture Empowered by Beamspot Blending.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In visible light communication (VLC), the quality of communication is primarily dominated by line-of-sight links. To ensure an appropriate link quality anywhere, beamsteering has been proposed where transmitters (TXs) dynamically steer their beams to create beamspots on the users. However, these highly dynamic TXs face the beam tracking problem and result in highly variable illumination. In this work, we propose BlendVLC, a cell-free network architecture to improve the mobility robustness of users by blending the beamspots from both steerable and fixed TXs. We solve the beam tracking by designing a centimeter-level visible light positioning algorithm empowered by a neural network. Relying on this location information, we formulate and solve an optimization problem on the beamspot blending, and design a fast and scalable heuristic for large networks. We build a proof-of-concept testbed as well as a simulator to evaluate BlendVLC. We show that it achieves superior performance compared to denser networks with fully fixed TXs. For example, in a large-scale VLC network of 8 m x 4 m, BlendVLC improves the average system throughput by 30%, while only requiring half the number of TXs.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488727"}, {"primary_key": "2186751", "vector": [], "sparse_vector": [], "title": "Low-Power Downlink for the Internet of Things using IEEE 802.11-compliant Wake-Up Receivers.", "authors": ["<PERSON>", "Tran Huy Vu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Ultra-low power communication is critical for supporting the next generation of battery-operated or energy harvesting battery-less Internet of Things (IoT) devices. Duty cycling protocols and wake-up receiver (WuRx) technologies, and their combinations, have been investigated as energy-efficient mechanisms to support selective, event-driven activation of devices. In this paper, we go one step further and show how WuRx can be used for an efficient and multi-purpose low power downlink (LPD) communication channel. We demonstrate how to (a) extend the wake-up signal to support low-power flexible and extensible unicast, multicast, and broadcast downlink communication and (b) utilize the WuRx-based LPD to also improve the energy efficiency of uplink data transfer. In addition, we show how the non-negligible energy overhead of conventional microcontroller based decoding of LPD communication can be substantially reduced by using the low-power universal asynchronous receiver/transmitter (LPUART) module of modern microcontrollers. Via experimental studies, involving both a functioning prototype and larger-scale simulations, we show that our proposed approach is compatible with conventional WLAN and offers a two-orders-of-magnitude improvement in uplink throughput and energy overheads over a competitive, IEEE 802.11 PSM-based baseline. This new LPD capability can also be used to improve the RF-based energy harvesting efficiency of battery-less IoT devices.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488838"}, {"primary_key": "2186752", "vector": [], "sparse_vector": [], "title": "SteaLTE: Private 5G Cellular Connectivity as a Service with Full-stack Wireless Steganography.", "authors": ["<PERSON>", "Salvatore D&apos;Oro", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Fifth-generation (5G) systems will extensively employ radio access network (RAN) softwarization. This key innovation enables the instantiation of \"virtual cellular networks\" running on different slices of the shared physical infrastructure. In this paper, we propose the concept of Private Cellular Connectivity as a Service (PCCaaS), where infrastructure providers deploy covert network slices known only to a subset of users. We then present SteaLTE as the first realization of a PCCaaS-enabling system for cellular networks. At its core, SteaLTE utilizes wireless steganography to disguise data as noise to adversarial receivers. Differently from previous work, however, it takes a full-stack approach to steganography, contributing an LTE-compliant stegano-graphic protocol stack for PCCaaS-based communications, and packet schedulers and operations to embed covert data streams on top of traditional cellular traffic (primary traffic). SteaLTE balances undetectability and performance by mimicking channel impairments so that covert data waveforms are almost indistinguishable from noise. We evaluate the performance of SteaLTE on an indoor LTE-compliant testbed under different traffic profiles, distance and mobility patterns. We further test it on the outdoor PAWR POWDER platform over long-range cellular links. Results show that in most experiments SteaLTE imposes little loss of primary traffic throughput in presence of covert data transmissions (<; 6%), making it suitable for undetectable PCCaaS networking.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488889"}, {"primary_key": "2186754", "vector": [], "sparse_vector": [], "title": "Trust Trackers for Computation Offloading in Edge-Based IoT Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Wireless Internet of Things (IoT) devices will be deployed to enable applications such as sensing and actuation. These devices are typically resource-constrained and are unable to perform resource-intensive computations. Therefore, these jobs need to be offloaded to resource-rich nodes at the edge of the IoT network for execution. However, the timeliness and correctness of edge nodes may not be trusted (such as during high network load or attack). In this paper, we look at the applicability of trust for successful offloading. Traditionally, trust is computed at the application level, with suitable mechanisms to adjust for factors such as recency. However, these do not work well in IoT networks due to resource constraints. We propose a novel device called Trust Tracker (denoted by Σ) that provides higher-level applications with up-to-date trust information of the resource-rich nodes. We prove impossibility results regarding computation offloading and show that Σ is necessary and sufficient for correct offloading. We show that, Σ cannot be implemented even in a synchronous network and we compute the probability of offloading to a bad node, which we show to be negligible when a majority of nodes are correct. We perform a small-scale deployment to demonstrate our approach.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488844"}, {"primary_key": "2186759", "vector": [], "sparse_vector": [], "title": "Distributed Neighbor Distribution Estimation with Adaptive Compressive Sensing in VANETs.", "authors": ["<PERSON>xia<PERSON>ai", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Acquiring the geographical distribution of neighbors can support more adaptive media access control (MAC) protocols and other safety applications in Vehicular ad hoc network (VANETs). However, it is very challenging for each vehicle to estimate its own neighbor distribution in a fully distributed setting. In this paper, we propose an online distributed neighbor distribution estimation scheme, called PeerProbe, in which vehicles collaborate with each other to probe their own neighborhood via simultaneous symbol-level wireless communication. An adaptive compressive sensing algorithm is developed to recover a neighbor distribution based on a small number of random probes with non-negligible noise. Moreover, the needed number of probes adapts to the sparseness of the distribution. We conduct extensive simulations and the results demonstrate that PeerProbe is lightweight and can accurately recover highly dynamic neighbor distributions in critical channel conditions.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488841"}, {"primary_key": "2186762", "vector": [], "sparse_vector": [], "title": "AWash: Handwashing Assistance for the Elderly with <PERSON><PERSON><PERSON> via Wearables.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Hand hygiene has a significant impact on human health. Proper handwashing, having a crucial effect on reducing bacteria, serves as the cornerstone of hand hygiene. For the elder with dementia, they suffer from a gradual loss of memory and difficulty in coordinating steps in the execution of handwashing. Proper assistance should be provided to them to ensure their hand hygiene adherence. Toward this end, we propose AWash, leveraging only commodity IMU sensor mounted on most wrist-worn devices (e.g., smartwatches) to characterize hand motions and provide assistance accordingly. To handle particular interference of senile dementia patients in IMU sensor readings, we design a number of effective techniques to segment handwashing actions, transform sensory input to body coordinate system, and extract sensor-body inclination angles. A hybrid neural network model is used to enable AWash to generalize to new users without retraining or adaptation, avoiding the trouble of collecting behavior information of every user. To meet the diverse needs of users with various executive functioning, we use a state machine to make prompt decisions, which supports customized assistance. Extensive experiments on a prototype with eight older participants demonstrate that AWash can increase the user's independence in the execution of handwashing.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488688"}, {"primary_key": "2186763", "vector": [], "sparse_vector": [], "title": "CanalScan: Tongue-Jaw Movement Recognition via Ear Canal Deformation Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Human-machine interface based on tongue-jaw movements has recently become one of the major technological trends. However, existing schemes have several limitations, such as requiring dedicated hardware and are usually uncomfortable to wear. This paper presents CanalScan, a nonintrusive system for tongue-jaw movement recognition using only commodity speaker and microphone mounted on ubiquitous off-the-shelf devices (e.g., smartphones). The basic idea is to send an acoustic signal, then captures its reflections and derive unique patterns of ear canal deformation caused by tongue-jaw movements. A dynamic segmentation method with Support Vector Domain Description is used to segment tongue-jaw movements. To combat sensor position-sensitive deficiency and ear-canal-shape-sensitive deficiency in multi-path reflections, we first design algorithms to assist users in adjusting the acoustic sensors to the same valid zone. Then we propose a data transformation mechanism to reduce the impacts of diversities in ear canal shapes and relative positions between sensors and the ear canal. CanalScan explores twelve unique and consistent features and applies a Random Forest classifier to distinguish tongue-jaw movements. Extensive experiments with twenty participants demonstrate that CanalScan achieves promising recognition for six tongue-jaw movements, is robust against various usage scenarios, and can be generalized to new users without retraining and adaptation.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488852"}, {"primary_key": "2186764", "vector": [], "sparse_vector": [], "title": "TrackSign: Guided Web Tracking Discovery.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Current web tracking practices pose a constant threat to the privacy of Internet users. As a result, the research community has recently proposed different tools to combat well-known tracking methods. However, the early detection of new, previously unseen tracking systems is still an open research problem. In this paper, we present TrackSign, a novel approach to discover new web tracking methods. The main idea behind TrackSign is the use of code fingerprinting to identify common pieces of code shared across multiple domains. To detect tracking fingerprints, TrackSign builds a novel 3-mode network graph that captures the relationship between fingerprints, resources and domains. We evaluated TrackSign with the top-100K most popular Internet domains, including almost 1M web resources from more than 5M HTTP requests. Our results show that our method can detect new web tracking resources with high precision (over 92%). TrackSign was able to detect 30K new trackers, more than 10K new tracking resources and 270K new tracking URLs, not yet detected by most popular blacklists. Finally, we also validate the effectiveness of TrackSign with more than 20 years of historical data from the Internet Archive.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488842"}, {"primary_key": "2186766", "vector": [], "sparse_vector": [], "title": "Blind Optimal User Association in Small-Cell Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Georgios S<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We learn optimal user association policies for traffic from different locations to Access Points(APs), in the presence of unknown dynamic traffic demand. We aim at minimizing a broad family of α-fair cost functions that express various objectives in load assignment in the wireless downlink, such as total load or total delay minimization. Finding an optimal user association policy in dynamic environments is challenging because traffic demand fluctuations over time are non-stationary and difficult to characterize statistically, which obstructs the computation of cost-efficient associations. Assuming arbitrary traffic patterns over time, we formulate the problem of online learning of optimal user association policies using the Online Convex Optimization (OCO) framework. We introduce a periodic benchmark for OCO problems that generalizes state-of-the-art benchmarks. We exploit inherent properties of the online user association problem and propose PerOnE, a simple online learning scheme that dynamically adapts the association policy to arbitrary traffic demand variations. We compare PerOnE against our periodic benchmark and prove that it enjoys the no-regret property, with additional sublinear dependence of the network size. To the best of our knowledge, this is the first work that introduces a periodic benchmark for OCO problems and a no-regret algorithm for the online user association problem. Our theoretical findings are validated through results on a real-trace dataset.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488867"}, {"primary_key": "2186767", "vector": [], "sparse_vector": [], "title": "LoFi: Enabling 2.4GHz LoRa and WiFi Coexistence by Detecting Extremely Weak Signals.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Jiamei Lv"], "summary": "Low-Power Wide Area Networks (LPWANs) emerges as attractive communication technologies to connect the Internet-of-Things. A new LoRa chip has been proposed to pro-vide long range and low power support on 2.4GHz. Comparing with previous LoRa radios operating on sub-gigahertz, the new one can transmit LoRa packets faster without strict channel duty cycle limitations and have attracted many attentions. Prior studies have shown that LoRa packets may suffer from severe corruptions with WiFi interference. However, there are many limitations in existing approaches such as too much signal processing overhead on weak devices or low detection accuracy. In this paper, we propose a novel weak signal detection approach, LoFi, to enable the coexistence of LoRa and WiFi. LoFi utilizes a typical physical phenomenon Stochastic Resonance (SR) to boost weak signals with a specific frequency by adding appropriate white noise. Based on the detected spectrum occupancy of LoRa signals, LoFi reserves the spectrum for LoRa transmissions. We implement LoFi on USRP N210 and conduct extensive experiments to evaluate its performance. Results show that LoFi can enable the coexistence of LoRa and WiFi in 2.4GHz. The packet reception ratio of LoRa achieves 98% over an occupied 20MHz WiFi channel, and the WiFi throughput loss is reduced by up to 13%.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488891"}, {"primary_key": "2186768", "vector": [], "sparse_vector": [], "title": "MTP: Avoiding Control Plane Overload with Measurement Task Placement.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Zhou", "<PERSON><PERSON>"], "summary": "In programmable networks, measurement tasks are placed on programmable switches to keep pace with high-speed traffic. At runtime, programmable switches send events to the control plane for further processing. However, existing solutions for task placement overlook the limitations of control plane resources. Thus, excessive events may overload the control plane. In this paper, we propose MTP, a system that eliminates control plane overload via careful task placement. For each task, MTP analyzes its structure to estimate its maximum possible rate of sending events to the control plane. Then it builds an optimization framework that addresses the resource restrictions of both switches and the control plane. We have implemented MTP on Barefoot Tofino switches. The experimental results indicate that MTP outperforms existing solutions with higher accuracy across four real use cases.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488732"}, {"primary_key": "2186769", "vector": [], "sparse_vector": [], "title": "Sequential Resource Access: Theory and Algorithm.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We formulate and analyze a generic sequential resource access problem arising in a variety of engineering fields, where a user disposes a number of heterogeneous computing, communication, or storage resources, each characterized by the probability of successfully executing the user's task and the related access delay and cost, and seeks an optimal access strategy to maximize her utility within a given time horizon, defined as the expected reward minus the access cost. We develop an algorithmic framework on the (near-)optimal sequential resource access strategy. We first prove that the problem of finding an optimal strategy is NP-hard in general. Given the hardness result, we present a greedy strategy implementable in linear time, and establish the closed-form sufficient condition for its optimality. We then develop a series of polynomial-time approximation algorithms achieving (ϵ, δ)-optimality. The key components in our design include a pruning process eliminating dominated strategies, thus maintaining polynomial time and space overhead, and a comprehensive scheme allowing flexibly trading-off time and space overhead against performance guarantee.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488892"}, {"primary_key": "2186770", "vector": [], "sparse_vector": [], "title": "Real-time Sampling and Estimation on Random Access Channels: Age of Information and Beyond.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Real-time sampling and estimation of autoregressive <PERSON>ov processes is considered in random access channels. Two classes of policies are studied: (i) oblivious policies in which decision making is independent of the source realizations, and (ii) non-oblivious policies in which sources are observed causally for decision making. In the first class, minimizing the expected time-average estimation error is equivalent to minimizing the expected age of information (AoI). Lower and upper bounds are provided for the achievable estimation error in this class and age-based threshold policies are shown to provide a two-fold improvement compared to the state-of-the-art. In the second class, an error-based threshold policy is proposed: a transmitter becomes active when its error exceeds a threshold in which case it transmits probabilistically following slotted ALOHA. A closed-form expression is derived for the estimation error as a function of the peak age, the transmission delay, a term which we call the silence delay, as well as the source realization. It is analyzed approximately by considering the underlying source as a discretized Wiener process. The proposed threshold policy provides a three-fold improvement compared to oblivious policies and its performance is close to that of centralized greedy scheduling.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488702"}, {"primary_key": "2186771", "vector": [], "sparse_vector": [], "title": "An Experience Driven Design for IEEE 802.11ac Rate Adaptation based on Reinforcement Learning.", "authors": ["Syuan<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "The IEEE 802.11ac supports gigabit speeds by extending 802.11n air-interface features and increases the number of rate options by more than two times. Enabling so many rate options can be a challenge to rate adaptation (RA) solutions. Particularly, they need to adapt rates to various fast-changing channels; they would suffer without scalability. In this work, we identify three limitations of current 802.11ac RAs on commodity network interface cards (NICs): no joint rate and bandwidth adaptation, lack of scalability, and no online learning capability. To address the limitations, we apply deep reinforcement learning (DRL) into designing a scalable, intelligent RA, designated as experience driven rate adaptation (EDRA). DRL enables the online learning capability of EDRA, which not only automatically identifies useful correlations between important factors and performance for the rate search, but also derives low-overhead avenues to approach highest-goodput (HG) rates by learning from experience. It can make EDRA scalable to timely locate HG rates among many rate options over time. We implement and evaluate EDRA using the Intel Wi-Fi driver and Google TensorFlow on Intel 802.11ac NICs. The evaluation result shows that EDRA can outperform the Intel and Linux default RAs by up to 821.4% and 242.8%, respectively, in various cases.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488876"}, {"primary_key": "2186774", "vector": [], "sparse_vector": [], "title": "Motion-Prediction-based Wireless Scheduling for Multi-User Panoramic Video Streaming.", "authors": ["<PERSON><PERSON>", "Xudong Qin", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Multi-user panoramic video streaming demands 4~6× bandwidth of a regular video with the same resolution, which poses a significant challenge on the wireless scheduling design to achieve desired performance. On the other hand, recent studies reveal that one can effectively predict the user's Field-of-View (FoV) and thus simply deliver the corresponding portion instead of the entire scenes. Motivated by this important fact, we aim to employ autoregressive process for motion prediction and analytically characterize the user's successful viewing probability as a function of the delivered portion. Then, we consider the problem of wireless scheduling design with the goal of maximizing application-level throughput (i.e., average rate for successfully viewing the desired content) and service regularity performance (i.e., how often each user gets successful views) subject to the minimum required service rate and wireless interference constraints. As such, we incorporate users' successful viewing probabilities into our scheduling design and develop a scheduling algorithm that not only asymptotically achieves the optimal application-level throughput but also provides service regularity guarantees. Finally, we perform simulations to demonstrate the efficiency of our proposed algorithm using a real dataset of users' head motion.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488771"}, {"primary_key": "2186775", "vector": [], "sparse_vector": [], "title": "Mobility- and Load-Adaptive Controller Placement and Assignment in LEO Satellite Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Software-defined networking (SDN) based LEO satellite networks can make full use of satellite resources through flexible function configuration and efficient resource management of controllers. Consequently, controllers have to be carefully deployed based on dynamical topology and time-varying workload. However, existing work on controller placement and assignment is not applicable to LEO satellite networks with highly dynamic topology and randomly fluctuating load. In this paper, we first formulate the adaptive controller placement and assignment (ACPA) problem and prove its NP-hardness. Then, we propose the control relation graph (CRG) to quantitatively capture the control overhead in LEO satellite networks. Next, we propose the CRG-based controller placement and assignment (CCPA) algorithm with a bounded approximation ratio. Finally, using the predicted topology and estimated traffic load, a lookahead-based improvement algorithm is designed to further decrease the overall management costs. Extensive emulation results demonstrate that the CCPA algorithm outperforms related schemes in terms of response time and load balancing.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488806"}, {"primary_key": "2186777", "vector": [], "sparse_vector": [], "title": "Popularity-Aware 360-Degree Video Streaming.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Tile-based streaming techniques have been widely used to save bandwidth in 360° video streaming. However, it is a challenge to determine the right tile size which directly affects the bandwidth usage. To address this problem, we propose to encode the video by considering the viewing popularity, where the popularly viewed areas are encoded as macrotiles to save bandwidth. We propose techniques to identify and build macrotiles, and adjust their sizes considering practical issues such as head movement randomness. In some cases, a user's viewing area may not be covered by the constructed macrotiles, and then the conventional tiling scheme is used. To support popularity-aware 360° video streaming, the client selects the right tiles (a macrotile or a set of conventional tiles) with the right quality level to maximize the QoE under bandwidth constraint. We formulate this problem as an optimization problem which is NP-hard, and then propose a heuristic algorithm to solve it. Through extensive evaluations based on real traces, we demonstrate that the proposed algorithm can significantly improve the QoE and save the bandwidth usage.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488856"}, {"primary_key": "2186778", "vector": [], "sparse_vector": [], "title": "Time-Varying Resource Graph Based Resource Model for Space-Terrestrial Integrated Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Zhetao Li", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "It is critical but difficult to efficiently model re-sources in space-terrestrial integrated networks (STINs). Existing work is not applicable to STINs because they lack the joint consideration of different movement patterns and fluctuating loads. In this paper, we propose the time-varying resource graph (TVRG) to model STINs from the resource perspective. Firstly, we propose the STIN mobility model to uniformly model different movement patterns in STINs. Then, we propose a layered Resource Modeling and Abstraction (RMA) approach, where evolutions of node resources are modeled as Markov processes, by encoding predictable topologies and influences of fluctuating loads as states. Besides, we propose the low-complexity domain resource abstraction algorithm by defining two mobility-based and load-aware partial orders on resource abilities. Finally, we propose an efficient TVRG-based Resource Scheduling (TRS) algorithm for time-sensitive and bandwidth-intensive data flows, with the multi-level on-demand scheduling ability. Comprehensive simulation results demonstrate that the RMA-TRS outperforms related schemes in terms of throughput, end-to-end delay and flow completion time.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488855"}, {"primary_key": "2186779", "vector": [], "sparse_vector": [], "title": "mCore: Achieving Sub-millisecond Scheduling for 5G MU-MIMO Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "MU-MIMO technology enables a base station (BS) to transmit signals to multiple users simultaneously on the same frequency band. It is a key technology for 5G NR to increase the data rate. In 5G specifications, an MU-MIMO scheduler needs to determine RBs allocation and MCS assignment to each user for each TTI. Under MU-MIMO, multiple users may be coscheduled on the same RB and each user may have multiple data streams simultaneously. In addition, the scheduler must meet the stringent real-time requirement (~1 ms) during decision making to be useful. This paper presents mCore, a novel 5G scheduler that can achieve ~1 ms scheduling with joint optimization of RB allocation and MCS assignment to MU-MIMO users. The key idea of mCore is to perform a multi-phase optimization, leveraging large-scale parallel computation. In each phase, m<PERSON>ore either decomposes the optimization problem into a number of independent sub-problems, or reduces the search space into a smaller but most promising subspace, or both. We implement mCore on a commercial-off-the-shelf GPU. Experimental results show that mCore can offer the best scheduling performance for up to 100 RBs, 100 users, 29 MCS levels and 4 × 12 antennas when compared to other state-of-the-art algorithms. It is also the only algorithm that can find its scheduling solution in ~1 ms.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488684"}, {"primary_key": "2186780", "vector": [], "sparse_vector": [], "title": "Bringing Fairness to Actor-Critic Reinforcement Learning for Network Utility Optimization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Fairness is a crucial design objective in virtually all network optimization problems, where limited system resources are shared by multiple agents. Recently, reinforcement learning has been successfully applied to autonomous online decision making in many network design and optimization problems. However, most of them try to maximize the long-term (discounted) reward of all agents, without taking fairness into account. In this paper, we propose a family of algorithms that bring fairness to actor-critic reinforcement learning for optimizing general fairness utility functions. In particular, we present a novel method for adjusting the rewards in standard reinforcement learning by a multiplicative weight depending on both the shape of fairness utility and some statistics of past rewards. It is shown that for proper choice of the adjusted rewards, a policy gradient update converges to at least a stationary point of general αfairness utility optimization. It inspires the design of fairness optimization algorithms in actor-critic reinforcement learning. Evaluations show that the proposed algorithm can be easily deployed in real-world network optimization problems, such as wireless scheduling and video QoE optimization, and can significantly improve the fairness utility value over previous heuristics and learning algorithms.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488823"}, {"primary_key": "2186781", "vector": [], "sparse_vector": [], "title": "A Universal Transcoding and Transmission Method for Livecast with Networked Multi-Agent Reinforcement Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Intensive video transcoding and data transmission are the most crucial tasks for large-scale Crowd-sourced Livecast Services (CLS). However, there exists no versatile model for joint optimization of computing resources (e.g., CPU) and transmission resources (e.g., bandwidth) in CLS systems, making maintaining the balance between saving resources and improving user viewing experience very challenging. In this paper, we first propose a novel universal model, called Augmented Graph Model (AGM), which converts the above joint optimization into a multi-hop routing problem. This model provides a new perspective for the analysis of resource allocation in CLS, as well as opens new avenues for problem-solving. Further, we design a decentralized Networked Multi-Agent Reinforcement Learning (MARL) approach and propose an actor-critic algorithm, allowing network nodes (agents) to distributively solve the multi-hop routing problem using AGM in a fully cooperative manner. By leveraging the computing resource of massive nodes efficiently, this approach has good scalability and can be employed in large-scale CLS. To the best of our knowledge, this work is the first attempt to apply networked MARL on CLS. Finally, we use the centralized (single-agent) RL algorithm as a benchmark to evaluate the numerical performance of our solution in a large-scale simulation. Additionally, experimental results based on a prototype system show that our solution is superior in saving resources and service performance to two alternative state-of-the-art solutions.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488868"}, {"primary_key": "2186782", "vector": [], "sparse_vector": [], "title": "Dynamically Choosing the Candidate Algorithm with Ostasos in Online Optimization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The increasing challenge in designing online algorithms lies in the distribution uncertainty. To cope with the distribution variations in online optimization, an intuitive idea is to reselect an algorithm from the candidate set that will be more suitable to future distributions. In this paper, we propose Ostasos, an automatic algorithm selection framework that can choose the most suitable algorithm on the fly with provable guarantees. Rigorous theoretical analysis demonstrates that the performance of Ostasos is no worse than that of any candidate algorithms in terms of competitive ratio. Finally, we apply Ostasos to the online car-hailing problem and trace-driven experiments verify the effectiveness of Ostasos.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488692"}, {"primary_key": "2186783", "vector": [], "sparse_vector": [], "title": "Push the Limit of Device-Free Acoustic Sensing on Commercial Mobile Devices.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Device-free acoustic sensing has obsessed with renovating human-computer interaction techniques for all-sized mobile devices in various applications. Recent advances have explored sound signals in different methods to achieve highly accurate and efficient tracking and recognition. However, accuracies of most approaches remain bottlenecked by the limited sampling rate and narrow bandwidth, leading to restrictions and inconvenience in applications. To bridge over the aforementioned daunting barriers, we propose PDF, a novel ultrasound-based device-free tracking scheme that can distinctly improve the resolution of fine-grained sensing to submillimetre level. In its heart lies an original Phase Difference based approach to derive time delay of the reflected Frequency-Modulated Continuous Wave (FMCW), thus precisely inferring absolute distance, catering to interaction needs of tinier perception with lower delay. The distance resolution of PDF is only related to the speed of actions and chirp duration. We implement a prototype with effective denoising methods all in the time domain on smartphones. The evaluation results show that PDF achieves accuracies of 2.5 mm, 3.6 mm, and 2.1 mm in distance change, absolute distance change, and trajectory tracking error respectively. PDF is also valid in recognizing 2 mm or even tinier micro-movements, which paves the way for more delicate sensing work.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488703"}, {"primary_key": "2186784", "vector": [], "sparse_vector": [], "title": "Looking for the Maximum Independent Set: A New Perspective on the Stable Path Problem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Ruzica Piskac", "<PERSON><PERSON>"], "summary": "The stable path problem (SPP) is a unified model for analyzing the convergence of distributed routing protocols (e.g., BGP), and a foundation for many network verification tools. Although substantial progress has been made on finding solutions (i.e., stable path assignments) for particular subclasses of SPP instances and analyzing the relation between properties of SPP instances and the convergence of corresponding routing policies, the non-trivial challenge of finding stable path assignments to generic SPP instances still remains. Tackling this challenge is important because it can enable multiple important, novel routing use cases. To fill this gap, in this paper we introduce a novel data structure called solvability digraph, which encodes key properties about stable path assignments in a compact graph representation. Thus SPP is equivalently transformed to the problem of finding in the solvability digraph a maximum independent set (MIS) of size equal to the number of autonomous systems (ASes) in the given SPP instance. We leverage this key finding to develop a heuristic polynomial algorithm GREEDYMIS that solves strictly more SPP instances than state-of-the-art heuristics. We apply GREEDYMIS to designing two important, novel use cases: (1) a centralized interdomain routing system that uses GREEDYMIS to compute paths for ASes and (2) a secure multi-party computation (SMPC) protocol that allows ASes to use GREEDYMIS collaboratively to compute paths without exposing their routing preferences. We demonstrate the benefits and efficiency of these use cases via evaluation using real-world datasets.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488682"}, {"primary_key": "2186785", "vector": [], "sparse_vector": [], "title": "Launching Smart Selective Jamming Attacks in WirelessHART Networks.", "authors": ["<PERSON><PERSON>", "Junyang Shi", "<PERSON>", "<PERSON><PERSON>"], "summary": "As a leading industrial wireless standard, WirelessHART has been widely implemented to build wireless sensor-actuator networks (WSANs) in industrial facilities, such as oil refineries, chemical plants, and factories. For instance, 54,835 WSANs that implement the WirelessHART standard have been deployed globally by Emerson process management, a WirelessHART network supplier, to support process automation. While the existing research to improve industrial WSANs focuses mainly on enhancing network performance, the security aspects have not been given enough attention. We have identified a new threat to WirelessHART networks, namely smart selective jamming attacks, where the attacker first cracks the channel usage, routes, and parameter configuration of the victim network and then jams the transmissions of interest on their specific communication channels in their specific time slots, which makes the attacks energy efficient and hardly detectable. In this paper, we present this severe, stealthy threat by demonstrating the step-by-step attack process on a 50-node network that runs a publicly accessible WirelessHART implementation. Experimental results show that the smart selective jamming attacks significantly reduce the network reliability without triggering network updates.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488805"}, {"primary_key": "2186786", "vector": [], "sparse_vector": [], "title": "Optimal Multicast Scheduling for Millimeter Wave Networks Leveraging Directionality and Reflections.", "authors": ["In-<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We investigate the minimum-delay multicast problem for millimeter wave (mmWave) networks. Salient characteristics of mmWave links, directionality and reflections, are considered under sectored antenna model. We first consider directionality only, and identify the property such that the optimal policy can be recursively partitioned into smaller sizes. Using such optimal substructure, we propose an iterative method based on graphs which finds the optimal schedule in polynomial time. Next, we extend our model to incorporate reflections. We introduce the concept of path diversity which states that the availability of reflected paths enables opportunistic reduction of multicast delay. We prove NP-hardness of the problem, and propose approximations with performance bounds and heuristics of reduced complexity. By simulation we show the outperformance of our method over conventional ones, and numerically characterize the gain of path diversity in terms of network size.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488427"}, {"primary_key": "2186787", "vector": [], "sparse_vector": [], "title": "Joint Age of Information and Self Risk Assessment for Safer 802.11p based V2V Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Emerging 802.11p vehicle-to-vehicle (V2V) networks rely on periodic Basic Safety Messages (BSMs) to disseminate time-sensitive safety-critical information, such as vehicle position, speed, and heading - that enables several safety applications and has the potential to improve on-road safety. Due to mobility, lack of global-knowledge and limited communication resources, designing an optimal BSM broadcast rate-control protocol is challenging. Recently, minimizing Age of Information (AoI) has gained momentum in designing BSM broadcast rate-control protocols. In this paper, we show that minimizing AoI solely does not always improve the safety of V2V networks. Specifically, we propose a novel metric, termed Trackability-aware Age of Information TAoI, that in addition to AoI, takes into account the self risk assessment of vehicles, quantified in terms of self tracking error (self-TE) - which provides an indication of collision risk posed by the vehicle. Self-TE is defined as the difference between the actual location of a certain vehicle and its self-estimated location. Our extensive experiments, based on realistic SUMO traffic traces on top of ns-3 simulator, demonstrate that TAoI based rate-protocol significantly outperforms baseline AoI based rate protocol and default 10 Hz broadcast rate in terms of safety performance, i.e., collision risk, in all considered V2V settings.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488864"}, {"primary_key": "2186788", "vector": [], "sparse_vector": [], "title": "Practical Analysis of Replication-Based Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Task replication has been advocated as a practical solution to reduce response times in parallel systems. The analysis of replication-based systems typically rests on some strong assumptions: Poisson arrivals, exponential service times, or independent service times of the replicas. This study is motivated not only by several studies which indicate that these assumptions are unrealistic, but also by some elementary observations highlighting some contriving behaviour. For instance, when service times are not exponential, adding a replication factor can stabilize an unstable system, i.e., having infinite delays, but a tempting higher replication factor can push the system back in a perilous unstable state. This behaviour disappears however if the replicas are sufficiently correlated, in which case any replication factor would even be detrimental.Motivated by the need to dispense with such common yet unrealistic and misleading assumptions, we provide a robust theoretical framework to compute stochastic bounds on response time distributions in general replication systems subject to Markovian arrivals, quite general service times, and correlated replicas. Numerical results show that our bounds are accurate and improve state-of-the-art bounds in the case of Markovian arrivals by as much as three orders of magnitude. We apply our results to a practical application and highlight that correctly setting the replication factor crucially depends on both the service time distributions of the replicas and the degree of correlation amongst.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488829"}, {"primary_key": "2186789", "vector": [], "sparse_vector": [], "title": "Self-adjusting Advertisement of Cache Indicators with Bandwidth Constraints.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Cache advertisements reduce the access cost by allowing users to skip the cache when it does not contain their datum. Such advertisements are used in multiple networked domains such as 5G networks, wide area networks, and information-centric networking. The selection of an advertisement strategy exposes a trade-off between the access cost and bandwidth consumption. Still, existing works mostly apply a trial-and-error approach for selecting the best strategy, as the rigorous foundations required for optimizing such decisions is lacking.Our work shows that the desired advertisement policy depends on numerous parameters such as the cache policy, the workload, the cache size, and the available bandwidth. In particular, we show that there is no ideal single configuration. Therefore, we design an adaptive, self-adjusting algorithm that periodically selects an advertisement policy. Our algorithm does not require any prior information about the cache policy, cache size, or work-load, and does not require any apriori configuration. Through extensive simulations, using several state-of-the-art cache policies, and real workloads, we show that our approach attains a similar cost to that of the best static configuration (which is only identified in retrospect) in each case.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488680"}, {"primary_key": "2186791", "vector": [], "sparse_vector": [], "title": "6GAN: IPv6 Multi-Pattern Target Generation via Generative Adversarial Nets with Reinforcement Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Peipei Fu", "<PERSON><PERSON>"], "summary": "Global IPv6 scanning has always been a challenge for researchers because of the limited network speed and computational power. Target generation algorithms are recently proposed to overcome the problem for Internet assessments by predicting a candidate set to scan. However, IPv6 custom address configuration emerges diverse addressing patterns discouraging algorithmic inference. Widespread IPv6 alias could also mislead the algorithm to discover aliased regions rather than valid host targets. In this paper, we introduce 6GAN, a novel architecture built with Generative Adversarial Net (GAN) and reinforcement learning for multi-pattern target generation. 6GAN forces multiple generators to train with a multi-class discriminator and an alias detector to generate non-aliased active targets with different addressing pattern types. The rewards from the discriminator and the alias detector help supervise the address sequence decision-making process. After adversarial training, 6GAN's generators could keep a strong imitating ability for each pattern and 6GAN's discriminator obtains outstanding pattern discrimination ability with a 0.966 accuracy. Experiments indicate that our work outperformed the state-of-the-art target generation algorithms by reaching a higher-quality candidate set.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488912"}, {"primary_key": "2186792", "vector": [], "sparse_vector": [], "title": "ShakeReader: &apos;Read&apos; UHF RFID using Smartphone.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Yuan<PERSON> Zheng", "Jinsong Han"], "summary": "UHF RFID technology becomes increasingly popular in RFID-enabled stores (e.g., UNIQLO), since UHF RFID readers can quickly read a large number of RFID tags from afar. The deployed RFID infrastructure, however, does not directly benefit smartphone users in the stores, mainly because smartphones cannot read UHF RFID tags or fetch relevant information (e.g., updated price, real-time promotion). This paper aims to bridge the gap and allow users to `read' UHF RFID tags using their smartphones, without any hardware modification to either deployed RFID systems or smartphone hardware. To `read' an interested tag, a user makes a pre-defined smartphone gesture in front of an interested tag. The smartphone gesture causes changes in 1) RFID measurement data (e.g., phase) captured by RFID infrastructure, and 2) motion sensor data (e.g., accelerometer) captured by the user's smartphone. By matching the two data, our system (named ShakeReader) can pair the interested tag with the corresponding smartphone, thereby enabling the smartphone to indirectly `read' the interested UHF tag. We build a novel reflector polarization model to analyze the impact of smartphone gesture to RFID backscattered signals. Experimental results show that ShakeReader can accurately pair interested tags with their corresponding smartphones with an accuracy of >94.6%.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488802"}, {"primary_key": "2186793", "vector": [], "sparse_vector": [], "title": "Can You Fix My Neural Network? Real-Time Adaptive Waveform Synthesis for Resilient Wireless Signal Classification.", "authors": ["Salvatore D&apos;Oro", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Due to the sheer scale of the Internet of Things (IoT) and 5G, the wireless spectrum is becoming severely congested. For this reason, wireless devices will need to continuously adapt to current spectrum conditions by changing their communication parameters in real-time. Therefore, wireless signal classification (WSC) will become a compelling necessity to decode fast-changing signals from dynamic transmitters. Thanks to its capability of classifying complex phenomena without explicit mathematical modeling, deep learning (DL) has been demonstrated to be a key enabler of WSC. Although DL can achieve a very high accuracy under certain conditions, recent research has unveiled that the wireless channel can disrupt the features learned by the DL model during training, thus drastically reducing the classification performance in real-world live settings. Since retraining classifiers is cumbersome after deployment, existing work has leveraged the usage of carefully-tailored Finite Impulse Response (FIR) filters that, when applied at the transmitter's side, can restore the features that are lost because of the the channel actions, i.e., waveform synthesis. However, these approaches compute FIRs using offline optimization strategies, which limits their efficacy in highly-dynamic channel settings. In this paper, we improve the state of the art by proposing <PERSON><PERSON>, a Deep Reinforcement Learning (DRL)-based framework for channel-resilient adaptive waveform synthesis. <PERSON><PERSON> adapts to new and unseen channel conditions by optimally computing through DRL the FIRs in real time. <PERSON><PERSON> is a DRL agent whose architecture is based upon the Twin Delayed Deep Deterministic Policy Gradients (TD3), which requires minimal feedback from the receiver and explores a continuous action space for best performance. <PERSON><PERSON> has been extensively evaluated on two well-known datasets with an extensive number of channels. We have also evaluated the real-time latency of Chares with an implementation on field-programmable gate array (FPGA). Results show that Chares increases the accuracy up to 4.1x when no waveform synthesis is performed, by 1.9x with respect to existing work, and can compute new actions within 41 μs.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488865"}, {"primary_key": "2186796", "vector": [], "sparse_vector": [], "title": "Asynchronous Deep Reinforcement Learning for Data-Driven Task Offloading in MEC-Empowered Vehicular Networks.", "authors": ["Penglin Dai", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mobile edge computing (MEC) has been an effective paradigm to support real-time computation-intensive vehicular applications. However, due to highly dynamic vehicular topology, these existing centralized-based or distributed-based scheduling algorithms requiring high communication overhead, are not suitable for task offloading in vehicular networks. Therefore, we investigate a novel service scenario of MEC-based vehicular crowdsourcing, where each MEC server is an independent agent and responsible for making scheduling of processing traffic data sensed by crowdsourcing vehicles. On this basis, we formulate a data-driven task offloading problem by jointly optimizing offloading decision and bandwidth/computation resource allocation, and renting cost of heterogeneous servers, such as powerful vehicles, MEC servers and cloud, which is a mixed-integer programming problem and NP-hard. To reduce high time-complexity, we propose the solution in two stages. First, we design an asynchronous deep Q-learning to determine offloading decision, which achieves fast convergence by training the local DQN model at each agent in parallel and uploading for global model update asynchronously. Second, we decompose the remaining resource allocation problem into several independent subproblems and derive optimal analytic formula based on convex theory. Lastly, we build a simulation model and conduct comprehensive simulation, which demonstrates the superiority of the proposed algorithm.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488886"}, {"primary_key": "2186797", "vector": [], "sparse_vector": [], "title": "Mobile Crowdsensing for Data Freshness: A Deep Reinforcement Learning Approach.", "authors": ["<PERSON>ipeng <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Data collection by mobile crowdsensing (MCS) is emerging as data sources for smart city applications, however how to ensure data freshness has sparse research exposure but quite important in practice. In this paper, we consider to use a group of mobile agents (MAs) like UAVs and driverless cars which are equipped with multiple antennas to move around in the task area to collect data from deployed sensor nodes (SNs). Our goal is to minimize the age of information (AoI) of all SNs and energy consumption of MAs during movement and data upload. To this end, we propose a centralized deep reinforcement learning (DRL)-based solution called \"DRL-freshMCS\" for controlling MA trajectory planning and SN scheduling. We further utilize implicit quantile networks to maintain the accurate value estimation and steady policies for MAs. Then, we design an exploration and exploitation mechanism by dynamic distributed prioritized experience replay. We also derive the theoretical lower bound for episodic AoI. Extensive simulation results show that DRL-freshMCS significantly reduces the episodic AoI per remaining energy, compared to five baselines when varying different number of antennas and data upload thresholds, and number of SNs. We also visualize their trajectories and AoI update process for clear illustrations.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488791"}, {"primary_key": "2186799", "vector": [], "sparse_vector": [], "title": "Uplink Multi-User Beamforming on Single RF Chain mmWave WLANs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Today's mmWave WLANs can realize simultaneous multi-user multi-stream transmission solely on the downlink. In this paper, we present Uplink Multi-user Beamforming on single RF chain AP (UMBRA), a novel framework for supporting multi-stream multi-user uplink transmissions via a single RF chain. We design multi-user overlayed constellations and multi-user receiver mechanisms to enable concurrent time-triggered uplink multi-user transmissions received on a single RF chain AP. We devise exemplary beam selection policies to jointly adapt beams at users and the AP for targeting aggregate rate maximization without increasing training requirements compared to single-user systems. We implement the key components of UMBRA using a programmable WLAN testbed using software-defined radios and commercial 60-GHz transceivers and collect over-the-air measurements using phased-array antennas and horn antennas with varying beamwidth. We find that in comparison to single-user transmissions, UMBRA achieves more than 1.45× improvement in aggregate rate regardless of the choice of the user group, geometric separation, and receiver beamwidth.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488826"}, {"primary_key": "2186802", "vector": [], "sparse_vector": [], "title": "PCL: Packet Classification with Limited Knowledge.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We introduce a novel representation of packet classifiers allowing to operate on partially available input data varying dynamically. For a given packet classifier, availability of fields or complexity of field computations, and free target specific resources, the proposed infrastructure computes a classifier representation satisfying performance and robustness requirements. We show the feasibility to reconstruct a classification result in this noisy environment, allowing for the improvement of performance and the achievement of additional robustness levels of network infrastructure. Our results are supported by extensive evaluations in various settings where only a partial input is available.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488751"}, {"primary_key": "2186803", "vector": [], "sparse_vector": [], "title": "FAIR: Quality-Aware Federated Learning with Precise User Incentive and Model Aggregation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Federated learning enables distributed learning in a privacy-protected manner, but two challenging reasons can affect learning performance significantly. First, mobile users are not willing to participate in learning due to computation and energy consumption. Second, with various factors (e.g., training data size/quality), the model update quality of mobile devices can vary dramatically, inclusively aggregating low-quality model updates can deteriorate the global model quality. In this paper, we propose a novel system named FAIR, i.e., Federated leArning with qualIty awaReness. FAIR integrates three major components: 1) learning quality estimation: we leverage historical learning records to estimate the user learning quality, where the record freshness is considered and the exponential forgetting function is utilized for weight assignment; 2) quality-aware incentive mechanism: within the recruiting budget, we model a reverse auction problem to encourage the participation of high-quality learning users, and the method is proved to be truthful, individually rational, and computationally efficient; and 3) model aggregation: we devise an aggregation algorithm that integrates the model quality into aggregation and filters out non-ideal model updates, to further optimize the global learning model. Based on real-world datasets and practical learning tasks, extensive experiments are carried out to demonstrate the efficacy of FAIR.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488743"}, {"primary_key": "2186805", "vector": [], "sparse_vector": [], "title": "NFReducer: Redundant Logic Elimination for Network Functions with Runtime Configurations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Network functions (NFs) are critical components in the network data plane. Their efficiency is important to the whole network's end-to-end performance. We identify three types of runtime redundant logic in individual NF and NF chains when they are deployed with concrete configured rules. We use program analysis techniques to optimize away the redundancy where we also overcome the NF specific challenges - we combine symbolic execution and dead code elimination to eliminate unused logic, we customize the common sub-expression elimination to eliminate duplicated logic, and we add network semantics to the dead code elimination to eliminate overwritten logic. We implement a prototype named NFReducer using LLVM. Our evaluation on both legacy and platform NFs shows that after eliminating the redundant logic, the packet processing rate of the NFs can be significantly improved and the operational overhead is small.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488903"}, {"primary_key": "2186806", "vector": [], "sparse_vector": [], "title": "Analyzing Learning-Based Networked Systems with Formal Verification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "As more applications of (deep) neural networks emerge in the computer networking domain, the correctness and predictability of a neural agent's behavior for corner case inputs are becoming crucial. Enabling the formal analysis of agents with nontrivial properties, we bridge between specifying intended high-level behavior and expressing low-level statements directly encoded into an efficient verification framework. Our results support that within minutes, one can establish the resilience of a neural network to adversarial attacks on its inputs, as well as formally prove properties that were previously relying on educated guesses. Finally, we also show how formal verification can help create an accurate visual representation of an agent behavior to perform visual inspection and improve its trustworthiness.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488898"}, {"primary_key": "2186808", "vector": [], "sparse_vector": [], "title": "Adaptive Clustering-based Malicious Traffic Classification at the Network Edge.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The rapid uptake of digital services and Internet of Things (IoT) technology gives rise to unprecedented numbers and diversification of cyber attacks, with which commonly-used rule-based Network Intrusion Detection Systems (NIDSs) are struggling to cope. Therefore, Artificial Intelligence (AI) is being exploited as second line of defense, since this methodology helps in extracting non-obvious patterns from network traffic and subsequently in detecting more confidently new types of threats. Cybersecurity is however an arms race and intelligent solutions face renewed challenges as attacks evolve while network traffic volumes surge. In this paper, we propose Adaptive Clustering-based Intrusion Detection (Acid), a novel approach to malicious traffic classification and a valid candidate for deployment at the network edge. Acid addresses the critical challenge of sensitivity to subtle changes in traffic features, which routinely leads to misclassification. We circumvent this problem by relying on low-dimensional embeddings learned with a lightweight neural model comprising multiple kernel networks that we introduce, which optimally separates samples of different classes. We empirically evaluate our approach with both synthetic and three intrusion detection datasets spanning 20 years, and demonstrate <PERSON>cid consistently attains 100% accuracy and F1-score, and 0% false alarm rate, thereby significantly outperforming state-of-the-art clustering methods and NIDSs.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488690"}, {"primary_key": "2186809", "vector": [], "sparse_vector": [], "title": "Incentive Mechanism Design for Distributed Coded Machine Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A distributed machine learning platform needs to recruit many heterogeneous worker nodes to finish computation simultaneously. As a result, the overall performance may be degraded due to straggling workers. By introducing redundancy into computation, coded machine learning can effectively improve the runtime performance by recovering the final computation result through the first k (out of the total n) workers who finish computation. While existing studies focus on designing efficient coding schemes, the issue of designing proper incentives to encourage worker participation is still under-explored. This paper studies the platform's optimal incentive mechanism for motivating proper workers' participation in coded machine learning, despite the incomplete information about heterogeneous workers' computation performances and costs. A key contribution of this work is to summarize workers' multi-dimensional heterogeneity as a one-dimensional metric, which guides the platform's efficient selection of workers under incomplete information with a linear computation complexity. Moreover, we prove that the optimal recovery threshold k is linearly proportional to the participator number n if we use the widely adopted MDS codes for data encoding. We also show that the platform's increased cost due to incomplete information disappears when worker number is sufficiently large, but it does not monotonically decrease in worker number.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488672"}, {"primary_key": "2186811", "vector": [], "sparse_vector": [], "title": "Multi-Agent Reinforcement Learning for Urban Crowd Sensing with For-Hire Vehicles.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>ming Jin", "<PERSON><PERSON><PERSON>"], "summary": "Recently, vehicular crowd sensing (VCS) that leverages sensor-equipped urban vehicles to collect city-scale sensory data has emerged as a promising paradigm for urban sensing. Nowadays, a wide spectrum of VCS tasks are carried out by for-hire vehicles (FHVs) due to various hardware and software constraints that are difficult for private vehicles to satisfy. However, such FHV-enabled VCS systems face a fundamental yet unsolved problem of striking a balance between the order-serving and sensing outcomes. To address this problem, we propose a novel graph convolutional cooperative multi-agent reinforcement learning (GCC-MARL) framework, which helps FHVs make distributed routing decisions that cooperatively optimize the system-wide global objective. Specifically, GCC-MARL meticulously assigns credits to agents in the training process to effectively stimulate cooperation, represents agents' actions by a carefully chosen statistics to cope with the variable agent scales, and integrates graph convolution to capture useful spatial features from complex large-scale urban road networks. We conduct extensive experiments with a real-world dataset collected in Shenzhen, China, containing around 1 million trajectories and 50 thousand orders of 553 taxis per-day from June 1st to 30th, 2017. Our experiment results show that GCC-MARL outperforms state-of-the-art baseline methods in order-serving revenue, as well as sensing coverage and quality.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488713"}, {"primary_key": "2186812", "vector": [], "sparse_vector": [], "title": "Competing Epidemics on Graphs - Global Convergence and Coexistence.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The dynamics of the spread of contagions such as viruses, infectious diseases or even rumors/opinions over contact networks (graphs) have effectively been captured by the well known Susceptible-Infected-Susceptible (SIS) epidemic model in recent years. When it comes to competition between two such contagions spreading on overlaid graphs, their propagation is captured by so-called bi-virus epidemic models. Analysis of such dynamical systems involve the identification of equilibrium points and its convergence properties, which determine whether either of the viruses dies out, or both survive together. We demonstrate how the existing works are unsuccessful in characterizing a large subset of the model parameter space, including all parameters for which the competitiveness of the bi-virus system is significant enough to attain coexistence of the epidemics. In this paper, we fill in this void and obtain convergence results for the entirety of the model parameter space; giving precise conditions (necessary and sufficient) under which the system globally converges to a trichotomy of possible outcomes: a virus-free state, a single-virus state, and to a coexistence state - the first such result.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488828"}, {"primary_key": "2186817", "vector": [], "sparse_vector": [], "title": "Multicast Communications with Varying Bandwidth Constraints.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>om", "<PERSON><PERSON><PERSON>"], "summary": "To find a maximum number of communication requests that can be satisfied concurrently, is a fundamental network scheduling problem. In this work we investigate the problem of finding a maximum number of multicast requests that can be scheduled simultaneously in a tree network in which the edges and links have heterogeneous bandwidth limitations.This problem generalizes two problems studied in the literature: maximum k-colorable subgraph in chordal graphs, maximum multi-commodity flow in trees. The problem is NP-hard and admits a 1.585-approximation in the special case of homogeneous bandwidth limitations.We first show that the problem is harder to approximate when the bandwidth limitations are heterogeneous, i.e. vary from link to link and from node to node. We then generalize of a classical algorithm and obtain an M-approximation where M is the maximum number of leaves of the communication subtrees. Surprisingly, variants of the same algorithm, are used in the literature at least four times to solve related problems. There exists a polynomial-time algorithm for the special case of unicast requests and star topology. We generalize this result and relax the second requirement so that the set of unicast requests share a common vertex with no restriction on the tree topology.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488869"}, {"primary_key": "2186818", "vector": [], "sparse_vector": [], "title": "On the Reliability of IEEE 802.1CB FRER.", "authors": ["Dog<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The introduction of IEEE Time-sensitive Networking (TSN) enables the design of real-time and mission-critical networks based on Ethernet technologies. Apart from providing necessary tools for near-deterministic scheduling, TSN comes with further functionalities for configurability, security, and reliability. IEEE 802.1CB Frame Replication and Elimination (FRER) is the only protocol in the TSN toolbox for adding fault-tolerance via sending the same packets via redundant paths. Although its core functions are defined by the standard, its effective use mainly depends on the actual deployment scenario and the path selection strategy. In this paper, we show that FRER can induce unintentional elimination of packets packets when the paths chosen for a particular packet flow are non-disjoint. We propose the new metric reassurance that can be used in FRER path selection. Besides, we propose an additional enhancement to FRER that can prevent unintended packet eliminations independent from the deployment scenario. Our simulation results indicate that the reassurance-based path selection performs better than random or maximum-disjoint path selection in random failure scenarios.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488750"}, {"primary_key": "2186819", "vector": [], "sparse_vector": [], "title": "Ruledger: Ensuring Execution Integrity in Trigger-Action IoT Platforms.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Smart home IoT systems utilize trigger-action platforms, e.g., IFTTT, to manage devices from various vendors. These platforms allow users to define rules for automatically triggering operations on devices. However, they may be abused by triggering malicious rule execution with forged IoT devices or events violating the execution integrity and the intentions of the users. To address this issue, we propose a ledger based IoT platform called Ruledger, which ensures the correct execution of rules by verifying the authenticity of the corresponding information. Ruledger utilizes smart contracts to enforce verifying the information associated with rule executions, e.g., the user and configuration information from users, device events, and triggers in the trigger-action platforms. In particular, we develop three algorithms to enable ledger-wallet based applications for Ruledger and guarantee that the records used for verification are stateful and correct. Thus, the execution integrity of rules is ensured even if devices and platforms in the smart home systems are compromised. We prototype Ruledger in a real IoT platform, i.e., IFTTT, and evaluate the performance with various settings. The experimental results demonstrate Ruledger incurs an average of 12.53% delay, which is acceptable for smart home systems.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488687"}, {"primary_key": "2186821", "vector": [], "sparse_vector": [], "title": "Towards Fine-Grained Spatio-Temporal Coverage for Vehicular Urban Sensing Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>ming Jin", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Vehicular urban sensing (VUS), which uses sensors mounted on crowdsourced vehicles or on-board drivers' smartphones, has become a promising paradigm for monitoring critical urban metrics. Due to various hardware and software constraints difficult for private vehicles to satisfy, for-hire vehicles (FHVs) are usually the major forces for VUS systems. However, FHVs alone are far from enough for fine-grained spatio-temporal sensing coverage, because of their severe distribution biases. To address this issue, we propose to use a hybrid approach, where a centralized platform not only leverages FHVs to conduct sensing tasks during their daily movements of serving passenger orders, but also controls multiple dedicated sensing vehicles (DSVs) to bridge FHVs' coverage gaps. Specifically, we aim to achieve fine-grained spatio-temporal sensing coverage at the minimum long-term operational cost by systematically optimizing the repositioning policy for DSVs. Technically, we formulate the problem as a stochastic dynamic program, and solve various challenges, including long-term cost minimization, stochastic demand with partial statistical knowledge, and computational intractability, by integrating distributionally robust optimization, primal-dual transformation, and second order conic programming methods. We validate the effectiveness of our methods using a real-world dataset from Shenzhen, China, containing 726,000 trajectories of 3848 taxis spanning overall 1 month in 2017.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488787"}, {"primary_key": "2186826", "vector": [], "sparse_vector": [], "title": "Bound Inference and Reinforcement Learning-based Path Construction in Bandwidth Tomography.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Inferring the bandwidth of internal links from the bandwidth of end-to-end paths, so-termed bandwidth tomography, is a long-standing open problem in the network tomography literature. The difficulty is due to the fact that no existing mathematical tool is directly applicable to solve the inverse problem with a set of min-equations. We systematically tackle this challenge by designing a polynomial-time algorithm that returns the exact bandwidth value for all identifiable links and the tightest error bound for unidentifiable links for a given set of measurement paths. When measurement paths are not given in advance, we prove the hardness of building measurement paths that can be used for deriving the global tightest error bounds for unidentifiable links. Accordingly, we develop a reinforcement learning (RL) approach for measurement path construction, that utilizes the special knowledge in bandwidth tomography and integrates both offline training and online prediction. Evaluation results with real-world ISP as well as simulated networks demonstrate that compared to other path construction methods, Random and Diversity Preferred, our RL-based path construction method can build measurement paths that result in much smaller average error bound of link bandwidth.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488691"}, {"primary_key": "2186828", "vector": [], "sparse_vector": [], "title": "Grafting Arborescences for Extra Resilience of Fast Rerouting Schemes.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "To provide a high availability and to be able to quickly react to link failures, most communication networks feature fast rerouting (FRR) mechanisms in the data plane. However, configuring these mechanisms to provide a high resilience against multiple failures is algorithmically challenging, as rerouting rules can only depend on local failure information and need to be pre-defined. This paper is motivated by the observation that the common approach to design fast rerouting algorithms, based on spanning trees and covering arborescences, comes at a cost of reduced resilience as it does not fully exploit the available links in heterogeneous topologies. We present several novel fast rerouting algorithms which are not limited by spanning trees, but rather extend and combine (\"graft\") multiple spanning arborescences to improve resilience. We compare our algorithms analytically and empirically, and show that they can significantly improve not only the resilience, but also accelerate the preprocessing to generate the local fast failover rules.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488782"}, {"primary_key": "2186832", "vector": [], "sparse_vector": [], "title": "Jellyfish: Locality-Sensitive Subflow Sketching.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "To cope with increasing network rates and massive traffic volumes, sketch-based methods have been extensively studied to trade accuracy for memory scalability and storage cost. However, sketches are sensitive to hash collisions due to skewed keys in real world environment, and need complicated performance control for line-rate packet streams.We present Jellyfish, a locality-sensitive sketching framework to address these issues. Jellyfish goes beyond network flow-based sketching towards fragments of network flows called subflows. First, Jellyfish splits consecutive packets from each network flow to subflow records, which not only reduces the rate contention but also provides intermediate subflow representations in form of truncated counters. Next, Jellyfish maps similar subflow records to the same bucket array and merges those from the same network flow to reconstruct the network-flow level counters. Real-world trace-driven experiments show that <PERSON><PERSON><PERSON> reduces the average estimation errors by up to six orders of magnitude for per-flow queries, by six orders of magnitude for entropy queries, and up to ten times for heavy-hitter queries.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488847"}, {"primary_key": "2186833", "vector": [], "sparse_vector": [], "title": "AutoML for Video Analytics with Edge Computing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Video analytics constitute a core component of many wireless services that require processing of voluminous data streams emanating from handheld devices. Multi-Access Edge Computing (MEC) is a promising solution for supporting such resource-hungry services, but there is a plethora of configuration parameters affecting their performance in an unknown and possibly time-varying fashion. To overcome this obstacle, we propose an Automated Machine Learning (AutoML) framework for jointly configuring the service and wireless network parameters, towards maximizing the analytics' accuracy subject to minimum frame rate constraints. Our experiments with a bespoke prototype reveal the volatile and system/data-dependent performance of the service, and motivate the development of a Bayesian online learning algorithm which optimizes on-the-fly the service performance. We prove that our solution is guaranteed to find a near-optimal configuration using safe exploration, i.e., without ever violating the set frame rate thresholds. We use our testbed to further evaluate this AutoML framework in a variety of scenarios, using real datasets.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488704"}, {"primary_key": "2186835", "vector": [], "sparse_vector": [], "title": "Auction-Based Combinatorial Multi-Armed Bandit Mechanisms with Strategic Arms.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The multi-armed bandit (MAB) model has been deeply studied to solve many online learning problems, such as rate allocation in communication networks, Ad recommendation in social networks, etc. In an MAB model, given N arms whose rewards are unknown in advance, the player selects exactly one arm in each round, and his goal is to maximize the cumulative rewards over a fixed horizon. In this paper, we study the budget-constrained auction-based combinatorial multi-armed bandit mechanism with strategic arms, where the player can select K (<; N) arms in a round and pulling each arm has a unique cost. In addition, each arm might strategically report its cost in the auction. To this end, we combine the upper confidence bound (UCB) with auction to define the UCB-based rewards and then devise an auction-based UCB algorithm (called AUCB). In each round, AUCB selects the top K arms according to the ratios of UCB-based rewards to bids and further determines the critical payment for each arm. For AUCB, we derive an upper bound on regret and prove the truthfulness, individual rationality, and computational efficiency. Extensive simulations show that the rewards achieved by AUCB are at least 12.49% higher than those of state-of-the-art algorithms.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488765"}, {"primary_key": "2186838", "vector": [], "sparse_vector": [], "title": "Leveraging Public-Private Blockchain Interoperability for Closed Consortium Interfacing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the increasing adoption of private blockchain platforms, consortia operating in various sectors such as trade, finance, logistics, etc., are becoming common. Despite having the benefits of a completely decentralized architecture which supports transparency and distributed control, existing private blockchains limit the data, assets, and processes within its closed boundary, which restricts secure and verifiable service provisioning to the end-consumers. Thus, platforms such as e-commerce with multiple sellers or cloud federation with a collection of cloud service providers cannot be decentralized with the existing blockchain platforms. This paper proposes a decentralized gateway architecture interfacing private blockchain with end-users by leveraging the unique combination of public and private blockchain platforms through interoperation. Through the use case of decentralized cloud federations, we have demonstrated the viability of the solution. Our testbed implementation with Ethereum and Hyperledger Fabric, with three service providers, shows that such consortium can operate within an acceptable response latency while scaling up to 64 parallel requests per second for cloud infrastructure provisioning. Further analysis over the Mininet emulation platform indicates that the platform can scale well with minimal impact over the latency as the number of participating service providers increases.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488683"}, {"primary_key": "2186840", "vector": [], "sparse_vector": [], "title": "SOBA: Session optimal MDP-based network friendly recommendations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Caching content over CDNs or at the network edge has been solidified as a means to improve network cost and offer better streaming experience to users. Furthermore, nudging the users towards low-cost content has recently gained momentum as a strategy to boost network performance. We focus on the problem of optimal policy design for Network Friendly Recommendations (NFR). We depart from recent modeling attempts, and propose a Markov Decision Process (MDP) formulation. MDPs offer a unified framework that can model a user with random session length. As it turns out, many state-of-the-art approaches can be cast as subcases of our MDP formulation. Moreover, the approach offers flexibility to model users who are reactive to the quality of the received recommendations. In terms of performance, for users consuming an arbitrary number of contents in sequence, we show theoretically and using extensive validation over real traces that the MDP approach outperforms myopic algorithms both in session cost as well as in offered recommendation quality. Finally, even compared to optimal state-of-art algorithms targeting specific subcases, our MDP framework is significantly more efficient, speeding the execution time by a factor of 10, and enjoying better scaling with the content catalog and recommendation batch sizes.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488720"}, {"primary_key": "2186845", "vector": [], "sparse_vector": [], "title": "Optimal Rack-Coordinated Updates in Erasure-Coded Data Centers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>ng Shen", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Erasure coding has been extensively deployed in today's data centers to tackle prevalent failures, yet it is prone to give rise to substantial cross-rack traffic for parity update. In this paper, we propose a new rack-coordinated update mechanism to suppress the cross-rack update traffic, which comprises two successive phases: a delta-collecting phase that collects data delta chunks, and another selective parity update phase that renews the parity chunks based on the update pattern and parity layout. We further design RackCU, an optimal rack-coordinated update solution that achieves the theoretical lower bound of the cross-rack update traffic. We finally conduct extensive evaluations, in terms of large-scale simulation and real-world data center experiments, showing that RackCU can reduce 22.1%-75.1% of the cross-rack update traffic and hence improve 34.2%-292.6% of the update throughput.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488813"}, {"primary_key": "2186847", "vector": [], "sparse_vector": [], "title": "Leveraging Website Popularity Differences to Identify Performance Anomalies.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Web performance anomalies (e.g. time periods when metrics like page load time are abnormally high) have significant impact on user experience and revenues of web service providers. Existing methods to automatically detect web performance anomalies focus on popular websites (e.g. with tens of thousands of visits per minute). Across a wider diversity of websites, however, the number of visits per hour varies enormously, and some sites will only have few visits per hour. Low rates of visits create measurement gaps and noise that prevent the use of existing methods. This paper develops WMF, a web performance anomaly detection method applicable across a range of websites with highly variable measurement volume. To demonstrate our method, we leverage data from a website monitoring company, which allows us to leverage cross-site measurements. WMF uses matrix factorization to mine patterns that emerge from a subset of the websites to \"fill in\" missing data on other websites. Our validation using both a controlled website and synthetic anomalies shows that WMF's F1-score is more than double that of the state-of-the-art method. We then apply WMF to three months of web performance measurements to shed light on performance anomalies across a variety of 125 small to medium websites.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488832"}, {"primary_key": "2186848", "vector": [], "sparse_vector": [], "title": "The Effect of Ground Truth Accuracy on the Evaluation of Localization Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The ability to accurately evaluate the performance of location determination systems is crucial for many applications. Typically, the performance of such systems is obtained by comparing ground truth locations with estimated locations. However, these ground truth locations are usually obtained by clicking on a map or using other worldwide available technologies like GPS. This introduces ground truth errors that are due to the marking process, map distortions, or inherent GPS inaccuracy.In this paper, we present a theoretical framework for analyzing the effect of ground truth errors on the evaluation of localization systems. Based on that, we design two algorithms for computing the real algorithmic error from the validation error and marking/map ground truth errors, respectively. We further establish bounds on different performance metrics.Validation of our theoretical assumptions and analysis using real data collected in a typical environment shows the ability of our theoretical framework to correct the estimated error of a localization algorithm in the presence of ground truth errors. Specifically, our marking error algorithm matches the real error CDF within 4%, and our map error algorithm provides a more accurate estimate of the median/tail error by 150%/72% when the map is shifted by 6m.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488767"}, {"primary_key": "2186849", "vector": [], "sparse_vector": [], "title": "Exploring Layered Container Structure for Cost Efficient Microservice Deployment.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Container, as a light-weight virtualization technology with the advantages of continuous integration and easy deployment, has been widely adopted to support diverse microservices. At runtime, non-local container images need to be frequently pulled from remote registries to local servers, resulting in large pulling traffic and hence long startup time. A distinctive feature in container-based microservice, which has not been exploited, is that container images are in layered structure and some common base layers can be shared between co-located microservices. In this paper, we propose a layer sharing microservice deployment and image pulling strategy which explores the advantage of layer sharing to speedup microservice startup and lower image storage consumption. The problem is formulated into an Integer Linear Programming (ILP) form. An Accelerated Distributed Augmented Lagrangian (ADAL) based distributed algorithm executed cooperatively by registries and servers is proposed. Through extensive trace driven experiments, we validate the high efficiency of our ADAL based algorithm as it accelerates the microservice startup by 2.30 times in average and reduces the storage consumption by 55.33%.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488918"}, {"primary_key": "2186851", "vector": [], "sparse_vector": [], "title": "Optimal Wireless Scheduling for Remote Sensing through Brownian Approximation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper studies a remote sensing system where multiple wireless sensors generate possibly noisy information updates of various surveillance fields and delivering these updates to a control center over a wireless network. The control center needs a sufficient number of recently generated information updates to have an accurate estimate of the current system status, which is critical for the control center to make appropriate control decisions. The goal of this work is then to design the optimal policy for scheduling the transmissions of information updates. Through Brownian approximation, we demonstrate that the control center's ability to make accurate real-time estimates depends on the averages and temporal variances of the delivery processes. We then formulate a constrained optimization problem to find the optimal means and variances. We also develop a simple online scheduling policy that employs the optimal means and variances to achieve the optimal system-wide performance. Simulation results show that our scheduling policy enjoys fast convergence speed and better performance when compared to other state-of-the-art policies.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488785"}, {"primary_key": "2186852", "vector": [], "sparse_vector": [], "title": "Tailored Learning-Based Scheduling for Kubernetes-Oriented Edge-Cloud System.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Kubernetes (k8s) has the potential to merge the distributed edge and the cloud but lacks a scheduling framework specifically for edge-cloud systems. Besides, the hierarchical distribution of heterogeneous resources and the complex dependencies among requests and resources make the modeling and scheduling of k8s-oriented edge-cloud systems particularly sophisticated. In this paper, we introduce KaiS, a learning-based scheduling framework for such edge-cloud systems to improve the long-term throughput rate of request processing. First, we design a coordinated multi-agent actor-critic algorithm to cater to decentralized request dispatch and dynamic dispatch spaces within the edge cluster. Second, for diverse system scales and structures, we use graph neural networks to embed system state information, and combine the embedding results with multiple policy networks to reduce the orchestration dimensionality by stepwise scheduling. Finally, we adopt a two-time-scale scheduling mechanism to harmonize request dispatch and service orchestration, and present the implementation design of deploying the above algorithms compatible with native k8s components. Experiments using real workload traces show that KaiS can successfully learn appropriate scheduling policies, irrespective of request arrival patterns and system scales. Moreover, KaiS can enhance the average system throughput rate by 14.3% while reducing scheduling cost by 34.7% compared to baselines.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488701"}, {"primary_key": "2186853", "vector": [], "sparse_vector": [], "title": "TiBroco: A Fast and Secure Distributed Learning Framework for Tiered Wireless Edge Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Recent proliferation of mobile devices and edge servers (e.g., small base stations) strongly motivates distributed learning at the wireless edge. In this paper, we propose a fast and secure distributed learning framework that utilizes computing resources at edge servers as well as distributed computing devices in tiered wireless edge networks. A fundamental lower bound is derived on the computational load that perfectly tolerates Byzantine attacks at both tiers. TiBroco, a hierarchical coding framework achieving this theoretically minimum computational load is proposed, which guarantees secure distributed learning by combating Byzantines. A fast distributed learning is possible by precisely allocating loads to the computing devices and edge servers, and also utilizing the broadcast nature of wireless devices. Extensive experimental results on Amazon EC2 indicate that our TiBroco allows significantly faster distributed learning than existing methods while guaranteeing full tolerance against Byzantine attacks at both tiers.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488860"}, {"primary_key": "2186854", "vector": [], "sparse_vector": [], "title": "Edge-assisted Online On-device Object Detection for Real-time Video Analytics.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Real-time on-device object detection for video analytics fails to meet the accuracy requirement due to limited resources of mobile devices while offloading object detection inference to edges is time-consuming due to the transference of video data over edge networks. Based on the system with both on-device object tracking and edge-assisted analysis, we formulate a non-linear time-coupled program over time, maximizing the overall accuracy of object detection by deciding the frequency of edge-assisted inference, under the consideration of both dynamic edge networks and the constrained detection latency. We then design a learning-based online algorithm to adjust the threshold for triggering edge-assisted inference on the fly in terms of the object tracking results, which essentially controls the deviation of on-device tracking between two consecutive frames in the video, by only taking previously observable inputs. We rigorously prove that our approach only incurs sub-linear dynamic regret for the optimality objective. At last, we implement our proposed online schema, and extensive testbed results with real-world traces confirm the empirical superiority over alternative algorithms, in terms of up to 36% improvement on detection accuracy with ensured detection latency.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488741"}, {"primary_key": "2186856", "vector": [], "sparse_vector": [], "title": "GOLDIE: Harmonization and Orchestration Towards a Global Directory for IoT.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "To scale the Internet of Things (IoT) beyond a single home or enterprise, we need an effective mechanism to manage the growth of data, facilitate resource discovery and name resolution, encourage data sharing, and foster cross-domain services. To address these needs, we propose a GlObaL Directory for Internet of Everything (GOLDIE). GOLDIE is a hierarchical location-based IoT directory architecture featuring diverse user-oriented modules and federated identity management. IoT-specific features include discoverability, aggregation and geospatial queries, and support for global access. We implement and evaluate the prototype on a Raspberry Pi and Intel mini servers. We show that a global implementation of GOLDIE could decrease service access latency by 87% compared to a centralized-server solution.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488752"}, {"primary_key": "2186861", "vector": [], "sparse_vector": [], "title": "CryptoEyes: Privacy Preserving Classification over Encrypted Images.", "authors": ["<PERSON><PERSON>", "Shusheng Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the concern of privacy, a user usually encrypts the images before they were uploaded to the cloud service providers. Classification over encrypted images is essential for the service providers to collect coarse-grained statistical information about the images, therefore offering better services without sacrificing users' privacy. In this paper, we propose CryptoEyes to address the challenges of privacy-preserving classification over encrypted images. We present a two-stream convolutional network architecture for classification over encrypted images to capture the contour of encrypted images, therefore significantly boosting the classification accuracy. By sharing a secret sequence between the service provider and the image owner, CryptoEyes allows the service provider to obtain category information of encrypted images while preventing the unauthorized users from learning it. We implemented and evaluated CryptoEyes on popular datasets and the experimental results demonstrate the superiority of CryptoEyes over existing state of the arts in terms of classification accuracy over encrypted images and better privacy preservation performance.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488738"}, {"primary_key": "2186862", "vector": [], "sparse_vector": [], "title": "Scalable On-Switch Rate Limiters for the Cloud.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Haifeng Li", "<PERSON><PERSON><PERSON>"], "summary": "While most clouds use on-server rate limiters for bandwidth allocation, we propose to implement them on switches. On-switch rate limiters can simplify network management and promote the performance of control-plane rate limiting applications. We leverage the recent progress of programmable switches to implement on-switch rate limiters, named SwRL. In the design of SwRL, we make design choices according to the programmable hardware characteristics, we deeply optimize the memory usage of the algorithm so as to fit a cloud-scale (one million) rate limiters in a single switch, and we complement the missing computation primitives of the hardware using a pre-computed approximate table. We further developed three control-plane applications and integrate them with SwRL, showing the control-plane interoperability of SwRL. We prototype and evaluate SwRL in both testbed and production environments, demonstrating its good properties of precision rate control, scalability, interoperability, and manageability (execution environmental isolation).", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488773"}, {"primary_key": "2186864", "vector": [], "sparse_vector": [], "title": "DyLoc: Dynamic Localization for Massive MIMO Using Predictive Recurrent Neural Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents a data-driven localization framework with high precision in time-varying complex multi-path environments, such as dense urban areas and indoors, where GPS and model-based localization techniques come short. We consider the angle-delay profile (ADP), a linear transformation of channel state information (CSI), in massive MIMO systems and show that ADPs preserve users' motion when stacked temporally. We discuss that given a static environment, future frames of ADP time-series are predictable employing a video frame prediction algorithm. We express that a deep convolutional neural network (DCNN) can be employed to learn the background static scattering environment. To detect foreground changes in the environment, corresponding to path blockage or addition, we introduce an algorithm taking advantage of the trained DCNN. Furthermore, we present DyLoc, a data-driven framework to recover distorted ADPs due to foreground changes and to obtain precise location estimations. We evaluate the performance of DyLoc in several dynamic scenarios employing DeepMIMO dataset [1] to generate geo-tagged CSI datasets for indoor and outdoor environments. We show that previous DCNN-based techniques fail to perform with desirable accuracy in dynamic environments, while DyLoc pursues localization precisely. Moreover, simulations show that as the environment gets richer in terms of the number of multipath, DyLoc gets more robust to foreground changes.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488913"}, {"primary_key": "2186866", "vector": [], "sparse_vector": [], "title": "Pyramid: A Layered Sharding Blockchain System.", "authors": ["Zicong Hong", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Sharding can significantly improve the blockchain scalability, by dividing nodes into small groups called shards that can handle transactions in parallel. However, all existing sharding systems adopt complete sharding, i.e., shards are isolated. It raises additional overhead to guarantee the atomicity and consistency of cross-shard transactions and seriously degrades the sharding performance. In this paper, we present Pyramid, the first layered sharding blockchain system, in which some shards can store the full records of multiple shards thus the cross-shard transactions can be processed and validated in these shards internally. When committing cross-shard transactions, to achieve consistency among the related shards, a layered sharding consensus based on the collaboration among several shards is presented. Compared with complete sharding in which each cross-shard transaction is split into multiple sub-transactions and cost multiple consensus rounds to commit, the layered sharding consensus can commit cross-shard transactions in one round. Furthermore, the security, scalability, and performance of layered sharding with different sharding structures are theoretically analyzed. Finally, we implement a prototype for Pyramid and its evaluation results illustrate that compared with the state-of-the-art complete sharding systems, Pyramid can improve the transaction throughput by 2.95 times in a system with 17 shards and 3500 nodes.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488747"}, {"primary_key": "2186868", "vector": [], "sparse_vector": [], "title": "6Hit: A Reinforcement Learning-based Approach to Target Generation for Internet-wide IPv6 Scanning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Jinshu Su", "<PERSON><PERSON><PERSON>"], "summary": "Fast Internet-wide network measurement plays an important role in cybersecurity analysis and network asset detection. The vast address space of IPv6, however, makes it infeasible to apply a brute-force approach for scanning the entire network. Even worse, the extremely uneven distribution of IPv6 active addresses results in a low hit rate for active scanning. To address the problem, we propose 6Hit, a reinforcement learning-based target generation method for active address discovery in the IPv6 address space. It first divides the IPv6 address space into different regions according to the structural information of a set of known seed addresses. Then, it allocates exploration resources according to the reward of the scanning on each region. Based on the evaluative feedback from existing scanning results, 6Hit optimizes the subsequent search direction to regions that have a higher density of activity addresses. Compared with other state-of-the-art target generation methods, 6Hit achieves better performance on hit rate. Our experiments over real-world networks show that 6Hit achieves 3.5% - 11.5% hit rate for the eight candidate datasets, which is 7.7% - 630% improvement over the state-of-the-art methods.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488794"}, {"primary_key": "2186869", "vector": [], "sparse_vector": [], "title": "Jamming of LoRa PHY and Countermeasure.", "authors": ["<PERSON><PERSON><PERSON>", "Xianjin Xia", "Yuan<PERSON> Zheng"], "summary": "LoRaWAN forms a one-hop star topology where LoRa nodes send data via one-hop up-link transmission to a LoRa gateway. If the LoRa gateway can be jammed by attackers, the LoRa gateway may not be able to receive any data from any nodes in the network. Our empirical study shows that although LoRa physical layer (PHY) is robust and resilient by design, it is still vulnerable to synchronized jamming chirps. Potential protection solutions (e.g., collision recovery, parallel decoding) may fail to extract LoRa packets if an attacker transmits synchronized jamming chirps at high power. To protect the LoRa PHY from such attacks, we propose a new protection method that can separate LoRa chirps from jamming chirps by leveraging their difference in the received signal strength in power domain. We note that the new protection solution is orthogonal to existing solutions which leverage the chirp misalignment in time domain or the frequency disparity in frequency domain. We conduct experiments with COTS LoRa nodes and software defined radios. The results show that synchronized jamming chirps at high power can jam all previous solutions, while our protection solution can effectively protect LoRa gateways from the jamming attacks.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488774"}, {"primary_key": "2186872", "vector": [], "sparse_vector": [], "title": "Towards Cross-Modal Forgery Detection and Localization on Live Surveillance Videos.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The cybersecurity breaches render surveillance systems vulnerable to video forgery attacks, under which authentic live video streams are tampered to conceal illegal human activities under surveillance cameras. Traditional video forensics approaches can detect and localize forgery traces in each video frame using computationally-expensive spatial-temporal analysis, while falling short in real-time verification of live video feeds. The recent work correlates time-series camera and wireless signals to recognize replayed surveillance videos using event-level timing information but it cannot realize fine-grained forgery detection and localization on each frame. To fill this gap, this paper proposes Secure-Pose, a novel cross-modal forgery detection and localization system for live surveillance videos using WiFi signals near the camera spot. We observe that coexisting camera and WiFi signals convey common human semantic information and the presence of forgery attacks on video frames will decouple such information correspondence. Secure-Pose extracts effective human pose features from synchronized multi-modal signals and detects and localizes forgery traces under both inter-frame and intra-frame attacks in each frame. We implement Secure-Pose using a commercial camera and two Intel 5300 NICs and evaluate it in real-world environments. Secure-Pose achieves a high detection accuracy of 95.1% and can effectively localize tampered objects under different forgery attacks.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488798"}, {"primary_key": "2186874", "vector": [], "sparse_vector": [], "title": "Traffic-aware Buffer Management in Shared Memory Switches.", "authors": ["<PERSON><PERSON> Huang", "<PERSON><PERSON>", "<PERSON>"], "summary": "Switch buffer serves an important role in modern internet. To achieve efficiency, today's switches often use on-chip shared memory. Shared memory switches rely on buffer management policies to allocate buffer among ports. To avoid waste of buffer resources or a few ports occupy too much buffer, existing policies tend to maximize overall buffer utilization and pursue queue length fairness. However, blind pursuit of utilization and misleading fairness definition based on queue length leads to buffer occupation with no benefit to throughput but extends queuing delay and undermines burst absorption of other ports. We contend that a buffer management policy should proactively detect port traffic and adjust buffer allocation accordingly. In this paper, we propose Traffic-aware Dynamic Threshold (TDT) policy. On the basis of classic dynamic threshold policy, TDT proactively raise or lower port threshold to absorb burst traffic or evacuate meaningless buffer occupation. We present detailed designs of port control state transition and state decision module that detect real time traffic and change port thresholds accordingly. Simulation and DPDK-based real testbed demonstrate that TDT simultaneously optimizes for throughput, loss and delay, and reduces up to 50% flow completion time.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488849"}, {"primary_key": "2186875", "vector": [], "sparse_vector": [], "title": "NFD: Using Behavior Models to Develop Cross-Platform Network Functions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "NFV ecosystem is flourishing and more and more NF platforms appear, but this makes NF vendors difficult to deliver NFs rapidly to diverse platforms. We propose an NF development framework named NFD for cross-platform NF development. NFD's main idea is to decouple the functional logic from the platform logic -it provides a platform-independent language to program NFs' behavior models, and a compiler with interfaces to develop platform-specific plugins. By enabling a plugin on the compiler, various NF models would be compiled to executables integrated with the target platform. We prototype NFD, build 14 NFs, and support 6 platforms (standard Linux, OpenNetVM, GPU, SGX, DPDK, OpenNF). Our evaluation shows that NFD can save development workload for cross-platform NFs and output valid and performant NFs.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488734"}, {"primary_key": "2186876", "vector": [], "sparse_vector": [], "title": "Strategic Information Revelation in Crowdsourcing Systems Without Verification.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study a crowdsourcing problem where the platform aims to incentivize distributed workers to provide high-quality and truthful solutions without the ability to verify the solutions. While most prior work assumes that the platform and workers have symmetric information, we study an asymmetric information scenario where the platform has informational advantages. Specifically, the platform knows more information regarding workers' average solution accuracy, and can strategically reveal such information to workers. Workers will utilize the announced information to determine the likelihood that they obtain a reward if exerting effort on the task. We study two types of workers: (1) naive workers who fully trust the announcement, and (2) strategic workers who update prior belief based on the announcement. For naive workers, we show that the platform should always announce a high average accuracy to maximize its payoff. However, this is not always optimal for strategic workers, as it may reduce the credibility of the platform's announcement and hence reduce the platform's payoff. Interestingly, the platform may have an incentive to even announce an average accuracy lower than the actual value when facing strategic workers. Another counter-intuitive result is that the platform's payoff may decrease in the number of high-accuracy workers.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488853"}, {"primary_key": "2186877", "vector": [], "sparse_vector": [], "title": "Towards Video Streaming Analysis and Sharing for Multi-Device Interaction with Lightweight DNNs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Multi-device interaction has attracted a growing interest in both mobile communication industry and mobile computing research community as mobile devices enabled social media and social networking continue to blossom. However, due to the stringent low latency requirements and the complexity and intensity of computation, implementing efficient multi-device interaction for real-time video streaming analysis and sharing is still in its infancy. Unlike previous approaches that rely on high network bandwidth and high availability of cloud center with GPUs to support intensive computations for multi-device interaction and for improving the service experience, we propose MIRSA, a novel edge centric multi-device interaction framework with a lightweight end-to-end DNN for on-device visual odometry (VO) streaming analysis by leveraging edge computing optimizations with three main contributions. First, we design MIRSA to migrate computations from the cloud to the device side, reducing the high overhead for large transmission of video streaming while alleviating the server load of the cloud. Second, we design a lightweight VO network by utilizing temporal shift module to support on-device pose estimation. Third, we provide on-device resource-aware scheduling algorithm to optimize the task allocation. Extensive experiments show MIRSA provides real-time high quality pose estimation as an interactive service and outperforms baseline methods.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488846"}, {"primary_key": "2186878", "vector": [], "sparse_vector": [], "title": "Finding Critical Files from a Packet.", "authors": ["Jun<PERSON>yung Hur", "<PERSON><PERSON><PERSON>", "<PERSON>yeon Gy Shon", "<PERSON> <PERSON>", "MyungKeun Yoon"], "summary": "Network-based intrusion detection and data leakage prevention systems inspect packets to detect if critical files such as malware or confidential documents are transferred. However, this kind of detection requires heavy computing resources in reassembling packets and only well-known protocols can be interpreted. Besides, finding similar files from a storage requires pairwise comparisons. In this paper, we present a new network-based file identification scheme that inspects packets independently without reassembly and finds similar files through inverted indexing instead of pairwise comparison. We use a contents-based chunking algorithm to consistently divide both files and packets into multiple byte sequences, called chunks. If a packet is a part of a file, they would have common chunks. The challenging problem is that packet chunking and inverted-index search should be fast and scalable enough for packet processing. The file identification should be accurate although many chunks are noises. In this paper, we use a small Bloom filter and a delayed query strategy to solve the problems. To the best of our knowledge, this is the first scheme that identifies a specific critical file from a packet over unknown protocols. Experimental results show that the proposed scheme can successfully identify a critical file from a packet.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488914"}, {"primary_key": "2186881", "vector": [], "sparse_vector": [], "title": "PROCESS: Privacy-Preserving On-Chain Certificate Status Service.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shouling Ji"], "summary": "Clients (e.g., browsers) and servers require public key certificates to establish secure connections. When a client accesses a server, it needs to check the signature, expiration time, and revocation status of the certificate to determine whether the server is reliable. The existing solutions for checking certificate status either have a long update cycle (e.g., CRL, CRLite) or violate clients' privacy (e.g., OCSP, CCSP), and these solutions also have the problem of trust concentration. In this paper, we present PROCESS, an online privacy-preserving on-chain certificate status service based on the blockchain architecture, which can ensure decentralized trust and provide privacy protection for clients. Specifically, we design Counting Garbled Bloom Filter (CGBF) that supports efficient queries and BlockOriented Revocation List (BORL) to update CGBF timely in the blockchain. With CGBF, we design a privacy-preserving protocol to protect clients' privacy when they check the certificate statuses from the blockchain nodes. Finally, we conduct experiments and compare PROCESS with another blockchain-based solution to demonstrate that PROCESS is suitable in practice.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488858"}, {"primary_key": "2186882", "vector": [], "sparse_vector": [], "title": "Reliability-aware Dynamic Service Chain Scheduling in 5G Networks based on Reinforcement Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As a key enabler of future 5G network, Service Function Chain (SFC) forwards the traffic flow along a chain of Virtual Network Functions (VNFs) to provide network services flexibility. One of the most important problems in SFC is to deploy the VNFs and schedule arriving requests among computing nodes to achieve low latency and high reliability. Existing works consider a static network and assume that all SFC requests are known in advance, which is impractical. In this paper, we focus on the dynamic 5G network environment where the SFC requests arrive randomly and the computing nodes can redeploy all types of VNF with a time cost. We formulate the problem of SFC scheduling in NFV-enabled 5G network as a mixed integer non-linear programing. The objective is to maximize the number of requests satisfying the latency and reliability constraints. To solve the problem, we propose an efficient algorithm to decide the redundancy of the VNFs while minimizing delay. Then we present a state-of-art Reinforcement Learning (RL) to learn SFC scheduling policy to increase the success rate of SFC requests. The effectiveness of our method is evaluated through extensive simulations. The result shows that our proposed RL solution can increase the success rate by 18.7% over the benchmarks.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488707"}, {"primary_key": "2186883", "vector": [], "sparse_vector": [], "title": "Analyzing Age of Information in Multiaccess Networks by Fluid Limits.", "authors": ["<PERSON><PERSON><PERSON> Jiang"], "summary": "In this paper, we adopt the fluid limits to analyze Age of Information (AoI) in a wireless multiaccess network with many users. We consider the case wherein users have heterogeneous i.i.d. channel conditions and the statuses are generate-at-will. Convergence of the AoI occupancy measure to the fluid limit, represented by a Partial Derivative Equation (PDE), is proved within an approximation error inversely proportional to the number of users. Global convergence to the equilibrium of the PDE, i.e., stationary AoI distribution, is also proved. Based on this framework, it is shown that an existing AoI lower bound in the literature is in fact asymptotically tight, and a simple threshold policy, with the thresholds explicitly derived, achieves the optimum asymptotically. The proposed threshold-based policy is also much easier to decentralize than the widely-known index-based policies which require comparing user indices. To showcase the usability of the framework, we also use it to analyze the average non-linear AoI functions (with power and logarithm forms) in wireless networks. Again, explicit optimal threshold-based policies are derived, and average age functions proven. Simulation results show that even when the number of users is limited, e.g., 10, the proposed policy and analysis are still effective.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488712"}, {"primary_key": "2186884", "vector": [], "sparse_vector": [], "title": "GPU-Ether: GPU-native Packet I/O for GPU Applications on Commodity Ethernet.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Despite the advent of various network enhancement technologies, it is yet a challenge to provide high-performance networking for GPU-accelerated applications on commodity Ethernet. Kernel-bypass I/O, such as DPDK or netmap, which is normally optimized for host memory-based CPU applications, has limitations on improving the performance of GPU-accelerated applications due to the data transfer overhead between host and GPU. In this paper, we propose GPU-Ether, GPU-native packet I/O on commodity Ethernet, which enables direct network access from GPU via dedicated persistent kernel threads. We implement GPU-Ether prototype on a commodity Ethernet NIC and perform extensive testing to evaluate it. The results show that GPU-Ether can provide high throughput and low latency for GPU applications.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488699"}, {"primary_key": "2186885", "vector": [], "sparse_vector": [], "title": "Age of Information in Random Access Networks with Stochastic Arrivals.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider a Random Access network with a number of nodes transmitting time-sensitive information to a wireless base station. Packets are generated according to a stochastic process and nodes employ either Slotted-ALOHA or Carrier-Sense Multiple Access (CSMA) to transmit these packets. A packet collision occurs when two or more nodes transmit simultaneously and a successful packet transmission occurs when a node transmits without interference. The goal is to optimize the Random Access mechanism in terms of information freshness, which is captured by the Age of Information (AoI) metric.In this paper, we propose a framework to analyze and optimize the average AoI in Random Access networks with stochastic packet generation. In particular, we develop a discrete-time model, derive an approximate expression for the average AoI in the network, and then use this expression to optimize the Random Access mechanism. Furthermore, we implement the optimized Random Access mechanism in a Software Defined Radio testbed and compare the AoI measurements with analytical and numerical results in order to validate our framework. Our approach allows us to evaluate the combined impact of the packet generation rate, transmission probability, and size of the network on the AoI performance.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488897"}, {"primary_key": "2186887", "vector": [], "sparse_vector": [], "title": "Rate Allocation and Content Placement in Cache Networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We introduce the problem of optimal congestion control in cache networks, whereby both rate allocations and content placements are optimized jointly. We formulate this as a maximization problem with non-convex constraints, and propose solving this problem via (a) a Lagrangian barrier algorithm and (b) a convex relaxation. We prove different optimality guarantees for each of these two algorithms; our proofs exploit the fact that the non-convex constraints of our problem involve DR-submodular functions.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488715"}, {"primary_key": "2186888", "vector": [], "sparse_vector": [], "title": "Comparison of Decentralized and Centralized Update Paradigms for Remote Tracking of Distributed Dynamic Sources.", "authors": ["<PERSON><PERSON><PERSON>", "Atilla E<PERSON>il<PERSON>z", "<PERSON><PERSON>"], "summary": "In this work, we perform a comparative study of centralized and decentralized update strategies for the basic remote tracking problem of many distributed users/devices with randomly evolving states. Our goal is to reveal the impact of the fundamentally different tradeoffs that exist between information accuracy and communication cost under these two update paradigms. In one extreme, decentralized updates are triggered by distributed users/transmitters based on exact local state-information, but also at a higher cost due to the need for uncoordinated multi-user communication. In the other extreme, centralized updates are triggered by the common tracker/receiver based on estimated global state-information, but also at a lower cost due to the capability of coordinated multi-user communication. We use a generic superlinear function to model the communication cost with respect to the number of simultaneous updates for multiple sources. We characterize the conditions under which transmitter-driven decentralized update policies outperform their receiver-driven centralized counterparts for symmetric sources, and vice versa. Further, we extend the results to a scenario where system parameters are unknown and develop learning-based update policies that asymptotically achieve the minimum cost levels attained by the optimal policies.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488777"}, {"primary_key": "2186889", "vector": [], "sparse_vector": [], "title": "Safety Critical Networks using Commodity SDNs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Rakesh B. Bobba", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Safety-critical networks often have stringent real-time requirements; they must also be resilient to failures. In this paper, we propose the RealFlow framework that uses commodity software-defined networks (SDNs) to realize networks with end-to-end timing guarantees, while also: (a) increasing resiliency against link/switch failures and (b) increasing network utilization. The use of SDNs in this space also improves the management capabilities of the system due to the global visibility into the network. RealFlow is implemented as a northbound SDN controller application compatible with standard OpenFlow protocols with little to no runtime overheads. We demonstrate feasibility on a real hardware testbed (Pica8 SDN switches+Raspberry Pi endhosts) and a practical avionics case study. Our evaluations show that RealFlow can accommodate 63% more network flows with safety-critical guarantees when compared to current designs and up to 18% when link resiliency (via backup paths) is also considered.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488708"}, {"primary_key": "2186892", "vector": [], "sparse_vector": [], "title": "Heuristic Algorithms for Co-scheduling of Edge Analytics and Routes for UAV Fleet Missions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Unmanned Aerial Vehicles (UAVs) or drones are increasingly used for urban applications like traffic monitoring and construction surveys. Autonomous navigation allows drones to visit waypoints and accomplish activities as part of their mission. A common activity is to hover and observe a location using on-board cameras. Advances in Deep Neural Networks (DNNs) allow such videos to be analyzed for automated decision making. UAVs also host edge computing capability for on-board inferencing by such DNNs. To this end, for a fleet of drones, we propose a novel Mission Scheduling Problem (MSP) that co-schedules the flight routes to visit and record video at waypoints, and their subsequent on-board edge analytics. The proposed schedule maximizes the utility from the activities while meeting activity deadlines as well as energy and computing constraints. We first prove that MSP is NP-hard and then optimally solve it by formulating a mixed integer linear programming (MILP) problem. Next, we design two efficient heuristic algorithms, jsc and vrc, that provide fast sub-optimal solutions. Evaluation of these three schedulers using real drone traces demonstrate utility-runtime trade-offs under diverse workloads.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488740"}, {"primary_key": "2186895", "vector": [], "sparse_vector": [], "title": "ECLAT: An ECN Marking System for Latency Guarantee in Cellular Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As the importance of latency performance increases, a number of multi-bit feedback-based congestion control mechanisms have been proposed for explicit latency control in cellular networks. However, due to their reactive nature and limited access to the network queue, while latency reduction was possible, latency guarantee has not been achieved. Also, due to the need for end-host modifications, it was hard to commonly provide latency benefit to all connected devices. To this end, we propose a novel network-assisted congestion control, ECLAT, which can always bound the queuing delay within a delay-budget through ECN-based single-bit feedback while maintaining high link utilization for any device. To do so, ECLAT 1) calculates its target operating point for each flow, which is related to the maximum allowable cwnd to meet the delay-budget under time-varying cellular networks, and 2) determines its single-bit feedback policy to limit cwnd within the target operating point. Our extensive experiments in our testbed demonstrate that ECLAT is able to bound the queuing delays of multiple flows within their delay-budget and achieve high utilization even in the dynamic cellular network environment.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488762"}, {"primary_key": "2186899", "vector": [], "sparse_vector": [], "title": "The Impact of Baseband Functional Splits on Resource Allocation in 5G Radio Access Networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "We study physical-layer (PHY) baseband functional split policies in 5G Centralized Radio-Access-Network (C-RAN) architectures that include a central location, the baseband unit (BBU) with some BBU servers, and a set of Base Stations (BSs), the remote radio heads (RRHs), each with a RRH server. Each RRH is connected to the BBU location through a fronthaul link. We consider a scenario with many frame streams at the BBU location, where each stream needs to be processed by a BBU server before being sent to a remote radio-head (RRH). For each stream, a functional split needs to be selected, which provides a way of partitioning the computational load of the baseband processing chain for stream frames between the BBU and RRH servers. For streams that are served by the same BBU server, a scheduling policy is also needed. We formulate and solve the joint resource allocation problem of functional split selection, BBU server allocation and server scheduling, with the goal to minimize total average end-to-end delay or to minimize maximum average delay over RRH streams. The total average end-to-end delay is the sum of (i) scheduling (queueing) and processing delay at the BBU servers, (ii) data transport delay at the fronthaul link, and (iii) processing delay at the RRH server. Numerical results show the resulting delay improvements, if we incorporate functional split selection in resource allocation.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488742"}, {"primary_key": "2186906", "vector": [], "sparse_vector": [], "title": "Flow Algebra: Towards an Efficient, Unifying Framework for Network Management Tasks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A modern network needs to conduct a diverse set of tasks, and the existing approaches focus on developing specific tools for specific tasks, resulting in increasing complexity and lacking reusability. In this paper, we propose Flow Algebra as a unifying, easy-to-use framework to accomplish a large set of network management tasks. Based on the observation that relational databases based on relational algebra are well understood and widely used as a unifying framework for data management, we develop flow algebra based on relational algebra. On the other hand, flow tables, which are the fundamental data specifying the state of a network, cannot be stored in traditional relations, because of fundamental features such as wildcard and priorities. We define flow algebra based on novel, generalized relational operations that use equivalency to achieve efficient, unifying data store, query, and manipulation of both flow tables and traditional relations. We realize flow algebra with FlowDB and demonstrate its ease of use on diverse tasks. We further demonstrate that generality and ease-of-use do not need to come with a performance penalty. For example, for the well-studied network verification task, our system outperforms two state-of-the-art network verification engines, NoD and HSA, in their targeted domain, by 55x.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488857"}, {"primary_key": "2186907", "vector": [], "sparse_vector": [], "title": "Contact Tracing App Privacy: What Data Is Shared By Europe&apos;s GAEN Contact Tracing Apps.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We describe the data transmitted to backend servers by the contact tracing apps now deployed in Europe with a view to evaluating user privacy. These apps consist of two separate components: a \"client\" app managed by the national public health authority and the Google/Apple Exposure Notification (GAEN) service, that on Android devices is managed by Google and is part of Google Play Services. We find that the health authority client apps are generally well behaved from a privacy point of view, although the privacy of the Irish, Polish, Danish and Latvian apps could be improved. In marked contrast, we find that the Google Play Services component of these apps is problematic from a privacy viewpoint. Even when minimally configured, Google Play Services still contacts Google servers roughly every 20 minutes, potentially allowing location tracking via IP address. In addition, the phone IMEI, hardware serial number, SIM serial number and IMSI, handset phone number etc are shared with Google, together with detailed data on phone activity. This data collection is enabled simply by enabling Google Play Services, even when all other Google services and settings are disabled, and so is unavoidable for users of GAEN-based contact tracing apps on Android.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488728"}, {"primary_key": "2186908", "vector": [], "sparse_vector": [], "title": "WiProg: A WebAssembly-based Approach to Integrated IoT Programming.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Programming a complete IoT application usually requires separated programming for device, edge and/or cloud sides, which slows down the development process and makes the project hardly portable. Existing solutions tackle this problem by proposing a single coherent language while leaving two issues unsolved: efficient migration among the three sides and the platform dependency of the binaries. We propose WiProg, an integrated approach to IoT application programming based on WebAssembly. WiProg proposes an edge-centric programming approach that enables developers to write the IoT application as if it runs on the edge. This is achieved by the peripheral-accessing SDKs and annotations specifying the computation placement. WiProg automatically processes the program to insert auxiliary code and then compile it to WebAssembly. At runtime, WiProg leverages dynamic code offloading with compact memory snapshotting to achieve efficient execution. WiProg also provides interfaces for the customization of offloading policies. Results on real-world applications and computation benchmarks show that WiProg achieves an average reduction by 18.7%~54.3% and 20.1%~57.6% in terms of energy consumption and execution time.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488424"}, {"primary_key": "2186909", "vector": [], "sparse_vector": [], "title": "ScreenID: Enhancing QRCode Security by Fingerprinting Screens.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xiaoyu Ji", "<PERSON><PERSON> Pan", "<PERSON><PERSON><PERSON> Yang", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Quick response (QR) codes have been widely used in mobile applications due to its convenience and the pervasive built-in cameras on smartphones. Recently, however, attacks against QR codes have been reported that attackers can capture a QR code of the victim and replay it to achieve a fraudulent transaction or intercept private information, just before the original QR code is scanned. In this study, we enhance the security of a QR code by identifying its authenticity. We propose SCREENID, which embeds a QR code with information of the screen which displays it, thereby the QR code can reveal whether it is reproduced by an adversary or not. In SCREENID, PWM frequency of screens is exploited as the unique screen fingerprint. To improve the estimation accuracy of PWM frequency, SCREENID incorporates a model for the interaction between the camera and screen in the temporal and spatial domains. Extensive experiments demonstrate that SCREENID can differentiate screens of different models, types, and manufacturers, thus improve the security of QR codes.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488859"}, {"primary_key": "2186911", "vector": [], "sparse_vector": [], "title": "Sample-level Data Selection for Federated Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Federated learning (FL) enables participants to collaboratively construct a global machine learning model without sharing their local training data to the remote server. In FL systems, the selection of training samples has a significant impact on model performances, e.g., selecting participants whose datasets have erroneous samples, skewed categorical distributions, and low content diversity would result in low accuracy and unstable models. In this work, we aim to solve the exigent optimization problem that selects a collection of high-quality training samples for a given FL task under a monetary budget in a privacy-preserving way, which is extremely challenging without visibility to participants' local data and training process. We provide a systematic analysis of important data related factors affecting the model performance and propose a holistic design to privately and efficiently select high-quality data samples considering all these factors. We verify the merits of our proposed solution with extensive experiments on a real AIoT system with 50 clients, including 20 edge computers, 20 laptops, and 10 desktops. The experimental results validates that our solution achieves accurate and efficient selection of high-quality data samples, and consequently an FL model with a faster convergence speed and higher accuracy than that achieved by existing solutions.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488723"}, {"primary_key": "2186912", "vector": [], "sparse_vector": [], "title": "Efficient Learning-based Scheduling for Information Freshness in Wireless Networks.", "authors": ["<PERSON>"], "summary": "Motivated by the recent trend of integrating artificial intelligence into the Internet-of-Things (IoT), we consider the problem of scheduling packets from multiple sensing sources to a central controller over a wireless network. Here, packets from different sensing sources have different values or degrees of importance to the central controller for intelligent decision making. In such a setup, it is critical to provide timely and valuable information for the central controller. In this paper, we develop a parameterized maximum-weight type scheduling policy that combines both the AoI metrics and Upper Confidence Bound (UCB) estimates in its weight measure with parameter η. Here, UCB estimates balance the tradeoff between exploration and exploitation in learning and are critical for yielding a small cumulative regret. We show that our proposed algorithm yields the running average total age at most by O(N 2 η). We also prove that our proposed algorithm achieves the cumulative regret over time horizon T at most by O(NT/η+ √{NTlogT} ). This reveals a tradeoff between the cumulative regret and the running average total age: when increasing η, the cumulative regret becomes smaller, but is at the cost of increasing running average total age. Simulation results are provided to evaluate the efficiency of our proposed algorithm.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488709"}, {"primary_key": "2186915", "vector": [], "sparse_vector": [], "title": "VideoLoc: Video-based Indoor Localization with Text Information.", "authors": ["Shusheng Li", "<PERSON><PERSON>"], "summary": "Indoor localization serves an important role in various scenarios such as navigation in shopping malls or hospitals. However, the existing technology is usually based on additional deployment and the signals suffer from strong environmental interference in the complex indoor environment. In this paper, we propose video-based indoor localization with text information (i.e. \"VideoLoc\") without the deployment of additional equipment. Videos taken by the phone carriers cover more critical information (e.g. logos in malls), while a single photo may fail to capture it. To reduce redundant information in the video, we propose key-frame selection based on deep learning model and clustering algorithm. Video frames are characterized with deep visual descriptors and the clustering algorithm efficiently clusters these descriptors into a set of non-overlapping snippets. We select keyframes from these non-overlapping snippets in terms of the cluster centroid that represents each snippet. Then, we propose text detection and recognition with the perspective transformation to make full use of stable and discriminative text information (e.g. logos or room numbers) in keyframes for localization. Finally, we obtain the location of the phone carrier via the triangulation algorithm. The experimental results show that VideoLoc achieves high precision of localization and is robust to dynamic environments.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488739"}, {"primary_key": "2186916", "vector": [], "sparse_vector": [], "title": "Your Home is Insecure: Practical Attacks on Wireless Home Alarm Systems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Wireless home alarm systems are being widely deployed, but their security has not been well studied. Existing attacks on wireless home alarm systems exploit the vulnerabilities of networking protocols while neglecting the problems arising from the physical component of IoT devices. In this paper, we present new event-eliminating and event-spoofing attacks on commercial wireless home alarm systems by interfering with the reed switch in almost all COTS alarm sensors. In both attacks, the external adversary uses his own magnet to control the state of the reed switch in order to either eliminate legitimate alarms or spoof false alarms. We also present a new battery-depletion attack with programmable electromagnets to deplete the alarm sensor's battery quickly and stealthily in hours which is expected to last a few years. The efficacy of our attacks is confirmed by detailed experiments on a representative Ring alarm system.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488873"}, {"primary_key": "2186917", "vector": [], "sparse_vector": [], "title": "Physical Layer Secure Communications Based on Collaborative Beamforming for UAV Networks: A Multi-objective Optimization Approach.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Unmanned aerial vehicle (UAV) communications and networks are promising technologies in the forthcoming fifth-generation wireless communications. However, they have the challenges for realizing secure communications. In this paper, we consider to construct a virtual antenna array consists UAV elements and use collaborative beamforming (CB) to achieve the UAV secure communications with different base stations (BSs), subject to the known and unknown eavesdroppers on the ground. To achieve a better secure performance, the UAV elements can fly to optimal positions with optimal excitation current weights for performing CB transmissions. However, this leads to extra motion energy consumptions. We formulate a secure communication multi-objective optimization problem (MOP) of UAV networks to simultaneously improve the total secrecy rates, total maximum sidelobe levels (SLLs) and total motion energy consumptions of UAVs by jointly optimizing the positions and excitation current weights of UAVs, and the order of communicating with different BSs. Due to the complexity and NP-hardness of the formulated MOP, we propose an improved multi-objective dragonfly algorithm with chaotic solution initialization and hybrid solution update operators (IMODACH) to solve the problem. Simulation results verify that the proposed IMODACH can effectively solve the formulated MOP and it has better performance than some other benchmark approaches.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488827"}, {"primary_key": "2186918", "vector": [], "sparse_vector": [], "title": "On Scheduling with AoI Violation Tolerance.", "authors": ["Chengzhang Li", "Qing<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study an Age of Information (AoI) scheduling problem where AoI for each source at the base station (BS) can tolerate occasional violations, which we define as a violation tolerance constraint. The problem is to determine whether a set of users with given AoI deadlines, tolerance rates, and packet loss rates (due to each source's channel condition) is schedulable, and if so find a feasible scheduler. We study two cases: (i) the stable tolerant case where the tolerance rate is higher than the packet loss rate for all sources; (ii) the unstable tolerant case where the tolerance rate is lower than the packet loss rate for at least one source. For stable tolerant case, we design an algorithm called stable tolerant scheduler (STS), which can find a feasible scheduler for any network when system load is no greater than ln 2. For unstable tolerance case, we develop unstable tolerant scheduler (UTS) and identify a schedulability condition for it. Through extensive simulations, we show that STS and UTS match our theoretical results.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488685"}, {"primary_key": "2186919", "vector": [], "sparse_vector": [], "title": "Robust Online Learning against Malicious Manipulation with Application to Network Flow Classification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Malicious data manipulation reduces the effectiveness of machine learning techniques, which rely on accurate knowledge of the input data. Motivated by real-world applications in network flow classification, we address the problem of robust online learning with delayed feedback in the presence of malicious data generators that attempt to gain favorable classification outcome by manipulating the data features. We propose online algorithms termed ROLC-NC and ROLC-C when the malicious data generators are non-clairvoyant and clairvoyant, respectively. We derive regret bounds for both algorithms and show that they are sub-linear under mild conditions. We further evaluate the proposed algorithms in network flow classification via extensive experiments using real-world data traces. Our experimental results demonstrate that both algorithms can approach the performance of an optimal static offline classifier that is not under attack, while outperforming the same offline classifier when tested with a mixture of normal and manipulated data.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488890"}, {"primary_key": "2186920", "vector": [], "sparse_vector": [], "title": "Detecting Localized Adversarial Examples: A Generic Approach using Critical Region Analysis.", "authors": ["<PERSON><PERSON> Li", "Xu<PERSON><PERSON> Liu", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Deep neural networks (DNNs) have been applied in a wide range of applications, e.g., face recognition and image classification; however, they are vulnerable to adversarial examples. By adding a small amount of imperceptible perturbations, an attacker can easily manipulate the outputs of a DNN. Particularly, the localized adversarial examples only perturb a small and contiguous region of the target object, so that they are robust and effective in both digital and physical worlds. Although the localized adversarial examples have more severe real-world impacts than traditional pixel attacks, they have not been well addressed in the literature. In this paper, we propose a generic defense system called TaintRadar to accurately detect localized adversarial examples via analyzing critical regions that have been manipulated by attackers. The main idea is that when removing critical regions from input images, the ranking changes of adversarial labels will be larger than those of benign labels. Compared with existing defense solutions, TaintRadar can effectively capture sophisticated localized partial attacks, e.g., the eye-glasses attack, while not requiring additional training or fine-tuning of the original model's structure. Comprehensive experiments have been conducted in both digital and physical worlds to verify the effectiveness and robustness of our defense.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488754"}, {"primary_key": "2186923", "vector": [], "sparse_vector": [], "title": "To Talk or to Work: Flexible Communication Compression for Energy Efficient Federated Learning over Heterogeneous Mobile Edge Devices.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Mia<PERSON> Pan", "<PERSON>"], "summary": "Recent advances in machine learning, wireless communication, and mobile hardware technologies promisingly enable federated learning (FL) over massive mobile edge devices, which opens new horizons for numerous intelligent mobile applications. Despite the potential benefits, FL imposes huge communication and computation burdens on participating devices due to periodical global synchronization and continuous local training, raising great challenges to battery constrained mobile devices. In this work, we target at improving the energy efficiency of FL over mobile edge networks to accommodate heterogeneous participating devices without sacrificing the learning performance. To this end, we develop a convergence-guaranteed FL algorithm enabling flexible communication compression. Guided by the derived convergence bound, we design a compression control scheme to balance the energy consumption of local computing (i.e., \"working\") and wireless communication (i.e., \"talking\") from the long-term learning perspective. In particular, the compression parameters are elaborately chosen for FL participants adapting to their computing and communication environments. Extensive simulations are conducted using various datasets to validate our theoretical analysis, and the results also demonstrate the efficacy of the proposed scheme in energy saving.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488839"}, {"primary_key": "2186925", "vector": [], "sparse_vector": [], "title": "SmartDistance: A Mobile-based Positioning System for Automatically Monitoring Social Distance.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Coronavirus disease 2019 (COVID-19) has resulted in an ongoing pandemic. Since COVID-19 spreads mainly via close contact among people, social distancing has become an effective manner to slow down the spread. However, completely forbidding close contact can also lead to unacceptable damage to the society. Thus, a system that can effectively monitor people's social distance and generate corresponding alerts when a high infection probability is detected is in urgent need. In this paper, we propose SmartDistance, a smartphone based software framework that monitors people's interaction in an effective manner, and generates a reminder whenever the infection probability is high. Specifically, SmartDistance dynamically senses both the relative distance and orientation during social interaction with a well-designed relative positioning system. In addition, it recognizes different events (e.g., speaking, coughing) and determines the infection space through a droplet transmission model. With event recognition and relative positioning, SmartDistance effectively detects risky social interaction, generates an alert immediately, and records the relevant data for close contact reporting. We prototype SmartDistance on different Android smartphones, and the evaluation shows it reduces the false positive rate from 33% to 1% and the false negative rate from 5% to 3% in infection risk detection.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488735"}, {"primary_key": "2186926", "vector": [], "sparse_vector": [], "title": "Train Once, Locate Anytime for Anyone: Adversarial Learning based Wireless Localization.", "authors": ["Danyang Li", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Among numerous indoor localization systems, WiFi fingerprint-based localization has been one of the most attractive solutions, which is known to be free of extra infrastructure and specialized hardware. To push forward this approach for wide deployment, three crucial goals on delightful deployment ubiquity, high localization accuracy, and low maintenance cost are desirable. However, due to severe challenges about signal variation, device heterogeneity, and database degradation root in environmental dynamics, pioneer works usually make a trade-off among them. In this paper, we propose iToLoc, a deep learning based localization system that achieves all three goals simultaneously. Once trained, iToLoc will provide accurate localization service for everyone using different devices and under diverse network conditions, and automatically update itself to maintain reliable performance anytime. iToLoc is purely based on WiFi fingerprints without relying on specific infrastructures. The core components of iToLoc are a domain adversarial neural network and a co-training based semi-supervised learning framework. Extensive experiments across 7 months with 8 different devices demonstrate that iToLoc achieves remarkable performance with an accuracy of 1.92m and > 95% localization success rate. Even 7 months after the original fingerprint database was established, the rate still maintains > 90%, which significantly outperforms previous works.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488693"}, {"primary_key": "2186927", "vector": [], "sparse_vector": [], "title": "Privacy Budgeting for Growing Machine Learning Datasets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The wide deployment of machine learning (ML) models and service APIs exposes the sensitive training data to untrusted and unknown parties, such as end-users and corporations. It is important to preserve data privacy in the released ML models. An essential issue with today's privacy-preserving ML platforms is a lack of concern on the tradeoff between data privacy and model utility: a private datablock can only be accessed a finite number of times as each access is privacy-leaking. However, it has never been interrogated whether such privacy leaked in the training brings good utility. We propose a differentially-private access control mechanism on the ML platform to assign datablocks to queries. Each datablock arrives at the platform with a privacy budget, which would be consumed at each query access. We aim to make the most use of the data under the privacy budget constraints. In practice, both datablocks and queries arrive continuously so that each access decision has to be made without knowledge about the future. Hence we propose online algorithms with a worst-case performance guarantee. Experiments on a variety of settings show our privacy budgeting scheme yields high utility on ML platforms.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488920"}, {"primary_key": "2186930", "vector": [], "sparse_vector": [], "title": "Medley: Predicting Social Trust in Time-Varying Online Social Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Social media, such as Reddit, has become a norm in our daily lives, where users routinely express their attitude using upvotes (likes) or downvotes. These social interactions may encourage users to interact frequently and form strong ties of trust between one another. It is therefore important to predict social trust from these interactions, as they facilitate routine features in social media, such as online recommendation and advertising.Conventional methods for predicting social trust often accept static graphs as input, oblivious of the fact that social interactions are time-dependent. In this work, we propose Medley, to explicitly model users' time-varying latent factors and to predict social trust that varies over time. We propose to use functional time encoding to capture continuous-time features and employ attention mechanisms to assign higher importance weights to social interactions that are more recent. By incorporating topological structures that evolve over time, our framework can infer pairwise social trust based on past interactions. Our experiments on benchmarking datasets show that <PERSON><PERSON> is able to utilize time-varying interactions effectively for predicting social trust, and achieves an accuracy that is up to 26% higher over its alternatives.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488814"}, {"primary_key": "2186934", "vector": [], "sparse_vector": [], "title": "Prison Break of Android Reflection Restriction and Defense.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Junzhou Luo"], "summary": "Java reflection technique is pervasively used in the Android system. To reduce the risk of reflection abuse, Android restricts the use of reflection at the Android Runtime (ART) to hide potentially dangerous methods/fields. We perform the first comprehensive study of the reflection restrictions and have discovered three novel approaches to bypass the reflection restrictions. Novel reflection-based attacks are also presented, including the password stealing attack. To mitigate the threats, we analyze these restriction bypassing approaches and find three techniques crucial to these approaches, i.e., double reflection, memory manipulation, and inline hook. We propose a defense mechanism that consists of classloader double checker, ART variable protector, and ART method protector, to prohibit the reflection restriction bypassing. Finally, we design and implement an automatic reflection detection framework and have discovered 5,531 reflection powered apps out of 100,000 downloaded apps, which are installed on our defense enabled Android system of a Google Pixel 2 to evaluate the effectiveness and efficiency of our defense mechanism. Extensive empirical experiment results demonstrate that our defense enabled system can accurately obstruct the malicious reflection attempts.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488788"}, {"primary_key": "2186938", "vector": [], "sparse_vector": [], "title": "ProHiCo: A Probabilistic Framework to Hide Communities in Large Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "While community detection has been one of the cornerstones in network analysis and data science, its opposite, community obfuscation, has received little attention in recent years. With the increasing awareness of data security and privacy protection, the need to understand the impact of such attacks on traditional community detection algorithms emerges. To this end, we investigate the community obfuscation problem which aims to hide a target set of communities from being detected by perturbing the network structure. We identify and analyze the Matthew effect incurred by the classical quality function based methods, which essentially results in the imbalanced allocation of perturbation resources. To mitigate such effect, we propose a probabilistic framework named as ProHiCo to hide communities. The key idea of ProHiCo is to first allocate the resource of perturbations randomly and fairly and then choose the appropriate edges to perturb via likelihood minimization. Our ProHiCo framework provides the additional freedom to choose the generative graph model with community structure. By incorporating the stochastic block model and its degree-corrected variant into the ProHiCo framework, we develop two scalable and effective algorithms called SBM and DCSBM. Via extensive experiments on 8 real-world networks and 5 community detection algorithms, we show that both SBM and DCSBM are about 30x faster than the prominent baselines in the literature when there are around 500 target communities, while their performance is comparable to the baselines.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488870"}, {"primary_key": "2186939", "vector": [], "sparse_vector": [], "title": "EdgeSharing: Edge Assisted Real-time Localization and Object Sharing in Urban Streets.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Collaborative object localization and sharing at smart intersections promises to improve situational awareness of traffic participants in key areas where hazards exist due to visual obstructions. By sharing a moving object's location between different camera-equipped devices, it effectively extends the vision of traffic participants beyond their field of view. However, accurately sharing objects between moving clients is extremely challenging due to the high accuracy requirements for localizing both the client position and positions of its detected objects. Therefore, we introduce EdgeSharing, a localization and object sharing system leveraging the resources of edge cloud platforms. EdgeSharing holds a real-time 3D feature map of its coverage region to provide accurate localization and object sharing service to the client devices passing through this region. We further propose several optimization techniques to increase the localization accuracy, reduce the bandwidth consumption and decrease the offloading latency of the system. The result shows that the system is able to achieve a mean vehicle localization error of 0.28-1.27 meters, an object sharing accuracy of 82.3%-91.4%, and a 54.7% object awareness increment in urban streets and intersections. In addition, the proposed optimization techniques reduce bandwidth consumption by 70.12% and end-to-end latency by 40.09%.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488830"}, {"primary_key": "2186941", "vector": [], "sparse_vector": [], "title": "LiveMap: Real-Time Dynamic Map in Automotive Edge Computing.", "authors": ["<PERSON><PERSON>", "Tao Han", "<PERSON> (<PERSON>) <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Autonomous driving needs various line-of-sight sensors to perceive surroundings that could be impaired under diverse environment uncertainties such as visual occlusion and extreme weather. To improve driving safety, we explore to wirelessly share perception information among connected vehicles within automotive edge computing networks. Sharing massive perception data in real time, however, is challenging under dynamic networking conditions and varying computation work-loads. In this paper, we propose LiveMap, a real-time dynamic map, that detects, matches, and tracks objects on the road with crowdsourcing data from connected vehicles in sub-second. We develop the data plane of LiveMap that efficiently processes individual vehicle data with object detection, projection, feature extraction, object matching, and effectively integrates objects from multiple vehicles with object combination. We design the control plane of LiveMap that allows adaptive offloading of vehicle computations, and develop an intelligent vehicle scheduling and offloading algorithm to reduce the offloading latency of vehicles based on deep reinforcement learning (DRL) techniques. We implement LiveMap on a small-scale testbed and develop a large-scale network simulator. We evaluate the performance of LiveMap with both experiments and simulations, and the results show LiveMap reduces 34.1% average latency than the baseline solution.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488872"}, {"primary_key": "2186944", "vector": [], "sparse_vector": [], "title": "Aion: A Bandwidth Optimized Scheduler with AoI Guarantee.", "authors": ["Qing<PERSON>", "Chengzhang Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper investigates bandwidth minimization under AoI constraints - a fundamental problem that has not been studied in AoI research. The problem is of critical importance in bandwidth-limited IoT environment when AoI is used as a constraint. We present a novel fast algorithm called Aion that can construct a scheduler to satisfy AoI constraints with strong theoretical guarantee in terms of minimizing required bandwidth. Specifically, we prove that the bandwidth required by Aion is minimum if the AoI constraint vector meets a special mathematical structure called Fractional Consecutively Divisible (FCD). In the general case when the given AoI constraint vector is not FCD, we prove that the bandwidth required by Aion is tightly upper bounded by a factor of the minimum. The results from this paper lay the foundation for future research on bandwidth minimization with AoI guarantee.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488781"}, {"primary_key": "2186945", "vector": [], "sparse_vector": [], "title": "A Worst-Case Approximate Analysis of Peak Age-of-Information Via Robust Queueing Approach.", "authors": ["Zhong<PERSON> Liu", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A new timeliness metric, called Age-of-Information (AoI), has recently attracted a lot of research interests for real-time applications with information updates. It has been extensively studied for various queueing models based on the probabilistic approaches, where the analyses heavily depend on the properties of specific distributions (e.g., the memoryless property of the exponential distribution or the i.i.d. assumption). In this work, we take an alternative new approach, the robust queueing approach, to analyze the Peak Age-of-Information (PAoI). Specifically, we first model the uncertainty in the stochastic arrival and service processes using uncertainty sets. This enables us to approximate the expected PAoI performance for very general arrival and service processes, including those exhibiting heavy-tailed behaviors or correlations, where traditional probabilistic approaches cannot be applied. We then derive a new bound on the PAoI in the single-source single-server setting. Furthermore, we generalize our analysis to two-source single-server systems with symmetric arrivals, which involves new challenges (e.g., the service times of the updates from two sources are coupled in one single uncertainty set). Finally, through numerical experiments, we show that our new bounds provide a good approximation for the expected PAoI. Compared to some well-known bounds in the literature (e.g., one based on <PERSON><PERSON>'s bound under the i.i.d. assumption) that tends to be inaccurate under light load, our new approximation is accurate under both light and high loads, both of which are critical scenarios for the AoI performance.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488894"}, {"primary_key": "2186947", "vector": [], "sparse_vector": [], "title": "AMT: Acoustic Multi-target Tracking with Smartphone MIMO System.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Acoustic target tracking has shown great advantages for device-free human-machine interaction over vision/RF based mechanisms. However, existing approaches for portable devices solely track single target, incapable for the ubiquitous and highly challenging multi-target situation such as double-hand multimedia controlling and multi-player gaming. In this paper, we propose AMT, a pioneering smartphone MIMO system to achieve centimeter-level multi-target tracking. Targets' absolute distance are simultaneously ranged by performing multi-lateration locating with multiple speaker-microphone pairs. The unique challenge raised by MIMO is the superposition of multisource signals due to the cross-correlation among speakers. We tackle this challenge by applying Zadoff-Chu(ZC) sequences with strong auto-correlation and weak cross-correlation. The most distinguishing advantage of AMT lies in the elimination of target raised multipath effect, which is commonly ignored in previous work by hastily assuming targets as particles. Concerning the multipath echoes reflected by each non-particle target, we define the novel concept of primary echo to best represent target movement. AMT then improves tracking accuracy by detecting primary echo and filtering out minor echoes. Implemented on commercial smartphones, AMT achieves on average 1.13 cm and 2.46 cm error for single and double target tracking respectively and on average 97% accuracy for 6 controlling gestures recognition.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488768"}, {"primary_key": "2186948", "vector": [], "sparse_vector": [], "title": "Bipartite Graph Matching Based Secret Key Generation.", "authors": ["Hongbo Liu", "<PERSON>", "Yanzhi Ren", "<PERSON><PERSON>"], "summary": "The physical layer secret key generation exploiting wireless channel reciprocity has attracted considerable attention in the past two decades. On-going research have demonstrated its viability in various radio frequency (RF) systems. Most of existing work rely on quantization technique to convert channel measurements into digital binaries that are suitable for secret key generation. However, non-simultaneous packet exchanges in time division duplex systems and noise effects in practice usually create random channel measurements between two users, leading to inconsistent quantization results and mismatched secret bits. While significant efforts were spent in recent research to mitigate such non-reciprocity, no efficient method has been found yet. Unlike existing quantization-based approaches, we take a different viewpoint and perform the secret key agreement by solving a bipartite graph matching problem. Specifically, an efficient dual-permutation secret key generation method, DP-SKG, is developed to match the randomly permuted channel measurements between a pair of users by minimizing their discrepancy holistically. DP-SKG allows two users to generate the same secret key based on the permutation order of channel measurements despite the non-reciprocity over wireless channels. Extensive experimental results show that DP-SKG could achieve error-free key agreement on received signal strength (RSS) with a low cost under various scenarios.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488848"}, {"primary_key": "2186949", "vector": [], "sparse_vector": [], "title": "DRL-OR: Deep Reinforcement Learning-based Online Routing for Multi-type Service Requirements.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Emerging applications raise critical QoS requirements for the Internet. The improvements of flow classification technologies, software defined networks (SDN), and programmable network devices make it possible to fast identify users' requirements and control the routing for fine-grained traffic flows. Meanwhile, the problem of optimizing the forwarding paths for traffic flows with multiple QoS requirements in an online fashion is not addressed sufficiently. To address the problem, we propose DRL-OR, an online routing algorithm using multi-agent deep reinforcement learning. DRL-OR organizes the agents to generate routes in a hop-by-hop manner, which inherently has good scalability. It adopts a comprehensive reward function, an efficient learning algorithm, and a novel deep neural network structure to learn an appropriate routing policy for different types of flow requirements. To guarantee the reliability and accelerate the online learning process, we further introduce safe learning mechanism to DRL-OR. We implement DRL-OR under SDN architecture and conduct Mininet-based experiments by using real network topologies and traffic traces. The results validate that DRL-OR can well satisfy the requirements of latency-sensitive, throughput-sensitive, latency-throughput-sensitive, and latency-loss-sensitive flows at the same time, while exhibiting great adaptiveness and reliability under the scenarios of link failure, traffic change, and partial deployment.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488736"}, {"primary_key": "2186950", "vector": [], "sparse_vector": [], "title": "DeepLoRa: Learning Accurate Path Loss Model for Long Distance Links in LPWAN.", "authors": ["<PERSON>", "Yuguang Yao", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "LoRa (Long Range) is an emerging wireless technology that enables long-distance communication and keeps low power consumption. Therefore, LoRa plays a more and more important role in Low-Power Wide-Area Networks (LPWANs), which easily extend many large-scale Internet of Things (IoT) applications in diverse scenarios (e.g., industry, agriculture, city). In lots of environments where various types of land-covers usually exist, it is challenging to precisely predict a LoRa link's path loss. As a result, how to deploy LoRa gateways to ensure reliable coverage and develop precise fingerprint-based localization becomes a difficult issue in practice. In this paper, we propose DeepLoRa, a deep learning-based approach to accurately estimate the path loss of long-distance links in complex environments. Specifically, DeepLoRa relies on remote sensing to automatically recognize land-cover types along a LoRa link. Then, DeepLoRa utilizes Bi-LSTM (Bidirectional Long Short Term Memory) to develop a land-cover aware path loss model. We implement DeepLoRa and use the data gathered from a real LoRaWAN deployment on campus to evaluate its performance extensively in terms of estimation accuracy and model transferability. The results show that DeepLoRa reduces the estimation error to less than 4 dB, which is 2× smaller than state-of-the-art models.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488784"}, {"primary_key": "2186951", "vector": [], "sparse_vector": [], "title": "Privacy-Preserving Outlier Detection with High Efficiency over Distributed Datasets.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The ability to detect outliers is crucial in data mining, with widespread usage in many fields, including fraud detection, malicious behavior monitoring, health diagnosis, etc. With the tremendous volume of data becoming more distributed than ever, global outlier detection for a group of distributed datasets is particularly desirable. In this work, we propose PIF (Privacy-preserving Isolation Forest), which can detect outliers for multiple distributed data providers with high efficiency and accuracy while giving certain security guarantees. To achieve the goal, PIF makes an innovative improvement to the traditional iForest algorithm, enabling it in distributed environments. With a series of carefully-designed algorithms, each participating party collaborates to build an ensemble of isolation trees efficiently without disclosing sensitive information of data. Besides, to deal with complicated real-world scenarios where different kinds of partitioned data are involved, we propose a comprehensive schema that can work for both horizontally and vertically partitioned data models. We have implemented our method and evaluated it with extensive experiments. It is demonstrated that PIF can achieve comparable AUC to existing iForest on average and maintains a linear time complexity without privacy violation.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488710"}, {"primary_key": "2186952", "vector": [], "sparse_vector": [], "title": "Cost-Effective Federated Learning Design.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Federated learning (FL) is a distributed learning paradigm that enables a large number of devices to collaboratively learn a model without sharing their raw data. Despite its practical efficiency and effectiveness, the iterative on-device learning process incurs a considerable cost in terms of learning time and energy consumption, which depends crucially on the number of selected clients and the number of local iterations in each training round. In this paper, we analyze how to design adaptive FL that optimally chooses these essential control variables to minimize the total cost while ensuring convergence. Theoretically, we analytically establish the relationship between the total cost and the control variables with the convergence upper bound. To efficiently solve the cost minimization problem, we develop a low-cost sampling-based algorithm to learn the convergence related unknown parameters. We derive important solution properties that effectively identify the design principles for different metric preferences. Practically, we evaluate our theoretical results both in a simulated environment and on a hardware prototype. Experimental evidence verifies our derived properties and demonstrates that our proposed solution achieves near-optimal performance for various datasets, different machine learning models, and heterogeneous system settings.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488679"}, {"primary_key": "2186953", "vector": [], "sparse_vector": [], "title": "A Fast-Convergence Routing of the Hot-Potato.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Interactions between the intra- and inter-domain routing protocols received little attention despite playing an important role in forwarding transit traffic. More precisely, by default, IGP distances are taken into account by BGP to select the closest exit gateway for the transit traffic (hot-potato routing). Upon an IGP update, the new best gateway may change and should be updated through the (full) re-convergence of BGP, causing superfluous BGP processing and updates in many cases. We propose OPTIC (Optimal Protection Technique for Inter-intra domain Convergence), an efficient way to assemble both protocols without losing the hot-potato property. OPTIC pre-computes sets of gateways (BGP next-hops) shared by groups of prefixes. Such sets are guaranteed to contain the post-convergence gateway after any single IGP event for the grouped prefixes. The new optimal exits can be found through a single walk-through of each set, allowing the transit traffic to benefit from optimal BGP routes almost as soon as the IGP converges. Compared to vanilla BGP, OPTIC's structures allow it to consider a reduced number of entries: this number can be reduced by 99% for stub networks. The update of OPTIC's structures, which is not required as long as border routers remain at least bi-connected, scales linearly in time with its number of groups.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488880"}, {"primary_key": "2186954", "vector": [], "sparse_vector": [], "title": "Rate Region of Scheduling a Wireless Network with Discrete Propagation Delays.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the link scheduling problem of wireless networks where signal propagation delays are multiples of certain time interval. The problem can be modeled as a character of the independent sets of periodic graphs, which have infinitely many vertices. We show that the rate region of scheduling a network can be achieved using collision-free, periodic schedules, and derive a graphical approach to explicitly characterize the rate region. In particular, a collision-free schedule can be equivalent to a path in a graph called the scheduling graph induced by the network collision profile and the propagation delays, and hence the rate region is equal to the convex hull of the rate vectors associated with the cycles of the scheduling graph, which have bounded length. With the maximal independent set problem as a special case, calculating the whole rate region is NP hard and also hard to approximate. By exploring a partial order on the paths, we derive an algorithm to calculate a subset of the rate region more efficiently. Our results are also of independent interest for periodic graphs.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488895"}, {"primary_key": "2186957", "vector": [], "sparse_vector": [], "title": "Context-aware Website Fingerprinting over Encrypted Proxies.", "authors": ["<PERSON><PERSON> Ma", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Website fingerprinting (WFP) could infer which websites a user is accessing via an encrypted proxy by passively inspecting the traffic between the user and the proxy. The key to WFP is designing a classifier capable of distinguishing traffic characteristics of accessing different websites. However, when deployed in real-life networks, a well-trained classifier may face a significant obstacle of training-testing asymmetry, which fundamentally limits its practicability. Specifically, although pure traffic samples can be collected in a controlled (clean) testbed for training, the classifier may fail to extract such pure traffic samples as its input from raw complicated traffic for testing. In this paper, we are interested in encrypted proxies that relay connections between the user and the proxy individually (e.g., Shadows<PERSON>), and design a context-aware system using built-in spatial-temporal flow correlation to address the obstacle. Extensive experiments demonstrate that our system does not only enable WFP against a popular type of encrypted proxies practical, but also achieves better performance than ideally training/testing pure samples.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488676"}, {"primary_key": "2186962", "vector": [], "sparse_vector": [], "title": "Learning-Driven Decentralized Machine Learning in Resource-Constrained Wireless Edge Computing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Data generated at the network edge can be processed locally by leveraging the paradigm of edge computing. To fully utilize the widely distributed data, we concentrate on a wireless edge computing system that conducts model training using decentralized peer-to-peer (P2P) methods. However, there are two major challenges on the way towards efficient P2P model training: limited resources (e.g., network bandwidth and battery life of mobile edge devices) and time-varying network connectivity due to device mobility or wireless channel dynamics, which have received less attention in recent years. To address these two challenges, this paper adaptively constructs a dynamic and efficient P2P topology, where model aggregation occurs at the edge devices. In a nutshell, we first formulate the topology construction for P2P learning (TCPL) problem with resource constraints as an integer programming problem. Then a learning-driven method is proposed to adaptively construct a topology at each training epoch. We further give the convergence analysis on training machine learning models even with non-convex loss functions. Extensive simulation results show that our proposed method can improve the model training efficiency by about 11% with resource constraints and reduce the communication cost by about 30% under the same accuracy requirement compared to the benchmarks.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488817"}, {"primary_key": "2186963", "vector": [], "sparse_vector": [], "title": "Setting the Record Straighter on <PERSON>.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Shadow banning consists for an online social net-work in limiting the visibility of some of its users, without them being aware of it. Twitter declares that it does not use such a practice, sometimes arguing about the occurrence of \"bugs\" to justify restrictions on some users. This paper is the first to address the plausibility of shadow banning on a major online platform, by adopting both a statistical and a graph topological approach.We first conduct an extensive data collection and analysis campaign, gathering occurrences of visibility limitations on user profiles (we crawl more than 2.5 millions of them). In such a black-box observation setup, we highlight the salient user profile features that may explain a banning practice (using machine learning predictors). We then pose two hypotheses for the phenomenon: i) limitations are bugs, as claimed by Twitter, and ii) shadow banning propagates as an epidemic on user-interaction ego-graphs. We show that hypothesis i) is statistically unlikely with regards to the data we collected. We then show some interesting correlation with hypothesis ii), suggesting that the interaction topology is a good indicator of the presence of groups of shadow banned users on the service.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488792"}, {"primary_key": "2186968", "vector": [], "sparse_vector": [], "title": "AMIS: Edge Computing Based Adaptive Mobile Video Streaming.", "authors": ["<PERSON>", "Jin<PERSON> Zheng", "<PERSON>", "<PERSON><PERSON>", "Mianxiong Dong", "<PERSON>"], "summary": "This work proposes AMIS, an edge computing-based adaptive video streaming system. AMIS explores the power of edge computing in three aspects. First, with video contents pre-cached in the local buffer, AMIS is content-aware which adapts the video playout strategy based on the scene features of video contents and quality of experience (QoE) of users. Second, AMIS is channel-aware which measures the channel conditions in real-time and estimates the wireless bandwidth. Third, by integrating the content features and channel estimation, AMIS applies the deep reinforcement learning model to optimize the playout strategy towards the best QoE. Therefore, AMIS is an intelligent content- and channel-aware scheme which fully explores the intelligence of edge computing and adapts to general environments and QoE requirements. Using trace-driven simulations, we show that AMIS can succeed in improving the average QoE by 14%-46% as compared to the state-of-the-art adaptive bitrate algorithms.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488426"}, {"primary_key": "2186972", "vector": [], "sparse_vector": [], "title": "Coexistence of Wi-Fi 6E and 5G NR-U: Can We Do Better in the 6 GHz Bands?", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Regulators in the US and Europe have stepped up their efforts to open the 6 GHz bands for unlicensed access. The two unlicensed technologies likely to operate and coexist in these bands are Wi-Fi 6E and 5G New Radio Unlicensed (NR-U). The greenfield 6 GHz bands allow us to take a fresh look at the coexistence between Wi-Fi and 3GPP-based unlicensed technologies. In this paper, using tools from stochastic geometry, we study the impact of Multi User Orthogonal Frequency Division Multiple Access, i.e., MU OFDMA-a feature introduced in 802.11ax-on this coexistence issue. Our results reveal that by disabling the use of the legacy contention mechanism (and allowing only MU OFDMA) for uplink access in Wi-Fi 6E, the performance of both NR-U networks and uplink Wi-Fi 6E can be improved. This is indeed feasible in the 6 GHz bands, where there are no operational Wi-Fi or NR-U users. In so doing, we also highlight the importance of accurate channel sensing at the entity that schedules uplink transmissions in Wi-Fi 6E and NR-U. If the channel is incorrectly detected as idle, factors that improve the uplink performance of one technology contribute negatively to the performance of the other technology.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488780"}, {"primary_key": "2186973", "vector": [], "sparse_vector": [], "title": "Age-Dependent Distributed MAC for Ultra-Dense Wireless Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider an ultra-dense wireless network with N channels and M = N devices. Messages with fresh information are generated at each device according to a random process and need to be transmitted to an access point. The value of a message decreases as it ages, so each device searches for an idle channel to transmit the message as soon as it can. However, each channel probing is associated with a fixed cost (energy), so a device needs to adapt its probing rate based on the \"age\" of the message. At each device, the design of the optimal probing strategy can be formulated as an infinite horizon Markov Decision Process (MDP) where the devices compete with each other to find idle channels. While it is natural to view the system as a Bayesian game, it is often intractable to analyze such a system. Thus, we use the Mean Field Game (MFG) approach to analyze the system in a large-system regime, where the number of devices is very large, to understand the structure of the problem and to find efficient probing strategies. We present an analysis based on the MFG perspective. We begin by characterizing the space of valid policies and use this to show the existence of a Mean Field Nash Equilibrium (MFNE) in a constrained set for any general increasing cost functions with diminishing rewards. Further we provide an algorithm for computing the equilibrium for any given device, and the corresponding age-dependent channel probing policy.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488674"}, {"primary_key": "2186974", "vector": [], "sparse_vector": [], "title": "Reversible Models for Wireless Multi-Channel Multiple Access.", "authors": ["<PERSON>"], "summary": "This paper presents a network layer model for a wireless multiple access system with both persistent and nonpersistent users. There is a single access point with multiple identical channels. Each user who wants to send a file first scans a subset of the channels to find one that is idle. If at least one idle channel is found, the user transmits a file over that channel. If no idle channel is found, a persistent user will repeat the access attempt at a later time, while a nonpersistent user will leave. This is a useful mathematical model for situations where a group of persistent users stay near an access point for an extended period of time while nonpersistent users come and go. Users have heterogeneous activity behavior, file upload rates, and service durations. The system is a complex multi-dimensional Markov chain. The steady state probabilities are found by exploiting a latent reversibility property and leveraging a discrete Fourier transform. This enables simple expressions for throughput and blocking probability.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488745"}, {"primary_key": "2186977", "vector": [], "sparse_vector": [], "title": "Store Edge Networked Data (SEND): A Data and Performance Driven Edge Storage Framework.", "authors": ["Adrian<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The number of devices that the edge of the Internet accommodates and the volume of the data these devices generate are expected to grow dramatically in the years to come. As a result, managing and processing such massive data amounts at the edge becomes a vital issue. This paper proposes \"Store Edge Networked Data\" (SEND), a novel framework for in-network storage management realized through data repositories deployed at the network edge. SEND considers different criteria (e.g., data popularity, data proximity from processing functions at the edge) to intelligently place different categories of raw and processed data at the edge based on system-wide identifiers of the data context, called labels. We implement a data repository prototype on top of the Google file system, which we evaluate based on real-world datasets of images and Internet of Things device measurements. To scale up our experiments, we perform a network simulation study based on synthetic and real-world datasets evaluating the performance and trade-offs of the SEND design as a whole. Our results demonstrate that SEND achieves data insertion times of 0.06ms-0.9ms, data lookup times of 0.5ms-5.3ms, and on-time completion of up to 92% of user requests for the retrieval of raw and processed data.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488804"}, {"primary_key": "2186978", "vector": [], "sparse_vector": [], "title": "Invisible Poison: A Blackbox Clean Label Backdoor Attack to Deep Neural Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Chunsheng Xin", "<PERSON><PERSON>"], "summary": "This paper reports a new clean-label data poisoning backdoor attack, named Invisible Poison, which stealthily and aggressively plants a backdoor in neural networks. It converts a regular trigger to a noised trigger that can be easily concealed inside images for training NN, with the objective to plant a backdoor that can be later activated by the trigger. Compared with existing data poisoning backdoor attacks, this newfound attack has the following distinct properties. First, it is a blackbox attack, requiring zero-knowledge of the target model. Second, this attack utilizes \"invisible poison\" to achieve stealthiness where the trigger is disguised as `noise', and thus can easily evade human inspection. On the other hand, this noised trigger remains effective in the feature space to poison training data. Third, the attack is practical and aggressive. A backdoor can be effectively planted with a small amount of poisoned data and is robust to most data augmentation methods during training. The attack is fully tested on multiple benchmark datasets including MNIST, Cifar10, and ImageNet10, as well as application specific data sets such as Yahoo Adblocker and GTSRB. Two countermeasures, namely Supervised and Unsupervised Poison Sample Detection, are introduced to defend the attack.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488902"}, {"primary_key": "2186979", "vector": [], "sparse_vector": [], "title": "Fault-Tolerant Energy Management for Real-Time Systems with Weakly Hard QoS Assurance.", "authors": ["<PERSON><PERSON>"], "summary": "While energy consumption is the primary concern for the design of real-time embedded systems, fault-tolerance and quality of service (QoS) are becoming increasingly important in the development of today's pervasive computing systems. In this work, we study the problem of energy-aware standby-sparing for weakly hard real-time embedded systems. The standby-sparing systems adopt a primary processor and a spare processor to provide fault tolerance for both permanent and transient faults. In order to reduce energy consumption for such kind of systems, we proposed two novel scheduling schemes: one for (1,1)-hard tasks and one for general (m,k)-hard tasks which require that at least m out of any k consecutive jobs of a task meet their deadlines. Through extensive evaluations, our results demonstrate that the proposed techniques significantly outperform the previous research in reducing energy consumption for both (1,1)-hard task sets and general (m,k)-hard task sets while assuring fault tolerance through standby-sparing.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488822"}, {"primary_key": "2186980", "vector": [], "sparse_vector": [], "title": "AdaPDP: Adaptive Personalized Differential Privacy.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Fenghua Li", "<PERSON>"], "summary": "Users usually have different privacy demands when they contribute individual data to a dataset that is maintained and queried by others. To tackle this problem, several personalized differential privacy (PDP) mechanisms have been proposed to render statistical information of the entire dataset without revealing individual privacy. However, existing mechanisms produce query results with low accuracy, which leads to poor data utility. This is primarily because (1) some users are over protected; (2) utility is not explicitly included in the design objective. Poor data utility impedes the adoption of PDP in the real-world applications. In this paper, we present an adaptive personalized differential privacy framework, called AdaPDP. Specifically, to maximize data utility in different cases, AdaPDP adaptively selects underlying noise generation algorithms and calculates the corresponding parameters based on the type of query functions, data distributions and privacy settings. In addition, AdaPDP performs multiple rounds of utility-aware sampling to satisfy different privacy requirements for users. Our privacy analysis shows that the proposed framework renders rigorous privacy guarantee. We conduct extensive experiments on synthetic and real-world datasets to demonstrate the much less utility losses of the proposed framework over various query functions.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488825"}, {"primary_key": "2186981", "vector": [], "sparse_vector": [], "title": "On the Performance of Pipelined HotStuff.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "HotStuff is a state-of-the-art Byzantine fault-tolerant consensus protocol. It can be pipelined to build large-scale blockchains. One of its variants called LibraBFT is adopted in Facebook's Libra blockchain. Although it is well known that pipelined HotStuff is secure against up to 1/3 of Byzantine nodes, its performance in terms of throughput and delay is still under-explored. In this paper, we develop a multi-metric evaluation framework to quantitatively analyze pipelined HotStuff's performance with respect to its chain growth rate, chain quality, and latency. We then propose several attack strategies and evaluate their effects on the performance of pipelined HotStuff. Our analysis shows that the chain growth rate (resp, chain quality) of pipelined HotStuff under our attacks can drop to as low as 4/9 (resp, 12/17) of that without attacks when 1/3 nodes are Byzantine. As another application, we use our framework to evaluate certain engineering optimizations adopted by LibraBFT. We find that these optimizations make the system more vulnerable to our attacks than the original pipelined HotStuff. Finally, we provide two countermeasures to thwart these attacks. We hope that our studies can shed light on the rigorous understanding of the state-of-the-art pipelined HotStuff protocol as well as its variants.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488706"}, {"primary_key": "2186983", "vector": [], "sparse_vector": [], "title": "π-ROAD: a Learn-as-You-Go Framework for On-Demand Emergency Slices in V2X Scenarios.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Vehicle-to-everything (V2X) is expected to become one of the main drivers of 5G business in the near future. Dedicated network slices are envisioned to satisfy the stringent requirements of advanced V2X services, such as autonomous driving, aimed at drastically reducing road casualties. However, as V2X services become more mission-critical, new solutions need to be devised to guarantee their successful service delivery even in exceptional situations, e.g. road accidents, congestion, etc. In this context, we propose π-ROAD, a deep learning framework to automatically learn regular mobile traffic patterns along roads, detect non-recurring events and classify them by severity level. π-ROAD enables operators to proactively instantiate dedicated Emergency Network Slices (ENS) as needed while re-dimensioning the existing slices according to their service criticality level. Our framework is validated by means of real mobile network traces collected within 400 km of a highway in Europe and augmented with publicly available information on related road events. Our results show that π-ROAD successfully detects and classifies non-recurring road events and reduces up to 30% the impact of ENS on already running services.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488677"}, {"primary_key": "2186984", "vector": [], "sparse_vector": [], "title": "Going the Extra Mile with Disaster-Aware Network Augmentation.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Network outages have significant economic and societal costs. While network operators have become adept at managing smaller failures, this is not the case for larger, regional failures such as natural disasters. Although it is not possible, and certainly not economic, to prevent all potential disaster damage and impact, we can reduce their impact by adding cost-efficient, geographically redundant, cable connections to the network.In this paper, we provide algorithms for finding cost-efficient, disaster-aware cable routes based on empirical hazard data. In contrast to previous work, our approach finds disaster-aware routes by considering the impact of a large set of input disasters on the network as a whole, as well as on the individual cable. For this, we propose the Disaster-Aware Network Augmentation Problem of finding a new cable connection that minimizes a function of disaster impact and cable cost. We prove that this problem is NP-hard and give an exact algorithm, as well as a heuristic, for solving it. Our algorithms are applicable to both planar and geographical coordinates. Using actual seismic hazard data, we demonstrate that by applying our algorithms, network operators can cost-efficiently raise the resilience of their network and future cable connections.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488861"}, {"primary_key": "2186988", "vector": [], "sparse_vector": [], "title": "Optimal Online Balanced Graph Partitioning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Distributed applications generate a significant amount of network traffic. By collocating frequently communicating nodes (e.g., virtual machines) on the same clusters (e.g., server or rack), we can reduce the network load and improve application performance. However, the communication pattern of different applications is often unknown a priori and may change over time, hence it needs to be learned in an online manner. This paper revisits the online balanced partitioning problem that asks for an algorithm that strikes an optimal tradeoff between the benefits of collocation (i.e., lower network load) and its costs (i.e., migrations). Our first contribution is a significantly improved deterministic lower bound of Ω(k · ℓ) on the competitive ratio, where ℓ is the number of clusters and k is the cluster size, even for a scenario in which the communication pattern is static and can be perfectly partitioned; we also provide an asymptotically tight upper bound of O(k·ℓ) for this scenario. For k = 3, we contribute an asymptotically tight upper bound of Θ(ℓ) for the general model in which the communication pattern can change arbitrarily over time. We improve the result for k = 2 by providing a strictly 6-competitive upper bound for the general model.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488824"}, {"primary_key": "2186989", "vector": [], "sparse_vector": [], "title": "Robust 360° Video Streaming via Non-Linear Sampling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We propose CoRE, a 360° video streaming approach that reduces bandwidth requirements compared to transferring the entire 360° video. CoRE uses non-linear sampling in both the spatial and temporal domains to achieve robustness to view direction prediction error and to transient wireless network bandwidth fluctuation. Each CoRE frame samples the environment in all directions, with full resolution over the predicted field of view and gradually decreasing resolution at the periphery, so that missing pixels are avoided, irrespective of the view prediction error magnitude. A CoRE video chunk has a main part at full frame rate, and an extension part at a gradually decreasing frame rate, which avoids stalls while waiting for a delayed transfer. We evaluate a prototype implementation of CoRE through trace-based experiments and a user study, and find that, compared to tiling with low-resolution padding, CoRE reduces data transfer amounts, stalls, and H.264 decoding overhead, increases frame rates, and eliminates missing pixels.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488700"}, {"primary_key": "2186990", "vector": [], "sparse_vector": [], "title": "WebMythBusters: An In-depth Study of Mobile Web Experience.", "authors": ["Seonghoon Park", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The quality of experience (QoE) is an important issue for users when accessing the web. Although many metrics have been designed to estimate the QoE in the desktop environment, few studies have confirmed whether the QoE metrics are valid in the mobile environment. In this paper, we ask questions regarding the validity of using desktop-based QoE metrics for the mobile web and find answers. We first classify the existing QoE metrics into several groups according to three criteria and then identify the differences between the mobile and desktop environments. Based on the analysis, we ask three research questions and develop a system, called WebMythBusters, for collecting and analyzing mobile web experiences. Through an extensive analysis of the collected user data, we find that (1) the metrics focusing on fast completion or fast initiation of the page loading process cannot estimate the actual QoE, (2) the conventional scheme of calculating visual progress is not appropriate, and (3) focusing only on the above-the-fold area is not sufficient in the mobile environment. The findings indicate that QoE metrics designed for the desktop environment are not necessarily adequate for the mobile environment, and appropriate metrics should be devised to reflect the mobile web experience.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488671"}, {"primary_key": "2186991", "vector": [], "sparse_vector": [], "title": "BLESS: BLE-aided Swift Wi-Fi Scanning in Multi-protocol IoT Networks.", "authors": ["Wonbin Park", "Dokyun Ryoo", "<PERSON><PERSON>", "Saewoong Bahk"], "summary": "Wi-Fi scanning that searches neighboring access points (APs) is an essential prerequisite for Wi-Fi operations such as initial association and handover. As the traffic demand increases, APs are more densely deployed and the number of operating Wi-Fi channels also increases, which, however, results in additional scanning delay and makes the scanning a burdensome task. In this paper, we note that the co-location of Wi-Fi protocol with BLE protocol is a common practice in IoT networks, and develop a Wi-Fi passive scanning framework that uses BLE to assist scanning. Although the framework has great potential to improve scanning performance without explicit message exchanges, there are technical challenges related to time synchronization and channel switching delay. We address the challenges and develop a practical passive scanning scheme, named BLESS-Sync. We verify its performance through testbed experiments and extensive simulations, and show that BLESS-Sync significantly outperforms legacy Wi-Fi scanning in terms of scanning delay and energy efficiency.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488795"}, {"primary_key": "2186992", "vector": [], "sparse_vector": [], "title": "Learning the unknown: Improving modulation classification performance in unseen scenarios.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Automatic Modulation Classification (AMC) is significant for the practical support of a plethora of emerging spectrum applications, such as Dynamic Spectrum Access (DSA) in 5G and beyond, resource allocation, jammer identification, intruder detection, and in general, automated interference analysis. Although a well-known problem, most of the existing AMC work has been done under the assumption that the classifier has prior knowledge about the signal and channel parameters. This paper shows that unknown signal and channel parameters significantly degrade the performance of two of the most popular research streams in modulation classification: expert feature-based and data-driven. By understanding why and where those methods fail, in such unknown scenarios, we propose two possible directions to make AMC more robust to signal shape transformations introduced by unknown signal and channel parameters. We show that Spatial Transformer Networks (STN) and Transfer Learning (TL) embedded into a light ResNeXt-based classifier can improve average classification accuracy up to 10-30% for specific unseen scenarios with only 5% labeled data for a large dataset of 20 complex higher-order modulations.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488835"}, {"primary_key": "2186994", "vector": [], "sparse_vector": [], "title": "Distributed Threshold-based Offloading for Large-Scale Mobile Cloud Computing.", "authors": ["Xudong Qin", "<PERSON>", "<PERSON><PERSON>"], "summary": "Mobile cloud computing enables compute-limited mobile devices to perform real-time intensive computations such as speech recognition or object detection by leveraging powerful cloud servers. An important problem in large-scale mobile cloud computing is computational offloading where each mobile device decides when and how much computation should be uploaded to cloud servers by considering the local processing delay and the cost of using cloud servers. In this paper, we develop a distributed threshold-based offloading algorithm where it uploads an incoming computing task to cloud servers if the number of tasks queued at the device reaches the threshold, and processes it locally otherwise. The threshold is updated iteratively based on the computational load and the cost of using cloud servers. We formulate the problem as a symmetric game, and characterize the sufficient and necessary conditions for the existence and uniqueness of the Nash Equilibrium (NE) assuming exponential service times. Then, we show the convergence of our proposed distributed algorithm to the NE when the NE exists. Finally, we perform extensive simulations to validate our theoretical findings and demonstrate the efficiency of our proposed distributed algorithm under various practical scenarios such as general service times, imperfect server utilization estimation, and asynchronous threshold updates.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488821"}, {"primary_key": "2186995", "vector": [], "sparse_vector": [], "title": "MIERank: Co-ranking Individuals and Communities with Multiple Interactions in Evolving Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Ranking has significant applications in real life. It aims to evaluate the importance (or popularity) of two categories of objects, i.e., individuals and communities. Numerous efforts have been dedicated to these two types of rankings respectively. Instead, in this paper, we for the first time explore the co-ranking of both individuals and communities. Our insight lies in that co-ranking may enhance the mutual evaluation on both sides. To this end, we first establish an Evolving Coupled Graph that contains a series of smoothly weighted snapshots, each of which characterizes and couples the intricate interactions of both individuals and communities till a certain evolution time into a single graph. Then we propose an algorithm, called MIERank to implement the co-ranking of individuals and communities in the proposed evolving graph. The core idea of MIERank lies in a novel unbiased random walk, which, when sampling the interplay among nodes over different generation times, incorporates the preference knowledge of ranking by utilizing nodes' future actions. MIERank returns the co-ranking of both individuals and communities by iteratively alternating between their corresponding stationary probabilities of the unbiased random walk in a mutually-reinforcing manner. We prove the efficiency of MIERank in terms of its convergence, optimality and extensiblity. Our experiments on a big scholarly dataset of 606862 papers and 1215 fields further validate the superiority of MIERank with fast convergence and an up to 26% ranking accuracy gain compared with the separate counterparts.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488753"}, {"primary_key": "2186996", "vector": [], "sparse_vector": [], "title": "Threshold-based rerouting and replication for resolving job-server affinity relations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider a system with several job types and two parallel server pools. Within the pools the servers are homogeneous, but across pools possibly not in the sense that the service speed of a job may depend on its type as well as the server pool. Immediately upon arrival, jobs are assigned to a server pool, possibly based on (partial) knowledge of their type. In case such knowledge is not available upon arrival, it can however be obtained while the job is in service; as the service progresses, the likelihood that the service speed of this job type is low increases, creating an incentive to execute the job on different, possibly faster, server(s). Two policies are considered: reroute the job to the other server pool, or replicate it there.We determine the effective load per server under both the rerouting and replication policy for completely unknown as well as partly known job types. We also examine the impact of these policies on the stability bound, which is defined as the maximum arrival rate of jobs for which the effective load per server is smaller than one. We demonstrate that the uncertainty in job types may significantly reduce the stability bound, and that for (highly) unbalanced service speeds full replication achieves the largest stability bound. Finally, we discuss how the use of threshold-based policies can help improve the expected latency for completely or partly unknown job types.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488909"}, {"primary_key": "2187000", "vector": [], "sparse_vector": [], "title": "Proximity-Echo: Secure Two Factor Authentication Using Active Sound Sensing.", "authors": ["Yanzhi Ren", "<PERSON>", "Hongbo Liu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Huang", "Hongwei Li"], "summary": "The two-factor authentication (2FA) has drawn increasingly attention as the mobile devices become more prevalent. For example, the user's possession of the enrolled phone could be used by the 2FA system as the second proof to protect his/her online accounts. Existing 2FA solutions mainly require some form of user-device interaction, which may severely affect user experience and creates extra burdens to users. In this work, we propose Proximity-Echo, a secure 2FA system utilizing the proximity of a user's enrolled phone and the login device as the second proof without requiring the user's interactions or pre-constructed device fingerprints. The basic idea of Proximity-Echo is to derive location signatures based on acoustic beep signals emitted alternately by both devices and sensing the echoes with microphones, and compare the extracted signatures for proximity detection. Given the received beep signal, our system designs a period selection scheme to identify two sound segments accurately: the chirp period is the sound segment propagating directly from the speaker to the microphone whereas the echo period is the sound segment reflected back by surrounding objects. To achieve an accurate proximity detection, we develop a new energy loss compensation extraction scheme by utilizing the extracted chirp periods to estimate the intrinsic differences of energy loss between microphones of the enrolled phone and the login device. Our proximity detection component then conducts the similarity comparison between the identified two echo periods after the energy loss compensation to effectively determine whether the enrolled phone and the login device are in proximity for 2FA. Our experimental results show that our Proximity-Echo is accurate in providing 2FA and robust to both man-in-the-middle (MiM) and co-located attacks across different scenarios and device models.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488866"}, {"primary_key": "2187006", "vector": [], "sparse_vector": [], "title": "GRADES: Grad<PERSON> Descent for Similarity Caching.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A similarity cache can reply to a query for an object with similar objects stored locally. In some applications of similarity caches, queries and objects are naturally represented as points in a continuous space. Examples include 360° videos where user's head orientation-expressed in spherical coordinates- determines what part of the video needs to be retrieved, and recommendation systems where the objects are embedded in a finite-dimensional space with a distance metric to capture content dissimilarity. Existing similarity caching policies are simple modifications of classic policies like LRU, LFU, and qLRU and ignore the continuous nature of the space where objects are embedded. In this paper, we propose Grades, a new similarity caching policy that uses gradient descent to navigate the continuous space and find the optimal objects to store in the cache. We provide theoretical convergence guarantees and show Grades increases the similarity of the objects served by the cache in both applications mentioned above.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488757"}, {"primary_key": "2187007", "vector": [], "sparse_vector": [], "title": "Owl: Congestion Control with Partially Invisible Networks via Reinforcement Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Years of research on transport protocols have not solved the tussle between in-network and end-to-end congestion control. This debate is due to the variance of conditions and assumptions in different network scenarios, e.g., cellular versus data center networks. Recently, the community has proposed a few transport protocols driven by machine learning, nonetheless limited to end-to-end approaches.In this paper, we present Owl, a transport protocol based on reinforcement learning, whose goal is to select the proper congestion window learning from end-to-end features and network signals, when available. We show that our solution converges to a fair resource allocation after the learning overhead. Our kernel implementation, deployed over emulated and large scale virtual network testbeds, outperforms all benchmark solutions based on end-to-end or in-network congestion control.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488851"}, {"primary_key": "2187009", "vector": [], "sparse_vector": [], "title": "Minimizing the Sum of Age of Information and Transmission Cost under Stochastic Arrival Model.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We consider a node-monitor pair, where updates are generated stochastically (according to a known distribution) at the node that it wishes to send to the monitor. The node is assumed to incur a fixed cost for each transmission, and the objective of the node is to find the update instants so as to minimize a linear combination of AoI of information and average transmission cost. First, we consider the Poisson arrivals case, where updates have an exponential inter-arrival time for which we derive an explicit optimal online policy. Next, for arbitrary distributions of inter-arrival time of updates, we propose a simple randomized algorithm that transmits any newly arrived update with a fixed probability (that depends on the distribution) or never transmits that update. The competitive ratio of the proposed algorithm is shown to be a function of the variance and the mean of the inter-arrival time distribution. For some of the commonly considered distributions such as exponential, uniform, and Rayleigh, the competitive ratio bound is shown to be 2.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488746"}, {"primary_key": "2187012", "vector": [], "sparse_vector": [], "title": "Towards the Fairness of Traffic Policer.", "authors": ["Danfeng Shan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Fengyuan Ren"], "summary": "Traffic policing is widely used by ISPs to limit their customers' traffic rates. It has long been believed that a well-tuned traffic policer offers a satisfactory performance for TCP. However, we find this belief breaks with the emergence of new congestion control (CC) algorithms like BBR: flows using these new CC algorithms can easily occupy the majority of the bandwidth, starving traditional TCP flows. We confirm this problem with experiments and reveal its root cause as follows. Without buffer in traffic policers, congestion only causes packet losses, while new CC algorithms are loss-resilient, i.e. they adjust the sending rate based on other network feedback like delay. Thus, when being policed they will not reduce the sending rate until an unacceptable loss ratio for TCP is reached, resulting in low throughput for TCP. Simply adding buffer to the traffic policer improves fairness but incurs high latency. To this end, we propose FairPolicer, which can achieve fair bandwidth allocation without sacrificing latency. FairPolicer regards token as a basic unit of bandwidth and fairly allocates tokens to active flows in a round-robin manner. Testbed experiments show that FairPolicer can significantly improve the fairness and achieve much lower latency than other kinds of rate-limiters.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488761"}, {"primary_key": "2187013", "vector": [], "sparse_vector": [], "title": "Ultra-Wideband Swarm Ranging.", "authors": ["Feng Shan", "<PERSON><PERSON><PERSON>", "Zengbao Li", "Junzhou Luo", "<PERSON><PERSON>"], "summary": "Nowadays, aerial and ground robots, wearable and portable devices are becoming smaller, lighter, cheaper, and thus popular. It is now possible to utilize tens and thousands of them to form a swarm to complete complicated cooperative tasks, such as searching, rescuing, mapping, and battling. A swarm usually contains a large number of robots or devices, which are in short distance to each other and may move dynamically. So this paper studies the dynamic and dense swarms. The ultra-wideband (UWB) technology is proposed to serve as the fundamental technique for both networking and localization, because UWB is so time sensitive that an accurate distance can be calculated using timestamps of the transmit and receive data packets. A UWB swarm ranging protocol is designed in this paper, with key features: simple yet efficient, adaptive and robust, scalable and supportive. This swarm ranging protocol is introduced part by part to uncover its support for each of these features. It is implemented on Crazyflie 2.1 drones, STM32 microcontrollers powered aerial robots, with onboard UWB wireless transceiver chips DW1000. Extensive real world experiments are conducted to verify the proposed protocol with a total of 9 Crazyflie drones in a compact area.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488717"}, {"primary_key": "2187015", "vector": [], "sparse_vector": [], "title": "Radio Frequency Fingerprint Identification for LoRa Using Spectrogram and CNN.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Shen", "Jun<PERSON> Zhang", "<PERSON>", "<PERSON><PERSON>g", "<PERSON><PERSON><PERSON>"], "summary": "Radio frequency fingerprint identification (RFFI) is an emerging device authentication technique that relies on intrin-sic hardware characteristics of wireless devices. We designed an RFFI scheme for Long Range (LoRa) systems based on spectrogram and convolutional neural network (CNN). Specifically, we used spectrogram to represent the fine-grained time-frequency characteristics of LoRa signals. In addition, we revealed that the instantaneous carrier frequency offset (CFO) is drifting, which will result in misclassification and significantly compromise the system stability; we demonstrated CFO compensation is an effective mitigation. Finally, we designed a hybrid classifier that can adjust CNN outputs with the estimated CFO. The mean value of CFO remains relatively stable, hence it can be used to rule out CNN predictions whose estimated CFO falls out of the range. We performed experiments in real wireless environments using 20 LoRa devices under test (DUTs) and a Universal Software Radio Peripheral (USRP) N210 receiver. By comparing with the IQ-based and FFT-based RFFI schemes, our spectrogram-based scheme can reach the best classification accuracy, i.e., 97.61% for 20 LoRa DUTs.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488793"}, {"primary_key": "2187016", "vector": [], "sparse_vector": [], "title": "Exploiting Simultaneous Communications to Accelerate Data Parallel Distributed Deep Learning.", "authors": ["Shaohuai Shi", "<PERSON><PERSON>", "<PERSON>"], "summary": "Synchronous stochastic gradient descent (S-SGD) with data parallelism is widely used for training deep learning (DL) models in distributed systems. A pipelined schedule of the computing and communication tasks of a DL training job is an effective scheme to hide some communication costs. In such pipelined S-SGD, tensor fusion (i.e., merging some consecutive layers' gradients for a single communication) is a key ingredient to improve communication efficiency. However, existing tensor fusion techniques schedule the communication tasks sequentially, which overlooks their independence nature. In this paper, we expand the design space of scheduling by exploiting simultaneous All-Reduce communications. Through theoretical analysis and experiments, we show that simultaneous All-Reduce communications can effectively improve the communication efficiency of small tensors. We formulate an optimization problem of minimizing the training iteration time, in which both tensor fusion and simultaneous communications are allowed. We develop an efficient optimal scheduling solution and implement the distributed training algorithm ASC-WFBP with Horovod and PyTorch. We conduct real-world experiments on an 8-node GPU cluster of 32 GPUs with 10Gbps Ethernet. Experimental results on four modern DNNs show that ASC-WFBP can achieve about 1.09 × -2.48× speedup over the baseline without tensor fusion, and 1.15× -1.35× speedup over the state-of-the-art tensor fusion solution.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488803"}, {"primary_key": "2187018", "vector": [], "sparse_vector": [], "title": "Crowdsourcing System for Numerical Tasks based on Latent Topic Aware Worker Reliability.", "authors": ["<PERSON><PERSON>", "Shanyang Jiang", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Crowdsourcing is a widely adopted way for various labor-intensive tasks. One of the core problems in crowdsourcing systems is how to assign tasks to most suitable workers for better results, which heavily relies on the accurate profiling of each worker's reliability for different topics of tasks. Many previous work have studied worker reliability for either explicit topics represented by task descriptions or latent topics for categorical tasks. In this work, we aim to accurately estimate more fine-grained worker reliability for latent topics in numerical tasks, so as to further improve the result quality. We propose a bayesian probabilistic model named Gaussian Latent Topic Model(GLTM) to mine the latent topics of numerical tasks based on workers' behaviors and to estimate workers' topic-level reliability. By utilizing the GLTM, we propose a truth inference algorithm named TI-GLTM to accurately infer the tasks' truth and topics simultaneously and dynamically update workers' topic-level reliability. We also design an online task assignment mechanism called MRA-GLTM, which assigns appropriate tasks to workers with the Maximum Reduced Ambiguity principle. The experiment results show our algorithms can achieve significantly lower MAE and MSE than that of the state-of-the-art approaches.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488748"}, {"primary_key": "2187019", "vector": [], "sparse_vector": [], "title": "Combining Regularization with Look-Ahead for Competitive Online Convex Optimization.", "authors": ["Ming <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "There has been significant interest in leveraging limited look-ahead to achieve low competitive ratios for online convex optimization (OCO). However, existing online algorithms (such as Averaging Fixed Horizon Control (AFHC)) that can leverage look-ahead to reduce the competitive ratios still produce competitive ratios that grow unbounded as the coefficient ratio (i.e., the maximum ratio of the switching-cost coefficient and the service-cost coefficient) increases. On the other hand, the regularization method can attain a competitive ratio that remains bounded when the coefficient ratio is large, but it does not benefit from look-ahead. In this paper, we propose a new algorithm, called Regularization with Look-Ahead (RLA), that can get the best of both AFHC and the regularization method, i.e., its competitive ratio decreases with the look-ahead window size when the coefficient ratio is small, and remains bounded when the coefficient ratio is large. We also provide a matching lower bound for the competitive ratios of all online algorithms with look-ahead, which differs from the achievable competitive ratio of RLA by a factor that only depends on the problem size. The competitive analysis of RLA involves a non-trivial generalization of online primal-dual analysis to the case with look-ahead.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488766"}, {"primary_key": "2187022", "vector": [], "sparse_vector": [], "title": "Privacy Preserving and Resilient RPKI.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Resource Public Key Infrastructure (RPKI) is vital to the security of inter-domain routing. However, RPKI enables Regional Internet Registries (RIRs) to unilaterally takedown IP prefixes - indeed, such attacks have been launched by nation-state adversaries. The threat of IP prefix takedowns is one of the factors hindering RPKI adoption.In this work, we propose the first distributed RPKI system, based on threshold signatures, that requires the coordination of a number of RIRs to make changes to RPKI objects; hence, preventing unilateral prefix takedown. We perform extensive evaluations using our implementation demonstrating the practicality of our solution. Furthermore, we show that our system is scalable and remains efficient even when RPKI is widely deployed.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488759"}, {"primary_key": "2187023", "vector": [], "sparse_vector": [], "title": "Fix with P6: Verifying Programmable Switches at Runtime.", "authors": ["Apoorv Shukla", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We design, develop, and evaluate P6, an automated approach to (a) detect, (b) localize, and (c) patch software bugs in P4 programs. Bugs are reported via a violation of pre-specified expected behavior that is captured by P6. P6 is based on machine learning-guided fuzzing that tests P4 switch non-intrusively, i.e., without modifying the P4 program for detecting runtime bugs. This enables an automated and real-time localization and patching of bugs. We used a P6 prototype to detect and patch existing bugs in various publicly available P4 application programs deployed on two different switch platforms: behavioral model (bmv2) and Tofino. Our evaluation shows that P6 significantly outperforms bug detection baselines while generating fewer packets and patches bugs in large P4 programs such as switch.p4 without triggering any regressions.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488772"}, {"primary_key": "2187025", "vector": [], "sparse_vector": [], "title": "Let&apos;s Share VMs: Optimal Placement and Pricing across Base Stations in MEC Systems.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Li", "<PERSON>"], "summary": "In mobile edge computing (MEC) systems, users offload computationally intensive tasks to edge servers at base stations. However, with unequal demand across the network, there might be excess demand at some locations and underutilized resources at other locations. To address such load-unbalanced problem in MEC systems, in this paper we propose virtual machines (VMs) sharing across base stations. Specifically, we consider the joint VM placement and pricing problem across base stations to match demand and supply and maximize revenue at the network level. To make this problem tractable, we decompose it into master and slave problems. For the placement master problem, we propose a Markov approximation algorithm MAP on the design of a continuous time Markov chain. As for the pricing slave problem, we propose OPA - an optimal VM pricing auction, where all users are truthful. Furthermore, given users' potential untruthful behaviors, we propose an incentive compatible auction iCAT along with a partitioning mechanism PUFF, for which we prove incentive compatibility and revenue guarantees. Finally, we combine MAP and OPA or PUFF to solve the original problem, and analyze the optimality gap. Simulation results show that collaborative base stations increases revenue by up to 50%.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488854"}, {"primary_key": "2187027", "vector": [], "sparse_vector": [], "title": "Energy-Efficient Orchestration of Metro-Scale 5G Radio Access Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "RAN energy consumption is a major OPEX source for mobile telecom operators, and 5G is expected to increase these costs by several folds. Moreover, paradigm-shifting aspects of the 5G RAN architecture like RAN disaggregation, virtualization and cloudification introduce new traffic-dependent resource management decisions that make the problem of energy-efficient 5G RAN orchestration harder. To address such a challenge, we present a first comprehensive virtualized RAN (vRAN) system model aligned with 5G RAN specifications, which embeds realistic and dynamic models for computational load and energy consumption costs. We then formulate the vRAN energy consumption optimization as an integer quadratic programming problem, whose NP-hard nature leads us to develop GreenRAN, a novel, computationally efficient and distributed solution that leverages Lagrangian decomposition and simulated annealing. Evaluations with real-world mobile traffic data for a large metropolitan area are another novel aspect of this work, and show that our approach yields energy efficiency gains up to 25% and 42%, over state-of-the-art and baseline traditional RAN approaches, respectively.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488786"}, {"primary_key": "2187029", "vector": [], "sparse_vector": [], "title": "INT-label: Lightweight In-band Network-Wide Telemetry via Interval-based Distributed Labelling.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The In-band Network Telemetry (INT) enables hop-by-hop device-internal state exposure for reliably maintaining and troubleshooting data center networks. For achieving network-wide telemetry, orchestration on top of the INT primitive is further required. One straightforward solution is to flood the INT probe packets into the network topology for maximum measurement coverage, which, however, leads to huge bandwidth overhead. A refined solution is to leverage the SDN controller to collect the topology and carry out centralized probing path planning, which, however, cannot seamlessly adapt to occasional topology changes. To tackle the above problems, in this work, we propose INT-label, a lightweight In-band Network-Wide Telemetry architecture via interval-based distributed labelling. INT-label periodically labels device-internal states onto sampled packets, which is cost-effective with minor bandwidth overhead and able to seamlessly adapt to topology changes. Furthermore, to avoid telemetry resolution degradation due to loss of labelled packets, we also design a feedback mechanism to adaptively change the instant label frequency. Evaluation on software P4 switches suggests that INT-label can achieve 99.72% measurement coverage under a label frequency of 20 times per second. With adaptive labelling enabled, the coverage can still reach 92% even if 60% of the packets are lost in the data plane.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488799"}, {"primary_key": "2187030", "vector": [], "sparse_vector": [], "title": "Enhanced Flooding-Based Routing Protocol for Swarm UAV Networks: Random Network Coding Meets Clustering.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Bodong Shang", "<PERSON>", "<PERSON>"], "summary": "Existing routing protocols may not be applicable in UAV networks because of their dynamic network topology and lack of accurate position information. In this paper, an enhanced flooding-based routing protocol is designed based on random network coding (RNC) and clustering for swarm UAV networks, enabling the efficient routing process without any routing path discovery or network topology information. RNC can naturally accelerate the routing process, with which in some hops fewer generations need to be transmitted. To address the issue of numerous hops and further expedite routing process, a clustering method is leveraged, where UAV networks are partitioned into multiple clusters and generations are only flooded from representatives of each cluster rather than flooded from each UAV. By this way, the amount of hops can be significantly reduced. The technical details of the introduced routing protocol are designed. Moreover, to capture the dynamic network topology, the Poisson cluster process is employed to model UAV networks. Afterwards, stochastic geometry tools are utilized to derive the distance distribution between two random selected UAVs and analytically evaluate performance. Extensive simulation studies are conducted to prove the validation of performance analysis, demonstrate the effectiveness of our designed routing protocol, and reveal its design insight.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488721"}, {"primary_key": "2187031", "vector": [], "sparse_vector": [], "title": "Minimizing Entropy for Crowdsourcing with Combinatorial Multi-Armed Bandit.", "authors": ["<PERSON><PERSON>", "<PERSON>ming Jin"], "summary": "Nowadays, crowdsourcing has become an increasingly popular paradigm for large-scale data collection, annotation, and classification. Today's rapid growth of crowdsourcing platforms calls for effective worker selection mechanisms, which oftentimes have to operate with a priori unknown worker reliability. We discover that the empirical entropy of workers' results, which measures the uncertainty in the final aggregated results, naturally becomes a suitable metric to evaluate the outcome of crowdsourcing tasks. Therefore, this paper designs a worker selection mechanism that minimizes the empirical entropy of the results submitted by participating workers. Specifically, we formulate worker selection under sequentially arriving tasks as a combinatorial multi-armed bandit problem, which treats each worker as an arm, and aims at learning the best combination of arms that minimize the cumulative empirical entropy. By information theoretic methods, we carefully derive an estimation of the upper confidence bound for empirical entropy minimization, and leverage it in our minimum entropy upper confidence bound (ME-UCB) algorithm to balance exploration and exploitation. Theoretically, we prove that ME-UCB has a regret upper bound of O(1), which surpasses existing submodular UCB algorithms. Our extensive experiments with both a synthetic and real-world dataset empirically demonstrate that our ME-UCB algorithm outperforms other state-of-the-art approaches.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488800"}, {"primary_key": "2187033", "vector": [], "sparse_vector": [], "title": "CTF: Anomaly Detection in High-Dimensional Time Series with Coarse-to-Fine Model Transfer.", "authors": ["Ming Sun", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Liu", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Xiaozhou Liu", "<PERSON><PERSON><PERSON>"], "summary": "Anomaly detection is indispensable in modern IT infrastructure management. However, the dimension explosion problem of the monitoring data (large-scale machines, many key performance indicators, and frequent monitoring queries) causes a scalability issue to the existing algorithms. We propose a coarse-to-fine model transfer based framework CTF to achieve a scalable and accurate data-center-scale anomaly detection. CTF pre-trains a coarse-grained model, uses the model to extract and compress per-machine features to a distribution, clusters machines according to the distribution, and conducts model transfer to fine-tune per-cluster models for high accuracy. The framework takes advantage of clustering on the per-machine latent representation distribution, reusing the pre-trained model, and partial-layer model fine-tuning to boost the whole training efficiency. We also justify design choices such as the clustering algorithm and distance algorithm to achieve the best accuracy. We prototype CTF and experiment on production data to show its scalability and accuracy. We also release a labeling tool for multivariate time series and a labeled dataset to the research community.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488755"}, {"primary_key": "2187037", "vector": [], "sparse_vector": [], "title": "Efficient Association of Wi-Fi Probe Requests under MAC Address Randomization.", "authors": ["<PERSON><PERSON><PERSON>", "S.<PERSON><PERSON><PERSON>"], "summary": "Wi-Fi-enabled devices such as smartphones periodically search for available networks by broadcasting probe requests which encapsulate MAC addresses as the device identifiers. To protect privacy (user identity and location), modern devices embed random MAC addresses in their probe frames, the so-called MAC address randomization. Such randomization greatly hampers statistical analysis such as people counting and trajectory inference. To mitigate its impact while respecting privacy, we propose Espresso, a simple, novel and efficient approach which establishes probe request association under MAC address randomization. Espresso models the frame association as a flow network, with frames as nodes and frame correlation as edge cost. To estimate the correlation between any two frames, it considers the multimodality of request frames, including information elements, sequence numbers and received signal strength. It then associates frames with minimum-cost flow optimization. To the best of our knowledge, this is the first piece of work that formulates the probe request association problem as network flow optimization using frame correlation. We have implemented Espresso and conducted extensive experiments in a leading shopping mall. Our results show that Espresso outperforms the state-of-the-art schemes in terms of discrimination accuracy (> 80%) and V-measure scores (> 0.85).", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488769"}, {"primary_key": "2187039", "vector": [], "sparse_vector": [], "title": "An Incentive Mechanism for Cross-Silo Federated Learning: A Public Goods Perspective.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In cross-silo federated learning (FL), organizations cooperatively train a global model with their local data. The organizations, however, may be heterogeneous in terms of their valuation on the precision of the trained global model and their training cost. Meanwhile, the computational and communication resources of the organizations are non-excludable public goods. That is, even if an organization does not perform any local training, other organizations cannot prevent that organization from using the outcome of their resources (i.e., the trained global model). To address the organization heterogeneity and the public goods feature, in this paper, we formulate a social welfare maximization problem and propose an incentive mechanism for cross-silo FL. With the proposed mechanism, organizations can achieve not only social welfare maximization but also individual rationality and budget balance. Moreover, we propose a distributed algorithm that enables organizations to maximize the social welfare without knowing the valuation and cost of each other. Our simulations with MNIST dataset show that the proposed algorithm converges faster than a benchmark method. Furthermore, when organizations have higher valuation on precision, the proposed mechanism and algorithm are more beneficial in the sense that the organizations can achieve higher social welfare through participating in cross-silo FL.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488705"}, {"primary_key": "2187040", "vector": [], "sparse_vector": [], "title": "Reusing Backup Batteries as BESS for Power Demand Reshaping in 5G and Beyond.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The mobile network operators are upgrading their network facilities and shifting to the 5G era at an unprecedented pace. The huge operating expense (OPEX), mainly the energy consumption cost, has become the major concern of the operators. In this work, we investigate the energy cost-saving potential by transforming the backup batteries of base stations (BSs) to a distributed battery energy storage system (BESS). Specifically, to minimize the total energy cost, we model the distributed BESS discharge/charge scheduling as an optimization problem by incorporating comprehensive practical considerations. Then, considering the dynamic BS power demands in practice, we propose a deep reinforcement learning (DRL) based approach to make BESS scheduling decisions in real-time. The experiments using real-world BS deployment and traffic load data demonstrate that with our DRL-based BESS scheduling, the peak power demand charge of BSs can be reduced by up to 26.59%, and the yearly OPEX saving for 2,282 5G BSs could reach up to US$185,000.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488760"}, {"primary_key": "2187043", "vector": [], "sparse_vector": [], "title": "On Network Topology Augmentation for Global Connectivity under Regional Failures.", "authors": ["<PERSON><PERSON><PERSON>", "Zsombor L. <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Several recent studies shed light on the vulnerability of networks against regional failures, which are failures of multiple nodes and links in a physical region due to a natural disaster. The paper defines a novel design framework, called Geometric Network Augmentation (GNA), which determines a set of node pairs and the new cable routes to be deployed between each of them to make the network always remain connected when a regional failure of a given size occurs. With the proposed GNA design framework, we provide mathematical analysis and efficient heuristic algorithms that are built on the latest computational geometry tools and combinatorial optimization techniques. Through extensive simulation, we demonstrate that augmentation with just a small number of new cable routes will achieve the desired resilience against all the considered regional failures.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488879"}, {"primary_key": "2187044", "vector": [], "sparse_vector": [], "title": "Tornadoes In The Cloud: Worst-Case Attacks on Distributed Resources Systems.", "authors": ["Jhonatan <PERSON>", "<PERSON><PERSON>"], "summary": "Geographically distributed cloud networks are used by a variety of applications and services worldwide. As the demand for these services increases, their data centers form an attractive target for malicious attackers, aiming at harming the services. In this study we address sophisticated attackers who aim at causing maximal-damage to the service. A worst-case (damage-maximizing) attack is an attack which minimizes the revenue of the system operator, due to disrupting the users from being served. A sophisticated attacker needs to decide how many attacking agents should be launched at each of the systems regions, in order to inflict maximal damage. We characterize and analyze damage-maximization strategies for a number of attacks including deterministic attack, concur-rent stochastic agents attack, approximation of a virus-spread attack and over-size binomial attack. We also address user-migration defense, allowing to dynamically migrate demands among regions, and we provide efficient algorithms for deriving worst-case attacks given a system with arbitrary placement and demands. The results form a basis for devising resource allocation strategies aiming at minimizing attack damages.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488673"}, {"primary_key": "2187046", "vector": [], "sparse_vector": [], "title": "Modeling Communication Reliability in LoRa Networks with Device-level Accuracy.", "authors": ["Verónica <PERSON>-Betancur", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Long Range (LoRa) is a low-power wireless communication technology for long-range connectivity, extensively used in the Internet of Things. Several works in the literature have analytically characterized the performance of LoRa networks, with particular focus on scalability and reliability. However, most of the related models are limited, as they cannot account for factors that occur in practice, or make strong assumptions on how devices are deployed in the network. This article proposes an analytical model that describes the delivery ratio in a LoRa network with device-level granularity. Specifically, it considers the impact of several key factors that affect real deployments, including multiple gateways and channel variation. Therefore, the proposed model can effectively evaluate the delivery ratio in realistic network topologies, without any restrictions on device deployment or configuration. It also accurately characterizes the delivery ratio of each device in a network, as demonstrated by extensive simulations in a wide variety of conditions, including diverse networks in terms of node deployment and link-level parameter settings. The proposed model provides a level of detail that is not available in the state of the art, and it matches the simulation results within an error of a few percentage points.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488783"}, {"primary_key": "2187048", "vector": [], "sparse_vector": [], "title": "Randomized Scheduling of Real-Time Traffic in Wireless Networks Over Fading Channels.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Despite the rich literature on scheduling algorithms for wireless networks, algorithms that can provide deadline guarantees on packet delivery for general traffic and interference models are very limited. In this paper, we study the problem of scheduling real-time traffic under a conflict-graph interference model with unreliable links due to channel fading. Packets that are not successfully delivered within their deadlines are of no value. We consider traffic (packet arrival and deadline) and fading (link reliability) processes that evolve as an unknown finite-state Markov chain. The performance metric is efficiency ratio which is the fraction of packets of each link which are delivered within their deadlines compared to that under the optimal (unknown) policy. We first show a conversion result that shows classical non-real-time scheduling algorithms can be ported to the real-time setting and yield a constant efficiency ratio, in particular, Max-Weight Scheduling (MWS) yields an efficiency ratio of 1/2. We then propose randomized algorithms that achieve efficiency ratios strictly higher than 1/2, by carefully randomizing over the maximal schedules. We further propose low-complexity and myopic distributed randomized algorithms, and characterize their efficiency ratio. Simulation results are presented that verify that randomized algorithms outperform classical algorithms such as MWS and GMS.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488917"}, {"primary_key": "2187051", "vector": [], "sparse_vector": [], "title": "DeepSense: Fast Wideband Spectrum Sensing Through Real-Time In-the-Loop Deep Learning.", "authors": ["<PERSON>", "Salvatore D&apos;Oro", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Spectrum sharing will be a key technology to tackle spectrum scarcity in the sub-6 GHz bands. To fairly access the shared bandwidth, wireless users will necessarily need to quickly sense large portions of spectrum and opportunistically access unutilized bands. The key unaddressed challenges of spectrum sensing are that (i) it has to be performed with extremely low latency over large bandwidths to detect tiny spectrum holes and to guarantee strict real-time digital signal processing (DSP) constraints; (ii) its underlying algorithms need to be extremely accurate, and flexible enough to work with different wireless bands and protocols to find application in real-world settings. To the best of our knowledge, the literature lacks spectrum sensing techniques able to accomplish both requirements. In this paper, we propose DeepSense, a software/hardware framework for real-time wideband spectrum sensing that relies on real-time deep learning tightly integrated into the transceiver's baseband processing logic to detect and exploit unutilized spectrum bands. DeepSense uses a convolutional neural network (CNN) implemented in the wireless platform's hardware fabric to analyze a small portion of the unprocessed baseband waveform to automatically extract the maximum amount of information with the least amount of I/Q samples. We extensively validate the accuracy, latency and generality performance of DeepSense with (i) a 400 GB dataset containing hundreds of thousands of WiFi transmissions collected \"in the wild\" with different Signal-to-Noise-Ratio (SNR) conditions and over different days; (ii) a dataset of transmissions collected using our own software-defined radio testbed; and (iii) a synthetic dataset of LTE transmissions under controlled SNR conditions. We also measure the real-time latency of the CNNs trained on the three datasets with an FPGA implementation, and compare our approach with a fixed energy threshold mechanism. Results show that our learning-based approach can deliver a precision and recall of 98% and 97% respectively and a latency as low as 0.61ms. For reproducibility and benchmarking purposes, we pledge to share the code and the datasets used in this paper to the community.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488764"}, {"primary_key": "2187053", "vector": [], "sparse_vector": [], "title": "Adaptive Batch Update in TCAM: How Collective Optimization Beats Individual Ones.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Rule update in TCAM has long been identified as a key technical challenge due to the rule order constraint. Existing algorithms take each rule update as an independent task. However, emerging applications produce batch rule update requests. Processing the updates individually causes high aggregated cost which can strain the processor and/or incur excessive TCAM lookup interrupts. This paper presents the first true batch update algorithm, ABUT. Unlike the other alleged batch update algorithms, ABUT collectively evaluates and optimizes the TCAM placement for whole batches throughout. By applying the topology grouping and maintaining the group order invariance in TCAM, ABUT achieves substantial computing time reduction yet still yields the best-in-class placement cost. Our evaluations show that ABUT is ideal for low-latency and high-throughput batch TCAM updates in modern high-performance switches.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488758"}, {"primary_key": "2187054", "vector": [], "sparse_vector": [], "title": "RespTracker: Multi-user Room-scale Respiration Tracking with Commercial Acoustic Devices.", "authors": ["<PERSON><PERSON>", "Shu<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Continuous domestic respiration monitoring provides vital information for diagnosing assorted diseases. In this paper, we introduce RESPTRACKER, the first continuous, multiple-person respiration tracking system in domestic settings using acoustic-based COTS devices. RESPTRACKER uses a two-stage algorithm to separate and recombine respiration signals from multiple paths in a short period so that it can track the respiration rate of multiple moving subjects. Our experimental results show that our two-stage algorithm can distinguish the respiration of at least four subjects at a distance of three meters.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488881"}, {"primary_key": "2187055", "vector": [], "sparse_vector": [], "title": "Taming Time-Varying Information Asymmetry in Fresh Status Acquisition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many online platforms are providing valuable real-time contents (e.g., traffic) by continuously acquiring the status of different Points of Interest (PoIs). In status acquisition, it is challenging to determine how frequently a PoI should upload its status to a platform, since they are self-interested with private and possibly time-varying preferences. This paper considers a general multi-period status acquisition system, aiming to maximize the aggregate social welfare and ensure the platform freshness. The freshness is measured by a metric termed age of information. For this goal, we devise a long-term decomposition (LtD) mechanism to resolve the time-varying information asymmetry. The key idea is to construct a virtual social welfare that only depends on the current private information, and then decompose the per-period operation into multiple distributed bidding problems for the PoIs and platforms. The LtD mechanism enables the platforms to achieve a tunable trade-off between payoff maximization and freshness conditions. Moreover, the LtD mechanism retains the same social performance compared to the benchmark with symmetric information and asymptotically ensures the platform freshness conditions. Numerical results based on real-world data show that when the platforms pay more attention to payoff maximization, each PoI still obtains a non-negative payoff in the long-term.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488884"}, {"primary_key": "2187056", "vector": [], "sparse_vector": [], "title": "MANDA: On Adversarial Example Detection for Network Intrusion Detection System.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the rapid advancement in machine learning (ML), ML-based Intrusion Detection Systems (IDSs) are widely deployed to protect networks from various attacks. Yet one of the biggest challenges is that ML-based IDSs suffer from adversarial example (AE) attacks. By applying small perturbations (e.g. slightly increasing packet inter-arrival time) to the intrusion traffic, an AE attack can flip the prediction of a well-trained IDS. We address this challenge by proposing MANDA, a MANifold and Decision boundary-based AE detection system. Through analyzing AE attacks, we notice that 1) an AE tends to be close to its original manifold (i.e., the cluster of samples in its original class) regardless which class it is misclassified into; and 2) AEs tend to be close to the decision boundary so as to minimize the perturbation scale. Based on the two observations, we design MANDA for accurate AE detection by exploiting inconsistency between manifold evaluation and IDS model inference and evaluating model uncertainty on small perturbations. We evaluate MANDA on NSL-KDD under three state-of-the-art AE attacks. Our experimental results show that MANDA achieves as high as 98.41% true-positive rate with 5% false-positive rate and can be applied to other problem spaces such as image recognition.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488874"}, {"primary_key": "2187057", "vector": [], "sparse_vector": [], "title": "RapidRider: Efficient WiFi Backscatter with Uncontrolled Ambient Signals.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents RapidRider, the first WiFi backscatter system that takes uncontrolled OFDM WiFi signals, e.g., 802.11a/g/n, as excitations and efficiently embeds tag data at the single-symbol rate. Such design brings us closer to the dream of pervasive backscatter communication since uncontrolled WiFi signals are everywhere. Specifically, we show that RapidRider can demodulate tag data for each OFDM symbol while previous systems rely on multi-symbol demodulation. Further, we design deinterleaving-twins decoding that enables RapidRider to use any uncontrolled WiFi signals as carriers. We prototype RapidRider using FPGAs, commodity radios, and USRPs. Comprehensive evaluations show that RapidRider's maximum throughput is 3.92x and 1.97x better than FreeRider and MOXcatter. To accommodate cases where there is only one receiver available, we design RapidRider+ that can take productive data and tag data on the same packet. Results demonstrate that it can achieve an aggregated goodput of productive and tag data around 1 Mbps on average.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488716"}, {"primary_key": "2187058", "vector": [], "sparse_vector": [], "title": "Physical Layer Key Generation between Backscatter Devices over Ambient RF Signals.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Ambient backscatter communication (AmBC), which enables energy harvesting and ultra-low-power communication by utilizing ambient radio frequency (RF) signals, has emerged as a cutting-edge technology to realize numerous Internet of Things (IoT) applications. However, the current literature lacks efficient secret key sharing solutions for resource-limited devices in AmBC systems to protect the backscatter communications, especially for private data transmission. Thus, we propose a novel physical layer key generation scheme between backscatter devices (BDs) by exploiting received superposed ambient signals. Based on the repeated patterns (i.e., cyclic prefix in OFDM symbols) in ambient RF signals, we present a joint transceiver design of BD backscatter waveform and BD receiver to extract the downlink signal and the backscatter signal from the superposed signals. By multiplying the downlink signal and the backscatter signal, we can actually obtain the triangle channel information as a shared random secret source for key generation. Besides, we study the trade-off between the rate of secret key generation and harvested energy by modeling it as a joint optimization problem. Finally, extensive numerical simulations are provided to evaluate the key generation performance, energy harvesting performance, and their trade-offs under various system settings.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488885"}, {"primary_key": "2187059", "vector": [], "sparse_vector": [], "title": "A Weak Consensus Algorithm and Its Application to High-Performance Blockchain.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "A large number of consensus algorithms have been proposed. However, the requirement of strict consistency limits their wide adoption, especially in high-performance required systems. In this paper, we propose a weak consensus algorithm that only maintains the consistency of relative positions between the messages. We apply this consensus algorithm to construct a high-performance blockchain system, called \\textit{<PERSON>phinx}. We implement the system with 32k+ lines of code including all components like consensus/P2P/ledger/etc. The evaluations show that Sphinx can reach a peak throughput of 43k TPS (with 8 full nodes), which is significantly faster than current blockchain systems such as Ethereum given the same experimental environment. To the best of our knowledge, we present the first weak consensus algorithm with a fully implemented blockchain system.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488725"}, {"primary_key": "2187060", "vector": [], "sparse_vector": [], "title": "Delay-Tolerant Constrained OCO with Application to Network Resource Allocation.", "authors": ["Jun<PERSON> Wang", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We consider online convex optimization (OCO) with multi-slot feedback delay, where an agent makes a sequence of online decisions to minimize the accumulation of time-varying convex loss functions, subject to short-term and long-term constraints that are possibly time-varying. The current convex loss function and the long-term constraint function are revealed to the agent only after the decision is made, and they may be delayed for multiple time slots. Existing work on OCO under this general setting has focused on the static regret, which measures the gap of losses between the online decision sequence and an offline benchmark that is fixed over time. In this work, we consider both the static regret and the more practically meaningful dynamic regret, where the benchmark is a time-varying sequence of per-slot optimizers. We propose an efficient algorithm, termed Delay-Tolerant Constrained-OCO (DTC-OCO), which uses a novel constraint penalty with double regularization to tackle the asynchrony between information feedback and decision updates. We derive upper bounds on its dynamic regret, static regret, and constraint violation, proving them to be sublinear under mild conditions. We further apply DTC-OCO to a general network resource allocation problem, which arises in many systems such as data networks and cloud computing. Simulation results demonstrate substantial performance gain of DTC-OCO over the known best alternative.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488698"}, {"primary_key": "2187063", "vector": [], "sparse_vector": [], "title": "Towards Minimum Fleet for Ridesharing-Aware Mobility-on-Demand Systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>ming Jin", "<PERSON>"], "summary": "The rapid development of information and communication technologies has given rise to mobility-on-demand (MoD) systems (e.g., <PERSON><PERSON>, Didi) that have fundamentally revolutionized urban transportation. One common feature of today's MoD systems is the integration of ridesharing due to its cost-efficient and environment-friendly natures. However, a fundamental unsolved problem for such systems is how to serve people's heterogeneous transportation demands with as few vehicles as possible. Naturally, solving such minimum fleet problem is essential to reduce the vehicles on the road to improve transportation efficiency. Therefore, we investigate the fleet minimization problem in ridesharing-aware MoD systems. We use graph-theoretic methods to construct a novel order graph capturing the complicated inter-order shareability, each order's spatial-temporal features, and various other real-world factors. We then formulate the problem as a tree cover problem over the order graph, which differs from the traditional coverage problems. Theoretically, we prove the problem is NP-hard, and propose a polynomial-time algorithm with a guaranteed approximation ratio. Besides, we address the online fleet minimization problem, where orders arrive in an online manner. Finally, extensive experiments on a city-scale dataset from Shenzhen, containing 21 million orders from June 1st to 30th, 2017, validate the effectiveness of our algorithms.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488862"}, {"primary_key": "2187064", "vector": [], "sparse_vector": [], "title": "Web-LEGO: Trading Content Strictness for Faster Webpages.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The current Internet content delivery model assumes strict mapping between a resource and its descriptor, e.g., a JPEG file and its URL. Content Distribution Networks (CDNs) extend it by replicating the same resources across multiple locations, and introducing multiple descriptors. The goal of this work is to build Web-LEGO, an opt-in service, to speedup webpages at client side. Our rationale is to replace the slow original content with fast similar or equal content. Further, we perform a reality check of this idea both in term of the prevalence of CDN-less websites, availability of similar content, and user perception of similar webpages via millions of scale automated tests and thousands of real users. Then, we devise Web-LEGO, and address natural concerns on content inconsistency and copyright infringements. The final evaluation shows that Web-LEGO brings significant improvements both in term of reduced Page Load Time (PLT) and user-perceived PLT. Specifically, CDN-less websites provide more room for speedup than CDN-hosted ones, i.e., 7x more in the median case. Besides, Web-LEGO achieves high visual accuracy (94.2%) and high scores from a paid survey: 92% of the feedback collected from 1,000 people confirm Web-LEGO's accuracy as well as positive interest in the service.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488904"}, {"primary_key": "2187066", "vector": [], "sparse_vector": [], "title": "Enabling Edge-Cloud Video Analytics for Robotics Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Xi<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Emerging deep learning-based video analytics tasks demand computation-intensive neural networks and powerful computing resources on the cloud to achieve high accuracy. Due to the latency requirement and limited network bandwidth, edge-cloud systems adaptively compress the data to strike a balance between overall analytics accuracy and bandwidth consumption. However, the degraded data leads to another issue of poor tail accuracy, which means the extremely low accuracy of a few semantic classes and video frames. Autonomous robotics applications especially value the tail accuracy performance but suffer using the prior edge-cloud systems.We present Runespoor, an edge-cloud video analytics system to manage the tail accuracy and enable emerging robotics applications. We train and deploy a super-resolution model tailored for the tail accuracy of analytics tasks on the server to significantly improves the performance on hard-to-detect classes and sophisticated frames. During online operation, we use an adaptive data rate controller to further improve the tail performance by instantly adjusting the data rate policy according to the video content. Our evaluation shows that Runespoor improves class-wise tail accuracy by up to 300%, frame-wise 90%/99% tail accuracy by up to 22%/54%, and greatly improves the overall accuracy and bandwidth trade-off.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488801"}, {"primary_key": "2187068", "vector": [], "sparse_vector": [], "title": "Resource-Efficient Federated Learning with Hierarchical Aggregation in Edge Computing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Federated learning (FL) has emerged in edge computing to address limited bandwidth and privacy concerns of traditional cloud-based centralized training. However, the existing FL mechanisms may lead to long training time and consume a tremendous amount of communication resources. In this paper, we propose an efficient FL mechanism, which divides the edge nodes into K clusters by balanced clustering. The edge nodes in one cluster forward their local updates to cluster header for aggregation by synchronous method, called cluster aggregation, while all cluster headers perform the asynchronous method for global aggregation. This processing procedure is called hierarchical aggregation. Our analysis shows that the convergence bound depends on the number of clusters and the training epochs. We formally define the resource-efficient federated learning with hierarchical aggregation (RFL-HA) problem. We propose an efficient algorithm to determine the optimal cluster structure (i.e., the optimal value of K) with resource constraints and extend it to deal with the dynamic network conditions. Extensive simulation results obtained from our study for different models and datasets show that the proposed algorithms can reduce completion time by 34.8%-70% and the communication resource by 33.8%-56.5% while achieving a similar accuracy, compared with the well-known FL mechanisms.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488756"}, {"primary_key": "2187069", "vector": [], "sparse_vector": [], "title": "ToP: Time-dependent Zone-enhanced Points-of-interest Embedding-based Explainable Recommender system.", "authors": ["<PERSON>", "<PERSON><PERSON> Xu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Points-of-interest (POIs) recommendation plays a vital role by introducing unexplored POIs to consumers and has drawn extensive attention from both academia and industry. Existing POI recommender systems usually learn latent vectors to represent both consumers and POIs from historical check-ins and make recommendations under the spatiotemporal constraints. However, we argue that the existing works still suffer from the challenges of explaining consumers complicated check-in actions. In this paper, we first explore the interpretability of recommendations from the POI aspect, i.e., for a specific POI, its function usually changes over time, so representing a POI with a single fixed latent vector is not sufficient to describe POIs dynamic function. Besides, check-in actions to a POI is also affected by the zone it belongs to. In other words, the zone's embedding learned from POI distributions, road segments, and historical check-ins could be jointly utilized to enhance the accuracy of POI recommendations. Along this line, we propose a Time-dependent Zone-enhanced POI embedding model (ToP), a recommender system that integrates knowledge graph and topic model to introduce the spatiotemporal effects into POI embeddings for strengthening interpretability of recommendation. Specifically, ToP learns multiple latent vectors for a POI in different time to capture its dynamic functions. Jointly combining these vectors with zones representations, ToP enhances the spatiotemporal interpretability of POI recommendations. With this hybrid architecture, some existing POI recommender systems can be treated as special cases of ToP. Extensive experiments on real-world Changchun city datasets demonstrate that ToP not only achieves state-of-the-art performance in terms of common metrics, but also provides more insights for consumers POI check-in actions.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488726"}, {"primary_key": "2187070", "vector": [], "sparse_vector": [], "title": "EdgeDuet: Tiling Small Object Detection for Edge Assisted Autonomous Mobile Vision.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Accurate, real-time object detection on resource-constrained devices enables autonomous mobile vision applications such as traffic surveillance, situational awareness, and safety inspection, where it is crucial to detect both small and large objects in crowded scenes. Prior studies either perform object detection locally on-board or offload the task to the edge/cloud. Local object detection yields low accuracy on small objects since it operates on low-resolution videos to fit in mobile memory. Offloaded object detection incurs high latency due to uploading high-resolution videos to the edge/cloud. Rather than either pure local processing or offloading, we propose to detect large objects locally while offloading small object detection to the edge. The key challenge is to reduce the latency of small object detection. Accordingly, we develop EdgeDuet, the first edge-device collaborative framework for enhancing small object detection with tile-level parallelism. It optimizes the offloaded detection pipeline in tiles rather than the entire frame for high accuracy and low latency. Evaluations on drone vision datasets under LTE, WiFi 2.4GHz, WiFi 5GHz show that EdgeDuet outperforms local object detection in small object detection accuracy by 233.0%. It also improves the detection accuracy by 44.7% and latency by 34.2% over the state-of-the-art offloading schemes.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488843"}, {"primary_key": "2187071", "vector": [], "sparse_vector": [], "title": "Cost-Driven Data Caching in the Cloud: An Algorithmic Approach.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Data caching in the cloud is an efficient way to improve the QoS of diverse data applications. However, this benefit is not freely available, given monetary cost to manage the caches in the cloud. In this paper, we study the data caching problem in the cloud that is driven by the monetary cost reduction, instead of the hit rate under limited capacity as in traditional cases. In particular, given a stream of requests R to a shared data item, we present a shortest-path based optimal algorithm that can minimize the total transfer and caching costs within O(mn) time for off-line case, here m represents the number of nodes in the network, while n is the length of the request stream. The cost model in this computation is semi-homo, which indicates that all pairs of nodes have the same transfer cost, but each cache server node has its own caching cost rate. Our off-line algorithm improves the previous results not only in reducing the time complexity from O(m 2 n) to O(mn), but also in relaxing the cost model to be semi-homogeneous, rendering the algorithm more practical in reality. Furthermore, we also study this problem in its online form, and by extending the anticipatory caching idea, we propose a 2-competitive online algorithm based on the same cost model and show its tightness by giving a lower bound of the competitive ratio as 2 - o(1) for any deterministic online algorithm. We provably achieve these results with our deep insights into the problem and careful analysis of the solution algorithms, together with a trace-based study to evaluate their performance in reality.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488820"}, {"primary_key": "2187072", "vector": [], "sparse_vector": [], "title": "Making Multi-String Pattern Matching Scalable and Cost-Efficient with Programmable Switching ASICs.", "authors": ["<PERSON><PERSON> Wang", "<PERSON><PERSON><PERSON>", "Guanyu Li", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Multi-string pattern matching is a crucial building block for many network security applications, and thus of great importance. Since every byte of a packet has to be inspected by a large set of patterns, it often becomes a bottleneck of these applications and dominates the performance of an entire system. Many existing works have been devoted to alleviate this performance bottleneck either by algorithm optimization or hardware acceleration. However, neither one provides the desired scalability and costs that keep pace with the dramatic increase of the network bandwidth and network traffic today. In this paper, we present BOLT, a scalable and cost-efficient multi-string pattern matching system leveraging the capability of emerging programmable switches. BOLT combines the following two techniques, a smart state encoding scheme to fit a large number of strings into the limited memory on the programmable switch, and a variable k-stride transition mechanism to increase the throughput significantly with the same level of memory costs. We implement a prototype of BOLT and make its source code publicly available. Extensive evaluations demonstrate that BOLT could provide orders of magnitude improvement in throughput which is scalable with pattern sets and workloads, and could also significantly decrease the number of entries and memory requirement.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488796"}, {"primary_key": "2187073", "vector": [], "sparse_vector": [], "title": "PolarTracker: Attitude-aware Channel Access for Floating Low Power Wide Area Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Huadong Ma"], "summary": "Low Power Wide Area Networks (LPWAN) such as Long Range (LoRa) show great potential in emerging aquatic IoT applications. However, our deployment experience shows that the floating LPWAN suffer significant performance degradation, compared to the static terrestrial deployments. Our measurement results reveal the reason behind this is due to the polarization and directivity of the antenna. The dynamic attitude of a floating node incurs varying signal strength losses, which is ignored by the attitude-oblivious link model adopted in most of the existing methods. When accessing the channel at a misaligned attitude, packet errors can happen. In this paper, we propose an attitude-aware link model that explicitly quantifies the impact of node attitude on link quality. Based on the new model, we propose PolarTracker, a novel channel access method for floating LPWAN. PolarTracker tracks the node attitude alignment state and schedules the transmissions into the aligned periods with better link quality. We implement a prototype of PolarTracker on commercial LoRa platforms and extensively evaluate its performance in various real-world environments. The experimental results show that PolarTracker can efficiently improve the packet reception ratio by 48.8%, compared with ALOHA in LoRaWAN.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488714"}, {"primary_key": "2187074", "vector": [], "sparse_vector": [], "title": "Robust Service Mapping in Multi-Tenant Clouds.", "authors": ["Jingzhou Wang", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In a multi-tenant cloud, cloud vendors provide services (e.g., elastic load-balancing, virtual private networks) on service nodes for tenants. Thus, the mapping of tenants' traffic and service nodes is an important issue in multi-tenant clouds. In practice, unreliability of service nodes and uncertainty/dynamics of tenants' traffic are two critical challenges that affect the tenants' QoS. However, previous works often ignore the impact of these two challenges, leading to poor system robustness when encountering system accidents. To bridge the gap, this paper studies the problem of robust service mapping in multi-tenant clouds (RSMP). Due to traffic dynamics, we take a two-step approach: service node assignment and tenant traffic scheduling. For service node assignment, we prove its NP-Hardness and analyze its problem difficulty. Then, we propose an efficient algorithm with bounded approximation factors based on randomized rounding and knapsack. For tenant traffic scheduling, we design an approximation algorithm based on fully polynomial time approximation scheme (FPTAS). The proposed algorithm achieves the approximation factor of 2+ ε , where ε is an arbitrarily small value. Both small-scale experimental results and large-scale simulation results show the superior performance of our proposed algorithms compared with other alternatives.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488675"}, {"primary_key": "2187076", "vector": [], "sparse_vector": [], "title": "Multi-Robot Path Planning for Mobile Sensing through Deep Reinforcement Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Mobile sensing is an effective way to collect environmental data such as air quality, humidity and temperature at low costs. However, mobile robots are typically battery powered and have limited travel distances. To accelerate data collection in large geographical areas, it is beneficial to deploy multiple robots to perform tasks in parallel. In this paper, we investigate the Multi-Robot Informative Path Planning (MIPP) problem, namely, to plan the most informative paths in a target area subject to the budget constraints of multiple robots. We develop two deep reinforcement learning (RL) based cooperative strategies: independent learning through credit assignment and sequential rollout based learning for MIPP. Both strategies are highly scalable with the number of robots. Extensive experiments are conducted to evaluate the performance of the proposed and baseline approaches using real-world WiFi Received Signal Strength (RSS) data. In most cases, the RL based solutions achieve superior or similar performance as a baseline genetic algorithm (GA)-based solution but at only a fraction of running time during inference. Furthermore, when the budgets and initial positions of the robots change, the pre-trained policies can be applied directly.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488669"}, {"primary_key": "2187077", "vector": [], "sparse_vector": [], "title": "FedServing: A Federated Prediction Serving Framework Based on Incentive Mechanism.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Data holders, such as mobile apps, hospitals and banks, are capable of training machine learning (ML) models and enjoy many intelligence services. To benefit more individuals lacking data and models, a convenient approach is needed which enables the trained models from various sources for prediction serving, but it has yet to truly take off considering three issues: (i) incentivizing prediction truthfulness; (ii) boosting prediction accuracy; (iii) protecting model privacy.We design FedServing, a federated prediction serving framework, achieving the three issues. First, we customize an incentive mechanism based on Bayesian game theory which ensures that joining providers at a Bayesian Nash Equilibrium will provide truthful (not meaningless) predictions. Second, working jointly with the incentive mechanism, we employ truth discovery algorithms to aggregate truthful but possibly inaccurate predictions for boosting prediction accuracy. Third, providers can locally deploy their models and their predictions are securely aggregated inside TEEs. Attractively, our design supports popular prediction formats, including top-1 label, ranked labels and posterior probability. Besides, blockchain is employed as a complementary component to enforce exchange fairness. By conducting extensive experiments, we validate the expected properties of our design. We also empirically demonstrate that FedServing reduces the risk of certain membership inference attack.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488807"}, {"primary_key": "2187078", "vector": [], "sparse_vector": [], "title": "NetMARKS: Network Metrics-AwaRe Kubernetes Scheduler Powered by Service Mesh.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Container technology has revolutionized the way software is being packaged and run. The telecommunications industry, now challenged with the 5G transformation, views containers as the best way to achieve agile infrastructure that can serve as a stable base for high throughput and low latency for 5G edge applications. These challenges make optimal scheduling of performance-sensitive containerized workflows a matter of emerging importance. Meanwhile, the wide adoption of Kubernetes across industries has placed it as a de-facto standard for container orchestration. Several attempts have been made to improve Kubernetes scheduling, but the existing solutions either do not respect current scheduling rules or only considered a static infrastructure viewpoint.To address this, we propose NetMARKS - a novel approach to Kubernetes pod scheduling that uses dynamic network metrics collected with Istio Service Mesh. This solution improves Kubernetes scheduling while being fully backward compatible. We validated our solution using different workloads and processing layouts. Based on our analysis, NetMARKS can reduce application response time up to 37 percent and save up to 50 percent of inter-node bandwidth in a fully automated manner. This significant improvement is crucial to Kubernetes adoption in 5G use cases, especially for multi-access edge computing and machine-to-machine communication.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488670"}, {"primary_key": "2187084", "vector": [], "sparse_vector": [], "title": "Programmable Switches for in-Networking Classification.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deploying accurate machine learning algorithms into a high-throughput networking environment is a challenging task. On the one hand, machine learning has proved itself useful for traffic classification in many contexts (e.g., intrusion detection, application classification, and early heavy hitter identification). On the other hand, most of the work in the area is related to post-processing (i.e., training and testing are performed offline on previously collected samples) or to scenarios where the traffic has to leave the data plane to be classified (i.e., high latency). In this work, we tackle the problem of creating simple and reasonably accurate machine learning models that can be deployed into the data plane in a way that performance degradation is acceptable. To that purpose, we introduce a framework and discuss issues related to the translation of simple models, for handling individual packets or flows, into the P4 language. We validate our framework with an intrusion detection use case and by deploying a single decision tree into a Netronome SmartNIC (Agilio CX 2x10GbE). Our results show that high-accuracy is achievable (above 95%) with minor performance degradation, even for a large number of flows.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488840"}, {"primary_key": "2187086", "vector": [], "sparse_vector": [], "title": "Attack Resilience of Cache Replacement Policies.", "authors": ["<PERSON><PERSON>", "Ting <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Caches are pervasively used in computer networks to speed up access by reusing previous communications, where various replacement policies are used to manage the cached contents. The replacement policy of a cache plays a key role in its performance, and is thus extensively engineered to achieve a high hit ratio in benign environments. However, some studies showed that a policy with a higher hit ratio in benign environments may be more vulnerable to denial of service (DoS) attacks that intentionally send requests for unpopular contents. To understand the cache performance under such attacks, we analyze a suite of representative replacement policies under the framework of TTL approximation in how well they preserve the hit ratios for legitimate users, while incorporating the delay for the cache to obtain a missing content. We further develop a scheme to adapt the cache replacement policy based on the perceived level of attack. Our analysis and validation on real traces show that although no single policy is resilient to all the attack strategies, suitably adapting the replacement policy can notably improve the attack resilience of the cache.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488697"}, {"primary_key": "2187087", "vector": [], "sparse_vector": [], "title": "A Lightweight Integrity Authentication Approach for RFID-enabled Supply Chains.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Major manufacturers and retailers are increasingly using RFID systems in supply-chain scenarios, where theft of goods during transport typically causes significant economic losses for the consumer. Recent sample-based authentication methods attempt to use a small set of random sample tags to authenticate the integrity of the entire tag population, which significantly reduces the authentication time at the expense of slightly reduced reliability. The problem is that it still incurs extensive initialization overhead when writing the authentication information to all of the tags. This paper presents KTAuth, a lightweight integrity authentication approach to efficiently and reliably detect missing tags and counterfeit tags caused by stolen attacks. The competitive advantage of KTAuth is that it only requires writing the authentication information to a small set of deterministic key tags, offering a significant reduction in initialization costs. In addition, KTAuth strictly follows the C1G2 specifications and thus can be deployed on Commercial-Off-The-Shelf RFID systems. Furthermore, KTAuth proposes a novel authentication chain mechanism to verify the integrity of tags exclusively based on data stored on them. To evaluate the feasibility and deployability of KTAuth, we implemented a small-scale prototype system using mainstream RFID devices. Using the parameters achieved from the real experiments, we also conducted extensive simulations to evaluate the performance of KTAuth in large-scale RFID systems.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488775"}, {"primary_key": "2187088", "vector": [], "sparse_vector": [], "title": "Expectile Tensor Completion to Recover Skewed Network Monitoring Data.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Network applications, such as network state tracking and forecasting, anomaly detection, and failure recovery, require complete network monitoring data. However, the monitoring data are often incomplete due to the use of partial measurements and the unavoidable loss of data during transmissions. Tensor completion has attracted some recent attentions with its capability of exploiting the multi-dimensional data structure for more accurate un-measurement/missing data inference. Although conventional tensor completion algorithms can work well when the application data follow the symmetric normal distribution, it cannot well handle network monitoring data which are highly skewed with heavy tails. To better follow the data distribution for more accurate recovery of the missing entries with large values, we propose a novel expectile tensor completion (ETC) formulation and a simple yet efficient tensor completion algorithm without hard-setting parameters for easy implementation. From both experimental and theoretical ways, we prove the convergence of the proposed algorithm. Extensive experiments on two real-world network monitoring datasets demonstrate the effectiveness of the proposed ETC.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488919"}, {"primary_key": "2187089", "vector": [], "sparse_vector": [], "title": "HearFit: Fitness Monitoring on Smart Speakers via Active Acoustic Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Fitness can help to strengthen muscles, increase resistance to diseases and improve body shape. Nowadays, more and more people tend to exercise at home/office, since they lack time to go to the dedicated gym. However, it is difficult for most of them to get good fitness effect due to the lack of professional guidance. Motivated by this, we propose HearFit, the first non-invasive fitness monitoring system based on commercial smart speakers for home/office environments. To achieve this, we turn smart speakers into active sonars. We design a fitness detection method based on Doppler shift and adopt the short time energy to segment fitness actions. We design a high-accuracy LSTM network to determine the type of fitness. Combined with incremental learning, users can easily add new actions. Finally, we evaluate the local (i.e., intensity and duration) and global (i.e., continuity and smoothness) fitness quality of users to help to improve fitness effect and prevent injury. Through extensive experiments including over 7,000 actions of 10 types of fitness with and without dumbbells from 12 participants, HearFit can detect fitness actions with an average accuracy of 96.13%, and give accurate statistics in various environments.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488811"}, {"primary_key": "2187090", "vector": [], "sparse_vector": [], "title": "POLO: Localizing RFID-Tagged Objects for Mobile Robots.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In many Internet-of-Things (IoT) applications, various RFID-tagged objects need to be localized by mobile robots. Existing RFID localization systems are infeasible, since they either demand bulky RFID infrastructures or cannot achieve sufficient localization accuracy. In this paper, a portable localization (POLO) system is developed for a mobile robot to locate RFID-tagged objects. Besides a single RFID reader on board, POLO is distinguished with a tag array and a lightweight receiver. The tag array is designed to reflect the RFID signal from an object into multi-path signals. The receiver captures such signals and estimates their multi-path channel coefficients by a tag-array-assisted channel estimation (TCE) mechanism. Such channel coefficients are further exploited to determine the object's direction by a spatial smoothing direction estimation (SSDE) algorithm. Based on the object's direction, POLO guides the robot to approach the object. When the object is in proximity, its 2D location is finally determined by a near-range positioning (NRP) algorithm. POLO is prototyped and evaluated via extensive experiments. Results show that the average angular error is within 1.6 degrees when the object is in the far-range (2~6 m), and the average location error is within 5 cm while the object is in the near-range (~1 m).", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488882"}, {"primary_key": "2187092", "vector": [], "sparse_vector": [], "title": "SODA: Similar 3D Object Detection Accelerator at Network Edge for Autonomous Driving.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>yang Hou", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Offloading the 3D object detection from autonomous vehicles to MEC is appealing because of the gains on quality, latency, and energy. However, detection requests lead to repetitive computations since the multitudinous requests share approximate detection results. It is crucial to reduce such fuzzy redundancy by reusing the previous results. A key challenge is that the requests mapping to the reusable result are only similar but not identical. An efficient method for similarity matching is needed to justify the use case. To this end, by taking advantage of TCAM's ap-proximate matching capability and NMC's computing efficiency, we design SODA, a first-of-its-kind hardware accelerator which sits in the mobile base stations between autonomous vehicles and MEC servers. We design efficient feature encoding and partition algorithms for SODA to ensure the quality of the similarity matching and result reuse. Our evaluation shows that SODA significantly improves the system performance and the detection results exceed the accuracy requirements on the subject matter, qualifying SODA as a practical domain-specific solution.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488833"}, {"primary_key": "2187095", "vector": [], "sparse_vector": [], "title": "Live Gradient Compensation for Evading Stragglers in Distributed Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The training efficiency of distributed learning systems is vulnerable to stragglers, namely, those slow worker nodes. A naive strategy is performing the distributed learning by incor-porating the fastest K workers and ignoring these stragglers, which may induce high deviation for non-IID data. To tackle this, we develop a Live Gradient Compensation (LGC) strategy to incorporate the one-step delayed gradients from stragglers, aiming to accelerate learning process and utilize the stragglers simultaneously. In LGC framework, mini-batch data are divided into smaller blocks and processed separately, which makes the gradient computed based on partial work accessible. In addition, we provide theoretical convergence analysis of our algorithm for non-convex optimization problem under non-IID training data to show that LGC-SGD has almost the same convergence error as full synchronous SGD. The theoretical results also allow us to quantify a novel tradeoff in minimizing training time and error by selecting the optimal straggler threshold. Finally, extensive simulation experiments of image classification on CIFAR-10 dataset are conducted, and the numerical results demonstrate the effectiveness of our proposed strategy.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488815"}, {"primary_key": "2187096", "vector": [], "sparse_vector": [], "title": "RFace: Anti-Spoofing Facial Authentication Using COTS RFID.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Yuan<PERSON> Zheng", "<PERSON>", "Jinsong Han", "<PERSON>", "<PERSON><PERSON>"], "summary": "Current facial authentication (FA) systems are mostly based on the images of human faces, thus suffering from privacy leakage and spoofing attacks. Mainstream systems utilize facial geometry features for spoofing mitigation, which are still easy to deceive with the feature manipulation, e.g., 3D-printed human faces. In this paper, we propose a novel privacy-preserving anti-spoofing FA system, named RFace, which extracts both the 3D geometry and inner biomaterial features of faces using a COTS RFID tag array. These features are difficult to obtain and forge, hence are resistant to spoofing attacks. RFace only requires users to pose their faces in front of a tag array for a few seconds, without leaking their visual facial information. We build a theoretical model to rigorously prove the feasibility of feature acquisition and the correlation between the facial features and RF signals. For practicality, we design an effective algorithm to mitigate the impact of unstable distance and angle deflection from the face to the array. Extensive experiments with 30 participants and three types of spoofing attacks show that RFace achieves an average authentication success rate of over 95.7% and an EER of 4.4%. More importantly, no spoofing attack succeeds in deceiving <PERSON><PERSON> in the experiments.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488737"}, {"primary_key": "2187097", "vector": [], "sparse_vector": [], "title": "Near Optimal and Dynamic Mechanisms Towards a Stable NFV Market in Multi-Tier Cloud Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Liang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "With the fast development of next-generation networking techniques, a Network Function Virtualization (NFV) market is emerging as a major market that allows network service providers to trade various network services among consumers. Therefore, efficient mechanisms that guarantee stable and efficient operations of the NFV market are urgently needed. One fundamental problem in the NFV market is how to maximize the social welfare of all players, so they have incentives to participate in activities of the market. In this paper, we first formulate the social welfare maximization problem, with an aim to maximize the total revenue of all players in the NFV market. For the social welfare maximization problem, we design an efficient incentive-compatible mechanism and analyze the existence of a Nash equilibrium of the mechanism. We also consider an online social welfare maximization problem without the knowledge of future request arrivals. We devise an online learning algorithm based on Multi-Armed Bandits (MAB) to allow both customers and network service providers to make decisions with uncertainty of customers' strategy. We evaluate the performance of the proposed mechanisms by both simulations and test-bed implementations, and the results show that the proposed mechanisms obtain at most 23% higher social welfare than existing studies.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488819"}, {"primary_key": "2187098", "vector": [], "sparse_vector": [], "title": "Pyramid: Real-Time LoRa Collision Decoding with Peak Tracking.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "LoRa, as a representative Lower Power Wide Area Network (LPWAN) technology, shows great potential in providing low power and long range wireless communication. Real LoRa deployments, however, suffer from severe collisions. Existing collision decoding methods cannot work well for low SNR LoRa signals. Most LoRa collision decoding methods process collisions offline and cannot support real-time collision decoding in practice. To address these problems, we propose Pyramid, a real-time LoRa collision decoding approach. To the best of our knowledge, this is the first real-time multi-packet LoRa collision decoding approach in low SNR. Pyramid exploits the subtle packet offset to separate packets in a collision. The core of Pyramid is to combine signals in multiple windows and transfers variation of chirp length in multiple windows to robust features in the frequency domain that are resistant to noise. We address practical challenges including accurate peak recovery and feature extraction in low SNR signals of collided packets. We theoretically prove that Pyramid incurs a very small SNR loss (<; 0.56 dB) to original LoRa transmissions. We implement Pyramid using USRP N210 and evaluate its performance in a 20-nodes network. Evaluation results show that Pyramid achieves real-time collision decoding and improves the throughput by 2.11 ×.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488695"}, {"primary_key": "2187101", "vector": [], "sparse_vector": [], "title": "vGaze: Implicit Saliency-Aware Calibration for Continuous Gaze Tracking on Mobile Devices.", "authors": ["Songzhou Yang", "<PERSON>", "<PERSON><PERSON>"], "summary": "Gaze tracking is a useful human-to-computer interface, which plays an increasingly important role in a range of mobile applications. Gaze calibration is an indispensable component of gaze tracking, which transforms the eye coordinates to the screen coordinates. The existing approaches of gaze tracking either have limited accuracy or require the user's cooperation in calibration and in turn hurt the quality of experience. We in this paper propose vGaze, implicit saliency-aware calibration for continuous gaze tracking on mobile devices. The design of vGaze stems from our insight on the temporal and spatial dependent relation between the visual saliency and the user's gaze. vGaze is implemented as a light-weight software that identifies video frames with \"useful\" saliency information, sensing the user's head movement, and performs opportunistic calibration using only those \"useful\" frames. We implement vGaze on a commercial mobile device and evaluate its performance in various scenarios. The results show that vGaze can work at real time with video playback applications. The average error of gaze tracking is 1.51cm.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488668"}, {"primary_key": "2187103", "vector": [], "sparse_vector": [], "title": "Bandit Learning with Predicted Context: Regret Analysis and Selective Context Query.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Contextual bandit learning selects actions (i.e., arms) based on context information to maximize rewards while balancing exploitation and exploration. In many applications (e.g., cloud resource management with dynamic workloads), before arm selection, the agent/learner can either predict context information online based on context history or selectively query the context from an outside expert. Motivated by this practical consideration, we study a novel contextual bandit setting where context information is either predicted online or queried from an expert. First, considering predicted context only, we quantify the impact of context prediction on the cumulative regret (compared to an oracle with perfect context information) by deriving an upper bound on regret, which takes the form of a weighted combination of regret incurred by standard bandit learning and the context prediction error. Then, inspired by the regret's structural decomposition, we propose context query algorithms to selectively obtain outside expert's input (subject to a total query budget) for more accurate context, decreasing the overall regret. Finally, we apply our algorithms to virtual machine scheduling on cloud platforms. The simulation results validate our regret analysis and shows the effectiveness of our selective context query algorithms.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488896"}, {"primary_key": "2187104", "vector": [], "sparse_vector": [], "title": "Individual Load Forecasting for Multi-Customers with Distribution-aware Temporal Pooling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "For smart grid services, accurate individual load forecasting is an essential element. When training individual forecasting models for multi-customers, discrepancies in data distribution among customers should be considered; there are two simple ways to build the models considering multi-customers: constructing each model independently or training as one model encompassing multi-customers. The independent approach shows higher accuracy than the latter. However, it deploys copious models, causing resource/management inefficiency; the latter is the opposite. A compromise between these two could be clustering-based forecasting. However, the previous studies are limited in applying to individual forecasting in that they focus on aggregated load and do not consider concept drift, which degrades accuracy over time. Therefore, we propose a distribution-aware temporal pooling framework that is enhanced clustering-based forecasting. For the clustering, we propose Variational Recurrent Deep Embedding (VaRDE) working in a distribution-aware manner, so it is suitable to process individual load. It allocates clusters to customers every time, so the clusters, where customers are assigned, are dynamically changed to resolve distribution change. We conducted experiments with real data for evaluation, and the result showed better performance than previous studies, especially with a few models even for unseen data, leading to high scalability.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488816"}, {"primary_key": "2187105", "vector": [], "sparse_vector": [], "title": "Bandwidth Isolation Guarantee for SDN Virtual Networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Yang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We introduce TeaVisor, which provides bandwidth isolation guarantee for software-defined networking (SDN)-based network virtualization (NV). SDN-NV provides topology and address virtualization while allowing flexible resource provisioning, control, and monitoring of virtual networks. However, to the best of our knowledge, the bandwidth isolation guarantee, which is essential for providing stable and reliable throughput on network services, is missing in SDN-NV. Without bandwidth isolation guarantee, tenants suffer degraded service quality and significant revenue loss. In fact, we find that the existing studies on bandwidth isolation guarantees are insufficient for SDN-NV. With SDN-NV, routing is performed by tenants, and existing studies have not addressed the overloaded link problem. To solve this problem, TeaVisor designs three components: path virtualization, bandwidth reservation, and path establishment, which utilize multipath routing. With these, TeaVisor achieves the bandwidth isolation guarantee while preserving the routing of the tenants. In addition, TeaVisor guarantees the minimum and maximum amounts of bandwidth simultaneously. We fully implement TeaVisor, and the comprehensive evaluation results show that near-zero error rates on achieving the bandwidth isolation guarantee. We also present an overhead analysis of control traffic and memory consumption.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488797"}, {"primary_key": "2187106", "vector": [], "sparse_vector": [], "title": "Joint Cache Size Scaling and Replacement Adaptation for Small Content Providers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Elastic Content Delivery Networks (Elastic CDNs) have been introduced to support explosive Internet traffic growth by providing small Content Providers (CPs) with just-in-time services. Due to the diverse requirements of small CPs, they need customized adaptive caching modules to help them adjust the cached contents to maximize their long-term utility. The traditional adaptive caching module is usually a built-in service in a cloud CDN. They adaptively change cache contents using size-scaling-only methods or strategy-adaptation-only methods. A natural question is: can we jointly optimize size and strategy to achieve tradeoff and better performance for small CPs when renting services from elastic CDNs? The problem is challenging because the two decision variables could involve both discrete and categorical variables, where discrete variables have an intrinsic order while categorical variables do not. In this paper, we propose a distribution-guided reinforcement learning framework JEANA to learn the joint cache size scaling and strategy adaptation policy. We design a distribution-guided regularizer to keep the intrinsic order of discrete variables. More importantly, we prove that our algorithm has a theoretical guarantee of performance improvement. Trace-driven experimental results demonstrate our method can improve the hit ratio while reducing the rental cost.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488694"}, {"primary_key": "2187108", "vector": [], "sparse_vector": [], "title": "A Sum-of-Ratios Multi-Dimensional-Knapsack Decomposition for DNN Resource Scheduling.", "authors": ["<PERSON><PERSON><PERSON> Yu", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In recent years, to sustain the resource-intensive computational needs for training deep neural networks (DNNs), it is widely accepted that exploiting the parallelism in large-scale computing clusters is critical for the efficient deployments of DNN training jobs. However, existing resource schedulers for traditional computing clusters are not well suited for DNN training, which results in unsatisfactory job completion time performance. The limitations of these resource scheduling schemes motivate us to propose a new computing cluster resource scheduling framework that is able to leverage the special layered structure of DNN jobs and significantly improve their job completion times. Our contributions in this paper are three-fold: i) We develop a new resource scheduling analytical model by considering DNN's layered structure, which enables us to analytically formulate the resource scheduling optimization problem for DNN training in computing clusters; ii) Based on the proposed performance analytical model, we then develop an efficient resource scheduling algorithm based on the widely adopted parameter-server architecture using a sum-of-ratios multi-dimensional-knapsack decomposition (SMD) method to offer strong performance guarantee; iii) We conduct extensive numerical experiments to demonstrate the effectiveness of the proposed schedule algorithm and its superior performance over the state of the art.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488916"}, {"primary_key": "2187109", "vector": [], "sparse_vector": [], "title": "Code is the (F)Law: Demystifying and Mitigating Blockchain Inconsistency Attacks Caused by Software Bugs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Yuandong Ni", "<PERSON><PERSON><PERSON>"], "summary": "Blockchains promise to provide a tamper-proof medium for transactions, and thus enable many applications including cryptocurrency. As a system built on consensus, the correctness of a blockchain heavily relies on the consistency of states between its nodes. But consensus protocols of blockchains only guarantee the consistency in the transaction sequence rather than nodes' internal states. Instead, nodes must replay and exe-cute all transactions to maintain their local states independently. When executing transactions, any different execution result could cause a node out-of-sync and thus gets isolated from other nodes.After systematically modeling the transaction execution process in blockchains, we present a new attack INCITE, which can lead different nodes to different states. Specifically, attackers could invoke an ambiguous transaction of a vulnerable smart contract, utilize software bugs in smart contracts to lead nodes that execute this transaction into different states. Unlike attacks that bring short-term inconsistencies, such as fork attacks, INCITE can cause nodes in the blockchain to fall into a long-term inconsistent state, which further leads to great damages to the chain (e.g., double-spending attacks and expelling mining power). We have discovered 7 0day vulnerabilities in 5 popular blockchains which can enable this attack. We also proposed a defense solution to mitigate this threat. Experiments showed that it is effective and lightweight.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488749"}, {"primary_key": "2187110", "vector": [], "sparse_vector": [], "title": "Efficient and Verifiable Proof of Replication with Fast Fault Localization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Chen", "<PERSON><PERSON>", "Jianting Ning", "<PERSON>", "<PERSON>"], "summary": "Proof of replication technique has been widely used to verify whether the cloud service providers (CSPs) store multiple replications of a file with dedicated and unique storage space, which effectively prevents CSPs from colluding and storing only one copy of the file. In this field, many representative schemes have been proposed and applied to various scenarios. However, most of the existing schemes are based on the timing assumption (i.e., the verifier rejects the proof of replication if the prover's response is timeout) and do not explicitly consider the problem of batch verification and fault localization. This will bring unnecessary computational overhead to the verifier and reduce the efficiency of batch auditing. To address the above problems, we propose a verifiable proof of replication scheme with fast fault localization and high efficiency. By integrating incompressible encoding and homomorphic linear authenticator, our scheme can effectively audit the integrity of file replications without timing assumptions. To support batch verification and fault localization, we propose a reversed signature aggregation tree (Rev-tree) by integrating the quick binary search and exponent testing. Compared with the traditional binary tree, Rev-tree can further reduce the overhead of batch verification and effectively locate a single fault replication. Moreover, benefit from the property of Rev-tree taking the existing error probability as an estimate of the rest of the tree, our scheme can adjust the verification strategy dynamically to meet with different situations. Finally, security analysis and experimental results show that our scheme is secure and efficient in proof of replication and fast fault localization.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488729"}, {"primary_key": "2187113", "vector": [], "sparse_vector": [], "title": "Privacy-Preserving Learning of Human Activity Predictors in Smart Environments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ladislau Bölöni", "<PERSON><PERSON>"], "summary": "The daily activities performed by a disabled or elderly person can be monitored by a smart environment, and the acquired data can be used to learn a predictive model of user behavior. To speed up the learning, several researchers designed collaborative learning systems that use data from multiple users. However, disclosing the daily activities of an elderly or disabled user raises privacy concerns.In this paper, we use state-of-the-art deep neural network-based techniques to learn predictive human activity models in the local, centralized, and federated learning settings. A novel aspect of our work is that we carefully track the temporal evolution of the data available to the learner and the data shared by the user. In contrast to previous work where users shared all their data with the centralized learner, we consider users that aim to preserve their privacy. Thus, they choose between approaches in order to achieve their goals of predictive accuracy while minimizing the shared data. To help users make decisions before disclosing any data, we use machine learning to predict the degree to which a user would benefit from collaborative learning. We validate our approaches on real-world data.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488681"}, {"primary_key": "2187114", "vector": [], "sparse_vector": [], "title": "Characterizing Ethereum&apos;s Mining Power Decentralization at a Deeper Level.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "For proof-of-work blockchains such as Ethereum, the mining power decentralization is an important discussion point in the community. Previous studies mostly focus on the aggregated power of the mining pools, neglecting the pool participants who are the source of the pools' power. In this paper, we present the first large-scale study of the pool participants in Ethereum's mining pools. Pool participants are not directly observable because they communicate with their pools via private channels. However, they leave \"footprints\" on chain as they use Ethereum accounts to anonymously receive rewards from mining pools. For this study, we combine several data sources to identify 62,358,646 pool reward transactions sent by 47 pools to their participants over Ethereum's entire near 5-year history. Our analyses about these transactions reveal interesting insights about three aspects of pool participants: the power decentralization at the participant level, their pool-switching behavior, and why they participate in pools. Our results provide a complementary and more balanced view about Ethereum's mining power decentralization at a deeper level.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488812"}, {"primary_key": "2187116", "vector": [], "sparse_vector": [], "title": "Federated Learning over Wireless Networks: A Band-limited Coordinated Descent Approach.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider a many-to-one wireless architecture for federated learning at the network edge, where multiple edge devices collaboratively train a model using local data. The unreliable nature of wireless connectivity, together with constraints in computing resources at edge devices, dictates that the local updates at edge devices should be carefully crafted and compressed to match the wireless communication resources available and should work in concert with the receiver. Thus motivated, we propose SGD-based bandlimited coordinate descent algorithms for such settings. Specifically, for the wireless edge employing over-the-air computing, a common subset of k-coordinates of the gradient updates across edge devices are selected by the receiver in each iteration, and then transmitted simultaneously over k sub-carriers, each experiencing time-varying channel conditions. We characterize the impact of communication error and compression, in terms of the resulting gradient bias and mean squared error, on the convergence of the proposed algorithms. We then study learning-driven communication error minimization via joint optimization of power allocation and learning rates. Our findings reveal that optimal power allocation across different sub-carriers should take into account both the gradient values and channel conditions, thus generalizing the widely used water-filling policy. We also develop sub-optimal distributed solutions amenable to implementation.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488818"}, {"primary_key": "2187117", "vector": [], "sparse_vector": [], "title": "Near-Optimal Topology-adaptive Parameter Synchronization in Distributed DNN Training.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Zongpeng Li"], "summary": "Distributed machine learning with multiple concurrent workers has been widely adopted to train large deep neural networks (DNNs). Parameter synchronization is a key component in each iteration of distributed training, where workers exchange locally computed gradients through an AllReduce operation or parameter servers, for global parameter updates. Parameter synchronization often constitutes a significant portion of the training time; minimizing the communication time contributes substantially to DNN training speed-up. Standard ring-based AllReduce or PS architecture work efficiently mostly with homogeneous inter-worker connectivity. However, available bandwidth among workers in real-world clusters is often heterogeneous, due to different hardware configurations, switching topologies, and contention with concurrent jobs. This work investigates the best parameter synchronization topology and schedule among workers for most expedited communication in distributed DNN training. We show that the optimal parameter synchronization topology should be comprised of trees with different workers as roots, each for aggregating or broadcasting a partition of gradients/parameters. We identify near-optimal forest packing to maximally utilize available bandwidth and overlap aggregation and broadcast stages to minimize communication time. We provide theoretical analysis of the performance bound, and show that our scheme outperforms state-of-the-art parameter synchronization schemes by up to 18.3 times with extensive evaluation under various settings.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488678"}, {"primary_key": "2187118", "vector": [], "sparse_vector": [], "title": "Low Sample and Communication Complexities in Decentralized Learning: A Triple Hybrid Approach.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Network-consensus-based decentralized learning optimization algorithms have attracted a significant amount of attention in recent years due to their rapidly growing applications. However, most of the existing decentralized learning algorithms could not achieve low sample and communication complexities simultaneously - two important metrics in evaluating the trade-off between computation and communication costs of decentralized learning. To overcome these limitations, in this paper, we propose a triple hybrid decentralized stochastic gradient descent (TH-DSGD) algorithm for efficiently solving non-convex network-consensus optimization problems for decentralized learning. We show that to reach an ϵ 2 -stationary solution, the total sample complexity of TH-DSGD is O(ϵ -3 ) and the communication complexity is O(ϵ -3 ), both of which are independent of dataset sizes and significantly improve the sample and communication complexities of the existing works. We conduct extensive experiments with a variety of learning models to verify our theoretical findings. We also show that our TH-DSGD algorithm is stable as the network topology gets sparse and enjoys better convergence in the large-system regime.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488686"}, {"primary_key": "2187119", "vector": [], "sparse_vector": [], "title": "DeepReserve: Dynamic Edge Server Reservation for Connected Vehicles with Deep Reinforcement Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Edge computing is promising to provide computational resources for connected vehicles. Resource demands for edge servers vary due to vehicle mobility. It is then challenging to reserve edge servers to meet variable demands. Existing schemes rely on statistical information of resource demands to determine edge server reservation. They are infeasible in practice, since the reservation based on statistics cannot adapt to time-varying demands. In this paper, a spatio-temporal reinforcement learning scheme called DeepReserve is developed to learn variable demands and then reserve edge servers accordingly. DeepReserve is adapted from the deep deterministic policy gradient algorithm with two major enhancements. First, by observing that the spatio-temporal correlation in vehicle traffic leads to the same property in resource demands of CVs, a convolutional LSTM network is employed to encode resource demands observed by edge servers for inference of future demands. Second, an action amender is designed to make sure an action does not violate spatio-temporal correlation. We also design a new training method, i.e., DR-Train, to stabilize the training procedure. DeepReserve is evaluated via experiments based on real-world datasets. Results show it achieves better performance than state-of-the-art approaches that require accurate demand information.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488888"}, {"primary_key": "2187120", "vector": [], "sparse_vector": [], "title": "Dual Attention-Based Federated Learning for Wireless Traffic Prediction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Wireless traffic prediction is essential for cellular networks to realize intelligent network operations, such as load-aware resource management and predictive control. Existing prediction approaches usually adopt centralized training architectures and require the transferring of huge amounts of traffic data, which may raise delay and privacy concerns for certain scenarios. In this work, we propose a novel wireless traffic prediction framework named \\textit{Dual Attention-Based Federated Learning} (FedDA), by which a high-quality prediction model is trained collaboratively by multiple edge clients. To simultaneously capture the various wireless traffic patterns and keep raw data locally, FedDA first groups the clients into different clusters by using a small augmentation dataset. Then, a quasi-global model is trained and shared among clients as prior knowledge, aiming to solve the statistical heterogeneity challenge confronted with federated learning. To construct the global model, a dual attention scheme is further proposed by aggregating the intra- and inter-cluster models, instead of simply averaging the weights of local models. We conduct extensive experiments on two real-world wireless traffic datasets and results show that FedDA outperforms state-of-the-art methods. The average mean squared error performance gains on the two datasets are up to 10\\% and 30\\%, respectively.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488883"}, {"primary_key": "2187121", "vector": [], "sparse_vector": [], "title": "Signal Detection and Classification in Shared Spectrum: A Deep Learning Approach.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Amirhos<PERSON>n <PERSON>"], "summary": "Accurate identification of the signal type in shared-spectrum networks is critical for efficient resource allocation and fair coexistence. It can be used for scheduling transmission opportunities to avoid collisions and improve system throughput, especially when the environment changes rapidly. In this paper, we develop deep neural networks (DNNs) to detect coexisting signal types based on In-phase/Quadrature (I/Q) samples without decoding them. By using segments of the samples of the received signal as input, a Convolutional Neural Network (CNN) and a Recurrent Neural Network (RNN) are combined and trained using categorical cross-entropy (CE) optimization. Classification results for coexisting Wi-Fi, LTE LAA, and 5G NR-U signals in the 5-6 GHz unlicensed band show high accuracy of the proposed design. We then exploit spectrum analysis of the I/Q sequences to further improve the classification accuracy. By applying Short-time Fourier Transform (STFT), additional information in the frequency domain can be presented as a spectrogram. Accordingly, we enlarge the input size of the DNN. To verify the effectiveness of the proposed detection framework, we conduct over-the-air (OTA) experiments using USRP radios. The proposed approach can achieve accurate classification in both simulations and hardware experiments.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488834"}, {"primary_key": "2187122", "vector": [], "sparse_vector": [], "title": "De-anonymizing Social Networks Under Partial Overlap: An F-score Based Approach.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper studies social network de-anonymization problem, which aims to identify users of an anonymized network by matching its user set with that of another auxiliary sanitized network. Prior arts primarily assume that both networks share exactly the same set of users, as opposed to many real situations of partially shared users in between. Different from the full matching case that only needs to take care of increasing the number of correctly matched pairs, the case of partial overlapping imposes additional demand on avoiding the wrong matches of those who do not have accounts across networks.To this end, we establish a new cost function, which we call the structural F-score to incorporate both the structural commonness and difference across networks. Intrinsically, the structural F-score computes the ratio of link agreements and disagreements, thus serving as the harmonic mean of precision and recall for any given matching function. Theoretically, we show that for networks parameterized by node overlap t 2 and link overlap s 2 , as long as the mean degree of networks grows as Ω(t -2 s -3 log n), maximizing the structural F-score provably ensures the perfect matching, where the nodal precision and recall are both maximized to 1. Algorithmically, for small-scale networks, we propose a two-step heuristic of F-score based de-anonymization, which firstly finds the optimal full matching between networks and then removes those pairs hindering structural F-score maximization. Due to the universal adaptability of the structural F-score, we further extend the algorithm to large-scale networks via a progressive matching process. Empirical results also validate the effectiveness of our methods in terms of improving the nodal F-score.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488911"}, {"primary_key": "2187123", "vector": [], "sparse_vector": [], "title": "ITE: A Structural Entropy Based Approach for Source Detection.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper studies the problem of source detection, which is to infer the source node out of an aftermath of a cascade, i.e., the observed infected graph GN of the network at some time. Prior arts have adopted various statistical quantities such as degree, distance or infection size to reflect the structural centrality of the source. In this paper, we propose a new metric which we call the infected tree entropy (ITE), to utilize richer underlying structural features for source detection. Our idea of ITE is inspired by the conception of structural entropy [1], which demonstrated that the minimization of average bits to encode the network structures with different partitions is the principle for detecting the natural or true structures in real-world networks. Accordingly, our proposed ITE based estimator for the source tries to minimize the coding of network partitions brought by the infected tree rooted at all the potential sources, thus minimizing the structural deviation between the cascades from the potential sources and the actual infection process included in GN. On polynomially growing geometric trees, with increasing tree heterogeneity, the ITE estimator remarkably yields more reliable detection under only moderate infection sizes. In contrast, for regular expanding trees, we still observe guaranteed detection probability of ITE estimator even with an infinite infection size, thanks to the degree regularity property. We also algorithmically realize the ITE based detection that enjoys linear time complexity via a message-passing scheme, and further extend it to general graphs. Experiments on various network topologies confirm the superiority of ITE to the baselines. For example, ITE returns an accuracy of 75% ranking the source among top 5%, far exceeding 45% of the classic algorithms on scale-free networks.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488696"}, {"primary_key": "2187125", "vector": [], "sparse_vector": [], "title": "FedSens: A Federated Learning Approach for Smart Health Sensing with Class Imbalance in Resource Constrained Edge Computing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The advance of mobile sensing and edge computing has brought new opportunities for abnormal health detection (AHD) systems where edge devices such as smartphones and wearable sensors are used to collect people's health information and provide early alerts for abnormal health conditions such as stroke and depression. The recent development of federated learning (FL) allows participants to collaboratively train powerful AHD models while keeping their health data private to local devices. This paper targets at addressing a critical challenge of adapting FL to train AHD models, where the participants' health data is highly imbalanced and contains biased class distributions. Existing FL solutions fail to address the class imbalance issue due to the strict privacy requirements of participants as well as the heterogeneous resource constraints of their edge devices. In this work, we propose FedSens, a new FL framework dedicated to address the class imbalance problem in AHD applications with explicit considerations of participant privacy and device resource constraints. We evaluate FedSens using a real-world edge computing testbed on two real-world AHD applications. The results show that FedSens can significantly improve the accuracy of AHD models in the presence of severe class imbalance with low energy cost to the edge devices.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488776"}, {"primary_key": "2187127", "vector": [], "sparse_vector": [], "title": "SILoc: A Speed Inconsistency-Immune Approach to Mobile RFID Robot Localization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> Gu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mobile RFID robots have been increasingly used in warehousing and intelligent manufacturing scenarios to pinpoint the locations of tagged objects. The accuracy of state-of-the-art RFID robot localization systems depends much on the stability of robot moving speed. However, in reality this assumption can hardly be guaranteed because a Commercial-Off-The-Shelf (COTS) robot typically has an inconsistent moving speed, and a small speed inconsistency will cause a large localization error. To this end, we propose a Speed Inconsistency-Immune approach to mobile RFID robot Localization (SILoc) system, which can accurately locate RFID tagged targets when the robot moving speed varies or is even unknown. SILoc employs multiple antennas fixed on the mobile robot to collect the phase data of target tags. We propose an optimized unwrapping method to maximize the use of the phase data, and a lightweight algorithm to calculate the locations in both 2D and 3D spaces based on the unwrapped phase profile. By utilizing the characteristics of tag-antenna distance and combining the phase data from multiple antennas, SILoc can effectively eliminate the side effects of moving speed inconsistency. Extensive experimental results demonstrate that SILoc can achieve a centimeter-level localization accuracy in the scenario with an inconsistent or unknown robot moving speed.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488718"}, {"primary_key": "2187128", "vector": [], "sparse_vector": [], "title": "Minimizing the Number of Deployed UAVs for Delay-bounded Data Collection of IoT Devices.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Xu", "<PERSON><PERSON>", "<PERSON><PERSON> Liang", "<PERSON><PERSON><PERSON>", "Xiaojiang Ren", "<PERSON><PERSON>"], "summary": "In this paper, we study the deployment of Unmanned Aerial Vehicles (UAVs) to collect data from IoT devices, by finding the data collection tour of each UAV. To ensure the `freshness' of the collected data, a strict requirement is that the total time spent in the tour of each UAV, which consists of UAV flying time and data collection time, must be no greater than a given maximum data collection delay B, e.g., 20 minutes. In this paper, we consider a problem of using the minimum number of UAVs and finding their data collection tours, subject to the constraint that the total time spent in each tour is no greater than B. We study two variants of the problem, one is that a UAV needs to fly to the location of each IoT device to collect its data; the other variant is that a UAV is able to collect the data of the IoT device as long as their Euclidean distance is no greater than a given wireless transmission range. For the first variant of the problem, we propose a novel 4-approximation algorithm, which improves the best approximation ratio 4 4/7 so far. For the second variant, we design the first constant factor approximation algorithm. In addition, we evaluate the performance of the proposed algorithms via extensive experiments, and experimental results show that the average numbers of UAVs deployed by the proposed algorithms are from 11% to 19% less than those by existing algorithms.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488887"}, {"primary_key": "2187130", "vector": [], "sparse_vector": [], "title": "Accelerating LSH-based Distributed Search with In-network Computation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Zhenyu Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Locality Sensitive Hashing (LSH) is widely adopted to index similar data in high-dimensional space for approximate nearest neighbor search. With the rapid increase of datasets, recent interests in LSH have moved to the implementation of distributed search systems with low response time and high throughput. However, as the scale of the concurrent queries and the volume of available data grow, large amounts of index messages still need to be transmitted to centralized servers for the candidate answer reducing and resorting. Hence, the network remains the bottleneck in distributed search systems.To address this gap, we turn our efforts to the network itself and propose NetSHa. NetSHa exploits the in-network computational capacity provided by programmable switches. Specially, NetSHa designs a sort-reduce approach to drop the potential poor candidate answers and aggregates the good candidate answers on programmable switches, while preserving the search quality. We implement NetSHa on Barefoot Tofino switches and evaluate it using 3 datasets (i.e., Random, Wiki and Image). The experimental results show that NetSHa reduces the packet volume by 10 times at most and improves the search efficiency by 3x at least, in comparison with typical LSH-based distributed search frameworks.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488722"}, {"primary_key": "2187132", "vector": [], "sparse_vector": [], "title": "Statistical Delay and Error-Rate Bounded QoS Provisioning for 6G mURLLC Over AoI-Driven and UAV-Enabled Wireless Networks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Massive ultra-reliable and low latency communications (mURLLC) has been developed as a new and dominating 6G standard traffic service to support statistical delay and error-rate bounded quality-of-services (QoS) provisioning for real-time data-transmissions. Inspired by mURLLC, finite blocklength coding (FBC) has been proposed to upper-bound both delay and errorrate by using short-packet data communications. On the other hand, to solve the massive connectivity problem imposed by mURLLC, the unmanned aerial vehicle (UAV)-enabled systems are developed by leveraging their deploying flexibility and high probability of establishing line-of-sight (LoS) wireless links while guaranteeing various QoS requirements. In addition, the age of information (AoI) has recently emerged as a new QoS performance metric in terms of information freshness. However, how to efficiently integrate and implement the above new techniques for statistical delay and error-rate bounded QoS provisioning over 6G standards has neither been well understood nor thoroughly studied. To overcome these challenges, we propose the statistical delay and error-rate bounded QoS provisioning schemes which leverage the AoI technique as a key QoS performance metric to efficiently support mURLLC over UAV-enabled 6G wireless networks in the finite blocklength regime. Specifically, first, we develop the UAV-enabled 3D wireless networking models with wireless-link channels using FBC. Second, we build up the AoI-metric based modeling frameworks in the finite blocklength regime. Third, taking into account the peak AoI violation probability, we formulate and solve the AoI-driven ε -effective capacity maximization problems to support statistical delay and error-rate bounded QoS provisioning. Finally, we conduct the extensive simulations to validate and evaluate our developed schemes.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488836"}, {"primary_key": "2187133", "vector": [], "sparse_vector": [], "title": "Optimal Resource Allocation for Statistical QoS Provisioning in Supporting mURLLC Over FBC-Driven 6G Terahertz Wireless Nano-Networks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The new and important service class of massive Ultra-Reliable Low-Latency Communications (mURLLC) is defined in the 6G era to guarantee very stringent quality-of-service (QoS) requirements, such as ultra-high data rate, super-high reliability, tightly-bounded end-to-end latency, etc. Various 6G promising techniques, such as finite blocklength coding (FBC) and Terahertz (THz), have been proposed to significantly improve QoS performances of mURLLC. Furthermore, with the rapid developments in nano techniques, THz wireless nano-networks have drawn great research attention due to its ability to support ultra-high data-rate while addressing the spectrum scarcity and capacity limitations problems. However, how to efficiently integrate THz-band nano communications with FBC in supporting statistical delay/error-rate bounded QoS provisioning for mURLLC still remains as an open challenge over 6G THz wireless nano-networks. To overcome these problems, in this paper we propose the THz-band statistical delay/error-rate bounded QoS provisioning schemes in supporting mURLLC standards by optimizing both the transmit power and blocklength over 6G THz wireless nano-networks in the finite blocklength regime. Specifically, first, we develop the FBC-driven THz-band wireless channel models in nano-scale. Second, we build up the THz-band interference model and derive the channel capacity and channel dispersion functions using FBC. Third, we maximize the ϵ-effective capacity by developing the joint optimal resource allocation policies under statistical delay/error-rate bounded QoS constraints. Finally, we conduct the extensive simulations to validate and evaluate our proposed schemes at the THz band in the finite blocklength regime.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488905"}, {"primary_key": "2187135", "vector": [], "sparse_vector": [], "title": "Counter-Collusion Smart Contracts for Watchtowers in Payment Channel Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ruozhou Yu"], "summary": "Payment channel networks (PCNs) are proposed to improve the cryptocurrency scalability by settling off-chain transactions. However, PCN introduces an undesirable assumption that a channel participant must stay online and be synchronized with the blockchain to defend against frauds. To alleviate this issue, watchtowers have been introduced, such that a hiring party can employ a watchtower to monitor the channel for fraud. However, a watchtower might profit from colluding with a cheating counterparty and fail to perform this job. Existing solutions either focus on heavy cryptographic techniques or require a large collateral. In this work, we leverage smart contracts through economic approaches to counter collusions for watchtowers in PCNs. This brings distrust between the watchtower and the counterparty, so that rational parties do not collude or cheat. We provide detailed analyses on the contracts and rigorously prove that the contracts are effective to counter collusions with minimal on-chain operations. In particular, a watchtower only needs to lock a small collateral, which incentivizes participation of watchtowers and users. We also provide an implementation of the contracts in Solidity and execute them on Ethereum to demonstrate the scalability and efficiency of the contracts.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488831"}, {"primary_key": "2187137", "vector": [], "sparse_vector": [], "title": "Redundant Entanglement Provisioning and Selection for Throughput Maximization in Quantum Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Quantum communication using qubits based on the principle of entangled photons is a promising solution to improve network security. However, it is difficult to successfully create an entanglement link or connection between two nodes, especially when they are far apart from each other. In addition, only one qubit can be exchanged over an established entanglement connection, resulting in a low throughput.In this paper, we propose Redundant Entanglement Pro-visioning and Selection (REPS) to maximize the throughput for multiple source-destination (SD) pairs in a circuit-switched, multi-hop quantum network. REPS has two distinct features: (i). It provisions backup resources for extra entanglement links between adjacent nodes for failure-tolerance; and (ii). It provides flexibility in selecting successfully created entanglement links to establish entanglement connections for the SD pairs to achieve network-wide optimization. Extensive analysis and simulations show that REPS can achieve optimal routing with a high probability, and improves the throughput by up to 68.35% over the highest-performing algorithms in existence. In addition, it also improves the fairness among the SD pairs in the networks.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488850"}, {"primary_key": "2187139", "vector": [], "sparse_vector": [], "title": "Leveraging Domain Knowledge for Robust Deep Reinforcement Learning in Networking.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Qing<PERSON> Duan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Xu"], "summary": "The past few years has witnessed a surge of interest towards deep reinforcement learning (Deep RL) in computer networks. With extraordinary ability of feature extraction, Deep RL has the potential to re-engineer the fundamental resource allocation problems in networking without relying on pre-programmed models or assumptions about dynamic environments. However, such black-box systems suffer from poor robustness, showing high performance variance and poor tail performance. In this work, we propose a unified Teacher-Student learning framework that harnesses rich domain knowledge to improve robustness. The domain-specific algorithms, less performant but more trustable than Deep RL, play the role of teachers providing advice at critical states; the student neural network is steered to maximize the expected reward as usual and mimic the teacher's advice meanwhile. The Teacher-Student method comprises of three modules where the confidence check module locates wrong decisions and risky decisions, the reward shaping module designs a new updating function to incentive the learning of student network, and the prioritized experience replay module to effectively utilize the advised actions. We further implement our Teacher-Student framework in existing video streaming (Pensieve), load balancing (DeepLB) and TCP congestion control (Aurora). Experimental results manifest that the proposed approach reduces the performance standard deviation of DeepLB by 37%; it improves the 90th, 95th and 99th tail performance of Pensieve by 7.6%, 8.8%, 10.7% respectively; and it accelerates the rate of growth of Aurora by 2x at the initial stage, and achieves a more stable performance in dynamic environments.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488863"}, {"primary_key": "2187140", "vector": [], "sparse_vector": [], "title": "First-Order Efficient General-Purpose Clean-Label Data Poisoning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As one of the recently emerged threats to Deep Learning (DL) models, clean-label data poisoning can teach DL models to make wrong predictions on specific target data, such as images or network traffic packets, by injecting a small set of poisoning data with clean labels into the training datasets. Although several clean-label poisoning methods have been developed before, they have two main limitations. First, the methods developed with bi-level optimization or influence functions usually require second-order information, leading to substantial computational overhead. Second, the methods based on feature collision are not very transferable to unseen feature spaces or generalizable to various scenarios. To address these limitations, we propose a first-order efficient general-purpose clean-label poisoning attack in this paper. In our attack, we first identify the first-order model update that can push the model towards predicting the target data as the attack targeted label. We then formulate a necessary condition based on the model update and other first-order information to optimize the poisoning data. Theoretically, we prove that our first-order poisoning method is an approximation of a second-order approach with theoretically-guaranteed performance. Empirically, extensive evaluations on image classification and network traffic classification demonstrate the outstanding efficiency, transferability, and generalizability of our poisoning method.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488730"}, {"primary_key": "2187142", "vector": [], "sparse_vector": [], "title": "Online Joint Optimization on Traffic Engineering and Network Update in Software-defined WANs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>peng Dai", "<PERSON><PERSON><PERSON>"], "summary": "State-of-the-art inter-datacenter WANs rely on centralized traffic engineering (TE) to improve the network performance, where TE computation is a periodical procedure and timely performs routing configurations (i.e., enforces TE polices via add, remove and modify forwarding rules) in response to the changing network conditions. The TE computation determines the routing configurations corresponding to the current network conditions and the network update operations change the routing configurations from last TE to current TE solution. Existing works take centralized TE computation and network update as two individual optimization procedures, which inevitably leads to suboptimal solution in the long run. In this paper we initiate the study of online joint optimization on TE computation and network update with the objective of minimizing the sum of TE cost and network update cost. We formulate this problem as an optimization program and propose a set of provable online algorithms with rigorous competitive and regret analysis. Trace-driven simulations on two empirical topologies demonstrate that our algorithms can significantly decrease the total cost.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488837"}, {"primary_key": "2187143", "vector": [], "sparse_vector": [], "title": "P-FedAvg: Parallelizing Federated Learning with Theoretical Guarantees.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "With the growth of participating clients, the centralized parameter server (PS) will seriously limit the scale and efficiency of Federated Learning (FL). A straightforward approach to scale up the FL system is to construct a Parallel FL (PFL) system with multiple PSes. However, it is unclear whether PFL can really achieve a faster convergence rate or not. Even if the answer is yes, it is non-trivial to design a highly efficient parameter average algorithm for a PFL system. In this paper, we propose a completely parallelizable FL algorithm called P-FedAvg under the PFL architecture. P-FedAvg extends the well-known FedAvg algorithm by allowing multiple PSes to cooperate and train a learning model together. In P-FedAvg, each PS is only responsible for a fraction of total clients, but PSes can mix model parameters in a dedicatedly designed way so that the FL model can well converge. Different from heuristic-based algorithms, P-FedAvg is with theoretical guarantees. To be rigorous, we conduct theoretical analysis on the convergence rate of P-FedAvg, and derive the optimal weights for each PS to mix parameters with its neighbors. We also examine how the overlay topology formed by PSes affects the convergence rate and robustness of a PFL system. Lastly, we perform extensive experiments with real datasets to verify our analysis and demonstrate that P-FedAvg can significantly improve convergence rates than traditional FedAvg and other competitive baselines. We believe that our work can help to lay a theoretical foundation for building more efficient PFL systems.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488877"}, {"primary_key": "2187144", "vector": [], "sparse_vector": [], "title": "Primus: Fast and Robust Centralized Routing for Large-scale Data Center Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Tingting Xu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>wei Lu", "<PERSON>", "<PERSON><PERSON>", "Hongbo Jiang"], "summary": "This paper presents a fast and robust centralized data center network (DCN) routing solution called Primus. For fast routing calculation, Primus uses centralized controller to collect/disseminates the network's link-states (LS), and offload the actual routing calculation onto each switch. Observing that the routing changes can be classified into a few fixed patterns in DCNs which have regular topologies, we simplify each switch's routing calculation into a table-lookup manner, i.e., comparing LS changes with pre-installed base topology and updating routing paths according to predefined rules. As such, the routing calculation time at each switch only needs 10s of us even in a large network topology containing 10K+ switches. For efficient controller fault-tolerance, Primus purposely uses reporter switch to ensure the LS updates successfully delivered to all affected switches. As such, Primus can use multiple stateless controllers and little redundant traffic to tolerate failures, which incurs little overhead under normal case, and keeps 10s of ms fast routing reaction time even under complex data-/control-plane failures. We design, implement and evaluate Primus with extensive experiments on Linux-machine controllers and white-box switches. Primus provides ~1200x and ~100x shorter convergence time than current distributed protocol BGP and the state-of-the-art centralized routing solution, respectively.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488689"}, {"primary_key": "2187145", "vector": [], "sparse_vector": [], "title": "Camel: Context-Aware Magnetic MIMO Wireless Power Transfer with In-band Communication.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Haisheng Tan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Wireless power transfer (e.g., based on RF or magnetic) enables convenient device-charging, and triggers innovative applications that typically call for faster, smarter, economic, and even simultaneous adaptive charging for multiple smart-devices. Designing such a wireless charging system meeting these multi-requirements faces critical challenges, mainly including the better understanding of real-time energy receivers' status and the power-transferring channels, the limited capability and the smart coordination of the transmitters and receivers. In this work, we devise Camel, a context-aware MIMO MRC-WPT (magnetic resonant coupling-based wireless power transfer) system, which enables adaptive charging of multiple devices simultaneously with a novel context sensing scheme. In Camel, we craft an innovative MIMO WPT channels' state estimation and collision-aware in-band parallel communication among multiple transmitters and receivers. We design and implement the Camel prototype and conduct extensive experimental studies. The results validate our design and demonstrate that Camel can support simultaneous charging of as many as 10 devices, high-speed context sensing within 50 milliseconds, and efficient parallel communication among transceivers within proximity of ~0.5m.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488809"}, {"primary_key": "2187147", "vector": [], "sparse_vector": [], "title": "HAVS: Hardware-accelerated Shared-memory-based VPP Network Stack.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Haibing Guan"], "summary": "The number of requests to transfer large files is increasing rapidly in web server and remote-storage scenarios, and this increase requires a higher processing capacity from the network stack. However, to fully decouple from applications, many latest userspace network stacks, such as VPP (vector packet processing) and snap, adopt a shared-memory-based solution to communicate with upper applications. During this communication, the application or network stack needs to copy data to or from shared memory queues. In our verification experiment, these multiple copy operations incur more than 50% CPU consumption and severe performance degradation when the transferred file is larger than 32 KB. This paper adopts a hardware-accelerated solution and proposes HAVS which integrates Intel I/O Acceleration Technology into the VPP network stack to achieve high-performance memory copy offloading. An asynchronous copy architecture is introduced in HAVS to free up CPU resources. Moreover, an abstract memcpy accelerator layer is constructed in HAVS to ease the use of different types of hardware accelerators and sustain high availability with a fault-tolerance mechanism. The comprehensive evaluation shows that HAVS can provide an average 50%-60% throughput improvement over the original VPP stack when accelerating the nginx and SPDK iSCSI target application.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488808"}, {"primary_key": "2187149", "vector": [], "sparse_vector": [], "title": "Cocktail Edge Caching: Ride Dynamic Trends of Content Popularity with Ensemble Learning.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Guangyu Li", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Edge caching will play a critical role in facilitating the emerging content-rich applications. However, it faces many new challenges, in particular, the highly dynamic content popularity and the heterogeneous caching configurations. In this paper, we propose Cocktail Edge Caching, that tackles the dynamic popularity and heterogeneity through ensemble learning. Instead of trying to find a single dominating caching policy for all the caching scenarios, we employ an ensemble of constituent caching policies and adaptively select the best-performing policy to control the cache. Towards this goal, we first show through formal analysis and experiments that different variations of the LFU and LRU polices have complementary performance in different caching scenarios. We further develop a novel caching algorithm that enhances LFU/LRU with deep recurrent neural network (LSTM) based time-series analysis. Finally, we develop a deep reinforcement learning agent that adaptively combines base caching policies according to their virtual hit ratios on parallel virtual caches. Through extensive experiments driven by real content requests from two large video streaming platforms, we demonstrate that CEC not only consistently outperforms all single policies, but also improves the robustness of them. CEC can be well generalized to different caching scenarios with low computation overheads for deployment.", "published": "2021-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM42981.2021.9488910"}]