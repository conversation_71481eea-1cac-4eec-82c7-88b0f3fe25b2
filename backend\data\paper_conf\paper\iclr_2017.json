[{"primary_key": "3795865", "vector": [], "sparse_vector": [], "title": "A recurrent neural network without chaos.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We introduce an exceptionally simple  gated recurrent neural network (RNN)  that achieves performance comparable to well-known gated architectures, such as LSTMs and GRUs, on the word-level language modeling task. We prove that our model has simple, predicable and non-chaotic dynamics. This stands in stark contrast to more standard gated architectures, whose underlying dynamical systems exhibit chaotic behavior.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795866", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON> Efficient Actor-Critic with <PERSON>.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents an actor-critic deep reinforcement learning agent with experience replay that is stable, sample efficient, and performs remarkably well on challenging environments, including the discrete 57-game Atari domain and several continuous control problems. To achieve this, the paper introduces several innovations, including truncated importance sampling with bias correction, stochastic dueling network architectures, and a new trust region policy optimization method.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795867", "vector": [], "sparse_vector": [], "title": "On Robust Concepts and Small Neural Nets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The universal approximation theorem for neural networks says that any reasonable function is well-approximated by a two-layer neural network with sigmoid gates but it does not provide good bounds on the number of hidden-layer nodes or the weights. However, robust concepts often have small neural networks in practice. We show an efficient analog of the universal approximation theorem on the boolean hypercube in this context.\n\nWe prove that any noise-stable boolean function on n boolean-valued input variables can be well-approximated by a two-layer linear threshold circuit with a small number of hidden-layer nodes and small weights, that depend only on the noise-stability and approximation parameters, and are independent of n. We also give a polynomial time learning algorithm that outputs a small two-layer linear threshold circuit that approximates such a given function. We also show weaker generalizations of this to noise-stable polynomial threshold functions and noise-stable boolean functions in general.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795868", "vector": [], "sparse_vector": [], "title": "Quasi-Recurrent Neural Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recurrent neural networks are a powerful tool for modeling sequential data, but the dependence of each timestep’s computation on the previous timestep’s output limits parallelism and makes RNNs unwieldy for very long sequences. We introduce quasi-recurrent neural networks (QRNNs), an approach to neural sequence modeling that alternates convolutional layers, which apply in parallel across timesteps, and a minimalist recurrent pooling function that applies in parallel across channels. Despite lacking trainable recurrent layers, stacked QRNNs have better predictive accuracy than stacked LSTMs of the same hidden size. Due to their increased parallelism, they are up to 16 times faster at train and test time. Experiments on language modeling, sentiment classification, and character-level neural machine translation demonstrate these advantages and underline the viability of QRNNs as a basic building block for a variety of sequence tasks.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795869", "vector": [], "sparse_vector": [], "title": "A Compositional Object-Based Approach to Learning Physical Dynamics.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present the Neural Physics Engine (NPE), a framework for learning simulators of intuitive physics that naturally generalize across variable object count and different scene configurations. We propose a factorization of a physical scene into composable object-based representations and a neural network architecture whose compositional structure factorizes object dynamics into pairwise interactions. Like a symbolic physics engine, the NPE is endowed with generic notions of objects and their interactions; realized as a neural network, it can be trained via stochastic gradient descent to adapt to specific object properties and dynamics of different worlds. We evaluate the efficacy of our approach on simple rigid body dynamics in two-dimensional worlds. By comparing to less structured architectures, we show that the NPE's compositional representation of the structure in physical interactions improves its ability to predict movement, generalize across variable object count and different scene configurations, and infer latent properties of objects such as mass.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795870", "vector": [], "sparse_vector": [], "title": "Learning Invariant Feature Spaces to Transfer Skills with Reinforcement Learning.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "People can learn a wide range of tasks from their own experience, but can also learn from observing other creatures. This can accelerate acquisition of new skills even when the observed agent differs substantially from the learning agent in terms of morphology. In this paper, we examine how reinforcement learning algorithms can transfer knowledge between morphologically different agents (e.g., different robots). We introduce a problem formulation where twp agents are tasked with learning multiple skills by sharing information. Our method uses the skills that were learned by both agents to train invariant feature spaces that can then be used to transfer other skills from one agent to another. The process of learning these invariant feature spaces can be viewed as a kind of ``analogy making,'' or implicit learning of partial correspondences between two distinct domains. We evaluate our transfer learning algorithm in two simulated robotic manipulation skills, and illustrate that we can transfer knowledge between simulated robotic arms with different numbers of links, as well as simulated arms with different actuation mechanisms, where one robot is torque-driven while the other is tendon-driven.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795871", "vector": [], "sparse_vector": [], "title": "Unsupervised Feature Learning for Audio Analysis.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Identifying acoustic events from a continuously streaming audio source is of interest for many applications including environmental monitoring for basic research. In this scenario neither different event classes are known nor what distinguishes one class from another. Therefore, an unsupervised feature learning method for exploration of audio data is presented in this paper. It incorporates the two following novel contributions: First, an audio frame predictor based on a Convolutional LSTM autoencoder is demonstrated, which is used for unsupervised feature extraction. Second, a training method for autoencoders is presented, which leads to distinct features by amplifying event similarities. In comparison to standard approaches, the features extracted from the audio frame predictor trained with the novel approach show 13 % better results when used with a classifier and 36 % better results when used for clustering.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795872", "vector": [], "sparse_vector": [], "title": "Pruning Filters for Efficient ConvNets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The success of CNNs in various applications is accompanied by a significant increase in the computation and parameter storage costs. Recent efforts toward reducing these overheads involve pruning and compressing the weights of various layers without hurting original accuracy.  However, magnitude-based pruning of weights reduces a significant number of parameters from the fully connected layers and may not adequately reduce the computation costs in the convolutional layers due to irregular sparsity in the pruned networks. We present an acceleration method for CNNs, where we prune filters from CNNs that are identified as having a small effect on the output accuracy. By removing whole filters in the network together with their connecting feature maps, the computation costs are reduced significantly. In contrast to pruning weights, this approach does not result in sparse connectivity patterns. Hence, it does not need the support of sparse convolution libraries and can work with existing efficient BLAS libraries for dense matrix multiplications. We show that even simple filter pruning techniques can reduce inference costs for VGG-16 by up to 34% and ResNet-110 by up to 38% on CIFAR10 while regaining close to the original accuracy by retraining the networks.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795873", "vector": [], "sparse_vector": [], "title": "Variational Lossy Autoencoder.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Representation learning seeks to expose certain aspects of observed data in a learned representation that's amenable to downstream tasks like classification. \nFor instance, a good representation for 2D images might be one that describes only global structure and discards information about detailed texture. \nIn this paper, we present a simple but principled method to learn such global representations by combining Variational Autoencoder (VAE) with neural autoregressive models such as RNN, MADE and PixelRNN/CNN. \nOur proposed VAE model allows us to have control over what the global latent code can learn and , by designing the architecture accordingly, we can force the global latent code to discard irrelevant information such as texture in 2D images, and hence the code only ``autoencodes'' data in a lossy fashion.\nIn addition, by leveraging autoregressive models as both prior distribution $p(z)$ and decoding distribution $p(x|z)$, we can greatly improve generative modeling performance of VAEs, achieving new state-of-the-art results on MNIST, OMNIGLOT and Caltech-101 as well as competitive results on CIFAR10.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795874", "vector": [], "sparse_vector": [], "title": "Robustness to Adversarial Examples through an Ensemble of Specialists.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We are proposing to use an ensemble of diverse specialists, where speciality is defined according to the confusion matrix. Indeed, we observed that for adversarial instances originating from a given class, labeling tend to be done into a small subset of (incorrect) classes. Therefore, we argue that an ensemble of specialists should be better able to identify and reject fooling instances, with a high entropy (i.e., disagreement) over the decisions in the presence of adversaries. Experimental results obtained confirm that interpretation, opening a way to make the system more robust to adversarial examples through a rejection mechanism, rather than trying to classify them properly at any cost.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795875", "vector": [], "sparse_vector": [], "title": "Fine-grained Analysis of Sentence Embeddings Using Auxiliary Prediction Tasks.", "authors": ["<PERSON><PERSON>", "Einat Kermany", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "There is a lot of research interest in encoding variable length sentences into fixed\nlength vectors, in a way that preserves the sentence meanings. Two common\nmethods include representations based on averaging word vectors, and representations based on the hidden states of recurrent neural networks such as LSTMs.\nThe sentence vectors are used as features for subsequent machine learning tasks\nor for pre-training in the context of deep learning. However, not much is known\nabout the properties that are encoded in these sentence representations and about\nthe language information they capture.\nWe propose a framework that facilitates better understanding of the encoded representations. We define prediction tasks around isolated aspects of sentence structure (namely sentence length, word content, and word order), and score representations by the ability to train a classifier to solve each prediction task when\nusing the representation as input. We demonstrate the potential contribution of the\napproach by analyzing different sentence representation mechanisms. The analysis sheds light on the relative strengths of different sentence embedding methods with respect to these low level prediction tasks, and on the effect of the encoded\nvector’s dimensionality on the resulting representations.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795876", "vector": [], "sparse_vector": [], "title": "Pl@ntNet app in the era of deep learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Pl@ntNet is a large-scale participatory platform and information system dedicated to the production of botanical data through image-based plant identification. In June 2015, Pl@ntNet mobile front-ends moved from classical hand-crafted visual features to deep-learning based image representations. This paper gives an overview of today's Pl@ntNet architecture and discusses how the introduction of convolutional neural networks did improve the whole workflow along the years.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795877", "vector": [], "sparse_vector": [], "title": "Charged Point Normalization: An Efficient Solution to the Saddle Point Problem.", "authors": ["<PERSON><PERSON>"], "summary": "Recently, the problem of local minima in very high dimensional non-convex optimization has been challenged and the problem of saddle points has been introduced. This paper introduces a dynamic type of normalization that forces the system to escape saddle points. Unlike other saddle point escaping algorithms, second order information is not utilized, and the system can be trained with an arbitrary gradient descent learner. The system drastically improves learning in a range of deep neural networks on various data-sets in comparison to non-CPN neural networks.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795878", "vector": [], "sparse_vector": [], "title": "Understanding intermediate layers using linear classifier probes.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Neural network models have a reputation for being black boxes.\nWe propose a new method to better understand the roles and dynamics\nof the intermediate layers.\n\nOur method uses linear classifiers, referred to as \"probes\",\nwhere a probe can only use the hidden units of a given intermediate layer\nas discriminating features.\nMoreover, these probes cannot affect the training phase of a model,\nand they are generally added after training.\n\nWe demonstrate how this can be used to develop a better intuition\nabout models and to diagnose potential problems.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795879", "vector": [], "sparse_vector": [], "title": "Bit-Pragmatic Deep Neural Network Computing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We quantify a source of ineffectual computations when processing the multiplications of the convolutional layers in Deep Neural Networks (DNNs) and propose Pragmatic (PRA), an architecture that exploits it improving performance and energy efficiency. \nThe source of these ineffectual computations is best understood in the context of conventional multipliers which generate internally multiple terms, that is, products of the multiplicand and powers of two, which added together produce the final product. At runtime, many of these terms are zero as they are generated when the multiplicand is combined with the zero-bits of the multiplicator. While conventional bit-parallel multipliers calculate all terms in parallel to reduce individual product latency, Pragmatic calculates only the non-zero terms resulting in a design whose execution time for convolutional layers is ideally proportional to the number of activation bits that are 1. Measurements demonstrate that for the convolutional layers on Convolutional Neural Networks and during inference, Pragmatic improves performance by 4.3x over the DaDiaNao (DaDN) accelerator and by 4.5x when DaDN uses an 8-bit quantized representation. DaDiannao was reported to be 300x faster than commodity graphics processors.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795880", "vector": [], "sparse_vector": [], "title": "Deep Variational Information Bottleneck.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a variational approximation to the information bottleneck of <PERSON><PERSON><PERSON> et al. (1999). This variational approach allows us to parameterize the information bottleneck model using a neural network and leverage the reparameterization trick for efficient training. We call this method “Deep Variational Information Bottleneck”, or Deep VIB. We show that models trained with the VIB objective outperform those that are trained with other forms of regularization, in terms of generalization performance and robustness to adversarial attack.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795881", "vector": [], "sparse_vector": [], "title": "Tree-structured decoding with doubly-recurrent neural networks.", "authors": ["<PERSON>", "Tommi S. Jaakkola"], "summary": "We propose a neural network architecture for generating tree-structured objects from encoded representations. The core of the method is a doubly-recurrent neural network that models separately the width and depth recurrences across the tree, and combines them inside each cell to generate an output. The topology of the tree is explicitly modeled, allowing the network to predict both content and topology of the tree when decoding. That is, given only an encoded vector representation, the network is able to simultaneously generate a tree from it and predict labels for the nodes. We test this architecture in an encoder-decoder framework, where we train a network to encode a sentence as a vector, and then generate a tree structure from it. The experimental results show the effectiveness of this architecture at recovering latent tree structure in sequences and at mapping sentences to simple functional programs.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795882", "vector": [], "sparse_vector": [], "title": "The High-Dimensional Geometry of Binary Neural Networks.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Traditionally, researchers thought that high-precision weights were crucial for training neural networks with gradient descent. However, recent research has obtained a finer understanding of the role of precision in neural network weights. One can train a NN with binary weights and activations at train time by augmenting the weights with a high-precision continuous latent variable that accumulates small changes from stochastic gradient descent. However, there is a dearth of theoretical analysis to explain why we can effectively capture the features in our data with binary weights and activations. Our main result is that the neural networks with binary weights and activations trained using the Courbariaux, <PERSON><PERSON> et al. (2016) method work because of the high-dimensional geometry of binary vectors. In particular, the continuous vectors that extract out features in these BNNs are well-approximated by binary vectors in the sense that dot products are approximately preserved. Compared to previous research that demonstrated the viability of such BNNs, our work explains why these BNNs work in terms of the geometry of high-dimensional binary vectors. Our theory serves as a foundation for understanding not only BNNs but networks that make use of low precision weights and activations. Furthermore, a better understanding of multilayer binary neural networks serves as a starting point for generalizing BNNs to other neural network architectures such as recurrent neural networks.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795883", "vector": [], "sparse_vector": [], "title": "Sparsely-Connected Neural Networks: Towards Efficient VLSI Implementation of Deep Neural Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recently deep neural networks have received considerable attention due to their ability to extract and represent high-level abstractions in data sets. Deep neural networks such as fully-connected and convolutional neural networks have shown excellent performance on a wide range of recognition and classification tasks. However, their hardware implementations currently suffer from large silicon area and high power consumption due to the their high degree of complexity. The power/energy consumption of neural networks is dominated by memory accesses, the majority of which occur in fully-connected networks. In fact, they contain most of the deep neural network parameters. In this paper, we propose sparsely-connected networks, by showing that the number of connections in fully-connected networks can be reduced by up to 90% while improving the accuracy performance on three popular datasets (MNIST, CIFAR10 and SVHN). We then propose an efficient hardware architecture based on linear-feedback shift registers to reduce the memory requirements of the proposed sparsely-connected networks. The proposed architecture can save up to 90% of memory compared to the conventional implementations of fully-connected neural networks. Moreover, implementation results show up to 84% reduction in the energy consumption of a single neuron of the proposed sparsely-connected networks compared to a single neuron of fully-connected neural networks.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795884", "vector": [], "sparse_vector": [], "title": "Towards Principled Methods for Training Generative Adversarial Networks.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The goal of this paper is not to introduce a single algorithm or method, but to make theoretical steps towards fully understanding the training dynamics of gen- erative adversarial networks. In order to substantiate our theoretical analysis, we perform targeted experiments to verify our assumptions, illustrate our claims, and quantify the phenomena. This paper is divided into three sections. The first sec- tion introduces the problem at hand. The second section is dedicated to studying and proving rigorously the problems including instability and saturation that arize when training generative adversarial networks. The third section examines a prac- tical and theoretically grounded direction towards solving these problems, while introducing new tools to study them.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795885", "vector": [], "sparse_vector": [], "title": "Accelerating SGD for Distributed Deep-Learning Using an Approximted Hessian Matrix.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce a novel method to compute a rank $m$ approximation of the inverse of the Hessian matrix, in the distributed regime. By leveraging the differences in gradients and parameters of multiple Workers, we are able to efficiently implement a distributed approximation of the <PERSON><PERSON><PERSON> method. We also present preliminary results which underline advantages and challenges of second-order methods for large stochastic optimization problems. In particular, our work suggests that novel strategies for combining gradients will provide further information on the loss surface.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795886", "vector": [], "sparse_vector": [], "title": "A Simple but Tough-to-Beat <PERSON><PERSON> for Sentence Embeddings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The success of neural network methods for computing word embeddings has motivated methods for generating semantic embeddings of longer pieces of text, such as sentences and paragraphs. Surprisingly, <PERSON><PERSON><PERSON> et al (ICLR'16) showed that such complicated methods are outperformed, especially in out-of-domain (transfer learning) settings, by simpler methods involving mild retraining of word embeddings and basic linear regression. The  method of <PERSON><PERSON><PERSON> et al. requires retraining with a substantial labeled dataset such as Paraphrase Database (<PERSON><PERSON><PERSON><PERSON> et al., 2013). \n\nThe current paper goes further, showing that the following completely unsupervised sentence embedding is a formidable baseline: Use word embeddings computed using one of the popular methods on unlabeled corpus like Wikipedia, represent the sentence by a weighted average of the word vectors, and then modify them a bit using PCA/SVD. This weighting improves performance by about 10% to 30% in textual similarity tasks, and beats sophisticated supervised methods including RNN's and LSTM's. It even improves <PERSON><PERSON><PERSON> et al.'s embeddings. \n This simple method should be used as the baseline to beat in future, especially when labeled training data is scarce or nonexistent. \n\nThe paper also gives a theoretical explanation of the success of the above unsupervised method using a latent variable generative model for sentences, which is a simple extension of the model in <PERSON><PERSON><PERSON> et al. (TACL'16) with new \"smoothing\" terms that allow for \nwords occurring out of context, as well as high probabilities for words like and, not in all contexts.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795887", "vector": [], "sparse_vector": [], "title": "Distributed Second-Order Optimization using Kronecker-Factored Approximations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "As more computational resources become available, machine learning researchers train ever larger neural networks on millions of data points using stochastic gradient descent (SGD). Although SGD scales well in terms of both the size of dataset and the number of parameters of the model, it has rapidly diminishing returns as parallel computing resources increase.  Second-order optimization methods have an affinity for well-estimated gradients and large mini-batches, and can therefore benefit much more from parallel computation in principle.   Unfortunately,  they often employ severe approximations to the curvature matrix in order to scale to large models with millions of parameters, limiting their effectiveness in practice versus well-tuned SGD with momentum.  The recently proposed K-FAC method(<PERSON><PERSON> and <PERSON>, 2015) uses a stronger and more sophisticated curvature approximation, and has been shown to make much more per-iteration progress than SGD, while only introducing a modest overhead.  In this paper, we develop a version of K-FAC that distributes the computation of gradients and additional quantities required by K-FAC across multiple machines, thereby taking advantage of method’s superior scaling to large mini-batches and mitigating its additional overheads. We provide a Tensorflow implementation of our approach which is easy to use and can be applied to many existing codebases without modification.  Additionally, we develop several algorithmic enhancements to K-FAC which can improve its computational performance for very large models. Finally, we show that our distributed K-FAC method speeds up training of various state-of-the-art ImageNet classification models by a factor of two compared to <PERSON><PERSON> Normalization(<PERSON><PERSON><PERSON> and <PERSON>, 2015).", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795888", "vector": [], "sparse_vector": [], "title": "Reinforcement Learning through Asynchronous Advantage Actor-Critic on a GPU.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce a hybrid CPU/GPU version of the Asynchronous Advantage Actor-Critic (A3C) algorithm, currently the state-of-the-art method in reinforcement learning for various gaming tasks. We analyze its computational traits and concentrate on aspects critical to leveraging the GPU's computational power. We introduce a system of queues and a dynamic scheduling strategy, potentially helpful for other asynchronous algorithms as well. Our hybrid CPU/GPU version of A3C, based on TensorFlow, achieves a significant speed up compared to a CPU implementation; we make it publicly available to other researchers at https://github.com/NVlabs/GA3C.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795889", "vector": [], "sparse_vector": [], "title": "Learning Algorithms for Active Learning.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a model that learns active learning algorithms via metalearning. For each metatask, our model jointly learns: a data representation, an item selection heuristic, and a one-shot classifier. Our model uses the item selection heuristic to construct a labeled support set for the one-shot classifier. Using metatasks based on the Omniglot and MovieLens datasets, we show that our model performs well in synthetic and practical settings.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795890", "vector": [], "sparse_vector": [], "title": "An Actor-C<PERSON> for Sequence Prediction.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present an approach to training neural networks to generate sequences using actor-critic methods from reinforcement learning (RL). Current log-likelihood training methods are limited by the discrepancy between their training and testing modes, as models must generate tokens conditioned on their previous guesses rather than the ground-truth tokens. We address this problem by introducing a textit{critic} network that is trained to predict the value of an output token, given the policy of an textit{actor} network. This results in a training procedure that is much closer to the test phase, and allows us to directly optimize for a task-specific score such as BLEU. Crucially, since we leverage these techniques in the supervised learning setting rather than the traditional RL setting, we condition the critic network on the ground-truth output. We show that our method leads to improved performance on both a synthetic task, and for German-English machine translation. Our analysis paves the way for such methods to be applied in natural language generation tasks, such as machine translation, caption generation, and dialogue modelling.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795891", "vector": [], "sparse_vector": [], "title": "Embracing Data Abundance.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "There is a practically unlimited amount of natural language data available. Still, recent work in text comprehension has focused on datasets which are small relative to current computing possibilities. This article is making a case for the community to move to larger data and is offering the BookTest dataset as a step in that direction.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795892", "vector": [], "sparse_vector": [], "title": "Predicting Medications from Diagnostic Codes with Recurrent Neural Networks.", "authors": ["Jacek M<PERSON>", "<PERSON>"], "summary": "It is a surprising fact that electronic medical records are failing at one of their primary purposes, that of tracking the set of medications that the patient is actively taking. Studies estimate that up to 50% of such lists omit active drugs, and that up to 25% of all active medications do not appear on the appropriate patient list. Manual efforts to maintain these lists involve a great deal of tedious human labor, which could be reduced by computational tools to suggest likely missing or incorrect medications on a patient’s list. We report here an application of recurrent neural networks to predict the likely therapeutic classes of medications that a patient is taking, given a sequence of the last 100 billing codes in their record. Our best model was a GRU that achieved high prediction accuracy (micro-averaged AUC 0.93, Label Ranking Loss 0.076), limited by hardware constraints on model size. Additionally, examining individual cases revealed that many of the predictions marked incorrect were likely to be examples of either omitted medications or omitted billing codes, supporting our assertion of a substantial number of errors and omissions in the data, and the likelihood of models such as these to help correct them.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795893", "vector": [], "sparse_vector": [], "title": "Designing Neural Network Architectures using Reinforcement Learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "At present, designing convolutional neural network (CNN) architectures requires both human expertise and labor. New architectures are handcrafted by careful experimentation or modified from a handful of existing networks. We introduce MetaQNN, a meta-modeling algorithm based on reinforcement learning to automatically generate high-performing CNN architectures for a given learning task. The learning agent is trained to sequentially choose CNN layers using $Q$-learning with an $\\epsilon$-greedy exploration strategy and experience replay. The agent explores a large but finite space of possible architectures and iteratively discovers designs with improved performance on the learning task. On image classification benchmarks, the agent-designed networks (consisting of only standard convolution, pooling, and fully-connected layers) beat existing networks designed with the same layer types and are competitive against the state-of-the-art methods that use more complex layer types. We also outperform existing meta-modeling approaches for network design on image classification tasks.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795894", "vector": [], "sparse_vector": [], "title": "End-to-end Optimized Image Compression.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We describe an image compression method, consisting of a nonlinear analysis transformation, a uniform quantizer, and a nonlinear synthesis transformation. The transforms are constructed in three successive stages of convolutional linear filters and nonlinear activation functions. Unlike most convolutional neural networks, the joint nonlinearity is chosen to implement a form of local gain control, inspired by those used to model biological neurons. Using a variant of stochastic gradient descent, we jointly optimize the entire model for rate-distortion performance over a database of training images, introducing a continuous proxy for the discontinuous loss function arising from the quantizer. Under certain conditions, the relaxed loss function may be interpreted as the log likelihood of a generative model, as implemented by a variational autoencoder. Unlike these models, however, the compression model must operate at any given point along the rate-distortion curve, as specified by a trade-off parameter. Across an independent set of test images, we find that the optimized method generally exhibits better rate-distortion performance than the standard JPEG and JPEG 2000 compression methods. More importantly, we observe a dramatic improvement in visual quality for all images at all bit rates, which is supported by objective quality estimates using MS-SSIM.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795895", "vector": [], "sparse_vector": [], "title": "DeepCoder: Learning to Write Programs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We develop a first line of attack for solving programming competition-style problems from input-output examples using deep learning. The approach is to train a neural network to predict properties of the program that generated the outputs from the inputs. We use the neural network's predictions to augment search techniques from the programming languages community, including enumerative search and an SMT-based solver. Empirically, we show that our approach leads to an order of magnitude speedup over the strong non-augmented baselines and a Recurrent Neural Network approach, and that we are able to solve problems of difficulty comparable to the simplest problems on programming competition websites.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795896", "vector": [], "sparse_vector": [], "title": "Optimal Binary Autoencoding with Pairwise Correlations.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We formulate learning of a binary autoencoder as a biconvex optimization problem which learns from the pairwise correlations between encoded and decoded bits. Among all possible algorithms that use this information, ours finds the autoencoder that reconstructs its inputs with worst-case optimal loss. The optimal decoder is a single layer of artificial neurons, emerging entirely from the minimax loss minimization, and with weights learned by convex optimization. All this is reflected in competitive experimental results, demonstrating that binary autoencoding can be done efficiently by conveying information in pairwise correlations in an optimal fashion.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795897", "vector": [], "sparse_vector": [], "title": "CommAI: Evaluating the first steps towards a useful general AI.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With machine learning successfully applied to new daunting problems almost every day, general AI starts looking like an attainable goal. However, most current research focuses instead on important but narrow applications, such as image classification or machine translation. We believe this to be largely due to the lack of objective ways to measure progress towards broad machine intelligence. In order to fill this gap, we propose here a set of concrete desiderata for general AI, together with a platform to test machines on how well they satisfy such desiderata, while keeping all further complexities to a minimum.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795898", "vector": [], "sparse_vector": [], "title": "Fast Adaptation in Generative Models with Generative Matching Networks.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We develop a new class of deep generative model called generative matching networks (GMNs) which is inspired by the recently proposed matching networks for one-shot learning in discriminative tasks.\nBy conditioning on the additional input dataset, generative matching networks may instantly learn new concepts that were not available during the training but conform to a similar generative process, without explicit limitations on the number of additional input objects or the number of concepts they represent. \nOur experiments on the Omniglot dataset demonstrate that GMNs can significantly improve predictive performance on the fly as more additional data is available and generate examples of previously unseen handwritten characters once only a few images of them are provided.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795899", "vector": [], "sparse_vector": [], "title": "Efficient Representation of Low-Dimensional Manifolds using Deep Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We consider the ability of deep neural networks to represent data that lies near a low-dimensional manifold in a high-dimensional space.  We show that deep networks can efficiently extract the intrinsic, low-dimensional coordinates of such data.  Specifically we show that the first two layers of a deep network can exactly embed points lying on a monotonic chain, a special type of piecewise linear manifold, mapping them to a low-dimensional Euclidean space.  Remarkably, the network can do this using an almost optimal number of parameters. We also show that this network projects nearby points onto the manifold and then embeds them with little error. Experiments demonstrate that training with stochastic gradient descent can indeed find efficient representations similar to the one presented in this paper.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795900", "vector": [], "sparse_vector": [], "title": "Multilayer Recurrent Network Models of Primate Retinal Ganglion Cell Responses.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>"], "summary": "Developing accurate predictive models of sensory neurons is vital to understanding sensory processing and brain computations. The current standard approach to modeling neurons is to start with simple models and to incrementally add interpretable features. An alternative approach is to start with a more complex model that captures responses accurately, and then probe the fitted model structure to understand the neural computations. Here, we show that a multitask recurrent neural network (RNN) framework provides the flexibility necessary to model complex computations of neurons that cannot be captured by previous methods. Specifically, multilayer recurrent neural networks that share features across neurons outperform generalized linear models (GLMs) in predicting the spiking responses of parasol ganglion cells in the primate retina to natural images. The networks achieve good predictive performance given surprisingly small amounts of experimental training data. Additionally, we present a novel GLM-RNN hybrid model with separate spatial and temporal processing components which provides insights into the aspects of retinal processing better captured by the recurrent neural networks.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795901", "vector": [], "sparse_vector": [], "title": "Recurrent Mixture Density Network for Spatiotemporal Visual Attention.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In many computer vision tasks, the relevant information to solve the problem at hand is mixed to irrelevant, distracting information. This has motivated researchers to design attentional models that can dynamically focus on parts of images or videos that are salient, e.g., by down-weighting irrelevant pixels. In this work, we propose a spatiotemporal attentional model that learns where to look in a video directly from human fixation data. We model visual attention with a mixture of Gaussians at each frame. This distribution is used to express the probability of saliency for each pixel. Time consistency in videos is modeled hierarchically by: 1) deep 3D convolutional features to represent spatial and short-term time relations and 2) a long short-term memory network on top that aggregates the clip-level representation of sequential clips and therefore expands the temporal domain from few frames to seconds. The parameters of the proposed model are optimized via maximum likelihood estimation using human fixations as training data, without knowledge of the action in each video. Our experiments on Hollywood2 show state-of-the-art performance on saliency prediction for video. We also show that  our attentional model trained on Hollywood2 generalizes well to UCF101 and it can be leveraged to improve action classification accuracy on both datasets.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795902", "vector": [], "sparse_vector": [], "title": "Joint Embeddings of Scene Graphs and Images.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Multimodal representations of text and images have become popular in recent years. Text however has inherent ambiguities when describing visual scenes, leading to the recent development of datasets with detailed graphical descriptions in the form of scene graphs. We consider the task of joint representation of semantically precise scene graphs and images. We propose models for representing scene graphs and aligning them with images.  We investigate methods based on bag-of-words, subpath representations, as well as neural networks. Our investigation proposes and contrasts several models which can address this task and highlights some unique challenges in both designing models and evaluation.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795903", "vector": [], "sparse_vector": [], "title": "Learning to Discover Sparse Graphical Models.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider structure discovery of undirected graphical models from observational data. Inferring likely structures from few examples is a complex task often requiring the formulation of priors and sophisticated inference procedures. In the setting of Gaussian Graphical Models (GGMs) a popular estimator is a maximum likelihood objective with a penalization on the precision matrix. Adapting this estimator to capture domain-specific knowledge as priors or a new data likelihood requires great effort. In addition, structure recovery is an indirect consequence of the data-fit term. By contrast, it may be easier to generate training samples of data that arise from graphs with the desired structure properties. We propose here to leverage this latter source of information as training data to learn a function mapping from empirical covariance matrices to estimated graph structures.  Learning this function brings two benefits: it implicitly models the desired structure or sparsity properties to form suitable priors, and it can be tailored to the specific problem of edge structure discovery, rather than maximizing data likelihood. We apply this framework to several real-world problems in structure discovery and show that it can be competitive to standard approaches such as graphical lasso, at a fraction of the execution speed. We use convolutional neural networks to parametrize our estimators due to the compositional structure of the problem. Experimentally, our learnable graph-discovery method trained on synthetic data generalizes well to different data: identifying relevant edges in real data, completely unknown at training time. We find that on genetics, brain imaging, and simulation data we obtain competitive(and generally superior) performance, compared with analytical methods.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795904", "vector": [], "sparse_vector": [], "title": "Neural Combinatorial Optimization with Reinforcement Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>uo<PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present a framework to tackle combinatorial optimization problems using neural networks and reinforcement learning. We focus on the traveling salesman problem (TSP) and train a recurrent neural network that, given a set of city \\mbox{coordinates}, predicts a distribution over different city permutations. Using negative tour length as the reward signal, we optimize the parameters of the recurrent neural network using a policy gradient method. Without much engineering and heuristic designing, Neural Combinatorial Optimization achieves close to optimal results on 2D Euclidean graphs with up to $100$ nodes. These results, albeit still quite far from state-of-the-art, give insights into how neural networks can be used as a general tool for tackling combinatorial optimization problems.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795905", "vector": [], "sparse_vector": [], "title": "Incorporating long-range consistency in CNN-based texture generation.", "authors": ["<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON> et al. (2015) showed that pair-wise products of features in a convolutional network are a very effective representation of image textures. We propose a simple modification to that representation which makes it possible to incorporate long-range structure into image generation, and to render images that satisfy various symmetry constraints. We show how this can greatly improve rendering of regular textures and of images that contain other kinds of symmetric structure. We also present applications to inpainting and season transfer.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795906", "vector": [], "sparse_vector": [], "title": "Trusting SVM for Piecewise Linear CNNs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present a novel layerwise optimization algorithm for the learning objective of Piecewise-Linear Convolutional Neural Networks (PL-CNNs), a large class of convolutional neural networks. Specifically, PL-CNNs employ piecewise linear non-linearities such as the commonly used ReLU and max-pool, and an SVM classifier as the final layer. The key observation of our approach is that the prob- lem corresponding to the parameter estimation of a layer can be formulated as a difference-of-convex (DC) program, which happens to be a latent structured SVM. We optimize the DC program using the concave-convex procedure, which requires us to iteratively solve a structured SVM problem. This allows to design an opti- mization algorithm with an optimal learning rate that does not require any tuning. Using the MNIST, CIFAR and ImageNet data sets, we show that our approach always improves over the state of the art variants of backpropagation and scales to large data and large network settings.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795907", "vector": [], "sparse_vector": [], "title": "Playing SNES in the Retro Learning Environment.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Mastering a video game requires skill, tactics and strategy. While these attributes may be acquired naturally by human players, teaching them to a computer program is a far more challenging task. In recent years, extensive research was carried out in the field of reinforcement learning and numerous algorithms were introduced, aiming to learn how to perform human tasks such as playing video games. As a result, the Arcade Learning Environment (ALE) has become a commonly used benchmark environment allowing algorithms to trainon various Atari 2600 games.  In many games the state-of-the-art algorithms out-perform  humans.   In  this  paper  we  introduce  a  new  learning  environment,  the Retro Learning Environment — RLE, that can run games on the Super Nintendo Entertainment System (SNES), Sega Genesis and several other gaming consoles.The environment is expandable, allowing for more video games and consoles to be easily added to the environment, while maintaining a simple unified interface. Moreover, RLE is compatible with Python and Torch. SNES games pose a significant challenge to current algorithms due to their higher level of complexity and versatility. A more extensive paper describing our work is available on arXiv", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795908", "vector": [], "sparse_vector": [], "title": "Program Synthesis for Character Level Language Modeling.", "authors": ["Pavol Bielik", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose a statistical model applicable to character level language modeling and show that it is a good fit for both, program source code and English text. The model is parameterized by a program from a domain-specific language (DSL) that allows expressing non-trivial data dependencies. Learning is done in two phases: (i) we synthesize a program from the DSL, essentially learning a good representation for the data, and (ii) we learn parameters from the training data - the process is done via counting, as in simple language models such as n-gram.\n\nOur experiments show that the precision of our model is comparable to that of neural networks while sharing a number of advantages with n-gram models such as fast query time and the capability to quickly add and remove training data samples. Further, the model is parameterized by a program that can be manually inspected, understood and updated, addressing a major problem of neural networks.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795909", "vector": [], "sparse_vector": [], "title": "Automated Generation of Multilingual Clusters for the Evaluation of Distributed Representations.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We propose a language-agnostic way of automatically generating sets of semantically similar clusters of entities along with sets of \"outlier\" elements, which may then be used to perform an intrinsic evaluation of word embeddings in the outlier detection task. We used our methodology to create a gold-standard dataset, which we call WikiSem500, and evaluated multiple state-of-the-art embeddings. The results show a correlation between performance on this dataset and performance on sentiment analysis.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795910", "vector": [], "sparse_vector": [], "title": "Learning End-to-End Goal-Oriented Dialog.", "authors": ["<PERSON>", "Y-<PERSON><PERSON>", "<PERSON>"], "summary": "Traditional dialog systems used in goal-oriented applications require a lot of domain-specific handcrafting, which hinders scaling up to new domains. End- to-end dialog systems, in which all components are trained from the dialogs themselves, escape this limitation. But the encouraging success recently obtained in chit-chat dialog may not carry over to goal-oriented settings. This paper proposes a testbed to break down the strengths and shortcomings of end-to-end dialog systems in goal-oriented applications. Set in the context of restaurant reservation, our tasks require manipulating sentences and symbols, so as to properly conduct conversations, issue API calls and use the outputs of such calls. We show that an end-to-end dialog system based on Memory Networks can reach promising, yet imperfect, performance and learn to perform non-trivial operations. We confirm those results by comparing our system to a hand-crafted slot-filling baseline on data from the second Dialog State Tracking Challenge (<PERSON> et al., 2014a). We show similar result patterns on data extracted from an online concierge service.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795911", "vector": [], "sparse_vector": [], "title": "Learning to Generate Samples from Noise through Infusion Training.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this work, we investigate a novel training procedure to learn a generative model as the transition operator of a Markov chain, such that, when applied repeatedly on an unstructured random noise sample, it will denoise it into a sample that matches the target distribution from the training set. The novel training procedure to learn this progressive denoising operation involves sampling from a slightly different chain than the model chain used for generation in the absence of a denoising target. In the training chain we infuse information from the training target example that we would like the chains to reach with a high probability. The thus learned transition operator is able to produce quality and varied samples in a small number of steps. Experiments show competitive results compared to the samples generated with a basic Generative Adversarial Net.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795912", "vector": [], "sparse_vector": [], "title": "Programming With a Differentiable Forth Interpreter.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "There are families of neural networks that can learn to compute any function, provided sufficient training data. However, given that in practice training data is scarce for all but a small set of problems, a core question is how to incorporate prior knowledge into a model. Here we consider the case of prior procedural knowledge, such as knowing the overall recursive structure of a sequence transduction program or the fact that a program will likely use arithmetic operations on real numbers to solve a task. To this end we present a differentiable interpreter for the programming language Forth. Through a neural implementation of the dual stack machine that underlies Forth, programmers can write program sketches with slots that can be filled with behaviour trained from program input-output data. As the program interpreter is end-to-end differentiable, we can optimize this behaviour directly through gradient descent techniques on user specified objectives, and also integrate the program into any larger neural computation graph. We show empirically that our interpreter is able to effectively leverage different levels of prior program structure and learn complex transduction tasks such as sequence sorting or addition with substantially less data and better generalisation over problem sizes. In addition, we introduce neural program optimisations based on symbolic computation and parallel branching that lead to significant speed improvements.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795913", "vector": [], "sparse_vector": [], "title": "Neural Photo Editing with Introspective Adversarial Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The increasingly photorealistic sample quality of generative image models suggests their feasibility in applications beyond image generation. We present the Neural Photo Editor, an interface that leverages the power of generative neural networks to make large, semantically coherent changes to existing images. To tackle the challenge of achieving accurate reconstructions without loss of feature quality, we introduce the Introspective Adversarial Network,  \na novel hybridization of the VAE and GAN. Our model efficiently captures long-range dependencies through use of a computational block based on weight-shared dilated convolutions, and improves generalization performance with Orthogonal Regularization, a novel weight regularization method. We validate our contributions on CelebA, SVHN, and CIFAR-100, and produce samples and reconstructions with high visual fidelity.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795914", "vector": [], "sparse_vector": [], "title": "Learning to superoptimize programs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Code super-optimization is the task of transforming any given program to a more efficient version while preserving its input-output behaviour. In some sense, it is similar to the paraphrase problem from natural language processing where the intention is to change the syntax of an utterance without changing its semantics. Code-optimization has been the subject of years of research that has resulted in the development of rule-based transformation strategies that are used by compilers. More recently, however, a class of stochastic search based methods have been shown to outperform these strategies. This approach involves repeated sampling of modifications to the program from a proposal distribution, which are accepted or rejected based on whether they preserve correctness, and the improvement they achieve. These methods, however, neither learn from past behaviour nor do they try to leverage the semantics of the program under consideration. Motivated by this observation, we present a novel learning based approach for code super-optimization. Intuitively, our method works by learning the proposal distribution using unbiased estimators of the gradient of the expected improvement. Experiments on benchmarks comprising of automatically generated as well as existing (``Hacker's Delight'') programs show that the proposed method is able to significantly outperform state of the art approaches for code super-optimization.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795915", "vector": [], "sparse_vector": [], "title": "Making Neural Programming Architectures Generalize via Recursion.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Empirically, neural networks that attempt to learn programs from data have exhibited poor generalizability. Moreover, it has traditionally been difficult to reason about the behavior of these models beyond a certain level of input complexity. In order to address these issues, we propose augmenting neural architectures with a key abstraction: recursion. As an application, we implement recursion in the Neural Programmer-Interpreter framework on four tasks: grade-school addition, bubble sort, topological sort, and quicksort. We demonstrate superior generalizability and interpretability with small amounts of training data. Recursion divides the problem into smaller pieces and drastically reduces the domain of each neural network component, making it tractable to prove guarantees about the overall system’s behavior. Our experience suggests that in order for neural architectures to robustly learn program semantics, it is necessary to incorporate a concept like recursion.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795916", "vector": [], "sparse_vector": [], "title": "The Preimage of Rectifier Network Activities.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We give a procedure for explicitely computing the complete preimage of activities of a layer in a rectifier network with fully connected layers, from knowledge of the weights in the network. The most general characterization of preimages is as piecewise linear manifolds in the input space with possibly multiple branches. This work therefore complements previous demonstrations of preimages obtained by heuristic optimization and regularization algorithms <PERSON><PERSON><PERSON> (2015; 2016) We are presently empirically evaluating the procedure and it’s ability to extract complete preimages as well as the general structure of preimage manifolds.\n\n\nICLR 2017 CONFRENCE TRACK SUBMISSION: https://openreview.net/forum?id=HJcLcw9xg&noteId=HJcLcw9xg", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795917", "vector": [], "sparse_vector": [], "title": "Latent Sequence Decompositions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>uo<PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Sequence-to-sequence models rely on a fixed decomposition of the target sequences into a sequence of tokens that may be words, word-pieces or characters. The choice of these tokens and the decomposition of the target sequences into a sequence of tokens is often static, and independent of the input, output data domains. This can potentially lead to a sub-optimal choice of token dictionaries, as the decomposition is not informed by the particular problem being solved. In this paper we present Latent Sequence Decompositions (LSD), a framework in which the decomposition of sequences into constituent tokens is learnt during the training of the model. The decomposition depends both on the input sequence and on the output sequence. In LSD, during training, the model samples decompositions incrementally, from left to right by locally sampling between valid extensions. We experiment with the Wall Street Journal speech recognition task. Our LSD model achieves 12.9% WER compared to a character baseline of 14.8% WER. When combined with a convolutional network on the encoder, we achieve a WER of 9.6%.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795918", "vector": [], "sparse_vector": [], "title": "Entropy-SGD: Biasing Gradient Descent Into Wide Valleys.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper proposes a new optimization algorithm called Entropy-SGD for training deep neural networks that is motivated by the local geometry of the energy landscape. Local extrema with low generalization error have a large proportion of almost-zero eigenvalues in the Hessian with very few positive or negative eigenvalues. We leverage upon this observation to construct a local-entropy-based objective function that favors well-generalizable solutions lying in large flat regions of the energy landscape, while avoiding poorly-generalizable solutions located in the sharp valleys. Conceptually, our algorithm resembles two nested loops of SGD where we use Langevin dynamics in the inner loop to compute the gradient of the local entropy before each update of the weights. We show that the new objective has a smoother energy landscape and show improved generalization over SGD using uniform stability, under certain assumptions. Our experiments on convolutional and recurrent neural networks demonstrate that Entropy-SGD compares favorably to state-of-the-art techniques in terms of generalization error and training time.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795919", "vector": [], "sparse_vector": [], "title": "Mode Regularized Generative Adversarial Networks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Although Generative Adversarial Networks achieve state-of-the-art results on a\nvariety of generative tasks, they are regarded as highly unstable and prone to miss\nmodes. We argue that these bad behaviors of GANs are due to the very particular\nfunctional shape of the trained discriminators in high dimensional spaces, which\ncan easily make training stuck or push probability mass in the wrong direction,\ntowards that of higher concentration than that of the data generating distribution.\nWe introduce several ways of regularizing the objective, which can dramatically\nstabilize the training of GAN models. We also show that our regularizers can help\nthe fair distribution of probability mass across the modes of the data generating\ndistribution during the early phases of training, thus providing a unified solution\nto the missing modes problem.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795920", "vector": [], "sparse_vector": [], "title": "Efficient Vector Representation for Documents through Corruption.", "authors": ["<PERSON><PERSON>"], "summary": "We present an efficient document representation learning framework, Document Vector through Corruption (Doc2VecC). Doc2VecC represents each document as a simple average of word embeddings. It ensures a representation generated as such captures the semantic meanings of the document during learning. A corruption model is included, which introduces a data-dependent regularization that favors informative or rare words while forcing the embeddings of common and non-discriminative ones to be close to zero. Doc2VecC produces significantly better word embeddings than Word2Vec. We compare Doc2VecC with several state-of-the-art document representation learning algorithms. The simple model architecture introduced by Doc2VecC matches or out-performs the state-of-the-art in generating high-quality document representations for sentiment analysis, document classification as well as semantic relatedness tasks. The simplicity of the model enables training on billions of words per hour on a single machine. At the same time, the model is very efficient in generating representations of unseen documents at test time.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795921", "vector": [], "sparse_vector": [], "title": "De novo drug design with deep generative models : an empirical study.", "authors": ["<PERSON><PERSON>", "Balázs <PERSON>", "<PERSON><PERSON>"], "summary": "We present an empirical study about the usage of RNN generative models for stochastic optimization in the context of de novo drug design. We study different kinds of architectures and we find models that can generate molecules with higher values than ones seen in the training set. Our results suggest that we can improve traditional stochastic optimizers, that rely on random perturbations or random sampling by using generative models trained on unlabeled data, to perform knowledge-driven optimization.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795922", "vector": [], "sparse_vector": [], "title": "Out-of-class novelty generation: an experimental foundation.", "authors": ["<PERSON><PERSON>", "Balázs <PERSON>", "<PERSON><PERSON>"], "summary": "Recent advances in machine learning have brought the field closer to computational creativity research. From a creativity research point of view, this offers the potential to study creativity in relationship with knowledge acquisition. From a machine learning perspective, however, several aspects of creativity need to be better defined to allow the machine learning community to develop and test hypotheses in a systematic way. We propose an actionable definition of creativity as the generation of out-of-distribution novelty. We assess several  metrics designed for evaluating the quality of generative models on this new task. We also propose a new experimental setup. Inspired by the usual held-out validation, we hold out entire classes for evaluating the generative potential of models. The goal of the novelty generator is then to use training classes to build a model that can generate objects from future (hold-out) classes, unknown at training time - and thus, are novel with respect to the knowledge the model incorporates. Through extensive experiments on various types of generative models, we are able to find architectures and hyperparameter combinations which lead to out-of-distribution novelty.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795923", "vector": [], "sparse_vector": [], "title": "Emergence of foveal image sampling from learning to attend in visual scenes.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We describe a neural attention model with a learnable retinal sampling lattice. The model is trained on a visual search task requiring the classification of an object embedded in a visual scene amidst background distractors using the smallest number of fixations. We explore the tiling properties that emerge in the model's retinal sampling lattice after training. Specifically, we show that this lattice resembles the eccentricity dependent sampling lattice of the primate retina, with a high resolution region in the fovea surrounded by a low resolution periphery. Furthermore, we find conditions where these emergent properties are amplified or eliminated providing clues to their function.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795924", "vector": [], "sparse_vector": [], "title": "Recurrent Environment Simulators.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Models that can simulate how environments change in response to actions can be used by agents to plan and act efficiently. We improve on previous environment simulators from high-dimensional pixel observations by introducing recurrent neural networks that are able to make temporally and spatially coherent predictions for hundreds of time-steps into the future. We present an in-depth analysis of the factors affecting performance, providing the most extensive attempt to advance the understanding of the properties of these models. We address the issue of computationally inefficiency with a model that does not need to generate a high-dimensional image at each time-step. We show that our approach can be used to improve exploration and is adaptable to many diverse environments, namely 10 Atari games, a 3D car racing environment, and complex 3D mazes.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795925", "vector": [], "sparse_vector": [], "title": "Semantic embeddings for program behaviour patterns.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we propose a new feature extraction technique for program execution logs. First, we automatically extract complex patterns from a program's behaviour graph. Then, we embed these patterns into a continuous space by training an autoencoder. We evaluate the proposed features on a real-world malicious software detection task. We also find that the embedding space captures interpretable structures in the space of pattern parts.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795926", "vector": [], "sparse_vector": [], "title": "Towards the Limit of Network Quantization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Network quantization is one of network compression techniques to reduce the redundancy of deep neural networks. It reduces the number of distinct network parameter values by quantization in order to save the storage for them. In this paper, we design network quantization schemes that minimize the performance loss due to quantization given a compression ratio constraint. We analyze the quantitative relation of quantization errors to the neural network loss function and identify that the Hessian-weighted distortion measure is locally the right objective function for the optimization of network quantization. As a result, Hessian-weighted k-means clustering is proposed for clustering network parameters to quantize. When optimal variable-length binary codes, e.g., Hu<PERSON>man codes, are employed for further compression, we derive that the network quantization problem can be related to the entropy-constrained scalar quantization (ECSQ) problem in information theory and consequently propose two solutions of ECSQ for network quantization, i.e., uniform quantization and an iterative solution similar to <PERSON>'s algorithm. Finally, using the simple uniform quantization followed by <PERSON><PERSON><PERSON> coding, we show from our experiments that the compression ratios of 51.25, 22.17 and 40.65 are achievable for LeNet, 32-layer ResNet and AlexNet, respectively.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795927", "vector": [], "sparse_vector": [], "title": "Song From PI: A Musically Plausible Network for Pop Music Generation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a novel framework for generating pop music. Our model is a hierarchical Recurrent Neural Network, where the layers and the structure of the hierarchy encode our prior knowledge about how pop music is composed. In particular, the bottom layers generate the melody, while the higher levels produce the drums and chords. We conduct several human studies that show strong preference of our generated music over that produced by the recent method by Google. We additionally show two applications of our framework: neural dancing and karaoke, as well as neural story singing.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795928", "vector": [], "sparse_vector": [], "title": "Hierarchical Multiscale Recurrent Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Learning both hierarchical and temporal representation has been among the long- standing challenges of recurrent neural networks. Multiscale recurrent neural networks have been considered as a promising approach to resolve this issue, yet there has been a lack of empirical evidence showing that this type of models can actually capture the temporal dependencies by discovering the latent hierarchical structure of the sequence. In this paper, we propose a novel multiscale approach, called the hierarchical multiscale recurrent neural network, that can capture the latent hierarchical structure in the sequence by encoding the temporal dependencies with different timescales using a novel update mechanism. We show some evidence that the proposed model can discover underlying hierarchical structure in the sequences without using explicit boundary information. We evaluate our proposed model on character-level language modelling and handwriting sequence generation.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795929", "vector": [], "sparse_vector": [], "title": "Inductive Bias of Deep Convolutional Networks through Pooling Geometry.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Our formal understanding of the inductive bias that drives the success of convolutional networks on computer vision tasks is limited. In particular, it is unclear what makes hypotheses spaces born from convolution and pooling operations so suitable for natural images. In this paper we study the ability of convolutional networks to model correlations among regions of their input. We theoretically analyze convolutional arithmetic circuits, and empirically validate our findings on other types of convolutional networks as well. Correlations are formalized through the notion of separation rank, which for a given partition of the input, measures how far a function is from being separable. We show that a polynomially sized deep network supports exponentially high separation ranks for certain input partitions, while being limited to polynomial separation ranks for others. The network's pooling geometry effectively determines which input partitions are favored, thus serves as a means for controlling the inductive bias. Contiguous pooling windows as commonly employed in practice favor interleaved partitions over coarse ones, orienting the inductive bias towards the statistics of natural images. Other pooling schemes lead to different preferences, and this allows tailoring the network to data that departs from the usual domain of natural imagery. In addition to analyzing deep networks, we show that shallow ones support only linear separation ranks, and by this gain insight into the benefit of functions brought forth by depth - they are able to efficiently model strong correlation under favored partitions of the input.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795930", "vector": [], "sparse_vector": [], "title": "Steerable CNNs.", "authors": ["Taco S. Cohen", "<PERSON>"], "summary": "It has long been recognized that the invariance and equivariance properties of a representation are critically important for success in many vision tasks. In this paper we present Steerable Convolutional Neural Networks, an efficient and flexible class of equivariant convolutional networks. We show that steerable CNNs achieve state of the art results on the CIFAR image classification benchmark. The mathematical theory of steerable representations reveals a type system in which any steerable representation is a composition of elementary feature types, each one associated with a particular kind of symmetry. We show how the parameter cost of a steerable filter bank depends on the types of the input and output features, and show how to use this knowledge to construct CNNs that utilize parameters effectively.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795931", "vector": [], "sparse_vector": [], "title": "Capacity and Trainability in Recurrent Neural Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Two potential bottlenecks on the expressiveness of recurrent neural networks (RNNs) are their ability to store information about the task in their parameters, and to store information about the input history in their units. We show experimentally that all common RNN architectures achieve nearly the same per-task and per-unit capacity bounds with careful training, for a variety of tasks and stacking depths. They can store an amount of task information which is linear in the number of parameters, and is approximately 5 bits per parameter. They can additionally store approximately one real number from their input history per hidden unit. We further find that for several tasks it is the per-task parameter capacity bound that determines performance. These results suggest that many previous results comparing RNN architectures are driven primarily by differences in training effectiveness, rather than differences in capacity. Supporting this observation, we compare training difficulty for several architectures, and show that vanilla RNNs are far more difficult to train, yet have slightly higher capacity. Finally, we propose two novel RNN architectures, one of which is easier to train than the LSTM or GRU for deeply stacked architectures.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795932", "vector": [], "sparse_vector": [], "title": "Recurrent Batch Normalization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>ülçeh<PERSON>", "<PERSON>"], "summary": "We propose a reparameterization of LSTM that brings the benefits of batch normalization to recurrent neural networks. Whereas previous works only apply batch normalization to the input-to-hidden transformation of RNNs, we demonstrate that it is both possible and beneficial to batch-normalize the hidden-to-hidden transition, thereby reducing internal covariate shift between time steps.\n\nWe evaluate our proposal on various sequential problems such as sequence classification, language modeling and question answering. Our empirical results show that our batch-normalized LSTM consistently leads to faster convergence and improved generalization.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795933", "vector": [], "sparse_vector": [], "title": "Reinterpreting Importance-Weighted Autoencoders.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The standard interpretation of importance-weighted autoencoders is that they maximize a tighter lower bound on the marginal likelihood. We give an alternate interpretation of this procedure: that it optimizes the standard variational lower bound, but using a more complex distribution.  We formally derive this result, and visualize the implicit importance-weighted approximate posterior.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795934", "vector": [], "sparse_vector": [], "title": "Calibrating Energy-based Generative Adversarial Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we propose to equip Generative Adversarial Networks with the ability to produce direct energy estimates for samples.\nSpecifically, we propose a flexible adversarial training framework, and prove this framework not only ensures the generator converges to the true data distribution, but also enables the discriminator to retain the density information at the global optimal.\nWe derive the analytic form of the induced solution, and analyze the properties.\nIn order to make the proposed framework trainable in practice, we introduce two effective approximation techniques.\nEmpirically, the experiment results closely match our theoretical analysis, verifying the discriminator is able to recover the energy of data distribution.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795935", "vector": [], "sparse_vector": [], "title": "Recurrent Hidden Semi-Markov Model.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Segmentation and labeling of high dimensional time series data has wide applications in behavior understanding and medical diagnosis. Due to the difficulty in obtaining the label information for high dimensional data, realizing this objective in an unsupervised way is highly desirable. Hidden Semi-Markov Model (HSMM) is a classical tool for this problem. However, existing HSMM and its variants has simple conditional assumptions of observations, thus the ability to capture the nonlinear and complex dynamics within segments is limited. To tackle this limitation, we propose to incorporate the Recurrent Neural Network (RNN) to model the generative process in HSMM, resulting the Recurrent HSMM (R-HSMM). To accelerate the inference while preserving accuracy, we designed a structure encoding function to mimic the exact inference. By generalizing the penalty method to distribution space, we are able to train the model and the encoding function simultaneously. Empirical results show that the proposed R-HSMM achieves the state-of-the-art performances on both synthetic and real-world datasets.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795936", "vector": [], "sparse_vector": [], "title": "Short and Deep: Sketching and Neural Networks.", "authors": ["<PERSON><PERSON>", "Nevena Lazic", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Data-independent methods for dimensionality reduction such as random projections, sketches, and feature hashing have become increasingly popular in recent years.  These methods often seek to reduce dimensionality while preserving the hypothesis class, resulting in inherent lower bounds on the size of projected data.  For example, preserving linear separability requires $\\Omega(1/\\gamma^2)$ dimensions, where $\\gamma$ is the margin, and in the case of polynomial functions, the number of required dimensions has an exponential dependence on the polynomial degree.\n                                                                                                                                                            \nDespite these limitations, we show that the dimensionality can be reduced further while maintaining performance guarantees, using improper learning with a slightly larger hypothesis class. In particular, we show that any sparse polynomial function of a sparse binary vector can be computed from a compact sketch by a single-layer neural network, where the sketch size has a logarithmic dependence on the polynomial degree.\n                                                                                                                                                           \nA practical consequence is that networks trained on sketched data are compact, and therefore suitable for settings with memory and power constraints. We empirically show that our approach leads to networks with fewer parameters than related methods such as feature hashing, at equal or better performance.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795937", "vector": [], "sparse_vector": [], "title": "Frustratingly Short Attention Spans in Neural Language Modeling.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Current language modeling architectures often use recurrent neural networks. Recently, various methods for incorporating differentiable memory into these architectures have been proposed. When predicting the next token, these models query information from a memory of the recent history and thus can facilitate learning mid- and long-range dependencies. However, conventional attention models produce a single output vector per time step that is used for predicting the next token as well as the key and value of a differentiable memory of the history of tokens. In this paper, we propose a key-value attention mechanism that produces separate representations for the key and value of a memory, and for a representation that encodes the next-word distribution. This usage of past memories outperforms existing memory-augmented neural language models on two corpora. Yet, we found that it mainly utilizes past memory only of the previous five representations. This led to the unexpected main finding that a much simpler model which simply uses a concatenation of output representations from the previous three-time steps is on par with more sophisticated memory-augmented neural language models.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795938", "vector": [], "sparse_vector": [], "title": "Dataset Augmentation in Feature Space.", "authors": ["<PERSON>nce <PERSON>", "<PERSON>"], "summary": "Dataset augmentation, the practice of applying a wide array of domain-specific transformations to synthetically expand a training set, is a standard tool in supervised learning. While effective in tasks such as visual recognition, the set of transformations must be carefully designed, implemented, and tested for every new domain, limiting its re-use and generality. In this paper, we adopt a simpler, domain-agnostic approach to dataset augmentation. We start with existing data points and apply simple transformations such as adding noise, interpolating, or extrapolating between them. Our main insight is to perform the transformation not in input space, but in a learned feature space. A re-kindling of interest in unsupervised representation learning makes this technique timely and more effective. It is a simple proposal, but to-date one that has not been tested empirically. Working in the space of context vectors generated by sequence-to-sequence models, we demonstrate a technique that is effective for both static and sequential data.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795939", "vector": [], "sparse_vector": [], "title": "A Differentiable Physics Engine for Deep Learning in Robotics.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "One of the most important fields in robotics is the optimization of controllers. Currently, robots are often treated as a black box in this optimization process, which is the reason why derivative-free optimization methods such as evolutionary algorithms or reinforcement learning are omnipresent. When gradient-based methods are used, models are kept small or rely on finite difference approximations for the Jacobian. This method quickly grows expensive with increasing numbers of parameters, such as found in deep learning. We propose an implementation of a modern physics engine, which can differentiate control parameters. This engine is implemented for both CPU and GPU. Firstly, this paper shows how such an engine speeds up the optimization process, even for small problems. Furthermore, it explains why this is an alternative approach to deep Q-learning, for using deep learning in robotics. Finally, we argue that this is a big step for deep learning in robotics, as it opens up new possibilities to optimize robots, both in hardware and software.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795940", "vector": [], "sparse_vector": [], "title": "Learning to Perform Physics Experiments via Deep Reinforcement Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "When encountering novel objects, humans are able to infer a wide range of physical properties such as mass, friction and deformability by interacting with them in a goal driven way. This process of active interaction is in the same spirit as a scientist performing experiments to discover hidden facts. Recent advances in artificial intelligence have yielded machines that can achieve superhuman performance in Go, Atari, natural language processing, and complex control problems; however, it is not clear that these systems can rival the scientific intuition of even a young child. In this work we introduce a basic set of tasks that require agents to estimate properties such as mass and cohesion of objects in an interactive simulated environment where they can manipulate the objects and observe the consequences. We found that deep reinforcement learning methods can learn to perform the experiments necessary to discover such hidden properties. By systematically manipulating the problem difficulty and the cost incurred by the agent for performing experiments, we found that agents learn different strategies that balance the cost of gathering information against the cost of making mistakes in different situations.  We also compare our learned experimentation policies to randomized baselines and show that the learned policies lead to better predictions.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795941", "vector": [], "sparse_vector": [], "title": "Learning and Policy Search in Stochastic Dynamical Systems with Bayesian Neural Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Finale Doshi-Velez", "Steffen Udluft"], "summary": "We present an algorithm for policy search in stochastic dynamical systems using\nmodel-based reinforcement learning. The system dynamics are described with\nBayesian neural networks (BNNs) that include stochastic input variables.  These\ninput variables allow us to capture complex statistical\npatterns in the transition dynamics (e.g. multi-modality and\nheteroskedasticity), which are usually missed by alternative modeling approaches. After\nlearning the dynamics, our BNNs are then fed into an algorithm that performs\nrandom roll-outs and uses stochastic optimization for policy learning. We train\nour BNNs by minimizing $\\alpha$-divergences with $\\alpha = 0.5$, which usually produces better\nresults than other techniques such as variational Bayes. We illustrate the performance of our method by\nsolving a challenging problem where model-based approaches usually fail and by\nobtaining promising results in real-world scenarios including the control of a\ngas turbine and an industrial benchmark.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795942", "vector": [], "sparse_vector": [], "title": "TopicRNN: A Recurrent Neural Network with Long-Range Semantic Dependency.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we propose TopicRNN, a recurrent neural network (RNN)-based language model designed to directly capture the global semantic meaning relating words in a document via latent topics. Because of their sequential nature, RNNs are good at capturing the local structure of a word sequence – both semantic and syntactic – but might face difficulty remembering long-range dependencies. Intuitively, these long-range dependencies are of semantic nature. In contrast, latent topic models are able to capture the global underlying semantic structure of a document but do not account for word ordering. The proposed TopicRNN model integrates the merits of RNNs and latent topic models: it captures local (syntactic) dependencies using an RNN and global (semantic) dependencies using latent topics. Unlike previous work on contextual RNN language modeling, our model is learned end-to-end. Empirical results on word prediction show that TopicRNN outperforms existing contextual RNN baselines. In addition, TopicRNN can be used as an unsupervised feature extractor for documents. We do this for sentiment analysis on the IMDB movie review dataset and report an error rate of 6.28%. This is comparable to the state-of-the-art 5.91% resulting from a semi-supervised approach. Finally, TopicRNN also yields sensible topics, making it a useful alternative to document models such as latent Dirichlet allocation.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795943", "vector": [], "sparse_vector": [], "title": "Density estimation using Real NVP.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Unsupervised learning of probabilistic models is a central yet challenging problem in machine learning. Specifically, designing models with tractable learning, sampling, inference and evaluation is crucial in solving this task. We extend the space of such models using real-valued non-volume preserving (real NVP) transformations, a set of powerful invertible and learnable transformations, resulting in an unsupervised learning algorithm with exact log-likelihood computation, exact sampling, exact inference of latent variables, and an interpretable latent space. We demonstrate its ability to model natural images on four datasets through sampling, log-likelihood evaluation and latent variable manipulations.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795944", "vector": [], "sparse_vector": [], "title": "Adversarial Feature Learning.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The ability of the Generative Adversarial Networks (GANs) framework to learn generative models mapping from simple latent distributions to arbitrarily complex data distributions has been demonstrated empirically, with compelling results showing generators learn to \"linearize semantics\" in the latent space of such models. Intuitively, such latent spaces may serve as useful feature representations for auxiliary problems where semantics are relevant. However, in their existing form, GANs have no means of learning the inverse mapping -- projecting data back into the latent space. We propose Bidirectional Generative Adversarial Networks (BiGANs) as a means of learning this inverse mapping, and demonstrate that the resulting learned feature representation is useful for auxiliary supervised discrimination tasks, competitive with contemporary approaches to unsupervised and self-supervised feature learning.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795945", "vector": [], "sparse_vector": [], "title": "Dance Dance Convolution.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Dance Dance Revolution (DDR) is a popular rhythm-based video game. Players perform steps on a dance platform in synchronization with music as directed by on-screen step charts. While many step charts are available in standardized packs, users may grow tired of existing charts, or wish to dance to a song for which no chart exists. We introduce the task of learning to choreograph. Given a raw audio track, the goal is to produce a new step chart. This task decomposes naturally into two subtasks:  deciding when to place steps and deciding which steps to select. We demonstrate deep learning solutions for both tasks and establish strong benchmarks for future work.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795946", "vector": [], "sparse_vector": [], "title": "Learning to Act by Predicting the Future.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present an approach to sensorimotor control in immersive environments. Our approach utilizes a high-dimensional sensory stream and a lower-dimensional measurement stream. The cotemporal structure of these streams provides a rich supervisory signal, which enables training a sensorimotor control model by interacting with the environment. The model is trained using supervised learning techniques, but without extraneous supervision. It learns to act based on raw sensory input from a complex three-dimensional environment. The presented formulation enables learning without a fixed goal at training time, and pursuing dynamically changing goals at test time. We conduct extensive experiments in three-dimensional simulations based on the classical first-person game Doom. The results demonstrate that the presented approach outperforms sophisticated prior formulations, particularly on challenging tasks. The results also show that trained models successfully generalize across environments and goals. A model trained using the presented approach won the Full Deathmatch track of the Visual Doom AI Competition, which was held in previously unseen environments.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795947", "vector": [], "sparse_vector": [], "title": "Deep Biaffine Attention for Neural Dependency Parsing.", "authors": ["<PERSON>", "<PERSON>"], "summary": "This paper builds off recent work from <PERSON><PERSON><PERSON><PERSON> & Goldberg (2016) using neural attention in a simple graph-based dependency parser. We use a larger but more thoroughly regularized parser than other recent BiLSTM-based approaches, with\nbiaffine classifiers to predict arcs and labels. Our parser gets state of the art or near state of the art performance on standard treebanks for six different languages, achieving 95.7% UAS and 94.1% LAS on the most popular English PTB dataset. This makes it the highest-performing graph-based parser on this benchmark—outperforming Kiperwasser & Goldberg (2016) by 1.8% and 2.2%—and comparable to the highest performing transition-based parser (<PERSON><PERSON><PERSON> et al., 2016), which achieves 95.8% UAS and 94.6% LAS. We also show which hyperparameter choices had a significant effect on parsing accuracy, allowing us to achieve large gains over other graph-based approaches.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795948", "vector": [], "sparse_vector": [], "title": "The Effectiveness of Transfer Learning in Electronic Health Records Data.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The application of machine learning to clinical data from Electronic Health Records is limited by the scarcity of meaningful labels.  Here we present initial results on the application of transfer learning to this problem.  We explore the transfer of knowledge from source tasks in which training labels are plentiful but of limited clinical value to more meaningful target tasks that have few labels.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795949", "vector": [], "sparse_vector": [], "title": "Adversarially Learned Inference.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce the adversarially learned inference (ALI) model, which jointly\nlearns a generation network and an inference network using an adversarial\nprocess. The generation network maps samples from stochastic latent variables to\nthe data space while the inference network maps training examples in data space\nto the space of latent variables. An adversarial game is cast between these two\nnetworks and a discriminative network that is trained to distinguish between\njoint latent/data-space samples from the generative network and joint samples\nfrom the inference network.  We illustrate the ability of the model to learn\nmutually coherent inference and generation networks through the inspections of\nmodel samples and reconstructions and confirm the usefulness of the learned\nrepresentations by obtaining a performance competitive with other recent\napproaches on the semi-supervised SVHN task.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795950", "vector": [], "sparse_vector": [], "title": "A Learned Representation For Artistic Style.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The diversity of painting styles represents a rich visual vocabulary for the construction of an image. The degree to which one may learn and parsimoniously capture this visual vocabulary measures our understanding of the higher level features of paintings, if not images in general. In this work we investigate the construction of a single, scalable deep network that can parsimoniously capture the artistic style of a diversity of paintings. We demonstrate that such a network generalizes across a diversity of artistic styles by reducing a painting to a point in an embedding space. Importantly, this model permits a user to explore new painting styles by arbitrarily combining the styles learned from individual paintings. We hope that this work provides a useful step towards building rich models of paintings and offers a window on to the structure of the learned representation of artistic style.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795951", "vector": [], "sparse_vector": [], "title": "Generative Multi-Adversarial Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Generative adversarial networks (GANs) are a framework for producing a generative model by way of a two-player minimax game.  In this paper, we propose the \\emph{Generative Multi-Adversarial Network} (GMAN), a framework that extends GANs to multiple discriminators. In previous work, the successful training of GANs requires modifying the minimax objective to accelerate training early on. In contrast, GMAN can be reliably trained with the original, untampered objective. We explore a number of design perspectives with the discriminator role ranging from formidable adversary to forgiving teacher.  Image generation tasks comparing the proposed framework to standard GANs demonstrate GMAN produces higher quality samples in a fraction of the iterations when measured by a pairwise GAM-type metric.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795952", "vector": [], "sparse_vector": [], "title": "Towards a Neural Statistician.", "authors": ["<PERSON>", "<PERSON>"], "summary": "An efficient learner is one who reuses what they already know to tackle a new problem. For a machine learner, this means understanding the similarities amongst datasets. In order to do this, one must take seriously the idea of working with datasets, rather than datapoints, as the key objects to model. Towards this goal, we demonstrate an extension of a variational autoencoder that can learn a method for computing representations, or statistics, of datasets in an unsupervised fashion. The network is trained to produce statistics that encapsulate a generative model for each dataset. Hence the network enables efficient learning from new datasets for both unsupervised and supervised tasks. We show that we are able to learn statistics that can be used for: clustering datasets, transferring generative models to new datasets, selecting representative samples of datasets and classifying previously unseen classes. We refer to our model as a neural statistician, and by this we mean a neural network that can learn to compute summary statistics of datasets without supervision.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795953", "vector": [], "sparse_vector": [], "title": "Learning Recurrent Representations for Hierarchical Behavior Modeling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Yu<PERSON>", "<PERSON>"], "summary": "We propose a framework for detecting action patterns from motion sequences and modeling the sensory-motor relationship of animals, using a generative recurrent neural network. The network has a discriminative part (classifying actions) and a generative part (predicting motion), whose recurrent cells are laterally connected, allowing higher levels of the network to represent high level behavioral phenomena. We test our framework on two types of tracking data, fruit fly behavior and online handwriting. Our results show that 1) taking advantage of unlabeled sequences, by predicting future motion, significantly improves action detection performance when training labels are scarce, 2) the network learns to represent high level phenomena such as writer identity and fly gender, without supervision, and 3) simulated motion trajectories, generated by treating motion prediction as input to the network, look realistic and may be used to qualitatively evaluate whether the model has learnt generative control rules.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795954", "vector": [], "sparse_vector": [], "title": "Neural Functional Programming.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We discuss a range of modeling choices that arise when constructing an end-to-end differentiable programming language suitable for learning programs from input-output examples. Taking cues from programming languages research, we study the effect of memory allocation schemes, immutable data, type systems, and built-in control-flow structures on the success rate of learning algorithms. We build a range of models leading up to a simple differentiable functional programming language. Our empirical evaluation shows that this language allows to learn far more programs than existing baselines.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795955", "vector": [], "sparse_vector": [], "title": "Generalizing Skills with Semi-Supervised Reinforcement Learning.", "authors": ["Chelsea Finn", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Deep reinforcement learning (RL) can acquire complex behaviors from low-level inputs, such as images. However, real-world applications of such methods require generalizing to the vast variability of the real world. Deep networks are known to achieve remarkable generalization when provided with massive amounts of labeled data, but can we provide this breadth of experience to an RL agent, such as a robot? The robot might continuously learn as it explores the world around it, even while it is deployed and performing useful tasks. However, this learning requires access to a reward function, to tell the agent whether it is succeeding or failing at its task. Such reward functions are often hard to measure in the real world, especially in domains such as robotics and dialog systems, where the reward could depend on the unknown positions of objects or the emotional state of the user. On the other hand, it is often quite practical to provide the agent with reward functions in a limited set of situations, such as when a human supervisor is present, or in a controlled laboratory setting. Can we make use of this limited supervision, and still benefit from the breadth of experience an agent might collect in the unstructured real world? In this paper, we formalize this problem setting as semi-supervised reinforcement learning (SSRL), where the reward function can only be evaluated in a set of “labeled” MDPs, and the agent must generalize its behavior to the wide range of states it might encounter in a set of “unlabeled” MDPs, by using experience from both settings. Our proposed method infers the task objective in the unlabeled MDPs through an algorithm that resembles inverse RL, using the agent’s own prior experience in the labeled MDPs as a kind of demonstration of optimal behavior. We evaluate our method on challenging, continuous control tasks that require control directly from images, and show that our approach can improve the generalization of a learned deep neural network policy by using experience for which no reward function is available. We also show that our method outperforms direct supervised learning of the reward.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795956", "vector": [], "sparse_vector": [], "title": "Adversarial Examples for Semantic Image Segmentation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Machine learning methods in general and Deep Neural Networks in particular have shown to be vulnerable to adversarial perturbations. So far this phenomenon has mainly been studied in the context of whole-image classification. In this contribution, we analyse how adversarial perturbations can affect the task of semantic segmentation. We show how existing adversarial attackers can be transferred to this task and that it is possible to create imperceptible adversarial perturbations\nthat lead a deep network to misclassify almost all pixels of a chosen class while leaving network prediction nearly unchanged outside this class.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795957", "vector": [], "sparse_vector": [], "title": "Stochastic Neural Networks for Hierarchical Reinforcement Learning.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Deep reinforcement learning has achieved many impressive results in recent years. However, tasks with sparse rewards or long horizons continue to pose significant challenges. To tackle these important problems, we propose a general framework that first learns useful skills in a pre-training environment, and then leverages the acquired skills for learning faster in downstream tasks.\nOur approach brings together some of the strengths of intrinsic motivation and hierarchical methods: the learning of useful skill is guided by a single proxy reward, the design of which requires very minimal domain knowledge about the downstream tasks. Then a high-level policy is trained on top of these skills, providing a significant improvement of the exploration and allowing to tackle sparse rewards in the downstream tasks. To efficiently pre-train a large span of skills, we use Stochastic Neural Networks combined with an information-theoretic regularizer. Our experiments show that this combination is effective in learning a wide span of interpretable skills in a sample-efficient way, and can significantly boost the learning performance uniformly across a wide range of downstream tasks.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795958", "vector": [], "sparse_vector": [], "title": "On Hyperparameter Optimization in Learning Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We study two procedures (reverse-mode and forward-mode) for computing the gradient of the validation error with respect to the hyperparameters  of any iterative learning algorithm. These procedures mirror two ways of computing gradients for recurrent neural networks and have different trade-offs  in terms of running time and space requirements. The reverse-mode procedure extends previous work by (<PERSON><PERSON><PERSON> et al. 2015) and offers the opportunity to insert constraints on the hyperparameters in a natural way. The forward-mode procedure is suitable for stochastic hyperparameter updates, which may significantly speedup the overall hyperparameter optimization procedure.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795959", "vector": [], "sparse_vector": [], "title": "Topology and Geometry of Half-Rectified Network Optimization.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The loss surface of deep neural networks has recently attracted interest \nin the optimization and machine learning communities as a prime example of \nhigh-dimensional non-convex problem. Some insights were recently gained using spin glass \nmodels and mean-field approximations, but at the expense of strongly simplifying the nonlinear nature of the model.\n\nIn this work, we do not make any such approximation and study conditions \non the data distribution and model architecture that prevent the existence \nof bad local minima. Our theoretical work quantifies and formalizes two \nimportant folklore facts: (i) the landscape of deep linear networks has a radically different topology \nfrom that of deep half-rectified ones, and (ii) that the energy landscape \nin the non-linear case is fundamentally controlled by the interplay between the smoothness of the data distribution and model over-parametrization. Our main theoretical contribution is to prove that half-rectified single layer networks are asymptotically connected, and we provide explicit bounds that reveal the aforementioned interplay.\n\nThe conditioning of gradient descent is the next challenge we address. \nWe study this question through the geometry of the level sets, and we introduce\nan algorithm to efficiently estimate the regularity of such sets on large-scale networks. \nOur empirical results show that these level sets remain connected throughout \nall the learning phase, suggesting a near convex behavior, but they become \nexponentially more curvy as the energy level decays, in accordance to what is observed in practice with \nvery low curvature attractors.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795960", "vector": [], "sparse_vector": [], "title": "DeepCloak: Masking Deep Neural Network Models for Robustness Against Adversarial Samples.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent studies have shown that deep neural networks (DNN) are vulnerable to adversarial samples: maliciously-perturbed samples crafted to yield incorrect model outputs. Such attacks can severely undermine DNN systems, particularly in security-sensitive settings. It was observed that an adversary could easily generate adversarial samples by making a small perturbation on irrelevant feature dimensions that are unnecessary for the current classification task.  To overcome this problem, we introduce a defensive mechanism called DeepCloak. By identifying and removing unnecessary features in a DNN model, DeepCloak limits the capacity an attacker can use generating adversarial samples and therefore increase the robustness against such inputs. Comparing with other defensive approaches, DeepCloak is easy to implement and computationally efficient. Experimental results show that DeepCloak can increase the performance of state-of-the-art DNN models against adversarial samples.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795961", "vector": [], "sparse_vector": [], "title": "Restricted Boltzmann Machines provide an accurate metric for retinal responses to visual stimuli.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "How to discriminate visual stimuli based on the activity they evoke in sensory neurons is still an open challenge. To measure discriminability power, we search for a neural metric that preserves distances in stimulus space, so that responses to different stimuli are far apart and responses to the same stimulus are close. Here, we show that Restricted Boltzmann Machines (RBMs) provide such a distance-preserving neural metric. Even when learned in a unsupervised way, RBM-based metric can discriminate stimuli with higher resolution than classical metrics.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795962", "vector": [], "sparse_vector": [], "title": "Neurogenesis-Inspired Dictionary Learning: Online Model Adaption in a Changing World.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We address the problem of online model adaptation when learning representations from non-stationary data streams. For now, we focus on single hidden-layer sparse linear autoencoders (i.e. sparse dictionary learning), although in the future, the proposed approach can be extended naturally to general multi-layer autoencoders and supervised models. We propose a simple but effective online model-selection, based on alternating-minimization scheme, which involves “birth” (addition of new elements) and “death” (removal, via l1/l2 group sparsity) of hidden units representing dictionary elements, in response to changing inputs; we draw inspiration from the adult neurogenesis phenomenon in the dentate gyrus of the hippocampus, known to be associated with better adaptation to new environments. Empirical evaluation on both real-life and synthetic data demonstrates that the proposed approach can considerably outperform the state-of-art non-adaptive online sparse coding of <PERSON> et al. (2009) in the presence of non-stationary data, especially when dictionaries are sparse.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795963", "vector": [], "sparse_vector": [], "title": "Shake-Shake regularization of 3-branch residual networks.", "authors": ["<PERSON>"], "summary": "The method introduced in this paper aims at helping computer vision practitioners faced with an overfit problem. The idea is to replace, in a 3-branch ResNet, the standard summation of residual branches by a stochastic affine combination. The largest tested model improves on the best single shot published result on CIFAR-10 by reaching 2.86% test error. Code is available at https://github.com/xgastaldi/shake-shake", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795964", "vector": [], "sparse_vector": [], "title": "Lifelong Perceptual Programming By Example.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce and develop solutions for the problem of Lifelong Perceptual Programming By Example (LPPBE). The problem is to induce a series of programs that require understanding perceptual data like images or text. LPPBE systems learn from weak supervision (input-output examples) and incrementally construct a shared library of components that grows and improves as more tasks are solved. Methodologically, we extend differentiable interpreters to operate on perceptual data and to share components across tasks. Empirically we show that this leads to a lifelong learning system that transfers knowledge to new tasks more effectively than baselines, and the performance on earlier tasks continues to improve even as the system learns on new, different tasks.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795965", "vector": [], "sparse_vector": [], "title": "Compositional Kernel Machines.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Convolutional neural networks (convnets) have achieved impressive results on recent computer vision benchmarks. While they benefit from multiple layers that encode nonlinear decision boundaries and a degree of translation invariance, training convnets is a lengthy procedure fraught with local optima. Alternatively, a kernel method that incorporates the compositionality and symmetry of convnets could learn similar nonlinear concepts yet with easier training and architecture selection. We propose compositional kernel machines (CKMs), which effectively create an exponential number of virtual training instances by composing transformed sub-regions of the original ones. Despite this, CKM discriminant functions can be computed efficiently using ideas from sum-product networks. The ability to compose virtual instances in this way gives CKMs invariance to translations and other symmetries, and combats the curse of dimensionality. Just as support vector machines (SVMs) provided a compelling alternative to multilayer perceptrons when they were introduced, CKMs could become an attractive approach for object recognition and other vision problems. In this paper we define CKMs, explore their properties, and present promising results on NORB datasets. Experiments show that CKMs can outperform SVMs and be competitive with convnets in a number of dimensions, by learning symmetries and compositional concepts from fewer samples without data augmentation.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795966", "vector": [], "sparse_vector": [], "title": "Explaining the Learning Dynamics of Direct Feedback Alignment.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Two recently developed methods, Feedback Alignment (FA) and Direct Feedback Alignment (DFA), have been shown to obtain surprising performance on vision tasks by replacing the traditional backpropagation update with a random feedback update. However, it is still not clear what mechanisms allow learning to happen with these random updates. \n    In this work we argue that DFA can be viewed as a noisy variant of a layer-wise training method we call Linear Aligned Feedback Systems (LAFS). We support this connection theoretically by comparing the update rules for the two methods.  We additionally empirically verify that the random update matrices used in DFA work effectively as readout matrices, and that strong correlations exist between the error vectors used in the DFA and LAFS updates. With this new connection between DFA and LAFS we are able to explain why the \"alignment\" happens in DFA.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795967", "vector": [], "sparse_vector": [], "title": "Fast Chirplet Transform Injects Priors in Deep Learning of Animal Calls and Speech.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Bioacoustic data set analyses require substantial baseline training data in order to accurately recognize and characterize specific kernels. Current approaches using the scattering framework and/or Convolutional Neural Nets (CNN) often require substantial dedicated computer time to achieve desired results. We propose a trade-off between these two approaches using a Chirplet kernel as an efficient Q constant bioacoustic representation to pretrain the CNN. First we implement a Chirplet bioinspired auditory representation. Second we implement the first algorithm (and code) for a Fast Chirplet Transform (FCT). Third, we demonstrate the computation efficiency of the FCT on selected large environmental databases: including months of Orca recordings and 1000 Birds species from the LifeClef challenge. Fourth, we validate the FCT on the vowels subset of the Speech TIMIT dataset. The results show that FCT accelerates CNN by twenty eight percent for birds classification, and by twenty six percent for vowel classification. Scores are also enhanced by FCT pretraining, with a relative gain of 7.8\\% of Mean Average Precision on birds, and 2.3\\% of vowel accuracy against raw audio CNN. We conclude on with perspectives on tonotopic FCT deep machine listening, and inter-species bioacoustic transfer learning to generalise the representation of animal communication systems.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795968", "vector": [], "sparse_vector": [], "title": "Training deep neural-networks using a noise adaptation layer.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The availability of large datsets has enabled neural networks to achieve impressive recognition results. However, the presence of inaccurate class labels is known  to deteriorate the performance of even the best classifiers in a broad range of classification problems. Noisy labels also  tend to be more harmful than noisy attributes. When the observed label is noisy, we can view the correct label as a latent random variable and model the noise processes by a communication channel with unknown parameters. Thus we can apply the EM algorithm to find the parameters of both the network and the noise  and to estimate the correct label. In this study we present a neural-network approach that optimizes  the same likelihood function as optimized by the EM algorithm. The noise is explicitly modeled by an additional softmax layer that connects the correct labels to the noisy ones. This scheme is then extended  to the case where the noisy labels are dependent  on the features in addition to the correct labels.  Experimental results demonstrate that this approach  outperforms previous methods.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795969", "vector": [], "sparse_vector": [], "title": "Improving Neural Language Models with a Continuous Cache.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose an extension to neural network language models to adapt their prediction to the recent history. Our model is a simplified version of memory augmented networks, which stores past hidden activations as memory and accesses them through a dot product with the current hidden activation. This mechanism is very efficient and scales to very large memory sizes. We also draw a link between the use of external memory in neural network and cache models used with count based language models. We demonstrate on several language model datasets that our approach performs significantly better than recent memory augmented networks.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795970", "vector": [], "sparse_vector": [], "title": "Highway and Residual Networks learn Unrolled Iterative Estimation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The past year saw the introduction of new architectures such as Highway networks and Residual networks which, for the first time, enabled the training of feedforward networks with dozens to hundreds of layers using simple gradient descent.\nWhile depth of representation has been posited as a primary reason for their success, there are indications that these architectures defy a popular view of deep learning as a hierarchical computation of increasingly abstract features at each layer.\n\nIn this report, we argue that this view is incomplete and does not adequately explain several recent findings.\nWe propose an alternative viewpoint based on unrolled iterative estimation---a group of successive layers iteratively refine their estimates of the same features instead of computing an entirely new representation.\nWe demonstrate that this viewpoint directly leads to the construction of highway and residual networks. \nFinally we provide preliminary experiments to discuss the similarities and differences between the two architectures.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795971", "vector": [], "sparse_vector": [], "title": "Neural Expectation Maximization.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce a novel framework for clustering that combines generalized EM\nwith neural networks and can be implemented as an end-to-end differentiable\nrecurrent neural network. It learns its statistical model directly from the data and\ncan represent complex non-linear dependencies between inputs. We apply our\nframework to a perceptual grouping task and empirically verify that it yields the\nintended behavior as a proof of concept.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795972", "vector": [], "sparse_vector": [], "title": "Variational Intrinsic Control.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce a new unsupervised reinforcement learning method for discovering the set of intrinsic options available to an agent. This set is learned by maximizing the number of different states an agent can reliably reach, as measured by the mutual information between the set of options and option termination states. To this end, we instantiate two policy gradient based algorithms, one that creates an explicit embedding space of options and one that represents options implicitly. Both algorithms also yield a tractable and explicit empowerment measure, which is useful for empowerment maximizing agents. Furthermore, they scale well with function approximation and we demonstrate their applicability on a range of tasks.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795973", "vector": [], "sparse_vector": [], "title": "Q-Prop: Sample-Efficient Policy Gradient with An Off-Policy Critic.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Model-free deep reinforcement learning (RL) methods have been successful in a wide variety of simulated domains. However, a major obstacle facing deep RL in the real world is their high sample complexity. Batch policy gradient methods offer stable learning, but at the cost of high variance, which often requires large batches. TD-style methods, such as off-policy actor-critic and Q-learning, are more sample-efficient but biased, and often require costly hyperparameter sweeps to stabilize. In this work, we aim to develop methods that combine the stability of policy gradients with the efficiency of off-policy RL. We present Q-Prop, a policy gradient method that uses a Taylor expansion of the off-policy critic as a control variate. Q-Prop is both sample efficient and stable, and effectively combines the benefits of on-policy and off-policy methods. We analyze the connection between Q-Prop and existing model-free algorithms, and use control variate theory to derive two variants of Q-Prop with conservative and aggressive adaptation. We show that conservative Q-Prop provides substantial gains in sample efficiency over trust region policy optimization (TRPO) with generalized advantage estimation (GAE), and improves stability over deep deterministic policy gradient (DDPG), the state-of-the-art on-policy and off-policy methods, on OpenAI Gym's MuJoCo continuous control environments.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795974", "vector": [], "sparse_vector": [], "title": "Mollifying Networks.", "authors": ["<PERSON><PERSON><PERSON>ülçeh<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The optimization of deep neural networks can be more challenging than the traditional convex optimization problems due to highly non-convex nature of the loss function, e.g. it can involve pathological landscapes such as saddle-surfaces that can be difficult to escape from for algorithms based on simple gradient descent. In this paper, we attack the problem of optimization of highly non-convex neural networks objectives by starting with a smoothed -- or mollified -- objective function which becomes more complex as the training proceeds.  Our proposition is inspired by the recent studies in continuation methods: similarly to curriculum methods, we begin by learning an easier (possibly convex) objective function and let it evolve during training until it eventually becomes the original, difficult to optimize objective function. The complexity of the mollified networks is controlled by a single hyperparameter that is annealed during training. We show improvements on various difficult optimization tasks and establish a relationship between recent works on continuation methods for neural networks and mollifiers.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795975", "vector": [], "sparse_vector": [], "title": "PixelVAE: A Latent Variable Model for Natural Images.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Natural image modeling is a landmark challenge of unsupervised learning. Variational Autoencoders (VAEs) learn a useful latent representation and model global structure well but have difficulty capturing small details. PixelCNN models details very well, but lacks a latent code and is difficult to scale for capturing large structures. We present PixelVAE, a VAE model with an autoregressive decoder based on PixelCNN. Our model requires very few expensive autoregressive layers compared to PixelCNN and learns latent codes that are more compressed than a standard VAE while still capturing most non-trivial structure. Finally, we extend our model to a hierarchy of latent variables at different scales. Our model achieves state-of-the-art performance on binarized MNIST, competitive performance on 64 × 64 ImageNet, and high-quality samples on the LSUN bedrooms dataset.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795976", "vector": [], "sparse_vector": [], "title": "Learning to Query, Reason, and Answer Questions On Ambiguous Texts.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A key goal of research in conversational systems is to train an interactive agent to help a user with a task. Human conversation, however, is notoriously incomplete, ambiguous, and full of extraneous detail. To operate effectively, the agent must not only understand what was explicitly conveyed but also be able to reason in the presence of missing or unclear information. When unable to resolve ambiguities on its own, the agent must be able to ask the user for the necessary clarifications and incorporate the response in its reasoning. Motivated by this problem we introduce QRAQ (\"crack\"; Query, Reason, and Answer Questions), a new synthetic domain, in which a User gives an Agent a short story and asks a challenge question. These problems are designed to test the reasoning and interaction capabilities of a learning-based Agent in a setting that requires multiple conversational turns. A good Agent should ask only non-deducible, relevant questions until it has enough information to correctly answer the User's question. We use standard and improved reinforcement learning based memory-network architectures to solve QRAQ problems in the difficult setting where the reward signal only tells the Agent if its final answer to the challenge question is correct or not. To provide an upper-bound to the RL results we also train the same architectures using supervised information that tells the Agent during training which variables to query and the answer to the challenge question. We evaluate our architectures on four QRAQ dataset types, and scale the complexity for each along multiple dimensions.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795977", "vector": [], "sparse_vector": [], "title": "HyperNetworks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>uo<PERSON> <PERSON><PERSON>"], "summary": "This work explores hypernetworks: an approach of using one network, also known as a hypernetwork, to generate the weights for another network.  We apply hypernetworks to generate adaptive weights for recurrent networks. In this case, hypernetworks can be viewed as a relaxed form of weight-sharing across layers. In our implementation, hypernetworks are are trained jointly with the main network in an end-to-end fashion.  Our main result is that hypernetworks can generate non-shared weights for LSTM and achieve state-of-the-art results on a variety of sequence modelling tasks including character-level language modelling, handwriting generation and neural machine translation, challenging the weight-sharing paradigm for recurrent networks.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795978", "vector": [], "sparse_vector": [], "title": "Metacontrol for Adaptive Imagination-Based Optimization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Oriol Vinyals", "<PERSON>", "<PERSON>"], "summary": "Many machine learning systems are built to solve the hardest examples of a particular task, which often makes them large and expensive to run---especially with respect to the easier examples, which might require much less computation. For an agent with a limited computational budget, this \"one-size-fits-all\" approach may result in the agent wasting valuable computation on easy examples, while not spending enough on hard examples. Rather than learning a single, fixed policy for solving all instances of a task, we introduce a metacontroller which learns to optimize a sequence of \"imagined\" internal simulations over predictive models of the world in order to construct a more informed, and more economical, solution. The metacontroller component is a model-free reinforcement learning agent, which decides both how many iterations of the optimization procedure to run, as well as which model to consult on each iteration. The models (which we call \"experts\") can be state transition models, action-value functions, or any other mechanism that provides information useful for solving the task, and can be learned on-policy or off-policy in parallel with the metacontroller. When the metacontroller, controller, and experts were trained with \"interaction networks\" (Bat<PERSON><PERSON> et al., 2016) as expert models, our approach was able to solve a challenging decision-making problem under complex non-linear dynamics. The metacontroller learned to adapt the amount of computation it performed to the difficulty of the task, and learned how to choose which experts to consult by factoring in both their reliability and individual computational resource costs. This allowed the metacontroller to achieve a lower overall cost (task loss plus computational cost) than more traditional fixed policy approaches. These results demonstrate that our approach is a powerful framework for using rich forward models for efficient model-based reinforcement learning.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795979", "vector": [], "sparse_vector": [], "title": "DSD: Dense-Sparse-<PERSON>se Training for Deep Neural Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Modern deep neural networks have a large number of parameters, making them very hard to train. We propose DSD, a dense-sparse-dense training flow, for regularizing deep neural networks and achieving better optimization performance. In the first D (Dense) step, we train a dense network to learn connection weights and importance. In the S (Sparse) step, we regularize the network by pruning the unimportant connections with small weights and retraining the network given the sparsity constraint. In the final D (re-Dense) step, we increase the model capacity by removing the sparsity constraint, re-initialize the pruned parameters from zero and retrain the whole dense network. Experiments show that DSD training can improve the performance for a wide range of CNNs, RNNs and LSTMs on the tasks of image classification, caption generation and speech recognition. On ImageNet, DSD improved the Top1 accuracy of GoogLeNet by 1.1%, VGG-16 by 4.3%, ResNet-18 by 1.2% and ResNet-50 by 1.1%, respectively. On the WSJ’93 dataset, DSD improved DeepSpeech and DeepSpeech2 WER by 2.0% and 1.1%. On the Flickr-8K dataset, DSD improved the NeuralTalk BLEU score by over 1.7. DSD is easy to use in practice: at training time, DSD incurs only one extra hyper-parameter: the sparsity ratio in the S step. At testing time, DSD doesn’t change the network architecture or incur any inference overhead. The consistent and significant performance gain of DSD experiments shows the inadequacy of the current training methods for finding the best local optimum, while DSD effectively achieves superior optimization performance for finding a better solution. DSD models are available to download at https://songhan.github.io/DSD.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795980", "vector": [], "sparse_vector": [], "title": "Identity Matters in Deep Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "An emerging design principle in deep learning is that each layer of a deep\nartificial neural network should be able to easily express the identity\ntransformation. This idea not only motivated various normalization techniques,\nsuch as batch normalization, but was also key to the immense success of\nresidual networks.\n\nIn this work, we put the principle of identity parameterization on a more \nsolid theoretical footing alongside further empirical progress. We first\ngive a strikingly simple proof that arbitrarily deep linear residual networks\nhave no spurious local optima. The same result for feed-forward networks in\ntheir standard parameterization is substantially more delicate.  Second, we\nshow that residual networks with ReLu activations have universal finite-sample\nexpressivity in the sense that the network can represent any function of its\nsample provided that the model has more parameters than the sample size.\n\nDirectly inspired by our theory, we experiment with a radically simple\nresidual architecture consisting of only residual convolutional layers and\nReLu activations, but no batch normalization, dropout, or max pool. Our model\nimproves significantly on previous all-convolutional networks on the CIFAR10,\nCIFAR100, and ImageNet classification benchmarks.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795981", "vector": [], "sparse_vector": [], "title": "Emergence of Language with Multi-agent Games: Learning to Communicate with Sequences of Symbols.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Learning to communicate through interaction, rather than relying on explicit supervision, is often considered a prerequisite for developing a general AI. We study a setting where two agents engage in playing a referential game and, from scratch, develop a communication protocol necessary to succeed in this game. We require that messages they exchange, both at train and test time, are in the form of a language (i.e. sequences of discrete symbols). As the ultimate goal is to ensure that communication is accomplished in natural language, we perform preliminary experiments where we inject prior information about natural language into our model and study properties of the resulting protocol.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795982", "vector": [], "sparse_vector": [], "title": "Learning to Play in a Day: Faster Deep Reinforcement Learning by Optimality Tightening.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We propose a novel training algorithm for reinforcement learning which combines the strength of deep Q-learning with a constrained optimization approach to tighten optimality and encourage faster reward propagation. Our novel technique makes deep reinforcement learning more practical by drastically reducing the training time.  We evaluate the performance of our approach on the 49 games of the challenging Arcade  Learning Environment, and report significant improvements in both training time and accuracy.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795983", "vector": [], "sparse_vector": [], "title": "Multi-view Recurrent Neural Acoustic Word Embeddings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Recent work has begun exploring neural acoustic word embeddings–fixed dimensional vector representations of arbitrary-length speech segments corresponding to words. Such embeddings are applicable to speech retrieval and recognition tasks, where reasoning about whole words may make it possible to avoid ambiguous sub-word representations. The main idea is to map acoustic sequences to fixed-dimensional vectors such that examples of the same word are mapped to similar vectors, while different-word examples are mapped to very different vectors. In this work we take a multi-view approach to learning acoustic word embeddings, in which we jointly learn to embed acoustic sequences and their corresponding character sequences. We use deep bidirectional LSTM embedding models and multi-view contrastive losses. We study the effect of different loss variants, including fixed-margin and cost-sensitive losses. Our acoustic word embeddings improve over previous approaches for the task of word discrimination. We also present results on other tasks that are enabled by the multi-view approach, including cross-view word discrimination and word similarity.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795984", "vector": [], "sparse_vector": [], "title": "Tracking the World State with Recurrent Entity Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We introduce a new model, the Recurrent Entity Network (EntNet). It is equipped\nwith a dynamic long-term memory which allows it to maintain and update a rep-\nresentation of the state of the world as it receives new data. For language under-\nstanding tasks, it can reason on-the-fly as it reads text, not just when it is required\nto answer a question or respond as is the case for a Memory Network (<PERSON><PERSON><PERSON><PERSON><PERSON>\net al., 2015). Like a Neural Turing Machine or Differentiable Neural Computer\n(<PERSON> et al., 2014; 2016) it maintains a fixed size memory and can learn to\nperform location and content-based read and write operations. However, unlike\nthose models it has a simple parallel architecture in which several memory loca-\ntions can be updated simultaneously. The EntNet sets a new state-of-the-art on\nthe bAbI tasks, and is the first method to solve all the tasks in the 10k training\nexamples setting. We also demonstrate that it can solve a reasoning task which\nrequires a large number of supporting facts, which other methods are not able to\nsolve, and can generalize past its training horizon. It can also be practically used\non large scale datasets such as Children’s Book Test, where it obtains competitive\nperformance, reading the story in a single pass.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795985", "vector": [], "sparse_vector": [], "title": "A Baseline for Detecting Misclassified and Out-of-Distribution Examples in Neural Networks.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We consider the two related problems of detecting if an example is misclassified or out-of-distribution. We present a simple baseline that utilizes probabilities from softmax distributions. Correctly classified examples tend to have greater maximum softmax probabilities than erroneously classified and out-of-distribution examples, allowing for their detection. We assess performance by defining several tasks in computer vision, natural language processing, and automatic speech recognition, showing the effectiveness of this baseline across all. We then show the baseline can sometimes be surpassed, demonstrating the room for future research on these underexplored detection tasks.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795986", "vector": [], "sparse_vector": [], "title": "Early Methods for Detecting Adversarial Images.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Many machine learning classifiers are vulnerable to adversarial perturbations. An adversarial perturbation modifies an input to change a classifier's prediction without causing the input to seem substantially different to human perception. We deploy three methods to detect adversarial images. Adversaries trying to bypass our detectors must make the adversarial image less pathological or they will fail trying. Our best detection method reveals that adversarial images place abnormal emphasis on the lower-ranked principal components from PCA. Other detectors and a colorful saliency map are in an appendix.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795987", "vector": [], "sparse_vector": [], "title": "Development of JavaScript-based deep learning platform and application to distributed training.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deep learning is increasingly attracting attention for processing big data.\nExisting frameworks for deep learning must be set up to specialized computer systems. Gaining sufficient computing resources therefore entails high costs of deployment and maintenance.\nIn this work, we implement a matrix library and deep learning framework that uses JavaScript. It can run on web browsers operating on ordinary personal computers and smartphones.\nUsing JavaScript, deep learning can be accomplished in widely diverse environments without the necessity for software installation. Using GPGPU from WebCL framework, our framework can train large scale convolutional neural networks such as VGGNet and ResNet.\nIn the experiments, we demonstrate their practicality by training VGGNet in a distributed manner using web browsers as the client.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795988", "vector": [], "sparse_vector": [], "title": "beta-VAE: Learning Basic Visual Concepts with a Constrained Variational Framework.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ark<PERSON> Pal", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Learning an interpretable factorised representation of the independent data generative factors of the world without supervision is an important precursor for the development of artificial intelligence that is able to learn and reason in the same way that humans do. We introduce beta-VAE, a new state-of-the-art framework for automated discovery of interpretable factorised latent representations from raw image data in a completely unsupervised manner. Our approach is a modification of the variational autoencoder (VAE) framework. We introduce an adjustable hyperparameter beta that balances latent channel capacity and independence constraints with reconstruction accuracy. We demonstrate that beta-VAE with appropriately tuned\f beta > 1 qualitatively outperforms VAE (beta = 1), as well as state of the art unsupervised (InfoGAN) and semi-supervised (DC-IGN) approaches to disentangled factor learning on a variety of datasets (celebA, faces and chairs). Furthermore, we devise a protocol to quantitatively compare the degree of disentanglement learnt by different models, and show that our approach also significantly outperforms all baselines quantitatively. Unlike InfoGAN, beta-VAE is stable to train, makes few assumptions about the data and relies on tuning a single hyperparameter, which can be directly optimised through a hyper parameter search using weakly labelled data or through heuristic visual inspection for purely unsupervised data.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795989", "vector": [], "sparse_vector": [], "title": "Semi-supervised deep learning by metric embedding.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep networks are successfully used as classification models yielding state-of-the-art results when trained on a large number of labeled samples. These models, however, are usually much less suited for semi-supervised problems because of their tendency to overfit easily when trained on small amounts of data. In this work we will explore a new training objective that is targeting a semi-supervised regime with only a small subset of labeled data. This criterion is based on a deep metric embedding over distance relations within the set of labeled samples, together with constraints over the embeddings of the unlabeled set. The final learned representations are discriminative in euclidean space, and hence can be used with subsequent nearest-neighbor classification using the labeled samples.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795990", "vector": [], "sparse_vector": [], "title": "Loss-aware Binarization of Deep Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Deep neural network models, though very powerful and highly successful, are computationally expensive in terms of space and time. Recently, there have been a number of attempts on binarizing the network weights and activations. This greatly reduces the network size, and replaces the underlying multiplications to additions or even XNOR bit operations. However, existing binarization schemes are based on simple matrix approximations and ignore the effect of binarization on the loss. In this paper, we propose a proximal Newton algorithm with diagonal Hessian approximation that directly minimizes the loss w.r.t. the binarized weights. The underlying proximal step has an efficient closed-form solution, and the second-order information can be efficiently obtained from the second moments already computed by the Adam optimizer. Experiments on both feedforward and recurrent networks show that the proposed loss-aware binarization algorithm outperforms existing binarization schemes, and is also more robust for wide and deep networks.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795991", "vector": [], "sparse_vector": [], "title": "Online Structure Learning for Sum-Product Networks with Gaussian Leaves.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Sum-product networks (SPNs) have recently emerged as an attractive representation due to their dual view as a special type of deep neural network with clear semantics and a special type of probabilistic graphical model for which inference is always tractable. Those properties follow from some conditions (i.e., completeness and decomposability) that must be respected by the structure of the network.  As a result, it is not easy to specify a valid sum-product network by hand and therefore structure learning techniques are typically used in practice.  This paper describes the first online structure learning technique for continuous SPNs with Gaussian leaves. We also introduce an accompanying new parameter learning technique.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795992", "vector": [], "sparse_vector": [], "title": "Arbitrary Style Transfer in Real-time with Adaptive Instance Normalization.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON> et al. (2015) recently introduced a neural algorithm that renders a content image in the style of another image, achieving so-called \\emph{style transfer}. However, their framework requires a slow iterative optimization process, which limits its practical application. Fast approximations with  feed-forward neural networks have been proposed to speed up neural style transfer. Unfortunately, the speed improvement comes at a cost: the network is usually tied to a fixed set of styles and cannot adapt to arbitrary new styles. In this paper, we present a simple yet effective approach that for the first time enables arbitrary style transfer in real-time. At the heart of our method is a novel adaptive instance normalization~(AdaIN) layer that aligns the mean and variance of the content features with those of the style features. Our method achieves speed comparable to the fastest existing approach, without the restriction to a pre-defined set of styles.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795993", "vector": [], "sparse_vector": [], "title": "Snapshot Ensembles: Train 1, <PERSON> M for Free.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Ensembles of neural networks are known to be much more robust and accurate than individual networks. However, training multiple deep networks for model averaging is computationally expensive. In this paper, we propose a method to obtain the seemingly contradictory goal of ensembling multiple neural networks at no additional training cost. We achieve this goal by training a single neural network, converging to several local minima along its optimization path and saving the model parameters.  To obtain repeated rapid convergence, we leverage recent work on cyclic learning rate schedules. The resulting technique, which we refer to as Snapshot Ensembling, is simple, yet surprisingly effective.  We show in a series of experiments that our approach is compatible with diverse network architectures and learning tasks. It consistently yields  lower error rates than state-of-the-art single models at no additional training cost, and compares favorably with traditional network ensembles. On CIFAR-10 and CIFAR-100 our DenseNet Snapshot Ensembles obtain error rates of 3.4% and 17.4% respectively.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795994", "vector": [], "sparse_vector": [], "title": "Adversarial Attacks on Neural Network Policies.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Machine learning classifiers are known to be vulnerable to inputs maliciously constructed by adversaries to force misclassification. Such adversarial examples have been extensively studied in the context of computer vision applications. In this work, we show adversarial attacks are also effective when targeting neural network policies in reinforcement learning. Specifically, we show existing adversarial example crafting techniques can be used to significantly degrade test-time performance of trained policies. Our threat model considers adversaries capable of introducing small perturbations to the raw input of the policy. We characterize the degree of vulnerability across tasks and training algorithms, for a subclass of  adversarial-example attacks in white-box and black-box settings. Regardless of the learned task or training algorithm, we observe a  significant drop in performance, even with small adversarial perturbations that do not interfere with human perception. Videos are available at http://rll.berkeley.edu/adversarial .", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795995", "vector": [], "sparse_vector": [], "title": "An Information-Theoretic Framework for Fast and Robust Unsupervised Learning via Neural Population Infomax.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A framework is presented for unsupervised learning of representations based on infomax principle for large-scale neural populations. We use an asymptotic approximation to the Shannon's mutual information for a large neural population to demonstrate that a good initial approximation to the global information-theoretic optimum can be obtained by a hierarchical infomax method. Starting from the initial solution, an efficient algorithm based on gradient descent of the final objective function is proposed to learn representations from the input datasets, and the method works for complete, overcomplete, and undercomplete bases. As confirmed by numerical experiments, our method is robust and highly efficient for extracting salient features from input datasets. Compared with the main existing methods, our algorithm has a distinct advantage in both the training speed and the robustness of unsupervised representation learning. Furthermore, the proposed method is easily extended to the supervised or unsupervised model for training deep structure networks.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795996", "vector": [], "sparse_vector": [], "title": "Tying Word Vectors and Word Classifiers: A Loss Framework for Language Modeling.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recurrent neural networks have been very successful at predicting sequences of words in tasks such as language modeling. However, all such models are based on the conventional classification framework, where the model is trained against one-hot targets, and each word is represented both as an input and as an output in isolation. This causes inefficiencies in learning both in terms of utilizing all of the information and in terms of the number of parameters needed to train. We introduce a novel theoretical framework that facilitates better learning in language modeling, and show that our framework leads to tying together the input embedding and the output projection matrices, greatly reducing the number of trainable variables. Our framework leads to state of the art performance on the Penn Treebank with a variety of network models.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795997", "vector": [], "sparse_vector": [], "title": "Reinforcement Learning with Unsupervised Auxiliary Tasks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Deep reinforcement learning agents have achieved state-of-the-art results by directly maximising cumulative reward. However, environments contain a much wider variety of possible training signals. In this paper, we introduce an agent that also maximises many other pseudo-reward functions simultaneously by reinforcement learning. All of these tasks share a common representation that, like unsupervised learning, continues to develop in the absence of extrinsic rewards. We also introduce a novel mechanism for focusing this representation upon extrinsic rewards, so that learning can rapidly adapt to the most relevant aspects of the actual task. Our agent significantly outperforms the previous state-of-the-art on Atari, averaging 880\\% expert human performance, and a challenging suite of first-person, three-dimensional \\emph{Labyrinth} tasks leading to a mean speedup in learning of 10$\\times$ and averaging 87\\% expert human performance on Labyrinth.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795998", "vector": [], "sparse_vector": [], "title": "Online Bayesian Transfer Learning for Sequential Data Modeling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider the problem of inferring a sequence of hidden states associated with a sequence of observations produced by an individual within a population.  Instead of learning a single sequence model for the population (which does not account for variations within the population), we learn a set of basis sequence models based on different individuals.  The sequence of hidden states for a new individual is inferred in an online fashion by estimating a distribution over the basis models that best explain the sequence of observations of this new individual.  We explain how to do this in the context of hidden Markov models with Gaussian mixture models that are learned based on streaming data by online Bayesian moment matching.  The resulting transfer learning technique is demonstrated with three real-word applications: activity recognition based on smartphone sensors, sleep classification based on electroencephalography data and the prediction of the direction of future packet flows between a pair of servers in telecommunication networks.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3795999", "vector": [], "sparse_vector": [], "title": "Categorical Reparameterization with Gumbel-Softmax.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Categorical variables are a natural choice for representing discrete structure in the world. However, stochastic neural networks rarely use categorical latent variables due to the inability to backpropagate through samples. In this work, we present an efficient gradient estimator that replaces the non-differentiable sample from a categorical distribution with a differentiable sample from a novel Gumbel-Softmax distribution. This distribution has the essential property that it can be smoothly annealed into a categorical distribution. We show that our Gumbel-Softmax estimator outperforms state-of-the-art gradient estimators on structured output prediction and unsupervised generative modeling tasks with categorical latent variables, and enables large speedups on semi-supervised classification.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796000", "vector": [], "sparse_vector": [], "title": "Tuning Recurrent Neural Networks with Reinforcement Learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The approach of training sequence models using supervised learning and next-step prediction  suffers from known failure modes. For example, it is notoriously difficult to ensure multi-step generated sequences have coherent global structure. We propose a novel sequence-learning approach in which we use a pre-trained Recurrent Neural Network (RNN) to supply part of the reward value in a Reinforcement Learning (RL) model. Thus, we can refine a sequence predictor by optimizing for some imposed reward functions, while maintaining good predictive properties learned from data. We propose efficient ways to solve this by augmenting deep Q-learning with a cross-entropy reward and deriving novel off-policy methods for RNNs from KL control. We explore the usefulness of our approach in the context of music generation. An LSTM is trained\non a large corpus of songs to predict the next note in a musical sequence. This Note-RNN is then refined using our method and rules of music theory. We show that by combining maximum likelihood (ML) and RL in this way, we can not only produce more pleasing melodies, but  significantly reduce unwanted behaviors and failure modes of the RNN, while maintaining information learned from data.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796001", "vector": [], "sparse_vector": [], "title": "Variable Computation in Recurrent Neural Networks.", "authors": ["Yacine Jernite", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Recurrent neural networks (RNNs) have been used extensively and with increasing success to model various types of sequential data. Much of this progress has been achieved through devising recurrent units and architectures with the flexibility to capture complex statistics in the data, such as long range dependency or localized attention phenomena. However, while many sequential data (such as video, speech or language) can have highly variable information flow, most recurrent models still consume input features at a constant rate and perform a constant number of computations per time step, which can be detrimental to both speed and model capacity. In this paper, we explore a modification to existing recurrent units which allows them to learn to vary the amount of computation they perform at each step, without prior knowledge of the sequence's time structure. We show experimentally that not only do our models require fewer operations, they also lead to better performance overall on evaluation tasks.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796002", "vector": [], "sparse_vector": [], "title": "Learning Graphical State Transitions.", "authors": ["<PERSON>"], "summary": "Graph-structured data is important in modeling relationships between multiple entities, and can be used to represent states of the world as well as many data structures. <PERSON> et al. (2016) describe a model known as a Gated Graph Sequence Neural Network (GGS-NN) that produces sequences from graph-structured input. In this work I introduce the Gated Graph Transformer Neural Network (GGT-NN), an extension of GGS-NNs that uses graph-structured data as an intermediate representation. The model can learn to construct and modify graphs in sophisticated ways based on textual input, and also to use the graphs to produce a variety of outputs. For example, the model successfully learns to solve almost all of the bAbI tasks (<PERSON> et al., 2016), and also discovers the rules governing graphical formulations of a simple cellular automaton and a family of Turing machines.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796003", "vector": [], "sparse_vector": [], "title": "Learning to Remember Rare Events.", "authors": ["Lukasz Kaiser", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Despite recent advances, memory-augmented deep neural networks are still limited\nwhen it comes to life-long and one-shot learning, especially in remembering rare events.\nWe present a large-scale life-long memory module for use in deep learning.\nThe module exploits fast nearest-neighbor algorithms for efficiency and\nthus scales to large memory sizes.\nExcept for the nearest-neighbor query, the module is fully differentiable\nand trained end-to-end with no extra supervision.  It operates in\na life-long manner, i.e., without the need to reset it during training.\n\nOur memory module can be easily added to any part of a supervised neural network.\nTo show its versatility we add it to a number of networks, from simple\nconvolutional ones tested on image classification to deep sequence-to-sequence\nand recurrent-convolutional models.\nIn all cases, the enhanced network gains the ability to remember\nand do life-long one-shot learning.\nOur module remembers training examples shown many thousands\nof steps in the past and it can successfully generalize from them.\nWe set new state-of-the-art for one-shot learning on the Omniglot dataset\nand demonstrate, for the first time, life-long one-shot learning in\nrecurrent neural networks on a large-scale machine translation task.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796004", "vector": [], "sparse_vector": [], "title": "HolStep: A Machine Learning Dataset for Higher-order Logic Theorem Proving.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Large computer-understandable proofs consist of millions of intermediate\nlogical steps. The vast majority of such steps originate from manually\nselected and manually guided heuristics applied to intermediate goals.\nSo far, machine learning has generally not been used to filter or\ngenerate these steps. In this paper, we introduce a new dataset based on\nHigher-Order Logic (HOL) proofs, for the purpose of developing new\nmachine learning-based theorem-proving strategies. We make this dataset\npublicly available under the BSD license. We propose various machine\nlearning tasks that can be performed on this dataset, and discuss their\nsignificance for theorem proving. We also benchmark a set of simple baseline\nmachine learning models suited for the tasks (including logistic regression\nconvolutional neural networks and recurrent neural networks). The results of our\nbaseline models show the promise of applying machine learning to HOL\ntheorem proving.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796005", "vector": [], "sparse_vector": [], "title": "Batch Policy Gradient Methods for Improving Neural Conversation Models.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study reinforcement learning of chat-bots with recurrent neural network\narchitectures when the rewards are noisy and expensive to\nobtain. For instance, a chat-bot used in automated customer service support can\nbe scored by quality assurance agents, but this process can be expensive, time consuming\nand noisy. \nPrevious reinforcement learning work for natural language uses on-policy updates\nand/or is designed for on-line learning settings.\nWe demonstrate empirically that such strategies are not appropriate for this setting\nand develop an off-policy batch policy gradient method (\\bpg).\nWe demonstrate the efficacy of our method via a series of\nsynthetic experiments and an Amazon Mechanical Turk experiment on\na restaurant recommendations dataset.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796006", "vector": [], "sparse_vector": [], "title": "Deep Variational Bayes Filters: Unsupervised Learning of State Space Models from Raw Data.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce Deep Variational Bayes Filters (DVBF), a new method for unsupervised learning and identification of latent Markovian state space models. Leveraging recent advances in Stochastic Gradient Variational Bayes, DVBF can overcome intractable inference distributions via variational inference. Thus, it can handle highly nonlinear input data with temporal and spatial dependencies such as image sequences without domain knowledge. Our experiments show that enabling backpropagation through transitions enforces state space assumptions and significantly improves information content of the latent embedding. This also enables realistic long-term prediction.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796007", "vector": [], "sparse_vector": [], "title": "On Large-Batch Training for Deep Learning: Generalization Gap and Sharp Minima.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The stochastic gradient descent (SGD) method and its variants are algorithms of choice for many Deep Learning tasks. These methods operate in a small-batch regime wherein a fraction of the training data,  say $32$--$512$ data points, is sampled to compute an approximation to the gradient. It has been observed in practice that when using a larger batch there is a  degradation in the quality of the model, as measured by its ability to generalize. We investigate the cause for this generalization drop in the large-batch regime and present numerical evidence that supports the view that large-batch methods tend to converge to sharp minimizers of the training and testing functions---and as is well known, sharp minima lead to poorer generalization. In contrast, small-batch methods consistently converge to flat minimizers, and our experiments support a commonly held view that this is due to the inherent noise in the gradient estimation. We  discuss several  strategies to attempt to help large-batch methods eliminate this generalization gap.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796008", "vector": [], "sparse_vector": [], "title": "Structured Attention Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Attention networks have proven to be an effective approach for embedding categorical inference within a deep neural network. However, for many tasks we may want to model richer structural dependencies without abandoning end-to-end training. In this work, we experiment with incorporating richer structural distributions, encoded using graphical models, within deep networks. We show that these structured attention networks are simple extensions of the basic attention procedure, and that they allow for extending attention  beyond the standard soft-selection approach, such as attending to partial segmentations or to subtrees. We experiment with two different classes of structured attention networks: a linear-chain conditional random field and a graph-based parsing model, and describe how these models can be practically implemented as neural network layers. Experiments show that this approach is effective for incorporating structural biases, and structured attention networks outperform baseline attention models on a variety of synthetic and real tasks: tree transduction, neural machine translation, question answering, and natural language inference. We further find that models trained in this way learn interesting unsupervised hidden representations that generalize simple attention.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796009", "vector": [], "sparse_vector": [], "title": "Transferring Knowledge to Smaller Network with Class-Distance Loss.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Training a network with small capacity that can perform as well as a larger capacity network is an important problem that needs to be solved in real life applications which require fast inference time and small memory requirement. Previous approaches that transfer knowledge from a bigger network to a smaller network show little benefit when applied to state-of-the-art convolutional neural network architectures such as Residual Network trained with batch normalization. We propose class-distance loss that helps teacher networks to form densely clustered vector space to make it easy for the student network to learn from it. We show that a small network with half the size of the original network trained with the proposed strategy can perform close to the original network on CIFAR-10 dataset.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796010", "vector": [], "sparse_vector": [], "title": "Hadamard Product for Low-rank Bilinear Pooling.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "K<PERSON>ung Woon On", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Bilinear models provide rich representations compared with linear models. They have been applied in various visual tasks, such as object recognition, segmentation, and visual question-answering, to get state-of-the-art performances taking advantage of the expanded representations. However, bilinear representations tend to be high-dimensional, limiting the applicability to computationally complex tasks. We propose low-rank bilinear pooling using <PERSON><PERSON>rd product for an efficient attention mechanism of multimodal learning. We show that our model outperforms compact bilinear pooling in visual question-answering tasks with the state-of-the-art results on the VQA dataset, having a better parsimonious property.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796011", "vector": [], "sparse_vector": [], "title": "Semi-Supervised Classification with Graph Convolutional Networks.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We present a scalable approach for semi-supervised learning on graph-structured data that is based on an efficient variant of convolutional neural networks which operate directly on graphs. We motivate the choice of our convolutional architecture via a localized first-order approximation of spectral graph convolutions. Our model scales linearly in the number of graph edges and learns hidden layer representations that encode both local graph structure and features of nodes. In a number of experiments on citation networks and on a knowledge graph dataset we demonstrate that our approach outperforms related methods by a significant margin.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796012", "vector": [], "sparse_vector": [], "title": "Learning Curve Prediction with Bayesian Neural Networks.", "authors": ["<PERSON>", "<PERSON>", "Jost <PERSON>", "<PERSON>"], "summary": "Different neural network architectures, hyperparameters and training protocols lead to different performances as a function of time.\nHuman experts routinely inspect the resulting learning curves to quickly terminate runs with poor hyperparameter settings and thereby considerably speed up manual hyperparameter optimization. Exploiting the same information in automatic Bayesian hyperparameter optimization requires a probabilistic model of learning curves across hyperparameter settings. Here, we study the use of Bayesian neural networks for this purpose and improve their performance by a specialized learning curve layer.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796013", "vector": [], "sparse_vector": [], "title": "Delving into adversarial attacks on deep policies.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Adversarial examples have been shown to exist for a variety of deep learning architectures. Deep reinforcement learning has shown promising results on training agent policies directly on raw inputs such as image pixels. In this paper we present a novel study into adversarial attacks on deep reinforcement learning polices. We compare the effectiveness of the attacks using adversarial examples vs. random noise. We present a novel method for reducing the number of times adversarial examples need to be injected for a successful attack, based on the value function. We further explore how re-training on random noise and FGSM perturbations affects the resilience against adversarial examples.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796014", "vector": [], "sparse_vector": [], "title": "Multiplicative LSTM for sequence modelling.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce multiplicative LSTM (mLSTM), a novel recurrent neural network\narchitecture for sequence modelling that combines the long short-term memory\n(LSTM) and multiplicative recurrent neural network architectures. mLSTM is\ncharacterised by its ability to have different recurrent transition functions for each\npossible input, which we argue makes it more expressive for autoregressive density\nestimation. We demonstrate empirically that mLSTM outperforms standard LSTM\nand its deep variants for a range of character level modelling tasks, and that this\nimprovement increases with the complexity of the task. This model achieves a\ntest error of 1.19 bits/character on the last 4 million characters of the Hutter prize\ndataset when combined with dynamic evaluation.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796015", "vector": [], "sparse_vector": [], "title": "Deep Nets Don&apos;t Learn via Memorization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>rz<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We use empirical methods to argue that deep neural networks (DNNs) do not achieve their performance by \\textit{memorizing} training data, in spite of overly-expressive model architectures. \nInstead, they learn a simple available hypothesis that fits the finite data samples.\nIn support of this view, we establish that there are qualitative differences when learning noise vs.~natural datasets, showing that: (1) more capacity is needed to fit noise, (2) time to convergence is longer for random labels, but \\emph{shorter} for random inputs, and (3) DNNs trained on real data examples learn simpler functions than when trained with noise data, as measured by the sharpness of the loss function at convergence.\nFinally, we demonstrate that for appropriately tuned explicit regularization, e.g.~dropout, we can degrade DNN training performance on noise datasets without compromising generalization on real data.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796016", "vector": [], "sparse_vector": [], "title": "Zoneout: Regularizing RNNs by Randomly Preserving Hidden Activations.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose zoneout, a novel method for regularizing RNNs.\nAt each timestep, zoneout stochastically forces some hidden units to maintain their previous values.\nLike dropout, zoneout uses random noise to train a pseudo-ensemble, improving generalization.\nBut by preserving instead of dropping hidden units, gradient information and state information are more readily propagated through time, as in feedforward stochastic depth networks.\nWe perform an empirical investigation of various RNN regularizers, and find that zoneout gives significant performance improvements across tasks. We achieve competitive results with relatively simple models in character- and word-level language modelling on the Penn Treebank and Text8 datasets, and combining with recurrent batch normalization yields state-of-the-art results on permuted sequential MNIST.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796017", "vector": [], "sparse_vector": [], "title": "Factorization tricks for LSTM networks.", "authors": ["Oleks<PERSON>cha<PERSON>v", "<PERSON>"], "summary": "Large Long Short-Term Memory (LSTM) networks have tens of millions of parameters and they are very expensive to train. We present two simple ways of reducing the number of parameters in LSTM network: the first one is ”matrix factorization by design” of LSTM matrix into the product of two smaller matrices, and the second one is partitioning of LSTM matrix, its inputs and states into the independent groups. Both approaches allow us to train large LSTM networks significantly faster to the state-of the art perplexity. On the One Billion Word Benchmark we improve single model perplexity down to 24.29.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796018", "vector": [], "sparse_vector": [], "title": "Audio Super-Resolution using Neural Networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We propose a neural network-based technique for enhancing the quality of audio signals such as speech or music by transforming inputs encoded at low sampling rates into higher-quality signals with an increased resolution in the time domain. This amounts to generating the missing samples within the low-resolution signal in a process akin to image super-resolution. On standard speech and music datasets, this approach outperforms baselines at 2x, 4x, and 6x upscaling ratios. The method has practical applications in telephony, compression, and text-to-speech generation; it can also be used to improve the scalability of recently-proposed generative models of audio.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796019", "vector": [], "sparse_vector": [], "title": "Adversarial Machine Learning at Scale.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Adversarial examples are malicious inputs designed to fool machine learning models.\nThey often transfer from one model to another, allowing attackers to mount black\nbox attacks without knowledge of the target model's parameters.\nAdversarial training is the process of explicitly training a model on adversarial\nexamples, in order to make it more robust to attack or to reduce its test error\non clean inputs.\nSo far, adversarial training has primarily been applied to small problems.\nIn this research, we apply adversarial training to ImageNet.\nOur contributions include:\n(1) recommendations for how to succesfully scale adversarial training to large models and datasets,\n(2) the observation that adversarial training confers robustness to single-step attack methods,\n(3) the finding that multi-step attack methods are somewhat less transferable than single-step attack\n      methods, so single-step attacks are the best for mounting black-box attacks,\n      and\n(4) resolution of a ``label leaking'' effect that causes adversarially trained models to perform\n      better on adversarial examples than on clean examples, because the adversarial\n      example construction process uses the true label and the model can learn to\n      exploit regularities in the construction process.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796020", "vector": [], "sparse_vector": [], "title": "Adversarial examples in the physical world.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Most existing machine learning classifiers are highly vulnerable to adversarial examples.\nAn adversarial example is a sample of input data which has been modified\nvery slightly in a way that is intended to cause a machine learning classifier\nto misclassify it.\nIn many cases, these modifications can be so subtle that a human observer does\nnot even notice the modification at all, yet the classifier still makes a mistake.\nAdversarial examples pose security concerns\nbecause they could be used to perform an attack on machine learning systems, even if the adversary has no\naccess to the underlying model.\nUp to now, all previous work has assumed a threat model in which the adversary can\nfeed data directly into the machine learning classifier.\nThis is not always the case for systems operating in the physical world,\nfor example those which are using signals from cameras and other sensors as input.\nThis paper shows that even in such physical world scenarios, machine learning systems are vulnerable\nto adversarial examples.\nWe demonstrate this by feeding adversarial images obtained from a cell-phone camera\nto an ImageNet Inception classifier and measuring the classification accuracy of the system.\nWe find that a large fraction of adversarial examples are classified incorrectly\neven when perceived through the camera.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796021", "vector": [], "sparse_vector": [], "title": "Temporal Ensembling for Semi-Supervised Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we present a simple and efficient method for training deep neural networks in a semi-supervised setting where only a small portion of training data is labeled. We introduce self-ensembling, where we form a consensus prediction of the unknown labels using the outputs of the network-in-training on different epochs, and most importantly, under different regularization and input augmentation conditions. This ensemble prediction can be expected to be a better predictor for the unknown labels than the output of the network at the most recent training epoch, and can thus be used as a target for training. Using our method, we set new records for two standard semi-supervised learning benchmarks, reducing the (non-augmented) classification error rate from 18.44% to 7.05% in SVHN with 500 labels and from 18.63% to 16.55% in CIFAR-10 with 4000 labels, and further to 5.12% and 12.16% by enabling the standard augmentations. We additionally obtain a clear improvement in CIFAR-100 classification accuracy by using random images from the Tiny Images dataset as unlabeled extra inputs during training. Finally, we demonstrate good tolerance to incorrect labels.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796022", "vector": [], "sparse_vector": [], "title": "Memory Matching Networks for Genomic Sequence Classification.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "When analyzing the genome, researchers have discovered that proteins bind to DNA based on certain patterns on the DNA sequence known as \"motifs\". However, it is difficult to manually construct motifs for protein binding location prediction due to their complexity. Recently, external learned memory models have proven to be effective methods for reasoning over inputs and supporting sets. In this work, we present memory matching networks (MMN) for classifying DNA sequences as protein binding sites. Our model learns a memory bank of encoded motifs, which are dynamic memory modules, and then matches a new test sequence to each of the motifs to classify the sequence as a binding or non-binding site.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796023", "vector": [], "sparse_vector": [], "title": "FractalNet: Ultra-Deep Neural Networks without Residuals.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce a design strategy for neural network macro-architecture based on self-similarity.  Repeated application of a simple expansion rule generates deep networks whose structural layouts are precisely truncated fractals.  These networks contain interacting subpaths of different lengths, but do not include any pass-through or residual connections; every internal signal is transformed by a filter and nonlinearity before being seen by subsequent layers.  In experiments, fractal networks match the excellent performance of standard residual networks on both CIFAR and ImageNet classification tasks, thereby demonstrating that residual representations may not be fundamental to the success of extremely deep convolutional neural networks.  Rather, the key may be the ability to transition, during training, from effectively shallow to deep.  We note similarities with student-teacher behavior and develop drop-path, a natural extension of dropout, to regularize co-adaptation of subpaths in fractal architectures.  Such regularization allows extraction of high-performance fixed-depth subnetworks.  Additionally, fractal networks exhibit an anytime property: shallow subnetworks provide a quick answer, while deeper subnetworks, with higher latency, provide a more accurate answer.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796024", "vector": [], "sparse_vector": [], "title": "Recurrent Normalization Propagation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose an LSTM parametrization  that preserves the means and variances of the hidden states and memory cells across time. While having training benefits similar to Recurrent Batch Normalization and Layer Normalization, it does not need to estimate statistics at each time step,  therefore, requiring fewer computations overall. We also investigate the parametrization impact on the gradient flows and  present a way of initializing the weights accordingly.\n\nWe evaluate our proposal on language modelling and image generative modelling tasks. We empirically show that it performs similarly or better than other recurrent normalization approaches, while being faster to execute.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796025", "vector": [], "sparse_vector": [], "title": "Multi-Agent Cooperation and the Emergence of (Natural) Language.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The current mainstream approach to train natural language systems is to expose them to large amounts of text. This passive learning is problematic if we are in- terested in developing interactive machines, such as conversational agents. We propose a framework for language learning that relies on multi-agent communi- cation. We study this learning in the context of referential games. In these games, a sender and a receiver see a pair of images. The sender is told one of them is the target and is allowed to send a message to the receiver, while the receiver must rely on it to identify the target. Thus, the agents develop their own language interactively out of the need to communicate. We show that two networks with simple configurations are able to learn to coordinate in the referential game. We further explore whether the “word meanings” induced in the game reflect intuitive semantic properties of the objects depicted in the image, and we present a simple strategy for grounding the agents’ code into natural language, a necessary step in developing machines that should eventually be able to communicate with humans.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796026", "vector": [], "sparse_vector": [], "title": "Learning Visual Servoing with Deep Features and Fitted Q-Iteration.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Visual servoing involves choosing actions that move a robot in response to observations from a camera, in order to reach a goal configuration in the world. Standard visual servoing approaches typically rely on manually designed features and analytical dynamics models, which limits their generalization capability and often requires extensive application-specific feature and model engineering. In this work, we study how learned visual features, learned predictive dynamics models, and reinforcement learning can be combined to learn visual servoing mechanisms. We focus on target following, with the goal of designing algorithms that can learn a visual servo using low amounts of data of the target in question, to enable quick adaptation to new targets. Our approach is based on servoing the camera in the space of learned visual features, rather than image pixels or manually-designed keypoints. We demonstrate that standard deep features, in our case taken from a model trained for object classification, can be used together with a bilinear predictive model to learn an effective visual servo that is robust to visual variation, changes in viewing angle and appearance, and occlusions. A key component of our approach is to use a sample-efficient fitted Q-iteration algorithm to learn which features are best suited for the task at hand. We show that we can learn an effective visual servo on a complex synthetic car following benchmark using just 20 training trajectory samples for reinforcement learning. We demonstrate substantial improvement over a conventional approach based on image pixels or hand-designed keypoints, and we show an improvement in sample-efficiency of more than two orders of magnitude over standard model-free deep reinforcement learning algorithms. Videos are available at http://rll.berkeley.edu/visual_servoing.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796027", "vector": [], "sparse_vector": [], "title": "Hyperband: Bandit-Based Configuration Evaluation for Hyperparameter Optimization.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Giulia DeSalvo", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Performance of machine learning algorithms depends critically on identifying a good set of hyperparameters.  While recent approaches use Bayesian Optimization to adaptively select configurations, we focus on speeding up random search through adaptive resource allocation.  We present Hyperband,  a novel algorithm for hyperparameter optimization that is simple, flexible, and theoretically sound.  Hyperband is a principled early-stoppping method that adaptively allocates a predefined resource, e.g., iterations, data samples or number of features, to randomly sampled configurations.  We compare Hyperband with state-of-the-art Bayesian Optimization methods on several hyperparameter optimization problems.  We observe that Hyperband can provide over an order of magnitude speedups over competitors on a variety of neural network and kernel-based learning problems.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796028", "vector": [], "sparse_vector": [], "title": "Filter shaping for Convolutional Neural Networks.", "authors": ["<PERSON><PERSON>yi Li", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Convolutional neural networks (CNNs) are powerful tools for classification of visual inputs. An important property of CNN is its restriction to local connections and sharing of local weights among different locations. In this paper, we consider the definition of appropriate local neighborhoods in CNN. We provide a theoretical analysis that justifies the traditional square filter used in CNN for analyzing natural images. The analysis also provides a principle for designing customized filter shapes for application domains that do not resemble natural images. We propose an approach that automatically designs multiple layers of different customized filter shapes by repeatedly solving lasso problems. It is applied to customize the filter shape for both bioacoustic applications and gene sequence analysis applications. In those domains with small sample sizes we demonstrate that the customized filters achieve superior classification accuracy, improved convergence behavior in training and reduced sensitivity to hyperparameters.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796029", "vector": [], "sparse_vector": [], "title": "Learning to Optimize.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Algorithm design is a laborious process and often requires many iterations of ideation and validation. In this paper, we explore automating algorithm design and present a method to learn an optimization algorithm. We approach this problem from a reinforcement learning perspective and represent any particular optimization algorithm as a policy. We learn an optimization algorithm using guided policy search and demonstrate that the resulting algorithm outperforms existing hand-engineered algorithms in terms of convergence speed and/or the final objective value.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796030", "vector": [], "sparse_vector": [], "title": "Dialogue Learning With Human-in-the-Loop.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Marc&a<PERSON>s;<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "An important aspect of developing conversational agents is to give a bot the ability to improve through communicating with humans and to learn from the mistakes that it makes.  Most research has focused on learning from fixed training sets of labeled data rather than interacting with a dialogue partner in an online fashion. In this paper we explore this direction in a reinforcement learning setting where the bot improves its question-answering ability from feedback a teacher gives following its generated responses. We build a simulator that tests various aspects of such learning in a synthetic environment, and introduce models that work in this regime.  Finally, real experiments with Mechanical Turk validate the approach.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796031", "vector": [], "sparse_vector": [], "title": "Learning through Dialogue Interactions by Asking Questions.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Marc&a<PERSON>s;<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A good dialogue agent should have the ability to interact with users by both responding to questions and by asking questions, and importantly to learn from both types of interactions. In this work, we explore this direction by designing a simulator and a set of synthetic tasks in the movie domain that allow such interactions between a learner and a teacher. We investigate how a learner can benefit from asking questions in both offline and online reinforcement learning settings, and demonstrate that the learner improves when asking questions. Our work represents a first step in developing such end-to-end learned interactive dialogue agents.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796032", "vector": [], "sparse_vector": [], "title": "Neural Program Lattices.", "authors": ["Cheng<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose the Neural Program Lattice (NPL), a neural network that learns to perform complex tasks by composing low-level programs to express high-level programs. Our starting point is the recent work on Neural Programmer-Interpreters (NPI), which can only learn from strong supervision that contains the whole hierarchy of low-level and high-level programs. NPLs remove this limitation by providing the ability to learn from weak supervision consisting only of sequences of low-level operations. We demonstrate the capability of NPL to learn to perform long-hand addition and arrange blocks in a grid-world environment. Experiments show that it performs on par with NPI while using weak supervision in place of most of the strong supervision, thus indicating its ability to infer the high-level program structure from examples containing only the low-level operations.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796033", "vector": [], "sparse_vector": [], "title": "Revisiting Batch Normalization For Practical Domain Adaptation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep neural networks (DNN) have shown unprecedented success in various computer vision applications such as image classification and object detection. However, it is still a common annoyance during the training phase, that one has to prepare at least thousands of labeled images to fine-tune a network to a specific domain. Recent study shows that a DNN has strong dependency towards the training dataset, and the learned features cannot be easily transferred to a different but relevant task without fine-tuning. In this paper, we propose a simple yet powerful remedy, called Adaptive Batch Normalization (AdaBN) to increase the generalization ability of a DNN. By modulating the statistics from the source domain to the target domain in all Batch Normalization layers across the network, our approach achieves deep adaptation effect for domain adaptation tasks. In contrary to other deep learning domain adaptation methods, our method does not require additional components, and is parameter-free. It archives state-of-the-art performance despite its surprising simplicity. Furthermore, we demonstrate that our method is complementary with other existing methods.  Combining AdaBN with existing domain adaptation treatments may further improve model performance.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796034", "vector": [], "sparse_vector": [], "title": "Why Deep Neural Networks for Function Approximation?", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recently there has been much interest in understanding why deep neural networks are preferred to shallow networks. We show that, for a large class of piecewise smooth functions, the number of neurons needed by a shallow network to approximate a function is exponentially larger than the corresponding number of neurons needed by a deep network for a given degree of function approximation. First, we consider univariate functions on a bounded interval and require a neural network to achieve an approximation error of $\\varepsilon$ uniformly over the interval. We show that shallow networks (i.e., networks whose depth does not depend on $\\varepsilon$) require $\\Omega(\\text{poly}(1/\\varepsilon))$ neurons while deep networks (i.e., networks whose depth grows with $1/\\varepsilon$) require $\\mathcal{O}(\\text{polylog}(1/\\varepsilon))$ neurons. We then extend these results to certain classes of important multivariate functions. Our results are derived for neural networks which use a combination of rectifier linear units (ReLUs) and binary step units, two of the most popular type of activation functions. Our analysis builds on a simple observation: the multiplication of two bits can be represented by a ReLU.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796035", "vector": [], "sparse_vector": [], "title": "A Structured Self-Attentive Sentence Embedding.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper proposes a new model for extracting an interpretable sentence embedding by introducing self-attention. Instead of using a vector, we use a 2-D matrix to represent the embedding, with each row of the matrix attending on a different part of the sentence. We also propose a self-attention mechanism and a special regularization term for the model. As a side effect, the embedding comes with an easy way of visualizing what specific parts of the sentence are encoded into the embedding. We evaluate our model on 3 different tasks: author profiling, sentiment classification and textual entailment. Results show that our model yields a significant performance gain compared to other sentence embedding methods in all of the 3 tasks.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796036", "vector": [], "sparse_vector": [], "title": "Tactics of Adversarial Attack on Deep Reinforcement Learning Agents.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We introduce two novel tactics for adversarial attack on deep reinforcement learning (RL) agents: strategically-timed and enchanting attack. For strategically- timed attack, our method selectively forces the deep RL agent to take the least likely action. For enchanting attack, our method lures the agent to a target state by staging a sequence of adversarial attacks. We show that both DQN and A3C agents are vulnerable to our proposed tactics of adversarial attack.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796037", "vector": [], "sparse_vector": [], "title": "Transfer of View-manifold Learning to Similarity Perception of Novel Objects.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We develop a model of perceptual similarity judgment based on re-training a deep convolution neural network (DCNN) that learns to associate different views of each 3D object to capture the notion of object persistence and continuity in our visual experience. The re-training process effectively performs distance metric learning under the object persistency constraints, to modify the view-manifold of object representations. It reduces the effective distance between the representations of different views of the same object without compromising the distance between those of the views of different objects, resulting in the untangling of the view-manifolds between individual objects within the same category and across categories. This untangling enables the model to discriminate and recognize objects within the same category, independent of viewpoints. We found that this ability is not limited to the trained objects, but transfers to novel objects in both trained and untrained categories, as well as to a variety of completely novel artificial synthetic objects. This transfer in learning suggests the modification of distance metrics in view- manifolds is more general and abstract, likely at the levels of parts, and independent of the specific objects or categories experienced during training. Interestingly, the resulting transformation of feature representation in the deep networks is found to significantly better match human perceptual similarity judgment than AlexNet, suggesting that object persistence could be an important constraint in the development of perceptual similarity judgment in biological neural networks.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796038", "vector": [], "sparse_vector": [], "title": "Precise Recovery of Latent Vectors from Generative Adversarial Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Generative adversarial networks (GANs) transform latent vectors into visually plausible images.  It is generally thought that the original GAN formulation gives no out-of-the-box method to reverse the mapping, projecting images back into latent space. We introduce a simple, gradient-based technique called stochastic clipping. In experiments, for images generated by the GAN, we exactly recover their latent vector pre-images 100% of the time. Additional experiments demonstrate that this method is robust to noise. Finally, we show that even for unseen images, our method appears to recover unique encodings.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796039", "vector": [], "sparse_vector": [], "title": "Delving into Transferable Adversarial Examples and Black-box Attacks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "An intriguing property of deep neural networks is the existence of adversarial examples, which can transfer among different architectures. These transferable adversarial examples may severely hinder deep neural network-based applications. Previous works mostly study the transferability using small scale datasets. In this work, we are the first to conduct an extensive study of the transferability over large models and a large scale dataset, and we are also the first to study the transferability of targeted adversarial examples with their target labels. We study both non-targeted and targeted adversarial examples, and show that while transferable non-targeted adversarial examples are easy to find, targeted adversarial examples generated using existing approaches almost never transfer with their target labels. Therefore, we propose novel ensemble-based approaches to generating transferable adversarial examples. Using such approaches, we observe a large proportion of targeted adversarial examples that are able to transfer with their target labels for the first time. We also present some geometric studies to help understanding the transferable adversarial examples. Finally, we show that the adversarial examples generated using ensemble-based approaches can successfully attack Clarifai.com, which is a black-box image classification system.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796040", "vector": [], "sparse_vector": [], "title": "Efficient Sparse-Winograd Convolutional Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Convolutional Neural Networks (CNNs) are compute intensive which limits their application on mobile devices. Their energy is dominated by the number of multiplies needed to perform the convolutions. Winograd’s minimal filtering algorithm (<PERSON><PERSON> (2015)) and network pruning (<PERSON> et al. (2015)) reduce the operation count. Unfortunately, these two methods cannot be combined—because applying theWinograd transform fills in the sparsity in both the weights and the activations. We propose two modifications to Winograd-based CNNs to enable these methods to exploit sparsity. First, we prune the weights in the ”Winograd domain” (after the transform) to exploit static weight sparsity. Second, we move the ReLU operation into the ”Winograd domain” to improve the sparsity of the transformed activations. On CIFAR-10, our method reduces the number of multiplications in the VGG-nagadomi model by 10.2x with no loss of accuracy.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796041", "vector": [], "sparse_vector": [], "title": "Maximum Entropy Flow Networks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Maximum entropy modeling is a flexible and popular framework for formulating statistical models given partial knowledge. In this paper, rather than the traditional method of optimizing over the continuous density directly, we learn a smooth and invertible transformation that maps a simple distribution to the desired maximum entropy distribution. Doing so is nontrivial in that the objective being maximized (entropy) is a function of the density itself.  By exploiting recent developments in normalizing flow networks, we cast the maximum entropy problem into a finite-dimensional constrained optimization, and solve the problem by combining stochastic optimization with the augmented Lagrangian method. Simulation results demonstrate the effectiveness of our method, and applications to finance and computer vision show the flexibility and accuracy of using maximum entropy flow networks.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796042", "vector": [], "sparse_vector": [], "title": "Deep Learning with Dynamic Computation Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Neural networks that compute over graph structures are a natural fit for problems in a variety of domains, including natural language (parse trees) and cheminformatics (molecular graphs). However, since the computation graph has a different shape and size for every input, such networks do not directly support batched training or inference. They are also difficult to implement in popular deep learning libraries, which are based on static data-flow graphs. We introduce a technique called dynamic batching, which not only batches together operations between different input graphs of dissimilar shape, but also between different nodes within a single input graph. The technique allows us to create static graphs, using popular libraries, that emulate dynamic computation graphs of arbitrary shape and size. We further present a high-level library of compositional blocks that simplifies the creation of dynamic graph models. Using the library, we demonstrate concise and batch-wise parallel implementations for a variety of models from the literature.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796043", "vector": [], "sparse_vector": [], "title": "Revisiting Classifier Two-Sample Tests.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The goal of two-sample tests is to assess whether two samples, $S_P \\sim P^n$ and $S_Q \\sim Q^m$, are drawn from the same distribution.  Perhaps intriguingly, one relatively unexplored method to build two-sample tests is the use of binary classifiers. In particular, construct a dataset by pairing the $n$ examples in $S_P$ with a positive label, and by pairing the $m$ examples in $S_Q$ with a negative label. If the null hypothesis ``$P = Q$'' is true, then the classification accuracy of a binary classifier on a held-out subset of this dataset should remain near chance-level.  As we will show, such \\emph{Classifier Two-Sample Tests} (C2ST) learn a suitable representation of the data on the fly, return test statistics in interpretable units, have a simple null distribution, and their predictive uncertainty allow to interpret where $P$ and $Q$ differ.\n\nThe goal of this paper is to establish the properties, performance, and uses of C2ST.  First, we analyze their main theoretical properties.  Second, we compare their performance against a variety of state-of-the-art alternatives.  Third, we propose their use to evaluate the sample quality of generative models with intractable likelihoods, such as Generative Adversarial Networks (GANs).  Fourth, we showcase the novel application of GANs together with C2ST for causal discovery.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796044", "vector": [], "sparse_vector": [], "title": "SGDR: Stochastic Gradient Descent with Warm Restarts.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Restart techniques are common in gradient-free optimization to deal with multimodal functions. Partial warm restarts are also gaining popularity in gradient-based optimization to improve the rate of convergence in accelerated gradient schemes to deal with ill-conditioned functions. In this paper, we propose a simple warm restart technique for stochastic gradient descent to improve its anytime performance when training deep neural networks. We empirically study its performance on the CIFAR-10 and CIFAR-100 datasets,   \nwhere we demonstrate new state-of-the-art results at 3.14\\% and 16.21\\%, respectively. We also demonstrate its advantages on a dataset of EEG recordings and on a downsampled version of the ImageNet dataset. Our source code is available at \\\\ \\url{https://github.com/loshchil/SGDR}", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796045", "vector": [], "sparse_vector": [], "title": "Deep Predictive Coding Networks for Video Prediction and Unsupervised Learning.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "While great strides have been made in using deep learning algorithms to solve supervised learning tasks, the problem of unsupervised learning - leveraging unlabeled examples to learn about the structure of a domain - remains a difficult unsolved challenge. Here, we explore prediction of future frames in a video sequence as an unsupervised learning rule for learning about the structure of the visual world. We describe a predictive neural network (\"PredNet\") architecture that is inspired by the concept of \"predictive coding\" from the neuroscience literature. These networks learn to predict future frames in a video sequence, with each layer in the network making local predictions and only forwarding deviations from those predictions to subsequent network layers. We show that these networks are able to robustly learn to predict the movement of synthetic (rendered) objects, and that in doing so, the networks learn  internal representations that are useful for decoding latent object parameters (e.g. pose) that support object recognition with fewer training views. We also show that these networks can scale to complex natural image streams (car-mounted camera videos), capturing key aspects of both egocentric movement and the movement of objects in the visual scene, and the representation learned in this setting is useful for estimating the steering angle. These results suggest that prediction represents a powerful framework for unsupervised learning, allowing for implicit learning of object and scene structure.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796046", "vector": [], "sparse_vector": [], "title": "Towards an automatic Turing test: Learning to evaluate dialogue responses.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Automatically evaluating the quality of dialogue responses for unstructured domains is a challenging problem.   Unfortunately,  existing automatic evaluation metrics are biased and correlate very poorly with human judgements of response quality (<PERSON> et al., 2016). Yet having an accurate automatic evaluation procedure is crucial for dialogue research, as it allows rapid prototyping and testing of new models with fewer expensive human evaluations. In response to this challenge, we formulate automatic dialogue evaluation as a learning problem. We present an evaluation model (ADEM) that learns to predict human-like scores to input responses, using a new dataset of human response scores. We show that the ADEM model’s predictions correlate significantly, and at level much higher than word-overlap metrics such as BLEU, with human judgements at both the utterance and system-level.We also show that ADEM can generalize to evaluating dialogue models unseen during training, an important step for automatic dialogue evaluation.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796047", "vector": [], "sparse_vector": [], "title": "Dropout with Expectation-linear Regularization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Dropout, a simple and effective way to train deep neural networks, has led to a number of impressive empirical successes and spawned many recent theoretical investigations. However, the gap between dropout’s training and inference phases, introduced due to tractability considerations, has largely remained under-appreciated. In this work, we first formulate dropout as a tractable approximation of some latent variable model, leading to a clean view of parameter sharing and enabling further theoretical analysis. Then, we introduce (approximate) expectation-linear dropout neural networks, whose inference gap we are able to formally characterize. Algorithmically, we show that our proposed measure of the inference gap can be used to regularize the standard dropout training objective, resulting in an explicit control of the gap. Our method is as simple and efficient as standard dropout. We further prove the upper bounds on the loss in accuracy due to expectation-linearization, describe classes of input distributions that expectation-linearize easily. Experiments on three image classification benchmark datasets demonstrate that reducing the inference gap can indeed improve the performance consistently.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796048", "vector": [], "sparse_vector": [], "title": "Particle Value Functions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The policy gradients of the expected return objective can react slowly to rare rewards. Yet, in some cases agents may wish to emphasize the low or high returns regardless of their probability. Borrowing from the economics and control literature, we review the risk-sensitive value function that arises from an exponential utility and illustrate its effects on an example. This risk-sensitive value function is not always applicable to reinforcement learning problems, so we introduce the particle value function defined by a particle filter over the distributions of an agent's experience, which bounds the risk-sensitive one. We illustrate the benefit of the policy gradients of this objective in Cliffworld.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796049", "vector": [], "sparse_vector": [], "title": "The Concrete Distribution: A Continuous Relaxation of Discrete Random Variables.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The reparameterization trick enables optimizing large scale stochastic computation graphs via gradient descent. The essence of the trick is to refactor each stochastic node into a differentiable function of its parameters and a random variable with fixed distribution. After refactoring, the gradients of the loss propagated by the chain rule through the graph are low variance unbiased estimators of the gradients of the expected loss. While many continuous random variables have such reparameterizations, discrete random variables lack useful reparameterizations due to the discontinuous nature of discrete states. In this work we introduce Concrete random variables -- continuous relaxations of discrete random variables. The Concrete distribution is a new family of distributions with closed form densities and a simple reparameterization. Whenever a discrete stochastic node of a computation graph can be refactored into a one-hot bit representation that is treated continuously, Concrete stochastic nodes can be used with automatic differentiation to produce low-variance biased gradients of objectives (including objectives that depend on the log-probability of latent stochastic nodes) on the corresponding discrete graph. We demonstrate the effectiveness of Concrete relaxations on density estimation and structured prediction tasks using neural networks.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796050", "vector": [], "sparse_vector": [], "title": "Extrapolation and learning equations.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In classical machine learning, regression is treated as a black box process of identifying a suitable function from a hypothesis set without attempting to gain insight into the mechanism connecting inputs and outputs. In the natural sciences, however, finding an interpretable function for a phenomenon is the prime goal as it allows to understand and generalize results. This paper proposes a novel type of function learning network, called equation learner (EQL), that can learn analytical expressions and is able to extrapolate to unseen domains. It is implemented as an end-to-end differentiable feed-forward network and allows for efficient gradient based training. Due to sparsity regularization concise interpretable expressions can be obtained. Often the true underlying source expression is identified.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796051", "vector": [], "sparse_vector": [], "title": "Performance guarantees for transferring representations.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "A popular machine learning strategy is the transfer of a representation (i.e. a feature extraction function) learned on a source task to a target task. Examples include the re-use of neural network weights or word embeddings. Our work proposes novel and general sufficient conditions for the success of this approach. If the representation learned from the source task is fixed, we identify conditions on how the tasks relate to obtain an upper bound on target task risk via a VC dimension-based argument. We then consider using the representation from the source task to construct a prior, which is fine-tuned using target task data. We give a PAC-Bayes target task risk bound in this setting under suitable conditions. We show examples of our bounds using feedforward neural networks. Our results motivate a practical approach to weight sharing, which we validate with experiments.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796052", "vector": [], "sparse_vector": [], "title": "SampleRNN: An Unconditional End-to-End Neural Audio Generation Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper we propose a novel model for unconditional audio generation task that generates one audio sample at a time. We show that our model which profits from combining memory-less modules, namely autoregressive multilayer perceptron, and stateful recurrent neural networks in a hierarchical structure is de facto powerful to capture the underlying sources of variations in temporal domain for very long time on three datasets of different nature. Human evaluation on the generated samples indicate that our model is preferred over competing models. We also show how each component of the model contributes to the exhibited performance.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796053", "vector": [], "sparse_vector": [], "title": "Pointer Sentinel Mixture Models.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent neural network sequence models with softmax classifiers have achieved their best language modeling performance only with very large hidden states and large vocabularies. Even then they struggle to predict rare or unseen words even if the context makes the prediction unambiguous. We introduce the pointer sentinel mixture architecture for neural sequence models which has the ability to either reproduce a word from the recent context or produce a word from a standard softmax classifier. Our pointer sentinel-LSTM model achieves state of the art language modeling performance on the Penn Treebank (70.9 perplexity) while using far fewer parameters than a standard softmax LSTM. In order to evaluate how well language models can exploit longer contexts and deal with more realistic vocabularies and corpora we also introduce the freely available WikiText corpus.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796054", "vector": [], "sparse_vector": [], "title": "Unrolled Generative Adversarial Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce a method to stabilize Generative Adversarial Networks (GANs) by defining the generator objective with respect to an unrolled optimization of the discriminator. This allows training to be adjusted between using the optimal discriminator in the generator's objective, which is ideal but infeasible in practice, and using the current value of the discriminator, which is often unstable and leads to poor solutions. We show how this technique solves the common problem of mode collapse, stabilizes training of GANs with complex recurrent generators, and increases diversity and coverage of the data distribution by the generator.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796055", "vector": [], "sparse_vector": [], "title": "On Detecting Adversarial Perturbations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Machine learning and  deep learning in particular has advanced tremendously on perceptual tasks in recent years. However, it remains vulnerable against adversarial perturbations of the input that have been crafted specifically to fool the system while being quasi-imperceptible to a human. In this work, we propose to augment deep neural networks with a small ``detector'' subnetwork which is trained on the binary classification task of distinguishing genuine data from data containing adversarial perturbations. Our method is orthogonal to prior work on addressing adversarial perturbations, which has mostly focused on making the classification network itself more robust.  We show empirically that adversarial perturbations can be detected surprisingly well even though they are quasi-imperceptible to humans. Moreover, while the detectors have been trained to detect only a specific adversary, they generalize to similar and weaker adversaries. In addition, we propose an adversarial attack that fools both the classifier and the detector and a novel training procedure for the detector that counteracts this attack.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796056", "vector": [], "sparse_vector": [], "title": "Learning to Navigate in Complex Environments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Learning to navigate in complex environments with dynamic elements is an important milestone in developing AI agents. In this work we formulate the navigation question as a reinforcement learning problem and show that data efficiency and task performance can be dramatically improved by relying on additional auxiliary tasks to bootstrap learning. In particular we consider jointly learning the goal-driven reinforcement learning problem with an unsupervised depth prediction task and a self-supervised loop closure classification task. Using this approach we can learn to navigate from raw sensory input in complicated 3D mazes, approaching human-level performance even under conditions where the goal location changes frequently. We provide detailed analysis of the agent behaviour, its ability to localise, and its network activity dynamics, that show that the agent implicitly learns key navigation abilities, with only sparse rewards and without direct supervision.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796057", "vector": [], "sparse_vector": [], "title": "Generalizable Features From Unsupervised Learning.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Humans learn a predictive model of the world and use this model to reason about future events and the consequences of actions. In contrast to most machine predictors, we exhibit an impressive ability to generalize to unseen scenarios and reason intelligently in these settings.  One important aspect of this ability is physical intuition(<PERSON> et al., 2016). In this work, we explore the potential of unsupervised learning to find features that promote better generalization to settings outside the supervised training distribution.  Our task is predicting the stability of towers of square blocks. We demonstrate that an unsupervised model, trained to predict future frames of a video sequence of stable and unstable block configurations, can yield features that support extrapolating stability prediction to blocks configurations outside the training set distribution", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796058", "vector": [], "sparse_vector": [], "title": "Deep Kernel Machines via the Kernel Reparametrization Trick.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "While deep neural networks have achieved state-of-the-art performance on many tasks across varied domains, they still remain black boxes whose inner workings are hard to interpret and understand. In this paper, we develop a novel method for efficiently capturing the behaviour of deep neural networks using kernels. In particular, we construct a hierarchy of increasingly complex kernels that encode individual hidden layers of the network. Furthermore, we discuss how our framework motivates a novel supervised weight initialization method that discovers highly discriminative features already at initialization.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796059", "vector": [], "sparse_vector": [], "title": "Adversarial Training Methods for Semi-Supervised Text Classification.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Adversarial training provides a means of regularizing supervised learning algorithms while virtual adversarial training is able to extend supervised learning algorithms to the semi-supervised setting.\nHowever, both methods require making small perturbations to numerous entries of the input vector, which is inappropriate for sparse high-dimensional inputs such as one-hot word representations.\nWe extend adversarial and virtual adversarial training to the text domain by applying perturbations to the word embeddings in a recurrent neural network rather than to the original input itself.\nThe proposed method achieves state of the art results on multiple benchmark semi-supervised and purely supervised tasks.\nWe provide visualizations and analysis showing that the learned word embeddings have improved in quality and that while training, the model is less prone to overfitting.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796060", "vector": [], "sparse_vector": [], "title": "Synthetic Gradient Methods with Virtual Forward-Backward Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The concept of synthetic gradient introduced by <PERSON><PERSON><PERSON> et al. (2016) provides an avant-garde framework for asynchronous learning of neural network.\nTheir model, however, has a weakness in its construction, because the structure of their synthetic gradient has little relation to the objective function of the target task.\nIn this paper we introduce virtual forward-backward networks (VFBN). \nVFBN is a model that produces synthetic gradient whose structure is analogous to the actual gradient of the objective function.\nVFBN is the first of its kind that succeeds in decoupling deep networks like ResNet-110 (<PERSON> et al., 2016) without compromising its performance.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796061", "vector": [], "sparse_vector": [], "title": "Pruning Convolutional Neural Networks for Resource Efficient Inference.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We propose a new formulation for pruning convolutional kernels in neural networks to enable efficient inference. We interleave greedy criteria-based pruning with fine-tuning by backpropagation-a computationally efficient procedure that maintains good generalization in the pruned network. We propose a new criterion based on Taylor expansion that approximates the change in the cost function induced by pruning network parameters. We focus on transfer learning, where large pretrained networks are adapted to specialized tasks. The proposed criterion demonstrates superior performance compared to other criteria, e.g. the norm of kernel weights or feature map activation, for pruning large CNNs after adaptation to fine-grained classification tasks (Birds-200 and Flowers-102) relaying only on the first order gradient information. We also show that pruning can lead to more than 10x theoretical reduction in adapted 3D-convolutional filters with a small drop in accuracy in a recurrent gesture classifier. Finally, we show results for the large-scale ImageNet dataset to emphasize the flexibility of our approach.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796062", "vector": [], "sparse_vector": [], "title": "Understanding Trainable Sparse Coding with Matrix Factorization.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Sparse coding is a core building block in many data analysis and machine learning pipelines. Typically it is solved by relying on generic optimization techniques, such as the Iterative Soft Thresholding Algorithm and its accelerated version (ISTA, FISTA). These methods are optimal in the class of first-order methods for non-smooth, convex functions. However, they do not exploit the particular structure of the problem at hand nor the input data distribution. An acceleration using neural networks, coined LISTA, was proposed in \\cite{Gregor10}, which showed empirically that one could achieve high quality estimates with few iterations by modifying the parameters of the proximal splitting appropriately.\n\nIn this paper we study the reasons for such acceleration. Our mathematical analysis reveals that it is related to a specific matrix factorization of the Gram kernel of the dictionary, which attempts to nearly diagonalise the kernel with a basis that produces a small perturbation of the $\\ell_1$ ball. When this factorization succeeds, we prove that the resulting splitting algorithm enjoys an improved convergence bound with respect to the non-adaptive version. Moreover, our analysis also shows that conditions for acceleration occur mostly at the beginning of the iterative process, consistent with numerical experiments. We further validate our analysis by showing that on dictionaries where this factorization does not exist, adaptive acceleration fails.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796063", "vector": [], "sparse_vector": [], "title": "Coupling Distributed and Symbolic Execution for Natural Language Queries.", "authors": ["<PERSON><PERSON>", "Zhengdong Lu", "Hang Li", "<PERSON><PERSON>"], "summary": "In this paper, we propose to combine neural execution and symbolic execution to query a table with natural languages. Our approach makes use the differentiability of neural networks and transfers (imperfect) knowledge to the symbolic executor before reinforcement learning. Experiments show our approach achieves high learning efficiency, high execution efficiency, high interpretability, as well as high performance.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796064", "vector": [], "sparse_vector": [], "title": "Geometry of Polysemy.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Vector representations of words have heralded a transformational approach to classical problems in NLP; the most popular example is word2vec. However, a single vector does not suffice to model the polysemous nature of many (frequent) words, i.e., words with multiple meanings.    In this paper, we propose a three-fold approach for unsupervised polysemy modeling: (a) context representations, (b) sense induction and disambiguation and (c) lexeme (as a word and sense pair) representations. A key feature of our work is the finding that  a sentence containing a target word is  well represented by a low-rank subspace,  instead of a point in a vector  space. We then show that the subspaces associated with a particular sense of the target word tend to intersect over a line (one-dimensional subspace), which we use to disambiguate senses using a  clustering algorithm that harnesses the Grassmannian geometry of the representations. The disambiguation algorithm, which we call $K$-Grassmeans, leads to a procedure to label the different senses of the target word in the corpus -- yielding lexeme vector representations, all in an unsupervised manner starting from a large (Wikipedia) corpus in English. Apart from several prototypical target (word,sense) examples and a host of empirical studies to intuit and justify  the various geometric representations,  we validate our algorithms on standard sense induction and disambiguation datasets and present new state-of-the-art results.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796065", "vector": [], "sparse_vector": [], "title": "Reasoning with Memory Augmented Neural Networks for Language Comprehension.", "authors": ["Tsendsuren Munkhdalai", "Hong Yu"], "summary": "Hypothesis testing is an important cognitive process that supports human reasoning. In this paper, we introduce a computational hypothesis testing approach based on memory augmented neural networks. Our approach involves a hypothesis testing loop that reconsiders and progressively refines a previously formed hypothesis in order to generate new hypotheses to test. We apply the proposed approach to language comprehension task by using Neural Semantic Encoders (NSE). Our NSE models achieve the state-of-the-art results showing an absolute improvement of 1.2% to 2.6% accuracy over previous results obtained by single and ensemble systems on standard machine comprehension benchmarks such as the Children's Book Test (CBT) and Who-Did-What (WDW) news article datasets.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796066", "vector": [], "sparse_vector": [], "title": "Automatic Rule Extraction from Long Short Term Memory Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Although deep learning models have proven effective at solving problems in natural language processing, the mechanism by which they come to their conclusions is often unclear.   As a result, these models are generally treated as black boxes, yielding no insight of the underlying learned patterns.  In this paper we consider Long Short Term Memory networks (LSTMs) and demonstrate a new approach for tracking the importance of a given input to the LSTM for a given output. By identifying consistently important patterns of words, we are able to distill state of the art LSTMs on sentiment analysis and question answering into a set of representative phrases. This representation is then quantitatively validated by using the extracted phrases to construct a simple, rule-based classifier which approximates the output of the LSTM.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796067", "vector": [], "sparse_vector": [], "title": "Improving Policy Gradient by Exploring Under-appreciated Rewards.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper presents a novel form of policy gradient for model-free reinforcement learning (RL) with improved exploration properties. Current policy-based methods use entropy regularization to encourage undirected exploration of the reward landscape, which is ineffective in high dimensional spaces with sparse rewards. We propose a more directed exploration strategy that promotes exploration of under-appreciated reward regions. An action sequence is considered under-appreciated if its log-probability under the current policy under-estimates its resulting reward. The proposed exploration strategy is easy to implement, requiring only small modifications to the standard REINFORCE algorithm. We evaluate the approach on a set of algorithmic tasks that have long challenged RL methods. We find that our approach reduces hyper-parameter sensitivity and demonstrates significant improvements over baseline methods. Notably, the approach is able to solve a benchmark multi-digit addition task. To our knowledge, this is the first time that a pure RL method has solved addition using only reward feedback.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796068", "vector": [], "sparse_vector": [], "title": "Stick-Breaking Variational Autoencoders.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We extend Stochastic Gradient Variational Bayes to perform posterior inference for the weights of Stick-Breaking processes. This development allows us to define a Stick-Breaking Variational Autoencoder (SB-VAE), a Bayesian nonparametric version of the variational autoencoder that has a latent representation with stochastic dimensionality. We experimentally demonstrate that the SB-VAE, and a semi-supervised variant, learn highly discriminative latent representations that often outperform the Gaussian VAE’s.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796069", "vector": [], "sparse_vector": [], "title": "Variational Reference Priors.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In modern probabilistic learning, we often wish to perform automatic inference for Bayesian models.  However, informative priors are often costly to elicit, and in consequence, flat priors are chosen with the hopes that they are reasonably uninformative.  Yet, objective priors such as the Jeffreys and <PERSON> would often be preferred over flat priors if deriving them was generally tractable.  We overcome this problem by proposing a black-box learning algorithm for Reference prior approximations.  We derive a lower bound on the mutual information between data and parameters and describe how its optimization can be made derivation-free and scalable via differentiable Monte Carlo expectations.  We experimentally demonstrate the method's effectiveness by recovering Jeffreys priors and learning the Variational Autoencoder's Reference prior.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796070", "vector": [], "sparse_vector": [], "title": "Exploring Sparsity in Recurrent Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recurrent neural networks (RNN) are widely used to solve a variety of problems and as the quantity of data and the amount of available compute have increased, so have model sizes. The number of parameters in recent state-of-the-art networks makes them hard to deploy, especially on mobile phones and embedded devices. The challenge is due to both the size of the model and the time it takes to evaluate it. In order to deploy these RNNs efficiently, we propose a technique to reduce the parameters of a network by pruning weights during the initial training of the network. At the end of training, the parameters of the network are sparse while accuracy is still close to the original dense neural network. The network size is reduced by 8× and the time required to train the model remains constant. Additionally, we can prune a larger dense network to achieve better than baseline performance while still reducing the total number of parameters significantly. Pruning RNNs reduces the size of the model and can also help achieve significant inference time speed-up using sparse GEMMs. Benchmarks show that using our technique model size can be reduced by 90% and speed-up is around 2× to 7×.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796071", "vector": [], "sparse_vector": [], "title": "Learning a Natural Language Interface with Neural Programmer.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>uo<PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Learning a natural language interface for database tables is a challenging task that involves deep language understanding and multi-step reasoning. The task is often approached by mapping natural language queries to logical forms or programs that provide the desired response when executed on the database. To our knowledge, this paper presents the first weakly supervised, end-to-end neural network model to induce such programs on a real-world dataset. We enhance the objective function of Neural Programmer, a neural network with built-in discrete operations, and apply it on WikiTableQuestions, a natural language question-answering dataset. The model is trained end-to-end with weak supervision of question-answer pairs, and does not require domain-specific grammars, rules, or annotations that are key elements in previous approaches to program induction. The main experimental result in this paper is that a single Neural Programmer model achieves 34.2% accuracy using only 10,000 examples with weak supervision. An ensemble of 15 models, with a trivial combination technique, achieves 37.7% accuracy, which is competitive to the current state-of-the-art accuracy of 37.1% obtained by a traditional natural language semantic parser.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796072", "vector": [], "sparse_vector": [], "title": "Exponential Machines.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Modeling interactions between features improves the performance of machine learning solutions in many domains (e.g. recommender systems or sentiment analysis). In this paper, we introduce Exponential Machines (ExM), a predictor that models all interactions of every order. The key idea is to represent an exponentially large tensor of parameters in a factorized format called Tensor Train (TT). The Tensor Train format regularizes the model and lets you control the number of underlying parameters. To train the model, we develop a stochastic Riemannian optimization procedure, which allows us to fit tensors with 2^160 entries. We show that the model achieves state-of-the-art performance on synthetic data with high-order interactions and that it works on par with high-order factorization machines on a recommender system dataset MovieLens 100K.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796073", "vector": [], "sparse_vector": [], "title": "Sigma Delta Quantized Networks.", "authors": ["<PERSON>;<PERSON>", "<PERSON>"], "summary": "Deep neural networks can be obscenely wasteful. When processing video, a convolutional network expends a fixed amount of computation for each frame with no regard to the similarity between neighbouring frames. As a result, it ends up repeatedly doing very similar computations. To put an end to such waste, we introduce Sigma-Delta networks. With each new input, each layer in this network sends a discretized form of its change in activation to the next layer. Thus the amount of computation that the network does scales with the amount of change in the input and layer activations, rather than the size of the network. We introduce an optimization method for converting any pre-trained deep network into an optimally efficient Sigma-Delta network, and show that our algorithm, if run on the appropriate hardware, could cut at least an order of magnitude from the computational cost of processing video data.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796074", "vector": [], "sparse_vector": [], "title": "Combining policy gradient and Q-learning.", "authors": ["Brendan <PERSON>;<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Policy gradient is an efficient technique for improving a policy in a reinforcement learning setting. However, vanilla online variants are on-policy only and not able to take advantage of off-policy data. In this paper we describe a new technique that combines policy gradient with off-policy Q-learning, drawing experience from a replay buffer. This is motivated by making a connection between the fixed points of the regularized policy gradient algorithm and the Q-values. This connection allows us to estimate the Q-values from the action preferences of the policy, to which we apply Q-learning updates. We refer to the new technique as ‘PGQL’, for policy gradient and Q-learning. We also establish an equivalency between action-value fitting techniques and actor-critic algorithms, showing that regularized policy gradient techniques can be interpreted as advantage function learning algorithms. We conclude with some numerical examples that demonstrate improved data efficiency and stability of PGQL. In particular, we tested PGQL on the full suite of Atari games and achieved performance exceeding that of both asynchronous advantage actor-critic (A3C) and Q-learning.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796075", "vector": [], "sparse_vector": [], "title": "Changing Model Behavior at Test-time Using Reinforcement Learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Machine learning models are often used at test-time subject to constraints and trade-offs not present\nat training-time. For example, a computer vision model operating on an embedded device may need to perform\nreal-time inference, or a translation model operating on a cell phone may wish to bound its average\ncompute time in order to be power-efficient. In this work we describe a mixture-of-experts model and show how to change its  test-time resource-usage on a per-input basis using reinforcement learning. We test our method on a small MNIST-based example.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796076", "vector": [], "sparse_vector": [], "title": "Gated Multimodal Units for Information Fusion.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents a novel model for multimodal learning based on gated neural networks. The Gated Multimodal Unit (GMU) model is intended to be used as an internal unit in a neural network architecture whose purpose is to find an intermediate representation based on a combination of data from different modalities. The GMU learns to decide how modalities influence the activation of the unit using multiplicative gates. It was evaluated on a multilabel scenario for genre classification of movies using the plot and the poster. The GMU improved the macro f-score performance of single-modality approaches and outperformed other fusion strategies, including mixture of experts models. Along with this work, the MM-IMDb dataset is released which, to the best of our knowledge, is the largest publicly available multimodal dataset for genre prediction on movies.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796077", "vector": [], "sparse_vector": [], "title": "Learning Invariant Representations Of Planar Curves.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose a metric learning framework for the construction of invariant geometric\nfunctions of planar curves for the Euclidean and Similarity group of transformations.\nWe leverage on the representational power of convolutional neural\nnetworks to compute these geometric quantities. In comparison with axiomatic\nconstructions, we show that the invariants approximated by the learning architectures\nhave better numerical qualities such as robustness to noise, resiliency to\nsampling, as well as the ability to adapt to occlusion and partiality. Finally, we develop\na novel multi-scale representation in a similarity metric learning paradigm.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796078", "vector": [], "sparse_vector": [], "title": "Semi-supervised Knowledge Transfer for Deep Learning from Private Training Data.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Some machine learning applications involve training data that is sensitive, such\nas the medical histories of patients in a clinical trial. A model may\ninadvertently and implicitly store some of its training data; careful analysis\nof the model may therefore reveal sensitive information.\n\nTo address this problem, we demonstrate a generally applicable approach to\nproviding strong privacy guarantees for training data: Private Aggregation of Teacher Ensembles (PATE). The approach combines, in\na black-box fashion, multiple models trained with disjoint datasets, such as\nrecords from different subsets of users. Because they rely directly on sensitive\ndata, these models are not published, but instead used as ''teachers'' for a ''student'' model. \nThe student learns to predict an output chosen by noisy voting\namong all of the teachers, and cannot directly access an individual teacher or\nthe underlying data or parameters. The student's privacy properties can be\nunderstood both intuitively (since no single teacher and thus no single dataset\ndictates the student's training) and formally, in terms of differential privacy.\n These properties hold even if an adversary can not only query the student but\nalso inspect its internal workings.\n\nCompared with previous work, the approach imposes only weak assumptions on how\nteachers are trained: it applies to any model, including non-convex models like\nDNNs. We achieve state-of-the-art privacy/utility trade-offs on MNIST and SVHN\nthanks to an improved privacy analysis and semi-supervised learning.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796079", "vector": [], "sparse_vector": [], "title": "Neuro-Symbolic Program Synthesis.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Recent years have seen the proposal of a number of neural architectures for the problem of Program Induction. Given a set of input-output examples, these architectures are able to learn mappings that generalize to new test inputs. While achieving impressive results, these approaches have a number of important limitations: (a) they are computationally expensive and hard to train, (b) a model has to be trained for each task (program) separately, and (c) it is hard to interpret or verify the correctness of the learnt mapping (as it is defined by a neural network). In this paper, we propose a novel technique, Neuro-Symbolic Program Synthesis, to overcome the above-mentioned problems. Once trained, our approach can automatically construct computer programs in a domain-specific language that are consistent with a set of input-output examples provided at test time. Our method is based on two novel neural modules. The first module, called the cross correlation I/O network, given a set of input-output examples, produces a continuous representation of the set of I/O examples. The second module, the Recursive-Reverse-Recursive Neural Network (R3NN), given the continuous representation of the examples, synthesizes a program by incrementally expanding partial programs. We demonstrate the effectiveness of our approach by applying it to the rich and complex domain of regular expression based string transformations. Experiments show that the R3NN model is not only able to construct programs from new input-output examples, but it is also able to construct new programs for tasks that it had never observed before during training.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796080", "vector": [], "sparse_vector": [], "title": "Faster CNNs with Direct Sparse Convolutions and Guided Pruning.", "authors": ["Jongsoo Park", "Sheng R. <PERSON>", "<PERSON>", "<PERSON>", "Hai <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Phenomenally successful in practical inference problems, convolutional neural networks (CNN) are widely deployed in mobile devices, data centers, and even supercomputers.\nThe number of parameters needed in CNNs, however, are often large and undesirable. Consequently, various methods have been developed to prune a CNN once it is trained. \nNevertheless, the resulting CNNs offer limited benefits. While pruning the fully connected layers reduces a CNN's size considerably, it does not improve inference speed noticeably as the compute heavy parts lie in convolutions. Pruning CNNs in a way that increase inference speed often imposes specific sparsity structures, thus limiting the achievable sparsity levels.\n\nWe present a method to realize simultaneously size economy and speed improvement while pruning CNNs. Paramount to our success is an efficient general sparse-with-dense matrix\nmultiplication implementation that is applicable to convolution of feature maps with kernels of arbitrary sparsity patterns. Complementing this, we developed a performance model that predicts sweet spots of sparsity levels for different layers and on different computer architectures. Together, these two allow us to demonstrate 3.1-7.3x convolution speedups over dense convolution in AlexNet, on Intel Atom, Xeon, and Xeon Phi processors, spanning the spectrum from mobile devices to supercomputers.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796081", "vector": [], "sparse_vector": [], "title": "Efficient variational Bayesian neural network ensembles for outlier detection.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this work we perform outlier detection using ensembles of neural networks obtained by variational approximation of the posterior in a Bayesian neural network setting. The variational parameters are obtained by sampling from the true posterior by gradient descent. We show our outlier detection results are comparable to those obtained using other efficient ensembling methods.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796082", "vector": [], "sparse_vector": [], "title": "Regularizing Neural Networks by Penalizing Confident Output Distributions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Lukasz Kaiser", "<PERSON>"], "summary": "We propose regularizing neural networks by penalizing low entropy output distributions. We show that penalizing low entropy output distributions, which has been shown to improve exploration in reinforcement learning, acts as a strong regularizer in supervised learning. We connect our confidence penalty to label smoothing through the direction of the KL divergence between networks output distribution and the uniform distribution. We exhaustively evaluate our proposed confidence penalty and label smoothing (uniform and unigram) on 6 common benchmarks: image classification (MNIST and Cifar-10), language modeling (Penn Treebank), machine translation (WMT'14 English-to-German), and speech recognition (TIMIT and WSJ). We find that both label smoothing and our confidence penalty improve state-of-the-art models across benchmarks without modifying existing hyper-parameters.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796083", "vector": [], "sparse_vector": [], "title": "Nonparametric Neural Networks.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Automatically determining the optimal size of a neural network for a given task without prior information currently requires an expensive global search and training many networks from scratch. In this paper, we address the problem of automatically finding a good network size during a single training cycle. We introduce {\\it nonparametric neural networks}, a non-probabilistic framework for conducting optimization over all possible network sizes and prove its soundness when network growth is limited via an $\\ell_p$ penalty. We train networks under this framework by continuously adding new units while eliminating redundant units via an $\\ell_2$ penalty. We employ a novel optimization algorithm, which we term ``Adaptive Radial-Angular Gradient Descent'' or {\\it AdaRad}, and obtain promising results.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796084", "vector": [], "sparse_vector": [], "title": "Unsupervised and Scalable Algorithm for Learning Node Representations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Representation learning is one of the foundations of Deep Learning and allowed big improvements on several Machine Learning fields, such as Neural Machine Translation, Question Answering and Speech Recognition. Recent works have proposed new methods for learning representations for nodes and edges in graphs. In this work, we propose a new unsupervised and efficient method, called here Neighborhood Based Node Embeddings (NBNE), capable of generating node embeddings for very large graphs. This method is based on SkipGram and uses nodes' neighborhoods as contexts to generate representations. NBNE achieves results comparable or better to the state-of-the-art in three different datasets.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796085", "vector": [], "sparse_vector": [], "title": "Intelligent synapses for multi-task and transfer learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep learning has led to remarkable advances when applied to problems in which the data distribution does not change over the course of learning. In stark contrast, biological neural networks exhibit continual learning, solve a diversity of tasks simultaneously, and have no clear separation between training and evaluation phase. Furthermore, synapses in biological neurons are not simply real-valued scalars, but possess complex molecular machinery that enable non-trivial learning dynamics. In this study, we take a first step toward bringing this biological complexity into artificial neural networks. We introduce intelligent synapses that are capable of accumulating information over time, and exploiting this information to efficiently protect old memories from being overwritten as new problems are learned. We apply our framework to learning sequences of related classification problems, and show that it dramatically reduces catastrophic forgetting while maintaining computational efficiency.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796086", "vector": [], "sparse_vector": [], "title": "Adaptive Feature Abstraction for Translating Video to Language.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A new model for video captioning is developed, using a deep three-dimensional Convolutional Neural Network (C3D) as an encoder for videos and a Recurrent Neural Network (RNN) as a decoder for captions. A novel attention mechanism with spatiotemporal alignment is employed to adaptively and sequentially focus on different layers of CNN features (levels of feature \"abstraction\"), as well as local spatiotemporal regions of the feature maps at each layer. The proposed approach is evaluated on the YouTube2Text benchmark. Experimental results demonstrate quantitatively the effectiveness of our proposed adaptive spatiotemporal feature abstraction for translating videos to sentences with rich semantic structures.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796087", "vector": [], "sparse_vector": [], "title": "Variational Recurrent Adversarial Deep Domain Adaptation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the problem of learning domain invariant representations for time series data while transferring the complex temporal latent dependencies between the domains. Our model termed as Variational Recurrent Adversarial Deep Domain Adaptation (VRADA) is built atop a variational recurrent neural network (VRNN) and trains adversarially to capture complex temporal relationships that are domain-invariant. This is (as far as we know) the first to capture and transfer temporal latent dependencies in multivariate time-series data. Through experiments on real-world multivariate healthcare time-series datasets, we empirically demonstrate that learning temporal dependencies helps our model's ability to create domain-invariant representations, allowing our model to outperform current state-of-the-art deep domain adaptation approaches.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796088", "vector": [], "sparse_vector": [], "title": "Paleo: A Performance Model for Deep Neural Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Although various scalable deep learning software packages have been proposed, it remains unclear how to best leverage parallel and distributed computing infrastructure to accelerate their training and deployment. Moreover, the effectiveness of existing parallel and distributed systems varies widely based on the neural network architecture and dataset under consideration.  In order to efficiently explore the space of scalable deep learning systems and quickly diagnose their effectiveness for a given problem instance, we introduce an analytical performance model called Paleo. Our key observation is that a neural network architecture carries with it a declarative specification of the computational requirements associated with its training and evaluation. By extracting these requirements from a given architecture and mapping them to a specific point within the design space of software, hardware and communication strategies, <PERSON><PERSON> can efficiently and accurately model the expected scalability and performance of a putative deep learning system.  We show that Paleo is robust to the choice of network architecture, hardware, software, communication schemes, and parallelization strategies. We further demonstrate its ability to accurately model various recently published scalability results for CNNs such as NiN, Inception and AlexNet.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796089", "vector": [], "sparse_vector": [], "title": "Neu0.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>khar BK", "<PERSON><PERSON><PERSON><PERSON> Narayan<PERSON> I<PERSON>", "<PERSON><PERSON>"], "summary": "MU0 is a deterministic computer that can store data in memory, manipulate it using programs, enabling decision making. Neu0 is a neural computational core modeled around the same principles. We create an ensemble of Neural Networks capable of executing ARM code, and discuss generalizations of our framework. We showcase the advantage of our technique by correctly executing malformed instructions, and discuss efficient memory management techniques.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796090", "vector": [], "sparse_vector": [], "title": "Training a Subsampling Mechanism in Expectation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We describe a mechanism for subsampling sequences and show how to compute its expected output so that it can be trained with standard backpropagation.  We test this approach on a simple toy problem and discuss its shortcomings.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796091", "vector": [], "sparse_vector": [], "title": "Attend, Adapt and Transfer: Attentive Deep Architecture for Adaptive Transfer from multiple sources in the same domain.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Transferring knowledge from prior source tasks in solving a new target task can be useful in several learning applications. The application of transfer poses two serious challenges which have not been adequately addressed. First, the agent should be able to avoid negative transfer, which happens when the transfer hampers or slows down the learning instead of helping it. Second, the agent should be able to selectively transfer, which is the ability to select and transfer from different and multiple source tasks for different parts of the state space of the target task. We propose A2T (Attend Adapt and Transfer), an attentive deep architecture which adapts and transfers from these source tasks. Our model is generic enough to effect transfer of either policies or value functions. Empirical evaluations on different learning algorithms show that A2T is an effective architecture for transfer by being able to avoid negative transfer while transferring selectively from multiple source tasks in the same domain.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796092", "vector": [], "sparse_vector": [], "title": "EPOpt: Learning Robust Neural Network Policies Using Model Ensembles.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Sample complexity and safety are major challenges when learning policies with reinforcement learning for real-world tasks, especially when the policies are represented using rich function approximators like deep neural networks. Model-based methods where the real-world target domain is approximated using a simulated source domain provide an avenue to tackle the above challenges by augmenting real data with simulated data. However, discrepancies between the simulated source domain and the target domain pose a challenge for simulated training. We introduce the EPOpt algorithm, which uses an ensemble of simulated source domains and a form of adversarial training to learn policies that are robust and generalize to a broad range of possible target domains, including to unmodeled effects. Further, the probability distribution over source domains in the ensemble can be adapted using data from the target domain and approximate Bayesian methods, to progressively make it a better approximation. Thus, learning on a model ensemble, along with source domain adaptation, provides the benefit of both robustness and learning.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796093", "vector": [], "sparse_vector": [], "title": "Fast Generation for Convolutional Autoregressive Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Convolutional autoregressive models have recently demonstrated state-of-the-art performance on a number of generation tasks. While fast, parallel training methods have been crucial for their success, generation is typically implemented in a naive fashion where redundant computations are unnecessarily repeated. This results in slow generation, making such models infeasible for production environments. In this work, we describe a method to speed up generation in convolutional autoregressive models. The key idea is to cache hidden states to avoid redundant computation. We apply our fast generation method to the Wavenet and PixelCNN++ models and achieve up to 21x and 183x speedups respectively.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796094", "vector": [], "sparse_vector": [], "title": "Discovering objects and their relations from entangled scene representations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Our world can be succinctly and compactly described as structured scenes of objects and relations. A typical room, for example, contains salient objects such as tables, chairs and books, and these objects typically relate to each other by their underlying causes and semantics. This gives rise to correlated features, such as position, function and shape. Humans exploit knowledge of objects and their relations for learning a wide spectrum of tasks, and more generally when learning the structure underlying observed data. In this work, we introduce relation networks (RNs) - a general purpose neural network architecture for object-relation reasoning. We show that RNs are capable of learning object relations from scene description data. Furthermore, we show that RNs can act as a bottleneck that induces the factorization of objects from entangled scene description inputs, and from distributed deep representations of scene images provided by a variational autoencoder. The model can also be used in conjunction with differentiable memory mechanisms for implicit relation discovery in one-shot learning tasks. Our results suggest that relation networks are a potentially powerful architecture for solving a variety of problems that require object relation reasoning.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796095", "vector": [], "sparse_vector": [], "title": "Deep Learning with Sets and Point Clouds.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Barnabás Pócz<PERSON>"], "summary": "We introduce a simple permutation equivariant layer for deep learning with set structure. This type of layer, obtained by parameter-sharing, has a simple implementation and linear-time complexity in the size of each set. We use deep\npermutation-invariant networks to perform point-could classification and MNIST-digit summation, where in both cases the output is invariant to permutations of the input. In a semi-supervised setting, where the goal is make predictions for\neach instance within a set, we demonstrate the usefulness of this type of layer in set-outlier detection as well as semi-supervised learning with clustering side-information.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796096", "vector": [], "sparse_vector": [], "title": "Optimization as a Model for Few-Shot Learning.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Though deep neural networks have shown great success in the large data domain, they generally perform poorly on few-shot learning tasks, where a model has to quickly generalize after seeing very few examples from each class. The general belief is that gradient-based optimization in high capacity models requires many iterative steps over many examples to perform well. Here, we propose an LSTM-based meta-learner model to learn the exact optimization algorithm used to train another learner neural network in the few-shot regime. The parametrization of our model allows it to learn appropriate parameter updates specifically for the scenario where a set amount of updates will be made, while also learning a general initialization of the learner network that allows for quick convergence of training. We demonstrate that this meta-learning model is competitive with deep metric-learning techniques for few-shot learning.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796097", "vector": [], "sparse_vector": [], "title": "Normalizing the Normalizers: Comparing and Extending Network Normalization Schemes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Normalization techniques have only recently begun to be exploited in supervised learning tasks. Batch normalization exploits mini-batch statistics to normalize the activations. This was shown to speed up training and result in better models. However its success has been very limited when dealing with recurrent neural networks. On the other hand, layer normalization normalizes the activations across all activities within a layer. This was shown to work  well in the recurrent setting. In this paper we propose a unified view of  normalization techniques, as forms of divisive normalization, which includes layer and batch normalization as special cases. Our second contribution is the finding that a small modification to these normalization schemes, in conjunction with a sparse regularizer on the activations, leads to significant benefits over standard normalization techniques. We demonstrate the effectiveness of our unified divisive normalization framework  in the context of convolutional neural nets and recurrent neural networks, showing  improvements over baselines in image classification, language modeling as well as super-resolution.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796098", "vector": [], "sparse_vector": [], "title": "Regularizing CNNs with Locally Constrained Decorrelations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Guillem Cucurull", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Regularization is key for deep learning since it allows training more complex models while keeping lower levels of overfitting. However, the most prevalent regularizations do not leverage all the capacity of the models since they rely on reducing the effective number of parameters. Feature decorrelation is an alternative for using the full capacity of the models but the overfitting reduction margins are too narrow given the overhead it introduces. In this paper, we show that regularizing negatively correlated features is an obstacle for effective decorrelation and present OrthoReg, a novel regularization technique that locally enforces feature orthogonality. As a result, imposing locality constraints in feature decorrelation removes interferences between negatively correlated feature weights, allowing the regularizer to reach higher decorrelation bounds, and reducing the overfitting more effectively. \nIn particular, we show that the models regularized with OrthoReg have higher accuracy bounds even when batch normalization and dropout are present. Moreover, since our regularization is directly performed on the weights, it is especially suitable for fully convolutional neural networks, where the weight space is constant compared to the feature map space. As a result, we are able to reduce the overfitting of state-of-the-art CNNs on CIFAR-10, CIFAR-100, and SVHN.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796099", "vector": [], "sparse_vector": [], "title": "Discrete Variational Autoencoders.", "authors": ["<PERSON>"], "summary": "Probabilistic models with discrete latent variables naturally capture datasets composed of discrete classes. However, they are difficult to train efficiently, since backpropagation through discrete variables is generally not possible. We present a novel method to train a class of probabilistic models with discrete latent variables using the variational autoencoder framework, including backpropagation through the discrete latent variables. The associated class of probabilistic models comprises an undirected discrete component and a directed hierarchical continuous component. The discrete component captures the distribution over the disconnected smooth manifolds induced by the continuous component. As a result, this class of models efficiently learns both the class of objects in an image, and their specific realization in pixels, from unsupervised data; and outperforms state-of-the-art methods on the permutation-invariant MNIST, Omniglot, and Caltech-101 Silhouettes datasets.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796100", "vector": [], "sparse_vector": [], "title": "Forced to Learn: Discovering Disentangled Representations Without Exhaustive Labels.", "authors": ["<PERSON><PERSON>", "Anna Rumshisky"], "summary": "Learning a better representation with neural networks is a challenging problem, which was tackled extensively from different prospectives in the past few years. In this work, we focus on learning a representation that could be used for  clustering and introduce a novel loss component that substantially improves the quality of produced clusters, is simple to apply to an arbitrary cost function, and does not require a complicated training procedure.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796101", "vector": [], "sparse_vector": [], "title": "Diet Networks: Thin Parameters for Fat Genomics.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Learning tasks such as those involving genomic data often poses a serious challenge: the number of input features can be orders of magnitude larger than the number of training examples, making it difficult to avoid overfitting, even when using the known regularization techniques. We focus here on tasks in which the input is a description of the genetic variation specific to a patient, the single nucleotide polymorphisms (SNPs), yielding millions of ternary inputs. Improving the ability of deep learning to handle such datasets could have an important impact in medical research, more specifically in precision medicine, where high-dimensional data regarding a particular patient is used to make predictions of interest. Even though the amount of data for such tasks is increasing, this mismatch between the number of examples and the number of inputs remains a concern. Naive implementations of classifier neural networks involve a huge number of free parameters in their first layer (number of input features times number of hidden units): each input feature is associated with as many parameters as there are hidden units. We propose a novel neural network parametrization which considerably reduces the number of free parameters. It is based on the idea that we can first learn or provide a distributed representation for each input feature (e.g. for each position in the genome where variations are observed in data), and then learn (with another neural network called the parameter prediction network) how to map a feature's distributed representation (based on the feature's identity not its value) to the vector of parameters specific to that feature in the classifier neural network (the weights which link the value of the feature to each of the hidden units). This approach views the problem of producing the parameters associated with each feature as a multi-task learning problem. We show experimentally on a population stratification task of interest to medical studies that the proposed approach can significantly reduce both the number of parameters and the error rate of the classifier.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796102", "vector": [], "sparse_vector": [], "title": "Tighter bounds lead to improved classifiers.", "authors": ["<PERSON>"], "summary": "The standard approach to supervised classification involves the minimization of a log-loss as an upper bound to the classification error. While this is a tight bound early on in the optimization, it overemphasizes the influence of incorrectly classified examples far from the decision boundary. Updating the upper bound during the optimization leads to improved classification rates while transforming the learning into a sequence of minimization problems. In addition, in the context where the classifier is part of a larger system, this modification makes it possible to link the performance of the classifier to that of the whole system, allowing the seamless introduction of external constraints.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796103", "vector": [], "sparse_vector": [], "title": "PixelCNN++: Improving the PixelCNN with Discretized Logistic Mixture Likelihood and Other Modifications.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "PixelCNNs are a recently proposed class of powerful generative models with tractable likelihood. Here we discuss our implementation of PixelCNNs which we make available at https://github.com/openai/pixel-cnn. Our implementation contains a number of modifications to the original model that both simplify its structure and improve its performance. 1) We use a discretized logistic mixture likelihood on the pixels, rather than a 256-way softmax, which we find to speed up training. 2) We condition on whole pixels, rather than R/G/B sub-pixels, simplifying the model structure. 3) We use downsampling to efficiently capture structure at multiple resolutions. 4) We introduce additional short-cut connections to further speed up optimization. 5) We regularize the model using dropout. Finally, we present state-of-the-art log likelihood results on CIFAR-10 to demonstrate the usefulness of these modifications.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796104", "vector": [], "sparse_vector": [], "title": "Perception Updating Networks: On architectural constraints for interpretable video generative models.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We investigate a neural network architecture and statistical framework that models frames in videos using principles inspired by computer graphics pipelines. The proposed model explicitly represents \"sprites\" or its percepts inferred from maximum likelihood of the scene and infers its movement independently of its content. We impose architectural constraints that forces resulting architecture to behave as a recurrent what-where prediction network.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796105", "vector": [], "sparse_vector": [], "title": "Deep Information Propagation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the behavior of untrained neural networks whose weights and biases are randomly distributed using mean field theory. We show the existence of depth scales that naturally limit the maximum depth of signal propagation through these random networks. Our main practical result is to show that random networks may be trained precisely when information can travel through them. Thus, the depth scales that we identify provide bounds on how deep a network may be trained for a specific choice of hyperparameters. As a corollary to this, we argue that in networks at the edge of chaos, one of these depth scales diverges. Thus arbitrarily deep networks may be trained only sufficiently close to criticality. We show that the presence of dropout destroys the order-to-chaos critical point and therefore strongly limits the maximum trainable depth for random networks. Finally, we develop a mean field theory for backpropagation and we show that the ordered and chaotic phases correspond to regions of vanishing and exploding gradient respectively.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796106", "vector": [], "sparse_vector": [], "title": "Towards &quot;AlphaChem&quot;: Chemical Synthesis Planning with Tree Search and Deep Neural Network Policies.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Retrosynthesis is a technique to plan the chemical synthesis of organic molecules, for example drugs, agro- and fine chemicals. In retrosynthesis, a search tree is built by analysing molecules recursively and dissecting them into simpler molecular building blocks until one obtains a set of known building blocks. The search space is intractably large, and it is difficult to determine the value of retrosynthetic positions. Here, we propose to model retrosynthesis as a Markov Decision Process. In combination with a Deep Neural Network policy trained on 5.5 million reactions, Monte Carlo Tree Search (MCTS) can be used to evaluate positions. In exploratory studies, we demonstrate that MCTS with neural network policies outperforms the traditionally used best-first search with hand-coded heuristics.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796107", "vector": [], "sparse_vector": [], "title": "Bidirectional Attention Flow for Machine Comprehension.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Machine comprehension (MC), answering a query about a given context paragraph, requires modeling complex interactions between the context and the query. Recently, attention mechanisms have been successfully extended to MC. Typically these methods use attention to focus on a small portion of the context and summarize it with a fixed-size vector, couple attentions temporally, and/or often form a uni-directional attention. In this paper we introduce the Bi-Directional Attention Flow (BIDAF) network, a multi-stage hierarchical process that represents the context at different levels of granularity and uses bi-directional attention flow mechanism to obtain a query-aware context representation without early summarization. Our experimental evaluations show that our model achieves the state-of-the-art results in Stanford Question Answering Dataset (SQuAD) and CNN/DailyMail cloze test.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796108", "vector": [], "sparse_vector": [], "title": "Query-Reduction Networks for Question Answering.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we study the problem of question answering when reasoning over multiple facts is required. We propose Query-Reduction Network (QRN), a variant of Recurrent Neural Network (RNN) that effectively handles both short-term (local) and long-term (global) sequential dependencies to reason over multiple facts. QRN considers the context sentences as a sequence of state-changing triggers, and reduces the original query  to a more informed query as it observes each trigger (context sentence) through time. Our experiments show that QRN produces the state-of-the-art results in  bAbI QA and dialog tasks, and in a real goal-oriented dialog dataset. In addition, QRN formulation allows parallelization on RNN's time axis, saving an order of magnitude in time complexity for training and inference.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796109", "vector": [], "sparse_vector": [], "title": "Unsupervised Perceptual Rewards for Imitation Learning.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Reward function design and exploration time are arguably the biggest obstacles to the deployment of reinforcement learning (RL) agents in the real world. In many real-world tasks, designing a suitable reward function takes considerable manual engineering and often requires additional and potentially visible sensors to be installed just to measure whether the task has been executed successfully. Furthermore, many interesting tasks consist of multiple steps that must be executed in sequence. Even when the final outcome can be measured, it does not necessarily provide useful feedback on these implicit intermediate steps or sub-goals. To address these issues, we propose leveraging the abstraction power of intermediate visual representations learned by deep models to quickly infer perceptual reward functions from small numbers of demonstrations. We present a method that is able to identify the key intermediate steps of a task from only a handful of demonstration sequences, and automatically identify the most discriminative features for identifying these steps. This method makes use of the features in a pre-trained deep model, but does not require any explicit sub-goal supervision. The resulting reward functions, which are dense and smooth, can then be used by an RL agent to learn to perform the task in real-world settings.\nTo evaluate the learned reward functions, we present qualitative results on two real-world tasks and a quantitative evaluation against a human-designed reward function. We also demonstrate that our method can be used to learn a complex real-world door opening skill using a real robot, even when the demonstration used for reward learning is provided by a human using their own hand. To our knowledge, these are the first results showing that complex robotic manipulation skills can be learned directly and without supervised labels from a video of a human performing the task.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796110", "vector": [], "sparse_vector": [], "title": "Compact Embedding of Binary-coded Inputs and Outputs using Bloom Filters.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The size of neural network models that deal with sparse inputs and outputs is often dominated by the dimensionality of those inputs and outputs. Large models with high-dimensional inputs and outputs are difficult to train due to the limited memory of graphical processing units, and difficult to deploy on mobile devices with limited hardware. To address these difficulties, we propose Bloom embeddings, a compression technique that can be applied to the input and output of neural network models dealing with sparse high-dimensional binary-coded instances. Bloom embeddings are computationally efficient, and do not seriously compromise the accuracy of the model up to 1/5 compression ratios. In some cases, they even improve over the original accuracy, with relative increases up to 12%. We evaluate Bloom embeddings on 7 data sets and compare it against 4 alternative methods, obtaining favorable results.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796111", "vector": [], "sparse_vector": [], "title": "Natural Language Generation in Dialogue using Lexicalized and Delexicalized Data.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Natural language generation plays a critical role in spoken dialogue systems. We present a new approach to natural language generation for task-oriented dialogue using recurrent neural networks in an encoder-decoder framework. In contrast to previous work, our model uses both lexicalized and delexicalized components i.e. slot-value pairs for dialogue acts, with slots and corresponding values aligned together. This allows our model to learn from all available data including the slot-value pairing, rather than being restricted to delexicalized slots. We show that this helps our model generate more natural sentences with better grammar. We further improve our model's performance by transferring weights learnt from a pretrained sentence auto-encoder. Human evaluation of our best-performing model indicates that it generates sentences which users find more appealing.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796112", "vector": [], "sparse_vector": [], "title": "Learning to Repeat: Fine Grained Action Repetition for Deep Reinforcement Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Reinforcement Learning algorithms can learn complex behavioral patterns for sequential decision making tasks wherein an agent interacts with an environment and acquires feedback in the form of rewards sampled from it. Traditionally, such algorithms make decisions, i.e., select actions to execute, at every single time step of the agent-environment interactions. In this paper, we propose a novel framework, Fine Grained Action Repetition (FiGAR), which enables the agent to decide the action as well as the time scale of repeating it.\nFiGAR can be used for improving any Deep Reinforcement Learning algorithm which maintains an explicit policy estimate  by enabling temporal abstractions in the action space and implicitly enabling planning through sequences of repetitive macro-actions.  \nWe empirically demonstrate the efficacy of our framework by showing performance improvements on top of three policy search algorithms in different domains: Asynchronous Advantage Actor Critic in the Atari 2600 domain, Trust Region Policy Optimization in Mujoco domain and Deep Deterministic Policy Gradients in the TORCS car racing domain.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796113", "vector": [], "sparse_vector": [], "title": "Online Multi-Task Learning Using Active Sampling.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "One of the long-standing challenges in Artificial Intelligence for goal-directed behavior is to build a single agent which can solve multiple tasks. Recent progress in multi-task learning  for  goal-directed sequential tasks has been in the form of distillation based learning wherein a single student network learns from multiple task-specific expert networks by mimicking the task-specific policies of the expert networks. While such approaches offer a promising solution to the multi-task learning problem, they require supervision from large task-specific (expert) networks which require  extensive training.\nWe propose a simple yet efficient multi-task learning framework which solves multiple goal-directed tasks in an online or  active learning setup without the need for expert supervision.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796114", "vector": [], "sparse_vector": [], "title": "Outrageously Large Neural Networks: The Sparsely-Gated Mixture-of-Experts Layer.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>uo<PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The capacity of a neural network to absorb information is limited by its number of parameters.  Conditional computation, where parts of the network are active on a per-example basis, has been proposed in theory as a way of dramatically increasing model capacity without a proportional increase in computation.  In practice, however, there are significant algorithmic and performance challenges.  In this work, we address these challenges and finally realize the promise of conditional computation, achieving greater than 1000x improvements in model capacity with only minor losses in computational efficiency on modern GPU clusters.  We introduce a Sparsely-Gated Mixture-of-Experts layer (MoE), consisting of up to thousands of feed-forward sub-networks.  A trainable gating network determines a sparse combination of these experts to use for each example.  We apply the MoE to the tasks of language modeling and machine translation, where model capacity is critical for absorbing the vast quantities of knowledge available in the training corpora.  We present model architectures in which a MoE with up to 137 billion parameters is applied convolutionally between stacked LSTM layers.  On large language modeling and machine translation benchmarks, these models achieve significantly better results than state-of-the-art at lower computational cost.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796115", "vector": [], "sparse_vector": [], "title": "Loss is its own Reward: Self-Supervision for Reinforcement Learning.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Max Argus", "<PERSON>"], "summary": "Reinforcement learning, driven by reward, addresses tasks by optimizing policies for expected return. Need the supervision be so narrow? Reward is delayed and sparse for many tasks, so we argue that reward alone is a noisy and impoverished signal for end-to-end optimization. To augment reward, we consider self-supervised tasks that incorporate states, actions, and successors to provide auxiliary losses. These losses offer ubiquitous and instantaneous supervision for representation learning even in the absence of reward. Self-supervised pre-training improves the data efficiency and returns of end-to-end reinforcement learning on Atari.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796116", "vector": [], "sparse_vector": [], "title": "A Smooth Optimisation Perspective on Training Feedforward Neural Networks.", "authors": ["<PERSON><PERSON>"], "summary": "We present a smooth optimisation perspective on training multilayer Feedforward Neural Networks (FNNs) in the supervised learning setting. By characterising the critical point conditions of an FNN based optimisation problem, we identify the conditions to eliminate local optima of the cost function. By studying the Hessian structure of the cost function at the global minima, we develop an approximate Newton FNN algorithm, which demonstrates promising convergence properties.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796117", "vector": [], "sparse_vector": [], "title": "Introspection: Accelerating Neural Network Training By Learning Weight Evolution.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Neural Networks are function approximators that have achieved state-of-the-art accuracy in numerous machine learning tasks. In spite of their great success in terms of accuracy, their large training time makes it difficult to use them for various tasks. In this paper, we explore the idea of learning weight evolution pattern from a simple network for accelerating training of novel neural networks.\n\nWe use a neural network to learn the training pattern from MNIST classification and utilize it to accelerate training of neural networks used for CIFAR-10 and ImageNet classification. Our method has a low memory footprint and is computationally efficient. This method can also be used with other optimizers to give faster convergence. The results indicate a general trend in the weight evolution during training of neural networks.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796118", "vector": [], "sparse_vector": [], "title": "RenderGAN: Generating Realistic Labeled Data.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Deep Convolutional Neuronal Networks (DCNNs) are showing remarkable performance on many computer vision tasks.  Due to their large parameter space, they require many labeled samples when trained in a supervised setting. The costs of annotating data manually can render the use of DCNNs infeasible.  We present a novel framework called RenderGAN that can generate large amounts of realistic, labeled images by combining a 3D model and the Generative Adversarial Network framework. In our approach, image augmentations (e.g. lighting, background, and detail) are learned from unlabeled data such that the generated images are strikingly realistic while preserving the labels known from the 3D model.  We apply the RenderGAN framework to generate images of barcode-like markers that are attached to honeybees. Training a DCNN on data generated by the RenderGAN yields considerably better performance than training it on various baselines.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796119", "vector": [], "sparse_vector": [], "title": "Offline bilingual word vectors, orthogonal transformations and the inverted softmax.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON>"], "summary": "Usually bilingual word vectors are trained \"online''. <PERSON><PERSON><PERSON> et al. showed they can also be found \"offline\"; whereby two pre-trained embeddings are aligned with a linear transformation, using dictionaries compiled from expert knowledge. In this work, we prove that the linear transformation between two spaces should be orthogonal. This transformation can be obtained using the singular value decomposition. We introduce a novel \"inverted softmax\" for identifying translation pairs, with which we improve the precision @1 of <PERSON><PERSON><PERSON>'s original mapping from 34% to 43%, when translating a test set composed of both common and rare English words into Italian. Orthogonal transformations are more robust to noise, enabling us to learn the transformation without expert bilingual signal by constructing a \"pseudo-dictionary\" from the identical character strings which appear in both languages, achieving 40% precision on the same test set. Finally, we extend our method to retrieve the true translations of English sentences from a corpus of 200k Italian sentences with a precision @1 of 68%.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796120", "vector": [], "sparse_vector": [], "title": "Amortised MAP Inference for Image Super-resolution.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Shi", "<PERSON><PERSON><PERSON>"], "summary": "Image super-resolution (SR) is an underdetermined inverse problem, where a large number of plausible high resolution images can explain the same downsampled image. Most current single image SR methods use empirical risk minimisation, often with a pixel-wise mean squared error (MSE) loss.\nHowever, the outputs from such methods tend to be blurry, over-smoothed and generally appear implausible. A more desirable approach would employ Maximum a Posteriori (MAP) inference, preferring solutions that always have a high probability under the image prior, and thus appear more plausible. Direct MAP estimation for SR is non-trivial, as it requires us to build a model for the image prior from samples. Here we introduce new methods for \\emph{amortised MAP inference} whereby we calculate the MAP estimate directly using a convolutional neural network. We first introduce a novel neural network architecture that performs a projection to the affine subspace of valid SR solutions ensuring that the high resolution output of the network is always consistent with the low resolution input. We show that, using this architecture, the amortised MAP inference problem reduces to minimising the cross-entropy between two distributions, similar to training generative models. We propose three methods to solve this optimisation problem: (1) Generative Adversarial Networks (GAN) (2) denoiser-guided SR which backpropagates gradient-estimates from denoising to train the network, and (3) a baseline method using a maximum-likelihood-trained image prior. Our experiments show that the GAN based approach performs best on real image data. Lastly, we establish a connection between GANs and amortised variational inference as in e.g. variational autoencoders.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796121", "vector": [], "sparse_vector": [], "title": "Generative Adversarial Learning of Markov Chains.", "authors": ["Jiaming Song", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We investigate generative adversarial training methods to learn a transition operator for a Markov chain, where the goal is to match its stationary distribution to a target data distribution. We propose a novel training procedure that avoids sampling directly from the stationary distribution, while still capable of reaching the target distribution asymptotically. The model can start from random noise, is likelihood free, and is able to generate multiple distinct samples during a single run. Preliminary experiment results show the chain can generate high quality samples when it approaches its stationary, even with smaller architectures traditionally considered for Generative Adversarial Nets.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796122", "vector": [], "sparse_vector": [], "title": "Char2Wav: End-to-End Speech Synthesis.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present Char2Wav, an end-to-end model for speech synthesis. Char2Wav has two components: a reader and a neural vocoder. The reader is an encoder-decoder model with attention. The encoder is a bidirectional recurrent neural network that accepts text or phonemes as inputs, while the decoder is a recurrent neural network (RNN) with attention that produces vocoder acoustic features. Neural vocoder refers to a conditional extension of SampleRNN which generates raw waveform samples from intermediate representations. Unlike traditional models for speech synthesis, Char2Wav learns to produce audio directly from text.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796123", "vector": [], "sparse_vector": [], "title": "Autoencoding Variational Inference For Topic Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Topic models are one of the most popular methods for learning representations of\ntext, but a major challenge is that any change to the topic model requires mathematically\nderiving a new inference algorithm. A promising approach to address\nthis problem is autoencoding variational Bayes (AEVB), but it has proven diffi-\ncult to apply to topic models in practice. We present what is to our knowledge the\nfirst effective AEVB based inference method for latent Dirichlet allocation (LDA),\nwhich we call Autoencoded Variational Inference For Topic Model (AVITM). This\nmodel tackles the problems caused for AEVB by the Dirichlet prior and by component\ncollapsing. We find that AVITM matches traditional methods in accuracy\nwith much better inference time. Indeed, because of the inference network, we\nfind that it is unnecessary to pay the computational cost of running variational\noptimization on test data. Because AVITM is black box, it is readily applied\nto new topic models. As a dramatic illustration of this, we present a new topic\nmodel called ProdLDA, that replaces the mixture model in LDA with a product\nof experts. By changing only one line of code from LDA, we find that ProdLDA\nyields much more interpretable topics, even if LDA is trained via collapsed Gibbs\nsampling.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796124", "vector": [], "sparse_vector": [], "title": "Third Person Imitation Learning.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Reinforcement learning (RL) makes it possible to train agents capable of achieving\nsophisticated goals in complex and uncertain environments. A key difficulty in\nreinforcement learning is specifying a reward function for the agent to optimize.\nTraditionally, imitation learning in RL has been used to overcome this problem.\nUnfortunately, hitherto imitation learning methods tend to require that demonstrations\nare supplied in the first-person: the agent is provided with a sequence of\nstates and a specification of the actions that it should have taken. While powerful,\nthis kind of imitation learning is limited by the relatively hard problem of collecting\nfirst-person demonstrations. Humans address this problem by learning from\nthird-person demonstrations: they observe other humans perform tasks, infer the\ntask, and accomplish the same task themselves.\nIn this paper, we present a method for unsupervised third-person imitation learning.\nHere third-person refers to training an agent to correctly achieve a simple\ngoal in a simple environment when it is provided a demonstration of a teacher\nachieving the same goal but from a different viewpoint; and unsupervised refers\nto the fact that the agent receives only these third-person demonstrations, and is\nnot provided a correspondence between teacher states and student states. Our\nmethods primary insight is that recent advances from domain confusion can be\nutilized to yield domain agnostic features which are crucial during the training\nprocess. To validate our approach, we report successful experiments on learning\nfrom third-person demonstrations in a pointmass domain, a reacher domain, and\ninverted pendulum.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796125", "vector": [], "sparse_vector": [], "title": "Generative Models and Model Criticism via Optimized Maximum Mean Discrepancy.", "authors": ["Danica J. Sutherland", "Hsiao<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose a method to optimize the representation and distinguishability of samples from two probability distributions, by maximizing the estimated power of a statistical test based on the maximum mean discrepancy (MMD). This optimized MMD is applied to the setting of unsupervised learning by generative adversarial networks (GAN), in which a model attempts to generate realistic samples, and a discriminator attempts to tell these apart from data samples. In this context, the MMD may be used in two roles: first, as a discriminator, either directly on the samples, or on features of the samples. Second, the MMD can be used to evaluate the performance of a generative model, by testing the model’s samples against a reference data set. In the latter role, the optimized MMD is particularly helpful, as it gives an interpretable indication of how the model and data distributions differ, even in cases where individual model samples are not easily distinguished either by eye or by classifier.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796126", "vector": [], "sparse_vector": [], "title": "Joint Multimodal Learning with Deep Generative Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We investigate deep generative models that can exchange multiple modalities bi-directionally, e.g., generating images from corresponding texts and vice versa. Recently, some studies handle multiple modalities on deep generative models. However, these models typically assume that modalities are forced to have a conditioned relation, i.e., we can only generate modalities in one direction. To achieve our objective, we should extract a joint representation that captures high-level concepts among all modalities and through which we can exchange them bi-directionally. As described herein, we propose a joint multimodal variational autoencoder (JMVAE), in which all modalities are independently conditioned on joint representation. In other words, it models a joint distribution of modalities. Furthermore, to be able to generate missing modalities from the remaining modalities properly, we develop an additional method, JMVAE-kl, that is trained by reducing the divergence between JMVAE's encoder and prepared networks of respective modalities. Our experiments show that JMVAE can generate multiple modalities bi-directionally.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796127", "vector": [], "sparse_vector": [], "title": "Unsupervised Cross-Domain Image Generation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of transferring a sample in one domain to an analog sample in another domain. Given two related domains, S and T, we would like to learn a generative function G that maps an input sample from S to the domain T, such that the output of a given representation function f, which accepts inputs in either domains, would remain unchanged. Other than f, the training data is unsupervised and consist of a set of samples from each domain, without any mapping between them. The Domain Transfer Network (DTN) we present employs a compound loss function that includes a multiclass GAN loss, an f preserving component, and a regularizing component that encourages G to map samples from T to themselves. We apply our method to visual domains including digits and face images and demonstrate its ability to generate convincing novel images of previously unseen entities, while preserving their identity.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796128", "vector": [], "sparse_vector": [], "title": "Mean teachers are better role models: Weight-averaged consistency targets improve semi-supervised deep learning results.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The recently proposed Temporal Ensembling has achieved state-of-the-art results in several semi-supervised learning benchmarks. It maintains an exponential moving average of label predictions on each training example, and penalizes predictions that are inconsistent with this target. However, because the targets change only once per epoch, Temporal Ensembling becomes unwieldy when learning large datasets. To overcome this problem, we propose Mean Teacher, a method that averages model weights instead of label predictions. As an additional benefit, Mean Teacher improves test accuracy and enables training with fewer labels than Temporal Ensembling. Mean Teacher achieves error rate 4.35% on SVHN with 250 labels, better than Temporal Ensembling does with 1000 labels.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796129", "vector": [], "sparse_vector": [], "title": "Lossy Image Compression with Compressive Autoencoders.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON> Shi", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose a new approach to the problem of optimizing autoencoders for lossy image compression. New media formats, changing hardware technology, as well as diverse requirements and content types create a need for compression algorithms which are more flexible than existing codecs. Autoencoders have the potential to address this need, but are difficult to optimize directly due to the inherent non-differentiabilty of the compression loss. We here show that minimal changes to the loss are sufficient to train deep autoencoders competitive with JPEG 2000 and outperforming recently proposed approaches based on RNNs. Our network is furthermore computationally efficient thanks to a sub-pixel architecture, which makes it suitable for high-resolution images. This is in contrast to previous work on autoencoders for compression using coarser approximations, shallower architectures, computationally expensive methods, or focusing on small images.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796130", "vector": [], "sparse_vector": [], "title": "Learning Features of Music From Scratch.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON>"], "summary": "This paper introduces a new large-scale music dataset, MusicNet, to serve as a source \nof supervision and evaluation of machine learning methods for music research. \nMusicNet consists of hundreds of freely-licensed classical music recordings \nby 10 composers, written for 11 instruments, together with instrument/note \nannotations resulting in over 1 million temporal labels on 34 hours of chamber music\nperformances under various studio and microphone conditions. \n\nThe paper defines a multi-label classification task to predict notes in musical recordings, \nalong with an evaluation protocol, and benchmarks several machine learning architectures for this task: \ni) learning from spectrogram features; \nii) end-to-end learning with a neural net; \niii) end-to-end learning with a convolutional neural net. \nThese experiments show that end-to-end models trained for note prediction learn frequency\nselective filters as a low-level representation of audio.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796131", "vector": [], "sparse_vector": [], "title": "Symmetry-Breaking Convergence Analysis of Certain Two-layered Neural Networks with ReLU nonlinearity.", "authors": ["Yuandong Tian"], "summary": "In this paper, we use dynamical system to analyze the nonlinear weight dynamics of two-layered bias-free networks in the form of $g(\\vx; \\vw) = \\sum_{j=1}^K \\sigma(\\vw_j\\trans\\vx)$, where $\\sigma(\\cdot)$ is ReLU nonlinearity. We assume that the input $\\vx$ follow Gaussian distribution. The network is trained using gradient descent to mimic the output of a teacher network of the same size with fixed parameters $\\vw\\opt$ using $l_2$ loss. We first show that when $K = 1$, the nonlinear dynamics can be written in close form, and converges to $\\vw\\opt$ with at least $(1-\\epsilon)/2$ probability, if random weight initializations of proper standard derivation ($\\sim 1/\\sqrt{d}$) is used, verifying empirical practice~\\cite{xavier, PReLU,lecun2012efficient}. For networks with many ReLU nodes ($K \\ge 2$), we apply our close form dynamics and prove that when the teacher parameters $\\{\\vw\\opt_j\\}_{j=1}^K$ forms orthonormal bases, (1) a symmetric weight initialization yields a convergence to a saddle point and (2) a certain symmetry-breaking weight initialization yields global convergence to $\\vw\\opt$ without local minima. To our knowledge, this is the first proof that shows global convergence in nonlinear neural network without unrealistic assumptions on the independence of ReLU activations. In addition, we also give a concise gradient update formulation for a multilayer ReLU network when it follows a teacher of the same size with $l_2$ loss. Simulations verify our theoretical analysis.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796132", "vector": [], "sparse_vector": [], "title": "Accelerating Eulerian Fluid Simulation With Convolutional Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Efficient simulation of the Navier-Stokes equations for fluid flow is a long standing problem in applied mathematics, for which state-of-the-art methods require large compute resources. In this work, we propose a data-driven approach that leverages the approximation power of deep-learning with the precision of standard solvers to obtain fast and highly realistic simulations. Our method solves the incompressible Euler equations using the standard operator splitting method, in which a large linear system with many free-parameters must be solved. We use a Convolutional Network with a highly tailored architecture, trained using a novel unsupervised learning framework to solve the linear system. We present real-time 2D and 3D simulations that outperform recently proposed data-driven methods; the obtained results are realistic and show good generalization properties.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796133", "vector": [], "sparse_vector": [], "title": "Deep Probabilistic Programming.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose <PERSON>, a Turing-complete probabilistic programming language. <PERSON> defines two compositional representations—random variables and inference. By treating inference as a first class citizen, on a par with modeling, we show that probabilistic programming can be as flexible and computationally efficient as traditional deep learning. For flexibility, <PERSON> makes it easy to fit the same model using a variety of composable inference methods, ranging from point estimation to variational inference to MCMC. In addition, <PERSON> can reuse the modeling representation as part of inference, facilitating the design of rich variational models and generative adversarial networks. For efficiency, <PERSON> is integrated into TensorFlow, providing significant speedups over existing probabilistic systems. For example, we show on a benchmark logistic regression task that <PERSON> is at least 35x faster than <PERSON> and 6x faster than PyMC3. Further, <PERSON> incurs no runtime overhead: it is as fast as handwritten TensorFlow.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796134", "vector": [], "sparse_vector": [], "title": "REBAR: Low-variance, unbiased gradient estimates for discrete latent variable models.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Learning in models with discrete latent variables is challenging due to high variance gradient estimators. Generally, approaches have relied on control variates to reduce the variance of the REINFORCE estimator. Recent work (<PERSON> et al. 2016, <PERSON><PERSON><PERSON> et al. 2016) has taken a different approach, introducing a continuous relaxation of discrete variables to produce low-variance, but biased, gradient estimates. In this work, we combine the two approaches through a novel control variate that produces low-variance, unbiased gradient estimates. We present encouraging preliminary results on a toy problem and on learning sigmoid belief networks.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796135", "vector": [], "sparse_vector": [], "title": "Adversarial Discriminative Domain Adaptation (workshop extended abstract).", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Domain adversarial approaches have been at the core of many recent unsupervised domain adaptation algorithms. However, each new algorithm is presented independently with limited or no connections mentioned across the works. Instead, in this work we propose a unified view of adversarial adaptation methods. We show how to describe a variety of state-of-the-art adaptation methods within our framework and furthermore use our generalized view in order to better understand the similarities and differences between these recent approaches. In turn, this framework facilitates the development of new adaptation methods through modeling choices that combine the desirable properties of multiple existing methods. In this way, we propose a novel adversarial adaptation method that is effective yet considerably simpler than other competing methods. We demonstrate the promise of our approach by achieving state-of-the-art unsupervised adaptation results on the standard Office dataset.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796136", "vector": [], "sparse_vector": [], "title": "Soft Weight-Sharing for Neural Network Compression.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The success of deep learning in numerous application domains created the desire to run and train them on mobile devices. This however, conflicts with their computationally, memory and energy intense nature, leading to a growing interest in compression.\nRecent work by <PERSON> et al. (2016) propose a pipeline that involves retraining, pruning and quantization of neural network weights, obtaining state-of-the-art compression rates.\nIn this paper, we show that competitive compression rates can be achieved by using a version of \"soft weight-sharing\" (<PERSON> & Hinton, 1991). Our method achieves both quantization and pruning in one simple (re-)training procedure. \nThis point of view also exposes the relation between compression and the minimum description length (MDL) principle.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796137", "vector": [], "sparse_vector": [], "title": "Do Deep Convolutional Nets Really Need to be Deep and Convolutional?", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Yes, they do.  This paper provides the first empirical demonstration that deep convolutional models really need to be both deep and convolutional, even when trained with methods such as distillation that allow small or shallow models of high accuracy to be trained.  Although previous research showed that shallow feed-forward nets sometimes can learn the complex functions previously learned by deep nets while using the same number of parameters as the deep models they mimic, in this paper we demonstrate that the same methods cannot be used to train accurate models on CIFAR-10 unless the student models contain multiple layers of convolution.  Although the student models do not have to be as deep as the teacher model they mimic, the students need multiple convolutional layers to learn functions of comparable accuracy as the deep convolutional teacher.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796138", "vector": [], "sparse_vector": [], "title": "What does it take to generate natural textures?", "authors": ["<PERSON>", "Wieland Brendel", "Leon A. <PERSON>", "<PERSON>"], "summary": "Natural image generation is currently one of the most actively explored fields in Deep Learning. Many approaches, e.g. for state-of-the-art artistic style transfer or natural texture synthesis, rely on the statistics of hierarchical representations in supervisedly trained deep neural networks. It is, however, unclear what aspects of this feature representation are crucial for natural image generation: is it the depth, the pooling or the training of the features on natural images? We here address this question for the task of natural texture synthesis and show that none of the above aspects are indispensable. Instead, we demonstrate that natural textures of high perceptual quality can be generated from networks with only a single layer, no pooling and random filters.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796139", "vector": [], "sparse_vector": [], "title": "Episodic Exploration for Deep Deterministic Policies for StarCraft Micromanagement.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider scenarios from the real-time strategy game StarCraft as benchmarks for reinforcement learning algorithms. We focus on micromanagement, that is, the short-term, low-level control of team members during a battle. We propose several scenarios that are challenging for reinforcement learning algorithms because the state- action space is very large, and there is no obvious feature representation for the value functions. We describe our approach to tackle the micromanagement scenarios with deep neural network controllers from raw state features given by the game engine. We also present a heuristic reinforcement learning algorithm which combines direct exploration in the policy space and backpropagation. This algorithm collects traces for learning using deterministic policies, which appears much more efficient than, e.g., ε-greedy exploration. Experiments show that this algorithm allows to successfully learn non-trivial strategies for scenarios with armies of up to 15 agents, where both Q-learning and REINFORCE struggle.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796140", "vector": [], "sparse_vector": [], "title": "Encoding and Decoding Representations with Sum- and Max-Product Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Sum-Product Networks (SPNs) are deep density estimators allowing exact and tractable inference. While up to now SPNs have been employed as black-box inference machines, we exploit them as feature extractors for unsupervised Representation\nLearning. Representations learned by SPNs are rich probabilistic and hierarchical part-based features. SPNs converted into Max-Product Networks (MPNs) provide a way to decode these representations back to the original input space. In extensive experiments, SPN and MPN encoding and decoding schemes prove highly competitive for Multi-Label Classification tasks.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796141", "vector": [], "sparse_vector": [], "title": "Decomposing Motion and Content for Natural Video Sequence Prediction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Hong", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose a deep neural network for the prediction of future frames in natural video sequences. To effectively handle complex evolution of pixels in videos, we propose to decompose the motion and content, two key components generating dynamics in videos. Our model is built upon the Encoder-Decoder Convolutional Neural Network and Convolutional LSTM for pixel-level prediction, which independently capture the spatial layout of an image and the corresponding temporal dynamics. By independently modeling motion and content, predicting the next frame reduces to converting the extracted content features into the next frame content by the identified motion features, which simplifies the task of prediction. Our model is end-to-end trainable over multiple time steps, and naturally learns to decompose motion and content without separate training. We evaluate the pro- posed network architecture on human activity videos using KTH, Weizmann action, and UCF-101 datasets. We show state-of-the-art performance in comparison to recent approaches. To the best of our knowledge, this is the first end-to-end trainable network architecture with motion and content separation to model the spatio-temporal dynamics for pixel-level future prediction in natural videos.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796142", "vector": [], "sparse_vector": [], "title": "On Improving the Numerical Stability of Winograd Convolutions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Deep convolutional neural networks rely on heavily optimized convolution algorithms. Winograd convolutions provide an efficient approach to performing such convolutions. Using larger Winograd convolution tiles, the convolution will become more efficient but less numerically accurate. Here we provide some approaches to mitigating this numerical inaccuracy. We will exemplify these approaches by working on a tile much larger than any previously documented: F(9x9, 5x5). Using these approaches, we will show that such a tile can be used to train modern networks and provide performance benefits.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796143", "vector": [], "sparse_vector": [], "title": "A Compare-Aggregate Model for Matching Text Sequences.", "authors": ["<PERSON><PERSON><PERSON>", "Jing <PERSON>"], "summary": "Many NLP tasks including machine comprehension, answer selection and text entailment require the comparison between sequences. Matching the important units between sequences is a key to solve these problems. In this paper, we present a general \"compare-aggregate\" framework that performs word-level matching followed by aggregation using Convolutional Neural Networks. We particularly focus on the different comparison functions we can use to match two vectors. We use four different datasets to evaluate the model. We find that some simple comparison functions based on element-wise operations can work better than standard neural network and neural tensor network.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796144", "vector": [], "sparse_vector": [], "title": "Machine Comprehension Using Match-LSTM and Answer Pointer.", "authors": ["<PERSON><PERSON><PERSON>", "Jing <PERSON>"], "summary": "Machine comprehension of text is an important problem in natural language processing. A recently released dataset, the Stanford Question Answering Dataset (SQuAD), offers a large number of real questions and their answers created by humans through crowdsourcing. SQuAD provides a challenging testbed for evaluating machine comprehension algorithms, partly because compared with previous datasets, in SQuAD the answers do not come from a small set of candidate answers and they have variable lengths. We propose an end-to-end neural architecture for the task. The architecture is based on match-LSTM, a model we proposed previously for textual entailment, and Pointer Net, a sequence-to-sequence model proposed by <PERSON><PERSON><PERSON> et al. (2015) to constrain the output tokens to be from the input sequences. We propose two ways of using Pointer Net for our tasks. Our experiments show that both of our two models substantially outperform the best results obtained by <PERSON> et al. (2016) using logistic regression and manually crafted features. Besides, our boundary model also achieves the best performance on the MSMARCO dataset (<PERSON><PERSON><PERSON> et al. 2016).", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796145", "vector": [], "sparse_vector": [], "title": "Training Compressed Fully-Connected Networks with a Density-Diversity Penalty.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Deep models have achieved great success on a variety of challenging tasks. How- ever, the models that achieve great performance often have an enormous number of parameters, leading to correspondingly great demands on both computational and memory resources, especially for fully-connected layers. In this work, we propose a new “density-diversity penalty” regularizer that can be applied to fully-connected layers of neural networks during training. We show that using this regularizer results in significantly fewer parameters (i.e., high sparsity), and also significantly fewer distinct values (i.e., low diversity), so that the trained weight matrices can be highly compressed without any appreciable loss in performance. The resulting trained models can hence reside on computational platforms (e.g., portables, Internet-of-Things devices) where it otherwise would be prohibitive.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796146", "vector": [], "sparse_vector": [], "title": "A Theoretical Framework for Robustness of (Deep) Classifiers against Adversarial Samples.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Most machine learning classifiers, including deep neural networks, are vulnerable to adversarial examples. Such inputs are typically generated by adding small but purposeful modifications that lead to incorrect outputs while imperceptible to human eyes. \nThe goal of this paper is not to introduce a single method, but to make theoretical steps toward fully understanding adversarial examples. By using concepts from topology, our theoretical analysis brings forth the key reasons why an adversarial example can fool a classifier ($f_1$) and adds its oracle ($f_2$, like human eyes) in such analysis. \nBy investigating the topological relationship between two (pseudo)metric spaces corresponding to predictor $f_1$ and oracle $f_2$, we develop necessary and sufficient conditions that can determine if $f_1$ is always robust (strong-robust) against adversarial examples according to $f_2$. Interestingly our theorems indicate that just one unnecessary feature can make $f_1$ not strong-robust, and the right feature representation learning is the key to getting a classifier that is both accurate and strong robust.\nTL;DR: We propose a theoretical framework to explain and measure model robustness and harden DNN model against adversarial attacks.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796147", "vector": [], "sparse_vector": [], "title": "Improving Generative Adversarial Networks with Denoising Feature Matching.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose an augmented training procedure for generative adversarial networks designed to address shortcomings of the original by directing the generator towards probable configurations of abstract discriminator features. We estimate and track the distribution of these features, as computed from data, with a denoising auto-encoder, and use it to propose high-level targets for the generator. We combine this new loss with the original and evaluate the hybrid criterion on the task of unsupervised image synthesis from datasets comprising a diverse set of visual categories, noting a qualitative and quantitative improvement in the ``objectness'' of the resulting samples.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796148", "vector": [], "sparse_vector": [], "title": "Joint Training of Ratings and Reviews with Recurrent Recommender Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Accurate modeling of ratings and text reviews is at the core of successful recommender systems. While neural networks have been remarkably successful in modeling images and natural language, they have been largely unexplored in recommender system research. In this paper, we provide a  neural network model that combines ratings, reviews, and temporal patterns to learn highly accurate recommendations. We co-train for prediction on both numerical ratings and natural language reviews, as well as using a recurrent architecture to capture the dynamic components of users' and items' states. We demonstrate that incorporating text reviews and temporal dynamic gives state-of-the-art results over the IMDb dataset.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796149", "vector": [], "sparse_vector": [], "title": "On the Quantitative Analysis of Decoder-Based Generative Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The past several years have seen remarkable progress in generative models which produce convincing samples of images and other modalities. A shared component of some popular models such as generative adversarial networks and generative moment matching networks, is a decoder network, a parametric deep neural net that defines a generative distribution. Unfortunately, it can be difficult to quantify the performance of these models because of the intractability of log-likelihood estimation, and inspecting samples can be misleading. We propose to use Annealed Importance Sampling for evaluating log-likelihoods for decoder-based models and validate its accuracy using bidirectional Monte Carlo. Using this technique, we analyze the performance of decoder-based models, the effectiveness of existing log-likelihood estimators, the degree of overfitting, and the degree to which these models miss important modes of the data distribution.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796150", "vector": [], "sparse_vector": [], "title": "Training Agent for First-Person Shooter Game with Actor-Critic Curriculum Learning.", "authors": ["<PERSON><PERSON>", "Yuandong Tian"], "summary": "In this paper, we propose a novel framework for training vision-based agent for First-Person Shooter (FPS) Game, in particular Doom.\nOur framework combines the state-of-the-art reinforcement learning approach (Asynchronous Advantage Actor-Critic (A3C) model) with curriculum learning. Our model is simple in design and only uses game states from the AI side, rather than using opponents' information. On a known map, our agent won 10 out of the 11 attended games and the champion of Track1 in ViZDoom AI Competition 2016 by a large margin, 35\\% higher score than the second place.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796151", "vector": [], "sparse_vector": [], "title": "Data Noising as Smoothing in Neural Network Language Models.", "authors": ["<PERSON><PERSON><PERSON>", "Sid<PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Data noising is an effective technique for regularizing neural network models. While noising is widely adopted in application domains such as vision and speech, commonly used noising primitives have not been developed for discrete sequence-level settings such as language modeling. In this paper, we derive a connection between input noising in neural network language models and smoothing in n-gram models. Using this connection, we draw upon ideas from smoothing to develop effective noising schemes. We demonstrate performance gains when applying the proposed schemes to language modeling and machine translation. Finally, we provide empirical analysis validating the relationship between noising and smoothing.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796152", "vector": [], "sparse_vector": [], "title": "Dynamic Coattention Networks For Question Answering.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Several deep learning models have been proposed for question answering. How- ever, due to their single-pass nature, they have no way to recover from local maxima corresponding to incorrect answers. To address this problem, we introduce the Dynamic Coattention Network (DCN) for question answering. The DCN first fuses co-dependent representations of the question and the document in order to focus on relevant parts of both. Then a dynamic pointer decoder iterates over potential answer spans. This iterative procedure enables the model to recover from initial local maxima corresponding to incorrect answers. On the Stanford question answering dataset, a single DCN model improves the previous state of the art from 71.0% F1 to 75.9%, while a DCN ensemble obtains 80.4% F1.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796153", "vector": [], "sparse_vector": [], "title": "Unseen Style Transfer Based on a Conditional Fast Style Transfer Network.", "authors": ["<PERSON><PERSON>"], "summary": "In this paper, we propose a feed-forward neural style transfer network which can\ntransfer unseen arbitrary styles. To do that, first, we extend the fast neural style\ntransfer network proposed by <PERSON> et al. (2016) so that the network can learn\nmultiple styles at the same time by adding a conditional input. We call this as “a\nconditional style transfer network”. Next, we add a style condition network which\ngenerates a conditional signal from a style image directly, and train “a conditional\nstyle transfer network with a style condition network” in an end-to-end manner.\nThe proposed network can generate a stylized image from a content image and a\nstyle image in one-time feed-forward computation instantly.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796154", "vector": [], "sparse_vector": [], "title": "Words or Characters? Fine-grained Gating for Reading Comprehension.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous work combines word-level and character-level representations using concatenation or scalar weighting, which is suboptimal for high-level tasks like reading comprehension. We present a fine-grained gating mechanism to dynamically combine word-level and character-level representations based on properties of the words. We also extend the idea of fine-grained gating to modeling the interaction between questions and paragraphs for reading comprehension. Experiments show that our approach can improve the performance on reading comprehension tasks, achieving new state-of-the-art results on the Children's Book Test and Who Did What datasets. To demonstrate the generality of our gating mechanism, we also show improved results on a social media tag prediction task.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796155", "vector": [], "sparse_vector": [], "title": "Deep Multi-task Representation Learning: A Tensor Factorisation Approach.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Most contemporary multi-task learning methods assume linear models. This setting is considered shallow in the era of deep learning. In this paper, we present a new deep multi-task representation learning framework that learns cross-task sharing structure at every layer in a deep network. Our approach is based on generalising the matrix factorisation techniques explicitly or implicitly used by many conventional MTL algorithms to tensor factorisation, to realise automatic learning of end-to-end knowledge sharing in deep networks. This is in contrast to existing deep learning approaches that need a user-defined multi-task sharing strategy. Our approach applies to both homogeneous and heterogeneous MTL. Experiments demonstrate the efficacy of our deep multi-task representation learning in terms of both higher accuracy and fewer design choices.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796156", "vector": [], "sparse_vector": [], "title": "Trace Norm Regularised Deep Multi-Task Learning.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We propose a framework for training multiple neural networks simultaneously. The parameters from all models are regularised by the tensor trace norm, so that each neural network is encouraged to reuse others' parameters if possible -- this is the main motivation behind multi-task learning. In contrast to many deep multi-task learning models, we do not predefine a parameter sharing strategy by specifying which layers have tied parameters. Instead, our framework considers sharing for all shareable layers, and the sharing strategy is learned in a data-driven way.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796157", "vector": [], "sparse_vector": [], "title": "LR-GAN: Layered Recursive Generative Adversarial Networks for Image Generation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present LR-GAN: an adversarial image generation model which takes scene structure and context into account. Unlike previous generative adversarial networks (GANs), the proposed GAN learns to generate image background and foregrounds separately and recursively, and stitch the foregrounds on the background in a contextually relevant manner to produce a complete natural image. For each foreground, the model learns to generate its appearance, shape and pose. The whole model is unsupervised, and is trained in an end-to-end manner with conventional gradient descent methods. The experiments demonstrate that LR-GAN can generate more natural images with objects that are more human recognizable than baseline GANs.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796158", "vector": [], "sparse_vector": [], "title": "Lie-Access Neural Turing Machines.", "authors": ["<PERSON>", "<PERSON>"], "summary": "External neural memory structures have recently become a popular tool for\n  algorithmic deep learning\n  (<PERSON> et al. 2014; <PERSON> et al. 2014).  These models\n  generally utilize differentiable versions of traditional discrete\n  memory-access structures (random access, stacks, tapes) to provide\n  the storage necessary for computational tasks.  In\n  this work, we argue that these neural memory systems lack specific\n  structure important for relative indexing, and propose an\n  alternative model, Lie-access memory, that is explicitly designed\n  for the neural setting.  In this paradigm, memory is accessed using\n  a continuous head in a key-space manifold. The head is moved via Lie\n  group actions, such as shifts or rotations, generated by a\n  controller, and memory access is performed by linear smoothing in\n  key space. We argue that Lie groups provide a natural generalization\n  of discrete memory structures, such as Turing machines, as they\n  provide inverse and identity operators while maintaining\n  differentiability. To experiment with this approach, we implement\n  a simplified Lie-access neural Turing machine (LANTM) with\n  different Lie groups.  We find that this approach is able to perform\n  well on a range of algorithmic tasks.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796159", "vector": [], "sparse_vector": [], "title": "Transfer Learning for Sequence Tagging with Hierarchical Recurrent Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recent papers have shown that neural networks obtain state-of-the-art performance on several different sequence tagging tasks. One appealing property of such systems is their generality, as excellent performance can be achieved with a unified architecture and without task-specific feature engineering.  However, it is unclear if such systems can be used for tasks without large amounts of training data. In this paper we explore the problem of transfer learning for neural sequence taggers, where a source task with plentiful annotations (e.g., POS tagging on Penn Treebank) is used to improve performance on a target task with fewer available annotations (e.g., POS tagging for microblogs). We examine the effects of transfer learning for deep hierarchical recurrent networks across domains, applications, and languages, and show that significant improvement can often be obtained.  These improvements lead to improvements over the current state-of-the-art on several well-studied tasks.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796160", "vector": [], "sparse_vector": [], "title": "Support Regularized Sparse Coding and Its Fast Encoder.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Sparse coding represents a signal by a linear combination of only a few atoms of a learned over-complete dictionary. While sparse coding exhibits compelling performance for various machine learning tasks, the process of obtaining sparse code with fixed dictionary is independent for each data point without considering the geometric information and manifold structure of the entire data. We propose Support Regularized Sparse Coding (SRSC) which produces sparse codes that account for the manifold structure of the data by encouraging nearby data in the manifold to choose similar dictionary atoms. In this way, the obtained support regularized sparse codes capture the locally linear structure of the data manifold and enjoy robustness to data noise. We present the optimization algorithm of SRSC with theoretical guarantee for the optimization over the sparse codes. We also propose a feed-forward neural network termed Deep Support Regularized Sparse Coding (Deep-SRSC) as a fast encoder to approximate the sparse codes generated by SRSC. Extensive experimental results demonstrate the effectiveness of SRSC and Deep-SRSC.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796161", "vector": [], "sparse_vector": [], "title": "Learning to Compose Words into Sentences with Reinforcement Learning.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We use reinforcement learning to learn\ntree-structured neural networks for computing representations of natural language sentences.\nIn contrast with prior work on tree-structured models, in which the trees are either provided as input or\npredicted using supervision from explicit treebank annotations,\nthe tree structures in this work are optimized to improve performance on a downstream task.\nExperiments demonstrate the benefit of\nlearning task-specific composition orders, outperforming both sequential encoders and recursive encoders based on treebank annotations.\nWe analyze the induced trees and show that while they discover\nsome linguistically intuitive structures (e.g., noun phrases, simple verb phrases),\nthey are different than conventional English syntactic structures.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796162", "vector": [], "sparse_vector": [], "title": "The Neural Noisy Channel.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We formulate sequence to sequence transduction as a noisy channel decoding problem and use recurrent neural networks to parameterise the source and channel models. Unlike direct models which can suffer from explaining-away effects during training, noisy channel models must produce outputs that explain their inputs, and their component models can be trained with not only paired training samples but also unpaired samples from the marginal output distribution. Using a latent variable to control how much of the conditioning sequence the channel model needs to read in order to generate a subsequent symbol, we obtain a tractable and effective beam search decoder. Experimental results on abstractive sentence summarisation, morphological inflection, and machine translation show that noisy channel models outperform direct models, and that they significantly benefit from increased amounts of unpaired output data that direct models cannot easily use.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796163", "vector": [], "sparse_vector": [], "title": "Towards Deep Interpretability (MUS-ROVER II): Learning Hierarchical Representations of Tonal Music.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "Music theory studies the regularity of patterns in music to capture concepts underlying music styles and composers' decisions. This paper continues the study of building \\emph{automatic theorists} (rovers) to learn and represent music concepts that lead to human interpretable knowledge and further lead to materials for educating people. Our previous work took a first step in algorithmic concept learning of tonal music, studying high-level representations (concepts) of symbolic music (scores) and extracting interpretable rules for composition. This paper further studies the representation \\emph{hierarchy} through the learning process, and supports \\emph{adaptive} 2D memory selection in the resulting language model. This leads to a deeper-level interpretability that expands from individual rules to a dynamic system of rules, making the entire rule learning process more cognitive. The outcome is a new rover, MUS-ROVER \\RN{2}, trained on <PERSON>'s chorales, which outputs customizable syllabi for learning compositional rules. We demonstrate comparable results to our music pedagogy, while also presenting the differences and variations. In addition, we point out the rover's potential usages in style recognition and synthesis, as well as applications beyond music.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796164", "vector": [], "sparse_vector": [], "title": "Paying More Attention to Attention: Improving the Performance of Convolutional Neural Networks via Attention Transfer.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Attention plays a critical role in human visual experience. Furthermore, it has recently been demonstrated that attention can also play an important role in the context of applying artificial neural networks to a variety of tasks from fields such as computer vision and NLP. In this work we show that, by properly defining attention for convolutional neural networks, we can actually use this type of information in order to significantly improve the performance of a student CNN network by forcing it to mimic the attention maps of a powerful teacher network. To that end, we propose several novel methods of transferring attention, showing consistent improvement across a variety of datasets and convolutional neural network architectures.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796165", "vector": [], "sparse_vector": [], "title": "Central Moment Discrepancy (CMD) for Domain-Invariant Representation Learning.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The learning of domain-invariant representations in the context of domain adaptation with neural networks is considered. We propose a new regularization method that minimizes the  domain-specific latent feature representations directly in the hidden activation space. Although some standard distribution matching approaches exist that can be interpreted as the matching of weighted sums of moments, e.g. Maximum Mean Discrepancy (MMD), an explicit order-wise matching of higher order moments has not been considered before.\nWe propose to match the higher order central moments of probability distributions by means of order-wise moment differences. Our model does not require computationally expensive distance and kernel matrix computations. We utilize the equivalent representation of probability distributions by moment sequences to define a new distance function, called Central Moment Discrepancy (CMD). We prove that CMD is a metric on the set of probability distributions on a compact interval. We further prove that convergence of probability distributions on compact intervals w.r.t. the new metric implies convergence in distribution of the respective random variables.\nWe test our approach on two different benchmark data sets for object recognition (Office) and sentiment analysis of product reviews (Amazon reviews). CMD achieves a new state-of-the-art performance on most domain adaptation tasks of Office and outperforms networks trained with MMD, Variational Fair Autoencoders and Domain Adversarial Neural Networks on Amazon reviews. In addition, a post-hoc parameter sensitivity analysis shows that the new approach is stable w. r. t. parameter changes in a certain interval. The source code of the experiments is publicly available.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796166", "vector": [], "sparse_vector": [], "title": "Understanding deep learning requires rethinking generalization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Oriol Vinyals"], "summary": "Despite their massive size, successful deep artificial neural networks can\nexhibit a remarkably small difference between training and test performance.\nConventional wisdom attributes small generalization error either to properties\nof the model family, or to the regularization techniques used during training.\n\nThrough extensive systematic experiments, we show how these traditional\napproaches fail to explain why large neural networks generalize well in\npractice. Specifically, our experiments establish that state-of-the-art\nconvolutional networks for image classification trained with stochastic\ngradient methods easily fit a random labeling of the training data. This\nphenomenon is qualitatively unaffected by explicit regularization, and occurs\neven if we replace the true images by completely unstructured random noise. We\ncorroborate these experimental findings with a theoretical construction\nshowing that simple depth two neural networks already have perfect finite\nsample expressivity as soon as the number of parameters exceeds the\nnumber of data points as it usually does in practice.\n\nWe interpret our experimental findings by comparison with traditional models.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796167", "vector": [], "sparse_vector": [], "title": "DeepDSL: A Compilation-based Domain-Specific Language for Deep Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In recent years, Deep Learning (DL) has found great success in domains such as multimedia understanding. However, the complex nature of multimedia data makes it difficult to develop DL-based software. The state-of-the-art tools, such as Caffe, TensorFlow, Torch7, and CNTK, while are successful in their applicable domains, are programming libraries with fixed user interface, internal representation, and execution environment. This makes it difficult to implement portable and customized DL applications.\n\nIn this paper, we present DeepDSL, a domain specific language (DSL) embedded in Scala, that compiles deep networks written in DeepDSL to Java source code. Deep DSL provides \n\n(1) intuitive constructs to support compact encoding of deep networks; \n(2) symbolic gradient derivation of the networks; \n(3) static analysis for memory consumption and error detection; and \n(4) DSL-level optimization to improve memory and runtime efficiency. \n\nDeepDSL programs are compiled into compact, efficient, customizable, and portable Java source code, which operates the CUDA and CUDNN interfaces running on NVIDIA GPU via a Java Native Interface (JNI) library. We evaluated DeepDSL with a number of popular DL networks. Our experiments show that the compiled programs have very competitive runtime performance and memory efficiency compared to the existing libraries.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796168", "vector": [], "sparse_vector": [], "title": "Energy-based Generative Adversarial Networks.", "authors": ["Jun<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce the \"Energy-based Generative Adversarial Network\" model (EBGAN) which views the discriminator as an energy function that attributes low energies to the regions near the data manifold and higher energies to other regions. Similar to the probabilistic GANs, a generator is seen as being trained to produce contrastive samples with minimal energies, while the discriminator is trained to assign high energies to these generated samples. Viewing the discriminator as an energy function allows to use a wide variety of architectures and loss functionals in addition to the usual binary classifier with logistic output. Among them, we show one instantiation of EBGAN framework as using an auto-encoder architecture, with the energy being the reconstruction error, in place of the discriminator. We show that this form of EBGAN exhibits more stable behavior than regular GANs during training. We also show that a single-scale architecture can be trained to generate high-resolution images.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796169", "vector": [], "sparse_vector": [], "title": "Incremental Network Quantization: Towards Lossless CNNs with Low-precision Weights.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents incremental network quantization (INQ), a novel method, targeting to efficiently convert any pre-trained full-precision convolutional neural network (CNN) model into a low-precision version whose weights are constrained to be either powers of two or zero. Unlike existing methods which are struggled in noticeable accuracy loss, our INQ has the potential to resolve this issue, as benefiting from two innovations. On one hand, we introduce three interdependent operations, namely weight partition, group-wise quantization and re-training. A well-proven measure is employed to divide the weights in each layer of a pre-trained CNN model into two disjoint groups. The weights in the first group are responsible to form a low-precision base, thus they are quantized by a variable-length encoding method. The weights in the other group are responsible to compensate for the accuracy loss from the quantization, thus they are the ones to be re-trained. On the other hand, these three operations are repeated on the latest re-trained group in an iterative manner until all the weights are converted into low-precision ones, acting as an incremental network quantization and accuracy enhancement procedure. Extensive experiments on the ImageNet classification task using almost all known deep CNN architectures including AlexNet, VGG-16, GoogleNet and ResNets well testify the efficacy of the proposed method. Specifically, at 5-bit quantization (a variable-length encoding: 1 bit for representing zero value, and the remaining 4 bits represent at most 16 different values for the powers of two), our models have improved accuracy than the 32-bit floating-point references. Taking ResNet-18 as an example, we further show that our quantized models with 4-bit, 3-bit and 2-bit ternary weights have improved or very similar accuracy against its 32-bit floating-point baseline. Besides, impressive results with the combination of network pruning and INQ are also reported. We believe that our method sheds new insights on how to make deep CNNs to be applicable on mobile or embedded devices. The code will be made publicly available.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796170", "vector": [], "sparse_vector": [], "title": "Trained Ternary Quantization.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Deep neural networks are widely used in machine learning applications. However, the deployment of large neural networks models can be difficult to deploy on mobile devices with limited power budgets. To solve this problem, we propose Trained Ternary Quantization (TTQ), a method that can reduce the precision of weights in neural networks to ternary values. This method has very little accuracy degradation and can even improve the accuracy of some models (32, 44, 56-layer ResNet) on CIFAR-10 and AlexNet on ImageNet. And our AlexNet model is trained from scratch, which means it’s as easy as to train normal full precision model. We highlight our trained quantization method that can learn both ternary values and ternary assignment. During inference, only ternary values (2-bit weights) and scaling factors are needed, therefore our models are nearly 16× smaller than full- precision models. Our ternary models can also be viewed as sparse binary weight networks, which can potentially be accelerated with custom circuit. Experiments on CIFAR-10 show that the ternary models obtained by trained quantization method outperform full-precision models of ResNet-32,44,56 by 0.04%, 0.16%, 0.36%, respectively. On ImageNet, our model outperforms full-precision AlexNet model by 0.3% of Top-1 accuracy and outperforms previous ternary models by 3%.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796171", "vector": [], "sparse_vector": [], "title": "Training Triplet Networks with GAN.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Triplet networks are widely used models that are characterized by good performance in classification and retrieval tasks. In this work we propose to train a triplet network by putting it as the discriminator in Generative Adversarial Nets (GANs). We make use of the good capability of representation learning of the discriminator to increase the predictive quality of the model. We evaluated our approach on Cifar10 and MNIST datasets and observed significant improvement on the classification performance using the simple k-nn method.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796172", "vector": [], "sparse_vector": [], "title": "Visualizing Deep Neural Network Decisions: Prediction Difference Analysis.", "authors": ["<PERSON><PERSON>", "Taco S. Cohen", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This article presents the prediction difference analysis method for visualizing the response of a deep neural network to a specific input. When classifying images, the method highlights areas in a given input image that provide evidence for or against a certain class. It overcomes several shortcoming of previous methods and provides great additional insight into the decision making process of classifiers. Making neural network decisions interpretable through visualization is important both to improve models and to accelerate the adoption of black-box classifiers in application areas such as medicine. We illustrate the method in experiments on natural images (ImageNet data), as well as medical images (MRI brain scans).", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}, {"primary_key": "3796173", "vector": [], "sparse_vector": [], "title": "Neural Architecture Search with Reinforcement Learning.", "authors": ["<PERSON><PERSON>", "<PERSON>uo<PERSON> <PERSON><PERSON>"], "summary": "Neural networks are powerful and flexible models that work well for many difficult learning tasks in image, speech and natural language understanding. Despite their success, neural networks are still hard to design. In this paper, we use a recurrent network to generate the model descriptions of neural networks and train this RNN with reinforcement learning to maximize the expected accuracy of the generated architectures on a validation set. On the CIFAR-10 dataset, our method, starting from scratch, can design a novel network architecture that rivals the best human-invented architecture in terms of test set accuracy. Our CIFAR-10 model achieves a test error rate of 3.65, which is 0.09 percent better and 1.05x faster than the previous state-of-the-art model that used a similar architectural scheme. On the Penn Treebank dataset, our model can compose a novel recurrent cell that outperforms the widely-used LSTM cell, and other state-of-the-art baselines.  Our cell achieves a test set perplexity of 62.4 on the Penn Treebank, which is 3.6 perplexity better than the previous state-of-the-art model. The cell can also be transferred to the character language modeling task on PTB and achieves a state-of-the-art perplexity of 1.214.", "published": "2017-01-01", "category": "iclr", "pdf_url": "", "sub_summary": "", "source": "iclr", "doi": ""}]