[{"primary_key": "1741890", "vector": [], "sparse_vector": [], "title": "Get a Model! Model Hijacking Attack Against Machine Learning Models.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Machine learning (ML) has established itself as a cornerstone for various critical applications ranging from autonomous driving to authentication systems. However, with this increasing adoption rate of machine learning models, multiple attacks have emerged. One class of such attacks is training time attack, whereby an adversary executes their attack before or during the machine learning model training. In this work, we propose a new training time attack against computer vision based machine learning models, namely model hijacking attack. The adversary aims to hijack a target model to execute a different task than its original one without the model owner noticing. Model hijacking can cause accountability and security risks since a hijacked model owner can be framed for having their model offering illegal or unethical services. Model hijacking attacks are launched in the same way as existing data poisoning attacks. However, one requirement of the model hijacking attack is to be stealthy, i.e., the data samples used to hijack the target model should look similar to the model's original training dataset. To this end, we propose two different model hijacking attacks, namely Chameleon and Adverse Chameleon, based on a novel encoder-decoder style ML model, namely the Camouflager. Our evaluation shows that both of our model hijacking attacks achieve a high attack success rate, with a negligible drop in model utility.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741891", "vector": [], "sparse_vector": [], "title": "Subverting Stateful Firewalls with Protocol States.", "authors": ["<PERSON><PERSON>"], "summary": "We analyzed the generation of protocol header fields in the implementations of multiple TCP/IP network stacks and found new ways to leak information about global protocol states. We then demonstrated new covert channels by remotely observing and modifying the system's global state via these protocol fields. Unlike earlier works, our research focuses on hosts that reside in firewalled networks (including source address validation – SAV), which is a very common scenario nowadays. Our attacks are designed to be non-disruptive – in the exfiltration scenario, this makes the attacks stealthier and thus extends their longevity, and in case of host alias resolution and similar techniques – this ensures the techniques are ethical. We focused on ICMP, which is commonly served by firewalls, and on UDP, which is forecasted to take a more prominent share of the Internet traffic with the advent of HTTP/3 and QUIC, though we report results for TCP as well. The information leakage scenarios we discovered enable the construction of practical covert channels which directly pierce firewalls, or indirectly establish communication via hosts in firewalled networks that also employ SAV. We describe and testthree novel attacks in this context: exfiltration via the firewall itself, exfiltration via a DMZ host, and exfiltration via co-residentcontainers. These are three generic, new use cases for covert channels that work around firewalling and enable devices thatare not allowed direct communication with the Internet, to still exfiltrate data out of the network. In other words, we exfiltratedata from isolated networks to the Internet. We also explain how to mount known attacks such as host alias resolution, de-NATtingand container co-residence detection, using the new information leakage techniques.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741892", "vector": [], "sparse_vector": [], "title": "NC-Max: Breaking the Security-Performance Tradeoff in Nakamoto Consensus.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Quake Wang", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "First implemented in Bitcoin, <PERSON><PERSON><PERSON> (NC) is the most influential consensus protocol in cryptocurrencies despite all the alternative protocols designed afterward. Nevertheless, NC is trapped by a security-performance tradeoff. While existing efforts mostly attempt to break this tradeoff via abandoning or adjusting NC's backbone protocol, we alternatively forward the relevance of the network layer. We identify and experimentally prove that the crux resides with the prolonged block propagation latency caused by not-yet-propagated transactions. We thus present a two-step mechanism to confirm only fully-propagated transactions, and therefore remove the limits upon NC's performance imposed by its security demands, realizing NC's untapped potential. Implementing this two-step mechanism, we propose NC-Max, whose (1) security is analyzed, proving that it provides stronger resistance than NC against transaction withholding attacks, and (2) performance is evaluated, showing that it exhausts the full throughput supported by the network, and shortens the transaction confirmation latency by 3.0 to 6.6 times compared to NC without compromising security. NC-Max is implemented in Nervos CKB, a public permissionless blockchain.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741893", "vector": [], "sparse_vector": [], "title": "Demystifying Local Business Search Poisoning for Illicit Drug Promotion.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A new type of underground illicit drug promotion, illicit drug business listings on local search services (e.g., local knowledge panel, map search, voice search), is increasingly being utilized by miscreants to advertise and sell controlled substances on the Internet. Miscreants exploit the problematic upstream local data brokers featuring loose control on data quality to post listings that promote illicit drug business. Such a promotion, in turn, pollutes the major downstream search providers’ knowledge bases and further reaches a large audience through web, map, and voice searches. To the best of our knowledge, little has been done so far to understand this new illicit promotion in terms of its scope, impact, and techniques, not to mention any effort to identify such illicit drug business listings on a large scale. In this paper, we report the first measurement study of the illicit drug business listings on local search services. Our findings have brought to light the vulnerable and less regulated local business listing ecosystem and the pervasiveness of such illicit activities, as well as the impact on local search audience.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741894", "vector": [], "sparse_vector": [], "title": "Privacy in Urban Sensing with Instrumented Fleets, Using Air Pollution Monitoring As A Usecase.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Companies providing services like cab sharing, e-commerce logistics and, food delivery are willing to instrument their vehicles for scaling up measurements of traffic congestion, travel time, road surface quality, air quality, etc.~cite{polmeasure}. Analyzing fine-grained sensors data from such large fleets can be highly beneficial; however, this sensor information reveals the locations and the number of vehicles in the deployed fleet. This sensitive data is of high business value to rival companies in the same business domain, e.g., Uber vs. Ola, Uber vs. Lyft in cab sharing, or Amazon vs. Alibaba in the e-commerce domain. This paper provides privacy guarantees for the scenario mentioned above using Gaussian Process Regression (GPR) based interpolation, Differential Privacy (DP), and  Secure two-party computations (2PC). The sensed values from instrumented vehicle fleets are made available preserving fleet and client privacy, along with client utility. Our system has efficient latency and bandwidth overheads, even for resource-constrained mobile clients. To demonstrate our end-to-end system, we build a sample Android application that gives the least polluted route alternatives given a source-destination pair in a privacy preserved manner.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741895", "vector": [], "sparse_vector": [], "title": "The Truth Shall Set Thee Free: Enabling Practical Forensic Capabilities in Smart Environments.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In smart environments such as smart homes and offices, the interaction between devices, users, and apps generate abundant data. Such data contain valuable forensic information about events and activities occurring in the smart environment. Nonetheless, current smart platforms do not provide any digital forensic capability to identify, trace, store, and analyze the data produced in these environments. To fill this gap, in this paper, we introduce VeritaS, a novel and practical digital forensic capability for the smart environment. VeritaS has two main components: Collector and Analyzer. The Collector implements mechanisms to automatically collect forensically-relevant data from the smart environment. Then, in the event of a forensic investigation, the Analyzer uses a First Order Markov Chain model to extract valuable and usable forensic information from the collected data. VeritaS then uses the forensic information to infer activities and behaviors from users, devices, and apps that violate the security policies defined for the environment. We implemented and tested VeritaS in a realistic smart office environment with 22 smart devices and sensors that generated 84209 forensically-valuable incidents. The evaluation shows that VeritaS achieves over 95% of accuracy in inferring different anomalous activities and forensic behaviors within the smart environment. Finally, VeritaS is extremely lightweight, yielding no overhead on the devices and minimal overhead in the backend resources (i.e., the cloud servers).", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741896", "vector": [], "sparse_vector": [], "title": "Binary Search in Secure Computation.", "authors": ["Marina Blanton", "<PERSON>"], "summary": "Binary search is one of the most popular algorithms in computer science. Realizing it in the context of secure multiparty computation which demands data-oblivious execution, however, is extremely non-trivial. It has been previously implemented only using oblivious RAM (ORAM) for secure computation and in this work we initiate the study of this topic using conventional secure computation techniques based on secret sharing. We develop a suite of protocols with different properties and of different structure for searching a private dataset of $m$ elements by a private numeric key. Our protocols result in $O(m)$ and $O(sqrt{m})$ communication using only standard and readily available operations based on secret sharing. We further extend our protocols to support write operations, namely, binary search that obliviously updates the selected element, and realize two variants: updating non-key fields and updating the key field. Our implementation results indicate that even after applying known and our own optimizations to the fastest ORAM constructions, our solutions are faster than optimized ORAM schemes for datasets of up to $2^{30}$ elements and by up to 2 orders of magnitude. We hope that this work will prompt further interest in seeking efficient realizations of this important problem.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741897", "vector": [], "sparse_vector": [], "title": "Titanium: A Metadata-Hiding File-Sharing System with Malicious Security.", "authors": ["<PERSON><PERSON><PERSON> Chen", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "End-to-end encrypted file-sharing systems enable users to share files without revealing the file contents to the  storage servers. However, the servers still learn metadata, including user identities and access patterns. Prior work tried to remove such leakage but relied on strong assumptions. Metal (NDSS '20) is not secure against malicious servers. MCORAM (ASIACRYPT '20) provides confidentiality against malicious servers, but not integrity. Titanium is a metadata-hiding file-sharing system that offers confidentiality and integrity against malicious users and servers. Compared with MCORAM, which offers confidentiality against malicious servers, Titanium also offers integrity. Experiments show that Titanium is 5x-200x faster or more than MCORAM.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741898", "vector": [], "sparse_vector": [], "title": "ROV-MI: Large-Scale, Accurate and Efficient Measurement of ROV Deployment.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Dongqi Han", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xingang Shi"], "summary": "Securing inter-domain routing systems of the Internet from illegitimate prefix annoucements has been a great concern for the researchers and network operators. After the failure of many BGP (Border Gateway Protocol) security enSecuring inter-domain routing systems of the Internet from illegitimate prefix annoucements has been a great concern for the researchers and network operators. After the failure of many BGP (Border Gateway Protocol) security enhancement mechanisms to achieve broad deployment, it is encouraging to see that the deployment of RPKI (Resource Public Key Infrastructure) is gradually increasing worldwide. For a deeper understanding of the impact of RPKI, many studies have been devoted to measuring the deployment of RPKI, including the deployment of ROA (Route Origin Authorization) and ROV (Route Origin Validation). Unlike the measurement of ROA deployment which can be directly derived from the data in RPKI repository, the measurement of ROV deployment requires more sophisticated measurement and inference techniques. However, existing work has limited measurement range, and the inference methods are either inaccurate or inefficient.hancement mechanisms to achieve broad deployment, it is encouraging to see that the deployment of RPKI (Resource Public Key Infrastructure) is gradually increasing worldwide. For a deeper understanding of the impact of RPKI, many studies have been devoted to measuring the deployment of RPKI, including the deployment of ROA (Route Origin Authorization) and ROV (Route Origin Validation). Unlike the measurement of ROA deployment which can be directly derived from the data in RPKI repository, the measurement of ROV deployment requires more sophisticated measurement and inference techniques. However, existing work has limited measurement range, and the inference methods are either inaccurate or inefficient. In this paper, we propose a new framework, ROV-MI, for the measurement of ROV deployment, which consist of a large-scale measurement infrastructure driven by in-the-wild invalid prefixes in the control plane to detect filtering of invalid updates with active probing in the data plane, and an efficient and accurate inference algorithm based on Bayesian inference techniques. We implement ROV-MI for measuring real-world ROV deployment and compare it to prior works, and the results show that ROVMI can accurately infer ROV adoption of ~10 times more ASes (Autonomous Systems) with less than 20% of the execution time compared to current state-of-the-art methods.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741899", "vector": [], "sparse_vector": [], "title": "RVPLAYER: Robotic Vehicle Forensics by Replay with What-if Reasoning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Robotic vehicle (RV) attack forensics identifies root cause of an accident. Reproduction of accident and reasoning about its causation are critical steps in the process. Ideally, such investigation would be performed in real-world field tests by faithfully regenerating the environmental conditions and varying the different factors to understand causality. However, such analysis is extremely expensive and in many cases infeasible due to the difficulties of enforcing physical conditions. Existing RV forensics techniques focus on faithful accident reproduction in simulation and hence lack the support of causality reasoning. They also entail substantial overhead. We propose RVPLAYER, a system for RV forensics. It supports replay with what if reasoning inside simulator (e.g., checking if an accident can be avoided by changing some control parameter, code, or vehicle states). It is a low-cost replacement of the expensive field test based forensics. It features an efficient demand-driven adaptive logging method capturing non-deterministic physical conditions, and a novel replay technique supporting various replay policies that selectively enable/disable information during replay for root cause analysis. Our evaluation on 6 RVs (4 real and 2 virtual), 5 real-world auto-driving traces, and 1194 attack instances of various kinds reported in the literature shows that it can precisely pinpoint the root causes of these attacks without false positives. It has only 6.57% of the overhead of a simple logging design.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741900", "vector": [], "sparse_vector": [], "title": "F-PKI: Enabling Innovation and Trust Flexibility in the HTTPS Public-Key Infrastructure.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present F-PKI, an enhancement to the HTTPS public-key infrastructure (or web PKI) that gives trust flexibility to both clients and domain owners, and enables certification authorities (CAs) to enforce stronger security measures. In today's web PKI, all CAs are equally trusted, and security is defined by the weakest link. We address this problem by introducing trust flexibility in two dimensions: with F-PKI, each domain owner can define a domain policy (specifying, for example, which CAs are authorized to issue certificates for their domain name) and each client can set or choose a validation policy based on trust levels. F-PKI thus supports a property that is sorely needed in today's Internet: trust heterogeneity. Different parties can express different trust preferences while still being able to verify all certificates. In contrast, today's web PKI only allows clients to fully distrust suspicious/misbehaving CAs, which is likely to cause collateral damage in the form of legitimate certificates being rejected. Our contribution is to present a system that is backward compatible, provides sensible security properties to both clients and domain owners, ensures the verifiability of all certificates, and prevents downgrade attacks. Furthermore, F-PKI provides a ground for innovation, as it gives CAs an incentive to deploy new security measures to attract more customers, without having these measures undercut by vulnerable CAs.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741901", "vector": [], "sparse_vector": [], "title": "Let&apos;s Authenticate: Automated Certificates for User Authentication.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Passwords have numerous drawbacks, and as a result many systems have been designed to replace them. Password replacements have generally failed to dislodge passwords due to the complexity of balancing usability, deployability, and security. However, despite this lack of success, recent advances with password managers and FIDO2 afford new opportunities to explore system design for password replacements. In this work, we explore the feasibility of a system for user authentication based on certificates. Rather than developing new cryptography, we develop a new *system*, called Let's Authenticate, which combines elements of password managers, FIDO2, and certificates. Our design incorporates feedback from a survey of 397 participants to understand their preferences for system features. Let’s Authenticate issues privacy-preserving certificates to users, automatically manages their credentials, and eliminates trust in third parties. We provide a detailed security and privacy analysis, an overhead analysis, and a systematic comparison of the system to a variety of alternatives using a well-known framework. We discuss how Let’s Authenticate compares to other systems, lessons learned from our design, and issues related to centralized management of authentication data.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741902", "vector": [], "sparse_vector": [], "title": "Chunked-Cache: On-Demand and Scalable Cache Isolation for Security Architectures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Shared cache resources in multi-core processors are vulnerable to cache side-channel attacks. Recently proposed defenses such as randomized mapping of addresses to cache lines or well-known cache partitioning have their own caveats: Randomization-based defenses have been shown vulnerable to newer attack algorithms besides relying on weak cryptographic primitives. They do not fundamentally address the root cause for cache side-channel attacks, namely, mutually distrusting codes sharing cache resources. Cache partitioning defenses provide the strict resource partitioning required to effectively block all side-channel threats. However, they usually rely on way-based partitioning which is not fine-grained and cannot scale to support a larger number of protection domains, e.g., in trusted execution environment (TEE) security architectures, besides degrading performance and often resulting in cache underutilization. To overcome the shortcomings of both approaches, we present a novel and flexible set-associative cache partitioning design for TEE architectures, called Chunked-Cache. The core idea of Chunked-Cache is to enable an execution context to “carve” out an exclusive configurable chunk of the cache if the execution requires side-channel resilience. If side-channel resilience is not required, mainstream cache resources can be freely utilized. Hence, our proposed cache design addresses the security performance trade-off practically by enabling efficient selective and on-demand utilization of side-channel-resilient caches, while providing well-grounded future-proof security guarantees. We show that Chunked-Cache provides side-channel-resilient cache utilization for sensitive code execution, with small hardware overhead, while incurring no performance overhead on the OS. We also show that it outperforms conventional way-based cache partitioning by 43%, while scaling significantly better to support a larger number of protection domains.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741903", "vector": [], "sparse_vector": [], "title": "SynthCT: Towards Portable Constant-Time Code.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>", "<PERSON>"], "summary": "Recent attacks have demonstrated that modern microarchitectures are fraught with microarchitectural side channels. Constant-time (CT) programming is a software development methodology where programs are carefully written to avoid these channels. In a nutshell, the idea is to only pass secret data to safe instructions, i.e., those whose execution creates operand-independent hardware resource usage. Yet, current CT programming practices have significant security and performance issues. CT code is written and compiled once, but may execute on multiple different microarchitectures. Yet, what instructions are safe vs. unsafe is fundamentally a microarchitecture-specific issue. A new microarchitectural optimization (or vulnerability) may change the set of safe instructions and break CT guarantees. In this work, we develop SynthCT to address the above issues. Given a specification of safe/unsafe instructions,  SynthCT automatically synthesizes translations for all unsafe instructions in the ISA using only instructions from the safe set. The synthesized translations can be used as a part of a late-stage compiler pass to generate hardened binaries for a specific microarchitecture. This closes the security hole as the specification, and hence the safe translations, can target each microarchitecture individually. This also allows CT code to reclaim some performance, e.g., use more complex/higher-performing instructions, when they are deemed safe for a specific microarchitecture. Using the techniques we develop in SynthCT, we are able to synthesize translations for a majority of the x86 64 ISA. Specifically, SynthCT is able to generate safe translations for 75% of the ISA using only the remaining 25% of the ISA. Interestingly, the majority of the instructions that SynthCT was unable to generate translations for are instructions that experts believe are safe instructions on today’s x86 64 microarchitectures.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741904", "vector": [], "sparse_vector": [], "title": "Clarion: Anonymous Communication from Multiparty Shuffling Protocols.", "authors": ["<PERSON><PERSON> E<PERSON>", "<PERSON>"], "summary": "This paper studies the role of multiparty shuffling protocols in enabling more efficient metadata-hiding communication. We show that the process of shuffling messages can be expedited by having servers collaboratively shuffle and verify secret-shares of messages instead of using a conventional mixnet approach where servers take turns performing independent verifiable shuffles of user messages. We apply this technique to achieve both practical and asymptotic improvements in anonymous broadcast and messaging systems. We first show how to build a three server anonymous broadcast scheme, secure against one malicious server, that relies only on symmetric cryptography. Next, we adapt our three server broadcast scheme to a k-server scheme secure against k-1 malicious servers, at the cost of a more expensive per-shuffle preprocessing phase. Finally, we show how our scheme can be used to significantly improve the performance of the MCMix anonymous messaging system. We implement our shuffling protocol in a system called Clarion and find that it outperforms a mixnet made up of a sequence of verifiable (single-server) shuffles by 9.2x for broadcasting small messages and outperforms the MCMix conversation protocol by 11.8x.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741905", "vector": [], "sparse_vector": [], "title": "Repttack: Exploiting Cloud Schedulers to Guide Co-Location Attacks.", "authors": ["Chongzhou Fang", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cloud computing paradigms have emerged as a major facility to store and process the massive data produced by various business units, public organizations, Internet-of-Things (IoT), and cyber-physical systems (CPS). To meet users' performance requirements while maximizing resource utilization to achieve cost-efficiency, cloud administrators leverage schedulers to orchestrate tasks to different physical nodes and allow applications from different users to share the same physical node. On the other hand, micro-architectural attacks, e.g, side-channel attacks, transient execution attacks, and Rowhammer attacks, exploit the shared resources to compromise the confidentiality/integrity of a co-located victim application. Since co-location is an essential requirement for micro-architectural attacks, in this work, we investigate whether attackers can exploit the cloud schedulers to satisfy the co-location requirement of the micro-architectural attacks. Specifically, in this paper, we comprehensively analyze if attackers can influence the scheduling process of cloud schedulers to co-locate with specific targeted applications in the cloud. Our analysis shows that for cloud schedulers that allow users to submit application requirements, an attacker can carefully select the attacker's application requirements to influence the scheduler to co-locate it with a targeted victim application. We call such attack textit{Rep}lication Atextit{ttack} (Repttack). Our experimental results, in both a simulated cluster environment and a real cluster, show similar trends; a single attack instance can reach up to $50%$ co-location rate (probability of co-location) and with only $5$ instances the co-location rate can reach up to $80%$ in a heterogeneous cloud. Furthermore, we propose and evaluate a mitigation strategy that can help defend against Repttack. We believe that our results highlight the fact that schedulers in multi-user clusters need to be more carefully designed with security in mind, and the process of making scheduling decisions should involve as little user-defined information as possible.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741906", "vector": [], "sparse_vector": [], "title": "PMTUD is not Panacea: Revisiting IP Fragmentation Attacks against TCP.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "There is a widespread belief that TCP is not vulnerable to IP fragmentation attacks since TCP performs the Path Maximum Transmission Unit Discovery (PMTUD) mechanism by default, which can avoid IP fragmentation by dynamically matching the maximum size of TCP segments with the maximum transmission unit (MTU) of the path from the originator to the destination. However, this paper reveals that TCP is in fact vulnerable to IP fragmentation attacks, which is contrary to the common belief. We conduct a systematic study on the complex interactions between IP fragmentation and TCP, and we discover two key scenarios under which IP fragmentation can still be triggered on TCP segments even if the originator performs PMTUD. First, when the next-hop MTU of an intermediate router is smaller than the originator’s acceptable minimum path MTU, TCP segments from the originator will be fragmented by the router. Second, when the originator’s path MTU values between the IP layer and the TCP layer are desynchronized due to a maliciously crafted ICMP error message, the originator could be tricked into fragmenting TCP segments. Once IP fragmentation on TCP segments could be falsely triggered, attackers can inject forged fragments into the victim connection to poison the target TCP traffic after successfully addressing practical issues of predicting IPID and deceiving TCP checksum. Our case studies on both HTTP and BGP demonstrate the feasibility and effectiveness of poisoning TCP-based applications via IP fragmentation. We also conduct a comprehensive evaluation to show that our attacks can cause serious damages in the real world. Finally, we propose countermeasures to mitigate malicious IP fragmentation on TCP segments and defeat the attacks.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741907", "vector": [], "sparse_vector": [], "title": "FedCRI: Federated Mobile Cyber-Risk Intelligence.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In the present era of ubiquitous digitization more and more services are becoming available online which is amplified by the Corona pandemic.  The fast-growing mobile service market opens up new attack surfaces to the mobile service ecosystem. Hence, mobile service providers are faced with various challenges to protect their services and in particular the associated mobile apps. Defenses for apps are, however, often limited to (lightweight) application-level protection such as app hardening and monitoring and intrusion detection. Therefore, effective risk management is crucial to limit the exposure of mobile services to threats and potential damages caused by attacks. In this paper, we present FedCRI, a solution for sharing Cyber-Risk Intelligence (CRI). At its core, FedCRI transforms mobile cyber-risks into machine learning (ML) models and leverages ML-based risk management to evaluate security risks on mobile devices.  FedCRI enables fast and autonomous sharing of actionable ML-based CRI knowledge by utilizing Federated Learning (FL). FL allows collaborative training of effective risk detection models based on information contributed by different mobile service providers while preserving the privacy of the training data of the individual organizations. We extensively evaluate our approach on several real-world user databases representing 23.8 million users of security-critical mobile apps (since Android 4 and iOS 6) provided by nine different service providers in different European countries. The datasets were collected over the course of six years in the domains of financial services, payments, or insurances. Our approach can successfully extract accurate CRI models, allowing the effective identification of cybersecurity risks on mobile devices. Our evaluation shows that the federated risk detection model can achieve better than 99% accuracy in terms of F1-score in most risk classification tasks with a very low number of false positives.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741908", "vector": [], "sparse_vector": [], "title": "CFInsight: A Comprehensive Metric for CFI Policies.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Software vulnerabilities are one of the major threats to computer security and have caused substantial damage over the past decades. Consequently, numerous techniques have been proposed to mitigate the risk of exploitation of vulnerable programs. One of the most relevant defense mechanisms is Control-Flow Integrity (CFI): multiple variants have been introduced and extensively discussed in academia as well as deployed in the industry. However, it is hard to compare the security guarantees of these implementations as existing metrics (such as AIR) do not consider the different usefulness to the attacker of different basic blocks, which are the fundamental components that constitute the code of any application. This paper introduces BlockInsulation and CFGInsulation, novel metrics designed to overcome this limitation by modeling the usefulness of basic blocks for an attacker trying to traverse the program’s control-flow graph. Moreover, we propose a new CFI policy generator, named NumCFI, which is orthogonal to existing policy generators and prevents the attacker from taking shortcuts from vulnerable code to a system call instruction. We evaluate NumCFI, as well as a number of other CFI policy generators, using BlockInsulation, CFGInsulation, and existing metrics. Lastly, we describe l+tCFI, our implementation that combines NumCFI and an existing label-based policy, with a performance overhead of just 1.27%.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741909", "vector": [], "sparse_vector": [], "title": "Shaduf: Non-Cycle Payment Channel Rebalancing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A leading approach to enhancing the performance and scalability of permissionless blockchains is to use the payment channel, which allows two users to perform off-chain payments with almost unlimited frequency. By linking payment channels together to form a payment channel network, users connected by a path of channels can perform off-chain payments rapidly. However, payment channels risk encountering fund depletion, which threatens the availability of both the payment channel and network. The most recent method needs a cycle-based channel rebalancing procedure, which requires a fair leader and users with rebalancing demands forming directed cycles in the network.  Therefore, its large-scale applications are restricted. In this work, we introduce Shaduf, a novel non-cycle off-chain rebalancing protocol that offers a new solution for users to shift coins between channels directly without relying on the cycle setting. Shaduf can be applied to more general rebalancing scenarios. We provide the details of <PERSON>haduf and formally prove its security under the Universal Composability framework. Our prototype demonstrates its feasibility and the experimental evaluation shows that <PERSON><PERSON><PERSON> enhances the Lighting Network performance in payment success ratio and volume. Moreover, our protocol prominently reduces users’ deposits in channels while maintaining the same amount of payments.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741910", "vector": [], "sparse_vector": [], "title": "ATTEQ-NN: Attention-based QoE-aware Evasive Backdoor Attacks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep neural networks have achieved remarkable success on a variety of mission-critical tasks. However, recent studies show that deep neural networks are vulnerable to backdoor attacks, where the attacker releases backdoored models that behave normally on benign samples but misclassify any trigger-imposed samples to a target label. Unlike adversarial examples, backdoor attacks manipulate both the inputs and the model, perturbing samples with the trigger and injecting backdoors into the model. In this paper, we propose a novel attention-based evasive backdoor attack, dubbed ATTEQ-NN. Different from existing works that arbitrarily set the trigger mask, we carefully design an attention-based trigger mask determination framework, which places the trigger at the crucial region with the most significant influence on the prediction results. To make the trigger-imposed samples appear more natural and imperceptible to human inspectors, we introduce a Quality-of-Experience (QoE) term into the loss function of trigger generation and carefully adjust the transparency of the trigger. During the process of iteratively optimizing the trigger generation and the backdoor injection components, we propose an alternating retraining strategy, which is shown to be effective in improving the clean data accuracy and evading some model-based defense approaches. We evaluate ATTEQ-NN with extensive experiments on VGG- Flower, CIFAR-10, GTSRB, and CIFAR-100 datasets. The results show that ATTEQ-NN can increase the attack success rate by as high as 82% over baselines when the poison ratio is low while achieving a high QoE of the backdoored samples. We demonstrate that ATTEQ-NN reaches an attack success rate of more than 41.7% in the physical world under different lighting conditions and shooting angles. ATTEQ-NN preserves an attack success rate of more than 92.5% even if the original backdoored model is fine-tuned with clean data. Our user studies show that the backdoored samples generated by ATTEQ-NN are indiscernible under visual inspections. ATTEQ-NN is shown to be evasive to state-of-the-art defense methods, including model pruning, NAD, STRIP, NC, and MNTD.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741911", "vector": [], "sparse_vector": [], "title": "Speeding Dumbo: Pushing Asynchronous BFT Closer to Practice.", "authors": ["<PERSON><PERSON><PERSON>", "Yuan Lu", "Zhenliang Lu", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Asynchronous BFT consensus can implement robust mission-critical decentralized services network without relying on any form of timing assumption. Starting from the work of HoneyBadgerBFT (CCS 2016), several studies tried to push asynchronous BFT towards practice. In a recent work of Du<PERSON> (CCS 2020), they redesigned the protocol backbone and used one multi-valued validated Byzantine agreement (MVBA) to replace $n$ concurrent asynchronous binary agreement (ABA) protocols and dramatically improved the performance. Despite those efforts, asynchronous BFT protocols remain to be slow, and in particular, the latency is still quite large. There are two reasons contributing to the inferior performance: (1) the reliable broadcast (RBC) protocols still incur substantial costs; (2) the MVBA protocols are quite complicated and heavy, and all existing constructions need dozens of rounds and take the majority of the overall latency. We first present a new construction of asynchronous BFT that replaces RBC instance with a cheaper broadcast component. It not only reduces the $mathcal{O}(n^3)$ message complexity incurred by $n$ RBCs to $mathcal{O}(n^2)$, but also saves up to 67%   communications  (in the presence of a fair network scheduler). Moreover, our technical core is a new MVBA protocol, Speeding MVBA, which is concretely more efficient than all existing MVBAs. It requires only 6 rounds in the best case and expected 12 rounds in the worst case (by contrast,   several dozens of rounds in the MVBA from <PERSON><PERSON><PERSON> et al. [12] and the recent Dumbo-MVBA [33], and around 20 rounds in the MVBA from <PERSON> et al. [4]). Our new technique of the construction might be of independent interests. We implemented Speeding Dumbo and did extensive tests among up to 150  EC2  t2.medium  instances evenly allocated in 15 AWS regions across the globe. The experimental results show that Speeding Dumbo reduces the latency to about a half of Dumbo's, and also doubles the throughput of Dumbo, through all system scales from 4 nodes to 150 nodes. We also did tests to benchmark individual components such as the broadcasts and the MVBA protocols, which may be of interests for future improvements.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741912", "vector": [], "sparse_vector": [], "title": "FirmWire: Transparent Dynamic Analysis for Cellular Baseband Firmware.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Shinjo Park", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Smartphones today leverage baseband processors to implement the multitude of cellular protocols. Basebands execute firmware, which is responsible for decoding hundreds of message types developed from three decades of cellular standards. Despite its large over-the-air attack surface, baseband firmware has received little security analysis. Previous work mostly analyzedonly a handful of firmware images from a few device models, but often relied heavily on time-consuming manual static analysis or single-function fuzzing. To fill this gap, we present FirmWire, the first full-system emulation platform for baseband processors that executes unmodified baseband binary firmware. FirmWire provides baseband-specific APIs to easily add support for new vendors, firmware images, and security analyses. To demonstrate FirmWire’s scalability, we support 213 firmware images across 2 vendors and 9 phone models, allowing them to be executed and tested. With these images, FirmWire automatically discovers and bridges internal baseband APIs, allowing protocol messages to be injected with ease. Using these entry points, we selected the LTE and GSM protocols for fuzzing and discovered 7 pre-authentication memory corruptions that could lead to remote code execution--4 of which were previously unknown. We reproduced these crashes over-the-air on real devices, proving FirmWire’s emulation accuracy. FirmWire is a scalable platform for baseband security testing and we release it as open-source to the community for future research.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741913", "vector": [], "sparse_vector": [], "title": "The Taming of the Stack: Isolating Stack Data from Memory Errors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Despite vast research on defenses to protect stack objects from the exploitation of memory errors, much stack data remains at risk. Historically, stack defenses focus on the protection of code pointers, such as return addresses, but emerging techniques to exploit memory errors motivate the need for practical solutions to protect stack data objects as well. However, recent approaches provide an incomplete view of security by not accounting for memory errors comprehensively and by limiting the set of objects that can be protected unnecessarily. In this paper, we present the DataGuard system that identifies which stack objects are safe statically from spatial, type, and temporal memory errors to protect those objects efficiently. DataGuard improves security through a more comprehensive and accurate safety analysis that proves a larger number of stack objects are safe from memory errors, while ensuring that no unsafe stack objects are mistakenly classified as safe. DataGuard's analysis of server programs and the SPEC CPU2006 benchmark suite shows that DataGuard improves security by: (1) ensuring that no memory safety violations are possible for any stack objects classified as safe, removing 6.3% of the stack objects previously classified safe by the Safe Stack method, and (2) blocking exploit of all 118 stack vulnerabilities in the CGC Binaries. DataGuard extends the scope of stack protection by validating as safe over 70% of the stack objects classified as unsafe by the Safe Stack method, leading to an average of 91.45% of all stack objects that can only be referenced safely. By identifying more functions with only safe stack objects, DataGuard reduces the overhead of using Clang's Safe Stack defense for protection of the SPEC CPU2006 benchmarks from 11.3% to 4.3%.  Thus, DataGuard shows that a comprehensive and accurate analysis can both increase the scope of stack data protection and reduce overheads.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741914", "vector": [], "sparse_vector": [], "title": "Forensic Analysis of Configuration-based Attacks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Causality analysis is an effective technique for investigating and detecting cyber attacks. However, by focusing on auditing at the Operating System level, existing causal analysis techniques lack visibility into important application-level semantics, such as configuration changes that control application runtime behavior. This leads to incorrect attack attribution and half-baked tracebacks. In this work, we propose <PERSON><PERSON><PERSON>, a specialized provenance tracker that enhances the visibility of the Linux auditing infrastructure. By providing additional hooks into the system, Dossier can generate a holistic view of the target application’s event history and causal chains, particularly those pertaining to configuration changes that are among the most common attack vectors observed in the real world. The extra “vantage points” in Dossier enable forensic investigators to bridge the semantic gap and correctly piece together attack fragments. Dossier leverages the versatility of information flow tracking and system call introspection to track all configuration changes, including both dynamic modifications that are applied directly to configuration-related program variables in memory and revisions to configuration files on disk with negligible runtime overhead (less than 7%). Evaluation on realistic workloads and real-world attack scenarios shows that <PERSON><PERSON><PERSON> can effectively reason about configuration-based attacks and accurately reconstruct the whole attack stories.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741915", "vector": [], "sparse_vector": [], "title": "Fooling the Eyes of Autonomous Vehicles: Robust Physical Adversarial Examples Against Traffic Sign Recognition Systems.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Adversarial Examples (AEs) can deceive Deep Neural Networks (DNNs) and have received a lot of attention recently. However, majority of the research on AEs is in the digital domain and the adversarial patches are static. Such research is very different from many real-world DNN applications such as Traffic Sign Recognition (TSR) systems in autonomous vehicles. In TSR systems, object detectors use DNNs to process streaming video in real time. From the view of object detectors, the traffic sign’s position and quality of the video are continuously changing, rendering the digital AEs ineffective in the physical world. In this paper, we propose a systematic pipeline to generate robust physical AEs against real-world object detectors. Robustness is achieved in three ways. First, we simulate the in-vehicle cameras by extending the distribution of image transformations with the blur transformation and the resolution transformation. Second, we design the single and multiple bounding boxes filters to improve the efficiency of the perturbation training. Third, we consider four representative attack vectors, namely Hiding Attack (HA), Appearance Attack (AA), Non-Target Attack (NTA) and Target Attack (TA). For each of them, a loss function is defined to minimize the impact of the fabrication process on the physical AEs. We perform a comprehensive set of experiments under a variety of environmental conditions by varying the distance from $0m$ to $30m$, changing the angle from $-60^{circ}$ to $60^{circ}$, and considering illuminations in sunny and cloudy weather as well as at night. The experimental results show that the physical AEs generated from our pipeline are effective and robust when attacking the YOLO v5 based TSR system. The attacks have good transferability and can deceive other state-of-the-art object detectors. We launched HA and NTA on a brand-new 2021 model vehicle. Both attacks are successful in fooling the TSR system, which could be a lifethreatening case for autonomous vehicles. Finally, we discuss three defense mechanisms based on image preprocessing, AEs detection, and model enhancing.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741916", "vector": [], "sparse_vector": [], "title": "Context-Sensitive and Directional Concurrency Fuzzing for Data-Race Detection.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Fuzzing is popular for bug detection and vulnerability discovery nowadays. To adopt fuzzing for concurrency problems like data races, several recent concurrency fuzzing approaches consider concurrency information of program execution, and explore thread interleavings by affecting threads scheduling at runtime. However, these approaches are still limited in data-race detection. On the one hand, they fail to consider the execution contexts of thread interleavings, which can miss real data races in specific runtime contexts. On the other hand, they perform random thread-interleaving exploration, which frequently repeats already covered thread interleavings and misses many infrequent thread interleavings. In this paper, we develop a novel concurrency fuzzing framework named CONZZER, to effectively explore thread interleavings and detect hard-to-find data races. The core of CONZZER is a context-sensitive and directional concurrency fuzzing approach for thread-interleaving exploration, with two new techniques. First, to ensure context sensitivity, we propose a new concurrencycoverage metric, concurrent call pair, to describe thread interleavings with runtime calling contexts. Second, to directionally explore thread interleavings, we propose an adjacency-directed mutation to generate new possible thread interleavings with already covered thread interleavings and then use a breakpoint-control method to attempt to actually cover them at runtime. With these two techniques, this concurrency fuzzing approach can effectively cover infrequent thread interleavings with concrete context information, to help discover hard-to-find data races. We have evaluated CONZZER on 8 user-level applications and 4 kernel-level filesystems, and found 95 real data races. We identify 75 of these data races to be harmful and send them to related developers, and 44 have been confirmed. We also compare CONZZER to existing fuzzing tools, and CONZZER continuously explores more thread interleavings and finds many real data races missed by these tools.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741917", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON>: Scanning for Generalized Transient Execution Gadgets in the Linux Kernel.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Due to the high cost of serializing instructions to mitigate Spectre-like attacks on mispredicted conditional branches (Spectre-PHT), developers of critical software such as the Linux kernel selectively apply such mitigations with annotations to code paths they assume to be dangerous under speculative execution. The approach leads to incomplete protection as it applies mitigations only to easy-to-spot gadgets. Still, until now, this was sufficient, because existing gadget scanners (and kernel developers) are pattern-driven: they look for known exploit signatures and cannot detect more generic gadgets. In this paper, we abandon pattern scanning for an approach that models the essential emph{steps} used in speculative execution attacks, allowing us to find more generic gadgets---well beyond the reach of existing scanners. In particular, we present <PERSON><PERSON>, a speculative execution gadget scanner that uses taint analysis policies to model an attacker capable of exploiting arbitrary software/hardware vulnerabilities on a transient path to control data (e.g., through memory massaging or LVI), access secrets (e.g., through out-of-bounds or use-after-free accesses), and leak these secrets (e.g., through cache-based, MDS-based, or port contention-based covert channels). Finally, where existing solutions target user programs, <PERSON><PERSON> finds gadgets in the kernel, a higher-value attack target, but also more complicated to analyze. Even though the kernel is heavily hardened against transient execution attacks, <PERSON><PERSON> finds 1379 gadgets that are not yet mitigated. We confirm our findings by demonstrating an end-to-end proof-of-concept exploit for one of the gadgets found by <PERSON><PERSON>.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741918", "vector": [], "sparse_vector": [], "title": "Probe the Proto: Measuring Client-Side Prototype Pollution Vulnerabilities of One Million Real-world Websites.", "authors": ["<PERSON><PERSON><PERSON>", "Song Li", "<PERSON><PERSON> Cao"], "summary": "Prototype pollution is a relatively new type of JavaScript vulnerabilities, which allows an adversaryto inject a property into a prototypical object, such as Object.prototype. The injected property may be used later in other sensitive functions like innerHTML, leading to Cross- site Scripting (XSS), or document.cookie, leading to cookie manipulations. Prior works proposed to detect prototype pollution in Node.js application using static analysis. However, it still remains unclear how prevalent prototype pollution exists in client-side websites, let alone what consequences (e.g., XSS and cookie manipulations) prototype pollution could lead to. In this paper, we propose ProbeTheProto, the first large-scale measurement study of clients-side prototype pollution among one million real-world websites. PROBETHEPROTO consists of two important parts: dynamic taint analysis that tracks so-called joint taint flows connecting property lookups and assignments, and input/exploit generation that guides joint taint flows into final sinks related to further consequences. ProbeTheProto answers the questions of whether a prototypical object is controllable, whether and what properties can be manipulated, and whether the injected value leads to further consequences. We implemented a prototype of ProbeTheProto and evaluated it on one million websites. The results reveal that 2,738 real-world websites—including ten among the top 1,000—are vulnerable to 2,917 zero-day, exploitable prototype pollution vulnerabilities. We verify that 48 vulnerabilities further lead to XSS, 736 to cookie manipulations, and 830 to URL manipulations. We reported all the findings to website maintainers and so far 185 vulnerable websites have already been patched.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741919", "vector": [], "sparse_vector": [], "title": "Testability Tarpits: the Impact of Code Patterns on the Security Testing of Web Applications.", "authors": ["Feras Al Kassar", "Giulia Clerici", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "While static application security testing tools (SAST) have many known limitations, the impact of coding style on their ability to discover vulnerabilities remained largely unexplored. To fill this gap, in this study we experimented with a combination of commercial and open source security scanners, and compiled a list of over 270 different code patterns that, when present, impede the ability of state-of-the-art tools to analyze PHP and JavaScript code. By discovering the presence of these patterns during the software development lifecycle, our approach can provide important feedback to developers about the **testability** of their code. It can also help them to better assess the residual risk that the code could still contain vulnerabilities even when static analyzers report no findings. Finally, our approach can also point to alternative ways to transform the code to increase its testability for SAST. Our experiments show that testability tarpits are very common. For instance, an average PHP application contains over 21 of them and even the best state of art static analysis tools fail to analyze more than 20 consecutive instructions before encountering one of them. To assess the impact of pattern transformations over static analysis findings, we experimented with both manual and automated code transformations designed to replace a subset of patterns with equivalent, but more testable, code. These transformations allowed existing tools to better understand and analyze the applications, and lead to the detection of 440 new potential vulnerabilities in 48 projects. We responsibly disclosed all these issues: 31 projects already answered confirming 182 vulnerabilities. Out of these confirmed issues-- that remained previously unknown due to the poor testability of the applications code-- there are 38 impacting popular Github projects (>1k stars), such as PHP Dzzoffice (3.3k), JS Docsify (19k), and JS Apexcharts (11k). 25 CVEs have been already published and we have others in-process.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741920", "vector": [], "sparse_vector": [], "title": "EqualNet: A Secure and Practical Defense for Long-term Network Topology Obfuscation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Seungwon Shin"], "summary": "Path tracing tools, such as traceroute, are simple yet fundamental network debugging tools for network operators to detect and fix network failures. Unfortunately, adversaries can also use such tools to retrieve previously unknown network topology information which is key to realizing sophisticated Denial-of-Service attacks, such as Link Flooding Attacks (LFAs), more efficiently. Over the last few years, several network obfuscation defenses have been proposed to proactively mitigate LFAs by exposing virtual (fake) topologies that conceal potential bottleneck network links from adversaries. However, to date there has been no comprehensive and systematic analysis of the level of security and utility their virtual topologies offer. A critical analysis is thus a necessary step towards better understanding their limitations and building stronger and more practical defenses against LFAs. In this paper, we first conduct a security analysis of the three state-of-the-art network obfuscation defenses. Our analysis reveals four important, common limitations that can significantly decrease the security and utility of their virtual topologies. Motivated by our findings, we present EqualNet, a secure and practical proactive defense for long-term network topology obfuscation that alleviates LFAs within a network domain. EqualNet aims to equalize tracing flow distributions over nodes and links so that adversaries are unable to distinguish which of them are the most important ones, thus significantly increasing the cost of performing LFAs. Meanwhile, EqualNet preserves subnet information, helping network operators who use path tracing tools to debug their networks. To demonstrate its feasibility, we implement a full prototype of it using Software-Defined Networking (SDN) and perform extensive evaluations both in software and hardware. Our results show that EqualNet is effective at equalizing the tracing flow distributions of small, medium and large networks even when only a small number of routers within the network support SDN. Finally, we analyze the security of EqualNet against a wide variety of attacks.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741921", "vector": [], "sparse_vector": [], "title": "Euler: Detecting Network Lateral Movement via Scalable Temporal Graph Link Prediction.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Lateral movement is a key stage of system compromise used by advanced persistent threats. Detecting it is no simple task. When network host logs are abstracted into discrete temporal graphs, the problem can be reframed as anomalous edge detection in an evolving network. Research in modern deep graph learning techniques has produced many creative and complicated models for this task. However, as is the case in many machine learning fields, the generality of models is of paramount importance for accuracy and scalability during training and inference. In this paper, we propose a formalized approach to this problem with a framework we call Euler. It consists of a model-agnostic graph neural network stacked upon a model-agnostic sequence encoding layer such as a recurrent neural network. Models built according to the Euler framework can easily distribute their graph convolutional layers across multiple machines for large performance improvements. Additionally, we demonstrate that Euler-based models are competitive, or better than many state-of-the-art approaches to anomalous link detection and prediction. As anomaly-based intrusion detection systems, Euler models can efficiently identify anomalous connections between entities with high precision and outperform other unsupervised techniques for anomalous lateral movement detection.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741922", "vector": [], "sparse_vector": [], "title": "The Droid is in the Details: Environment-aware Evasion of Android Sandboxes.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Malware sandboxes have long been a valuable tool for detecting and analyzing malicious software. The proliferation of mobile devices and, subsequently, mobile applications, has led to a surge in the development and use of mobile device sandboxes to ensure the integrity of application marketplaces. In turn, to evade these sandboxes, malware has evolved to suspend its malicious activity when it is executed in a sandbox environment. Sophisticated malware sandboxes attempt to prevent sandbox detection by patching runtime properties indicative of malware- analysis systems. In this paper, we propose a set of novel mobile-sandbox- evasion techniques that we collectively refer to as “environment- aware” sandbox detection. We explore the distribution of artifacts extracted from readily available APIs in order to distinguish real user devices from sandboxes. To that end, we identify Android APIs that can be used to extract environment-related features, such as artifacts of user configurations (e.g. screen brightness), population of files on the device (e.g. number of photos and songs), and hardware sensors (e.g. presence of a step counter). By collecting ground truth data from real users and Android sandboxes, we show that attackers can straightforwardly build a classifier capable of differentiating between real Android devices and well-known mobile sandboxes with 98.54% accuracy. More- over, to demonstrate the inefficacy of patching APIs in sandbox environments individually, we focus on feature inconsistencies between the claimed manufacturer of a sandbox (Samsung, LG, etc.) and real devices from these manufacturers. Our findings emphasize the difficulty of creating robust sandbox environments regardless of their underlying platform being an emulated en- vironment, or an actual mobile device. Most importantly, our work signifies the lack of protection against “environment-aware” sandbox detection in state-of-the-art mobile sandboxes which can be readily abused by mobile malware to evade detection and increase their lifespan.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741923", "vector": [], "sparse_vector": [], "title": "Tetrad: Actively Secure 4PC for Secure Training and Inference.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mixing arithmetic and boolean circuits to perform privacy-preserving machine learning has become increasingly popular. Towards this, we propose a framework for the case of four parties with at most one active corruption called Tetrad. Tetrad works over rings and supports two levels of security, fairness and robustness. The fair multiplication protocol costs 5 ring elements, improving over the state-of-the-art Trident (<PERSON><PERSON><PERSON> et al. NDSS'20). A key feature of Tetrad is that robustness comes for free over fair protocols. Other highlights across the two variants include (a) probabilistic truncation without overhead, (b) multi-input multiplication protocols, and (c) conversion protocols to switch between the computational domains, along with a tailor-made garbled circuit approach. Benchmarking of Tetrad for both training and inference is conducted over deep neural networks such as LeNet and VGG16. We found that Tetrad is up to 4 times faster in ML training and up to 5 times faster in ML inference. Tetrad is also lightweight in terms of deployment cost, costing up to 6 times less than Trident.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741924", "vector": [], "sparse_vector": [], "title": "DRAWN APART: A Device Identification Technique based on Remote GPU Fingerprinting.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Clé<PERSON><PERSON> Maurice", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Browser fingerprinting aims to identify users or their devices, through scripts that execute in the users’ browser and collect information on software or hardware characteristics. It is used to track users or as an additional means of identification to improve security. Fingerprinting techniques have one significant limitation: they are unable to track individual users for an extended duration. This happens because browser fingerprints evolve over time, and these evolutions ultimately cause a fingerprint to be confused with those from other devices sharing similar hardware and software. In this paper, we report on a new technique that can significantly extend the tracking time of fingerprint-based tracking methods. Our technique, which we call DRAWNAPART, is a new GPU fingerprinting technique that identifies a device from the unique properties of its GPU stack. Specifically, we show that variations in speed among the multiple execution units that comprise a GPU can serve as a reliable and robust device signature, which can be collected using unprivileged JavaScript. We investigate the accuracy of DRAWNAPART under two scenarios. In the first scenario, our controlled experiments confirm that the technique is effective in distinguishing devices with similar hardware and software configurations, even when they are considered identical by current state-of-the-art fingerprinting algorithms. In the second scenario, we integrate a one-shot learning version of our technique into a state-of-the-art browser fingerprint tracking algorithm. We verify our technique through a large-scale experiment involving data collected from over 2,500 crowd-sourced devices over a period of several months and show it provides a boost of up to 67% to the median tracking duration, compared to the state-of-the-art method. DRAWNAPART makes two contributions to the state of the art in browser fingerprinting. On the conceptual front, it is the first work that explores the manufacturing differences between *Both authors are considered co-first authors. identical GPUs and the first to exploit these differences in a privacy context. On the practical front, it demonstrates a robust technique for distinguishing between machines with identical hardware and software configurations, a technique that delivers practical accuracy gains in a realistic setting.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741925", "vector": [], "sparse_vector": [], "title": "HeadStart: Efficiently Verifiable and Low-Latency Participatory Randomness Generation at Scale.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Generating randomness by public participation allows participants to contribute randomness directly and verify the result's security. Ideally, the difficulty of participating in such activities should be as low as possible to reduce the computational burden of being a contributor. However, existing randomness generation protocols are unsuitable for this scenario because of scalability or usability issues. Hence, in this paper we present HeadStart, a participatory randomness protocol designed for public participation at scale. HeadStart allows contributors to verify the result on commodity devices efficiently, and provides a parameter $L$ that can make the result-publication latency $L$ times lower. Additionally, we propose two implementation improvements to speed up the verification further and reduce the proof size. The verification complexity of HeadStart is only $O(L times polylog(T) +log C)$ for a contribution phase lasting for time $T$ with $C$ contributions.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741926", "vector": [], "sparse_vector": [], "title": "SpiralSpy: Exploring a Stealthy and Practical Covert Channel to Attack Air-gapped Computing Devices via mmWave Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "Baicheng Chen", "Xingyu Chen", "Huining Li", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Covert channels are a method of communication that is used to exfiltrate information from computing devices and break the security policy of computer systems. Any shared resource can be potentially leveraged as a covert channel, and conventional wisdom of cyber-security believes that air-gapped computing devices, disconnected from the Internet, are highly secured. Recent studies show that advanced covert channel attacks using acoustic, thermal, and electromagnetic effects can only work under a limited proximity constraint (e.g., within 2 meters). In this work, we present SpiralSpy, a new covert channel to attack air-gapped computing devices through millimeter-wave (mmWave) sensing technologies. SpiralSpy can be stealthily launched and circumvent strongly isolated computing devices from a practical distance (up to 8 meters). Specifically, we demonstrate that ordinal cooling fans can be leveraged for covert channel attacks. A malicious software inside air-gapped computing devices can saliently encode confidential data into the fan control signals, and modulated status on fan motions can be remotely decoded by a commodity mmWave sensor. SpiralSpy can be adopted on multiple-fan systems and enable a scalable capacity for multi-channel and high-speed information transfer. We evaluate SpiralSpy with 71 computing devices with cooling fans. Experimental results demonstrate that SpiralSpy can achieve up to 6 bps that is 6-24X faster than existing covert channels on air-gapped computing devices. We evaluate the usability and robustness of SpiralSpy under different real-world scenarios. Moreover, we conduct in-depth analysis and discussion on countermeasures for SpiralSpy-based covert channel attacks to improve computer and information security.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741927", "vector": [], "sparse_vector": [], "title": "Chosen-Instruction Attack Against Commercial Code Virtualization Obfuscators.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Chen", "Jiang <PERSON>", "<PERSON><PERSON>"], "summary": "Code virtualization is a well-known sophisticated obfuscation technique that uses custom virtual machines (VM) to emulate the semantics of original native instructions. Commercial VM-based obfuscators (e.g.,  Themida and VMProtect) are often abused by malware developers to conceal malicious behaviors. Since the internal mechanism of commercial obfuscators is a black box, it is a daunting challenge for the analyst to understand the behavior of virtualized programs. To figure out the code virtualization mechanism and design deobfuscation techniques, the analyst has to perform reverse-engineering on large-scale highly obfuscated programs. This knowledge learning process suffers from painful cost and imprecision. In this project, we study how to automatically extract knowledge from the commercial VM-based obfuscator via a novel textit{chosen-instruction attack} (CIA) technique. Our idea is inspired by chosen-plaintext attack, which is a cryptanalysis attack model to gain information that reduces the security of the encryption scheme. Given a commercial VM-based obfuscator, we carefully construct input programs, proactively interact with the obfuscator, and extract knowledge from virtualized output programs. We propose using the anchor instruction and the guided simplification technique to efficiently locate and extract knowledge-related instructions from output programs, respectively. Our experimental results demonstrate that the modern commercial VM-based obfuscators are under the threat of CIA. We have discovered 760 anchor instructions and extracted 1,915 verified instruction mapping rules from the four most widely used commercial obfuscators. The extracted knowledge enables security analysts to understand virtualized malware and improve deobfuscation techniques. Besides, we also contributed the first fine-grained benchmark suite for systematically evaluating the deobfuscation techniques. The evaluation result shows that three state-of-the-art deobfuscation techniques are insufficient to defeat modern commercial VM-based obfuscators and can be improved by our extracted knowledge.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741928", "vector": [], "sparse_vector": [], "title": "Packet-Level Open-World App Fingerprinting on Wireless Traffic.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Ma"], "summary": "Mobile apps have profoundly reshaped modern lifestyles in different aspects. Several concerns are naturally raised about the privacy risk of mobile apps. Despite the prevalence of encrypted communication, app fingerprinting (AF) attacks still pose a serious threat to users’ online privacy. However, existing AF attacks are usually hampered by four challenging issues, namely i) hidden destination, ii) invisible boundary, iii) app multiplexing, and iv) open-world recognition, when they are applied to wireless traffic. None of existing AF attacks can address all these challenges. In this paper, we advance a novel AF attack, dubbed PACKETPRINT, to recognize user activities associated with the app of interest from encrypted wireless traffic and tackle the above challenges by proposing two novel models, i.e., sequential XGBoost and hierarchical bag-of- words model. We conduct extensive experiments to evaluate the proposed attack in a series of challenging scenarios, including i) open-world setting, ii) packet loss and network congestion, iii) simultaneous use of different apps, and iv) cross-dataset recognition. The experimental results show that PACKETPRINT can accurately recognize user activities associated with the apps of interest. It achieves the average F1-score 0.884 for open-world app recognition and the average F1-score 0.959 for in-app user action recognition.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741929", "vector": [], "sparse_vector": [], "title": "Hiding My Real Self! Protecting Intellectual Property in Additive Manufacturing Systems Against Optical Side-Channel Attacks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose an optical side-channel attack to recover intellectual property in Additive Manufacturing (AM) systems. Specifically, we use a deep neural network to estimate the coordinates of the printhead as a function of time by analyzing the video of the printer frame by frame. We found that the deep neural network can successfully recover the path for an arbitrary printing process. By using data augmentation, the neural network can tolerate a certain level of variation in the position and angle of the camera as well as the lighting conditions. The neural network can intelligently perform interpolation and accurately recover the coordinates of an image that is not seen in the training dataset. To defend against the optical side-channel attack, we propose to use the optical noise injection method. Specifically, we use an optical projector to artificially inject carefully crafted optical noise onto the printing area in an attempt to confuse the attacker and make it harder to recover the printing path. We found that existing noise generation algorithms, such as replaying, random blobs, white noise, and full power, can effortlessly defeat a naive attacker who is not aware of the existence of the injected noise. However, an advanced attacker who knows about the injected noise and incorporates images with injected noise in the training dataset can defeat all of the existing noise generation algorithms. To defend against such an advanced attacker, we propose three novel noise generation algorithms: channel uniformization, state uniformization, and state randomization. Our experiment results show that noise generated via state randomization can successfully defend against the advanced attacker.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741930", "vector": [], "sparse_vector": [], "title": "Fighting Fake News in Encrypted Messaging with the Fuzzy Anonymous Complaint Tally System (FACTS).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Recent years have seen a strong uptick in both the prevalence and real-world consequences of false information spread through online platforms. At the same time, encrypted messaging systems such as WhatsApp, Signal, and Telegram, are rapidly gaining popularity as users seek increased privacy in their digital lives. The challenge we address is how to combat the viral spread of misinformation without compromising privacy. Our FACTS system tracks user complaints on messages obliviously, only revealing the message's contents and originator once sufficiently many complaints have been lodged. Our system is *private*, meaning it does not reveal anything about the senders or contents of messages which have received few or no complaints; *secure*, meaning there is no way for a malicious user to evade the system or gain an outsized impact over the complaint system; and *scalable*, as we demonstrate excellent practical efficiency for up to millions of complaints per day. Our main technical contribution is a new collaborative counting Bloom filter, a simple construction with difficult probabilistic analysis, which may have independent interest as a privacy-preserving randomized count sketch data structure. Compared to prior work on message flagging and tracing in end-to-end encrypted messaging, our novel contribution is the addition of a high threshold of multiple complaints that are needed before a message is audited or flagged. We present and carefully analyze the probabilistic performance of our data structure, provide a precise security definition and proof, and then measure the accuracy and scalability of our scheme via experimentation.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741931", "vector": [], "sparse_vector": [], "title": "ScriptChecker: To Tame Third-party Script Execution With Task Capabilities.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present ScriptChecker, a novel browser-based framework to effectively and efficiently restrict  third-party script execution according to the host web page's needs. Different from all existing schemes functioning at the JavaScript layer, ScriptChecker holistically harnesses context separation and the browser's security monitors to enforce on-demand access controls upon tasks executing untrusted code. The host page can flexibly assign resource-access capabilities to tasks upon their creation. Reaping the benefits of the task capability approach, ScriptChecker outperforms existing techniques in security, usability and performance. We have implemented a prototype of ScriptChecker on Chrome and rigorously evaluated its security and performance with case studies and benchmarks. The experimental results show that its strong security strength and ease-of-use are attained at the cost of unnoticeable performance loss. It incurs about 0.2 microseconds overhead to mediate a DOM access, and 5% delay when loading popular JS graphics and utility libraries.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741932", "vector": [], "sparse_vector": [], "title": "EMS: History-Driven Mutation for Coverage-based Fuzzing.", "authors": ["Chenyang Lyu", "Shouling Ji", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mutation-based fuzzing is one of the most popular approaches to discover vulnerabilities in a program. To alleviate the inefficiency of mutation-based fuzzing incurred by high randomness in the mutation process, multiple solutions are developed in recent years, especially coverage-based fuzzing. They mainly employ adaptive mutation strategies or integrate constraint-solving techniques to make a good exploration of the test cases which trigger unique paths and crashes. However, they lack a fine-grained reusing of fuzzing history to construct these interesting test cases, i.e., they largely fail to properly utilize fuzzing history across different fuzzing trials. In fact, we discover that test cases in fuzzing history contain rich knowledge of the key mutation strategies that lead to the discovery of unique paths and crashes.  Specifically, partial path constraint solutions implicitly carried in these mutation strategies can be reused to accelerate the discovery of new paths and crashes that share similar partial path constraints. Therefore, we first propose a lightweight and efficient Probabilistic Byte Orientation Model  (PBOM) that properly captures the byte-level mutation strategies from  intra- and inter-trial history and thus can effectively trigger unique paths and crashes. We then present a novel history-driven mutation framework named EMS that employs PBOM as one of the mutation operators to probabilistically provide desired mutation byte values according to the input ones. We evaluate EMS against state-of-the-art fuzzers including AFL,  QSYM, MOPT, MOPT-dict, EcoFuzz, and AFL++ on 9 real world programs. The results show that EMS discovers up to 4.91X more unique vulnerabilities than the baseline, and finds more line coverage than other fuzzers on most programs. We report all of the discovered new vulnerabilities to vendors and will open source the prototype of EMS on GitHub.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741933", "vector": [], "sparse_vector": [], "title": "Preventing Kernel Hacks with HAKCs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Carolina Ortega", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Commodity operating system kernels remain monolithic for practical and historical reasons.All kernel code shares a single address space, executes with elevated processor privileges, and has largely unhindered access to all data, including data irrelevant to the completion of a specific task. Applying the principle of least privilege, which limits available resources only to those needed to perform a particular task, to compartmentalize the kernel would realize major security gains, similar to microkernels yet without the major redesign effort. Here, we introduce a compartmentalization design, called a Hardware-Assisted Kernel Compartment (HAKC), that approximates least privilege separation, while minimizing both developer effort and performance overhead. HAKC divides code and data into separate partitions, and specifies an access policy for each partition. Data is owned by a single partition, and a partition's access-control policy is enforced at runtime, preventing unauthorized data access. When a partition needs to transfer control flow to outside itself, data ownership is transferred to the target, and transferred back upon return. The HAKC design allows for isolating code and data from the rest of the kernel, without utilizing any additional Trusted Computing Base while compartmentalized code is executing. Instead, HAKC relies on hardware for enforcement. Loadable kernel modules (LKMs), which dynamically load kernel code and data providing specialized functionality, are the single largest part of the Linux source base. Unfortunately, their collective size and complexity makes LKMs the cause of the majority of CVEs issued for the Linux kernel. The combination of a large attack surface in kernel modules, and the monolithic design of the Linux kernel, make LKMs ideal candidates for compartmentalization. To demonstrate the effectiveness of our approach, we implement HAKC in Linux v5.10 using extensions to the Arm v8.5-A ISA, and compartmentalize the `ipv6.ko` LKM, which consists of over 55k LOC. The average overhead measured in `Apachebench` tests was just 1.6%--24%. Additionally, we compartmentalize the `nf_tables.ko` packet filtering LKM, and measure the combined impact of using both LKMs. We find a reasonable linear growth in overhead when both compartmentalized LKMs are used. Finally, we measure no significant difference in performance when using the compartmentalized `ipv6.ko` LKM over the unmodified LKM during real-world web browsing experiments on the Alexa Top 50.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741934", "vector": [], "sparse_vector": [], "title": "ditto: WAN Traffic Obfuscation at Line Rate.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many large organizations operate dedicated wide area networks (WANs) distinct from the Internet to connect their data centers and remote sites through high-throughput links. While encryption generally protects these WANs well against content eavesdropping, they remain vulnerable to traffic analysis attacks that infer visited websites, watched videos or contents of VoIP calls from analysis of the traffic volume, packet sizes or timing information. Existing techniques to obfuscate Internet traffic are not well suited for WANs as they are either highly inefficient or require modifications to the communication protocols used by end hosts. This paper presents ditto, a traffic obfuscation system adapted to the requirements of WANs: achieving high-throughput traffic obfuscation at line rate without modifications of end hosts. ditto adds padding to packets and introduces chaff packets to make the resulting obfuscated traffic independent of production traffic with respect to packet sizes, timing and traffic volume. We evaluate a full implementation of ditto running on programmable switches in the network data plane. Our results show that ditto runs at 100 Gbps line rate and performs with negligible performance overhead up to a realistic traffic load of 70 Gbps per WAN link.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741935", "vector": [], "sparse_vector": [], "title": "D-Box: DMA-enabled Compartmentalization for Embedded Applications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Embedded and Internet-of-Things (IoT) devices have seen an increase in adoption in many domains. The security of these devices is of great importance as they are often used to control critical infrastructure, medical devices, and vehicles. Existing solutions to isolate microcontroller (MCU) resources in order to increase their security face significant challenges such as specific hardware unavailability, Memory Protection Unit (MPU) limitations and a significant lack of Direct Memory Access (DMA) support. Nevertheless, DMA is fundamental for the power and performance requirements of embedded applications. In this paper, we present D-Box, a systematic approach to enable secure DMA operations for compartmentalization solutions of embedded applications using real-time operating systems (RTOS). D Box defines a reference architecture and a workflow to protect DMA operations holistically. It provides practical methods to harden the kernel and define capability-based security policies for easy definition of DMA operations with strong security properties. We implemented a D-Box prototype for the Cortex M3/M4 on top of the popular FreeRTOS-MPU (F-MPU). The D-Box procedures and a stricter security model enabled DMA operations, yet it exposed 41 times less ROP (return-orienting-programming) gadgets when compared with the standard F-MPU. D-Box adds only a 2% processor overhead while reducing the power consumption of peripheral operation benchmarks by 18.2%. The security properties and performance of D Box were tested and confirmed on a real-world case study of a Programmable Logic Controller (PLC) application.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741936", "vector": [], "sparse_vector": [], "title": "Cross-Language Attacks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Memory corruption attacks against unsafe programming languages like C/C++ have been a major threat to computer systems for multiple decades. Various sanitizers and runtime exploit mitigation techniques have been shown to only provide partial protection at best. Recently developed ‘safe’ programming languages such as Rust and Go hold the promise to change this paradigm by preventing memory corruption bugs using a strong type system and proper compile-time and runtime checks. Gradual deployment of these languages has been touted as a way of improving the security of existing applications before entire applications can be developed in safe languages. This is notable in popular applications such as Firefox and Tor. In this paper, we systematically analyze the security of multi-language applications. We show that because language safety checks in safe languages and exploit mitigation techniques applied to unsafe languages (e.g., Control-Flow Integrity) break different stages of an exploit to prevent control hijacking attacks, an attacker can carefully maneuver between the languages to mount a successful attack. In essence, we illustrate that the incompatible set of assumptions made in various languages enables attacks that are not possible in each language alone. We study different variants of these attacks and analyze Firefox to illustrate the feasibility and extent of this problem. Our findings show that gradual deployment of safe programming languages, if not done with extreme care, can indeed be detrimental to security.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741937", "vector": [], "sparse_vector": [], "title": "An In-depth Analysis of Duplicated Linux Kernel Bug Reports.", "authors": ["<PERSON><PERSON><PERSON> Mu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Chensheng Yu", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In the past three years, the continuous fuzzing projects Syzkaller and Syzbot have achieved great success in detecting kernel vulnerabilities, finding more kernel bugs than those found in the past 20 years. However, a side effect of continuous fuzzing is that it generates an excessive number ofcrash reports, many of which are “duplicated” reports caused by the same bug. While Syzbot uses a simple heuristic to group (deduplicate) reports, we find that it is often inaccurate. In thispaper, we empirically analyze the duplicated kernel bug reports to understand: (1) the prevalence of duplication; (2) the potential costs introduced by duplication; and (3) the key causes behind the duplication problem. We collected all of the fixed kernel bugs from September 2017 to November 2020, including 3.24 million crash reports grouped by Syzbot under 2,526 bug reports (identified by unique bug titles). We found the bug reports indeed had duplication: 47.1% of the 2,526 bug reports are duplicated with one or more other reports. By analyzing the metadata of these reports, we found undetected duplication introduced extra costs in terms of time and developer efforts. Then we organized Linux kernel experts to analyze a sample of duplicated bugs (375 bug reports, unique 120 bugs) and identified 6 key contributing factors to the duplication. Based on these empirical findings, we proposed and prototyped actionable strategies for bug deduplication. After confirming their effectiveness using a ground-truth dataset, we further applied our methods and identified previously unknown duplication cases among open bugs.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741938", "vector": [], "sparse_vector": [], "title": "Local and Central Differential Privacy for Robustness and Privacy in Federated Learning.", "authors": ["<PERSON>", "<PERSON>", "Emiliano De Cristofaro"], "summary": "Federated Learning (FL) allows multiple participants to train machine learning models collaboratively by keeping their datasets local while only exchanging model updates. Alas, this is not necessarily free from privacy and robustness vulnerabilities, e.g., via membership, property, and backdoor attacks. This paper investigates whether and to what extent one can use differential Privacy (DP) to protect both privacy and robustness in FL. To this end, we present a first-of-its-kind evaluation of Local and Central Differential Privacy (LDP/CDP) techniques in FL, assessing their feasibility and effectiveness. Our experiments show that both DP variants do defend against backdoor attacks, albeit with varying levels of protection-utility trade-offs, but anyway more effectively than other robustness defenses. DP also mitigates white-box membership inference attacks in FL, and our work is the first to show it empirically. Neither LDP nor CDP, however, defend against property inference. Overall, our work provides a comprehensive, re-usable measurement methodology to quantify the trade-offs between robustness/privacy and utility in differentially private FL.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741939", "vector": [], "sparse_vector": [], "title": "On Utility and Privacy in Synthetic Genomic Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Emiliano De Cristofaro"], "summary": "The availability of genomic data is essential to progress in biomedical research, personalized medicine, etc. However, its extreme sensitivity makes it problematic, if not outright impossible, to publish or share it. As a result, several initiatives have been launched to experiment with synthetic genomic data, e.g., using generative models to learn the underlying distribution of the real data and generate artificial datasets that preserve its salient characteristics without exposing it. This paper provides the first evaluation of the utility and the privacy protection of six state-of-the-art models for generating synthetic genomic data. We assess the performance of the synthetic data on several common tasks, such as allele population statistics and linkage disequilibrium. We then measure privacy through the lens of membership inference attacks, i.e., inferring whether a record was part of the training data. Our experiments show that no single approach to generate synthetic genomic data yields both high utility and strong privacy across the board. Also, the size and nature of the training dataset matter. Moreover, while some combinations of datasets and models produce synthetic data with distributions close to the real data, there often are target data points that are vulnerable to membership inference. Looking forward, our techniques can be used by practitioners to assess the risks of deploying synthetic genomic data in the wild and serve as a benchmark for future work.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741940", "vector": [], "sparse_vector": [], "title": "Interpretable Federated Transformer Log Learning for Cloud Threat Forensics.", "authors": ["<PERSON><PERSON><PERSON> De <PERSON> Torre Parra", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Threat detection and forensics have become an imperative objective for any digital forensic triage. Supervised approaches have been proposed for inferring system and network anomalies; including anomaly detection contributions using syslogs. Nevertheless, most works downplay the importance of the interpretability of a model's decision-making process. In this research, we are among the first to propose an interpretable federated transformer log learning model for threat detection supporting explainable cyber forensics. The proposed model is generated by training a local transformer-based threat detection model at each client in an organizational unit. Local models learn the system's normal behavior from the syslogs which keep records of execution flows. Subsequently, a federated learning server aggregates the learned model parameters from local models to generate a global federated learning model. Log time-series capturing normal behavior are expected to differ from those possessing cyber threat activity. We demonstrate this difference through a goodness of fit test based on <PERSON><PERSON><PERSON>'s Chi-square statistic. To provide insights on actions triggering this difference, we integrate an attention-based interpretability module. We implement and evaluate our proposed model using HDFS, a publicly available log dataset, and an in-house collected and publicly-released dataset named CTDD, which consists of more than 8 million syslogs representing cloud collaboration services and systems compromised by different classes of cyber threats. Moreover, through different experiments, we demonstrate the log agnostic capability and applicability of our approach on real-world operational settings such as edge computing systems. Our interpretability module manifests significant attention difference between normal and abnormal logs which provide insightful interpretability of the model's decision-making process. Finally, we deem the obtained results as a validation for the appropriate adoption of our approach in achieving threat forensics in the real world.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741941", "vector": [], "sparse_vector": [], "title": "LogicMEM: Automatic Profile Generation for Binary-Only Memory Forensics via Logic Inference.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Memory forensic tools rely on the knowledge of kernel symbols and kernel object layouts to retrieve digital evidence and artifacts from memory dumps. This knowledge is called profile. Existing solutions for profile generation are either inconvenient or inaccurate. In this paper, we propose a logic inference approach to automatically generating a profile directly from a memory dump. It leverages the invariants existing in kernel data structures across all kernel versions and configurations to precisely locate forensics-required fields in kernel objects. We have implemented a prototype named LOGICMEM and evaluated it on memory dumps collected from mainstream Linux distributions, customized Linux kernels with random configurations, and operating systems designed for Android smartphones and embedded devices. The evaluation results show that the proposed logic inference approach is well-suited for locating forensics-required fields and achieves 100% precision and recall for mainstream Linux distributions and 100% precision and 95% recall for customized kernels with random configurations. Moreover, we show that false negatives can be eliminated with improved logic rules. We also demonstrate that LOGICMEM can generate profiles when it is otherwise difficult (if not impossible) for existing approaches, and support memory forensics tasks such as rootkit detection.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741942", "vector": [], "sparse_vector": [], "title": "VPNInspector: Systematic Investigation of the VPN Ecosystem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Use of Virtual Private Networks (VPNs) has been growing rapidly due to increased public awareness of online risks to privacy and security. This growth has fueled the VPN ecosystem to expand into a multi-billion dollar industry that sees a frequent influx of new VPN providers. Nevertheless, the VPN ecosystem remains severely understudied, and the limited research concerning VPNs has relied on laborious manual processes. There is a need for a solution which empowers researchers and average users to investigate their VPN providers. In this work, we present VPNalyzer, a system that enables systematic, semi-automated investigation into the VPN ecosystem. We develop a cross-platform tool with a comprehensive measurement test suite containing 15 measurements that test for aspects of service, security and privacy essentials, misconfigurations, and leakages. Using the VPNalyzer tool, we conduct the largest investigation into 80 desktop VPNs. Our investigation reveals several previously unreported findings highlighting key issues and implementation shortcomings in the VPN ecosystem. We find evidence of traffic leaks during tunnel failure in 26 VPN providers, which seriously risk exposing sensitive user data. We are the first to measure and detect DNS leaks during tunnel failure, which we observe in eight providers. Overall, we find a majority of providers lack IPv6 support, and five even leak IPv6 traffic to the user's ISP. We observe that adoption of practices we consider security and privacy essentials is not uniform across VPN providers. Multiple providers share underlying infrastructure, and 29 providers use third-party, public DNS services. Alarmingly, 10 VPN providers leak traffic even in their most secure configuration, with six leaking data even with a \"kill switch\" feature enabled. Our results highlight the effectiveness of VPNalyzer in finding issues even in the most popular VPN providers. Consumer Reports used VPNalyzer in their efforts to create data-driven recommendations for their users.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741943", "vector": [], "sparse_vector": [], "title": "FakeGuard: Exploring Haptic Response to Mitigate the Vulnerability in Commercial Fingerprint Anti-Spoofing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Jinsong Han", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "How to defend against presentation attacks via artificial fake fingers is a core challenge in fingerprint biometrics. The trade-off among security, usability, and production cost has driven researchers to reach a common standpoint, i.e., integrate the commercial fingerprint technology with anti-spoofing detection (e.g., ridge traits). These anti-spoofing solutions are perceived as sufficiently resilient under the assumption that a fake finger can never closely relate to a live finger due to either composition of spoofing materials or non-automated manufacturing errors. In this paper, we first identify the vulnerability of in-practice anti-spoofing solutions in commercial fingerprint products. Instead of using expensive 3D fake fingers (above USD $1000), we mimic a more realistic scenario where an attacker fabricates high-precision fake fingerprints using low-cost polyvinylacetate materials (less than USD $50). Building on this, we introduce a practical and secure countermeasure, namely FakeGuard, to overcome the exposed vulnerability. We examine the nature of 3D haptic response effect that arises when a fingertip epidermis interacts with a tactile surface and reflects the disparate anatomy of live and fake fingers. Unlike the previous mitigation strategies, FakeGuard offers both hardware and software compatibility with existing fingerprint scanners. As the first exploration of haptic-based anti-spoofing solution, we demonstrate the capability of FakeGuard to prevent known and unknown fake finger attacks with an average detection error of 1.4%. We also examine and prove FakeGuard resilience against seven different physical attacks, e.g., brute-force through pressure variations or partial fingerprints, haptic response alteration via advanced spoofing materials or side-channel interference, and denial-of-service attacks by manipulating the moisture, lighting, and temperature of the ambient environment.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741944", "vector": [], "sparse_vector": [], "title": "DeepSight: Mitigating Backdoor Attacks in Federated Learning Through Deep Model Inspection.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Federated Learning (FL) allows multiple clients to collaboratively train a Neural Network (NN) model on their private data without revealing the data. Recently, several targeted poisoning attacks against FL have been introduced. These attacks inject a backdoor into the resulting model that allows adversary-controlled inputs to be misclassified. Existing countermeasures against backdoor attacks are inefficient and often merely aim to exclude deviating models from the aggregation. However, this approach also removes benign models of clients with deviating data distributions, causing the aggregated model to perform poorly for such clients. To address this problem, we propose emph{DeepSight}, a novel model filtering approach for mitigating backdoor attacks. It is based on three novel techniques that allow to characterize the distribution of data used to train model updates and seek to measure fine-grained differences in the internal structure and outputs of NNs. Using these techniques,DeepSight can identify suspicious model updates. We also develop a scheme that can accurately cluster model updates. Combining the results of both components, DeepSight is able to identify and eliminate model clusters containing poisoned models with high attack impact. We also show that the backdoor contributions of possibly undetected poisoned models can be effectively mitigated with existing weight clipping-based defenses. We evaluate the performance and effectiveness of DeepSight and show that it can mitigate state-of-the-art backdoor attacks with a negligible impact on the model's performance on benign data.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741945", "vector": [], "sparse_vector": [], "title": "SemperFi: Anti-spoofing GPS Receiver for UAVs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "It is well-known that GPS is vulnerable to signal spoofing attacks. Although several spoofing detection techniques exist, they are incapable of mitigation and recovery from stealthy attackers. In this work, we present SemperFi, a single antenna GPS receiver capable of tracking legitimate GPS satellite signals and estimating the true location even against strong adversaries. Our design leverages a combination of the Extended Kalman Filter based GPS failsafe mechanism built into majority of UAVs and a custom designed legitimate signal retriever module to detect and autonomously recover from majority of spoofing attacks. We develop algorithms to carefully synthesize recovery signals and extend the successive interference cancellation technique to preserve the legitimate signal’s ToA, while eliminating the attacker’s signal. For strong adversaries capable of stealthy and seamless takeover, SemperFi uses brief maneuvers designed to exploit the short-term stability of inertial sensors and identify stealthy spoofing attacks. We implement SemperFi in GNSS-SDR, an open-source software-defined GNSS receiver, and evaluate its performance using UAV simulators, real drones, a variety of real-world GPS datasets, as well as on various embedded platforms. Our evaluation results indicate that in many scenarios, SemperFi can identify adversarial peaks by executing flight patterns less than 100 m long and recover the true location within 0.54 s (<PERSON><PERSON>). We show that our receiver is secure against both naive and stealthy spoofers who exploit inertial sensor errors and execute seamless takeover attacks. Furthermore, we design SemperFi as a pluggable module capable of generating a spoofer-free GPS signal for processing on any commercial-off-the-shelf GPS receiver available today. Finally, we release our implementation to the community for usage and further research.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741946", "vector": [], "sparse_vector": [], "title": "Remote Memory-Deduplication Attacks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Memory utilization can be reduced by merging identical memory blocks into copy-on-write mappings. Previous work showed that this so-calledmemory deduplicationcan be exploited in local attacks to break ASLR, spy on other programs, and determine the presence of data, i.e., website images. All these attacks exploit memory deduplication across security domains, which in turn was disabled. However, within a security domain or on an isolated system with no untrusted local access, memory deduplication is still not considered a security risk and was recently re-enabled on Windows by default. In this paper, we present the first fully remote memory-deduplication attacks. Unlike previous attacks, our attacks require no local code execution. Consequently, we can disclose memory contents from a remote server merely by sending and timing HTTP/1 and HTTP/2 network requests. We demonstrate our attacks on deduplication both on Windows and Linux and attack widely used server software such as Memcached and InnoDB. Our side channel leaks up to 34.41B/h over the internet, making it faster than comparable remote memory-disclosure channels. We showcase our remote memory-deduplication attack in three case studies: First, we show that an attacker can disclose the presence of data in memory on a server running Memcached. We show that this information disclosure channel can also be used for fingerprinting and detect the correct libc version over the internet in 166.51s. Second, in combination with InnoDB, we present an information disclosure attack to leak MariaDB database records. Third, we demonstrate a fully remote KASLR break in less than 4 minutes allowing to derandomize the kernel image of a virtual machine over the Internet, i.e., 14 network hops away. We conclude that memory deduplication must also be considered a security risk if only applied within a single security domain.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741947", "vector": [], "sparse_vector": [], "title": "V-Range: Enabling Secure Ranging in 5G Wireless Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A number of safety- and security-critical applications such as asset tracking, smart ecosystems, autonomous vehicles and driver assistance functions, etc., are expected to benefit from the position information available through 5G.Driven by the aim to support such a wide-array of location-aware services and applications, the current release of 5G seeks to explore ranging and positioning [1] as an integral part of 5G technology. In recent years, many attacks on positioning and ranging systems have been demonstrated, and hence it is important to build 5G systems that are resilient to distance and location manipulation attacks. No existing proposal either by 3GPP or the research community addresses the challenges of secure position estimation in 5G.In this paper, we develop V-Range, the first secure ranging system that is fully compatible with 5G standards and can be implemented directly on top of existing 5G-NR transceivers.We design V-Range, a system capable of executing secure ranging operations resilient to both distance enlargement and reduction attacks.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741948", "vector": [], "sparse_vector": [], "title": "Above and Beyond: Organizational Efforts to Complement U.S. Digital Security Compliance Mandates.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Organizations that provide essential services such as electricity, healthcare, and secure financial transactions are required to use digital-security compliance programs to establish a baseline of minimum security. Unfortunately, these compliance programs are known to suffer from a multitude of issues (both in how they are written and in how organizations implement them), resulting in organizations implementing their own security measures to fill actual or perceived compliance gaps. In this study, we survey 40 security professionals from six U.S. essential-service sectors to gain insight into how organizations complement compliance to fix perceived security gaps, which measures worked particularly well, and how their organizations prioritize and evaluate the measures they adopt. We find that organizations complement compliance programs often, with 37 of 40 participants confirming that their organizations have gone beyond what they perceive as mandated compliance measures to mitigate otherwise unaddressed risks. While participants were generally positive about these perceived complementary measures, they also reported challenges related to poor management, information saturation, and difficulty keeping complementary measures up-to-date and relevant. Based on these results, we recommend that compliance standards directly integrate guidance for carefully managing and auditing any perceived complementary measures that an organization chooses to implement and that organizations carefully plan end-to-end deployment and operation before implementing these measures.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741949", "vector": [], "sparse_vector": [], "title": "Evaluating Susceptibility of VPN Implementations to DoS Attacks Using Adversarial Testing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many systems today rely heavily on virtual private network (VPN) technology to connect networks and protect services on the Internet. While prior studies compare the performance of different implementations, they do not consider adversarial settings. To address this gap, we evaluate the resilience of VPN implementations to flooding-based denial-of-service (DoS) attacks. We focus on a class of emph{stateless flooding}  attacks, which are particularly threatening because an attacker that operates stealthily by spoofing its IP addresses can perform them.We have implemented various attacks to evaluate the DoS resilience of four widely used VPN solutions and measured their impact on a high-performance server with a $40,mathrm{Gb/s}$ interface, which has revealed surprising results:An adversary can deny data transfer over an already established WireGuard connection with just $300,mathrm{Mb/s}$ of attack traffic.When using strongSwan (IPsec), $75,mathrm{Mb/s}$ of attack traffic is sufficient to block connection establishment.A $100,mathrm{Mb/s}$ flood overwhelms OpenVPN, denying data transfer through VPN connections and connection establishments.Cisco's AnyConnect VPN solution can be overwhelmed with even less attack traffic:When using IPsec, $50,mathrm{Mb/s}$ of attack traffic deny connection establishment. When using SSL, $50,mathrm{Mb/s}$ suffice to deny data transfer over already established connections.Furthermore, performance analysis of WireGuard revealed significant inefficiencies in the implementation related to multi-core synchronization. We also found vulnerabilities in the implementations of strongSwan and OpenVPN, which an attacker can easily exploit for highly effective DoS attacks.These findings demonstrate the need for adversarial testing of VPN implementations with respect to DoS resilience.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741950", "vector": [], "sparse_vector": [], "title": "ProvTalk: Towards Interpretable Multi-level Provenance Analysis in Networking Functions Virtualization (NFV).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Network functions virtualization (NFV) enables agile deployment of network services on top of clouds. However, as NFV involves multiple levels of abstraction representing the same components, pinpointing the root cause of security incidents can become challenging. For instance, a security incident may be detected at a different level from where its root cause operations were conducted with no obvious link between the two. Moreover, existing provenance analysis techniques may produce results that are impractically large for human analysts to interpret due to the inherent complexity of NFV. In this paper, we propose ProvTalk, a provenance analysis system that handles the unique multi-level nature of NFV and assists the analyst to identify the root cause of security incidents. Specifically, we first define a multi-level provenance model to capture the dependencies between NFV levels. Next, we improve the interpretability through three novel techniques, i.e., multi-level pruning, mining-based aggregation, and rule-based natural language translation. We implement ProvTalk on a Tacker-OpenStack NFV platform and validate its effectiveness based on real-world security incidents. We demonstrate that ProvTalk captures management API calls issued to all NFV services, and produces more interpretable results by significantly reducing the size of the provenance graphs (up to 3.6 times smaller than the existing one-level pruning scheme and two orders of magnitude via multi-level aggregation scheme). Our user study shows that ProvTalk facilitates the analysis task of real-world users by generating more interpretable results.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741951", "vector": [], "sparse_vector": [], "title": "MIRROR: Model Inversion for Deep LearningNetwork with High Fidelity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Guangyu Shen", "Shengwei An", "<PERSON><PERSON> Xu", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Model inversion reverse-engineers input samples from a given model, and hence poses serious threats to information confidentiality. We propose a novel inversion technique based on StyleGAN, whose generator has a special architecture that forces the decomposition of an input to styles of various granularities such that the model can learn them separately in training. During sample generation, the generator transforms a latent value to parameters controlling these styles to compose a sample. In our inversion, given a target label of some subject model to invert (e.g., a private face based identity recognition model), our technique leverages a StyleGAN trained on public data from the same domain (e.g., a public human face dataset), uses the gradient descent or genetic search algorithm, together with distribution based clipping, to find a proper parameterization of the styles such that the generated sample is correctly classified to the target label (by the subject model) and recognized by humans. The results show that our inverted samples have high fidelity, substantially better than those by existing state-of-the-art techniques.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741952", "vector": [], "sparse_vector": [], "title": "A Lightweight IoT Cryptojacking Detection Mechanism in Heterogeneous Smart Home Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Recently, cryptojacking malware has become an easy way of reaching and profiting from a large number of victims. Prior works studied the cryptojacking detection systems focusing on both in-browser and host-based cryptojacking malware. However, none of these earlier works investigated different attack configurations and network settings in this context. For example, an attacker with an aggressive profit strategy may increase computational resources to the maximum utilization to benefit more in a short time, while a stealthy attacker may want to stay undetected longer time on the victim's device. The accuracy of the detection mechanism may differ for an aggressive and stealthy attacker. Not only profit strategies but also the cryptojacking malware type, the victim's device as well as various network settings where the network is fully or partially compromised may play a key role in the performance evaluation of the detection mechanisms. In addition, smart home networks with multiple IoT devices are easily exploited by the attackers, and they are equipped to mine cryptocurrency on behalf of the attacker. However, no prior works investigated the impact of cryptojacking malware on IoT devices and compromised smart home networks. In this paper, we first propose an accurate and efficient IoT cryptojacking detection mechanism based on network traffic features, which can detect both in-browser and host-based cryptojacking. Then, we focus on the cryptojacking implementation problem on new device categories (e.g., IoT) and designed several novel experiment scenarios to assess our detection mechanism to cover the current attack surface of the attackers. Particularly, we tested our mechanism in various attack configurations and network settings. For this, we used a dataset of network traces consisting of 6.4M network packets and showed that our detection algorithm can obtain accuracy as high as 99% with only one hour of training data. To the best of our knowledge, this work is the first study focusing on IoT cryptojacking and the first study analyzing various attacker behaviors and network settings in the area of cryptojacking detection.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741953", "vector": [], "sparse_vector": [], "title": "FANDEMIC: Firmware Attack Construction and Deployment on Power Management Integrated Circuit and Impacts on IoT Applications.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Supply chains have become a pillar of our economic world, and they have brought tremendous advantages to both enterprises and users. These networks consist of companies and suppliers with the goal of reducing costs and production time by offloading various stages of the production process to third party foundries. Although globalized supply chains offer many advantages, they are also vulnerable to attacks at many different points along the pipeline. For Internet-of-Things (IoT) devices, this problem is exacerbated by firmware vulnerabilities, which influence the low-level control of the system hardware. Moreover, according to the National Vulnerability Database (NVD) the number of firmware vulnerabilities within IoT devices is rapidly increasing every year, making such firmware vulnerabilities a cause for growing concern and magnifying the need to address emerging firmware vulnerabilities.In this paper we attempt to define and expand upon a class of firmware vulnerability that is characterized by the malicious configuration of power management integrated circuits (PMIC). We propose a firmware attack construction and deployment on power management IC (FANDEMIC) that involves reverse engineering bare-metal IoT firmware binaries and identifying the functions that interact with its PMIC. We demonstrate the possibility of directly altering the binary to deliberately misconfigure the PMIC such that supply line voltages are altered, which could result in a variety of problems with the device. We propose a workflow to reverse engineer the binary, using Ghidra and Python scripting, and provide two simple, but novel function matching algorithms. Furthermore, we highlight and discuss the potential aforementioned consequences of PMIC attacks, in particular, battery degradation and failure, accelerated aging effects, and sensor data corruption. As a proof of concept we implement the proposed attack on an nRF52 microcontroller and a MAX20303 PMIC to demonstrate sensor data corruption. Finally, we discuss possible mitigation techniques, which include binary auditing and secure firmware updates.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741954", "vector": [], "sparse_vector": [], "title": "Transparency Dictionaries with Succinct Proofs of Correct Operation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper introduces Verdict, a transparency dictionary, where an untrusted service maintains a label-value map that clients can query and update (foundational infrastructure for end-to-end encryption and other applications). To prevent unauthorized modifications to the dictionary, for example, by a malicious or a compromised service provider, Verdict produces publicly-verifiable cryptographic proofs that it correctly executes both reads and authorized updates. A key advance over prior work is that Verdict produces efficiently-verifiable proofs while incurring modest proving overheads. Verdict accomplishes this by composing indexed Merkle trees (a new SNARK-friendly data structure) with Phalanx (a new SNARK that supports amortized constant-sized proofs and leverages particular workload characteristics to speed up the prover). Our experimental evaluation demonstrates that Verdict scales to dictionaries with millions of labels while imposing modest overheads on the service and clients.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741955", "vector": [], "sparse_vector": [], "title": "RamBoAttack: A Robust and Query Efficient Deep Neural Network Decision Exploit.", "authors": ["Viet Quoc Vo", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Machine learning models are critically susceptible to evasion attacks from adversarial examples. Generally, adversarial examples—modified inputs deceptively similar to the original input—are constructed under whitebox access settings by adversaries with full access to the model. However, recent attacks have shown a remarkable reduction in the number of queries to craft adversarial examples using blackbox attacks. Particularly alarming is the now, practical, ability to exploit simply the classification decision (hard-label only) from a trained model’s access interface provided by a growing number of Machine Learning as a Service (MLaaS) providers—including Google, Microsoft, IBM—and used by a plethora of applications incorporating these models. An adversary’s ability to exploit only the predicted hard-label from a model query to craft adversarial examples is distinguished as a decision-based attack. In our study, we first deep-dive into recent state-of-the-art decision-based attacks in ICLR and S&P to highlight the costly nature of discovering low distortion adversarial examples employing approximate gradient estimation methods. We develop a robust class of query efficient attacks capable of avoiding entrapment in a local minimum and misdirection from noisy gradients seen in gradient estimation methods. The attack method we propose, RamBoAttack, exploits the notion of Randomized Block Coordinate Descent to explore the hidden classifier manifold, targeting perturbations to manipulate only localized input features to address the issues of gradient estimation methods. Importantly, the RamBoAttack is demonstrably more robust to the different sample inputs available to an adversary and/or the targeted class. Overall, for a given target class, RamBoAttack is demonstrated to be more robust at achieving a lower distortion and higher attack success rate within a given query budget. We curate our results using the large-scale high-resolution ImageNet dataset and open-source our attack, test samples and artifacts.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741956", "vector": [], "sparse_vector": [], "title": "Too Afraid to Drive: Systematic Discovery of Semantic DoS Vulnerability in Autonomous Driving Planning under Physical-World Attacks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In high-level Autonomous Driving (AD) systems, behavioral planning is in charge of making high-level driving decisions such as cruising and stopping, and thus highly security-critical. In this work, we perform the first systematic study of semantic security vulnerabilities specific to overly-conservative AD behavioral planning behaviors, i.e., those that can cause failed or significantly-degraded mission performance, which can be critical for AD services such as robo-taxi/delivery. We call them semantic Denial-of-Service (DoS) vulnerabilities, which we envision to be most generally exposed in practical AD systems due to the tendency for conservativeness to avoid safety incidents. To achieve high practicality and realism, we assume that the attacker can only introduce seemingly-benign external physical objects to the driving environment, e.g., off-road dumped cardboard boxes. To systematically discover such vulnerabilities, we design PlanFuzz, a novel dynamic testing approach that addresses various problem-specific design challenges. Specifically, we propose and identify planning invariants as novel testing oracles, and design new input generation to systematically enforce problem-specific constraints for attacker-introduced physical objects. We also design a novel behavioral planning vulnerability distance metric to effectively guide the discovery. We evaluate PlanFuzz on 3 planning implementations from practical open-source AD systems, and find that it can effectively discover 9 previously-unknown semantic DoS vulnerabilities without false positives. We find all our new designs necessary, as without each design, statistically significant performance drops are generally observed. We further perform exploitation case studies using simulation and real-vehicle traces. We discuss root causes and potential fixes.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741957", "vector": [], "sparse_vector": [], "title": "Multi-Certificate Attacks against Proof-of-Elapsed-Time and Their Countermeasures.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Proof-of-Elapsed-Time (POET) is a blockchain consensus protocol in which each participating node is required to wait for the passage of a specified time duration before it can participate in the block leader election in each round. It relies on trusted execution environments, such as Intel SGX, to ensure its security, and has been implemented in Hyperledger Sawtooth and used in many real-world settings. This paper examines the security issues including fairness guarantees of the Sawtooth’s POET design and implementation, and discovers a new category of security attacks against POET, dubbed Multi-Certificate Attacks, which allows a malicious node to unfairly create multiple Certificates in each round of block leader election and select the one that maximizes her probability of winning. We have systematically analyzed the root causes of these attacks and assisted the Sawtooth community to fix several vulnerabilities in the latest version of POET. To further mitigate the identified threats, we propose a new design of POET in this paper, which we call POETA, that can be used to address the remaining vulnerabilities we have discovered. We have implemented POETA and evaluated its security and performance.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741958", "vector": [], "sparse_vector": [], "title": "GhostTalk: Interactive Attack on Smartphone Voice System Through Power Line.", "authors": ["<PERSON><PERSON>", "Han<PERSON> Guo", "<PERSON><PERSON>"], "summary": "Inaudible voice command injection is one of the most threatening attacks towards voice assistants. Existing attacks aim at injecting the attack signals over the air, but they require the access to the authorized user’s voice for activating the voice assistants. Moreover, the effectiveness of the attacks can be greatly deteriorated in a noisy environment. In this paper, we explore a new type of channel, the power line side-channel, to launch the inaudible voice command injection. By injecting the audio signals over the power line through a modified charging cable, the attack becomes more resilient against various environmental factors and liveness detection models. Meanwhile, the smartphone audio output can be eavesdropped through the modified cable, enabling a highly-interactive attack. To exploit the power line side-channel, we present GhostTalk , a new hidden voice attack that is capable of injecting and eavesdropping simultaneously. Via a quick modification of the power bank cables, the attackers could launch interactive attacks by remotely making a phone call or capturing private information from the voice assistants. GhostTalk overcomes the challenge of bypassing the speaker verification system by stealthily triggering a switch component to simulate the press button on the headphone. In case when the smartphones are charged by an unaltered standard cable, we discover that it is possible to recover the audio signal from smartphone loudspeakers by monitoring the charging current on the power line. To demonstrate the feasibility, we design GhostTalk-SC , an adaptive eavesdropper system targeting smartphones charged in the public USB ports. To correctly recognize the private information in the audio, GhostTalk-SC carefully extracts audio spectra and integrates a neural network model to classify spoken digits in the speech. We launch GhostTalk and GhostTalk-SC attacks towards 9 main-stream commodity smartphones. The experimental results prove that GhostTalk can inject unauthorized voice commands to different smartphones with 100% success rate, and the injected audios can fool human ears and multiple liveness detection models. Moreover, GhostTalk-SC achieves 92% accuracy on average for recognizing spoken digits on different smartphones, which makes it an easily-deployable but highly-effective attack that could infiltrate sensitive information such as passwords and verification codes. For defense, we provide countermeasure recommendations to defend against this new threat.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741959", "vector": [], "sparse_vector": [], "title": "PHYjacking: Physical Input Hijacking for Zero-Permission Authorization Attacks on Android.", "authors": ["<PERSON><PERSON><PERSON>", "Shangcheng Shi", "<PERSON><PERSON><PERSON>", "<PERSON>eon<PERSON>"], "summary": "Nowadays, most mobile devices are equipped with various hardware interfaces such as touchscreen, fingerprint scanner, camera and microphone to capture inputs from the user.Many mobile apps use these physical interfaces to receive user-input for authentication/authorization operations including one-click login, fingerprint-based payment approval, and face/voice unlocking.In this paper, we investigate the so-called PHYjacking attack where a victim is misled by a zero-permission malicious app to feed physical inputs to different hardware interfaces on a mobile device to result in unintended authorization.We analyze the protection mechanisms in Android for different types of physical input interfaces and introduce new techniques to bypass them.Specifically, we identify weaknesses in the existing protection schemes for the related system APIs and observe common pitfalls when apps implement physical-input-based authorization.Worse still, we discover a race-condition bug in Android that can be exploited even when app-based mitigations are properly implemented.Based on these findings, we introduce fingerprint-jacking and facejacking techniques and demonstrate their impact on real apps.We also discuss the feasibility of launching similar attacks against NFC and microphone inputs, as well as effective tapjacking attacks against Single Sign-On apps.We have designed a static analyzer to examine 3000+ real-world apps and find 44% of them contain PHYjacking-related implementation flaws.We demonstrate the practicality and potential impact of PHYjacking via proof-of-concept implementations which enable unauthorized money transfer on a payment app with over 800 million users, user-privacy leak from a social media app with over 400 million users and escalating app permissions in Android 11.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741960", "vector": [], "sparse_vector": [], "title": "Hybrid Trust Multi-party Computation with Trusted Execution Environment.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Jianting Ning", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Wang", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Trusted execution environment (TEE) such as Intel SGX relies on hardware protection and can perform secure multi-party computation (MPC) much more efficiently than pure software solutions. However, multiple side-channel attacks have been discovered in current implementations, leading to various levels of trust among different parties. For instance, a party might assume that an adversary is unable to compromise TEE, while another might only have a partial trust in TEE or even does not trust it at all. In an MPC scenario consisting of parties with different levels of trust, one could fall back to pure software solutions. While satisfying the security concerns of all parties, those who accept TEE would not be able to enjoy the benefit brought by it. In this paper, we study the above-mentioned scenario by proposing HybrTC, a generic framework for evaluating a function in the emph{hybrid trust} manner. We give a security formalization in universal composability (UC) and introduce a new cryptographic model for the TEEs-like hardware, named emph{multifaceted trusted hardware} $mathcal{F}_{TH}$, that captures various levels of trust perceived by different parties. To demonstrate the relevance of the hybrid setting, we give a distributed database scenario where a user completely or partially trusts different TEEs in protecting her distributed query, whereas multiple servers refuse to use TEE in protecting their sensitive databases. We propose a maliciously-secure protocol for a typical select-and-join query in the multi-party setting. Experimental result has shown that on two servers with $2^{20}$ records in datasets, and with a quarter of records being selected, only 165.82s is incurred which achieves more than $18,752.58times$ speedups compared to cryptographic solutions.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741961", "vector": [], "sparse_vector": [], "title": "PoF: Proof-of-Following for Vehicle Platoons.", "authors": ["<PERSON><PERSON><PERSON>", "Jingcheng Li", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Cooperative vehicle platooning significantly improves highway safety and fuel efficiency. In this model, a set of vehicles move in line formation and coordinate functions such as acceleration, braking, and steering using a combination of physical sensing and vehicle-to-vehicle (V2V) messaging. The authenticity and integrity of the V2V messages are paramount to highway safety. For this reason, recent V2V and V2X standards support the integration of a PKI. However, a PKI cannot bind a vehicle's digital identity to the vehicle's physical state (location, heading, velocity, etc.). As a result, a vehicle with valid cryptographic credentials can impact the platoon by creating ``ghost'' vehicles and injecting false state information. In this paper, we seek to provide the missing link between the physical and the digital world in the context of verifying a vehicle’s platoon membership. We focus on the property of following, where vehicles follow each other in a close and coordinated manner. We aim at developing a Proof-of-Following (PoF) protocol that enables a candidate vehicle to prove that it follows a verifier within the typical platooning distance. The main idea of the proposed {em PoF} protocol is to draw security from the common, but constantly changing environment experienced by the closely traveling vehicles. We use the large-scale fading effect of ambient RF signals as a common source of randomness to construct a {em PoF} primitive. The correlation of large-scale fading is an ideal candidate for the mobile outdoor environment because it exponentially decays with distance and time. We evaluate our PoF protocol on an experimental platoon of two vehicles in freeway, highway, and urban driving conditions. In such realistic conditions, we demonstrate that the PoF withstands both the pre-recording and following attacks with overwhelming probability.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741962", "vector": [], "sparse_vector": [], "title": "COOPER: Testing the Binding Code of Scripting Languages with Cooperative Mutation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Hong Hu", "Purui Su"], "summary": "Scripting languages like JavaScript are being integrated into commercial software to support easy file modification. For example, Adobe Acrobat accepts JavaScript to dynamically manipulate PDF files. To bridge the gap between the high-level scripts and the low-level languages (like C/C++) used to implement the software, a binding layer is necessary to transfer data and transform representations. However, due to the complexity of two sides, the binding code is prone to inconsistent semantics and security holes, which lead to severe vulnerabilities. Existing efforts for testing binding code merely focus on the script side, and thus miss bugs that require special program native inputs. In this paper, we propose cooperative mutation, which modifies both the script code and the program native input to trigger bugs in binding code. Our insight is that many bugs are due to the interplay between the program initial state and the dynamic operations, which can only be triggered through two-dimensional mutations. We develop three novel techniques to enable practical cooperative mutation on popular scripting languages: we first cluster objects into semantics similar classes to reduce the mutation space of native inputs; then, we statistically infer the relationship between script code and object classes based on a large number of executions; at last, we use the inferred relationship to select proper objects and related script code for targeted mutation. We applied our tool, COOPER, on three popular systems that integrate scripting languages, including Adobe Acrobat, Foxit Reader and Microsoft Word. COOPER successfully found 134 previously unknown bugs. We have reported all of them to the developers. At the time of paper publishing, 59 bugs have been fixed and 33 of them are assigned CVE numbers. We are awarded totally 22K dollars bounty for 17 out of all reported bugs.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741963", "vector": [], "sparse_vector": [], "title": "What You See is Not What the Network Infers: Detecting Adversarial Examples Based on Semantic Contradiction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Adversarial examples (AEs) pose severe threats to the applications of deep neural networks (DNNs) to safety-critical domains (e.g., autonomous driving and healthcare analytics). While there has been a vast body of research to defend against AEs, to the best of our knowledge, they all suffer from some weaknesses, e.g., defending against only a subset of AEs or causing a relatively high accuracy loss for legitimate inputs. Moreover, most of the existing solutions cannot defend against adaptive attacks, wherein attackers are knowledgeable about the defense mechanisms and craft AEs accordingly. In this paper, we propose a novel AE detection framework based on the very nature of AEs, i.e., their semantic information is inconsistent with the discriminative features extracted by the target DNN model. To be specific, the proposed solution, namely ContraNet, models such contradiction by first taking both the input and the inference result to a generator to obtain a synthetic output and then comparing it against the original input. For legitimate inputs that are correctly inferred, the synthetic output tries to reconstruct the input. On the contrary, for AEs, instead of reconstructing the input, the synthetic output would be created to conform to the wrong label whenever possible. Consequently, by measuring the distance between the input and the synthetic output with metric learning, we can differentiate AEs from legitimate inputs. We perform comprehensive evaluations under various types of AE attack scenarios, and experimental results show that ContraNet outperforms existing solutions by a large margin, especially for adaptive attacks. Moreover, further analysis shows that successful AEs that can bypass ContraNet tend to have much-weakened adversarial semantics. We have also shown that ContraNet can be easily combined with adversarial training techniques to achieve more outstanding AE defense capabilities.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741964", "vector": [], "sparse_vector": [], "title": "Building Embedded Systems Like It&apos;s 1996.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Embedded devices are ubiquitous. However, preliminary evidence shows that attack mitigations protecting our desktops/servers/phones are missing in embedded devices, posing a significant threat to embedded security. To this end, this paper presents an in-depth study on the adoption of common attack mitigations on embedded devices. Precisely, it measures the presence of standard mitigations against memory corruptions in over 10k Linux-based firmware of deployed embedded devices. The study reveals that embedded devices largely omit both user-space and kernel-level attack mitigations. The adoption rates on embedded devices are multiple times lower than their desktop counterparts. An equally important observation is that the situation is not improving over time. Without changing the current practices, the attack mitigations will remain missing, which may become a bigger threat in the upcoming IoT era. Throughout follow-up analyses, we further inferred a set of factors possibly contributing to the absence of attack mitigations. The exemplary ones include massive reuse of non-protected software, lateness in upgrading outdated kernels, and restrictions imposed by automated building tools. We envision these will turn into insights towards improving the adoption of attack mitigations on embedded devices in the future.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741965", "vector": [], "sparse_vector": [], "title": "hbACSS: How to Robustly Share Many Secrets.", "authors": ["<PERSON>", "Licheng Luo", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Despite significant recent progress toward making multi-party computation (MPC) practical, no existing MPC library offers complete robustness---meaning guaranteed output delivery, including in the offline phase---in a network that even has intermittent delays. Importantly, several theoretical MPC constructions already ensure robustness in this setting. We observe that the key reason for this gap between theory and practice is the absence of efficient verifiable/complete secret sharing (VSS/CSS) constructions; existing CSS protocols either require a) challenging broadcast channels in practice or b) introducing computation and communication overhead that is at least quadratic in the number of players. This work presents hbACSS, a suite of optimal-resilience asynchronous complete secret sharing protocols that are (quasi)linear in both computation and communication overhead. Towards developing hbACSS, we develop hbPolyCommit, an efficient polynomial commitment scheme that is (quasi)linear (in the polynomial degree) in terms of computation and communication overhead without requiring a trusted setup. We implement our hbACSS protocols, extensively analyze their practicality, and observe that our protocols scale well with an increasing number of parties. In particular, we use hbACSS to generate MPC input masks: a useful primitive which had previously only been calculated nonrobustly in practice.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741966", "vector": [], "sparse_vector": [], "title": "Hazard Integrated: Understanding Security Risks in App Extensions to Team Chat Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Wang", "<PERSON><PERSON> Zhang", "<PERSON><PERSON>"], "summary": "Team Chat (textit{TACT}) systems are now widely used for online collaborations and project management. A unique feature of these systems is their integration of third-party apps, which extends their capabilities but also brings in the complexity that could potentially put the TACT system and its end-users at risk. In this paper, for the first time, we demonstrate that third-party apps in TACT systems indeed open the door to new security risks, such as privilege escalation, deception, and privacy leakage. We studied 12 popular TACT systems, following the key steps of a third-party app's life cycle (its installation, update, configuration, and runtime operations). Notably, we designed and implemented a pipeline for efficiently identifying the security risks of TA APIs, a core feature provided for system-app communication. Our study leads to the discovery of 55 security issues across the 12 platforms, with 25 in the install and configuration stages and 30 vulnerable (or risky) APIs. These security weaknesses are mostly introduced by improper design, lack of fine-grained access control, and ambiguous data-access policies. We reported our findings to all related parties, and 8 have been acknowledged. Although we are still working with the TACT vendors to determine the security impacts of the remaining flaws, their significance has already been confirmed by our user study, which further reveals users' concerns about some security policies implemented on mainstream TACT platforms and their misconceptions about the protection in place. Also, our communication with the vendors indicates that their threat models have not been well-thought-out, with some assumptions conflicting with each other.  We further provide suggestions to enhance the security quality of today's TACT systems.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741967", "vector": [], "sparse_vector": [], "title": "Progressive Scrutiny: Incremental Detection of UBI bugs in the Linux Kernel.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON>", "<PERSON>"], "summary": "The Linux kernel has a rapid development cycle, with 10 commits every hour, on average. While these updates provide new features and bug fixes, they can also introduce new bugs and security vulnerabilities. Recent techniques showed how to detect some types of vulnerabilities using static analysis, but these tools cannot run quickly enough to keep up with the pace of kernel development. Ideally, an incremental analysis technique could address this problem, by doing a complete analysis once and then only analyzing changed portions of the code subsequently. However, incremental analysis of the Linux kernel poses unique challenges, due to its enormous scale and the high precision required to reduce false positives. In this paper, we design and implement INCRELUX, a novel Linux kernel incremental analysis tool. It allows rapid vulnerability detection after each update, via targeted analysis of the new code and affected prior code, and also speeds the tracking of pre-existing bugs to understand how long they have been present, thereby increasing awareness of such bugs. Our approach hinges on a bottom-up, function-summary-based approach, which leverages the benefits of a one-time clean-slate, but expensive analysis of a prior Linux baseline. INCRELUX also uses an effective heuristic to apply symbolic execution to incremental results to improve precision. Via extensive experiments on the challenging problem of finding use-before-initialization (UBI) bugs, we showcase a number of benefits of INCRELUX: (a) we can rapidly check if any new releases introduce UBI bugs and help eliminate them early in the process. (b) we perform a historical analysis to determine when a bug was first introduced and when it was fixed (a critical procedure in bug triage in the Linux kernel). (c) we compare the incremental analysis results with a clean-slate analysis and show our approach yields nearly the exact same results, demonstrating its fidelity in addition to efficiency. While the clean-slate analysis took 106.45 hours, the incremental analysis was often completed within minutes, achieving an average 200 X speed-up for the mainline kernel and 440X on average, when analyzing stable branches.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741968", "vector": [], "sparse_vector": [], "title": "HARPO: Learning to Subvert Online Behavioral Advertising.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Online  behavioral  advertising,  and  the  associated tracking  paraphernalia,  poses  a  real  privacy  threat.  Unfortunately,  existing  privacy-enhancing  tools  are  not  always  effective against  online  advertising  and  tracking.  We  propose  HARPO, a  principled  learning-based  approach  to  subvert  online  behavioral advertising through obfuscation. HARPO uses reinforcement learning to adaptively interleave real page visits with fake pages to distort a tracker’s view of a user’s browsing profile. We evaluate HARPO against real-world user profiling and ad targeting models used  for  online  behavioral  advertising.  The  results  show  that HARPO improves privacy by triggering more than 40% incorrect interest segments and 6×higher bid values. HARPO outperforms existing  obfuscation  tools  by  as  much  as  16×for  the  same overhead.  HARPO is  also  able  to  achieve  better  stealthiness  to adversarial  detection  than  existing  obfuscation  tools.   HARPO meaningfully  advances  the  state-of-the-art  in  leveraging  obfuscation  to  subvert  online  behavioral  advertising.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741969", "vector": [], "sparse_vector": [], "title": "MobFuzz: Adaptive Multi-objective Optimization in Gray-box Fuzzing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Xiangdong Kong", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Coverage-guided gray-box fuzzing (CGF) is an efficient software testing technique. There are usually multiple objectives to optimize in CGF. However, existing CGF methods cannot successfully find the optimal values for multiple objectives simultaneously. In this paper, we propose a gray-box fuzzer for multi-objective optimization (MOO) called MobFuzz. We model the multi-objective optimization process as a multi-player multi-armed bandit (MPMAB). First, it adaptively selects the objective combination that contains the most appropriate objectives for the current situation. Second, our model deals with the power schedule, which adaptively allocates energy to the seeds under the chosen objective combination. In MobFuzz, we propose an evolutionary algorithm called NIC to optimize our chosen objectives simultaneously without incurring additional performance overhead. To prove the effectiveness of MobFuzz, we conduct experiments on 12 real-world programs and the MAGMA data set. Experiment results show that multi-objective optimization in MobFuzz outperforms single-objective fuzzing in the baseline fuzzers. In contrast to them, MobFuzz can select the optimal objective combination and increase the values of multiple objectives up to 107%, with at most a 55% reduction in the energy consumption. Moreover, MobFuzz has up to 6% more program coverage and finds 3x more unique bugs than the baseline fuzzers. The NIC algorithm has at least a 2x improvement with a performance overhead of approximately 3%.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741970", "vector": [], "sparse_vector": [], "title": "Semantic-Informed Driver Fuzzing Without Both the Hardware Devices and the Emulators.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Device drivers are security-critical. In monolithic kernels like Linux, there are hundreds of thousands of drivers which run in the same privilege as the core kernel. Consequently, a bug in a driver can compromise the whole system. More critically, drivers are particularly buggy. First, drivers receive complex and untrusted inputs from not only the user space but also the hardware. Second, the driver code can be developed by less-experienced third parties, and is less tested because running a driver requires the corresponding hardware device or the emulator. Therefore, existing studies show that drivers tend to have a higher bug density and have become a major security threat. Existing testing techniques have to focus the fuzzing on a limited number of drivers that have the corresponding devices or the emulators, thus cannot scale. In this paper, we propose a device-free driver fuzzing system, D R .FUZZ, that does not require hardware devices to fuzz-test drivers. The core of D R .FUZZ is a semantic-informed mechanism that efficiently generates inputs to properly construct relevant data structures to pass the “validation chain” in driving initialization, which enables subsequent device-free driver fuzzing. The elimination of the needs for the hardware devices and the emulators removes the bottleneck in driver testing. The semantic-informed mechanism incorporates multiple new techniques to make device-free driver fuzzing practical: inferring valid input values for passing the validation chain in initialization, inferring the temporal usage order of input bytes to minimize mutation space, and employing error states as a feedback to direct the fuzzing going through the validation chain. Moreover, the semantic-informed mechanism is generic; we can also instruct it to generate semi-malformed inputs for a higher code coverage. We evaluate D R .FUZZ on 214 Linux drivers. With an only 24-hour time budget, D R .FUZZ can successfully initialize and enable most of the drivers without the corresponding devices, whereas existing fuzzers like syzkaller cannot succeed in any case. D R .F UZZ also significantly outperforms existing driver fuzzers that are even equipped with the device or emulator in other aspects: it increases the code coverage by 70% and the throughput by 18%. With D R .FUZZ, we also find 46 new bugs in the Linux drivers.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741971", "vector": [], "sparse_vector": [], "title": "Property Inference Attacks Against GANs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "While machine learning (ML) has made tremendous progress during the past decade, recent research has shown that ML models are vulnerable to various security and privacy attacks. So far, most of the attacks in this field focus on discriminative models, represented by classifiers. Meanwhile, little attention has been paid to the security and privacy risks of generative models, such as generative adversarial networks (GANs). In this paper, we propose the first set of training dataset property inference attacks against GANs. Concretely, the adversary aims to infer the macro-level training dataset property, i.e., the proportion of samples used to train a target GAN with respect to a certain attribute. A successful property inference attack can allow the adversary to gain extra knowledge of the target GAN's training dataset, thereby directly violating the intellectual property of the target model owner. Also, it can be used as a fairness auditor to check whether the target GAN is trained with a biased dataset. Besides, property inference can serve as a building block for other advanced attacks, such as membership inference. We propose a general attack pipeline that can be tailored to two attack scenarios, including the full black-box setting and partial black-box setting. For the latter, we introduce a novel optimization framework to increase the attack efficacy. Extensive experiments over four representative GAN models on five property inference tasks show that our attacks achieve strong performance. In addition, we show that our attacks can be used to enhance the performance of membership inference against GANs.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1741972", "vector": [], "sparse_vector": [], "title": "Uncovering Cross-Context Inconsistent Access Control Enforcement in Android.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Due to the complexity resulted from the huge code base and the multi-context nature of Android, inconsistent access control enforcement exists in Android, which can be exploited by malware to bypass the access control and perform unauthorized security-sensitive operations. Unfortunately, existing studies only focus on the inconsistent access control enforcement in the Java context of Android. In this paper, we conduct the first systematic investigation on the inconsistent access control enforcement across the Java context and native context of Android. In particular, to automatically discover cross-context inconsistencies, we design and implement IAceFinder, a new tool that extracts and contrasts the access control enforced in the Java context and native context of Android. Applying IAceFinder to 14 open-source Android ROMs, we find that it can effectively uncover their cross-context inconsistent access control enforcement. Specifically, IAceFinder discovers 23 inconsistencies that can be abused by attackers to compromise the device and violate user privacy.", "published": "2022-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}]