[{"primary_key": "4158005", "vector": [], "sparse_vector": [], "title": "Deciding First-Order Satisfiability when Universal and Existential Variables are Separated.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce a new decidable fragment of first-order logic with equality, which strictly generalizes two already well-known ones -- the Bernays-Sch\\\"onfinkel-Ramsey (BSR) Fragment and the Monadic Fragment. The defining principle is the syntactic separation of universally quantified variables from existentially quantified ones at the level of atoms. Thus, our classification neither rests on restrictions on quantifier prefixes (as in the BSR case) nor on restrictions on the arity of predicate symbols (as in the monadic case). We demonstrate that the new fragment exhibits the finite model property and derive a non-elementary upper bound on the computing time required for deciding satisfiability in the new fragment. For the subfragment of prenex sentences with the quantifier prefix $\\exists^* \\forall^* \\exists^*$ the satisfiability problem is shown to be complete for NEXPTIME. Finally, we discuss how automated reasoning procedures can take advantage of our results.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934532"}, {"primary_key": "4158006", "vector": [], "sparse_vector": [], "title": "Data Communicating Processes with Unreliable Channels.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We extend the classical model of lossy channel systems by considering systems that operate on a finite set of variables ranging over an infinite data domain. Furthermore, each message inside a channel is equipped with a data item representing its value. Although we restrict the model by allowing the variables to be only tested for (dis-)equality, we show that the state reachability problem is undecidable. In light of this negative result, we consider bounded-phase reachability, where the processes are restricted to performing either send or receive operations during each phase. We show decidability of state reachability in this case by computing a symbolic encoding of the set of system configurations that are reachable from a given configuration.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934535"}, {"primary_key": "4158007", "vector": [], "sparse_vector": [], "title": "The complexity of regular abstractions of one-counter languages.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the computational and descriptional complexity of the following transformation: Given a one-counter automaton (OCA) A, construct a nondeterministic finite automaton (NFA) B that recognizes an abstraction of the language L(A): its (1) downward closure, (2) upward closure, or (3) Parikh image. For the Parikh image over a fixed alphabet and for the upward and downward closures, we find polynomial-time algorithms that compute such an NFA. For the Parikh image with the alphabet as part of the input, we find a quasi-polynomial time algorithm and prove a completeness result: we construct a sequence of OCA that admits a polynomial-time algorithm iff there is one for all OCA. For all three abstractions, it was previously unknown whether appropriate NFA of sub-exponential size exist.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934561"}, {"primary_key": "4158008", "vector": [], "sparse_vector": [], "title": "Proving Differential Privacy via Probabilistic Couplings.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Over the last decade, differential privacy has achieved widespread adoption within the privacy community. Moreover, it has attracted significant attention from the verification community, resulting in several successful tools for formally proving differential privacy. Although their technical approaches vary greatly, all existing tools rely on reasoning principles derived from the composition theorem of differential privacy. While this suffices to verify most common private algorithms, there are several important algorithms whose privacy analysis does not rely solely on the composition theorem. Their proofs are significantly more complex, and are currently beyond the reach of verification tools.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934554"}, {"primary_key": "4158009", "vector": [], "sparse_vector": [], "title": "The algebraic dichotomy conjecture for infinite domain Constraint Satisfaction Problems.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We prove that an ω-categorical core structure primitively positively interprets all finite structures with parameters if and only if some stabilizer of its polymorphism clone has a homomorphism to the clone of projections, and that this happens if and only if its polymorphism clone does not contain operations α, β, s satisfying the identity αs(x, y, x, z, y, z) ≈ βs(y, x, z, x, z, y).", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934544"}, {"primary_key": "4158010", "vector": [], "sparse_vector": [], "title": "Type Theory based on Dependent Inductive and Coinductive Types.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We develop a dependent type theory that is based purely on inductive and coinductive types, and the corresponding recursion and corecursion principles. This results in a type theory with a small set of rules, while still being fairly expressive. For example, all well-known basic types and type formers that are needed for using this type theory as a logic are definable: propositional connectives, like falsity, conjunction, disjunction, and function space, dependent function space, existential quantification, equality, natural numbers, vectors etc. The reduction relation on terms consists solely of a rule for recursion and a rule for corecursion. The reduction relations for well-known types arise from that. To further support the introduction of this new type theory, we also prove fundamental properties of its term calculus. Most importantly, we prove subject reduction and strong normalisation of the reduction relation, which gives computational meaning to the terms.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934514"}, {"primary_key": "4158011", "vector": [], "sparse_vector": [], "title": "Stochastic mechanics of graph rewriting.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We propose an algebraic approach to stochastic graph-rewriting which extends the classical construction of the Heisenberg-Weyl algebra and its canonical representation on the Fock space. Rules are seen as particular elements of an algebra of \"diagrams\": the diagram algebra D. Diagrams can be thought of as formal computational traces represented in partial time. They can be evaluated to normal diagrams (each corresponding to a rule) and generate an associative unital non-commutative algebra of rules: the rule algebra R. Evaluation becomes a morphism of unital associative algebras which maps general diagrams in D to normal ones in R. In this algebraic reformulation, usual distinctions between graph observables (real-valued maps on the set of graphs defined by counting subgraphs) and rules disappear. Instead, natural algebraic substructures of R arise: formal observables are seen as rules with equal left and right hand sides and form a commutative subalgebra, the ones counting subgraphs forming a sub-subalgebra of identity rules. Actual graph-rewriting is recovered as a canonical representation of the rule algebra as linear operators over the vector space generated by (isomorphism classes of) finite graphs. The construction of the representation is in close analogy with and subsumes the classical (multi-type bosonic) Fock space representation of the Heisenberg-Weyl algebra.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934537"}, {"primary_key": "4158012", "vector": [], "sparse_vector": [], "title": "A Step Up in Expressiveness of Decidable Fixpoint Logics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Guardedness restrictions are one of the principal means to obtain decidable logics --- operators such as negation are restricted so that the free variables are contained in an atom. While guardedness has been applied fruitfully in the setting of first-order logic, the ability to add fixpoints while retaining decidability has been very limited. Here we show that one of the main restrictions imposed in the past can be lifted, getting a richer decidable logic by allowing fixpoints in which the parameters of the fixpoint can be unguarded. Using automata, we show that the resulting logics have a decidable satisfiability problem, and provide a fine study of the complexity of satisfiability. We show that similar methods apply to decide questions concerning the elimination of fixpoints within formulas of the logic.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2933592"}, {"primary_key": "4158013", "vector": [], "sparse_vector": [], "title": "Querying Visible and Invisible Information.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> ten <PERSON>", "<PERSON><PERSON>"], "summary": "We provide a wide-ranging study of the scenario where a subset of the relations in the schema are visible --- that is, their complete contents are known --- while the remaining relations are invisible. We also have integrity constraints (invariants given by logical sentences) which may relate the visible relations to the invisible ones. We want to determine which information about a query (a positive existential sentence) can be inferred from the visible instance and the constraints. We consider both positive and negative query information, that is, whether the query or its negation holds. We consider the instance-level version of the problem, where both the query and the visible instance are given, as well as the schema-level version, where we want to know whether truth or falsity of the query can be inferred in some instance of the schema.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2935306"}, {"primary_key": "4158014", "vector": [], "sparse_vector": [], "title": "Near-Optimal Lower Bounds on Quantifier Depth and Weisfeiler-Leman Refinement Steps.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We prove near-optimal trade-offs for quantifier depth versus number of variables in first-order logic by exhibiting pairs of n-element structures that can be distinguished by a k-variable first-order sentence but where every such sentence requires quantifier depth at least nΩ(k / log k). Our trade-offs also apply to first-order counting logic, and by the known connection to the k-dimensional Weisfeiler--<PERSON><PERSON> algorithm imply near-optimal lower bounds on the number of refinement iterations. A key component in our proof is the hardness condensation technique recently introduced by [<PERSON><PERSON><PERSON><PERSON> '16] in the context of proof complexity. We apply this method to reduce the domain size of relational structures while maintaining the quantifier depth required to distinguish them.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934560"}, {"primary_key": "4158015", "vector": [], "sparse_vector": [], "title": "Understanding Gentzen and Frege Systems for QBF.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Recently <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> [10] introduced a natural class of Frege systems for quantified Boolean formulas (QBF) and showed strong lower bounds for restricted versions of these systems. Here we provide a comprehensive analysis of the new extended Frege system from [10], denoted EF + ∀red, which is a natural extension of classical extended Frege EF.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2933597"}, {"primary_key": "4158016", "vector": [], "sparse_vector": [], "title": "Hybrid realizability for intuitionistic and classical choice.", "authors": ["<PERSON><PERSON>"], "summary": "In intuitionistic realizability like <PERSON><PERSON><PERSON>s or <PERSON><PERSON><PERSON>'s, the axiom of choice is trivially realized. It is even provable in <PERSON><PERSON><PERSON><PERSON><PERSON>'s intuitionistic type theory. In classical logic, however, even the weaker axiom of countable choice proves the existence of non-computable functions. This logical strength comes at the price of a complicated computational interpretation which involves strong recursion schemes like bar recursion. We take the best from both worlds and define a realizability model for arithmetic and the axiom of choice which encompasses both intuitionistic and classical reasoning. In this model two versions of the axiom of choice can co-exist in a single proof: intuitionistic choice and classical countable choice. We interpret intuitionistic choice efficiently, however its premise cannot come from classical reasoning. Conversely, our version of classical choice is valid in full classical logic, but it is restricted to the countable case and its realizer involves bar recursion. Having both versions allows us to obtain efficient extracted programs while keeping the provability strength of classical logic.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934511"}, {"primary_key": "4158017", "vector": [], "sparse_vector": [], "title": "Reducts of finitely bounded homogeneous structures, and lifting tractability from finite-domain constraint satisfaction.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Many natural decision problems can be formulated as constraint satisfaction problems for reducts of finitely bounded homogeneous structures. This class of problems is a large generalisation of the class of CSPs over finite domains. Our first result is a general polynomial-time reduction from such infinite-domain CSPs to finite-domain CSPs. We use this reduction to obtain new powerful polynomial-time tractability conditions that can be expressed in terms of topological polymorphism clones. Moreover, we study the subclass C of CSPs for structures that are first-order definable over equality with parameters. Also this class C properly extends the class of all finite-domain CSPs. We show that the tractability conjecture for reducts of finitely bounded homogeneous structures is for C equivalent to the finite-domain tractability conjecture.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934515"}, {"primary_key": "4158018", "vector": [], "sparse_vector": [], "title": "Definability equals recognizability for graphs of bounded treewidth.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We prove a conjecture of <PERSON><PERSON><PERSON><PERSON>, which states that a graph property is definable in MSO with modular counting predicates on graphs of constant treewidth if, and only if it is recognizable in the following sense: constant-width tree decompositions of graphs satisfying the property can be recognized by tree automata. While the forward implication is a classic fact known as <PERSON><PERSON><PERSON><PERSON>'s theorem, the converse direction remained open.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934508"}, {"primary_key": "4158019", "vector": [], "sparse_vector": [], "title": "Rewriting modulo symmetric monoidal structure.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "String diagrams are a powerful and intuitive graphical syntax for terms of symmetric monoidal categories (SMCs). They find many applications in computer science and are becoming increasingly relevant in other fields such as physics and control theory. An important role in many such approaches is played by equational theories of diagrams, typically oriented and applied as rewrite rules. This paper lays a comprehensive foundation of this form of rewriting. We interpret diagrams combinatorially as typed hypergraphs and establish the precise correspondence between diagram rewriting modulo the laws of SMCs on the one hand and double pushout (DPO) rewriting of hypergraphs, subject to a soundness condition called convexity, on the other. This result rests on a more general characterisation theorem in which we show that typed hypergraph DPO rewriting amounts to diagram rewriting modulo the laws of SMCs with a chosen special Frobenius structure. We illustrate our approach with a proof of termination for the theory of non-commutative bimonoids.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2935316"}, {"primary_key": "4158020", "vector": [], "sparse_vector": [], "title": "Graphs of relational structures: restricted types.", "authors": ["<PERSON>"], "summary": "In our LICS 2004 paper we introduced an approach to the study of the local structure of finite algebras and relational structures that aims at applications in the Constraint Satisfaction Problem (CSP). This approach involves a graph associated with an algebra A or a relational structure A, whose vertices are the elements of A (or A), the edges represent subsets of A such that the restriction of some term operation of A is 'good' on the subset, that is, act as an operation of one of the 3 types: semilattice, majority, or affine. In this paper we significantly refine and advance this approach. In particular, we prove certain connectivity and rectangularity properties of relations over algebras related to components of the graph connected by semilattice and affine edges. We also prove a result similar to 2-decomposition of relations invariant under a majority operation, only here we do not impose any restrictions on the relation. These results allow us to give a new, somewhat more intuitive proof of the bounded width theorem: the CSP over algebra A has bounded width if and only if A does not contain affine edges. Actually, this result shows that bounded width implies width (2,3). We also consider algebras with edges from a restricted set of types. In particular, it can be proved that type restrictions are preserved under the standard algebraic constructions. Finally, we prove that algebras without semilattice edges have few subalgebras of powers, that is, the CSP over such algebras is also polynomial time.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2933604"}, {"primary_key": "4158021", "vector": [], "sparse_vector": [], "title": "Automata on Infinite Trees with Equality and Disequality Constraints Between Siblings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This article is inspired by two works from the early 90s. The first one is by <PERSON><PERSON><PERSON> and <PERSON><PERSON> who considered a model of automata on finite ranked trees where one can check equality and disequality constraints between direct subtrees: they proved that this class of automata is closed under Boolean operations and that both the emptiness and the finiteness problem of the accepted language are decidable. The second one is by <PERSON><PERSON><PERSON> who showed that one can compute the cardinality of any ω-regular language of infinite trees.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934504"}, {"primary_key": "4158022", "vector": [], "sparse_vector": [], "title": "Comparing Chemical Reaction Networks: A Categorical and Algorithmic Perspective.", "authors": ["<PERSON>", "Mirco Tribastone", "<PERSON>", "<PERSON>"], "summary": "We study chemical reaction networks (CRNs) as a kernel language for concurrency models with semantics based on ordinary differential equations. We investigate the problem of comparing two CRNs, i.e., to decide whether the trajectories of a source CRN can be matched by a target CRN under an appropriate choice of initial conditions. Using a categorical framework, we extend and relate model-comparison approaches based on structural (syntactic) and on dynamical (semantic) properties of a CRN, proving their equivalence. Then, we provide an algorithm to compare CRNs, running linearly in time with respect to the cardinality of all possible comparisons. Finally, we apply our results to biological models from the literature.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2935318"}, {"primary_key": "4158023", "vector": [], "sparse_vector": [], "title": "On the Satisfiability of Some Simple Probabilistic Logics.", "authors": ["Souymodip Chakraborty", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper shows that the satisfiability problems for a bounded fragment of probabilistic CTL (called bounded PCTL) and an extension of the modal μ-calculus with probabilistic quantification over next-modalities (called PμTL) are decidable. For bounded PCTL we provide an NEXPTIME-algorithm for the satisfiability problem and show that the logic has a small model property where the model size is independent from the probability bounds in the formula. We show that the satisfiability problem of a simple sub-logic of bounded PCTL is PSPACE-complete. We prove that PμTL has a small model property and that a decision procedure using 2 player parity games can be employed for the satisfiability problem of PμTL. These results imply that PμTL and qualitative PCTL formulas with only thresholds >0 and =1---are incomparable. We also establish that---in contrast to PCTL---every satisfiable PμTL-formula has a rational model, a model with rational probabilities only.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934526"}, {"primary_key": "4158024", "vector": [], "sparse_vector": [], "title": "Perfect-Information Stochastic Games with Generalized Mean-Payoff Objectives.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Graph games provide the foundation for modeling and synthesizing reactive processes. In the synthesis of stochastic reactive processes, the traditional model is perfect-information stochastic games, where some transitions of the game graph are controlled by two adversarial players, and the other transitions are executed probabilistically. We consider such games where the objective is the conjunction of several quantitative objectives (specified as mean-payoff conditions), which we refer to as generalized mean-payoff objectives. The basic decision problem asks for the existence of a finite-memory strategy for a player that ensures the generalized mean-payoff objective be satisfied with a desired probability against all strategies of the opponent. A special case of the decision problem is the almost-sure problem where the desired probability is 1. Previous results presented a semi-decision procedure for ∈-approximations of the almost-sure problem. In this work, we show that both the almost-sure problem as well as the general basic decision problem are coNP-complete, significantly improving the previous results. Moreover, we show that in the case of 1-player stochastic games, randomized memoryless strategies are sufficient and the problem can be solved in polynomial time. In contrast, in two-player stochastic games, we show that even with randomized strategies exponential memory is required in general, and present a matching exponential upper bound. We also study the basic decision problem with infinite-memory strategies and present computational complexity results for the problem. Our results are relevant in the synthesis of stochastic reactive systems with multiple quantitative requirements.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934513"}, {"primary_key": "4158025", "vector": [], "sparse_vector": [], "title": "Model and Objective Separation with Conditional Lower Bounds: Disjunction is Harder than Conjunction.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Given a model of a system and an objective, the model-checking question asks whether the model satisfies the objective. We study polynomial-time problems in two classical models, graphs and Markov Decision Processes (MDPs), with respect to several fundamental ω-regular objectives, e.g., <PERSON><PERSON> and <PERSON><PERSON> objectives. For many of these problems the best-known upper bounds are quadratic or cubic, yet no super-linear lower bounds are known. In this work our contributions are two-fold: First, we present several improved algorithms, and second, we present the first conditional super-linear lower bounds based on widely believed assumptions about the complexity of CNF-SAT and combinatorial Boolean matrix multiplication. A separation result for two models with respect to an objective means a conditional lower bound for one model that is strictly higher than the existing upper bound for the other model, and similarly for two objectives with respect to a model. Our results establish the following separation results: (1) A separation of models (graphs and MDPs) for disjunctive queries of reachability and <PERSON><PERSON>chi objectives. (2) Two kinds of separations of objectives, both for graphs and MDPs, namely, (2a) the separation of dual objectives such as <PERSON><PERSON>/Rabin objectives, and (2b) the separation of conjunction and disjunction of multiple objectives of the same type such as safety, Büchi, and coBüchi. In summary, our results establish the first model and objective separation results for graphs and MDPs for various classical ω-regular objectives. Quite strikingly, we establish conditional lower bounds for the disjunction of objectives that are strictly higher than the existing upper bounds for the conjunction of the same objectives.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2935304"}, {"primary_key": "4158026", "vector": [], "sparse_vector": [], "title": "Quantitative Automata under Probabilistic Semantics.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Automata with monitor counters, where the transitions do not depend on counter values, and nested weighted automata are two expressive automata-theoretic frameworks for quantitative properties. For a well-studied and wide class of quantitative functions, we establish that automata with monitor counters and nested weighted automata are equivalent. We study for the first time such quantitative automata under probabilistic semantics. We show that several problems that are undecidable for the classical questions of emptiness and universality become decidable under the probabilistic semantics. We present a complete picture of decidability for such automata, and even an almost-complete picture of computational complexity, for the probabilistic questions we consider.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2933588"}, {"primary_key": "4158027", "vector": [], "sparse_vector": [], "title": "On Recurrent Reachability for Continuous Linear Dynamical Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The continuous evolution of a wide variety of systems, including continuous-time Markov chains and linear hybrid automata, can be described in terms of linear differential equations. In this paper we study the decision problem of whether the solution x(t) of a system of linear differential equations dx/dt = Ax reaches a target halfspace infinitely often. This recurrent reachability problem can equivalently be formulated as the following Infinite Zeros Problem: does a real-valued function f: R≥0 → R satisfying a given linear differential equation have infinitely many zeros? Our main decidability result is that if the differential equation has order at most 7, then the Infinite Zeros Problem is decidable. On the other hand, we show that a decision procedure for the Infinite Zeros Problem at order 9 (and above) would entail a major breakthrough in Diophantine Approximation, specifically an algorithm for computing the Lagrange constants of arbitrary real algebraic numbers to arbitrary precision.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934548"}, {"primary_key": "4158028", "vector": [], "sparse_vector": [], "title": "The Diagonal Problem for Higher-Order Recursion Schemes is Decidable.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A non-deterministic recursion scheme recognizes a language of finite trees. This very expressive model can simulate, among others, higher-order pushdown automata with collapse. We show decidability of the diagonal problem for schemes. This result has several interesting consequences. In particular, it gives an algorithm that computes the downward closure of languages of words recognized by schemes. In turn, this has immediate application to separability problems and reachability analysis of concurrent systems.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934527"}, {"primary_key": "4158029", "vector": [], "sparse_vector": [], "title": "Games with bound guess actions.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We introduce games with (bound) guess actions. These are games in which the players may be asked along the play to provide numbers that need to satisfy some bounding constraints. These are natural extensions of domination games occurring in the regular cost function theory. In this paper we consider more specifically the case where the constraints to be bounded are regular cost functions, and the long term goal is an ω-regular winning condition. We show that such games are decidable on finite arenas.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934502"}, {"primary_key": "4158030", "vector": [], "sparse_vector": [], "title": "The Power of Arc Consistency for CSPs Defined by Partially-Ordered Forbidden Patterns.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Characterising tractable fragments of the constraint satisfaction problem (CSP) is an important challenge in theoretical computer science and artificial intelligence. Forbidding patterns (generic sub-instances) provides a means of defining CSP fragments which are neither exclusively language-based nor exclusively structure-based. It is known that the class of binary CSP instances in which the broken-triangle pattern (BTP) does not occur, a class which includes all tree-structured instances, are decided by arc consistency (AC), a ubiquitous reduction operation in constraint solvers. We provide a characterisation of simple partially-ordered forbidden patterns which have this AC-solvability property. It turns out that BTP is just one of five such AC-solvable patterns. The four other patterns allow us to exhibit new tractable classes.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2933587"}, {"primary_key": "4158031", "vector": [], "sparse_vector": [], "title": "Minimization of Symbolic Tree Automata.", "authors": ["Loris D&apos;Antoni", "<PERSON><PERSON>"], "summary": "Symbolic tree automata allow transitions to carry predicates over rich alphabet theories, such as linear arithmetic, and therefore extend finite tree automata to operate over infinite alphabets, such as the set of rational numbers. Existing tree automata algorithms rely on the alphabet being finite, and generalizing them to the symbolic setting is not a trivial task. In this paper we study the problem of minimizing symbolic tree automata. First, we formally define and prove the properties of minimality in the symbolic setting. Second, we lift existing minimization algorithms to symbolic tree automata. Third, we present a new algorithm based on the following idea: the problem of minimizing symbolic tree automata can be reduced to the problem of minimizing symbolic (string) automata by encoding the tree structure as part of the alphabet theory. We implement and evaluate all our algorithms against existing implementations and show that the symbolic algorithms scale to large alphabets and can minimize automata over complex alphabet theories.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2933578"}, {"primary_key": "4158032", "vector": [], "sparse_vector": [], "title": "First-order logic with reachability for infinite-state systems.", "authors": ["Emanuele D&apos;<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "First-order logic with the reachability predicate (FO[R]) is an important means of specification in system analysis. Its decidability status is known for some individual types of infinite-state systems such as pushdown (decidable) and vector addition systems (undecidable).", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934552"}, {"primary_key": "4158033", "vector": [], "sparse_vector": [], "title": "Two-Way Visibly Pushdown Automata and Transducers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Automata-logic connections are pillars of the theory of regular languages. Such connections are harder to obtain for transducers, but important results have been obtained recently for word-to-word transformations, showing that the three following models are equivalent: deterministic two-way transducers, monadic second-order (MSO) transducers, and deterministic one-way automata equipped with a finite number of registers. Nested words are words with a nesting structure, allowing to model unranked trees as their depth-first-search linearisations. In this paper, we consider transformations from nested words to words, allowing in particular to produce unranked trees if output words have a nesting structure. The model of visibly pushdown transducers allows to describe such transformations, and we propose a simple deterministic extension of this model with two-way moves that has the following properties: i) it is a simple computational model, that naturally has a good evaluation complexity; ii) it is expressive: it subsumes nested word-to-word MSO transducers, and the exact expressiveness of MSO transducers is recovered using a simple syntactic restriction; iii) it has good algorithmic/closure properties: the model is closed under composition with a unambiguous one-way letter-to-letter transducer which gives closure under regular look-around, and has a decidable equivalence problem.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2935315"}, {"primary_key": "4158034", "vector": [], "sparse_vector": [], "title": "From positive and intuitionistic bounded arithmetic to monotone proof complexity.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We study versions of second-order bounded arithmetic where induction and comprehension formulae are positive or where the underlying logic is intuitionistic, examining their relationships to monotone and deep inference proof systems for propositional logic.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934570"}, {"primary_key": "4158035", "vector": [], "sparse_vector": [], "title": "A Generalised Twinning Property for Minimisation of Cost Register Automata.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Weighted automata (WA) extend finite-state automata by associating with transitions weights from a semiring S, defining functions from words to S. Recently, cost register automata (CRA) have been introduced as an alternative model to describe any function realised by a WA by means of a deterministic machine. Unambiguous WA over a monoid (M, ⊗) can equivalently be described by cost register automata whose registers take their values in M, and are updated by operations of the form x: = y ⊗ c, with c ∈ M. This class is denoted by CRA⊗c(M).", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934549"}, {"primary_key": "4158036", "vector": [], "sparse_vector": [], "title": "Unifying Logical and Statistical AI.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Hoifung Poon", "<PERSON>", "<PERSON><PERSON>"], "summary": "Intelligent agents must be able to handle the complexity and uncertainty of the real world. Logical AI has focused mainly on the former, and statistical AI on the latter. Markov logic combines the two by attaching weights to first-order formulas and viewing them as templates for features of Markov networks. Inference algorithms for Markov logic draw on ideas from satisfiability, Markov chain Monte Carlo and knowledge-based model construction. Learning algorithms are based on the voted perceptron, pseudo-likelihood and inductive logic programming. Markov logic has been successfully applied to a wide variety of problems in natural language understanding, vision, computational biology, social networks and others, and is the basis of the open-source Alchemy system.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2935321"}, {"primary_key": "4158037", "vector": [], "sparse_vector": [], "title": "Decidability and Complexity for Quiescent Consistency.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Quiescent consistency is a notion of correctness for a concurrent object that gives meaning to the object's behaviours in quiescent states, i.e., states in which none of the object's operations are being executed. The condition enables greater flexibility in object design by allowing more behaviours to be admitted, which in turn allows the algorithms implementing quiescent consistent objects to be more efficient (when executed in a multithreaded environment).", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2933576"}, {"primary_key": "4158038", "vector": [], "sparse_vector": [], "title": "Towards Completeness via Proof Search in the Linear Time μ-calculus: The case of Büchi inclusions.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Modal μ-calculus is one of the central languages of logic and verification, whose study involves notoriously complex objects: automata over infinite structures on the model-theoretical side; infinite proofs and proofs by (co)induction on the proof-theoretical side. Nevertheless, axiomatizations have been given for both linear and branching time μ-calculi, with quite involved completeness arguments. We come back to this central problem, considering it from a proof search viewpoint, and provide some new completeness arguments in the linear time μ-calculus. Our results only deal with restricted classes of formulas that closely correspond to (non-alternating) ω-automata but, compared to earlier proofs, our completeness arguments are direct and constructive. We first consider a natural circular proof system based on sequent calculus, and show that it is complete for inclusions of parity automata expressed as formulas, making use of <PERSON><PERSON>'s construction directly in proof search. We then consider the corresponding finitary proof system, featuring (co)induction rules, and provide a partial translation result from circular to finitary proofs. This yields completeness of the finitary proof system for inclusions of sufficiently deterministic parity automata, and finally for arbitrary Büchi automata.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2933598"}, {"primary_key": "4158039", "vector": [], "sparse_vector": [], "title": "Interacting Frobenius Algebras are Hopf.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Theories featuring the interaction between a Frobenius algebra and a Hopf algebra have recently appeared in several areas in computer science: concurrent programming, control theory, and quantum computing, among others. <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> [9] have shown that, given a suitable distribution law, a pair of Hopf algebras forms two Frobenius algebras. Here we take the opposite approach, and show that interacting Frobenius algebras form Hopf algebras. We generalise [9] by including non-trivial dynamics of the underlying object---the so-called phase group---and investigate the effects of finite dimensionality of the underlying model, and recover the system of <PERSON><PERSON> et al as a subtheory in the prime power dimensional case. However the more general theory does not arise from a distributive law.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934550"}, {"primary_key": "4158040", "vector": [], "sparse_vector": [], "title": "Order Invariance on Decomposable Structures.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Order-invariant formulas access an ordering on a structure's universe, but the model relation is independent of the used ordering. They are frequently used for logic-based approaches in computer science. Order-invariant formulas capture unordered problems of complexity classes and they model the independence of the answer to a database query from low-level aspects of databases. We study the expressive power of order-invariant monadic second-order (MSO) and first-order (FO) logic on restricted classes of structures that admit certain forms of tree decompositions (not necessarily of bounded width).", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934517"}, {"primary_key": "4158041", "vector": [], "sparse_vector": [], "title": "Reachability in Two-Dimensional Unary Vector Addition Systems with States is NL-Complete.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON> et al. showed at LICS 2015 that two-dimensional vector addition systems with states have reachability witnesses of length exponential in the number of states and polynomial in the norm of vectors. The resulting guess-and-verify algorithm is optimal (PSPACE), but only if the input vectors are given in binary. We answer positively the main question left open by their work, namely establish that reachability witnesses of pseudo-polynomial length always exist. Hence, when the input vectors are given in unary, the improved guess-and-verify algorithm requires only logarithmic space.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2933577"}, {"primary_key": "4158042", "vector": [], "sparse_vector": [], "title": "Proving Liveness of Parameterized Programs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Correctness of multi-threaded programs typically requires that they satisfy liveness properties. For example, a program may require that no thread is starved of a shared resource, or that all threads eventually agree on a single value. This paper presents a method for proving that such liveness properties hold. Two particular challenges which are addressed in this work are that (1) the correctness argument may rely on global behaviour of the system (e.g., the correctness argument may require that all threads collectively progress towards \"the good thing\" rather than one thread progressing while the others do not interfere), and (2) such programs are often designed to be executed by any number of threads, and the desired liveness properties must hold no matter how many threads are active in the system.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2935310"}, {"primary_key": "4158043", "vector": [], "sparse_vector": [], "title": "A Mechanization of the Blakers-Massey Connectivity Theorem in Homotopy Type Theory.", "authors": ["<PERSON><PERSON><PERSON><PERSON> (Favonia)", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper contributes to recent investigations of the use of homotopy type theory to give machine-checked proofs of constructions from homotopy theory. We present a mechanized proof of a result called the Blakers-<PERSON> connectivity theorem, which relates the higher-dimensional loop structures of two spaces sharing a common part (represented by a pushout type, which is a generalization of a disjoint sum type) to those of the common part itself. This theorem gives important information about the pushout type, and has a number of useful corollaries, including the <PERSON><PERSON><PERSON> suspension theorem, which was used in previous formalizations. The proof is more direct than existing ones that apply in general category-theoretic settings for homotopy theory, and its mechanization is concise and high-level, due to novel combinations of ideas from homotopy theory and from type theory.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934545"}, {"primary_key": "4158044", "vector": [], "sparse_vector": [], "title": "Semantically Acyclic Conjunctive Queries under Functional Dependencies.", "authors": ["<PERSON>"], "summary": "The evaluation problem for Conjunctive Queries (CQ) is known to be NP-complete in combined complexity and W[1]-hard in parameterized complexity. However, acyclic CQs and CQs of bounded tree-width can be evaluated in polynomial time in combined complexity and they are fixed-parameter tractable.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2933580"}, {"primary_key": "4158045", "vector": [], "sparse_vector": [], "title": "First-order definability of rational transductions: An algebraic approach.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The algebraic theory of rational languages has provided powerful decidability results. Among them, one of the most fundamental is the definability of a rational language in the class of aperiodic languages, i.e., languages recognized by finite automata whose transition relation defines an aperiodic congruence. An important corollary of this result is the first-order definability of monadic second-order formulas over finite words.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934520"}, {"primary_key": "4158046", "vector": [], "sparse_vector": [], "title": "Divide and Congruence II: <PERSON><PERSON> and <PERSON>ak Bisimilarity.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Earlier we presented a method to decompose modal formulas for processes with the internal action τ; congruence formats for branching and η-bisimilarity were derived on the basis of this decomposition method. The idea is that a congruence format for a semantics must ensure that formulas in the modal characterisation of this semantics are always decomposed into formulas in this modal characterisation. Here the decomposition method is enhanced to deal with modal characterisations that contain a modality 〈ϵ〉〈a〉φ, to derive congruence formats for delay and weak bisimilarity.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2933590"}, {"primary_key": "4158047", "vector": [], "sparse_vector": [], "title": "A categorical approach to open and interconnected dynamical systems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In his 1986 Automatica paper <PERSON><PERSON> introduced the influential behavioural approach to control theory with an investigation of linear time-invariant (LTI) discrete dynamical systems. The behavioural approach places open systems at its centre, modelling by tearing, zooming, and linking. We show that these ideas are naturally expressed in the language of symmetric monoidal categories.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934556"}, {"primary_key": "4158048", "vector": [], "sparse_vector": [], "title": "A New Perspective on FO Model Checking of Dense Graph Classes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "We study the FO model checking problem of dense graph classes, namely those which are FO-interpretable in some sparse graph classes. Note that if an input dense graph is given together with the corresponding FO interpretation in a sparse graph, one can easily solve the model checking problem using the existing algorithms for sparse graph classes. However, if the assumed interpretation is not given, then the situation is markedly harder.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2935314"}, {"primary_key": "4158049", "vector": [], "sparse_vector": [], "title": "Effective Brenier Theorem: Applications to Computable Analysis and Algorithmic Randomness.", "authors": ["<PERSON>"], "summary": "<PERSON><PERSON><PERSON>'s theorem is a landmark result in Optimal Transport. It postulates existence, monotonicity and uniqueness of an optimal map, with respect to the quadratic cost function, between two given probability measures (under some weak regularity conditions). We prove an effective version of <PERSON><PERSON><PERSON>'s theorem: we show that for any two computable absolutely continuous measures on Rn, μ, and ν, with some restrictions on their support, there exists a computable convex function φ, whose gradient ▽φ is the optimal transport map between μ and ν.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2933596"}, {"primary_key": "4158050", "vector": [], "sparse_vector": [], "title": "Duality in Computer Science.", "authors": ["<PERSON>"], "summary": "This is a paper on Stone duality in computer science with special focus on topics with applications in formal language theory. In Section 2 we give a general overview of Stone duality in its various forms: for Boolean algebras, distributive lattices, and frames. For distributive lattices, we discuss both <PERSON> and <PERSON><PERSON> duality. We identify how to move between the different dualities and which dual spaces carry the Scott topology. We then focus on three themes.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934575"}, {"primary_key": "4158051", "vector": [], "sparse_vector": [], "title": "Monadic second order logic as the model companion of temporal logic.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The main focus of this paper is on bisimulation-invariant MSO, and more particularly on giving a novel model-theoretic approach to it. In model theory, a model companion of a theory is a first-order description of the class of models in which all potentially solvable systems of equations and non-equations have solutions. We show that bisimulation-invariant MSO on trees gives the model companion for a new temporal logic, \"fair CTL\", an enrichment of CTL with local fairness constraints. To achieve this, we give a completeness proof for the logic fair CTL which combines tableaux and Stone duality, and a fair CTL encoding of the automata for the modal μ-calculus. Moreover, we also show that MSO on binary trees is the model companion of binary deterministic fair CTL.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2933609"}, {"primary_key": "4158052", "vector": [], "sparse_vector": [], "title": "Hanf normal form for first-order logic with unary counting quantifiers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the existence of Hanf normal forms for extensions FO(Q) of first-order logic by sets Q ⊆ P(N) of unary counting quantifiers. A formula is in Hanf normal form if it is a Boolean combination of formulas ζ(x) describing the isomorphism type of a local neighbourhood around its free variables x and statements of the form \"the number of witnesses y of ψ(y) belongs to (Q+k)\" where Q ∈ Q, k ∈ N, and ψ describes the isomorphism type of a local neighbourhood around its unique free variable y.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934571"}, {"primary_key": "4158053", "vector": [], "sparse_vector": [], "title": "Blockchains and the Logic of Accountability: Keynote Address.", "authors": ["<PERSON>", "<PERSON>"], "summary": "research-article Share on Blockchains and the Logic of Accountability: Keynote Address Authors: <AUTHORS>", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934579"}, {"primary_key": "4158054", "vector": [], "sparse_vector": [], "title": "Healthiness from Duality.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Healthiness is a good old question in program logics that dates back to <PERSON><PERSON><PERSON>. It asks for an intrinsic characterization of those predicate transformers which arise as the (backward) interpretation of a certain class of programs. There are several results known for healthiness conditions: for deterministic programs, nondeterministic ones, probabilistic ones, etc. Building upon our previous works on so-called state-and-effect triangles, we contribute a unified categorical framework for investigating healthiness conditions. This framework is based on a dual adjunction induced by a dualizing object and on our notion of relative Eilenberg-Moore algebra. The latter notion seems interesting in its own right in the context of monads, Lawvere theories and enriched categories.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2935319"}, {"primary_key": "4158055", "vector": [], "sparse_vector": [], "title": "Conflict nets: Efficient locally canonical MALL proof nets.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Proof nets for MLL (unit-free multiplicative linear logic) and ALL (unit-free additive linear logic) are graphical abstractions of proofs which are efficient (proofs translate in linear time) and canonical (invariant under rule commutation). This paper solves a three-decade open problem: are there efficient canonical proof nets for MALL (unit-free multiplicative-additive linear logic)?", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934559"}, {"primary_key": "4158056", "vector": [], "sparse_vector": [], "title": "The Definitional Side of the Forcing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper studies forcing translations of proofs in dependent type theory, through the <PERSON><PERSON><PERSON> correspondence. Based on a call-by-push-value decomposition, we synthesize two simply-typed translations: i) one call-by-value, corresponding to the translation derived from the presheaf construction as studied in a previous paper; ii) one call-by-name, whose intuitions already appear in <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>'s work. Focusing on the call-by-name translation, we adapt it to the dependent case and prove that it is compatible with the definitional equality of our system, thus avoiding coherence problems. This allows us to use any category as forcing conditions, which is out of reach with the call-by-value translation. Our construction also exploits the notion of storage operators in order to interpret dependent elimination for inductive types. This is a novel example of a dependent theory with side-effects, clarifying how dependent elimination for inductive types must be restricted in a non-pure setting. Being implemented as a Coq plugin, this work gives the possibility to formalize easily consistency results, for instance the consistency of the negation of <PERSON><PERSON><PERSON><PERSON><PERSON>'s univalence axiom.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2935320"}, {"primary_key": "4158057", "vector": [], "sparse_vector": [], "title": "Trace semantics for polymorphic references.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce a trace semantics for a call-by-value language with full polymorphism and higher-order references. This is an operational game semantics model based on a nominal interpretation of parametricity whereby polymorphic values are abstracted with special kinds of names. The use of polymorphic references leads to violations of parametricity which we counter by closely recoding the disclosure of typing information in the semantics. We prove the model sound for the full language and strengthen our result to full abstraction for a large fragment where polymorphic references obey specific inhabitation conditions.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934509"}, {"primary_key": "4158058", "vector": [], "sparse_vector": [], "title": "On Thin Air Reads Towards an Event Structures Model of Relaxed Memory.", "authors": ["<PERSON>", "<PERSON>"], "summary": "This is the first paper to propose a pure event structures model of relaxed memory. We propose confusion-free event structures over an alphabet with a justification relation as a model. Executions are modeled by justified configurations, where every read event has a justifying write event. Justification alone is too weak a criterion, since it allows cycles of the kind that result in so-called thin-air reads. Acyclic justification forbids such cycles, but also invalidates event reorderings that result from compiler optimizations and dynamic instruction scheduling. We propose a notion well-justification, based on a game-like model, which strikes a middle ground.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934536"}, {"primary_key": "4158059", "vector": [], "sparse_vector": [], "title": "The Probabilistic Model Checking Landscape.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Randomization is a key element in sequential and distributed computing. Reasoning about randomized algorithms is highly non-trivial. In the 1980s, this initiated first proof methods, logics, and model-checking algorithms. The field of probabilistic verification has developed considerably since then. This paper surveys the algorithmic verification of probabilistic models, in particular probabilistic model checking. We provide an informal account of the main models, the underlying algorithms, applications from reliability and dependability analysis---and beyond---and describe recent developments towards automated parameter synthesis.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934574"}, {"primary_key": "4158060", "vector": [], "sparse_vector": [], "title": "Complexity Theory of (Functions on) Compact Metric Spaces.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We promote the theory of computational complexity on metric spaces: as natural common generalization of (i) the classical discrete setting of integers, binary strings, graphs etc. as well as of (ii) the bit-complexity theory on real numbers and functions according to <PERSON>, <PERSON> (1982ff), <PERSON>, <PERSON> et al.; as (iii) resource-bounded refinement of the theories of computability on, and representations of, continuous universes by <PERSON><PERSON><PERSON><PERSON> (1989) and <PERSON><PERSON><PERSON> (1993ff); and as (iv) computational perspective on quantitative concepts from classical Analysis: Our main results relate (i.e. upper and lower bound) <PERSON><PERSON><PERSON><PERSON>'s entropy of a compact metric space X polynomially to the uniform relativized complexity of approximating various families of continuous functions on X. The upper bounds are attained by carefully crafted oracles and bit-cost analyses of algorithms perusing them. They all employ the same representation (i.e. encoding, as infinite binary sequences, of the elements) of such spaces, which thus may be of own interest. The lower bounds adapt adversary arguments from unit-cost Information-Based Complexity to the bit model. They extend to, and indicate perhaps surprising limitations even of, encodings via binary string functions (rather than sequences) as introduced by <PERSON><PERSON><PERSON>&Cook (SToC'2010, §3.4). These insights offer some guidance towards suitable notions of complexity for higher types.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2935311"}, {"primary_key": "4158061", "vector": [], "sparse_vector": [], "title": "Quantifier Free Definability on Infinite Algebras.", "authors": ["<PERSON><PERSON>"], "summary": "An operation f: An → A on the domain A of an algebra A is definable if there exists a first order logic formula Φ(x, y) with parameters from A such that for all ā ∈ An and b ∈ A we have f (ā) = b iff A ⊨ Φ (ā, b). The goal of this paper is to study definability of operations by quantifier-free formulas on countable infinite algebras from computability and model-theoretic definability points of view.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934572"}, {"primary_key": "4158062", "vector": [], "sparse_vector": [], "title": "Distinguishing Hidden Markov Chains.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Hidden Markov Chains (HMCs) are commonly used mathematical models of probabilistic systems. They are employed in various fields such as speech recognition, signal processing, and biological sequence analysis. Motivated by applications in stochastic runtime verification, we consider the problem of distinguishing two given HMCs based on a single observation sequence that one of the HMCs generates. More precisely, given two HMCs and an observation sequence, a distinguishing algorithm is expected to identify the HMC that generates the observation sequence. Two HMCs are called distinguishable if for every ε > 0 there is a distinguishing algorithm whose error probability is less than ε. We show that one can decide in polynomial time whether two HMCs are distinguishable. Further, we present and analyze two distinguishing algorithms for distinguishable HMCs. The first algorithm makes a decision after processing a fixed number of observations, and it exhibits two-sided error. The second algorithm processes an unbounded number of observations, but the algorithm has only one-sided error. The error probability, for both algorithms, decays exponentially with the number of processed observations. We also provide an algorithm for distinguishing multiple HMCs.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2933608"}, {"primary_key": "4158063", "vector": [], "sparse_vector": [], "title": "Upper Bounds on the Quantifier Depth for Graph Differentiation in First Order Logic.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We show that on graphs with n vertices the 2-dimensional <PERSON><PERSON><PERSON><PERSON><PERSON> algorithm requires at most O(n2 / log(n)) iterations to reach stabilization. This in particular shows that the previously best, trivial upper bound of O(n2) is asymptotically not tight. In the logic setting this translates to the statement that if two graphs of size n can be distinguished by a formula in first order logic with counting with 3 variables (i.e., in C3) then they can also be distinguished by a C3-formula that has quantifier depth at most O(n2 / log(n)).", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2933595"}, {"primary_key": "4158064", "vector": [], "sparse_vector": [], "title": "How unprovable is <PERSON><PERSON>&<PERSON>po<PERSON>;s decidability theorem?", "authors": ["Leszek Aleksander <PERSON>", "<PERSON><PERSON>"], "summary": "We study the strength of set-theoretic axioms needed to prove <PERSON><PERSON>'s theorem on the decidability of the MSO theory of the infinite binary tree. We first show that over the second-order arithmetic theory ACA0, the complementation theorem for nondeterministic tree automata is equivalent to a statement expressing the determinacy of all Gale-Stewart games given by <PERSON><PERSON>(∑02) sets. It follows that the complementation theorem is provable from Π13- but not Δ13-comprehension.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934543"}, {"primary_key": "4158065", "vector": [], "sparse_vector": [], "title": "Invisible Pushdown Languages.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Context-free languages allow one to express data with hierarchical structure, at the cost of losing some of the useful properties of languages recognized by finite automata on words. However, it is possible to restore some of these properties by making the structure of the tree visible, such as is done by visibly pushdown languages, or finite automata on trees. In this paper, we show that the structure given by such approaches remains invisible when it is read by a finite automaton (on word). In particular, we show that separability with a regular language is undecidable for visibly pushdown languages, just as it is undecidable for general context-free languages.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2933579"}, {"primary_key": "4158066", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON>rov Extension, Martingale Convergence, and Compositionality of Processes.", "authors": ["<PERSON>"], "summary": "We show that the <PERSON><PERSON><PERSON><PERSON> extension theorem and the <PERSON>b martingale convergence theorem are two aspects of a common generalization, namely a colimit-like construction in a category of Radon spaces and reversible Markov kernels. The construction provides a compositional denotational semantics for lossless iteration in probabilistic programming languages, even in the absence of a natural partial order.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2933610"}, {"primary_key": "4158067", "vector": [], "sparse_vector": [], "title": "Weak consistency notions for all the CSPs of bounded width.", "authors": ["<PERSON><PERSON>"], "summary": "The characterization of all the Constraint Satisfaction Problems of bounded width, proposed by <PERSON><PERSON> and <PERSON><PERSON><PERSON> [SICOMP'98], was confirmed in [Bulatov'09] and independently in [FOCS'09, JACM'14]. Both proofs are based on the (2,3)-consistency (using Prague consistency in [FOCS'09], directly in [Bulatov'09]) which is costly to verify.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934510"}, {"primary_key": "4158068", "vector": [], "sparse_vector": [], "title": "Constructions with Non-Recursive Higher Inductive Types.", "authors": ["<PERSON><PERSON>"], "summary": "Higher inductive types (HITs) in homotopy type theory are a powerful generalization of inductive types. Not only can they have ordinary constructors to define elements, but also higher constructors to define equalities (paths). We say that a HIT H is non-recursive if its constructors do not quantify over elements or paths in H. The advantage of non-recursive HITs is that their elimination principles are easier to apply than those of general HITs.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2933586"}, {"primary_key": "4158069", "vector": [], "sparse_vector": [], "title": "Two-variable Logic with a Between Relation.", "authors": ["<PERSON>", "<PERSON>", "Paritosh K<PERSON>", "<PERSON>"], "summary": "We study an extension of FO2[<], first-order logic interpreted in finite words, in which formulas are restricted to use only two variables. We adjoin to this language two-variable atomic formulas that say, 'the letter a appears between positions x and y'. This is, in a sense, the simplest property that is not expressible using only two variables.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2935308"}, {"primary_key": "4158070", "vector": [], "sparse_vector": [], "title": "Infinitary Lambda Calculi from a Linear Perspective.", "authors": ["Ugo <PERSON>"], "summary": "We introduce a linear infinitary λ-calculus, called ℓΛ∞, in which two exponential modalities are available, the first one being the usual, finitary one, the other being the only construct interpreted coinductively. The obtained calculus embeds the infinitary applicative λ-calculus and is universal for computations over infinite strings. What is particularly interesting about ℓΔ∞, is that the refinement induced by linear logic allows to restrict both modalities so as to get calculi which are terminating inductively and productive coinductively. We exemplify this idea by analysing a fragment of ℓΛ built around the principles of SLL and 4LL. Interestingly, it enjoys confluence, contrarily to what happens in ordinary infinitary λ-calculi.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934505"}, {"primary_key": "4158071", "vector": [], "sparse_vector": [], "title": "Fixed Points In Quantitative Semantics.", "authors": ["<PERSON>"], "summary": "We describe an interpretation of recursive computation in a symmetric monoidal category with infinite biproducts and cofree commutative comonoids (for instance, the category of free modules over a complete semiring). Such categories play a significant role in \"quantitative\" models of computation: they bear a canonical complete monoid enrichment, but may not be cpo-enriched, making standard techniques for reasoning about fixed points unavailable. By constructing a bifree algebra for the cofree exponential, we obtain fixed points for morphisms in its co-K<PERSON><PERSON><PERSON> category without requiring any order-theoretic structure. These fixed points corresponding to infinite sums of finitary approximants indexed over the nested finite multisets, each representing a unique call-pattern for computation of the fixed point. We illustrate this construction by using it to give a denotational semantics for PCF with non-deterministic choice and scalar weights from a complete semiring, proving that this is computationally adequate with respect to an operational semantics which evaluates a term by taking a weighted sum of the residues of its terminating reduction paths.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934569"}, {"primary_key": "4158072", "vector": [], "sparse_vector": [], "title": "The Complexity of Coverability in ν-Petri Nets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We show that the coverability problem in ν-Petri nets is complete for 'double Ackermann' time, thus closing an open complexity gap between an A<PERSON>mann lower bound and a hyper-Ackermann upper bound. The coverability problem captures the verification of safety properties in this nominal extension of Petri nets with name management and fresh name creation. Our completeness result establishes ν-Petri nets as a model of intermediate power among the formalisms of nets enriched with data, and relies on new algorithmic insights brought by the use of well-quasi-order ideals.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2933593"}, {"primary_key": "4158073", "vector": [], "sparse_vector": [], "title": "Ability to Count Messages Is Worth Θ(Δ) Rounds in Distributed Computing.", "authors": ["<PERSON><PERSON>"], "summary": "<PERSON> et al. (PODC 2012, Distributed Computing 2015) identified seven different message-passing models of distributed computing---one of which is the port-numbering model---and provided a complete classification of their computational power relative to each other. However, their method for simulating the ability to count incoming messages causes an additive overhead of 2Δ -- 2 communication rounds, and it was not clear if this is actually optimal. In this paper we give a positive answer, by using bisimulation as our main tool: there is a matching linear-in-Δ lower bound. This closes the final gap in our understanding of the models, with respect to the number of communication rounds. By a previously identified connection to modal logic, our result has implications to the relationship between multimodal logic and graded multimodal logic.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934567"}, {"primary_key": "4158074", "vector": [], "sparse_vector": [], "title": "Unified Semantics and Proof System for Classical, Intuitionistic and Affine Logics.", "authors": ["<PERSON>"], "summary": "This paper modifies our previous work in combining classical logic with intuitionistic logic [16, 17] to also include affine linear logic, resulting in a system we call Affine Control Logic. A propositional system with six binary connectives is defined and given a phase space interpretation. Choosing classical, intuitionistic or affine reasoning is entirely dependent on the subformula property. Moreover, the connectives of these logics can mix without restriction. We give a sound and complete sequent calculus that requires novel proof transformations for cut elimination. Compared to linear logic, classical fragments of proofs are better isolated from non-classical fragments. One of our goals is to allow non-classical restrictions to coexist with computational interpretations of classical logic such as found in the λμ calculus. In fact, we show that the transition between different modes of proof, classical, intuitionistic and affine, can be interpreted by delimited control operators. We also discuss how to extend the definition of focused proofs to this logic.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2933581"}, {"primary_key": "4158075", "vector": [], "sparse_vector": [], "title": "Differential Refinement Logic.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We introduce differential refinement logic (dRL), a logic with first-class support for refinement relations on hybrid systems, and a proof calculus for verifying such relations. dRL simultaneously solves several seemingly different challenges common in theorem proving for hybrid systems: 1. When hybrid systems are complicated, it is useful to prove properties about simpler and related subsystems before tackling the system as a whole. 2. Some models of hybrid systems can be implementation-specific. Verification can be aided by abstracting the system down to the core components necessary for safety, but only if the relations between the abstraction and the original system can be guaranteed. 3. One approach to taming the complexities of hybrid systems is to start with a simplified version of the system and iteratively expand it. However, this approach can be costly, since every iteration has to be proved safe from scratch, unless refinement relations can be leveraged in the proof. 4. When proofs become large, it is difficult to maintain a modular or comprehensible proof structure. By using a refinement relation to arrange proofs hierarchically according to the structure of natural subsystems, we can increase the readability and modularity of the resulting proof. dRL extends an existing specification and verification language for hybrid systems (differential dynamic logic, dL) by adding a refinement relation to directly compare hybrid systems. This paper gives a syntax, semantics, and proof calculus for dRL. We demonstrate its usefulness with examples where using refinement results in easier and better-structured proofs.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934555"}, {"primary_key": "4158076", "vector": [], "sparse_vector": [], "title": "Quantitative Algebraic Reasoning.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We develop a quantitative analogue of equational reasoning which we call quantitative algebra. We define an equality relation indexed by rationals: a = ε b which we think of as saying that \"a is approximately equal to b up to an error of ε \". We have 4 interesting examples where we have a quantitative equational theory whose free algebras correspond to well known structures. In each case we have finitary and continuous versions. The four cases are: Hausdorff metrics from quantitive semilattices; p-Was<PERSON>stein metrics (hence also the Ka<PERSON><PERSON>ich metric) from barycentric algebras and also from pointed barycentric algebras and the total variation metric from a variant of barycentric algebras.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934518"}, {"primary_key": "4158077", "vector": [], "sparse_vector": [], "title": "Church Meets <PERSON> and <PERSON>.", "authors": ["<PERSON><PERSON>"], "summary": "The Cook<PERSON><PERSON> theorem (the statement that SAT is NP-complete) is a central result in structural complexity theory. Is it possible to prove it using the lambda-calculus instead of Turing machines? We address this question via the notion of affine approximation, which offers the possibility of using order-theoretic arguments, in contrast to the machine-level arguments employed in standard proofs. However, due to the size explosion problem in the lambda-calculus (a linear number of reduction steps may generate exponentially big terms), a naive transliteration of the proof of the Cook<PERSON><PERSON> theorem fails. We propose to fix this mismatch using the author's recently introduced parsimonious lambda-calculus, reproving the Cook<PERSON><PERSON> theorem and several related results in this higher-order framework. We also present an interesting relationship between approximations and intersection types, and discuss potential applications.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934541"}, {"primary_key": "4158078", "vector": [], "sparse_vector": [], "title": "A bifibrational reconstruction of <PERSON><PERSON>&apos;s presheaf hyperdoctrine.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Combining insights from the study of type refinement systems and of monoidal closed chiralities, we show how to reconstruct <PERSON><PERSON>'s hyperdoctrine of presheaves using a full and faithful embedding into a monoidal closed bifibration living now over the compact closed category of small categories and distributors. Besides revealing dualities which are not immediately apparent in the traditional presentation of the presheaf hyperdoctrine, this reconstruction leads us to an axiomatic treatment of directed equality predicates (modelled by hom presheaves), realizing a vision initially set out by <PERSON><PERSON> (1970). It also leads to a simple calculus of string diagrams (representing presheaves) that is highly reminiscent of <PERSON><PERSON> <PERSON><PERSON>'s existential graphs for predicate logic, refining an earlier interpretation of existential graphs in terms of Boolean hyperdoctrines by <PERSON> and <PERSON>. Finally, we illustrate how this work extends to a bifibrational setting a number of fundamental ideas of linear logic.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934525"}, {"primary_key": "4158079", "vector": [], "sparse_vector": [], "title": "Denotational semantics of recursive types in synthetic guarded domain theory.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Guarded recursion is a form of recursion where recursive calls are guarded by delay modalities. Previous work has shown how guarded recursion is useful for reasoning operationally about programming languages with advanced features including general references, recursive types, countable non-determinism and concurrency.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934516"}, {"primary_key": "4158080", "vector": [], "sparse_vector": [], "title": "Reasoning about Recursive Probabilistic Programs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents a wp--style calculus for obtaining expectations on the outcomes of (mutually) recursive probabilistic programs. We provide several proof rules to derive one-- and two--sided bounds for such expectations, and show the soundness of our wp--calculus with respect to a probabilistic pushdown automaton semantics. We also give a wp--style calculus for obtaining bounds on the expected runtime of recursive programs that can be used to determine the (possibly infinite) time until termination of such programs.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2935317"}, {"primary_key": "4158081", "vector": [], "sparse_vector": [], "title": "Solvability of Matrix-Exponential Equations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider a continuous analogue of <PERSON><PERSON> et al.'s and <PERSON><PERSON> et al.'s problem of solving multiplicative matrix equations. Given $k+1$ square matrices $A_{1}, \\ldots, A_{k}, C$, all of the same dimension, whose entries are real algebraic, we examine the problem of deciding whether there exist non-negative reals $t_{1}, \\ldots, t_{k}$ such that \\begin{align*} \\prod \\limits_{i=1}^{k} \\exp(A_{i} t_{i}) = C . \\end{align*} We show that this problem is undecidable in general, but decidable under the assumption that the matrices $A_{1}, \\ldots, A_{k}$ commute. Our results have applications to reachability problems for linear hybrid automata. Our decidability proof relies on a number of theorems from algebraic and transcendental number theory, most notably those of <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, as well as some useful geometric and linear-algebraic results, including the <PERSON><PERSON> theorem and a new (to the best of our knowledge) result about the uniqueness of strictly upper triangular matrix logarithms of upper unitriangular matrices. On the other hand, our undecidability result is shown by reduction from <PERSON><PERSON>'s Tenth Problem.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934538"}, {"primary_key": "4158082", "vector": [], "sparse_vector": [], "title": "Program Equivalence is Coinductive.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We describe computational models, notably Turing and counter machines, as state transition systems with side effects. Side effects are expressed via an algebraic signature and interpreted over comodels for that signature: comodels describe the memory model while the transition system captures the control structure. Equational reasoning over comodels is known to be subtle. We identify a criterion on equational theories and classes of comodels that guarantees completeness, over the given class of comodels, of the standard equational calculus, and show that this criterion is satisfied in our leading examples. Based on a complete equational axiomatization of the memory (co)model, we then give a complete inductive-coinductive calculus for simulation between states, where a state simulates another if it has at least the same terminating computations, with the same cumulative effect on global state. Extensional equivalence of computations can then be expressed as mutual simulation. The crucial use of coinduction is to deal with non-termination of the simulated computation where the coinductive rule permits infinite unfolding.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934506"}, {"primary_key": "4158083", "vector": [], "sparse_vector": [], "title": "A constructive function-theoretic approach to topological compactness.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We introduce 2-compactness, a constructive function-theoretic alternative to topological compactness, based on the notions of <PERSON> space and <PERSON> morphism, which are constructive function-theoretic alternatives to topological space and continuous function, respectively. We show that the notion of Bishop morphism is reduced to uniform continuity in important cases, overcoming one of the obstacles in developing constructive general topology posed by <PERSON>. We prove that 2-compactness generalizes metric compactness, namely that the uniformly continuous real-valued functions on a compact metric space form a 2-compact Bishop topology. Among other properties of 2-compact Bishop spaces, the countable Tychonoff compactness theorem is proved for them. We work within BISH*, <PERSON>'s informal system of constructive mathematics BISH equipped with inductive definitions with rules of countably many premises, a system strongly connected to <PERSON><PERSON><PERSON><PERSON><PERSON>'s Type Theory.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2933582"}, {"primary_key": "4158084", "vector": [], "sparse_vector": [], "title": "Coinduction All the Way Up.", "authors": ["<PERSON>"], "summary": "We revisit coinductive proof principles from a lattice theoretic point of view. By associating to any monotone function a function which we call the companion, we give a new presentation of both <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'s seminal result, and of the more recent theory of enhancements of the coinductive proof method (up-to techniques).", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934564"}, {"primary_key": "4158085", "vector": [], "sparse_vector": [], "title": "Gödel&apos;s functional interpretation and the concept of learning.", "authors": ["<PERSON>"], "summary": "In this article we study <PERSON><PERSON><PERSON>'s functional interpretation from the perspective of learning. We define the notion of a learning algorithm, and show that intuitive realizers of the functional interpretation of both induction and various comprehension schemas can be given in terms of these algorithms. In the case of arithmetical comprehension, we clarify how our learning realizers compare to those obtained traditionally using bar recursion, demonstrating that bar recursive interpretations of comprehension correspond to 'forgetful' learning algorithms. The main purpose of this work is to gain a deeper insight into the semantics of programs extracted using the functional interpretation. However, in doing so we also aim to better understand how it relates to other interpretations of classical logic for which the notion of learning is inbuilt, such as <PERSON><PERSON>'s epsilon calculus or the more recent learning-based realizability interpretations of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2933605"}, {"primary_key": "4158086", "vector": [], "sparse_vector": [], "title": "Towards Compositional Feedback in Non-Deterministic and Non-Input-Receptive Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Feedback is an essential composition operator in many classes of reactive and other systems. This paper studies feedback in the context of compositional theories with refinement. Such theories allow to reason about systems on a component-by-component basis, and to characterize substitutability as a refinement relation. Although compositional theories of feedback do exist, they are limited either to deterministic systems (functions) or input-receptive systems (total relations). In this work we propose a compositional theory of feedback which applies to non-deterministic and non-input-receptive systems (e.g., partial relations). To achieve this, we use the semantic frameworks of predicate and property transformers, and relations with fail and unknown values. We show how to define instantaneous feedback for stateless systems and feedback with unit delay for stateful systems. Both operations preserve the refinement relation, and both can be applied to non-deterministic and non-input-receptive systems.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934503"}, {"primary_key": "4158087", "vector": [], "sparse_vector": [], "title": "Factor Varieties and Symbolic Computation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose an algebraization of classical and non-classical logics, based on factor varieties and decomposition operators. In particular, we provide a new method for determining whether a propositional formula is a tautology or a contradiction. This method can be automatized by defining a term rewriting system that enjoys confluence and strong normalization. This also suggests an original notion of logical gate and circuit, where propositional variables becomes logical gates and logical operations are implemented by substitution. Concerning formulas with quantifiers, we present a simple algorithm based on factor varieties for reducing first-order classical logic to equational logic. We achieve a completeness result for first-order classical logic without requiring any additional structure.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2933600"}, {"primary_key": "4158088", "vector": [], "sparse_vector": [], "title": "Interaction Graphs: Full Linear Logic.", "authors": ["<PERSON>"], "summary": "Interaction graphs were introduced as a general, uniform, construction of dynamic models of linear logic, encompassing all Geometry of Interaction (GoI) constructions introduced so far. This series of work was inspired from <PERSON><PERSON><PERSON>'s hyperfinite GoI, and develops a quantitative approach that should be understood as a dynamic version of weighted relational models. Until now, the interaction graphs framework has been shown to deal with exponentials for the constrained system ELL (Elementary Linear Logic) while keeping its quantitative aspect. Adapting older constructions by <PERSON><PERSON><PERSON>, one can clearly define \"full\" exponentials, but at the cost of these quantitative features. We show here that allowing interpretations of proofs to use continuous (yet finite in a measure-theoretic sense) sets of states, as opposed to earlier Interaction Graphs constructions were these sets of states were discrete (and finite), provides a model for full linear logic with second order quantification.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934568"}, {"primary_key": "4158089", "vector": [], "sparse_vector": [], "title": "Semantics for probabilistic programming: higher-order functions, continuous distributions, and soft constraints.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study the semantic foundation of expressive probabilistic programming languages, that support higher-order functions, continuous distributions, and soft constraints (such as Anglican, Church, and Venture). We define a metalanguage (an idealised version of Anglican) for probabilistic computation with the above features, develop both operational and denotational semantics, and prove soundness, adequacy, and termination. They involve measure theory, stochastic labelled transition systems, and functor categories, but admit intuitive computational readings, one of which views sampled random variables as dynamically allocated read-only variables. We apply our semantics to validate nontrivial equations underlying the correctness of certain compiler optimisations and inference algorithms such as sequential Monte Carlo simulation. The language enables defining probability distributions on higher-order functions, and we study their properties.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2935313"}, {"primary_key": "4158090", "vector": [], "sparse_vector": [], "title": "Plays as Resource Terms via Non-idempotent Intersection Types.", "authors": ["<PERSON><PERSON>", "C.<PERSON><PERSON><PERSON>"], "summary": "A program is interpreted as a collection of resource terms by the <PERSON> expansion, as a collection of plays by game semantics, and as a collection of types by a non-idempotent intersection type assignment system. This paper investigates the connection between these models and aims to show that they are essentially the same in a certain sense. Technically we study the relational interpretations of resource terms and of plays, which can be seen as non-idempotent intersection type assignment systems for resource terms and plays, respectively. We show that both relational interpretations are injective, have the same image, and respect composition. This result allows us to study a property of the game model by using the syntax of a resource calculus and vice versa.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934553"}, {"primary_key": "4158091", "vector": [], "sparse_vector": [], "title": "Semi-galois Categories I: The Classical Eilenberg Variety Theory.", "authors": ["<PERSON><PERSON>"], "summary": "Recently, <PERSON><PERSON><PERSON>'s variety theorem was reformulated in the light of <PERSON>'s duality theorem. On one level, this reformulation led to a unification of several existing Eilenberg-type theorems and further generalizations of these theorems. On another level, this reformulation is also a natural continuation of a research line on profinite monoids that has been developed since the late 1980s. The current paper concerns the latter in particular. In this relation, this paper introduces and studies the class of semi-galois categories, i.e. an extension of galois categories; and develops a particularly fundamental theory concerning semi-galois categories: That is, (I) a duality theorem between profinite monoids and semi-galois categories; (II) a coherent duality-based reformulation of two classical Eilenberg-type variety theorems due to <PERSON><PERSON><PERSON><PERSON> [30] and <PERSON><PERSON><PERSON> et al. [10]; and (III) a Galois-type classification of closed subgroups of profinite monoids in terms of finite discrete cofibrations over semi-galois categories.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2934528"}, {"primary_key": "4158092", "vector": [], "sparse_vector": [], "title": "Winning Cores in Parity Games.", "authors": ["Steen Vester"], "summary": "We introduce the novel notion of winning cores in parity games and develop a deterministic polynomial-time under-approximation algorithm for solving parity games based on winning core approximation. Underlying this algorithm are a number properties about winning cores which are interesting in their own right. In particular, we show that the winning core and the winning region for a player in a parity game are equivalently empty. Moreover, the winning core contains all fatal attractors but is not necessarily a dominion itself. Experimental results are very positive both with respect to quality of approximation and running time. It outperforms existing state-of-the-art algorithms significantly on most benchmarks.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2933589"}, {"primary_key": "4158093", "vector": [], "sparse_vector": [], "title": "Order-Invariance of Two-Variable Logic is Decidable.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "It is shown that order-invariance of two-variable first-logic is decidable in the finite. This is an immediate consequence of a decision procedure obtained for the finite satisfiability problem for existential second-order logic with two first-order variables (ESO2) on structures with two linear orders and one induced successor. We also show that finite satisfiability is decidable on structures with two successors and one induced linear order. In both cases, so far only decidability for monadic ESO2 has been known. In addition, the finite satisfiability problem for ESO2 on structures with one linear order and its induced successor relation is shown to be decidable in non-deterministic exponential time.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575.2933594"}, {"primary_key": "4210560", "vector": [], "sparse_vector": [], "title": "Proceedings of the 31st Annual ACM/IEEE Symposium on Logic in Computer Science, LICS &apos;16, New York, NY, USA, July 5-8, 2016", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This volume contains the proceedings of the Thirty-First Annual ACM/IEEE Symposium on Logic in Computer Science (LICS) held at Columbia University in New York City from July 5 to July 8, 2016. LICS is an annual international forum on theoretical and practical topics in computer science that relate to logic. The first LICS symposium was held in 1986, and LICS 2016 marks the thirtieth anniversary of that event.", "published": "2016-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/2933575"}]