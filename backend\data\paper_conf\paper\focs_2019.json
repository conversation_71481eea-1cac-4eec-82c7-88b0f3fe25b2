[{"primary_key": "3008645", "vector": [], "sparse_vector": [], "title": "A Deterministic Algorithm for Counting Colorings with 2-Delta Colors.", "authors": ["Jingcheng Liu", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give a polynomial time deterministic approximation algorithm (an FPTAS) for counting the number of q-colorings of a graph of maximum degree Delta, provided only that q ≥ 2Delta. This substantially improves on previous deterministic algorithms for this problem, the best of which requires q ≥ 2.58Delta, and matches the natural bound for randomized algorithms obtained by a straightforward application of Markov chain Monte Carlo. In the case when the graph is also triangle-free, we show that our algorithm applies under the weaker condition q ≥ αΔ+β, where α ≈ 1.764 and β = β(α) are absolute constants. Our result applies more generally to list colorings, and to the partition function of the anti-ferromagnetic Potts model. The core of our argument is the establishment of a region in the complex plane in which the <PERSON>tts model partition function (a classical graph polynomial) has no zeros. This result, which substantially sharpens previous work on the same problem, is of independent interest. Our algorithms follow immediately from zero-freeness via the \"polynomial interpolation\" method of Barvinok. Interestingly, our method for identifying the zero-free region leverages probabilistic and combinatorial ideas that have been used in the analysis of Markov chains.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00085"}, {"primary_key": "3008646", "vector": [], "sparse_vector": [], "title": "Leakage-Resilient Secret Sharing Against Colluding Parties.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this work, we consider the natural goal of designing secret sharing schemes that ensure security against an adversary who may learn some \"leaked'' information about all the shares. We say that a secret sharing scheme is p-party leakage-resilient, if the secret remains statistically hidden even after a computationally unbounded adversary learns a bounded amount of leakage, where each bit of leakage adaptively and jointly depends on the shares of an adaptively chosen subset of p parties. Existing multi-party secret sharing schemes (<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> FOCS 07), (<PERSON><PERSON> and Kumar STOC 18) and (<PERSON><PERSON><PERSON><PERSON>, <PERSON>gwe<PERSON>, <PERSON><PERSON> and <PERSON>bin CRYPTO 18) have focused on handling non-adaptive and individual leakage for (limited special cases of) threshold secret sharing schemes. (1) We give an unconditional compiler that transforms any secret sharing scheme on n parties into a p-party leakage-resilient one for p upto O(log n). This yields the first multi-party secret sharing schemes that are secure against adaptive or joint leakage. (2) As a natural extension, we initiate the study of leakage-resilient non-malleable secret sharing. We empower the adversary to adaptively leak from each of the shares and then use the leakage to tamper with all of them arbitrarily and independently. Leveraging our p-party leakage-resilient schemes, we compile any secret sharing scheme into a non-malleable one ensuring that any such tampering either preserves the secret or completely `destroys' it. This improves upon the non-malleable secret sharing scheme of (<PERSON><PERSON> and <PERSON> CRYPTO 18) where no leakage was permitted. Leakage-resilient non-malleable codes can be seen as 2-out-of-2 schemes satisfying our guarantee and have already found many applications in cryptography. (3) Our constructions rely on a clean connection we draw to communication complexity in the well-studied number-on-forehead (NOF) model and rely on functions that have strong communication-complexity lower bounds in the NOF model (in a black-box way). We get efficient p-party leakage-resilient schemes for p upto O(log n) as our share sizes have exponential dependence on p. We observe that improving this exponential dependence, even for simultaneous, non-adaptive leakage, will lead to progress on longstanding open problems in complexity theory.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00045"}, {"primary_key": "3008647", "vector": [], "sparse_vector": [], "title": "A Quantum Query Complexity Trichotomy for Regular Languages.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a trichotomy theorem for the quantum query complexity of regular languages. Every regular language has quantum query complexity Θ(1), Θ̃(√ n), or Θ(n). The extreme uniformity of regular languages prevents them from taking any other asymptotic complexity. This is in contrast to even the context-free languages, which we show can have query complexity Θ(n c ) for all computable c [1/2,1]. Our result implies an equivalent trichotomy for the approximate degree of regular languages, and a dichotomy-either Θ(1) or Θ(n)-for sensitivity, block sensitivity, certificate complexity, deterministic query complexity, and randomized query complexity. The heart of the classification theorem is an explicit quantum algorithm which decides membership in any star-free language in Õ(√n) time. This well-studied family of the regular languages admits many interesting characterizations, for instance, as those languages expressible as sentences in first-order logic over the natural numbers with the less-than relation. Therefore, not only do the star-free languages capture functions such as OR, they can also express functions such as \"there exist a pair of 2's such that everything between them is a 0.\" Thus, we view the algorithm for star-free languages as a nontrivial generalization of <PERSON><PERSON>'s algorithm which extends the quantum quadratic speedup to a much wider range of string-processing algorithms than was previously known. We show a variety of applications-new quantum algorithms for dynamic constant-depth Boolean formulas, balanced parentheses nested constantly many levels deep, binary addition, a restricted word break problem, and path-discovery in narrow grids-all obtained as immediate consequences of our classification theorem.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00061"}, {"primary_key": "3008648", "vector": [], "sparse_vector": [], "title": "Reed-Muller Codes Polarize.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Reed-Muller (RM) codes were introduced in 1954 and have long been conjectured to achieve <PERSON>'s capacity on symmetric channels. The activity on this conjecture has recently been revived with the emergence of polar codes. RM codes and polar codes are generated by the same matrix G_m= [1/1 0/1] ^⊗m but using different subset of rows. RM codes select simply rows having largest weights. Polar codes select instead rows having the largest conditional mutual information proceeding top to down in G_m; while this is a more elaborate and channel-dependent rule, the top-to-down ordering allows <PERSON><PERSON> to show that the conditional mutual information polarizes, and this gives directly a capacity-achieving code on any symmetric channel. RM codes are yet to be proved to have such a property, despite the recent success for the erasure channel. In this paper, we connect RM codes to polarization theory. We show that proceeding in the RM code ordering, i.e., not top-to-down but from the lightest to the heaviest rows in G_m, the conditional mutual information again polarizes. We further demonstrate that it does so faster than for polar codes. This implies that G_m contains another code, different than the polar code and called here the twin-RM code, that is provably capacity-achieving on any symmetric channel. This gives in particular a necessary condition for RM codes to achieve capacity on symmetric channels. It further gives a sufficient condition if the rows with largest conditional mutual information correspond to the heaviest rows, i.e., if the twin-RM code is the RM code. We demonstrate here that the two codes are at least similar and give further evidence that they are indeed the same.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00026"}, {"primary_key": "3008649", "vector": [], "sparse_vector": [], "title": "Beyond the Lovász Local Lemma: Point to Set Correlations and Their Algorithmic Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Following the groundbreaking algorithm of <PERSON><PERSON> and <PERSON><PERSON><PERSON> for the Lovasz Local Lemma (LLL), there has been a plethora of results analyzing local search algorithms for various constraint satisfaction problems. The algorithms considered fall into two broad categories: resampling algorithms, analyzed via different algorithmic LLL conditions; and backtracking algorithms, analyzed via entropy compression arguments. This paper introduces a new convergence condition that seamlessly handles resampling, backtracking, and hybrid algorithms, i.e., algorithms that perform both resampling and backtracking steps. Unlike previous work on the LLL, our condition replaces the notion of a dependency or causality graph by quantifying point-to-set correlations between bad events. As a result, our condition simultaneously: (i) captures the most general algorithmic LLL condition known as a special case; (ii) significantly simplifies the analysis of entropy compression applications; (iii) relates backtracking algorithms, which are conceptually very different from resampling algorithms, to the LLL; and most importantly (iv) allows for the analysis of hybrid algorithms, which were outside the scope of previous techniques. We give several applications of our condition, including a new hybrid vertex coloring algorithm that extends the recent breakthrough result of Molloy for coloring triangle-free graphs to arbitrary graphs.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00049"}, {"primary_key": "3008650", "vector": [], "sparse_vector": [], "title": "Stoquastic PCP vs. Randomness.", "authors": ["<PERSON><PERSON>", "Alex <PERSON>l Grilo"], "summary": "The derandomization of MA, the probabilistic version of NP, is a long standing open question. In this work, we connect this problem to a variant of another major problem: the quantum PCP conjecture. Our connection goes through the surprising quantum characterization of MA by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. They proved the MA-completeness of the problem of deciding whether the groundenergy of a uniform stoquastic local Hamiltonian is zero or inverse polynomial. We show that the gapped version of this problem, i.e. deciding if a given uniform stoquastic local Hamiltonian is frustration-free or has energy at least some constant ε, is in NP. Thus, if there exists a gap-amplification procedure for uniform stoquastic Local Hamiltonians (in analogy to the gap amplification procedure for constraint satisfaction problems in the original PCP theorem), then MA = NP (and vice versa). Furthermore, if this gap amplification procedure exhibits some additional (natural) properties, then P = RP. We feel this work opens up a rich set of new directions to explore, which might lead to progress on both quantum PCP and derandomization. We also provide two small side results of potential interest. First, we are able to generalize our result by showing that deciding if a uniform stoquastic Local Hamiltonian has negligible or constant frustration can be also solved in NP. Additionally, our work reveals a new MA-complete problem which we call SetCSP, stated in terms of classical constraints on strings of bits, which we define in the appendix. As far as we know this is the first (arguably) natural MA-complete problem stated in non-quantum CSP language.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00065"}, {"primary_key": "3008651", "vector": [], "sparse_vector": [], "title": "Approximating Constraint Satisfaction Problems on High-Dimensional Expanders.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We consider the problem of approximately solving constraint satisfaction problems with arity k > 2 (kCSPs) on instances satisfying certain expansion properties, when viewed as hypergraphs. Random instances of k-CSPs, which are also highly expanding, are well-known to be hard to approximate using known algorithmic techniques (and are widely believed to be hard to approximate in polynomial time). However, we show that this is not necessarily the case for instances where the hypergraph is a high-dimensional expander. We consider the spectral definition of highdimensional expansion used by <PERSON><PERSON> and <PERSON> [FOCS 2017] to construct certain primitives related to PCPs. They measure the expansion in terms of a parameter γ which is the analogue of the second singular value for expanding graphs. Extending the results by <PERSON><PERSON>, <PERSON><PERSON> and <PERSON> [FOCS 2011] for 2-CSPs, we show that if an instance of MAX k-CSP over alphabet [q] is a high-dimensional expander with parameter γ, then it is possible to approximate the maximum fraction of satisfiable constraints up to an additive error ε using q O(k) · (k/ε) O(1) levels of the sum-of-squares SDP hierarchy, provided γ ≤ ε O(1) · (1/(kq)) O(k) . Based on our analysis, we also suggest a notion of threshold-rank for hypergraphs, which can be used to extend the results for approximating 2-CSPs on low threshold-rank graphs. We show that if an instance of MAX k-CSP has threshold rank r for a threshold τ = (ε/k) O(1) · (1/q) O(k) , then it is possible to approximately solve the instance up to additive error ε, using r · q O(k) · (k/ε) O(1) levels of the sum-of-squares hierarchy. As in the case of graphs, high-dimensional expanders (with sufficiently small γ) have threshold rank 1 according to our definition.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00021"}, {"primary_key": "3008652", "vector": [], "sparse_vector": [], "title": "Efficient Construction of Rigid Matrices Using an NP Oracle.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "For a matrix H over a field F, its rank-r rigidity, denoted R_H (r), is the minimum Hamming distance from H to a matrix of rank at most r over F. A central open challenge in complexity theory is to give explicit constructions of rigid matrices for a variety of parameter settings. In this work, building on <PERSON>' seminal connection between circuit-analysis algorithms and lower bounds [<PERSON>, <PERSON><PERSON> 2014], we give a construction of rigid matrices in P^NP. Letting q = p^r be a prime power, we show: • There is an absolute constant δ>0 such that, for all constants ε >0, there is a P^NP machine M such that, for infinitely many N's, M(1^N) outputs a matrix H_N ∊ {0,1}^N×N with R_H_N (2^ (log N)^ 1/4-ε) ≥ δ ⋅ N^2 over F_q. Using known connections between matrix rigidity and other topics in complexity theory, we derive several consequences of our constructions, including: • There is a function f ∊ TIME [2^ (log n)^ ω(1)]^ NP such that f ∉ PH^cc. Previously, it was open whether E^NP ⊂ PH^cc. • For all ε >0, there is a P^NP machine M such that, for infinitely many N's, M(1^N) outputs an N × N matrix H_N ∊ {0,1}^N×N whose linear transformation requires depth-2 F_q-linear circuits of size Ω(N ⋅ 2^ (log N)^ 1/4 - ε). The previous best lower bound for an explicit family of N × N matrices over F_q was only Ω(N log^2 N / (log log N)^2), for asymptotically good error-correcting codes.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00067"}, {"primary_key": "3008653", "vector": [], "sparse_vector": [], "title": "A Tight Analysis of Bethe Approximation for Permanent.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We prove that the permanent of nonnegative matrices can be deterministically approximated within a factor of √2 n in polynomial time, improving upon the previous deterministic approximations. We show this by proving that the Bethe approximation of the permanent, a quantity computable in polynomial time, is at least as large as the permanent divided by √2 n . This resolves a conjecture of <PERSON><PERSON><PERSON><PERSON> [21]. Our bound is tight, and when combined with previously known inequalities lower bounding the permanent, fully resolves the quality of <PERSON><PERSON> approximation for permanent.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.000-3"}, {"primary_key": "3008654", "vector": [], "sparse_vector": [], "title": "Quantum Log-Approximate-Rank Conjecture is Also False.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In a recent breakthrough result, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON> [ECCC TR18-17] showed an exponential separation between the log approximate rank and randomized communication complexity of a total function $f$, hence refuting the log approximate rank conjecture of <PERSON> and <PERSON><PERSON><PERSON><PERSON> [2009]. We provide an alternate proof of their randomized communication complexity lower bound using the information complexity approach. Using the intuition developed there, we derive a polynomially-related quantum communication complexity lower bound using the quantum information complexity approach, thus providing an exponential separation between the log approximate rank and quantum communication complexity of $f$. Previously, the best known separation between these two measures was (almost) quadratic, due to <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> and <PERSON> [CCC, 2017]. This settles one of the main question left open by <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>, and refutes the quantum log approximate rank conjecture of <PERSON> and <PERSON> [2009]. Along the way, we develop a Shearer-type protocol embedding for product input distributions that might be of independent interest.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00063"}, {"primary_key": "3008655", "vector": [], "sparse_vector": [], "title": "Sampling Graphs without Forbidden Subgraphs and Unbalanced Expanders with Negligible Error.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Suppose that you wish to sample a random graph G over n vertices and m edges conditioned on the event that G does not contain a \"small\" t-size graph H (e.g., clique) as a subgraph. Assuming that most such graphs are H-free, the problem can be solved by a simple rejected-sampling algorithm (that tests for t-cliques) with an expected running time of n O(t) . Is it possible to solve the problem in running time that does not grow polynomially with n t ? In this paper, we introduce the general problem of sampling a \"random looking'' graph G with a given edge density that avoids some arbitrary predefined t-size subgraph H. As our main result, we show that the problem is solvable with respect to some specially crafted k-wise independent distribution over graphs. That is, we design a sampling algorithm for k-wise independent graphs that supports efficient testing for subgraph-freeness in time f(t) · n c where f is a function of t and the constant c in the exponent is independent of t. Our solution extends to the case where both G and H are d-uniform hypergraphs. We use these algorithms to obtain the first probabilistic construction of constant-degree polynomially-unbalanced expander graphs whose failure probability is negligible in n (i.e., n -ω(1) ). In particular, given constants d>c, we output a bipartite graph that has n left nodes, n c right nodes with right-degree of d so that any right set of size at most n Ω(1) expands by factor of Ω(d). This result is extended to the setting of unique expansion as well. We observe that such a negligible-error construction can be employed in many useful settings, and present applications in coding theory (batch codes and LDPC codes), pseudorandomness (low-bias generators and randomness extractors) and cryptography. Notably, we show that our constructions yield a collection of polynomial-stretch locally-computable cryptographic pseudorandom generators based on Goldreich's one-wayness assumption resolving a central open problem in parallel-cryptography (cf., Applebaum-Ishai-Kushilevitz, FOCS 2004; and Ishai-Kushilevitz-Ostrovsky-Sahai, STOC 2008).", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00020"}, {"primary_key": "3008656", "vector": [], "sparse_vector": [], "title": "Fast Uniform Generation of Random Graphs with Given Degree Sequences.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper we provide an algorithm that generates a graph with given degree sequence uniformly at random. Provided that Δ 4 =O(m), where Δ is the maximal degree and m is the number of edges, the algorithm runs in expected time O(m). Our algorithm significantly improves the previously most efficient uniform sampler, which runs in expected time O(m 2 Δ 2 ) for the same family of degree sequences. Our method uses a novel ingredient which progressively relaxes restrictions on an object being generated uniformly at random, and we use this to give fast algorithms for uniform sampling of graphs with other degree sequences as well. Using the same method, we also obtain algorithms with expected run time which is (i) linear for power-law degree sequences in cases where the previous best was O(n 4.081 ), and (ii) O(nd+d 4 ) for d-regular graphs when d=o(√ n), where the previous best was O(nd 3 ).", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00084"}, {"primary_key": "3008657", "vector": [], "sparse_vector": [], "title": "Improved Truthful Mechanisms for Combinatorial Auctions with Submodular Bidders.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A longstanding open problem in Algorithmic Mechanism Design is to design computationally-efficient truthful mechanisms for (approximately) maximizing welfare in combinatorial auctions with submodular bidders. The first such mechanism was obtained by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> [STOC'06] who gave an O(log 2 m)-approximation where m is number of items. This problem has been studied extensively since, culminating in an O(√log m)-approximation mechanism by <PERSON><PERSON><PERSON><PERSON> [STOC'16]. We present a computationally-efficient truthful mechanism with approximation ratio that improves upon the state-of-the-art by an exponential factor. In particular, our mechanism achieves an O((log log m) 3 )-approximation in expectation, uses only O(n) demand queries, and has universal truthfulness whether Θ(√log m) is the best approximation ratio in this guarantee. This settles an open question of <PERSON><PERSON><PERSON><PERSON> on setting in negative.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00024"}, {"primary_key": "3008658", "vector": [], "sparse_vector": [], "title": "Automating Resolution is NP-Hard.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We show that the problem of finding a Resolution refutation that is at most polynomially longer than a shortest one is NP-hard. In the parlance of proof complexity, Resolution is not automatizable unless P = NP. Indeed, we show that it is NP-hard to distinguish between formulas that have Resolution refutations of polynomial length and those that do not have subexponential length refutations. This also implies that Resolution is not automatizable in subexponential time or quasi-polynomial time unless~NP is included in SUBEXP or QP, respectively.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00038"}, {"primary_key": "3008659", "vector": [], "sparse_vector": [], "title": "General Framework for Metric Optimization Problems with Delay or with Deadlines.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we present a framework used to construct and analyze algorithms for online optimization problems with deadlines or with delay over a metric space. Using this framework, we present algorithms for several different problems. We present an O(D ^ 2) -competitive deterministic algorithm for online multilevel aggregation with delay on a tree of depth D, an exponential improvement over the O(D 4 2 D ) -competitive algorithm of <PERSON><PERSON><PERSON> et al. (ESA '16), where the only previously-known improvement was for the special case of deadlines by <PERSON><PERSON><PERSON><PERSON> et al. (SODA '17). We also present an O(log 2 n) -competitive randomized algorithm for online service with delay over any general metric space of n points, improving upon the O(log 4 n) -competitive algorithm by <PERSON><PERSON> et al. (STOC '17). In addition, we present the problem of online facility location with deadlines. In this problem, requests arrive over time in a metric space, and need to be served until their deadlines by facilities that are opened momentarily for some cost. We also consider the problem of facility location with delay, in which the deadlines are replaced with arbitrary delay functions. For those problems, we present O(log 2 n) -competitive algorithms, with n the number of points in the metric space. The algorithmic framework we present includes techniques for the design of algorithms as well as techniques for their analysis.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00013"}, {"primary_key": "3008660", "vector": [], "sparse_vector": [], "title": "Lower Bounds for Maximal Matchings and Maximal Independent Sets.", "authors": ["Alki<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "There are distributed graph algorithms for finding maximal matchings and maximal independent sets in O(Δ + log^* n) communication rounds; here n is the number of nodes and Δ is the maximum degree. The lower bound by <PERSON><PERSON> (1987, 1992) shows that the dependency on n is optimal: these problems cannot be solved in o(log^* n) rounds even if Δ = 2. However, the dependency on Δ is a long-standing open question, and there is currently an exponential gap between the upper and lower bounds. We prove that the upper bounds are tight. We show that maximal matchings and maximal independent sets cannot be found in o(Δ + log log n / log log log n) rounds with any randomized algorithm in the LOCAL model of distributed computing. As a corollary, it follows that there is no deterministic algorithm for maximal matchings or maximal independent sets that runs in o(Δ + log n / log log n) rounds; this is an improvement over prior lower bounds also as a function of n.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00037"}, {"primary_key": "3008661", "vector": [], "sparse_vector": [], "title": "Beyond Trace Reconstruction: Population Recovery from the Deletion Channel.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Population recovery is the problem of learning an unknown distribution over an unknown set of n-bit strings, given access to independent draws from the distribution that have been independently corrupted according to some noise channel. Recent work has intensively studied such problems both for the bit-flip noise channel and for the erasure noise channel. In this paper we initiate the study of population recovery under the deletion channel, in which each bit b is independently deleted with some fixed probability and the surviving bits are concatenated and transmitted. This is a far more challenging noise model than bit-flip~noise or erasure noise; indeed, even the simplest case in which the population is of size 1 (corresponding to a trivial probability distribution supported on a single string) corresponds to the trace reconstruction problem, which is a challenging problem that has received much recent attention. In this work we give algorithms and lower bounds for population recovery under the deletion channel when the population size is some value ℓ > 1. As our main sample complexity upper bound, we show that for any population size ℓ = o(log n / log log n), a population of ℓ strings from {o,1} n can be learned under deletion channel noise using 2 n(1/2+o(1)) samples. On the lower bound side, we show that at least n Ω(ℓ) samples are required to perform population recovery under the deletion channel when the population size is ℓ, for all ℓ ≤ n 1/2-ε . Our upper bounds are obtained via a robust multivariate generalization of a polynomial-based analysis, due to <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [KR97], of how the k-deck of a bit-string uniquely identifies the string; this is a very different approach from recent algorithms for trace reconstruction (the ℓ = 1 case). Our lower bounds build on moment-matching results of <PERSON><PERSON>[<PERSON><PERSON>:00] and <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>[<PERSON><PERSON>15].", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00050"}, {"primary_key": "3008662", "vector": [], "sparse_vector": [], "title": "New Notions and Constructions of Sparsification for Graphs and Hypergraphs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A sparsifier of a graph G (<PERSON><PERSON><PERSON><PERSON> and <PERSON>; <PERSON><PERSON><PERSON> and <PERSON>) is a sparse weighted subgraph G that approximately retains the same cut structure of G. For general graphs, non-trivial sparsification is possible only by using weighted graphs in which different edges have different weights. Even for graphs that admit unweighted sparsifiers (that is, sparsifiers in which all the edge weights are equal to the same scaling factor), there are no known polynomial time algorithms that find such unweighted sparsifiers. We study a weaker notion of sparsification suggested by <PERSON><PERSON><PERSON>, in which the number of cut edges in each cut (S, S) is not approximated within a multiplicative factor (1+ε), but is, instead, approximated up to an additive term bounded by ε times d·|S| + vol (S), where d is the average degree of the graph and vol (S) is the sum of the degrees of the vertices in S. We provide a probabilistic polynomial time construction of such sparsifiers for every graph, and our sparsifiers have a near-optimal number of edges O(ε -2 npolylog (1/ε)). We also provide a deterministic polynomial time construction that constructs sparsifiers with a weaker property having the optimal number of edges O(ε -2 n). Our constructions also satisfy a spectral version of the \"additive sparsification'' property. Notions of sparsification have also been studied for hypergraphs. Our construction of \"additive sparsifiers'' with O ε (n) edges also works for hypergraphs, and provides the first non-trivial notion of sparsification for hypergraphs achievable with O(n) hyperedges when ε and the rank r of the hyperedges are constant. Finally, we provide a new construction of spectral hypergraph sparsifiers, according to the standard definition, with poly (ε -1 , r) · n log n hyperedges, improving over the previous spectral construction (Soma and Yoshida) that used Õ(n 3 ) hyperedges even for constant r and ε.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00059"}, {"primary_key": "3008663", "vector": [], "sparse_vector": [], "title": "Breaking of 1RSB in Random Regular MAX-NAE-SAT.", "authors": ["<PERSON><PERSON><PERSON>", "Nike Sun", "<PERSON><PERSON><PERSON>"], "summary": "For several models of random constraint satisfaction problems, it was conjectured by physicists and later proved that a sharp satisfiability transition occurs. In the unsatisfiable regime, it is natural to consider the problem of max-satisfiability: violating the least number of constraints. This is a combinatorial optimization problem on the random energy landscape defined by the problem instance. In the bounded density regime, a very precise estimate of the max-sat value was obtained by <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> (2007), but it is not sharp enough to indicate the nature of the energy landscape. Later work (Sen, 2016; <PERSON>, 2016) shows that for very large but bounded density, the max-sat value approaches the mean-field (complete graph) limit: this is conjectured to have an \"FRSB\" structure where near-optimal configurations form clusters within clusters, in an ultrametric hierarchy of infinite depth inside the discrete cube. A stronger form of FRSB was shown in several recent works to have algorithmic implications (again, in complete graphs). Consequently we find it of interest to understand how the model transitions from 1RSB near the satisfiability threshold, to (conjecturally) FRSB at large density. In this paper we show that in the random regular NAE-SAT model, the 1RSB description breaks down by a certain threshold density that we estimate rather precisely. This is proved by an explicit perturbation in the 2RSB parameter space. The choice of perturbation is inspired by the \"bug proliferation\" mechanism proposed by physicists (<PERSON><PERSON> and <PERSON>, 2003; <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, 2004), corresponding roughly to a percolation-like threshold for a subgraph of dependent variables.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00086"}, {"primary_key": "3008664", "vector": [], "sparse_vector": [], "title": "Near-Optimal Massively Parallel Graph Connectivity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>"], "summary": "Identifying the connected components of a graph, apart from being a fundamental problem with countless applications, is a key primitive for many other algorithms. In this paper, we consider this problem in parallel settings. Particularly, we focus on the Massively Parallel Computations (MPC) model, which is the standard theoretical model for modern parallel frameworks such as MapReduce, Hadoop, or Spark. We consider the truly sublinear regime of MPC for graph problems where the space per machine is n δ for some desirably small constant δ ϵ (0, 1). We present an algorithm that for graphs with diameter D in the wide range [log ε n, n], takes O(log D) rounds to identify the connected components and takes O(log log n) rounds for all other graphs. The algorithm is randomized, succeeds with high probability, does not require prior knowledge of D, and uses an optimal total space of O(m). We complement this by showing a conditional lower-bound based on the widely believed TwoCycle conjecture that Ω(log D) rounds are indeed necessary in this setting. Studying parallel connectivity algorithms received a resurgence of interest after the pioneering work of <PERSON><PERSON> etal [FOCS 2018] who presented an algorithm with O(log D log log n) round-complexity. Our algorithm improves this result for the whole range of values of D and almost settles the problem due to the conditional lower-bound. Additionally, we show that with minimal adjustments, our algorithm can also be implemented in a variant of (CRCW) PRAM in asymptotically the same number of rounds.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00095"}, {"primary_key": "3008665", "vector": [], "sparse_vector": [], "title": "Fully Dynamic Maximal Independent Set with Polylogarithmic Update Time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Madhu <PERSON>"], "summary": "We present the first algorithm for maintaining a maximal independent set (MIS) of a fully dynamic graph-which undergoes both edge insertions and deletions-in polylogarithmic time. Our algorithm is randomized and, per update, takes O(log 2 Δ log 2 n) expected time. Furthermore, the algorithm can be adjusted to have O(log 2 Δ log 4 n) worst-case update-time with high probability. Here, n denotes the number of vertices and Δ is the maximum degree in the graph. The MIS problem in fully dynamic graphs has attracted significant attention after a breakthrough result of <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON> [STOC'18] who presented an algorithm with O(m 3/4 ) update-time (and thus broke the natural Ω(m) barrier) where m denotes the number of edges in the graph. This result was improved in a series of subsequent papers, though, the update-time remained polynomial. In particular, the fastest algorithm prior to our work had Õ(min{√n, m 1/3 }) update-time [<PERSON><PERSON><PERSON> et al. SODA'19]. Our algorithm maintains the lexicographically first MIS over a random order of the vertices. As a result, the same algorithm also maintains a 3-approximation of correlation clustering. We also show that a simpler variant of our algorithm can be used to maintain a random-order lexicographically first maximal matching in the same update-time.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00032"}, {"primary_key": "3008666", "vector": [], "sparse_vector": [], "title": "Exponentially Faster Massively Parallel Maximal Matching.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The study of approximate matching in the Massively Parallel Computations (MPC) model has recently seen a burst of breakthroughs. Despite this progress, however, we still have a far more limited understanding of maximal matching which is one of the central problems of parallel and distributed computing. All known MPC algorithms for maximal matching either take polylogarithmic time which is considered inefficient, or require a strictly super-linear space of n1+Ω(1) per machine. In this work, we close this gap by providing a novel analysis of an extremely simple algorithm. This affirmatively resolves the conjecture of <PERSON><PERSON><PERSON><PERSON> et al. [STOC'18] that a variant of this algorithm might work. The algorithm edge-samples the graph, randomly partitions the vertices, and finds a random greedy maximal matching within each partition. We show that this algorithm drastically reduces the vertex degrees. This, among some other results, leads to an O(log log Δ) round algorithm for maximal matching with O(n) space (or even mildly sublinear in n using standard techniques). As an immediate corollary, we get a 2 approximate minimum vertex cover in essentially the same rounds and space. This is the best possible approximation factor under standard assumptions, culminating a long line of research. It also leads to an improved O(log log Δ) round algorithm for 1+ ε approximate matching. All these results can also be implemented in the congested clique model within the same number of rounds.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00096"}, {"primary_key": "3008667", "vector": [], "sparse_vector": [], "title": "Finding Monotone Patterns in Sublinear Time.", "authors": ["<PERSON><PERSON><PERSON>", "Clément L. <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the problem of finding monotone subsequences in an array from the viewpoint of sublinear algorithms. For fixed k ϵ N and ε > 0, we show that the non-adaptive query complexity of finding a length-k monotone subsequence of f : [n] → R, assuming that f is ε-far from free of such subsequences, is Θ((log n) ⌊log_2k⌋ ). Prior to our work, the best algorithm for this problem, due to <PERSON>, <PERSON>, <PERSON>, and <PERSON> (2017), made (log n) O(k2) non-adaptive queries; and the only lower bound known, of Ω(log n) queries for the case k = 2, followed from that on testing monotonicity due to <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> (2000) and <PERSON> (2004).", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.000-1"}, {"primary_key": "3008668", "vector": [], "sparse_vector": [], "title": "Smoothed Analysis in Unsupervised Learning via Decoupling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Smoothed analysis is a powerful paradigm in overcoming worst-case intractability in unsupervised learning and high-dimensional data analysis. While polynomial time smoothed analysis guarantees have been obtained for worst-case intractable problems like tensor decompositions and learning mixtures of Gaussians, such guarantees have been hard to obtain for several other important problems in unsupervised learning. A core technical challenge in analyzing algorithms is obtaining lower bounds on the least singular value for random matrix ensembles with dependent entries, that are given by low-degree polynomials of a few base underlying random variables. In this work, we address this challenge by obtaining high-confidence lower bounds on the least singular value of new classes of structured random matrix ensembles of the above kind. We then use these bounds to design algorithms with polynomial time smoothed analysis guarantees for the following three important problems in unsupervised learning: (1) Robust subspace recovery, when the fraction of inliers in the d-dimensional subspace T of the n-dimensional Euclidean space is at least (d/n) t for any positive integer t. This contrasts with the known worst-case intractability when the fraction of inliers is at most d/n, and the previous smoothed analysis result (<PERSON><PERSON> and <PERSON>, 2013). (2) Learning overcomplete hidden markov models, where the size of the state space is any polynomial in the dimension of the observations. This gives the first polynomial time guarantees for learning overcomplete HMMs in the smoothed analysis model. (3) Higher order tensor decompositions, where we generalize and analyze the so-called FOOBI algorithm of Cardoso to find order-t rank-one tensors in a subspace. This gives polynomially robust decomposition algorithms for order-2t tensors with rank n t .", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00043"}, {"primary_key": "3008669", "vector": [], "sparse_vector": [], "title": "Residual Based Sampling for Online Low Rank Approximation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose online algorithms for Column Subset Selection (CSS) and Principal Component Analysis (PCA), two methods that are widely employed for data analysis, summarization, and visualization. Given a data matrix A that is revealed one column at a time, the online CSS problems asks to keep a small set of columns, S, that best approximates the space spanned by the columns of A. As each column arrives, the algorithm must irrevocably decide whether to add it to S, or to ignore it. In the online PCA problem, the goal is to output a projection of each column to a low dimensional subspace. In other words, the algorithm must provide an embedding for each column as it arrives, which cannot be changed as new columns arrive. While both of these problems have been studied in the online setting, only additive approximations were known prior to our work. The core of our approach is an adaptive sampling technique that gives a practical and efficient algorithm for both of these problems. We prove that by sampling columns using their 'residual norm'' (i.e. their norm orthogonal to directions sampled so far), we end up with a significantly better dependence between the number of columns sampled, and the desired error in the approximation. We further show how to combine our algorithm \"in series'' with prior algorithms. In particular, using the results of <PERSON><PERSON><PERSON><PERSON> et al. and <PERSON> et al. that have additive guarantees, we show how to improve the bounds on the error of our algorithm.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00094"}, {"primary_key": "3008670", "vector": [], "sparse_vector": [], "title": "A New Deterministic Algorithm for Dynamic Set Cover.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Danupon <PERSON>"], "summary": "We present a deterministic dynamic algorithm for maintaining a (1+ε)f-approximate minimum cost set cover with O(f log(Cn)/ε^2) amortized update time, when the input set system is undergoing element insertions and deletions. Here, n denotes the number of elements, each element appears in at most f sets, and the cost of each set lies in the range [1/C, 1]. Our result, together with that of <PERSON>~et~al.~[STOC'17], implies that there is a deterministic algorithm for this problem with O(f log(Cn)) amortized update time and O(min(log n, f)) -approximation ratio, which nearly matches the polynomial-time hardness of approximation for minimum set cover in the static setting. Our update time is only O(log (Cn)) away from a trivial lower bound. Prior to our work, the previous best approximation ratio guaranteed by deterministic algorithms was O(f^2), which was due to <PERSON><PERSON><PERSON><PERSON>~et~al.~[ICALP`15]. In contrast, the only result that guaranteed O(f) -approximation was obtained very recently by <PERSON><PERSON><PERSON>~et~al.~[STOC`19], who designed a dynamic algorithm with (1+ε)f-approximation ratio and O(f^2 log n/ε) amortized update time. Besides the extra O(f) factor in the update time compared to our and <PERSON>~et~al.'s results, the Abboud~et~al.~algorithm is randomized, and works only when the adversary is oblivious and the sets are unweighted (each set has the same cost). We achieve our result via the primal-dual approach, by maintaining a fractional packing solution as a dual certificate. This approach was pursued previously by <PERSON>hattacharya~et~al.~and <PERSON>~et~al., but not in the recent paper by Abboud~et~al. Unlike previous primal-dual algorithms that try to satisfy some local constraints for individual sets at all time, our algorithm basically waits until the dual solution changes significantly globally, and fixes the solution only where the fix is needed.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00033"}, {"primary_key": "3008671", "vector": [], "sparse_vector": [], "title": "An Improved Lower Bound for Sparse Reconstruction from Subsampled Hadamard Matrices.", "authors": ["Jaroslaw Blasiok", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give a short argument that yields a new lower bound on the number of subsampled rows from a bounded, orthonormal matrix necessary to form a matrix with the restricted isometry property. We show that a matrix formed by uniformly subsampling rows of an N × N Hadamard matrix contains a K-sparse vector in the kernel, unless the number of subsampled rows is Ω(K log K log (N/K)) --- our lower bound applies whenever min(K, N/K) > log C N. Containing a sparse vector in the kernel precludes not only the restricted isometry property, but more generally the application of those matrices for uniform sparse recovery.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00091"}, {"primary_key": "3008672", "vector": [], "sparse_vector": [], "title": "The Average-Case Complexity of Counting Cliques in Erdős-Rényi Hypergraphs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The complexity of clique problems on Erdos-Renyi random graphs has become a central topic in average-case complexity. Algorithmic phase transitions in these problems have been shown to have broad connections ranging from mixing of Markov chains and statistical physics to information-computation gaps in high-dimensional statistics. We consider the problem of counting k-cliques in s-uniform Erdos-Renyi hypergraphs G(n, c, s) with edge density c and show that its fine-grained average-case complexity can be based on its worstcase complexity. We prove the following: 1) Dense Erdos-Renyi hypergraphs: Counting k-cliques on G(n, c, s) with k and c constant matches its worst-case complexity up to a polylog(n) factor. Assuming ETH, it takes n Ω(k) time to count k-cliques in G(n, c, s) if k and c are constant. 2)Sparse Erdos-Renyi hypergraphs: When c = Θ(n -α ), for each fixed α our reduction yields different average-case phase diagrams depicting a tradeoff between runtime and k. Assuming the best known worst-case algorithms are optimal, in the graph case of s = 2, we establish that the exponent in n of the optimal running time for k-clique counting in G(n, c, s) is ωk/3 - Cα(k/2) + O k,α (1), where ω/9 ≤ C ≤ 1 and w is the matrix multiplication constant. In the hypergraph case of s ≥ 3, we show a lower bound at the exponent of k-α(k/s)+O k,α (1) which surprisingly is s tight against algorithmic achievability exactly for the set of c above the Erdos-Renyi k-clique percolation threshold. Our reduction yields the first known average-case hardness result on Erdos-Renyi hypergraphs based on a worst-case hardness assumption. We also analyze several natural algorithms for counting k-cliques in G(n, c, s) that establish our upper bounds in the sparse case c = Θ(n -α ).", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00078"}, {"primary_key": "3008673", "vector": [], "sparse_vector": [], "title": "Dynamic Approximate Shortest Paths and Beyond: Subquadratic and Worst-Case Update Time.", "authors": ["<PERSON>", "Danupon <PERSON>"], "summary": "Consider the following distance query for an n-node graph G undergoing edge insertions and deletions: given two sets of nodes I and <PERSON>, return the distances between every pair of nodes in I×J. This query is rather general and captures several versions of the dynamic shortest paths problem. In this paper, we develop an efficient (1 + ε)-approximation algorithm for this query using fast matrix multiplication. Our algorithm leads to answers for some open problems for Single-Source and All-Pairs Shortest Paths (SSSP and APSP), as well as for Diameter, Radius, and Eccentricities. Below are some highlights. Note that all our algorithms guarantee worst-case update time and are randomized (<PERSON>), but do not need the oblivious adversary assumption. Subquadratic update time for SSSP, Diameter, Centralities, ect.: When we want to maintain distances from a single node explicitly (without queries), a fundamental question is to beat trivially calling <PERSON><PERSON><PERSON>'s static algorithm after each update, taking Θ(n 2 ) update time on dense graphs. A better time complexity was not known even with amortization. It was known to be improbable for exact algorithms and for combinatorial any-approximation algorithms to polynomially beat the Ω(n 2 ) bound (under some conjectures) [<PERSON><PERSON><PERSON>, Zwick, ESA'04; <PERSON><PERSON><PERSON>, <PERSON><PERSON>, FOCS'14]. 1 Our algorithm with I = {s} and J = V (G) implies a (1 + ε)-approximation algorithm for this, guaranteeing Õ(n 1.823 /ε 2 ) worst-case update time for directed graphs with positive real weights in [1, W]. 2 With ideas from [<PERSON>itty, V. <PERSON>, STOC'13], we also obtain the first subquadratic worst-case update time for (5/3 + ε)-approximating the eccentricities and (1.5 + ε)-approximating the diameter and radius for unweighted graphs (with small additive errors). We also obtain the first subquadratic worst-case update time for (1 + ε)-approximating the closeness centralities for undirected unweighted graphs. Worst-case update time for APSP: When we want to maintain distances between all-pairs of nodes explicitly, the Õ(n 2 ) amortized update time by Demetrescu and Italiano [STOC'03] already matches the trivial Ω(n 2 ) lower bound. A fundamental question is whether it can be made worst-case. The state-of-the-art algorithm takes Õ(n 2+2/3 ) worst-case update time to maintain the distances exactly [Abraham, Chechik, Krinninger, SODA'17; Thorup STOC'05]. When it comes to (1+ε) approximation, this bound is still higher than calling the Õ(n ω /ε)-time static algorithm of Zwick [FOCS'98], where ω ≈ 2.373. Our algorithm with I = J = V (G) implies nearly tight bounds for this, namely Õ(n 2 /ε 1+ω ) for undirected unweighted graphs and Õ(n 2.045 /ε 2 ) for directed graphs with positive real weights. Besides this, we also obtain the first dynamic APSP algorithm with subquadratic update time and sublinear query time.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00035"}, {"primary_key": "3008674", "vector": [], "sparse_vector": [], "title": "Dynamic Matrix Inverse: Improved Algorithms and Matching Conditional Lower Bounds.", "authors": ["<PERSON>", "Danupon <PERSON>", "Thatchaphol <PERSON>"], "summary": "The dynamic matrix inverse problem is to maintain the inverse of a matrix undergoing element and column updates. It is the main subroutine behind the best algorithms for many dynamic problems whose complexity is not yet well-understood, such as maintaining the largest eigenvalue, rank and determinant of a matrix and maintaining reachability, distances, maximum matching size, and k-paths/cycles in a graph. Understanding the complexity of dynamic matrix inverse is a key to understand these problems. In this paper, we present (i) improved algorithms for dynamic matrix inverse and their extensions to some incremental/look-ahead variants, and (ii) variants of the Online Matrix-Vector conjecture [<PERSON><PERSON><PERSON>~et~al. STOC'15] that, if true, imply that these algorithms are tight. Our algorithms automatically lead to faster dynamic algorithms for the aforementioned problems, some of which are also tight under our conjectures, e.g. reachability and maximum matching size (closing the gaps for these two problems was in fact asked by <PERSON><PERSON><PERSON> and <PERSON><PERSON> [FOCS'14]). Prior best bounds for most of these problems date back to more than a decade ago [Sankowski FOCS'04, COCOON'05, SODA'07; <PERSON><PERSON><PERSON>'08; <PERSON><PERSON> and <PERSON><PERSON> Algorithmica'10; <PERSON><PERSON> et al. FOCS'14]. Our improvements stem mostly from the ability to use fast matrix multiplication \"one more time'', to maintain a certain transformation matrix which could be maintained only combinatorially previously (i.e. without fast matrix multiplication). Oddly, unlike other dynamic problems where this approach, once successful, could be repeated several times (\"bootstrapping''), our conjectures imply that this is not the case for dynamic matrix inverse and some related problems. However, when a small additional \"look-ahead'' information is provided we can perform such repetition to drive the bounds down further.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00036"}, {"primary_key": "3008675", "vector": [], "sparse_vector": [], "title": "Sensitive Distance and Reachability Oracles for Large Batch Updates.", "authors": ["<PERSON>", "Thatchaphol <PERSON>"], "summary": "In the sensitive distance oracle problem, there are three phases. We first preprocess a given directed graph G with n nodes and integer weights from [-W,W]. Second, given a single batch of f edge insertions and deletions, we update the data structure. Third, given a query pair of nodes (u,v), return the distance from u to v. In the easier problem called sensitive reachability oracle problem, we only ask if there exists a directed path from u to v. Our first result is a sensitive distance oracle with Õ(Wn ω+(3-ω)μ ) preprocessing time, Õ(Wn 2-μ f 2 + Wnf ω ) update time, and Õ(Wn 2-μ f + Wnf 2 ) query time where the parameter μ ϵ [0,1] can be chosen. The data-structure requires O(Wn 2+μ log n) bits of memory. This is the first algorithm that can handle f ≥ log n updates. Previous results (e.g. [<PERSON><PERSON><PERSON> et al. SICOMP'08; <PERSON> and <PERSON>rger SODA'08 and FOCS'09; <PERSON><PERSON> and <PERSON><PERSON>'09; <PERSON><PERSON> and <PERSON>OC<PERSON>'12]) can handle at most 2 updates. When 3 ≤ f ≤ log n, the only non-trivial algorithm was by [<PERSON><PERSON> and <PERSON><PERSON> FOCS'10]. When W = Õ(1), our algorithm simultaneously improves their preprocessing time, update time, and query time. In particular, when f = ω(1), their update and query time is Ω(n 2-o(1) ), while our update and query time are truly subquadratic in n, i.e., ours is faster by a polynomial factor of n. To highlight the technique, ours is the first graph algorithm that exploits the kernel basis decomposition of polynomial matrices by [Jeannerod and Villard J.Comp'05; Zhou, Labahn and Storjohann J.Comp'15] developed in the symbolic computation community. As an easy observation from our technique, we obtain the first sensitive reachability oracle can handle f≥log n updates. Our algorithm has O(n ω ) preprocessing time, O(f ω ) update time, and O(f 2 ) query time. This data-structure requires O(n 2 log n) bits of memory. Efficient sensitive reachability oracles were asked in [Chechik, Cohen, Fiat, and Kaplan SODA'17]. Our algorithm can handle any constant number of updates in constant time. Previous algorithms with constant update and query time can handle only at most f ≤ 2 updates. Otherwise, there are non-trivial results for f ≤ log n, though, with query time Ω(n) by adapting [Baswana, Choudhary and Roditty STOC'16].", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00034"}, {"primary_key": "3008676", "vector": [], "sparse_vector": [], "title": "Quantum Advantage with Noisy Shallow Circuits in 3D.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Prior work has shown that there exists a relation problem which can be solved with certainty by a constant-depth quantum circuit composed of geometrically local gates in two dimensions, but cannot be solved with high probability by any classical constant depth circuit composed of bounded fan-in gates. Here we provide two extensions of this result. Firstly, we show that a separation in computational power persists even when the constant-depth quantum circuit is restricted to geometrically local gates in one dimension. The corresponding quantum algorithm is the simplest we know of which achieves a quantum advantage of this type. Our second, main result, is that a separation persists even if the shallow quantum circuit is corrupted by noise. We construct a relation problem which can be solved with near certainty using a noisy constant-depth quantum circuit composed of geometrically local gates in three dimensions, provided the noise rate is below a certain constant threshold value. On the other hand, the problem cannot be solved with high probability by a noise-free classical circuit of constant depth. A key component of the proof is a quantum error-correcting code which admits constant-depth logical Clifford gates and single-shot logical state preparation. We show that the surface code meets these criteria.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00064"}, {"primary_key": "3008677", "vector": [], "sparse_vector": [], "title": "Towards a Theory of Non-Commutative Optimization: Geodesic 1st and 2nd Order Methods for Moment Maps and Polytopes.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper initiates a systematic development of a theory of non-commutative optimization, a setting which greatly extends ordinary (Euclidean) convex optimization. It aims to unify and generalize a growing body of work from the past few years which developed and analyzed algorithms for natural geodesically convex optimization problems on Riemannian manifolds that arise from the symmetries of non-commutative groups. More specifically, these are algorithms to minimize the moment map (a noncommutative notion of the usual gradient), and to test membership in moment polytopes (a vast class of polytopes, typically of exponential vertex and facet complexity, which quite magically arise from this apriori non-convex, non-linear setting). The importance of understanding this very general setting of geodesic optimization, as these works unveiled and powerfully demonstrate, is that it captures a diverse set of problems, many non-convex, in different areas of CS, math, and physics. Several of them were solved efficiently for the first time using noncommutative methods; the corresponding algorithms also lead to solutions of purely structural problems and to many new connections between disparate fields. In the spirit of standard convex optimization, we develop two general methods in the geodesic setting, a first order and a second order method, which respectively receive first and second order information on the \"derivatives\" of the function to be optimized. These in particular subsume all past results. The main technical work, again unifying and extending much of the previous work, goes into identifying the key parameters of the underlying group actions which control convergence to the optimum in each of these methods. These non-commutative analogues of \"smoothness\" in the commutative case are far more complex, and require significant algebraic and analytic machinery (much existing and some newly developed here). Despite this complexity, the way in which these parameters control convergence in both methods is quite simple and elegant. We also bound these parameters in several general cases. Our work points to intriguing open problems and suggests further research directions. We believe that extending this theory, namely understanding geodesic optimization better, is both mathematically and computationally fascinating; it provides a great meeting place for ideas and techniques from several very different research areas, and promises better algorithms for existing and yet unforeseen applications.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00055"}, {"primary_key": "3008678", "vector": [], "sparse_vector": [], "title": "Faster Matroid Intersection.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we consider the classic matroid intersection problem: given two matroids M 1 = (V, I 1 ) and M 2 = (V, I 2 ) defined over a common ground set V , compute a set S ∈ I 1 ∩ I 2 of largest possible cardinality, denoted by r. We consider this problem both in the setting where each Mi is accessed through an independence oracle, i.e. a routine which returns whether or not a set S ∈ I i in T ind time, and the setting where each Mi is accessed through a rank oracle, i.e. a routine which returns the size of the largest independent subset of S in M i in T rank time. In each setting we provide faster exact and approximate algorithms. Given an independence oracle, we provide an exact O(nr log r · T ind ) time algorithm. This improves upon previous best known running times of O(nr 1.5 ·T ind ) due to <PERSON>(n 2 ·T ind in 1986 and + n 3 ) due to <PERSON>, <PERSON>, and <PERSON> in 2015. We also provide two algorithms which compute a (1- ε-approximate solution to matroid intersection running in times O(n 1.5 /ε 1.5 · Tind) and O((n 2 r -1 ε -2 + r 1.5 ε -4.5 ) · Tind), respectively. These results improve upon the O(nr/ε · T ind )time algorithm of <PERSON> (noted recently by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>). Given a rank oracle, we provide algorithms with even better dependence on n and r. We provide an O(n√r log n · T rank )time exact algorithm and an O(nε -1 log n · T rank )-time algorithm which obtains a (1 - 0)-approximation to the matroid intersection problem. The former result improves over the O(nr · T rank + n 3 )-time algorithm by Lee, Sidford, and Wong. The rank oracle is of particular interest as the matroid intersection problem with this oracle is a special case (via Edmond's minimax characterization of matroid intersection) of the submodular function minimization (SFM) problem with an evaluation oracle, and understanding SFM query complexity is an outstanding open question.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00072"}, {"primary_key": "3008679", "vector": [], "sparse_vector": [], "title": "Expander Graphs - Both Local and Global.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Let G=(V,E) be a finite graph. For vϵ V we denote by G_v the subgraph of G that is induced by v's neighbor set. We say that G is (a,b) -regular for a>b>0 integers, if G is a-regular and G v is b-regular for every vϵ V. Recent advances in PCP theory call for the construction of infinitely many (a,b) -regular expander graphs G that are expanders also locally. Namely, all the graphs {G v |vϵ V} should be expanders as well. While random regular graphs are expanders with high probability, they almost surely fail to expand locally. Here we construct two families of (a,b) -regular graphs that expand both locally and globally. We also analyze the possible local and global spectral gaps of (a,b) -regular graphs. In addition, we examine our constructions vis-a-vis properties which are considered characteristic of high-dimensional expanders.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00019"}, {"primary_key": "3008680", "vector": [], "sparse_vector": [], "title": "Multi-resolution Hashing for Fast Pairwise Summations.", "authors": ["<PERSON>", "Paris Siminelakis"], "summary": "A basic computational primitive in the analysis of massive datasets is summing simple functions over a large number of objects. Modern applications pose an additional challenge in that such functions often depend on a parameter vector y (query) that is unknown a priori. Given a set of points X and a pairwise function w(x,y), we study the problem of designing a data-structure that enables sub-linear time approximation of the summation of w(x,y) for all x in X for any query point y. By combining ideas from Harmonic Analysis (partitions of unity and approximation theory) with Hashing-Based-Estimators [<PERSON><PERSON><PERSON>, Siminelakis FOCS'17], we provide a general framework for designing such data structures through hashing that reaches far beyond what previous techniques allowed. A key design principle is constructing a collection of hash families, each inducing a different collision probability between points in the dataset, such that the pointwise supremum of the collision probabilities scales as the square root of the function w(x,y). This leads to a data-structure that approximates pairwise summations using a sub-linear number of samples from each hash family. Using this new framework along with Distance Sensitive Hashing [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>'18], we show that such a collection can be constructed and evaluated efficiently for log-convex functions of the inner product between two vectors. Our method leads to data structures with sub-linear query time that significantly improve upon random sampling and can be used for Kernel Density, Partition Function Estimation and sampling.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00051"}, {"primary_key": "3008681", "vector": [], "sparse_vector": [], "title": "Fully Dynamic Maximal Independent Set in Expected Poly-Log Update Time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the fully dynamic maximal independent set (MIS) problem our goal is to maintain an MIS in a given graph G while edges are inserted and deleted from the graph. The first non-trivial algorithm for this problem was presented by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON> [STOC 2018] who obtained a deterministic fully dynamic MIS with O(m 3/4 ) update time. Later, this was independently improved by <PERSON> and <PERSON> and by <PERSON> and <PERSON> [arXiv 2018] to Õ(m 2/3 ) update time 1 <PERSON> and <PERSON> [arXiv 2018] also presented a randomized algorithm against an oblivious adversary with Õ(√m) update time. The current state of art is by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON> [SODA 2019] who obtained randomized algorithms against oblivious adversary with Õ(√n) and Õ(m 1/3 ) update times. In this paper, we propose a dynamic randomized algorithm against oblivious adversary with expected worst-case update time of O(log 4 n). As a direct corollary, one can apply the black-box reduction from a recent work by <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> [SODA 2019] to achieve O(log 6 n) worst-case update time with high probability. This is the first dynamic MIS algorithm with very fast update time of poly-log.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00031"}, {"primary_key": "3008682", "vector": [], "sparse_vector": [], "title": "Non-deterministic Quasi-Polynomial Time is Average-Case Hard for ACC Circuits.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Following the seminal work of [<PERSON>, <PERSON><PERSON> 2014], in a recent breakthrough, [<PERSON> and <PERSON>, STOC 2018] proved that NQP (non-deterministic quasi-polynomial time) does not have polynomial-size ACC 0 circuits. We strengthen the above lower bound to an average case one, by proving that for all constants c, there is a language in NQP, which is not 1/2+1/log c (n)-approximable by polynomial-size ACC 0 circuits. In fact, our lower bound holds for a larger circuit class: 2(log a n)-size ACC 0 circuits with a layer of threshold gates at the bottom (ACC ο THR circuits), for all constants a. Our work also improves the average-case lower bound for NEXP against polynomial-size ACC circuits by [<PERSON>, <PERSON>, and <PERSON><PERSON>, LATIN 2018]. Our new lower bound builds on several interesting components, including: : <PERSON><PERSON>'s theorem and the existence of an NC 1 -complete language which is random self-reducible. : The sub-exponential witness-size lower bound for NE against ACC 0 and the conditional non-deterministic PRG construction in [Williams, SICOMP 2016]. : An \"almost'' almost-everywhere MA average-case lower bound (which strengthens the corresponding worst-case lower bound in [<PERSON> and <PERSON>, STOC 2018]). A PSPACE-complete language which is same-length checkable, error-correctable and also has some other nice reducibility properties, which builds on [<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, Computational Complexity 2007]. Moreover, all its reducibility properties have corresponding low-depth non-adaptive oracle circuits. Like other lower bounds proved via the \"algorithmic approach'', the only property of ACC 0 ο THR exploited by us is the existence of a non-trivial SAT algorithm for ACC 0 ο THR [Williams, STOC 2014]. Therefore, for any typical circuit class ℓ, our results apply to them as well if the corresponding non-trivial SAT (in fact, GAP-UNSAT) algorithms are discovered.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00079"}, {"primary_key": "3008683", "vector": [], "sparse_vector": [], "title": "Hardness Magnification for all Sparse NP Languages.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In the Minimum Circuit Size Problem (MCSP[s(m)]), we ask if there is a circuit of size s(m) computing a given truth-table of length n = 2m. Recently, a surprising phenomenon termed as hardness magnification by [<PERSON> and <PERSON>, FOCS 2018] was discovered for MCSP[s(m)] and the related problem MKtP of computing time-bounded Kolmogorov complexity. In [<PERSON> and <PERSON>, FOCS 2018], [<PERSON>, <PERSON>, and Sant<PERSON>, CCC 2019], and [<PERSON>, <PERSON>, and <PERSON>, STOC 2019], it was shown that minor (n 1+ε -style) lower bounds for MCSP[2 o(m) ] or MKtP[ 2o(m) ] would imply breakthrough circuit lower bounds such as NP⊄P/ poly , NP⊄NC 1 , or EXP⊄P/poly. We consider the question: What is so special about MCSP and MKtP? Why do they admit this striking phenomenon? One simple property is that all variants of MCSP (and MKtP) considered in prior work are sparse languages. For example, MCSP[s(m)] has 2 Õ(s(m)) yes-instances of length n = 2m, so MCSP[2 o(m) ] is 2 no(1) -sparse. We show that there is a hardness magnification phenomenon for all equally-sparse NP languages. Formally, suppose there is an ε > 0 and a language L ∈ NP which is 2n o(1) -sparse, and L ∈/ Circuit[n1+ε]. Then NP does not have nk-size circuits for all k. We prove analogous theorems for <PERSON> <PERSON> formulas, B2-formulas, branching programs, AC 0 [6] and TC 0 circuits, and more: improving the state of the art in NP lower bounds against any of these models by an ε factor in the exponent would already imply NP lower bounds for all fixed polynomials. In fact, in our proofs it is not necessary to prove a (say) n 1+ε circuit size lower bound for L: one only has to prove a lower bound against n 1+ε -time n ε -space deterministic algorithms with n ε advice bits. Such lower bounds are well-known for non-sparse problems. Building on our techniques, we also show interesting new hardness magnifications for search-MCSP and search-MKtP (where one must output small circuits or short representations of strings), showing consequences such as ⊕P (or PP, PSPACE, and EXP) is not contained in P/poly (or NC 1 , AC 0 [6], or branching programs of polynomial size). For instance, if there is an ε > 0 such that search-MCSP[2 βm ] does not have De Morgan formulas of size n 3+ε for all constants ß > 0, then ⊕P⊄NC 1 .", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00077"}, {"primary_key": "3008684", "vector": [], "sparse_vector": [], "title": "A Polynomial-Time Approximation Scheme for Facility Location on Planar Graphs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the classic Facility Location problem on planar graphs (non-uniform, uncapacitated). Given an edge-weighted planar graph G, a set of clients C ⊆ V(G), a set of facilities F ⊆ V(G), and opening costs open: F → R ≥0 , the goal is to find a subset D of F that minimizes Σ cϵC min fϵD dist(c,f) + Σ fϵD open(f). The Facility Location problem remains one of the most classic and fundamental optimization problem for which it is not known whether it admits a polynomial-time approximation scheme (PTAS) on planar graphs despite significant effort for obtaining one. We solve this open problem by giving an algorithm that for any ε>0, computes a solution of cost at most (1+ε) times the optimum in time n (2 O(ε(-2) log(1/ε)) ) .", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00042"}, {"primary_key": "3008685", "vector": [], "sparse_vector": [], "title": "Inapproximability of Clustering in Lp Metrics.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON> C. S."], "summary": "Proving hardness of approximation for min-sum objectives is an infamous challenge. For classic problems such as the Traveling Salesman problem, the Steiner tree problem, or the k-means and k-median problems, the best known inapproximability bounds for L-p metrics of dimension O(log n) remain well below 1.01. In this paper, we take a significant step to improve the hardness of approximation of the k-means problem in various L-p metrics, and more particularly on Manhattan (L-1), Euclidean (L-2), Hamming (L-0) and <PERSON><PERSON><PERSON><PERSON>v (L-infinity) metrics of dimension log n and above. We show that it is hard to approximate the k-means objective in O(log n) dimensional space: (1) To a factor of 3.94 in the L-infinity metric when centers have to be chosen from a discrete set of locations (i.e., the discrete case). This improves upon the result of <PERSON><PERSON><PERSON> and <PERSON><PERSON> (SODA'03) who proved hardness of approximation for a factor less than 1.01. (2) To a factor of 1.56 in the L-1 metric and to a factor of 1.17 in the L-2 metric, both in the discrete case. This improves upon the result of <PERSON><PERSON><PERSON><PERSON> (SICOMP'00) who proved hardness of approximation for a factor less than 1.01 in both the metrics. (3) To a factor of 1.07 in the L-2 metric, when centers can be placed at arbitrary locations, (i.e., the continuous case). This improves on a result of <PERSON><PERSON><PERSON> (IPL'17) who proved hardness of approximation for a factor of 1.0013. We also obtain similar improvements over the state of the art hardness of approximation results for the k-median objective in various L-p metrics. Our hardness result given in (1) above, is under the standard NP is not equal to P assumption, whereas all the remaining results given above are under the Unique Games Conjecture (UGC). We can remove our reliance on UGC and prove standard NP-hardness for the above problems but for smaller approximation factors. Finally, we note that in order to obtain our result for the L-1 and L-infinity metrics in O(log n) dimensional space we introduce an embedding technique which combines the transcripts of certain communication protocols with the geometric realization of certain graphs.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00040"}, {"primary_key": "3008686", "vector": [], "sparse_vector": [], "title": "Tight Bounds for Online Edge Coloring.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>'s celebrated theorem asserts that any graph of maximum degree Δ admits an edge coloring using at most Δ+1 colors. In contrast, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> showed over a quarter century ago that the trivial greedy algorithm, which uses 2Δ-1 colors, is optimal among online algorithms. Their lower bound has a caveat, however: it only applies to low-degree graphs, with Δ=O(log n), and they conjectured the existence of online algorithms using Δ(1+o(1)) colors for Δ=ω(log n). Progress towards resolving this conjecture was only made under stochastic arrivals (<PERSON><PERSON><PERSON><PERSON> et al., FOCS'03 and <PERSON><PERSON> et al., SODA'10). We resolve the above conjecture for adversarial vertex arrivals in bipartite graphs, for which we present a (1+o(1))Δ-edge-coloring algorithm for Δ=ω(log n) known a priori. Surprisingly, if Δ is not known ahead of time, we show that no (e/(e-1) - Ω(1)) Δ-edge-coloring algorithm exists. We then provide an optimal, (e/(e-1)+o(1)) Δ-edge-coloring algorithm for unknown Δ=ω(log n). To obtain our results, we study a nonstandard fractional relaxation for edge coloring, for which we present optimal fractional online algorithms and a near-lossless online rounding scheme, yielding our optimal randomized algorithms.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00010"}, {"primary_key": "3008687", "vector": [], "sparse_vector": [], "title": "Modified log-Sobolev Inequalities for Strongly Log-Concave Distributions.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show that the modified log-So<PERSON>ev constant for a natural Markov chain which converges to an r-homogeneous strongly log-concave distribution is at least 1/r. Applications include an asymptotically optimal mixing time bound for the bases-exchange walk for matroids, and a concentration bound for <PERSON><PERSON><PERSON>tz functions over these distributions.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00083"}, {"primary_key": "3008688", "vector": [], "sparse_vector": [], "title": "A Characterization of Graph Properties Testable for General Planar Graphs with one-Sided Error (It&apos;s all About Forbidden Subgraphs).", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The problem of characterizing testable graph properties (properties that can be tested with a number of queries independent of the input size) is a fundamental problem in the area of property testing. While there has been some extensive prior research characterizing testable graph properties in the dense graphs model and we have good understanding of the bounded degree graphs model, no similar characterization has been known for general graphs, with no degree bounds. In this paper we take on this major challenge and consider the problem of characterizing all testable graph properties in general planar graphs. We consider the model in which a general planar graph can be accessed by the random neighbor oracle that allows access to any given vertex and access to a random neighbor of a given vertex. We show that, informally, a graph property P is testable with one-sided error for general planar graphs if and only if testing P can be reduced to testing for a finite family of finite forbidden subgraphs. While our presentation focuses on planar graphs, our approach extends easily to general minor-free graphs. Our analysis of the necessary condition relies on a recent construction of canonical testers in the random neighbor oracle model that is applied here to the one-sided error model for testing in planar graphs. The sufficient condition in the characterization reduces the problem to the task of testing H-freeness in planar graphs, and is the main and most challenging technical contribution of the paper: we show that for planar graphs (with arbitrary degrees), the property of being H-free is testable with one-sided error for every finite graph H, in the random neighbor oracle model.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00089"}, {"primary_key": "3008689", "vector": [], "sparse_vector": [], "title": "Junta Correlation is Testable.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The problem of tolerant junta testing is a natural and challenging problem which asks if the property of a function having some specified correlation with a k-Junta is testable. In this paper we give an affirmative answer to this question: There is an algorithm which given distance parameters c, d, and oracle access to a Boolean function f on the hypercube, has query complexity exp(k).poly(1/(cd)) and distinguishes between the following cases: 1) The distance of f from any k-junta is at least c; 2) There is a k-junta g which has distance at most d from f. This is the first non-trivial tester (i.e., query complexity is independent of the ambient dimension n) which works for all c and d (bounded by 0.5). The best previously known results by <PERSON><PERSON><PERSON> et al., required c to be at least 16d. In fact, with the same query complexity, we accomplish the stronger goal of identifying the most correlated k-junta, up to permutations of the coordinates. We can further improve the query complexity to poly(k/(c-d)) for the (weaker) task of distinguishing between the following cases: 1) The distance of f from any k'-junta is at least c. 2) There is a k-junta g which is at a distance at most d from f. Here k'=poly(k/(c-d)). Our main tools are Fourier analysis based algorithms that simulate oracle access to influential coordinates of functions.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00090"}, {"primary_key": "3008690", "vector": [], "sparse_vector": [], "title": "Agreement Testing Theorems on Layered Set Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce a framework of layered subsets, and give a sufficient condition for when a set system supports an agreement test. Agreement testing is a certain type of property testing that generalizes PCP tests such as the plane vs. plane test. Previous work has shown that high dimensional expansion is useful for agreement tests. We extend these results to more general families of subsets, beyond simplicial complexes. These include - Agreement tests for set systems whose sets are faces of high dimensional expanders. Our new tests apply to all dimensions of complexes both in case of two-sided expansion and in the case of one sided partite expansion. This improves and extends an earlier work of <PERSON><PERSON> and <PERSON> (FOCS 2017) and applies to matroids, and potentially many additional complexes. - Agreement tests for set systems whose sets are neighborhoods of vertices in a high dimensional expander. This family resembles the expander neighborhood family used in the gap-amplification proof of the PCP theorem. This set system is quite natural yet does not sit in a simplicial complex, and demonstrates some versatility in our proof technique. - Agreement tests on families of subspaces (also known as the Grassmann poset). This extends the classical low degree agreement tests beyond the setting of low degree polynomials. Our analysis relies on a new random walk on simplicial complexes which we call the \"complement random walk\" and which may be of independent interest. This random walk generalizes the non-lazy random walk on a graph to higher dimensions, and has significantly better expansion than previously-studied random walks on simplicial complexes.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00088"}, {"primary_key": "3008691", "vector": [], "sparse_vector": [], "title": "Laconic Conditional Disclosure of Secrets and Applications.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In a Conditional Disclosure of Secrets (CDS) a verifier V wants to reveal a message m to a prover P conditioned on the fact that x is an accepting instance of some NP-language L. An honest prover (holding the corresponding witness w) always obtains the message m at the end of the interaction. On the other hand, if x ∉ L we require that no PPT P* can learn the message m. We introduce laconic CDS, a two round CDS protocol with optimal computational cost for the verifier V and optimal communication cost. More specifically, the verifier's computation and overall communication grows with poly(|x|; λ; log(T)), where λ is the security parameter and T is the verification time for checking that x ϵ L (given w). We obtain constructions of laconic CDS under standard assumptions, such as CDH or LWE. Laconic CDS serves as a powerful tool for maliciousifying semi-honest protocols while preserving their computational and communication complexities. To substantiate this claim, we consider the setting of non-interactive secure computation: <PERSON> wants to publish a short digest corresponding to a private large input x on her web page such that (possibly many) <PERSON>, with a private input y, can send a short message to <PERSON> allowing her to learn C(x; y) (where C is a public circuit). The protocol must be reusable in the sense that <PERSON> can engage in arbitrarily many executions on the same digest. In this context we obtain the following new implications. 1) UC Secure Bob-optimized 2PC: We obtain a UC secure protocol where <PERSON>'s computational cost and the communication cost of the protocol grows with poly(|x|; |y|; λ; d), where d is the depth of the computed circuit C. 2) Malicious Laconic Function Evaluation: Next, we move on to the setting where Alice's input x is large. For this case, UC secure protocols must have communication cost growing with |x|. Thus, with the goal of achieving better efficiency, we consider a weaker notion of malicious security. For this setting, we obtain a protocol for which Bob's computational cost and the communication cost of the protocol grows with poly(|y|; λ; d), where d is the depth of the computed circuit C.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00046"}, {"primary_key": "3008692", "vector": [], "sparse_vector": [], "title": "Planar Graphs have Bounded Queue-Number.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We show that planar graphs have bounded queue-number, thus proving a conjecture of <PERSON>, <PERSON> and <PERSON> from 1992. The key to the proof is a new structural tool called layered partitions, and the result that every planar graph has a vertex-partition and a layering, such that each part has a bounded number of vertices in each layer, and the quotient graph has bounded treewidth. This result generalises for graphs of bounded Euler genus. Moreover, we prove that every graph in a minor-closed class has such a layered partition if and only if the class excludes some apex graph. Building on this work and using the graph minor structure theorem, we prove that every proper minor-closed class of graphs has bounded queue-number. Layered partitions can be interpreted in terms of strong products. We show that every planar graph is a subgraph of the strong product of a path with some graph of bounded treewidth. Similar statements hold for all proper minor-closed classes.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00056"}, {"primary_key": "3008693", "vector": [], "sparse_vector": [], "title": "Learning from Outcomes: Evidence-Based Rankings.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "G<PERSON> <PERSON>na"], "summary": "Many selection procedures involve ordering candidates according to their qualifications. For example, a university might order applicants according to a perceived probability of graduation within four years, and then select the top 1000 applicants. In this work, we address the problem of ranking members of a population according to their \"probability\" of success, based on a training set of historical binary outcome data (e.g., graduated in four years or not). We show how to obtain rankings that satisfy a number of desirable accuracy and fairness criteria, despite the coarseness of the training data. As the task of ranking is global (the rank of every individual depends not only on their own qualifications, but also on every other individuals' qualifications) ranking is more subtle and vulnerable to manipulation than standard prediction tasks. Towards mitigating unfair discrimination caused by inaccuracies in rankings, we develop two parallel definitions of evidence-based rankings. The first definition relies on a semantic notion of domination-compatibility: if the training data suggest that members of a set S are more qualified (on average) than the members of T, then a ranking that favors T over S (i.e. where T dominates S) is blatantly inconsistent with the evidence, and likely to be discriminatory. The definition asks for domination-compatibility, not just for a pair of sets, but rather for every pair of sets from a rich collection C of subpopulations. The second definition aims at precluding even more general forms of discrimination; this notion of evidence-consistency requires that the ranking must be justified on the basis of consistency with the expectations for every set in the collection C. Somewhat surprisingly, while evidence-consistency is a strictly stronger notion than domination-compatibility when the collection C is predefined, the two notions are equivalent when the collection C may depend on the ranking in question.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00016"}, {"primary_key": "3008694", "vector": [], "sparse_vector": [], "title": "Radio Network Coding Requires Logarithmic Overhead.", "authors": ["<PERSON><PERSON>", "Gillat Kol", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We consider the celebrated radio network model for abstracting communication in wireless networks. In this model, in any round, each node in the network may broadcast a message to all its neighbors. However, a node is able to hear a message broadcast by a neighbor only if no collision occurred, meaning that it was the only neighbor broadcasting. While the (noiseless) radio network model received a lot of attention over the last few decades, the effect of noise on radio networks is still not well understood. In this paper, we take a step forward and show that making radio network protocols resilient to noise may require a substantial performance overhead. Specifically, we construct a multi-hop network and a communication protocol over this network that works in T rounds when there is no noise. We prove that any scheme that simulates our protocol and is resilient to stochastic noise, requires at least cT log(n) rounds, for some constant c. This stands in contrast to our previous result (STOC, 2018), showing that protocols over the single-hop (clique) network can be made noise resilient with only a constant overhead. Our result also settles a recent conjecture by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>ic (2018). We complement the above result by giving a scheme to simulate any protocol with a fixed order of transmissions with only an O(log (n)) overhead.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00030"}, {"primary_key": "3008695", "vector": [], "sparse_vector": [], "title": "Polylogarithmic Guarantees for Generalized Reordering Buffer Management.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In the Generalized Reordering Buffer Management Problem (GRBM) a sequence of items located in a metric space arrives online, and has to be processed by a set of k servers moving within the space. In a single step the first b still unprocessed items from the sequence are accessible, and a scheduling strategy has to select an item and a server. Then the chosen item is processed by moving the chosen server to its location. The goal is to process all items while minimizing the total distance travelled by the servers. This problem was introduced in [<PERSON>, <PERSON>, Sit<PERSON>, van Stee TCS 12] and has been subsequently studied in an online setting by [<PERSON><PERSON>, <PERSON>, Gamzu, Kidron STACS 14]. The problem is a natural generalization of two very well-studied problems: the k-server problem for b=1 and the Reordering Buffer Management Problem (RBM) for k=1. In this paper we consider the GRBM problem on a uniform metric in the online version. We show how to obtain a competitive ratio of O(log k(log k+loglog b)) for this problem. Our result is a drastic improvement in the dependency on b compared to the previous best bound of O(√b log k), and is asymptotically optimal for constant k, because Ω(log k + loglog b) is a lower bound for GRBM on uniform metrics.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00012"}, {"primary_key": "3008696", "vector": [], "sparse_vector": [], "title": "Settling the Communication Complexity of Combinatorial Auctions with Two Subadditive Buyers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>-<PERSON>", "<PERSON><PERSON>"], "summary": "We study the communication complexity of welfare maximization in combinatorial auctions with m items and two players with subadditive valuations. We show that outperforming the trivial 1/2-approximation requires exponential communication, settling an open problem of <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [STOC'05, MOR'10] and <PERSON><PERSON> [STOC'06, SICOMP '09]. To derive our results, we introduce a new class of subadditive functions that are \"far from\" fractionally subadditive (XOS) functions, and establish randomized communication lower bounds for a new \"near-EQUALITY\" problem, both of which may be of independent interest.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00025"}, {"primary_key": "3008697", "vector": [], "sparse_vector": [], "title": "Parametric Shortest Paths in Planar Graphs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We construct a family of planar graphs {G n } n≥4 , where G_n has n vertices including a source vertex s, a sink vertex t, and edge weights that change linearly with a parameter λ such that, as λ varies in (-∞,+∞), the piece-wise linear cost of the shortest path from s to t has n Ω(logn) pieces. This shows that lower bounds obtained by <PERSON><PERSON><PERSON> (1983) and <PERSON><PERSON><PERSON><PERSON> (2001) for general graphs also hold for planar graphs, refuting a conjecture of <PERSON><PERSON><PERSON> (2009). <PERSON><PERSON> (1980) and <PERSON> (2009) showed that the number of pieces for every n-vertex graph with linear edge weights is n logn+O(1) . We generalize this result in two ways. (i) If the edge weights vary as a polynomial of degree at most d, then the number of pieces is n(logn+(α(n)+O(1)) d ) , where α(n) is the inverse Ackermann function. (ii) If the edge weights are linear forms of three parameters, then the number of pieces, appropriately defined for R 3 , is n((logn) 2 +O(logn)).", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00057"}, {"primary_key": "3008698", "vector": [], "sparse_vector": [], "title": "Polynomial Calculus Space and Resolution Width.", "authors": ["<PERSON>", "Leszek Aleksander <PERSON>", "<PERSON>"], "summary": "We show that if a k-CNF requires width w to refute in resolution, then it requires space square root of √ω to refute in polynomial calculus, where the space of a polynomial calculus refutation is the number of monomials that must be kept in memory when working through the proof. This is the first analogue, in polynomial calculus, of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s result lower-bounding clause space in resolution by resolution width. As a by-product of our new approach to space lower bounds we give a simple proof of <PERSON><PERSON><PERSON>'s recent result that total space in resolution (the total number of variable occurrences that must be kept in memory) is lower-bounded by the width squared. As corollaries of the main result we obtain some new lower bounds on the PCR space needed to refute specific formulas, as well as partial answers to some open problems about relations between space, size, and degree for polynomial calculus.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00081"}, {"primary_key": "3008699", "vector": [], "sparse_vector": [], "title": "Online Matching with General Arrivals.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The online matching problem was introduced by <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> nearly three decades ago. In that seminal work, they studied this problem in bipartite graphs with vertices arriving only on one side, and presented optimal deterministic and randomized algorithms for this setting. In comparison, more general arrival models, such as edge arrivals and general vertex arrivals, have proven more challenging and positive results are known only for various relaxations of the problem. In particular, even the basic question of whether randomization allows one to beat the trivially-optimal deterministic competitive ratio of 1/2 for either of these models was open. In this paper, we resolve this question for both these natural arrival models, and show the following.1) For edge arrivals, randomization does not help - no randomized algorithm is better than 1/2 competitive. 2)For general vertex arrivals, randomization helps - there exists a randomized (1/2+Ω(1)) -competitive online matching algorithm.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00011"}, {"primary_key": "3008700", "vector": [], "sparse_vector": [], "title": "Balancing Straight-Line Programs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We show that a context-free grammar of size m that produces a single string w of length n (such a grammar is also called a string straight-line program) can be transformed in linear time into a context-free grammar for w of size O(m), whose unique derivation tree has depth O(log n). This solves an open problem in the area of grammar-based compression, improves many results in this area and greatly simplifies many existing constructions. Similar results are stated for two formalisms for grammar-based tree compression: top dags and forest straight-line programs. These balancing results can be all deduced from a single meta theorem stating that the depth of an algebraic circuit over an algebra with a certain finite base property can be reduced to O(log n) with the cost of a constant multiplicative size increase. Here, n refers to the size of the unfolding (or unravelling) of the circuit. In particular, this results applies to standard arithmetic circuits over (non-commutative) semirings.A long version of the paper can be found in [1].", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00073"}, {"primary_key": "3008701", "vector": [], "sparse_vector": [], "title": "More Barriers for Rank Methods, via a &quot;numeric to Symbolic&quot; Transfer.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We prove new barrier results in arithmetic complexity theory, showing severe limitations of natural lifting (aka escalation) techniques.For example, we prove that even optimal rank lower bounds on k-tensors cannot yield non-trivial lower bounds on the rank of d-tensors, for any constant d > k.This significantly extends recent barrier results on the limits of (matrix) rank methods by [EGOW17], which handles the (very important) case k = 2.Our generalization requires the development of new technical tools and results in algebraic geometry, which are interesting in their own right and possibly applicable elsewhere.The basic issue they probe is the relation between numeric and symbolic rank of tensors, essential in the proofs of previous and current barriers.Our main technical result implies that for every symbolic k-tensor (namely one whose entries are polynomials in some set of variables), if the tensor rank is small for every evaluation of the variables, then it is small symbolically.This statement is obvious for k = 2.To prove an analogous statement for k > 2 we develop a \"numeric to symbolic\" transfer of algebraic relations to algebraic functions, somewhat in the spirit of the implicit function theorem.It applies in the general setting of inclusion of images of polynomial maps, in the form appearing in <PERSON><PERSON>'s elusive functions approach to proving VP = VNP.We give a toy application showing how our transfer theorem may be useful in pursuing this approach to prove arithmetic complexity lower bounds.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00054"}, {"primary_key": "3008702", "vector": [], "sparse_vector": [], "title": "Conditional Hardness Results for Massively Parallel Computation from Distributed Lower Bounds.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present the first conditional hardness results for massively parallel algorithms for some central graph problems including (approximating) maximum matching, vertex cover, maximal independent set, and coloring. In some cases, these hardness results match or get close to the state of the art algorithms. Our hardness results are conditioned on a widely believed conjecture in massively parallel computation about the complexity of the connectivity problem. We also note that it is known that an unconditional variant of such hardness results might be somewhat out of reach for now, as it would lead to considerably improved circuit complexity lower bounds and would concretely imply that NC 1 is a proper subset of P. We obtain our conditional hardness result via a general method that lifts unconditional lower bounds from the well-studied LOCAL model of distributed computing to the massively parallel computation setting.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00097"}, {"primary_key": "3008703", "vector": [], "sparse_vector": [], "title": "Computationally-Secure and Composable Remote State Preparation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We introduce a protocol between a classical polynomial-time verifier and a quantum polynomial-time prover that allows the verifier to securely delegate to the prover the preparation of certain single-qubit quantum states The prover is unaware of which state he received and moreover, the verifier can check with high confidence whether the preparation was successful. The delegated preparation of single-qubit states is an elementary building block in many quantum cryptographic protocols. We expect our implementation of \"random remote state preparation with verification\", a functionality first defined in (<PERSON><PERSON><PERSON><PERSON> and <PERSON> 2014), to be useful for removing the need for quantum communication in such protocols while keeping functionality. The main application that we detail is to a protocol for blind and verifiable delegated quantum computation (DQC) that builds on the work of (<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> 2018), who provided such a protocol with quantum communication. Recently, both blind an verifiable DQC were shown to be possible, under computational assumptions, with a classical polynomial-time client (<PERSON><PERSON><PERSON> 2017, <PERSON><PERSON><PERSON> 2018). Compared to the work of <PERSON><PERSON><PERSON>, our protocol is more modular, applies to the measurement-based model of computation (instead of the Hamiltonian model) and is composable. Our proof of security builds on ideas introduced in (<PERSON><PERSON><PERSON><PERSON> et al. 2018).", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00066"}, {"primary_key": "3008704", "vector": [], "sparse_vector": [], "title": "Sublinear Algorithms for Gap Edit Distance.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The edit distance is a way of quantifying how similar two strings are to one another by counting the minimum number of character insertions, deletions, and substitutions required to transform one string into the other. A simple dynamic programming computes the edit distance between two strings of length n in O(n2) time, and a more sophisticated algorithm runs in time O(n + t2) when the edit distance is t [<PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> 1998]. In pursuit of obtaining faster running time, the last couple of decades have seen a flurry of research on approximating edit distance, including polylogarithmic approximation in near-linear time [<PERSON><PERSON>, <PERSON> and <PERSON>, FOCS 2010], and a constant-factor approximation in subquadratic time [<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON>, FOCS 2018]. We study sublinear-time algorithms for small edit distance, which was investigated extensively because of its numerous applications. Our main result is an algorithm for distinguishing whether the edit distance is at most t or at least t^2 (the quadratic gap problem) in time Õ(n/t+t^3). This time bound is sublinear roughly for all t in [ω(1), o(n^1/3)], which was not known before. The best previous algorithms solve this problem in sublinear time only for t=ω(n^1/3) [<PERSON><PERSON> and <PERSON><PERSON>, STOC 2009]. Our algorithm is based on a new approach that adaptively switches between uniform sampling and reading contiguous blocks of the input strings. In contrast, all previous algorithms choose which coordinates to query non-adaptively. Moreover, it can be extended to solve the t vs t^2-ε gap problem in time Õ(n/t^1-ε+t^3).", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00070"}, {"primary_key": "3008705", "vector": [], "sparse_vector": [], "title": "Non-Malleable Commitments using Goldreich-Levin List Decoding.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We give the first construction of three-round non-malleable commitments from the almost minimal assumption of injective one-way functions. Combined with the lower bound of Pass (TCC 2013), our result is almost the best possible w.r.t. standard polynomial-time hardness assumptions (at least w.r.t. black-box reductions). Our results rely on a novel technique which we call 'bidirectional Goldreich-Levin extraction'. Along the way, we also obtain the first rewind secure delayed-input witness indistinguishable (WI) proofs from only injective one-way functions. We also obtain the first construction of an epsilon-extractable commitment scheme from injective one-way functions. We believe both of these to be of independent interest. In particular, as a direct corollary of our rewind secure WI construction, we are able to obtain a construction of 3-round promise zero-knowledge from only injective one-way functions.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00047"}, {"primary_key": "3008706", "vector": [], "sparse_vector": [], "title": "Perfect Zero Knowledge for Quantum Multiprover Interactive Proofs.", "authors": ["Alex <PERSON>l Grilo", "<PERSON>", "<PERSON>"], "summary": "In this work we consider the interplay between multiprover interactive proofs, quantum entanglement, and zero knowledge proofs - notions that are central pillars of complexity theory, quantum information and cryptography. In particular, we study the relationship between the complexity class MIP*, the set of languages decidable by multiprover interactive proofs with quantumly entangled provers, and the class PZK-MIP*, which is the set of languages decidable by MIP* protocols that furthermore possess the perfect zero knowledge property. Our main result is that the two classes are equal, i.e., MIP* = PZK-MIP*. This result provides a quantum analogue of the celebrated result of <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> (STOC 1988) who show that MIP = PZK-MIP (in other words, all classical multiprover interactive protocols can be made zero knowledge). We prove our result by showing that every MIP* protocol can be efficiently transformed into an equivalent zero knowledge MIP* protocol in a manner that preserves the completeness-soundness gap. Combining our transformation with previous results, we obtain the corollaries that i) all languages that can be solved in non-deterministic double exponential time have zero knowledge MIP* protocols and ii) all co-recursively enumerable languages (which include undecidable problems as well as all decidable problems) have zero knowledge MIP* protocols with vanishing promise gap.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00044"}, {"primary_key": "3008707", "vector": [], "sparse_vector": [], "title": "Derandomization from Algebraic Hardness: Treading the Borders.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A hitting-set generator (HSG) is a polynomial map Gen:F k → F n such that for all n-variate polynomials Q of small enough circuit size and degree, if Q is non-zero, then Q o Gen is non-zero. In this paper, we give a new construction of such a HSG assuming that we have an explicit polynomial of sufficient hardness in the sense of approximative or border complexity. Formally, we prove the following result over any characteristic zero field F: Suppose P(z 1 ,..., z k ) is an explicit k-variate degree d polynomial that is not in the border of circuits of size s. Then, there is an explicit hitting-set generator Gen(P): F 2k → F n such that every non-zero n-variate degree D polynomial Q(x) in the border of size s' circuits satisfies Q ≠ 0 ⇒ Q o Gen(P) ≠ 0 provided n 10k d Ds' 0 be a constant and k be a large enough constant. Suppose, for every s ≥ k, there is an explicit hitting set of size s k-δ for all degree s polynomials in the border of k-variate size s algebraic circuits. Then, there is an explicit hitting set of size poly(s) for the border s-variate algebraic circuits of size s and degree s. Unlike the prior constructions of such maps (e.g.[NW94], [KI04], [AGS19], [KST19]), our construction is purely algebraic and does not rely on the notion of combinatorial designs.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00018"}, {"primary_key": "3008708", "vector": [], "sparse_vector": [], "title": "Optimal Document Exchange and New Codes for Insertions and Deletions.", "authors": ["<PERSON>"], "summary": "We give the first communication-optimal document exchange protocol. For any n and k ε , produces a summary of size O(klog 2 k + k log n), and succeeds with probability 1-(k log n) - O(1 ) . We also give an efficient derandomized document exchange protocol with summary size O(k log 2 n/k). This improves, for any k, over a deterministic document exchange protocol by Belazzougui with summary size O(k 2 + k log 2 n). Our deterministic document exchange directly provides new efficient systematic error correcting codes for insertions and deletions. These (binary) codes correct any δ fraction of adversarial insertions/deletions while having a rate of 1 - O(δ log 2 1/δ) and improve over the codes of Guru<PERSON>wami and Li and Haeupler, Shahrasbi and Vitercik which have rate 1 - Θ (√δ log O(1) 1/ε).", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00029"}, {"primary_key": "3008709", "vector": [], "sparse_vector": [], "title": "Distributed Local Approximation Algorithms for Maximum Matching in Graphs and Hypergraphs.", "authors": ["<PERSON>"], "summary": "We describe approximation algorithms in <PERSON><PERSON>'s classic LOCAL model of distributed computing to find maximum-weight matchings in a hypergraph of rank r. Our main result is a deterministic algorithm to generate a matching which is an O(r)-approximation to the maximum weight matching, running in Õ(r log Δ + log 2 Δ + log* n) rounds. (Here, the Õ() notations hides polyloglog Δ and polylog r factors). This is based on a number of new derandomization techniques extending methods of <PERSON><PERSON><PERSON><PERSON>, <PERSON> & <PERSON> (2017). The first main application is to nearly-optimal algorithms for the long-studied problem of maximum-weight graph matching. Specifically, we get a (1+ε) approximation algorithm using Õ(log Δ/ε 3 + polylog(1/ε, log log n)) randomized time and Õ(log 2 Δ/ε 4 + log*n/ε) deterministic time. The second application is a faster algorithm for hypergraph maximal matching, a versatile subroutine introduced in <PERSON><PERSON> et al. (2017) for a variety of local graph algorithms. This gives an algorithm for (2Δ - 1) -edge-list coloring in Õ(log 2 Δ log n) rounds deterministically or Õ((log log n) 3 ) rounds randomly. Another consequence (with additional optimizations) is an algorithm which generates an edge-orientation with out-degree at most ⌈(1+ε)λ⌉ for a graph of arboricity λ; for fixed ε this runs in Õ(log 6 n) rounds deterministically or Õ(log 3 n ) rounds randomly.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00048"}, {"primary_key": "3008710", "vector": [], "sparse_vector": [], "title": "Random k-out Subgraph Leaves only O(n/k) Inter-Component Edges.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Each vertex of an arbitrary simple graph on n vertices chooses k random incident edges. What is the expected number of edges in the original graph that connect different connected components of the sampled subgraph? We prove that the answer is O(n/k), when k ≥ c log n, for some large enough c. We conjecture that the same holds for smaller values of k, possibly for any k ≥ 2. Such a result is best possible for any k ≥ 2. As an application, we use this sampling result to obtain a one-way communication protocol with private randomness for finding a spanning forest of a graph in which each vertex sends only O (√n log n) bits to a referee.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00058"}, {"primary_key": "3008711", "vector": [], "sparse_vector": [], "title": "Adversarial Bandits with Knapsacks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We consider Bandits with Knapsacks (henceforth, BwK), a general model for multi-armed bandits under supply/budget constraints. In particular, a bandit algorithm needs to solve a well-known knapsack problem: find an optimal packing of items into a limited-size knapsack. The BwK problem is a common generalization of numerous motivating examples, which range from dynamic pricing to repeated auctions to dynamic ad allocation to network routing and scheduling. While the prior work on BwK focused on the stochastic version, we pioneer the other extreme in which the outcomes can be chosen adversarially. This is a considerably harder problem, compared to both the stochastic version and the \"classic\" adversarial bandits, in that regret minimization is no longer feasible. Instead, the objective is to minimize the competitive ratio: the ratio of the benchmark reward to algorithm's reward. We design an algorithm with competitive ratio O(log T) relative to the best fixed distribution over actions, where T is the time horizon; we also prove a matching lower bound. The key conceptual contribution is a new perspective on the stochastic version of the problem. We suggest a new algorithm for the stochastic version, which builds on the framework of regret minimization in repeated games and admits a substantially simpler analysis compared to prior work. We then analyze this algorithm for the adversarial version, and use it as a subroutine to solve the latter. Our algorithm is the first \"black-box reduction\" from bandits to BwK: it takes an arbitrary bandit algorithm and uses it as a subroutine. We use this reduction to derive several extensions.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00022"}, {"primary_key": "3008712", "vector": [], "sparse_vector": [], "title": "The Role of Interactivity in Local Differential Privacy.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the power of interactivity in local differential privacy. First, we focus on the difference between fully interactive and sequentially interactive protocols. Sequentially interactive protocols may query users adaptively in sequence, but they cannot return to previously queried users. The vast majority of existing lower bounds for local differential privacy apply only to sequentially interactive protocols, and before this paper it was not known whether fully interactive protocols were more powerful. We resolve this question. First, we classify locally private protocols by their compositionality, the multiplicative factor by which the sum of a protocol's single-round privacy parameters exceeds its overall privacy guarantee. We then show how to efficiently transform any fully interactive compositional protocol into an equivalent sequentially interactive protocol with a blowup in sample complexity linear in this compositionality. Next, we show that our reduction is tight by exhibiting a family of problems such that any sequentially interactive protocol requires this blowup in sample complexity over a fully interactive compositional protocol. We then turn our attention to hypothesis testing problems. We show that for a large class of compound hypothesis testing problems - which include all simple hypothesis testing problems as a special case - a simple noninteractive test is optimal among the class of all (possibly fully interactive) tests.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00015"}, {"primary_key": "3008713", "vector": [], "sparse_vector": [], "title": "Efficient Truncated Statistics with Unknown Truncation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Manolis Zampetakis"], "summary": "We study the problem of estimating the parameters of a Gaussian distribution when samples are only shown if they fall in some (unknown) set. This core problem in truncated statistics has long history going back to <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON>. Recent work by <PERSON><PERSON><PERSON><PERSON> et al. (FOCS'18), provides the first efficient algorithm that works for arbitrary sets in high dimension when the set is known, but leaves as an open problem the more challenging and relevant case of unknown truncation set. Our main result is a computationally and sample efficient algorithm for estimating the parameters of the Gaussian under arbitrary unknown truncation sets whose performance decays with a natural measure of complexity of the set, namely its Gaussian surface area. Notably, this algorithm works for large families of sets including intersections of halfspaces, polynomial threshold functions and general convex sets. We show that our algorithm closely captures the tradeoff between the complexity of the set and the number of samples needed to learn the parameters by exhibiting a set with small Gaussian surface area for which it is information theoretically impossible to learn the true Gaussian with few samples.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00093"}, {"primary_key": "3008714", "vector": [], "sparse_vector": [], "title": "Approximation Schemes for a Unit-Demand Buyer with Independent Items via Symmetries.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We consider a revenue-maximizing seller with n items facing a single buyer. We introduce the notion of symmetric menu complexity of a mechanism, which counts the number of distinct options the buyer may purchase, up to permutations of the items. Our main result is that a mechanism of quasi-polynomial symmetric menu complexity suffices to guarantee a (1 - epsilon )-approximation when the buyer is unit-demand over independent items, even when the value distribution is unbounded, and that this mechanism can be found in quasi-polynomial time. Our key technical result is a polynomial-time, (symmetric) menu-complexity-preserving black-box reduction from achieving a (1 - epsilon )-approximation for unbounded valuations that are subadditive over independent items to achieving a (1 - O(epsilon ))-approximation when the values are bounded (and still subadditive over independent items). We further apply this reduction to deduce approximation schemes for a suite of valuation classes beyond our main result. Finally, we show that selling separately (which has exponential menu complexity) can be approximated up to a (1 - epsilon ) factor with a menu of efficient-linear (f (epsilon) · n) symmetric menu complexity.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00023"}, {"primary_key": "3008715", "vector": [], "sparse_vector": [], "title": "The Complexity of 3-Colouring H-Colourable Graphs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the complexity of approximation on satisfiable instances for graph homomorphism problems. For a fixed graph H, the H-colouring problem is to decide whether a given graph has a homomorphism to H. By a result of <PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, this problem is NP-hard for any non-bipartite graph H. In the context of promise constraint satisfaction problems, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> conjectured that this hardness result extends to promise graph homomorphism as follows: fix any non-bipartite graph H and another graph G with a homomorphism from H to G, it is NP-hard to find a homomorphism to G from a given H-colourable graph. Arguably, the two most important special cases of this conjecture are when H is fixed to be the complete graph on 3 vertices (and G is any graph with a triangle) and when G is the complete graph on 3 vertices (and H is any 3-colourable graph). The former case is equivalent to the notoriously difficult approximate graph colouring problem. In this paper, we confirm the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> conjecture for the latter case. Our proofs rely on a novel combination of the universal-algebraic approach to promise constraint satisfaction, that was recently developed by <PERSON><PERSON>, <PERSON><PERSON> and the authors, with some ideas from algebraic topology.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00076"}, {"primary_key": "3008716", "vector": [], "sparse_vector": [], "title": "Spectral Analysis of Matrix Scaling and Operator Scaling.", "authors": ["Tsz Chiu Kwok", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a spectral analysis of a continuous scaling algorithm for matrix scaling and operator scaling. The main result is that if the input matrix or operator has a spectral gap, then a natural gradient flow has linear convergence. This implies that a simple gradient descent algorithm also has linear convergence under the same assumption. The spectral gap condition for operator scaling is closely related to the notion of quantum expander studied in quantum information theory. The spectral analysis also provides bounds on some important quantities of the scaling problems, such as the condition number of the scaling solution and the capacity of the matrix and operator. These results can be used in various applications of scaling problems, including matrix scaling on expander graphs, permanent lower bounds on random matrices, the <PERSON><PERSON> problem on random frames, and Brascamp-Lieb constants on random operators. In some applications, the inputs of interest satisfy the spectral condition and we prove significantly stronger bounds than the worst case bounds.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00074"}, {"primary_key": "3008717", "vector": [], "sparse_vector": [], "title": "Truly Optimal Euclidean Spanners.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Euclidean spanners are important geometric structures, having found numerous applications over the years. Cornerstone results in this area from the late 80s and early 90s state that for any d-dimensional n-point Euclidean space, there exists a (1+ε) -spanner with O(nε^-d+1) edges and lightness (normalized weight) O(ε^-2d)^1. Surprisingly, the fundamental question of whether or not these dependencies on ε and d for small d can be improved has remained elusive, even for d = 2. This question naturally arises in any application of Euclidean spanners where precision is a necessity (thus ε is tiny). In the most extreme case ε is inverse polynomial in n, and then one could potentially improve the size and lightness bounds by factors that are polynomial in n. The state-of-the-art bounds O(nε^-d+1) and O(ε^-2d) on the size and lightness of spanners are realized by the greedy spanner. In 2016, <PERSON><PERSON><PERSON> and <PERSON> [25] proved that, in low dimensional spaces, the greedy spanner is \"near-optimal''; informally, their result states that the greedy spanner for dimension d is just as sparse and light as any other spanner but for dimension larger by a constant factor. Hence the question of whether the greedy spanner is truly optimal remained open to date. The contribution of this paper is two-fold. 1) We resolve these longstanding questions by nailing down the exact dependencies on ε and d and showing that the greedy spanner is truly optimal. Specifically, for any d= O(1), ε = Ω(n^-1/d-1): • We show that any (1+ε) -spanner must have Ω(nε^-d+1) edges, implying that the greedy (and other) spanners achieve the optimal size. • We show that any (1+ε) -spanner must have lightness Ω(ε^-d), and then improve the upper bound on the lightness of the greedy spanner from O(ε^-2d) to Õ_ε (ε^-d). 2) We then complement our negative result for the size of spanners with a rather counterintuitive positive result: Steiner points lead to a quadratic improvement in the size of spanners! Our bound for the size of Steiner spanners is tight as well (up to lower-order terms).", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00069"}, {"primary_key": "3008718", "vector": [], "sparse_vector": [], "title": "Faster Minimum k-cut of a Simple Graph.", "authors": ["<PERSON>"], "summary": "We consider the (exact, minimum) k-CUT problem: given a graph and an integer k, delete a minimum-weight set of edges so that the remaining graph has at least k connected components. This problem is a natural generalization of the global minimum cut problem, where the goal is to break the graph into k=2 pieces. Our main result is a (combinatorial) k-CUT algorithm on simple graphs that runs in n (1+o(1))k time for any constant k, improving upon the previously best n (2ω/3+o(1))k time algorithm of <PERSON> et al. [FOCS'18] and the previously best n^ (1.981+o(1))k time combinatorial algorithm of <PERSON> et al. [STOC'19]. For combinatorial algorithms, this algorithm is optimal up to o(1) factors assuming recent hardness conjectures: we show by a straightforward reduction that k-CUT on even a simple graph is as hard as (k-1)-clique, establishing a lower bound of n (1-o(1))k for k-CUT. This settles, up to lower-order factors, the complexity of k-CUT on a simple graph for combinatorial algorithms.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00068"}, {"primary_key": "3008719", "vector": [], "sparse_vector": [], "title": "Noise Sensitivity on the p -Biased Hypercube.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The noise sensitivity of a Boolean function measures how susceptible the value of f on a typical input x to a slight perturbation of the bits of x: it is the probability f(x) and f(y) are different when x is a uniformly chosen n-bit Boolean string, and y is formed by flipping each bit of x with small probability ε. The noise sensitivity of a function is a key concept with applications to combinatorics, complexity theory, learning theory, percolation theory and more. In this paper, we investigate noise sensitivity on the p-biased hypercube, extending the theory for polynomially small p. Specifically, we give sufficient conditions for monotone functions with large groups of symmetries to be noise sensitive (which in some cases are also necessary). As an application, we show that the 2-SAT function is noise sensitive around its critical probability. En route, we study biased versions of the invariance principle for monotone functions and give p-biased versions of <PERSON><PERSON><PERSON>'s tail theorem and the Majority is Stablest theorem, showing that in this case the correct analog of ``small low degree influences'' is lack of correlation with constant width DNF formulas.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00075"}, {"primary_key": "3008720", "vector": [], "sparse_vector": [], "title": "Parallel Reachability in Almost Linear Work and Square Root Depth.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper we provide a parallel algorithm that given any n-node m-edge directed graph and source vertex s computes all vertices reachable from s with Õ(m) work and n {1/2 + o(1) } depth with high probability in n. This algorithm also computes a set of Õ(n) edges which when added to the graph preserves reachability and ensures that the diameter of the resulting graph is at most n {1/2 + o(1) }. Our result improves upon the previous best known almost linear work reachability algorithm due to <PERSON><PERSON> [1] which had depth Õ(n 2/3 ). Further, we show how to leverage this algorithm to achieve improved distributed algorithms for single source reachability in the CONGEST model. In particular, we provide a distributed algorithm that given a n-node digraph of undirected hop-diameter D solves the single source reachability problem with Õ(n 1/2 + n 1/3+o(1) D 2/3 ) rounds of the communication in the CONGEST model with high probability in n. Our algorithm is nearly optimal whenever D = O(n 1/4-ε ) for any constant ε > 0 and is the first nearly optimal algorithm for general graphs whose diameter is Ω(n δ ) for any constant δ.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00098"}, {"primary_key": "3008721", "vector": [], "sparse_vector": [], "title": "Faster <PERSON><PERSON>ope Rounding, Sampling, and Volume Computation via a Sub-Linear Ball Walk.", "authors": ["<PERSON><PERSON>", "Nisheeth K. <PERSON>"], "summary": "This paper studies the problem of \"isotropically rounding\" a polytope K ⊆ R^n, that is, computing a linear transformation which makes the uniform distribution on the polytope have roughly identity covariance matrix. It is assumed that K ⊆ R^n is defined by m linear inequalities. We introduce a new variant of the ball walk Markov chain and show that, roughly, the expected number of arithmetic operations per-step of this Markov chain is O(m) that is sub-linear in the input size mn -- the per-step time of all prior Markov chains. Subsequently, we apply this new variant of the ball walk to obtain a rounding algorithm that gives a factor of √n improvement on the number of arithmetic operations over the previous bound which uses the hit-and-run algorithm. Since the cost of the rounding pre-processing step is in many cases the bottleneck in improving sampling or volume computation running time bounds, our results imply improved bounds for these tasks. Our algorithm achieves this improvement by a novel method of computing polytope membership, where one avoids checking inequalities which are estimated to have a very low probability of being violated. We believe that this method is likely to be of independent interest for constrained sampling and optimization problems.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00082"}, {"primary_key": "3008722", "vector": [], "sparse_vector": [], "title": "Optimization of the Sherrington-Kirkpatrick Hamiltonian.", "authors": ["<PERSON>"], "summary": "Let A be a symmetric random matrix with independent and identically distributed Gaussian entries above the diagonal. We consider the problem of maximizing the quadratic form associated to A over binary vectors. In the language of statistical physics, this amounts to finding the ground state of the <PERSON><PERSON><PERSON><PERSON> model of spin glasses. The asymptotic value of this optimization problem was characterized by <PERSON><PERSON> via a celebrated variational principle, subsequently proved by <PERSON><PERSON><PERSON>. We give an algorithm that, for any ε > 0, outputs a feasible solution whose value is at least (1 - ε) of the optimum, with probability converging to one as the dimension n of the matrix diverges. The algorithm's time complexity is of order n 2 . It is a message-passing algorithm, but the specific structure of its update rules is new. As a side result, we prove that, at (low) non-zero temperature, the algorithm constructs approximate solutions of the Thou<PERSON>-<PERSON><PERSON> equations.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00087"}, {"primary_key": "3008723", "vector": [], "sparse_vector": [], "title": "(Nearly) Sample-Optimal Sparse Fourier Transform in Any Dimension; RIPless and Filterless.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we consider the extensively studied problem of computing a k-sparse approximation to the d-dimensional Fourier transform of a length n signal. Our algorithm uses O(k log k log n) samples, is dimension-free, operates for any universe size, and achieves the strongest ℓ ∞ /ℓ 2 guarantee, while running in a time comparable to the Fast Fourier Transform. In contrast to previous algorithms which proceed either via the Restricted Isometry Property or via filter functions, our approach offers a fresh perspective to the sparse Fourier Transform problem.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00092"}, {"primary_key": "3008724", "vector": [], "sparse_vector": [], "title": "NEEXP is Contained in MIP.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We study multiprover interactive proof systems. The power of classical multiprover interactive proof systems, in which the provers do not share entanglement, was characterized in a famous work by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> (Computational Complexity 1991), whose main result was the equality MIP = NEXP. The power of quantum multiprover interactive proof systems, in which the provers are allowed to share entanglement, has proven to be much more difficult to characterize. The best known lower-bound on MIP* is NEXP ⊆ MIP* due to <PERSON><PERSON> and <PERSON><PERSON><PERSON> (FOCS 2012). As for upper bounds, MIP* could be as large as RE, the class of recursively enumerable languages. The main result of this work is the inclusion NEEXP = NTIME[2 2poly(n) ] ⊆ MIP*. This is an exponential improvement over the prior lower bound and shows that proof systems with entangled provers are at least exponentially more powerful than classical provers. In our protocol the verifier delegates a classical, exponentially large MIP protocol for NEEXP to two entangled provers: the provers obtain their exponentially large questions by measuring their shared state, and use a classical PCP to certify the correctness of their exponentially-long answers. For the soundness of our protocol, it is crucial that each player should not only sample its own question correctly but also avoid performing measurements that would reveal the other player's sampled question. We ensure this by commanding the players to perform a complementary measurement, relying on the Heisenberg uncertainty principle to prevent the forbidden measurements from being performed.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00039"}, {"primary_key": "3008725", "vector": [], "sparse_vector": [], "title": "How to Use Heuristics for Differential Privacy.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We develop theory for using heuristics to solve computationally hard problems in differential privacy. Heuristic approaches have enjoyed tremendous success in machine learning, for which performance can be empirically evaluated. However, privacy guarantees cannot be evaluated empirically, and must be proven --- without making heuristic assumptions. We show that learning problems over broad classes of functions --- those that have polynomially sized universal identification sets --- can be solved privately and efficiently, assuming the existence of a non-private oracle for solving the same problem. Our first algorithm yields a privacy guarantee that is contingent on the correctness of the oracle. We then give a reduction which applies to a class of heuristics which we call certifiable, which allows us to convert oracle-dependent privacy guarantees to worst-case privacy guarantee that hold even when the heuristic standing in for the oracle might fail in adversarial ways. Finally, we consider classes of functions for which both they and their dual classes have small universal identification sets. This includes most classes of simple boolean functions studied in the PAC learning literature, including conjunctions, disjunctions, parities, and discrete halfspaces. We show that there is an efficient algorithm for privately constructing synthetic data for any such class, given a non-private learning oracle. This in particular gives the first oracle-efficient algorithm for privately generating synthetic data for contingency tables. The most intriguing question left open by our work is whether or not every problem that can be solved differentially privately can be privately solved with an oracle-efficient algorithm. While we do not resolve this, we give a barrier result that suggests that any generic oracle-efficient reduction must fall outside of a natural class of algorithms (which includes the algorithms given in this paper).", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00014"}, {"primary_key": "3008726", "vector": [], "sparse_vector": [], "title": "Why are Proof Complexity Lower Bounds Hard?", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We formalize and study the question of whether there are inherent difficulties to showing lower bounds on propositional proof complexity. We establish the following unconditional result: Propositional proof systems cannot efficiently show that truth tables of random Boolean functions lack polynomial size non-uniform proofs of hardness. Assuming a conjecture of <PERSON><PERSON><PERSON>, propositional proof systems also cannot efficiently show that random k-CNFs of linear density lack polynomial size non-uniform proofs of unsatisfiability. Since the statements in question assert the average-case hardness of standard NP problems (MCSP and 3-SAT respectively) against co-nondeterministic circuits for natural distributions, one interpretation of our result is that propositional proof systems are inherently incapable of efficiently proving strong complexity lower bounds in our formalization. Another interpretation is that an analogue of the Razborov-Rudich `natural proofs' barrier holds in proof complexity: under reasonable hardness assumptions, there are natural distributions on hard tautologies for which it is infeasible to show proof complexity lower bounds for strong enough proof systems. For the specific case of the Extended Frege (EF) propositional proof system, we show that at least one of the following cases holds: (1) EF has no efficient proofs of superpolynomial circuit lower bound tautologies for any Boolean function or (2) There is an explicit family of tautologies of each length such that under reasonable hardness assumptions, most tautologies are hard but no propositional proof system can efficiently establish hardness for most tautologies in the family. Thus, under reasonable hardness assumptions, either the Circuit Lower Bounds program toward complexity separations cannot be implemented in EF, or there are inherent obstacles to implementing the Cook-Reckhow program for EF.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00080"}, {"primary_key": "3008727", "vector": [], "sparse_vector": [], "title": "Linear-Time and Efficient Distributed Algorithms for List Coloring Graphs on Surfaces.", "authors": ["<PERSON>"], "summary": "In 1994, <PERSON><PERSON> proved that every planar graph is 5-list-colorable. In 1995, <PERSON><PERSON> proved that every planar graph of girth at least five is 3-list-colorable. His proofs naturally lead to quadratic-time algorithms to find such colorings. Here, we provide the first linear-time algorithms to find such colorings. For a fixed surface S, <PERSON><PERSON> showed in 1997 that there exists a linear-time algorithm to decide if a graph embedded in S is 5-colorable and similarly in 2003 if a graph of girth at least five embedded in S is 3-colorable. Using the theory of hyperbolic families, the author and <PERSON> showed such algorithms exist for list-colorings. Around the same time, <PERSON><PERSON><PERSON> and <PERSON><PERSON> also provided such algorithms. Moreover, they gave an algorithm to find such colorings (if they exist). Here we provide the first such algorithm which is fixed parameter tractable with genus as the parameter; indeed, we provide a linear-time algorithm to find such colorings. In 1988, <PERSON>, <PERSON> and <PERSON> provided a deterministic distributed algorithm for 7-coloring n-vertex planar graphs in O(log n) rounds. In 2018, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> provided a deterministic distributed algorithm for 6-coloring n-vertex planar graphs in polylogarithmic rounds. Their algorithm in fact works for 6-list-coloring. They also provided a polylogarithmic algorithm for 4-list-coloring triangle-free planar graphs. <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> independently obtained such algorithms for ordinary coloring in O(log n) rounds, which is best possible in terms of running time. Here we provide the first polylogarithmic deterministic distributed algorithms for 5-coloring n-vertex planar graphs and similarly for 3-coloring planar graphs of girth at least five. Indeed, these algorithms run in O(log n) rounds, work also for list-colorings, and even work on a fixed surface (assuming such a coloring exists).", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00060"}, {"primary_key": "3008728", "vector": [], "sparse_vector": [], "title": "Waring Rank, Parameterized and Exact Algorithms.", "authors": ["<PERSON>"], "summary": "We show that the Waring rank (symmetric tensor rank) of a certain family of polynomials has intimate connections to the areas of parameterized and exact algorithms, generalizing some well-known methods and providing a concrete approach to obtain faster approximate counting and deterministic decision algorithms. To illustrate the amenability and utility of this approach, we give an algorithm for approximately counting subgraphs of bounded treewidth, improving on earlier work of <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Along the way we give an exact answer to an open problem of <PERSON><PERSON><PERSON> and <PERSON> and sharpen a lower bound on the size of perfectly balanced hash families given by <PERSON><PERSON> and <PERSON><PERSON><PERSON>.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00053"}, {"primary_key": "3008729", "vector": [], "sparse_vector": [], "title": "Approximation Algorithms for LCS and LIS with Truly Improved Running Times.", "authors": ["<PERSON>via<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Longest common subsequence (LCS) is a classic and central problem in combinatorial optimization. While LCS admits a quadratic time solution, recent evidence suggests that solving the problem may be impossible in truly subquadratic time. A special case of LCS wherein each character appears at most once in every string is equivalent to the longest increasing subsequence problem (LIS) which can be solved in quasilinear time. In this work, we present novel algorithms for approximating LCS in truly subquadratic time and LIS in truly sublinear time. Our approximation factors depend on the ratio of the optimal solution size over the input size. We denote this ratio by λ and obtain the following results for LCS and LIS without any prior knowledge of λ. • A truly subquadratic time algorithm for LCS with approximation factor O(λ^3). • A truly sublinear time algorithm for LIS with approximation factor O(λ^3). Triangle inequality was recently used by <PERSON><PERSON><PERSON><PERSON> et al. [1] and <PERSON><PERSON><PERSON><PERSON> et al.[2] to present new approximation algorithms for edit distance. Our techniques for LCS extend the notion of triangle inequality to non-metric settings.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00071"}, {"primary_key": "3008730", "vector": [], "sparse_vector": [], "title": "Near-Linear Time Approximations Schemes for Clustering in Doubling Metrics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider the classic Facility Location, k-Median, and k-Means problems in metric spaces of constant doubling dimension. We give the first nearly linear-time approximation schemes for each problem, making a significant improvement over the state-of-the-art algorithms. Moreover, we show how to extend the techniques used to get the first efficient approximation schemes for the problems of prize-collecting k-Medians and k-Means, and efficient bicriteria approximation schemes for k-Medians with outliers, k-Means with outliers and k-Center.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00041"}, {"primary_key": "3008731", "vector": [], "sparse_vector": [], "title": "Quasilinear Time List-Decodable Codes for Space Bounded Channels.", "authors": ["<PERSON><PERSON>", "Swas<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider codes for space bounded channels. This is a model for communication under noise that was studied by <PERSON><PERSON><PERSON> and <PERSON> (<PERSON><PERSON> 2016) and lies between the Shannon (random) and <PERSON><PERSON> (adversarial) models. In this model, a channel is a space bounded procedure that reads the codeword in one pass, and modifies at most a p fraction of the bits of the codeword. <PERSON><PERSON><PERSON> and <PERSON>, and later work by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (RANDOM 2016), gave constructions of listdecodable codes with rate approaching 1 - H(p) against channels with space s = clog n, with encoding/decoding time poly(2 s ) = poly(n c ). In this paper we show that for every constant 0 0, there are codes with rate R ≥ 1 - H(p) - ε, list size poly(1/ε), and furthermore: . Our codes can handle channels with space s = n Ω(1) , which is much larger than O(log n) achieved by previous work. . We give encoding and decoding algorithms that run in time n · polylog(n). Previous work achieved large and unspecified poly(n) time (even for space s = 1 · log n channels). . We can handle space bounded channels that read the codeword in any order, whereas previous work considered channels that read the codeword in the standard order. Our construction builds on the machinery of <PERSON><PERSON><PERSON> and <PERSON> (with some key modifications) replacing some nonconstructive codes and pseudorandom objects (that are found in exponential time by brute force) with efficient explicit constructions. For this purpose we exploit recent results of <PERSON><PERSON><PERSON>, <PERSON> and <PERSON> (SICOMP 2018) on pseudorandom properties of \"t-wise independence + low weight noise\" which we quantitatively improve using techniques by <PERSON> and <PERSON> (FOCS 2018). To make use of such distributions, we give new explicit constructions of binary linear codes that have dual distance of n Ω(1) , and are also polynomial time list-decodable from relative distance á1/2-ε, with list size poly(1/ε). To the best of our knowledge, no such construction was previously known. Somewhat surprisingly, we show that Reed-Solomon codes with dimension k <; √n, have this property if interpreted as binary codes (in some specific interpretation)which we term: \"Raw Reed-Solomon Codes\". A key idea is viewing Reed-Solomon codes as \"bundles\" of certain dualBCH codewords.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00028"}, {"primary_key": "3008732", "vector": [], "sparse_vector": [], "title": "Exponential Separation between Quantum Communication and Logarithm of Approximate Rank.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON> (CMS19) recently exhibited a total Boolean function, the sink function, that has polynomial approximate rank and polynomial randomized communication complexity. This gives an exponential separation between randomized communication complexity and logarithm of the approximate rank, refuting the log-approximate-rank conjecture. We show that even the quantum communication complexity of the sink function is polynomial, thus also refuting the quantum log-approximate-rank conjecture. Our lower bound is based on the fooling distribution method introduced by <PERSON> and <PERSON> (Theory Comput., 2018) for the classical case and extended by <PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON> (STOC, 2017) for the quantum case. We also give a new proof of the classical lower bound using the fooling distribution method.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00062"}, {"primary_key": "3008733", "vector": [], "sparse_vector": [], "title": "SETH-Hardness of Coding Problems.", "authors": ["<PERSON>-<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We show that assuming the strong exponential-time hypothesis (SETH), there are no non-trivial algorithms for the nearest codeword problem (NCP), the minimum distance problem (MDP), or the nearest codeword problem with preprocessing (NCPP) on linear codes over any finite field. More precisely, we show that there are no NCP, MDP, or NCPP algorithms running in time q (1-ε)n for any constant ε > 0 for codes with q n codewords. (In the case of NCPP, we assume non-uniform SETH.) We also show that there are no sub-exponential time algorithms for y-approximate versions of these problems for some constant -y > 1, under different versions of the exponential-time hypothesis.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00027"}, {"primary_key": "3008734", "vector": [], "sparse_vector": [], "title": "Collaborative Learning with Limited Interaction: Tight Bounds for Distributed Exploration in Multi-armed Bandits.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Best arm identification (or, pure exploration) in multi-armed bandits is a fundamental problem in machine learning. In this paper we study the distributed version of this problem where we have multiple agents, and they want to learn the best arm collaboratively. We want to quantify the power of collaboration under limited interaction (or, communication steps), as interaction is expensive in many settings. We measure the running time of a distributed algorithm as the speedup over the best centralized algorithm where there is only one agent. We give almost tight round-speedup tradeoffs for this problem, along which we develop several new techniques for proving lower bounds on the number of communication steps under time or confidence constraints.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00017"}, {"primary_key": "3008735", "vector": [], "sparse_vector": [], "title": "Fast Generalized DFTs for all Finite Groups.", "authors": ["<PERSON>"], "summary": "For any finite group G, we give an arithmetic algorithm to compute generalized Discrete Fourier Transforms (DFTs) with respect to G, using O(|G| ω/2+ε ) operations, for any ε > 0. Here, ω is the exponent of matrix multiplication.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.00052"}, {"primary_key": "3008736", "vector": [], "sparse_vector": [], "title": "The Kikuchi Hierarchy and Tensor PCA.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "For the tensor PCA (principal component analysis) problem, we propose a new hierarchy of increasingly powerful algorithms with increasing runtime. Our hierarchy is analogous to the sum-of-squares (SOS) hierarchy but is instead inspired by statistical physics and related algorithms such as belief propagation and AMP (approximate message passing). Our level-t algorithm can be thought of as a linearized message-passing algorithm that keeps track of t-wise dependencies among the hidden variables. Specifically, our algorithms are spectral methods based on the Kikuchi Hessian, which generalizes the well-studied Bethe Hessian to the higher-order Kikuchi free energies. It is known that AMP, the flagship algorithm of statistical physics, has substantially worse performance than SOS for tensor PCA. In this work we 'redeem' the statistical physics approach by showing that our hierarchy gives a polynomial-time algorithm matching the performance of SOS. Our hierarchy also yields a continuum of subexponential-time algorithms, and we prove that these achieve the same (conjecturally optimal) tradeoff between runtime and statistical power as SOS. Our proofs are much simpler than prior work, and also apply to the related problem of refuting random k-XOR formulas. The results we present here apply to tensor PCA for tensors of all orders, and to k-XOR when k is even. Our methods suggest a new avenue for systematically obtaining optimal algorithms for Bayesian inference problems, and our results constitute a step toward unifying the statistical physics and sum-of-squares approaches to algorithm design.", "published": "2019-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2019.000-2"}]