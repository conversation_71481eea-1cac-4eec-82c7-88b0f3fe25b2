[{"primary_key": "183175", "vector": [], "sparse_vector": [], "title": "ANVIL: An In-Storage Accelerator for Name-Value Data Stores.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Name–value pairs (NVPs) are a widely-used abstraction to organize data in millions of applications. At a high level, an NVP associates a name (e.g., array index, key, hash) with each value in a collection of data. Specific NVP data store formats can vary widely, ranging from simple arrays/dictionaries and lookup tables to key–value stores and data mining workloads. Despite their importance, existing optimizations for NVPs are limited to only a single data store format, as the broad definition of NVPs allows for significant heterogeneity in encoding and implementation. We propose ANVIL, the first end-to-end system that allows programmers to broadly accelerate most formats of NVPs. With a conventional solid-state drive (SSD), large-scale NVP lookups can saturate both external and internal SSD bandwidth, as every NVP in the data store needs to be sent back to the host CPU to check for a matching name. ANVIL makes use of in-storage processing to avoid reading out any data for names that do not match, by performing name match checks directly inside the SSD’s NAND flash chips. We demonstrate that ANVIL can substantially reduce disk I/O, reduce metadata overheads, and provide speedups of 4.0 ×, 25 ×, and 14.6% over a conventional SSD, for three different NVP workloads (database transactions, analytics, and graph processing).", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731000"}, {"primary_key": "183176", "vector": [], "sparse_vector": [], "title": "The XOR Cache: A Catalyst for Compression.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Modern computing systems allocate significant amounts of resources for caching, especially for the last level cache (LLC). We observe that there is untapped potential for compression by leveraging redundancy due to private caching and inclusion that are common in today’s systems. We introduce the XOR Cache to exploit this redundancy via XOR compression. Unlike conventional cache architectures, XOR Cache stores bitwise XOR values of line pairs, halving the number of stored lines via a form of inter-line compression. When combined with other compression schemes, XOR Cache can further boost intra-line compression ratios by XORing lines of similar value, reducing the entropy of the data prior to compression. Evaluation results show that the XOR Cache can save LLC area by 1.93 × and power by 1.92 × at a cost of \\( 2.06\\% \\) performance overhead compared to a larger uncompressed cache, reducing energy-delay product by \\( 26.3\\% \\).", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3730995"}, {"primary_key": "183177", "vector": [], "sparse_vector": [], "title": "Leveraging control-flow similarity to reduce branch predictor cold effects in microservices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Georgia Antoniou", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Modern datacenter applications commonly adopt a microservice software architecture, where an application is decomposed into smaller interconnected microservices communicating via the network. These microservices often operate under strict latency requirements, rendering them particularly vulnerable to microarchitectural cold effects that may arise from the interleaved execution of services on cores or power-gating cores between invocations. Previous analyses of microservices find branch mispredictions due to cold predictor resources to be a significant contributor to performance degradation, indicating that the dynamic control flow must be very similar in the set and order of executed instructions across different requests. Our analysis of control-flow similarity across requests, using static and dynamic control flow information to determine dynamic control flow reconvergence, confirms that, indeed, a large portion of requests follow similar paths. Motivated by the above findings, we propose Similarity-based Branch Prediction (SBP), a hybrid predictor architecture that enhances conventional predictors with a similarity component. SBP leverages the control-flow similarity across microservice requests to predict control flow (branch direction and target) by utilizing the control flow of past executions encoded in a reference execution trace. We realize a specific instantiation of SBP, called CHESS, which combines a conventional history-based fetch predictor, a static-hint predictor, and a similarity predictor. CHESS judiciously applies similarity prediction for branches identified as hard-to-predict through conventional prediction techniques, effectively mitigating branch predictor cold-start effects while keeping the length of the reference trace practical. Evaluation through a suite of microservices shows that CHESS reduces branch MPKI by 94% over a cold fetch predictor and 78% over a state-of-the-art predictor, while requiring a modest 18.1KB of additional storage space. This enables CHESS to deliver performance that is, on average, within 95% of a warm baseline system.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731059"}, {"primary_key": "183178", "vector": [], "sparse_vector": [], "title": "Reconfigurable Stream Network Architecture.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "As AI systems grow increasingly specialized and complex, managing hardware heterogeneity becomes a pressing challenge. How can we efficiently coordinate and synchronize heterogeneous hardware resources to achieve high utilization? How can we minimize the friction of transitioning between diverse computation phases, reducing costly stalls from initialization, pipeline setup, or drain? Our insight is that a network abstraction at the ISA level naturally unifies heterogeneous resource orchestration and phase transitions. This paper presents a Reconfigurable Stream Network Architecture (RSN), a novel ISA abstraction designed for the DNN domain. RSN models the datapath as a circuit-switched network with stateful functional units as nodes and data streaming on the edges. Programming a computation corresponds to triggering a path. Software is explicitly exposed to the compute and communication latency of each functional unit, enabling precise control over data movement for optimizations such as compute-communication overlap and layer fusion. As nodes in a network naturally differ, the RSN abstraction can efficiently virtualize heterogeneous hardware resources by separating control from the data plane, enabling low instruction-level intervention. We build a proof-of-concept design RSN-XNN on VCK190, a heterogeneous platform with FPGA fabric and AI engines. Compared to the SOTA solution on this platform, it reduces latency by 6.1x and improves throughput by 2.4x–3.2x. Compared to the T4 GPU with the same FP32 performance, it matches latency with only 18% of the memory bandwidth. Compared to the A100 GPU at the same 7nm process node, it achieves 2.1x higher energy efficiency in FP32.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731088"}, {"primary_key": "183179", "vector": [], "sparse_vector": [], "title": "FATE: Boosting the Performance of Hyper-Dimensional Computing Intelligence with Flexible Numerical DAta TypE.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Li <PERSON>"], "summary": "Hyper-Dimensional Computing (HDC) is a promising brain-inspired learning framework designed for efficient, hardware-friendly computation. By utilizing highly parallel operations, HDC encodes raw data into a hyper-dimensional space, facilitating efficient training and inference processes. However, the high precision required for representing high-dimensional vectors presents challenges for implementing HDC on resource-constrained edge devices, mainly due to the significant computational cost of performing multiplication for cosine similarity calculations. On the other hand, binary HDC offers lower costs but sacrifices accuracy. This paper addresses these challenges by focusing on data quantization, a hardware-efficient compression technique that incorporates sparsity to effectively balance cost and accuracy on embedded FPGA. Unlike existing methods that employ the same quantization scheme for all dimensions, we propose a novel solution that applies different numerical data types to different dimensions of data representations. This approach is motivated by two factors: (1) the varying importance among dimensions and (2) the potential for better utilization of heterogeneous FPGA hardware resources. To achieve this, we propose FATE, an algorithm/architecture co-designed solution that utilizes low-precision data types for less important dimensions, enabling the replacement of multiplication operations with logic calculations. This approach leverages FPGA Look-Up Table (LUT) resources for efficient implementation. The key insight of FATE is that dimensions with high importance require high-precision data types, while the low-importance dimensions do not, allowing them to be sacrificed for low hardware overheads. Furthermore, to maximize resource utilization, we design a workload-aware mixed quantization scheme that offers flexible compression based on these differences in dimensional importance. The proposed quantization framework is seamlessly integrated into the existing FPGA implementations, leveraging the heterogeneous resources available (LUTs and DSPs) for low-precision and high-precision dimensions, respectively. We evaluate FATE across multiple application domains. With optimized data type ratios on the Kintex-7 FPGA device, our design results in up to \\( 50\\% \\) speedup and \\( 53.79\\% \\) energy saving, while maintaining accuracy compared to solely exploiting DSPs for all dimension computations.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731031"}, {"primary_key": "183180", "vector": [], "sparse_vector": [], "title": "Unified Memory Protection with Multi-granular MAC and Integrity Tree for Heterogeneous Processors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Na", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Recent system-on-a-chip (SoC) architectures for edge systems incorporate a variety of processing units, such as CPUs, GPUs, and NPUs. Although hardware-based memory protection is crucial for the security of edge systems, conventional mechanisms experience a significant performance degradation in such heterogeneous SoCs due to the increased memory traffic with diverse access patterns from different processing units. To mitigate the overheads, recent studies, targeting a specific domain such as machine learning software or accelerator, proposed techniques based on custom granularities applicable either to counters or MACs, but not both. In response to this challenge, we propose a unified mechanism to support both multi-granular MACs and counters in a device-independent way. It supports a granularity-aware integrity tree to make it adaptable to various access patterns. The multi-granular tree architecture stores both coarse-grained and fine-grained counters at different levels in the tree. Combined with the multi-granularity technique for MACs. Our optimization technique, termed multi-granular MAC&tree, supports four different levels of granularity. Its dynamic detection mechanism can select the most appropriate granularity for different memory regions accessed by heterogeneous processing units. In addition, we combine the multi-granularity support with the prior subtree approaches to further reduce the overheads. Our simulation-based evaluation results show that the multi-granular MAC and tree reduce the execution time by 14.2% from the conventional fixed-granular MAC&tree. By combining prior sub-tree techniques, the multi-granular MAC and tree finally reduce the execution time by 21.1% compared to the conventional fixed-granular MAC&tree.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731066"}, {"primary_key": "183181", "vector": [], "sparse_vector": [], "title": "Transitive Array: An Efficient GEMM Accelerator with Result Reuse.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>ng <PERSON>", "<PERSON>", "<PERSON>", "Hai <PERSON>", "<PERSON><PERSON>"], "summary": "Deep Neural Networks (DNNs) and Large Language Models (LLMs) have revolutionized artificial intelligence, yet their deployment faces significant memory and computational challenges, especially in resource-constrained environments. Quantization techniques have mitigated some of these issues by reducing data precision, primarily focusing on General Matrix Multiplication (GEMM). This study introduces a novel sparsity paradigm, transitive sparsity, which leverages the reuse of previously computed results to substantially minimize computational overhead in GEMM operations. By representing transitive relations using a directed acyclic graph, we develop an efficient strategy for determining optimal execution orders, thereby overcoming inherent challenges related to execution dependencies and parallelism. Building on this foundation, we present the Transitive Array, a multiplication-free accelerator designed to exploit transitive sparsity in GEMM. Our architecture effectively balances computational workloads across multiple parallel lanes, ensuring high efficiency and optimal resource utilization. Comprehensive evaluations demonstrate that the Transitive Array achieves approximately 7.46 × and 3.97 × speedup and 2.31 × and 1.65 × energy reduction compared to state-of-the-art accelerators such as Olive and BitVert while maintaining comparable model accuracy on LLaMA models.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731043"}, {"primary_key": "183182", "vector": [], "sparse_vector": [], "title": "Oaken: Fast and Efficient LLM Serving with Online-Offline Hybrid KV Cache Quantization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ryeowook Ko", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Jongse Park"], "summary": "Modern Large Language Model (LLM) serving system batches multiple requests to achieve high throughput, while batching attention operations is challenging, rendering memory bandwidth a critical bottleneck. Today, to mitigate this issue, the community relies on high-end GPUs with multiple high-bandwidth memory (HBM) channels. Unfortunately, HBM’s high bandwidth often comes at the expense of limited memory capacity, necessitating systems to scale, which reduces core utilization and increases costs. Moreover, recent advancements enabling longer contexts for LLMs have substantially increased the key-value (KV) cache size, further intensifying the pressures on memory capacity. To lower the pressure, the literature has explored KV cache quantization techniques, which commonly use low bitwidth (e.g., INT4) for most values, selectively using higher bitwidth (e.g., FP16) for outlier values. While this approach helps achieve high accuracy and low bitwidth simultaneously, it comes with the limitation that the cost for online outlier detection is excessively high, negating the advantages of quantization. Inspired by these insights, we propose Oaken, an acceleration solution that achieves high accuracy and high performance simultaneously through co-designing algorithm and hardware. To effectively find a sweet spot in the accuracy-performance trade-off space of KV cache quantization, Oaken employs an online-offline hybrid approach, setting outlier thresholds offline, which are then used to determine the quantization scale online. To translate the proposed algorithmic technique into tangible performance gains, Oaken also comes with custom quantization/dequantization engines and memory management units that can be integrated with any LLM accelerators. We built an Oaken accelerator on top of an LLM accelerator, LPU, and conducted a comprehensive evaluation. Our experiments show that for a batch size of 256, Oaken achieves up to 1.58 × throughput improvement over NVIDIA A100 GPU, incurring a minimal accuracy loss of only 0.54% on average, compared to state-of-the-art KV cache quantization techniques.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731019"}, {"primary_key": "183183", "vector": [], "sparse_vector": [], "title": "Genesis: A Compiler for Hamiltonian Simulation on Hybrid CV-DV Quantum Computers.", "authors": ["<PERSON><PERSON><PERSON>", "Jiakang Li", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhou", "<PERSON>", "<PERSON>"], "summary": "This paper introduces <PERSON>, the first compiler designed to support Hamiltonian Simulation on hybrid continuous-variable (CV) and discrete-variable (DV) quantum computing systems. Genesis is a two-level compilation system. At the first level, it decomposes an input Hamiltonian into basis gates using the native instruction set of the target hybrid CV-DV quantum computer. At the second level, it tackles the mapping and routing of qumodes/qubits to implement long-range interactions for the gates decomposed from the first level. Rather than a typical implementation that relies on SWAP primitives similar to qubit-based (or DV-only) systems, we propose an integrated design of connectivity-aware gate synthesis and beamsplitter SWAP insertion tailored for hybrid CV-DV systems. We also introduce an OpenQASM-like domain-specific language (DSL) named CVDV-QASM to represent Hamiltonian in terms of Pauli-exponentials and basic gate sequences from the hybrid CV-DV gate set. Genesis has successfully compiled several important Hamiltonians, including the Bose-Hubbard model, \\( \\mathbb {Z}_2- \\) Higgs model, Hubbard-Holstein model, Heisenberg model and Electron-vibration coupling Hamiltonians, which are critical in domains like quantum field theory, condensed matter physics, and quantum chemistry. Our implementation is available at Genesis-CVDV-Compiler https://github.com/ruadapt/Genesis-CVDV-Compiler.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731065"}, {"primary_key": "183184", "vector": [], "sparse_vector": [], "title": "Cambricon-SR: An Accelerator for Neural Scene Representation with Sparse Encoding Table.", "authors": ["<PERSON><PERSON><PERSON>", "Xinka<PERSON> Song", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Zidong Du", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Guangzhong Sun", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Neural Scene Representation (NSR) is a promising technique for representing real scenes. By learning from dozens of 2D photos captured from different viewpoints, NSR computes the 3D representation of real scenes. However, the performance of NSR processing running on GPU is insufficient for applications. Cambricon-R achieves high performance of more than 60 scenes per second, but at the cost of modeling quality. In this paper, we propose Cambricon-SR, an algorithm-hardware co-designed accelerator to improve NSR performance and quality. First, we propose a novel NSR algorithm with sparse encoding table, it achieves more than 80% of sparsity for the encoding table with negligible accuracy loss. Second, to efficiently eliminate the invalid memory access requests to the pruned entries, we propose the sparse index unit based on sequential SRAM access to address the challenge of irregular memory access to the sparse structure bitmap. It achieves a throughput increase of 7.54 × for the encoding stage with an additional area overhead of 8.59%. Third, we propose the dynamic shared buffer for the MLP units to reduce the buffer usage by 85.3% so that we can increase the number of the MLP units. We conduct experimental evaluations on 8 typical scenes. The results demonstrate that, compared with A100 GPU and Cambricon-R, Cambricon-SR achieves 1259 × and 4.12 × speedup, and reduces the energy consumption by 1139 × and 2.98 × for each training iteration of NSR, respectively.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731018"}, {"primary_key": "183185", "vector": [], "sparse_vector": [], "title": "Lumina: Real-Time Neural Rendering by Exploiting Computational Redundancy.", "authors": ["<PERSON>", "<PERSON><PERSON> Lin", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "3D Gaussian Splatting (3DGS) has vastly advanced the pace of neural rendering, but it remains computationally demanding on today’s mobile SoCs. To address this challenge, we propose Lumina, a hardware-algorithm co-designed system, which integrates two principal optimizations: a novel algorithm, \\( {\\mathcal {S}}^2 \\), and a radiance caching mechanism, \\( {\\mathcal {RC}} \\), to improve the efficiency of neural rendering. \\( {\\mathcal {S}}^2 \\) algorithm exploits temporal coherence in rendering to reduce the computational overhead, while \\( {\\mathcal {RC}} \\) leverages the color integration process of 3DGS to decrease the frequency of intensive rasterization computations. Coupled with these techniques, we propose an accelerator architecture, LuminCore, to further accelerate cache lookup and address the fundamental inefficiencies in Rasterization. We show that <PERSON><PERSON> achieves 4.5 × speedup and 5.3 × energy reduction against a mobile Volta GPU, with a marginal quality loss (< 0.2 dB peak signal-to-noise ratio reduction) across synthetic and real-world datasets.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731003"}, {"primary_key": "183186", "vector": [], "sparse_vector": [], "title": "MD-pipe: A <PERSON> Scaling Enhanced Pipeline Architecture for Ab Initio Accuracy Molecular Dynamics.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Sun", "<PERSON><PERSON><PERSON> Tan"], "summary": "Molecular Dynamics (MD) simulations with first-principles accuracy are widely applied in various fields, including materials science and molecular pharmacology. Current research focus on reducing the solution time of ab initio molecular dynamics (AIMD) from both algorithmic and software perspectives. However, these optimizations are still far from meeting the ever-increasing performance demands for AIMD. In this paper, a fine-grained pipeline architecture is proposed to further optimize the solution time of MD. We design a high-utilization systolic line that eliminates the injection and evacuation time during each matrix operation. Besides, in conjunction with the MD dataflow, a computation migration strategy is introduced to reduce the storage overhead. Furthermore, we leverage dataflow rearrangement and preloading to eliminate the matrix transpose costs. The MD-pipe architecture has been implemented and verified using AMD VPK180 FPGA and ASIC synthesis tools. Evaluation results show that our work on FPGA and ASIC outperforms the NVIDIA A100 GPU by 2.97 × and 23.77 × respectively. Moreover, the ASIC implementation achieves a simulation speed of 67.6μs/day, outperforming state-of-the-art work on Fugaku supercomputer by 454 times under extremely strong scaling deployment (one-atom-per-core).", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731052"}, {"primary_key": "183187", "vector": [], "sparse_vector": [], "title": "Accelerating Simulation of Quantum Circuits under Noise via Computational Reuse.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "To realize the full potential of quantum computers, we must mitigate qubit errors by developing noise-aware algorithms, compilers, and architectures. Thus, simulating quantum programs on high-performance computing (HPC) systems with different noise models is a de facto tool researchers use. Unfortunately, noisy simulators iteratively execute a similar circuit for thousands of trials, thereby incurring significant performance overheads. To address this, we propose a noisy simulation technique called Tree-Based Quantum Circuit Simulation (TQSim) 1. TQSim exploits the reusability of intermediate results during the noisy simulation, reducing computation. TQSim dynamically partitions a circuit into several subcircuits. It then reuses the intermediate results from these subcircuits during computation. Compared to a noisy Qulacs-based baseline simulator, TQSim achieves a speedup of up to 3.89 × for noisy simulations. TQSim is designed to be efficient with multi-node setups while also maintaining tight fidelity bounds.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3730992"}, {"primary_key": "183188", "vector": [], "sparse_vector": [], "title": "Folded Banks: 3D-Stacked HBM Design for Fine-Grained Random-Access Bandwidth.", "authors": ["<PERSON><PERSON><PERSON>", "Bradford <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Despite significant improvements in peak bandwidth, the HBM industry has neglected random-access (irregular) bandwidth, limiting performance in many real-world applications. Improving effective HBM bandwidth is challenging due to power-constrained activations and coarse-grained, long-distance data movement. Rather than addressing these issues directly, hardware vendors have opted for incremental changes, achieving a 6.4 × increase in sequential access bandwidth over two generations while leaving the irregular bandwidth challenges unresolved. To remedy this, we introduce Folded Banks (FB-HBM), a novel 3D bank design that redistributes bank subarrays (“folds”) across multiple dies and relocates command, control, and global sense amplifiers to an additional base layer. By implementing this logic in a new base layer, we eliminate the DRAM die overheads inherent in previous designs. This architecture enables vertical routing of intra-bank wires—column select lines (CSLs) and master data lines (MDLs)—through thin-pitch through-silicon vias (TSVs) and hybrid bonds, significantly reducing RC power losses. By employing self-timed sense amplifiers, we eliminate costly dummy subarrays previously required for reference voltages. Furthermore, our distributed TSV architecture minimizes inter-bank data movement and we reduce HBM row size and activation energy by selectively disabling memory arrays (MATs) within a bank. Compared to a projected HBM4 design, FB-HBM achieves a 6.7 × improvement in random-access performance with a conservative 5 µm TSV pitch. This architectural advantage translates to a 2.28 × speedup across high-performance computing (HPC) and sparse machine learning applications.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731111"}, {"primary_key": "183189", "vector": [], "sparse_vector": [], "title": "HPVM-HDC: A Heterogeneous Programming System for Accelerating Hyperdimensional Computing.", "authors": ["Russel <PERSON>e", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Hyperdimensional Computing (HDC), a technique inspired by cognitive models of computation, has been proposed as an efficient and robust alternative basis for machine learning. HDC programs are often manually written in low-level and target specific languages targeting CPUs, GPUs, and FPGAs—these codes cannot be easily retargeted onto HDC-specific accelerators. No previous programming system enables productive development of HDC programs and generates efficient code for several hardware targets. We propose a heterogeneous programming system for HDC: a novel programming language, HDC++, for writing applications using a unified programming model, including HDC-specific primitives to improve programmability, and a heterogeneous compiler, HPVM-HDC, that provides an intermediate representation for compiling HDC programs to many hardware targets. We implement two tuning optimizations, automatic binarization and reduction perforation, that exploit the error resilient nature of HDC. Our evaluation shows that HPVM-HDC generates performance-competitive code for CPUs and GPUs, achieving a geomean speed-up of 1.17x over optimized baseline CUDA implementations with a geomean reduction in total lines of code of 1.6x across CPUs and GPUs. Additionally, HPVM-HDC targets an HDC Digital ASIC and an HDC ReRAM accelerator simulator, enabling the first execution of HDC applications on these devices.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731095"}, {"primary_key": "183190", "vector": [], "sparse_vector": [], "title": "Caravan: A Hardware/Software Co-Design for Efficient SIMD Neighbor Search on Point Clouds.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Neighbor search is the backbone task for point cloud processing, which is widely employed in current 3D computer vision applications. To be efficient, neighbor search relies on spatial data structures such as k-d trees, to prune the search space. We found that consecutive neighbor search queries in point cloud processing are often similar, visiting k-d tree nodes with considerable resemblance. In this work, we show how to leverage this observation to effectively exploit the available CPU Vector Processing Unit (VPU) to cheaply speed up neighbor search. We devise our solution with a hardware/software co-design called Caravan. At the software level, Caravan-SW exploits this search similarity, gathering consecutive queries to search for their neighbors in parallel with SIMD instructions. Yet, when the navigation of queries diverges, particularly in the deeper levels of the k-d tree, Caravan-SW faces sparsity and VPU lanes are underutilized. We tackle this with Caravan-HW, adding two new instructions that re-index valid vector elements and allow fast operand shuffling and dense SIMD operations to take place, suppressing the hard-to-predict runtime sparsity of Caravan-SW. With AVX512, Caravan-SW speeds up neighbor search by 4.05 × (1.85 × end-to-end) during point cloud segmentation in a commodity CPU. With the additional Caravan-HW support, the leaf processing part of neighbor search can be further optimized, boosting speed up to 5.19 × (1.97 × end-to-end), with minimal area costs of 0.032 mm². Our programmable and minimally intrusive solution has end-to-end benefits comparable to accelerators.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731075"}, {"primary_key": "183191", "vector": [], "sparse_vector": [], "title": "Avalanche: Optimizing Cache Utilization via Matrix Reordering for Sparse Matrix Multiplication Accelerator.", "authors": ["Gwangeu<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Sparse Matrix Multiplication (SpMM) is essential in various scientific and engineering applications but poses significant challenges due to irregular memory access patterns. Many hardware accelerators have been proposed to accelerate SpMM. However, they have yet to focus on on-chip memory utilization. In this paper, we highlight the underutilization of the on-chip memory in the SpMM accelerators. Then we propose Avalanche, a novel hardware accelerator that optimally utilizes the on-chip memory to efficiently cache both matrices B and C. Avalanche incorporates three key techniques: Matrix Reordering (Mat-Reorder), Dead-Product Early Eviction (DP-Evict), and Reuse Distance-Aware Matrix Caching (RM-Caching). Mat-Reorder enhances data locality by reordering the columns of matrix A, ensuring early completion of computations for matrix C. DP-Evict optimizes on-chip memory usage by promptly evicting fully computed (dead) products from on-chip memory. RM-Caching maximizes data reuse by caching frequently accessed elements of matrix B based on their reuse distance. Experimental results demonstrate that Avalanche achieves an average performance improvement of 1.97 × compared to the state-of-the-art SpMM accelerator, with a chip area of 6.15 mm2", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3730990"}, {"primary_key": "183192", "vector": [], "sparse_vector": [], "title": "Enabling Ahead Prediction with Practical Energy Constraints.", "authors": ["Lingzhe <PERSON>", "<PERSON><PERSON><PERSON>", "Yale N. <PERSON>"], "summary": "Accurate branch predictors require multiple cycles to produce a prediction, and that latency hurts processor performance. \"Ahead prediction\" solves the performance problem by starting the prediction early. Unfortunately, this means making the prediction without the directions of the N branches between when the prediction starts and when the relevant branch’s prediction is needed. The energy required to consider all 2n possible cases increases by 14.6x, making ahead prediction not viable. This paper shows that most of the intermediate branch directions never materialize, reducing the number of observed missing history patterns significantly (usually, only one or two). We modified the TAGE predictor to eliminate those branches from having to be considered. The result, our ahead predictor can produce a performance benefit of 4.4%, while causing an increase in energy consumption of only 1.5x, far less than the 14.6x that was thought to be necessary, and very much viable.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3730998"}, {"primary_key": "183193", "vector": [], "sparse_vector": [], "title": "AMALI: An Analytical Model for Accurately Modeling LLM Inference on Modern GPUs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Hong An", "<PERSON><PERSON><PERSON>"], "summary": "Large language model (LLM) inference applications are surging in recent years, which largely relies on modern GPUs. On the other hand, GPU analytical model is a commonly used tool for architects to precisely identify bottlenecks quickly with deep insights. However, existing GPU analytical models fall short of accurately modeling LLM inference applications on modern GPUs, because of unsuitable tensor core modeling, ignoring constant cache as well as instruction cache modeling and abstracting away important details for LLM inference applications. To address this problem, we propose a novel analytical model dubbed AMALI to accurately model LLM inference on modern GPUs with three innovations. First, we develop an instruction modifier and throughput based tensor core model by accurately capturing the math pipe throttle stalls to enhance the architecture modeling for modern GPUs. Second, we propose analytical models for constant cache and instruction cache by developing micro-benchmarks to measure CUDA kernel launching latencies. This significantly improves AMALI’s accuracy compared to real GPU hardware. Finally, we design a multi-warp model by leveraging warp instruction number distribution to reflect LLM inference application characteristics. We validate AMALI on an A100 GPU by using typical LLM inference applications. The results show that AMALI reduces the MAPE (mean absolute percentage error) from 127.56% to 23.59% compared to the state-of-the-art GCoM model. We further showcase that AMALI can be used to explore architecture design space by designing the tensor core capability of H100. The results show that AMALI accurately predicts the end-to-end performance improvements with the enhanced tensor core capability.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731064"}, {"primary_key": "183194", "vector": [], "sparse_vector": [], "title": "GCStack+GCScaler: Fast and Accurate GPU Performance Analyses Using Fine-Grained Stall Cycle Accounting and Interval Analysis.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "To design next-generation Graphics Processing Units (GPUs), GPU architects rely on GPU performance analyses to identify key GPU performance bottlenecks and explore GPU design spaces. Unfortunately, the existing GPU performance analysis mechanisms make it difficult for GPU architects to conduct fast and accurate GPU performance analyses. The existing mechanisms can provide misleading insights into GPU performance bottlenecks. They characterize the performance-degrading stall events of a GPU using coarse-grained, issue-stage-centric, and priority-based cycle stacks which tend to exaggerate memory-side stall events and hide concurrently occurring stall events. The existing mechanisms also incur high GPU design space exploration overhead, as they involve repetitive cycle-level timing simulations for evaluating alternative GPU designs. In this paper, we propose two GPU performance analysis mechanisms, namely GCStack and GCScaler. The two mechanisms enable fast and accurate GPU performance analyses by (1) accurately characterizing the performance bottlenecks of a baseline GPU using fine-grained stall cycle accounting, and (2) accurately scaling the stall cycles of the baseline GPU using interval analysis and analytical scaling models. GCStack captures all the concurrently occurring stall events within each stall cycle, and characterizes the performance as a fine-grained cycle stack. Using the fine-grained cycle stack, GCScaler leverages the existing GPU interval analysis techniques’ accurate stall cycle scaling capability to estimate the cycle stack for an alternative GPU design. GCScaler further employs analytical scaling models designed to scale the idle and synchronization stall cycles accurately. Our evaluation using 47 benchmarks shows that GCStack and GCScaler accelerate an exploration of 1,000 GPU designs by 32.7× over repetitive timing simulations while achieving a low mean absolute performance estimation error rate of 6.37%.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731068"}, {"primary_key": "183195", "vector": [], "sparse_vector": [], "title": "REIS: A High-Performance and Energy-Efficient Retrieval System with In-Storage Processing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Hai<PERSON> Mao", "Jisung Park", "<PERSON>", "<PERSON><PERSON>"], "summary": "Large Language Models (LLMs) face an inherent challenge: their knowledge is confined to the data that they have been trained on. This limitation, combined with the significant cost of retraining renders them incapable of providing up-to-date responses. To overcome these issues, Retrieval-Augmented Generation (RAG) complements the static training-derived knowledge of LLMs with an external knowledge repository. RAG consists of three stages: (i) indexing, which creates a database that facilitates similarity search on text embeddings, (ii) retrieval, which, given a user query, searches and retrieves relevant data from the database and (iii) generation, which uses the user query and the retrieved data to generate a response. The retrieval stage of RAG in particular becomes a significant performance bottleneck in inference pipelines. In this stage, (i) a given user query is mapped to an embedding vector and (ii) an Approximate Nearest Neighbor Search (ANNS) algorithm searches for the most semantically similar embedding vectors in the database to identify relevant items. Due to the large database sizes, ANNS incurs significant data movement overheads between the host and the storage system. To alleviate these overheads, prior works propose In-Storage Processing (ISP) techniques that accelerate ANNS workloads by performing computations inside the storage system. However, existing works that leverage ISP for ANNS (i) employ algorithms that are not tailored to ISP systems, (ii) do not accelerate data retrieval operations for data selected by ANNS, and (iii) introduce significant hardware modifications to the storage system, limiting performance and hindering their adoption. We propose REIS, the first Retrieval system tailored for RAG with In-Storage processing that addresses the limitations of existing implementations with three key mechanisms. First, REIS employs a database layout that links database embedding vectors to their associated documents, enabling efficient retrieval. Second, it enables efficient ANNS by introducing an ISP-tailored algorithm and data placement technique that: (i) distributes embeddings across all planes of the storage system to exploit parallelism, and (ii) employs a lightweight Flash Translation Layer (FTL) to improve performance. Third, REIS leverages an ANNS engine that uses the existing computational resources inside the storage system, without requiring hardware modifications. The three key mechanisms form a cohesive framework that largely enhances both the performance and energy efficiency of RAG pipelines. Compared to a high-end server-grade system, REIS improves the performance (energy efficiency) of the retrieval stage by an average of 13 × (55 ×). REIS offers improved performance against existing ISP-based ANNS accelerators, without introducing any hardware modifications, enabling easier adoption for RAG pipelines.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731116"}, {"primary_key": "183196", "vector": [], "sparse_vector": [], "title": "Ecco: Improving Memory Bandwidth and Capacity for LLMs via Entropy-Aware Cache Compression.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hai <PERSON>", "<PERSON><PERSON>"], "summary": "Large language models (LLMs) have demonstrated transformative capabilities across diverse artificial intelligence applications, yet their deployment is hindered by substantial memory and computational demands, especially in resource-constrained environments. Quantization techniques have emerged as a critical solution, reducing data precision to enhance memory and computational efficiency. However, existing methods often suffer from high runtime overheads and potential accuracy degradation. To address these challenges, we propose Ecco, an entropy-based cache compression technique tailored for LLMs. <PERSON><PERSON> combines group-wise and non-uniform quantization with pre-defined shared k-means patterns and Huffman coding to exploit the inherent entropy characteristics of LLM cache data. Recognizing the inefficiencies of traditional <PERSON><PERSON>man coding in terms of parallelism and latency, we introduce a novel parallel Huffman-based decoding process with a multi-stage pipeline design, reducing latency by two orders of magnitude and achieving throughput comparable to GPU L2 caches. Comprehensive evaluations demonstrate that <PERSON><PERSON> achieves an up to 2.9 × and 1.9 × speedup over the state-of-the-art AWQ and SmoothQuant framework, 2.4 × over the Olive accelerator, all while increasing memory capacity by nearly 4 × and maintaining state-of-the-art LLM accuracy. These results underscore the effectiveness of our entropy-based cache compression in enhancing LLM performance and efficiency, paving the way for more deployable large-scale AI models.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731024"}, {"primary_key": "183197", "vector": [], "sparse_vector": [], "title": "Adaptive CHERI Compartmentalization for Heterogeneous Accelerators.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Hardware accelerators offer high performance and energy efficiency for specific tasks compared to general-purpose processors. However, current hardware accelerator designs focus primarily on performance, overlooking security. This poses significant security risks due to potential memory safety violations that can affect the entire hardware system. Existing methods either rely on Input-Output Memory Management Units (IOMMUs) for memory isolation between memory pages, leading to vulnerabilities in intra-page memory accesses, or modify an accelerator architecture for specialized memory protection, which requires significant effort and cannot scale across multiple diverse applications. In this paper, we propose a general method for fine-grained memory protection in heterogeneous systems without modifying accelerator architectures. We extend the Capability Hardware Enhanced RISC Instructions (CHERI) from CPUs to an adaptive hardware interface named CapChecker. The CapChecker imports capabilities from the CPU and guards memory accesses at the pointer level from CHERI-unaware accelerators as if they were CHERI-aware natively. Over a set of benchmarks on hardware accelerators in a heterogeneous system, our approach achieves fine-grained memory protection, with a 1.4% performance overhead compared to CHERI-unaware accelerators on average.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731062"}, {"primary_key": "183198", "vector": [], "sparse_vector": [], "title": "Scaling Llama 3 Training with Efficient Parallelism Strategies.", "authors": ["Weiwei Chu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Jongsoo Park"], "summary": "Llama is a widely used open-source large language model. This paper presents the design and implementation of the parallelism techniques used in Llama 3 pre-training. To achieve efficient training on tens of thousands of GPUs, Llama 3 employs a combination of four-dimensional parallelism: fully sharded data parallelism, tensor parallelism, pipeline parallelism, and context parallelism. Beyond achieving efficiency through parallelism and model co-design, we also address other equally critical aspects. First, we enhance flexibility—for example, through novel pipeline parallelism that supports evolving batch sizes and heterogeneous model architectures, and innovative context parallelism that enables model innovations such as document-mask attention. Second, we prioritize practicality—for example, by enabling the diagnosis of performance and numerical issues at scale. Finally, drawing on our experience with large-scale training, we provide recommendations for future hardware design.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731410"}, {"primary_key": "183199", "vector": [], "sparse_vector": [], "title": "Meta&apos;s Second Generation AI Chip: Model-Chip Co-Design and Productionization Experiences.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Dixit", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Jongsoo Park", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>-<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Zitong Zeng", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON>"], "summary": "The rapid growth of AI workloads at Meta has motivated our in-house development of AI chips, aiming to significantly reduce the total cost of ownership and mitigate risks posed by unpredictable GPU supplies. At ISCA’23, we presented Meta’s first-generation AI chip, MTIA 1. This paper describes its successor, MTIA 2i, now deployed at scale and serving billions of users. MTIA 2i significantly improves upon MTIA 1, reducing total cost of ownership by 44% compared to GPUs while delivering competitive performance per watt. A key differentiator is its memory hierarchy: instead of costly HBM, it uses large SRAM alongside LPDDR. Although there has been a proliferation of publications on AI chips, they often focus on architectural design and overlook three critical aspects: (1) co-designing and optimizing ML models to work effectively with the AI chip; (2) demonstrating sufficient flexibility to support a wide range of models; and (3) during the productionization process, addressing challenges unanticipated or decisions deferred at design time, such as dealing with memory errors, safe overclocking, reducing provisioned power, and implementing real-time firmware updates to mitigate silicon design defects. A key contribution of this paper is sharing our experience with these aspects, based on our journey of productionizing MTIA 2i at scale.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731409"}, {"primary_key": "183200", "vector": [], "sparse_vector": [], "title": "Need for zkSpeed: Accelerating HyperPlonk for Zero-Knowledge Proofs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Benedikt <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Zero-Knowledge Proofs (ZKPs) are a rapidly growing technique for privacy-preserving and verifiable computation. ZKPs enable one party (a prover: \\( \\mathcal {P} \\)) to prove to another (a verifier: \\( \\mathcal {V} \\)) that a statement is true or correct without revealing any additional information. This powerful capability has led to ZKPs being applied and proposed for application in blockchain technologies, verifiable machine learning, and electronic voting. However, ZKPs have yet to see widespread, ubiquitous adoption due to the exceptionally high computational complexity of the proving process. Naturally, there has been recent work to accelerate ZKP primitives and protocols using GPUs and ASICs. However, the protocols considered so far face one of two challenges: they require a trusted setup for each new application or generate large proofs with high verification costs, limiting their applicability in scenarios with numerous verifiers or strict verification time constraints. HyperPlonk is a state-of-the-art ZKP protocol that supports both one-time, universal setup and small proof sizes/verification costs expected by publicly verifiable, consensus-based systems (e.g., blockchain). While HyperPlonk’s setup and verifier properties are highly desirable, the proving phase is costly. A HyperPlonk prover must compute on large bitwidths (e.g., 255-381b) and polynomials (e.g., of degree 224), employs computationally (e.g., MSM) and bandwidth (e.g., SumCheck) intensive kernels, and the complete protocol comprises many steps, each constituting distinct kernels. We present an accelerator, zkSpeed, to address these challenges and effectively accelerate HyperPlonk. zkSpeed provides hardware support for all major primitives (e.g., SumCheck and Multi-Scalar Multiplications (MSMs)) and judiciously schedules each protocol phase onto the allocated hardware. We leverage high-level synthesis to thoroughly explore and optimize the hardware design tradeoffs of each unit. These are then input into a full-chip simulator for large-scale design space exploration to optimize all aspects of the architecture in unison. Our Pareto analysis demonstrates that with a 366mm2 chip and 2 TB/s of off-chip bandwidth, zkSpeed is able to accelerate the entire proof generation by 801 × (geomean) over a CPU baseline.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731021"}, {"primary_key": "183201", "vector": [], "sparse_vector": [], "title": "Variational Quantum Algorithms in the era of Early Fault Tolerance.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Quantum computing roadmaps predict the availability of 10,000-qubit devices within the next 3–5 years. With projected two-qubit error rates of 0.1%, these systems will enable certain operations under quantum error correction (QEC) using lightweight codes, offering significantly improved fidelities compared to the NISQ era. However, the high qubit cost of QEC codes like the surface code (especially at near-threshold physical error rates) limits the error correction capabilities of these devices. In this emerging era of Early Fault Tolerance (EFT), it will be essential to use QEC resources efficiently and focus on applications that derive the greatest benefit. In this work, we investigate the implementation of Variational Quantum Algorithms in the EFT regime (EFT-VQA). We explore the ideas of partial quantum error correction (pQEC), a strategy that error-corrects Clifford operations while performing Rz(θ) rotations via magic state injection instead of the more expensive T-state distillation, and adapt it to VQAs. Our results show that pQEC can improve VQA fidelities by 9.27x over standard approaches. Furthermore, we propose architectural optimizations that reduce circuit latency by ∼ 2x, and achieve qubit packing efficiency of \\( 66\\% \\) in the EFT regime. The source code can be accessed here https://github.com/siddharthdangwal/EFT-VQA.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731112"}, {"primary_key": "183202", "vector": [], "sparse_vector": [], "title": "Zettafly: A Network Topology with Flexible Non-blocking Regions for Large-scale AI and HPC Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Interconnection networks are playing an increasingly crucial role in achieving scalability and throughput for post-exascale and zettascale computing systems. Resource consumption characteristics of AI and HPC workloads are essential to designing effective network infrastructure. The operation practice of production supercomputing systems reveals that small and medium-sized jobs consume most compute-core hours, generating traffic that mainly utilizes a size-restricted portion of the network. Unfortunately, current topologies lack sufficient support for flexible partitioning of distinct concurrent jobs. This study bridges this gap by exploring a new tradeoff among scalability, throughput, and non-blocking regions. We present Zettafly, a family of low-diameter topologies with large-scale non-blocking sub-networks, allowing the majority of jobs to be isolated within a single sub-network. Zettafly is a two-layer structure, consisting of non-blocking groups and global routers, such that each minimally routed packet between groups traverses at most one global router. We also introduce simple and efficient adaptive routing algorithms for Zettafly. We conduct extensive simulations and analysis to evaluate the performance and cost of Zettafly against state-of-the-art topologies. The results show that Zettafly achieves great performance under typical HPC workloads, offers cost-effectiveness under multi-task mixed traffic, supports flexible configurations and incremental deployments, and is cost-effective to deploy for post-exascale and zettascale systems.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731098"}, {"primary_key": "183203", "vector": [], "sparse_vector": [], "title": "MagiCache: A Virtual In-Cache Computing Engine.", "authors": ["<PERSON><PERSON>", "Yikai Cui", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The rise of data-parallel applications poses a significant challenge to the energy consumption of computing architectures. In-cache computation is a promising solution for achieving high parallelism and energy efficiency because it can eliminate data movement between the cache and the processor. Existing in-cache computing architectures transform a portion of cache arrays into computing arrays, with all rows of these arrays serving as computing lines. The remaining cache arrays are used as cachelines to store the data required by computing arrays or processors. However, in these array-level in-cache computing architectures, only a few computing lines in each computing array are active at runtime while the others are idle, which incurs severe cache capacity loss and space under-utilization. In addition, bursty memory accesses of data-parallel applications also cause significant in-cache data movement latency. To address these problems, we propose MagiCache, a virtual in-cache computing engine. First, we design a novel cacheline-level in-cache computing architecture in which each cache array can configure some rows as computing lines and the other rows as cachelines with negligible overhead. Second, a virtual engine is further designed on this novel architecture to dynamically allocate different rows of each array as computing lines or cachelines based on runtime computation and storage requirements, thus realizing efficient cacheline-level space management. Finally, we present an instruction chaining technique to overlap the bursty access latency by enabling asynchronous execution of computing arrays. Evaluation results show that MagiCache achieves a 1.19x-1.61x speedup over the state-of-the-art in-cache computing architectures with 6.5 KB of additional storage. Our cacheline-level space management improves cache utilization by 42% and reduces cache miss rate by 10%-40% over various memory access patterns. The instruction chaining technique also reduces the memory access time by 2%-27%.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731113"}, {"primary_key": "183204", "vector": [], "sparse_vector": [], "title": "FAST: An FHE Accelerator for Scalable-parallelism with Tunable-bit.", "authors": ["Shengy<PERSON> Fan", "<PERSON><PERSON><PERSON>", "Liang Kong", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Fully Homomorphic Encryption (FHE) enables direct computation on encrypted data, providing substantial security advantages in cloud-based modern society. However, FHE suffers from significant computational overhead compared to plaintext computation, hindering its adoption in real-world applications. While many accelerators have been designed to address performance bottlenecks, most do not fully leverage cryptographic optimization technologies, leaving room for further performance enhancements. In this work, we propose FAST, an FHE accelerator incorporating recent cryptographic optimizations, including hoisting technology and the gadget decomposition key-switching method (named KLSS method). We analyze ciphertext level consumption throughout application execution and observe that workload requirements vary significantly with different ciphertext levels for both hybrid and KLSS key-switching methods. Additionally, we note the differing computational precision requirements for these key-switching methods. Based on these observations, we designed a versatile framework that supports multiple key-switching methods during a single application execution and integrates hoisting technology. Furthermore, we developed a scalable, precision-tunable multiplier to accommodate the needs of hybrid and KLSS key-switching methods. FAST architecture features a specialized multiplier and novel data organization to exploit cryptographic optimizations effectively. To our knowledge, this is the first accelerator to support hoisting technology and the gadget decomposition key-switching method. Our solution achieves a significant performance improvement, averaging a 1.8 × speedup.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731407"}, {"primary_key": "183205", "vector": [], "sparse_vector": [], "title": "Rethinking Prefetching for Intermittent Computing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Prefetching improves performance by reducing cache misses. However, conventional prefetchers are too aggressive to serve batteryless energy harvesting systems (EHSs) where energy efficiency is the utmost design priority due to weak input energy and the resulting frequent outages. To this end, this paper proposes IPEX, an Intermittence-aware Prefetching EXtension that can be integrated into existing prefetchers on EHSs. IPEX aims to avert useless prefetches by suppressing the prefetching of the cache blocks receiving no hit before their loss on power failure, which would otherwise waste harvested energy. At a proper moment before an upcoming outage, IPEX throttles the prefetch degree to target only those blocks that are likely to be used before the outage. That way IPEX saves energy and spends it on making further execution progress. Experimental results show that on average, IPEX reduces energy consumption by 7.86% (up to 21.64%) and improves performance by 8.96% (up to 23.49%) compared to a conventional prefetcher.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731038"}, {"primary_key": "183206", "vector": [], "sparse_vector": [], "title": "CaliQEC: In-situ Qubit Calibration for Surface Code Quantum Error Correction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Yunong Shi"], "summary": "Quantum Error Correction (QEC) is essential for fault-tolerant, large-scale quantum computation. However, error drift in qubits undermines QEC performance during long computations, necessitating frequent calibration. Conventional calibration methods disrupt quantum states, requiring system downtime and rendering in situ calibration impractical. To address this challenge, we propose QECali, a novel framework that enables in situ calibration for surface codes. Our evaluation demonstrates that QECali introduces modest qubit overhead and negligible increases in execution time, offering the first practical solution for in situ calibration in surface code based quantum computation.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731042"}, {"primary_key": "183207", "vector": [], "sparse_vector": [], "title": "NetCrafter: Tailoring Network Traffic for Non-Uniform Bandwidth Multi-GPU Systems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Multiple Graphics Processing Units (GPUs) are being integrated into systems to meet the computing demands of emerging workloads. To continuously support more GPUs in a system, it is important to connect them efficiently and effectively. To this end, emerging multi-GPU systems are adopting a hierarchical approach – a group of GPUs with high affinity are connected with higher-bandwidth networks, while multiple groups of GPUs are connected with lower-bandwidth networks to support the scaling of GPUs. Unfortunately, such a non-uniform bandwidth configuration leads to significant performance bottlenecks, especially across lower-bandwidth networks. We present NetCrafter, a combination of novel approaches to deal with the network traffic. NetCrafter is based on three observations: a) not all flits in the network fully utilize the network bandwidth, b) not all requested flits are even necessary – they are requested in the hope that their data might be useful later, c) some flits are more latency-sensitive than others and must be prioritized in the network. NetCrafter leverages these observations to reduce the network traffic by stitching compatible flits that are partly filled, and trimming the number of flits by not fetching flits that are unnecessary. NetCrafter also effectively manages network traffic by sequencing flits such that latency-sensitive flits reach their destinations faster. Although our proposed techniques are generic and can be applied to any network, they are especially useful in alleviating the bottlenecks presented by lower-bandwidth networks connecting multiple groups of GPUs. Overall, NetCrafter significantly improves multi-GPU performance, thereby contributing to efficient scaling of GPU-based systems.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731040"}, {"primary_key": "183208", "vector": [], "sparse_vector": [], "title": "Topology-Aware Virtualization over Inter-Core Connected Neural Processing Units.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Chen", "<PERSON><PERSON>"], "summary": "With the rapid development of artificial intelligence (AI) applications, an emerging class of AI accelerators, termed Inter-core Connected Neural Processing Units (NPU), has been adopted in both cloud and edge computing environments, like Graphcore IPU, Tenstorrent, etc. Despite their innovative design, these NPUs often demand substantial hardware resources, leading to suboptimal resource utilization due to the imbalance of hardware requirements across various tasks. To address this issue, prior research has explored virtualization techniques for monolithic NPUs, but has neglected inter-core connected NPUs with the hardware topology. This paper introduces vNPU, the first comprehensive virtualization design for inter-core connected NPUs, integrating three novel techniques: (1) NPU route virtualization, which redirects instruction and data flow from virtual NPU cores to physical ones, creating a virtual topology; (2) NPU memory virtualization, designed to minimize translation stalls for SRAM-centric and NoC-equipped NPU cores, thereby maximizing the memory bandwidth; and (3) Best-effort topology mapping, which determines the optimal mapping from all candidate virtual topologies, balancing resource utilization with end-to-end performance. We have developed a prototype of vNPU on both an FPGA platform (Chipyard+FireSim) and a simulator (DCRA). Evaluation results demonstrate that when executing multiple NPU workloads on virtual NPUs, vNPU achieves performance improvements of up to 1.92x and 1.28x for the Transformer and ResNet models, respectively, in comparison to the MIG-based virtualization method. Furthermore, the hardware performance overhead associated with the virtualization itself is minimal, incurring less than 1% reduction in end-to-end performance.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731002"}, {"primary_key": "183209", "vector": [], "sparse_vector": [], "title": "WindServe: Efficient Phase-Disaggregated LLM Serving with Stream-based Dynamic Scheduling.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Huang", "<PERSON><PERSON>", "Sicheng Liang", "Ming Yan", "<PERSON><PERSON>"], "summary": "Existing large language model (LLM) serving systems typically batch the compute-bound prefill and I/O-bound decoding phases together. This co-location approach not only leads to significant interference between the two phases but also limits resource allocation and placements. To address these limitations, recent work proposes disaggregating the prefill and decoding phases to enhance performance. However, these works often rely on coarse-grained static scheduling strategies, resulting in imbalanced and insufficient resource utilization. For instance, compute resources for the prefill phase may be overloaded while those for the decoding phase remain idle, resulting in performance bottlenecks. In this paper, we propose WindServe, an efficient phase dis-aggregated LLM serving system that leverages stream-based, fine-grained dynamic scheduling to enhance resource utilization and performance. WindServe features a global scheduler that monitors compute and memory resource usage to dynamically orchestrate cross-phase jobs, effectively reducing queuing delay and KV cache swapping overhead. We also introduce a stall-free rescheduling strategy to saturate the memory resources while minimizing the scheduling overhead from KV cache transfers. Furthermore, we design a stream-based approach to mitigate interference between prefill and decoding jobs. Our evaluation demonstrates that WindServe achieves remarkable stability and SLO attainment under high-load scenarios, outperforming state-of-the-art phase-disaggregated LLM serving systems by delivering a 4.28 × improvement in TTFT median latency and a 1.5 × reduction in TPOT P99 latency.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3730999"}, {"primary_key": "183210", "vector": [], "sparse_vector": [], "title": "Heliostat: Harnessing Ray Tracing Accelerators for Page Table Walks.", "authors": ["Yuan Feng", "<PERSON><PERSON>", "<PERSON><PERSON>", "Won Woo Ro", "<PERSON><PERSON><PERSON>"], "summary": "This paper introduces Heliostat, which enhances page translation bandwidth on GPUs by harnessing underutilized ray tracing accelerators (RTAs). While most existing studies focused on better utilizing the provided translation bandwidth, this paper introduces a new opportunity to fundamentally increase the translation bandwidth. Instead of overprovisioning the GPU memory management unit (GMMU), Heliostat repurposes the existing RTAs by leveraging the operational similarities between ray tracing and page table walks. Unlike earlier studies that utilized RTAs for certain workloads, Heliostat democratizes RTA for supporting any workloads by improving virtual memory performance. Heliostat+ optimizes Heliostat by handling predicted future address translations proactively. Heliostat outperforms baseline and two state-of-the-arts by 1.93 ×, 1.92 ×, and 1.66 ×. Heliostat+ further speeds up Heliostat by 1.23 ×. Compared to an overprovisioned comparable solution, Heliostat occupies only 1.53% of the area and consumes 5.8% of the power.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731011"}, {"primary_key": "183211", "vector": [], "sparse_vector": [], "title": "Magellan: A High-Performance Loop-Guided Prefetcher for Indirect Memory Access.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Graph analytics and sparse linear algebra applications heavily rely on indirect memory access (IMA). IMAs are characterized by poor temporal and spatial locality, which causes frequent high-latency DRAM accesses. While dedicated hardware prefetchers for IMA have been explored, they target narrow access patterns and tend to introduce significant hardware complexity. Software prefetching offers a promising alternative, leveraging compiler analysis to prefetch indirection patterns. However, existing software prefetchers struggle with sparse applications due to limited loop iterations and complex IMA patterns across nested loops. We propose Magellan, a novel loop-guided software prefetcher designed to detect and schedule IMA prefetches efficiently. Magellan introduces two key innovations: (1) extracting dependence graphs across loop levels to detect complex IMA patterns and (2) capturing inner-outer loop semantics to prefetch for both current and future iterations. We evaluate <PERSON>gellan on 14 memory-intensive benchmarks using real-world datasets from social networks and web graphs. Compared to the best existing IMA software prefetcher, <PERSON><PERSON><PERSON> reduces cache misses by 25% and dynamic instruction counts by 14% on average. This results in a 1.14 × average speedup, with performance gains of up to 1.41 ×.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731054"}, {"primary_key": "183212", "vector": [], "sparse_vector": [], "title": "SpecASan: Mitigating Transient Execution Attacks Using Speculative Address Sanitization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Transient execution attacks (TEAs), such as <PERSON>pect<PERSON> and Melt<PERSON>, exploit speculative execution to leak sensitive data through residual microarchitectural state. Traditional defenses often incur high performance and hardware costs by delaying speculative execution or requiring additional shadow structures and dynamic information flow tracking. In contrast, our approach models these attacks as violations of software-defined security contracts and enforces these contracts in hardware using existing features. We introduce Speculative Address Sanitization (SpecASan), which leverages ARM’s Memory Tagging Extension (MTE) to extend memory safety protection from the committed path to the speculative path. When a speculative access does not pass the MTE tag comparison, this access is delayed until speculation resolves. This ensures that only validated accesses affect the microarchitectural state while preserving the performance benefits of speculation. When combined with Control-Flow Integrity (CFI) enforcement mechanisms, already supported by some hardware implementations, our evaluation shows that SpecASan effectively mitigates a broad class of transient execution attacks, including Spectre and Microarchitectural Data Sampling (MDS). Furthermore, SpecASan achieves this with low performance overhead and minimal hardware complexity, highlighting its practicality and efficiency.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731119"}, {"primary_key": "183213", "vector": [], "sparse_vector": [], "title": "NUPEA: Optimizing Critical Loads on Spatial Dataflow Architectures via Non-Uniform Processing-Element Access.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Data movement is the dominant energy, performance, and scalability bottleneck in modern architectures. Systems have tackled data movement by distributing data, e.g., via non-uniform memory access (NUMA) architectures. However, to reduce data movement, these architectures must identify critical data and place it closer to compute. Clever data placement is complex and often ineffective. Spatial dataflow architectures (SDAs) present a new opportunity to tackle data movement. SDAs distribute program instructions across a spatial fabric of processing elements (PEs). On large SDAs, some PEs are necessarily closer to memory than others, giving rise to non-uniform processing-element access (NUPEA). Clever instruction placement can thus reduce data movement by, e.g., placing critical loads close to memory. This paper introduces NUPEA and contrasts it with prior data-centric approaches to scaling data movement. We find that it is often easier for the compiler to identify critical loads than the data they access, making NUPEA applicable where NUMA is not. We present simple architecture and compiler optimizations for NUPEA and implement them on the Monaco SDA architecture and effcc compiler, both industry products by Efficient Computer. On Monaco, across a range of important kernels, NUPEA yields an avg 28% speedup over a uniform-PE-access (UPEA) SDA and an avg 20% speed over a UPEA SDA with NUMA.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731061"}, {"primary_key": "183214", "vector": [], "sparse_vector": [], "title": "Avant-Garde: Empowering GPUs with Scaled Numeric Formats.", "authors": ["<PERSON><PERSON><PERSON> Gil", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Won Woo Ro", "<PERSON><PERSON>"], "summary": "The escalating computational and memory demands of deep neural networks have outpaced chip density improvements, making arithmetic density a key bottleneck for GPUs. Scaled numeric formats, such as FP8 and Microscaling (MX), improve arithmetic density by applying adaptive scaling factors across varying block sizes and multiple scaling hierarchies. Unfortunately, supporting diverse scaled numeric formats often requires GPUs to rely on software-based implementations, increasing instruction and register overhead and degrading performance. We propose Avant-Garde, a GPU microarchitecture that natively supports diverse scaled numeric formats by converting them into a consistent single-level internal representation. Avant-Garde integrates an Operand Transformer, a hardware module that dynamically flattens multi-level scaling formats into single-level internal representations, a novel Tensor Core, and an optimized data layout to eliminate instruction and register overhead. Our evaluations show that Avant-Garde achieves up to 74% higher throughput and 44% lower execution time, while maintaining accuracy within 0.2% compared to conventional GPUs.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731100"}, {"primary_key": "183215", "vector": [], "sparse_vector": [], "title": "Single Spike Artificial Neural Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Spiking neural networks (SNNs) circumvent the need for large scale arithmetic using techniques inspired by biology. However, SNNs are designed with fundamentally different algorithms from ANNs, which have benefited from a rich history of theoretical advances and an increasingly mature software stack. In this paper we explore the potential of a new technique that lies between these two approaches, one that can leverage the software and system level optimizations of ANNs while utilizing biologically inspired circuits for energy efficient computation. The resulting hardware represents the traditional weight of an ANN as nothing more than a delay element and the degree of activation as nothing more than arrival time of a digital signal. Building on these fundamental operations, we can implement complete ANNs through several innovations: spatial and temporal reuse that facilitates classical dataflows, reducing memory system demands for ANN temporal operations; a new noise-tolerant temporal summation operation; novel hybrid digital/temporal memories; and the integration of temporal memory circuits for shepherding inter-layer activations. Using the MLPerf Tiny benchmark suite, we demonstrate how several architectural parameters can impact inference accuracy, that our proposed systolic array can provide 11 × better energy consumption with a 4 × improvement in latency compared to SNNs, and when equipped with temporal memories provides 3.5 × improvements in energy compared to the most aggressive 8-bit digital systolic arrays.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731027"}, {"primary_key": "183216", "vector": [], "sparse_vector": [], "title": "Cassandra: Efficient Enforcement of Sequential Execution for Cryptographic Programs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Constant-time programming is a widely deployed approach to harden cryptographic programs against side channel attacks. However, modern processors often violate the underlying assumptions of standard constant-time policies by transiently executing unintended paths of the program. Despite many solutions proposed, addressing control flow misspeculations in an efficient way without losing performance is an open problem. In this work, we propose Cassandra, a novel hardware/software mechanism to enforce sequential execution for constant-time cryptographic code in a highly efficient manner. <PERSON> explores the radical design point of disabling the branch predictor and recording-and-replaying sequential control flow of the program. Two key insights that enable our design are that (1) the sequential control flow of a constant-time program is mostly static over different runs, and (2) cryptographic programs are loop-intensive and their control flow patterns repeat in a highly compressible way. These insights allow us to perform an upfront branch analysis that significantly compresses control flow traces. We add a small component to a typical processor design, the Branch Trace Unit, to store compressed traces and determine fetch redirections according to the sequential model of the program. Despite providing a strong security guarantee, <PERSON> counterintuitively provides an average \\( 1.85\\% \\) speedup compared to an unsafe baseline processor, mainly due to enforcing near-perfect fetch redirections.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731048"}, {"primary_key": "183217", "vector": [], "sparse_vector": [], "title": "LightNobel: Improving Sequence Length Limitation in Protein Structure Prediction Model via Adaptive Activation Quantization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Recent advances in Protein Structure Prediction Models (PPMs), such as AlphaFold2 and ESMFold, have revolutionized computational biology by achieving unprecedented accuracy in predicting three-dimensional protein folding structures. However, these models face significant scalability challenges, particularly when processing proteins with long amino acid sequences (e.g., sequence length > 1,000). The primary bottleneck that arises from the exponential growth in activation sizes is driven by the unique data structure in PPM, which introduces an additional dimension that leads to substantial memory and computational demands. These limitations have hindered the effective scaling of PPM for real-world applications, such as analyzing large proteins or complex multimers with critical biological and pharmaceutical relevance. In this paper, we present LightNobel, the first hardware-software co-designed accelerator developed to overcome scalability limitations on the sequence length in PPM. At the software level, we propose Token-wise Adaptive Activation Quantization (AAQ), which leverages unique token-wise characteristics, such as distogram patterns in PPM activations, to enable fine-grained quantization techniques without compromising accuracy. At the hardware level, LightNobel integrates the multi-precision reconfigurable matrix processing unit (RMPU) and versatile vector processing unit (VVPU) to enable the efficient execution of AAQ. Through these innovations, LightNobel achieves up to 8.44 ×, 8.41 × speedup and 37.29 ×, 43.35 × higher power efficiency over the latest NVIDIA A100 and H100 GPUs, respectively, while maintaining negligible accuracy loss. It also reduces the peak memory requirement up to 120.05 × in PPM, enabling scalable processing for proteins with long sequences.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731006"}, {"primary_key": "183218", "vector": [], "sparse_vector": [], "title": "Fair-CO2: Fair Attribution for Cloud Carbon Emissions.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Fair-CO2 is a system for fairly attributing operational and embodied carbon in cloud data centers to user workloads. It leverages the Shapley value, a game theory solution for fair shared cost attribution with theoretical fairness guarantees. We propose the standard Shapley value solution as a ground truth for attribution in cloud data centers, addressing two key gaps in existing carbon attribution methods that lead to unfair attributions: the effect of dynamic demand on embodied carbon, and interference effects in colocated scenarios. However, the computational cost of the Shapley value solution scales exponentially with the number of workloads and becomes intractable for large systems. Fair-CO2 addresses the scalability challenges of the Shapley value while preserving its fairness benefits via two core components: demand-aware embodied carbon attribution and interference-aware resource cost attribution. Using Monte Carlo simulations of workload schedules and colocation scenarios, we show that Fair-CO2 can approximate the ground truth Shapley attribution solution at scale. We also show how users, once provided a fair way of estimating their workload carbon footprint, can dynamically optimize workload deployment for carbon savings.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731023"}, {"primary_key": "183219", "vector": [], "sparse_vector": [], "title": "Telos: A Dataflow Accelerator for Sparse Triangular Solver of Partial Differential Equations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Partial Differential Equations (PDEs) serve as the backbone of numerous scientific problems. Their solutions often rely on numerical methods, which transform these equations into large, sparse systems of linear equations. These systems, solved with iterative methods, exhibit structured sparsity patterns when derived from stencil-based numerical schemes. In preconditioned solvers, the sparse triangular solve procedure, SpTRSV, usually dominates the entire execution due to its loop-carried dependencies. Optimizing SpTRSV requires extracting parallelism from dependent computations. However, prior works have struggled to achieve both high parallelism and data locality, leading to suboptimal performance. We propose Telos, a dataflow accelerator for SpTRSV that exploits structured sparsity patterns in PDE solving. The dataflow execution leverages stencil patterns, efficiently utilizing pipeline parallelism to resolve data dependencies with minimal overhead. We tackle the challenge of complex data dependencies by proposing a plane-parallel pipelining technique that maps computations onto processing elements (PEs) while preserving data locality. A cross-plane communication aggregation technique is developed to streamline data transfers into a systolic manner. Our accelerator features effective pipelining of dependent computations and overlapping of computations with memory accesses. Experiments demonstrate that Telos delivers average speedups of 61 ×, 8 ×, and 11 × over CPUs, GPUs, state-of-the-art accelerator, respectively.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731078"}, {"primary_key": "183220", "vector": [], "sparse_vector": [], "title": "WarmCache: Exploiting STT-RAM Cache for Low-Power Intermittent Systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper introduces WarmCache, an optimized STT-RAM cache design with relaxed non-volatility, for an energy harvesting system (EHS) to avoid such compulsory misses across power failure. The key insight is that if the retention time of a cache is longer than a power outage period, the cache contents can be preserved, thereby preventing compulsory misses. Based on this insight, WarmCache leverages a STT-RAM cache with reduced thermal stability to preserve non-volatility during power outages while not persisting any data. To mitigate retention failure that may occur in the relaxed STT-RAM cache, WarmCache lets its compiler partition program into a series of regions and conducts region-level error correction. At each region boundary, WarmCache verifies the execution of the region by scrubbing updated cache lines and re-executes it if any multi-bit error is detected therein. For optimization, WarmCache compiler introduces a novel region formation technique that adjusts the size of each region to match the scrubbing interval. This is achieved through region stitching for combining shorter regions and region splitting for dividing longer regions. Our experiments demonstrate that WarmCache manages to avoid compulsory cache misses and improves the performance by 1.3 ∼ 1.4x on average compared to the state-of-the-art cache design for EHS.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731049"}, {"primary_key": "183221", "vector": [], "sparse_vector": [], "title": "SEAL: A Single-Event Architecture for In-Sensor Visual Localization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Image sensors have low costs and broad applications, but the large data volume they generate can result in significant energy and latency overheads during data transfer, storage, and processing. This paper explores how shifting from traditional binary encoding to delay-based codes early on can address these inefficiencies, enabling keypoint detection and tracking within digital pixel sensors. The result is SEAL, a Single-Event Architecture for In-Sensor Localization. SEAL optimizes the entire pipeline between the pixel array and the sensor-processor interface by introducing a temporal processor co-designed with analog-to-time converters, followed by a custom heavily quantized frontend processor. Its implementation is fully digital, relying on off-the-shelf CMOS cells and EDA tools, and adheres to race logic’s single-wire-per-variable and single-event-per-wire policies to maximize energy and area efficiency wherever possible. Our evaluation—combining analog and digital simulations, FPGA prototyping, and an end-to-end system analysis incorporating a host processor for visual inertial odometry (VIO) backend tasks—demonstrates a 16–61 × reduction in the latency of keypoint detection and tracking compared to software baselines running on the host processor, and a 7 × reduction in energy consumption compared to a standard digital pixel sensor without processing capabilities. Meanwhile, SEAL preserves robust tracking accuracy: on the EuRoC dataset, the average root mean square absolute trajectory error decreases by 1.0 cm for HybVIO and increases by just 0.3 cm for VINS-Mono compared to their original implementations.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731034"}, {"primary_key": "183222", "vector": [], "sparse_vector": [], "title": "Dadu<PERSON>Corki: Algorithm-Architecture Co-Design for Embodied AI-powered Robotic Manipulation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Feng Yan", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Embodied AI robots have the potential to fundamentally improve the way human beings live and manufacture. Continued progress in the burgeoning field of using large language models to control robots depends critically on an efficient computing substrate, and this trend is strongly evident in manipulation tasks. In particular, today’s computing systems for embodied AI robots for manipulation tasks are designed purely based on the interest of algorithm developers, where robot actions are divided into a discrete frame basis. Such an execution pipeline creates high latency and energy consumption. This paper proposes Corki, an algorithm-architecture co-design framework for real-time embodied AI-powered robotic manipulation applications. We aim to decouple LLM inference, robotic control, and data communication in the embodied AI robots’ compute pipeline. Instead of predicting action for one single frame, <PERSON><PERSON> predicts the trajectory for the near future to reduce the frequency of LLM inference. The algorithm is coupled with a hardware that accelerates transforming trajectory into actual torque signals used to control robots and an execution pipeline that parallels data communication with computation. <PERSON><PERSON> largely reduces LLM inference frequency by up to 5.1 ×, resulting in up to 5.9 × speed up. The success rate improvement can be up to 13.9%.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731099"}, {"primary_key": "183223", "vector": [], "sparse_vector": [], "title": "TRACI: Network Acceleration of Input-Dynamic Communication for Large-Scale Deep Learning Recommendation Model.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Le Qin", "<PERSON><PERSON><PERSON>", "Yang<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Large-scale deep learning recommendation models (DLRMs) rely on embedding layers with terabyte-scale embedding tables, which present significant challenges to memory capacity. In addition, these embedding layers exhibit sparse and random data access patterns, which demand high memory bandwidth. Multi-GPU systems provide a promising solution, allowing for the scaling of both memory and aggregated bandwidth. However, network communication bandwidth becomes a bottleneck for multi-GPU DLRM systems. Overcoming the communication bottleneck is crucial to unlocking the potential of multi-GPU systems for efficient and high-performance DLRM training. This paper introduces TRACI, an in-network acceleration architecture designed to optimize the communication operator in embedding layers: Aggregation. While in-network acceleration has proven successful for the All-Reduce communication collective, existing solutions do not directly apply to Aggregation due to two key challenges. Firstly, existing multi-GPU shared memory operations are designed for point-to-point communication and do not allow the network to proactively optimize communication. Secondly, in Aggregation, data transfer patterns are dynamic and dependent on input, demanding the network to dynamically discover and exploit message connections on-the-fly. To address these challenges, we propose a solution that involves a novel network transaction and switch hardware design. We introduce a new network transaction that augments messages with input reuse and output reuse identifications, and can empower the network to proactively reduce data transfers. Additionally, we present an in-switch cache and a reduction table structure, effectively harnessing input and output reuse opportunities. Using these innovations, TRACI achieves up to 4.04 × and an average of 3.12 × speedup to Aggregation for a 64-GPU system.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731105"}, {"primary_key": "183224", "vector": [], "sparse_vector": [], "title": "AQB8: Energy-Efficient Ray Tracing Accelerator through Multi-Level Quantization.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Tsung Tai Yeh"], "summary": "Ray tracing (RT) is a rendering technique that produces high-fidelity images by simulating how light physically interacts with objects in a scene. This realism comes at a high computational and memory cost, largely driven by the need to find numerous ray-object intersections. To accelerate this process, scenes are typically structured using bounding volume hierarchy (BVH) trees, where objects are grouped within bounding boxes to minimize the number of required intersection tests. Specialized hardware, known as RT accelerators, accelerates BVH processing to boost computational speed, yet memory traffic persists as a major bottleneck, largely due to the bandwidth consumed by standard 32-bit floating-point (FP32) bounding boxes. Consequently, previous work has aimed to reduce memory traffic by compressing these boxes into low-bit (e.g., 8-bit) representations. However, existing compression techniques typically require decompressing bounding boxes back to FP32 for intersection tests, thus failing to eliminate the computational burden and energy cost associated with complex FP32 arithmetic during BVH traversal. This work introduces AQB8, an RT accelerator designed to operate on a quantized BVH tree constructed using a novel multi-level quantization technique. This approach enables RT to operate directly on low-bit integers using simpler, area-efficient hardware units, thereby drastically reducing the need for FP32 arithmetic during BVH traversal while mitigating overheads associated with reduced precision. As a result, across various scenes, AQB8 achieves a 70% reduction in DRAM accesses, a 49% reduction in energy consumption, a 27% hardware area reduction, and a 1.82x performance speedup over modern GPU RT accelerators. Our code is publicly available at https://github.com/nycu-caslab/AQB8.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731104"}, {"primary_key": "183225", "vector": [], "sparse_vector": [], "title": "InfiniMind: A Learning-Optimized Large-Scale Brain-Computer Interface.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Brain-computer interfaces (BCIs) provide an interactive closed-loop connection between the brain and a computer. By employing signal processors implanted within the brain, BCIs are driving innovations across various fields in neuroscience and medicine. Recent studies highlight the need to integrate non-volatile memories (NVMs) into the implanted system for large-scale applications. At the same time, they emphasize the importance of continual learning within the system to address non-stationarities in the recorded signals. This work is the first to address the performance and lifetime issues of deploying learning on NVM-assisted BCI systems. To reduce excessive write overhead associated with learning support, we propose four optimization schemes tailored for BCI workloads. First, update filtering minimizes unnecessary writes by leveraging the sparse and recurring nature of BCI signals. Second, delta buffering exploits temporal locality inherent in BCI signals to minimize NVM writes. Third, out-of-place flushing reduces write amplification by packing multiple sub-page updates into a single page write. Fourth, waveform compression decreases the volume of written data by exploiting the structural characteristics of neural signals. We implement these optimizations in a memory controller and integrate it into the state-of-the-art NVM-assisted BCI system, realizing an end-to-end learning-optimized system. Evaluation results show that our system improves performance and lifetime by 5.39 × and 23.52 ×, respectively, on representative continual learning algorithms.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731067"}, {"primary_key": "183226", "vector": [], "sparse_vector": [], "title": "UPP: Universal Predicate Pushdown to Smart Storage.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Dohyun Park", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Yongjoo Park"], "summary": "In large-scale analytics, in-storage processing (ISP) can significantly boost query performance by letting ISP engines (e.g., FPGAs) pre-select only the relevant data before sending them to databases. This reduces the amount of not only data transfer between storage and host, but also database computation, facilitating faster query processing. However, existing ISP solutions cannot effectively support a wide range of modern analytical queries because they only support simple combinations of frequently used operators (e.g., =, <), particularly on fixed-length columns. As modern databases allow filter predicates to include numerous operators/functions (e.g., dateadd) compatible with diverse data formats (and their complex combinations), it becomes more challenging for existing approaches to accelerate such queries efficiently. To address the limitations, we propose a new ISP approach, called Universal Predicate Pushdown (UPP), that can accelerate modern analytical databases, leveraging hardware/software co-design for a high level of flexibility. Our core insight is that instead of programming for individual filter operators/functions, we should devise a compact instruction set architecture (ISA) tailored explicitly for predicate pushdown. The software (i.e., database) layer recognizes and compiles various general filters (called a universal predicate) to a set of UPP-compliant instructions, which are then processed efficiently by FPGA using bitwise comparisons, leveraging lightweight metadata. In our experiments with a 100 GB TPC-H dataset, UPP running on SmartSSD could speed up Spark’s end-to-end query performance by 1.2 × –7.9 × without changing input data formats.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731005"}, {"primary_key": "183227", "vector": [], "sparse_vector": [], "title": "RAGO: Systematic Performance Optimization for Retrieval-Augmented Generation Serving.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Retrieval-augmented generation (RAG) is emerging as a popular approach for reliable LLM serving. However, efficient RAG serving remains an open challenge due to the rapid emergence of many RAG variants and the substantial differences in workload characteristics across them. This paper makes three fundamental contributions to advancing RAG serving. First, we introduce RAGSchema, a structured abstraction that captures the wide range of RAG algorithms, serving as a foundation for performance optimization. Second, we analyze several representative RAG workloads with distinct RAGSchema, revealing significant performance variability across these workloads. Third, to address this variability and meet diverse performance requirements, we propose RAGO (Retrieval-Augmented Generation Optimizer), a system optimization framework for efficient RAG serving. RAGO achieves up to a 2 × increase in QPS per chip and a 55% reduction in time-to-first-token latency compared to RAG systems built on LLM-system extensions.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731093"}, {"primary_key": "183228", "vector": [], "sparse_vector": [], "title": "Neo: Towards Efficient Fully Homomorphic Encryption Acceleration using Tensor Core.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shengy<PERSON> Fan", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Fully Homomorphic Encryption (FHE) is an emerging cryptographic technique for privacy-preserving computation, which enables computations on the encrypted data. Nonetheless, the massive computational demands of FHE prevent its further application to real-world workloads. To tackle this problem, several studies focus on the ASIC-based acceleration for FHE. However, the rapid evolution of FHE algorithms poses challenges to the generality of ASIC accelerator design. By contrast, a number of works rely on GPGPUs for FHE accelerations, due to the high parallelism and flexibility provided by GPGPUs. In this work, we propose a GPGPU-based acceleration solution that supports the Cheon-Kim-Kim-Song (CKKS) scheme by further exploiting Tensor Core(TCU) capabilities. In our study, we first analyze the FHE applications based on GPGPUs and emphasize the poor data reuse of some FHE kernels. Subsequently, we highlight the inefficient usage of TCUs due to the unused FP64 components. To address these issues, we propose a novel FHE acceleration solution based on GPGPU named Neo, which features: 1) algorithmic optimizations that transform scalar multiplication and element-wise multiplication into matrix multiplication; 2) data layout optimizations for FHE kernels to optimize for matrix multiplications; 3) Accelerating matrix multiplication with the floating-point components in TCUs to leverage the strengths of various components within the GPU. Experimental results demonstrate that within NVIDIA A100 GPGPU, Neo outperforms TensorFHE by 3.28 ×.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731408"}, {"primary_key": "183229", "vector": [], "sparse_vector": [], "title": "Evaluating Ruche Networks: Physically Scalable, Cost-Effective, Bandwidth-Flexible NoCs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "2-D mesh has been widely used as an on-chip network topology, because of its low design complexity and physical scalability. However, its poor latency and throughput scaling have been well-noted in the past. Previous solutions to overcome its unscalability relied on outdated assumptions that no longer hold true in recent architectures. Concentrated routers make an assumption that low injection rate would cause low conflicts; however, recent manycore processors and accelerators require streaming bandwidth for their data-intensive workloads. Widening the channel width to recover the bisection bandwidth halved by concentration assumes that the underlying architecture can flexibly adapt to the wider channel width; however, it comes with an additional cost associated with wider datapaths, serialization, and additional buffering. Ruche Networks retain all the desirable properties of 2-D mesh to remain physically scalable, yet provide an architecturally flexible and cost-effective mechanism to effortlessly scale up the network performance by adding uniform long-range physical links. While their feasibility in real silicon has been demonstrated, there has not been any detailed evaluation of its network performance, scalability, and energy efficiency. This paper aims to fill the gap in research by providing some insight on design tradeoffs. Using RTL-level implementations, we demonstrate that Ruche Networks are superior to 2-D mesh and torus in terms of power, area efficiency, cycle time and network performance.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731010"}, {"primary_key": "183230", "vector": [], "sparse_vector": [], "title": "DX100: Programmable Data Access Accelerator for Indirection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yu<PERSON> Gu", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Indirect memory accesses frequently appear in applications where memory bandwidth is a critical bottleneck. Prior indirect memory access proposals, such as indirect prefetchers, runahead execution, fetchers, and decoupled access/execute architectures, primarily focus on improving memory access latency by loading data ahead of computation but still rely on the DRAM controllers to reorder memory requests and enhance memory bandwidth utilization. DRAM controllers have limited visibility to future memory accesses due to the small capacity of request buffers and the restricted memory-level parallelism of conventional core and memory systems. We introduce DX100, a programmable data access accelerator for indirect memory accesses. DX100 is shared across cores to offload bulk indirect memory accesses and associated address calculation operations. DX100 reorders, interleaves, and coalesces memory requests to improve DRAM row-buffer hit rate and memory bandwidth utilization. DX100 provides a general-purpose ISA to support diverse access types, loop patterns, conditional accesses, and address calculations. To support this accelerator without significant programming efforts, we discuss a set of MLIR compiler passes that automatically transform legacy code to utilize DX100. Experimental evaluations on 12 benchmarks spanning scientific computing, database, and graph applications show that DX100 achieves performance improvements of 2.6 × over a multicore baseline and 2.0 × over the state-of-the-art indirect prefetcher.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731015"}, {"primary_key": "183231", "vector": [], "sparse_vector": [], "title": "EOD: Enabling Low Latency GNN Inference via Near-Memory Concatenate Aggregation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "As online services based on graph databases increasingly integrate with machine learning, serving low-latency Graph Neural Network (GNN) inference for individual requests has become a critical challenge. Real-time GNN inference services operate in an inductive setup, which can handle newly added, previously unseen nodes and their edges. In this setup, the system must prepare the computational graph and input node features for target nodes, followed by GNN inference using the given input data. However, the workflow of a GNN serving system presents two key challenges that hinder low-latency inference. The first challenge arises from the extensive preparation step, which involves heavy memory access in host memory and significant data I/O to devices, constituting the largest portion of end-to-end inference latency. The second challenge is the well-known neighborhood explosion problem in GNN research. As the receptive field for target nodes increases exponentially with the number of layers, this issue exacerbates overall latency. To address these challenges, we propose a Near-Memory Processing (NMP) based low-latency GNN inference serving system named EOD. To ensure low-latency real-time GNN service, we co-design the algorithm and hardware to tackle the aforementioned issues. First, to mitigate the neighborhood explosion problem, we propose a precomputation method for the training node set, reducing memory access and computational complexity from exponential to linear growth. Additionally, we introduce a concatenated ZVC compression method to minimize the overhead of storing precomputed hidden features. Finally, to alleviate heavy host-side memory access and data I/O, we design an NMP architecture that enables efficient aggregation on concatenated ZVC-compressed data. As a result, EOD achieves a geometric mean of 981.1 × and 912.0 × aggregation speedup over the baseline and the existing architecture for GNN aggregation. Additionally, EOD achieves a geometric mean of 17.9 ×, and up to 74.3 × end-to-end latency speedup over the GPU baseline.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731083"}, {"primary_key": "183232", "vector": [], "sparse_vector": [], "title": "QR-Map: A Map-Based Approach to Quantum Circuit Abstraction for Qubit Reuse Optimization.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Won Woo Ro"], "summary": "Recent advances in quantum computing introduce the ability to reuse qubits through mid-circuit measurements, thereby enhancing the efficiency of quantum devices with limited computational resources. However, identifying optimal reuse opportunities in quantum circuits remains challenging due to the intricate dependencies between quantum gates. Existing frameworks address this by either directly searching for reuse opportunities or converting circuits into directed acyclic graphs (DAGs). Unfortunately, these frameworks may require exponential search complexity or may not always ensure optimal results due to their non-deterministic property. To overcome these challenges, we propose QR-Map (Qubit Reuse Map), a map-based framework that abstracts computational dependencies for efficient qubit reuse. By extracting and aligning two-qubit gates, QR-Map facilitates dependency detection and ensures qubit savings without incurring excessive idle time. This approach achieves an optimal balance between gate serialization depth and crosstalk reduction. Evaluations with various quantum circuit benchmarks demonstrate that quantum circuits optimized with QR-Map achieve average reductions of 20% in qubit usage, 25% in circuit depth, and 22% in SWAP insertions compared to those optimized with the state-of-the-art framework.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731020"}, {"primary_key": "183233", "vector": [], "sparse_vector": [], "title": "NMP-PaK: Near-Memory Processing Acceleration of Scalable De Novo Genome Assembly.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "De novo assembly enables investigations of unknown genomes, paving the way for personalized medicine and disease management. However, it faces immense computational challenges arising from the excessive data volumes and algorithmic complexity. While state-of-the-art de novo assemblers utilize distributed systems for extreme-scale genome assembly, they demand substantial computational and memory resources. They also fail to address the inherent challenges of de novo assembly, including a large memory footprint, memory-bound behavior, and irregular data patterns stemming from complex, interdependent data structures. Given these challenges, de novo assembly merits a custom hardware solution, though existing approaches have not fully addressed the limitations. We propose NMP-PaK, a hardware-software co-designed system that accelerates scalable de novo genome assembly through near-memory processing (NMP). Our channel-level NMP architecture addresses memory bottlenecks while providing sufficient scratchpad space for processing elements. Customized processing elements maximize parallelism while efficiently handling large data structures that are both dynamic and interdependent. Software optimizations include customized batch processing to reduce the memory footprint and hybrid CPU-NMP processing to address hardware underutilization caused by irregular data patterns. NMP-PaK conducts the same genome assembly while incurring a 14 × smaller memory footprint compared to the state-of-the-art de novo assembly. Moreover, NMP-PaK delivers 16 × and 5.7 × performance improvements over the CPU and GPU baselines, respectively, with a 2.4 × reduction in memory operations. Consequently, NMP-PaK achieves 8.3 × greater throughput than state-of-the-art de novo assembly under the same resource constraints, showcasing its superior computational efficiency.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731056"}, {"primary_key": "183234", "vector": [], "sparse_vector": [], "title": "LIA: A Single-GPU LLM Inference Acceleration with Cooperative AMX-Enabled CPU-GPU Computation and CXL Offloading.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The limited memory capacity of single GPUs constrains large language model (LLM) inference, necessitating cost-prohibitive multi-GPU deployments or frequent performance-limiting CPU-GPU transfers over slow PCIe. In this work, we first benchmark recent Intel CPUs with Advanced Matrix Extensions (AMX), including 4th generation (Sapphire Rapids) and 6th generation (Granite Rapids) Xeon Scalable Processors, demonstrating matrix multiplication throughput of 20 TFLOPS and 40 TFLOPS, respectively— comparable to some recent GPUs. These findings unlock more extensive computation offloading to CPUs, reducing CPU-GPU transfers and alleviating throughput bottlenecks compared to prior-generation CPUs. Building on these insights, we design LIA, a single-GPU LLM inference acceleration framework leveraging cooperative AMX-enabled CPU-GPU computation and CXL offloading. LIA systematically offloads computation to CPUs, optimizing both latency and throughput. The framework also introduces a memory-offloading policy that seamlessly integrates affordable CXL memory with DDR memory to enhance performance in throughput-driven tasks. On Saphhire Rapids (Granite Rapids) systems with a single H100 GPU, LIA achieves up to 5.1 × (19 ×) lower latency and 3.7 × (5.1 ×) higher throughput compared to the latest single-GPU offloading framework. Furthermore, LIA deploying CXL offloading yields an additional 1.5 × throughput improvement over LIA using only DDR memory with a 1.8 × increase in maximum batch size (900 → 1.6K).", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731092"}, {"primary_key": "183235", "vector": [], "sparse_vector": [], "title": "Garibaldi: A Pairwise Instruction-Data Management for Enhancing Shared Last-Level Cache Performance in Server Workloads.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Hongju Kal", "Won Woo Ro"], "summary": "Modern CPUs suffer from the frontend bottleneck because the instruction footprint of server workloads exceeds the private cache capacity. Prior works have examined the CPU components or private cache to improve the instruction hit rate. The large footprint leads to significant cache misses not only in the core and faster-level cache but also in the last-level cache (LLC). We observe that even with an advanced branch predictor and instruction prefetching techniques, a considerable amount of instruction accesses descend to the LLC. However, state-of-the-art LLC designs with elaborate data management overlook handling the instruction misses that precede corresponding data accesses. Specifically, when an instruction requiring numerous data accesses is missed, the frontend of a CPU should wait for the instruction fetch, regardless of how much data are present in the LLC. To preserve hot instructions in the LLC, we propose Garibaldi, a novel pairwise instruction-data management scheme. <PERSON><PERSON><PERSON><PERSON> tracks the hotness of instruction accesses by coupling it with that of data accesses and adopts management techniques. On the one hand, this scheme includes a selective protection mechanism that prevents the cache evictions of high-cost instruction cachelines. On the other hand, in the case of unprotected instruction line misses, <PERSON><PERSON><PERSON><PERSON> conservatively issues prefetch requests of the paired data lines while handling those misses. In our experiments, we evaluate <PERSON><PERSON><PERSON><PERSON> with 16 server workloads on a 40-core machine. We also implement <PERSON><PERSON><PERSON><PERSON> on top of a modern LLC design, including Mocking<PERSON>. <PERSON><PERSON><PERSON><PERSON> improves 13.2% and 6.1% of CPU performance on baseline LLC design and Mockingjay, respectively.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731029"}, {"primary_key": "183236", "vector": [], "sparse_vector": [], "title": "AiF: Accelerating On-Device LLM Inference Using In-Flash Processing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Chun", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "While large language models (LLMs) achieve remarkable performance across diverse application domains, their substantial memory demands present challenges, especially on personal devices with limited DRAM capacity. Recent LLM inference engines have introduced SSD offloading for model parameters to reduce memory footprint. However, the highly memory-bound nature of on-device LLMs makes inference speed heavily dependent on read bandwidth, leading to significant performance degradation due to the limited bandwidth of SSDs. In this paper, we propose an in-flash processing solution for on-device LLM, called Accelerator-in-Flash (AiF), which integrates matrix-vector multiplication (GEMV) operations directly into flash chips. By enabling in-flash GEMV operations, AiF leverages the high internal bandwidth of flash chips without being constrained by the limited external bandwidth. Building on this core structure, AiF employs two novel flash read techniques that were specifically optimized for reading LLM parameters stored in flash memory. AiF achieves a 4x boost in internal read bandwidth during inference with minimal implementation overhead, thanks to its streamlined error correction process. Evaluations on eight real-world LLMs reveal that AiF provides a 14.6x throughput improvement compared to baseline SSD offloading schemes. Furthermore, AiF surpasses in-memory inference, delivering 1.4x higher throughput with a significantly reduced memory footprint.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731073"}, {"primary_key": "183237", "vector": [], "sparse_vector": [], "title": "Debunking the CUDA Myth Towards GPU-based AI Systems: Evaluation of the Performance and Programmability of Intel&apos;s Gaudi NPU for AI Model Serving.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Cho", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Jinseop Im", "<PERSON><PERSON><PERSON>", "<PERSON> <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents a comprehensive evaluation of Intel Gaudi NPUs as an alternative to NVIDIA GPUs, which is currently the de facto standard in AI system design. First, we create microbenchmarks to compare Intel Gaudi-2 with NVIDIA A100, showing that Gaudi-2 achieves competitive performance not only in primitive AI compute, memory, and communication operations but also in executing several important AI workloads end-to-end. We then assess Gaudi NPU’s programmability by discussing several software-level optimization strategies to employ for implementing critical FBGEMM operators and vLLM, evaluating their efficiency against GPU-optimized counterparts. Results indicate that Gaudi-2 achieves energy efficiency comparable to A100, though there are notable areas for improvement in terms of software maturity. Overall, we conclude that, with effective integration into high-level AI frameworks, Gaudi NPUs could challenge NVIDIA GPU’s dominance in the AI server market, though further improvements are necessary to fully compete with NVIDIA’s robust software ecosystem.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731050"}, {"primary_key": "183238", "vector": [], "sparse_vector": [], "title": "Reinforcement Learning-Guided Graph State Generation in Photonic Quantum Computers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The photonic quantum computer (PQC) is an emerging and promising quantum computing paradigm that has gained momentum in recent years. In PQC, computations are executed by performing measurements on photons in graph states (i.e., a collection of entangled photons). The graph state generation process is fulfilled by applying a sequence of quantum gates to quantum emitters, referred to as the “generation sequence”. In a generation sequence, i) the time required to complete the generation sequence, ii) the number of quantum emitters used, and iii) the number of CZ gates performed between emitters greatly affect the fidelity of the generated graph state. In this paper, we propose RLGS (Reinforcement Learning-guided Graph State generation), a novel compilation framework to identify optimal generation sequences that optimize the three fidelity metrics. Experimental results show that RLGS achieves an average reduction in generation time of 31.1%, 49.6%, and 57.5% for small, medium, and large graph states compared to the baseline. Additionally, the reductions in the number of quantum emitters are 13.9%, 16.7%, and 17.5%, whereas the reductions in the number of CZ gates are 37.7%, 53.4%, and 57.8%, respectively.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731085"}, {"primary_key": "183239", "vector": [], "sparse_vector": [], "title": "Single-Address-Space FaaS with <PERSON><PERSON>.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Function-as-a-Service (FaaS) has emerged as a popular cloud paradigm that simplifies software development and deployment by providing scalable and event-driven function execution without the burden of managing servers. FaaS was originally created with a function as a function semantics to enable standalone microservices with adequately short execution time to meet microsecond-scale service-level objectives (SLOs). Unfortunately, today’s FaaS systems fundamentally suffer from millisecond-level performance bottlenecks that arise from isolating functions in separate address spaces inside containers or microVMs. Prior work has focused on optimizing FaaS performance, but these systems still fall short of meeting microsecond-level SLOs. In this paper, we present Jord, a FaaS system that revives the original function-as-a-function vision of FaaS. <PERSON><PERSON> leverages hardware/software co-design to colocate functions in a single address space with user-level in-process memory isolation, extending the capability of traditional virtual memory. By performing memory isolation and management in nanoseconds, <PERSON><PERSON> enables zero-copy cross-function communication and scalable function dispatch, thereby minimizing FaaS overheads. We demonstrate that <PERSON><PERSON> can meet microsecond-level SLOs for microservice workloads while performing within 16% of an idealized but insecure baseline and delivering over 2 × higher throughput compared to enhanced state-of-the-art systems.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731108"}, {"primary_key": "183240", "vector": [], "sparse_vector": [], "title": "TrioSim: A Lightweight Simulator for Large-Scale DNN Workloads on Multi-GPU Systems.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep Neural Networks (DNNs) have become increasingly capable of performing tasks ranging from image recognition to content generation. The training and inference of DNNs heavily rely on GPUs, as GPUs’ massively parallel architecture delivers extremely high computing capability. With the growing complexity of DNNs and the size of training datasets, training DNNs with a large number of GPUs is becoming a prevalent strategy. Researchers have been exploring how to design software and hardware systems for GPU farms to achieve the best utilization, efficiency, and DNN accuracy during training or inference. However, when designing and deploying such systems, designers usually rely on testing on physical hardware platforms equipped with many GPUs, incurring high costs that are almost prohibitive for system designers to test different configurations and designs, even for highly resourceful companies. While an alternative solution is to test on GPU simulators, they are often too slow for these large-scale systems and depend on profiling details collected from real distributed systems to initiate the simulation. To address these challenges, we present TrioSim, a novel lightweight simulator for DNNs on multi-GPU systems. TrioSim combines performance modeling techniques and simulation methods to achieve high flexibility, high simulation speed, and high accuracy. TrioSim minimizes the required input from users by only relying on operator-level execution traces collected on a single GPU and can simulate new software and hardware designs that involve multiple GPUs connected with complex, asymmetrical interconnects. Completing within seconds, TrioSim predicts the execution time of DNN training on multi-GPU systems with average errors of 2.91%, 4.54%, and 6.82% for data parallelism, tensor parallelism, and pipeline parallelism, respectively.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731082"}, {"primary_key": "183241", "vector": [], "sparse_vector": [], "title": "ANSMET: Approximate Nearest Neighbor Search with Near-Memory Processing and Hybrid Early Termination.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Approximate nearest neighbor search (ANNS) is a fundamental operation in modern vector databases to efficiently retrieve nearby vectors to a given query. On general-purpose computing platforms, ANNS is found not only to be highly memory-bound due to the large amount of high-dimensional vectors to access, but also exhibits very low utilization of the fetched data as many memory accesses and computations are wasted on not-so-nearby vectors. To alleviate these two inefficiencies, we propose a hardware-software co-design that integrates near-data processing architectures with a novel hybrid partial-dimension/bit early termination strategy. Distance calculation and comparison in ANNS are offloaded to the near-data processing units at the memory rank level. As a vector is being gradually fetched from memory, we conservatively estimate a lower bound of its distance to the query using the partially fetched data, e.g., a subset of dimensions and/or partial bits of each element. If this lower bound already exceeds a threshold, we could early terminate to avoid future unnecessary data accesses and computations. In the presence of such irregular early termination execution flow, we further optimize the data layouts within a single memory access and across multiple memory ranks in the system, and handle efficient coordination between the near-data units and the host processor that executes the rest of index traversal and result sorting. With all the above optimizations, our design demonstrates an average 5.26 × speedup of using near-data processing, and another 1.52 × from enabling hybrid early termination on top of it.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731013"}, {"primary_key": "183242", "vector": [], "sparse_vector": [], "title": "HYTE: Flexible Tiling for Sparse Accelerators via Hybrid Static-Dynamic Approaches.", "authors": ["Xintong Li", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Specialized hardware accelerators are widely used for sparse tensor computations. For very large tensors that do not fit in on-chip buffers, tiling is a promising solution to improve data reuse on these sparse accelerators. Nevertheless, existing tiling strategies on sparse accelerators are either purely dynamic and suffering from high design complexity, or purely static and using simple heuristics with insufficient adaptivity. In addition, they have not extensively explored the full design space of tiling to identify the optimal schemes, nor have they supported efficient management of the non-negligible metadata needed for tiling. We propose HYTE, a hybrid static-dynamic framework to enable flexible and efficient tiling on sparse accelerators. HYTE relies on a static offline scheduler to first identify a near-optimal initial tiling scheme through effective and lightweight sampling. The tile size and shape, the dimension iteration order across different tiles, and the buffer allocation policies can all be flexibly configured to adapt to the specific data sparsity patterns. Then at runtime, HYTE supports efficient management of the tiling metadata in both the off-chip memory and the on-chip buffer, as well as a technique of dynamic tuning on the tile shape to ensure high buffer utilization in the presence of highly varying local data patterns. Our evaluation shows that HYTE outperforms state-of-the-art sparse tiling strategies by 3.3 × to 6.2 × on average for diverse sparse matrices.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731044"}, {"primary_key": "183243", "vector": [], "sparse_vector": [], "title": "H2-LLM: Hardware-Dataflow Co-Exploration for Heterogeneous Hybrid-Bonding-based Low-Batch LLM Inference.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Guangyu Sun"], "summary": "Low-batch large language model (LLM) inference has been extensively applied to edge-side generative tasks, such as personal chat helper, virtual assistant, reception bot, private edge server, etc. To efficiently handle both prefill and decoding stages in LLM inference, near-memory processing (NMP) enabled heterogeneous computation paradigm has been proposed. However, existing NMP designs typically embed processing engines into DRAM dies, resulting in limited computation capacity, which in turn restricts their ability to accelerate edge-side low-batch LLM inference. To tackle this problem, we propose H \\( ^\\text{2} \\)-LLM, a Hybrid-bonding-based Heterogeneous accelerator for edge-side low-batch LLM inference. To balance the trade-off between computation capacity and bandwidth intrinsic to hybrid-bonding technology, we propose H \\( ^\\text{2} \\)-LLM’s architecture and extract its architecture design space. We further propose a data-centric dataflow abstraction to fully exploit the heterogeneous architecture’s acceleration opportunities in low-batch LLM inference. Based on the whole design space, we propose a design space exploration (DSE) framework to automatically find out the optimal design. Compared with existing in-die NMP-based heterogeneous accelerators, H \\( ^\\text{2} \\)-LLM achieves 2.72 × geomean speedup and 1.48 × geomean better energy efficiency. H \\( ^\\text{2} \\)-LLM’s data-centric dataflow exploration framework is open-sourced at https://github.com/leesou/H2-LLM-ISCA-2025.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731008"}, {"primary_key": "183244", "vector": [], "sparse_vector": [], "title": "Profile-Guided Temporal Prefetching.", "authors": ["<PERSON><PERSON><PERSON> Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Yongqing Ren", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Temporal prefetching shows promise for handling irregular memory access patterns, which are common in data-dependent and pointer-based data structures. Recent studies introduced on-chip metadata storage to reduce the memory traffic caused by accessing metadata from off-chip DRAM. However, existing prefetching schemes struggle to efficiently utilize the limited on-chip storage. An alternative solution, software indirect access prefetching, remains ineffective for optimizing temporal prefetching. In this work, we propose Prophet—a hardware-software co-designed framework that leverages profile-guided methods to optimize metadata storage management. <PERSON> profiles programs using counters instead of traces, injects hints into programs to guide metadata storage management, and dynamically tunes these hints to enable the optimized binary to adapt to different program inputs. <PERSON> is designed to coexist with existing hardware temporal prefetchers, delivering efficient, high-performance solutions for frequently executed workloads while preserving the original runtime scheme for less frequently executed workloads. <PERSON> outperforms the state-of-the-art temporal prefetcher, <PERSON>ang<PERSON>, by 14.23%, effectively addressing complex temporal patterns where prior profile-guided solutions fall short (only achieving 0.1% performance gain). <PERSON> delivers superior performance across all evaluated workload inputs, introducing negligible profiling, analysis, and instruction overhead.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731070"}, {"primary_key": "183245", "vector": [], "sparse_vector": [], "title": "Forest: Access-aware GPU UVM Management.", "authors": ["<PERSON>", "Yuan Feng", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With GPU unified virtual memory (UVM), CPU and GPU can share a flat virtual address space. UVM enables the GPUs to utilize the larger CPU system memory as an expanded memory space. However, UVM’s on-demand page migration is accompanied by expensive page fault handling overhead. To mitigate such overhead, tree-based neighboring prefetcher (TBNp) has been used by GPUs. TBNp effectively reduces page faults by exploiting locality at multiple levels. However, we observe its access-pattern oblivious design leads to excessive page thrashing and unnecessary migrations. In this paper, we tackle the inefficiencies with a novel access-aware UVM management, Forest. Forest uses a software-hardware codesign to configure the optimal tree prefetchers at runtime based on each data object’s access patterns. With the heterogeneous tree-based prefetching, Forest provides 1.86 × and 1.39 × speedups over the baseline TBNp and state-of-the-art optimization solutions, respectively. Forest also shows a 1.51 × speedup for real-world deep learning models, including CNNs and Transformers.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731047"}, {"primary_key": "183246", "vector": [], "sparse_vector": [], "title": "LightML: A Photonic Accelerator for Efficient General Purpose Machine Learning.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The rapid integration of AI technologies into everyday life across sectors such as healthcare, autonomous driving, and smart home applications requires extensive computational resources, placing strain on server infrastructure and incurring significant costs. We present LightML, the first system-level photonic crossbar design, optimized for high-performance machine learning applications. This work provides the first complete memory and buffer architecture carefully designed to support the high-speed photonic crossbar, achieving over 80% utilization. LightML also introduces solutions for key ML functions, including large-scale matrix multiplication (MMM), element-wise operations, non-linear functions, and convolutional layers. Delivering 325 TOP/s at only 3 watts, LightML offers significant improvements in speed and power efficiency, making it ideal for both edge devices and dense data center workloads.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731053"}, {"primary_key": "183247", "vector": [], "sparse_vector": [], "title": "HeterRAG: Heterogeneous Processing-in-Memory Acceleration for Retrieval-augmented Generation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Liu", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "By integrating external knowledge bases, Retrieval-augmented Generation (RAG) enhances natural language generation for knowledge-intensive scenarios and specialized domains, producing content that is both more informative and personalized. RAG systems typically consist of two fundamental stages: retrieval and generation. The retrieval stage experiences low bandwidth utilization due to its random and irregular memory access patterns. Meanwhile, the generation stage is also constrained by memory bandwidth limitations, which arise from involving a significant number of General Matrix-Vector Multiplications (GEMV) operations. These two stages collectively lead to memory bottlenecks within RAG systems. Recent efforts leverage HBM-based Processing-in-Memory (PIM) to accelerate conventional Large Language Models (LLMs). However, the retrieval stage incurs substantial storage overhead due to the need to maintain large-scale knowledge bases, resulting in a capacity bottleneck. Solely relying on HBM-based PIM in RAG is both costly and insufficient to meet the capacity demands. Fortunately, DIMM-based PIM provides a low-cost, high-capacity alternative that complements HBM. In this work, we propose HeterRAG, a novel heterogeneous PIM acceleration system for RAG. It combines HBM-based PIM and DIMM-based PIM to achieve high performance, energy efficiency, and low hardware cost. HeterRAG uses HBM-based PIM for the generation stage to meet bandwidth needs and DIMM-based PIM for the retrieval stage to satisfy memory capacity requirements. To further improve performance, HeterRAG incorporates three software–hardware co-optimization techniques: locality-aware retrieval, locality-aware generation, and fine-grained parallel pipelining. Experimental results demonstrate that, compared to RAG systems deployed on Intel Xeon CPUs and NVIDIA GPUs, HeterRAG achieves up to 26.5 × higher throughput, up to 27.6 × lower latency, and up to 2.8 × greater energy efficiency.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731089"}, {"primary_key": "183248", "vector": [], "sparse_vector": [], "title": "The Sparsity-Aware LazyGPU Architecture.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "General-Purpose Graphics Processing Units (GPUs) are essential accelerators in data-parallel applications, including machine learning, and physical simulations. Although GPUs utilize fast wavefront context switching to hide memory access latency, memory continues to be a significant bottleneck, limiting overall performance for many important workloads. Current GPU hardware enhancements focus on issuing memory requests in advance to help solve the memory bandwidth bottleneck and improve GPU performance. However, this approach can still be inefficient, leading to hardware contention and suboptimal resource utilization. Instead of issuing memory requests in advance, we take an alternative view on improving GPU performance: lazily issuing memory requests to eliminate memory requests where either (a) the fetched values are zero or (b) they do not affect the result of the executing workload. Building on these insights, we propose LazyGPU, which integrates lazy execution cores with a Zero Cache to eliminate memory requests when all data required by a wavefront is zero. Moreover, LazyGPU utilizes instructions, including multiplication and multiply-add, to eliminate memory requests whose fetched values do not affect the outcomes of these instructions. For example, LazyGPU achieves a 2.18 × speedup compared with the baseline at 60% weight sparsity for the inference of LLaMA 7B.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731009"}, {"primary_key": "183249", "vector": [], "sparse_vector": [], "title": "OptiPIM: Optimizing Processing-in-Memory Acceleration Using Integer Linear Programming.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Processing-in-memory (PIM) accelerators provide superior performance and energy efficiency to conventional architectures by minimizing off-chip data movement and exploiting extensive internal memory bandwidth for computation. However, efficient PIM acceleration requires careful software-hardware mapping that transforms application algorithms into PIM operations and data layout. Unfortunately, existing PIM accelerators adopt manually tuned heuristics or exhaustive search to determine the mappings on PIM accelerators, leading to under-optimized performance and/or long optimization time. In this work, we propose OptiPIM, a novel optimization framework based on Integer Linear Programming (ILP) to efficiently generate the optimal mapping for data-intensive applications on PIM accelerators. The proposed framework adopts a PIM-friendly mapping representation with accurate cost modeling and a concise description of the entire design space, allowing us to formulate an efficient and effective ILP problem and optimize the mapping on PIM architectures. We implement OptiPIM in the open-source MLIR framework, enabling OptiPIM to generate optimized mappings for PyTorch workloads on PIM accelerators. We evaluate widely used machine learning workloads on two state-of-the-art PIM accelerators. Our experiments show that OptiPIM can generate optimal mappings within 4 minutes. Mappings generated by OptiPIM are at least 1.9 × faster than those generated by heuristics.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731041"}, {"primary_key": "183250", "vector": [], "sparse_vector": [], "title": "Dynamic Load Balancer in Intel Xeon Scalable Processor: Performance Analyses, Enhancements, and Guidelines.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The rapid increase in inter-host networking speed has challenged host processing capabilities, as bursty traffic and uneven load distribution among host CPU cores give rise to excessive queuing delays and service latency variances. To cost-efficiently tackle this challenge, the latest Intel Xeon Scalable Processor has integrated an on-chip accelerator, named Dynamic Load Balancer (DLB). It consists of hardware queues, arbiters, and priority-based Quality of Service (QoS) features to maximize load-balancing performance while minimizing host CPU cycle consumption. In this work, we first compare the performance of DLB against popular software-based load balancers with a microbenchmark suite, demonstrating that DLB significantly outperforms them. Yet, DLB still consumes a significant number of host CPU cycles to prepare and enqueue work descriptors for received packets. Second, to eliminate the consumption of host CPU cycles in load balancing, we propose a system architecture/software co-design solution, AccDirect. Specifically, AccDirect leverages the Peer-to-Peer (P2P) communication capability of PCIe devices to enable a direct communication path between DLB and NIC, through which NIC directly enqueues the work descriptors. Our evaluation shows that AccDirect-based DLB offers practically the same performance as conventional DLB while reducing the system-wide power consumption of host CPU by 10%. Compared to the throughput of a commodity hardware-based load balancer for an end-to-end application, that of AccDirect-based DLB is 14–50\\( \\% \\) higher with comparable p99 latency. Lastly, we provide guidelines to make the best use of DLB, which is riddled with a vast configuration space and advanced features, after conducting a comprehensive evaluation.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731026"}, {"primary_key": "183251", "vector": [], "sparse_vector": [], "title": "In-Storage Acceleration of Retrieval Augmented Generation as a Service.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Xu", "<PERSON><PERSON>"], "summary": "Retrieval-augmented generation (RAG) services are rapidly gaining adoption in enterprise settings as they combine information retrieval systems (e.g., databases) with large language models (LLMs) to enhance response generation and reduce hallucinations. By augmenting an LLM’s fixed pre-trained knowledge with real-time information retrieval, RAG enables models to effectively extend their context to large knowledge bases by selectively retrieving only the most relevant information. As a result, RAG provides the effect of dynamic updates to the LLM’s knowledge without requiring expensive and time-consuming retraining. While some deployments keep the entire database in memory, RAG services are increasingly shifting toward persistent storage to accommodate ever-growing knowledge bases, enhance utility, and improve cost-efficiency. However, this transition fundamentally reshapes the system’s performance profile: empirical analysis reveals that the Search & Retrieval phase emerges as the dominant contributor to end-to-end latency. This phase typically involves (1) running a smaller language model to generate query embeddings, (2) executing similarity and relevance checks over varying data structures, and (3) performing frequent, long-latency accesses to persistent storage. To address this triad of challenges, we propose a metamorphic in-storage accelerator architecture that provides the necessary programmability to support diverse RAG algorithms, dynamic data structures, and varying computational patterns. The architecture also supports in-storage execution of smaller language models for query embedding generation while final LLM generation is executed on DGX A100 systems. Experimental results show up to 4.3 × and 1.5 × improvement in end-to-end throughput compared to conventional retrieval pipelines using Xeon CPUs with NVMe storage and A100 GPUs with DRAM, respectively.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731032"}, {"primary_key": "183252", "vector": [], "sparse_vector": [], "title": "Synchronization for Fault-Tolerant Quantum Computers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Quantum Error Correction (QEC) codes store information reliably in logical qubits by encoding them in a larger number of less reliable qubits. The surface code, known for its high resilience to physical errors, is a leading candidate for fault-tolerant quantum computing (FTQC). Logical qubits encoded with the surface code can be in different phases of their syndrome generation cycle, thereby introducing desynchronization in the system. This can occur due to the production of non-Clifford states, dropouts due to fabrication defects, and the use of other QEC codes with the surface code to reduce resource requirements. Logical operations require the syndrome generation cycles of the logical qubits involved to be synchronized. This requires the leading qubit to pause or slow down its cycle, allowing more errors to accumulate before the next cycle, thereby increasing the risk of uncorrectable errors. To synchronize the syndrome generation cycles of logical qubits, we define three policies - Passive, Active, and Hybrid. The Passive policy is the baseline, and the simplest, wherein the leading logical qubits idle until they are synchronized with the remaining logical qubits. On the other hand, the Active policy aims to slow the leading logical qubits down gradually, by inserting short idle periods before multiple code cycles. This approach reduces the logical error rate (LER) by up to 2.4 × compared to the Passive policy. The Hybrid policy further reduces the LER by up to 3.4 × by reducing the synchronization slack and running a few additional rounds of error correction. Furthermore, the reduction in the logical error rate with the proposed synchronization policies enables a speedup in decoding latency of up to 2.2 × with a circuit-level noise model.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3730991"}, {"primary_key": "183253", "vector": [], "sparse_vector": [], "title": "Nyx: Virtualizing dataflow execution on shared FPGA platforms.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Dionisios N. Pnev<PERSON>"], "summary": "As FPGAs become more widespread for improving computing performance within cloud infrastructure, researchers aim to equip them with virtualization features to enable resource sharing in both temporal and spatial domains, thereby improving hardware utilization. Existing multi-tenant solutions focus on task-parallel models, where tasks are assigned to distinct regions to process separate sets of data. However, this model introduces waiting times between dependent and pipelined tasks, leading to longer response times for applications. The root cause is the lack of support for dataflow execution -a key potential of FPGAs and a crucial optimization for applications. Dataflow allows direct data streaming between operators, forming a task-pipelined model that reduces application latency by overlapping task operations within its workflow. This paper presents Nyx, the first system to enable dataflow execution in a task-based virtualized and shared FPGA environment. Nyx enables efficient resource sharing by dividing the FPGA into distinct reconfigurable regions. At its core, Nyx employs virtual FIFOs, independent channels that allow seamless communication between pipelined tasks. Its approach ensures smooth task operation even when the predecessor or successor tasks are not simultaneously scheduled in the FPGA, making them agnostic to their dependencies, communication channels or data locations. An FPGA hypervisor is designed to handle all data dependencies and efficiently dispatch pipelined tasks across regions at high throughput. Nyx outperforms existing state of the art virtualized task-parallel approaches by 1.26x - 8.87x across a series of real-world benchmarks. Furthermore, it reduces response times by 2.8x - 3.28x during low-demand periods, decreasing also deadline violations by up to 76.5%. Under high-demand conditions, Nyx delivers 2x - 2.75x reduction, 34.5% fewer violations, and up to 1.9x reduced tail response time.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731094"}, {"primary_key": "183254", "vector": [], "sparse_vector": [], "title": "LUT Tensor Core: A Software-Hardware Co-Design for LUT-Based Low-Bit LLM Inference.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Naifeng Jing", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Large Language Model (LLM) inference becomes resource-intensive, prompting a shift toward low-bit model weights to reduce the memory footprint and improve efficiency. Such low-bit LLMs necessitate the mixed-precision matrix multiplication (mpGEMM), an important yet underexplored operation involving the multiplication of lower-precision weights with higher-precision activations. Off-the-shelf hardware does not support this operation natively, leading to indirect, thus inefficient, dequantization-based implementations. In this paper, we study the lookup table (LUT)-based approach for mpGEMM and find that a conventional LUT implementation fails to achieve the promised gains. To unlock the full potential of LUT-based mpGEMM, we propose LUT Tensor Core, a software-hardware co-design for low-bit LLM inference. LUT Tensor Core differentiates itself from conventional LUT designs through: 1) software-based optimizations to minimize table precompute overhead and weight reinterpretation to reduce table storage; 2) a LUT-based Tensor Core hardware design with an elongated tiling shape to maximize table reuse and a bit-serial design to support diverse precision combinations in mpGEMM; 3) a new instruction set and compilation optimizations for LUT-based mpGEMM. LUT Tensor Core significantly outperforms existing pure software LUT implementations and achieves a 1.44 × improvement in compute density and energy efficiency compared to previous state-of-the-art LUT-based accelerators.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731057"}, {"primary_key": "183255", "vector": [], "sparse_vector": [], "title": "Hybe: GPU-NPU Hybrid System for Efficient LLM Inference with Million-Token Context Window.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hyunjun Park", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The growth of context window size in large language model (LLM) inference poses a very distinct computational challenge of hardware inefficiency. The inefficiency arises from the computational imbalance during LLM inference between the compute-intensive prefill stage, and memory-intensive decode stage. The predominant inference hardware, GPU, boasts large number of cores to excel in the prefill stage, which processes the entire input context at once, but suffers from hardware underutilization in the decode stage, which iteratively generates one output token at a time. In conventional LLM, batching has been able to alleviate the underutilization by generating multiple tokens of different requests. However, batching becomes infeasible in models with large context windows over 100K tokens because the Key-Value (KV) activations dominate the physical memory capacity, surpassing the entire model size. In this paper, we propose Hybe, a GPU-NPU hybrid system for efficient LLM inference with a million-token context window. Hybe utilizes the preexisting GPU for the prefill stage and employs lightweight NPUs during the decode stage. Each NPU includes only the necessary computing resources to fully utilize the given memory bandwidth, thereby achieving maximum hardware efficiency. Furthermore, Hybe introduces fine-grained KV transmission, a kernel scheduling method that immediately offloads partial KV produced from the GPU to the NPU, which significantly reduces the KV memory required in the GPU. Lastly, Hybe scheduler applies stage-wise pipelining that dynamically assigns queued requests to idle hardware to minimize stalls. Hybe utilizes NVIDIA H100 GPU with inference-optimized vLLM library and implement Hybe NPU in 4nm process with equal HBM specification. Hybe achieves 2.1 × speedup for Phi-3 with 100K-token context window and 3.9 × energy efficiency for Llama-3 with 1M-token context window, over H100 GPUs with equal total device count.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731051"}, {"primary_key": "183256", "vector": [], "sparse_vector": [], "title": "Light-weight Cache Replacement for Instruction Heavy Workloads.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The last-level cache (LLC) is the last chance for memory accesses from the processor to avoid the costly latency of accessing the main memory. In recent years, an increasing number of instruction heavy workloads have put pressure on the last-level cache. We find that, for instruction heavy workloads, a simple replacement policy with minimal overhead provides at least the same benefit as a state-of-the-art, high-overhead replacement policy in the presence of aggressive prefetching. Our proposal is based on specifying insertion and promotion vectors (IPVs) as a generalization of re-reference interval prediction (RRIP) in such a way that the space of feasible policies may be searched exhaustively to find the best policy for the training set of workloads. The policies are formulated to deliver the best performance taking into account demand and prefetch accesses. We show that our technique, Prefetch Aware Coarse-grained Insertion and Promotion Vectors (PACIPV), improves performance over a state-of-the-art LLC replacement policy (Mockingjay) for instruction heavy workloads, and remains competitive for data heavy workloads with significantly less hardware overhead. We show that RRIP-based IPVs are very easy to implement but outperform far more complex replacement policies. PACIPV achieves a speedup of 3.3% over the baseline of LRU, outperforming SRRIP by 1.1% and the much more hardware intensive Mockingjay by 0.1%.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3730993"}, {"primary_key": "183257", "vector": [], "sparse_vector": [], "title": "MeshSlice: Efficient 2D Tensor Parallelism for Distributed DNN Training.", "authors": ["Hyoungwook Nam", "Gerasi<PERSON>", "<PERSON><PERSON>"], "summary": "In distributed training of large DNN models, the scalability of one-dimensional (1D) tensor parallelism (TP) is limited because of its high communication cost. 2D TP attains extra scalability and efficiency because it reduces communication relative to 1D TP. Unfortunately, existing algorithms for general matrix multiplication (GeMM) in 2D TP suffer from inefficiencies. Indeed, <PERSON>’s algorithm incurs high traffic, SUMMA suffers from high synchronization overhead, and a 2D GeMM with collective communication operations does not overlap communication with computation. In addition, it is difficult to optimize the numerous parameters of 2D TP, including the dataflow, mesh shape, and sharding. As a result, human experts are needed to find efficient configurations of 2D TP. To address these problems, this paper proposes MeshSlice, a novel 2D GeMM algorithm for efficient 2D TP in distributed DNN training. The MeshSlice algorithm slices the collective communications into multiple partial collectives that allow overlapping communication with computation. As a result, MeshSlice hides most of the communication latency. We also present the MeshSlice LLM autotuner, which automates finding an efficient 2D GeMM dataflow configuration, mesh shape, and communication granularity for Large Language Model (LLM) training using analytical cost models. To evaluate MeshSlice, we simulate TPUv4 clusters training LLM models. We show that MeshSlice maintains good efficiency up to at least 256-way 2D TP. In a cluster of 256 TPUs, MeshSlice trains the GPT-3 and Megatron-NLG models 12.0% and 23.4% faster, respectively, than the state-of-the-art algorithm.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731077"}, {"primary_key": "183258", "vector": [], "sparse_vector": [], "title": "Concorde: Fast and Accurate CPU Performance Modeling with Compositional Analytical-ML Fusion.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Cycle-level simulators such as gem5 are widely used in microarchitecture design, but they are prohibitively slow for large-scale design space explorations. We present Concorde, a new methodology for learning fast and accurate performance models of microarchitectures. Unlike existing simulators and learning approaches that emulate each instruction, <PERSON><PERSON> predicts the behavior of a program based on compact performance distributions that capture the impact of different microarchitectural components. It derives these performance distributions using simple analytical models that estimate bounds on performance induced by each microarchitectural component, providing a simple yet rich representation of a program’s performance characteristics across a large space of microarchitectural parameters. Experiments show that Concorde is more than five orders of magnitude faster than a reference cycle-level simulator, with about 2% average Cycles-Per-Instruction (CPI) prediction error across a range of SPEC, open-source, and proprietary benchmarks. This enables rapid design-space exploration and performance sensitivity analyses that are currently infeasible, e.g., in about an hour, we conducted a first-of-its-kind fine-grained performance attribution to different microarchitectural components across a diverse set of programs, requiring nearly 150 million CPI evaluations.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731037"}, {"primary_key": "183259", "vector": [], "sparse_vector": [], "title": "Chip Architectures Under Advanced Computing Sanctions✱.", "authors": ["August Ning", "<PERSON>"], "summary": "The rise of large scale machine learning models has generated unprecedented requirements and demand on computing hardware to enable these trillion parameter models. However, the importance of these bleeding-edge chips to the global economy, technological advancement, and strategic national interests have made them targets of sanctions. Recent advanced computing sanctions set limits on a device’s Total Processing Performance, device bandwidth, and Performance Density and placed export controls on flagship data center and consumer products. In this work, we present the first study on the architectural and economic externality implications of these advanced computing sanctions and their effects on large language model (LLM) inference. We identify which architectural parameters are limited under existing regulations, and perform thorough design space exploration of compliant designs. Optimized designs are able to improve LLM inference prefill performance by 4% and decoding performance by 27% compared to a restricted device baseline. We then demonstrate how an architecture-first approach for computing policies allows chip designers and policymakers to craft efficient guidelines that achieve desired goals while minimizing negative externalities. We show how architectural features can unify marketing-based data center vs. non-data center regulations and how policies can be specified to create gaming-focused device architectures which are inherently limited in AI performance. Augmenting existing performance metrics with insightful architectural constraints better predict workload performance. Combined metrics achieved up to 42.4x narrower distributions compared to using theoretical compute performance alone, enable targeted and efficient policies.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731012"}, {"primary_key": "183260", "vector": [], "sparse_vector": [], "title": "FlexNeRFer: A Multi-Dataflow, Adaptive Sparsity-Aware Accelerator for On-Device NeRF Rendering.", "authors": ["Se<PERSON><PERSON><PERSON><PERSON>", "Banseok Shin", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Neural Radiance Fields (NeRF), an AI-driven approach for 3D view reconstruction, has demonstrated impressive performance, sparking active research across fields. As a result, a range of advanced NeRF models has emerged, leading on-device applications to increasingly adopt NeRF for highly realistic scene reconstructions. With the advent of diverse NeRF models, NeRF-based applications leverage a variety of NeRF frameworks, creating the need for hardware capable of efficiently supporting these models. However, GPUs fail to meet the performance, power, and area (PPA) cost demanded by these on-device applications, or are specialized for specific NeRF algorithms, resulting in lower efficiency when applied to other NeRF models. To address this limitation, in this work, we introduce FlexNeRFer, an energy-efficient versatile NeRF accelerator. The key components enabling the enhancement of FlexNeRFer include: i) a flexible network-on-chip (NoC) supporting multi-dataflow and sparsity on precision-scalable MAC array, and ii) efficient data storage using an optimal sparsity format based on the sparsity ratio and precision modes. To evaluate the effectiveness of FlexNeRFer, we performed a layout implementation using 28nm CMOS technology. Our evaluation shows that FlexNeRFer achieves 8.2 ∼ 243.3 × speedup and 24.1 ∼ 520.3 × improvement in energy efficiency over a GPU (i.e., NVIDIA RTX 2080 Ti), while demonstrating 4.2 ∼ 86.9 × speedup and 2.3 ∼ 47.5 × improvement in energy efficiency compared to a state-of-the-art NeRF accelerator (i.e., NeuRex).", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731107"}, {"primary_key": "183261", "vector": [], "sparse_vector": [], "title": "Finesse: An Agile Design Framework for Pairing-based Cryptography via Software/Hardware Co-Design.", "authors": ["<PERSON><PERSON><PERSON>", "Tianao Dai", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chunming Hu", "<PERSON><PERSON><PERSON> Zhao"], "summary": "Pairing-based cryptography (PBC) is crucial in modern cryptographic applications. With the rapid advancement of adversarial research and the growing diversity of application requirements, PBC accelerators need regular updates in algorithms, parameter configurations, and hardware design. However, traditional design methodologies face significant challenges, including prolonged design cycles, difficulties in balancing performance and flexibility, and insufficient support for potential architectural exploration. To address these challenges, we introduce Finesse, an agile design framework based on co-design methodology. Finesse leverages a co-optimization cycle driven by a specialized compiler and a multi-granularity hardware simulator, enabling both optimized performance metrics and effective design space exploration. Furthermore, Finesse adopts a modular design flow to significantly shorten design cycles, while its versatile abstraction ensures flexibility across various curve families and hardware architectures. Finesse offers flexibility, efficiency, and rapid prototyping, comparing with previous frameworks. With compilation times reduced to minutes, Finesse enables faster iteration cycles and streamlined hardware-software co-design. Experiments on popular curves demonstrate its effectiveness, achieving 34 × improvement in throughput and 6.2 × increase in area efficiency compared to previous flexible frameworks, while outperforming state-of-the-art non-flexible ASIC designs with a 3 × gain in throughput and 3.2 × improvement in area efficiency.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731033"}, {"primary_key": "183262", "vector": [], "sparse_vector": [], "title": "A4: Microarchitecture-Aware LLC Management for Datacenter Servers with Emerging I/O Devices.", "authors": ["Haneul Park", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "KyoungSoo Park", "<PERSON><PERSON><PERSON> Son", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In modern server CPUs, the Last-Level Cache (LLC) serves not only as a victim cache for higher-level private caches but also as a buffer for low-latency DMA transfers between CPU cores and I/O devices through Direct Cache Access (DCA). However, prior work has shown that high-bandwidth network-I/O devices can rapidly flood the LLC with packets, often causing significant contention with co-running workloads. One step further, this work explores hidden microarchitectural properties of the Intel Xeon CPUs, uncovering two previously unrecognized LLC contentions triggered by emerging high-bandwidth I/O devices. Specifically, (C1) DMA-written cache lines in LLC ways designated for DCA (referred to as DCA ways) are migrated to certain LLC ways (denoted as inclusive ways) when accessed by CPU cores, unexpectedly contending with non-I/O cache lines within the inclusive ways. In addition, (C2) high-bandwidth storage-I/O devices, which are increasingly common in datacenter servers, benefit little from DCA while contending with (latency-sensitive) network-I/O devices within DCA ways. To this end, we present A4, a runtime LLC management framework designed to alleviate both (C1) and (C2) among diverse co-running workloads, using a hidden knob and other hardware features implemented in those CPUs. Additionally, we demonstrate that A4 can also alleviate other previously known network-I/O-driven LLC contentions. Overall, it improves the performance of latency-sensitive, high-priority workloads by 51% without notably compromising that of low-priority workloads.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731114"}, {"primary_key": "183263", "vector": [], "sparse_vector": [], "title": "Constant-Rate Entanglement Distillation for Fast Quantum Interconnects.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Distributed quantum computing allows the modular construction of large-scale quantum computers and enables new protocols for blind quantum computation. However, such applications in the large-scale, fault-tolerant regime place stringent demands on the fidelity and rate of entanglement generation, which are not met by existing methods for quantum interconnects. In this work, we develop constant-rate entanglement distillation methods to address this bottleneck in the setting of noisy local operations. By using a sequence of two-way entanglement distillation protocols based on quantum error detecting codes with increasing rate, and combining with standard fault tolerance techniques, we achieve constant-rate entanglement distillation. We show that the scheme has constant-rate in expectation, and further numerically optimize to achieve low practical overhead under memory constraints. We find that compared to existing quantum interconnect schemes, our methods reduce the communication overhead by more than 10 × in relevant regimes, leading to a direct speed-up in the execution of distributed quantum algorithms.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731069"}, {"primary_key": "183264", "vector": [], "sparse_vector": [], "title": "XHarvest: Rethinking High-Performance and Cost-Efficient SSD Architecture with CXL-Driven Harvesting.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Shushu <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The occasional nature of I/O bursts in production clusters makes the substantial and expensive SSD internal hardware resources (e.g., computation and memory resources) always underutilized, resulting in cost inefficiency. Open-Channel SSD (OCSSD), as a pioneering solution, removes the SSD internal resources but rather leverages the host-side resources to serve I/O requests. Unfortunately, it faces adoption obstacles due to the heavy resource contention with user applications, hampered host-SSD collaboration, and proprietary firmware leakage risks. Tackling these challenges, we propose XHarvest, a new cost-efficient and high-performance SSD architecture, which harnesses compute express link (CXL) and trusted execution environment (TEE) to facilitate dynamic, efficient, and secure host resource harvesting. It reserves moderate SSD internal resources to isolate SSD internal tasks and applications under regular I/O loads while coping with occasional I/O bursts via dynamic host resource harvesting. To this end, XHarvest executes the firmware within the host-side TEE without disclosing sensitive algorithms while leveraging the cache-coherent and fine-grained CXL to build a unified and efficient metadata cache. XHarvest also capitalizes on the CXL and its security feature to enable secure and efficient host-SSD collaboration. The evaluation results show that XHarvest reduces the hardware cost by 31.50% while achieving the same or even higher performance than conventional SSDs. It relieves resource contention via dynamic harvesting, reducing 2.27 × execution time over OCSSD in memory-intensive tasks.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731028"}, {"primary_key": "183265", "vector": [], "sparse_vector": [], "title": "Chimera: Communication Fusion for Hybrid Parallelism in Large Language Models.", "authors": ["Le Qin", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Large Language Models (LLMs), exemplified by ChatGPT, have emerged as a predominant workload in current machine learning systems. To achieve efficient training and inference within the constraints of limited single-NPU memory capacity, deploying LLMs on multi-NPU systems typically adopt a hybrid approach that combines various parallelism patterns. This hybrid parallelism within LLMs introduces a significant amount of diverse collective communications. However, these frequent blocking communications impose a substantial burden on the multi-NPU systems. Overcoming the communication bottleneck is crucial to unlocking the potential of multi-NPU systems for efficient and scalable LLM processing. This paper introduces Chimera, a communication fusion mechanism for hybrid parallelism in LLMs. We comprehensively analyze the communication processes of each LLM parallelism pattern, identify the communication redundancy in hybrid parallelism and eliminate redundancy by fusing adjacent communication operators during parallelism transformation. By reordering operations and generating redundancy-free communication operator, Chimera effectively mitigates communication bottleneck in hybrid LLM parallelism. Our results show that Chimera achieves 1.23-7.06 × network bandwidth speedup. Additionally, the end-to-end performance of LLM forward pass and backward pass on different typical multi-NPU systems achieves respective 1.32-1.58 × and 1.16-1.36 × speedups on average compared with those without communication fusion.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731025"}, {"primary_key": "183266", "vector": [], "sparse_vector": [], "title": "DReX: Accurate and Scalable Dense Retrieval Acceleration via Algorithmic-Hardware Codesign.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Retrieval-augmented generation (RAG) supplements large language models (LLM) with information retrieval to ensure up-to-date, accurate, factually grounded, and contextually relevant outputs. RAG implementations often employ dense retrieval methods and approximate k-nearest neighbor search (ANNS). Unfortunately, ANNS is inherently dataset-specific and prone to low recall, potentially leading to inaccuracies when irrelevant or incomplete context is passed to the LLM. Furthermore, sending numerous imprecise documents to the LLM for generation can significantly degrade performance compared to processing a smaller set of accurate documents. We propose DReX, a dataset-agnostic, accurate, and scalable Dense Retrieval Acceleration scheme enabled through a novel algorithmic-hardware co-design. We leverage in-DRAM logic to enable early filtering of embedding vectors far from the query vector. An outside-DRAM near-memory accelerator then performs exact nearest neighbor searches on the remaining filtered embeddings. This resulting design minimizes off-chip data movement and ensures precise and efficient retrieval, laying the foundation for robust and performant RAG systems that are broadly applicable. Our evaluation shows that DReX delivers a 6.2-7 × reduction in time-to-first-token for a representative RAG application over a state-of-the-art mechanism while incurring reasonable area and power overheads in the memory subsystem.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731079"}, {"primary_key": "183267", "vector": [], "sparse_vector": [], "title": "MicroScopiQ: Accelerating Foundational Models through Outlier-Aware Microscaling Quantization.", "authors": ["<PERSON><PERSON><PERSON>", "Souvik Kundu", "<PERSON><PERSON><PERSON>"], "summary": "Quantization of foundational models (FMs) is significantly more challenging than traditional DNNs due to the emergence of large magnitude values called outliers. Existing outlier-aware algorithm-architecture co-design techniques either use mixed-precision, retaining outliers at high precision but compromise hardware efficiency, or quantize inliers and outliers at the same precision, improving hardware efficiency at the cost of accuracy. To address this mutual exclusivity, we propose MicroScopiQ, a novel co-design technique that leverages pruning to complement outlier-aware quantization. MicroScopiQ retains outliers at higher precision while pruning a certain fraction of least important weights to distribute the additional outlier bits; ensuring high accuracy, aligned memory and hardware efficiency. We design a high-throughput, low overhead accelerator architecture composed of multi-precision INT processing elements and a network-on-chip called ReCoN that efficiently abstracts the complexity of supporting high-precision outliers. Additionally, unlike prior techniques, MicroScopiQ does not assume any locality of outlier weights, enabling applicability to a broad range of FMs. Extensive experiments across diverse quantization settings demonstrate that MicroScopiQ achieves state-of-the-art quantization accuracy, while delivering up to 3 × faster inference and 2 × lower energy consumption compared to existing alternatives. Code is available at: MicroScopiQ-LLM-Quantization.git", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3730989"}, {"primary_key": "183268", "vector": [], "sparse_vector": [], "title": "FRED: A Wafer-scale Fabric for 3D Parallel DNN Training.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Wafer-scale systems are an emerging technology that tightly integrates high-end accelerator chiplets with high-speed wafer-scale interconnects, enabling low-latency and high-bandwidth connectivity. This makes them a promising platform for deep neural network (DNN) training. However, current network-on-wafer topologies, such as 2D Meshes, lack the flexibility needed to support various parallelization strategies effectively. In this paper, we propose <PERSON>, a wafer-scale fabric architecture tailored to the unique communication needs of DNN training. <PERSON> creates a distributed on-wafer topology with tiny microswitches, providing nonblocking connectivity for collective communications between arbitrary groups of accelerators and enabling in-switch collective support. Our results show that for sample parallelization strategies, <PERSON> can improve the average end-to-end training time of ResNet-152, Transformer-17B, GPT-3, and Transformer-1T by 1.76 ×, 1.87 ×, 1.34 ×, and 1.4 ×, respectively, compared to a baseline wafer-scale Mesh.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731055"}, {"primary_key": "183269", "vector": [], "sparse_vector": [], "title": "Neoscope: How Resilient Is My SoC to Workload Churn?", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The lifetime of hardware is increasing, but the lifetime of software is not. This leads to devices that, while performant when released, have fall-off due to changing workload suitability. To ensure that performance is maintained, computer architects must begin considering the effects of evolving workloads early in the design process. The ways that workloads can change, i.e., churn, over time profoundly affect hardware’s ability to maintain performance. To better understand churn, we introduce the concepts Magnitude and Disruption, which enable how workloads change to be quantitatively described. By leveraging these terms, we present churn as a span, where the churn of a given workload can be categorized as either Minimal, Perturbing, Escalating, or Volatile. To account for how churn affects performance, we propose Neoscope, the first multi-objective pre-silicon design space exploration tool for investigating System-on-Chip (SoC) architectures that are resilient to workload churn. Neoscope uses integer linear programming and concepts from job-shop scheduling to construct near-optimal SoCs for a given workload. Unlike previous methods, Neoscope approaches (and often finds) the globally optimal SoC with a single invocation, instead of needing to parameter sweep. Neoscope is also multi-objective, i.e., it can optimize for many kinds of metrics other than absolute performance, including area, energy, cost, and carbon efficiencies. Using Neoscope, we explore near-optimal SoCs for many workload-churn configurations, and investigate how changing the objective function changes the ideal SoC. Neoscope shows that small SoCs need high levels of specialization, but that this is risky, as it increases susceptibility to churn.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731014"}, {"primary_key": "183270", "vector": [], "sparse_vector": [], "title": "Hermes: Algorithm-System Co-design for Efficient Retrieval-Augmented Generation At-Scale.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The rapid advancement of Large Language Models (LLMs) as well as the constantly expanding amount of data make keeping the latest models constantly up-to-date a challenge. The high computational cost required to constantly retrain models to handle evolving data has led to the development of Retrieval-Augmented Generation (RAG). RAG presents a promising solution that enables LLMs to access and incorporate real-time information from external datastores, thus minimizing the need for retraining to update the information available to an LLM. However, as the RAG datastores used to augment information expand into the range of trillions of tokens, retrieval overheads become significant, impacting latency, throughput, and energy efficiency. To address this, we propose Hermes, an algorithm-systems co-design framework that addresses the unique bottlenecks of large-scale RAG systems. <PERSON><PERSON> mitigates retrieval latency by partitioning and distributing datastores across multiple nodes, while also enhancing throughput and energy efficiency through an intelligent hierarchical search that dynamically directs queries to optimized subsets of the datastore. On open-source RAG datastores and models, we demonstrate <PERSON><PERSON> optimizes end-to-end latency and energy by up to 9.33 × and 2.10 ×, without sacrificing retrieval quality for at-scale trillion token retrieval datastores.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731076"}, {"primary_key": "183271", "vector": [], "sparse_vector": [], "title": "ATiM: Autotuning Tensor Programs for Processing-in-DRAM.", "authors": ["<PERSON><PERSON>", "Dookyung Kang", "<PERSON><PERSON><PERSON>"], "summary": "Processing-in-DRAM (DRAM-PIM) has emerged as a promising technology for accelerating memory-intensive operations in modern applications, such as Large Language Models (LLMs). Despite its potential, current software stacks for DRAM-PIM face significant challenges, including reliance on hand-tuned libraries that hinder programmability, limited support for high-level abstractions, and the lack of systematic optimization frameworks. To address these limitations, we present ATiM, a search-based optimizing tensor compiler for UPMEM. Key features of ATiM include: (1) automated searches of the joint search space for host and kernel tensor programs, (2) PIM-aware optimizations for efficiently handling boundary conditions, and (3) improved search algorithms for the expanded search space of UPMEM systems. Our experimental results on UPMEM hardware demonstrate performance gains of up to 6.18 × for various UPMEM benchmark kernels and 8.21 × for GPT-J layers. To the best of our knowledge, ATiM is the first tensor compiler to provide fully automated, autotuning-integrated code generation support for a DRAM-PIM system. By bridging the gap between high-level tensor computation abstractions and low-level hardware-specific requirements, ATiM establishes a foundation for advancing DRAM-PIM programmability and enabling streamlined optimization.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731096"}, {"primary_key": "183272", "vector": [], "sparse_vector": [], "title": "Precise exceptions in relaxed architectures.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON>ara<PERSON><PERSON>", "<PERSON>"], "summary": "To manage exceptions, software relies on a key architectural guarantee, precision: that exceptions appear to execute between instructions. However, this definition, dating back over 60 years, fundamentally assumes a sequential programmers model. Modern architectures such as Arm-A with programmer-observable relaxed behaviour make such a naive definition inadequate, and it is unclear exactly what guarantees programmers have on exception entry and exit. In this paper, we clarify the concepts needed to discuss exceptions in the relaxed-memory setting – a key aspect of precisely specifying the architectural interface between hardware and software. We explore the basic relaxed behaviour across exception boundaries, and the semantics of external aborts, using Arm-A as a representative modern architecture. We identify an important problem, present yet unexplored for decades: pinning down what it means for exceptions to be precise in a relaxed setting. We describe key phenomena that any definition should account for. We develop an axiomatic model for Arm-A precise exceptions, tooling for axiomatic model execution, and a library of tests. Finally we explore the relaxed semantics of software-generated interrupts, as used in sophisticated programming patterns, and sketch how they too could be modelled.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731102"}, {"primary_key": "183273", "vector": [], "sparse_vector": [], "title": "Hybrid SLC-MLC RRAM Mixed-Signal Processing-in-Memory Architecture for Transformer Acceleration via Gradient Redistribution.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Transformers, while revolutionary, face challenges due to their demanding computational cost and large data movement. To address this, we propose HyFlexPIM, a novel mixed-signal processing-in-memory (PIM) accelerator for inference that flexibly utilizes both single-level cell (SLC) and multi-level cell (MLC) RRAM technologies to trade-off accuracy and efficiency. HyFlexPIM achieves efficient dual-mode operation by utilizing digital PIM for high-precision and write-intensive operations while analog PIM for high parallel and low-precision computations. The analog PIM further distributes tasks between SLC and MLC PIM operations, where a single analog PIM module can be reconfigured to switch between two operations (SLC/MLC) with minimal overhead (<1% for area & energy). Critical weights are allocated to SLC RRAM for high accuracy, while less critical weights are assigned to MLC RRAM to maximize capacity, power, and latency efficiency. However, despite employing such a hybrid mechanism, brute-force mapping on hardware fails to deliver significant benefits due to the limited proportion of weights accelerated by the MLC and the noticeable degradation in accuracy. To maximize the potential of our hybrid hardware architecture, we propose an algorithm co-optimization technique, called gradient redistribution, which uses Singular Value Decomposition (SVD) to decompose and truncate matrices based on their importance, then fine-tune them to concentrate significance into a small subset of weights. By doing so, only 5-10% of the weights have dominantly large gradients, making it favorable for HyFlexPIM by minimizing the use of expensive SLC RRAM while maximizing the efficient MLC RRAM. Our evaluation shows that HyFlexPIM significantly enhances computational throughput and energy efficiency, achieving maximum 1.86 × and 1.45 × higher than state-of-the-art methods.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731109"}, {"primary_key": "183274", "vector": [], "sparse_vector": [], "title": "HardHarvest: Hardware-Supported Core Harvesting for Microservices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In microservice environments, users size their virtual machines (VMs) for peak loads, leaving cores idle much of the time. To improve core utilization and overall throughput, it is instructive to consider a recently-introduced software technique for environments with relatively long-running monolithic applications: Core Harvesting. With this technique, Harvest VMs running batch applications temporarily steal idle cores allocated by Primary VMs running latency-critical applications, and return them on demand. Unfortunately, re-assigning cores across VMs has substantial overhead, resulting from hypervisor calls, context switching, and flushing TLBs/caches. While such overhead is acceptable in monolithic application environments, it would be prohibitive in environments with sub-millisecond microservices. To address this problem, this paper proposes, for the first time, an architecture for core harvesting in hardware. The architecture, called HardHarvest, targets microservices. It aims to: 1) maximize core utilization, 2) minimize impact on Primary VM tail latency, and 3) boost Harvest VM throughput. HardHarvest eliminates software overheads by using in-hardware request scheduling and partitioning TLBs/caches with a smart replacement algorithm. On average, compared to state-of-the-art software core harvesting, HardHarvest increases core utilization by 1.5 ×, increases Harvest VM throughput by 1.8 ×, and reduces Primary VM tail latency by 6.0 ×.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731071"}, {"primary_key": "183275", "vector": [], "sparse_vector": [], "title": "DCPerf: An Open-Source, Battle-Tested Performance Benchmark Suite for Datacenter Workloads.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present DCPerf, the first open-source performance benchmark suite actively used to inform procurement decisions for millions of CPU in hyperscale datacenters. Although numerous benchmarks exist, our evaluation reveals that they inaccurately project server performance for datacenter workloads or fail to scale to resemble production workloads on modern many-core servers. DCPerf distinguishes itself in two aspects: (1) it faithfully models essential software architectures and features of datacenter applications, such as microservice architecture and highly optimized multi-process or multi-thread concurrency; and (2) it strives to align its performance characteristics with those of production workloads, at both the system level and microarchitecture level. Both are made possible by our direct access to the source code and hyperscale production deployments of datacenter workloads. Additionally, we share real-world examples of using DCPerf in critical decision-making, such as selecting future CPU SKUs and guiding CPU vendors in optimizing their designs. Our evaluation demonstrates that DCPerf accurately projects the performance of representative production workloads within a 3.3% error margin across four generations of production servers introduced over a span of six years, with core counts varying widely from 36 to 176.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731411"}, {"primary_key": "183276", "vector": [], "sparse_vector": [], "title": "DREAM: Enabling Low-Overhead Rowhammer Mitigation via Directed Refresh Management.", "authors": ["<PERSON><PERSON><PERSON> Taneja", "<PERSON><PERSON>"], "summary": "This paper focuses on Memory-Controller (MC) side Rowhammer mitigation. MC-side mitigation consists of two parts: First, a tracker to identify the aggressor rows. Second, a command to let the MC inform the DRAM chip to perform victim-refresh for the specified aggressor row. To facilitate this, prior works assumed a per-bank Nearby Row Refresh (NRR) command. However, JEDEC standards did not support NRR. Instead, JEDEC introduced Directed Refresh Management (DRFM), which can simultaneously perform mitigations for one row each in 8 (DRFMsb) or 32 (DRFMab) banks. As DRFM stalls 8-32 banks, it incurs high overheads. For example, at a threshold of 2K, PARA incurs a slowdown of 3.9% with NRR, 12.7% with DRFMsb, and 49% with DRFMab. Although counter-based trackers can avoid these slowdowns, they require significant storage. The goal of our paper is to reduce the performance and storage overheads of MC-based mitigations by using properties of DRFM. Our paper proposes DREAM, DRFM-Aware Rowhammer Mitigation. We propose two variants of DREAM. Our first design, DREAM-R, reduces the performance overhead of randomized trackers by increasing the time between sampling the row and issuing a DRFM. The delay allows other banks the time to sample their own rows, thereby increasing the number of rows mitigated under a single DRFM. DREAM-R reduces the average performance overhead of PARA from 12.7% (DRFMsb) to 4.24% and MINT from 15.9% (DRFMsb) to 2.1%. We bound the impact of delayed DRFM on the tolerated Rowhammer threshold. Our second design DREAM-C, reduces the storage for counter-based trackers by leveraging the fact that DRFM can concurrently mitigate several rows. DREAM-C forms a gang containing 32-256 rows, randomly selected equally from all the 32 banks, and allocates a single counter for the entire gang. DREAM-C reduces the storage required at a threshold of 500 to only 1KB/bank, which is 8x lower than Graphene while avoiding the complexity of CAM lookups and incurring negligible slowdown. We also show that DREAM compares favorably to PRAC.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731117"}, {"primary_key": "183277", "vector": [], "sparse_vector": [], "title": "Qtenon: Towards Low-Latency Architecture Integration for Accelerating Hybrid Quantum-Classical Computing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Minghua Shen", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Hybrid quantum-classical algorithms have shown great promise in leveraging the computational potential of quantum systems. However, the efficiency of these algorithms is severely constrained by the limitations of current quantum hardware architectures. These architectures, which typically feature a decoupled design, lack both hardware support for low-latency communication and software support for fine-grained optimization. In this paper, we propose Qtenon, a tightly coupled system for efficient hybrid quantum-classical algorithm acceleration. Qtenon is composed of both hardware part and software part. To enable efficient communication and computation, the hardware part provides a unified memory hierarchy, an efficient quantum controller, as well as a multi-stage processing pipeline. The unified memory hierarchy functions as a communication buffer between host and quantum accelerators, with dedicated data paths and interfaces provided by the quantum controller. The multi-stage pipeline leverages hardware pipelines to fully exploit parallelism. To program hybrid quantum-classical algorithms on the hardware, our software part provides a set of instructions for data communication and computation. The instructions also enable fine-grained synchronization and efficient scheduling for quantum-host interaction. We design Qtenon as a RISC-V extended chip and implement it using Chisel. In evaluation, we achieve up to 14.9 × end-to-end speedup compared to state-of-the-art work for hybrid quantum-classical algorithms.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731087"}, {"primary_key": "183278", "vector": [], "sparse_vector": [], "title": "ARTERY: Fast Quantum Feedback using Branch Prediction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Tingting Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Quantum feedback makes the execution of dynamic quantum circuits possible and is widely used in quantum algorithms. However, due to the inherent computation and transmission cost, the latency of the quantum feedback becomes a considerable burden on the current quantum algorithm. The dynamic property of the feedback also makes the gates blocked until the feedback is finished. In this paper, we propose ARTERY, which uses branch prediction to support instruction pre-execution and speed up the feedback. ARTERY integrates historical statistics of branches and a real-time readout pulse analysis to predict the branch. With this idea, we build up a reconciled branch predictor that concatenates the historical statistics of branches and a real-time branch circuit speculation obtained from the readout-pulse trajectory predictor. We further explore the implementation of peripheral hardware for feedback, including a scalable inter-FPGA connection via the backplane, a feedback trigger mechanism for dynamic instruction timing, and an adaptive pulse sampling technique to maximize the hardware bandwidth. ARTERY accelerates quantum feedback process by 2.07 × compared to the state-of-the-art method, with over 90% prediction accuracy, achieving 1.24 × fidelity improvement.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731086"}, {"primary_key": "183279", "vector": [], "sparse_vector": [], "title": "HiPER: Hierarchically-Composed Processing for Efficient Robot Learning-Based Control.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Learning-Based Model Predictive Control (LMPC) is a class of algorithms that enhances Model Predictive Control (MPC) by including machine learning methods, improving robot navigation in complex environments. However, the combination of machine learning and MPC computation in LMPC creates a unique workload that cannot be efficiently handled by a simple GPU and CPU integration. We present HiPER, a hierarchically-composed processing array that provides temporal and spatial mapping capabilities, allowing efficient adaptation to workload changes at runtime. To simplify control, HiPER employs a pointer queue hierarchy to compose and orchestrate program execution. Additionally, HiPER utilizes a fractal interconnect topology that combines local systolic interconnects and their hierarchical extensions to efficiently support the workload’s traffic characteristics. To evaluate the performance and efficiency of HiPER, we synthesized a 16.37 mm2 design in 16nm CMOS. The design consists of 6 pointer queue levels and 1024 PEs. The prototype was assessed using a representative LMPC workload, demonstrating 10.75 × improvement in performance compared to a GTX 1080 GPU, 12.80 × improvement in energy efficiency compared to a Jetson Orin Nano embedded GPU, and 11.6 × /22.2 × improvement in performance compared to the RoboX/Plasticine accelerators.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731097"}, {"primary_key": "183280", "vector": [], "sparse_vector": [], "title": "CoopRT: Accelerating BVH Traversal for Ray Tracing via Cooperative Threads.", "authors": ["Yavuz <PERSON>", "<PERSON><PERSON> Zhou"], "summary": "Ray Tracing is a rendering technique to simulate the way light interacts with objects to create realistic images. It has become prominent thanks to the latest hardware support on Graphics Processing Units (GPUs), i.e., the Ray-Tracing (RT) unit, specially designed to accelerate ray tracing operations. Despite such hardware advances, ray tracing remains a performance bottleneck for high-performance graphics workloads, such as real-time path tracing (PT), which is an application of ray tracing where multiple bouncing rays are traced per pixel. The key reasons are (a) the costly Bounding Volume Hierarchy (BVH) traversal operation, and (b) low Single-Instruction-Multiple-Thread (SIMT) efficiency as the rays in the same warp deviate inevitably in their traversal paths. In this work, we propose a novel architecture design for cooperative BVH traversal that exploits the parallelism present in the BVH traversal process. The key idea of our CoopRT scheme is to make use of the idle threads, either completely inactive when the ray tracing instruction is executed or partially idle due to early completion, to help the long running threads in the same warp. Specifically, we enable idle threads in a GPU warp to utilize their readily available traversal hardware to help traverse the BVH tree for the busy threads, therefore helping them finish their traversal much faster. This approach is implemented purely in hardware, requiring no changes to the programming model. We present our architecture design and show that it only involves small changes to the existing RT unit. We evaluated CoopRT in Vulkan-sim, a cycle-level simulator, and observed up to 5.11x speedup over the baseline, with a geometric mean of 2.15x speedup at the cost of a moderate area overhead of 3.0% of the warp buffer in the RT unit. Using the energy-delay product, our CoopRT achieves an average of 2.29x improvement over the baseline.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731118"}, {"primary_key": "183281", "vector": [], "sparse_vector": [], "title": "SWIPER: Minimizing Fault-Tolerant Quantum Program Latency via Speculative Window Decoding.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yanjing Li", "<PERSON>"], "summary": "Real-time decoding is a key ingredient in future fault-tolerant quantum systems, yet many decoders are too slow to run in real time. Prior work has shown that parallel window decoding can scalably meet throughput requirements in the presence of increasing decoding times. However, windowed decoding require that some decoding tasks be delayed until others have completed, which can be problematic during time-sensitive operations such as T gate teleportation, leading to suboptimal program runtimes. To alleviate this, we introduce SWIPER, a speculative window decoder. Taking inspiration from branch prediction in classical computer architecture, SWIPER utilizes a light-weight speculation step to predict data dependencies between adjacent decoding windows, allowing multiple layers of decoding tasks to be resolved simultaneously. Through a state-of-the-art compilation pipeline and a detailed open-source simulator, we find that SWIPER reduces application runtimes by 40% on average compared to prior parallel window decoders.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731022"}, {"primary_key": "183282", "vector": [], "sparse_vector": [], "title": "MoPAC: Efficiently Mitigating Rowhammer with Probabilistic Activation Counting.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Rowhammer has worsened over the last decade. Existing in-DRAM solutions, such as TRR, were broken with simple patterns. In response, the recent DDR5 JEDEC standards modify the DRAM array to enable Per-Row Activation Counters (PRAC) for tracking aggressor rows. They also extend the DRAM timings to support the operations required to update the PRAC counters. Unfortunately, the increased memory timings cause significant performance overheads (on average 10%) even for benign applications and even at current Rowhammer thresholds. The goal of this paper is to minimize the slowdown of PRAC while retaining the security benefits of PRAC. This paper proposes Mitigating Rowhammer with Probabilistic Activation Counts (MoPAC), which reduces the slowdown of updating the PRAC counters by performing the updates probabilistically, thereby incurring the latency overhead of counter updates for only a small subset of activations. To ensure security in the presence of probabilistic counters, MOPAC adjusts the threshold at which the row undergoes mitigation. We propose two variants of MoPAC: MoPAC-C (Memory-Controller Side) and MoPAC-D (DRAM Side). MoPAC-C relies on having two types of precharge commands: one that incurs normal latency and does not do counter updates, and the other that incurs higher latency and performs counter updates. MoPAC-C probabilistically chooses when the longer precharge must be used to perform update of the PRAC counter. MoPAC-D is a completely in-DRAM solution that probabilistically selects which activations will be selected for performing counter updates and obtains the time required for counter-updates using ALERT or REF. Our evaluations show that, for a Rowhammer threshold of 500 (10 × lower than current thresholds), MoPAC-C and MoPAC-D incur an average slowdown of only \\( 1.7\\% \\) and \\( 0.7\\% \\), much less than the 10% incurred by PRAC. MoPAC removes one of the major obstacles to the commercial adoption of PRAC.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3730997"}, {"primary_key": "183283", "vector": [], "sparse_vector": [], "title": "Process Only Where You Look: Hardware and Algorithm Co-optimization for Efficient Gaze-Tracked Foveated Rendering in Virtual Reality.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Virtual reality (VR) plays a crucial role in advancing immersive, interactive experiences that transform learning, work, and entertainment by enhancing user engagement and expanding possibilities across various fields. Image rendering is one of the most crucial application in VR, as it produces high-quality, realistic visuals that are vital for maintaining immersive user experiences and preventing visual discomfort or motion sickness. However, the cost of image rendering in VR environment is considerable, primarily due to the demands of high-quality visual experiences from users. This challenge is even greater in real-time applications, where maintaining low latency further increases the complexity of the rendering process. On the other hand, VR devices, such as head-mounted displays (HMDs), are intrinsically linked to human behavior, using insights from perception and cognition to enhance user experience. In this work, we aim to reduce the high computational costs of the rendering process in VR by leveraging natural human eye dynamics and focusing on processing only where you look (POLO). This involves co-optimizing AI algorithms with underlying hardware for greater efficiency. We introduce POLONet, an efficient multitask deep learning framework designed to track human eye movements with minimal latency. Integrated with the POLO accelerator as a plug-in for VR HMD SoCs, this approach significantly lowers image rendering costs, achieving up to a 3.9 × reduction in end-to-end latency compared to the latest gaze tracking methods.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731110"}, {"primary_key": "183284", "vector": [], "sparse_vector": [], "title": "Phi: Leveraging Pattern-based Hierarchical Sparsity for High-Efficiency Spiking Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhang", "<PERSON><PERSON><PERSON>", "Hai <PERSON>", "<PERSON><PERSON>"], "summary": "Spiking Neural Networks (SNNs) are gaining attention for their energy efficiency and biological plausibility, utilizing 0-1 activation sparsity through spike-driven computation. While existing SNN accelerators exploit this sparsity to skip zero computations, they often overlook the unique distribution patterns inherent in binary activations. In this work, we observe that particular patterns exist in spike activations, which we can utilize to reduce the substantial computation of SNN models. Based on these findings, we propose a novel pattern-based hierarchical sparsity framework, termed Phi, to optimize computation. Phi introduces a two-level sparsity hierarchy: Level 1 exhibits vector-wise sparsity by representing activations with pre-defined patterns, allowing for offline pre-computation with weights and significantly reducing most runtime computation. Level 2 features element-wise sparsity by complementing the Level 1 matrix, using a highly sparse matrix to further reduce computation while maintaining accuracy. We present an algorithm-hardware co-design approach. Algorithmically, we employ a k-means-based pattern selection method to identify representative patterns and introduce a pattern-aware fine-tuning technique to enhance Level 2 sparsity. Architecturally, we design Phi, a dedicated hardware architecture that efficiently processes the two levels of Phi sparsity on the fly. Extensive experiments demonstrate that <PERSON> achieves a 3.45 × speedup and a 4.93 × improvement in energy efficiency compared to state-of-the-art SNN accelerators, showcasing the effectiveness of our framework in optimizing SNN computation.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731035"}, {"primary_key": "183285", "vector": [], "sparse_vector": [], "title": "RAP: Reconfigurable Automata Processor.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Regular pattern matching is essential for applications such as text processing, malware detection, network security, and bioinformatics. Recent in-memory automata processors have significantly advanced the energy and memory efficiency over conventional computing platforms. However, these processors are typically optimized only for one type of automata, limiting their capability to efficiently support regex processing under diverse real-world workloads. This paper presents RAP, the first reconfigurable in-memory automata processor for efficient regular pattern matching across diverse workloads. It supports Nondeterministic Finite Automata (NFA), Nondeterministic Bit Vector Automata (NBVA), and Linear NFA (LNFA) through reconfigurable architecture and circuit designs, and a compiler for translation. RAP is evaluated in 28nm CMOS PDK, achieving 1.2-1.5 × higher energy efficiency and 1.3-2.5 × higher compute density compared to SotA automata processors for NFA (CA and CAMA) over diverse real-world benchmarks. It also achieves 1.6 × higher compute density and similar energy efficiency as BVAP, a SotA optimized for bounded repetitions. Finally, RAP is >100 × and >1000 × more energy efficient than SotA GPU and CPU solutions.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731106"}, {"primary_key": "183286", "vector": [], "sparse_vector": [], "title": "Assassyn: A Unified Abstraction for Architectural Simulation and Implementation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The continuous growth of on-chip transistors driven by technology scaling urges architecture developers to design and implement novel architectures to effectively utilize the excessive on-chip resources. Due to the challenges of programming in register-transfer level (RTL) languages, performance modeling based on simulation is typically developed alongside hardware implementation, allowing the exploration of high-level design decisions before dealing with the error-prone, low-level RTL details. However, this approach also introduces new challenges in coordinating across multiple teams to align implementation details separate codebases. In this paper, we address this issue by presenting Assassyn, a unified, high-level, and general-purpose programming framework for architectural simulation and implementation. By taking advantage of the concept of asynchronous event handling, a widely existing behavior in both hardware design and implementation and software engineering, a general-purpose, and high-level programming abstraction is proposed to mitigate the difficulties of RTL programming. Moreover, the unified programming interface naturally enables an accurate and faithful alignment between the simulation-based performance modeling and RTL implementation. Our evaluation demonstrates that Assassyn’s high-level programming interface is sufficiently expressive to implement a wide range of architectures, from architectural components, and application-specific accelerators, to designs as complicated as out-of-order CPUs. All the generated simulators perfectly align with the generated RTL behavior, while achieving 2.2-8.1 × simulation speedup, and requiring 70% lines of code. The generated RTL achieves comparable perf/area compared to handcrafted RTL, and 6 × perf/area compared to high-level synthesis generated RTL code by introducing by mean 1.26 × lines of code overhead.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731004"}, {"primary_key": "183287", "vector": [], "sparse_vector": [], "title": "GPUs All Grown-Up: Fully Device-Driven SpMV Using GPU Work Graphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Bradford <PERSON>", "<PERSON><PERSON><PERSON>", "Matthä<PERSON> G<PERSON>"], "summary": "Sparse matrix-vector multiplication (SpMV) is a key operation across high-performance computing, graph analytics, and many more applications. In these applications, the matrix characteristics, notably non-zero elements per row, can vary widely and impact which algorithm performs best. Thus, Graphics Processing Unit (GPU) SpMV algorithms often rely on costly preprocessing to determine what per-row algorithm to select to achieve high performance. In this work we combine SpMV preprocessing and the subsequent per-row processing on the GPU by leveraging the novel “Work Graphs” GPU programming model—initially designed for graphics applications—for dynamic on-device self-scheduling. Work Graphs allow for fine-grain dataflow execution of individual workgroups using emerging hardware and firmware support. As soon as preprocessing has generated sufficient work, workgroups of individual processing kernels are self-scheduled and executed, interleaved with those of other kernels. This improves cache locality and eliminates host interaction altogether. Across a suite of 59 sparse matrices, the best of various novel Work Graphs SpMV implementations outperforms state-of-the-art rocSPARSE “LRB” for a single SpMV by up to 7.19 × (mean: 3.35 ×, SD: 1.89). Furthermore, it achieves much more stable performance across various sparsity patterns than the rocSPARSE CSR-General algorithm, and even beats the advanced rocSPARSE CSR-Adaptive algorithm for up to 92 consecutive SpMV calculations. In addition, compared to rocSPARSE LRB, it reduces code complexity by 75%. Its memory footprint for supporting data structures is a fixed ∼ 25 MiB independent of matrix size, compared to rocSPARSE LRB’s data structures that scale with matrix size to hundreds of megabytes. Overall, this work showcases the performance potential of emerging dynamic on-device scheduling techniques for GPU compute applications.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731060"}, {"primary_key": "183288", "vector": [], "sparse_vector": [], "title": "When Mitigations Backfire: Timing Channel Attacks and Defense for PRAC-Based RowHammer Mitigations.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Per Row Activation Counting (PRAC) has emerged as a robust framework for mitigating RowHammer (RH) vulnerabilities in modern DRAM systems. However, we uncover a critical vulnerability: a timing channel introduced by the Alert Back-Off (ABO) protocol and Refresh Management (RFM) commands. We present PRACLeak, a novel attack that exploits these timing differences to leak sensitive information, such as secret keys from vulnerable AES implementations, by monitoring memory access latencies. To counter this, we propose Timing-Safe PRAC (TPRAC), a defense that eliminates PRAC-induced timing channels without compromising RH mitigation efficacy. TPRAC uses Timing-Based RFMs, issued periodically and independent of memory activity. It requires only a single-entry in-DRAM mitigation queue per DRAM bank and is compatible with existing DRAM standards. Our evaluations demonstrate that TPRAC closes timing channels while incurring only 3.4% performance overhead at the RH threshold of 1024.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731007"}, {"primary_key": "183289", "vector": [], "sparse_vector": [], "title": "DS-TPU: Dynamical System for on-Device Lifelong Graph Learning with Nonlinear Node Interaction.", "authors": ["<PERSON><PERSON> Wu", "Ruibing Song", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Graph learning on dynamical systems has recently surfaced as an emerging research domain. By leveraging a novel electronic Dynamical System (DS), various graph learning challenges have been effectively tackled through a rapid, spontaneous natural annealing process. This method has attracted increasing attention due to its orders-of-magnitude improvements in speed and energy efficiency compared to traditional Graph Neural Network (GNN) approaches for inference tasks. However, (1) the current DS hardware only supports inference, missing its native solution for training; while relying on conventional hardware is likely more expensive than GNNs. (2) The current DS architecture only allows linear interactions among its nodes, limiting training accuracy. In this work, we present a Dynamical-System Training-Processing Unit (DS-TPU) developed through algorithm-architecture co-design to tackle the two major challenges: (1) An on-device lifelong learning mechanism that leverages feedback electric current as the loss function in response to the observed training data, allowing electron-speed refinement on the present model parameters. (2) A nonlinear DS node interaction mechanism constructed from Chebyshev polynomials to significantly improve the compatibility between the DS hardware and the embedded relation of graph data. Extensive evaluations using six real-world graph learning applications demonstrate that for accuracy, DS-TPU achieves 10.8% MAE reduction over the best results of five widely used GNNs. In terms of training performance, the 5-Watt DS-TPU architecture achieves on-average 810 × speedup over the offline training for DS on an Nvidia A100 GPU, and 640 × over GNN training on the same GPU. In terms of inference performance, DS-TPU achieves 2548 × over the A100 GPU and 115 × over the best state-of-the-art GNN accelerators.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731091"}, {"primary_key": "183290", "vector": [], "sparse_vector": [], "title": "WSC-LLM: Efficient LLM Service and Architecture Co-exploration for Wafer-scale Chips.", "authors": ["<PERSON>", "Dehao Kong", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The deployment of large language models (LLMs) imposes significant demands on computing, memory, and communication resources. Wafer-scale technology enables the high-density integration of multiple single-die chips with high-speed Die-to-Die (D2D) interconnections, presenting a promising solution to meet these demands arising from LLMs. However, given the limited wafer area, a trade-off needs to be made among computing, storage, and communication resources. Maximizing the benefits and minimizing the drawbacks of wafer-scale technology is crucial for enhancing the performance of LLM service systems, which poses challenges to both architecture and scheduling. Unfortunately, existing methods cannot effectively address these challenges. To bridge the gap, we propose WSC-LLM, an architecture and scheduling co-exploration framework. We first define a highly configurable general hardware template designed to explore optimal architectural parameters for wafer-scale chips. Based on it, we capitalize on the high D2D bandwidth and fine-grained operation advantages inherent to wafer-scale chips to investigate optimal disaggregated scheduling strategies, effectively addressing the highly dynamic demands of LLM workloads. Compared to the state-of-the-art (SOTA) LLM service systems, WSC-LLM can achieve an average overall performance improvement of 3.12 × across various LLM models and datasets. Moreover, we leverage WSC-LLM to reveal intriguing insights about wafer-scale architecture design and the execution of LLM workloads.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731101"}, {"primary_key": "183291", "vector": [], "sparse_vector": [], "title": "SpecEE: Accelerating Large Language Model Inference with Speculative Early Exiting.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Early exiting has recently emerged as a promising technique for accelerating large language models (LLMs) by effectively reducing the hardware computation and memory access. In this paper, we identify that the LLM vocabulary serves as the runtime search space of the early exiting predictor and significantly influences the predictor workload (e.g., \\( \\sim 20\\% \\) overall inference latency with ∼ 3 × 104 vocabulary size in Llama2). We propose a novel paradigm using speculative models to reduce this search space, while addressing three critical challenges for further predictor optimization. (1) Time-consuming predictor with high computational complexity. Current predictor designs leverage basic models with high-dimensional input that ignore inherent data variation and GPU parallelization opportunities, resulting in \\( \\sim 15\\% \\) overall inference latency. (2) Under-utilization of layer-wise predictor deployment. Current early exiting systems treat the predictor in each layer equally without considering the activation frequencies of layer-wise predictors, leading to \\( \\sim 20\\% \\) inference overhead. (3) Exponential mapping complexity of predictor in speculative decoding. Each token in the token tree of speculative decoding is treated as an independent search space when applying the current early exiting mapping, leading to exponential mapping complexity and failing to incorporate the high-throughput benefits To address the above challenges, we present SpecEE, a fast LLM inference engine with speculative early exiting. (1) At the algorithm level, we propose the speculation-based lightweight predictor design by exploiting the probabilistic correlation between the speculative tokens and the correct results and high parallelism of GPUs. (2) At the system level, we point out that not all layers need a predictor and design the two-level heuristic predictor scheduling engine based on skewed distribution and contextual similarity. (3) At the mapping level, we point out that different decoding methods share the same essential characteristics, and propose the context-aware merged mapping for predictor with efficient GPU implementations to support speculative decoding, and form a framework for various existing orthogonal acceleration techniques (e.g., quantization and sparse activation) on cloud and personal computer (PC) scenarios, successfully pushing the Pareto frontier of accuracy and speedup. It is worth noting that SpecEE can be applied to any LLM by negligible training overhead in advance without affecting the model’s original parameters. Extensive experiments show that SpecEE achieves 2.25 × and 2.43 × speedup with Llama2-7B on cloud and PC scenarios respectively. The code is open-sourced in https://github.com/infinigence/SpecEE", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3730996"}, {"primary_key": "183292", "vector": [], "sparse_vector": [], "title": "Bishop: Sparsified Bundling Spiking Transformers on Heterogeneous Cores with Error-constrained Pruning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Spiking neural networks(SNNs) have emerged as a promising solution for deployment on resource-constrained edge devices and neuromorphic hardware due to their low power consumption. Spiking transformers, which integrate attention mechanisms similar to those found in artificial neural networks (ANNs), have recently exhibited impressive performance. However, these models are large in size and involve high-volume computation in both time and space, posing significant challenges for efficient hardware acceleration. We present <PERSON>, the first dedicated hardware accelerator architecture and HW/SW co-design framework for spiking transformers that optimally represents, manages, and processes spike-based workloads while exploring spatiotemporal sparsity and data reuse. Specifically, we introduce the concept of Token-Time Bundle (TTB), a container that bundles spiking data of a set of tokens over multiple time points. Our heterogeneous accelerator architecture <PERSON> concurrently processes workload packed in TTBs and explores intra- and inter-bundle multiple-bit weight reuse to significantly reduce memory access. <PERSON> utilizes a stratifier, a dense core array, and a sparse core array to process MLP blocks and projection layers. The stratifier routes high-density spiking activation workload to the dense core and low-density counterpart to the sparse core, ensuring optimized processing tailored to the given spatiotemporal sparsity level. To further reduce data access and computation, we introduce a novel Bundle Sparsity-Aware (BSA) training pipeline that enhances not only the overall but also structured TTB-level firing sparsity. Moreover, the processing efficiency of self-attention layers is boosted by the proposed Error-Constrained TTB Pruning (ECP), which trims activities in spiking queries, keys, and values both before and after the computation of spiking attention maps with a well-defined error bound. Finally, we design a reconfigurable TTB spiking attention core to efficiently compute spiking attention maps by executing highly simplified “AND” and “Accumulate” operations. On average, Bishop achieves a 5.91 × speedup and 6.11 × improvement in energy efficiency over previous SNN accelerators, while delivering higher accuracy across multiple datasets.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731063"}, {"primary_key": "183293", "vector": [], "sparse_vector": [], "title": "BingoGCN: Towards Scalable and Efficient GNN Acceleration with Fine-Grained Partitioning and SLT.", "authors": ["Jiale Yan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Graph Neural Networks (GNNs) are increasingly popular due to their wide applicability to tasks requiring the understanding of unstructured graph data, such as those in social network analysis and autonomous driving. However, real-time, large-scale GNN inference faces challenges due to the large size of node features and adjacency matrices, leading to memory communication and buffer size overheads caused by irregular memory access patterns. While graph partitioning can help with localized access patterns and reduction in on-chip buffer size, fine-grained partitioning results in increased inter-partition edges and off-chip memory accesses, negatively impacting overall performance. To overcome these limitations, we propose BingoGCN, a scalable GNN acceleration framework that introduces multidimensional dynamic feature summarization called Cross-Partition Message Quantization (CMQ) for inter-partition message passing. This eliminates irregular off-chip memory access without additional training and accuracy loss, even with fine-grained partitioning. By shifting the bottleneck from memory to computation, BingoGCN allows for further performance optimization through the Strong Lottery Ticket (SLT) theory using randomly generated weights. BingoGCN addresses the challenge of SLT’s unstructured sparsity in hardware acceleration with a novel training algorithm and random weight generator designs, enabling fine-grained (FG) sparsity and improved load balancing. We integrated CMQ and FG-SLT into the message-passing of GNNs and designed an efficient hardware architecture to support this flow. Our FPGA-based implementation achieves a significant reduction in memory accesses while preserving accuracy comparable to the original models.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731115"}, {"primary_key": "183294", "vector": [], "sparse_vector": [], "title": "DiTile-DGNN: An Efficient Accelerator for Distributed Dynamic Graph Neural Network Inference.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Dynamic Graph Neural Networks (DGNNs) have recently emerged as a promising model for learning complex temporal and spatial relationships in evolving graphs. The performance of DGNNs is enabled by the simultaneous integration of both graph neural networks (GNNs) and recurrent neural networks (RNNs). Despite the theoretical advancements, the design space of such complex models has significantly exploded due to the combinatorial challenges of heterogeneous computation kernels and intricate data dependency (i.e., intra- and inter-snapshot data dependency). This makes the computations of DGNN hard to scale, posing significant challenges in parallelism, data reuse, and communication. To address this challenge, we propose DiTile-DGNN, an efficient accelerator for large-scale DGNN execution. The proposed DiTile-DGNN consists of a redundancy-free parallelism strategy, workload balance optimization, and a reconfigurable accelerator architecture. Specifically, we propose a redundancy-free framework that can efficiently find an efficient parallelism strategy that can fully eliminate the data redundancy between graph snapshots while minimizing the communication complexity. Additionally, we propose a workload balance optimization for DGNN models to enhance resource utilization and eliminate synchronization overhead between snapshots. Lastly, we propose a reconfigurable accelerator architecture, with a flexible interconnect, that can be dynamically configured in support of various DGNN dataflows. Our simulations demonstrate that DiTile-DGNN achieves 48.4%, 56.1%, 23.2%, and 36.1% reductions in execution time and 83.4%, 84.0%, 75.6%, and 71.4% improvements in energy efficiency compared to state-of-the-art accelerators, including ReaDy [20], DGNN-Booster [8], RACE [51], and MEGA [12], on average across multiple DGNN datasets.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731017"}, {"primary_key": "183295", "vector": [], "sparse_vector": [], "title": "PD Constraint-aware Physical/Logical Topology Co-Design for Network on Wafer.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sihan Guan", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Wang", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "As cluster scales for LLM training expand, waferscale chips, characterized by the high integration density and bandwidth, emerge as a promising approach to enhancing training performance. The role of Network on Wafer (NoW) is becoming increasingly significant, which puts an emphasis on two facts: physical and logical topology. However, existing networks fail to co-design both aspects. Additionally, physical topology typically focuses on optimizing communication or computation separately, neglecting opportunities to improve overall training performance. In this paper, we propose a physical design (PD) constraint-aware joint optimization strategy, developing mesh-switch physical topology and a dual-granularity logical topology. Mesh-switch leverages the high integration density of mesh and the efficient communication performance of fat tree, optimizing the allocation of on-chip communication and computation resources thoroughly considering the physical constraints of waferscale chips. Furthermore, we conduct a DSE algorithm to search for the optimal mesh-switch configuration. Based on the proposed physical topology, we design the most appropriate logical topology, and further enhance bandwidth utilization through a fine-grained overlap strategy. Evaluation results demonstrate that our NoW design achieves nearly a 2.39 × performance improvement in LLM training compared to existing networks. Our comprehensive design approach, which integrates physical and logical topologies with constraint considerations, can also be applied to network designs in other contexts.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731045"}, {"primary_key": "183296", "vector": [], "sparse_vector": [], "title": "ArtMem: Adaptive Migration in Reinforcement Learning-Enabled Tiered Memory.", "authors": ["Xinyue Yi", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "With the increasing memory demands of emerging applications, tiered memory has become a viable solution for reducing data center hardware costs. Given the low performance of the capacity tiers in tiered memory systems, optimizing memory management is crucial in improving overall system performance. This paper identifies three key limitations in existing tiered memory solutions. First, existing solutions often perform differently across different workloads, leading to suboptimal performance in some workloads. Second, they often fail to adjust migration strategies in response to low fast memory tier access rates, resulting in ineffective data placement. Third, they often miss the opportunity to dynamically tune the memory migration scope based on workload patterns, leading to unnecessary page migrations and under-utilization of tiered memory potential. This paper proposes ArtMem, a reinforcement learning (RL)-driven framework that dynamically manages tiered memory systems and adapts to workload evolution to address these limitations. ArtMem enables better placement of memory pages, enhancing system performance while reducing unnecessary migrations. Experimental evaluations show that ArtMem outperforms state-of-the-art tiering systems, achieving 35% - 172% performance improvements over diverse workloads.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731001"}, {"primary_key": "183297", "vector": [], "sparse_vector": [], "title": "Cramming a Data Center into One Cabinet, a Co-Exploration of Computing and Hardware Architecture of Waferscale Chip.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Dingcheng Jiang", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The rapid advancements in large language models (LLMs) have significantly increased hardware demands. Wafer-scale chips, which integrate numerous compute units on an entire wafer, offer a high-density computing solution for data centers and can extend Moore’s Law at system level. However, current wafer-scale data center architectures face inefficiencies, such as uncoordinated resource allocation and lack of co-optimization for system area, preventing optimal integration density and performance within given cost and physical constraints. We propose a co-exploration approach of computing and hardware architectures to bridge this gap. We first develop an optimized wafer-scale single-cabinet data center model, integrating configurable on-chip memory dies and employing a vertically stacked hardware architecture. Based on this model, we introduce Titan, an automated exploration framework for intra-chip and inter-chip architecture design and optimization. Based on the architecture features of wafer-scale systems with optimal integration density, Titan establishes parameter dependencies to co-design the computing and hardware architectures. To reduce the design cycle for wafer-scale systems, Titan introduces vertical area constraints and pre-checks physical limits by integrating a series of reliability prediction models. It also integrates hardware capacity and cost evaluations to enable multi-objective optimization. Under the same cost constraint, on average, Titan’s cabinet design architecture improves system computing capacity by 2.90 ×, communication bandwidth by 2.11 ×, and memory bandwidth by 11.23 × compared to the state-of-the-art Dojo-like wafer-scale single tray architecture. Simulated in different scenarios, Titan’s design delivers 3.17 × and 10.66 × performance improvement for Llama2-7B and Llama2-72B. Moreover, we leverage Titan to uncover insights into the optimal single-chip area choice within a cabinet under different cost constraints.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731016"}, {"primary_key": "183298", "vector": [], "sparse_vector": [], "title": "CORD: Low-Latency, Bandwidth-Efficient and Scalable Release Consistency via Directory Ordering.", "authors": ["<PERSON><PERSON><PERSON> Yu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Increasingly, multi-processing unit (PU) systems (e.g., CPU-GPU, multi-CPU, multi-GPU, etc.) are embracing cache-coherent shared memory to facilitate inter-PU communication. The coherence protocols in these systems support write-through accesses that place the data directly at the LLC to enable efficient producer-consumer communications pervasive in AI/ML workloads. Moreover, release consistency has emerged as the standard memory model in such systems due to its programming simplicity and ability to support high performance. In today’s multi-PU systems, the source processor that issues the writes also orders them to enforce release consistency, even for write-through accesses. Unfortunately, such source ordering of write-through operations results in unnecessary communications between the source processor and the LLC directory, incurring significant performance, interconnect traffic, and energy overheads for multi-PU applications. To eliminate such communication, we present cord 1, a novel cache coherence protocol that orders write-through accesses directly at the cache directory. cord employs several novel mechanisms to minimize the metadata required for ordering traffic while efficiently scaling to multiple directories. Evaluations atop the gem5 simulator show that compared to source ordering, cord improves application performance by \\( 24\\% \\) and reduces traffic by \\( 13\\% \\) on average while incurring \\( \\lt 1\\% \\) storage, area, and power overheads. Compared to hand-optimized message-passing implementations, cord observes a mere \\( 3\\% \\) performance overhead and \\( 6\\% \\) more traffic on average with a significantly simpler programming model.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731074"}, {"primary_key": "183299", "vector": [], "sparse_vector": [], "title": "PuDHammer: Experimental Analysis of Read Disturbance Effects of Processing-using-DRAM in Real DRAM Chips.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Ataberk Olgun", "Oguzhan Canpolat", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Processing-using-DRAM (PuD) is a promising paradigm for alleviating the data movement bottleneck using a DRAM array’s massive internal parallelism and bandwidth to execute very wide data-parallel operations. Performing a PuD operation involves activating multiple DRAM rows in quick succession or simultaneously, i.e., multiple-row activation. Multiple-row activation is fundamentally different from conventional memory access patterns that activate one DRAM row at a time. However, repeatedly activating even one DRAM row (e.g., RowHammer) can induce bitflips in unaccessed DRAM rows because modern DRAM is subject to read disturbance, a worsening safety, security, and reliability issue. Unfortunately, no prior work investigates the effects of multiple-row activation, as commonly used by PuD operations, on DRAM read disturbance. In this paper, we present the first characterization study of read disturbance effects of multiple-row activation-based PuD (which we call PuDHammer) using 316 real DDR4 DRAM chips from four major DRAM manufacturers. Our detailed characterization results covering various operational conditions and parameters (i.e., temperature, data patterns, access patterns, timing parameters, and spatial variation) show that 1) PuDHammer significantly exacerbates the read disturbance vulnerability, causing up to 158.58 × reduction in the minimum hammer count required to induce the first bitflip (HCfirst), compared to <PERSON><PERSON><PERSON>mer, 2) PuDHammer is affected by various operational conditions and parameters, 3) combining RowH<PERSON>mer with PuDHammer is more effective than using RowHammer alone to induce read disturbance errors (e.g., compared to RowHammer, doing so reduces HCfirst by 1.66 × on average across all tested rows), and 4) PuDHammer bypasses an in-DRAM RowHammer mitigation mechanism called Target Row Refresh and induces more bitflips than RowHammer. To develop future robust PuD-enabled systems in the presence of PuDHammer, we 1) develop three countermeasures and 2) adapt and evaluate the effectiveness of state-of-the-art RowHammer mitigation standardized by industry, called Per Row Activation Counting (PRAC). We show that the adapted PRAC incurs large performance overheads to mitigate PuDHammer (e.g., an average performance overhead of 48.26% across 60 five-core multiprogrammed workloads). We hope and expect that our findings motivate and guide system-level and architectural solutions to enable read-disturbance-resilient future PuD systems.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731030"}, {"primary_key": "183300", "vector": [], "sparse_vector": [], "title": "QPlacer: Frequency-Aware Component Placement for Superconducting Quantum Computers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Hai <PERSON>", "<PERSON><PERSON>"], "summary": "Quantum Computers face a critical limitation in qubit numbers, hindering their progression towards large-scale and fault-tolerant quantum computing. A significant challenge impeding scaling is crosstalk, characterized by unwanted interactions among neighboring components on quantum chips, including qubits, resonators, and substrates. We motivate a general approach to systematically resolving multifaceted crosstalks in a limited substrate area. We propose QPlacer, a frequency-aware electrostatic-based placement framework tailored for superconducting quantum computers, to alleviate crosstalk by isolating these components in spatial and frequency domains alongside compact substrate design. QPlacer commences with a frequency assigner that ensures frequency domain isolation for qubits and resonators. It then incorporates a padding strategy and resonator partitioning for layout flexibility. Central to our approach is the conceptualization of quantum components as charged particles, enabling strategic spatial isolation through a ‘frequency repulsive force’ concept. Our results demonstrate that <PERSON><PERSON><PERSON><PERSON> carefully crafts the physical component layout in mitigating various crosstalk impacts while maintaining a compact substrate size. On various device topologies and NISQ benchmarks, QPlacer improves fidelity by an average of 37.5 × and reduces spatial violations (susceptible to crosstalk) by an average of 12.76 ×, compared to classical placement engines. Regarding area optimization, compared to manual designs, QPlacer can reduce the required layout area by 2.14 × on average.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3730994"}, {"primary_key": "183301", "vector": [], "sparse_vector": [], "title": "AIM: Software and Hardware Co-design for Architecture-level IR-drop Mitigation in High-performance PIM.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Guangyu Sun"], "summary": "SRAM Processing-in-Memory (PIM) has emerged as the most promising implementation for high-performance PIM, delivering superior computing density, energy efficiency, and computational precision. However, the pursuit of higher performance necessitates more complex circuit designs and increased operating frequencies, which exacerbate IR-drop issues. Severe IR-drop can significantly degrade chip performance and even threaten reliability. Conventional circuit-level IR-drop mitigation methods, such as back-end optimizations, are resource-intensive and often compromise power, performance, and area (PPA). To address these challenges, we propose AIM, comprehensive software and hardware co-design for architecture-level IR-drop mitigation in high-performance PIM. Initially, leveraging the bit-serial and in-situ dataflow processing properties of PIM, we introduce Rtog and HR, which establish a direct correlation between PIM workloads and IR-drop. Building on this foundation, we propose LHR and WDS, enabling extensive exploration of architecture-level IR-drop mitigation while maintaining computational accuracy through software optimization. Subsequently, we develop IR-Booster, a dynamic adjustment mechanism that integrates software-level HR information with hardware-based IR-drop monitoring to adapt the V-f pairs of the PIM macro, achieving enhanced energy efficiency and performance. Finally, we propose the HR-aware task mapping method, bridging software and hardware designs to achieve optimal improvement. Post-layout simulation results on a 7nm 256-TOPS PIM chip demonstrate that AIM achieves up to 69.2% IR-drop mitigation, resulting in 2.29 × energy efficiency improvement and 1.152 × speedup.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3730987"}, {"primary_key": "183302", "vector": [], "sparse_vector": [], "title": "IDEA-GP: Instruction-Driven Architecture with Efficient Online Workload Allocation for Geometric Perception.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Jincheng Yu", "<PERSON>"], "summary": "The algorithmic complexity of robotic systems presents significant challenges to achieving generalized acceleration in robot applications. On the one hand, the diversity of operators and computational flows within similar task categories prevents the reuse of specialized computational units. On the other hand, task variations and environmental dynamics can cause workload fluctuations, leading to inefficient resource utilization. This paper focuses on the geometric perception capability of robots, taking localization and mapping as the basic applications, and proposes IDEA-GP, an Instruction-Driven Architecture with Efficient online workload Allocation for Geometric Perception. Built around an array of general computational units designed for spatial positioning representations, IDEA-GP supports a wide range of robot pose-related computational tasks. IDEA-GP employs a compiler to perform online workload analysis and resource allocation. It generates instructions tailored to processing elements (PEs) to schedule computations, thereby accelerating optimization problems and enhancing geometric perception performance. Deployed on the ZCU102 evaluation board, IDEA-GP demonstrates an average speedup of 7.5 × over the Intel CPU and 19.7 × over the ARM CPU in Simultaneous Localization and Mapping (SLAM) tasks, and a 16.4 × speedup over the Intel CPU and 41.6 × over the ARM CPU in Structure from Motion (SfM) tasks.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731080"}, {"primary_key": "183303", "vector": [], "sparse_vector": [], "title": "SwitchQNet: Optimizing Distributed Quantum Computing for Quantum Data Centers with Switch Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Distributed Quantum Computing (DQC) provides a scalable architecture by interconnecting multiple quantum processor units (QPUs). Among various DQC implementations, quantum data centers (QDCs) — where QPUs in different racks are connected through reconfigurable optical switch networks — are becoming feasible in the near term. However, the latency of cross-rack communications and dynamic switch reconfigurations poses unique challenges to communications in QDCs, significantly increasing the overall latency, thereby also reducing the overall fidelity. In this paper, we address these challenges by introducing a novel compiler that optimizes scheduling of communications across the program and network layers. Our evaluation shows that it reduces the overall latency by \\( {\\color {black}8.02}\\times \\) over prior approaches with a small overhead and can be integrated with quantum error correction (QEC) to facilitate fault-tolerant quantum computing (FTQC). We have open-sourced our codes at https://zenodo.org/records/15377656.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731046"}, {"primary_key": "183304", "vector": [], "sparse_vector": [], "title": "RTSpMSpM: Harnessing Ray Tracing for Efficient Sparse Matrix Computations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The significance of sparse matrix algebra pushes the development of sparse matrix accelerators. Despite the general reception of using hardware accelerators to address application demands and the convincement of substantial performance gain, integrating heterogeneous hardware accelerators always introduces manufacturing costs on new hardware and challenges the system interconnects. Inspired by the similar algorithmic behaviors between ray tracing and sparse matrix problems, this paper exploits ray tracing hardware. This increasingly popular accelerator has become the standard in modern GPU architectures to address the demand for virtual/augmented/mixed reality applications. We propose an algorithm that maps the most classical sparse matrix multiplication problem (SpMSpM) as a ray tracing problem. By implementing the proposed algorithm using a modern ray tracing programming framework, the resulting program, SW-RTSpMSpM can leverage the accelerated intersection test unit in commercialized GPUs and reveals 1.85 × speedup over the state-of-the-art SpMSpM library. Despite the “similarities” in both problems enabling the potential of accelerating SpMSpM using existing ray tracing hardware, the “difference” between these two algorithms leaves room for performance gain with architectural optimizations. The insights from evaluating SW-RTSpMSpM guide the proposal of RT+SpMSpM, where we present two major architectural optimizations that (1) allow simultaneous computation on multiplications along with intersection tests and (2) ray mapping and scheduling and a small row accumulation engine to leverage <PERSON><PERSON>’s dataflow and eliminate the reliance of additional shader functions and redundant memory operations. The resulting RT+SpMSpM only introduces 0.2% area overhead to a modern high-end ray-tracing-hardware-equipped GPU and improves SW-RTSpMSpM by 1.66 ×, achieving 3.06 × speedup over software library and 80% performance per area compared to a state-of-the-art SpMSpM accelerator, without losing the capability in supporting ray tracing.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731072"}, {"primary_key": "183305", "vector": [], "sparse_vector": [], "title": "Insights into DeepSeek-V3: Scaling Challenges and Reflections on Hardware for AI Architectures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chong <PERSON>", "Damai Dai", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shi<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Wang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "The rapid scaling of large language models (LLMs) has unveiled critical limitations in current hardware architectures, including constraints in memory capacity, computational efficiency, and interconnection bandwidth. DeepSeek-V3, trained on 2,048 NVIDIA H800 GPUs, demonstrates how hardware-aware model co-design can effectively address these challenges, enabling cost-efficient training and inference at scale. This paper presents an in-depth analysis of the DeepSeek-V3/R1 model architecture and its AI infrastructure, highlighting key innovations such as Multi-head Latent Attention (MLA) for enhanced memory efficiency, Mixture of Experts (MoE) architectures for optimized computation-communication trade-offs, FP8 mixed-precision training to unlock the full potential of hardware capabilities, and a Multi-Plane Network Topology to minimize cluster-level network overhead. Building on the hardware bottlenecks encountered during DeepSeek-V3’s development, we engage in a broader discussion with academic and industry peers on potential future hardware directions, including precise low-precision computation units, scale-up and scale-out convergence, and innovations in low-latency communication fabrics. These insights underscore the critical role of hardware and model co-design in meeting the escalating demands of AI workloads, offering a practical blueprint for innovation in next-generation AI systems.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731412"}, {"primary_key": "183306", "vector": [], "sparse_vector": [], "title": "UGPU: Dynamically Constructing Unbalanced GPUs for Enhanced Resource Efficiency.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Huadong Dai"], "summary": "Different GPU generations have various numbers of SMs but still keep the balanced idea during the manufacture, i.e., the proportion of compute and memory resources within a single physical GPU is similar. Although GPU applications have different characteristics, it is still uncommon and uneconomic to build unbalanced physical GPUs for customers. With their powerful computational capabilities, GPUs are widely used in the cloud to accelerate diverse workloads from multiple users, creating opportunities to explore the unbalanced GPU concept in multitasking environments. In this paper, we take the first step in exploring the feasibility and performance benefits of building unbalanced GPUs. Specifically, these unbalanced GPUs, referred to as GPU slices, are dynamically constructed with dedicated compute and memory resources from a single physical GPU to effectively address the diverse demands of co-executing applications, achieving high performance during the execution. However, there are two challenges that must to be solved. First, determining the size of unbalanced GPU slices during execution is challenging, as predicting GPU performance under varying resource allocations is inherently difficult. Second, reallocating memory resources after partitioning requires extensive data migration, with traditional methods leading to unacceptable performance degradation. To address the first challenge, UGPU employs a demand-aware resource partitioning algorithm that partitions resources dynamically without relying on a complex or inaccurate performance model. For the second challenge, UGPU introduces PageMove, a novel mechanism for efficient page migration between different memory dies within an HBM stack. Our key insight is that all memory channels already have physical connections to all through-silicon via (TSV) within a DRAM stack, while different bank groups can transfer data at the same time. PageMove slightly modifies DRAM architecture, uses a customized memory address mapping, designs a new parallel page migration mode (PPMM) and updates the virtual memory management scheme. By doing this, PageMove supports fast entire page migration from one memory die to another memory die which significantly reduces the data migration overhead during the memory resource reallocation. For the heterogeneous workloads with different characteristics, compared to the traditional balanced GPU design, UGPU increases the system performance by 34.3% on average while providing QoS support.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731103"}, {"primary_key": "183307", "vector": [], "sparse_vector": [], "title": "Resource Analysis of Low-Overhead Transversal Architectures for Reconfigurable Atom Arrays.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Neutral atom arrays have recently emerged as a promising platform for fault-tolerant quantum computing. Based on these advances, including dynamically-reconfigurable connectivity and fast transversal operations, we present a low-overhead architecture that supports the layout and resource estimation of large-scale fault-tolerant quantum algorithms. Utilizing recent advances in fault tolerance with transversal gate operations, this architecture achieves a run time speed-up on the order of the code distance d, which we find directly translates to run time improvements of large-scale quantum algorithms. Our architecture consists of functional building blocks of key algorithmic subroutines, including magic state factories, quantum arithmetic units, and quantum look-up tables. These building blocks are implemented using efficient transversal operations, and we design space-time efficient versions of them that minimize interaction distance, thereby reducing atom move times and minimizing the volume for correlated decoding. We further propose models to estimate their logical error performance. We perform resource estimation for a large-scale implementation of <PERSON><PERSON>’s factoring algorithm, one of the prototypical benchmarks for large-scale quantum algorithms, finding that 2048-bit RSA factoring can be executed with 19 million qubits in 5.6 days, for 1 ms QEC cycle times. This represents close to 50 × speed-up of the run-time compared to existing estimates with similar assumptions, with no increase in space footprint.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731039"}, {"primary_key": "183308", "vector": [], "sparse_vector": [], "title": "Hardware-aware Calibration Protocol for Quantum Computers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Boxi Li", "Kecheng Liu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Calibration of a quantum computer is the process of optimizing its control parameters to ensure the accurate implementation of quantum gates. It remains a critical challenge in scaling quantum computers. Existing calibration methods take a generalized approach that focuses on the trade-off between calibration time and fidelity. However, these methods lack the awareness of hardware differences among physical qubits and an elaborate design of parallel calibration. In this paper, we introduce a fine-grained calibration protocol that contains three calibration policies for hardware differences and a method to enable parallel calibration. We begin by profiling qubit pairs to evaluate their responses to different waveform candidates. Based on profiling results, we determine the best calibration policy for the quantum computer, which is the first part of the calibration protocol. The second part of our protocol is to use graph traverse to enable parallel calibration by identifying compatible calibration operations. We validate our protocol through intensive experiments on real quantum machines with up to 127 qubits. Our experimental results demonstrate a 1.84 × reduction in terms of the medium of the two-qubit gate error rate, 1.26 × reduction in pulse duration, an 8 × to 25 × reduction in total calibration overhead compared with sequential calibration, an average of 2.12 × further reduction in total calibration overhead owing to profiling policy, double of the quantum volume, and a 2.0 × to 2.3 × reduction in error per layered gate. The proposed protocol emphasizes the importance of hardware-aware and parallel calibration and advances current quantum computers towards fault-tolerant quantum computing.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731036"}, {"primary_key": "183309", "vector": [], "sparse_vector": [], "title": "S-SYNC: Shuttle and Swap Co-Optimization in Quantum Charge-Coupled Devices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Wang", "<PERSON><PERSON>"], "summary": "The Quantum Charge-Coupled Device (QCCD) architecture is a modular design to expand trapped-ion quantum computer that relies on the coherent shuttling of qubits across an array of segmented electrodes. Leveraging trapped ions for their long coherence times and high-fidelity quantum operations, QCCD technology represents a significant advancement toward practical, large-scale quantum processors. However, shuttling increases thermal motion and consistently necessitates qubit swaps, significantly extend execution time and negatively affect application success rates. In this paper, we introduce S-SYNC – a compiler designed to co-optimize the number of shuttling and swapping operations. S-SYNC exploits the unique properties of QCCD and incorporates generic SWAP operations to efficiently manage shuttle and SWAP counts simultaneously. Building on the static topology formulation of QCCD, we develop scheduling heuristics to enhance overall performance. Our evaluations demonstrate that our approach reduces the shuttling number by 3.69x on average and improves the success rate of quantum applications by 1.73x on average. Moreover, we apply S-SYNC to gain insights into executing applications across various QCCD topologies and to compare the trade-offs between different initial mapping methods.", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053.3731084"}, {"primary_key": "204940", "vector": [], "sparse_vector": [], "title": "Proceedings of the 52nd Annual International Symposium on Computer Architecture, ISCA 2025, Tokyo, Japan, June 21-25, 2025", "authors": [], "summary": "Welcome to the 42nd International Symposium on Computer Architecture (ISCA), in Portland, Oregon, June 13-17, 2015, at the Oregon Convention Center. ISCA has a long history of leadership as the top conference in the field of computer architecture. ISCA's success is because of all of you, our participants, organizers, and supporters. Thank you for coming, participating, and supporting this conference. As in previous ISCA conferences, expect to participate in technical presentations, workshops, tutorials, and networking opportunities of the highest caliber with colleagues from all over the world!", "published": "2025-01-01", "category": "isca", "pdf_url": "", "sub_summary": "", "source": "isca", "doi": "10.1145/3695053"}]