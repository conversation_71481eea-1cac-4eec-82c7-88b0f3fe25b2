[{"primary_key": "949206", "vector": [], "sparse_vector": [], "title": "Interaction Proxy Manager: Semantic Model Generation and Run-time Support for Reconstructing Ubiquitous User Interfaces of Mobile Services.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Weinan Shi", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Emerging terminals, such as smartwatches, true wireless earphones, in-vehicle computers, etc., are complementing our portals to ubiquitous information services. However, the current ecology of information services, encapsulated into millions of mobile apps, is largely restricted to smartphones; accommodating them to new devices requires tremendous and almost unbearable engineering efforts. Interaction Proxy, firstly proposed as an accessible technique, is a potential solution to mitigate this problem. Rather than re-building an entire application, Interaction Proxy constructs an alternative user interface that intercepts and translates interaction events and states between users and the original app's interface. However, in such a system, one key challenge is how to robustly and efficiently \"communicate\" with the original interface given the instability and dynamicity of mobile apps (e.g., dynamic application status and unstable layout). To handle this, we first define UI-Independent Application Description (UIAD), a reverse-engineered semantic model of mobile services, and then propose Interaction Proxy Manager (IPManager), which is responsible for synchronizing and managing the original apps' interface, and providing a concise programming interface that exposes information and method entries of the concerned mobile services. In this way, developers can build alternative interfaces without dealing with the complexity of communicating with the original app's interfaces. In this paper, we elaborate the design and implementation of our IPManager, and demonstrate its effectiveness by developing three typical proxies, mobile-smartwatch, mobile-vehicle and mobile-voice. We conclude by discussing the value of our approach to promote ubiquitous computing, as well as its limitations.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610929"}, {"primary_key": "949280", "vector": [], "sparse_vector": [], "title": "Cross-Modality Graph-based Language and Sensor Data Co-Learning of Human-Mobility Interaction.", "authors": ["<PERSON><PERSON>", "Suining He", "<PERSON>"], "summary": "Learning the human--mobility interaction (HMI) on interactive scenes (e.g., how a vehicle turns at an intersection in response to traffic lights and other oncoming vehicles) can enhance the safety, efficiency, and resilience of smart mobility systems (e.g., autonomous vehicles) and many other ubiquitous computing applications. Towards the ubiquitous and understandable HMI learning, this paper considers both \"spoken language\" (e.g., human textual annotations) and \"unspoken language\" (e.g., visual and sensor-based behavioral mobility information related to the HMI scenes) in terms of information modalities from the real-world HMI scenarios. We aim to extract the important but possibly implicit HMI concepts (as the named entities) from the textual annotations (provided by human annotators) through a novel human language and sensor data co-learning design. To this end, we propose CG-HMI, a novel Cross-modality Graph fusion approach for extracting important Human-Mobility Interaction concepts from co-learning of textual annotations as well as the visual and behavioral sensor data. In order to fuse both unspoken and spoken \"languages\", we have designed a unified representation called the human--mobility interaction graph (HMIG) for each modality related to the HMI scenes, i.e., textual annotations, visual video frames, and behavioral sensor time-series (e.g., from the on-board or smartphone inertial measurement units). The nodes of the HMIG in these modalities correspond to the textual words (tokenized for ease of processing) related to HMI concepts, the detected traffic participant/environment categories, and the vehicle maneuver behavior types determined from the behavioral sensor time-series. To extract the inter- and intra-modality semantic correspondences and interactions in the HMIG, we have designed a novel graph interaction fusion approach with differentiable pooling-based graph attention. The resulting graph embeddings are then processed to identify and retrieve the HMI concepts within the annotations, which can benefit the downstream human-computer interaction and ubiquitous computing applications. We have developed and implemented CG-HMI into a system prototype, and performed extensive studies upon three real-world HMI datasets (two on car driving and the third one on e-scooter riding). We have corroborated the excellent performance (on average 13.11% higher accuracy than the other baselines in terms of precision, recall, and F1 measure) and effectiveness of CG-HMI in recognizing and extracting the important HMI concepts through cross-modality learning. Our CG-HMI studies also provide real-world implications (e.g., road safety and driving behaviors) about the interactions between the drivers and other traffic participants.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610904"}, {"primary_key": "949299", "vector": [], "sparse_vector": [], "title": "Detecting Social Contexts from Mobile Sensing Indicators in Virtual Interactions with Socially Anxious Individuals.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Mobile sensing is a ubiquitous and useful tool to make inferences about individuals' mental health based on physiology and behavior patterns. Along with sensing features directly associated with mental health, it can be valuable to detect different features of social contexts to learn about social interaction patterns over time and across different environments. This can provide insight into diverse communities' academic, work and social lives, and their social networks. We posit that passively detecting social contexts can be particularly useful for social anxiety research, as it may ultimately help identify changes in social anxiety status and patterns of social avoidance and withdrawal. To this end, we recruited a sample of highly socially anxious undergraduate students (N=46) to examine whether we could detect the presence of experimentally manipulated virtual social contexts via wristband sensors. Using a multitask machine learning pipeline, we leveraged passively sensed biobehavioral streams to detect contexts relevant to social anxiety, including (1) whether people were in a social situation, (2) size of the social group, (3) degree of social evaluation, and (4) phase of social situation (anticipating, actively experiencing, or had just participated in an experience). Results demonstrated the feasibility of detecting most virtual social contexts, with stronger predictive accuracy when detecting whether individuals were in a social situation or not and the phase of the situation, and weaker predictive accuracy when detecting the level of social evaluation. They also indicated that sensing streams are differentially important to prediction based on the context being predicted. Our findings also provide useful information regarding design elements relevant to passive context detection, including optimal sensing duration, the utility of different sensing modalities, and the need for personalization. We discuss implications of these findings for future work on context detection (e.g., just-in-time adaptive intervention development).", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610916"}, {"primary_key": "949143", "vector": [], "sparse_vector": [], "title": "Hierarchical Wi-Fi Trajectory Embedding for Indoor User Mobility Pattern Analysis.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The recent advances in smart building technologies have enabled us to collect massive Wi-Fi network based trajectory data, which provide an unparalleled opportunity for understanding the indoor user mobility pattern and enabling a wide range of business applications. While some previous studies have explored the Wi-Fi positioning of users, there still lacks a systematic and effective solution for indoor user mobility pattern analysis based on Wi-Fi trajectory data. To this end, in this paper, we propose a unified framework for modeling Wi-Fi trajectory data, namely HWTE, which can empower various tasks of indoor user mobility pattern analysis, such as user classification, next location prediction and schedule estimation. Specifically, we first propose a session trajectory construction module to extract the spatio-temporal semantic information from the Wi-Fi trajectories of users. Then, we devise a pre-training module to learn the unified representation of Wi-Fi trajectories. In particular, a session position embedding technique and a position query task is introduced to enhance the representation ability of the whole trajectory. Moreover, we further propose a hierarchical Transformer-based fine-tuning module to support various application tasks with time and space efficiency. Finally, we validate our framework on a real-world dataset with all three kinds of downstream tasks.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596237"}, {"primary_key": "949146", "vector": [], "sparse_vector": [], "title": "PARROT: Interactive Privacy-Aware Internet of Things Application Design Tool.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>mer F. Rana", "<PERSON><PERSON>"], "summary": "Internet of Things (IoT) applications typically collect and analyse personal data that is categorised as sensitive or special category of personal data. These data are subject to a higher degree of protection under data privacy laws. Regardless of legal requirements to support privacy practices, such as in Privacy by Design (PbD) schemes, these practices are not yet commonly followed by software developers. The difficulty of developing privacy-preserving applications emphasises the importance of exploring the problems developers face to embed privacy techniques, suggesting the need for a supporting tool. An interactive IoT application design tool - PARROT (PrivAcy by design tool foR inteRnet Of Things) - is presented. This tool helps developers to design privacy-aware IoT applications, taking account of privacy compliance during the design process and providing real-time feedback on potential privacy violations. A user study with 18 developers was conducted, comprising a semi-structured interview and a design exercise to understand how developers typically handle privacy within the design process. Collaboration with a privacy lawyer was used to review designs produced by developers to uncover privacy limitations that could be addressed by developing a software tool. Based on the findings, a proof-of-concept prototype of PARROT was implemented and evaluated in two controlled lab studies. The outcome of the study indicates that IoT applications designed with PARROT addressed privacy concerns better and managed to reduce several of the limitations identified. From a privacy compliance perspective, PARROT helps developers to address compliance requirements throughout the design and testing process. This is achieved by incorporating privacy specific design features into the IoT application from the beginning rather than retrospectively. (Demo Video).", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580880"}, {"primary_key": "949148", "vector": [], "sparse_vector": [], "title": "E3D: Harvesting Energy from Everyday Kinetic Interactions Using 3D Printed Attachment Mechanisms.", "authors": ["<PERSON><PERSON> Al Arab<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The increase of distributed embedded systems has enabled pervasive sensing, actuation, and information displays across buildings and surrounding environments, yet also entreats huge cost expenditure for energy and human labor for maintenance. Our daily interactions, from opening a window to closing a drawer to twisting a doorknob, are great potential sources of energy but are often neglected. Existing commercial devices to harvest energy from these ambient sources are unaffordable, and DIY solutions are left with inaccessibility for non-experts preventing fully imbuing daily innovations in end-users. We present E3D, an end-to-end fabrication toolkit to customize self-powered smart devices at low cost. We contribute to a taxonomy of everyday kinetic activities that are potential sources of energy, a library of parametric mechanisms to harvest energy from manual operations of kinetic objects, and a holistic design system for end-user developers to capture design requirements by demonstrations then customize augmentation devices to harvest energy that meets unique lifestyle.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610897"}, {"primary_key": "949162", "vector": [], "sparse_vector": [], "title": "HMGAN: A Hierarchical Multi-Modal Generative Adversarial Network Model for Wearable Human Activity Recognition.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Wearable Human Activity Recognition (WHAR) is an important research field of ubiquitous and mobile computing. Deep WHAR models suffer from the overfitting problem caused by the lack of a large amount and variety of labeled data, which is usually addressed by generating data to enlarge the training set, i.e., Data Augmentation (DA). Generative Adversarial Networks (GANs) have shown their excellent data generation ability, and the generalization ability of a classification model can be improved by GAN-based DA. However, existing GANs cannot make full use of the important modality information and fail to balance modality details and global consistency, which cannot meet the requirements of deep multi-modal WHAR. In this paper, a hierarchical multi-modal GAN model (HMGAN) is proposed for WHAR. HMGAN consists of multiple modal generators, one hierarchical discriminator, and one auxiliary classifier. Multiple modal generators can learn the complex multi-modal data distributions of sensor data. Hierarchical discriminator can provide discrimination outputs for both low-level modal discrimination losses and high-level overall discrimination loss to draw a balance between modality details and global consistency. Experiments on five public WHAR datasets demonstrate that HMGAN achieves the state-of-the-art performance for WHAR, outperforming the best baseline by an average of 3.4%, 3.8%, and 3.5% in accuracy, macro F1 score, and weighted F1 score, respectively.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610909"}, {"primary_key": "949164", "vector": [], "sparse_vector": [], "title": "MagDot: Drift-free, Wearable Joint Angle Tracking at Low Cost.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Tracking the angular movement of body joints has been a critical enabler for various applications, such as virtual and augmented reality, sports monitoring, and medical rehabilitation. Despite the strong demand for accurate joint tracking, existing techniques, such as cameras, IMUs, and flex sensors, suffer from major limitations that include occlusion, cumulative error, and high cost. These issues collectively undermine the practicality of joint tracking. We introduce MagDot, a new magnetic-based joint tracking method that enables high-accuracy, drift-free, and wearable joint angle tracking. To overcome the limitations of existing techniques, MagDot employs a novel tracking scheme that compensates for various real-world impacts, achieving high tracking accuracy. We tested MagDot on eight participants with a professional motion capture system, i.e., Qualisys motion capture system with nine Arqus A12 cameras. The results indicate MagDot can accurately track major body joints. For example, MagDot can achieve tracking accuracy of 2.72°, 4.14°, and 4.61° for elbow, knee, and shoulder, respectively. With a power consumption of only 98 mW, MagDot can support one-day usage with a small battery pack.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631423"}, {"primary_key": "949167", "vector": [], "sparse_vector": [], "title": "BodyTouch: Investigating Eye-Free, On-Body and Near-Body Touch Interactions with HMDs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents a study on the touch precision of an eye-free, body-based interface using on-body and near-body touch methods with and without skin contact. We evaluate user touch accuracy on four different button layouts. These layouts progressively increase the number of buttons between adjacent body joints, resulting in 12, 20, 28, and 36 touch buttons distributed across the body. Our study indicates that the on-body method achieved an accuracy beyond 95% for the 12- and 20-button layouts, whereas the near-body method only for the 12-button layout. Investigating user touch patterns, we applied SVM classifiers, which boost both the on-body and near-body methods to support up to the 28-button layouts by learning individual touch patterns. However, using generalized touch patterns did not significantly improve accuracy for more complex layouts, highlighting considerable differences in individual touch habits. When evaluating user experience metrics such as workload perception, confidence, convenience, and willingness-to-use, users consistently favored the 20-button layout regardless of the touch technique used. Remarkably, the 20-button layout, when applied to on-body touch methods, does not necessitate personal touch patterns, showcasing an optimal balance of practicality, effectiveness, and user experience without the need for trained models. In contrast, the near-body touch targeting the 20-button layout needs a personalized model; otherwise, the 12-button layout offers the best immediate practicality.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631426"}, {"primary_key": "949168", "vector": [], "sparse_vector": [], "title": "TwinkleTwinkle: Interacting with Your Smart Devices by Eye Blink.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent years have witnessed the rapid boom of mobile devices interweaving with changes the epidemic has made to people's lives. Though a tremendous amount of novel human-device interaction techniques have been put forward to facilitate various audiences and scenarios, limitations and inconveniences still occur to people having difficulty speaking or using their fingers/hands/arms or wearing masks/glasses/gloves. To fill the gap of such interaction contexts beyond using hands, voice, face, or mouth, in this work, we take the first step to propose a novel Human-Computer Interaction (HCI) system, TwinkleTwinkle, which senses and recognizes eye blink patterns in a contact-free and training-free manner leveraging ultrasound signals on commercial devices. Twinkle<PERSON>win<PERSON> first applies a phase difference based approach to depicting candidate eye blink motion profiles without removing any noises, followed by modeling intrinsic characteristics of blink motions through adaptive constraints to separate tiny patterns from interferences in conditions where blink habits and involuntary movements vary between individuals. We propose a vote-based approach to get final patterns designed to map with number combinations either self-defined or based on carriers like ASCII code and Morse code to make interaction seamlessly embedded with normal and well-known language systems. We implement TwinkleTwinkle on smartphones with all methods realized in the time domain and conduct extensive evaluations in various settings. Results show that TwinkleTwinkle achieves about 91% accuracy in recognizing 23 blink patterns among different people.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596238"}, {"primary_key": "949173", "vector": [], "sparse_vector": [], "title": "Come Fly With Me: Investigating the Effects of Path Visualizations in Automated Urban Air Mobility.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Automated Urban Air Mobility will enhance passenger transportation in metropolitan areas in the near future. Potential passengers, however, have little knowledge about this mobility form. Therefore, there could be concerns about safety and low trust. As trajectories are essential information to address these concerns, we evaluated seven path visualizations in an online video-based study (N=99). We found that a path line visualization was rated highest for trust and perceived safety. In a follow-up virtual reality study (N=24), we evaluated the effects of this visualization and of other air traffic flying by. We found that the participants looked at the path line more often when other air traffic was present and that the path line increased trust and predictability of the air taxi's future path.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596249"}, {"primary_key": "949184", "vector": [], "sparse_vector": [], "title": "Abacus Gestures: A Large Set of Math-Based Usable Finger-Counting Gestures for Mid-Air Interactions.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Designing an extensive set of mid-air gestures that are both easy to learn and perform quickly presents a significant challenge. Further complicating this challenge is achieving high-accuracy detection of such gestures using commonly available hardware, like a 2D commodity camera. Previous work often proposed smaller, application-specific gesture sets, requiring specialized hardware and struggling with adaptability across diverse environments. Addressing these limitations, this paper introduces Abacus Gestures, a comprehensive collection of 100 mid-air gestures. Drawing on the metaphor of Finger Abacus counting, gestures are formed from various combinations of open and closed fingers, each assigned different values. We developed an algorithm using an off-the-shelf computer vision library capable of detecting these gestures from a 2D commodity camera feed with an accuracy exceeding 98% for palms facing the camera and 95% for palms facing the body. We assessed the detection accuracy, ease of learning, and usability of these gestures in a user study involving 20 participants. The study found that participants could learn Abacus Gestures within five minutes after executing just 15 gestures and could recall them after a four-month interval. Additionally, most participants developed motor memory for these gestures after performing 100 gestures. Most of the gestures were easy to execute with the designated finger combinations, and the flexibility in executing the gestures using multiple finger combinations further enhanced the usability. Based on these findings, we created a taxonomy that categorizes Abacus Gestures into five groups based on motor memory development and three difficulty levels according to their ease of execution. Finally, we provided design guidelines and proposed potential use cases for Abacus Gestures in the realm of mid-air interaction.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610898"}, {"primary_key": "949187", "vector": [], "sparse_vector": [], "title": "FewShotBP: Towards Personalized Ubiquitous Continuous Blood Pressure Measurement.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deep learning-based methods demonstrate promising results in continuous non-invasive blood pressure measurement, whereas those models trained on large public datasets suffer from severe performance degradation in predicting from real-world user data collected in home settings. Transfer learning has been recently introduced to personalize the pre-trained model with unseen users' data to solve the problem. However, the existing methods based on network fine-tuning for model personalization require a large amount of labeled data, lacking practicality due to labeling using a cuff-based blood pressure monitor is extremely tedious and laborious for home users. In this paper, we propose a novel few-shot transfer learning approach named FewShotBP, which addresses the above-mentioned challenges by introducing a personalization adapter at the personalization stage (i.e., the transfer learning stage), and a multi-modal spectro-temporal neural network at the pre-train stage, to bridge the gap between data-hungry models and limited labeled data in realistic scenarios. To evaluate the approach's significance, we conducted experiments using both a publicly available dataset and a real-world user experiment. The results demonstrated that the proposed approach achieves similar accuracy of blood pressure prediction with 10× less data for personalization compared with the state-of-the-art method in the public dataset and achieves a mean absolute error of 6.68 mmHg (systolic blood pressure) and 3.91 mmHg (diastolic blood pressure) with only 10 personal data samples in the real-world user experiment.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610918"}, {"primary_key": "949194", "vector": [], "sparse_vector": [], "title": "DAPPER: Label-Free Performance Estimation after Personalization for Heterogeneous Mobile Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Adiba <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many applications utilize sensors in mobile devices and machine learning to provide novel services. However, various factors such as different users, devices, and environments impact the performance of such applications, thus making the domain shift (i.e., distributional shift between the training domain and the target domain) a critical issue in mobile sensing. Despite attempts in domain adaptation to solve this challenging problem, their performance is unreliable due to the complex interplay among diverse factors. In principle, the performance uncertainty can be identified and redeemed by performance validation with ground-truth labels. However, it is infeasible for every user to collect high-quality, sufficient labeled data. To address the issue, we present DAPPER (Domain AdaPtation Performance EstimatoR) that estimates the adaptation performance in a target domain with only unlabeled target data. Our key idea is to approximate the model performance based on the mutual information between the model inputs and corresponding outputs. Our evaluation with four real-world sensing datasets compared against six baselines shows that on average, DAPPER outperforms the state-of-the-art baseline by 39.8% in estimation accuracy. Moreover, our on-device experiment shows that DAPPER achieves up to 396x less computation overhead compared with the baselines.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596256"}, {"primary_key": "949196", "vector": [], "sparse_vector": [], "title": "Touch-and-Heal: Data-driven Affective Computing in Tactile Interaction with Robotic Dog.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yancheng Cao", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Affective touch plays an important role in human-robot interaction. However, it is challenging for robots to perceive various natural human tactile gestures accurately, and feedback human intentions properly. In this paper, we propose a data-driven affective computing system based on a biomimetic quadruped robot with large-format, high-density flexible pressure sensors, which can mimic the natural tactile interaction between humans and pet dogs. We collect 208-minute videos from 26 participates and construct a dataset of 1212 human gestures-dog actions interaction sequences. The dataset is manually annotated with an 81-tactile-gesture vocabulary and a 44-corresponding-dog-reaction vocabulary, which are constructed through literature, questionnaire, and video observation. Then, we propose a deep learning algorithm pipeline with a gesture classification algorithm based on ResNet and an action prediction algorithm based on Transformer, which achieve the classification accuracy of 99.1% and the 1-gram BLEU score of 0.87 respectively. Finally, we conduct a field study to evaluate the emotion regulation effects through tactile affective interaction, and compare it with voice interaction. The results show that our system with tactile interaction plays a significant role in alleviating user anxiety, stimulating user excitement and improving the acceptability of robotic dogs.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596258"}, {"primary_key": "949209", "vector": [], "sparse_vector": [], "title": "GlassMessaging: Towards Ubiquitous Messaging Using OHMDs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Sheng<PERSON> Zhao", "<PERSON>n <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Wang", "<PERSON><PERSON>"], "summary": "Communicating with others while engaging in simple daily activities is both common and natural for people. However, due to the hands- and eyes-busy nature of existing digital messaging applications, it is challenging to message someone while performing simple daily activities. We present GlassMessaging, a messaging application on Optical See-Through Head-Mounted Displays (OHMDs), to support messaging with voice and manual inputs in hands- and eyes-busy scenarios. GlassMessaging is iteratively developed through a formative study identifying current messaging behaviors and challenges in common multitasking with messaging scenarios. We then evaluated this application against the mobile phone platform on varying texting complexities in eating and walking scenarios. Our results showed that, compared to phone-based messaging, GlassMessaging increased messaging opportunities during multitasking due to its hands-free, wearable nature, and multimodal input capabilities. The affordance of GlassMessaging also allows users easier access to voice input than the phone, which thus reduces the response time by 33.1% and increases the texting speed by 40.3%, with a cost in texting accuracy of 2.5%, particularly when the texting complexity increases. Lastly, we discuss trade-offs and insights to lay a foundation for future OHMD-based messaging applications.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610931"}, {"primary_key": "949213", "vector": [], "sparse_vector": [], "title": "InfoPrint: Embedding Interactive Information in 3D Prints Using Low-Cost Readily-Available Printers and Materials.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a fully-printable method to embed interactive information inside 3D printed objects. The information is invisible to the human eye and can be read using thermal imaging after temperature transfer through interaction with the objects. Prior methods either modify the surface appearance, require customized devices or not commonly used materials, or embed components that are not fully 3D printable. Such limitations restrict the design space for 3D prints, or cannot be readily applied to the already deployed 3D printing setups. In this paper, we present an information embedding technique using low-cost off-the-shelf dual extruder FDM (Fused Deposition Modeling) 3D printers, common materials (e.g., generic PLA), and a mobile thermal device (e.g., a thermal smartphone), by leveraging the thermal properties of common 3D print materials. In addition, we show our method can also be generalized to conventional near-infrared imaging scenarios. We evaluate our technique against multiple design and fabrication parameters and propose a design guideline for different use cases. Finally, we demonstrate various everyday applications enabled by our method, such as interactive thermal displays, user-activated augmented reality, automating thermal triggered events, and hidden tokens for social activities.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610933"}, {"primary_key": "949214", "vector": [], "sparse_vector": [], "title": "SmartASL: &quot;Point-of-Care&quot; Comprehensive ASL Interpreter Using Wearables.", "authors": ["Yincheng Jin", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Sign language builds up an important bridge between the d/Deaf and hard-of-hearing (DHH) and hearing people. Regrettably, most hearing people face challenges in comprehending sign language, necessitating sign language translation. However, state-of-the-art wearable-based techniques mainly concentrate on recognizing manual markers (e.g., hand gestures), while frequently overlooking non-manual markers, such as negative head shaking, question markers, and mouthing. This oversight results in the loss of substantial grammatical and semantic information in sign language. To address this limitation, we introduce SmartASL, a novel proof-of-concept system that can 1) recognize both manual and non-manual markers simultaneously using a combination of earbuds and a wrist-worn IMU, and 2) translate the recognized American Sign Language (ASL) glosses into spoken language. Our experiments demonstrate the SmartASL system's significant potential to accurately recognize the manual and non-manual markers in ASL, effectively bridging the communication gaps between ASL signers and hearing people using commercially available devices.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596255"}, {"primary_key": "949216", "vector": [], "sparse_vector": [], "title": "SeRaNDiP: Leveraging Inherent Sensor Random Noise for Differential Privacy Preservation in Wearable Community Sensing Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Personal data collected from today's wearable sensors contain a rich amount of information that can reveal a user's identity. Differential privacy (DP) is a well-known technique for protecting the privacy of the sensor data being sent to community sensing applications while preserving its statistical properties. However, differential privacy algorithms are computationally expensive, requiring user-level random noise generation which incurs high overheads on wearables with constrained hardware resources. In this paper, we propose SeRaNDiP -- which utilizes the inherent random noise existing in wearable sensors for distributed differential privacy. We show how various hardware configuration parameters available in wearable sensors can enable different amounts of inherent sensor noise and ensure distributed differential privacy guarantee for various community sensing applications with varying sizes of populations. Our evaluations of SeRaNDiP on five wearable sensors that are widely used in today's commercial wearables -- MPU-9250 accelerometer, ADXL345 accelerometer, BMP 388 barometer, MLP 3115A2 barometer, and MLX90632 body temperature sensor show a 1.4X-1.8X computation/communication speedup and 1.2X-1.5X energy savings against state-of-the-art DP implementation. To the best of our knowledge, SeRaNDiP is the first framework to leverage the inherent random sensor noise for differential privacy preservation in community sensing without any hardware modification.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596252"}, {"primary_key": "949217", "vector": [], "sparse_vector": [], "title": "MIRROR: Towards Generalizable On-Device Video Virtual Try-On for Mobile Shopping.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sungwook Son", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present MIRROR, an on-device video virtual try-on (VTO) system that provides realistic, private, and rapid experiences in mobile clothes shopping. Despite recent advancements in generative adversarial networks (GANs) for VTO, designing MIRROR involves two challenges: (1) data discrepancy due to restricted training data that miss various poses, body sizes, and backgrounds and (2) local computation overhead that uses up 24% of battery for converting only a single video. To alleviate the problems, we propose a generalizable VTO GAN that not only discerns intricate human body semantics but also captures domain-invariant features without requiring additional training data. In addition, we craft lightweight, reliable clothes/pose-tracking that generates refined pixel-wise warping flow without neural-net computation. As a holistic system, MIRROR integrates the new VTO GAN and tracking method with meticulous pre/post-processing, operating in two distinct phases (on/offline). Our results on Android smartphones and real-world user videos show that compared to a cutting-edge VTO GAN, MIRROR achieves 6.5× better accuracy with 20.1× faster video conversion and 16.9× less energy consumption.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631420"}, {"primary_key": "949219", "vector": [], "sparse_vector": [], "title": "Do I Just Tap My Headset?: How Novice Users Discover Gestural Interactions with Consumer Augmented Reality Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A variety of consumer Augmented Reality (AR) applications have been released on mobile devices and novel immersive headsets over the last five years, creating a breadth of new AR-enabled experiences. However, these applications, particularly those designed for immersive headsets, require users to employ unfamiliar gestural input and adopt novel interaction paradigms. To better understand how everyday users discover gestures and classify the types of interaction challenges they face, we observed how 25 novices from diverse backgrounds and technical knowledge used four different AR applications requiring a range of interaction techniques. A detailed analysis of gesture interaction traces showed that users struggled to discover the correct gestures, with the majority of errors occurring when participants could not determine the correct sequence of actions to perform or could not evaluate their actions. To further reflect on the prevalence of our findings, we carried out an expert validation study with eight professional AR designers, engineers, and researchers. We discuss implications for designing discoverable gestural input techniques that align with users' mental models, inventing AR-specific onboarding and help systems, and enhancing system-level machine recognition.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631451"}, {"primary_key": "949220", "vector": [], "sparse_vector": [], "title": "ProxiFit: Proximity Magnetic Sensing Using a Single Commodity Mobile toward Holistic Weight Exercise Monitoring.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Although many works bring exercise monitoring to smartphone and smartwatch, inertial sensors used in such systems require device to be in motion to detect exercises. We introduce ProxiFit, a highly practical on-device exercise monitoring system capable of classifying and counting exercises even if the device stays still. Utilizing novel proximity sensing of natural magnetism in exercise equipment, ProxiFit brings (1) a new category of exercise not involving device motion such as lower-body machine exercise, and (2) a new off-body exercise monitoring mode where a smartphone can be conveniently viewed in front of the user during workouts. ProxiFit addresses common issues of faint magnetic sensing by choosing appropriate preprocessing, negating adversarial motion artifacts, and designing a lightweight yet noise-tolerant classifier. Also, application-specific challenges such as a wide variety of equipment and the impracticality of obtaining large datasets are overcome by devising a unique yet challenging training policy. We evaluate ProxiFit on up to 10 weight machines (5 lower- and 5 upper-body) and 4 free-weight exercises, on both wearable and signage mode, with 19 users, at 3 gyms, over 14 months, and verify robustness against user and weather variations, spatial and rotational device location deviations, and neighboring machine interference.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610920"}, {"primary_key": "949223", "vector": [], "sparse_vector": [], "title": "Individualized Tracking of Neurocognitive-State-Dependent Eye-Movement Features Using Mobile Devices.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "With current clinical techniques, it is difficult to assess a patient's neurodegenerative disease (e.g., Alzheimer's) state accurately and frequently. The most widely used tests are qualitative or only performed intermittently, motivating the need for quantitative, accurate, and unobtrusive metrics to track disease progression. Clinical studies have shown that saccade latency (an eye movement measure of reaction time) and error rate (the proportion of eye movements in the wrong direction) may be significantly affected by neurocognitive diseases. Nevertheless, how these features change over time as a disease progresses is underdeveloped due to the constrained recording setup. In this work, our goal is to first understand how these features change over time in healthy individuals. To do so, we used a mobile app to frequently and accurately measure these features outside of the clinical environment from 80 healthy participants. We analyzed their longitudinal characteristics and designed an individualized longitudinal model using a Gaussian process. With a system that can measure eye-movement features on a much finer timescale in a broader population, we acquired a better understanding of eye-movement features from healthy individuals and provided research directions in understanding whether eye-movement features can be used to track neurocognitive states.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580843"}, {"primary_key": "949234", "vector": [], "sparse_vector": [], "title": "AMIR: Active Multimodal Interaction Recognition from Video and Network Traffic in Connected Environments.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Jinjin Zhao", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Activity recognition using video data is widely adopted for elder care, monitoring for safety and security, and home automation. Unfortunately, using video data as the basis for activity recognition can be brittle, since models trained on video are often not robust to certain environmental changes, such as camera angle and lighting changes. There has been a proliferation of network-connected devices in home environments. Interactions with these smart devices are associated with network activity, making network data a potential source for recognizing these device interactions. This paper advocates for the synthesis of video and network data for robust interaction recognition in connected environments. We consider machine learning-based approaches for activity recognition, where each labeled activity is associated with both a video capture and an accompanying network traffic trace. We develop a simple but effective framework AMIR (Active Multimodal Interaction Recognition)1 that trains independent models for video and network activity recognition respectively, and subsequently combines the predictions from these models using a meta-learning framework. Whether in lab or at home, this approach reduces the amount of \"paired\" demonstrations needed to perform accurate activity recognition, where both network and video data are collected simultaneously. Specifically, the method we have developed requires up to 70.83% fewer samples to achieve 85% F1 score than random data collection, and improves accuracy by 17.76% given the same number of samples.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580818"}, {"primary_key": "949236", "vector": [], "sparse_vector": [], "title": "TelecomTM: A Fine-Grained and Ubiquitous Traffic Monitoring System Using Pre-Existing Telecommunication Fiber-Optic Cables as Sensors.", "authors": ["<PERSON><PERSON><PERSON>", "Siyuan Yuan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce the TelecomTM system that uses pre-existing telecommunication fiber-optic cables as virtual strain sensors to sense vehicle-induced ground vibrations for fine-grained and ubiquitous traffic monitoring and characterization. Here we call it a virtual sensor because it is a software-based representation of a physical sensor. Due to the extensively installed telecommunication fiber-optic cables at the roadside, our system using redundant dark fibers enables to monitor traffic at low cost with low maintenance. Many existing traffic monitoring approaches use cameras, piezoelectric sensors, and smartphones, but they are limited due to privacy concerns and/or deployment requirements. Previous studies attempted to use telecommunication cables for traffic monitoring, but they were only exploratory and limited to simple tasks at a coarse granularity, e.g., vehicle detection, due to their hardware constraints and real-world challenges. In particular, those challenges are 1) unknown and heterogeneous properties of virtual sensors and 2) large and complex noise conditions. To this end, our TelecomTM system first characterizes the geographic location and analyzes the signal pattern of each virtual sensor through driving tests. We then develop a spatial-domain Bayesian filtering and smoothing algorithm to detect, track, and characterize each vehicle. Our approach uses the spatial dependency of multiple virtual sensors and <PERSON>'s laws of motion to combine the distributed sensor data to reduce uncertainties in vehicle detection and tracking. In our real-world evaluation on a two-way traffic road with 1120 virtual sensors, TelecomTM achieved 90.18% vehicle detection accuracy, 27x and 5x error reduction for vehicle position and speed tracking compared to a baseline method, and ±3.92% and ±11.98% percent error for vehicle wheelbase and weight estimation, respectively.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596262"}, {"primary_key": "949239", "vector": [], "sparse_vector": [], "title": "Eggly: Designing Mobile Augmented Reality Neurofeedback Training Games for Children with Autism Spectrum Disorder.", "authors": ["<PERSON><PERSON>", "Pengcheng An", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Autism Spectrum Disorder (ASD) is a neurodevelopmental disorder that affects how children communicate and relate to other people and the world around them. Emerging studies have shown that neurofeedback training (NFT) games are an effective and playful intervention to enhance social and attentional capabilities for autistic children. However, NFT is primarily available in a clinical setting that is hard to scale. Also, the intervention demands deliberately-designed gamified feedback with fun and enjoyment, where little knowledge has been acquired in the HCI community. Through a ten-month iterative design process with four domain experts, we developed Eggly, a mobile NFT game based on a consumer-grade EEG headband and a tablet. <PERSON><PERSON> uses novel augmented reality (AR) techniques to offer engagement and personalization, enhancing their training experience. We conducted two field studies (a single-session study and a three-week multi-session study) with a total of five autistic children to assess <PERSON><PERSON> in practice at a special education center. Both quantitative and qualitative results indicate the effectiveness of the approach as well as contribute to the design knowledge of creating mobile AR NFT games.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596251"}, {"primary_key": "949243", "vector": [], "sparse_vector": [], "title": "Interaction Harvesting: A Design Probe of User-Powered Widgets.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Whenever a user interacts with a device, mechanical work is performed to actuate the user interface elements; the resulting energy is typically wasted, dissipated as sound and heat. Previous work has shown that many devices can be powered entirely from this otherwise wasted user interface energy. For these devices, wires and batteries, along with the related hassles of replacement and charging, become unnecessary and onerous. So far, these works have been restricted to proof-of-concept demonstrations; a specific bespoke harvesting and sensing circuit is constructed for the application at hand. The challenge of harvesting energy while simultaneously sensing fine-grained input signals from diverse modalities makes prototyping new devices difficult. To fill this gap, we present a hardware toolkit which provides a common electrical interface for harvesting energy from user interface elements. This facilitates exploring the composability, utility, and breadth of enabled applications of interaction-powered smart devices. We design a set of \"energy as input\" harvesting circuits, a standard connective interface with 3D printed enclosures, and software libraries to enable the exploration of devices where the user action generates the energy needed to perform the device's primary function. This exploration culminated in a demonstration campaign where we prototype several exemplar popular toys and gadgets, including battery-free Bop-It--- a popular 90s rhythm game, an electronic Etch-a-sketch, a \"Simon-Says\"-style memory game, and a service rating device. We run exploratory user studies to understand how generativity, creativity, and composability are hampered or facilitated by these devices. These demonstrations, user study takeaways, and the toolkit itself provide a foundation for building interactive and user-focused gadgets whose usability is not affected by battery charge and whose service lifetime is not limited by battery wear.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610880"}, {"primary_key": "949245", "vector": [], "sparse_vector": [], "title": "Surveying the Social Comfort of Body, Device, and Environment-Based Augmented Reality Interactions in Confined Passenger Spaces Using Mixed Reality Composite Videos.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Augmented Reality (AR) headsets could significantly improve the passenger experience, freeing users from the restrictions of physical smartphones, tablets and seatback displays. However, the confined space of public transport and the varying proximity to other passengers may restrict what interaction techniques are deemed socially acceptable for AR users - particularly considering current reliance on mid-air interactions in consumer headsets. We contribute and utilize a novel approach to social acceptability video surveys, employing mixed reality composited videos to present a real user performing interactions across different virtual transport environments. This approach allows for controlled evaluation of perceived social acceptability whilst freeing researchers to present interactions in any simulated context. Our resulting survey (N=131) explores the social comfort of body, device, and environment-based interactions across seven transit seating arrangements. We reflect on the advantages of discreet inputs over mid-air and the unique challenges of face-to-face seating for passenger <PERSON>.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610923"}, {"primary_key": "949247", "vector": [], "sparse_vector": [], "title": "Towards Efficient Emotion Self-report Collection Using Human-AI Collaboration: A Case Study on Smartphone Keyboard Interaction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Emotion-aware services are increasingly used in different applications such as gaming, mental health tracking, video conferencing, and online tutoring. The core of such services is usually a machine learning model that automatically infers its user's emotions based on different biological indicators (e.g., physiological signals and facial expressions). However, such machine learning models often require a large number of emotion annotations or ground truth labels, which are typically collected as manual self-reports by conducting long-term user studies, commonly known as Experience Sampling Method (ESM). Responding to repetitive ESM probes for self-reports is time-consuming and fatigue-inducing. The burden of repetitive self-report collection leads to users responding arbitrarily or dropping out from the studies, compromising the model performance. To counter this issue, we, in this paper, propose a Human-AI Collaborative Emotion self-report collection framework, HACE, that reduces the self-report collection effort significantly. HACE encompasses an active learner, bootstrapped with a few emotion self-reports (as seed samples), and enables the learner to query for only not-so-confident instances to retrain the learner to predict the emotion self-reports more efficiently. We evaluated the framework in a smartphone keyboard-based emotion self-report collection scenario by performing a 3-week in-the-wild study (N = 32). The evaluation of HACE on this dataset (≈11,000 typing sessions corresponding to more than 200 hours of typing data) demonstrates that it requires 46% fewer self-reports than the baselines to train the emotion self-report detection model and yet outperforms the baselines with an average self-report detection F-score of 85%. These findings demonstrate the possibility of adopting such a human-AI collaborative approach to reduce emotion self-report collection efforts.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596269"}, {"primary_key": "949248", "vector": [], "sparse_vector": [], "title": "LapTouch: Using the Lap for Seated Touch Interaction with HMDs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Use of virtual reality while seated is common, but studies on seated interaction beyond the use of controllers or hand gestures have been sparse. This work present <PERSON><PERSON><PERSON><PERSON><PERSON>, which makes use of the lap as a touch interface and includes two user studies to inform the design of direct and indirect touch interaction using the lap with visual feedback that guides the user touch, as well as eye-free interaction in which users are not provided with such visual feedback. The first study suggests that direct interaction can provide effective layouts with 95% accuracy with up to a 4×4 layout and a shorter completion time, while indirect interaction can provide effective layouts with up to a 4×5 layout but a longer completion time. Considering user experience, which revealed that 4-row and 5-column layouts are not preferred, it is recommended to use both direct and indirect interaction with a maximum of a 3×4 layout. According to the second study, increasing the eye-free interaction with support vector machine (SVM) allows for a 2×2 layout with a generalized model and 2×2, 2×3 and 3×2 layouts with personalized models.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610878"}, {"primary_key": "949249", "vector": [], "sparse_vector": [], "title": "Spatial-Temporal Masked Autoencoder for Multi-Device Wearable Human Activity Recognition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The widespread adoption of wearable devices has led to a surge in the development of multi-device wearable human activity recognition (WHAR) systems. Nevertheless, the performance of traditional supervised learning-based methods to WHAR is limited by the challenge of collecting ample annotated wearable data. To overcome this limitation, self-supervised learning (SSL) has emerged as a promising solution by first training a competent feature extractor on a substantial quantity of unlabeled data, followed by refining a minimal classifier with a small amount of labeled data. Despite the promise of SSL in WHAR, the majority of studies have not considered missing device scenarios in multi-device WHAR. To bridge this gap, we propose a multi-device SSL WHAR method termed Spatial-Temporal Masked Autoencoder (STMAE). STMAE captures discriminative activity representations by utilizing the asymmetrical encoder-decoder structure and two-stage spatial-temporal masking strategy, which can exploit the spatial-temporal correlations in multi-device data to improve the performance of SSL WHAR, especially on missing device scenarios. Experiments on four real-world datasets demonstrate the efficacy of STMAE in various practical scenarios.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631415"}, {"primary_key": "949251", "vector": [], "sparse_vector": [], "title": "Narrative-Based Visual Feedback to Encourage Sustained Physical Activity: A Field Trial of the WhoIsZuki Mobile Health Platform.", "authors": ["<PERSON>", "Yekaterina S<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Stories are a core way human beings make meaning and sense of the world and our lived experiences, including our behaviors, desires, and goals. Narrative structures, both visual and textual, help us understand and act on information, while also evoking strong emotions. Focusing on the health context, this research examines the effectiveness of narrative-based feedback in motivating physical activity behaviors and underlying attitudes over longitudinal periods. After collecting two weeks of baseline physical activity levels, N=39 participants installed our smartphone application, WhoIsZuki. The WhoIsZuki app supports goal setting and semi-automated activity tracking, and it provides an ambient display that visually encodes these tracked activities as well as progress toward goals. Half of participants received a version of the interface that supplied behavioral feedback in the form of a multi-chapter episodic narrative, while the other half received a control condition version that provided an aesthetically-similar visualization but without any characterization, episodic structure, dramatic effect, or other narrative elements. After interacting with these versions for four months, our analysis showed that participants receiving the multi-chapter narrative feedback performed more physical activity, achieved more goals, experienced more positive psychological shifts, and overall engaged more meaningfully with the digital intervention.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580786"}, {"primary_key": "949252", "vector": [], "sparse_vector": [], "title": "eat2pic: An Eating-Painting Interactive System to Nudge Users into Making Healthier Diet Choices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Given the complexity of human eating behaviors, developing interactions to change the way users eat or their choice of meals is challenging. In this study, we propose an interactive system called eat2pic designed to encourage healthy eating habits such as adopting a balanced diet and eating more slowly, by refraining the task of selecting meals into that of adding color to landscape pictures. The eat2pic system comprises a sensor-equipped chopstick (one of a pair) and two types of digital canvases. It provides fast feedback by recognizing a user's eating behavior in real time and displaying the result on a small canvas called \"one-meal eat2pic.\" Moreover, it also provides slow feedback by displaying the number of colors of foods that the user consumed on a large canvas called \"one-week eat2pic.\" The former was designed and implemented as a guide to help people eat more slowly, and the latter to encourage people to select more balanced menus. Through two user studies, we explored the experience of interaction with eat2pic, in which users' daily eating behavior was reflected in a series of \"paintings,\" that is, images produced by the automated system. The experimental results suggest that eat2pic may provide an opportunity for reflection in meal selection and while eating, as well as assist users in becoming more aware of how they are eating and how balanced their daily meals are. We expect this system to inspire users' curiosity about different diets and ways of eating. This research also contributes to expanding the design space for products and services related to dietary support.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580784"}, {"primary_key": "949255", "vector": [], "sparse_vector": [], "title": "Understanding Disengagement in Just-in-Time Mobile Health Interventions.", "authors": ["Joonyoung Park", "<PERSON><PERSON><PERSON>"], "summary": "Just-in-time (JIT) intervention aims to proactively detect a user's problematic behaviors and deliver interventions at an opportune moment to facilitate target behaviors. However, prior studies have shown that JIT intervention may suffer from user disengagement, a phenomenon in which a user's level of engagement with intervention apps and target behaviors declines over time. In this study, we aimed to deepen our understanding of disengagement in a mobile JIT intervention system. As a case study, we conducted a user study with college students (n = 54) for eight weeks to understand how disengagement appears over time and what factors influence user disengagement. Our findings reveal that personal traits, such as boredom proneness and self-control issues, are closely related to disengagement, with key factors including 1) boredom and habituation related to repetitive and monotonous JIT interventions, 2) inopportune alarm, 3) distrust for the JIT feedback mechanism, and 4) a lack of motivation due to low rewards. We provide theoretical and practical design guidelines for follow-up studies on JIT intervention system design.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596240"}, {"primary_key": "949256", "vector": [], "sparse_vector": [], "title": "AttFL: A Personalized Federated Learning Framework for Time-series Mobile and Embedded Sensor Data Processing.", "authors": ["Jaeyeon Park", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "JeongGil Ko"], "summary": "This work presents AttFL, a federated learning framework designed to continuously improve a personalized deep neural network for efficiently analyzing time-series data generated from mobile and embedded sensing applications. To better characterize time-series data features and efficiently abstract model parameters, AttFL appends a set of attention modules to the baseline deep learning model and exchanges their feature map information to gather collective knowledge across distributed local devices at the server. The server groups devices with similar contextual goals using cosine similarity, and redistributes updated model parameters for improved inference performance at each local device. Specifically, unlike previously proposed federated learning frameworks, AttFL is designed specifically to perform well for various recurrent neural network (RNN) baseline models, making it suitable for many mobile and embedded sensing applications producing time-series sensing data. We evaluate the performance of AttFL and compare with five state-of-the-art federated learning frameworks using three popular mobile/embedded sensing applications (e.g., physiological signal analysis, human activity recognition, and audio processing). Our results obtained from CPU core-based emulations and a 12-node embedded platform testbed shows that AttFL outperforms all alternative approaches in terms of model accuracy and communication/computational overhead, and is flexible enough to be applied in various application scenarios exploiting different baseline deep learning model architectures.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610917"}, {"primary_key": "949259", "vector": [], "sparse_vector": [], "title": "Investigating Generalizability of Speech-based Suicidal Ideation Detection Using Mobile Phones.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Speech-based diaries from mobile phones can capture paralinguistic patterns that help detect mental illness symptoms such as suicidal ideation. However, previous studies have primarily evaluated machine learning models on a single dataset, making their performance unknown under distribution shifts. In this paper, we investigate the generalizability of speech-based suicidal ideation detection using mobile phones through cross-dataset experiments using four datasets with N=786 individuals experiencing major depressive disorder, auditory verbal hallucinations, persecutory thoughts, and students with suicidal thoughts. Our results show that machine and deep learning methods generalize poorly in many cases. Thus, we evaluate unsupervised domain adaptation (UDA) and semi-supervised domain adaptation (SSDA) to mitigate performance decreases owing to distribution shifts. While SSDA approaches showed superior performance, they are often ineffective, requiring large target datasets with limited labels for adversarial and contrastive training. Therefore, we propose sinusoidal similarity sub-sampling (S3), a method that selects optimal source subsets for the target domain by computing pair-wise scores using sinusoids. Compared to prior approaches, S3 does not use labeled target data or transform features. Fine-tuning using S3 improves the cross-dataset performance of deep models across the datasets, thus having implications in ubiquitous technology, mental health, and machine learning.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631452"}, {"primary_key": "949265", "vector": [], "sparse_vector": [], "title": "VidAdapter: Adapting Blackboard-Style Videos for Ubiquitous Viewing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Sheng<PERSON> Zhao", "Chi-Wing Fu"], "summary": "Video lectures are increasingly being used by learners in a ubiquitous manner. However, existing video designs are not optimised for ubiquitous use, creating the need to adapt the style of these videos to meet the constraints of the learning platform and context of use. Our formative study with experienced video editing users, however, found that performing these adaptations using traditional video editors can be a challenging and time-consuming task. We developed VidAdapter, a tool that facilitates lecture video adaptation by allowing direct manipulation of the video content. For this, VidAdapter automatically extracts meaningful elements from the video, enables spatial and temporal reorganisation of the elements, and streamlines the modification of an element's visual appearance. We demonstrate the capabilities and specific use cases of VidAdapter within the domain of adapting existing blackboard lecture videos for on-the-go learning on Optical Head-Mounted Displays. Our evaluation of the tool with experienced video editing users revealed that VidAdapter was strongly preferred over traditional approaches and can improve the efficiency of the adaptation process by over 53% on average.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610928"}, {"primary_key": "949271", "vector": [], "sparse_vector": [], "title": "CrowdQ: Predicting the Queue State of Hospital Emergency Department Using Crowdsensing Mobility Data-Driven Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Hospital Emergency Departments (EDs) are essential for providing emergency medical services, yet often overwhelmed due to increasing healthcare demand. Current methods for monitoring ED queue states, such as manual monitoring, video surveillance, and front-desk registration are inefficient, invasive, and delayed to provide real-time updates. To address these challenges, this paper proposes a novel framework, CrowdQ, which harnesses spatiotemporal crowdsensing data for real-time ED demand sensing, queue state modeling, and prediction. By utilizing vehicle trajectory and urban geographic environment data, CrowdQ can accurately estimate emergency visits from noisy traffic flows. Furthermore, it employs queueing theory to model the complex emergency service process with medical service data, effectively considering spatiotemporal dependencies and event context impact on ED queue states. Experiments conducted on large-scale crowdsensing urban traffic datasets and hospital information system datasets from Xiamen City demonstrate the framework's effectiveness. It achieves an F1 score of 0.93 in ED demand identification, effectively models the ED queue state of key hospitals, and reduces the error in queue state prediction by 18.5%-71.3% compared to baseline methods. CrowdQ, therefore, offers valuable alternatives for public emergency treatment information disclosure and maximized medical resource allocation.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610875"}, {"primary_key": "949274", "vector": [], "sparse_vector": [], "title": "MR Object Identification and Interaction: Fusing Object Situation Information from Heterogeneous Sources.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The increasing number of objects in ubiquitous computing environments creates a need for effective object detection and identification mechanisms that permit users to intuitively initiate interactions with these objects. While multiple approaches to such object detection -- including through visual object detection, fiducial markers, relative localization, or absolute spatial referencing -- are available, each of these suffers from drawbacks that limit their applicability. In this paper, we propose ODIF, an architecture that permits the fusion of object situation information from such heterogeneous sources and that remains vertically and horizontally modular to allow extending and upgrading systems that are constructed accordingly. We furthermore present BLEARVIS, a prototype system that builds on the proposed architecture and integrates computer-vision (CV) based object detection with radio-frequency (RF) angle of arrival (AoA) estimation to identify BLE-tagged objects. In our system, the front camera of a Mixed Reality (MR) head-mounted display (HMD) provides a live image stream to a vision-based object detection module, while an antenna array that is mounted on the HMD collects AoA information from ambient devices. In this way, BLEARVIS is able to differentiate between visually identical objects in the same environment and can provide an MR overlay of information (data and controls) that relates to them. We include experimental evaluations of both, the CV-based object detection and the RF-based AoA estimation, and discuss the applicability of the combined RF and CV pipelines in different ubiquitous computing scenarios. This research can form a starting point to spawn the integration of diverse object detection, identification, and interaction approaches that function across the electromagnetic spectrum, and beyond.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610879"}, {"primary_key": "949277", "vector": [], "sparse_vector": [], "title": "Using Wearable Sensors to Measure Interpersonal Synchrony in Actors and Audience Members During a Live Theatre Performance.", "authors": ["<PERSON><PERSON>", "Dwaynica A<PERSON>", "<PERSON>", "Antonia F. de C. Hamilton", "<PERSON>", "<PERSON>"], "summary": "Studying social interaction in real-world settings is of increasing importance to social cognitive researchers. Theatre provides an ideal opportunity to study rich face-to-face interactions in a controlled, yet natural setting. Here we collaborated with Flute Theatre to investigate interpersonal synchrony between actors-actors, actors-audience and audience-audience within a live theatrical setting. Our 28 participants consisted of 6 actors and 22 audience members, with 5 of these audience members being audience participants in the show. The performance was a compilation of acting, popular science talks and demonstrations, and an audience participation period. Interpersonal synchrony was measured using inertial measurement unit (IMU) wearable accelerometers worn on the heads of participants, whilst audio-visual data recorded everything that occurred on the stage. Participants also completed post-show self-report questionnaires on their engagement with the overall scientists and actors performance. Cross Wavelet Transform (XWT) and Wavelet Coherence Transform (WCT) analysis were conducted to extract synchrony at different frequencies, pairing with audio-visual data. Findings revealed that XWT and WCT analysis are useful methods in extracting the multiple types of synchronous activity that occurs when people perform or watch a live performance together. We also found that audience members with higher ratings on questionnaire items such as the strength of their emotional response to the performance, or how empowered they felt by the performance, showed a high degree of interpersonal synchrony with actors during the acting segments of performance. We further found that audience members rated the scientists performance higher than the actors performance on questions related to their emotional response to the performance as well as, how uplifted, empowered, and connected to social issues they felt. This shows the types of potent connections audience members can have with live performances. Additionally, our findings highlight the importance of the performance context for audience engagement, in our case a theatre performance as part of public engagement with science rather than a stand-alone theatre performance. In sum we conclude that interdisciplinary real-world paradigms are an important and understudied route to understanding in-person social interactions.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580781"}, {"primary_key": "949279", "vector": [], "sparse_vector": [], "title": "Driver Maneuver Interaction Identification with Anomaly-Aware Federated Learning on Heterogeneous Feature Representations.", "authors": ["<PERSON><PERSON>", "Suining He"], "summary": "Driver maneuver interaction learning (DMIL) refers to the classification task with the goal of identifying different driver-vehicle maneuver interactions (e.g., left/right turns). Existing conventional studies largely focused on the centralized collection of sensor data from the drivers' smartphones (say, inertial measurement units or IMUs, like accelerometer and gyroscope). Such a centralized mechanism might be precluded by data regulatory constraints. Furthermore, how to enable an adaptive and accurate DMIL framework remains challenging due to (i) complexity in heterogeneous driver maneuver patterns, and (ii) impacts of anomalous driver maneuvers due to, for instance, aggressive driving styles and behaviors. To overcome the above challenges, we propose AF-DMIL, an Anomaly-aware Federated Driver Maneuver Interaction Learning system. We focus on the real-world IMU sensor datasets (e.g., collected by smartphones) for our pilot case study. In particular, we have designed three heterogeneous representations for AF-DMIL regarding spectral, time series, and statistical features that are derived from the IMU sensor readings. We have designed a novel heterogeneous representation attention network (HetRANet) based on spectral channel attention, temporal sequence attention, and statistical feature learning mechanisms, jointly capturing and identifying the complex patterns within driver maneuver behaviors. Furthermore, we have designed a densely-connected convolutional neural network in HetRANet to enable the complex feature extraction and enhance the computational efficiency of HetRANet. In addition, we have designed within AF-DMIL a novel anomaly-aware federated learning approach for decentralized DMIL in response to anomalous maneuver data. To ease extraction of the maneuver patterns and evaluation of their mutual differences, we have designed an embedding projection network that projects the high-dimensional driver maneuver features into low-dimensional space, and further derives the exemplars that represent the driver maneuver patterns for mutual comparison. Then, AF-DMIL further leverages the mutual differences of the exemplars to identify those that exhibit anomalous patterns and deviate from others, and mitigates their impacts upon the federated DMIL. We have conducted extensive driver data analytics and experimental studies on three real-world datasets (one is harvested on our own) to evaluate the prototype of AF-DMIL, demonstrating AF-DMIL's accuracy and effectiveness compared to the state-of-the-art DMIL baselines (on average by more than 13% improvement in terms of DMIL accuracy), as well as fewer communication rounds (on average 29.20% fewer than existing distributed learning mechanisms).", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631421"}, {"primary_key": "949285", "vector": [], "sparse_vector": [], "title": "Aragorn: A Privacy-Enhancing System for Mobile Cameras.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>-Power", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mobile app developers often rely on cameras to implement rich features. However, giving apps unfettered access to the mobile camera poses a privacy threat when camera frames capture sensitive information that is not needed for the app's functionality. To mitigate this threat, we present Aragorn, a novel privacy-enhancing mobile camera system that provides fine grained control over what information can be present in camera frames before apps can access them. Aragorn automatically sanitizes camera frames by detecting regions that are essential to an app's functionality and blocking out everything else to protect privacy while retaining app utility. Aragorn can cater to a wide range of camera apps and incorporates knowledge distillation and crowdsourcing to extend robust support to previously unsupported apps. In our evaluations, we see that, with no degradation in utility, Aragorn detects credit cards with 89% accuracy and faces with 100% accuracy in context of credit card scanning and face recognition respectively. We show that Aragorn's implementation in the Android camera subsystem only suffers an average drop of 0.01 frames per second in frame rate. Our evaluations show that the overhead incurred by <PERSON>gorn to system performance is reasonable.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631406"}, {"primary_key": "949287", "vector": [], "sparse_vector": [], "title": "Society&apos;s Attitudes Towards Human Augmentation and Performance Enhancement Technologies (SHAPE) Scale.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Human augmentation technologies (ATs) are a subset of ubiquitous on-body devices designed to improve cognitive, sensory, and motor capacities. Although there is a large corpus of knowledge concerning ATs, less is known about societal attitudes towards them and how they shift over time. To that end, we developed The Society's Attitudes Towards Human Augmentation and Performance Enhancement Technologies (SHAPE) Scale, which measures how users of ATs are perceived. To develop the scale, we first created a list of possible scale items based on past work on how people respond to new technologies. The items were then reviewed by experts. Next, we performed exploratory factor analysis to reduce the scale to its final length of thirteen items. Subsequently, we confirmed test-retest validity of our instrument, as well as its construct validity. The SHAPE scale enables researchers and practitioners to understand elements contributing to attitudes toward augmentation technology users. The SHAPE scale assists designers of ATs in designing artifacts that will be more universally accepted.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610915"}, {"primary_key": "949291", "vector": [], "sparse_vector": [], "title": "VoiceListener: A Training-free and Universal Eavesdropping Attack on Built-in Speakers of Mobile Devices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Li Lu", "Zhongjie Ba", "<PERSON>", "<PERSON><PERSON>"], "summary": "Recently, voice leakage gradually raises more significant concerns of users, due to its underlying sensitive and private information when providing intelligent services. Existing studies demonstrate the feasibility of applying learning-based solutions on built-in sensor measurements to recover voices. However, due to the privacy concerns, large-scale voices-sensor measurements samples for model training are not publicly available, leading to significant efforts in data collection for such an attack. In this paper, we propose a training-free and universal eavesdropping attack on built-in speakers, VoiceListener, which releases the data collection efforts and is able to adapt to various voices, platforms, and domains. In particular, VoiceListener develops an aliasing-corrected super resolution mechanism, including an aliasing-based pitch estimation and an aliasing-corrected voice recovering, to convert the undersampled narrow-band sensor measurements to wide-band voices. Extensive experiments demonstrate that our proposed VoiceListener could accurately recover the voices from undersampled sensor measurements and is robust to different voices, platforms and domains, realizing the universal eavesdropping attack.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580789"}, {"primary_key": "949295", "vector": [], "sparse_vector": [], "title": "Cross-technology Communication between Visible Light and Battery-free RFIDs.", "authors": ["<PERSON><PERSON>", "Lubing Han", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The ubiquity of illumination facilities enables the versatile development of Visible Light Communication (VLC). VLC-based research achieved high-speed wireless access and decimeter-level indoor localization with complex equipment. However, it is still unclear whether the VLC is applicable for widely-used battery-free Internet-of-Things nodes, e.g., passive RFIDs. This paper proposes LightSign, the first cross-technology system that enables passive RFID tags to receive visible light messages. LightSign is compatible with commercial protocols, transparent to routine RFID communications, and invisible to human eyes. We propose a pseudo-timing instruction to achieve microsecond-level light switching to modulate the VLC message. To make it perceptible to passive RFIDs, we design an augmented RFID tag and prove its effectiveness theoretically and experimentally. With only one reply from an augmented tag, LightSign can decode 100-bit-long VLC messages. We evaluate LightSign in real industry environments and test its performance with two use cases. The results show that LightSign achieves up to 99.2% decoding accuracy in varying scenarios.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610883"}, {"primary_key": "949304", "vector": [], "sparse_vector": [], "title": "Knowing Your Heart Condition Anytime: User-Independent ECG Measurement Using Commercial Mobile Phones.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>peng Dai", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> Gu"], "summary": "Electrocardiogram (ECG) monitoring has been widely explored in detecting and diagnosing cardiovascular diseases due to its accuracy, simplicity, and sensitivity. However, medical- or commercial-grade ECG monitoring devices can be costly for people who want to monitor their ECG on a daily basis. These devices typically require several electrodes to be attached to the human body which is inconvenient for continuous monitoring. To enable low-cost measurement of ECG signals with off-the-shelf devices on a daily basis, in this paper, we propose a novel ECG sensing system that uses acceleration data collected from a smartphone. Our system offers several advantages over previous systems, including low cost, ease of use, location and user independence, and high accuracy. We design a two-tiered denoising process, comprising SWT and Soft-Thresholding, to effectively eliminate interference caused by respiration, body, and hand movements. Finally, we develop a multi-level deep learning recovery model to achieve efficient, real-time and user-independent ECG measurement on commercial mobile phones. We conduct extensive experiments with 30 participants (with nearly 36,000 heartbeat samples) under a user-independent scenario. The average errors of the PR interval, QRS interval, QT interval, and RR interval are 12.02 ms, 16.9 ms, 16.64 ms, and 1.84 ms, respectively. As a case study, we also demonstrate the strong capability of our system in signal recovery for patients with common heart diseases, including tachycardia, bradycardia, arrhythmia, unstable angina, and myocardial infarction.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610871"}, {"primary_key": "949307", "vector": [], "sparse_vector": [], "title": "Genie in the Model: Automatic Generation of Human-in-the-Loop Deep Neural Networks for Mobile Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Advances in deep neural networks (DNNs) have fostered a wide spectrum of intelligent mobile applications ranging from voice assistants on smartphones to augmented reality with smart-glasses. To deliver high-quality services, these DNNs should operate on resource-constrained mobile platforms and yield consistent performance in open environments. However, DNNs are notoriously resource-intensive, and often suffer from performance degradation in real-world deployments. Existing research strives to optimize the resource-performance trade-off of DNNs by compressing the model without notably compromising its inference accuracy. Accordingly, the accuracy of these compressed DNNs is bounded by the original ones, leading to more severe accuracy drop in challenging yet common scenarios such as low-resolution, small-size, and motion-blur. In this paper, we propose to push forward the frontiers of the DNN performance-resource trade-off by introducing human intelligence as a new design dimension. To this end, we explore human-in-the-loop DNNs (H-DNNs) and their automatic performance-resource optimization. We present H-Gen, an automatic H-DNN compression framework that incorporates human participation as a new hyperparameter for accurate and efficient DNN generation. It involves novel hyperparameter formulation, metric calculation, and search strategy in the context of automatic H-DNN generation. We also propose human participation mechanisms for three common DNN architectures to showcase the feasibility of H-Gen. Extensive experiments on twelve categories of challenging samples with three common DNN structures demonstrate the superiority of H-Gen in terms of the overall trade-off between performance (accuracy, latency), and resource (storage, energy, human labour).", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580815"}, {"primary_key": "949308", "vector": [], "sparse_vector": [], "title": "TextureSight: Texture Detection for Routine Activity Awareness with Wearable Laser Speckle Imaging.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Objects engaged by users' hands contain rich contextual information for their strong correlation with user activities. Tools such as toothbrushes and wipes indicate cleansing and sanitation, while mice and keyboards imply work. Much research has been endeavored to sense hand-engaged objects to supply wearables with implicit interactions or ambient computing with personal informatics. We propose TextureSight, a smart-ring sensor that detects hand-engaged objects by detecting their distinctive surface textures using laser speckle imaging on a ring form factor. We conducted a two-day experience sampling study to investigate the unicity and repeatability of the object-texture combinations across routine objects. We grounded our sensing with a theoretical model and simulations, powered it with state-of-the-art deep neural net techniques, and evaluated it with a user study. TextureSight constitutes a valuable addition to the literature for its capability to sense passive objects without emission of EMI or vibration and its elimination of lens for preserving user privacy, leading to a new, practical method for activity recognition and context-aware computing.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631413"}, {"primary_key": "949309", "vector": [], "sparse_vector": [], "title": "AdaStreamLite: Environment-adaptive Streaming Speech Recognition on Mobile Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Jiang<PERSON><PERSON>", "Junz<PERSON> Du"], "summary": "Streaming speech recognition aims to transcribe speech to text in a streaming manner, providing real-time speech interaction for smartphone users. However, it is not trivial to develop a high-performance streaming speech recognition system purely running on mobile platforms, due to the complex real-world acoustic environments and the limited computational resources of smartphones. Most existing solutions lack the generalization to unseen environments and have difficulty to work with streaming speech. In this paper, we design AdaStreamLite, an environment-adaptive streaming speech recognition tool for smartphones. AdaStreamLite interacts with its surroundings to capture the characteristics of the current acoustic environment to improve the robustness against ambient noise in a lightweight manner. We design an environment representation extractor to model acoustic environments with compact feature vectors, and construct a representation lookup table to improve the generalization of AdaStreamLite to unseen environments. We train our system using large speech datasets publicly available covering different languages. We conduct experiments in a large range of real acoustic environments with different smartphones. The results show that AdaStreamLite outperforms the state-of-the-art methods in terms of recognition accuracy, computational resource consumption and robustness against unseen environments.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631460"}, {"primary_key": "949313", "vector": [], "sparse_vector": [], "title": "I Know Your Intent: Graph-enhanced Intent-aware User Device Interaction Prediction via Contrastive Learning.", "authors": ["<PERSON><PERSON>", "Qingsong Zou", "Qing Li", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Ruoyu Li", "<PERSON>"], "summary": "With the booming of smart home market, intelligent Internet of Things (IoT) devices have been increasingly involved in home life. To improve the user experience of smart homes, some prior works have explored how to use machine learning for predicting interactions between users and devices. However, the existing solutions have inferior User Device Interaction (UDI) prediction accuracy, as they ignore three key factors: routine, intent and multi-level periodicity of human behaviors. In this paper, we present SmartUDI, a novel accurate UDI prediction approach for smart homes. First, we propose a Message-Passing-based Routine Extraction (MPRE) algorithm to mine routine behaviors, then the contrastive loss is applied to narrow representations among behaviors from the same routines and alienate representations among behaviors from different routines. Second, we propose an Intent-aware Capsule Graph Attention Network (ICGAT) to encode multiple intents of users while considering complex transitions between different behaviors. Third, we design a Cluster-based Historical Attention Mechanism (CHAM) to capture the multi-level periodicity by aggregating the current sequence and the semantically nearest historical sequence representations through the attention mechanism. SmartUDI can be seamlessly deployed on cloud infrastructures of IoT device vendors and edge nodes, enabling the delivery of personalized device service recommendations to users. Comprehensive experiments on four real-world datasets show that SmartUDI consistently outperforms the state-of-the-art baselines with more accurate and highly interpretable results.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610906"}, {"primary_key": "949315", "vector": [], "sparse_vector": [], "title": "RimSense: Enabling Touch-based Interaction on Eyeglass Rim Using Piezoelectric Sensors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Smart eyewear's interaction mode has attracted significant research attention. While most commercial devices have adopted touch panels situated on the temple front of eyeglasses for interaction, this paper identifies a drawback stemming from the unparalleled plane between the touch panel and the display, which disrupts the direct mapping between gestures and the manipulated objects on display. Therefore, this paper proposes RimSense, a proof-of-concept design for smart eyewear, to introduce an alternative realm for interaction - touch gestures on eyewear rim. RimSense leverages piezoelectric (PZT) transducers to convert the eyeglass rim into a touch-sensitive surface. When users touch the rim, the alteration in the eyeglass's structural signal manifests its effect into a channel frequency response (CFR). This allows RimSense to recognize the executed touch gestures based on the collected CFR patterns. Technically, we employ a buffered chirp as the probe signal to fulfil the sensing granularity and noise resistance requirements. Additionally, we present a deep learning-based gesture recognition framework tailored for fine-grained time sequence prediction and further integrated with a Finite-State Machine (FSM) algorithm for event-level prediction to suit the interaction experience for gestures of varying durations. We implement a functional eyewear prototype with two commercial PZT transducers. RimSense can recognize eight touch gestures on the eyeglass rim and estimate gesture durations simultaneously, allowing gestures of varying lengths to serve as distinct inputs. We evaluate the performance of RimSense on 30 subjects and show that it can sense eight gestures and an additional negative class with an F1-score of 0.95 and a relative duration estimation error of 11%. We further make the system work in real-time and conduct a user study on 14 subjects to assess the practicability of RimSense through interactions with two demo applications. The user study demonstrates RimSense's good performance, high usability, learnability and enjoyability. Additionally, we conduct interviews with the subjects, and their comments provide valuable insight for future eyewear design.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631456"}, {"primary_key": "949322", "vector": [], "sparse_vector": [], "title": "Headar: Sensing Head Gestures for Confirmation Dialogs on Smartwatches with Wearable Millimeter-Wave Radar.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Gaofeng Dong", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Nod and shake of one's head are intuitive and universal gestures in communication. As smartwatches become increasingly intelligent through advances in user activity sensing technologies, many use scenarios of smartwatches demand quick responses from users in confirmation dialogs, to accept or dismiss proposed actions. Such proposed actions include making emergency calls, taking service recommendations, and starting or stopping exercise timers. Head gestures in these scenarios could be preferable to touch interactions for being hands-free and easy to perform. We propose <PERSON><PERSON> to recognize these gestures on smartwatches using wearable millimeter wave sensing. We first surveyed head gestures to understand how they are performed in conversational settings. We then investigated positions and orientations to which users raise their smartwatches. Insights from these studies guided the implementation of Headar. Additionally, we conducted modeling and simulation to verify our sensing principle. We developed a real-time sensing and inference pipeline using contemporary deep learning techniques, and proved the feasibility of our proposed approach with a user study (n=15) and a live test (n=8). Our evaluation yielded an average accuracy of 84.0% in the user study across 9 classes including nod and shake as well as seven other signals -- still, speech, touch interaction, and four non-gestural head motions (i.e., head up, left, right, and down). Furthermore, we obtained an accuracy of 72.6% in the live test which reveals rich insights into the performance of our approach in various realistic conditions.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610900"}, {"primary_key": "949328", "vector": [], "sparse_vector": [], "title": "Semi-Supervised Learning for Wearable-based Momentary Stress Detection in the Wild.", "authors": ["<PERSON>", "Akane Sano"], "summary": "Physiological and behavioral data collected from wearable or mobile sensors have been used to estimate self-reported stress levels. Since stress annotation usually relies on self-reports during the study, a limited amount of labeled data can be an obstacle to developing accurate and generalized stress-predicting models. On the other hand, the sensors can continuously capture signals without annotations. This work investigates leveraging unlabeled wearable sensor data for stress detection in the wild. We propose a two-stage semi-supervised learning framework that leverages wearable sensor data to help with stress detection. The proposed structure consists of an auto-encoder pre-training method for learning information from unlabeled data and the consistency regularization approach to enhance the robustness of the model. Besides, we propose a novel active sampling method for selecting unlabeled samples to avoid introducing redundant information to the model. We validate these methods using two datasets with physiological signals and stress labels collected in the wild, as well as four human activity recognition (HAR) datasets to evaluate the generality of the proposed method. Our approach demonstrated competitive results for stress detection, improving stress classification performance by approximately 7% to 10% on the stress detection datasets compared to the baseline supervised learning models. Furthermore, the ablation study we conducted for the HAR tasks supported the effectiveness of our methods. Our approach showed comparable performance to state-of-the-art semi-supervised learning methods for both stress detection and HAR tasks.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596246"}, {"primary_key": "949332", "vector": [], "sparse_vector": [], "title": "TouchEditor: Interaction Design and Evaluation of a Flexible Touchpad for Text Editing of Head-Mounted Displays in Speech-unfriendly Environments.", "authors": ["<PERSON><PERSON><PERSON>", "Tianyang <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A text editing solution that adapts to speech-unfriendly (inconvenient to speak or difficult to recognize speech) environments is essential for head-mounted displays (HMDs) to work universally. For existing schemes, e.g., touch bar, virtual keyboard and physical keyboard, there are shortcomings such as insufficient speed, uncomfortable experience or restrictions on user location and posture. To mitigate these restrictions, we propose TouchEditor, a novel text editing system for HMDs based on a flexible piezoresistive film sensor, supporting cursor positioning, text selection, text retyping and editing commands (i.e., Copy, Paste, Delete, etc.). Through literature overview and heuristic study, we design a pressure-controlled menu and a shortcut gesture set for entering editing commands, and propose an area-and-pressure-based method for cursor positioning and text selection that skillfully maps gestures in different areas and with different strengths to cursor movements with different directions and granularities. The evaluation results show that TouchEditor i) adapts to various contents and scenes well with a stable correction speed of 0.075 corrections per second; ii) achieves 95.4% gesture recognition accuracy; iii) reaches a considerable level with a mobile phone in text selection tasks. The comparison results with the speech-dependent EYEditor and the built-in touch bar further prove the flexibility and robustness of TouchEditor in speech-unfriendly environments.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631454"}, {"primary_key": "949340", "vector": [], "sparse_vector": [], "title": "DYPA: A Machine Learning Dyslexia Prescreening Mobile Application for Chinese Children.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "S.<PERSON><PERSON><PERSON>"], "summary": "Identifying early a person with dyslexia, a learning disorder with reading and writing, is critical for effective treatment. As accredited specialists for clinical diagnosis of dyslexia are costly and undersupplied, we research and develop a computer-assisted approach to efficiently prescreen dyslexic Chinese children so that timely resources can be channelled to those at higher risk. Previous works in this area are mostly for English and other alphabetic languages, tailored narrowly for the reading disorder, or require costly specialized equipment. To overcome that, we present DYPA, a novel DYslexia Prescreening mobile Application for Chinese children. DYPA collects multimodal data from children through a set of specially designed interactive reading and writing tests in Chinese, and comprehensively analyzes their cognitive-linguistic skills with machine learning. To better account for the dyslexia-associated features in handwritten characters, DYPA employs a deep learning based multilevel Chinese handwriting analysis framework to extract features across the stroke, radical and character levels. We have implemented and installed DYPA in tablets, and our extensive trials with more than 200 pupils in Hong Kong validate its high predictive accuracy (81.14%), sensitivity (74.27%) and specificity (82.71%).", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610908"}, {"primary_key": "949142", "vector": [], "sparse_vector": [], "title": "Automatic Update for Wi-Fi Fingerprinting Indoor Localization via Multi-Target Domain Adaptation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Mengling Ou", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Wi-Fi fingerprinting system in the long term suffers from gradually deteriorative localization accuracy, leading to poor user experiences. To keep high accuracy yet at a low cost, we first study long-term variation of access points (APs) and characteristics of their Wi-Fi signals through over-one-year experiments. Motivated by the experimental findings, we then design MTLoc, a Multi-Target domain adaptation network-based Wi-Fi fingerprinting Localization system. As the core, MTDAN (Multi-Target Domain Adaptation Network) model adopts the framework of generative adversarial network to learn time-invariant, time-specific, and location-aware features from the source and target domains. To enhance the alignment among the source and targets, two-level cycle consistency constraints are proposed. Hence, MTDAN is able to transfer location knowledge from the source domain to multiple targets. In addition, domain selection and outlier detection are designed to avoid explosive growth of storage for targets and to limit the impact of random variations of Wi-Fi signals. Extensive experiments are carried out on five datasets collected over two years in various real-world indoor environments with a total area of 8, 350 m2. Experimental results demonstrate that MTLoc retains high localization accuracy with limited storage and training cost in the long term, which significantly outperforms its counterparts. We share our dataset to the community for other researchers to validate our results and conduct further research.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596239"}, {"primary_key": "949144", "vector": [], "sparse_vector": [], "title": "JoulesEye: Energy Expenditure Estimation and Respiration Sensing from Thermal Imagery While Exercising.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Nipun Batra", "<PERSON><PERSON>"], "summary": "Smartphones and smartwatches have contributed significantly to fitness monitoring by providing real-time statistics, thanks to accurate tracking of physiological indices such as heart rate. However, the estimation of calories burned during exercise is inaccurate and cannot be used for medical diagnosis. In this work, we present JoulesEye, a smartphone thermal camera-based system that can accurately estimate calorie burn by monitoring respiration rate. We evaluated JoulesEye on 54 participants who performed high intensity cycling and running. The mean absolute percentage error (MAPE) of JoulesEye was 5.8%, which is significantly better than the MAPE of 37.6% observed with commercial smartwatch-based methods that only use heart rate. Finally, we show that an ultra-low-resolution thermal camera that is small enough to fit inside a watch or other wearables is sufficient for accurate calorie burn estimation. These results suggest that JoulesEye is a promising new method for accurate and reliable calorie burn estimation.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631422"}, {"primary_key": "949145", "vector": [], "sparse_vector": [], "title": "AeroTraj: Trajectory Planning for Fast, and Accurate 3D Reconstruction Using a Drone-based LiDAR.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "John D&apos;Ambrosio", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents AeroTraj, a system that enables fast, accurate, and automated reconstruction of 3D models of large buildings using a drone-mounted LiDAR. LiDAR point clouds can be used directly to assemble 3D models if their positions are accurately determined. AeroTraj uses SLAM for this, but must ensure complete and accurate reconstruction while minimizing drone battery usage. Doing this requires balancing competing constraints: drone speed, height, and orientation. AeroTraj exploits building geometry in designing an optimal trajectory that incorporates these constraints. Even with an optimal trajectory, SLAM's position error can drift over time, so AeroTraj tracks drift in-flight by offloading computations to the cloud and invokes a re-calibration procedure to minimize error. AeroTraj can reconstruct large structures with centimeter-level accuracy and with an average end-to-end latency below 250 ms, significantly outperforming the state of the art.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610911"}, {"primary_key": "949147", "vector": [], "sparse_vector": [], "title": "Behavior Modeling Approach for Forecasting Physical Functioning of People with Multiple Sclerosis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Forecasting physical functioning of people with Multiple Sclerosis (MS) can inform timely clinical interventions and accurate \"day planning\" to improve their well-being. However, people's physical functioning often remains unchecked in between infrequent clinical visits, leading to numerous negative healthcare outcomes. Existing Machine Learning (ML) models trained on in-situ data collected outside of clinical settings (e.g., in people's homes) predict which people are currently experiencing low functioning. However, they do not forecast if and when people's symptoms and behaviors will negatively impact their functioning in the future. Here, we present a computational behavior model that formalizes clinical knowledge about MS to forecast people's end-of-day physical functioning in advance to support timely interventions. Our model outperformed existing ML baselines in a series of quantitative validation experiments. We showed that our model captured clinical knowledge about MS using qualitative visual model exploration in different \"what-if\" scenarios. Our work enables future behavior-aware interfaces that deliver just-in-time clinical interventions and aid in \"day planning\" and \"activity pacing\".", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580887"}, {"primary_key": "949149", "vector": [], "sparse_vector": [], "title": "LemurDx: Using Unconstrained Passive Sensing for an Objective Measurement of Hyperactivity in Children with no Parent Input.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Hyperactivity is the most dominant presentation of Attention-Deficit/Hyperactivity Disorder in young children. Currently, measuring hyperactivity involves parents' or teachers' reports. These reports are vulnerable to subjectivity and can lead to misdiagnosis. LemurDx provides an objective measure of hyperactivity using passive mobile sensing. We collected data from 61 children (25 with hyperactivity) who wore a smartwatch for up to 7 days without changing their daily routine. The participants' parents maintained a log of the child's activities at a half-hour granularity (e.g., sitting, exercising) as contextual information. Our ML models achieved 85.2% accuracy in detecting hyperactivity in children (using parent-provided activity labels). We also built models that estimated children's context from the sensor data and did not rely on activity labels to reduce parent burden. These models achieved 82.0% accuracy in detecting hyperactivity. In addition, we interviewed five clinicians who suggested a need for a tractable risk score that enables analysis of a child's behavior across contexts. Our results show the feasibility of supporting the diagnosis of hyperactivity by providing clinicians with an interpretable and objective score of hyperactivity using off-the-shelf watches and adding no constraints to children or their guardians.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596244"}, {"primary_key": "949150", "vector": [], "sparse_vector": [], "title": "MI-Poser: Human Body Pose Tracking Using Magnetic and Inertial Sensor Fusion with Metal Interference Mitigation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>"], "summary": "Inside-out tracking of human body poses using wearable sensors holds significant potential for AR/VR applications, such as remote communication through 3D avatars with expressive body language. Current inside-out systems often rely on vision-based methods utilizing handheld controllers or incorporating densely distributed body-worn IMU sensors. The former limits hands-free and occlusion-robust interactions, while the latter is plagued by inadequate accuracy and jittering. We introduce a novel body tracking system, MI-Poser, which employs AR glasses and two wrist-worn electromagnetic field (EMF) sensors to achieve high-fidelity upper-body pose estimation while mitigating metal interference. Our lightweight system demonstrates a minimal error (6.6 cm mean joint position error) with real-world data collected from 10 participants. It remains robust against various upper-body movements and operates efficiently at 60 Hz. Furthermore, by incorporating an IMU sensor co-located with the EMF sensor, MI-Poser presents solutions to counteract the effects of metal interference, which inherently disrupts the EMF signal during tracking. Our evaluation effectively showcases the successful detection and correction of interference using our EMF-IMU fusion approach across environments with diverse metal profiles. Ultimately, MI-Poser offers a practical pose tracking system, particularly suited for body-centric AR applications.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610891"}, {"primary_key": "949151", "vector": [], "sparse_vector": [], "title": "Semantic Loss: A New Neuro-Symbolic Approach for Context-Aware Human Activity Recognition.", "authors": ["<PERSON>", "Gabriele Civitarese", "<PERSON>"], "summary": "Deep Learning models are a standard solution for sensor-based Human Activity Recognition (HAR), but their deployment is often limited by labeled data scarcity and models' opacity. Neuro-Symbolic AI (NeSy) provides an interesting research direction to mitigate these issues by infusing knowledge about context information into HAR deep learning classifiers. However, existing NeSy methods for context-aware HAR require computationally expensive symbolic reasoners during classification, making them less suitable for deployment on resource-constrained devices (e.g., mobile devices). Additionally, NeSy approaches for context-aware HAR have never been evaluated on in-the-wild datasets, and their generalization capabilities in real-world scenarios are questionable. In this work, we propose a novel approach based on a semantic loss function that infuses knowledge constraints in the HAR model during the training phase, avoiding symbolic reasoning during classification. Our results on scripted and in-the-wild datasets show the impact of different semantic loss functions in outperforming a purely data-driven model. We also compare our solution with existing NeSy methods and analyze each approach's strengths and weaknesses. Our semantic loss remains the only NeSy solution that can be deployed as a single DNN without the need for symbolic reasoning modules, reaching recognition rates close (and better in some cases) to existing approaches.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631407"}, {"primary_key": "949152", "vector": [], "sparse_vector": [], "title": "Scribe: Simultaneous Voice and Handwriting Interface.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents the design and implementation of Scribe, a comprehensive voice processing and handwriting interface for voice assistants. Distinct from prior works, Scribe is a precise tracking interface that can co-exist with the voice interface on low sampling rate voice assistants. Scribe can be used for 3D free-form drawing, writing, and motion tracking for gaming. Taking handwriting as a specific application, it can also capture natural strokes and the individualized style of writing while occupying only a single frequency. The core technique includes an accurate acoustic ranging method called Cross Frequency Continuous Wave (CFCW) sonar, enabling voice assistants to use ultrasound as a ranging signal while using the regular microphone system of voice assistants as a receiver. We also design a new optimization algorithm that only requires a single frequency for time difference of arrival. Scribe prototype achieves 73 μm of median error for 1D ranging and 1.4 mm of median error in 3D tracking of an acoustic beacon using the microphone array used in voice assistants. Our implementation of an in-air handwriting interface achieves 94.1% accuracy with automatic handwriting-to-text software, similar to writing on paper (96.6%). At the same time, the error rate of voice-based user authentication only increases from 6.26% to 8.28%.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631411"}, {"primary_key": "949153", "vector": [], "sparse_vector": [], "title": "PulmoListener: Continuous Acoustic Monitoring of Chronic Obstructive Pulmonary Disease in the Wild.", "authors": ["<PERSON><PERSON><PERSON>", "Salaar Liaqat", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Prior work has shown the utility of acoustic analysis in controlled settings for assessing chronic obstructive pulmonary disease (COPD) --- one of the most common respiratory diseases that impacts millions of people worldwide. However, such assessments require active user input and may not represent the true characteristics of a patient's voice. We propose PulmoListener, an end-to-end speech processing pipeline that identifies segments of the patient's speech from smartwatch audio collected during daily living and analyzes them to classify COPD symptom severity. To evaluate our approach, we conducted a study with 8 COPD patients over 164 ± 92 days on average. We found that PulmoListener achieved an average sensitivity of 0.79 ± 0.03 and a specificity of 0.83 ± 0.05 per patient when classifying their symptom severity on the same day. PulmoListener can also predict the severity level up to 4 days in advance with an average sensitivity of 0.75 ± 0.02 and a specificity of 0.74 ± 0.07. The results of our study demonstrate the feasibility of leveraging natural speech for monitoring COPD in real-world settings, offering a promising solution for disease management and even diagnosis.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610889"}, {"primary_key": "949154", "vector": [], "sparse_vector": [], "title": "Mites: Design and Deployment of a General-Purpose Sensing Infrastructure for Buildings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "There is increasing interest in deploying building-scale, general-purpose, and high-fidelity sensing to drive emerging smart building applications. However, the real-world deployment of such systems is challenging due to the lack of system and architectural support. Most existing sensing systems are purpose-built, consisting of hardware that senses a limited set of environmental facets, typically at low fidelity and for short-term deployment. Furthermore, prior systems with high-fidelity sensing and machine learning fail to scale effectively and have fewer primitives, if any, for privacy and security. For these reasons, IoT deployments in buildings are generally short-lived or done as a proof of concept. We present the design of Mites, a scalable end-to-end hardware-software system for supporting and managing distributed general-purpose sensors in buildings. Our design includes robust primitives for privacy and security, essential features for scalable data management, as well as machine learning to support diverse applications in buildings. We deployed our Mites system and 314 Mites devices in Tata Consultancy Services (TCS) Hall at Carnegie Mellon University (CMU), a fully occupied, five-story university building. We present a set of comprehensive evaluations of our system using a series of microbenchmarks and end-to-end evaluations to show how we achieved our stated design goals. We include five proof-of-concept applications to demonstrate the extensibility of the Mites system to support compelling IoT applications. Finally, we discuss the real-world challenges we faced and the lessons we learned over the five-year journey of our stack's iterative design, development, and deployment.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580865"}, {"primary_key": "949155", "vector": [], "sparse_vector": [], "title": "TAO: Context Detection from Daily Activity Patterns Using Temporal Analysis and Ontology.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Translating fine-grained activity detection (e.g., phone ring, talking interspersed with silence and walking) into semantically meaningful and richer contextual information (e.g., on a phone call for 20 minutes while exercising) is essential towards enabling a range of healthcare and human-computer interaction applications. Prior work has proposed building ontologies or temporal analysis of activity patterns with limited success in capturing complex real-world context patterns. We present TAO, a hybrid system that leverages OWL-based ontologies and temporal clustering approaches to detect high-level contexts from human activities. TAO can characterize sequential activities that happen one after the other and activities that are interleaved or occur in parallel to detect a richer set of contexts more accurately than prior work. We evaluate TAO on real-world activity datasets (Casas and Extrasensory) and show that our system achieves, on average, 87% and 80% accuracy for context detection, respectively. We deploy and evaluate TAO in a real-world setting with eight participants using our system for three hours each, demonstrating TAO's ability to capture semantically meaningful contexts in the real world. Finally, to showcase the usefulness of contexts, we prototype wellness applications that assess productivity and stress and show that the wellness metrics calculated using contexts provided by TAO are much closer to the ground truth (on average within 1.1%), as compared to the baseline approach (on average within 30%).", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610896"}, {"primary_key": "949156", "vector": [], "sparse_vector": [], "title": "FeverPhone: Accessible Core-Body Temperature Sensing for Fever Monitoring Using Commodity Smartphones.", "authors": ["<PERSON>", "Masta<PERSON>ton", "<PERSON>", "Shwetak N. Patel"], "summary": "Inside all smart devices, such as smartphones or smartwatches, there are thermally sensitive resistors known as thermistors which are used to monitor the temperature of the device. These thermistors are sensitive to temperature changes near their location on-device. While they are designed to measure the temperature of the device components such as the battery, they can also sense changes in the temperature of the ambient environment or thermal entities in contact with the device. We have developed a model to estimate core body temperature from signals sensed by these thermistors during a user interaction in which the user places the capacitive touchscreen of a smart device against a thermal site on their body such as their forehead. During the interaction, the device logs the temperature sensed by the thermistors as well as the raw capacitance seen by the touch screen to capture features describing the rate of heat transfer from the body to the device and device-to-skin contact respectively. These temperature and contact features are then used to model the rate of heat transferred from the user's body to the device and thus core-body temperature of the user for ubiquitous and accessible fever monitoring using only a smart device. We validate this system in a lab environment on a simulated skin-like heat source with a temperature estimate mean absolute error of 0.743$^{\\circ}$F (roughly 0.4$^{\\circ}$C) and limit of agreement of $\\pm2.374^{\\circ}$F (roughly 1.3$^{\\circ}$C) which is comparable to some off-the-shelf peripheral and tympanic thermometers. We found a Pearson's correlation $R^2$ of 0.837 between ground truth temperature and temperature estimated by our system. We also deploy this system in an ongoing clinical study on a population of 7 participants in a clinical environment to show the similarity between simulated and clinical trials.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580850"}, {"primary_key": "949157", "vector": [], "sparse_vector": [], "title": "EarAcE: Empowering Versatile Acoustic Sensing via Earable Active Noise Cancellation Platform.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In recent years, particular attention has been devoted to earable acoustic sensing due to its numerous applications. However, the lack of a common platform for accessing raw audio samples has forced researchers/developers to pay great efforts to the trifles of prototyping often irrelevant to the core sensing functions. Meanwhile, the growing popularity of active noise cancellation (ANC) has endowed common earphones with high standard acoustic capability yet to be explored by sensing. To this end, we propose EarACE to be the first acoustic sensing platform exploiting the native acoustics of commercial ANC earphones, significantly improving upon self-crafted earphone sensing devices. EarACE takes a compact design to handle hardware heterogeneity and to deliver flexible control on audio facilities. Leveraging a systematic study on in-ear acoustic signals, EarACE gains abilities to combat performance sensitivity to device wearing states and to eliminate body motion interference. We further implement three major acoustic sensing applications to showcase the efficacy and adaptability of EarACE; the results evidently demonstrate EarACE's promising future in facilitating earable acoustic sensing research.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596242"}, {"primary_key": "949158", "vector": [], "sparse_vector": [], "title": "ViSig: Automatic Interpretation of Visual Body Signals Using On-Body Sensors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Visual body signals are designated body poses that deliver an application-specific message. Such signals are widely used for fast message communication in sports (signaling by umpires and referees), transportation (naval officers and aircraft marshallers), and construction (signaling by riggers and crane operators), to list a few examples. Automatic interpretation of such signals can help maintaining safer operations in these industries, help in record-keeping for auditing or accident investigation purposes, and function as a score-keeper in sports. When automation of these signals is desired, it is traditionally performed from a viewer's perspective by running computer vision algorithms on camera feeds. However, computer vision based approaches suffer from performance deterioration in scenarios such as lighting variations, occlusions, etc., might face resolution limitations, and can be challenging to install. Our work, ViSig, breaks with tradition by instead deploying on-body sensors for signal interpretation. Our key innovation is the fusion of ultra-wideband (UWB) sensors for capturing on-body distance measurements, inertial sensors (IMU) for capturing orientation of a few body segments, and photodiodes for finger signal recognition, enabling a robust interpretation of signals. By deploying only a small number of sensors, we show that body signals can be interpreted unambiguously in many different settings, including in games of Cricket, Baseball, and Football, and in operational safety use-cases such as crane operations and flag semaphores for maritime navigation, with &gt; 90% accuracy. Overall, we have seen substantial promise in this approach and expect a large body of future follow-on work to start using UWB and IMU fused modalities for the more general human pose estimation problems.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580797"}, {"primary_key": "949159", "vector": [], "sparse_vector": [], "title": "FSS-Tag: High Accuracy Material Identification System Based On Frequency Selective Surface Tag.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Guodong Xie", "<PERSON><PERSON>", "<PERSON>"], "summary": "Material sensing is crucial in many emerging applications, such as waste classification and hazardous material detection. Although existing Radio Frequency (RF) signal based systems achieved great success, they have limited identification accuracy when either RF signals can not penetrate through a target or a target has different outer and inner materials. This paper introduces a Frequency Selective Surface (FSS) tag based high accuracy material identification system, namely FSS-Tag, which utilises both the penetrating signals and the coupling effect. Specifically, we design and attach a FSS tag to a target, and use frequency responses of the tag for material sensing, since different target materials have different frequency responses. The key advantage of our system is that, when RF signals pass through a target with the FSS tag, the penetrating signal responds more to the inner material, and the coupling effect (between the target and the tag) reflects more about the outer material; thus, one can achieve a higher sensing accuracy. The challenge lies in how to find optimal tag design parameters so that the frequency response of different target materials can be clearly distinguished. We address this challenge by establishing a tag parameter optimization model. Real-world experiments show that FSS-Tag achieves more than 91% accuracy on identifying eight common materials, and improves the accuracy by up to 38% and 8% compared with the state of the art (SOTA) penetrating signal based method TagScan and the SOTA coupling effect based method Tagtag.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631457"}, {"primary_key": "949160", "vector": [], "sparse_vector": [], "title": "VoiceCloak: Adversarial Example Enabled Voice De-Identification with Balanced Privacy and Utility.", "authors": ["<PERSON><PERSON>", "Li Lu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Zhongjie Ba", "<PERSON>", "<PERSON><PERSON>"], "summary": "Faced with the threat of identity leakage during voice data publishing, users are engaged in a privacy-utility dilemma when enjoying the utility of voice services. Existing machine-centric studies employ direct modification or text-based re-synthesis to de-identify users' voices but cause inconsistent audibility for human participants in emerging online communication scenarios, such as virtual meetings. In this paper, we propose a human-centric voice de-identification system, VoiceCloak, which uses adversarial examples to balance the privacy and utility of voice services. Instead of typical additive examples inducing perceivable distortions, we design a novel convolutional adversarial example that modulates perturbations into real-world room impulse responses. Benefiting from this, VoiceCloak could preserve user identity from exposure by Automatic Speaker Identification (ASI), while remaining the voice perceptual quality for non-intrusive de-identification. Moreover, VoiceCloak learns a compact speaker distribution through a conditional variational auto-encoder to synthesize diverse targets on demand. Guided by these pseudo targets, VoiceCloak constructs adversarial examples in an input-specific manner, enabling any-to-any identity transformation for robust de-identification. Experimental results show that VoiceCloak could achieve over 92% and 84% successful de-identification on mainstream ASIs and commercial systems with excellent voiceprint consistency, speech integrity, and audio quality.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596266"}, {"primary_key": "949161", "vector": [], "sparse_vector": [], "title": "CAvatar: Real-time Human Activity Mesh Reconstruction via Tactile Carpets.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Liu", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Human mesh reconstruction is essential for various applications, including virtual reality, motion capture, sports performance analysis, and healthcare monitoring. In healthcare contexts such as nursing homes, it is crucial to employ plausible and non-invasive methods for human mesh reconstruction that preserve privacy and dignity. Traditional vision-based techniques encounter challenges related to occlusion, viewpoint limitations, lighting conditions, and privacy concerns. In this research, we present CAvatar, a real-time human mesh reconstruction approach that innovatively utilizes pressure maps recorded by a tactile carpet as input. This advanced, non-intrusive technology obviates the need for cameras during usage, thereby safeguarding privacy. Our approach addresses several challenges, such as the limited spatial resolution of tactile sensors, extracting meaningful information from noisy pressure maps, and accommodating user variations and multiple users. We have developed an attention-based deep learning network, complemented by a discriminator network, to predict 3D human pose and shape from 2D pressure maps with notable accuracy. Our model demonstrates promising results, with a mean per joint position error (MPJPE) of 5.89 cm and a per vertex error (PVE) of 6.88 cm. To the best of our knowledge, we are the first to generate 3D mesh of human activities solely using tactile carpet signals, offering a novel approach that addresses privacy concerns and surpasses the limitations of existing vision-based and wearable solutions. The demonstration of CAvatar is shown at https://youtu.be/ZpO3LEsgV7Y.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631424"}, {"primary_key": "949163", "vector": [], "sparse_vector": [], "title": "DisPad: Flexible On-Body Displacement of Fabric Sensors for Robust Joint-Motion Tracking.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Ji<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hongbo Fu"], "summary": "The last few decades have witnessed an emerging trend of wearable soft sensors; however, there are important signal-processing challenges for soft sensors that still limit their practical deployment. They are error-prone when displaced, resulting in significant deviations from their ideal sensor output. In this work, we propose a novel prototype that integrates an elbow pad with a sparse network of soft sensors. Our prototype is fully bio-compatible, stretchable, and wearable. We develop a learning-based method to predict the elbow orientation angle and achieve an average tracking error of 9.82 degrees for single-user multi-motion experiments. With transfer learning, our method achieves the average tracking errors of 10.98 degrees and 11.81 degrees across different motion types and users, respectively. Our core contributions lie in a solution that realizes robust and stable human joint motion tracking across different device displacements.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580832"}, {"primary_key": "949165", "vector": [], "sparse_vector": [], "title": "Environment-aware Multi-person Tracking in Indoor Environments with MmWave Radars.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xiaoyang B<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "Device-free indoor localization and tracking using commercial millimeter wave radars have attracted much interest lately due to their non-intrusive nature and high spatial resolution. However, it is challenging to achieve high tracking accuracy due to rich multipath reflection and occlusion in indoor environments. Static objects with non-negligible reflectance of mmWave signals interact with moving human subjects and generate time-varying multipath ghosts and shadow ghosts, which can be easily confused as real subjects. To characterize the complex interactions, we first develop a geometric model that estimates the location of multipath ghosts given the locations of humans and static reflectors. Based on this model, the locations of static reflectors that form a reflection map are automatically estimated from received radar signals as a single person traverses the environment along arbitrary trajectories. The reflection map allows for the elimination of multipath and shadow ghost interference as well as the augmentation of weakly reflected human subjects in occluded areas. The proposed environment-aware multi-person tracking system can generate reflection maps with a mean error of 15.5cm and a 90-percentile error of 30.3cm, and achieve multi-person tracking accuracy with a mean error of 8.6cm and a 90-percentile error of 17.5cm, in four representative indoor spaces with diverse subjects using a single mmWave radar.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610902"}, {"primary_key": "949166", "vector": [], "sparse_vector": [], "title": "RF-Mic: Live Voice Eavesdropping via Capturing Subtle Facial Speech Dynamics Leveraging RFID.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Linghe Kong", "Hao Kong", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Eavesdropping on human voice is one of the most common but harmful threats to personal privacy. Glasses are in direct contact with human face, which could sense facial motions when users speak, so human speech contents could be inferred by sensing the movements of glasses. In this paper, we present a live voice eavesdropping method, RF-Mic, which utilizes common glasses attached with a low-cost RFID tag to sense subtle facial speech dynamics for inferring possible voice contents. When a user with a glasses, which is attached an RFID tag on the glass bridge, is speaking, RF-Mic first collects RF signals through forward propagation and backscattering. Then, body motion interference is eliminated from the collected RF signals through a proposed Conditional Denoising AutoEncoder (CDAE) network. Next, RF-Mic extracts three kinds of facial speech dynamic features (i.e., facial movements, bone-borne vibrations, and airborne vibrations) by designing three different deep-learning models. Based on the extracted features, a facial speech dynamics model is constructed for live voice eavesdropping. Extensive experiments in different real environments demonstrate that RF-Mic can achieve robust and accurate human live voice eavesdropping.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596259"}, {"primary_key": "949169", "vector": [], "sparse_vector": [], "title": "AI-to-Human Actuation: Boosting Unmodified AI&apos;s Robustness by Proactively Inducing Favorable Human Sensing Conditions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Imagine a near-future smart home. Home-embedded visual AI sensors continuously monitor the resident, inferring her activities and internal states that enable higher-level services. Here, as home-embedded sensors passively monitor a free person, good inferences happen randomly. The inferences' confidence highly depends on how congruent her momentary conditions are to the conditions favored by the AI models, e.g., front-facing or unobstructed. We envision new strategies of AI-to-Human Actuation (AHA) that empower the sensory AIs with proactive actuation so that they induce the person's conditions to be more favorable to the AIs. In this light, we explore the initial feasibility and efficacy of AHA in the context of home-embedded visual AIs. We build a taxonomy of actuations that could be issued to home residents to benefit visual AIs. We deploy AHA in an actual home rich in sensors and interactive devices. With 20 participants, we comprehensively study their experiences with proactive actuation blended with their usual home routines. We also demonstrate the substantially improved inferences of the actuation-empowered AIs over the passive sensing baseline. This paper sets forth an initial step towards interweaving human-targeted AIs and proactive actuation to yield more chances for high-confidence inferences without sophisticating the model, in order to improve robustness against unfavorable conditions.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580812"}, {"primary_key": "949170", "vector": [], "sparse_vector": [], "title": "RealityReplay: Detecting and Replaying Temporal Changes In Situ Using Mixed Reality.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Humans easily miss events in their surroundings due to limited short-term memory and field of view. This happens, for example, while watching an instructor's machine repair demonstration or conversing during a sports game. We present RealityReplay, a novel Mixed Reality (MR) approach that tracks and visualizes these significant events using in-situ MR visualizations without modifying the physical space. It requires only a head-mounted MR display and a 360-degree camera. We contribute a method for egocentric tracking of important motion events in users' surroundings based on a combination of semantic segmentation and saliency prediction, and generating in-situ MR visual summaries of temporal changes. These summary visualizations are overlaid onto the physical world to reveal which objects moved, in what order, and their trajectory, enabling users to observe previously hidden events. The visualizations are informed by a formative study comparing different styles on their effects on users' perception of temporal changes. Our evaluation shows that RealityReplay significantly enhances sensemaking of temporal motion events compared to memory-based recall. We demonstrate application scenarios in guidance, education, and observation, and discuss implications for extending human spatiotemporal capabilities through technological augmentation.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610888"}, {"primary_key": "949171", "vector": [], "sparse_vector": [], "title": "VibPath: Two-Factor Authentication with Your Hand&apos;s Vibration Response to Unlock Your Phone.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> <PERSON>", "Yincheng Jin", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Technical advances in the smart device market have fixated smartphones at the heart of our lives, warranting an ever more secure means of authentication. Although most smartphones have adopted biometrics-based authentication, after a couple of failed attempts, most users are given the option to quickly bypass the system with passcodes. To add a layer of security, two-factor authentication (2FA) has been implemented but has proven to be vulnerable to various attacks. In this paper, we introduce VibPath, a simultaneous 2FA scheme that can understand the user's hand neuromuscular system through touch behavior. VibPath captures the individual's vibration path responses between the hand and the wrist with the attention-based encoder-decoder network, authenticating the genuine users from the imposters unobtrusively. In a user study with 30 participants, VibPath achieved an average performance of 0.98 accuracy, 0.99 precision, 0.98 recall, 0.98 f1-score for user verification, and 94.3% accuracy for user identification across five passcodes. Furthermore, we also conducted several extensive studies, including in-the-wile, permanence, vulnerability, usability, and system overhead studies, to assess the practicability and viability of the VibPath from multiple aspects.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610894"}, {"primary_key": "949172", "vector": [], "sparse_vector": [], "title": "Scalability in External Communication of Automated Vehicles: Evaluation and Recommendations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Automated vehicles will alter traffic fundamentally. While users can engage in non-driving-related tasks such as reading or even sleeping, the possibility to interact with other road users such as pedestrians via, for example, eye contact vanishes. Therefore, external communication of automated vehicles is currently researched with various concepts spanning dimensions such as anthropomorphism, technology, viewpoint, locus, message type, and others. However, the proposed concepts are mostly evaluated in simple scenarios, such as one person trying to cross in front of one automated vehicle. Therefore, we implemented a WebGL application of a four-lane road and conducted a within-subject study (N=46) to study the effects of nine concepts with and without the presence of other pedestrians and altering the yielding target of the automated vehicle. We found that all concepts were rated better than having no external communication. However, the effects were not uniform across the concepts.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596248"}, {"primary_key": "949174", "vector": [], "sparse_vector": [], "title": "Effects of Uncertain Trajectory Prediction Visualization in Highly Automated Vehicles on Trust, Situation Awareness, and Cognitive Load.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Automated vehicles are expected to improve safety, mobility, and inclusion. User acceptance is required for the successful introduction of this technology. One essential prerequisite for acceptance is appropriately trusting the vehicle's capabilities. System transparency via visualizing internal information could calibrate this trust by enabling the surveillance of the vehicle's detection and prediction capabilities, including its failures. Additionally, concurrently increased situation awareness could improve take-overs in case of emergency. This work reports the results of two online comparative video-based studies on visualizing prediction and maneuver-planning information. Effects on trust, cognitive load, and situation awareness were measured using a simulation (N=280) and state-of-the-art road user prediction and maneuver planning on a pre-recorded real-world video using a real prototype (N=238). Results show that color conveys uncertainty best, that the planned trajectory increased trust, and that the visualization of other predicted trajectories improved perceived safety.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631408"}, {"primary_key": "949175", "vector": [], "sparse_vector": [], "title": "RoVaR: Robust Multi-agent Tracking through Dual-layer Diversity in Visual and RF Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The plethora of sensors in our commodity devices provides a rich substrate for sensor-fused tracking. Yet, today's solutions are unable to deliver robust and high tracking accuracies across multiple agents in practical, everyday environments - a feature central to the future of immersive and collaborative applications. This can be attributed to the limited scope of diversity leveraged by these fusion solutions, preventing them from catering to the multiple dimensions of accuracy, robustness (diverse environmental conditions) and scalability (multiple agents) simultaneously. In this work, we take an important step towards this goal by introducing the notion of dual-layer diversity to the problem of sensor fusion in multi-agent tracking. We demonstrate that the fusion of complementary tracking modalities, - passive/relative (e.g. visual odometry) and active/absolute tracking (e.g.infrastructure-assisted RF localization) offer a key first layer of diversity that brings scalability while the second layer of diversity lies in the methodology of fusion, where we bring together the complementary strengths of algorithmic (for robustness) and data-driven (for accuracy) approaches. ROVAR is an embodiment of such a dual-layer diversity approach that intelligently attends to cross-modal information using algorithmic and data-driven techniques that jointly share the burden of accurately tracking multiple agents in the wild. Extensive evaluations reveal ROVAR'S multi-dimensional benefits in terms of tracking accuracy, scalability and robustness to enable practical multi-agent immersive applications in everyday environments.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580854"}, {"primary_key": "949176", "vector": [], "sparse_vector": [], "title": "&quot;It&apos;s Not an Issue of Malice, but of Ignorance&quot;: Towards Inclusive Video Conferencing for Presenters Who are d/Deaf or Hard of Hearing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "As video conferencing (VC) has become necessary for many professional, educational, and social tasks, people who are d/Deaf and hard of hearing (DHH) face distinct accessibility barriers. We conducted studies to understand the challenges faced by DHH people during VCs and found that they struggled to easily present or communicate effectively due to accessibility limitations of VC platforms. These limitations include the lack of tools for DHH speakers to discreetly communicate their accommodation needs to the group. Based on these findings, we prototyped a suite of tools, called Erato that enables DHH speakers to be aware of their performance while speaking and remind participants of proper etiquette. We evaluated <PERSON><PERSON> by running a mock classroom case study over VC for three sessions. All participants felt more confident in their speaking ability and paid closer attention to making the classroom more inclusive while using our tool. We share implications of these results for the design of VC interfaces and human-the-the-loop assistive systems that can support users who are DHH to communicate effectively and advocate for their accessibility needs.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610901"}, {"primary_key": "949177", "vector": [], "sparse_vector": [], "title": "Reenvisioning Patient Education with Smart Hospital Patient Rooms.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Smart hospital patient rooms incorporate various smart devices to allow digital control of the entertainment --- such as TV and soundbar --- and the environment --- including lights, blinds, and thermostat. This technology can benefit patients by providing a more accessible, engaging, and personalized approach to their care. Many patients arrive at a rehabilitation hospital because they suffered a life-changing event such as a spinal cord injury or stroke. It can be challenging for patients to learn to cope with the changed abilities that are the new norm in their lives. This study explores ways smart patient rooms can support rehabilitation education to prepare patients for life outside the hospital's care. We conducted 20 contextual inquiries and four interviews with rehabilitation educators as they performed education sessions with patients and informal caregivers. Using thematic analysis, our findings offer insights into how smart patient rooms could revolutionize patient education by fostering better engagement with educational content, reducing interruptions during sessions, providing more agile education content management, and customizing therapy elements for each patient's unique needs. Lastly, we discuss design opportunities for future smart patient room implementations for a better educational experience in any healthcare context.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631419"}, {"primary_key": "949178", "vector": [], "sparse_vector": [], "title": "Unobtrusive Air Leakage Estimation for Earables with In-ear Microphones.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Khaldoon Al-Naimi", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Earables (in-ear wearables) are gaining increasing attention for sensing applications and healthcare research thanks to their ergonomy and non-invasive nature. However, air leakages between the device and the user's ear, resulting from daily activities or wearing variabilities, can decrease the performance of applications, interfere with calibrations, and reduce the robustness of the overall system. Existing literature lacks established methods for estimating the degree of air leaks (i.e., seal integrity) to provide information for the earable applications. In this work, we proposed a novel unobtrusive method for estimating the air leakage level of earbuds based on an in-ear microphone. The proposed method aims to estimate the magnitude of distortions, reflections, and external noise in the ear canal while excluding the speaker output by learning the speaker-to-microphone transfer function which allows us to perform the task unobtrusively. Using the obtained residual signal in the ear canal, we extract three features and deploy a machine-learning model for estimating the air leakage level. We investigated our system under various conditions to validate its robustness and resilience against the motion and other artefacts. Our extensive experimental evaluation shows that the proposed method can track air leakage levels under different daily activities. \"The best computer is a quiet, invisible servant.\" ~<PERSON>", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631405"}, {"primary_key": "949179", "vector": [], "sparse_vector": [], "title": "Midas: Generating mmWave Radar Data from Videos for Training Pervasive and Privacy-preserving Human Sensing Tasks.", "authors": ["Kaikai Deng", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Huadong Ma"], "summary": "Millimeter wave radar is a promising sensing modality for enabling pervasive and privacy-preserving human sensing. However, the lack of large-scale radar datasets limits the potential of training deep learning models to achieve generalization and robustness. To close this gap, we resort to designing a software pipeline that leverages wealthy video repositories to generate synthetic radar data, but it confronts key challenges including i) multipath reflection and attenuation of radar signals among multiple humans, ii) unconvertible generated data leading to poor generality for various applications, and iii) the class-imbalance issue of videos leading to low model stability. To this end, we design Midas to generate realistic, convertible radar data from videos via two components: (i) a data generation network (DG-Net) combines several key modules, depth prediction, human mesh fitting and multi-human reflection model, to simulate the multipath reflection and attenuation of radar signals to output convertible coarse radar data, followed by a Transformer model to generate realistic radar data; (ii) a variant Siamese network (VS-Net) selects key video clips to eliminate data redundancy for addressing the class-imbalance issue. We implement and evaluate Midas with video data from various external data sources and real-world radar data, demonstrating its great advantages over the state-of-the-art approach for both activity recognition and object detection tasks.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580872"}, {"primary_key": "949180", "vector": [], "sparse_vector": [], "title": "MI-Mesh: 3D Human Mesh Construction by Fusing Image and Millimeter Wave.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Estimating 3D human mesh is appealing for various application scenarios. Current mainstream solution predicts the meshes either from the image or the human reflected RF-signals. In this paper, instead of investigating which approach is better, we propose to design a multi-modality fusion framework, namely MI-Mesh, which estimates 3D meshes by fusing image and mmWave. To realize this, we design a deep neural network model. It first automatically correlate mmWave point clouds to certain human joints and extracts useful fused features from two modalities. Then, the features are refined by predicting 2D joints and silhouette. Finally, we regress pose and shape parameters and feed them to SMPL model to generate the 3D human meshes. We build a prototype on commercial mmWave radar and camera. The experimental results demonstrate that with the integration of multi-modality strengths, MI-Mesh can effectively recover human meshes on dynamic motions and across different conditions.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580861"}, {"primary_key": "949181", "vector": [], "sparse_vector": [], "title": "Soil Moisture Sensing with UAV-Mounted IR-UWB Radar and Deep Learning.", "authors": ["<PERSON><PERSON>", "<PERSON>ming Jin", "<PERSON>", "<PERSON><PERSON> Wang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Lu <PERSON>", "<PERSON><PERSON>", "Mingyuan Tao", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Wide-area soil moisture sensing is a key element for smart irrigation systems. However, existing soil moisture sensing methods usually fail to achieve both satisfactory mobility and high moisture estimation accuracy. In this paper, we present the design and implementation of a novel soil moisture sensing system, named as SoilId, that combines a UAV and a COTS IR-UWB radar for wide-area soil moisture sensing without the need of burying any battery-powered in-ground device. Specifically, we design a series of novel methods to help SoilId extract soil moisture related features from the received radar signals, and automatically detect and discard the data contaminated by the UAV's uncontrollable motion and the multipath interference. Furthermore, we leverage the powerful representation ability of deep neural networks and carefully design a neural network model to accurately map the extracted radar signal features to soil moisture estimations. We have extensively evaluated SoilId against a variety of real-world factors, including the UAV's uncontrollable motion, the multipath interference, soil surface coverages, and many others. Specifically, the experimental results carried out by our UAV-based system validate that SoilId can push the accuracy limits of RF-based soil moisture sensing techniques to a 50% quantile MAE of 0.23%.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580867"}, {"primary_key": "949182", "vector": [], "sparse_vector": [], "title": "Bias Mitigation in Federated Learning for Edge Computing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Federated learning (FL) is a distributed machine learning paradigm that enables data owners to collaborate on training models while preserving data privacy. As FL effectively leverages decentralized and sensitive data sources, it is increasingly used in ubiquitous computing including remote healthcare, activity recognition, and mobile applications. However, FL raises ethical and social concerns as it may introduce bias with regard to sensitive attributes such as race, gender, and location. Mitigating FL bias is thus a major research challenge. In this paper, we propose Astral, a novel bias mitigation system for FL. Astral provides a novel model aggregation approach to select the most effective aggregation weights to combine FL clients' models. It guarantees a predefined fairness objective by constraining bias below a given threshold while keeping model accuracy as high as possible. Astral handles the bias of single and multiple sensitive attributes and supports all bias metrics. Our comprehensive evaluation on seven real-world datasets with three popular bias metrics shows that Astral outperforms state-of-the-art FL bias mitigation techniques in terms of bias mitigation and model accuracy. Moreover, we show that Astral is robust against data heterogeneity and scalable in terms of data size and number of FL clients. Astral's code base is publicly available.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631455"}, {"primary_key": "949183", "vector": [], "sparse_vector": [], "title": "EarSE: Bringing Robust Speech Enhancement to COTS Headphones.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "T<PERSON>xing Li"], "summary": "Speech enhancement is regarded as the key to the quality of digital communication and is gaining increasing attention in the research field of audio processing. In this paper, we present EarSE, the first robust, hands-free, multi-modal speech enhancement solution using commercial off-the-shelf headphones. The key idea of EarSE is a novel hardware setting---leveraging the form factor of headphones equipped with a boom microphone to establish a stable acoustic sensing field across the user's face. Furthermore, we designed a sensing methodology based on Frequency-Modulated Continuous-Wave, which is an ultrasonic modality sensitive to capture subtle facial articulatory gestures of users when speaking. Moreover, we design a fully attention-based deep neural network to self-adaptively solve the user diversity problem by introducing the Vision Transformer network. We enhance the collaboration between the speech and ultrasonic modalities using a multi-head attention mechanism and a Factorized Bilinear Pooling gate. Extensive experiments demonstrate that EarSE achieves remarkable performance as increasing SiSDR by 14.61 dB and reducing the word error rate of user speech recognition by 22.45--66.41% in real-world application. EarSE not only outperforms seven baselines by 38.0% in SiSNR, 12.4% in STOI, and 20.5% in PESQ on average but also maintains practicality.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631447"}, {"primary_key": "949185", "vector": [], "sparse_vector": [], "title": "Navigating the Data Avalanche: Towards Supporting Developers in Developing Privacy-Friendly Children&apos;s Apps.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper critically examines the intersection of privacy concerns in children's apps and the support required by developers to effectively address these concerns. Third-party libraries and software development kits (SDKs) are widely used in mobile app development, however, these libraries are commonly known for posing significant data privacy risks to users. Recent research has shown that app developers for children are particularly struggling with the lack of support in navigating the complex market of third-party SDKs. The support needed for developers to build privacy-friendly apps is largely understudied. Motivated by the needs of developers and an empirical analysis of 137 'expert-approved' children's apps, we designed DataAvalanche.io, a web-based tool to support app developers in navigating the privacy and legal implications associated with common third-party SDKs on the market. Through semi-structured interviews with 12 app developers for children, we demonstrate that app developers largely perceive the transparency supported by our tool positively. However, they raised several barriers, including the challenges of adopting privacy-friendly alternatives and the struggle to safeguard their own legal interests when facing the imbalance of power in the app market. We contribute to our understanding of the open challenges and barriers faced by app developers in creating privacy-friendly apps for children and provide critical future design and policy directions.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596267"}, {"primary_key": "949186", "vector": [], "sparse_vector": [], "title": "PASTEL: Privacy-Preserving Federated Learning in Edge Computing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Federated Learning (FL) aims to improve machine learning privacy by allowing several data owners in edge and ubiquitous computing systems to collaboratively train a model, while preserving their local training data private, and sharing only model training parameters. However, FL systems remain vulnerable to privacy attacks, and in particular, to membership inference attacks that allow adversaries to determine whether a given data sample belongs to participants' training data, thus, raising a significant threat in sensitive ubiquitous computing systems. Indeed, membership inference attacks are based on a binary classifier that is able to differentiate between member data samples used to train a model and non-member data samples not used for training. In this context, several defense mechanisms, including differential privacy, have been proposed to counter such privacy attacks. However, the main drawback of these methods is that they may reduce model accuracy while incurring non-negligible computational costs. In this paper, we precisely address this problem with PASTEL, a FL privacy-preserving mechanism that is based on a novel multi-objective learning function. On the one hand, PASTEL decreases the generalization gap to reduce the difference between member data and non-member data, and on the other hand, PASTEL reduces model loss and leverages adaptive gradient descent optimization for preserving high model accuracy. Our experimental evaluations conducted on eight widely used datasets and five model architectures show that PASTEL significantly reduces membership inference attack success rates by up to -28%, reaching optimal privacy protection in most cases, with low to no perceptible impact on model accuracy.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3633808"}, {"primary_key": "949188", "vector": [], "sparse_vector": [], "title": "Investigating Passive Haptic Learning of Piano Songs Using Three Tactile Sensations of Vibration, Stroking and Tapping.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Passive Haptic Learning (PHL) is a method by which users are able to learn motor skills without paying active attention. In past research, vibration is widely applied in PHL as the signal delivered on the participant's skin. The human somatosensory system provides not only discriminative input (the perception of pressure, vibration, slip, and texture, etc.) to the brain but also an affective input (sliding, tapping and stroking, etc.). The former is often described as being mediated by low-threshold mechanosensitive (LTM) units with rapidly conducting large myelinated (Aᵬ) afferents, while the latter is mediated by a class of LTM afferents called C-tactile afferents (CTs). We investigated whether different tactile sensations (tapping, light stroking, and vibration) influence the learning effect of PHL in this work. We built three wearable systems corresponding to the three sensations respectively. 17 participants were invited to learn to play three different note sequences passively via three different systems. The subjects were then tested on their remembered note sequences after each learning session. Our results indicate that the sensations of tapping or stroking are as effective as the vibration system in passive haptic learning of piano songs, providing viable alternatives to the vibration sensations that have been used so far. We also found that participants on average made up to 1.06 errors less when using affective inputs, namely tapping or stroking. As the first work exploring the differences in multiple types of tactile sensations in PHL, we offer our design to the readers and hope they may employ our works for further research of PHL.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610899"}, {"primary_key": "949189", "vector": [], "sparse_vector": [], "title": "Fingerprinting IoT Devices Using Latent Physical Side-Channels.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The proliferation of low-end low-power internet-of-things (IoT) devices in \"smart\" environments necessitates secure identification and authentication of these devices via low-overhead fingerprinting methods. Previous work typically utilizes characteristics of the device's wireless modulation (WiFi, BLE, etc.) in the spectrum, or more recently, electromagnetic emanations from the device's DRAM to perform fingerprinting. The problem is that many devices, especially low-end IoT/embedded systems, may not have transmitter modules, DRAM, or other complex components, therefore making fingerprinting infeasible or challenging. To address this concern, we utilize electromagnetic emanations derived from the processor's clock to fingerprint. We present Digitus, an emanations-based fingerprinting system that can authenticate IoT devices at range. The advantage of <PERSON>gitus is that we can authenticate low-power IoT devices using features intrinsic to their normal operation without the need for additional transmitters and/or other complex components such as DRAM. Our experiments demonstrate that we achieve ≥ 95% accuracy on average, applicability in a wide range of IoT scenarios (range ≥ 5m, non-line-of-sight, etc.), as well as support for IoT applications such as finding hidden devices. Digitus represents a low-overhead solution for the authentication of low-end IoT devices.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596247"}, {"primary_key": "949190", "vector": [], "sparse_vector": [], "title": "Powered by AI: Examining How AI Descriptions Influence Perceptions of Fertility Tracking Applications.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Recently, there has been a proliferation of personal health applications describing to use Artificial Intelligence (AI) to assist health consumers in making health decisions based on their data and algorithmic outputs. However, it is still unclear how such descriptions influence individuals' perceptions of such apps and their recommendations. We therefore investigate how current AI descriptions influence individuals' attitudes towards algorithmic recommendations in fertility self-tracking through a simulated study using three versions of a fertility app. We found that participants preferred AI descriptions with explanation, which they perceived as more accurate and trustworthy. Nevertheless, they were unwilling to rely on these apps for high-stakes goals because of the potential consequences of a failure. We then discuss the importance of health goals for AI acceptance, how literacy and assumptions influence perceptions of AI descriptions and explanations, and the limitations of transparency in the context of algorithmic decision-making for personal health.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631414"}, {"primary_key": "949191", "vector": [], "sparse_vector": [], "title": "Real-time Context-Aware Multimodal Network for Activity and Activity-Stage Recognition from Team Communication in Dynamic Clinical Settings.", "authors": ["Chenyang Gao", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Gestrich-<PERSON>", "<PERSON>"], "summary": "In clinical settings, most automatic recognition systems use visual or sensory data to recognize activities. These systems cannot recognize activities that rely on verbal assessment, lack visual cues, or do not use medical devices. We examined speech-based activity and activity-stage recognition in a clinical domain, making the following contributions. (1) We collected a high-quality dataset representing common activities and activity stages during actual trauma resuscitation events-the initial evaluation and treatment of critically injured patients. (2) We introduced a novel multimodal network based on audio signal and a set of keywords that does not require a high-performing automatic speech recognition (ASR) engine. (3) We designed novel contextual modules to capture dynamic dependencies in team conversations about activities and stages during a complex workflow. (4) We introduced a data augmentation method, which simulates team communication by combining selected utterances and their audio clips, and showed that this method contributed to performance improvement in our data-limited scenario. In offline experiments, our proposed context-aware multimodal model achieved F1-scores of 73.2±0.8% and 78.1±1.1% for activity and activity-stage recognition, respectively. In online experiments, the performance declined about 10% for both recognition types when using utterance-level segmentation of the ASR output. The performance declined about 15% when we omitted the utterance-level segmentation. Our experiments showed the feasibility of speech-based activity and activity-stage recognition during dynamic clinical events.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580798"}, {"primary_key": "949192", "vector": [], "sparse_vector": [], "title": "MMTSA: Multi-Modal Temporal Segment Attention Network for Efficient Human Activity Recognition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shwetak N. Patel", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Multimodal sensors provide complementary information to develop accurate machine-learning methods for human activity recognition (HAR), but introduce significantly higher computational load, which reduces efficiency. This paper proposes an efficient multimodal neural architecture for HAR using an RGB camera and inertial measurement units (IMUs) called Multimodal Temporal Segment Attention Network (MMTSA). MMTSA first transforms IMU sensor data into a temporal and structure-preserving gray-scale image using the Gramian Angular Field (GAF), representing the inherent properties of human activities. MMTSA then applies a multimodal sparse sampling method to reduce data redundancy. Lastly, MMTSA adopts an inter-segment attention module for efficient multimodal fusion. Using three well-established public datasets, we evaluated MMTSA's effectiveness and efficiency in HAR. Results show that our method achieves superior performance improvements (11.13% of cross-subject F1-score on the MMAct dataset) than the previous state-of-the-art (SOTA) methods. The ablation study and analysis suggest that MMTSA's effectiveness in fusing multimodal data for accurate HAR. The efficiency evaluation on an edge device showed that MMTSA achieved significantly better accuracy, lower computational load, and lower inference latency than SOTA methods.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610872"}, {"primary_key": "949193", "vector": [], "sparse_vector": [], "title": "Deep Heterogeneous Contrastive Hyper-Graph Learning for In-the-Wild Context-Aware Human Activity Recognition.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Human Activity Recognition (HAR) is a challenging, multi-label classification problem as activities may co-occur and sensor signals corresponding to the same activity may vary in different contexts (e.g., different device placements). This paper proposes a Deep Heterogeneous Contrastive Hyper-Graph Learning (DHC-HGL) framework that captures heterogenous Context-Aware HAR (CA-HAR) hypergraph properties in a message-passing and neighborhood-aggregation fashion. Prior work only explored homogeneous or shallow-node-heterogeneous graphs. DHC-HGL handles heterogeneous CA-HAR data by innovatively 1) Constructing three different types of sub-hypergraphs that are each passed through different custom HyperGraph Convolution (HGC) layers designed to handle edge-heterogeneity and 2) Adopting a contrastive loss function to ensure node-heterogeneity. In rigorous evaluation on two CA-HAR datasets, DHC-HGL significantly outperformed state-of-the-art baselines by 5.8% to 16.7% on Matthews Correlation Coefficient (MCC) and 3.0% to 8.4% on Macro F1 scores. UMAP visualizations of learned CA-HAR node embeddings are also presented to enhance model explainability.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631444"}, {"primary_key": "949195", "vector": [], "sparse_vector": [], "title": "LocCams: An Efficient and Robust Approach for Detecting and Localizing Hidden Wireless Cameras via Commodity Devices.", "authors": ["Yangyang Gu", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Zim<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Unlawful wireless cameras are often hidden to secretly monitor private activities. However, existing methods to detect and localize these cameras are interactively complex or require expensive specialized hardware. In this paper, we present LocCams, an efficient and robust approach for hidden camera detection and localization using only a commodity device (e.g., a smartphone). By analyzing data packets in the wireless local area network, LocCams passively detects hidden cameras based on the packet transmission rate. Camera localization is achieved by identifying whether the physical channel between our detector and the hidden camera is a Line-of-Sight (LOS) propagation path based on the distribution of channel state information subcarriers, and utilizing a feature extraction approach based on a Convolutional Neural Network (CNN) model for reliable localization. Our extensive experiments, involving various subjects, cameras, distances, user positions, and room configurations, demonstrate LocCams' effectiveness. Additionally, to evaluate the performance of the method in real life, we use subjects, cameras, and rooms that do not appear in the training set to evaluate the transferability of the model. With an overall accuracy of 95.12% within 30 seconds of detection, LocCams provides robust detection and localization of hidden cameras.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631432"}, {"primary_key": "949197", "vector": [], "sparse_vector": [], "title": "ForceSticker: Wireless, Batteryless, Thin &amp; Flexible Force Sensors.", "authors": ["<PERSON><PERSON>", "Daegue Park", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Siddhi Mundhra", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Any two objects in contact with each other exert a force that could be simply due to gravity or mechanical contact, such as any ubiquitous object exerting weight on a platform or the contact between two bones at our knee joints. The most ideal way of capturing these contact forces is to have a flexible force sensor which can conform well to the contact surface. Further, the sensor should be thin enough to not affect the contact physics between the two objects. In this paper, we showcase the design of such thin, flexible sticker-like force sensors dubbed as 'ForceStickers', ushering into a new era of miniaturized force sensors. ForceSticker achieves this miniaturization by creating new class of capacitive force sensors which avoid both batteries, as well as wires. The wireless and batteryless readout is enabled via hybrid analog-digital backscatter, by piggybacking analog sensor data onto a digitally identified RFID link. Hence, ForceSticker finds natural applications in space and battery-constraint in-vivo usecases, like force-sensor backed orthopaedic implants, surgical robots. Further, ForceSticker finds applications in ubiquiti-constraint scenarios. For example, these force-stickers enable cheap, digitally readable barcodes that can provide weight information, with possible usecases in warehouse integrity checks. To meet these varied application scenarios, we showcase the general framework behind design of ForceSticker. With ForceSticker framework, we design 4mm*2mm sensor prototypes, with two different polymer layers of ecoflex and neoprene rubber, having force ranges of 0-6N and 0-40N respectively, with readout errors of 0.25, 1.6 N error each (&lt;5% of max. force). Further, we stress test ForceSticker by &gt;10,000 force applications without significant error degradation. We also showcase two case-studies onto the possible applications of ForceSticker: sensing forces from a toy knee-joint model and integrity checks of warehouse packaging.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580793"}, {"primary_key": "949198", "vector": [], "sparse_vector": [], "title": "Exploring Smart Standing Desks to Foster a Healthier Workplace.", "authors": ["<PERSON>", "Saba Kheirineja<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Sedentary behavior is endemic in modern workplaces, contributing to negative physical and mental health outcomes. Although adjustable standing desks are increasing in popularity, people still avoid standing. We developed an open-source plug-and-play system to remotely control standing desks and investigated three system modes with a three-week in-the-wild user study (N=15). Interval mode forces users to stand once per hour, causing frustration. Adaptive mode nudges users to stand every hour unless the user has stood already. Smart mode, which raises the desk during breaks, was the best rated, contributing to increased standing time with the most positive qualitative feedback. However, non-computer activities need to be accounted for in the future. Therefore, our results indicate that a smart standing desk that shifts modes at opportune times has the most potential to reduce sedentary behavior in the workplace. We contribute our open-source system and insights for future intelligent workplace well-being systems.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596260"}, {"primary_key": "949199", "vector": [], "sparse_vector": [], "title": "Feeling the Temperature of the Room: Unobtrusive Thermal Display of Engagement during Group Communication.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Thermal signals have been explored in HCI for emotion-elicitation and enhancing two-person communication, showing that temperature invokes social and emotional signals in individuals. Yet, extending these findings to group communication is missing. We investigated how thermal signals can be used to communicate group affective states in a hybrid meeting scenario to help people feel connected over a distance. We conducted a lab study (N=20 participants) and explored wrist-worn thermal feedback to communicate audience emotions. Our results show that thermal feedback is an effective method of conveying audience engagement without increasing workload and can help a presenter feel more in tune with the audience. We outline design implications for real-world wearable social thermal feedback systems for both virtual and in-person communication that support group affect communication and social connectedness. Thermal feedback has the potential to connect people across distances and facilitate more effective and dynamic communication in multiple contexts.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580820"}, {"primary_key": "949200", "vector": [], "sparse_vector": [], "title": "TrackPose: Towards Stable and User Adaptive Finger Pose Estimation on Capacitive Touchscreens.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jianjiang Feng", "<PERSON><PERSON>"], "summary": "Several studies have explored the estimation of finger pose/angle to enhance the expressiveness of touchscreens. However, the accuracy of previous algorithms is limited by large estimation errors, and the sequential output angles are unstable, making it difficult to meet the demands of practical applications. We believe the defect arises from improper rotation representation, the lack of time-series modeling, and the difficulty in accommodating individual differences among users. To address these issues, we conduct in-depth study of rotation representation for the 2D pose problem by minimizing the errors between representation space and original space. A deep learning model, TrackPose, using a self-attention mechanism is proposed for time-series modeling to improve accuracy and stability of finger pose. A registration application on a mobile phone is developed to collect touchscreen images of each new user without the use of optical tracking device. The combination of the three measures mentioned above has resulted in a 33% reduction in the angle estimation error, 47% for the yaw angle especially. Additionally, the instability of sequential estimations, measured by the proposed metric MAEΔ, is reduced by 62%. User study further confirms the effectiveness of our proposed algorithm.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631459"}, {"primary_key": "949201", "vector": [], "sparse_vector": [], "title": "BabyNutri: A Cost-Effective Baby Food Macronutrients Analyzer Based on Spectral Reconstruction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The physical and physiological development of infants and toddlers requires the proper amount of macronutrient intake, making it an essential problem to estimate the macronutrient in baby food. Nevertheless, existing solutions are either too expensive or poor performing, preventing the widespread use of automatic baby nutrient intake logging. To narrow this gap, this paper proposes a cost-effective and portable baby food macronutrient estimation system, BabyNutri. BabyNutri exploits a novel spectral reconstruction algorithm to reconstruct high-dimensional informative spectra from low-dimensional spectra, which are available from low-cost spectrometers. We propose a denoising autoencoder for the reconstruction process, by which <PERSON><PERSON>ut<PERSON> can reconstruct a 160-dimensional spectrum from a 5-dimensional spectrum. Since the high-dimensional spectrum is rich in light absorption features of macronutrients, it can achieve more accurate macronutrient estimation. In addition, considering that baby food contains complex ingredients, we also design a CNN nutrition estimation model with good generalization performance over various types of baby food. Our extensive experiments over 88 types of baby food show that the spectral reconstruction error of BabyNutri is only 5.91%, reducing 33% than the state-of-the-art baseline with the same time complexity. In addition, the nutrient estimation performance of BabyNutri not only obviously outperforms state-of-the-art and cost-effective solutions but also is highly correlated with the professional spectrometer, with the correlation coefficients of 0.81, 0.88, 0.82 for protein, fat, and carbohydrate, respectively. However the price of our system is only one percent of the commercial solution. We also validate that BabyNutri is robust regarding various factors, e.g., ambient light, food volume, and even unseen baby food samples.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580858"}, {"primary_key": "949202", "vector": [], "sparse_vector": [], "title": "Iris: Passive Visible Light Positioning Using Light Spectral Information.", "authors": ["Ji<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose a novel Visible Light Positioning (VLP) method, called Iris, that leverages light spectral information (LSI) to localize individuals in a completely passive manner. This means that the user does not need to carry any device, and the existing lighting infrastructure remains unchanged. Our method uses a background subtraction approach to accurately detect changes in ambient LSI caused by human movement. Furthermore, we design a Convolutional Neural Network (CNN) capable of learning and predicting user locations from the LSI change data. To validate our approach, we implemented a prototype of Iris using a commercial-off-the-shelf light spectral sensor and conducted experiments in two typical real-world indoor environments: a 25 m2 one-bedroom apartment and a 13.3m × 8.4m office space. Our results demonstrate that Iris performs effectively in both artificial lighting at night and in highly dynamic natural lighting conditions during the day. Moreover, Iris outperforms the state-of-the-art passive VLP techniques significantly in terms of localization accuracy and the required density of light sensors. To reduce the overhead associated with multi-channel spectral sensing, we develop and validate an algorithm that can minimize the required number of spectral channels for a given environment. Finally, we propose a conditional Generative Adversarial Network (cGAN) that can artificially generate LSI and reduce data collection effort by 50% without sacrificing localization accuracy.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610913"}, {"primary_key": "949203", "vector": [], "sparse_vector": [], "title": "MicroCam: Leveraging Smartphone Microscope Camera for Context-Aware Contact Surface Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The primary focus of this research is the discreet and subtle everyday contact interactions between mobile phones and their surrounding surfaces. Such interactions are anticipated to facilitate mobile context awareness, encompassing aspects such as dispensing medication updates, intelligently switching modes (e.g., silent mode), or initiating commands (e.g., deactivating an alarm). We introduce MicroCam, a contact-based sensing system that employs smartphone IMU data to detect the routine state of phone placement and utilizes a built-in microscope camera to capture intricate surface details. In particular, a natural dataset is collected to acquire authentic surface textures in situ for training and testing. Moreover, we optimize the deep neural network component of the algorithm, based on continual learning, to accurately discriminate between object categories (e.g., tables) and material constituents (e.g., wood). Experimental results highlight the superior accuracy, robustness and generalization of the proposed method. Lastly, we conducted a comprehensive discussion centered on our prototype, encompassing topics such as system performance and potential applications and scenarios.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610921"}, {"primary_key": "949204", "vector": [], "sparse_vector": [], "title": "NF-Heart: A Near-field Non-contact Continuous User Authentication System via Ballistocardiogram.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The increasingly remote workforce resulting from the global coronavirus pandemic has caused unprecedented cybersecurity concerns to organizations. Considerable evidence has shown that one-pass authentication fails to meet security needs when the workforce work from home. The recent advent of continuous authentication (CA) has shown the potential to solve this predicament. In this paper, we propose NF-Heart, a physiological-based CA system utilizing a ballistocardiogram (BCG). The key insight is that the BCG measures the body's micro-movements produced by the recoil force of the body in reaction to the cardiac ejection of blood, and we can infer cardiac biometrics from BCG signals. To measure BCG, we deploy a lightweight accelerometer on an office chair, turning the common chair into a smart continuous identity \"scanner\". We design multiple stages of signal processing to decompose and transform the distorted BCG signals so that the effects of motion artifacts and dynamic variations are eliminated. User-specific fiducial features are then extracted from the processed BCG signals for authentication. We conduct comprehensive experiments on 105 subjects in terms of verification accuracy, security, robustness, and long-term availability. The results demonstrate that NF-Heart achieves a mean balanced accuracy of 96.45% and a median equal error rate of 3.83% for CA. The proposed signal processing pipeline is effective in addressing various practical disturbances.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580851"}, {"primary_key": "949205", "vector": [], "sparse_vector": [], "title": "SurfShare: Lightweight Spatially Consistent Physical Surface and Virtual Replica Sharing with Head-mounted Mixed-Reality.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Shared Mixed Reality experiences allow two co-located users to collaborate on both physical and digital tasks with familiar social protocols. However, extending the same to remote collaboration is limited by cumbersome setups for aligning distinct physical environments and the lack of access to remote physical artifacts. We present SurfShare, a general-purpose symmetric remote collaboration system with mixed-reality head-mounted displays (HMDs). Our system shares a spatially consistent physical-virtual workspace between two remote users, anchored on a physical plane in each environment (e.g., a desk or wall). The video feed of each user's physical surface is overlaid virtually on the other side, creating a shared view of the physical space. We integrate the physical and virtual workspace through virtual replication. Users can transmute physical objects to the virtual space as virtual replicas. Our system is lightweight, implemented using only the capabilities of the headset, without requiring any modifications to the environment (e.g. cameras or motion tracking hardware). We discuss the design, implementation, and interaction capabilities of our prototype, and demonstrate the utility of SurfShare through four example applications. In a user experiment with a comprehensive prototyping task, we found that SurfShare provides a physical-virtual workspace that supports low-fi prototyping with flexible proxemics and fluid collaboration dynamics.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631418"}, {"primary_key": "949207", "vector": [], "sparse_vector": [], "title": "On the Long-Term Effects of Continuous Keystroke Authentication: Keeping User Frustration Low through Behavior Adaptation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Younghan <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "One of the main challenges in deploying a keystroke dynamics-based continuous authentication scheme on smartphones is ensuring low error rates over time. Unstable false rejection rates (FRRs) would lead to frequent phone locks during long-term use, and deteriorating attack detection rates would jeopardize its security benefits. The fact that it is undesirable to train complex deep learning models directly on smartphones or send private sensor data to servers for training present unique deployment constraints, requiring on-device solutions that can be trained fully on smartphones. To improve authentication accuracy while satisfying such real-world deployment constraints, we propose two novel feature engineering techniques: (1) computation of pair-wise correlations between accelerometer and gyroscope sensor values, and (2) on-device feature extraction technique to compute dynamic time warping (DTW) distance measurements between autoencoder inputs and outputs via transfer-learning. Using those two feature sets in an ensemble blender, we achieved 6.4 percent equal error rate (EER) in a public dataset. In comparison, blending two state-of-the-art solutions achieved 14.1 percent EER in the same test settings. Our real-world dataset evaluation showed increasing FRRs (user frustration) over two months; however, through periodic model retraining, we were able to maintain average FRRs around 2.5 percent while keeping attack detection rates around 89 percent. The proposed solution has been deployed in the latest Samsung Galaxy smartphone series to protect secure workspace through continuous authentication.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596236"}, {"primary_key": "949208", "vector": [], "sparse_vector": [], "title": "SpaceX Mag: An Automatic, Scalable, and Rapid Space Compactor for Optimizing Smartphone App Interfaces for Low-Vision Users.", "authors": ["<PERSON><PERSON><PERSON> <PERSON><PERSON>", "<PERSON>"], "summary": "Low-vision users interact with smartphones via screen magnifiers, which uniformly magnify raw screen pixels, including whitespace and user interface (UI) elements. Screen magnifiers thus occlude important contextual information, such as visual cues, from the user's viewport. This requires low-vision users to pan over the occluded portions and mentally reconstruct the context, which is cumbersome, tiring, and mentally demanding. Prior work aimed to address these usability issues with screen magnifiers by optimizing the representation of UI elements suitable for low-vision users or by magnifying whitespace and non-whitespace content (e.g., text, graphics, borders) differently. This paper combines both techniques and presents SpaceXMag, an optimization framework that automatically reduces whitespace within a smartphone app, thereby packing more information within the current magnification viewport. A study with 11 low-vision users indicates that, with a traditional screen magnifier, the space-optimized UI is more usable and saves at least 28.13% time for overview tasks and 42.89% time for target acquisition tasks, compared to the original, unoptimized UI of the same app. Furthermore, our framework is scalable, fast, and automatable. For example, on a public dataset containing 16, 566 screenshots of different Android apps, it saves approximately 47.17% of the space (area) on average, with a mean runtime of around 1.44 seconds, without requiring any human input. All are indicative of the promise and potential of SpaceXMag for low-vision screen magnifier users.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596253"}, {"primary_key": "949210", "vector": [], "sparse_vector": [], "title": "X-CHAR: A Concept-based Explainable Complex Human Activity Recognition Model.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "End-to-end deep learning models are increasingly applied to safety-critical human activity recognition (HAR) applications, e.g., healthcare monitoring and smart home control, to reduce developer burden and increase the performance and robustness of prediction models. However, integrating HAR models in safety-critical applications requires trust, and recent approaches have aimed to balance the performance of deep learning models with explainable decision-making for complex activity recognition. Prior works have exploited the compositionality of complex HAR (i.e., higher-level activities composed of lower-level activities) to form models with symbolic interfaces, such as concept-bottleneck architectures, that facilitate inherently interpretable models. However, feature engineering for symbolic concepts-as well as the relationship between the concepts-requires precise annotation of lower-level activities by domain experts, usually with fixed time windows, all of which induce a heavy and error-prone workload on the domain expert. In this paper, we introduce X-CHAR, an eXplainable Complex Human Activity Recognition model that doesn't require precise annotation of low-level activities, offers explanations in the form of human-understandable, high-level concepts, while maintaining the robust performance of end-to-end deep learning models for time series data. X-CHAR learns to model complex activity recognition in the form of a sequence of concepts. For each classification, X-CHAR outputs a sequence of concepts and a counterfactual example as the explanation. We show that the sequence information of the concepts can be modeled using Connectionist Temporal Classification (CTC) loss without having accurate start and end times of low-level annotations in the training dataset-significantly reducing developer burden. We evaluate our model on several complex activity datasets and demonstrate that our model offers explanations without compromising the prediction accuracy in comparison to baseline models. Finally, we conducted a mechanical Turk study to show that the explanations provided by our model are more understandable than the explanations from existing methods for complex activity recognition.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580804"}, {"primary_key": "949211", "vector": [], "sparse_vector": [], "title": "A Data-Driven Context-Aware Health Inference System for Children during School Closures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Running <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Many countries have implemented school closures due to the outbreak of the COVID-19 pandemic, which has inevitably affected children's physical and mental health. It is vital for parents to pay special attention to their children's health status during school closures. However, it is difficult for parents to recognize the changes in their children's health, especially without visible symptoms, such as psychosocial functioning in mental health. Moreover, healthcare resources and understanding of the health and societal impact of COVID-19 are quite limited during the pandemic. Against this background, we collected real-world datasets from 1,172 children in Hong Kong during four time periods under different pandemic and school closure conditions from September 2019 to January 2022. Based on these data, we first perform exploratory data analysis to explore the impact of school closures on six health indicators, including physical activity intensity, physical functioning, self-rated health, psychosocial functioning, resilience, and connectedness. We further study the correlation between children's contextual characteristics (i.e., demographics, socioeconomic status, electronic device usage patterns, financial satisfaction, academic performance, sleep pattern, exercise habits, and dietary patterns) and the six health indicators. Subsequently, a health inference system is designed and developed to infer children's health status based on their contextual features to derive the risk factors of the six health indicators. The evaluation and case studies on real-world datasets show that this health inference system can help parents and authorities better understand key factors correlated with children's health status during school closures.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580800"}, {"primary_key": "949212", "vector": [], "sparse_vector": [], "title": "Fast Radio Map Construction with Domain Disentangled Learning for Wireless Localization.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The accuracy of wireless fingerprint-based indoor localization largely depends on the precision and density of radio maps. Although many research efforts have been devoted to incremental updating of radio maps, few consider the laborious initial construction of a new site. In this work, we propose an accurate and generalizable framework for efficient radio map construction, which takes advantage of readily-available fine-grained radio maps and constructs fine-grained radio maps of a new site with a small proportion of measurements in it. Specifically, we regard radio maps as domains and propose a Radio Map construction approach based on Domain Adaptation (RMDA). We first employ the domain disentanglement feature extractor to learn domain-invariant features for aligning the source domains (available radio maps) with the target domain (initial radio map) in the domain-invariant latent space. Furthermore, we propose a dynamic weighting strategy, which learns the relevancy of the source and target domain in the domain adaptation. Then, we extract the domain-specific features based on the site's floorplan and use them to constrain the super-resolution of the domain-invariant features. Experimental results demonstrate that RMDA constructs a fine-grained initial radio map of a target site efficiently with a limited number of measurements. Meanwhile, the localization accuracy of the refined radio map with RMDA significantly improved by about 41.35% after construction and is comparable with the dense surveyed radio map (the reduction is less than 8%).", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610922"}, {"primary_key": "949215", "vector": [], "sparse_vector": [], "title": "A Meta-Synthesis of the Barriers and Facilitators for Personal Informatics Systems.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Personal informatics (PI) systems are designed for diverse users in the real world. Even when these systems are usable, people encounter barriers while engaging with them in ways designers cannot anticipate, which impacts the system's effectiveness. Although PI literature extensively reports such barriers, the volume of this information can be overwhelming. Researchers and practitioners often find themselves repeatedly addressing the same challenges since sifting through this enormous volume of knowledge looking for relevant insights is often infeasible. We contribute to alleviating this issue by conducting a meta-synthesis of the PI literature and categorizing people's barriers and facilitators to engagement with PI systems into eight themes. Based on the synthesized knowledge, we discuss specific generalizable barriers and paths for further investigations. This synthesis can serve as an index to identify barriers pertinent to each application domain and possibly to identify barriers from one domain that might apply to a different domain. Finally, to ensure the sustainability of the syntheses, we propose a Design Statements (DS) block for research articles.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610893"}, {"primary_key": "949218", "vector": [], "sparse_vector": [], "title": "SF-Adapter: Computational-Efficient Source-Free Domain Adaptation for Human Activity Recognition.", "authors": ["<PERSON><PERSON>", "Qingyong Hu", "<PERSON><PERSON>"], "summary": "Wearable sensor-based human activity recognition (HAR) has gained significant attention due to the widespread use of smart wearable devices. However, variations in different subjects can cause a domain shift that impedes the scaling of the recognition model. Unsupervised domain adaptation has been proposed as a solution to recognize activities in new, unlabeled target domains by training the source and target data together. However, the need for accessing source data raises privacy concerns. Source-free domain adaptation has emerged as a practical setting, where only a pre-trained source model is provided for the unlabeled target domain. This setup aligns with the need for personalized activity model adaptation on target local devices. As the edge devices are resource-constrained with limited memory, it is crucial to take the computational efficiency, i.e., memory cost into consideration. In this paper, we develop a source-free domain adaptation framework for wearable sensor-based HAR, with a focus on computational efficiency for target edge devices. Firstly, we design a lightweight add-on module called adapter to adapt the frozen pre-trained model to the unlabeled target domain. Secondly, to optimize the adapter, we adopt a simple yet effective model adaptation method that leverages local representation similarity and prediction consistency. Additionally, we design a set of sample selection optimization strategies to select samples effective for adaptation and further enhance computational efficiency while maintaining adaptation performance. Our extensive experiments on three datasets demonstrate that our method achieves comparable recognition accuracy to the state-of-the-art source free domain adaptation methods with fewer than 1% of the parameters updated and saves up to 4.99X memory cost.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631428"}, {"primary_key": "949221", "vector": [], "sparse_vector": [], "title": "What and When to Explain?: On-road Evaluation of Explanations in Highly Automated Vehicles.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Explanations in automated vehicles help passengers understand the vehicle's state and capabilities, leading to increased trust in the technology. Specifically, for passengers of SAE Level 4 and 5 vehicles who are not engaged in the driving process, the enhanced sense of control provided by explanations reduces potential anxieties, enabling them to fully leverage the benefits of automation. To construct explanations that enhance trust and situational awareness without disturbing passengers, we suggest testing with people who ultimately employ such explanations, ideally under real-world driving conditions. In this study, we examined the impact of various visual explanation types (perception, attention, perception+attention) and timing mechanisms (constantly provided or only under risky scenarios) on passenger experience under naturalistic driving scenarios using actual vehicles with mixed-reality support. Our findings indicate that visualizing the vehicle's perception state improves the perceived usability, trust, safety, and situational awareness without adding cognitive burden, even without explaining the underlying causes. We also demonstrate that the traffic risk probability could be used to control the timing of an explanation delivery, particularly when passengers are overwhelmed with information. Our study's on-road evaluation method offers a safe and reliable testing environment and can be easily customized for other AI models and explanation modalities.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610886"}, {"primary_key": "949222", "vector": [], "sparse_vector": [], "title": "SkinLink: On-body Construction and Prototyping of Reconfigurable Epidermal Interfaces.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Applying customized epidermal electronics closely onto the human skin offers the potential for biometric sensing and unique, always-available on-skin interactions. However, iterating designs of an on-skin interface from schematics to physical circuit wiring can be time-consuming, even with tiny modifications; it is also challenging to preserve skin wearability after repeated alteration. We present SkinLink, a reconfigurable on-skin fabrication approach that allows users to intuitively explore and experiment with the circuitry adjustment on the body. We demonstrate SkinLink with a customized on-skin prototyping toolkit comprising tiny distributed circuit modules and a variety of streamlined trace modules that adapt to diverse body surfaces. To evaluate SkinLink's performance, we conducted a 14-participant usability study to compare and contrast the workflows with a benchmark on-skin construction toolkit. Four case studies targeting a film makeup artist, two beauty makeup artists, and a wearable computing designer further demonstrate different application scenarios and usages.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596241"}, {"primary_key": "949224", "vector": [], "sparse_vector": [], "title": "CircuitGIue: A Software Configurable Converter for Interconnecting Multiple Heterogeneous Electronic Components.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present CircuitGlue, an electronic converter board that allows heterogeneous electronic components to be readily interconnected. Electronic components are plugged into an eight-pin programmable header on the board, and the assignment of each pin in the header is configured in software. CircuitGlue supports a variety of connections, including power, ground, analog signals, and various digital protocols at different voltages. As such, off-the-shelf electronic components and modules are instantly compatible no matter what voltage levels, interface types, communication protocols, and pinouts they use. In this paper, we demonstrate the use of CircuitGlue to ease and expedite prototyping with electronics and we explore new opportunities enabled by CircuitGlue. Finally, we reflect on the results of a preliminary user study evaluating the usability of CircuitGlue for people new to electronics.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596265"}, {"primary_key": "949225", "vector": [], "sparse_vector": [], "title": "LAUREATE: A Dataset for Supporting Research in Affective Computing and Human Memory Augmentation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The latest developments in wearable sensors have resulted in a wide range of devices available to consumers, allowing users to monitor and improve their physical activity, sleep patterns, cognitive load, and stress levels. However, the lack of out-of-the-lab labelled data hinders the development of advanced machine learning models for predicting affective states. Furthermore, to the best of our knowledge, there are no publicly available datasets in the area of Human Memory Augmentation. This paper presents a dataset we collected during a 13-week study in a university setting. The dataset, named LAUREATE, contains the physiological data of 42 students during 26 classes (including exams), daily self-reports asking the students about their lifestyle habits (e.g. studying hours, physical activity, and sleep quality) and their performance across multiple examinations. In addition to the raw data, we provide expert features from the physiological data, and baseline machine learning models for estimating self-reported affect, models for recognising classes vs breaks, and models for user identification. Besides the use cases presented in this paper, among which Human Memory Augmentation, the dataset represents a rich resource for the UbiComp community in various domains, including affect recognition, behaviour modelling, user privacy, and activity and context recognition.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610892"}, {"primary_key": "949226", "vector": [], "sparse_vector": [], "title": "SweatSkin: Rapidly Prototyping Sweat-Sensing On-Skin Interface Based on Microfluidics.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Sweat sensing affords monitoring essential bio-signals tailored for various well-being inspections. We present SweatSkin, the fabrication approach for customizable sweat-sensing on-skin interfaces. SweatSkin is unique in exploiting on-skin microfluidic channels to access bio-fluid secretes within the skin for personalized health monitoring. To lower the barrier to creating skin-conformable microfluidics capable of collecting and analyzing sweat, four fabrication methods utilizing accessible materials are proposed. Technical characterizations of paper- and polymer-based devices indicate that colorimetric analysis can effectively visualize sweat loss, chloride, glucose, and pH values. To support general to extreme sweating scenarios, we consulted five athletic experts on the SweatSkin devices' customization guidelines, application potential, and envisioned usages. The two-session fabrication workshop study with ten participants verified that the four fabrication methods are easy to learn and easy to make. Overall, SweatSkin is an extensible and user-friendly platform for designing and creating customizable on-skin sweat-sensing interfaces for UbiComp and HCI, affording ubiquitous personalized health sensing.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631425"}, {"primary_key": "949227", "vector": [], "sparse_vector": [], "title": "Exploring the Opportunities of AR for Enriching Storytelling with Family Photos between <PERSON><PERSON><PERSON> and Grandchildren.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Fan"], "summary": "Storytelling with family photos, as an important mode of reminiscence-based activities, can be instrumental in promoting intergenerational communication between grandparents and grandchildren by strengthening generation bonds and shared family values. Motivated by challenges that existing technology approaches encountered for improving intergenerational storytelling (e.g., the need to hold the tablet, the potential view detachment from the physical world in Virtual Reality (VR)), we sought to find new ways of using Augmented Reality (AR) to support intergenerational storytelling, which offers new capabilities (e.g., 3D models, new interactivity) to enhance the expression for the storyteller. We conducted a two-part exploratory study, where pairs of grandparents and grandchildren 1) participated in an in-person storytelling activity with a semi-structured interview 2) and then a participatory design session with AR technology probes that we designed to inspire their exploration. Our findings revealed insights into the possible ways of intergenerational storytelling, the feasibility and usages of AR in facilitating it, and the key design implications for leveraging AR in intergenerational storytelling.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610903"}, {"primary_key": "949228", "vector": [], "sparse_vector": [], "title": "SignRing: Continuous American Sign Language Recognition Using IMU Rings and Virtual IMU Data.", "authors": ["<PERSON><PERSON> Li", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Yincheng Jin", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Junsong Yuan", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Sign language is a natural language widely used by Deaf and hard of hearing (DHH) individuals. Advanced wearables are developed to recognize sign language automatically. However, they are limited by the lack of labeled data, which leads to a small vocabulary and unsatisfactory performance even though laborious efforts are put into data collection. Here we propose SignRing, an IMU-based system that breaks through the traditional data augmentation method, makes use of online videos to generate the virtual IMU (v-IMU) data, and pushes the boundary of wearable-based systems by reaching the vocabulary size of 934 with sentences up to 16 glosses. The v-IMU data is generated by reconstructing 3D hand movements from two-view videos and calculating 3-axis acceleration data, by which we are able to achieve a word error rate (WER) of 6.3% with a mix of half v-IMU and half IMU training data (2339 samples for each), and a WER of 14.7% with 100% v-IMU training data (6048 samples), compared with the baseline performance of the 8.3% WER (trained with 2339 samples of IMU data). We have conducted comparisons between v-IMU and IMU data to demonstrate the reliability and generalizability of the v-IMU data. This interdisciplinary work covers various areas such as wearable sensor development, computer vision techniques, deep learning, and linguistics, which can provide valuable insights to researchers with similar research objectives.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610881"}, {"primary_key": "949229", "vector": [], "sparse_vector": [], "title": "Hierarchical Clustering-based Personalized Federated Learning for Robust and Fair Human Activity Recognition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Lingling An"], "summary": "Currently, federated learning (FL) can enable users to collaboratively train a global model while protecting the privacy of user data, which has been applied to human activity recognition (HAR) tasks. However, in real HAR scenarios, deploying an FL system needs to consider multiple aspects, including system accuracy, fairness, robustness, and scalability. Most existing FL frameworks aim to solve specific problems while ignoring other properties. In this paper, we propose FedCHAR, a personalized FL framework with a hierarchical clustering method for robust and fair HAR, which not only improves the accuracy and the fairness of model performance by exploiting the intrinsically similar relationship between users but also enhances the robustness of the system by identifying malicious nodes through clustering in attack scenarios. In addition, to enhance the scalability of FedCHAR, we also propose FedCHAR-DC, a scalable and adaptive FL framework which is featured by dynamic clustering and adapting to the addition of new users or the evolution of datasets for realistic FL-based HAR scenarios. We conduct extensive experiments to evaluate the performance of FedCHAR on seven datasets of different sizes. The results demonstrate that FedCHAR could obtain better performance on different datasets than the other five state-of-the-art methods in terms of accuracy, robustness, and fairness. We further validate that FedCHAR-DC exhibits satisfactory scalability on three large-scale datasets regardless of the number of participants.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580795"}, {"primary_key": "949230", "vector": [], "sparse_vector": [], "title": "Automated Face-To-Face Conversation Detection on a Commodity Smartwatch with Acoustic Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>az"], "summary": "Understanding social interactions is relevant across many domains and applications, including psychology, behavioral sciences, human computer interaction, and healthcare. In this paper, we present a practical approach for automatically detecting face-to-face conversations by leveraging the acoustic sensing capabilities of an off-the-shelf, unmodified smartwatch. Our proposed framework incorporates feature representations extracted from different neural network setups and shows the benefits of feature fusion. The framework does not require an acoustic model specifically trained to the speech of the individual wearing the watch or of those nearby. We evaluate our framework with 39 participants in 18 homes in a semi-naturalistic study and with four participants in free living, obtaining an F1 score of 83.2% and 83.3% respectively for detecting user's conversations with the watch. Additionally, we study the real-time capability of our framework by deploying a system on an actual smartwatch and discuss several strategies to improve its practicality in real life. To support further work in this area by the research community, we also release our annotated dataset of conversations.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610882"}, {"primary_key": "949231", "vector": [], "sparse_vector": [], "title": "mmStress: Distilling Human Stress from Daily Activities via Contact-less Millimeter-wave Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Huadong Ma", "<PERSON><PERSON>"], "summary": "Long-term exposure to stress hurts human's mental and even physical health,and stress monitoring is of increasing significance in the prevention, diagnosis, and management of mental illness and chronic disease. However, current stress monitoring methods are either burdensome or intrusive, which hinders their widespread usage in practice. In this paper, we propose mmStress, a contact-less and non-intrusive solution, which adopts a millimeter-wave radar to sense a subject's activities of daily living, from which it distills human stress. mmStress is built upon the psychologically-validated relationship between human stress and \"displacement activities\", i.e., subjects under stress unconsciously perform fidgeting behaviors like scratching, wandering around, tapping foot, etc. Despite the conceptual simplicity, to realize mmStress, the key challenge lies in how to identify and quantify the latent displacement activities autonomously, as they are usually transitory and submerged in normal daily activities, and also exhibit high variation across different subjects. To address these challenges, we custom-design a neural network that learns human activities from both macro and micro timescales and exploits the continuity of human activities to extract features of abnormal displacement activities accurately. Moreover, we also address the unbalance stress distribution issue by incorporating a post-hoc logit adjustment procedure during model training. We prototype, deploy and evaluate mmStress in ten volunteers' apartments for over four weeks, and the results show that mmStress achieves a promising accuracy of ~80% in classifying low, medium and high stress. In particular, mmStress manifests advantages, particularly under free human movement scenarios, which advances the state-of-the-art that focuses on stress monitoring in quasi-static scenarios.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610926"}, {"primary_key": "949232", "vector": [], "sparse_vector": [], "title": "Towards a Dynamic Fresnel Zone Model to WiFi-based Human Activity Recognition.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON> Gu", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Zhang"], "summary": "The passive WiFi sensing research has largely centered on activity sensing using fixed-location WiFi transceivers, leading to the development of several theoretical models that aim to map received WiFi signals to human activity. Of these models, the Fresnel zone model has shown to be particularly noteworthy. However, the growing popularity of mobile WiFi receivers has not been matched by corresponding research on mobile receiver-based theoretical models. This paper fills this gap by presenting the first theoretical model to quantify the impact of moving a moving receiver for WiFi sensing. We propose a novel dynamic Fresnel zone model in the free space of an indoor environment, which takes the form of a cluster of concentric hyperbolas centered on the transmitter and reflection subject. We examine three properties of this model, i.e., relating the variation in RF signals received by the receiver to the position and orientation of the human, the movement of the receiver, and the presence of other objects in the environment. To validate this model, we develop a prototype system and conduct extensive experiments. The results are consistent with our theoretical analysis, and the system is able to detect the direction of the transmitter with an accuracy of 10° or better, measure the receiver's relative motion displacement within 1 cm a millimeter-level accuracy, and classify five receiver-side activities with an accuracy of 98%. Our work moves a significant step forward in WiFi sensing and may potentially open up new avenues for future research.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596270"}, {"primary_key": "949233", "vector": [], "sparse_vector": [], "title": "PmTrack: Enabling Personalized mmWave-based Human Tracking.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The difficulty in obtaining targets' identity poses a significant obstacle to the pursuit of personalized and customized millimeter-wave (mmWave) sensing. Existing solutions that learn individual differences from signal features have limitations in practical applications. This paper presents a Personalized mmWave-based human Tracking system, PmTrack, by introducing inertial measurement units (IMUs) as identity indicators. Widely available in portable devices such as smartwatches and smartphones, IMUs utilize existing wireless networks for data uploading of identity and data, and are therefore able to assist in radar target identification in a lightweight manner with little deployment and carrying burden for users. PmTrack innovatively adopts orientation as the matching feature, thus well overcoming the data heterogeneity between radar and IMU while avoiding the effect of cumulative errors. In the implementation of PmTrack, we propose a comprehensive set of optimization methods in detection enhancement, interference suppression, continuity maintenance, and trajectory correction, which successfully solved a series of practical problems caused by the three major challenges of weak reflection, point cloud overlap, and body-bounce ghost in multi-person tracking. In addition, an orientation correction method is proposed to overcome the IMU gimbal lock. Extensive experimental results demonstrate that PmTrack achieves an identification accuracy of 98% and 95% with five people in the hall and meeting room, respectively.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631433"}, {"primary_key": "949235", "vector": [], "sparse_vector": [], "title": "Understanding In-Situ Programming for Smart Home Automation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Programming a smart home is an iterative process in which users configure and test the automation during the in-situ experience with IoT space. However, current end-user programming mechanisms are primarily preset configurations on GUI and fail to leverage in-situ behaviors and context. This paper proposed in-situ programming (ISP) as a novel programming paradigm for AIoT automation that extensively leverages users' natural in-situ interaction with the smart environment. We built a Wizard-of-Oz system and conducted a user-enactment study to explore users' behavior models in this paradigm. We identified a dynamic programming flow in which participants iteratively configure and confirm through query, control, edit, and test. We especially identified a novel method \"snapshot\" for automation configuration and a novel method \"simulation\" for automation testing, in which participants leverage ambient responses and in-situ interaction. Based on our findings, we proposed design spaces on dynamic programming flow, coherency and clarity of interface, and state and scene management to build an ideal in-situ programming experience.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596254"}, {"primary_key": "949237", "vector": [], "sparse_vector": [], "title": "UniFi: A Unified Framework for Generalizable Gesture Recognition with Wi-Fi Signals Using Consistency-guided Multi-View Networks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "In recent years, considerable endeavors have been devoted to exploring Wi-Fi-based sensing technologies by modeling the intricate mapping between received signals and corresponding human activities. However, the inherent complexity of Wi-Fi signals poses significant challenges for practical applications due to their pronounced susceptibility to deployment environments. To address this challenge, we delve into the distinctive characteristics of Wi-Fi signals and distill three pivotal factors that can be leveraged to enhance generalization capabilities of deep learning-based Wi-Fi sensing models: 1) effectively capture valuable input to mitigate the adverse impact of noisy measurements; 2) adaptively fuse complementary information from multiple Wi-Fi devices to boost the distinguishability of signal patterns associated with different activities; 3) extract generalizable features that can overcome the inconsistent representations of activities under different environmental conditions (e.g., locations, orientations). Leveraging these insights, we design a novel and unified sensing framework based on Wi-Fi signals, dubbed UniFi, and use gesture recognition as an application to demonstrate its effectiveness. UniFi achieves robust and generalizable gesture recognition in real-world scenarios by extracting discriminative and consistent features unrelated to environmental factors from pre-denoised signals collected by multiple transceivers. To achieve this, we first introduce an effective signal preprocessing approach that captures the applicable input data from noisy received signals for the deep learning model. Second, we propose a multi-view deep network based on spatio-temporal cross-view attention that integrates multi-carrier and multi-device signals to extract distinguishable information. Finally, we present the mutual information maximization as a regularizer to learn environment-invariant representations via contrastive loss without requiring access to any signals from unseen environments for practical adaptation. Extensive experiments on the Widar 3.0 dataset demonstrate that our proposed framework significantly outperforms state-of-the-art approaches in different settings (99% and 90%-98% accuracy for in-domain and cross-domain recognition without additional data collection and model training).", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631429"}, {"primary_key": "949238", "vector": [], "sparse_vector": [], "title": "Designing Data Visualisations for Self-Compassion in Personal Informatics.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>"], "summary": "Wearable personal trackers offer exciting opportunities to contribute to one's well-being, but they also can foster negative experiences. It remains a challenge to understand how we can design personal informatics experiences that help users frame their data in a positive manner and foster self-compassion. To explore this, we conducted a study where we compared different visualisations for user-generated screen time data. We examined positive, neutral and negative framings of the data and whether or not a point of reference was provided in a visualisation. The results show that framing techniques have a significant effect on reflection, rumination and self-compassion. We contribute insights into what design features of data representations can support positive experiences in personal informatics.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631448"}, {"primary_key": "949240", "vector": [], "sparse_vector": [], "title": "ClearSpeech: Improving Voice Quality of Earbuds Using Both In-Ear and Out-Ear Microphones.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Wireless earbuds have been gaining increasing popularity and using them to make phone calls or issue voice commands requires the earbud microphones to pick up human speech. When the speaker is in a noisy environment, speech quality degrades significantly and requires speech enhancement (SE). In this paper, we present ClearSpeech, a novel deep-learning-based SE system designed for wireless earbuds. Specifically, by jointly using the earbud's in-ear and out-ear microphones, we devised a suite of techniques to effectively fuse the two signals and enhance the magnitude and phase of the speech spectrogram. We built an earbud prototype to evaluate ClearSpeech under various settings with data collected from 20 subjects. Our results suggest that ClearSpeech can improve the SE performance significantly compared to conventional approaches using the out-ear microphone only. We also show that ClearSpeech can process user speech in real-time on smartphones.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631409"}, {"primary_key": "949241", "vector": [], "sparse_vector": [], "title": "FocalPoint: Adaptive Direct Manipulation for Selecting Small 3D Virtual Objects.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We propose FocalPoint, a direct manipulation technique in smartphone augmented reality (AR) for selecting small densely-packed objects within reach, a fundamental yet challenging task in AR due to the required accuracy and precision. FocalPoint adaptively and continuously updates a cylindrical geometry for selection disambiguation based on the user's selection history and hand movements. This design is informed by a preliminary study which revealed that participants preferred selecting objects appearing in particular regions of the screen. We evaluate FocalPoint against a baseline direct manipulation technique in a 12-participant study with two tasks: selecting a 3 mm wide target from a pile of cubes and virtually decorating a house with LEGO pieces. FocalPoint was three times as accurate for selecting the correct object and 5.5 seconds faster on average; participants using FocalPoint decorated their houses more and were more satisfied with the result. We further demonstrate the finer control enabled by FocalPoint in example applications of robot repair, 3D modeling, and neural network visualizations.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580856"}, {"primary_key": "949242", "vector": [], "sparse_vector": [], "title": "PoseSonic: 3D Upper Body Pose Estimation Through Egocentric Acoustic Sensing on Smartglasses.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we introduce PoseSonic, an intelligent acoustic sensing solution for smartglasses that estimates upper body poses. Our system only requires two pairs of microphones and speakers on the hinges of the eyeglasses to emit FMCW-encoded inaudible acoustic signals and receive reflected signals for body pose estimation. Using a customized deep learning model, PoseSonic estimates the 3D positions of 9 body joints including the shoulders, elbows, wrists, hips, and nose. We adopt a cross-modal supervision strategy to train our model using synchronized RGB video frames as ground truth. We conducted in-lab and semi-in-the-wild user studies with 22 participants to evaluate PoseSonic, and our user-independent model achieved a mean per joint position error of 6.17 cm in the lab setting and 14.12 cm in semi-in-the-wild setting when predicting the 9 body joint positions in 3D. Our further studies show that the performance was not significantly impacted by different surroundings or when the devices were remounted or by real-world environmental noise. Finally, we discuss the opportunities, challenges, and limitations of deploying PoseSonic in real-world applications.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610895"}, {"primary_key": "949244", "vector": [], "sparse_vector": [], "title": "ADA-SHARK: A Shark Detection Framework Employing Underwater Cameras and Domain Adversarial Neural Nets.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Due to global warming, sharks are moving closer to the beaches, affecting the risk to humans and their own lives. Within the past decade, several technologies were developed to reduce the risks for swimmers and surfers. This study proposes a robust method based on computer vision to detect sharks using an underwater camera monitoring system to secure coastlines. The system is autonomous, environment-friendly, and requires low maintenance. 43,679 images extracted from 175 hours of videos of marine life were used to train our algorithms. Our approach allows the collection and analysis of videos in real-time using an autonomous underwater camera connected to a smart buoy charged with solar panels. The videos are processed by a Domain Adversarial Convolutional Neural Network to discern sharks regardless of the background environment with an F2-score of 83.2% and a recall of 90.9%, while human experts have an F2-score of 94% and a recall of 95.7%.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631416"}, {"primary_key": "949246", "vector": [], "sparse_vector": [], "title": "BMAR: Barometric and Motion-based Alignment and Refinement for Offline Signal Synchronization across Devices.", "authors": ["<PERSON>", "<PERSON>"], "summary": "A requirement of cross-modal signal processing is accurate signal alignment. Though simple on a single device, accurate signal synchronization becomes challenging as soon as multiple devices are involved, such as during activity monitoring, health tracking, or motion capture---particularly outside controlled scenarios where data collection must be standalone, low-power, and support long runtimes. In this paper, we present BMAR, a novel synchronization method that operates purely based on recorded signals and is thus suitable for offline processing. BMAR needs no wireless communication between devices during runtime and does not require any specific user input, action, or behavior. BMAR operates on the data from devices worn by the same person that record barometric pressure and acceleration---inexpensive, low-power, and thus commonly included sensors in today's wearable devices. In its first stage, BMAR verifies that two recordings were acquired simultaneously and pre-aligns all data traces. In a second stage, BMAR refines the alignment using acceleration measurements while accounting for clock skew between devices. In our evaluation, three to five body-worn devices recorded signals from the wearer for up to ten hours during a series of activities. BMAR synchronized all signal recordings with a median error of 33.4 ms and reliably rejected non-overlapping signal traces. The worst-case activity was sleeping, where BMAR's second stage could not exploit motion for refinement and, thus, aligned traces with a median error of 3.06 s.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596268"}, {"primary_key": "949250", "vector": [], "sparse_vector": [], "title": "TouchKey: Touch to Generate Symmetric Keys by Skin Electric Potentials Induced by Powerline Radiation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Zhenyu Yan", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Secure device pairing is important to wearables. Existing solutions either degrade usability due to the need of specific actions like shaking, or they lack universality due to the need of dedicated hardware like electrocardiogram sensors. This paper proposes TouchKey, a symmetric key generation scheme that exploits the skin electric potential (SEP) induced by powerline electromagnetic radiation. The SEP is ubiquitously accessible indoors with analog-to-digital converters widely available on Internet of Things devices. Our measurements show that the SEP has high randomness and the SEPs measured at two close locations on the same human body are similar. Extensive experiments show that TouchKey achieves a high key generation rate of 345 bit/s and an average success rate of 99.29%. Under a range of adversary models including active and passive attacks, TouchKey shows a low false acceptance rate of 0.86%, which outperforms existing solutions. Besides, the overall execution time and energy usage are 0.44 s and 2.716 mJ, which make it suitable for resource-constrained devices.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596264"}, {"primary_key": "949253", "vector": [], "sparse_vector": [], "title": "KeyStub: A Passive RFID-based Keypad Interface Using Resonant Stubs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The proliferation of the Internet of Things is calling for new modalities that enable human interaction with smart objects. Recent research has explored RFID tags as passive sensors to detect finger touch. However, existing approaches either rely on custom-built RFID readers or are limited to pre-trained finger-swiping gestures. In this paper, we introduce KeyStub, which can discriminate multiple discrete keystrokes on an RFID tag. KeyStub interfaces with commodity RFID ICs with multiple microwave-band resonant stubs as keys. Each stub's geometry is designed to create a predefined impedance mismatch to the RFID IC upon a keystroke, which in turn translates into a known amplitude and phase shift, remotely detectable by an RFID reader. KeyStub combines two ICs' signals through a single common-mode antenna and performs differential detection to evade the need for calibration and ensure reliability in heavy multi-path environments. Our experiments using a commercial-off-the-shelf RFID reader and ICs show that up to 8 buttons can be detected and decoded with accuracy greater than 95%. KeyStub points towards a novel way of using resonant stubs to augment RF antenna structures, thus enabling new passive wireless interaction modalities.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631442"}, {"primary_key": "949254", "vector": [], "sparse_vector": [], "title": "Supporting Solar Energy Coordination among Communities.", "authors": ["Georgia Panagiotidou", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The transition to renewable energy is likely to require the creation of growing numbers of energy communities: collectives organized around shared, local renewable resources. Unlike individual households however, the requirements for such communities to share a resource and demand-shift their consumption are still unexplored. By deploying a custom sensor energy monitoring kit and data physicalization workshops with 17 households, we examine the factors that impact their coordination around the shared resource. We found that collective demand-shifting has an extended set of considerations including trade-offs related to privacy, flexibility and social cohesion which are core for navigating already delicate neighborly relations. We use these factors to propose design considerations for a digital system that can act as a mediator among households. Such a system should enable multiple levels of immediacy to account for people's routines, should have adjustable levels of privacy to balance policing and fairness and should be able to offload some of the mundane decision-making. This study moves beyond individual energy consumption behavior to help identify energy as a collective issue that demands collective action. Accordingly, our findings contribute to the development of a next generation of Ubicomp technologies that can support collective action for environmental sustainability.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596243"}, {"primary_key": "949257", "vector": [], "sparse_vector": [], "title": "Exergy: A Toolkit to Simplify Creative Applications of Wind Energy Harvesting.", "authors": ["<PERSON>", "Sienna Xi<PERSON> Sun", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Youngwook Do", "<PERSON>", "<PERSON>"], "summary": "Energy harvesting reduces the burden of power source maintenance and promises to make computing systems genuinely ubiquitous. Researchers have made inroads in this area, but their novel energy harvesting materials and fabrication techniques remain inaccessible to the general maker communities. Therefore, this paper aims to provide a toolkit that makes energy harvesting accessible to novices. In Study 1, we investigate the challenges and opportunities associated with devising energy harvesting technology with experienced researchers and makers (N=9). Using the lessons learned from this investigation, we design a wind energy harvesting toolkit, Exergy, in Study 2. It consists of a simulator, hardware tools, a software example, and ideation cards. We apply it to vehicle environments, which have yet to be explored despite their potential. In Study 3, we conduct a two-phase workshop: hands-on experience and ideation sessions. The results show that novices (N=23) could use Exergy confidently and invent self-sustainable energy harvesting applications creatively.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580814"}, {"primary_key": "949258", "vector": [], "sparse_vector": [], "title": "VAX: Using Existing Video and Audio-based Activity Recognition Models to Bootstrap Privacy-Sensitive Sensors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The use of audio and video modalities for Human Activity Recognition (HAR) is common, given the richness of the data and the availability of pre-trained ML models using a large corpus of labeled training data. However, audio and video sensors also lead to significant consumer privacy concerns. Researchers have thus explored alternate modalities that are less privacy-invasive such as mmWave doppler radars, IMUs, motion sensors. However, the key limitation of these approaches is that most of them do not readily generalize across environments and require significant in-situ training data. Recent work has proposed cross-modality transfer learning approaches to alleviate the lack of trained labeled data with some success. In this paper, we generalize this concept to create a novel system called VAX (Video/Audio to 'X'), where training labels acquired from existing Video/Audio ML models are used to train ML models for a wide range of 'X' privacy-sensitive sensors. Notably, in VAX, once the ML models for the privacy-sensitive sensors are trained, with little to no user involvement, the Audio/Video sensors can be removed altogether to protect the user's privacy better. We built and deployed VAX in ten participants' homes while they performed 17 common activities of daily living. Our evaluation results show that after training, VAX can use its onboard camera and microphone to detect approximately 15 out of 17 activities with an average accuracy of 90%. For these activities that can be detected using a camera and a microphone, VAX trains a per-home model for the privacy-preserving sensors. These models (average accuracy = 84%) require no in-situ user input. In addition, when VAX is augmented with just one labeled instance for the activities not detected by the VAX A/V pipeline (~2 out of 17), it can detect all 17 activities with an average accuracy of 84%. Our results show that VAX is significantly better than a baseline supervised-learning approach of using one labeled instance per activity in each home (average accuracy of 79%) since VAX reduces the user burden of providing activity labels by 8x (~2 labels vs. 17 labels).", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610907"}, {"primary_key": "949260", "vector": [], "sparse_vector": [], "title": "CASES: A Cognition-Aware Smart Eyewear System for Understanding How People Read.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Qi", "<PERSON>", "Wen<PERSON>o Pan", "<PERSON><PERSON>", "<PERSON><PERSON>", "Mingzhi Dong", "<PERSON><PERSON>", "Qin Lv", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The process of reading has attracted decades of scientific research. Work in this field primarily focuses on using eye gaze patterns to reveal cognitive processes while reading. However, eye gaze patterns suffer from limited resolution, jitter noise, and cognitive biases, resulting in limited accuracy in tracking cognitive reading states. Moreover, using sequential eye gaze data alone neglects the linguistic structure of text, undermining attempts to provide semantic explanations for cognitive states during reading. Motivated by the impact of the semantic context of text on the human cognitive reading process, this work uses both the semantic context of text and visual attention during reading to more accurately predict the temporal sequence of cognitive states. To this end, we present a Cognition-Aware Smart Eyewear System (CASES), which fuses semantic context and visual attention patterns during reading. The two feature modalities are time-aligned and fed to a temporal convolutional network based multi-task classification deep model to automatically estimate and further semantically explain the reading state timeseries. CASES is implemented in eyewear and its use does not interrupt the reading process, thus reducing subjective bias. Furthermore, the real-time association between visual and semantic information enables the interactions between visual attention and semantic context to be better interpreted and explained. Ablation studies with 25 subjects demonstrate that CASES improves multi-label reading state estimation accuracy by 20.90% for sentence compared to eye tracking alone. Using CASES, we develop an interactive reading assistance system. Three and a half months of deployment with 13 in-field studies enables several observations relevant to the study of reading. In particular, observed how individual visual history interacts with the semantic context at different text granularities. Furthermore, CASES enables just-in-time intervention when readers encounter processing difficulties, thus promoting self-awareness of the cognitive process involved in reading and helping to develop more effective reading habits.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610910"}, {"primary_key": "949261", "vector": [], "sparse_vector": [], "title": "Lost in the Deep?: Performance Evaluation of Dead Reckoning Techniques in Underwater Environments.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Computing research is increasingly addressing underwater environments and examining how computing can support diving and other activities. Unlike on land, where well-established positioning methods are widely available, underwater environments lack a common positioning mechanism, which is a prerequisite for many applications. Dead reckoning, the use of angle and distance estimates to track position changes from a known point of origin, is a promising candidate for underwater positioning as it does not rely on wireless signals (which decay rapidly in underwater environments) and as there is a wide range of literature and algorithms freely available. Yet, currently it is unclear whether the existing techniques can be adopted in underwater environments or whether the differences in medium and environment affect the performance of the dead reckoning techniques. We contribute by evaluating and systematically analyzing the performance and trade-offs associated with dead reckoning techniques in underwater environments. We present AEOLUS, a prototype unit comprising of a low-cost microcontroller and inertial measurement unit, to perform experiments on the ground and in underwater environments to assess how well the performance of different techniques translates from ground-based use cases to underwater environments. We benchmark 15 different algorithms and compare their performance in such environments to identify common patterns and dissimilarities, and identify root causes for these differences. The results show that displacement and turn errors can be estimated to within 5% error but that the best performing methods vary between land and underwater environments. We also show that the performance depends on the shape of the motion patterns with some algorithms performing better for hard turns whereas others perform better for gradual, more continuous turns.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596245"}, {"primary_key": "949262", "vector": [], "sparse_vector": [], "title": "Diagnosing Medical Score Calculator Apps.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mobile medical score calculator apps are widely used among practitioners to help make decisions regarding patient treatment and diagnosis. Errors in score definition, input, or calculations can result in severe and potentially life-threatening situations. Despite these high stakes, there has been no systematic or rigorous effort to examine and verify score calculator apps. We address these issues via a novel, interval-based score checking approach. Based on our observation that medical reference tables themselves may contain errors (which can propagate to apps) we first introduce automated correctness checking of reference tables. Specifically, we reduce score correctness checking to partition checking (coverage and non-overlap) over score parameters' ranges. We checked 12 scoring systems used in emergency, intensive, and acute care. Surprisingly, though some of these scores have been used for decades, we found errors in 5 score specifications: 8 coverage violations and 3 non-overlap violations. Second, we design and implement an automatic, dynamic analysis-based approach for verifying score correctness in a given Android app; the approach combines efficient, automatic GUI extraction and app exploration with partition/consistency checking to expose app errors. We applied the approach to 90 Android apps that implement medical score calculators. We found 23 coverage violations in 11 apps; 32 non-overlap violations in 12 apps, and 16 incorrect score calculations in 16 apps. We reported all findings to developers, which so far has led to fixes in 6 apps.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610912"}, {"primary_key": "949263", "vector": [], "sparse_vector": [], "title": "A User-Centered Framework to Empower People with Parkinson&apos;s Disease.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Sooyong Park", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a user-centric validation of a teleneurology platform, assessing its effectiveness in conveying screening information, facilitating user queries, and offering resources to enhance user empowerment. This validation process is implemented in the setting of Parkinson's disease (PD), in collaboration with a neurology department of a major medical center in the USA. Our intention is that with this platform, anyone globally with a webcam and microphone-equipped computer can carry out a series of speech, motor, and facial mimicry tasks. Our validation method demonstrates to users a mock PD risk assessment and provides access to relevant resources, including a chatbot driven by GPT, locations of local neurologists, and actionable and scientifically-backed PD prevention and management recommendations. We share findings from 91 participants (48 with PD, 43 without) aimed at evaluating the user experience and collecting feedback. Our framework was rated positively by 80.85% (standard deviation ± 8.92%) of the participants, and it achieved an above-average 70.42 (standard deviation ± 13.85) System-Usability-Scale (SUS) score. We also conducted a thematic analysis of open-ended feedback to further inform our future work. When given the option to ask any questions to the chatbot, participants typically asked for information about neurologists, screening results, and the community support group. We also provide a roadmap of how the knowledge generated in this paper can be generalized to screening frameworks for other diseases through designing appropriate recording environments, appropriate tasks, and tailored user-interfaces.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631430"}, {"primary_key": "949264", "vector": [], "sparse_vector": [], "title": "Auto-Gait: Automatic Ataxia Risk Assessment with Computer Vision from Gait Task Videos.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Titilayo Olubajo", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Readisca Investigators", "<PERSON><PERSON><PERSON>"], "summary": "Many patients with neurological disorders, such as Ataxia, do not have easy access to neurologists, -especially those living in remote localities and developing/underdeveloped countries. Ataxia is a degenerative disease of the nervous system that surfaces as difficulty with motor control, such as walking imbalance. Previous studies have attempted automatic diagnosis of Ataxia with the help of wearable biomarkers, Kinect, and other sensors. These sensors, while accurate, do not scale efficiently well to naturalistic deployment settings. In this study, we propose a method for identifying ataxic symptoms by analyzing videos of participants walking down a hallway, captured with a standard monocular camera. In a collaboration with 11 medical sites located in 8 different states across the United States, we collected a dataset of 155 videos along with their severity rating from 89 participants (24 controls and 65 diagnosed with or are pre-manifest spinocerebellar ataxias). The participants performed the gait task of the Scale for the Assessment and Rating of Ataxia (SARA). We develop a computer vision pipeline to detect, track, and separate the participants from their surroundings and construct several features from their body pose coordinates to capture gait characteristics such as step width, step length, swing, stability, speed, etc. Our system is able to identify and track a patient in complex scenarios. For example, if there are multiple people present in the video or an interruption from a passerby. Our Ataxia risk-prediction model achieves 83.06% accuracy and an 80.23% F1 score. Similarly, our Ataxia severity-assessment model achieves a mean absolute error (MAE) score of 0.6225 and a Pearson's correlation coefficient score of 0.7268. Our model competitively performed when evaluated on data from medical sites not used during training. Through feature importance analysis, we found that our models associate wider steps, decreased walking speed, and increased instability with greater Ataxia severity, which is consistent with previously established clinical knowledge. Furthermore, we are releasing the models and the body-pose coordinate dataset to the research community - the largest dataset on ataxic gait (to our knowledge). Our models could contribute to improving health access by enabling remote Ataxia assessment in non-clinical settings without requiring any sensors or special cameras. Our dataset will help the computer science community to analyze different characteristics of Ataxia and to develop better algorithms for diagnosing other movement disorders.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580845"}, {"primary_key": "949266", "vector": [], "sparse_vector": [], "title": "Synthetic Smartwatch IMU Data Generation from In-the-wild ASL Videos.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The scarcity of training data available for IMUs in wearables poses a serious challenge for IMU-based American Sign Language (ASL) recognition. In this paper, we ask the following question: can we \"translate\" the large number of publicly available, in-the-wild ASL videos to their corresponding IMU data? We answer this question by presenting a video to IMU translation framework (Vi2IMU) that takes as input user videos and estimates the IMU acceleration and gyro from the perspective of user's wrist. Vi2IMU consists of two modules, a wrist orientation estimation module that accounts for wrist rotations by carefully incorporating hand joint positions, and an acceleration and gyro prediction module, that leverages the orientation for transformation while capturing the contributions of hand movements and shape to produce realistic wrist acceleration and gyro data. We evaluate Vi2IMU by translating publicly available ASL videos to their corresponding wrist IMU data and train a gesture recognition model purely using the translated data. Our results show that the model using translated data performs reasonably well compared to the same model trained using measured IMU data.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596261"}, {"primary_key": "949267", "vector": [], "sparse_vector": [], "title": "ConvBoost: Boosting ConvNets for Sensor-based Activity Recognition.", "authors": ["<PERSON><PERSON>", "Yu <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Human activity recognition (HAR) is one of the core research themes in ubiquitous and wearable computing. With the shift to deep learning (DL) based analysis approaches, it has become possible to extract high-level features and perform classification in an end-to-end manner. Despite their promising overall capabilities, DL-based HAR may suffer from overfitting due to the notoriously small, often inadequate, amounts of labeled sample data that are available for typical HAR applications. In response to such challenges, we propose ConvBoost -- a novel, three-layer, structured model architecture and boosting framework for convolutional network based HAR. Our framework generates additional training data from three different perspectives for improved HAR, aiming to alleviate the shortness of labeled training data in the field. Specifically, with the introduction of three conceptual layers--Sampling Layer, Data Augmentation Layer, and Resilient Layer -- we develop three \"boosters\" -- R-Frame, Mix-up, and C-Drop -- to enrich the per-epoch training data by dense-sampling, synthesizing, and simulating, respectively. These new conceptual layers and boosters, that are universally applicable for any kind of convolutional network, have been designed based on the characteristics of the sensor data and the concept of frame-wise HAR. In our experimental evaluation on three standard benchmarks (Opportunity, PAMAP2, GOTOV) we demonstrate the effectiveness of our ConvBoost framework for HAR applications based on variants of convolutional networks: vanilla CNN, ConvLSTM, and Attention Models. We achieved substantial performance gains for all of them, which suggests that the proposed approach is generic and can serve as a practical solution for boosting the performance of existing ConvNet-based HAR models. This is an open-source project, and the code can be found at https://github.com/sshao2013/ConvBoost", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596234"}, {"primary_key": "949268", "vector": [], "sparse_vector": [], "title": "N-euro Predictor: A Neural Network Approach for Smoothing and Predicting Motion Trajectory.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Vu An Tran", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>"], "summary": "Jitter and lag severely impact the smoothness and responsiveness of user experience on vision-based human-display interactive systems such as phones, TVs, and VR/AR. Current manually-tuned filters for smoothing and predicting motion trajectory struggle to effectively address both issues, especially for applications that have a large range of movement speed. To overcome this, we introduce N-euro, a residual-learning-based neural network predictor that can simultaneously reduce jitter and lag while maintaining low computational overhead. Compared to the fine-tuned existing filters, N-euro improves prediction performance by 36% and smoothing performance by 42%. We fabricated a Fish Tank VR system and an AR mirror system and conducted a user experience study (n=34) with the real-time implementation of N-euro. Our results indicate that the N-euro predictor brings a statistically significant improvement in user experience. With its validated effectiveness and usability, we expect this approach to bring a better user experience to various vision-based interactive systems.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610884"}, {"primary_key": "949269", "vector": [], "sparse_vector": [], "title": "Predicting Symptom Improvement During Depression Treatment Using Sleep Sensory Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shweta Ware", "Jin<PERSON> B<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Dong<PERSON> Song", "<PERSON>"], "summary": "Depression is a serious mental illness. The current best guideline in depression treatment is closely monitoring patients and adjusting treatment as needed. Close monitoring of patients through physician-administered follow-ups or self-administered questionnaires, however, is difficult in clinical settings due to high cost, lack of trained professionals, and burden to the patients. Sensory data collected from mobile devices has been shown to provide a promising direction for long-term monitoring of depression symptoms. Most existing studies in this direction, however, focus on depression detection; the few studies that are on predicting changes in depression are not in clinical settings. In this paper, we investigate using one type of sensory data, sleep data, collected from wearables to predict improvement of depression symptoms over time after a patient initiates a new pharmacological treatment. We apply sleep trend filtering to noisy sleep sensory data to extract high-level sleep characteristics and develop a family of machine learning models that use simple sleep features (mean and variation of sleep duration) to predict symptom improvement. Our results show that using such simple sleep features can already lead to validation F1 score up to 0.68, indicating that using sensory data for predicting depression improvement during treatment is a promising direction.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610932"}, {"primary_key": "949270", "vector": [], "sparse_vector": [], "title": "Conversational Localization: Indoor Human Localization through Intelligent Conversation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose a novel sensorless approach to indoor localization by leveraging natural language conversations with users, which we call conversational localization. To show the feasibility of conversational localization, we develop a proof-of-concept system that guides users to describe their surroundings in a chat and estimates their position based on the information they provide. We devised a modular architecture for our system with four modules. First, we construct an entity database with available image-based floor maps. Second, we enable the dynamic identification and scoring of information provided by users through our utterance processing module. Then, we implement a conversational agent that can intelligently strategize and guide the interaction to elicit localizationally valuable information from users. Finally, we employ visibility catchment area and line-of-sight heuristics to generate spatial estimates for the user's location. We conduct two user studies in designing and testing the system. We collect 800 natural language descriptions of unfamiliar indoor spaces in an online crowdsourcing study to learn the feasibility of extracting localizationally useful entities from user utterances. We then conduct a field study with 10 participants at 10 locations to evaluate the feasibility and performance of conversational localization. The results show that conversational localization can achieve within-10 meter localization accuracy at eight out of the ten study sites, showing the technique's utility for classes of indoor location-based services.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631404"}, {"primary_key": "949272", "vector": [], "sparse_vector": [], "title": "Learning from User-driven Events to Generate Automation Sequences.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Enabling smart devices to learn automating actions as expected is a crucial yet challenging task. The traditional Trigger-Action rule approach for device automation is prone to ambiguity in complex scenarios. To address this issue, we propose a data-driven approach that leverages recorded user-driven event sequences to predict potential actions users may take and generate fine-grained device automation sequences. Our key intuition is that user-driven event sequences, like human-written articles and programs, are governed by consistent semantic contexts and contain regularities that can be modeled to generate sequences that express the user's preferences. We introduce ASGen, a deep learning framework that combines sequential information, event attributes, and external knowledge to form the event representation and output sequences of arbitrary length to facilitate automation. To evaluate our approach from both quantitative and qualitative perspectives, we conduct two studies using a realistic dataset containing over 4.4 million events. Our results show that our approach surpasses other methods by providing more accurate recommendations. And the automation sequences generated by our model are perceived as equally or even more rational and useful compared to those generated by humans.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631427"}, {"primary_key": "949273", "vector": [], "sparse_vector": [], "title": "Can You Ear Me?: A Comparison of Different Private and Public Notification Channels for the Earlobe.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The earlobe is a well-known location for wearing jewelry, but might also be promising for electronic output, such as presenting notifications. This work elaborates the pros and cons of different notification channels for the earlobe. Notifications on the earlobe can be private (only noticeable by the wearer) as well as public (noticeable in the immediate vicinity in a given social situation). A user study with 18 participants showed that the reaction times for the private channels (Poke, Vibration, Private Sound, Electrotactile) were on average less than 1 s with an error rate (missed notifications) of less than 1 %. Thermal Warm and Cold took significantly longer and Cold was least reliable (26 % error rate). The participants preferred Electrotactile and Vibration. Among the public channels the recognition time did not differ significantly between Sound (738 ms) and LED (828 ms), but Display took much longer (3175 ms). At 22 % the error rate of Display was highest. The participants generally felt comfortable wearing notification devices on their earlobe. The results show that the earlobe indeed is a suitable location for wearable technology, if properly miniaturized, which is possible for Electrotactile and LED. We present application scenarios and discuss design considerations. A small field study in a fitness center demonstrates the suitability of the earlobe notification concept in a sports context.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610925"}, {"primary_key": "949275", "vector": [], "sparse_vector": [], "title": "Laser-Powered Vibrotactile Rendering.", "authors": ["<PERSON><PERSON>", "Yuhua Jin", "<PERSON><PERSON>", "Yong<PERSON> Shi", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We investigate the feasibility of a vibrotactile device that is both battery-free and electronic-free. Our approach leverages lasers as a wireless power transfer and haptic control mechanism, which can drive small actuators commonly used in AR/VR and mobile applications with DC or AC signals. To validate the feasibility of our method, we developed a proof-of-concept prototype that includes low-cost eccentric rotating mass (ERM) motors and linear resonant actuators (LRAs) connected to photovoltaic (PV) cells. This prototype enabled us to capture laser energy from any distance across a room and analyze the impact of critical parameters on the effectiveness of our approach. Through a user study, testing 16 different vibration patterns rendered using either a single motor or two motors, we demonstrate the effectiveness of our approach in generating vibration patterns of comparable quality to a baseline, which rendered the patterns using a signal generator.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631449"}, {"primary_key": "949276", "vector": [], "sparse_vector": [], "title": "Single Packet, Single Channel, Switched Antenna Array for RF Localization.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (Jack) <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cost-effective and accurate means of localizing radio transmitters has the potential to enable a wide range of applications in the consumer electronics, IoT, and healthcare domains. However, existing multi-antenna localization methods require high-cost synchronized receivers, long integration times, and/or specialized packet structures. This paper proposes using a high-speed RF mux that sequentially connects antennas to a single 2MHz radio receiver and sub-packet switching to determine the Angle of Arrival of individual packets. Importantly, this approach does not need synchronization between the mux and the receiver, reducing cost and system complexity. Our signal processing pipeline recovers both switch timing and the antenna number from the received RF signal. The sub-packet waveforms are used to generate a synthetic reference packet, and our customized Multi-Resolution Beaming and MUSIC algorithms are used to determine the Angle of Arrival. Results show that our real-time system is highly accurate even when the target is moving, with a mean AoA accuracy of 3.4 degrees and a 2D localization accuracy of 36.4 cm. Furthermore, the system is capable of tracking multiple users carrying smartphones in either their hands or pockets. Ultimately this approach enables a single low-cost, low bandwidth commodity RF receiver to be used to create an N-element phased array receiver.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596263"}, {"primary_key": "949278", "vector": [], "sparse_vector": [], "title": "HapticPilot: Authoring In-situ Hand Posture-Adaptive Vibrotactile Feedback for Virtual Reality.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The emergence of vibrotactile feedback in hand wearables enables immersive virtual reality (VR) experience with whole-hand haptic rendering. However, existing haptic rendering neglects inconsistent sensations caused by hand postures. In our study, we observed that changing hand postures alters the distribution of vibrotactile signals which might degrade one's haptic perception. To address the issues, we present HapticPilot which allows an in-situ haptic experience design for hand wearables in VR. We developed an in-situ authoring system supporting instant haptic design. In the authoring tool, we applied our posture-adaptive haptic rendering algorithm with a novel haptic design abstraction called phantom grid. The algorithm adapts phantom grid to the target posture and incorporates 1D &amp; 2D phantom sensation with a unique actuator arrangement to provide a whole-hand experience. With this method, HapticPilot provides a consistent haptic experience across various hand postures is available. Through measuring perceptual haptic performance and collecting qualitative feedback, we validated the usability of the system. In the end, we demonstrated our system with prospective VR scenarios showing how it enables an intuitive, empowering, and responsive haptic authoring framework.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631453"}, {"primary_key": "949281", "vector": [], "sparse_vector": [], "title": "The Tale of a Complicated Relationship: Insights from Users&apos; Love/Breakup Letters to Their Smartphones before and during the COVID-19 Pandemic.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Smartphones have gotten under public scrutiny due to their ostensible negative impact on users' well-being. Nonetheless, users and related work report positive aspects of smartphones, too. We investigated this discrepancy through the prism of the emotional user-smartphone relationship by having people write love/breakup letters to their smartphones. We gathered 82 letters - 42 before and 40 during the COVID-19 pandemic. We found a mixed nature regarding the distribution of love and breakup letters and associated emotions based on the revisited OCC-model of emotions - with a slight shift towards the negative emotional spectrum during the COVID-19 pandemic. Furthermore, we performed an extensive qualitative analysis of 819 user statements extracted from the letters, resulting in a connection of emotions to 17 smartphone features and eight themes of real-life consequences of smartphone use. We then identified eight common patterns of this connection, classified as smartphone roles. The collected letters mostly model a complex user-smartphone relationship, comprising different roles depending on users' inner and outer context. We discuss how HCI could help in shaping the complex user-smartphone relationship in future research and suggest supporting a healthy balance between users' daily life and smartphone use.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580792"}, {"primary_key": "949282", "vector": [], "sparse_vector": [], "title": "Privacy against Real-Time Speech Emotion Detection via Acoustic Adversarial Evasion of Machine Learning.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Smart speaker voice assistants (VAs) such as Amazon Echo and Google Home have been widely adopted due to their seamless integration with smart home devices and the Internet of Things (IoT) technologies. These VA services raise privacy concerns, especially due to their access to our speech. This work considers one such use case: the unaccountable and unauthorized surveillance of a user's emotion via speech emotion recognition (SER). This paper presents DARE-GP, a solution that creates additive noise to mask users' emotional information while preserving the transcription-relevant portions of their speech. DARE-GP does this by using a constrained genetic programming approach to learn the spectral frequency traits that depict target users' emotional content, and then generating a universal adversarial audio perturbation that provides this privacy protection. Unlike existing works, DARE-GP provides: a) real-time protection of previously unheard utterances, b) against previously unseen black-box SER classifiers, c) while protecting speech transcription, and d) does so in a realistic, acoustic environment. Further, this evasion is robust against defenses employed by a knowledgeable adversary. The evaluations in this work culminate with acoustic evaluations against two off-the-shelf commercial smart speakers using a small-form-factor (raspberry pi) integrated with a wake-word system to evaluate the efficacy of its real-world, real-time deployment.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610887"}, {"primary_key": "949283", "vector": [], "sparse_vector": [], "title": "Mood Measurement on Smartphones: Which Measure, Which Design?", "authors": ["<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, often studied using smartphones, influences human perception, judgment, thought, and behavior. Mood measurements on smartphones face challenges concerning the selection of a proper mood measure and its transfer, or translation, into a digital application (app) that is user-engaging. Addressing these challenges, researchers sometimes end up developing a new interaction design and modifying the classic mood measure for an app. However, the extent to which such design alterations can impact user compliance, user experience, and the accuracy of mood measurements throughout a mood self-tracking study is unclear. In this paper, we explore and investigate how the selection of a mood measure (from two widely used measures) and its design alteration (from three options of classic, chatbot, and interactive designs) impact the (i) validity, (ii) user compliance, and (iii) user experience of mood measurement apps. For this purpose, we conducted a hybrid study with a mixed design in three parts. The first part suggests that a measure's validity can be susceptible to design modifications and introduces the concept of measure's resilience which can be essential when modifying the interaction design of a measurement tool. The second part discovers that both the type and design of the chosen measure can impact user compliance. This part also portrays a more complete picture of user compliance by demonstrating the use of several variables to investigate compliance. This investigation reveals that user compliance is not just about the response duration or length of a measurement tool. The final part finds that a measure or its design does not significantly influence the user experience for a well-designed app. In this part, we also discover which user experience criteria are more impactful for improving user compliance when designing mood tracking (or mood self-tracking) tools. Our results further suggest that, for a resilient measure, the interactive design is more likely to attract users and have higher user compliance and satisfaction as a whole. Ultimately, choosing a measure or design alternative would be a three-way trade-off between the measure's validity (or accuracy), user compliance, and user satisfaction, which researchers have to prioritize. A successful mood measurement with a smartphone needs to balance both concepts of app quality and assessment quality.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580864"}, {"primary_key": "949284", "vector": [], "sparse_vector": [], "title": "Wi-Flex: Reflex Detection with Commodity WiFi.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we are interested in startle reflex detection with WiFi signals. We propose that two parameters related to the received signal bandwidth, maximum normalized bandwidth and bandwidth-intense duration, can successfully detect reflexes and robustly differentiate them from non-reflex events, even from those that involve intense body motions (e.g., certain exercises). In order to confirm this, we need a massive RF reflex dataset which would be prohibitively laborious to collect. On the other hand, there are many available reflex/non-reflex videos online. We then propose an efficient way of translating the content of a video to the bandwidth of the corresponding received RF signal that would have been measured if there was a link near the event in the video, by drawing analogies between our problem and the classic bandwidth modeling work of <PERSON><PERSON> in the context of analog FM radios (<PERSON>'s Rule). This then allows us to translate online reflex/non-reflex videos to an instant large RF bandwidth dataset, and characterize optimum 2D reflex/non-reflex decision regions accordingly, to be used during real operation with WiFi. We extensively test our approach with 203 reflex events, 322 non-reflex events (including 142 intense body motion events), over four areas (including several through-wall ones), and with 15 participants, achieving a correct reflex detection rate of 90.15% and a false alarm rate of 2.49% (all events are natural). While the paper is extensively tested with startle reflexes, it is also applicable to sport-type reflexes, and is thus tested with sport-related reflexes as well. We further show reflex detection with multiple people simultaneously engaged in a series of activities. Optimality of the proposed design is also demonstrated experimentally. Finally, we conduct experiments to show the potential of our approach for providing cost-effective and quantifiable metrics in sports, by quantifying a goalkeeper's reaction. Overall, our results confirm a fast, robust, and cost-effective reflex detection system, without collecting any RF training data, or training a neural network.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610930"}, {"primary_key": "949286", "vector": [], "sparse_vector": [], "title": "HyWay: <PERSON><PERSON><PERSON> in the Hybrid World.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Arshia Arya", "<PERSON><PERSON>", "Venkata N. <PERSON>"], "summary": "We present HyWay, short for \"Hybrid Hallway\", to enable mingling and informal interactions among physical and virtual users, in casual spaces and settings, such as office water cooler areas, conference hallways, trade show floors, and more. We call out how the hybrid and unstructured (or semi-structured) nature of such settings set these apart from the all-virtual and/or structured settings considered in prior work. Key to the design of HyWay is bridging the awareness gap between physical and virtual users, and providing the virtual users the same agency as physical users. To this end, we have designed HyWay to incorporate reciprocity (users can see and hear others only if they can be seen and heard), porosity (conversations in physical space are porous and not within airtight compartments), and agency (the ability for users to seamlessly move between conversations). We present our implementation of HyWay and the user survey findings from multiple deployments in unstructured settings (e.g., social gatherings), and semi-structured ones (e.g., a poster event). Results from these deployments show that HyWay enables effective mingling between physical and virtual users.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596235"}, {"primary_key": "949288", "vector": [], "sparse_vector": [], "title": "Voicify Your UI: Towards Android App Control with Voice Commands.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chunyang Chen"], "summary": "Nowadays, voice assistants help users complete tasks on the smartphone with voice commands, replacing traditional touchscreen interactions when such interactions are inhibited. However, the usability of those tools remains moderate due to the problems in understanding rich language variations in human commands, along with efficiency and comprehensibility issues. Therefore, we introduce <PERSON>oicify, an Android virtual assistant that allows users to interact with on-screen elements in mobile apps through voice commands. Using a novel deep learning command parser, Voicify interprets human verbal input and performs matching with UI elements. In addition, the tool can directly open a specific feature from installed applications by fetching application code information to explore the set of in-app components. Our command parser achieved 90% accuracy on the human command dataset. Furthermore, the direct feature invocation module achieves better feature coverage in comparison to Google Assistant. The user study demonstrates the usefulness of Voicify in real-world scenarios.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3581998"}, {"primary_key": "949289", "vector": [], "sparse_vector": [], "title": "GlucoScreen: A Smartphone-based Readerless Glucose Test Strip for Prediabetes Screening.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Shwetak N. Patel"], "summary": "Blood glucose measurement is commonly used to screen for and monitor diabetes, a chronic condition characterized by the inability to effectively modulate blood glucose that can lead to heart disease, vision loss, and kidney failure. Early detection of prediabetes can forestall or reverse more serious illness if healthy lifestyle adjustments or medical interventions are made in a timely manner. Current diabetes screening methods require visits to a healthcare facility and use of over-the-counter glucose-testing devices (glucometers), both of which are costly or inaccessible for many populations, reducing the chances of early disease detection. We therefore developed GlucoScreen, a readerless glucose test strip that enables affordable, single-use, at-home glucose testing, leveraging the user's touchscreen cellphone for reading and displaying results. By integrating minimal, low-cost electronics with commercially available blood glucose testing strips, the GlucoScreen prototype introduces a new type of low-cost, battery-free glucose testing tool that works with any smartphone, obviating the need to purchase a separate dedicated reader. Our key innovation is using the phone's capacitive touchscreen for the readout of the minimally modified commercially available glucose test strips. In an in vitro evaluation with artificial glucose solutions, we tested GlucoScreen with five different phones and compared the findings to two common glucometers, AccuChek and True Metrix. The mean absolute error (MAE) for our GlucoScreen prototype was 4.52 mg/dl (Accu-Chek test strips) and 3.7 mg/dl (True Metrix test strips), compared to 4.98 mg/dl and 5.44 mg/dl for the AccuChek glucometer and True Metrix glucometer, respectively. In a clinical investigation with 75 patients, GlucoScreen had a MAE of 10.47 mg/dl, while the AccuChek glucometer had a 9.88 mg/dl MAE. These results indicate that GlucoScreen's performance is comparable to that of commonly available over-the-counter blood glucose testing devices. With further development and validation, GlucoScreen has the potential to facilitate large-scale and lower cost diabetes screening. This work employs GlucoScreen's smartphone-based technology for glucose testing, but it could be extended to build other readerless electrochemical assays in the future.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580855"}, {"primary_key": "949290", "vector": [], "sparse_vector": [], "title": "WiMeasure: Millimeter-level Object Size Measurement with Commodity WiFi Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "In the past few years, a large range of wireless signals such as WiFi, RFID, UWB and Millimeter Wave were utilized for sensing purposes. Among these wireless sensing modalities, WiFi sensing attracts a lot of attention owing to the pervasiveness of WiFi infrastructure in our surrounding environments. While WiFi sensing has achieved a great success in capturing the target's motion information ranging from coarse-grained activities and gestures to fine-grained vital signs, it still has difficulties in precisely obtaining the target size owing to the low frequency and small bandwidth of WiFi signals. Even Millimeter Wave radar can only achieve a very coarse-grained size measurement. High precision object size sensing requires using RF signals in the extremely high-frequency band (e.g., Terahertz band). In this paper, we utilize low-frequency WiFi signals to achieve accurate object size measurement without requiring any learning or training. The key insight is that when an object moves between a pair of WiFi transceivers, the WiFi CSI variations contain singular points (i.e., singularities) and we observe an exciting opportunity of employing the number of singularities to measure the object size. In this work, we model the relationship between the object size and the number of singularities when an object moves near the LoS path, which lays the theoretical foundation for the proposed system to work. By addressing multiple challenges, for the first time, we make WiFi-based object size measurement work on commodity WiFi cards and achieve a surprisingly low median error of 2.6 mm. We believe this work is an important missing piece of WiFi sensing and opens the door to size measurement using low-cost low-frequency RF signals.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596250"}, {"primary_key": "949292", "vector": [], "sparse_vector": [], "title": "Human Parsing with Joint Learning for Dynamic mmWave Radar Point Cloud.", "authors": ["<PERSON><PERSON>", "Dongjiang Cao", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Human sensing and understanding is a key requirement for many intelligent systems, such as smart monitoring, human-computer interaction, and activity analysis, etc. In this paper, we present mmParse, the first human parsing design for dynamic point cloud from commercial millimeter-wave radar devices. mmParse proposes an end-to-end neural network design that addresses the inherent challenges in parsing mmWave point cloud (e.g., sparsity and specular reflection). First, we design a novel multi-task learning approach, in which an auxiliary task can guide the network to understand human structural features. Secondly, we introduce a multi-task feature fusion method that incorporates both intra-task and inter-task attention to aggregate spatio-temporal features of the subject from a global view. Through extensive experiments in both indoor and outdoor environments, we demonstrate that our proposed system is able to achieve ~ 92% accuracy and ~ 84% IoU accuracy. We also show that the predicted semantic labels can increase the performance of two downstream tasks (pose estimation and action recognition) by ~ 18% and ~ 6% respectively.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580779"}, {"primary_key": "949293", "vector": [], "sparse_vector": [], "title": "sUrban: Stable Prediction for Unseen Urban Data from Location-based Sensors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent machine learning research on smart cities has achieved great success in predicting future trends, under the key assumption that the test data follows the same distribution of the training data. The rapid urbanization, however, makes this assumption challenging to hold in practice. Because new data is emerging from new environments (e.g., an emerging city or region), which may follow different distributions from data in existing environments. Different from transfer-learning methods accessing target data during training, we often do not have any prior knowledge about the new environment. Therefore, it is critical to explore a predictive model that can be effectively adapted to unseen new environments. This work aims to address this Out-of-Distribution (OOD) challenge for sustainable cities. We propose to identify two kinds of features that are useful for OOD prediction in each environment: (1) the environment-invariant features to capture the shared commonalities for predictions across different environments; and (2) the environment-aware features to characterize the unique information of each environment. Take bike riding as an example. The bike demands of different cities often follow the same pattern that they significantly increase during the rush hour on workdays. Meanwhile, there are also some local patterns in each city because of different cultures and citizens' travel preferences. We introduce a principled framework -- sUrban -- that consists of an environment-invariant optimization module for learning invariant representation and an environment-aware optimization module for learning environment-aware representation. Evaluation on real-world datasets from various urban application domains corroborates the generalizability of sUrban. This work opens up new avenues to smart city development.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610877"}, {"primary_key": "949294", "vector": [], "sparse_vector": [], "title": "LiqDetector: Enabling Container-Independent Liquid Detection with mmWave Signals Based on a Dual-Reflection Model.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the advancement of wireless sensing technologies, RF-based contact-less liquid detection attracts more and more attention. Compared with other RF devices, the mmWave radar has the advantages of large bandwidth and low cost. While existing radar-based liquid detection systems demonstrate promising performance, they still have a shortcoming that in the detection result depends on container-related factors (e.g., container placement, container caliber, and container material). In this paper, to enable container-independent liquid detection with a COTS mmWave radar, we propose a dual-reflection model by exploring reflections from different interfaces of the liquid container. Specifically, we design a pair of amplitude ratios based on the signals reflected from different interfaces, and theoretically demonstrate how the refractive index of liquids can be estimated by eliminating the container's impact. To validate the proposed approach, we implement a liquid detection system LiqDetector. Experimental results show that LiqDetector achieves cross-container estimation of the liquid's refractive index with a mean absolute percentage error (MAPE) of about 4.4%. Moreover, the classification accuracies for 6 different liquids and alcohol with different strengths (even a difference of 1%) exceed 96% and 95%, respectively. To the best of our knowledge, this is the first study that achieves container-independent liquid detection based on the COTS mmWave radar by leveraging only one pair of Tx-Rx antennas.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631443"}, {"primary_key": "949296", "vector": [], "sparse_vector": [], "title": "ToothFairy: Real-time Tooth-by-tooth Brushing Monitor Using Earphone Reversed Signals.", "authors": ["<PERSON>", "Feng Hong", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Tooth brushing monitors have the potential to enhance oral hygiene and encourage the development of healthy brushing habits. However, previous studies fall short of recognizing each tooth due to limitations in external sensors and variations among users. To address these challenges, we present ToothFairy, a real-time tooth-by-tooth brushing monitor that uses earphone reverse signals captured within the oral cavity to identify each tooth during brushing. The key component of ToothFairy is a novel bone-conducted acoustic attenuation model, which quantifies sound propagation within the oral cavity. This model eliminates the need for machine learning and can be calibrated with just one second of brushing data for each tooth by a new user. ToothFairy also addresses practical issues such as brushing detection and tooth region determination. Results from extensive experiments, involving 10 volunteers and 25 combinations of five commercial off-the-shelf toothbrush and earphone models each, show that <PERSON>thFair<PERSON> achieves tooth recognition with an average accuracy of 90.5%.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631412"}, {"primary_key": "949297", "vector": [], "sparse_vector": [], "title": "Spectral-Loc: Indoor Localization Using Light Spectral Information.", "authors": ["<PERSON><PERSON><PERSON>", "Ji<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "For indoor settings, we investigate the impact of location on the spectral distribution of the received light, i.e., the intensity of light for different wavelengths. Our investigations confirm that even under the same light source, different locations exhibit slightly different spectral distribution due to reflections from their localised environment containing different materials or colours. By exploiting this observation, we propose Spectral-Loc, a novel indoor localization system that uses light spectral information to identify the location of the device. With spectral sensors finding their way into the latest products and applications, such as white balancing in smartphone photography, Spectral-Loc can be readily deployed without requiring any additional hardware or infrastructure. We prototype Spectral-Loc using a commercial-off-the-shelf light spectral sensor, AS7265x, which can measure light intensity over 18 different wavelength sub-bands. We benchmark the localization accuracy of Spectral-Loc against the conventional light intensity sensors that provide only a single intensity value. Our evaluations over two different indoor spaces, a meeting room, and a large office space, demonstrate that the use of light spectral information significantly reduces the localization error for the different percentiles.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580878"}, {"primary_key": "949298", "vector": [], "sparse_vector": [], "title": "RF-CM: Cross-Modal Framework for RF-enabled Few-Shot Human Activity Recognition.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Xiao<PERSON> Chen"], "summary": "Radio-Frequency (RF) based human activity recognition (HAR) enables many attractive applications such as smart home, health monitoring, and virtual reality (VR). Among multiple RF sensors, mmWave radar is emerging as a new trend due to its fine-grained sensing capability. However, laborious data collection and labeling processes are required when employing a radar-based sensing system in a new environment. To this end, we propose RF-CM, a general cross-modal human activity recognition framework. The key enabler is to leverage the knowledge learned from a massive WiFi dataset to build a radar-based HAR system with limited radar samples. It can significantly reduce the overhead of training data collection. In addition, RF-CM can work well regardless of the deployment setups of WiFi and mmWave radar, such as performing environments, users' characteristics, and device deployment. RF-CM achieves this by first capturing the activity-related variation patterns through data processing schemes. It then employs a convolution neural network-based feature extraction module to extract the high-dimensional features to be fed into the activity recognition module. Finally, RF-CM takes the generalization knowledge from WiFi networks as guide labels to supervise the training of the radar model, thus enabling a few-shot radar-based HAR system. We evaluate RF-CM by applying it to two HAR applications, fine-grained American sign language recognition (WiFi-cross-radar) and coarse-grained gesture recognition (WiFi-cross-RFID). The accuracy improvement of over 10% in both applications demonstrates the effectiveness of RF-CM. This cross-modal ability allows RF-CM to support more cross-modal applications.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580859"}, {"primary_key": "949300", "vector": [], "sparse_vector": [], "title": "Orientation-Aware 3D SLAM in Alternating Magnetic Field from Powerlines.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Zhenyu Yan", "<PERSON>"], "summary": "Identifying new sensing modalities for indoor localization is an interest of research. This paper studies powerline-induced alternating magnetic field (AMF) that fills the indoor space for the orientation-aware three-dimensional (3D) simultaneous localization and mapping (SLAM). While an existing study has adopted a uniaxial AMF sensor for SLAM in a plane surface, the design falls short of addressing the vector field nature of AMF and is therefore susceptible to sensor orientation variations. Moreover, although the higher spatial variability of AMF in comparison with indoor geomagnetism promotes location sensing resolution, extra SLAM algorithm designs are needed to achieve robustness to trajectory deviations from the constructed map. To address the above issues, we design a new triaxial AMF sensor and a new SLAM algorithm that constructs a 3D AMF intensity map regularized and augmented by a Gaussian process. The triaxial sensor's orientation estimation is free of the error accumulation problem faced by inertial sensing. From extensive evaluation in eight indoor environments, our AMF-based 3D SLAM achieves sub-1m to 3m median localization errors in spaces of up to 500 m2, sub-2° mean error in orientation sensing, and outperforms the SLAM systems based on Wi-Fi, geomagnetism, and uniaxial AMF by more than 30%.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631446"}, {"primary_key": "949301", "vector": [], "sparse_vector": [], "title": "MagSound: Magnetic Field Assisted Wireless Earphone Tracking.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>peng Dai", "<PERSON><PERSON><PERSON>"], "summary": "Wireless earphones are pervasive acoustic sensing platforms that can be used for many applications such as motion tracking and handwriting input. However, wireless earphones suffer clock offset between the connected smart devices, which would accumulate error rapidly over time. Moreover, compared with smartphone and voice assistants, the acoustic signal transmitted by wireless earphone is much weaker due to the poor frequency response. In this paper, we propose MagSound, which uses the built-in magnets to improve the tracking and acoustic sensing performance of Commercial-Off-The-Shelf (COTS) earphones. Leveraging magnetic field strength, MagSound can predict the position of wireless earphones free from clock offset, which can be used to re-calibrate the acoustic tracking. Further, the fusion of the two modalities mitigates the accumulated clock offset and multipath effect. Besides, to increase the robustness to noise, MagSound employs finely designed Orthogonal Frequency-Division Multiplexing (OFDM) ranging signals. We implement a prototype of MagSound on COTS and perform experiments for tracking and handwriting input. Results demonstrate that MagSound maintains millimeter-level error in 2D tracking, and improves the handwriting recognition accuracy by 49.81%. We believe that MagSound can contribute to practical applications of wireless earphones-based sensing.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580889"}, {"primary_key": "949302", "vector": [], "sparse_vector": [], "title": "Optimization-Free Test-Time Adaptation for Cross-Person Activity Recognition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>un Xi", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Human Activity Recognition (HAR) models often suffer from performance degradation in real-world applications due to distribution shifts in activity patterns across individuals. Test-Time Adaptation (TTA) is an emerging learning paradigm that aims to utilize the test stream to adjust predictions in real-time inference, which has not been explored in HAR before. However, the high computational cost of optimization-based TTA algorithms makes it intractable to run on resource-constrained edge devices. In this paper, we propose an Optimization-Free Test-Time Adaptation (OFTTA) framework for sensor-based HAR. OFTTA adjusts the feature extractor and linear classifier simultaneously in an optimization-free manner. For the feature extractor, we propose Exponential Decay Test-time Normalization (EDTN) to replace the conventional batch normalization (CBN) layers. EDTN combines CBN and Test-time batch Normalization (TBN) to extract reliable features against domain shifts with TBN's influence decreasing exponentially in deeper layers. For the classifier, we adjust the prediction by computing the distance between the feature and the prototype, which is calculated by a maintained support set. In addition, the update of the support set is based on the pseudo label, which can benefit from reliable features extracted by EDTN. Extensive experiments on three public cross-person HAR datasets and two different TTA settings demonstrate that OFTTA outperforms the state-of-the-art TTA approaches in both classification performance and computational efficiency. Finally, we verify the superiority of our proposed OFTTA on edge devices, indicating possible deployment in real applications. Our code is available at https://github.com/Claydon-Wang/OFTTA.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631450"}, {"primary_key": "949303", "vector": [], "sparse_vector": [], "title": "PATCH: A Plug-in Framework of Non-blocking Inference for Distributed Multimodal System.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Wang", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "T<PERSON>xing Li"], "summary": "Recent advancements in deep learning have shown that multimodal inference can be particularly useful in tasks like autonomous driving, human health, and production line monitoring. However, deploying state-of-the-art multimodal models in distributed IoT systems poses unique challenges since the sensor data from low-cost edge devices can get corrupted, lost, or delayed before reaching the cloud. These problems are magnified in the presence of asymmetric data generation rates from different sensor modalities, wireless network dynamics, or unpredictable sensor behavior, leading to either increased latency or degradation in inference accuracy, which could affect the normal operation of the system with severe consequences like human injury or car accident. In this paper, we propose PATCH, a framework of speculative inference to adapt to these complex scenarios. PATCH serves as a plug-in module in the existing multimodal models, and it enables speculative inference of these off-the-shelf deep learning models. PATCH consists of 1) a Masked-AutoEncoder-based cross-modality imputation module to impute missing data using partially-available sensor data, 2) a lightweight feature pair ranking module that effectively limits the searching space for the optimal imputation configuration with low computation overhead, and 3) a data alignment module that aligns multimodal heterogeneous data streams without using accurate timestamp or external synchronization mechanisms. We implement PATCH in nine popular multimodal models using five public datasets and one self-collected dataset. The experimental results show that PATCH achieves up to 13% mean accuracy improvement over the state-of-art method while only using 10% of training data and reducing the training overhead by 73% compared to the original cost of retraining the model.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610885"}, {"primary_key": "949305", "vector": [], "sparse_vector": [], "title": "The Power of Speech in the Wild: Discriminative Power of Daily Voice Diaries in Understanding Auditory Verbal Hallucinations Using Deep Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Subigya Nepal", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Mobile phone sensing is increasingly being used in clinical research studies to assess a variety of mental health conditions (e.g., depression, psychosis). However, in-the-wild speech analysis -- beyond conversation detecting -- is a missing component of these mobile sensing platforms and studies. We augment an existing mobile sensing platform with a daily voice diary to assess and predict the severity of auditory verbal hallucinations (i.e., hearing sounds or voices in the absence of any speaker), a condition that affects people with and without psychiatric or neurological diagnoses. We collect 4809 audio diaries from N=384 subjects over a one-month-long study period. We investigate the performance of various deep-learning architectures using different combinations of sensor behavioral streams (e.g., voice, sleep, mobility, phone usage, etc.) and show the discriminative power of solely using audio recordings of speech as well as automatically generated transcripts of the recordings; specifically, our deep learning model achieves a weighted f-1 score of 0.78 solely from daily voice diaries. Our results surprisingly indicate that a simple periodic voice diary combined with deep learning is sufficient enough of a signal to assess complex psychiatric symptoms (e.g., auditory verbal hallucinations) collected from people in the wild as they go about their daily lives.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610890"}, {"primary_key": "949306", "vector": [], "sparse_vector": [], "title": "ThermoFit: Thermoforming Smart Orthoses via Metamaterial Structures for Body-Fitting and Component-Adjusting.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Junzhe Ji", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Sun"], "summary": "Smart orthoses hold great potential for intelligent rehabilitation monitoring and training. However, most of these electronic assistive devices are typically too difficult for daily use and challenging to modify to accommodate variations in body shape and medical needs. For existing clinicians, the customization pipeline of these smart devices imposes significant learning costs. This paper introduces ThermoFit, an end-to-end design and fabrication pipeline for thermoforming smart orthoses that adheres to the clinically accepted procedure. ThermoFit enables the shapes and electronics positions of smart orthoses to conform to bodies and allows rapid iteration by integrating low-cost Low-Temperature Thermoplastics (LTTPs) with custom metamaterial structures and electronic components. Specifically, three types of metamaterial structures are used in LTTPs to reduce the wrinkles caused by the thermoforming process and to permit component position adjustment and joint movement. A design tool prototype aids in generating metamaterial patterns and optimizing component placement and circuit routing. Three applications show that ThermoFit can be shaped on bodies to different wearables. Finally, a hands-on study with a clinician verifies the user-friendliness of thermoforming smart orthosis, and technical evaluations demonstrate fabrication efficiency and electronic continuity.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580806"}, {"primary_key": "949310", "vector": [], "sparse_vector": [], "title": "SmarCyPad: A Smart Seat Pad for Cycling Fitness Tracking Leveraging Low-cost Conductive Fabric Sensors.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cycling is an efficient and effective way to improve one's overall fitness level, such as cardiovascular fitness, stamina, lower body strength, and body fat percentage. To improve fitness performance, real-time cycling fitness tracking can not only allow cyclists to better control their energy outputs but also help push workout intensity and keep users accountable for their fitness progress. However, existing bike sensors (e.g., the ones mounted to bike's wheel hub or crank arm) are only limited to measuring cycling cadence and speed. Although several recent studies relying on on-body sensors or cameras can provide more fine-grained information (e.g., riding position and knee joint angle), they would either require inconvenient setups or raise serious privacy concerns. To circumvent these limitations, in this paper, we propose SmarCyPad, an innovative smart seat pad that can continuously and unobtrusively track five cycling-specific metrics, including cadence, per-leg stability, leg strength balance, riding position, and knee joint angle of the cyclist. Specifically, we embed conductive fabric sensors in the seat pad to sense the pressure applied to the bike's seat exerted by the cyclist's gluteal muscles. A series of signal processing algorithms are developed to estimate the pedaling period from the sensed pressure signal and further derive the cycling cadence, per-leg stability, and leg strength balance. Additionally, we leverage a deep learning model to detect the cyclist's riding position and reconstruct the cyclist's knee joint angles via linear regression. The sensors and the system prototype are manufactured from scratch leveraging off-the-shelf materials, and the total cost is less than $50. Extensive experiments involving 15 participants demonstrate that SmarCyPad can accurately estimate the cycling cadence with an average error of 1.13 rounds per minute, quantify the cycling stability for each leg, detect cycling imbalance, distinguish five riding positions with an accuracy of 96.60%, and continuously track the knee joint angle with an average mean error as low as 9.58 degrees.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610927"}, {"primary_key": "949311", "vector": [], "sparse_vector": [], "title": "TS2ACT: Few-Shot Human Activity Sensing with Cross-Modal Co-Learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Human Activity Recognition (HAR) based on embedded sensor data has become a popular research topic in ubiquitous computing, which has a wide range of practical applications in various fields such as human-computer interaction, healthcare, and motion tracking. Due to the difficulties of annotating sensing data, unsupervised and semi-supervised HAR methods are extensively studied, but their performance gap to the fully-supervised methods is notable. In this paper, we proposed a novel cross-modal co-learning approach called TS2ACT to achieve few-shot HAR. It introduces a cross-modal dataset augmentation method that uses the semantic-rich label text to search for human activity images to form an augmented dataset consisting of partially-labeled time series and fully-labeled images. Then it adopts a pre-trained CLIP image encoder to jointly train with a time series encoder using contrastive learning, where the time series and images are brought closer in feature space if they belong to the same activity class. For inference, the feature extracted from the input time series is compared with the embedding of a pre-trained CLIP text encoder using prompt learning, and the best match is output as the HAR classification results. We conducted extensive experiments on four public datasets to evaluate the performance of the proposed method. The numerical results show that TS2ACT significantly outperforms the state-of-the-art HAR methods, and it achieves performance close to or better than the fully supervised methods even using as few as 1% labeled data for model training. The source codes of TS2ACT are publicly available on GitHub1.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631445"}, {"primary_key": "949312", "vector": [], "sparse_vector": [], "title": "Reading Between the Heat: Co-Teaching Body Thermal Signatures for Non-intrusive Stress Detection.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Zhang", "Dessa Bergen-Cico", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Stress impacts our physical and mental health as well as our social life. A passive and contactless indoor stress monitoring system can unlock numerous important applications such as workplace productivity assessment, smart homes, and personalized mental health monitoring. While the thermal signatures from a user's body captured by a thermal camera can provide important information about the \"fight-flight\" response of the sympathetic and parasympathetic nervous system, relying solely on thermal imaging for training a stress prediction model often lead to overfitting and consequently a suboptimal performance. This paper addresses this challenge by introducing ThermaStrain, a novel co-teaching framework that achieves high-stress prediction performance by transferring knowledge from the wearable modality to the contactless thermal modality. During training, ThermaStrain incorporates a wearable electrodermal activity (EDA) sensor to generate stress-indicative representations from thermal videos, emulating stress-indicative representations from a wearable EDA sensor. During testing, only thermal sensing is used, and stress-indicative patterns from thermal data and emulated EDA representations are extracted to improve stress assessment. The study collected a comprehensive dataset with thermal video and EDA data under various stress conditions and distances. ThermaStrain achieves an F1 score of 0.8293 in binary stress classification, outperforming the thermal-only baseline approach by over 9%. Extensive evaluations highlight ThermaStrain's effectiveness in recognizing stress-indicative attributes, its adaptability across distances and stress scenarios, real-time executability on edge platforms, its applicability to multi-individual sensing, ability to function on limited visibility and unfamiliar conditions, and the advantages of its co-teaching approach. These evaluations validate ThermaStrain's fidelity and its potential for enhancing stress assessment.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631441"}, {"primary_key": "949314", "vector": [], "sparse_vector": [], "title": "Wall Matters: Rethinking the Effect of Wall for Wireless Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Wireless sensing has demonstrated its potential of utilizing radio frequency (RF) signals to sense individuals and objects. Among different wireless signals, LoRa signal is particularly promising for through-wall sensing owing to its strong penetration capability. However, existing works view walls as a \"bad\" thing as they attenuate signal power and decrease the sensing coverage. In this paper, we show a counter-intuitive observation, i.e., walls can be used to increase the sensing coverage if the RF devices are placed properly with respect to walls. To fully understand the underlying principle behind this observation, we develop a through-wall sensing model to mathematically quantify the effect of walls. We further show that besides increasing the sensing coverage, we can also use the wall to help mitigate interference, which is one well-known issue in wireless sensing. We demonstrate the effect of wall through two representative applications, i.e., macro-level human walking sensing and micro-level human respiration monitoring. Comprehensive experiments show that by properly deploying the transmitter and receiver with respect to the wall, the coverage of human walking detection can be expanded by more than 160%. By leveraging the effect of wall to mitigate interference, we can sense the tiny respiration of target even in the presence of three interferers walking nearby.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631417"}, {"primary_key": "949316", "vector": [], "sparse_vector": [], "title": "HyperTracking: Exploring the Hyperbolic Model for Non-line-of-sight Device-free Wi-Fi Tracking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Wireless sensing technology allows for non-intrusive sensing without the need for physical sensors worn by the target, enabling a wide range of applications, such as indoor tracking, and activity identification. To theoretically reveal the fundamental principles of wireless sensing, the Fresnel zone model has been introduced in the field of Wi-Fi sensing. While the Fresnel zone model is effective in explaining the sensing mechanism in line-of-sight (LoS) scenarios, achieving accurate sensing in non-line-of-sight (NLoS) situations continues to pose a significant challenge. In this paper, we propose a novel theoretical model called the Hyperbolic zone to reveal the fundamental sensing mechanism in NLoS scenarios. The main principle is to eliminate the complex NLoS path shared among different transmitter-receiver pairs, which allows us to obtain a series of simple \"virtual\" reflection paths among receivers. Since these \"virtual\" reflection paths satisfy the properties of the hyperbola, we propose the hyperbolic tracking model. Based on the proposed model, we implement the HyperTracking system using commercial Wi-Fi devices. The experimental results show that the proposed hyperbolic model is suitable for accurate tracking in both LoS and NLoS scenarios. We can reduce 0.36 m tracking error in contrast to the Fresnel zone model in NLoS scenarios. When we utilize the proposed hyperbolic model to train a typical LSTM neural network, we are able to further reduce the tracking error by 0.13 m and save the execution time by 281% with the same data. As a whole, our method can reduce the tracking error by 54% in NLoS scenarios compared with the Fresnel zone model.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631434"}, {"primary_key": "949317", "vector": [], "sparse_vector": [], "title": "DIPA2: An Image Dataset with Cross-cultural Privacy Perception Annotations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The world today is increasingly visual. Many of the most popular online social networking services are largely powered by images, making image privacy protection a critical research topic in the fields of ubiquitous computing, usable security, and human-computer interaction (HCI). One topical issue is understanding privacy-threatening content in images that are shared online. This dataset article introduces DIPA2, an open-sourced image dataset that offers object-level annotations with high-level reasoning properties to show perceptions of privacy among different cultures. DIPA2 provides 5,897 annotations describing perceived privacy risks of 3,347 objects in 1,304 images. The annotations contain the type of the object and four additional privacy metrics: 1) information type indicating what kind of information may leak if the image containing the object is shared, 2) a 7-point Likert item estimating the perceived severity of privacy leakages, and 3) intended recipient scopes when annotators assume they are either image owners or allowing others to repost the image. Our dataset contains unique data from two cultures: We recruited annotators from both Japan and the U.K. to demonstrate the impact of culture on object-level privacy perceptions. In this paper, we first illustrate how we designed and performed the construction of DIPA2, along with data analysis of the collected annotations. Second, we provide two machine-learning baselines to demonstrate how DIPA2 challenges the current image privacy recognition task. DIPA2 facilitates various types of research on image privacy, including machine learning methods inferring privacy threats in complex scenarios, quantitative analysis of cultural influences on privacy preferences, understanding of image sharing behaviors, and promotion of cyber hygiene for general user populations.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631439"}, {"primary_key": "949318", "vector": [], "sparse_vector": [], "title": "Thermal Earring: Low-power Wireless Earring for Longitudinal Earlobe Temperature Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Masta<PERSON>ton", "<PERSON><PERSON><PERSON>", "Shwetak N. Patel"], "summary": "Body temperature is an important vital sign which can indicate fever and is known to be correlated with activities such as eating, exercise and stress. However, continuous temperature monitoring poses a significant challenge. We present Thermal Earring, a first-of-its-kind smart earring that enables a reliable wearable solution for continuous temperature monitoring. The Thermal Earring takes advantage of the unique position of earrings in proximity to the head, a region with tight coupling to the body unlike watches and other wearables which are more loosely worn on extremities. We develop a hardware prototype in the form factor of real earrings measuring a maximum width of 11.3 mm and a length of 31 mm, weighing 335 mg, and consuming only 14.4 uW which enables a battery life of 28 days in real-world tests. We demonstrate this form factor is small and light enough to integrate into real jewelry with fashionable designs. Additionally, we develop a dual sensor design to differentiate human body temperature change from environmental changes. We explore the use of this novel sensing platform and find its measured earlobe temperatures are stable within ±0.32 °C during periods of rest. Using these promising results, we investigate its capability of detecting fever by gathering data from 5 febrile patients and 20 healthy participants. Further, we perform the first-ever investigation of the relationship between earlobe temperature and a variety of daily activities, demonstrating earlobe temperature changes related to eating and exercise. We also find the surprising result that acute stressors such as public speaking and exams cause measurable changes in earlobe temperature. We perform multi-day in-the-wild experiments and confirm the temperature changes caused by these daily activities in natural daily scenarios. This initial exploration seeks to provide a foundation for future automatic activity detection and earring-based wearables.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631440"}, {"primary_key": "949319", "vector": [], "sparse_vector": [], "title": "Echo: Reverberation-based Fast Black-Box Adversarial Attacks on Intelligent Audio Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Routing <PERSON>"], "summary": "Intelligent audio systems are ubiquitous in our lives, such as speech command recognition and speaker recognition. However, it is shown that deep learning-based intelligent audio systems are vulnerable to adversarial attacks. In this paper, we propose a physical adversarial attack that exploits reverberation, a natural indoor acoustic effect, to realize imperceptible, fast, and targeted black-box attacks. Unlike existing attacks that constrain the magnitude of adversarial perturbations within a fixed radius, we generate reverberation-alike perturbations that blend naturally with the original voice sample 1. Additionally, we can generate more robust adversarial examples even under over-the-air propagation by considering distortions in the physical environment. Extensive experiments are conducted using two popular intelligent audio systems in various situations, such as different room sizes, distance, and ambient noises. The results show that Echo can invade into intelligent audio systems in both digital and physical over-the-air environment.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610874"}, {"primary_key": "949320", "vector": [], "sparse_vector": [], "title": "SDE: Early Screening for Dry Eye Disease with Wireless Signals.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Shengkang Gu", "<PERSON><PERSON>", "<PERSON><PERSON>", "Changzhen<PERSON> Chen"], "summary": "Early screening for dry eye disease (DED) is crucial to identify and provide timely intervention to high-risk susceptible populations. Currently, clinical methods for diagnosing DED include the tear break-up time test, meibomian gland analysis, tear osmolarity test, and tear river height test, which require in-hospital detection. Unfortunately, there is no convenient way to screen for DED yet. In this paper, we propose SDE, a contactless, convenient, and ubiquitous DED screening system based on RF signals. To extract biomarkers for early screening of DED from RF signals, we construct frame chirps variance and extract fine-grained spontaneous blinking action. SDE is carefully designed to remove interference in RF signals and refine the characterization of biomarkers that denote the symptoms of DED. To endow SDE with the ability to adapt to new users, we develop a deep learning-based model of unsupervised domain adaptation to remove the influence of different users and environments in local and global two-level feature spaces. We conduct extensive experiments to evaluate SDE with 54 volunteers in 4 scenes. The experimental results confirm that SDE can accurately screen for DED in a new user in real environments such as eye examination rooms, clinics, offices, and homes.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631438"}, {"primary_key": "949321", "vector": [], "sparse_vector": [], "title": "Wi-Painter: Fine-grained Material Identification and Image Delineation Using COTS WiFi Devices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "WiFi has gradually developed into one of the main candidate technologies for indoor environment sensing. In this paper, we are interested in using COTS WiFi devices to identify material details, including location, material type, and shape, of stationary objects in the surrounding environment, which may open up new opportunities for many applications. Specifically, we present Wi-Painter, a model-driven system that can accurately detects smooth-surfaced material types and their edges using COTS WiFi devices without modification. Different from previous arts for material identification, Wi-Painter subdivides the target into individual 2D pixels, and simultaneously forms a 2D image based on identifying the material type of each pixel. The key idea of Wi-Painter is to exploit the complex permittivity of the object surface which can be estimated by the different reflectivity of signals with different polarization directions. In particular, we construct the multi-incident angle model to characterize the material, using only the power ratios of the vertically and horizontally polarized signals measured at several different incident angles, which avoids the use of inaccurate WiFi signal phases. We implement and evaluate Wi-Painter in the real world, showing an average classification accuracy of 93.4% for different material types including metal, wood, rubber and plastic of different sizes and thicknesses, and across different environments. In addition, Wi-Painter can accurately detect the material type and edge of the word \"LOVE\" spliced with different materials, with an average size of 60cm × 80cm, and material edges with different orientations.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3633809"}, {"primary_key": "949323", "vector": [], "sparse_vector": [], "title": "Soil-Powered Computing: The Engineer&apos;s Guide to Practical Soil Microbial Fuel Cell Design.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Philoth<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Human-caused climate degradation and the explosion of electronic waste have pushed the computing community to explore fundamental alternatives to the current battery-powered, over-provisioned ubiquitous computing devices that need constant replacement and recharging. Soil Microbial Fuel Cells (SMFCs) offer promise as a renewable energy source that is biocompatible and viable in difficult environments where traditional batteries and solar panels fall short. However, SMFC development is in its infancy, and challenges like robustness to environmental factors and low power output stymie efforts to implement real-world applications in terrestrial environments. This work details a 2-year iterative process that uncovers barriers to practical SMFC design for powering electronics, which we address through a mechanistic understanding of SMFC theory from the literature. We present nine months of deployment data gathered from four SMFC experiments exploring cell geometries, resulting in an improved SMFC that generates power across a wider soil moisture range. From these experiments, we extracted key lessons and a testing framework, assessed SMFC's field performance, contextualized improvements with emerging and existing computing systems, and demonstrated the improved SMFC powering a wireless sensor for soil moisture and touch sensing. We contribute our data, methodology, and designs to establish the foundation for a sustainable, soil-powered future.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631410"}, {"primary_key": "949324", "vector": [], "sparse_vector": [], "title": "Uncovering Bias in Personal Informatics.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> Vakali", "<PERSON>-<PERSON>"], "summary": "Personal informatics (PI) systems, powered by smartphones and wearables, enable people to lead healthier lifestyles by providing meaningful and actionable insights that break down barriers between users and their health information. Today, such systems are used by billions of users for monitoring not only physical activity and sleep but also vital signs and women's and heart health, among others. Despite their widespread usage, the processing of sensitive PI data may suffer from biases, which may entail practical and ethical implications. In this work, we present the first comprehensive empirical and analytical study of bias in PI systems, including biases in raw data and in the entire machine learning life cycle. We use the most detailed framework to date for exploring the different sources of bias and find that biases exist both in the data generation and the model learning and implementation streams. According to our results, the most affected minority groups are users with health issues, such as diabetes, joint issues, and hypertension, and female users, whose data biases are propagated or even amplified by learning models, while intersectional biases can also be observed.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610914"}, {"primary_key": "949325", "vector": [], "sparse_vector": [], "title": "From 2D to 3D: Facilitating Single-Finger Mid-Air Typing on QWERTY Keyboards with Probabilistic Touch Modeling.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mid-air text entry on virtual keyboards suffers from the lack of tactile feedback, which brings challenges to both tap detection and input prediction. In this paper, we explored the feasibility of single-finger typing on virtual QWERTY keyboards in mid-air. We first conducted a study to examine users' 3D typing behavior on different sizes of virtual keyboards. Results showed that the participants perceived the vertical projection of the lowest point on the keyboard during a tap as the target location and inferring taps based on the intersection between the finger and the keyboard was not applicable. Aiming at this challenge, we derived a novel input prediction algorithm that took the uncertainty in tap detection into a calculation as probability, and performed probabilistic decoding that could tolerate false detection. We analyzed the performance of the algorithm through a full-factorial simulation. Results showed that the SVM-based probabilistic touch detection together with a 2D elastic probabilistic decoding algorithm (elasticity = 2) could achieve the optimal top-5 accuracy of 94.2%. In the evaluation user study, the participants reached a single-finger typing speed of 26.1 WPM with 3.2% uncorrected word-level error rate, which was significantly better than both tap-based and gesture-based baseline techniques. Also, the proposed technique received the highest preference score from the users, proving its usability in real text entry tasks.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580829"}, {"primary_key": "949326", "vector": [], "sparse_vector": [], "title": "Enabling <PERSON><PERSON><PERSON><PERSON>sing on New-generation WiFi Cards.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "The last few years have witnessed the rapid development of WiFi sensing with a large spectrum of applications enabled. However, existing works mainly leverage the obsolete 802.11n WiFi cards (i.e., Intel 5300 and Atheros AR9k series cards) for sensing. On the other hand, the mainstream WiFi protocols currently in use are 802.11ac/ax and commodity WiFi products on the market are equipped with new-generation WiFi chips such as Broadcom BCM43794 and Qualcomm QCN5054. After conducting some benchmark experiments, we find that WiFi sensing has problems working on these new cards. The new communication features (e.g., MU-MIMO) designed to facilitate data transmissions negatively impact WiFi sensing. Conventional CSI base signals such as CSI amplitude and/or CSI phase difference between antennas which worked well on Intel 5300 802.11n WiFi card may fail on new cards. In this paper, we propose delicate signal processing schemes to make wireless sensing work well on these new WiFi cards. We employ two typical sensing applications, i.e., human respiration monitoring and human trajectory tracking to demonstrate the effectiveness of the proposed schemes. We believe it is critical to ensure WiFi sensing compatible with the latest WiFi protocols and this work moves one important step towards real-life adoption of WiFi sensing.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3633807"}, {"primary_key": "949327", "vector": [], "sparse_vector": [], "title": "PrintShear: Shear Input Based on Fingerprint Deformation.", "authors": ["<PERSON><PERSON>", "Jianjiang Feng", "<PERSON><PERSON>"], "summary": "Most touch-based input devices, such as touchscreens and touchpads, capture low-resolution capacitive images when a finger touches the device's surface. These devices only output the two-dimensional (2D) positions of contacting points, which are insufficient for complex control tasks, such as the manipulation of 3D objects. To expand the modalities of touch inputs, researchers have proposed a variety of techniques, including finger poses, chording gestures, touch pressure, etc. With the rapid development of fingerprint sensing technology, especially under-screen fingerprint sensors, it has become possible to generate input commands to control multiple degrees of freedom (DOF) at a time using fingerprint images. In this paper, we propose PrintShear, a shear input technique based on fingerprint deformation. Lateral, longitudinal and rotational deformations are extracted from fingerprint images and mapped to 3DOF control commands. Further DOF expansion can be achieved through recognition of the contact region of the touching finger. We conducted a 12-person user study to evaluate the performance of PrintShear on 3D docking tasks. Comparisons with other input methods demonstrated the superiority of our approach. Specifically, a 19.79% reduction in completion time was achieved compared with conventional touch input in a full 6DOF 3D object manipulation task.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3596257"}, {"primary_key": "949329", "vector": [], "sparse_vector": [], "title": "A User-Centric Evaluation of Smart Home Resolution Approaches for Conflicts Between Routines.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the increasing adoption of smart home devices, users rely on device automation to control their homes. This automation commonly comes in the form of smart home routines, an abstraction available via major vendors. Yet, questions remain about how a system should best handle conflicts in which different routines access the same devices simultaneously. In particular---among the myriad ways a smart home system could handle conflicts, which of them are currently utilized by existing systems, and which ones result in the highest user satisfaction? We investigate the first question via a survey of existing literature and find a set of conditions, modifications, and system strategies related to handling conflicts. We answer the second question via a scenario-based Mechanical-Turk survey of users interested in owning smart home devices and current smart home device owners (N=197). We find that: (i) there is no context-agnostic strategy that always results in high user satisfaction, and (ii) users' personal values frequently form the basis for shaping their expectations of how routines should execute.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3581997"}, {"primary_key": "949330", "vector": [], "sparse_vector": [], "title": "PyroSense: 3D Posture Reconstruction Using Pyroelectric Infrared Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "Gen Li", "T<PERSON>xing Li"], "summary": "We present PyroSense, the first-of-its-kind system that enables fine-grained 3D posture reconstruction using ubiquitous COTS passive infrared sensor (PIR sensor). PyroSense senses heat signals generated by the human body and airflow due to body movement to reconstruct the corresponding human postures in real time. PyroSense greatly advances the prior PIR-based sensing design by improving the sensitivity of COTS PIR sensor to body movement, increasing spatial resolution without additional deployment overhead, and designing intellectual algorithms to adapt to diverse environmental factors. We build a low-cost PyroSense prototype using off-the-shelf hardware components. The experimental findings indicate that PyroSense not only attains a classification accuracy of 99.46% across 15 classes, but it also registers a mean joint distance error of less than 16 cm for 14 body joints for posture reconstruction in challenging environments.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631435"}, {"primary_key": "949331", "vector": [], "sparse_vector": [], "title": "mSilent: Towards General Corpus Silent Speech Recognition Using COTS mmWave Radar.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Shu<PERSON>", "<PERSON>"], "summary": "Silent speech recognition (SSR) allows users to speak to the device without making a sound, avoiding being overheard or disturbing others. Compared to the video-based approach, wireless signal-based SSR can work when the user is wearing a mask and has fewer privacy concerns. However, previous wireless-based systems are still far from well-studied, e.g. they are only evaluated in corpus with highly limited size, making them only feasible for interaction with dozens of deterministic commands. In this paper, we present mSilent, a millimeter-wave (mmWave) based SSR system that can work in the general corpus containing thousands of daily conversation sentences. With the strong recognition capability, mSilent not only supports the more complex interaction with assistants, but also enables more general applications in daily life such as communication and input. To extract fine-grained articulatory features, we build a signal processing pipeline that uses a clustering-selection algorithm to separate articulatory gestures and generates a multi-scale detrended spectrogram (MSDS). To handle the complexity of the general corpus, we design an end-to-end deep neural network that consists of a multi-branch convolutional front-end and a Transformer-based sequence-to-sequence back-end. We collect a general corpus dataset of 1,000 daily conversation sentences that contains 21K samples of bi-modality data (mmWave and video). Our evaluation shows that mSilent achieves a 9.5% average word error rate (WER) at a distance of 1.5m, which is comparable to the performance of the state-of-the-art video-based approach. We also explore deploying mSilent in two typical scenarios of text entry and in-car assistant, and the less than 6% average WER demonstrates the potential of mSilent in general daily applications.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580838"}, {"primary_key": "949333", "vector": [], "sparse_vector": [], "title": "SIDA: Self-Supervised Imbalanced Domain Adaptation for Sound Enhancement and Cross-Domain WiFi Sensing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The coronavirus disease 2019 (COVID-19) pneumonia still persists and its chief complaint is dry cough. Physicians design wireless stethoscopes to facilitate diagnosis, however, lung sounds could be easily interfered with by external noises. To achieve lung sound enhancement, prior researches mostly assume the amount of clean and noisy data are the same. This assumption is hardly met due to extensive labor effort for data collection and annotation. The data imbalance across domains widely happens in real-world IoT systems, e.g. sound enhancement and WiFi-based human sensing. In this paper, we propose SIDA, a self-supervised imbalanced domain adaptation framework for sound enhancement and WiFi sensing, which makes it a generic time series domain adaptation solution for IoT systems. SIDA proposes a self-supervised imbalanced domain adaptation model that separately learns the representation of time series signals in a minority domain with limited samples, a majority domain with rich samples, and their mapping relations. For lung sound enhancement, we further proposes a phase correction model to sanitize the phase and a SNR prediction algorithm to recursively perform domain adaptation in an imbalanced noisy and clean lung sound dataset. Extensive experiments demonstrate SIDA increases noisy samples' SNR by 16.49dB and 4.06dB on a synthetic and a realistic imbalanced lung sound dataset, respectively. For WiFi-based human sensing, SIDA designs a cross-domain WiFi-based human identification model irrespective of walking trajectory. A specific trajectory where a group of people walks along in a realistic testing environment is considered the minority domain, and several other trajectories are stored at a server as the majority domain. Extensive experiments show SIDA could recognize individuals with an average accuracy of 94.72% and significantly outperform baselines on highly imbalanced WiFi dataset in cross-domain human identification tasks.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610919"}, {"primary_key": "949334", "vector": [], "sparse_vector": [], "title": "Contact Tracing for Healthcare Workers in an Intensive Care Unit.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Victoria J<PERSON>", "<PERSON>", "Chenyang Lu"], "summary": "Contact tracing is a powerful tool for mitigating the spread of COVID-19 during the pandemic. Front-line healthcare workers are particularly at high risk of infection in hospital units. This paper presents ContAct TraCing for Hospitals (CATCH), an automated contact tracing system designed specifically for healthcare workers in hospital environments. CATCH employs distributed embedded devices placed throughout a hospital unit to detect close contacts among healthcare workers wearing Bluetooth Low Energy (BLE) beacons. We first identify a set of distinct contact tracing scenarios based on the diverse environmental characteristics of a real-world intensive care unit (ICU) and the different working patterns of healthcare workers in different spaces within the unit. We then develop a suite of novel contact tracing methods tailored for each scenario. CATCH has been deployed and evaluated in the ICU of a major medical center, demonstrating superior accuracy in contact tracing over existing approaches through a wide range of experiments. Furthermore, the real-world case study highlights the effectiveness and efficiency of CATCH compared to standard contact tracing practices.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610924"}, {"primary_key": "949335", "vector": [], "sparse_vector": [], "title": "LT-Fall: The Design and Implementation of a Life-threatening Fall Detection and Alarming System.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "Falls are the leading cause of fatal injuries to elders in modern society, which has motivated researchers to propose various fall detection technologies. We observe that most of the existing fall detection solutions are diverging from the purpose of fall detection: timely alarming the family members, medical staff or first responders to save the life of the human with severe injury caused by fall. Instead, they focus on detecting the behavior of human falls, which does not necessarily mean a human is in real danger. The real critical situation is when a human cannot get up without assistance and is thus lying on the ground after the fall because of losing consciousness or becoming incapacitated due to severe injury. In this paper, we define a life-threatening fall as a behavior that involves a falling down followed by a long-lie of humans on the ground, and for the first time point out that a fall detection system should focus on detecting life-threatening falls instead of detecting any random falls. Accordingly, we design and implement LT-Fall, a mmWave-based life-threatening fall detection and alarming system. LT-Fall detects and reports both fall and fall-like behaviors in the first stage and then identifies life-threatening falls by continuously monitoring the human status after fall in the second stage. We propose a joint spatio-temporal localization technique to detect and locate the micro-motions of the human, which solves the challenge of mmWave's insufficient spatial resolution when the human is static, i.e., lying on the ground. Extensive evaluation on 15 volunteers demonstrates that compared to the state-of-the-art work (92% precision and 94% recall), LT-Fall achieves zero false alarms as well as a precision of 100% and a recall of 98.8%.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580835"}, {"primary_key": "949336", "vector": [], "sparse_vector": [], "title": "RLoc: Towards Robust Indoor Localization by Quantifying Uncertainty.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Sun", "<PERSON>"], "summary": "In recent years, decimeter-level accuracy in WiFi indoor localization has become attainable within controlled environments. However, existing methods encounter challenges in maintaining robustness in more complex indoor environments: angle-based methods are compromised by the significant localization errors due to unreliable Angle of Arrival (AoA) estimations, and fingerprint-based methods suffer from performance degradation due to environmental changes. In this paper, we propose RLoc, a learning-based system designed for reliable localization and tracking. The key design principle of RLoc lies in quantifying the uncertainty level arises in the AoA estimation task and then exploiting the uncertainty to enhance the reliability of localization and tracking. To this end, RLoc first manually extracts the underutilized beamwidth feature via signal processing techniques. Then, it integrates the uncertainty quantification into neural network design through Kullback-Leibler (KL) divergence loss and ensemble techniques. Finally, these quantified uncertainties guide RLoc to optimally leverage the diversity of Access Points (APs) and the temporal continuous information of AoAs. Our experiments, evaluating on two datasets gathered from commercial off-the-shelf WiFi devices, demonstrate that RLoc surpasses state-of-the-art approaches by an average of 36.27% in in-domain scenarios and 20.40% in cross-domain scenarios.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631437"}, {"primary_key": "949337", "vector": [], "sparse_vector": [], "title": "Waffle: A Waterproof mmWave-based Human Sensing System inside Bathrooms with Running Water.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> Zhang"], "summary": "The bathroom has consistently ranked among the most perilous rooms in households, with slip and fall incidents during showers posing a critical threat, particularly to the elders. To address this concern while ensuring privacy and accuracy, the mmWave-based sensing system has emerged as a promising solution. Capable of precisely detecting human activities and promptly triggering alarms in response to critical events, it has proved especially valuable within bathroom environments. However, deploying such a system in bathrooms faces a significant challenge: interference from running water. Similar to the human body, water droplets reflect substantial mmWave signals, presenting a major obstacle to accurate sensing. Through rigorous empirical study, we confirm that the interference caused by running water adheres to a Weibull distribution, offering insight into its behavior. Leveraging this understanding, we propose a customized Constant False Alarm Rate (CFAR) detector, specifically tailored to handle the interference from running water. This innovative detector effectively isolates human-generated signals, thus enabling accurate human detection even in the presence of running water interference. Our implementation of \"Waffle\" on a commercial off-the-shelf mmWave radar demonstrates exceptional sensing performance. It achieves median errors of 1.8cm and 6.9cm for human height estimation and tracking, respectively, even in the presence of running water. Furthermore, our fall detection system, built upon this technique, achieves remarkable performance (a recall of 97.2% and an accuracy of 97.8%), surpassing the state-of-the-art method.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631458"}, {"primary_key": "949338", "vector": [], "sparse_vector": [], "title": "LoCal: An Automatic Location Attribute Calibration Approach for Large-Scale Deployment of mmWave-based Sensing Systems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Zhang"], "summary": "Millimeter wave (mmWave) radar excels in accurately estimating the distance, speed, and angle of the signal reflectors relative to the radar. However, for diverse sensing applications reliant on radar's tracking capability, these estimates must be transformed from radar to room coordinates. This transformation hinges on the mmWave radar's location attribute, encompassing its position and orientation in room coordinates. Traditional outdoor calibration solutions for autonomous driving utilize corner reflectors as static reference points to derive the location attribute. When deployed in the indoor environment, it is challenging, even for the mmWave radar with GHz bandwidth and a large antenna array, to separate the static reference points from other multipath reflectors. To tackle the static multipath, we propose to deploy a moving reference point (a moving robot) to fully harness the velocity resolution of mmWave radar. Specifically, we select a SLAM-capable robot to accurately obtain its locations under room coordinates during motion, without requiring human intervention. Accurately pairing the locations of the robot under two coordinate systems requires tight synchronization between the mmWave radar and the robot. We therefore propose a novel trajectory correspondence based calibration algorithm that takes the estimated trajectories of two systems as input, decoupling the operations of two systems to the maximum. Extensive experimental results demonstrate that the proposed calibration solution exhibits very high accuracy (1.74 cm and 0.43° accuracy for location and orientation respectively) and could ensure outstanding performance in three representative applications: fall detection, point cloud fusion, and long-distance human tracking.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631436"}, {"primary_key": "949339", "vector": [], "sparse_vector": [], "title": "Radio2Text: Streaming Speech Recognition Using mmWave Radio Signals.", "authors": ["Running <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Millimeter wave (mmWave) based speech recognition provides more possibility for audio-related applications, such as conference speech transcription and eavesdropping. However, considering the practicality in real scenarios, latency and recognizable vocabulary size are two critical factors that cannot be overlooked. In this paper, we propose Radio2Text, the first mmWave-based system for streaming automatic speech recognition (ASR) with a vocabulary size exceeding 13,000 words. Radio2Text is based on a tailored streaming Transformer that is capable of effectively learning representations of speech-related features, paving the way for streaming ASR with a large vocabulary. To alleviate the deficiency of streaming networks unable to access entire future inputs, we propose the Guidance Initialization that facilitates the transfer of feature knowledge related to the global context from the non-streaming Transformer to the tailored streaming Transformer through weight inheritance. Further, we propose a cross-modal structure based on knowledge distillation (KD), named cross-modal KD, to mitigate the negative effect of low quality mmWave signals on recognition performance. In the cross-modal KD, the audio streaming Transformer provides feature and response guidance that inherit fruitful and accurate speech information to supervise the training of the tailored radio streaming Transformer. The experimental results show that our Radio2Text can achieve a character error rate of 5.7% and a word error rate of 9.4% for the recognition of a vocabulary consisting of over 13,000 words.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610873"}, {"primary_key": "949341", "vector": [], "sparse_vector": [], "title": "3D Deformation Capture via a Configurable Self-Sensing IMU Sensor Network.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Yinyu Lu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Sun", "<PERSON><PERSON><PERSON>"], "summary": "Motion capture technologies reconstruct human movements and have wide-ranging applications. Mainstream research on motion capture can be divided into vision-based methods and inertial measurement unit (IMU)-based methods. The vision-based methods capture complex 3D geometrical deformations with high accuracy, but they rely on expensive optical equipment and suffer from the line-of-sight occlusion problem. IMU-based methods are lightweight but hard to capture fine-grained 3D deformations. In this work, we present a configurable self-sensing IMU sensor network to bridge the gap between the vision-based and IMU-based methods. To achieve this, we propose a novel kinematic chain model based on the four-bar linkage to describe the minimum deformation process of 3D deformations. We also introduce three geometric priors, obtained from the initial shape, material properties and motion features, to assist the kinematic chain model in reconstructing deformations and overcome the data sparsity problem. Additionally, to further enhance the accuracy of deformation capture, we propose a fabrication method to customize 3D sensor networks for different objects. We introduce origami-inspired thinking to achieve the customization process, which constructs 3D sensor networks through a 3D-2D-3D digital-physical transition. The experimental results demonstrate that our method achieves comparable performance with state-of-the-art methods.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580874"}, {"primary_key": "949342", "vector": [], "sparse_vector": [], "title": "MoCaPose: Motion Capturing with Textile-integrated Capacitive Sensors in Loose-fitting Smart Garments.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Sizhen Bian", "Gesche Joost", "<PERSON>"], "summary": "We present MoCaPose, a novel wearable motion capturing (MoCap) approach to continuously track the wearer's upper body's dynamic poses through multi-channel capacitive sensing integrated in fashionable, loose-fitting jackets. Unlike conventional wearable IMU MoCap based on inverse dynamics, MoCaPose decouples the sensor position from the pose system. MoCaPose uses a deep regressor to continuously predict the 3D upper body joints coordinates from 16-channel textile capacitive sensors, unbound by specific applications. The concept is implemented through two prototyping iterations to first solve the technical challenges, then establish the textile integration through fashion-technology co-design towards a design-centric smart garment. A 38-hour dataset of synchronized video and capacitive data from 21 participants was recorded for validation. The motion tracking result was validated on multiple levels from statistics (R2 ~ 0.91) and motion tracking metrics (MP JPE ~ 86mm) to the usability in pose and motion recognition (0.9 F1 for 10-class classification with unsupervised class discovery). The design guidelines impose few technical constraints, allowing the wearable system to be design-centric and usecase-specific. Overall, MoCaPose demonstrates that textile-based capacitive sensing with its unique advantages, can be a promising alternative for wearable motion tracking and other relevant wearable motion recognition applications.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580883"}, {"primary_key": "949343", "vector": [], "sparse_vector": [], "title": "Reflected Reality: Augmented Reality through the Mirror.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose Reflected Reality: a new dimension for augmented reality that expands the augmented physical space into mirror reflections. By synchronously tracking the physical space in front of the mirror and the reflection behind it using an AR headset and an optional smart mirror component, reflected reality enables novel AR interactions that allow users to use their physical and reflected bodies to find and interact with virtual objects. We propose a design space for AR interaction with mirror reflections, and instantiate it using a prototype system featuring a HoloLens 2 and a smart mirror. We explore the design space along the following dimensions: the user's perspective of input, the spatial frame of reference, and the direction of the mirror space relative to the physical space. Using our prototype, we visualise a use case scenario that traverses the design space to demonstrate its interaction affordances in a practical context. To understand how users perceive the intuitiveness and ease of reflected reality interaction, we conducted an exploratory and a formal user evaluation studies to characterise user performance of AR interaction tasks in reflected reality. We discuss the unique interaction affordances that reflected reality offers, and outline possibilities of its future applications.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3631431"}, {"primary_key": "949344", "vector": [], "sparse_vector": [], "title": "Integrating Gaze and Mouse Via Joint Cross-Attention Fusion Net for Students&apos; Activity Recognition in E-learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "E-learning has emerged as an indispensable educational mode in the post-epidemic era. However, this mode makes it difficult for students to stay engaged in learning without appropriate activity monitoring. Our work explores a promising solution that combines gaze and mouse data to recognize students' activities, thereby facilitating activity monitoring and analysis during e-learning. We initially surveyed 200 students from a local university, finding more acceptance for eye trackers and mouse loggers compared to video surveillance. We then designed eight students' routine digital activities to collect a multimodal dataset and analyze the patterns and correlations between gaze and mouse across various activities. Our proposed Joint Cross-Attention Fusion Net, a multimodal activity recognition framework, leverages the gaze-mouse relationship to yield improved classification performance by integrating cross-modal representations through a cross-attention mechanism and integrating the joint features that characterize gaze-mouse coordination. Evaluation results show that our method can achieve up to 94.87% F1-score in predicting 8-classes activities, with an improvement of at least 7.44% over using gaze or mouse data independently. This research illuminates new possibilities for monitoring student engagement in intelligent education systems, also suggesting a promising strategy for melding perception and action modalities in behavioral analysis across a range of ubiquitous computing environments.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610876"}, {"primary_key": "949345", "vector": [], "sparse_vector": [], "title": "Combining Smart Speaker and Smart Meter to Infer Your Residential Power Usage by Self-supervised Cross-modal Learning.", "authors": ["Guanzhou Zhu", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Huadong Ma"], "summary": "Energy disaggregation is a key enabling technology for residential power usage monitoring, which benefits various applications such as carbon emission monitoring and human activity recognition. However, existing methods are difficult to balance the accuracy and usage burden (device costs, data labeling and prior knowledge). As the high penetration of smart speakers offers a low-cost way for sound-assisted residential power usage monitoring, this work aims to combine a smart speaker and a smart meter in a house to liberate the system from a high usage burden. However, it is still challenging to extract and leverage the consistent/complementary information (two types of relationships between acoustic and power features) from acoustic and power data without data labeling or prior knowledge. To this end, we design COMFORT, a cross-modality system for self-supervised power usage monitoring, including (i) a cross-modality learning component to automatically learn the consistent and complementary information, and (ii) a cross-modality inference component to utilize the consistent and complementary information. We implement and evaluate COMFORT with a self-collected dataset from six houses in 14 days, demonstrating that COMFORT finds the most appliances (98%), improves the appliance recognition performance in F-measure by at least 41.1%, and reduces the Mean Absolute Error (MAE) of energy disaggregation by at least 30.4% over other alternative solutions.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3610905"}, {"primary_key": "949346", "vector": [], "sparse_vector": [], "title": "IoTBeholder: A Privacy Snooping Attack on User Habitual Behaviors from Smart Home Wi-Fi Traffic.", "authors": ["Qingsong Zou", "Qing Li", "Ruoyu Li", "<PERSON><PERSON> Huang", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "With the deployment of a growing number of smart home IoT devices, privacy leakage has become a growing concern. Prior work on privacy-invasive device localization, classification, and activity identification have proven the existence of various privacy leakage risks in smart home environments. However, they only demonstrate limited threats in real world due to many impractical assumptions, such as having privileged access to the user's home network. In this paper, we identify a new end-to-end attack surface using IoTBeholder, a system that performs device localization, classification, and user activity identification. IoTBeholder can be easily run and replicated on commercial off-the-shelf (COTS) devices such as mobile phones or personal computers, enabling attackers to infer user's habitual behaviors from smart home Wi-Fi traffic alone. We set up a testbed with 23 IoT devices for evaluation in the real world. The result shows that IoTBeholder has good device classification and device activity identification performance. In addition, IoTBeholder can infer the users' habitual behaviors and automation rules with high accuracy and interpretability. It can even accurately predict the users' future actions, highlighting a significant threat to user privacy that IoT vendors and users should highly concern.", "published": "2023-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3580890"}]