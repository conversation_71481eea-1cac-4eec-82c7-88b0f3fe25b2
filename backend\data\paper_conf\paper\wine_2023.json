[{"primary_key": "1271842", "vector": [], "sparse_vector": [], "title": "Partial Allocations in Budget-Feasible Mechanism Design: Bridging Multiple Levels of Service and Divisible Agents.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Budget-feasible procurement has been a major paradigm in mechanism design since its introduction by <PERSON> [24]. An auctioneer (buyer) with a strict budget constraint is interested in buying goods or services from a group of strategic agents (sellers). In many scenarios it makes sense to allow the auctioneer to only partially buy what an agent offers, e.g., an agent might have multiple copies of an item to sell, they might offer multiple levels of a service, or they may be available to perform a task for any fraction of a specified time interval. Nevertheless, the focus of the related literature has been on settings where each agent’s services are either fully acquired or not at all. A reason for this is that in settings with partial allocations, without any assumptions on the costs, there are strong inapproximability results (see, e.g., <PERSON> and <PERSON> [10], <PERSON><PERSON> et al. [5]). Under the mild assumption of being able to afford each agent entirely, we are able to circumvent such results. We design a polynomial-time, deterministic, truthful, budget-feasible,\\((2+\\sqrt{3})\\)-approximation mechanism for the setting where each agent offers multiple levels of service and the auctioneer has a discrete separable concave valuation function. We then use this result to design a deterministic, truthful and budget-feasible mechanism for the setting where any fraction of a service can be acquired and the auctioneer’s valuation function is separable concave (i.e., the sum of concave functions). The approximation ratio of this mechanism depends on how “nice” the concave functions are, and isO(1) for valuation functions that are sums ofO(1)-regular functions (e.g., functions like\\(\\log (1+x)\\)). For the special case of a linear valuation function, we improve the best known approximation ratio from\\(1+\\phi \\)(by Klumper and Schäfer [17]) to 2. This establishes a separation result between this setting and its indivisible counterpart.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_3"}, {"primary_key": "1271843", "vector": [], "sparse_vector": [], "title": "Stable Dinner Party Seating Arrangements.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A group ofnagents with numerical preferences for each other are to be assigned to thenseats of a dining table. We study two natural topologies: circular (cycle) tables and panel (path) tables. For a given seating arrangement, an agent’s utility is the sum of their preference values towards their (at most two) direct neighbors. An arrangement is envy-free if no agent strictly prefers someone else’s seat, and it is stable if no two agents strictly prefer each other’s seats. Recently, it was shown that for both paths and cycles it is NP-hard to decide whether an envy-free arrangement exists, even for symmetric binary preferences. In contrast, we show that, if agents come from a bounded number of classes, the problem is solvable in polynomial time for arbitrarily-valued possibly asymmetric preferences, including outputting an arrangement if possible. We also give simpler proofs of the previous hardness results if preferences are allowed to be asymmetric. For stability, it is known that deciding the existence of stable arrangements is NP-hard for both topologies, but only if sufficiently-many numerical values are allowed. As it turns out, even constructing unstable instances can be challenging in certain cases, e.g., binary values. We propose a complete characterization of the existence of stable arrangements based on the number of distinct values in the preference matrix and the number of agent classes. We also ask the same question for non-negative values and give an almost-complete characterization, the most interesting outstanding case being that of paths with two-valued non-negative preferences, for which we experimentally find that stable arrangements always exist and prove it under the additional constraint that agents can only swap seats when sitting at most two positions away. Similarly to envy-freeness, we also give a polynomial-time algorithm for determining a stable arrangement assuming a bounded number of classes. We moreover consider the swap dynamics and exhibit instances where they do not converge, despite a stable arrangement existing.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_1"}, {"primary_key": "1271844", "vector": [], "sparse_vector": [], "title": "High-Welfare Matching Markets via Descending Price.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We consider the design of monetary mechanisms for two-sided matching. Mechanisms in the tradition of the deferred acceptance algorithm, even in variants incorporating money, tend to focus on the criterion of stability. In this work, instead, we seek a simple auction-inspired mechanism with social welfare guarantees. We consider a descending-price mechanism called the Marshallian Match, proposed (but not analyzed) by [22]. When all values for potential matches are positive, we show the Marshallian Match with a “rebate” payment rule achieves constant price of anarchy. This result extends to models with costs for acquiring information about one’s values, and also to group formation, i.e. matching on hypergraphs. With possibly-negative valuations, which capture e.g. job markets, the problem becomes harder. We introduce notions of approximate stability and show that they have beneficial welfare implications. However, the main problem of proving constant factor welfare guarantees in “ex ante stable equilibrium” remains open.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_4"}, {"primary_key": "1271845", "vector": [], "sparse_vector": [], "title": "Fair Division with Allocator&apos;s Preference.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Biaosh<PERSON><PERSON>"], "summary": "We consider the problem of fairly allocating indivisible resources to agents, which has been studied for years. Most previous work focuses on fairness and/or efficiencyamong agentsgiven agents’ preferences. However, besides the agents, the allocator as the resource owner may also be involved in many real-world scenarios (e.g., government resource allocation, heritage division, company personnel assignment, etc.). The allocator has the inclination to obtain a fair or efficient allocation based on her own preference over the items and to whom each item is allocated. In this paper, we propose a new model and focus on the following two problems concerning the allocator’s fairness and efficiency: Is it possible to find an allocation that is fair for both the agents and the allocator? What is the complexity of maximizing the allocator’s social welfare while satisfying the agents’ fairness? We consider the two fundamental fairness criteria:envy-freenessandproportionality. For the first problem, we study the existence of an allocation that is envy-free up tocgoods (EF-c) or proportional up tocgoods (PROP-c) from both the agents’ and the allocator’s perspectives, in which such an allocation is calleddoubly EF-cordoubly PROP-crespectively. When the allocator’s utility depends exclusively on the items (but not to whom an item is allocated), we prove that a doubly EF-1 allocation always exists. For the general setting where the allocator has a preference over the itemsandto whom each item is allocated, we prove that a doubly EF-1 allocation always exists for two agents, a doubly PROP-2 allocation always exists for binary valuations, and a doubly PROP-\\(O(\\log n)\\)allocation always exists in general. For the second problem, we provide various (in)approximability results in which the gaps between approximation and inapproximation ratios are asymptotically closed under most settings. Most of our results are based on some novel technical tools including the chromatic numbers of the Kneser graphs and linear programming-based analysis.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_5"}, {"primary_key": "1271846", "vector": [], "sparse_vector": [], "title": "Optimal Stopping with Multi-dimensional Comparative Loss Aversion.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Motivated by behavioral biases in human decision makers, recent work by [11] explores the effects of loss aversion and reference dependence on the prophet inequality problem, where an online decision maker sees candidates one by one in sequence and must decide immediately whether to select the current candidate or forego it and lose it forever. In their model, the online decision-maker forms a reference point equal to the best candidate previously rejected, and the decision-maker suffers from loss aversion based on the quality of their reference point, and a parameter\\(\\lambda \\)that quantifies their loss aversion. We consider the same prophet inequality setup, but with candidates that havemultiple features. The decision maker still forms a reference point, and still suffers loss aversion in comparison to their reference point as a function of\\(\\lambda \\), but now their reference point is a (hypothetical) combination of the best candidate seen so farin each feature. Despite having the same basic prophet inequality setup and model of loss aversion, conclusions in our multi-dimensional model differs considerably from the one-dimensional model of [11]. For example, [11] gives a tight closed-form on the competitive ratio that an online decision-maker can achieve as a function of\\(\\lambda \\), for any\\(\\lambda \\ge 0\\). In our multi-dimensional model, there is a sharp phase transition: ifkdenotes the number of dimensions, then when\\(\\lambda \\cdot (k-1) \\ge 1\\),no non-trivial competitive ratio is possible. On the other hand, when\\(\\lambda \\cdot (k-1) < 1\\), we give a tight bound on the achievable competitive ratio (similar to [11]). As another example, [11] uncovers an exponential improvement in their competitive ratio for the random-order vs. worst-case prophet inequality problem. In our model with\\(k\\ge 2\\)dimensions, the gap is at most a constant-factor. We uncover several additional key differences in the multi- and single-dimensional models.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_6"}, {"primary_key": "1271847", "vector": [], "sparse_vector": [], "title": "Selling to Multiple No-Regret Buyers.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider the problem of repeatedly auctioning a single item to multiple i.i.d buyers who each use a no-regret learning algorithm to bid over time. In particular, we study the seller’s optimal revenue, if they know that the buyers are no-regret learners (but only that their behavior satisfies some no-regret property—they do not know the precise algorithm/heuristic used). Our main result designs an auction that extracts revenue equal to thefull expected welfarewhenever the buyers are “mean-based” (a property satisfied by standard no-regret learning algorithms such as Multiplicative Weights, Follow-the-Perturbed-Leader, etc.). This extends a main result of [4] which held only for a single buyer. Our other results consider the case when buyers are mean-based but never overbid. On this front, [4] provides a simple LP formulation for the revenue-maximizing auction for a single-buyer. We identify several formal barriers to extending this approach to multiple buyers.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_7"}, {"primary_key": "1271848", "vector": [], "sparse_vector": [], "title": "Penalties and Rewards for Fair Learning in Paired Kidney Exchange Programs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A kidney exchange program, also called a kidney paired donation program, can be viewed as a repeated, dynamic trading and allocation mechanism. This suggests that a dynamic algorithm for transplant exchange selection may have superior performance in comparison to the repeated use of a static algorithm. We confirm this hypothesis using a full scale simulation of the Canadian Kidney Paired Donation Program: learning algorithms, that attempt to learn optimal patient-donor weights in advance via dynamic simulations, do lead to improved outcomes. Specifically, our learning algorithms, designed with the objective of fairness (that is, equity in terms of transplant accessibility across cPRA groups), also lead to an increased number of transplants and shorter average waiting times. Indeed, our highest performing learning algorithm improves egalitarian fairness by 10% whilst also increasing the number of transplants by 6% and decreasing waiting times by 24%. However, our main result is much more surprising. We find that the most critical factor in determining the performance of a kidney exchange program isnotthe judicious assignment of positive weights (rewards) to patient-donor pairs. Rather, the key factor in increasing the number of transplants, decreasing waiting times and improving group fairness is the judicious assignment of a negative weight (penalty) to the small number of non-directed donors in the kidney exchange program.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_8"}, {"primary_key": "1271849", "vector": [], "sparse_vector": [], "title": "Deterministic Impartial Selection with Weights.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In the impartial selection problem, a subset of agents up to a fixed sizekamong a group ofnis to be chosen based on votes cast by the agents themselves. A selection mechanism isimpartialif no agent can influence its own chance of being selected by changing its vote. It is\\(\\alpha \\)-optimalif, for every instance, the ratio between the votes received by the selected subset is at least a fraction of\\(\\alpha \\)of the votes received by the subset of sizekwith the highest number of votes. We study deterministic impartial mechanisms in a more general setting with arbitrarily weighted votes and provide the first approximation guarantee, roughly\\(1/\\lceil 2n/k\\rceil \\). When the number of agents to select is large enough compared to the total number of agents, this yields an improvement on the previously best known approximation ratio of 1/kfor the unweighted setting. We further show that our mechanism can be adapted to the impartial assignment problem, in which multiple sets of up tokagents are to be selected, with a loss in the approximation ratio of 1/2.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_9"}, {"primary_key": "1271850", "vector": [], "sparse_vector": [], "title": "Blockchain Participation Games.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study game-theoretic models for capturing participation in blockchain systems. Existing blockchains can be naturally viewed as games, where a set of potentially interested users is faced with the dilemma of whether to engage with the protocol or not. Engagement here implies that the user will be asked to complete certain tasks, whenever she is selected to contribute, according to some stochastic process. Apart from the basic dilemma of engaging or not, even more strategic considerations arise in systems where users may be able to declare participation and then retract (while still being able to receive rewards). We propose two models for studying such games, with the first one focusing on the basic dilemma of engaging or not, whereas the latter focuses on the retraction effects. In both models we provide characterization results or necessary conditions on the structure of Nash equilibria. Our findings reveal that appropriate reward mechanisms can be used to stimulate participation and avoid negative effects of free riding, results that are in line with real world blockchain system deployments.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_10"}, {"primary_key": "1271851", "vector": [], "sparse_vector": [], "title": "Buy-Many Mechanisms for Many Unit-Demand Buyers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A recent line of research has established a novel desideratum for designing approximately-revenue-optimal multi-item mechanisms, namely the buy-many constraint. Under this constraint, prices for different allocations made by the mechanism must be subadditive, implying that the price of a bundle cannot exceed the sum of prices of individual items it contains. This natural constraint has enabled several positive results in multi-item mechanism design bypassing well-established impossibility results. Our work addresses the main open question from this literature of extending the buy-many constraint to multiple buyer settings and developing an approximation. We propose a new revenue benchmark for multi-buyer mechanisms via an ex-ante relaxation that captures several different ways of extending the buy-many constraint to the multi-buyer setting. Our main result is that a simple sequential item pricing mechanism with buyer-specific prices can achieve an\\(O(\\log m)\\)approximation to this revenue benchmark when all buyers have unit-demand or additive preferences over m items. This is the best possible as it directly matches the previous results for the single-buyer setting where no simple mechanism can obtain a better approximation. From a technical viewpoint we make two novel contributions. First, we develop a supply-constrained version of buy-many approximation for a single buyer. Second, we develop a multi-dimensional online contention resolution scheme for unit-demand buyers that may be of independent interest in mechanism design.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_2"}, {"primary_key": "1271852", "vector": [], "sparse_vector": [], "title": "Recovering Single-Crossing Preferences from Approval Ballots.", "authors": ["<PERSON>", "<PERSON>"], "summary": "An electorate with fully-ranked innate preferences casts approval votes over a finite set of alternatives. As a result, only partial information about the true preferences is revealed to the voting authorities. In an effort to understand the nature of the true preferences given only partial information, one might ask whether the unknown innate preferences could possibly be single-crossing. The existence of a polynomial time algorithm to determine this has been asked as an outstanding problem in the works of <PERSON> and <PERSON> [18]. We hereby give a polynomial time algorithm determining a single-crossing collection of fully-ranked preferences that could have induced the elicited approval ballots, or reporting the nonexistence thereof. Moreover, we consider the problem of identifying negative instances with a set of forbidden sub-ballots, showing that any such characterization requires infinitely many forbidden configurations.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_11"}, {"primary_key": "1271853", "vector": [], "sparse_vector": [], "title": "The Good, the Bad and the Submodular: Fairly Allocating Mixed Manna Under Order-Neutral Submodular Preferences.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of fairly allocating indivisible goods (positively valued items) and chores (negatively valued items) among agents with decreasing marginal utilities over items. Our focus is on instances where all the agents havesimplepreferences; specifically, we assume the marginal value of an item can be either\\(-1\\), 0 or some positive integerc. Under this assumption, we present an efficient algorithm to compute leximin allocations for a broad class of valuation functions we callorder-neutralsubmodular valuations. Order-neutral submodular valuations strictly contain the well-studied class of additive valuations but are a strict subset of the class of submodular valuations. We show that these leximin allocations are Lorenz dominating and approximately proportional. We also show that, under further restriction to additive valuations, these leximin allocations are approximately envy-free and guarantee each agent their maximin share. We complement this algorithmic result with a lower bound showing that the problem of computing leximin allocations is NP-hard whencis a rational number.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_12"}, {"primary_key": "1271854", "vector": [], "sparse_vector": [], "title": "Dividing Good and Great Items Among Agents with Bivalued Submodular Valuations.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of fairly allocating a set of indivisible goods among agents withbivalued submodular valuations—each good provides a marginal gain of eitheraorb(\\(a < b\\)) and goods have decreasing marginal gains. This is a natural generalization of two well-studied valuation classes—bivalued additive valuations and binary submodular valuations. We present a simple sequential algorithmic framework, based on the recently introduced Yankee Swap mechanism, that can be adapted to compute a variety of solution concepts, including max Nash welfare (MNW), leximin andp-mean welfare maximizing allocations whenadividesb. This result is complemented by an existing result on the computational intractability of MNW and leximin allocations whenadoes not divideb. We show that MNW and leximin allocations guarantee each agent at least\\(\\frac{2}{5}\\)and\\(\\frac{a}{b+2a}\\)of their maximin share, respectively, whenadividesb. We also show that neither the leximin nor the MNW allocation is guaranteed to be envy free up to one good (EF1). This is surprising since for the simpler classes of bivalued additive valuations and binary submodular valuations, MNW allocations are known to be envy free up to<PERSON><PERSON> (EFX).", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_13"}, {"primary_key": "1271855", "vector": [], "sparse_vector": [], "title": "Equilibrium Analysis of Customer Attraction Games.", "authors": ["<PERSON><PERSON>", "Ningyuan Li", "<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce a game model called “customer attraction game” to demonstrate the competition among online content providers. In this model, customers exhibit interest in various topics. Each content provider selects one topic and benefits from the attracted customers. We investigate both symmetric and asymmetric settings involving agents and customers. In the symmetric setting, the existence of pure Nash equilibrium (PNE) is guaranteed, but finding a PNE is PLS-complete. To address this, we propose a fully polynomial time approximation scheme to identify an approximate PNE. Moreover, the tight Price of Anarchy (PoA) is established. In the asymmetric setting, we show the nonexistence of PNE in certain instances and establish that determining its existence is NP-hard. Nevertheless, we prove the existence of an approximate PNE. Additionally, when agents select topics sequentially, we demonstrate that finding a subgame-perfect equilibrium is PSPACE-hard. Furthermore, we present the sequential PoA for the two-agent setting.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_14"}, {"primary_key": "1271856", "vector": [], "sparse_vector": [], "title": "The Importance of Knowing the Arrival Order in Combinatorial Bayesian Settings.", "authors": ["<PERSON><PERSON>", "<PERSON>ar <PERSON>"], "summary": "We study the measure of order-competitive ratio introduced by <PERSON> et al. [16] for online algorithms in Bayesian combinatorial settings. In our setting, a decision-maker observes a sequence of elements that are associated with stochastic rewards that are drawn from known priors, but revealed one by one in an online fashion. The decision-maker needs to decide upon the arrival of each element whether to select it or discard it (according to some feasibility constraint), and receives the associated rewards of the selected elements. The order-competitive ratio is defined as the worst-case ratio (over all distribution sequences) between the performance of the best order-unaware and order-aware algorithms, and quantifies the loss incurred due to the lack of knowledge of the arrival order. <PERSON> et al. [16] showed how to design algorithms that achieve better approximations with respect to the new benchmark (order-competitive ratio) in the single-choice setting, which raises the natural question of whether the same can be achieved in combinatorial settings. In particular, whether it is possible to achieve a constant approximation with respect to the best online algorithm for downward-closed feasibility constraints, whether\\(\\omega (1/n)\\)-approximation is achievable for general (non-downward-closed) feasibility constraints, or whether a convergence rate of\\(1 - o(1/\\sqrt{k})\\)is achievable for the multi-unit setting. We show, by devising novel constructions that may be of independent interest, that for all three scenarios, the asymptotic lower bounds with respect to the old benchmark, also hold with respect to the new benchmark.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_15"}, {"primary_key": "1271857", "vector": [], "sparse_vector": [], "title": "Prophet Inequalities via the Expected Competitive Ratio.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>-<PERSON><PERSON>"], "summary": "We consider prophet inequalities under downward-closed constraints. In this problem, a decision-maker makes immediate and irrevocable choices on arriving elements, subject to constraints. Traditionally, performance is compared to the expected offline optimum, called theRatio of Expectations(\\(\\textsf {RoE} \\)). However,\\(\\textsf {RoE} \\)has limitations as it only guarantees the average performance compared to the optimum, and might perform poorly against the realized ex-post optimal value. We study an alternative performance measure, theExpected Ratio(\\(\\textsf {EoR} \\)), namely the expectation of the ratio between algorithm’s and prophet’s value.\\(\\textsf {EoR} \\)offers robust guarantees, e.g., a constant\\(\\textsf {EoR} \\)implies achieving a constant fraction of the offline optimum with constant probability. For the special case of single-choice problems the\\(\\textsf {EoR} \\)coincides with the well-studied notion of probability of selecting the maximum. However, the\\(\\textsf {EoR} \\)naturally generalizes the probability of selecting the maximum for combinatorial constraints, which are the main focus of this paper. Specifically, we establish two reductions: for every constraint,\\(\\textsf {RoE} \\)and the\\(\\textsf {EoR} \\)are at most a constant factor apart. Additionally, we show that the\\(\\textsf {EoR} \\)is a stronger benchmark than the\\(\\textsf {RoE} \\)in that, for every instance (constraint and distribution), the\\(\\textsf {RoE} \\)is at least a constant fraction of the\\(\\textsf {EoR} \\), but not vice versa. Both these reductions imply a wealth of\\(\\textsf {EoR} \\)results in multiple settings where\\(\\textsf {RoE} \\)results are known.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_16"}, {"primary_key": "1271858", "vector": [], "sparse_vector": [], "title": "Smoothed Analysis of Social Choice Revisited.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A canonical problem in social choice is how to aggregate ranked votes: that is, givennvoters’ rankings overmcandidates, whatvoting rulefshould we use to aggregate these votes and select a single winner? One standard method for comparing voting rules is by their satisfaction ofaxioms— properties that we want a “reasonable” rule to satisfy. Unfortunately, this approach leads to several impossibilities: no voting rule can simultaneously satisfy all the properties we would want, at least in the worst case over all possible inputs. Motivated by this, we consider a relaxation of this worst case requirement: a “smoothed” model of social choice, where votes are independently perturbed with small amounts of noise. If no matter which input profile we start with, the probability of an axiom being satisfied post-noise becomes large as the number of votersngrows, we take it to be as good as satisfied — called “smoothed-satisfied” — even if it may be violated in the worst case. Within our smoothed model - a mild restriction of Lirong Xia’s - we give a cohesive overview of when smoothed noise is sufficient to overcome axiomatic impossibilities. We give simple sufficient conditions for smoothed-satisfaction or smoothed-violation of several axioms and paradoxes, including most that have been studied plus some previously unstudied. We then observe that in a practically important subclass of noise models, convergence to smoothed satisfaction is prohibitively slow asngrows. Motivated by this, we prove bounds specifically within a canonical noise model from this subclass — the Mallows model. Here, we find a more nuanced picture on exactly when smoothed analysis can help.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_17"}, {"primary_key": "1271859", "vector": [], "sparse_vector": [], "title": "A Discrete and Bounded Locally Envy-Free Cake Cutting Protocol on Trees.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the classic problem offairlyallocating a divisible resource modeled as a unit interval [0, 1] and referred to as acake. In a landmark result, <PERSON> and <PERSON> [4] gave the first discrete and bounded protocol for computing anenvy-free cake division, but with a huge query complexity consisting of six towers of exponent in the number of agents,n. However, the best-known lower bound for the same is\\(\\varOmega (n^2)\\), leaving a massive gap in our understanding of the complexity of the problem. In this work, we study an important variant of the problem where agents are embedded on a graph whose edges determine agent relations. Given a graph, the goal is to find alocally envy-freeallocation where every agent values her share of the cake at least as much as that of any of herneighbors’share. We identify anon-trivialgraph structure, namely a tree having depth at most 2 (Depth2Tree), that admits a query efficient protocol to find locally envy-free allocations using\\(O(n^4 \\log n)\\)queries under the standard Robertson-Webb (RW) query model. To the best of our knowledge, this is the first such non-trivial graph structure. In our second result, we develop a novel cake-division protocol that finds a locally envy-free allocation amongnagents onanyTreegraph using\\(O(n^{2n})\\)RW queries. Though exponential, our protocol forTreegraphs achieves a significant improvement over the best-known query complexity of six-towers-of-nfor complete graphs.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_18"}, {"primary_key": "1271860", "vector": [], "sparse_vector": [], "title": "A Mechanism for Participatory Budgeting with Funding Constraints and Project Interactions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Participatory budgeting (PB) has been widely adopted and has attracted significant research efforts; however, there is a lack of mechanisms for PB which elicit project interactions, such as substitution and complementarity, from voters. Also, the outcomes of PB in practice are subject to various minimum/maximum funding constraints on ‘types’ of projects. There is an insufficient understanding of how these funding constraints affect PB’s strategic and computational complexities. We propose a novel preference elicitation scheme for PB which allows voters to express how their utilities from projects within ‘groups’ interact. We consider preference aggregation done under minimum and maximum funding constraints on ‘types’ of projects, where a project can have multiple type labels as long as this classification can be defined by a 1-laminar structure (henceforth called 1-laminar funding constraints). Overall, we extend the Knapsack voting model of <PERSON><PERSON> et al. [23] in two ways – enriching the preference elicitation scheme to include project interactions and generalizing the preference aggregation scheme to include 1-laminar funding constraints. We show that the strategyproofness results of <PERSON><PERSON> et al. [23] for Knapsack voting continue to hold under 1-laminar funding constraints. Moreover, when the funding constraints cannot be described by a 1-laminar structure, strategyproofness does not hold. Although project interactions often break the strategyproofness, we study a special case of vote profiles where truthful voting is a Nash equilibrium under substitution project interactions. We then turn to the study of the computational complexity of preference aggregation. Social welfare maximization under project interactions is NP-hard. As a workaround for practical instances, we give a fixed parameter tractable (FPT) algorithm for social welfare maximization with respect to the maximum number of projects in a group when the overall budget is specified in a fixed number of bits. We also give an FPT algorithm with respect to the number of distinct votes.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_19"}, {"primary_key": "1271861", "vector": [], "sparse_vector": [], "title": "Randomized Algorithm for MPMD on Two Sources.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A 3-competitive deterministic algorithm for the problem of min-cost perfect matching with delays on two sources (2-MPMD) was proposed years ago. However, whether randomness leads to a more competitive algorithm remains open. 2-MPMD is similar to the famous ski rental problem. Indeed, for both problems, we must choose between continuing to pay a repeating cost or a one-time fee. There is a memoryless randomized algorithm for ski rental that is more competitive than its best deterministic algorithm. But, surprisingly, memoryless randomized algorithms for 2-MPMD cannot do better than 3-competitive. In this paper, we devise a 2-competitive randomized algorithm for 2-MPMD. Moreover, we prove that 2 is also the lower bound.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_20"}, {"primary_key": "1271862", "vector": [], "sparse_vector": [], "title": "Polyhedral Clinching Auctions for Indivisible Goods.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this study, we propose the polyhedral clinching auction for indivisible goods, which has so far been studied for divisible goods. As in the divisible setting by <PERSON><PERSON> et al. (2015), our mechanism enjoys incentive compatibility, individual rationality, and Pareto optimality, and works with polymatroidal environments. A notable feature for the indivisible setting is that the whole procedure can be conducted in time polynomial of the number of buyers and goods. Moreover, we show additional efficiency guarantees, recently established by <PERSON> for the divisible setting: The liquid welfare (LW) of our mechanism achieves more than 1/2 of the optimal LW, and that the social welfare is more than the optimal LW.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_21"}, {"primary_key": "1271863", "vector": [], "sparse_vector": [], "title": "Online Matching with Stochastic Rewards: Advanced Analyses Using Configuration Linear Programs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jun<PERSON> Song", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (2012) proposed Online Matching with Stochastic Rewards, which generalizes the Online Bipartite Matching problem of <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> (1990) by associating the edges with success probabilities. This new feature captures the pay-per-click model in online advertising. Recently, <PERSON> and <PERSON> (2020) studied this problem under the online primal dual framework using the Configuration Linear Program (LP), and got the best known competitive ratios of the Stochastic Balance algorithm. Their work suggests that the more expressive Configuration LP is more suitable for this problem than the Matching LP. This paper advances the theory of Configuration LP in two directions. Our technical contribution includes a characterization of the joint matching outcome of an offline vertex andall its neighbors. This characterization may be of independent interest, and is aligned with the spirit of Configuration LP. By contrast, previous analyses of Ranking generally focus on only one neighbor. Second, we designed a Stochastic Configuration LP that captures a stochastic benchmark proposed by <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (2020), who used a Path-based LP. The Stochastic Configuration LP is smaller and simpler than the Path-based LP. Moreover, using the new LP we improved the competitive ratio of Stochastic Balance from 0.596 to 0.611 when the success probabilities are infinitesimal, and to 0.613 when the success probabilities are further equal.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_22"}, {"primary_key": "1271864", "vector": [], "sparse_vector": [], "title": "Online Nash Welfare Maximization Without Predictions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Li", "Xinkai Shu", "<PERSON><PERSON><PERSON>"], "summary": "The maximization of Nash welfare, which equals the geometric mean of agents’ utilities, is widely studied because it balances efficiency and fairness in resource allocation problems. <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> (2022) recently introduced the model of online Nash welfare maximization forTdivisible items andNagents with additive utilities with predictions of each agent’s utility for receiving all items. They gave online algorithms whose competitive ratios are logarithmic. We initiate the study of online Nash welfare maximizationwithout predictions, assuming either that the agents’ utilities for receiving all items differ by a bounded ratio, or that their utilities for the Nash welfare maximizing allocation differ by a bounded ratio. We design online algorithms whose competitive ratios are logarithmic in the aforementioned ratios of agents’ utilities and the number of agents.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_23"}, {"primary_key": "1271865", "vector": [], "sparse_vector": [], "title": "The Price of Anarchy of Probabilistic Serial in One-Sided Allocation Problems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study “fair mechanisms” for the (asymmetric) one-sided allocation problem withmitems andnmulti-unit demand agents with additive, unit-sum valuations. The symmetric case (\\(m=n\\)), the one-sided matching problem, has been studied extensively for the special class of unit demand agents, in particular with respect to the folkloreRandom Prioritymechanism and theProbabilistic Serialmechanism, introduced by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> [6]. These are both fair mechanisms and attention has focused on their structural properties, incentives, and performance with respect to social welfare. Under the standard assumption of unit-sum valuation functions, <PERSON><PERSON><PERSON><PERSON> et al. [10] proved that the price of anarchy is\\(\\varTheta (\\sqrt{n})\\)in the one-sided matching problem for both the Random Priority and Probabilistic Serial mechanisms. Whilst both Random Priority and Probabilistic Serial areordinal mechanisms, these approximation guarantees are the best possible even for the broader class ofcardinal mechanisms. To extend these results to the general setting of the one-sided allocation problems there are two technical obstacles. One, asymmetry (\\(m\\ne n\\))is problematic especially when the number of items is much greater than the number of agents,\\(m\\gg n\\). Two, it is necessary to study multi-unit demand agents rather than simply unit demand agents. For this paper, our focus is on Probabilistic Serial. Our first main result is an upper bound of\\(O(\\sqrt{n}\\cdot \\log m)\\)on the price of anarchy for the asymmetric one-sided allocation problem with multi-unit demand agents. We then present a complementary lower bound of\\(\\varOmega (\\sqrt{n})\\)for any fair mechanism. That lower bound is unsurprising. More intriguing is our second main result: the price of anarchy of Probabilistic Serial degrades with the number of items. Specifically, a logarithmic dependence on the number of items is necessary as we show a lower bound of\\(\\varOmega (\\min \\{n\\, , \\, \\log m\\})\\).", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_24"}, {"primary_key": "1271866", "vector": [], "sparse_vector": [], "title": "An Adaptive and Verifiably Proportional Method for Participatory Budgeting.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Participatory Budgeting (PB) is a form of participatory democracy in which citizens select a set of projects to be implemented, subject to a budget constraint. The Method of Equal Shares (MES), introduced in [18], is a simple iterative method for this task, which runs in polynomial time and satisfies a demanding proportionality axiom (Extended Justified Representation) in the setting of approval utilities. However, a downside of MES is that it is non-exhaustive: given an MES outcome, it may be possible to expand it by adding new projects without violating the budget constraint. To complete the outcome, the approach currently used in practice (e.g., in Wieliczka in Apr 2023,https://equalshares.net/resources/zielony-milion/) is as follows: given an instance with budgetb, one searches for a budget\\(b'\\ge b\\)such that when MES is executed with budget\\(b'\\), it produces a maximal feasible solution forb. The search is greedy, i.e., one has to execute MES from scratch for each value of\\(b'\\). To avoid redundant computation, we introduce a variant of MES, which we call Adaptive Method of Equal Shares (AMES). Our method is budget-adaptive, in the sense that, given an outcomeWfor a budgetband a new budget\\(b'>b\\), it can compute the outcome\\(W'\\)for budget\\(b'\\)by leveraging similarities betweenWand\\(W'\\). This eliminates the need to recompute solutions from scratch when increasing virtual budgets. Furthermore, AMES satisfies EJR in a certifiable way: given the output of our method, one can check in time\\(O(n\\log n+mn)\\)that it provides EJR (here,nis the number of voters andmis the number of projects). We evaluate the potential of AMES on real-world PB data, showing that small increases in budget typically require only minor modifications of the outcome.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_25"}, {"primary_key": "1271867", "vector": [], "sparse_vector": [], "title": "Routing MEV in Constant Function Market Makers.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Miner Extractable Value (MEV) refers to excess value captured by miners (or validators) from users in a cryptocurrency network. This excess value often comes from reordering users’ transactions to maximize fees or from inserting new transactions that front-run users’ transactions. One of the most common types of MEV involves a ‘sandwich attack’ against a user trading on a constant function market maker (CFMM), which is a popular class of decentralized exchange. We analyze game theoretic properties of MEV in CFMMs that we callrouting MEV, where an aggregate user trade is routed over a network and potentially sandwich attacked. We present examples where the existence of routing MEV both degrades and, counterintuitively,improvesthe quality of routing. We construct an analogue of the price of anarchy for this setting and demonstrate that if the impact of a sandwich attack is localized in a suitable sense, then the price of anarchy is constant (The code for all the numerical experiments in this paper can be found at this link:https://github.com/tjdiamandis/mev-cfmm).", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_26"}, {"primary_key": "1271868", "vector": [], "sparse_vector": [], "title": "Auction Design for Value Maximizers with Budget and Return-on-Spend Constraints.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Xu", "<PERSON><PERSON><PERSON>"], "summary": "The paper designs revenue-maximizing auction mechanisms for agents who aim to maximize their total obtained values rather than the classical quasi-linear utilities. Several models have been proposed to capture the behaviors of such agents in the literature. In the paper, we consider the model where agents are subject to budget and return-on-spend constraints. The budget constraint of an agent limits the maximum payment she can afford, while the return-on-spend constraint means that the ratio of the total obtained value (return) to the total payment (spend) cannot be lower than the targeted bar set by the agent. The problem was first coined by [5]. In their work, only Bayesian mechanisms were considered. We initiate the study of the problem in the worst-case model and compare the revenue of our mechanisms to an offline optimal solution, the most ambitious benchmark. The paper distinguishes two main auction settings based on the accessibility of agents’ information:fully privateandpartially private. In the fully private setting, an agent’s valuation, budget, and target bar are all private. We show that if agents are unit-demand, constant approximation mechanisms can be obtained; while for additive agents, there exists a mechanism that achieves a constant approximation ratio under a large market assumption. The partially private setting is the setting considered in the previous work [5] where only the agents’ target bars are private. We show that in this setting, the approximation ratio of the single-item auction can be further improved, and a\\(\\mathrm {\\Omega }(1/\\sqrt{n})\\)-approximation mechanism can be derived for additive agents.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_27"}, {"primary_key": "1271869", "vector": [], "sparse_vector": [], "title": "Auction Design for Bidders with Ex Post ROI Constraints.", "authors": ["Hongtao Lv", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Motivated by practical constraints in online advertising, we investigate single-parameter auction design for bidders with constraints on their Return On Investment (ROI) – a targeted minimum ratio between the obtained value and the payment. We focus onex postROI constraints, which require the ROI condition to be satisfied for every realized value profile. With ROI-constrained bidders, we first provide a full characterization of the allocation and payment rules of dominant-strategy incentive compatible (DSIC) auctions. In particular, we show that given any monotone allocation rule, the corresponding DSIC payment should be the Myerson payment with arebatefor each bidder to meet their ROI constraints. Furthermore, we also determine the optimal auction structure when the item is sold to a single bidder under a mild regularity condition. This structure entails a randomized allocation scheme and a first-price payment rule, which differs from the deterministic Myerson auction and previous works on ex ante ROI constraints.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_28"}, {"primary_key": "1271870", "vector": [], "sparse_vector": [], "title": "Nash Stability in Fractional Hedonic Games with Bounded Size Coalitions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider fractional hedonic games, a natural and succinct subclass of hedonic games able to model many real-world settings in which agents have to organize themselves in groups called coalitions. An outcome of the game, also called coalition structure, is a partition of all agents into coalitions. Previous work assumed that coalitions can be of any size. However, in many real-world situations, the size of the coalitions is bounded: vehicles, offices, classrooms and project teams are some examples of possible coalitions of bounded size. In this paper, we initiate the studyk-fractional hedonic games (\\(\\mathrm {{{k}\\!-\\!FHG}}\\)), in which all coalitions have size at mostk, by considering Nash stable coalition structures, i.e., outcomes in which no agent can improve her utility by unilaterally changing her own coalition; in particular, we study existence of, convergence to, complexity and efficiency of Nash stable outcomes, and we also provide results about the complexity and approximation of optimal outcomes. We perform a thoroughgoing analysis of\\(\\mathrm {{{k}\\!-\\!FHG}}\\)for\\(k=2,3,4\\). We remark that, on the one hand, considering these values ofkis interesting in itself as many real world scenarios (as some of the aforementioned ones) deal with coalitions of small size; on the other hand, studying\\(\\mathrm {{{k}\\!-\\!FHG}}\\)for small values of<PERSON><PERSON>h represents a necessary step for understanding the cases with higher values ofkand already constitutes a challenging task.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_29"}, {"primary_key": "1271871", "vector": [], "sparse_vector": [], "title": "Improved Competitive Ratio for Edge-Weighted Online Stochastic Matching.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the edge-weighted online stochastic matching problem, in which an edge-weighted bipartite graph\\(G=(I\\cup J, E)\\)with offline verticesJand online vertex typesIis given. The online vertices have types sampled fromIwith probability proportional to the arrival rates of online vertex types. The online algorithm must make immediate and irrevocable matching decisions with the objective of maximizing the total weight of the matching. For the problem with general arrival rates, <PERSON><PERSON><PERSON> et al. (FOCS 2009) proposed theSuggested Matchingalgorithm and showed that it achieves a competitive ratio of\\(1-1/e \\approx 0.632\\). The ratio has recently been improved to 0.645 by <PERSON> (2022), who proposed theMultistage Suggested Matching(MSM) algorithm. In this paper, we propose theEvolving Suggested Matching(ESM) algorithm and show that it achieves a competitive ratio of 0.650.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_30"}, {"primary_key": "1271872", "vector": [], "sparse_vector": [], "title": "Separation in Distributionally Robust Monopolist Problem.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider a monopoly pricing problem, where a seller has multiple items to sell to a single buyer, only knowing the distribution of the buyer’s value profile. The seller’s goal is to maximize her expected revenue. In general, this is a difficult problem to solve, even if the distribution is well specified. In this paper, we solve a subclass of this problem when the distribution is assumed to belong to the class of distributions defined by given marginal partial information. Under this model, we show that the optimal strategy for the seller is a randomized posted price mechanism under which the items are sold separately, and the result continues to hold even when the buyer has a budget feasibility constraint. Consequently, under some specific ambiguity sets which include moment-based and Wasserstein ambiguity sets, we provide analytical solutions for these single-item problems. Based on the additive separation property, we show the general additive separation problem is a special case of resource allocation problems that can be solved by known polynomial-time algorithms.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_31"}, {"primary_key": "1271873", "vector": [], "sparse_vector": [], "title": "Target-Oriented Regret Minimization for Satisficing Monopolists.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study a robust monopoly pricing problem where a seller aspires to sell an item to a buyer. We assume that the seller, unaware of the buyer’s willingness to pay, ambitiously optimizes over a space of all individually rational and incentive compatible mechanisms with a regret-type objective criterion. We particularly adopt a robust satisficing approach, which has been touted as a promising alternative of robust optimization, and aim at minimizing the excess regret above the predetermined target level. We interpret our pricing problem both probabilistically and distributionally robustly, and we analytically show that the optimal mechanism involves the seller offering a menu of lotteries that charges a buyer-dependent participation fee and allocates the item with a buyer-dependent probability. Then, we consider two additional variants of the problem where the seller restricts her attention to a class of only deterministic posted price mechanisms and where the seller is relieved from specifying the target regret in advance. Finally, we determine a randomized posted price mechanism that is readily implementable and equivalent to the optimal mechanism, compute its statistics, and quantify the strength of the entailed randomization. Besides, we compare the proposed mechanism with a robust benchmark and numerically find that the former is predominantly superior to the latter in terms of the expected regret and the expected revenue especially when the coefficient of variation of the buyer’s value is under a hundred percent.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_32"}, {"primary_key": "1271874", "vector": [], "sparse_vector": [], "title": "One Quarter Each (on Average) Ensures Proportionality.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the problem of fair allocation ofmindivisible items to a group ofnagents with subsidy (money). Our work mainly focuses on the allocation of chores but most of our results extend to the allocation of goods as well. We consider the case when agents have (general) additive cost functions. Assuming that the maximum cost of an item to an agent can be compensated by one dollar, we show that a total ofn/4 dollars of subsidy suffices to ensure a proportional allocation. Moreover, we show thatn/4 is tight in the sense that there exists an instance withnagents for which every proportional allocation requires a total subsidy of at leastn/4. We also consider the weighted case and show that a total subsidy of\\((n-1)/2\\)suffices to ensure a weighted proportional allocation.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_33"}, {"primary_key": "1271875", "vector": [], "sparse_vector": [], "title": "Two-Sided Capacitated Submodular Maximization in Gig Platforms.", "authors": ["<PERSON>"], "summary": "In this paper, we propose three generic models of capacitated coverage and, more generally, submodular maximization to study task-worker assignment problems that arise in a wide range of gig economy platforms. Our models incorporate the following features: (1) Each task and worker can have an arbitrary matching capacity, which captures the limited number of copies or finite budget for the task and the working capacity of the worker; (2) Each task is associated with a coverage or, more generally, a monotone submodular utility function. Our objective is to design an allocation policy that maximizes the sum of all tasks’ utilities, subject to capacity constraints on tasks and workers. We consider two settings: offline, where all tasks and workers are static, and online, where tasks are static while workers arrive dynamically. We present three LP-based rounding algorithms that achieve optimal approximation ratios of\\(1-1/\\textsf{e} \\sim 0.632\\)for offline coverage maximization, competitive ratios of\\((19-67/\\textsf{e}^3)/27\\sim 0.580\\)and 0.436 for online coverage and online monotone submodular maximization, respectively.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_34"}, {"primary_key": "1271876", "vector": [], "sparse_vector": [], "title": "Price Cycles in Ridesharing Platforms.", "authors": ["Chen<PERSON> Yu", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In ridesharing platforms such as Uber and Lyft, it is observed that drivers sometimes collaboratively go offline when the price is low, and then return after the price has risen due to the perceived lack of supply. This collective strategy leads to cyclic fluctuations in prices and available drivers, resulting in poor reliability and social welfare. We study a continuous-time, non-atomic model and prove that such online/offline strategies may form a Nash equilibrium among drivers, but lead to a lower total driver payoff if the market is sufficiently dense. Furthermore, we show how to setprice floorsthat effectively mitigate the emergence and impact of price cycles.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_35"}, {"primary_key": "1271877", "vector": [], "sparse_vector": [], "title": "Improved Truthful Rank Approximation for Rank-Maximal Matchings.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this work, we study truthful mechanisms for the rank-maximal matching problem, in the view of approximation. Our result reduces the gap from both the upper and lower bound sides. We propose a lexicographically truthful (LT) and nearly Pareto optimal (PO) randomized mechanism with an approximation ratio\\(\\frac{2 \\sqrt{e}-1}{2 \\sqrt{e}-2} \\approx 1.77\\). The previous best result is 2. The crucial and novel ingredients of our algorithm are preservation lemmas, which allow us to utilize techniques from online algorithms to analyze the new ratio. We also provide several hardness results in variant settings to complement our upper bound. In particular, we improve the lower bound of the approximation ratio for our LT and PO mechanism to\\(18/13\\approx 1.38\\). To the best of our knowledge, it is the first time to obtain a lower bound by utilizing the linear programming approach along this research line.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_36"}, {"primary_key": "1271878", "vector": [], "sparse_vector": [], "title": "Reallocation Mechanisms Under Distributional Constraints in the Full Preference Domain.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the problem of reallocating indivisible goods among a set of agents in one-sided matching market, where the feasible set for each good is subject to an associated distributionalmatroidorM-convexconstraint. Agents’ preferences are allowed to have ties and not all the agents have initial endowments. We present feasible, Pareto optimal, strategy-proof mechanisms for the problems with matroid orM-convex constraints. Strategy-proofness is proved based on new structural properties over first choice graphs, which should be of independent interest. These mechanisms strictly generalize the best-known mechanism for non-strict preferences [21] with all desired properties carried over.", "published": "2023-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-031-48974-7_37"}]